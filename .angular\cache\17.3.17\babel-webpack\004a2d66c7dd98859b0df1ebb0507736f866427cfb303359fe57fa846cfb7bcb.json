{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.concat = void 0;\nvar concatAll_1 = require(\"../operators/concatAll\");\nvar args_1 = require(\"../util/args\");\nvar from_1 = require(\"./from\");\nfunction concat() {\n  var args = [];\n  for (var _i = 0; _i < arguments.length; _i++) {\n    args[_i] = arguments[_i];\n  }\n  return concatAll_1.concatAll()(from_1.from(args, args_1.popScheduler(args)));\n}\nexports.concat = concat;\n//# sourceMappingURL=concat.js.map", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}