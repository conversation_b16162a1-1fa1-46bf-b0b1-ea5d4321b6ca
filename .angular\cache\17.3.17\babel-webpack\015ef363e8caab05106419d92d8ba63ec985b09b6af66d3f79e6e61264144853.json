{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.timer = void 0;\nvar Observable_1 = require(\"../Observable\");\nvar async_1 = require(\"../scheduler/async\");\nvar isScheduler_1 = require(\"../util/isScheduler\");\nvar isDate_1 = require(\"../util/isDate\");\nfunction timer(dueTime, intervalOrScheduler, scheduler) {\n  if (dueTime === void 0) {\n    dueTime = 0;\n  }\n  if (scheduler === void 0) {\n    scheduler = async_1.async;\n  }\n  var intervalDuration = -1;\n  if (intervalOrScheduler != null) {\n    if (isScheduler_1.isScheduler(intervalOrScheduler)) {\n      scheduler = intervalOrScheduler;\n    } else {\n      intervalDuration = intervalOrScheduler;\n    }\n  }\n  return new Observable_1.Observable(function (subscriber) {\n    var due = isDate_1.isValidDate(dueTime) ? +dueTime - scheduler.now() : dueTime;\n    if (due < 0) {\n      due = 0;\n    }\n    var n = 0;\n    return scheduler.schedule(function () {\n      if (!subscriber.closed) {\n        subscriber.next(n++);\n        if (0 <= intervalDuration) {\n          this.schedule(undefined, intervalDuration);\n        } else {\n          subscriber.complete();\n        }\n      }\n    }, due);\n  });\n}\nexports.timer = timer;\n//# sourceMappingURL=timer.js.map", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}