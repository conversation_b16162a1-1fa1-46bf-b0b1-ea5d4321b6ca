{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.executeSchedule = void 0;\nfunction executeSchedule(parentSubscription, scheduler, work, delay, repeat) {\n  if (delay === void 0) {\n    delay = 0;\n  }\n  if (repeat === void 0) {\n    repeat = false;\n  }\n  var scheduleSubscription = scheduler.schedule(function () {\n    work();\n    if (repeat) {\n      parentSubscription.add(this.schedule(null, delay));\n    } else {\n      this.unsubscribe();\n    }\n  }, delay);\n  parentSubscription.add(scheduleSubscription);\n  if (!repeat) {\n    return scheduleSubscription;\n  }\n}\nexports.executeSchedule = executeSchedule;\n//# sourceMappingURL=executeSchedule.js.map", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}