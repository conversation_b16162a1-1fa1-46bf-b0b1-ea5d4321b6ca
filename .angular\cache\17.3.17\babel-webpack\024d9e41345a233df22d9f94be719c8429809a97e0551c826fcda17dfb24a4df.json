{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Indonesian [id]\n//! author : <PERSON> : https://github.com/tyok\n//! reference: http://id.wikisource.org/wiki/Pedoman_<PERSON><PERSON>_<PERSON>an_Bahasa_Indonesia_yang_Disempurnakan\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  var id = moment.defineLocale('id', {\n    months: 'Jan<PERSON><PERSON>_Februari_Maret_April_Mei_Juni_Juli_Agustus_September_Oktober_November_Desember'.split('_'),\n    monthsShort: 'Jan_Feb_Mar_Apr_Mei_Jun_Jul_Agt_Sep_Okt_Nov_Des'.split('_'),\n    weekdays: '<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>_<PERSON><PERSON>_Jumat_Sabtu'.split('_'),\n    weekdaysShort: '<PERSON>_<PERSON>_Sel_Rab_Kam_Jum_Sab'.split('_'),\n    weekdaysMin: 'Mg_Sn_Sl_Rb_Km_Jm_Sb'.split('_'),\n    longDateFormat: {\n      LT: 'HH.mm',\n      LTS: 'HH.mm.ss',\n      L: 'DD/MM/YYYY',\n      LL: 'D MMMM YYYY',\n      LLL: 'D MMMM YYYY [pukul] HH.mm',\n      LLLL: 'dddd, D MMMM YYYY [pukul] HH.mm'\n    },\n    meridiemParse: /pagi|siang|sore|malam/,\n    meridiemHour: function (hour, meridiem) {\n      if (hour === 12) {\n        hour = 0;\n      }\n      if (meridiem === 'pagi') {\n        return hour;\n      } else if (meridiem === 'siang') {\n        return hour >= 11 ? hour : hour + 12;\n      } else if (meridiem === 'sore' || meridiem === 'malam') {\n        return hour + 12;\n      }\n    },\n    meridiem: function (hours, minutes, isLower) {\n      if (hours < 11) {\n        return 'pagi';\n      } else if (hours < 15) {\n        return 'siang';\n      } else if (hours < 19) {\n        return 'sore';\n      } else {\n        return 'malam';\n      }\n    },\n    calendar: {\n      sameDay: '[Hari ini pukul] LT',\n      nextDay: '[Besok pukul] LT',\n      nextWeek: 'dddd [pukul] LT',\n      lastDay: '[Kemarin pukul] LT',\n      lastWeek: 'dddd [lalu pukul] LT',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: 'dalam %s',\n      past: '%s yang lalu',\n      s: 'beberapa detik',\n      ss: '%d detik',\n      m: 'semenit',\n      mm: '%d menit',\n      h: 'sejam',\n      hh: '%d jam',\n      d: 'sehari',\n      dd: '%d hari',\n      M: 'sebulan',\n      MM: '%d bulan',\n      y: 'setahun',\n      yy: '%d tahun'\n    },\n    week: {\n      dow: 0,\n      // Sunday is the first day of the week.\n      doy: 6 // The week that contains Jan 6th is the first week of the year.\n    }\n  });\n  return id;\n});", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}