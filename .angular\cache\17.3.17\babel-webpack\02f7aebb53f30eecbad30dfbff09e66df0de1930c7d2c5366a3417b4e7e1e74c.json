{"ast": null, "code": "/*!\n * Chart.js v2.9.4\n * https://www.chartjs.org\n * (c) 2020 Chart.js Contributors\n * Released under the MIT License\n */\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' ? module.exports = factory(function () {\n    try {\n      return require('moment');\n    } catch (e) {}\n  }()) : typeof define === 'function' && define.amd ? define(['require'], function (require) {\n    return factory(function () {\n      try {\n        return require('moment');\n      } catch (e) {}\n    }());\n  }) : (global = global || self, global.Chart = factory(global.moment));\n})(this, function (moment) {\n  'use strict';\n\n  moment = moment && moment.hasOwnProperty('default') ? moment['default'] : moment;\n  function createCommonjsModule(fn, module) {\n    return module = {\n      exports: {}\n    }, fn(module, module.exports), module.exports;\n  }\n  function getCjsExportFromNamespace(n) {\n    return n && n['default'] || n;\n  }\n  var colorName = {\n    \"aliceblue\": [240, 248, 255],\n    \"antiquewhite\": [250, 235, 215],\n    \"aqua\": [0, 255, 255],\n    \"aquamarine\": [127, 255, 212],\n    \"azure\": [240, 255, 255],\n    \"beige\": [245, 245, 220],\n    \"bisque\": [255, 228, 196],\n    \"black\": [0, 0, 0],\n    \"blanchedalmond\": [255, 235, 205],\n    \"blue\": [0, 0, 255],\n    \"blueviolet\": [138, 43, 226],\n    \"brown\": [165, 42, 42],\n    \"burlywood\": [222, 184, 135],\n    \"cadetblue\": [95, 158, 160],\n    \"chartreuse\": [127, 255, 0],\n    \"chocolate\": [210, 105, 30],\n    \"coral\": [255, 127, 80],\n    \"cornflowerblue\": [100, 149, 237],\n    \"cornsilk\": [255, 248, 220],\n    \"crimson\": [220, 20, 60],\n    \"cyan\": [0, 255, 255],\n    \"darkblue\": [0, 0, 139],\n    \"darkcyan\": [0, 139, 139],\n    \"darkgoldenrod\": [184, 134, 11],\n    \"darkgray\": [169, 169, 169],\n    \"darkgreen\": [0, 100, 0],\n    \"darkgrey\": [169, 169, 169],\n    \"darkkhaki\": [189, 183, 107],\n    \"darkmagenta\": [139, 0, 139],\n    \"darkolivegreen\": [85, 107, 47],\n    \"darkorange\": [255, 140, 0],\n    \"darkorchid\": [153, 50, 204],\n    \"darkred\": [139, 0, 0],\n    \"darksalmon\": [233, 150, 122],\n    \"darkseagreen\": [143, 188, 143],\n    \"darkslateblue\": [72, 61, 139],\n    \"darkslategray\": [47, 79, 79],\n    \"darkslategrey\": [47, 79, 79],\n    \"darkturquoise\": [0, 206, 209],\n    \"darkviolet\": [148, 0, 211],\n    \"deeppink\": [255, 20, 147],\n    \"deepskyblue\": [0, 191, 255],\n    \"dimgray\": [105, 105, 105],\n    \"dimgrey\": [105, 105, 105],\n    \"dodgerblue\": [30, 144, 255],\n    \"firebrick\": [178, 34, 34],\n    \"floralwhite\": [255, 250, 240],\n    \"forestgreen\": [34, 139, 34],\n    \"fuchsia\": [255, 0, 255],\n    \"gainsboro\": [220, 220, 220],\n    \"ghostwhite\": [248, 248, 255],\n    \"gold\": [255, 215, 0],\n    \"goldenrod\": [218, 165, 32],\n    \"gray\": [128, 128, 128],\n    \"green\": [0, 128, 0],\n    \"greenyellow\": [173, 255, 47],\n    \"grey\": [128, 128, 128],\n    \"honeydew\": [240, 255, 240],\n    \"hotpink\": [255, 105, 180],\n    \"indianred\": [205, 92, 92],\n    \"indigo\": [75, 0, 130],\n    \"ivory\": [255, 255, 240],\n    \"khaki\": [240, 230, 140],\n    \"lavender\": [230, 230, 250],\n    \"lavenderblush\": [255, 240, 245],\n    \"lawngreen\": [124, 252, 0],\n    \"lemonchiffon\": [255, 250, 205],\n    \"lightblue\": [173, 216, 230],\n    \"lightcoral\": [240, 128, 128],\n    \"lightcyan\": [224, 255, 255],\n    \"lightgoldenrodyellow\": [250, 250, 210],\n    \"lightgray\": [211, 211, 211],\n    \"lightgreen\": [144, 238, 144],\n    \"lightgrey\": [211, 211, 211],\n    \"lightpink\": [255, 182, 193],\n    \"lightsalmon\": [255, 160, 122],\n    \"lightseagreen\": [32, 178, 170],\n    \"lightskyblue\": [135, 206, 250],\n    \"lightslategray\": [119, 136, 153],\n    \"lightslategrey\": [119, 136, 153],\n    \"lightsteelblue\": [176, 196, 222],\n    \"lightyellow\": [255, 255, 224],\n    \"lime\": [0, 255, 0],\n    \"limegreen\": [50, 205, 50],\n    \"linen\": [250, 240, 230],\n    \"magenta\": [255, 0, 255],\n    \"maroon\": [128, 0, 0],\n    \"mediumaquamarine\": [102, 205, 170],\n    \"mediumblue\": [0, 0, 205],\n    \"mediumorchid\": [186, 85, 211],\n    \"mediumpurple\": [147, 112, 219],\n    \"mediumseagreen\": [60, 179, 113],\n    \"mediumslateblue\": [123, 104, 238],\n    \"mediumspringgreen\": [0, 250, 154],\n    \"mediumturquoise\": [72, 209, 204],\n    \"mediumvioletred\": [199, 21, 133],\n    \"midnightblue\": [25, 25, 112],\n    \"mintcream\": [245, 255, 250],\n    \"mistyrose\": [255, 228, 225],\n    \"moccasin\": [255, 228, 181],\n    \"navajowhite\": [255, 222, 173],\n    \"navy\": [0, 0, 128],\n    \"oldlace\": [253, 245, 230],\n    \"olive\": [128, 128, 0],\n    \"olivedrab\": [107, 142, 35],\n    \"orange\": [255, 165, 0],\n    \"orangered\": [255, 69, 0],\n    \"orchid\": [218, 112, 214],\n    \"palegoldenrod\": [238, 232, 170],\n    \"palegreen\": [152, 251, 152],\n    \"paleturquoise\": [175, 238, 238],\n    \"palevioletred\": [219, 112, 147],\n    \"papayawhip\": [255, 239, 213],\n    \"peachpuff\": [255, 218, 185],\n    \"peru\": [205, 133, 63],\n    \"pink\": [255, 192, 203],\n    \"plum\": [221, 160, 221],\n    \"powderblue\": [176, 224, 230],\n    \"purple\": [128, 0, 128],\n    \"rebeccapurple\": [102, 51, 153],\n    \"red\": [255, 0, 0],\n    \"rosybrown\": [188, 143, 143],\n    \"royalblue\": [65, 105, 225],\n    \"saddlebrown\": [139, 69, 19],\n    \"salmon\": [250, 128, 114],\n    \"sandybrown\": [244, 164, 96],\n    \"seagreen\": [46, 139, 87],\n    \"seashell\": [255, 245, 238],\n    \"sienna\": [160, 82, 45],\n    \"silver\": [192, 192, 192],\n    \"skyblue\": [135, 206, 235],\n    \"slateblue\": [106, 90, 205],\n    \"slategray\": [112, 128, 144],\n    \"slategrey\": [112, 128, 144],\n    \"snow\": [255, 250, 250],\n    \"springgreen\": [0, 255, 127],\n    \"steelblue\": [70, 130, 180],\n    \"tan\": [210, 180, 140],\n    \"teal\": [0, 128, 128],\n    \"thistle\": [216, 191, 216],\n    \"tomato\": [255, 99, 71],\n    \"turquoise\": [64, 224, 208],\n    \"violet\": [238, 130, 238],\n    \"wheat\": [245, 222, 179],\n    \"white\": [255, 255, 255],\n    \"whitesmoke\": [245, 245, 245],\n    \"yellow\": [255, 255, 0],\n    \"yellowgreen\": [154, 205, 50]\n  };\n  var conversions = createCommonjsModule(function (module) {\n    /* MIT license */\n\n    // NOTE: conversions should only return primitive values (i.e. arrays, or\n    //       values that give correct `typeof` results).\n    //       do not use box values types (i.e. Number(), String(), etc.)\n\n    var reverseKeywords = {};\n    for (var key in colorName) {\n      if (colorName.hasOwnProperty(key)) {\n        reverseKeywords[colorName[key]] = key;\n      }\n    }\n    var convert = module.exports = {\n      rgb: {\n        channels: 3,\n        labels: 'rgb'\n      },\n      hsl: {\n        channels: 3,\n        labels: 'hsl'\n      },\n      hsv: {\n        channels: 3,\n        labels: 'hsv'\n      },\n      hwb: {\n        channels: 3,\n        labels: 'hwb'\n      },\n      cmyk: {\n        channels: 4,\n        labels: 'cmyk'\n      },\n      xyz: {\n        channels: 3,\n        labels: 'xyz'\n      },\n      lab: {\n        channels: 3,\n        labels: 'lab'\n      },\n      lch: {\n        channels: 3,\n        labels: 'lch'\n      },\n      hex: {\n        channels: 1,\n        labels: ['hex']\n      },\n      keyword: {\n        channels: 1,\n        labels: ['keyword']\n      },\n      ansi16: {\n        channels: 1,\n        labels: ['ansi16']\n      },\n      ansi256: {\n        channels: 1,\n        labels: ['ansi256']\n      },\n      hcg: {\n        channels: 3,\n        labels: ['h', 'c', 'g']\n      },\n      apple: {\n        channels: 3,\n        labels: ['r16', 'g16', 'b16']\n      },\n      gray: {\n        channels: 1,\n        labels: ['gray']\n      }\n    };\n\n    // hide .channels and .labels properties\n    for (var model in convert) {\n      if (convert.hasOwnProperty(model)) {\n        if (!('channels' in convert[model])) {\n          throw new Error('missing channels property: ' + model);\n        }\n        if (!('labels' in convert[model])) {\n          throw new Error('missing channel labels property: ' + model);\n        }\n        if (convert[model].labels.length !== convert[model].channels) {\n          throw new Error('channel and label counts mismatch: ' + model);\n        }\n        var channels = convert[model].channels;\n        var labels = convert[model].labels;\n        delete convert[model].channels;\n        delete convert[model].labels;\n        Object.defineProperty(convert[model], 'channels', {\n          value: channels\n        });\n        Object.defineProperty(convert[model], 'labels', {\n          value: labels\n        });\n      }\n    }\n    convert.rgb.hsl = function (rgb) {\n      var r = rgb[0] / 255;\n      var g = rgb[1] / 255;\n      var b = rgb[2] / 255;\n      var min = Math.min(r, g, b);\n      var max = Math.max(r, g, b);\n      var delta = max - min;\n      var h;\n      var s;\n      var l;\n      if (max === min) {\n        h = 0;\n      } else if (r === max) {\n        h = (g - b) / delta;\n      } else if (g === max) {\n        h = 2 + (b - r) / delta;\n      } else if (b === max) {\n        h = 4 + (r - g) / delta;\n      }\n      h = Math.min(h * 60, 360);\n      if (h < 0) {\n        h += 360;\n      }\n      l = (min + max) / 2;\n      if (max === min) {\n        s = 0;\n      } else if (l <= 0.5) {\n        s = delta / (max + min);\n      } else {\n        s = delta / (2 - max - min);\n      }\n      return [h, s * 100, l * 100];\n    };\n    convert.rgb.hsv = function (rgb) {\n      var rdif;\n      var gdif;\n      var bdif;\n      var h;\n      var s;\n      var r = rgb[0] / 255;\n      var g = rgb[1] / 255;\n      var b = rgb[2] / 255;\n      var v = Math.max(r, g, b);\n      var diff = v - Math.min(r, g, b);\n      var diffc = function (c) {\n        return (v - c) / 6 / diff + 1 / 2;\n      };\n      if (diff === 0) {\n        h = s = 0;\n      } else {\n        s = diff / v;\n        rdif = diffc(r);\n        gdif = diffc(g);\n        bdif = diffc(b);\n        if (r === v) {\n          h = bdif - gdif;\n        } else if (g === v) {\n          h = 1 / 3 + rdif - bdif;\n        } else if (b === v) {\n          h = 2 / 3 + gdif - rdif;\n        }\n        if (h < 0) {\n          h += 1;\n        } else if (h > 1) {\n          h -= 1;\n        }\n      }\n      return [h * 360, s * 100, v * 100];\n    };\n    convert.rgb.hwb = function (rgb) {\n      var r = rgb[0];\n      var g = rgb[1];\n      var b = rgb[2];\n      var h = convert.rgb.hsl(rgb)[0];\n      var w = 1 / 255 * Math.min(r, Math.min(g, b));\n      b = 1 - 1 / 255 * Math.max(r, Math.max(g, b));\n      return [h, w * 100, b * 100];\n    };\n    convert.rgb.cmyk = function (rgb) {\n      var r = rgb[0] / 255;\n      var g = rgb[1] / 255;\n      var b = rgb[2] / 255;\n      var c;\n      var m;\n      var y;\n      var k;\n      k = Math.min(1 - r, 1 - g, 1 - b);\n      c = (1 - r - k) / (1 - k) || 0;\n      m = (1 - g - k) / (1 - k) || 0;\n      y = (1 - b - k) / (1 - k) || 0;\n      return [c * 100, m * 100, y * 100, k * 100];\n    };\n\n    /**\n     * See https://en.m.wikipedia.org/wiki/Euclidean_distance#Squared_Euclidean_distance\n     * */\n    function comparativeDistance(x, y) {\n      return Math.pow(x[0] - y[0], 2) + Math.pow(x[1] - y[1], 2) + Math.pow(x[2] - y[2], 2);\n    }\n    convert.rgb.keyword = function (rgb) {\n      var reversed = reverseKeywords[rgb];\n      if (reversed) {\n        return reversed;\n      }\n      var currentClosestDistance = Infinity;\n      var currentClosestKeyword;\n      for (var keyword in colorName) {\n        if (colorName.hasOwnProperty(keyword)) {\n          var value = colorName[keyword];\n\n          // Compute comparative distance\n          var distance = comparativeDistance(rgb, value);\n\n          // Check if its less, if so set as closest\n          if (distance < currentClosestDistance) {\n            currentClosestDistance = distance;\n            currentClosestKeyword = keyword;\n          }\n        }\n      }\n      return currentClosestKeyword;\n    };\n    convert.keyword.rgb = function (keyword) {\n      return colorName[keyword];\n    };\n    convert.rgb.xyz = function (rgb) {\n      var r = rgb[0] / 255;\n      var g = rgb[1] / 255;\n      var b = rgb[2] / 255;\n\n      // assume sRGB\n      r = r > 0.04045 ? Math.pow((r + 0.055) / 1.055, 2.4) : r / 12.92;\n      g = g > 0.04045 ? Math.pow((g + 0.055) / 1.055, 2.4) : g / 12.92;\n      b = b > 0.04045 ? Math.pow((b + 0.055) / 1.055, 2.4) : b / 12.92;\n      var x = r * 0.4124 + g * 0.3576 + b * 0.1805;\n      var y = r * 0.2126 + g * 0.7152 + b * 0.0722;\n      var z = r * 0.0193 + g * 0.1192 + b * 0.9505;\n      return [x * 100, y * 100, z * 100];\n    };\n    convert.rgb.lab = function (rgb) {\n      var xyz = convert.rgb.xyz(rgb);\n      var x = xyz[0];\n      var y = xyz[1];\n      var z = xyz[2];\n      var l;\n      var a;\n      var b;\n      x /= 95.047;\n      y /= 100;\n      z /= 108.883;\n      x = x > 0.008856 ? Math.pow(x, 1 / 3) : 7.787 * x + 16 / 116;\n      y = y > 0.008856 ? Math.pow(y, 1 / 3) : 7.787 * y + 16 / 116;\n      z = z > 0.008856 ? Math.pow(z, 1 / 3) : 7.787 * z + 16 / 116;\n      l = 116 * y - 16;\n      a = 500 * (x - y);\n      b = 200 * (y - z);\n      return [l, a, b];\n    };\n    convert.hsl.rgb = function (hsl) {\n      var h = hsl[0] / 360;\n      var s = hsl[1] / 100;\n      var l = hsl[2] / 100;\n      var t1;\n      var t2;\n      var t3;\n      var rgb;\n      var val;\n      if (s === 0) {\n        val = l * 255;\n        return [val, val, val];\n      }\n      if (l < 0.5) {\n        t2 = l * (1 + s);\n      } else {\n        t2 = l + s - l * s;\n      }\n      t1 = 2 * l - t2;\n      rgb = [0, 0, 0];\n      for (var i = 0; i < 3; i++) {\n        t3 = h + 1 / 3 * -(i - 1);\n        if (t3 < 0) {\n          t3++;\n        }\n        if (t3 > 1) {\n          t3--;\n        }\n        if (6 * t3 < 1) {\n          val = t1 + (t2 - t1) * 6 * t3;\n        } else if (2 * t3 < 1) {\n          val = t2;\n        } else if (3 * t3 < 2) {\n          val = t1 + (t2 - t1) * (2 / 3 - t3) * 6;\n        } else {\n          val = t1;\n        }\n        rgb[i] = val * 255;\n      }\n      return rgb;\n    };\n    convert.hsl.hsv = function (hsl) {\n      var h = hsl[0];\n      var s = hsl[1] / 100;\n      var l = hsl[2] / 100;\n      var smin = s;\n      var lmin = Math.max(l, 0.01);\n      var sv;\n      var v;\n      l *= 2;\n      s *= l <= 1 ? l : 2 - l;\n      smin *= lmin <= 1 ? lmin : 2 - lmin;\n      v = (l + s) / 2;\n      sv = l === 0 ? 2 * smin / (lmin + smin) : 2 * s / (l + s);\n      return [h, sv * 100, v * 100];\n    };\n    convert.hsv.rgb = function (hsv) {\n      var h = hsv[0] / 60;\n      var s = hsv[1] / 100;\n      var v = hsv[2] / 100;\n      var hi = Math.floor(h) % 6;\n      var f = h - Math.floor(h);\n      var p = 255 * v * (1 - s);\n      var q = 255 * v * (1 - s * f);\n      var t = 255 * v * (1 - s * (1 - f));\n      v *= 255;\n      switch (hi) {\n        case 0:\n          return [v, t, p];\n        case 1:\n          return [q, v, p];\n        case 2:\n          return [p, v, t];\n        case 3:\n          return [p, q, v];\n        case 4:\n          return [t, p, v];\n        case 5:\n          return [v, p, q];\n      }\n    };\n    convert.hsv.hsl = function (hsv) {\n      var h = hsv[0];\n      var s = hsv[1] / 100;\n      var v = hsv[2] / 100;\n      var vmin = Math.max(v, 0.01);\n      var lmin;\n      var sl;\n      var l;\n      l = (2 - s) * v;\n      lmin = (2 - s) * vmin;\n      sl = s * vmin;\n      sl /= lmin <= 1 ? lmin : 2 - lmin;\n      sl = sl || 0;\n      l /= 2;\n      return [h, sl * 100, l * 100];\n    };\n\n    // http://dev.w3.org/csswg/css-color/#hwb-to-rgb\n    convert.hwb.rgb = function (hwb) {\n      var h = hwb[0] / 360;\n      var wh = hwb[1] / 100;\n      var bl = hwb[2] / 100;\n      var ratio = wh + bl;\n      var i;\n      var v;\n      var f;\n      var n;\n\n      // wh + bl cant be > 1\n      if (ratio > 1) {\n        wh /= ratio;\n        bl /= ratio;\n      }\n      i = Math.floor(6 * h);\n      v = 1 - bl;\n      f = 6 * h - i;\n      if ((i & 0x01) !== 0) {\n        f = 1 - f;\n      }\n      n = wh + f * (v - wh); // linear interpolation\n\n      var r;\n      var g;\n      var b;\n      switch (i) {\n        default:\n        case 6:\n        case 0:\n          r = v;\n          g = n;\n          b = wh;\n          break;\n        case 1:\n          r = n;\n          g = v;\n          b = wh;\n          break;\n        case 2:\n          r = wh;\n          g = v;\n          b = n;\n          break;\n        case 3:\n          r = wh;\n          g = n;\n          b = v;\n          break;\n        case 4:\n          r = n;\n          g = wh;\n          b = v;\n          break;\n        case 5:\n          r = v;\n          g = wh;\n          b = n;\n          break;\n      }\n      return [r * 255, g * 255, b * 255];\n    };\n    convert.cmyk.rgb = function (cmyk) {\n      var c = cmyk[0] / 100;\n      var m = cmyk[1] / 100;\n      var y = cmyk[2] / 100;\n      var k = cmyk[3] / 100;\n      var r;\n      var g;\n      var b;\n      r = 1 - Math.min(1, c * (1 - k) + k);\n      g = 1 - Math.min(1, m * (1 - k) + k);\n      b = 1 - Math.min(1, y * (1 - k) + k);\n      return [r * 255, g * 255, b * 255];\n    };\n    convert.xyz.rgb = function (xyz) {\n      var x = xyz[0] / 100;\n      var y = xyz[1] / 100;\n      var z = xyz[2] / 100;\n      var r;\n      var g;\n      var b;\n      r = x * 3.2406 + y * -1.5372 + z * -0.4986;\n      g = x * -0.9689 + y * 1.8758 + z * 0.0415;\n      b = x * 0.0557 + y * -0.2040 + z * 1.0570;\n\n      // assume sRGB\n      r = r > 0.0031308 ? 1.055 * Math.pow(r, 1.0 / 2.4) - 0.055 : r * 12.92;\n      g = g > 0.0031308 ? 1.055 * Math.pow(g, 1.0 / 2.4) - 0.055 : g * 12.92;\n      b = b > 0.0031308 ? 1.055 * Math.pow(b, 1.0 / 2.4) - 0.055 : b * 12.92;\n      r = Math.min(Math.max(0, r), 1);\n      g = Math.min(Math.max(0, g), 1);\n      b = Math.min(Math.max(0, b), 1);\n      return [r * 255, g * 255, b * 255];\n    };\n    convert.xyz.lab = function (xyz) {\n      var x = xyz[0];\n      var y = xyz[1];\n      var z = xyz[2];\n      var l;\n      var a;\n      var b;\n      x /= 95.047;\n      y /= 100;\n      z /= 108.883;\n      x = x > 0.008856 ? Math.pow(x, 1 / 3) : 7.787 * x + 16 / 116;\n      y = y > 0.008856 ? Math.pow(y, 1 / 3) : 7.787 * y + 16 / 116;\n      z = z > 0.008856 ? Math.pow(z, 1 / 3) : 7.787 * z + 16 / 116;\n      l = 116 * y - 16;\n      a = 500 * (x - y);\n      b = 200 * (y - z);\n      return [l, a, b];\n    };\n    convert.lab.xyz = function (lab) {\n      var l = lab[0];\n      var a = lab[1];\n      var b = lab[2];\n      var x;\n      var y;\n      var z;\n      y = (l + 16) / 116;\n      x = a / 500 + y;\n      z = y - b / 200;\n      var y2 = Math.pow(y, 3);\n      var x2 = Math.pow(x, 3);\n      var z2 = Math.pow(z, 3);\n      y = y2 > 0.008856 ? y2 : (y - 16 / 116) / 7.787;\n      x = x2 > 0.008856 ? x2 : (x - 16 / 116) / 7.787;\n      z = z2 > 0.008856 ? z2 : (z - 16 / 116) / 7.787;\n      x *= 95.047;\n      y *= 100;\n      z *= 108.883;\n      return [x, y, z];\n    };\n    convert.lab.lch = function (lab) {\n      var l = lab[0];\n      var a = lab[1];\n      var b = lab[2];\n      var hr;\n      var h;\n      var c;\n      hr = Math.atan2(b, a);\n      h = hr * 360 / 2 / Math.PI;\n      if (h < 0) {\n        h += 360;\n      }\n      c = Math.sqrt(a * a + b * b);\n      return [l, c, h];\n    };\n    convert.lch.lab = function (lch) {\n      var l = lch[0];\n      var c = lch[1];\n      var h = lch[2];\n      var a;\n      var b;\n      var hr;\n      hr = h / 360 * 2 * Math.PI;\n      a = c * Math.cos(hr);\n      b = c * Math.sin(hr);\n      return [l, a, b];\n    };\n    convert.rgb.ansi16 = function (args) {\n      var r = args[0];\n      var g = args[1];\n      var b = args[2];\n      var value = 1 in arguments ? arguments[1] : convert.rgb.hsv(args)[2]; // hsv -> ansi16 optimization\n\n      value = Math.round(value / 50);\n      if (value === 0) {\n        return 30;\n      }\n      var ansi = 30 + (Math.round(b / 255) << 2 | Math.round(g / 255) << 1 | Math.round(r / 255));\n      if (value === 2) {\n        ansi += 60;\n      }\n      return ansi;\n    };\n    convert.hsv.ansi16 = function (args) {\n      // optimization here; we already know the value and don't need to get\n      // it converted for us.\n      return convert.rgb.ansi16(convert.hsv.rgb(args), args[2]);\n    };\n    convert.rgb.ansi256 = function (args) {\n      var r = args[0];\n      var g = args[1];\n      var b = args[2];\n\n      // we use the extended greyscale palette here, with the exception of\n      // black and white. normal palette only has 4 greyscale shades.\n      if (r === g && g === b) {\n        if (r < 8) {\n          return 16;\n        }\n        if (r > 248) {\n          return 231;\n        }\n        return Math.round((r - 8) / 247 * 24) + 232;\n      }\n      var ansi = 16 + 36 * Math.round(r / 255 * 5) + 6 * Math.round(g / 255 * 5) + Math.round(b / 255 * 5);\n      return ansi;\n    };\n    convert.ansi16.rgb = function (args) {\n      var color = args % 10;\n\n      // handle greyscale\n      if (color === 0 || color === 7) {\n        if (args > 50) {\n          color += 3.5;\n        }\n        color = color / 10.5 * 255;\n        return [color, color, color];\n      }\n      var mult = (~~(args > 50) + 1) * 0.5;\n      var r = (color & 1) * mult * 255;\n      var g = (color >> 1 & 1) * mult * 255;\n      var b = (color >> 2 & 1) * mult * 255;\n      return [r, g, b];\n    };\n    convert.ansi256.rgb = function (args) {\n      // handle greyscale\n      if (args >= 232) {\n        var c = (args - 232) * 10 + 8;\n        return [c, c, c];\n      }\n      args -= 16;\n      var rem;\n      var r = Math.floor(args / 36) / 5 * 255;\n      var g = Math.floor((rem = args % 36) / 6) / 5 * 255;\n      var b = rem % 6 / 5 * 255;\n      return [r, g, b];\n    };\n    convert.rgb.hex = function (args) {\n      var integer = ((Math.round(args[0]) & 0xFF) << 16) + ((Math.round(args[1]) & 0xFF) << 8) + (Math.round(args[2]) & 0xFF);\n      var string = integer.toString(16).toUpperCase();\n      return '000000'.substring(string.length) + string;\n    };\n    convert.hex.rgb = function (args) {\n      var match = args.toString(16).match(/[a-f0-9]{6}|[a-f0-9]{3}/i);\n      if (!match) {\n        return [0, 0, 0];\n      }\n      var colorString = match[0];\n      if (match[0].length === 3) {\n        colorString = colorString.split('').map(function (char) {\n          return char + char;\n        }).join('');\n      }\n      var integer = parseInt(colorString, 16);\n      var r = integer >> 16 & 0xFF;\n      var g = integer >> 8 & 0xFF;\n      var b = integer & 0xFF;\n      return [r, g, b];\n    };\n    convert.rgb.hcg = function (rgb) {\n      var r = rgb[0] / 255;\n      var g = rgb[1] / 255;\n      var b = rgb[2] / 255;\n      var max = Math.max(Math.max(r, g), b);\n      var min = Math.min(Math.min(r, g), b);\n      var chroma = max - min;\n      var grayscale;\n      var hue;\n      if (chroma < 1) {\n        grayscale = min / (1 - chroma);\n      } else {\n        grayscale = 0;\n      }\n      if (chroma <= 0) {\n        hue = 0;\n      } else if (max === r) {\n        hue = (g - b) / chroma % 6;\n      } else if (max === g) {\n        hue = 2 + (b - r) / chroma;\n      } else {\n        hue = 4 + (r - g) / chroma + 4;\n      }\n      hue /= 6;\n      hue %= 1;\n      return [hue * 360, chroma * 100, grayscale * 100];\n    };\n    convert.hsl.hcg = function (hsl) {\n      var s = hsl[1] / 100;\n      var l = hsl[2] / 100;\n      var c = 1;\n      var f = 0;\n      if (l < 0.5) {\n        c = 2.0 * s * l;\n      } else {\n        c = 2.0 * s * (1.0 - l);\n      }\n      if (c < 1.0) {\n        f = (l - 0.5 * c) / (1.0 - c);\n      }\n      return [hsl[0], c * 100, f * 100];\n    };\n    convert.hsv.hcg = function (hsv) {\n      var s = hsv[1] / 100;\n      var v = hsv[2] / 100;\n      var c = s * v;\n      var f = 0;\n      if (c < 1.0) {\n        f = (v - c) / (1 - c);\n      }\n      return [hsv[0], c * 100, f * 100];\n    };\n    convert.hcg.rgb = function (hcg) {\n      var h = hcg[0] / 360;\n      var c = hcg[1] / 100;\n      var g = hcg[2] / 100;\n      if (c === 0.0) {\n        return [g * 255, g * 255, g * 255];\n      }\n      var pure = [0, 0, 0];\n      var hi = h % 1 * 6;\n      var v = hi % 1;\n      var w = 1 - v;\n      var mg = 0;\n      switch (Math.floor(hi)) {\n        case 0:\n          pure[0] = 1;\n          pure[1] = v;\n          pure[2] = 0;\n          break;\n        case 1:\n          pure[0] = w;\n          pure[1] = 1;\n          pure[2] = 0;\n          break;\n        case 2:\n          pure[0] = 0;\n          pure[1] = 1;\n          pure[2] = v;\n          break;\n        case 3:\n          pure[0] = 0;\n          pure[1] = w;\n          pure[2] = 1;\n          break;\n        case 4:\n          pure[0] = v;\n          pure[1] = 0;\n          pure[2] = 1;\n          break;\n        default:\n          pure[0] = 1;\n          pure[1] = 0;\n          pure[2] = w;\n      }\n      mg = (1.0 - c) * g;\n      return [(c * pure[0] + mg) * 255, (c * pure[1] + mg) * 255, (c * pure[2] + mg) * 255];\n    };\n    convert.hcg.hsv = function (hcg) {\n      var c = hcg[1] / 100;\n      var g = hcg[2] / 100;\n      var v = c + g * (1.0 - c);\n      var f = 0;\n      if (v > 0.0) {\n        f = c / v;\n      }\n      return [hcg[0], f * 100, v * 100];\n    };\n    convert.hcg.hsl = function (hcg) {\n      var c = hcg[1] / 100;\n      var g = hcg[2] / 100;\n      var l = g * (1.0 - c) + 0.5 * c;\n      var s = 0;\n      if (l > 0.0 && l < 0.5) {\n        s = c / (2 * l);\n      } else if (l >= 0.5 && l < 1.0) {\n        s = c / (2 * (1 - l));\n      }\n      return [hcg[0], s * 100, l * 100];\n    };\n    convert.hcg.hwb = function (hcg) {\n      var c = hcg[1] / 100;\n      var g = hcg[2] / 100;\n      var v = c + g * (1.0 - c);\n      return [hcg[0], (v - c) * 100, (1 - v) * 100];\n    };\n    convert.hwb.hcg = function (hwb) {\n      var w = hwb[1] / 100;\n      var b = hwb[2] / 100;\n      var v = 1 - b;\n      var c = v - w;\n      var g = 0;\n      if (c < 1) {\n        g = (v - c) / (1 - c);\n      }\n      return [hwb[0], c * 100, g * 100];\n    };\n    convert.apple.rgb = function (apple) {\n      return [apple[0] / 65535 * 255, apple[1] / 65535 * 255, apple[2] / 65535 * 255];\n    };\n    convert.rgb.apple = function (rgb) {\n      return [rgb[0] / 255 * 65535, rgb[1] / 255 * 65535, rgb[2] / 255 * 65535];\n    };\n    convert.gray.rgb = function (args) {\n      return [args[0] / 100 * 255, args[0] / 100 * 255, args[0] / 100 * 255];\n    };\n    convert.gray.hsl = convert.gray.hsv = function (args) {\n      return [0, 0, args[0]];\n    };\n    convert.gray.hwb = function (gray) {\n      return [0, 100, gray[0]];\n    };\n    convert.gray.cmyk = function (gray) {\n      return [0, 0, 0, gray[0]];\n    };\n    convert.gray.lab = function (gray) {\n      return [gray[0], 0, 0];\n    };\n    convert.gray.hex = function (gray) {\n      var val = Math.round(gray[0] / 100 * 255) & 0xFF;\n      var integer = (val << 16) + (val << 8) + val;\n      var string = integer.toString(16).toUpperCase();\n      return '000000'.substring(string.length) + string;\n    };\n    convert.rgb.gray = function (rgb) {\n      var val = (rgb[0] + rgb[1] + rgb[2]) / 3;\n      return [val / 255 * 100];\n    };\n  });\n  var conversions_1 = conversions.rgb;\n  var conversions_2 = conversions.hsl;\n  var conversions_3 = conversions.hsv;\n  var conversions_4 = conversions.hwb;\n  var conversions_5 = conversions.cmyk;\n  var conversions_6 = conversions.xyz;\n  var conversions_7 = conversions.lab;\n  var conversions_8 = conversions.lch;\n  var conversions_9 = conversions.hex;\n  var conversions_10 = conversions.keyword;\n  var conversions_11 = conversions.ansi16;\n  var conversions_12 = conversions.ansi256;\n  var conversions_13 = conversions.hcg;\n  var conversions_14 = conversions.apple;\n  var conversions_15 = conversions.gray;\n\n  /*\n  \tthis function routes a model to all other models.\n  \n  \tall functions that are routed have a property `.conversion` attached\n  \tto the returned synthetic function. This property is an array\n  \tof strings, each with the steps in between the 'from' and 'to'\n  \tcolor models (inclusive).\n  \n  \tconversions that are not possible simply are not included.\n  */\n\n  function buildGraph() {\n    var graph = {};\n    // https://jsperf.com/object-keys-vs-for-in-with-closure/3\n    var models = Object.keys(conversions);\n    for (var len = models.length, i = 0; i < len; i++) {\n      graph[models[i]] = {\n        // http://jsperf.com/1-vs-infinity\n        // micro-opt, but this is simple.\n        distance: -1,\n        parent: null\n      };\n    }\n    return graph;\n  }\n\n  // https://en.wikipedia.org/wiki/Breadth-first_search\n  function deriveBFS(fromModel) {\n    var graph = buildGraph();\n    var queue = [fromModel]; // unshift -> queue -> pop\n\n    graph[fromModel].distance = 0;\n    while (queue.length) {\n      var current = queue.pop();\n      var adjacents = Object.keys(conversions[current]);\n      for (var len = adjacents.length, i = 0; i < len; i++) {\n        var adjacent = adjacents[i];\n        var node = graph[adjacent];\n        if (node.distance === -1) {\n          node.distance = graph[current].distance + 1;\n          node.parent = current;\n          queue.unshift(adjacent);\n        }\n      }\n    }\n    return graph;\n  }\n  function link(from, to) {\n    return function (args) {\n      return to(from(args));\n    };\n  }\n  function wrapConversion(toModel, graph) {\n    var path = [graph[toModel].parent, toModel];\n    var fn = conversions[graph[toModel].parent][toModel];\n    var cur = graph[toModel].parent;\n    while (graph[cur].parent) {\n      path.unshift(graph[cur].parent);\n      fn = link(conversions[graph[cur].parent][cur], fn);\n      cur = graph[cur].parent;\n    }\n    fn.conversion = path;\n    return fn;\n  }\n  var route = function (fromModel) {\n    var graph = deriveBFS(fromModel);\n    var conversion = {};\n    var models = Object.keys(graph);\n    for (var len = models.length, i = 0; i < len; i++) {\n      var toModel = models[i];\n      var node = graph[toModel];\n      if (node.parent === null) {\n        // no possible conversion, or this node is the source model.\n        continue;\n      }\n      conversion[toModel] = wrapConversion(toModel, graph);\n    }\n    return conversion;\n  };\n  var convert = {};\n  var models = Object.keys(conversions);\n  function wrapRaw(fn) {\n    var wrappedFn = function (args) {\n      if (args === undefined || args === null) {\n        return args;\n      }\n      if (arguments.length > 1) {\n        args = Array.prototype.slice.call(arguments);\n      }\n      return fn(args);\n    };\n\n    // preserve .conversion property if there is one\n    if ('conversion' in fn) {\n      wrappedFn.conversion = fn.conversion;\n    }\n    return wrappedFn;\n  }\n  function wrapRounded(fn) {\n    var wrappedFn = function (args) {\n      if (args === undefined || args === null) {\n        return args;\n      }\n      if (arguments.length > 1) {\n        args = Array.prototype.slice.call(arguments);\n      }\n      var result = fn(args);\n\n      // we're assuming the result is an array here.\n      // see notice in conversions.js; don't use box types\n      // in conversion functions.\n      if (typeof result === 'object') {\n        for (var len = result.length, i = 0; i < len; i++) {\n          result[i] = Math.round(result[i]);\n        }\n      }\n      return result;\n    };\n\n    // preserve .conversion property if there is one\n    if ('conversion' in fn) {\n      wrappedFn.conversion = fn.conversion;\n    }\n    return wrappedFn;\n  }\n  models.forEach(function (fromModel) {\n    convert[fromModel] = {};\n    Object.defineProperty(convert[fromModel], 'channels', {\n      value: conversions[fromModel].channels\n    });\n    Object.defineProperty(convert[fromModel], 'labels', {\n      value: conversions[fromModel].labels\n    });\n    var routes = route(fromModel);\n    var routeModels = Object.keys(routes);\n    routeModels.forEach(function (toModel) {\n      var fn = routes[toModel];\n      convert[fromModel][toModel] = wrapRounded(fn);\n      convert[fromModel][toModel].raw = wrapRaw(fn);\n    });\n  });\n  var colorConvert = convert;\n  var colorName$1 = {\n    \"aliceblue\": [240, 248, 255],\n    \"antiquewhite\": [250, 235, 215],\n    \"aqua\": [0, 255, 255],\n    \"aquamarine\": [127, 255, 212],\n    \"azure\": [240, 255, 255],\n    \"beige\": [245, 245, 220],\n    \"bisque\": [255, 228, 196],\n    \"black\": [0, 0, 0],\n    \"blanchedalmond\": [255, 235, 205],\n    \"blue\": [0, 0, 255],\n    \"blueviolet\": [138, 43, 226],\n    \"brown\": [165, 42, 42],\n    \"burlywood\": [222, 184, 135],\n    \"cadetblue\": [95, 158, 160],\n    \"chartreuse\": [127, 255, 0],\n    \"chocolate\": [210, 105, 30],\n    \"coral\": [255, 127, 80],\n    \"cornflowerblue\": [100, 149, 237],\n    \"cornsilk\": [255, 248, 220],\n    \"crimson\": [220, 20, 60],\n    \"cyan\": [0, 255, 255],\n    \"darkblue\": [0, 0, 139],\n    \"darkcyan\": [0, 139, 139],\n    \"darkgoldenrod\": [184, 134, 11],\n    \"darkgray\": [169, 169, 169],\n    \"darkgreen\": [0, 100, 0],\n    \"darkgrey\": [169, 169, 169],\n    \"darkkhaki\": [189, 183, 107],\n    \"darkmagenta\": [139, 0, 139],\n    \"darkolivegreen\": [85, 107, 47],\n    \"darkorange\": [255, 140, 0],\n    \"darkorchid\": [153, 50, 204],\n    \"darkred\": [139, 0, 0],\n    \"darksalmon\": [233, 150, 122],\n    \"darkseagreen\": [143, 188, 143],\n    \"darkslateblue\": [72, 61, 139],\n    \"darkslategray\": [47, 79, 79],\n    \"darkslategrey\": [47, 79, 79],\n    \"darkturquoise\": [0, 206, 209],\n    \"darkviolet\": [148, 0, 211],\n    \"deeppink\": [255, 20, 147],\n    \"deepskyblue\": [0, 191, 255],\n    \"dimgray\": [105, 105, 105],\n    \"dimgrey\": [105, 105, 105],\n    \"dodgerblue\": [30, 144, 255],\n    \"firebrick\": [178, 34, 34],\n    \"floralwhite\": [255, 250, 240],\n    \"forestgreen\": [34, 139, 34],\n    \"fuchsia\": [255, 0, 255],\n    \"gainsboro\": [220, 220, 220],\n    \"ghostwhite\": [248, 248, 255],\n    \"gold\": [255, 215, 0],\n    \"goldenrod\": [218, 165, 32],\n    \"gray\": [128, 128, 128],\n    \"green\": [0, 128, 0],\n    \"greenyellow\": [173, 255, 47],\n    \"grey\": [128, 128, 128],\n    \"honeydew\": [240, 255, 240],\n    \"hotpink\": [255, 105, 180],\n    \"indianred\": [205, 92, 92],\n    \"indigo\": [75, 0, 130],\n    \"ivory\": [255, 255, 240],\n    \"khaki\": [240, 230, 140],\n    \"lavender\": [230, 230, 250],\n    \"lavenderblush\": [255, 240, 245],\n    \"lawngreen\": [124, 252, 0],\n    \"lemonchiffon\": [255, 250, 205],\n    \"lightblue\": [173, 216, 230],\n    \"lightcoral\": [240, 128, 128],\n    \"lightcyan\": [224, 255, 255],\n    \"lightgoldenrodyellow\": [250, 250, 210],\n    \"lightgray\": [211, 211, 211],\n    \"lightgreen\": [144, 238, 144],\n    \"lightgrey\": [211, 211, 211],\n    \"lightpink\": [255, 182, 193],\n    \"lightsalmon\": [255, 160, 122],\n    \"lightseagreen\": [32, 178, 170],\n    \"lightskyblue\": [135, 206, 250],\n    \"lightslategray\": [119, 136, 153],\n    \"lightslategrey\": [119, 136, 153],\n    \"lightsteelblue\": [176, 196, 222],\n    \"lightyellow\": [255, 255, 224],\n    \"lime\": [0, 255, 0],\n    \"limegreen\": [50, 205, 50],\n    \"linen\": [250, 240, 230],\n    \"magenta\": [255, 0, 255],\n    \"maroon\": [128, 0, 0],\n    \"mediumaquamarine\": [102, 205, 170],\n    \"mediumblue\": [0, 0, 205],\n    \"mediumorchid\": [186, 85, 211],\n    \"mediumpurple\": [147, 112, 219],\n    \"mediumseagreen\": [60, 179, 113],\n    \"mediumslateblue\": [123, 104, 238],\n    \"mediumspringgreen\": [0, 250, 154],\n    \"mediumturquoise\": [72, 209, 204],\n    \"mediumvioletred\": [199, 21, 133],\n    \"midnightblue\": [25, 25, 112],\n    \"mintcream\": [245, 255, 250],\n    \"mistyrose\": [255, 228, 225],\n    \"moccasin\": [255, 228, 181],\n    \"navajowhite\": [255, 222, 173],\n    \"navy\": [0, 0, 128],\n    \"oldlace\": [253, 245, 230],\n    \"olive\": [128, 128, 0],\n    \"olivedrab\": [107, 142, 35],\n    \"orange\": [255, 165, 0],\n    \"orangered\": [255, 69, 0],\n    \"orchid\": [218, 112, 214],\n    \"palegoldenrod\": [238, 232, 170],\n    \"palegreen\": [152, 251, 152],\n    \"paleturquoise\": [175, 238, 238],\n    \"palevioletred\": [219, 112, 147],\n    \"papayawhip\": [255, 239, 213],\n    \"peachpuff\": [255, 218, 185],\n    \"peru\": [205, 133, 63],\n    \"pink\": [255, 192, 203],\n    \"plum\": [221, 160, 221],\n    \"powderblue\": [176, 224, 230],\n    \"purple\": [128, 0, 128],\n    \"rebeccapurple\": [102, 51, 153],\n    \"red\": [255, 0, 0],\n    \"rosybrown\": [188, 143, 143],\n    \"royalblue\": [65, 105, 225],\n    \"saddlebrown\": [139, 69, 19],\n    \"salmon\": [250, 128, 114],\n    \"sandybrown\": [244, 164, 96],\n    \"seagreen\": [46, 139, 87],\n    \"seashell\": [255, 245, 238],\n    \"sienna\": [160, 82, 45],\n    \"silver\": [192, 192, 192],\n    \"skyblue\": [135, 206, 235],\n    \"slateblue\": [106, 90, 205],\n    \"slategray\": [112, 128, 144],\n    \"slategrey\": [112, 128, 144],\n    \"snow\": [255, 250, 250],\n    \"springgreen\": [0, 255, 127],\n    \"steelblue\": [70, 130, 180],\n    \"tan\": [210, 180, 140],\n    \"teal\": [0, 128, 128],\n    \"thistle\": [216, 191, 216],\n    \"tomato\": [255, 99, 71],\n    \"turquoise\": [64, 224, 208],\n    \"violet\": [238, 130, 238],\n    \"wheat\": [245, 222, 179],\n    \"white\": [255, 255, 255],\n    \"whitesmoke\": [245, 245, 245],\n    \"yellow\": [255, 255, 0],\n    \"yellowgreen\": [154, 205, 50]\n  };\n\n  /* MIT license */\n\n  var colorString = {\n    getRgba: getRgba,\n    getHsla: getHsla,\n    getRgb: getRgb,\n    getHsl: getHsl,\n    getHwb: getHwb,\n    getAlpha: getAlpha,\n    hexString: hexString,\n    rgbString: rgbString,\n    rgbaString: rgbaString,\n    percentString: percentString,\n    percentaString: percentaString,\n    hslString: hslString,\n    hslaString: hslaString,\n    hwbString: hwbString,\n    keyword: keyword\n  };\n  function getRgba(string) {\n    if (!string) {\n      return;\n    }\n    var abbr = /^#([a-fA-F0-9]{3,4})$/i,\n      hex = /^#([a-fA-F0-9]{6}([a-fA-F0-9]{2})?)$/i,\n      rgba = /^rgba?\\(\\s*([+-]?\\d+)\\s*,\\s*([+-]?\\d+)\\s*,\\s*([+-]?\\d+)\\s*(?:,\\s*([+-]?[\\d\\.]+)\\s*)?\\)$/i,\n      per = /^rgba?\\(\\s*([+-]?[\\d\\.]+)\\%\\s*,\\s*([+-]?[\\d\\.]+)\\%\\s*,\\s*([+-]?[\\d\\.]+)\\%\\s*(?:,\\s*([+-]?[\\d\\.]+)\\s*)?\\)$/i,\n      keyword = /(\\w+)/;\n    var rgb = [0, 0, 0],\n      a = 1,\n      match = string.match(abbr),\n      hexAlpha = \"\";\n    if (match) {\n      match = match[1];\n      hexAlpha = match[3];\n      for (var i = 0; i < rgb.length; i++) {\n        rgb[i] = parseInt(match[i] + match[i], 16);\n      }\n      if (hexAlpha) {\n        a = Math.round(parseInt(hexAlpha + hexAlpha, 16) / 255 * 100) / 100;\n      }\n    } else if (match = string.match(hex)) {\n      hexAlpha = match[2];\n      match = match[1];\n      for (var i = 0; i < rgb.length; i++) {\n        rgb[i] = parseInt(match.slice(i * 2, i * 2 + 2), 16);\n      }\n      if (hexAlpha) {\n        a = Math.round(parseInt(hexAlpha, 16) / 255 * 100) / 100;\n      }\n    } else if (match = string.match(rgba)) {\n      for (var i = 0; i < rgb.length; i++) {\n        rgb[i] = parseInt(match[i + 1]);\n      }\n      a = parseFloat(match[4]);\n    } else if (match = string.match(per)) {\n      for (var i = 0; i < rgb.length; i++) {\n        rgb[i] = Math.round(parseFloat(match[i + 1]) * 2.55);\n      }\n      a = parseFloat(match[4]);\n    } else if (match = string.match(keyword)) {\n      if (match[1] == \"transparent\") {\n        return [0, 0, 0, 0];\n      }\n      rgb = colorName$1[match[1]];\n      if (!rgb) {\n        return;\n      }\n    }\n    for (var i = 0; i < rgb.length; i++) {\n      rgb[i] = scale(rgb[i], 0, 255);\n    }\n    if (!a && a != 0) {\n      a = 1;\n    } else {\n      a = scale(a, 0, 1);\n    }\n    rgb[3] = a;\n    return rgb;\n  }\n  function getHsla(string) {\n    if (!string) {\n      return;\n    }\n    var hsl = /^hsla?\\(\\s*([+-]?\\d+)(?:deg)?\\s*,\\s*([+-]?[\\d\\.]+)%\\s*,\\s*([+-]?[\\d\\.]+)%\\s*(?:,\\s*([+-]?[\\d\\.]+)\\s*)?\\)/;\n    var match = string.match(hsl);\n    if (match) {\n      var alpha = parseFloat(match[4]);\n      var h = scale(parseInt(match[1]), 0, 360),\n        s = scale(parseFloat(match[2]), 0, 100),\n        l = scale(parseFloat(match[3]), 0, 100),\n        a = scale(isNaN(alpha) ? 1 : alpha, 0, 1);\n      return [h, s, l, a];\n    }\n  }\n  function getHwb(string) {\n    if (!string) {\n      return;\n    }\n    var hwb = /^hwb\\(\\s*([+-]?\\d+)(?:deg)?\\s*,\\s*([+-]?[\\d\\.]+)%\\s*,\\s*([+-]?[\\d\\.]+)%\\s*(?:,\\s*([+-]?[\\d\\.]+)\\s*)?\\)/;\n    var match = string.match(hwb);\n    if (match) {\n      var alpha = parseFloat(match[4]);\n      var h = scale(parseInt(match[1]), 0, 360),\n        w = scale(parseFloat(match[2]), 0, 100),\n        b = scale(parseFloat(match[3]), 0, 100),\n        a = scale(isNaN(alpha) ? 1 : alpha, 0, 1);\n      return [h, w, b, a];\n    }\n  }\n  function getRgb(string) {\n    var rgba = getRgba(string);\n    return rgba && rgba.slice(0, 3);\n  }\n  function getHsl(string) {\n    var hsla = getHsla(string);\n    return hsla && hsla.slice(0, 3);\n  }\n  function getAlpha(string) {\n    var vals = getRgba(string);\n    if (vals) {\n      return vals[3];\n    } else if (vals = getHsla(string)) {\n      return vals[3];\n    } else if (vals = getHwb(string)) {\n      return vals[3];\n    }\n  }\n\n  // generators\n  function hexString(rgba, a) {\n    var a = a !== undefined && rgba.length === 3 ? a : rgba[3];\n    return \"#\" + hexDouble(rgba[0]) + hexDouble(rgba[1]) + hexDouble(rgba[2]) + (a >= 0 && a < 1 ? hexDouble(Math.round(a * 255)) : \"\");\n  }\n  function rgbString(rgba, alpha) {\n    if (alpha < 1 || rgba[3] && rgba[3] < 1) {\n      return rgbaString(rgba, alpha);\n    }\n    return \"rgb(\" + rgba[0] + \", \" + rgba[1] + \", \" + rgba[2] + \")\";\n  }\n  function rgbaString(rgba, alpha) {\n    if (alpha === undefined) {\n      alpha = rgba[3] !== undefined ? rgba[3] : 1;\n    }\n    return \"rgba(\" + rgba[0] + \", \" + rgba[1] + \", \" + rgba[2] + \", \" + alpha + \")\";\n  }\n  function percentString(rgba, alpha) {\n    if (alpha < 1 || rgba[3] && rgba[3] < 1) {\n      return percentaString(rgba, alpha);\n    }\n    var r = Math.round(rgba[0] / 255 * 100),\n      g = Math.round(rgba[1] / 255 * 100),\n      b = Math.round(rgba[2] / 255 * 100);\n    return \"rgb(\" + r + \"%, \" + g + \"%, \" + b + \"%)\";\n  }\n  function percentaString(rgba, alpha) {\n    var r = Math.round(rgba[0] / 255 * 100),\n      g = Math.round(rgba[1] / 255 * 100),\n      b = Math.round(rgba[2] / 255 * 100);\n    return \"rgba(\" + r + \"%, \" + g + \"%, \" + b + \"%, \" + (alpha || rgba[3] || 1) + \")\";\n  }\n  function hslString(hsla, alpha) {\n    if (alpha < 1 || hsla[3] && hsla[3] < 1) {\n      return hslaString(hsla, alpha);\n    }\n    return \"hsl(\" + hsla[0] + \", \" + hsla[1] + \"%, \" + hsla[2] + \"%)\";\n  }\n  function hslaString(hsla, alpha) {\n    if (alpha === undefined) {\n      alpha = hsla[3] !== undefined ? hsla[3] : 1;\n    }\n    return \"hsla(\" + hsla[0] + \", \" + hsla[1] + \"%, \" + hsla[2] + \"%, \" + alpha + \")\";\n  }\n\n  // hwb is a bit different than rgb(a) & hsl(a) since there is no alpha specific syntax\n  // (hwb have alpha optional & 1 is default value)\n  function hwbString(hwb, alpha) {\n    if (alpha === undefined) {\n      alpha = hwb[3] !== undefined ? hwb[3] : 1;\n    }\n    return \"hwb(\" + hwb[0] + \", \" + hwb[1] + \"%, \" + hwb[2] + \"%\" + (alpha !== undefined && alpha !== 1 ? \", \" + alpha : \"\") + \")\";\n  }\n  function keyword(rgb) {\n    return reverseNames[rgb.slice(0, 3)];\n  }\n\n  // helpers\n  function scale(num, min, max) {\n    return Math.min(Math.max(min, num), max);\n  }\n  function hexDouble(num) {\n    var str = num.toString(16).toUpperCase();\n    return str.length < 2 ? \"0\" + str : str;\n  }\n\n  //create a list of reverse color names\n  var reverseNames = {};\n  for (var name in colorName$1) {\n    reverseNames[colorName$1[name]] = name;\n  }\n\n  /* MIT license */\n\n  var Color = function (obj) {\n    if (obj instanceof Color) {\n      return obj;\n    }\n    if (!(this instanceof Color)) {\n      return new Color(obj);\n    }\n    this.valid = false;\n    this.values = {\n      rgb: [0, 0, 0],\n      hsl: [0, 0, 0],\n      hsv: [0, 0, 0],\n      hwb: [0, 0, 0],\n      cmyk: [0, 0, 0, 0],\n      alpha: 1\n    };\n\n    // parse Color() argument\n    var vals;\n    if (typeof obj === 'string') {\n      vals = colorString.getRgba(obj);\n      if (vals) {\n        this.setValues('rgb', vals);\n      } else if (vals = colorString.getHsla(obj)) {\n        this.setValues('hsl', vals);\n      } else if (vals = colorString.getHwb(obj)) {\n        this.setValues('hwb', vals);\n      }\n    } else if (typeof obj === 'object') {\n      vals = obj;\n      if (vals.r !== undefined || vals.red !== undefined) {\n        this.setValues('rgb', vals);\n      } else if (vals.l !== undefined || vals.lightness !== undefined) {\n        this.setValues('hsl', vals);\n      } else if (vals.v !== undefined || vals.value !== undefined) {\n        this.setValues('hsv', vals);\n      } else if (vals.w !== undefined || vals.whiteness !== undefined) {\n        this.setValues('hwb', vals);\n      } else if (vals.c !== undefined || vals.cyan !== undefined) {\n        this.setValues('cmyk', vals);\n      }\n    }\n  };\n  Color.prototype = {\n    isValid: function () {\n      return this.valid;\n    },\n    rgb: function () {\n      return this.setSpace('rgb', arguments);\n    },\n    hsl: function () {\n      return this.setSpace('hsl', arguments);\n    },\n    hsv: function () {\n      return this.setSpace('hsv', arguments);\n    },\n    hwb: function () {\n      return this.setSpace('hwb', arguments);\n    },\n    cmyk: function () {\n      return this.setSpace('cmyk', arguments);\n    },\n    rgbArray: function () {\n      return this.values.rgb;\n    },\n    hslArray: function () {\n      return this.values.hsl;\n    },\n    hsvArray: function () {\n      return this.values.hsv;\n    },\n    hwbArray: function () {\n      var values = this.values;\n      if (values.alpha !== 1) {\n        return values.hwb.concat([values.alpha]);\n      }\n      return values.hwb;\n    },\n    cmykArray: function () {\n      return this.values.cmyk;\n    },\n    rgbaArray: function () {\n      var values = this.values;\n      return values.rgb.concat([values.alpha]);\n    },\n    hslaArray: function () {\n      var values = this.values;\n      return values.hsl.concat([values.alpha]);\n    },\n    alpha: function (val) {\n      if (val === undefined) {\n        return this.values.alpha;\n      }\n      this.setValues('alpha', val);\n      return this;\n    },\n    red: function (val) {\n      return this.setChannel('rgb', 0, val);\n    },\n    green: function (val) {\n      return this.setChannel('rgb', 1, val);\n    },\n    blue: function (val) {\n      return this.setChannel('rgb', 2, val);\n    },\n    hue: function (val) {\n      if (val) {\n        val %= 360;\n        val = val < 0 ? 360 + val : val;\n      }\n      return this.setChannel('hsl', 0, val);\n    },\n    saturation: function (val) {\n      return this.setChannel('hsl', 1, val);\n    },\n    lightness: function (val) {\n      return this.setChannel('hsl', 2, val);\n    },\n    saturationv: function (val) {\n      return this.setChannel('hsv', 1, val);\n    },\n    whiteness: function (val) {\n      return this.setChannel('hwb', 1, val);\n    },\n    blackness: function (val) {\n      return this.setChannel('hwb', 2, val);\n    },\n    value: function (val) {\n      return this.setChannel('hsv', 2, val);\n    },\n    cyan: function (val) {\n      return this.setChannel('cmyk', 0, val);\n    },\n    magenta: function (val) {\n      return this.setChannel('cmyk', 1, val);\n    },\n    yellow: function (val) {\n      return this.setChannel('cmyk', 2, val);\n    },\n    black: function (val) {\n      return this.setChannel('cmyk', 3, val);\n    },\n    hexString: function () {\n      return colorString.hexString(this.values.rgb);\n    },\n    rgbString: function () {\n      return colorString.rgbString(this.values.rgb, this.values.alpha);\n    },\n    rgbaString: function () {\n      return colorString.rgbaString(this.values.rgb, this.values.alpha);\n    },\n    percentString: function () {\n      return colorString.percentString(this.values.rgb, this.values.alpha);\n    },\n    hslString: function () {\n      return colorString.hslString(this.values.hsl, this.values.alpha);\n    },\n    hslaString: function () {\n      return colorString.hslaString(this.values.hsl, this.values.alpha);\n    },\n    hwbString: function () {\n      return colorString.hwbString(this.values.hwb, this.values.alpha);\n    },\n    keyword: function () {\n      return colorString.keyword(this.values.rgb, this.values.alpha);\n    },\n    rgbNumber: function () {\n      var rgb = this.values.rgb;\n      return rgb[0] << 16 | rgb[1] << 8 | rgb[2];\n    },\n    luminosity: function () {\n      // http://www.w3.org/TR/WCAG20/#relativeluminancedef\n      var rgb = this.values.rgb;\n      var lum = [];\n      for (var i = 0; i < rgb.length; i++) {\n        var chan = rgb[i] / 255;\n        lum[i] = chan <= 0.03928 ? chan / 12.92 : Math.pow((chan + 0.055) / 1.055, 2.4);\n      }\n      return 0.2126 * lum[0] + 0.7152 * lum[1] + 0.0722 * lum[2];\n    },\n    contrast: function (color2) {\n      // http://www.w3.org/TR/WCAG20/#contrast-ratiodef\n      var lum1 = this.luminosity();\n      var lum2 = color2.luminosity();\n      if (lum1 > lum2) {\n        return (lum1 + 0.05) / (lum2 + 0.05);\n      }\n      return (lum2 + 0.05) / (lum1 + 0.05);\n    },\n    level: function (color2) {\n      var contrastRatio = this.contrast(color2);\n      if (contrastRatio >= 7.1) {\n        return 'AAA';\n      }\n      return contrastRatio >= 4.5 ? 'AA' : '';\n    },\n    dark: function () {\n      // YIQ equation from http://24ways.org/2010/calculating-color-contrast\n      var rgb = this.values.rgb;\n      var yiq = (rgb[0] * 299 + rgb[1] * 587 + rgb[2] * 114) / 1000;\n      return yiq < 128;\n    },\n    light: function () {\n      return !this.dark();\n    },\n    negate: function () {\n      var rgb = [];\n      for (var i = 0; i < 3; i++) {\n        rgb[i] = 255 - this.values.rgb[i];\n      }\n      this.setValues('rgb', rgb);\n      return this;\n    },\n    lighten: function (ratio) {\n      var hsl = this.values.hsl;\n      hsl[2] += hsl[2] * ratio;\n      this.setValues('hsl', hsl);\n      return this;\n    },\n    darken: function (ratio) {\n      var hsl = this.values.hsl;\n      hsl[2] -= hsl[2] * ratio;\n      this.setValues('hsl', hsl);\n      return this;\n    },\n    saturate: function (ratio) {\n      var hsl = this.values.hsl;\n      hsl[1] += hsl[1] * ratio;\n      this.setValues('hsl', hsl);\n      return this;\n    },\n    desaturate: function (ratio) {\n      var hsl = this.values.hsl;\n      hsl[1] -= hsl[1] * ratio;\n      this.setValues('hsl', hsl);\n      return this;\n    },\n    whiten: function (ratio) {\n      var hwb = this.values.hwb;\n      hwb[1] += hwb[1] * ratio;\n      this.setValues('hwb', hwb);\n      return this;\n    },\n    blacken: function (ratio) {\n      var hwb = this.values.hwb;\n      hwb[2] += hwb[2] * ratio;\n      this.setValues('hwb', hwb);\n      return this;\n    },\n    greyscale: function () {\n      var rgb = this.values.rgb;\n      // http://en.wikipedia.org/wiki/Grayscale#Converting_color_to_grayscale\n      var val = rgb[0] * 0.3 + rgb[1] * 0.59 + rgb[2] * 0.11;\n      this.setValues('rgb', [val, val, val]);\n      return this;\n    },\n    clearer: function (ratio) {\n      var alpha = this.values.alpha;\n      this.setValues('alpha', alpha - alpha * ratio);\n      return this;\n    },\n    opaquer: function (ratio) {\n      var alpha = this.values.alpha;\n      this.setValues('alpha', alpha + alpha * ratio);\n      return this;\n    },\n    rotate: function (degrees) {\n      var hsl = this.values.hsl;\n      var hue = (hsl[0] + degrees) % 360;\n      hsl[0] = hue < 0 ? 360 + hue : hue;\n      this.setValues('hsl', hsl);\n      return this;\n    },\n    /**\n     * Ported from sass implementation in C\n     * https://github.com/sass/libsass/blob/0e6b4a2850092356aa3ece07c6b249f0221caced/functions.cpp#L209\n     */\n    mix: function (mixinColor, weight) {\n      var color1 = this;\n      var color2 = mixinColor;\n      var p = weight === undefined ? 0.5 : weight;\n      var w = 2 * p - 1;\n      var a = color1.alpha() - color2.alpha();\n      var w1 = ((w * a === -1 ? w : (w + a) / (1 + w * a)) + 1) / 2.0;\n      var w2 = 1 - w1;\n      return this.rgb(w1 * color1.red() + w2 * color2.red(), w1 * color1.green() + w2 * color2.green(), w1 * color1.blue() + w2 * color2.blue()).alpha(color1.alpha() * p + color2.alpha() * (1 - p));\n    },\n    toJSON: function () {\n      return this.rgb();\n    },\n    clone: function () {\n      // NOTE(SB): using node-clone creates a dependency to Buffer when using browserify,\n      // making the final build way to big to embed in Chart.js. So let's do it manually,\n      // assuming that values to clone are 1 dimension arrays containing only numbers,\n      // except 'alpha' which is a number.\n      var result = new Color();\n      var source = this.values;\n      var target = result.values;\n      var value, type;\n      for (var prop in source) {\n        if (source.hasOwnProperty(prop)) {\n          value = source[prop];\n          type = {}.toString.call(value);\n          if (type === '[object Array]') {\n            target[prop] = value.slice(0);\n          } else if (type === '[object Number]') {\n            target[prop] = value;\n          } else {\n            console.error('unexpected color value:', value);\n          }\n        }\n      }\n      return result;\n    }\n  };\n  Color.prototype.spaces = {\n    rgb: ['red', 'green', 'blue'],\n    hsl: ['hue', 'saturation', 'lightness'],\n    hsv: ['hue', 'saturation', 'value'],\n    hwb: ['hue', 'whiteness', 'blackness'],\n    cmyk: ['cyan', 'magenta', 'yellow', 'black']\n  };\n  Color.prototype.maxes = {\n    rgb: [255, 255, 255],\n    hsl: [360, 100, 100],\n    hsv: [360, 100, 100],\n    hwb: [360, 100, 100],\n    cmyk: [100, 100, 100, 100]\n  };\n  Color.prototype.getValues = function (space) {\n    var values = this.values;\n    var vals = {};\n    for (var i = 0; i < space.length; i++) {\n      vals[space.charAt(i)] = values[space][i];\n    }\n    if (values.alpha !== 1) {\n      vals.a = values.alpha;\n    }\n\n    // {r: 255, g: 255, b: 255, a: 0.4}\n    return vals;\n  };\n  Color.prototype.setValues = function (space, vals) {\n    var values = this.values;\n    var spaces = this.spaces;\n    var maxes = this.maxes;\n    var alpha = 1;\n    var i;\n    this.valid = true;\n    if (space === 'alpha') {\n      alpha = vals;\n    } else if (vals.length) {\n      // [10, 10, 10]\n      values[space] = vals.slice(0, space.length);\n      alpha = vals[space.length];\n    } else if (vals[space.charAt(0)] !== undefined) {\n      // {r: 10, g: 10, b: 10}\n      for (i = 0; i < space.length; i++) {\n        values[space][i] = vals[space.charAt(i)];\n      }\n      alpha = vals.a;\n    } else if (vals[spaces[space][0]] !== undefined) {\n      // {red: 10, green: 10, blue: 10}\n      var chans = spaces[space];\n      for (i = 0; i < space.length; i++) {\n        values[space][i] = vals[chans[i]];\n      }\n      alpha = vals.alpha;\n    }\n    values.alpha = Math.max(0, Math.min(1, alpha === undefined ? values.alpha : alpha));\n    if (space === 'alpha') {\n      return false;\n    }\n    var capped;\n\n    // cap values of the space prior converting all values\n    for (i = 0; i < space.length; i++) {\n      capped = Math.max(0, Math.min(maxes[space][i], values[space][i]));\n      values[space][i] = Math.round(capped);\n    }\n\n    // convert to all the other color spaces\n    for (var sname in spaces) {\n      if (sname !== space) {\n        values[sname] = colorConvert[space][sname](values[space]);\n      }\n    }\n    return true;\n  };\n  Color.prototype.setSpace = function (space, args) {\n    var vals = args[0];\n    if (vals === undefined) {\n      // color.rgb()\n      return this.getValues(space);\n    }\n\n    // color.rgb(10, 10, 10)\n    if (typeof vals === 'number') {\n      vals = Array.prototype.slice.call(args);\n    }\n    this.setValues(space, vals);\n    return this;\n  };\n  Color.prototype.setChannel = function (space, index, val) {\n    var svalues = this.values[space];\n    if (val === undefined) {\n      // color.red()\n      return svalues[index];\n    } else if (val === svalues[index]) {\n      // color.red(color.red())\n      return this;\n    }\n\n    // color.red(100)\n    svalues[index] = val;\n    this.setValues(space, svalues);\n    return this;\n  };\n  if (typeof window !== 'undefined') {\n    window.Color = Color;\n  }\n  var chartjsColor = Color;\n  function isValidKey(key) {\n    return ['__proto__', 'prototype', 'constructor'].indexOf(key) === -1;\n  }\n\n  /**\r\n   * @namespace Chart.helpers\r\n   */\n  var helpers = {\n    /**\r\n     * An empty function that can be used, for example, for optional callback.\r\n     */\n    noop: function () {},\n    /**\r\n     * Returns a unique id, sequentially generated from a global variable.\r\n     * @returns {number}\r\n     * @function\r\n     */\n    uid: function () {\n      var id = 0;\n      return function () {\n        return id++;\n      };\n    }(),\n    /**\r\n     * Returns true if `value` is neither null nor undefined, else returns false.\r\n     * @param {*} value - The value to test.\r\n     * @returns {boolean}\r\n     * @since 2.7.0\r\n     */\n    isNullOrUndef: function (value) {\n      return value === null || typeof value === 'undefined';\n    },\n    /**\r\n     * Returns true if `value` is an array (including typed arrays), else returns false.\r\n     * @param {*} value - The value to test.\r\n     * @returns {boolean}\r\n     * @function\r\n     */\n    isArray: function (value) {\n      if (Array.isArray && Array.isArray(value)) {\n        return true;\n      }\n      var type = Object.prototype.toString.call(value);\n      if (type.substr(0, 7) === '[object' && type.substr(-6) === 'Array]') {\n        return true;\n      }\n      return false;\n    },\n    /**\r\n     * Returns true if `value` is an object (excluding null), else returns false.\r\n     * @param {*} value - The value to test.\r\n     * @returns {boolean}\r\n     * @since 2.7.0\r\n     */\n    isObject: function (value) {\n      return value !== null && Object.prototype.toString.call(value) === '[object Object]';\n    },\n    /**\r\n     * Returns true if `value` is a finite number, else returns false\r\n     * @param {*} value  - The value to test.\r\n     * @returns {boolean}\r\n     */\n    isFinite: function (value) {\n      return (typeof value === 'number' || value instanceof Number) && isFinite(value);\n    },\n    /**\r\n     * Returns `value` if defined, else returns `defaultValue`.\r\n     * @param {*} value - The value to return if defined.\r\n     * @param {*} defaultValue - The value to return if `value` is undefined.\r\n     * @returns {*}\r\n     */\n    valueOrDefault: function (value, defaultValue) {\n      return typeof value === 'undefined' ? defaultValue : value;\n    },\n    /**\r\n     * Returns value at the given `index` in array if defined, else returns `defaultValue`.\r\n     * @param {Array} value - The array to lookup for value at `index`.\r\n     * @param {number} index - The index in `value` to lookup for value.\r\n     * @param {*} defaultValue - The value to return if `value[index]` is undefined.\r\n     * @returns {*}\r\n     */\n    valueAtIndexOrDefault: function (value, index, defaultValue) {\n      return helpers.valueOrDefault(helpers.isArray(value) ? value[index] : value, defaultValue);\n    },\n    /**\r\n     * Calls `fn` with the given `args` in the scope defined by `thisArg` and returns the\r\n     * value returned by `fn`. If `fn` is not a function, this method returns undefined.\r\n     * @param {function} fn - The function to call.\r\n     * @param {Array|undefined|null} args - The arguments with which `fn` should be called.\r\n     * @param {object} [thisArg] - The value of `this` provided for the call to `fn`.\r\n     * @returns {*}\r\n     */\n    callback: function (fn, args, thisArg) {\n      if (fn && typeof fn.call === 'function') {\n        return fn.apply(thisArg, args);\n      }\n    },\n    /**\r\n     * Note(SB) for performance sake, this method should only be used when loopable type\r\n     * is unknown or in none intensive code (not called often and small loopable). Else\r\n     * it's preferable to use a regular for() loop and save extra function calls.\r\n     * @param {object|Array} loopable - The object or array to be iterated.\r\n     * @param {function} fn - The function to call for each item.\r\n     * @param {object} [thisArg] - The value of `this` provided for the call to `fn`.\r\n     * @param {boolean} [reverse] - If true, iterates backward on the loopable.\r\n     */\n    each: function (loopable, fn, thisArg, reverse) {\n      var i, len, keys;\n      if (helpers.isArray(loopable)) {\n        len = loopable.length;\n        if (reverse) {\n          for (i = len - 1; i >= 0; i--) {\n            fn.call(thisArg, loopable[i], i);\n          }\n        } else {\n          for (i = 0; i < len; i++) {\n            fn.call(thisArg, loopable[i], i);\n          }\n        }\n      } else if (helpers.isObject(loopable)) {\n        keys = Object.keys(loopable);\n        len = keys.length;\n        for (i = 0; i < len; i++) {\n          fn.call(thisArg, loopable[keys[i]], keys[i]);\n        }\n      }\n    },\n    /**\r\n     * Returns true if the `a0` and `a1` arrays have the same content, else returns false.\r\n     * @see https://stackoverflow.com/a/14853974\r\n     * @param {Array} a0 - The array to compare\r\n     * @param {Array} a1 - The array to compare\r\n     * @returns {boolean}\r\n     */\n    arrayEquals: function (a0, a1) {\n      var i, ilen, v0, v1;\n      if (!a0 || !a1 || a0.length !== a1.length) {\n        return false;\n      }\n      for (i = 0, ilen = a0.length; i < ilen; ++i) {\n        v0 = a0[i];\n        v1 = a1[i];\n        if (v0 instanceof Array && v1 instanceof Array) {\n          if (!helpers.arrayEquals(v0, v1)) {\n            return false;\n          }\n        } else if (v0 !== v1) {\n          // NOTE: two different object instances will never be equal: {x:20} != {x:20}\n          return false;\n        }\n      }\n      return true;\n    },\n    /**\r\n     * Returns a deep copy of `source` without keeping references on objects and arrays.\r\n     * @param {*} source - The value to clone.\r\n     * @returns {*}\r\n     */\n    clone: function (source) {\n      if (helpers.isArray(source)) {\n        return source.map(helpers.clone);\n      }\n      if (helpers.isObject(source)) {\n        var target = Object.create(source);\n        var keys = Object.keys(source);\n        var klen = keys.length;\n        var k = 0;\n        for (; k < klen; ++k) {\n          target[keys[k]] = helpers.clone(source[keys[k]]);\n        }\n        return target;\n      }\n      return source;\n    },\n    /**\r\n     * The default merger when Chart.helpers.merge is called without merger option.\r\n     * Note(SB): also used by mergeConfig and mergeScaleConfig as fallback.\r\n     * @private\r\n     */\n    _merger: function (key, target, source, options) {\n      if (!isValidKey(key)) {\n        // We want to ensure we do not copy prototypes over\n        // as this can pollute global namespaces\n        return;\n      }\n      var tval = target[key];\n      var sval = source[key];\n      if (helpers.isObject(tval) && helpers.isObject(sval)) {\n        helpers.merge(tval, sval, options);\n      } else {\n        target[key] = helpers.clone(sval);\n      }\n    },\n    /**\r\n     * Merges source[key] in target[key] only if target[key] is undefined.\r\n     * @private\r\n     */\n    _mergerIf: function (key, target, source) {\n      if (!isValidKey(key)) {\n        // We want to ensure we do not copy prototypes over\n        // as this can pollute global namespaces\n        return;\n      }\n      var tval = target[key];\n      var sval = source[key];\n      if (helpers.isObject(tval) && helpers.isObject(sval)) {\n        helpers.mergeIf(tval, sval);\n      } else if (!target.hasOwnProperty(key)) {\n        target[key] = helpers.clone(sval);\n      }\n    },\n    /**\r\n     * Recursively deep copies `source` properties into `target` with the given `options`.\r\n     * IMPORTANT: `target` is not cloned and will be updated with `source` properties.\r\n     * @param {object} target - The target object in which all sources are merged into.\r\n     * @param {object|object[]} source - Object(s) to merge into `target`.\r\n     * @param {object} [options] - Merging options:\r\n     * @param {function} [options.merger] - The merge method (key, target, source, options)\r\n     * @returns {object} The `target` object.\r\n     */\n    merge: function (target, source, options) {\n      var sources = helpers.isArray(source) ? source : [source];\n      var ilen = sources.length;\n      var merge, i, keys, klen, k;\n      if (!helpers.isObject(target)) {\n        return target;\n      }\n      options = options || {};\n      merge = options.merger || helpers._merger;\n      for (i = 0; i < ilen; ++i) {\n        source = sources[i];\n        if (!helpers.isObject(source)) {\n          continue;\n        }\n        keys = Object.keys(source);\n        for (k = 0, klen = keys.length; k < klen; ++k) {\n          merge(keys[k], target, source, options);\n        }\n      }\n      return target;\n    },\n    /**\r\n     * Recursively deep copies `source` properties into `target` *only* if not defined in target.\r\n     * IMPORTANT: `target` is not cloned and will be updated with `source` properties.\r\n     * @param {object} target - The target object in which all sources are merged into.\r\n     * @param {object|object[]} source - Object(s) to merge into `target`.\r\n     * @returns {object} The `target` object.\r\n     */\n    mergeIf: function (target, source) {\n      return helpers.merge(target, source, {\n        merger: helpers._mergerIf\n      });\n    },\n    /**\r\n     * Applies the contents of two or more objects together into the first object.\r\n     * @param {object} target - The target object in which all objects are merged into.\r\n     * @param {object} arg1 - Object containing additional properties to merge in target.\r\n     * @param {object} argN - Additional objects containing properties to merge in target.\r\n     * @returns {object} The `target` object.\r\n     */\n    extend: Object.assign || function (target) {\n      return helpers.merge(target, [].slice.call(arguments, 1), {\n        merger: function (key, dst, src) {\n          dst[key] = src[key];\n        }\n      });\n    },\n    /**\r\n     * Basic javascript inheritance based on the model created in Backbone.js\r\n     */\n    inherits: function (extensions) {\n      var me = this;\n      var ChartElement = extensions && extensions.hasOwnProperty('constructor') ? extensions.constructor : function () {\n        return me.apply(this, arguments);\n      };\n      var Surrogate = function () {\n        this.constructor = ChartElement;\n      };\n      Surrogate.prototype = me.prototype;\n      ChartElement.prototype = new Surrogate();\n      ChartElement.extend = helpers.inherits;\n      if (extensions) {\n        helpers.extend(ChartElement.prototype, extensions);\n      }\n      ChartElement.__super__ = me.prototype;\n      return ChartElement;\n    },\n    _deprecated: function (scope, value, previous, current) {\n      if (value !== undefined) {\n        console.warn(scope + ': \"' + previous + '\" is deprecated. Please use \"' + current + '\" instead');\n      }\n    }\n  };\n  var helpers_core = helpers;\n\n  // DEPRECATIONS\n\n  /**\r\n   * Provided for backward compatibility, use Chart.helpers.callback instead.\r\n   * @function Chart.helpers.callCallback\r\n   * @deprecated since version 2.6.0\r\n   * @todo remove at version 3\r\n   * @private\r\n   */\n  helpers.callCallback = helpers.callback;\n\n  /**\r\n   * Provided for backward compatibility, use Array.prototype.indexOf instead.\r\n   * Array.prototype.indexOf compatibility: Chrome, Opera, Safari, FF1.5+, IE9+\r\n   * @function Chart.helpers.indexOf\r\n   * @deprecated since version 2.7.0\r\n   * @todo remove at version 3\r\n   * @private\r\n   */\n  helpers.indexOf = function (array, item, fromIndex) {\n    return Array.prototype.indexOf.call(array, item, fromIndex);\n  };\n\n  /**\r\n   * Provided for backward compatibility, use Chart.helpers.valueOrDefault instead.\r\n   * @function Chart.helpers.getValueOrDefault\r\n   * @deprecated since version 2.7.0\r\n   * @todo remove at version 3\r\n   * @private\r\n   */\n  helpers.getValueOrDefault = helpers.valueOrDefault;\n\n  /**\r\n   * Provided for backward compatibility, use Chart.helpers.valueAtIndexOrDefault instead.\r\n   * @function Chart.helpers.getValueAtIndexOrDefault\r\n   * @deprecated since version 2.7.0\r\n   * @todo remove at version 3\r\n   * @private\r\n   */\n  helpers.getValueAtIndexOrDefault = helpers.valueAtIndexOrDefault;\n\n  /**\r\n   * Easing functions adapted from Robert Penner's easing equations.\r\n   * @namespace Chart.helpers.easingEffects\r\n   * @see http://www.robertpenner.com/easing/\r\n   */\n  var effects = {\n    linear: function (t) {\n      return t;\n    },\n    easeInQuad: function (t) {\n      return t * t;\n    },\n    easeOutQuad: function (t) {\n      return -t * (t - 2);\n    },\n    easeInOutQuad: function (t) {\n      if ((t /= 0.5) < 1) {\n        return 0.5 * t * t;\n      }\n      return -0.5 * (--t * (t - 2) - 1);\n    },\n    easeInCubic: function (t) {\n      return t * t * t;\n    },\n    easeOutCubic: function (t) {\n      return (t = t - 1) * t * t + 1;\n    },\n    easeInOutCubic: function (t) {\n      if ((t /= 0.5) < 1) {\n        return 0.5 * t * t * t;\n      }\n      return 0.5 * ((t -= 2) * t * t + 2);\n    },\n    easeInQuart: function (t) {\n      return t * t * t * t;\n    },\n    easeOutQuart: function (t) {\n      return -((t = t - 1) * t * t * t - 1);\n    },\n    easeInOutQuart: function (t) {\n      if ((t /= 0.5) < 1) {\n        return 0.5 * t * t * t * t;\n      }\n      return -0.5 * ((t -= 2) * t * t * t - 2);\n    },\n    easeInQuint: function (t) {\n      return t * t * t * t * t;\n    },\n    easeOutQuint: function (t) {\n      return (t = t - 1) * t * t * t * t + 1;\n    },\n    easeInOutQuint: function (t) {\n      if ((t /= 0.5) < 1) {\n        return 0.5 * t * t * t * t * t;\n      }\n      return 0.5 * ((t -= 2) * t * t * t * t + 2);\n    },\n    easeInSine: function (t) {\n      return -Math.cos(t * (Math.PI / 2)) + 1;\n    },\n    easeOutSine: function (t) {\n      return Math.sin(t * (Math.PI / 2));\n    },\n    easeInOutSine: function (t) {\n      return -0.5 * (Math.cos(Math.PI * t) - 1);\n    },\n    easeInExpo: function (t) {\n      return t === 0 ? 0 : Math.pow(2, 10 * (t - 1));\n    },\n    easeOutExpo: function (t) {\n      return t === 1 ? 1 : -Math.pow(2, -10 * t) + 1;\n    },\n    easeInOutExpo: function (t) {\n      if (t === 0) {\n        return 0;\n      }\n      if (t === 1) {\n        return 1;\n      }\n      if ((t /= 0.5) < 1) {\n        return 0.5 * Math.pow(2, 10 * (t - 1));\n      }\n      return 0.5 * (-Math.pow(2, -10 * --t) + 2);\n    },\n    easeInCirc: function (t) {\n      if (t >= 1) {\n        return t;\n      }\n      return -(Math.sqrt(1 - t * t) - 1);\n    },\n    easeOutCirc: function (t) {\n      return Math.sqrt(1 - (t = t - 1) * t);\n    },\n    easeInOutCirc: function (t) {\n      if ((t /= 0.5) < 1) {\n        return -0.5 * (Math.sqrt(1 - t * t) - 1);\n      }\n      return 0.5 * (Math.sqrt(1 - (t -= 2) * t) + 1);\n    },\n    easeInElastic: function (t) {\n      var s = 1.70158;\n      var p = 0;\n      var a = 1;\n      if (t === 0) {\n        return 0;\n      }\n      if (t === 1) {\n        return 1;\n      }\n      if (!p) {\n        p = 0.3;\n      }\n      if (a < 1) {\n        a = 1;\n        s = p / 4;\n      } else {\n        s = p / (2 * Math.PI) * Math.asin(1 / a);\n      }\n      return -(a * Math.pow(2, 10 * (t -= 1)) * Math.sin((t - s) * (2 * Math.PI) / p));\n    },\n    easeOutElastic: function (t) {\n      var s = 1.70158;\n      var p = 0;\n      var a = 1;\n      if (t === 0) {\n        return 0;\n      }\n      if (t === 1) {\n        return 1;\n      }\n      if (!p) {\n        p = 0.3;\n      }\n      if (a < 1) {\n        a = 1;\n        s = p / 4;\n      } else {\n        s = p / (2 * Math.PI) * Math.asin(1 / a);\n      }\n      return a * Math.pow(2, -10 * t) * Math.sin((t - s) * (2 * Math.PI) / p) + 1;\n    },\n    easeInOutElastic: function (t) {\n      var s = 1.70158;\n      var p = 0;\n      var a = 1;\n      if (t === 0) {\n        return 0;\n      }\n      if ((t /= 0.5) === 2) {\n        return 1;\n      }\n      if (!p) {\n        p = 0.45;\n      }\n      if (a < 1) {\n        a = 1;\n        s = p / 4;\n      } else {\n        s = p / (2 * Math.PI) * Math.asin(1 / a);\n      }\n      if (t < 1) {\n        return -0.5 * (a * Math.pow(2, 10 * (t -= 1)) * Math.sin((t - s) * (2 * Math.PI) / p));\n      }\n      return a * Math.pow(2, -10 * (t -= 1)) * Math.sin((t - s) * (2 * Math.PI) / p) * 0.5 + 1;\n    },\n    easeInBack: function (t) {\n      var s = 1.70158;\n      return t * t * ((s + 1) * t - s);\n    },\n    easeOutBack: function (t) {\n      var s = 1.70158;\n      return (t = t - 1) * t * ((s + 1) * t + s) + 1;\n    },\n    easeInOutBack: function (t) {\n      var s = 1.70158;\n      if ((t /= 0.5) < 1) {\n        return 0.5 * (t * t * (((s *= 1.525) + 1) * t - s));\n      }\n      return 0.5 * ((t -= 2) * t * (((s *= 1.525) + 1) * t + s) + 2);\n    },\n    easeInBounce: function (t) {\n      return 1 - effects.easeOutBounce(1 - t);\n    },\n    easeOutBounce: function (t) {\n      if (t < 1 / 2.75) {\n        return 7.5625 * t * t;\n      }\n      if (t < 2 / 2.75) {\n        return 7.5625 * (t -= 1.5 / 2.75) * t + 0.75;\n      }\n      if (t < 2.5 / 2.75) {\n        return 7.5625 * (t -= 2.25 / 2.75) * t + 0.9375;\n      }\n      return 7.5625 * (t -= 2.625 / 2.75) * t + 0.984375;\n    },\n    easeInOutBounce: function (t) {\n      if (t < 0.5) {\n        return effects.easeInBounce(t * 2) * 0.5;\n      }\n      return effects.easeOutBounce(t * 2 - 1) * 0.5 + 0.5;\n    }\n  };\n  var helpers_easing = {\n    effects: effects\n  };\n\n  // DEPRECATIONS\n\n  /**\r\n   * Provided for backward compatibility, use Chart.helpers.easing.effects instead.\r\n   * @function Chart.helpers.easingEffects\r\n   * @deprecated since version 2.7.0\r\n   * @todo remove at version 3\r\n   * @private\r\n   */\n  helpers_core.easingEffects = effects;\n  var PI = Math.PI;\n  var RAD_PER_DEG = PI / 180;\n  var DOUBLE_PI = PI * 2;\n  var HALF_PI = PI / 2;\n  var QUARTER_PI = PI / 4;\n  var TWO_THIRDS_PI = PI * 2 / 3;\n\n  /**\r\n   * @namespace Chart.helpers.canvas\r\n   */\n  var exports$1 = {\n    /**\r\n     * Clears the entire canvas associated to the given `chart`.\r\n     * @param {Chart} chart - The chart for which to clear the canvas.\r\n     */\n    clear: function (chart) {\n      chart.ctx.clearRect(0, 0, chart.width, chart.height);\n    },\n    /**\r\n     * Creates a \"path\" for a rectangle with rounded corners at position (x, y) with a\r\n     * given size (width, height) and the same `radius` for all corners.\r\n     * @param {CanvasRenderingContext2D} ctx - The canvas 2D Context.\r\n     * @param {number} x - The x axis of the coordinate for the rectangle starting point.\r\n     * @param {number} y - The y axis of the coordinate for the rectangle starting point.\r\n     * @param {number} width - The rectangle's width.\r\n     * @param {number} height - The rectangle's height.\r\n     * @param {number} radius - The rounded amount (in pixels) for the four corners.\r\n     * @todo handle `radius` as top-left, top-right, bottom-right, bottom-left array/object?\r\n     */\n    roundedRect: function (ctx, x, y, width, height, radius) {\n      if (radius) {\n        var r = Math.min(radius, height / 2, width / 2);\n        var left = x + r;\n        var top = y + r;\n        var right = x + width - r;\n        var bottom = y + height - r;\n        ctx.moveTo(x, top);\n        if (left < right && top < bottom) {\n          ctx.arc(left, top, r, -PI, -HALF_PI);\n          ctx.arc(right, top, r, -HALF_PI, 0);\n          ctx.arc(right, bottom, r, 0, HALF_PI);\n          ctx.arc(left, bottom, r, HALF_PI, PI);\n        } else if (left < right) {\n          ctx.moveTo(left, y);\n          ctx.arc(right, top, r, -HALF_PI, HALF_PI);\n          ctx.arc(left, top, r, HALF_PI, PI + HALF_PI);\n        } else if (top < bottom) {\n          ctx.arc(left, top, r, -PI, 0);\n          ctx.arc(left, bottom, r, 0, PI);\n        } else {\n          ctx.arc(left, top, r, -PI, PI);\n        }\n        ctx.closePath();\n        ctx.moveTo(x, y);\n      } else {\n        ctx.rect(x, y, width, height);\n      }\n    },\n    drawPoint: function (ctx, style, radius, x, y, rotation) {\n      var type, xOffset, yOffset, size, cornerRadius;\n      var rad = (rotation || 0) * RAD_PER_DEG;\n      if (style && typeof style === 'object') {\n        type = style.toString();\n        if (type === '[object HTMLImageElement]' || type === '[object HTMLCanvasElement]') {\n          ctx.save();\n          ctx.translate(x, y);\n          ctx.rotate(rad);\n          ctx.drawImage(style, -style.width / 2, -style.height / 2, style.width, style.height);\n          ctx.restore();\n          return;\n        }\n      }\n      if (isNaN(radius) || radius <= 0) {\n        return;\n      }\n      ctx.beginPath();\n      switch (style) {\n        // Default includes circle\n        default:\n          ctx.arc(x, y, radius, 0, DOUBLE_PI);\n          ctx.closePath();\n          break;\n        case 'triangle':\n          ctx.moveTo(x + Math.sin(rad) * radius, y - Math.cos(rad) * radius);\n          rad += TWO_THIRDS_PI;\n          ctx.lineTo(x + Math.sin(rad) * radius, y - Math.cos(rad) * radius);\n          rad += TWO_THIRDS_PI;\n          ctx.lineTo(x + Math.sin(rad) * radius, y - Math.cos(rad) * radius);\n          ctx.closePath();\n          break;\n        case 'rectRounded':\n          // NOTE: the rounded rect implementation changed to use `arc` instead of\n          // `quadraticCurveTo` since it generates better results when rect is\n          // almost a circle. 0.516 (instead of 0.5) produces results with visually\n          // closer proportion to the previous impl and it is inscribed in the\n          // circle with `radius`. For more details, see the following PRs:\n          // https://github.com/chartjs/Chart.js/issues/5597\n          // https://github.com/chartjs/Chart.js/issues/5858\n          cornerRadius = radius * 0.516;\n          size = radius - cornerRadius;\n          xOffset = Math.cos(rad + QUARTER_PI) * size;\n          yOffset = Math.sin(rad + QUARTER_PI) * size;\n          ctx.arc(x - xOffset, y - yOffset, cornerRadius, rad - PI, rad - HALF_PI);\n          ctx.arc(x + yOffset, y - xOffset, cornerRadius, rad - HALF_PI, rad);\n          ctx.arc(x + xOffset, y + yOffset, cornerRadius, rad, rad + HALF_PI);\n          ctx.arc(x - yOffset, y + xOffset, cornerRadius, rad + HALF_PI, rad + PI);\n          ctx.closePath();\n          break;\n        case 'rect':\n          if (!rotation) {\n            size = Math.SQRT1_2 * radius;\n            ctx.rect(x - size, y - size, 2 * size, 2 * size);\n            break;\n          }\n          rad += QUARTER_PI;\n        /* falls through */\n        case 'rectRot':\n          xOffset = Math.cos(rad) * radius;\n          yOffset = Math.sin(rad) * radius;\n          ctx.moveTo(x - xOffset, y - yOffset);\n          ctx.lineTo(x + yOffset, y - xOffset);\n          ctx.lineTo(x + xOffset, y + yOffset);\n          ctx.lineTo(x - yOffset, y + xOffset);\n          ctx.closePath();\n          break;\n        case 'crossRot':\n          rad += QUARTER_PI;\n        /* falls through */\n        case 'cross':\n          xOffset = Math.cos(rad) * radius;\n          yOffset = Math.sin(rad) * radius;\n          ctx.moveTo(x - xOffset, y - yOffset);\n          ctx.lineTo(x + xOffset, y + yOffset);\n          ctx.moveTo(x + yOffset, y - xOffset);\n          ctx.lineTo(x - yOffset, y + xOffset);\n          break;\n        case 'star':\n          xOffset = Math.cos(rad) * radius;\n          yOffset = Math.sin(rad) * radius;\n          ctx.moveTo(x - xOffset, y - yOffset);\n          ctx.lineTo(x + xOffset, y + yOffset);\n          ctx.moveTo(x + yOffset, y - xOffset);\n          ctx.lineTo(x - yOffset, y + xOffset);\n          rad += QUARTER_PI;\n          xOffset = Math.cos(rad) * radius;\n          yOffset = Math.sin(rad) * radius;\n          ctx.moveTo(x - xOffset, y - yOffset);\n          ctx.lineTo(x + xOffset, y + yOffset);\n          ctx.moveTo(x + yOffset, y - xOffset);\n          ctx.lineTo(x - yOffset, y + xOffset);\n          break;\n        case 'line':\n          xOffset = Math.cos(rad) * radius;\n          yOffset = Math.sin(rad) * radius;\n          ctx.moveTo(x - xOffset, y - yOffset);\n          ctx.lineTo(x + xOffset, y + yOffset);\n          break;\n        case 'dash':\n          ctx.moveTo(x, y);\n          ctx.lineTo(x + Math.cos(rad) * radius, y + Math.sin(rad) * radius);\n          break;\n      }\n      ctx.fill();\n      ctx.stroke();\n    },\n    /**\r\n     * Returns true if the point is inside the rectangle\r\n     * @param {object} point - The point to test\r\n     * @param {object} area - The rectangle\r\n     * @returns {boolean}\r\n     * @private\r\n     */\n    _isPointInArea: function (point, area) {\n      var epsilon = 1e-6; // 1e-6 is margin in pixels for accumulated error.\n\n      return point.x > area.left - epsilon && point.x < area.right + epsilon && point.y > area.top - epsilon && point.y < area.bottom + epsilon;\n    },\n    clipArea: function (ctx, area) {\n      ctx.save();\n      ctx.beginPath();\n      ctx.rect(area.left, area.top, area.right - area.left, area.bottom - area.top);\n      ctx.clip();\n    },\n    unclipArea: function (ctx) {\n      ctx.restore();\n    },\n    lineTo: function (ctx, previous, target, flip) {\n      var stepped = target.steppedLine;\n      if (stepped) {\n        if (stepped === 'middle') {\n          var midpoint = (previous.x + target.x) / 2.0;\n          ctx.lineTo(midpoint, flip ? target.y : previous.y);\n          ctx.lineTo(midpoint, flip ? previous.y : target.y);\n        } else if (stepped === 'after' && !flip || stepped !== 'after' && flip) {\n          ctx.lineTo(previous.x, target.y);\n        } else {\n          ctx.lineTo(target.x, previous.y);\n        }\n        ctx.lineTo(target.x, target.y);\n        return;\n      }\n      if (!target.tension) {\n        ctx.lineTo(target.x, target.y);\n        return;\n      }\n      ctx.bezierCurveTo(flip ? previous.controlPointPreviousX : previous.controlPointNextX, flip ? previous.controlPointPreviousY : previous.controlPointNextY, flip ? target.controlPointNextX : target.controlPointPreviousX, flip ? target.controlPointNextY : target.controlPointPreviousY, target.x, target.y);\n    }\n  };\n  var helpers_canvas = exports$1;\n\n  // DEPRECATIONS\n\n  /**\r\n   * Provided for backward compatibility, use Chart.helpers.canvas.clear instead.\r\n   * @namespace Chart.helpers.clear\r\n   * @deprecated since version 2.7.0\r\n   * @todo remove at version 3\r\n   * @private\r\n   */\n  helpers_core.clear = exports$1.clear;\n\n  /**\r\n   * Provided for backward compatibility, use Chart.helpers.canvas.roundedRect instead.\r\n   * @namespace Chart.helpers.drawRoundedRectangle\r\n   * @deprecated since version 2.7.0\r\n   * @todo remove at version 3\r\n   * @private\r\n   */\n  helpers_core.drawRoundedRectangle = function (ctx) {\n    ctx.beginPath();\n    exports$1.roundedRect.apply(exports$1, arguments);\n  };\n  var defaults = {\n    /**\r\n     * @private\r\n     */\n    _set: function (scope, values) {\n      return helpers_core.merge(this[scope] || (this[scope] = {}), values);\n    }\n  };\n\n  // TODO(v3): remove 'global' from namespace.  all default are global and\n  // there's inconsistency around which options are under 'global'\n  defaults._set('global', {\n    defaultColor: 'rgba(0,0,0,0.1)',\n    defaultFontColor: '#666',\n    defaultFontFamily: \"'Helvetica Neue', 'Helvetica', 'Arial', sans-serif\",\n    defaultFontSize: 12,\n    defaultFontStyle: 'normal',\n    defaultLineHeight: 1.2,\n    showLines: true\n  });\n  var core_defaults = defaults;\n  var valueOrDefault = helpers_core.valueOrDefault;\n\n  /**\r\n   * Converts the given font object into a CSS font string.\r\n   * @param {object} font - A font object.\r\n   * @return {string} The CSS font string. See https://developer.mozilla.org/en-US/docs/Web/CSS/font\r\n   * @private\r\n   */\n  function toFontString(font) {\n    if (!font || helpers_core.isNullOrUndef(font.size) || helpers_core.isNullOrUndef(font.family)) {\n      return null;\n    }\n    return (font.style ? font.style + ' ' : '') + (font.weight ? font.weight + ' ' : '') + font.size + 'px ' + font.family;\n  }\n\n  /**\r\n   * @alias Chart.helpers.options\r\n   * @namespace\r\n   */\n  var helpers_options = {\n    /**\r\n     * Converts the given line height `value` in pixels for a specific font `size`.\r\n     * @param {number|string} value - The lineHeight to parse (eg. 1.6, '14px', '75%', '1.6em').\r\n     * @param {number} size - The font size (in pixels) used to resolve relative `value`.\r\n     * @returns {number} The effective line height in pixels (size * 1.2 if value is invalid).\r\n     * @see https://developer.mozilla.org/en-US/docs/Web/CSS/line-height\r\n     * @since 2.7.0\r\n     */\n    toLineHeight: function (value, size) {\n      var matches = ('' + value).match(/^(normal|(\\d+(?:\\.\\d+)?)(px|em|%)?)$/);\n      if (!matches || matches[1] === 'normal') {\n        return size * 1.2;\n      }\n      value = +matches[2];\n      switch (matches[3]) {\n        case 'px':\n          return value;\n        case '%':\n          value /= 100;\n          break;\n      }\n      return size * value;\n    },\n    /**\r\n     * Converts the given value into a padding object with pre-computed width/height.\r\n     * @param {number|object} value - If a number, set the value to all TRBL component,\r\n     *  else, if and object, use defined properties and sets undefined ones to 0.\r\n     * @returns {object} The padding values (top, right, bottom, left, width, height)\r\n     * @since 2.7.0\r\n     */\n    toPadding: function (value) {\n      var t, r, b, l;\n      if (helpers_core.isObject(value)) {\n        t = +value.top || 0;\n        r = +value.right || 0;\n        b = +value.bottom || 0;\n        l = +value.left || 0;\n      } else {\n        t = r = b = l = +value || 0;\n      }\n      return {\n        top: t,\n        right: r,\n        bottom: b,\n        left: l,\n        height: t + b,\n        width: l + r\n      };\n    },\n    /**\r\n     * Parses font options and returns the font object.\r\n     * @param {object} options - A object that contains font options to be parsed.\r\n     * @return {object} The font object.\r\n     * @todo Support font.* options and renamed to toFont().\r\n     * @private\r\n     */\n    _parseFont: function (options) {\n      var globalDefaults = core_defaults.global;\n      var size = valueOrDefault(options.fontSize, globalDefaults.defaultFontSize);\n      var font = {\n        family: valueOrDefault(options.fontFamily, globalDefaults.defaultFontFamily),\n        lineHeight: helpers_core.options.toLineHeight(valueOrDefault(options.lineHeight, globalDefaults.defaultLineHeight), size),\n        size: size,\n        style: valueOrDefault(options.fontStyle, globalDefaults.defaultFontStyle),\n        weight: null,\n        string: ''\n      };\n      font.string = toFontString(font);\n      return font;\n    },\n    /**\r\n     * Evaluates the given `inputs` sequentially and returns the first defined value.\r\n     * @param {Array} inputs - An array of values, falling back to the last value.\r\n     * @param {object} [context] - If defined and the current value is a function, the value\r\n     * is called with `context` as first argument and the result becomes the new input.\r\n     * @param {number} [index] - If defined and the current value is an array, the value\r\n     * at `index` become the new input.\r\n     * @param {object} [info] - object to return information about resolution in\r\n     * @param {boolean} [info.cacheable] - Will be set to `false` if option is not cacheable.\r\n     * @since 2.7.0\r\n     */\n    resolve: function (inputs, context, index, info) {\n      var cacheable = true;\n      var i, ilen, value;\n      for (i = 0, ilen = inputs.length; i < ilen; ++i) {\n        value = inputs[i];\n        if (value === undefined) {\n          continue;\n        }\n        if (context !== undefined && typeof value === 'function') {\n          value = value(context);\n          cacheable = false;\n        }\n        if (index !== undefined && helpers_core.isArray(value)) {\n          value = value[index];\n          cacheable = false;\n        }\n        if (value !== undefined) {\n          if (info && !cacheable) {\n            info.cacheable = false;\n          }\n          return value;\n        }\n      }\n    }\n  };\n\n  /**\r\n   * @alias Chart.helpers.math\r\n   * @namespace\r\n   */\n  var exports$2 = {\n    /**\r\n     * Returns an array of factors sorted from 1 to sqrt(value)\r\n     * @private\r\n     */\n    _factorize: function (value) {\n      var result = [];\n      var sqrt = Math.sqrt(value);\n      var i;\n      for (i = 1; i < sqrt; i++) {\n        if (value % i === 0) {\n          result.push(i);\n          result.push(value / i);\n        }\n      }\n      if (sqrt === (sqrt | 0)) {\n        // if value is a square number\n        result.push(sqrt);\n      }\n      result.sort(function (a, b) {\n        return a - b;\n      }).pop();\n      return result;\n    },\n    log10: Math.log10 || function (x) {\n      var exponent = Math.log(x) * Math.LOG10E; // Math.LOG10E = 1 / Math.LN10.\n      // Check for whole powers of 10,\n      // which due to floating point rounding error should be corrected.\n      var powerOf10 = Math.round(exponent);\n      var isPowerOf10 = x === Math.pow(10, powerOf10);\n      return isPowerOf10 ? powerOf10 : exponent;\n    }\n  };\n  var helpers_math = exports$2;\n\n  // DEPRECATIONS\n\n  /**\r\n   * Provided for backward compatibility, use Chart.helpers.math.log10 instead.\r\n   * @namespace Chart.helpers.log10\r\n   * @deprecated since version 2.9.0\r\n   * @todo remove at version 3\r\n   * @private\r\n   */\n  helpers_core.log10 = exports$2.log10;\n  var getRtlAdapter = function (rectX, width) {\n    return {\n      x: function (x) {\n        return rectX + rectX + width - x;\n      },\n      setWidth: function (w) {\n        width = w;\n      },\n      textAlign: function (align) {\n        if (align === 'center') {\n          return align;\n        }\n        return align === 'right' ? 'left' : 'right';\n      },\n      xPlus: function (x, value) {\n        return x - value;\n      },\n      leftForLtr: function (x, itemWidth) {\n        return x - itemWidth;\n      }\n    };\n  };\n  var getLtrAdapter = function () {\n    return {\n      x: function (x) {\n        return x;\n      },\n      setWidth: function (w) {// eslint-disable-line no-unused-vars\n      },\n      textAlign: function (align) {\n        return align;\n      },\n      xPlus: function (x, value) {\n        return x + value;\n      },\n      leftForLtr: function (x, _itemWidth) {\n        // eslint-disable-line no-unused-vars\n        return x;\n      }\n    };\n  };\n  var getAdapter = function (rtl, rectX, width) {\n    return rtl ? getRtlAdapter(rectX, width) : getLtrAdapter();\n  };\n  var overrideTextDirection = function (ctx, direction) {\n    var style, original;\n    if (direction === 'ltr' || direction === 'rtl') {\n      style = ctx.canvas.style;\n      original = [style.getPropertyValue('direction'), style.getPropertyPriority('direction')];\n      style.setProperty('direction', direction, 'important');\n      ctx.prevTextDirection = original;\n    }\n  };\n  var restoreTextDirection = function (ctx) {\n    var original = ctx.prevTextDirection;\n    if (original !== undefined) {\n      delete ctx.prevTextDirection;\n      ctx.canvas.style.setProperty('direction', original[0], original[1]);\n    }\n  };\n  var helpers_rtl = {\n    getRtlAdapter: getAdapter,\n    overrideTextDirection: overrideTextDirection,\n    restoreTextDirection: restoreTextDirection\n  };\n  var helpers$1 = helpers_core;\n  var easing = helpers_easing;\n  var canvas = helpers_canvas;\n  var options = helpers_options;\n  var math = helpers_math;\n  var rtl = helpers_rtl;\n  helpers$1.easing = easing;\n  helpers$1.canvas = canvas;\n  helpers$1.options = options;\n  helpers$1.math = math;\n  helpers$1.rtl = rtl;\n  function interpolate(start, view, model, ease) {\n    var keys = Object.keys(model);\n    var i, ilen, key, actual, origin, target, type, c0, c1;\n    for (i = 0, ilen = keys.length; i < ilen; ++i) {\n      key = keys[i];\n      target = model[key];\n\n      // if a value is added to the model after pivot() has been called, the view\n      // doesn't contain it, so let's initialize the view to the target value.\n      if (!view.hasOwnProperty(key)) {\n        view[key] = target;\n      }\n      actual = view[key];\n      if (actual === target || key[0] === '_') {\n        continue;\n      }\n      if (!start.hasOwnProperty(key)) {\n        start[key] = actual;\n      }\n      origin = start[key];\n      type = typeof target;\n      if (type === typeof origin) {\n        if (type === 'string') {\n          c0 = chartjsColor(origin);\n          if (c0.valid) {\n            c1 = chartjsColor(target);\n            if (c1.valid) {\n              view[key] = c1.mix(c0, ease).rgbString();\n              continue;\n            }\n          }\n        } else if (helpers$1.isFinite(origin) && helpers$1.isFinite(target)) {\n          view[key] = origin + (target - origin) * ease;\n          continue;\n        }\n      }\n      view[key] = target;\n    }\n  }\n  var Element = function (configuration) {\n    helpers$1.extend(this, configuration);\n    this.initialize.apply(this, arguments);\n  };\n  helpers$1.extend(Element.prototype, {\n    _type: undefined,\n    initialize: function () {\n      this.hidden = false;\n    },\n    pivot: function () {\n      var me = this;\n      if (!me._view) {\n        me._view = helpers$1.extend({}, me._model);\n      }\n      me._start = {};\n      return me;\n    },\n    transition: function (ease) {\n      var me = this;\n      var model = me._model;\n      var start = me._start;\n      var view = me._view;\n\n      // No animation -> No Transition\n      if (!model || ease === 1) {\n        me._view = helpers$1.extend({}, model);\n        me._start = null;\n        return me;\n      }\n      if (!view) {\n        view = me._view = {};\n      }\n      if (!start) {\n        start = me._start = {};\n      }\n      interpolate(start, view, model, ease);\n      return me;\n    },\n    tooltipPosition: function () {\n      return {\n        x: this._model.x,\n        y: this._model.y\n      };\n    },\n    hasValue: function () {\n      return helpers$1.isNumber(this._model.x) && helpers$1.isNumber(this._model.y);\n    }\n  });\n  Element.extend = helpers$1.inherits;\n  var core_element = Element;\n  var exports$3 = core_element.extend({\n    chart: null,\n    // the animation associated chart instance\n    currentStep: 0,\n    // the current animation step\n    numSteps: 60,\n    // default number of steps\n    easing: '',\n    // the easing to use for this animation\n    render: null,\n    // render function used by the animation service\n\n    onAnimationProgress: null,\n    // user specified callback to fire on each step of the animation\n    onAnimationComplete: null // user specified callback to fire when the animation finishes\n  });\n  var core_animation = exports$3;\n\n  // DEPRECATIONS\n\n  /**\r\n   * Provided for backward compatibility, use Chart.Animation instead\r\n   * @prop Chart.Animation#animationObject\r\n   * @deprecated since version 2.6.0\r\n   * @todo remove at version 3\r\n   */\n  Object.defineProperty(exports$3.prototype, 'animationObject', {\n    get: function () {\n      return this;\n    }\n  });\n\n  /**\r\n   * Provided for backward compatibility, use Chart.Animation#chart instead\r\n   * @prop Chart.Animation#chartInstance\r\n   * @deprecated since version 2.6.0\r\n   * @todo remove at version 3\r\n   */\n  Object.defineProperty(exports$3.prototype, 'chartInstance', {\n    get: function () {\n      return this.chart;\n    },\n    set: function (value) {\n      this.chart = value;\n    }\n  });\n  core_defaults._set('global', {\n    animation: {\n      duration: 1000,\n      easing: 'easeOutQuart',\n      onProgress: helpers$1.noop,\n      onComplete: helpers$1.noop\n    }\n  });\n  var core_animations = {\n    animations: [],\n    request: null,\n    /**\r\n     * @param {Chart} chart - The chart to animate.\r\n     * @param {Chart.Animation} animation - The animation that we will animate.\r\n     * @param {number} duration - The animation duration in ms.\r\n     * @param {boolean} lazy - if true, the chart is not marked as animating to enable more responsive interactions\r\n     */\n    addAnimation: function (chart, animation, duration, lazy) {\n      var animations = this.animations;\n      var i, ilen;\n      animation.chart = chart;\n      animation.startTime = Date.now();\n      animation.duration = duration;\n      if (!lazy) {\n        chart.animating = true;\n      }\n      for (i = 0, ilen = animations.length; i < ilen; ++i) {\n        if (animations[i].chart === chart) {\n          animations[i] = animation;\n          return;\n        }\n      }\n      animations.push(animation);\n\n      // If there are no animations queued, manually kickstart a digest, for lack of a better word\n      if (animations.length === 1) {\n        this.requestAnimationFrame();\n      }\n    },\n    cancelAnimation: function (chart) {\n      var index = helpers$1.findIndex(this.animations, function (animation) {\n        return animation.chart === chart;\n      });\n      if (index !== -1) {\n        this.animations.splice(index, 1);\n        chart.animating = false;\n      }\n    },\n    requestAnimationFrame: function () {\n      var me = this;\n      if (me.request === null) {\n        // Skip animation frame requests until the active one is executed.\n        // This can happen when processing mouse events, e.g. 'mousemove'\n        // and 'mouseout' events will trigger multiple renders.\n        me.request = helpers$1.requestAnimFrame.call(window, function () {\n          me.request = null;\n          me.startDigest();\n        });\n      }\n    },\n    /**\r\n     * @private\r\n     */\n    startDigest: function () {\n      var me = this;\n      me.advance();\n\n      // Do we have more stuff to animate?\n      if (me.animations.length > 0) {\n        me.requestAnimationFrame();\n      }\n    },\n    /**\r\n     * @private\r\n     */\n    advance: function () {\n      var animations = this.animations;\n      var animation, chart, numSteps, nextStep;\n      var i = 0;\n\n      // 1 animation per chart, so we are looping charts here\n      while (i < animations.length) {\n        animation = animations[i];\n        chart = animation.chart;\n        numSteps = animation.numSteps;\n\n        // Make sure that currentStep starts at 1\n        // https://github.com/chartjs/Chart.js/issues/6104\n        nextStep = Math.floor((Date.now() - animation.startTime) / animation.duration * numSteps) + 1;\n        animation.currentStep = Math.min(nextStep, numSteps);\n        helpers$1.callback(animation.render, [chart, animation], chart);\n        helpers$1.callback(animation.onAnimationProgress, [animation], chart);\n        if (animation.currentStep >= numSteps) {\n          helpers$1.callback(animation.onAnimationComplete, [animation], chart);\n          chart.animating = false;\n          animations.splice(i, 1);\n        } else {\n          ++i;\n        }\n      }\n    }\n  };\n  var resolve = helpers$1.options.resolve;\n  var arrayEvents = ['push', 'pop', 'shift', 'splice', 'unshift'];\n\n  /**\r\n   * Hooks the array methods that add or remove values ('push', pop', 'shift', 'splice',\r\n   * 'unshift') and notify the listener AFTER the array has been altered. Listeners are\r\n   * called on the 'onData*' callbacks (e.g. onDataPush, etc.) with same arguments.\r\n   */\n  function listenArrayEvents(array, listener) {\n    if (array._chartjs) {\n      array._chartjs.listeners.push(listener);\n      return;\n    }\n    Object.defineProperty(array, '_chartjs', {\n      configurable: true,\n      enumerable: false,\n      value: {\n        listeners: [listener]\n      }\n    });\n    arrayEvents.forEach(function (key) {\n      var method = 'onData' + key.charAt(0).toUpperCase() + key.slice(1);\n      var base = array[key];\n      Object.defineProperty(array, key, {\n        configurable: true,\n        enumerable: false,\n        value: function () {\n          var args = Array.prototype.slice.call(arguments);\n          var res = base.apply(this, args);\n          helpers$1.each(array._chartjs.listeners, function (object) {\n            if (typeof object[method] === 'function') {\n              object[method].apply(object, args);\n            }\n          });\n          return res;\n        }\n      });\n    });\n  }\n\n  /**\r\n   * Removes the given array event listener and cleanup extra attached properties (such as\r\n   * the _chartjs stub and overridden methods) if array doesn't have any more listeners.\r\n   */\n  function unlistenArrayEvents(array, listener) {\n    var stub = array._chartjs;\n    if (!stub) {\n      return;\n    }\n    var listeners = stub.listeners;\n    var index = listeners.indexOf(listener);\n    if (index !== -1) {\n      listeners.splice(index, 1);\n    }\n    if (listeners.length > 0) {\n      return;\n    }\n    arrayEvents.forEach(function (key) {\n      delete array[key];\n    });\n    delete array._chartjs;\n  }\n\n  // Base class for all dataset controllers (line, bar, etc)\n  var DatasetController = function (chart, datasetIndex) {\n    this.initialize(chart, datasetIndex);\n  };\n  helpers$1.extend(DatasetController.prototype, {\n    /**\r\n     * Element type used to generate a meta dataset (e.g. Chart.element.Line).\r\n     * @type {Chart.core.element}\r\n     */\n    datasetElementType: null,\n    /**\r\n     * Element type used to generate a meta data (e.g. Chart.element.Point).\r\n     * @type {Chart.core.element}\r\n     */\n    dataElementType: null,\n    /**\r\n     * Dataset element option keys to be resolved in _resolveDatasetElementOptions.\r\n     * A derived controller may override this to resolve controller-specific options.\r\n     * The keys defined here are for backward compatibility for legend styles.\r\n     * @private\r\n     */\n    _datasetElementOptions: ['backgroundColor', 'borderCapStyle', 'borderColor', 'borderDash', 'borderDashOffset', 'borderJoinStyle', 'borderWidth'],\n    /**\r\n     * Data element option keys to be resolved in _resolveDataElementOptions.\r\n     * A derived controller may override this to resolve controller-specific options.\r\n     * The keys defined here are for backward compatibility for legend styles.\r\n     * @private\r\n     */\n    _dataElementOptions: ['backgroundColor', 'borderColor', 'borderWidth', 'pointStyle'],\n    initialize: function (chart, datasetIndex) {\n      var me = this;\n      me.chart = chart;\n      me.index = datasetIndex;\n      me.linkScales();\n      me.addElements();\n      me._type = me.getMeta().type;\n    },\n    updateIndex: function (datasetIndex) {\n      this.index = datasetIndex;\n    },\n    linkScales: function () {\n      var me = this;\n      var meta = me.getMeta();\n      var chart = me.chart;\n      var scales = chart.scales;\n      var dataset = me.getDataset();\n      var scalesOpts = chart.options.scales;\n      if (meta.xAxisID === null || !(meta.xAxisID in scales) || dataset.xAxisID) {\n        meta.xAxisID = dataset.xAxisID || scalesOpts.xAxes[0].id;\n      }\n      if (meta.yAxisID === null || !(meta.yAxisID in scales) || dataset.yAxisID) {\n        meta.yAxisID = dataset.yAxisID || scalesOpts.yAxes[0].id;\n      }\n    },\n    getDataset: function () {\n      return this.chart.data.datasets[this.index];\n    },\n    getMeta: function () {\n      return this.chart.getDatasetMeta(this.index);\n    },\n    getScaleForId: function (scaleID) {\n      return this.chart.scales[scaleID];\n    },\n    /**\r\n     * @private\r\n     */\n    _getValueScaleId: function () {\n      return this.getMeta().yAxisID;\n    },\n    /**\r\n     * @private\r\n     */\n    _getIndexScaleId: function () {\n      return this.getMeta().xAxisID;\n    },\n    /**\r\n     * @private\r\n     */\n    _getValueScale: function () {\n      return this.getScaleForId(this._getValueScaleId());\n    },\n    /**\r\n     * @private\r\n     */\n    _getIndexScale: function () {\n      return this.getScaleForId(this._getIndexScaleId());\n    },\n    reset: function () {\n      this._update(true);\n    },\n    /**\r\n     * @private\r\n     */\n    destroy: function () {\n      if (this._data) {\n        unlistenArrayEvents(this._data, this);\n      }\n    },\n    createMetaDataset: function () {\n      var me = this;\n      var type = me.datasetElementType;\n      return type && new type({\n        _chart: me.chart,\n        _datasetIndex: me.index\n      });\n    },\n    createMetaData: function (index) {\n      var me = this;\n      var type = me.dataElementType;\n      return type && new type({\n        _chart: me.chart,\n        _datasetIndex: me.index,\n        _index: index\n      });\n    },\n    addElements: function () {\n      var me = this;\n      var meta = me.getMeta();\n      var data = me.getDataset().data || [];\n      var metaData = meta.data;\n      var i, ilen;\n      for (i = 0, ilen = data.length; i < ilen; ++i) {\n        metaData[i] = metaData[i] || me.createMetaData(i);\n      }\n      meta.dataset = meta.dataset || me.createMetaDataset();\n    },\n    addElementAndReset: function (index) {\n      var element = this.createMetaData(index);\n      this.getMeta().data.splice(index, 0, element);\n      this.updateElement(element, index, true);\n    },\n    buildOrUpdateElements: function () {\n      var me = this;\n      var dataset = me.getDataset();\n      var data = dataset.data || (dataset.data = []);\n\n      // In order to correctly handle data addition/deletion animation (an thus simulate\n      // real-time charts), we need to monitor these data modifications and synchronize\n      // the internal meta data accordingly.\n      if (me._data !== data) {\n        if (me._data) {\n          // This case happens when the user replaced the data array instance.\n          unlistenArrayEvents(me._data, me);\n        }\n        if (data && Object.isExtensible(data)) {\n          listenArrayEvents(data, me);\n        }\n        me._data = data;\n      }\n\n      // Re-sync meta data in case the user replaced the data array or if we missed\n      // any updates and so make sure that we handle number of datapoints changing.\n      me.resyncElements();\n    },\n    /**\r\n     * Returns the merged user-supplied and default dataset-level options\r\n     * @private\r\n     */\n    _configure: function () {\n      var me = this;\n      me._config = helpers$1.merge(Object.create(null), [me.chart.options.datasets[me._type], me.getDataset()], {\n        merger: function (key, target, source) {\n          if (key !== '_meta' && key !== 'data') {\n            helpers$1._merger(key, target, source);\n          }\n        }\n      });\n    },\n    _update: function (reset) {\n      var me = this;\n      me._configure();\n      me._cachedDataOpts = null;\n      me.update(reset);\n    },\n    update: helpers$1.noop,\n    transition: function (easingValue) {\n      var meta = this.getMeta();\n      var elements = meta.data || [];\n      var ilen = elements.length;\n      var i = 0;\n      for (; i < ilen; ++i) {\n        elements[i].transition(easingValue);\n      }\n      if (meta.dataset) {\n        meta.dataset.transition(easingValue);\n      }\n    },\n    draw: function () {\n      var meta = this.getMeta();\n      var elements = meta.data || [];\n      var ilen = elements.length;\n      var i = 0;\n      if (meta.dataset) {\n        meta.dataset.draw();\n      }\n      for (; i < ilen; ++i) {\n        elements[i].draw();\n      }\n    },\n    /**\r\n     * Returns a set of predefined style properties that should be used to represent the dataset\r\n     * or the data if the index is specified\r\n     * @param {number} index - data index\r\n     * @return {IStyleInterface} style object\r\n     */\n    getStyle: function (index) {\n      var me = this;\n      var meta = me.getMeta();\n      var dataset = meta.dataset;\n      var style;\n      me._configure();\n      if (dataset && index === undefined) {\n        style = me._resolveDatasetElementOptions(dataset || {});\n      } else {\n        index = index || 0;\n        style = me._resolveDataElementOptions(meta.data[index] || {}, index);\n      }\n      if (style.fill === false || style.fill === null) {\n        style.backgroundColor = style.borderColor;\n      }\n      return style;\n    },\n    /**\r\n     * @private\r\n     */\n    _resolveDatasetElementOptions: function (element, hover) {\n      var me = this;\n      var chart = me.chart;\n      var datasetOpts = me._config;\n      var custom = element.custom || {};\n      var options = chart.options.elements[me.datasetElementType.prototype._type] || {};\n      var elementOptions = me._datasetElementOptions;\n      var values = {};\n      var i, ilen, key, readKey;\n\n      // Scriptable options\n      var context = {\n        chart: chart,\n        dataset: me.getDataset(),\n        datasetIndex: me.index,\n        hover: hover\n      };\n      for (i = 0, ilen = elementOptions.length; i < ilen; ++i) {\n        key = elementOptions[i];\n        readKey = hover ? 'hover' + key.charAt(0).toUpperCase() + key.slice(1) : key;\n        values[key] = resolve([custom[readKey], datasetOpts[readKey], options[readKey]], context);\n      }\n      return values;\n    },\n    /**\r\n     * @private\r\n     */\n    _resolveDataElementOptions: function (element, index) {\n      var me = this;\n      var custom = element && element.custom;\n      var cached = me._cachedDataOpts;\n      if (cached && !custom) {\n        return cached;\n      }\n      var chart = me.chart;\n      var datasetOpts = me._config;\n      var options = chart.options.elements[me.dataElementType.prototype._type] || {};\n      var elementOptions = me._dataElementOptions;\n      var values = {};\n\n      // Scriptable options\n      var context = {\n        chart: chart,\n        dataIndex: index,\n        dataset: me.getDataset(),\n        datasetIndex: me.index\n      };\n\n      // `resolve` sets cacheable to `false` if any option is indexed or scripted\n      var info = {\n        cacheable: !custom\n      };\n      var keys, i, ilen, key;\n      custom = custom || {};\n      if (helpers$1.isArray(elementOptions)) {\n        for (i = 0, ilen = elementOptions.length; i < ilen; ++i) {\n          key = elementOptions[i];\n          values[key] = resolve([custom[key], datasetOpts[key], options[key]], context, index, info);\n        }\n      } else {\n        keys = Object.keys(elementOptions);\n        for (i = 0, ilen = keys.length; i < ilen; ++i) {\n          key = keys[i];\n          values[key] = resolve([custom[key], datasetOpts[elementOptions[key]], datasetOpts[key], options[key]], context, index, info);\n        }\n      }\n      if (info.cacheable) {\n        me._cachedDataOpts = Object.freeze(values);\n      }\n      return values;\n    },\n    removeHoverStyle: function (element) {\n      helpers$1.merge(element._model, element.$previousStyle || {});\n      delete element.$previousStyle;\n    },\n    setHoverStyle: function (element) {\n      var dataset = this.chart.data.datasets[element._datasetIndex];\n      var index = element._index;\n      var custom = element.custom || {};\n      var model = element._model;\n      var getHoverColor = helpers$1.getHoverColor;\n      element.$previousStyle = {\n        backgroundColor: model.backgroundColor,\n        borderColor: model.borderColor,\n        borderWidth: model.borderWidth\n      };\n      model.backgroundColor = resolve([custom.hoverBackgroundColor, dataset.hoverBackgroundColor, getHoverColor(model.backgroundColor)], undefined, index);\n      model.borderColor = resolve([custom.hoverBorderColor, dataset.hoverBorderColor, getHoverColor(model.borderColor)], undefined, index);\n      model.borderWidth = resolve([custom.hoverBorderWidth, dataset.hoverBorderWidth, model.borderWidth], undefined, index);\n    },\n    /**\r\n     * @private\r\n     */\n    _removeDatasetHoverStyle: function () {\n      var element = this.getMeta().dataset;\n      if (element) {\n        this.removeHoverStyle(element);\n      }\n    },\n    /**\r\n     * @private\r\n     */\n    _setDatasetHoverStyle: function () {\n      var element = this.getMeta().dataset;\n      var prev = {};\n      var i, ilen, key, keys, hoverOptions, model;\n      if (!element) {\n        return;\n      }\n      model = element._model;\n      hoverOptions = this._resolveDatasetElementOptions(element, true);\n      keys = Object.keys(hoverOptions);\n      for (i = 0, ilen = keys.length; i < ilen; ++i) {\n        key = keys[i];\n        prev[key] = model[key];\n        model[key] = hoverOptions[key];\n      }\n      element.$previousStyle = prev;\n    },\n    /**\r\n     * @private\r\n     */\n    resyncElements: function () {\n      var me = this;\n      var meta = me.getMeta();\n      var data = me.getDataset().data;\n      var numMeta = meta.data.length;\n      var numData = data.length;\n      if (numData < numMeta) {\n        meta.data.splice(numData, numMeta - numData);\n      } else if (numData > numMeta) {\n        me.insertElements(numMeta, numData - numMeta);\n      }\n    },\n    /**\r\n     * @private\r\n     */\n    insertElements: function (start, count) {\n      for (var i = 0; i < count; ++i) {\n        this.addElementAndReset(start + i);\n      }\n    },\n    /**\r\n     * @private\r\n     */\n    onDataPush: function () {\n      var count = arguments.length;\n      this.insertElements(this.getDataset().data.length - count, count);\n    },\n    /**\r\n     * @private\r\n     */\n    onDataPop: function () {\n      this.getMeta().data.pop();\n    },\n    /**\r\n     * @private\r\n     */\n    onDataShift: function () {\n      this.getMeta().data.shift();\n    },\n    /**\r\n     * @private\r\n     */\n    onDataSplice: function (start, count) {\n      this.getMeta().data.splice(start, count);\n      this.insertElements(start, arguments.length - 2);\n    },\n    /**\r\n     * @private\r\n     */\n    onDataUnshift: function () {\n      this.insertElements(0, arguments.length);\n    }\n  });\n  DatasetController.extend = helpers$1.inherits;\n  var core_datasetController = DatasetController;\n  var TAU = Math.PI * 2;\n  core_defaults._set('global', {\n    elements: {\n      arc: {\n        backgroundColor: core_defaults.global.defaultColor,\n        borderColor: '#fff',\n        borderWidth: 2,\n        borderAlign: 'center'\n      }\n    }\n  });\n  function clipArc(ctx, arc) {\n    var startAngle = arc.startAngle;\n    var endAngle = arc.endAngle;\n    var pixelMargin = arc.pixelMargin;\n    var angleMargin = pixelMargin / arc.outerRadius;\n    var x = arc.x;\n    var y = arc.y;\n\n    // Draw an inner border by cliping the arc and drawing a double-width border\n    // Enlarge the clipping arc by 0.33 pixels to eliminate glitches between borders\n    ctx.beginPath();\n    ctx.arc(x, y, arc.outerRadius, startAngle - angleMargin, endAngle + angleMargin);\n    if (arc.innerRadius > pixelMargin) {\n      angleMargin = pixelMargin / arc.innerRadius;\n      ctx.arc(x, y, arc.innerRadius - pixelMargin, endAngle + angleMargin, startAngle - angleMargin, true);\n    } else {\n      ctx.arc(x, y, pixelMargin, endAngle + Math.PI / 2, startAngle - Math.PI / 2);\n    }\n    ctx.closePath();\n    ctx.clip();\n  }\n  function drawFullCircleBorders(ctx, vm, arc, inner) {\n    var endAngle = arc.endAngle;\n    var i;\n    if (inner) {\n      arc.endAngle = arc.startAngle + TAU;\n      clipArc(ctx, arc);\n      arc.endAngle = endAngle;\n      if (arc.endAngle === arc.startAngle && arc.fullCircles) {\n        arc.endAngle += TAU;\n        arc.fullCircles--;\n      }\n    }\n    ctx.beginPath();\n    ctx.arc(arc.x, arc.y, arc.innerRadius, arc.startAngle + TAU, arc.startAngle, true);\n    for (i = 0; i < arc.fullCircles; ++i) {\n      ctx.stroke();\n    }\n    ctx.beginPath();\n    ctx.arc(arc.x, arc.y, vm.outerRadius, arc.startAngle, arc.startAngle + TAU);\n    for (i = 0; i < arc.fullCircles; ++i) {\n      ctx.stroke();\n    }\n  }\n  function drawBorder(ctx, vm, arc) {\n    var inner = vm.borderAlign === 'inner';\n    if (inner) {\n      ctx.lineWidth = vm.borderWidth * 2;\n      ctx.lineJoin = 'round';\n    } else {\n      ctx.lineWidth = vm.borderWidth;\n      ctx.lineJoin = 'bevel';\n    }\n    if (arc.fullCircles) {\n      drawFullCircleBorders(ctx, vm, arc, inner);\n    }\n    if (inner) {\n      clipArc(ctx, arc);\n    }\n    ctx.beginPath();\n    ctx.arc(arc.x, arc.y, vm.outerRadius, arc.startAngle, arc.endAngle);\n    ctx.arc(arc.x, arc.y, arc.innerRadius, arc.endAngle, arc.startAngle, true);\n    ctx.closePath();\n    ctx.stroke();\n  }\n  var element_arc = core_element.extend({\n    _type: 'arc',\n    inLabelRange: function (mouseX) {\n      var vm = this._view;\n      if (vm) {\n        return Math.pow(mouseX - vm.x, 2) < Math.pow(vm.radius + vm.hoverRadius, 2);\n      }\n      return false;\n    },\n    inRange: function (chartX, chartY) {\n      var vm = this._view;\n      if (vm) {\n        var pointRelativePosition = helpers$1.getAngleFromPoint(vm, {\n          x: chartX,\n          y: chartY\n        });\n        var angle = pointRelativePosition.angle;\n        var distance = pointRelativePosition.distance;\n\n        // Sanitise angle range\n        var startAngle = vm.startAngle;\n        var endAngle = vm.endAngle;\n        while (endAngle < startAngle) {\n          endAngle += TAU;\n        }\n        while (angle > endAngle) {\n          angle -= TAU;\n        }\n        while (angle < startAngle) {\n          angle += TAU;\n        }\n\n        // Check if within the range of the open/close angle\n        var betweenAngles = angle >= startAngle && angle <= endAngle;\n        var withinRadius = distance >= vm.innerRadius && distance <= vm.outerRadius;\n        return betweenAngles && withinRadius;\n      }\n      return false;\n    },\n    getCenterPoint: function () {\n      var vm = this._view;\n      var halfAngle = (vm.startAngle + vm.endAngle) / 2;\n      var halfRadius = (vm.innerRadius + vm.outerRadius) / 2;\n      return {\n        x: vm.x + Math.cos(halfAngle) * halfRadius,\n        y: vm.y + Math.sin(halfAngle) * halfRadius\n      };\n    },\n    getArea: function () {\n      var vm = this._view;\n      return Math.PI * ((vm.endAngle - vm.startAngle) / (2 * Math.PI)) * (Math.pow(vm.outerRadius, 2) - Math.pow(vm.innerRadius, 2));\n    },\n    tooltipPosition: function () {\n      var vm = this._view;\n      var centreAngle = vm.startAngle + (vm.endAngle - vm.startAngle) / 2;\n      var rangeFromCentre = (vm.outerRadius - vm.innerRadius) / 2 + vm.innerRadius;\n      return {\n        x: vm.x + Math.cos(centreAngle) * rangeFromCentre,\n        y: vm.y + Math.sin(centreAngle) * rangeFromCentre\n      };\n    },\n    draw: function () {\n      var ctx = this._chart.ctx;\n      var vm = this._view;\n      var pixelMargin = vm.borderAlign === 'inner' ? 0.33 : 0;\n      var arc = {\n        x: vm.x,\n        y: vm.y,\n        innerRadius: vm.innerRadius,\n        outerRadius: Math.max(vm.outerRadius - pixelMargin, 0),\n        pixelMargin: pixelMargin,\n        startAngle: vm.startAngle,\n        endAngle: vm.endAngle,\n        fullCircles: Math.floor(vm.circumference / TAU)\n      };\n      var i;\n      ctx.save();\n      ctx.fillStyle = vm.backgroundColor;\n      ctx.strokeStyle = vm.borderColor;\n      if (arc.fullCircles) {\n        arc.endAngle = arc.startAngle + TAU;\n        ctx.beginPath();\n        ctx.arc(arc.x, arc.y, arc.outerRadius, arc.startAngle, arc.endAngle);\n        ctx.arc(arc.x, arc.y, arc.innerRadius, arc.endAngle, arc.startAngle, true);\n        ctx.closePath();\n        for (i = 0; i < arc.fullCircles; ++i) {\n          ctx.fill();\n        }\n        arc.endAngle = arc.startAngle + vm.circumference % TAU;\n      }\n      ctx.beginPath();\n      ctx.arc(arc.x, arc.y, arc.outerRadius, arc.startAngle, arc.endAngle);\n      ctx.arc(arc.x, arc.y, arc.innerRadius, arc.endAngle, arc.startAngle, true);\n      ctx.closePath();\n      ctx.fill();\n      if (vm.borderWidth) {\n        drawBorder(ctx, vm, arc);\n      }\n      ctx.restore();\n    }\n  });\n  var valueOrDefault$1 = helpers$1.valueOrDefault;\n  var defaultColor = core_defaults.global.defaultColor;\n  core_defaults._set('global', {\n    elements: {\n      line: {\n        tension: 0.4,\n        backgroundColor: defaultColor,\n        borderWidth: 3,\n        borderColor: defaultColor,\n        borderCapStyle: 'butt',\n        borderDash: [],\n        borderDashOffset: 0.0,\n        borderJoinStyle: 'miter',\n        capBezierPoints: true,\n        fill: true // do we fill in the area between the line and its base axis\n      }\n    }\n  });\n  var element_line = core_element.extend({\n    _type: 'line',\n    draw: function () {\n      var me = this;\n      var vm = me._view;\n      var ctx = me._chart.ctx;\n      var spanGaps = vm.spanGaps;\n      var points = me._children.slice(); // clone array\n      var globalDefaults = core_defaults.global;\n      var globalOptionLineElements = globalDefaults.elements.line;\n      var lastDrawnIndex = -1;\n      var closePath = me._loop;\n      var index, previous, currentVM;\n      if (!points.length) {\n        return;\n      }\n      if (me._loop) {\n        for (index = 0; index < points.length; ++index) {\n          previous = helpers$1.previousItem(points, index);\n          // If the line has an open path, shift the point array\n          if (!points[index]._view.skip && previous._view.skip) {\n            points = points.slice(index).concat(points.slice(0, index));\n            closePath = spanGaps;\n            break;\n          }\n        }\n        // If the line has a close path, add the first point again\n        if (closePath) {\n          points.push(points[0]);\n        }\n      }\n      ctx.save();\n\n      // Stroke Line Options\n      ctx.lineCap = vm.borderCapStyle || globalOptionLineElements.borderCapStyle;\n\n      // IE 9 and 10 do not support line dash\n      if (ctx.setLineDash) {\n        ctx.setLineDash(vm.borderDash || globalOptionLineElements.borderDash);\n      }\n      ctx.lineDashOffset = valueOrDefault$1(vm.borderDashOffset, globalOptionLineElements.borderDashOffset);\n      ctx.lineJoin = vm.borderJoinStyle || globalOptionLineElements.borderJoinStyle;\n      ctx.lineWidth = valueOrDefault$1(vm.borderWidth, globalOptionLineElements.borderWidth);\n      ctx.strokeStyle = vm.borderColor || globalDefaults.defaultColor;\n\n      // Stroke Line\n      ctx.beginPath();\n\n      // First point moves to it's starting position no matter what\n      currentVM = points[0]._view;\n      if (!currentVM.skip) {\n        ctx.moveTo(currentVM.x, currentVM.y);\n        lastDrawnIndex = 0;\n      }\n      for (index = 1; index < points.length; ++index) {\n        currentVM = points[index]._view;\n        previous = lastDrawnIndex === -1 ? helpers$1.previousItem(points, index) : points[lastDrawnIndex];\n        if (!currentVM.skip) {\n          if (lastDrawnIndex !== index - 1 && !spanGaps || lastDrawnIndex === -1) {\n            // There was a gap and this is the first point after the gap\n            ctx.moveTo(currentVM.x, currentVM.y);\n          } else {\n            // Line to next point\n            helpers$1.canvas.lineTo(ctx, previous._view, currentVM);\n          }\n          lastDrawnIndex = index;\n        }\n      }\n      if (closePath) {\n        ctx.closePath();\n      }\n      ctx.stroke();\n      ctx.restore();\n    }\n  });\n  var valueOrDefault$2 = helpers$1.valueOrDefault;\n  var defaultColor$1 = core_defaults.global.defaultColor;\n  core_defaults._set('global', {\n    elements: {\n      point: {\n        radius: 3,\n        pointStyle: 'circle',\n        backgroundColor: defaultColor$1,\n        borderColor: defaultColor$1,\n        borderWidth: 1,\n        // Hover\n        hitRadius: 1,\n        hoverRadius: 4,\n        hoverBorderWidth: 1\n      }\n    }\n  });\n  function xRange(mouseX) {\n    var vm = this._view;\n    return vm ? Math.abs(mouseX - vm.x) < vm.radius + vm.hitRadius : false;\n  }\n  function yRange(mouseY) {\n    var vm = this._view;\n    return vm ? Math.abs(mouseY - vm.y) < vm.radius + vm.hitRadius : false;\n  }\n  var element_point = core_element.extend({\n    _type: 'point',\n    inRange: function (mouseX, mouseY) {\n      var vm = this._view;\n      return vm ? Math.pow(mouseX - vm.x, 2) + Math.pow(mouseY - vm.y, 2) < Math.pow(vm.hitRadius + vm.radius, 2) : false;\n    },\n    inLabelRange: xRange,\n    inXRange: xRange,\n    inYRange: yRange,\n    getCenterPoint: function () {\n      var vm = this._view;\n      return {\n        x: vm.x,\n        y: vm.y\n      };\n    },\n    getArea: function () {\n      return Math.PI * Math.pow(this._view.radius, 2);\n    },\n    tooltipPosition: function () {\n      var vm = this._view;\n      return {\n        x: vm.x,\n        y: vm.y,\n        padding: vm.radius + vm.borderWidth\n      };\n    },\n    draw: function (chartArea) {\n      var vm = this._view;\n      var ctx = this._chart.ctx;\n      var pointStyle = vm.pointStyle;\n      var rotation = vm.rotation;\n      var radius = vm.radius;\n      var x = vm.x;\n      var y = vm.y;\n      var globalDefaults = core_defaults.global;\n      var defaultColor = globalDefaults.defaultColor; // eslint-disable-line no-shadow\n\n      if (vm.skip) {\n        return;\n      }\n\n      // Clipping for Points.\n      if (chartArea === undefined || helpers$1.canvas._isPointInArea(vm, chartArea)) {\n        ctx.strokeStyle = vm.borderColor || defaultColor;\n        ctx.lineWidth = valueOrDefault$2(vm.borderWidth, globalDefaults.elements.point.borderWidth);\n        ctx.fillStyle = vm.backgroundColor || defaultColor;\n        helpers$1.canvas.drawPoint(ctx, pointStyle, radius, x, y, rotation);\n      }\n    }\n  });\n  var defaultColor$2 = core_defaults.global.defaultColor;\n  core_defaults._set('global', {\n    elements: {\n      rectangle: {\n        backgroundColor: defaultColor$2,\n        borderColor: defaultColor$2,\n        borderSkipped: 'bottom',\n        borderWidth: 0\n      }\n    }\n  });\n  function isVertical(vm) {\n    return vm && vm.width !== undefined;\n  }\n\n  /**\r\n   * Helper function to get the bounds of the bar regardless of the orientation\r\n   * @param bar {Chart.Element.Rectangle} the bar\r\n   * @return {Bounds} bounds of the bar\r\n   * @private\r\n   */\n  function getBarBounds(vm) {\n    var x1, x2, y1, y2, half;\n    if (isVertical(vm)) {\n      half = vm.width / 2;\n      x1 = vm.x - half;\n      x2 = vm.x + half;\n      y1 = Math.min(vm.y, vm.base);\n      y2 = Math.max(vm.y, vm.base);\n    } else {\n      half = vm.height / 2;\n      x1 = Math.min(vm.x, vm.base);\n      x2 = Math.max(vm.x, vm.base);\n      y1 = vm.y - half;\n      y2 = vm.y + half;\n    }\n    return {\n      left: x1,\n      top: y1,\n      right: x2,\n      bottom: y2\n    };\n  }\n  function swap(orig, v1, v2) {\n    return orig === v1 ? v2 : orig === v2 ? v1 : orig;\n  }\n  function parseBorderSkipped(vm) {\n    var edge = vm.borderSkipped;\n    var res = {};\n    if (!edge) {\n      return res;\n    }\n    if (vm.horizontal) {\n      if (vm.base > vm.x) {\n        edge = swap(edge, 'left', 'right');\n      }\n    } else if (vm.base < vm.y) {\n      edge = swap(edge, 'bottom', 'top');\n    }\n    res[edge] = true;\n    return res;\n  }\n  function parseBorderWidth(vm, maxW, maxH) {\n    var value = vm.borderWidth;\n    var skip = parseBorderSkipped(vm);\n    var t, r, b, l;\n    if (helpers$1.isObject(value)) {\n      t = +value.top || 0;\n      r = +value.right || 0;\n      b = +value.bottom || 0;\n      l = +value.left || 0;\n    } else {\n      t = r = b = l = +value || 0;\n    }\n    return {\n      t: skip.top || t < 0 ? 0 : t > maxH ? maxH : t,\n      r: skip.right || r < 0 ? 0 : r > maxW ? maxW : r,\n      b: skip.bottom || b < 0 ? 0 : b > maxH ? maxH : b,\n      l: skip.left || l < 0 ? 0 : l > maxW ? maxW : l\n    };\n  }\n  function boundingRects(vm) {\n    var bounds = getBarBounds(vm);\n    var width = bounds.right - bounds.left;\n    var height = bounds.bottom - bounds.top;\n    var border = parseBorderWidth(vm, width / 2, height / 2);\n    return {\n      outer: {\n        x: bounds.left,\n        y: bounds.top,\n        w: width,\n        h: height\n      },\n      inner: {\n        x: bounds.left + border.l,\n        y: bounds.top + border.t,\n        w: width - border.l - border.r,\n        h: height - border.t - border.b\n      }\n    };\n  }\n  function inRange(vm, x, y) {\n    var skipX = x === null;\n    var skipY = y === null;\n    var bounds = !vm || skipX && skipY ? false : getBarBounds(vm);\n    return bounds && (skipX || x >= bounds.left && x <= bounds.right) && (skipY || y >= bounds.top && y <= bounds.bottom);\n  }\n  var element_rectangle = core_element.extend({\n    _type: 'rectangle',\n    draw: function () {\n      var ctx = this._chart.ctx;\n      var vm = this._view;\n      var rects = boundingRects(vm);\n      var outer = rects.outer;\n      var inner = rects.inner;\n      ctx.fillStyle = vm.backgroundColor;\n      ctx.fillRect(outer.x, outer.y, outer.w, outer.h);\n      if (outer.w === inner.w && outer.h === inner.h) {\n        return;\n      }\n      ctx.save();\n      ctx.beginPath();\n      ctx.rect(outer.x, outer.y, outer.w, outer.h);\n      ctx.clip();\n      ctx.fillStyle = vm.borderColor;\n      ctx.rect(inner.x, inner.y, inner.w, inner.h);\n      ctx.fill('evenodd');\n      ctx.restore();\n    },\n    height: function () {\n      var vm = this._view;\n      return vm.base - vm.y;\n    },\n    inRange: function (mouseX, mouseY) {\n      return inRange(this._view, mouseX, mouseY);\n    },\n    inLabelRange: function (mouseX, mouseY) {\n      var vm = this._view;\n      return isVertical(vm) ? inRange(vm, mouseX, null) : inRange(vm, null, mouseY);\n    },\n    inXRange: function (mouseX) {\n      return inRange(this._view, mouseX, null);\n    },\n    inYRange: function (mouseY) {\n      return inRange(this._view, null, mouseY);\n    },\n    getCenterPoint: function () {\n      var vm = this._view;\n      var x, y;\n      if (isVertical(vm)) {\n        x = vm.x;\n        y = (vm.y + vm.base) / 2;\n      } else {\n        x = (vm.x + vm.base) / 2;\n        y = vm.y;\n      }\n      return {\n        x: x,\n        y: y\n      };\n    },\n    getArea: function () {\n      var vm = this._view;\n      return isVertical(vm) ? vm.width * Math.abs(vm.y - vm.base) : vm.height * Math.abs(vm.x - vm.base);\n    },\n    tooltipPosition: function () {\n      var vm = this._view;\n      return {\n        x: vm.x,\n        y: vm.y\n      };\n    }\n  });\n  var elements = {};\n  var Arc = element_arc;\n  var Line = element_line;\n  var Point = element_point;\n  var Rectangle = element_rectangle;\n  elements.Arc = Arc;\n  elements.Line = Line;\n  elements.Point = Point;\n  elements.Rectangle = Rectangle;\n  var deprecated = helpers$1._deprecated;\n  var valueOrDefault$3 = helpers$1.valueOrDefault;\n  core_defaults._set('bar', {\n    hover: {\n      mode: 'label'\n    },\n    scales: {\n      xAxes: [{\n        type: 'category',\n        offset: true,\n        gridLines: {\n          offsetGridLines: true\n        }\n      }],\n      yAxes: [{\n        type: 'linear'\n      }]\n    }\n  });\n  core_defaults._set('global', {\n    datasets: {\n      bar: {\n        categoryPercentage: 0.8,\n        barPercentage: 0.9\n      }\n    }\n  });\n\n  /**\r\n   * Computes the \"optimal\" sample size to maintain bars equally sized while preventing overlap.\r\n   * @private\r\n   */\n  function computeMinSampleSize(scale, pixels) {\n    var min = scale._length;\n    var prev, curr, i, ilen;\n    for (i = 1, ilen = pixels.length; i < ilen; ++i) {\n      min = Math.min(min, Math.abs(pixels[i] - pixels[i - 1]));\n    }\n    for (i = 0, ilen = scale.getTicks().length; i < ilen; ++i) {\n      curr = scale.getPixelForTick(i);\n      min = i > 0 ? Math.min(min, Math.abs(curr - prev)) : min;\n      prev = curr;\n    }\n    return min;\n  }\n\n  /**\r\n   * Computes an \"ideal\" category based on the absolute bar thickness or, if undefined or null,\r\n   * uses the smallest interval (see computeMinSampleSize) that prevents bar overlapping. This\r\n   * mode currently always generates bars equally sized (until we introduce scriptable options?).\r\n   * @private\r\n   */\n  function computeFitCategoryTraits(index, ruler, options) {\n    var thickness = options.barThickness;\n    var count = ruler.stackCount;\n    var curr = ruler.pixels[index];\n    var min = helpers$1.isNullOrUndef(thickness) ? computeMinSampleSize(ruler.scale, ruler.pixels) : -1;\n    var size, ratio;\n    if (helpers$1.isNullOrUndef(thickness)) {\n      size = min * options.categoryPercentage;\n      ratio = options.barPercentage;\n    } else {\n      // When bar thickness is enforced, category and bar percentages are ignored.\n      // Note(SB): we could add support for relative bar thickness (e.g. barThickness: '50%')\n      // and deprecate barPercentage since this value is ignored when thickness is absolute.\n      size = thickness * count;\n      ratio = 1;\n    }\n    return {\n      chunk: size / count,\n      ratio: ratio,\n      start: curr - size / 2\n    };\n  }\n\n  /**\r\n   * Computes an \"optimal\" category that globally arranges bars side by side (no gap when\r\n   * percentage options are 1), based on the previous and following categories. This mode\r\n   * generates bars with different widths when data are not evenly spaced.\r\n   * @private\r\n   */\n  function computeFlexCategoryTraits(index, ruler, options) {\n    var pixels = ruler.pixels;\n    var curr = pixels[index];\n    var prev = index > 0 ? pixels[index - 1] : null;\n    var next = index < pixels.length - 1 ? pixels[index + 1] : null;\n    var percent = options.categoryPercentage;\n    var start, size;\n    if (prev === null) {\n      // first data: its size is double based on the next point or,\n      // if it's also the last data, we use the scale size.\n      prev = curr - (next === null ? ruler.end - ruler.start : next - curr);\n    }\n    if (next === null) {\n      // last data: its size is also double based on the previous point.\n      next = curr + curr - prev;\n    }\n    start = curr - (curr - Math.min(prev, next)) / 2 * percent;\n    size = Math.abs(next - prev) / 2 * percent;\n    return {\n      chunk: size / ruler.stackCount,\n      ratio: options.barPercentage,\n      start: start\n    };\n  }\n  var controller_bar = core_datasetController.extend({\n    dataElementType: elements.Rectangle,\n    /**\r\n     * @private\r\n     */\n    _dataElementOptions: ['backgroundColor', 'borderColor', 'borderSkipped', 'borderWidth', 'barPercentage', 'barThickness', 'categoryPercentage', 'maxBarThickness', 'minBarLength'],\n    initialize: function () {\n      var me = this;\n      var meta, scaleOpts;\n      core_datasetController.prototype.initialize.apply(me, arguments);\n      meta = me.getMeta();\n      meta.stack = me.getDataset().stack;\n      meta.bar = true;\n      scaleOpts = me._getIndexScale().options;\n      deprecated('bar chart', scaleOpts.barPercentage, 'scales.[x/y]Axes.barPercentage', 'dataset.barPercentage');\n      deprecated('bar chart', scaleOpts.barThickness, 'scales.[x/y]Axes.barThickness', 'dataset.barThickness');\n      deprecated('bar chart', scaleOpts.categoryPercentage, 'scales.[x/y]Axes.categoryPercentage', 'dataset.categoryPercentage');\n      deprecated('bar chart', me._getValueScale().options.minBarLength, 'scales.[x/y]Axes.minBarLength', 'dataset.minBarLength');\n      deprecated('bar chart', scaleOpts.maxBarThickness, 'scales.[x/y]Axes.maxBarThickness', 'dataset.maxBarThickness');\n    },\n    update: function (reset) {\n      var me = this;\n      var rects = me.getMeta().data;\n      var i, ilen;\n      me._ruler = me.getRuler();\n      for (i = 0, ilen = rects.length; i < ilen; ++i) {\n        me.updateElement(rects[i], i, reset);\n      }\n    },\n    updateElement: function (rectangle, index, reset) {\n      var me = this;\n      var meta = me.getMeta();\n      var dataset = me.getDataset();\n      var options = me._resolveDataElementOptions(rectangle, index);\n      rectangle._xScale = me.getScaleForId(meta.xAxisID);\n      rectangle._yScale = me.getScaleForId(meta.yAxisID);\n      rectangle._datasetIndex = me.index;\n      rectangle._index = index;\n      rectangle._model = {\n        backgroundColor: options.backgroundColor,\n        borderColor: options.borderColor,\n        borderSkipped: options.borderSkipped,\n        borderWidth: options.borderWidth,\n        datasetLabel: dataset.label,\n        label: me.chart.data.labels[index]\n      };\n      if (helpers$1.isArray(dataset.data[index])) {\n        rectangle._model.borderSkipped = null;\n      }\n      me._updateElementGeometry(rectangle, index, reset, options);\n      rectangle.pivot();\n    },\n    /**\r\n     * @private\r\n     */\n    _updateElementGeometry: function (rectangle, index, reset, options) {\n      var me = this;\n      var model = rectangle._model;\n      var vscale = me._getValueScale();\n      var base = vscale.getBasePixel();\n      var horizontal = vscale.isHorizontal();\n      var ruler = me._ruler || me.getRuler();\n      var vpixels = me.calculateBarValuePixels(me.index, index, options);\n      var ipixels = me.calculateBarIndexPixels(me.index, index, ruler, options);\n      model.horizontal = horizontal;\n      model.base = reset ? base : vpixels.base;\n      model.x = horizontal ? reset ? base : vpixels.head : ipixels.center;\n      model.y = horizontal ? ipixels.center : reset ? base : vpixels.head;\n      model.height = horizontal ? ipixels.size : undefined;\n      model.width = horizontal ? undefined : ipixels.size;\n    },\n    /**\r\n     * Returns the stacks based on groups and bar visibility.\r\n     * @param {number} [last] - The dataset index\r\n     * @returns {string[]} The list of stack IDs\r\n     * @private\r\n     */\n    _getStacks: function (last) {\n      var me = this;\n      var scale = me._getIndexScale();\n      var metasets = scale._getMatchingVisibleMetas(me._type);\n      var stacked = scale.options.stacked;\n      var ilen = metasets.length;\n      var stacks = [];\n      var i, meta;\n      for (i = 0; i < ilen; ++i) {\n        meta = metasets[i];\n        // stacked   | meta.stack\n        //           | found | not found | undefined\n        // false     |   x   |     x     |     x\n        // true      |       |     x     |\n        // undefined |       |     x     |     x\n        if (stacked === false || stacks.indexOf(meta.stack) === -1 || stacked === undefined && meta.stack === undefined) {\n          stacks.push(meta.stack);\n        }\n        if (meta.index === last) {\n          break;\n        }\n      }\n      return stacks;\n    },\n    /**\r\n     * Returns the effective number of stacks based on groups and bar visibility.\r\n     * @private\r\n     */\n    getStackCount: function () {\n      return this._getStacks().length;\n    },\n    /**\r\n     * Returns the stack index for the given dataset based on groups and bar visibility.\r\n     * @param {number} [datasetIndex] - The dataset index\r\n     * @param {string} [name] - The stack name to find\r\n     * @returns {number} The stack index\r\n     * @private\r\n     */\n    getStackIndex: function (datasetIndex, name) {\n      var stacks = this._getStacks(datasetIndex);\n      var index = name !== undefined ? stacks.indexOf(name) : -1; // indexOf returns -1 if element is not present\n\n      return index === -1 ? stacks.length - 1 : index;\n    },\n    /**\r\n     * @private\r\n     */\n    getRuler: function () {\n      var me = this;\n      var scale = me._getIndexScale();\n      var pixels = [];\n      var i, ilen;\n      for (i = 0, ilen = me.getMeta().data.length; i < ilen; ++i) {\n        pixels.push(scale.getPixelForValue(null, i, me.index));\n      }\n      return {\n        pixels: pixels,\n        start: scale._startPixel,\n        end: scale._endPixel,\n        stackCount: me.getStackCount(),\n        scale: scale\n      };\n    },\n    /**\r\n     * Note: pixel values are not clamped to the scale area.\r\n     * @private\r\n     */\n    calculateBarValuePixels: function (datasetIndex, index, options) {\n      var me = this;\n      var chart = me.chart;\n      var scale = me._getValueScale();\n      var isHorizontal = scale.isHorizontal();\n      var datasets = chart.data.datasets;\n      var metasets = scale._getMatchingVisibleMetas(me._type);\n      var value = scale._parseValue(datasets[datasetIndex].data[index]);\n      var minBarLength = options.minBarLength;\n      var stacked = scale.options.stacked;\n      var stack = me.getMeta().stack;\n      var start = value.start === undefined ? 0 : value.max >= 0 && value.min >= 0 ? value.min : value.max;\n      var length = value.start === undefined ? value.end : value.max >= 0 && value.min >= 0 ? value.max - value.min : value.min - value.max;\n      var ilen = metasets.length;\n      var i, imeta, ivalue, base, head, size, stackLength;\n      if (stacked || stacked === undefined && stack !== undefined) {\n        for (i = 0; i < ilen; ++i) {\n          imeta = metasets[i];\n          if (imeta.index === datasetIndex) {\n            break;\n          }\n          if (imeta.stack === stack) {\n            stackLength = scale._parseValue(datasets[imeta.index].data[index]);\n            ivalue = stackLength.start === undefined ? stackLength.end : stackLength.min >= 0 && stackLength.max >= 0 ? stackLength.max : stackLength.min;\n            if (value.min < 0 && ivalue < 0 || value.max >= 0 && ivalue > 0) {\n              start += ivalue;\n            }\n          }\n        }\n      }\n      base = scale.getPixelForValue(start);\n      head = scale.getPixelForValue(start + length);\n      size = head - base;\n      if (minBarLength !== undefined && Math.abs(size) < minBarLength) {\n        size = minBarLength;\n        if (length >= 0 && !isHorizontal || length < 0 && isHorizontal) {\n          head = base - minBarLength;\n        } else {\n          head = base + minBarLength;\n        }\n      }\n      return {\n        size: size,\n        base: base,\n        head: head,\n        center: head + size / 2\n      };\n    },\n    /**\r\n     * @private\r\n     */\n    calculateBarIndexPixels: function (datasetIndex, index, ruler, options) {\n      var me = this;\n      var range = options.barThickness === 'flex' ? computeFlexCategoryTraits(index, ruler, options) : computeFitCategoryTraits(index, ruler, options);\n      var stackIndex = me.getStackIndex(datasetIndex, me.getMeta().stack);\n      var center = range.start + range.chunk * stackIndex + range.chunk / 2;\n      var size = Math.min(valueOrDefault$3(options.maxBarThickness, Infinity), range.chunk * range.ratio);\n      return {\n        base: center - size / 2,\n        head: center + size / 2,\n        center: center,\n        size: size\n      };\n    },\n    draw: function () {\n      var me = this;\n      var chart = me.chart;\n      var scale = me._getValueScale();\n      var rects = me.getMeta().data;\n      var dataset = me.getDataset();\n      var ilen = rects.length;\n      var i = 0;\n      helpers$1.canvas.clipArea(chart.ctx, chart.chartArea);\n      for (; i < ilen; ++i) {\n        var val = scale._parseValue(dataset.data[i]);\n        if (!isNaN(val.min) && !isNaN(val.max)) {\n          rects[i].draw();\n        }\n      }\n      helpers$1.canvas.unclipArea(chart.ctx);\n    },\n    /**\r\n     * @private\r\n     */\n    _resolveDataElementOptions: function () {\n      var me = this;\n      var values = helpers$1.extend({}, core_datasetController.prototype._resolveDataElementOptions.apply(me, arguments));\n      var indexOpts = me._getIndexScale().options;\n      var valueOpts = me._getValueScale().options;\n      values.barPercentage = valueOrDefault$3(indexOpts.barPercentage, values.barPercentage);\n      values.barThickness = valueOrDefault$3(indexOpts.barThickness, values.barThickness);\n      values.categoryPercentage = valueOrDefault$3(indexOpts.categoryPercentage, values.categoryPercentage);\n      values.maxBarThickness = valueOrDefault$3(indexOpts.maxBarThickness, values.maxBarThickness);\n      values.minBarLength = valueOrDefault$3(valueOpts.minBarLength, values.minBarLength);\n      return values;\n    }\n  });\n  var valueOrDefault$4 = helpers$1.valueOrDefault;\n  var resolve$1 = helpers$1.options.resolve;\n  core_defaults._set('bubble', {\n    hover: {\n      mode: 'single'\n    },\n    scales: {\n      xAxes: [{\n        type: 'linear',\n        // bubble should probably use a linear scale by default\n        position: 'bottom',\n        id: 'x-axis-0' // need an ID so datasets can reference the scale\n      }],\n      yAxes: [{\n        type: 'linear',\n        position: 'left',\n        id: 'y-axis-0'\n      }]\n    },\n    tooltips: {\n      callbacks: {\n        title: function () {\n          // Title doesn't make sense for scatter since we format the data as a point\n          return '';\n        },\n        label: function (item, data) {\n          var datasetLabel = data.datasets[item.datasetIndex].label || '';\n          var dataPoint = data.datasets[item.datasetIndex].data[item.index];\n          return datasetLabel + ': (' + item.xLabel + ', ' + item.yLabel + ', ' + dataPoint.r + ')';\n        }\n      }\n    }\n  });\n  var controller_bubble = core_datasetController.extend({\n    /**\r\n     * @protected\r\n     */\n    dataElementType: elements.Point,\n    /**\r\n     * @private\r\n     */\n    _dataElementOptions: ['backgroundColor', 'borderColor', 'borderWidth', 'hoverBackgroundColor', 'hoverBorderColor', 'hoverBorderWidth', 'hoverRadius', 'hitRadius', 'pointStyle', 'rotation'],\n    /**\r\n     * @protected\r\n     */\n    update: function (reset) {\n      var me = this;\n      var meta = me.getMeta();\n      var points = meta.data;\n\n      // Update Points\n      helpers$1.each(points, function (point, index) {\n        me.updateElement(point, index, reset);\n      });\n    },\n    /**\r\n     * @protected\r\n     */\n    updateElement: function (point, index, reset) {\n      var me = this;\n      var meta = me.getMeta();\n      var custom = point.custom || {};\n      var xScale = me.getScaleForId(meta.xAxisID);\n      var yScale = me.getScaleForId(meta.yAxisID);\n      var options = me._resolveDataElementOptions(point, index);\n      var data = me.getDataset().data[index];\n      var dsIndex = me.index;\n      var x = reset ? xScale.getPixelForDecimal(0.5) : xScale.getPixelForValue(typeof data === 'object' ? data : NaN, index, dsIndex);\n      var y = reset ? yScale.getBasePixel() : yScale.getPixelForValue(data, index, dsIndex);\n      point._xScale = xScale;\n      point._yScale = yScale;\n      point._options = options;\n      point._datasetIndex = dsIndex;\n      point._index = index;\n      point._model = {\n        backgroundColor: options.backgroundColor,\n        borderColor: options.borderColor,\n        borderWidth: options.borderWidth,\n        hitRadius: options.hitRadius,\n        pointStyle: options.pointStyle,\n        rotation: options.rotation,\n        radius: reset ? 0 : options.radius,\n        skip: custom.skip || isNaN(x) || isNaN(y),\n        x: x,\n        y: y\n      };\n      point.pivot();\n    },\n    /**\r\n     * @protected\r\n     */\n    setHoverStyle: function (point) {\n      var model = point._model;\n      var options = point._options;\n      var getHoverColor = helpers$1.getHoverColor;\n      point.$previousStyle = {\n        backgroundColor: model.backgroundColor,\n        borderColor: model.borderColor,\n        borderWidth: model.borderWidth,\n        radius: model.radius\n      };\n      model.backgroundColor = valueOrDefault$4(options.hoverBackgroundColor, getHoverColor(options.backgroundColor));\n      model.borderColor = valueOrDefault$4(options.hoverBorderColor, getHoverColor(options.borderColor));\n      model.borderWidth = valueOrDefault$4(options.hoverBorderWidth, options.borderWidth);\n      model.radius = options.radius + options.hoverRadius;\n    },\n    /**\r\n     * @private\r\n     */\n    _resolveDataElementOptions: function (point, index) {\n      var me = this;\n      var chart = me.chart;\n      var dataset = me.getDataset();\n      var custom = point.custom || {};\n      var data = dataset.data[index] || {};\n      var values = core_datasetController.prototype._resolveDataElementOptions.apply(me, arguments);\n\n      // Scriptable options\n      var context = {\n        chart: chart,\n        dataIndex: index,\n        dataset: dataset,\n        datasetIndex: me.index\n      };\n\n      // In case values were cached (and thus frozen), we need to clone the values\n      if (me._cachedDataOpts === values) {\n        values = helpers$1.extend({}, values);\n      }\n\n      // Custom radius resolution\n      values.radius = resolve$1([custom.radius, data.r, me._config.radius, chart.options.elements.point.radius], context, index);\n      return values;\n    }\n  });\n  var valueOrDefault$5 = helpers$1.valueOrDefault;\n  var PI$1 = Math.PI;\n  var DOUBLE_PI$1 = PI$1 * 2;\n  var HALF_PI$1 = PI$1 / 2;\n  core_defaults._set('doughnut', {\n    animation: {\n      // Boolean - Whether we animate the rotation of the Doughnut\n      animateRotate: true,\n      // Boolean - Whether we animate scaling the Doughnut from the centre\n      animateScale: false\n    },\n    hover: {\n      mode: 'single'\n    },\n    legendCallback: function (chart) {\n      var list = document.createElement('ul');\n      var data = chart.data;\n      var datasets = data.datasets;\n      var labels = data.labels;\n      var i, ilen, listItem, listItemSpan;\n      list.setAttribute('class', chart.id + '-legend');\n      if (datasets.length) {\n        for (i = 0, ilen = datasets[0].data.length; i < ilen; ++i) {\n          listItem = list.appendChild(document.createElement('li'));\n          listItemSpan = listItem.appendChild(document.createElement('span'));\n          listItemSpan.style.backgroundColor = datasets[0].backgroundColor[i];\n          if (labels[i]) {\n            listItem.appendChild(document.createTextNode(labels[i]));\n          }\n        }\n      }\n      return list.outerHTML;\n    },\n    legend: {\n      labels: {\n        generateLabels: function (chart) {\n          var data = chart.data;\n          if (data.labels.length && data.datasets.length) {\n            return data.labels.map(function (label, i) {\n              var meta = chart.getDatasetMeta(0);\n              var style = meta.controller.getStyle(i);\n              return {\n                text: label,\n                fillStyle: style.backgroundColor,\n                strokeStyle: style.borderColor,\n                lineWidth: style.borderWidth,\n                hidden: isNaN(data.datasets[0].data[i]) || meta.data[i].hidden,\n                // Extra data used for toggling the correct item\n                index: i\n              };\n            });\n          }\n          return [];\n        }\n      },\n      onClick: function (e, legendItem) {\n        var index = legendItem.index;\n        var chart = this.chart;\n        var i, ilen, meta;\n        for (i = 0, ilen = (chart.data.datasets || []).length; i < ilen; ++i) {\n          meta = chart.getDatasetMeta(i);\n          // toggle visibility of index if exists\n          if (meta.data[index]) {\n            meta.data[index].hidden = !meta.data[index].hidden;\n          }\n        }\n        chart.update();\n      }\n    },\n    // The percentage of the chart that we cut out of the middle.\n    cutoutPercentage: 50,\n    // The rotation of the chart, where the first data arc begins.\n    rotation: -HALF_PI$1,\n    // The total circumference of the chart.\n    circumference: DOUBLE_PI$1,\n    // Need to override these to give a nice default\n    tooltips: {\n      callbacks: {\n        title: function () {\n          return '';\n        },\n        label: function (tooltipItem, data) {\n          var dataLabel = data.labels[tooltipItem.index];\n          var value = ': ' + data.datasets[tooltipItem.datasetIndex].data[tooltipItem.index];\n          if (helpers$1.isArray(dataLabel)) {\n            // show value on first line of multiline label\n            // need to clone because we are changing the value\n            dataLabel = dataLabel.slice();\n            dataLabel[0] += value;\n          } else {\n            dataLabel += value;\n          }\n          return dataLabel;\n        }\n      }\n    }\n  });\n  var controller_doughnut = core_datasetController.extend({\n    dataElementType: elements.Arc,\n    linkScales: helpers$1.noop,\n    /**\r\n     * @private\r\n     */\n    _dataElementOptions: ['backgroundColor', 'borderColor', 'borderWidth', 'borderAlign', 'hoverBackgroundColor', 'hoverBorderColor', 'hoverBorderWidth'],\n    // Get index of the dataset in relation to the visible datasets. This allows determining the inner and outer radius correctly\n    getRingIndex: function (datasetIndex) {\n      var ringIndex = 0;\n      for (var j = 0; j < datasetIndex; ++j) {\n        if (this.chart.isDatasetVisible(j)) {\n          ++ringIndex;\n        }\n      }\n      return ringIndex;\n    },\n    update: function (reset) {\n      var me = this;\n      var chart = me.chart;\n      var chartArea = chart.chartArea;\n      var opts = chart.options;\n      var ratioX = 1;\n      var ratioY = 1;\n      var offsetX = 0;\n      var offsetY = 0;\n      var meta = me.getMeta();\n      var arcs = meta.data;\n      var cutout = opts.cutoutPercentage / 100 || 0;\n      var circumference = opts.circumference;\n      var chartWeight = me._getRingWeight(me.index);\n      var maxWidth, maxHeight, i, ilen;\n\n      // If the chart's circumference isn't a full circle, calculate size as a ratio of the width/height of the arc\n      if (circumference < DOUBLE_PI$1) {\n        var startAngle = opts.rotation % DOUBLE_PI$1;\n        startAngle += startAngle >= PI$1 ? -DOUBLE_PI$1 : startAngle < -PI$1 ? DOUBLE_PI$1 : 0;\n        var endAngle = startAngle + circumference;\n        var startX = Math.cos(startAngle);\n        var startY = Math.sin(startAngle);\n        var endX = Math.cos(endAngle);\n        var endY = Math.sin(endAngle);\n        var contains0 = startAngle <= 0 && endAngle >= 0 || endAngle >= DOUBLE_PI$1;\n        var contains90 = startAngle <= HALF_PI$1 && endAngle >= HALF_PI$1 || endAngle >= DOUBLE_PI$1 + HALF_PI$1;\n        var contains180 = startAngle === -PI$1 || endAngle >= PI$1;\n        var contains270 = startAngle <= -HALF_PI$1 && endAngle >= -HALF_PI$1 || endAngle >= PI$1 + HALF_PI$1;\n        var minX = contains180 ? -1 : Math.min(startX, startX * cutout, endX, endX * cutout);\n        var minY = contains270 ? -1 : Math.min(startY, startY * cutout, endY, endY * cutout);\n        var maxX = contains0 ? 1 : Math.max(startX, startX * cutout, endX, endX * cutout);\n        var maxY = contains90 ? 1 : Math.max(startY, startY * cutout, endY, endY * cutout);\n        ratioX = (maxX - minX) / 2;\n        ratioY = (maxY - minY) / 2;\n        offsetX = -(maxX + minX) / 2;\n        offsetY = -(maxY + minY) / 2;\n      }\n      for (i = 0, ilen = arcs.length; i < ilen; ++i) {\n        arcs[i]._options = me._resolveDataElementOptions(arcs[i], i);\n      }\n      chart.borderWidth = me.getMaxBorderWidth();\n      maxWidth = (chartArea.right - chartArea.left - chart.borderWidth) / ratioX;\n      maxHeight = (chartArea.bottom - chartArea.top - chart.borderWidth) / ratioY;\n      chart.outerRadius = Math.max(Math.min(maxWidth, maxHeight) / 2, 0);\n      chart.innerRadius = Math.max(chart.outerRadius * cutout, 0);\n      chart.radiusLength = (chart.outerRadius - chart.innerRadius) / (me._getVisibleDatasetWeightTotal() || 1);\n      chart.offsetX = offsetX * chart.outerRadius;\n      chart.offsetY = offsetY * chart.outerRadius;\n      meta.total = me.calculateTotal();\n      me.outerRadius = chart.outerRadius - chart.radiusLength * me._getRingWeightOffset(me.index);\n      me.innerRadius = Math.max(me.outerRadius - chart.radiusLength * chartWeight, 0);\n      for (i = 0, ilen = arcs.length; i < ilen; ++i) {\n        me.updateElement(arcs[i], i, reset);\n      }\n    },\n    updateElement: function (arc, index, reset) {\n      var me = this;\n      var chart = me.chart;\n      var chartArea = chart.chartArea;\n      var opts = chart.options;\n      var animationOpts = opts.animation;\n      var centerX = (chartArea.left + chartArea.right) / 2;\n      var centerY = (chartArea.top + chartArea.bottom) / 2;\n      var startAngle = opts.rotation; // non reset case handled later\n      var endAngle = opts.rotation; // non reset case handled later\n      var dataset = me.getDataset();\n      var circumference = reset && animationOpts.animateRotate ? 0 : arc.hidden ? 0 : me.calculateCircumference(dataset.data[index]) * (opts.circumference / DOUBLE_PI$1);\n      var innerRadius = reset && animationOpts.animateScale ? 0 : me.innerRadius;\n      var outerRadius = reset && animationOpts.animateScale ? 0 : me.outerRadius;\n      var options = arc._options || {};\n      helpers$1.extend(arc, {\n        // Utility\n        _datasetIndex: me.index,\n        _index: index,\n        // Desired view properties\n        _model: {\n          backgroundColor: options.backgroundColor,\n          borderColor: options.borderColor,\n          borderWidth: options.borderWidth,\n          borderAlign: options.borderAlign,\n          x: centerX + chart.offsetX,\n          y: centerY + chart.offsetY,\n          startAngle: startAngle,\n          endAngle: endAngle,\n          circumference: circumference,\n          outerRadius: outerRadius,\n          innerRadius: innerRadius,\n          label: helpers$1.valueAtIndexOrDefault(dataset.label, index, chart.data.labels[index])\n        }\n      });\n      var model = arc._model;\n\n      // Set correct angles if not resetting\n      if (!reset || !animationOpts.animateRotate) {\n        if (index === 0) {\n          model.startAngle = opts.rotation;\n        } else {\n          model.startAngle = me.getMeta().data[index - 1]._model.endAngle;\n        }\n        model.endAngle = model.startAngle + model.circumference;\n      }\n      arc.pivot();\n    },\n    calculateTotal: function () {\n      var dataset = this.getDataset();\n      var meta = this.getMeta();\n      var total = 0;\n      var value;\n      helpers$1.each(meta.data, function (element, index) {\n        value = dataset.data[index];\n        if (!isNaN(value) && !element.hidden) {\n          total += Math.abs(value);\n        }\n      });\n\n      /* if (total === 0) {\r\n      \ttotal = NaN;\r\n      }*/\n\n      return total;\n    },\n    calculateCircumference: function (value) {\n      var total = this.getMeta().total;\n      if (total > 0 && !isNaN(value)) {\n        return DOUBLE_PI$1 * (Math.abs(value) / total);\n      }\n      return 0;\n    },\n    // gets the max border or hover width to properly scale pie charts\n    getMaxBorderWidth: function (arcs) {\n      var me = this;\n      var max = 0;\n      var chart = me.chart;\n      var i, ilen, meta, arc, controller, options, borderWidth, hoverWidth;\n      if (!arcs) {\n        // Find the outmost visible dataset\n        for (i = 0, ilen = chart.data.datasets.length; i < ilen; ++i) {\n          if (chart.isDatasetVisible(i)) {\n            meta = chart.getDatasetMeta(i);\n            arcs = meta.data;\n            if (i !== me.index) {\n              controller = meta.controller;\n            }\n            break;\n          }\n        }\n      }\n      if (!arcs) {\n        return 0;\n      }\n      for (i = 0, ilen = arcs.length; i < ilen; ++i) {\n        arc = arcs[i];\n        if (controller) {\n          controller._configure();\n          options = controller._resolveDataElementOptions(arc, i);\n        } else {\n          options = arc._options;\n        }\n        if (options.borderAlign !== 'inner') {\n          borderWidth = options.borderWidth;\n          hoverWidth = options.hoverBorderWidth;\n          max = borderWidth > max ? borderWidth : max;\n          max = hoverWidth > max ? hoverWidth : max;\n        }\n      }\n      return max;\n    },\n    /**\r\n     * @protected\r\n     */\n    setHoverStyle: function (arc) {\n      var model = arc._model;\n      var options = arc._options;\n      var getHoverColor = helpers$1.getHoverColor;\n      arc.$previousStyle = {\n        backgroundColor: model.backgroundColor,\n        borderColor: model.borderColor,\n        borderWidth: model.borderWidth\n      };\n      model.backgroundColor = valueOrDefault$5(options.hoverBackgroundColor, getHoverColor(options.backgroundColor));\n      model.borderColor = valueOrDefault$5(options.hoverBorderColor, getHoverColor(options.borderColor));\n      model.borderWidth = valueOrDefault$5(options.hoverBorderWidth, options.borderWidth);\n    },\n    /**\r\n     * Get radius length offset of the dataset in relation to the visible datasets weights. This allows determining the inner and outer radius correctly\r\n     * @private\r\n     */\n    _getRingWeightOffset: function (datasetIndex) {\n      var ringWeightOffset = 0;\n      for (var i = 0; i < datasetIndex; ++i) {\n        if (this.chart.isDatasetVisible(i)) {\n          ringWeightOffset += this._getRingWeight(i);\n        }\n      }\n      return ringWeightOffset;\n    },\n    /**\r\n     * @private\r\n     */\n    _getRingWeight: function (dataSetIndex) {\n      return Math.max(valueOrDefault$5(this.chart.data.datasets[dataSetIndex].weight, 1), 0);\n    },\n    /**\r\n     * Returns the sum of all visibile data set weights.  This value can be 0.\r\n     * @private\r\n     */\n    _getVisibleDatasetWeightTotal: function () {\n      return this._getRingWeightOffset(this.chart.data.datasets.length);\n    }\n  });\n  core_defaults._set('horizontalBar', {\n    hover: {\n      mode: 'index',\n      axis: 'y'\n    },\n    scales: {\n      xAxes: [{\n        type: 'linear',\n        position: 'bottom'\n      }],\n      yAxes: [{\n        type: 'category',\n        position: 'left',\n        offset: true,\n        gridLines: {\n          offsetGridLines: true\n        }\n      }]\n    },\n    elements: {\n      rectangle: {\n        borderSkipped: 'left'\n      }\n    },\n    tooltips: {\n      mode: 'index',\n      axis: 'y'\n    }\n  });\n  core_defaults._set('global', {\n    datasets: {\n      horizontalBar: {\n        categoryPercentage: 0.8,\n        barPercentage: 0.9\n      }\n    }\n  });\n  var controller_horizontalBar = controller_bar.extend({\n    /**\r\n     * @private\r\n     */\n    _getValueScaleId: function () {\n      return this.getMeta().xAxisID;\n    },\n    /**\r\n     * @private\r\n     */\n    _getIndexScaleId: function () {\n      return this.getMeta().yAxisID;\n    }\n  });\n  var valueOrDefault$6 = helpers$1.valueOrDefault;\n  var resolve$2 = helpers$1.options.resolve;\n  var isPointInArea = helpers$1.canvas._isPointInArea;\n  core_defaults._set('line', {\n    showLines: true,\n    spanGaps: false,\n    hover: {\n      mode: 'label'\n    },\n    scales: {\n      xAxes: [{\n        type: 'category',\n        id: 'x-axis-0'\n      }],\n      yAxes: [{\n        type: 'linear',\n        id: 'y-axis-0'\n      }]\n    }\n  });\n  function scaleClip(scale, halfBorderWidth) {\n    var tickOpts = scale && scale.options.ticks || {};\n    var reverse = tickOpts.reverse;\n    var min = tickOpts.min === undefined ? halfBorderWidth : 0;\n    var max = tickOpts.max === undefined ? halfBorderWidth : 0;\n    return {\n      start: reverse ? max : min,\n      end: reverse ? min : max\n    };\n  }\n  function defaultClip(xScale, yScale, borderWidth) {\n    var halfBorderWidth = borderWidth / 2;\n    var x = scaleClip(xScale, halfBorderWidth);\n    var y = scaleClip(yScale, halfBorderWidth);\n    return {\n      top: y.end,\n      right: x.end,\n      bottom: y.start,\n      left: x.start\n    };\n  }\n  function toClip(value) {\n    var t, r, b, l;\n    if (helpers$1.isObject(value)) {\n      t = value.top;\n      r = value.right;\n      b = value.bottom;\n      l = value.left;\n    } else {\n      t = r = b = l = value;\n    }\n    return {\n      top: t,\n      right: r,\n      bottom: b,\n      left: l\n    };\n  }\n  var controller_line = core_datasetController.extend({\n    datasetElementType: elements.Line,\n    dataElementType: elements.Point,\n    /**\r\n     * @private\r\n     */\n    _datasetElementOptions: ['backgroundColor', 'borderCapStyle', 'borderColor', 'borderDash', 'borderDashOffset', 'borderJoinStyle', 'borderWidth', 'cubicInterpolationMode', 'fill'],\n    /**\r\n     * @private\r\n     */\n    _dataElementOptions: {\n      backgroundColor: 'pointBackgroundColor',\n      borderColor: 'pointBorderColor',\n      borderWidth: 'pointBorderWidth',\n      hitRadius: 'pointHitRadius',\n      hoverBackgroundColor: 'pointHoverBackgroundColor',\n      hoverBorderColor: 'pointHoverBorderColor',\n      hoverBorderWidth: 'pointHoverBorderWidth',\n      hoverRadius: 'pointHoverRadius',\n      pointStyle: 'pointStyle',\n      radius: 'pointRadius',\n      rotation: 'pointRotation'\n    },\n    update: function (reset) {\n      var me = this;\n      var meta = me.getMeta();\n      var line = meta.dataset;\n      var points = meta.data || [];\n      var options = me.chart.options;\n      var config = me._config;\n      var showLine = me._showLine = valueOrDefault$6(config.showLine, options.showLines);\n      var i, ilen;\n      me._xScale = me.getScaleForId(meta.xAxisID);\n      me._yScale = me.getScaleForId(meta.yAxisID);\n\n      // Update Line\n      if (showLine) {\n        // Compatibility: If the properties are defined with only the old name, use those values\n        if (config.tension !== undefined && config.lineTension === undefined) {\n          config.lineTension = config.tension;\n        }\n\n        // Utility\n        line._scale = me._yScale;\n        line._datasetIndex = me.index;\n        // Data\n        line._children = points;\n        // Model\n        line._model = me._resolveDatasetElementOptions(line);\n        line.pivot();\n      }\n\n      // Update Points\n      for (i = 0, ilen = points.length; i < ilen; ++i) {\n        me.updateElement(points[i], i, reset);\n      }\n      if (showLine && line._model.tension !== 0) {\n        me.updateBezierControlPoints();\n      }\n\n      // Now pivot the point for animation\n      for (i = 0, ilen = points.length; i < ilen; ++i) {\n        points[i].pivot();\n      }\n    },\n    updateElement: function (point, index, reset) {\n      var me = this;\n      var meta = me.getMeta();\n      var custom = point.custom || {};\n      var dataset = me.getDataset();\n      var datasetIndex = me.index;\n      var value = dataset.data[index];\n      var xScale = me._xScale;\n      var yScale = me._yScale;\n      var lineModel = meta.dataset._model;\n      var x, y;\n      var options = me._resolveDataElementOptions(point, index);\n      x = xScale.getPixelForValue(typeof value === 'object' ? value : NaN, index, datasetIndex);\n      y = reset ? yScale.getBasePixel() : me.calculatePointY(value, index, datasetIndex);\n\n      // Utility\n      point._xScale = xScale;\n      point._yScale = yScale;\n      point._options = options;\n      point._datasetIndex = datasetIndex;\n      point._index = index;\n\n      // Desired view properties\n      point._model = {\n        x: x,\n        y: y,\n        skip: custom.skip || isNaN(x) || isNaN(y),\n        // Appearance\n        radius: options.radius,\n        pointStyle: options.pointStyle,\n        rotation: options.rotation,\n        backgroundColor: options.backgroundColor,\n        borderColor: options.borderColor,\n        borderWidth: options.borderWidth,\n        tension: valueOrDefault$6(custom.tension, lineModel ? lineModel.tension : 0),\n        steppedLine: lineModel ? lineModel.steppedLine : false,\n        // Tooltip\n        hitRadius: options.hitRadius\n      };\n    },\n    /**\r\n     * @private\r\n     */\n    _resolveDatasetElementOptions: function (element) {\n      var me = this;\n      var config = me._config;\n      var custom = element.custom || {};\n      var options = me.chart.options;\n      var lineOptions = options.elements.line;\n      var values = core_datasetController.prototype._resolveDatasetElementOptions.apply(me, arguments);\n\n      // The default behavior of lines is to break at null values, according\n      // to https://github.com/chartjs/Chart.js/issues/2435#issuecomment-216718158\n      // This option gives lines the ability to span gaps\n      values.spanGaps = valueOrDefault$6(config.spanGaps, options.spanGaps);\n      values.tension = valueOrDefault$6(config.lineTension, lineOptions.tension);\n      values.steppedLine = resolve$2([custom.steppedLine, config.steppedLine, lineOptions.stepped]);\n      values.clip = toClip(valueOrDefault$6(config.clip, defaultClip(me._xScale, me._yScale, values.borderWidth)));\n      return values;\n    },\n    calculatePointY: function (value, index, datasetIndex) {\n      var me = this;\n      var chart = me.chart;\n      var yScale = me._yScale;\n      var sumPos = 0;\n      var sumNeg = 0;\n      var i, ds, dsMeta, stackedRightValue, rightValue, metasets, ilen;\n      if (yScale.options.stacked) {\n        rightValue = +yScale.getRightValue(value);\n        metasets = chart._getSortedVisibleDatasetMetas();\n        ilen = metasets.length;\n        for (i = 0; i < ilen; ++i) {\n          dsMeta = metasets[i];\n          if (dsMeta.index === datasetIndex) {\n            break;\n          }\n          ds = chart.data.datasets[dsMeta.index];\n          if (dsMeta.type === 'line' && dsMeta.yAxisID === yScale.id) {\n            stackedRightValue = +yScale.getRightValue(ds.data[index]);\n            if (stackedRightValue < 0) {\n              sumNeg += stackedRightValue || 0;\n            } else {\n              sumPos += stackedRightValue || 0;\n            }\n          }\n        }\n        if (rightValue < 0) {\n          return yScale.getPixelForValue(sumNeg + rightValue);\n        }\n        return yScale.getPixelForValue(sumPos + rightValue);\n      }\n      return yScale.getPixelForValue(value);\n    },\n    updateBezierControlPoints: function () {\n      var me = this;\n      var chart = me.chart;\n      var meta = me.getMeta();\n      var lineModel = meta.dataset._model;\n      var area = chart.chartArea;\n      var points = meta.data || [];\n      var i, ilen, model, controlPoints;\n\n      // Only consider points that are drawn in case the spanGaps option is used\n      if (lineModel.spanGaps) {\n        points = points.filter(function (pt) {\n          return !pt._model.skip;\n        });\n      }\n      function capControlPoint(pt, min, max) {\n        return Math.max(Math.min(pt, max), min);\n      }\n      if (lineModel.cubicInterpolationMode === 'monotone') {\n        helpers$1.splineCurveMonotone(points);\n      } else {\n        for (i = 0, ilen = points.length; i < ilen; ++i) {\n          model = points[i]._model;\n          controlPoints = helpers$1.splineCurve(helpers$1.previousItem(points, i)._model, model, helpers$1.nextItem(points, i)._model, lineModel.tension);\n          model.controlPointPreviousX = controlPoints.previous.x;\n          model.controlPointPreviousY = controlPoints.previous.y;\n          model.controlPointNextX = controlPoints.next.x;\n          model.controlPointNextY = controlPoints.next.y;\n        }\n      }\n      if (chart.options.elements.line.capBezierPoints) {\n        for (i = 0, ilen = points.length; i < ilen; ++i) {\n          model = points[i]._model;\n          if (isPointInArea(model, area)) {\n            if (i > 0 && isPointInArea(points[i - 1]._model, area)) {\n              model.controlPointPreviousX = capControlPoint(model.controlPointPreviousX, area.left, area.right);\n              model.controlPointPreviousY = capControlPoint(model.controlPointPreviousY, area.top, area.bottom);\n            }\n            if (i < points.length - 1 && isPointInArea(points[i + 1]._model, area)) {\n              model.controlPointNextX = capControlPoint(model.controlPointNextX, area.left, area.right);\n              model.controlPointNextY = capControlPoint(model.controlPointNextY, area.top, area.bottom);\n            }\n          }\n        }\n      }\n    },\n    draw: function () {\n      var me = this;\n      var chart = me.chart;\n      var meta = me.getMeta();\n      var points = meta.data || [];\n      var area = chart.chartArea;\n      var canvas = chart.canvas;\n      var i = 0;\n      var ilen = points.length;\n      var clip;\n      if (me._showLine) {\n        clip = meta.dataset._model.clip;\n        helpers$1.canvas.clipArea(chart.ctx, {\n          left: clip.left === false ? 0 : area.left - clip.left,\n          right: clip.right === false ? canvas.width : area.right + clip.right,\n          top: clip.top === false ? 0 : area.top - clip.top,\n          bottom: clip.bottom === false ? canvas.height : area.bottom + clip.bottom\n        });\n        meta.dataset.draw();\n        helpers$1.canvas.unclipArea(chart.ctx);\n      }\n\n      // Draw the points\n      for (; i < ilen; ++i) {\n        points[i].draw(area);\n      }\n    },\n    /**\r\n     * @protected\r\n     */\n    setHoverStyle: function (point) {\n      var model = point._model;\n      var options = point._options;\n      var getHoverColor = helpers$1.getHoverColor;\n      point.$previousStyle = {\n        backgroundColor: model.backgroundColor,\n        borderColor: model.borderColor,\n        borderWidth: model.borderWidth,\n        radius: model.radius\n      };\n      model.backgroundColor = valueOrDefault$6(options.hoverBackgroundColor, getHoverColor(options.backgroundColor));\n      model.borderColor = valueOrDefault$6(options.hoverBorderColor, getHoverColor(options.borderColor));\n      model.borderWidth = valueOrDefault$6(options.hoverBorderWidth, options.borderWidth);\n      model.radius = valueOrDefault$6(options.hoverRadius, options.radius);\n    }\n  });\n  var resolve$3 = helpers$1.options.resolve;\n  core_defaults._set('polarArea', {\n    scale: {\n      type: 'radialLinear',\n      angleLines: {\n        display: false\n      },\n      gridLines: {\n        circular: true\n      },\n      pointLabels: {\n        display: false\n      },\n      ticks: {\n        beginAtZero: true\n      }\n    },\n    // Boolean - Whether to animate the rotation of the chart\n    animation: {\n      animateRotate: true,\n      animateScale: true\n    },\n    startAngle: -0.5 * Math.PI,\n    legendCallback: function (chart) {\n      var list = document.createElement('ul');\n      var data = chart.data;\n      var datasets = data.datasets;\n      var labels = data.labels;\n      var i, ilen, listItem, listItemSpan;\n      list.setAttribute('class', chart.id + '-legend');\n      if (datasets.length) {\n        for (i = 0, ilen = datasets[0].data.length; i < ilen; ++i) {\n          listItem = list.appendChild(document.createElement('li'));\n          listItemSpan = listItem.appendChild(document.createElement('span'));\n          listItemSpan.style.backgroundColor = datasets[0].backgroundColor[i];\n          if (labels[i]) {\n            listItem.appendChild(document.createTextNode(labels[i]));\n          }\n        }\n      }\n      return list.outerHTML;\n    },\n    legend: {\n      labels: {\n        generateLabels: function (chart) {\n          var data = chart.data;\n          if (data.labels.length && data.datasets.length) {\n            return data.labels.map(function (label, i) {\n              var meta = chart.getDatasetMeta(0);\n              var style = meta.controller.getStyle(i);\n              return {\n                text: label,\n                fillStyle: style.backgroundColor,\n                strokeStyle: style.borderColor,\n                lineWidth: style.borderWidth,\n                hidden: isNaN(data.datasets[0].data[i]) || meta.data[i].hidden,\n                // Extra data used for toggling the correct item\n                index: i\n              };\n            });\n          }\n          return [];\n        }\n      },\n      onClick: function (e, legendItem) {\n        var index = legendItem.index;\n        var chart = this.chart;\n        var i, ilen, meta;\n        for (i = 0, ilen = (chart.data.datasets || []).length; i < ilen; ++i) {\n          meta = chart.getDatasetMeta(i);\n          meta.data[index].hidden = !meta.data[index].hidden;\n        }\n        chart.update();\n      }\n    },\n    // Need to override these to give a nice default\n    tooltips: {\n      callbacks: {\n        title: function () {\n          return '';\n        },\n        label: function (item, data) {\n          return data.labels[item.index] + ': ' + item.yLabel;\n        }\n      }\n    }\n  });\n  var controller_polarArea = core_datasetController.extend({\n    dataElementType: elements.Arc,\n    linkScales: helpers$1.noop,\n    /**\r\n     * @private\r\n     */\n    _dataElementOptions: ['backgroundColor', 'borderColor', 'borderWidth', 'borderAlign', 'hoverBackgroundColor', 'hoverBorderColor', 'hoverBorderWidth'],\n    /**\r\n     * @private\r\n     */\n    _getIndexScaleId: function () {\n      return this.chart.scale.id;\n    },\n    /**\r\n     * @private\r\n     */\n    _getValueScaleId: function () {\n      return this.chart.scale.id;\n    },\n    update: function (reset) {\n      var me = this;\n      var dataset = me.getDataset();\n      var meta = me.getMeta();\n      var start = me.chart.options.startAngle || 0;\n      var starts = me._starts = [];\n      var angles = me._angles = [];\n      var arcs = meta.data;\n      var i, ilen, angle;\n      me._updateRadius();\n      meta.count = me.countVisibleElements();\n      for (i = 0, ilen = dataset.data.length; i < ilen; i++) {\n        starts[i] = start;\n        angle = me._computeAngle(i);\n        angles[i] = angle;\n        start += angle;\n      }\n      for (i = 0, ilen = arcs.length; i < ilen; ++i) {\n        arcs[i]._options = me._resolveDataElementOptions(arcs[i], i);\n        me.updateElement(arcs[i], i, reset);\n      }\n    },\n    /**\r\n     * @private\r\n     */\n    _updateRadius: function () {\n      var me = this;\n      var chart = me.chart;\n      var chartArea = chart.chartArea;\n      var opts = chart.options;\n      var minSize = Math.min(chartArea.right - chartArea.left, chartArea.bottom - chartArea.top);\n      chart.outerRadius = Math.max(minSize / 2, 0);\n      chart.innerRadius = Math.max(opts.cutoutPercentage ? chart.outerRadius / 100 * opts.cutoutPercentage : 1, 0);\n      chart.radiusLength = (chart.outerRadius - chart.innerRadius) / chart.getVisibleDatasetCount();\n      me.outerRadius = chart.outerRadius - chart.radiusLength * me.index;\n      me.innerRadius = me.outerRadius - chart.radiusLength;\n    },\n    updateElement: function (arc, index, reset) {\n      var me = this;\n      var chart = me.chart;\n      var dataset = me.getDataset();\n      var opts = chart.options;\n      var animationOpts = opts.animation;\n      var scale = chart.scale;\n      var labels = chart.data.labels;\n      var centerX = scale.xCenter;\n      var centerY = scale.yCenter;\n\n      // var negHalfPI = -0.5 * Math.PI;\n      var datasetStartAngle = opts.startAngle;\n      var distance = arc.hidden ? 0 : scale.getDistanceFromCenterForValue(dataset.data[index]);\n      var startAngle = me._starts[index];\n      var endAngle = startAngle + (arc.hidden ? 0 : me._angles[index]);\n      var resetRadius = animationOpts.animateScale ? 0 : scale.getDistanceFromCenterForValue(dataset.data[index]);\n      var options = arc._options || {};\n      helpers$1.extend(arc, {\n        // Utility\n        _datasetIndex: me.index,\n        _index: index,\n        _scale: scale,\n        // Desired view properties\n        _model: {\n          backgroundColor: options.backgroundColor,\n          borderColor: options.borderColor,\n          borderWidth: options.borderWidth,\n          borderAlign: options.borderAlign,\n          x: centerX,\n          y: centerY,\n          innerRadius: 0,\n          outerRadius: reset ? resetRadius : distance,\n          startAngle: reset && animationOpts.animateRotate ? datasetStartAngle : startAngle,\n          endAngle: reset && animationOpts.animateRotate ? datasetStartAngle : endAngle,\n          label: helpers$1.valueAtIndexOrDefault(labels, index, labels[index])\n        }\n      });\n      arc.pivot();\n    },\n    countVisibleElements: function () {\n      var dataset = this.getDataset();\n      var meta = this.getMeta();\n      var count = 0;\n      helpers$1.each(meta.data, function (element, index) {\n        if (!isNaN(dataset.data[index]) && !element.hidden) {\n          count++;\n        }\n      });\n      return count;\n    },\n    /**\r\n     * @protected\r\n     */\n    setHoverStyle: function (arc) {\n      var model = arc._model;\n      var options = arc._options;\n      var getHoverColor = helpers$1.getHoverColor;\n      var valueOrDefault = helpers$1.valueOrDefault;\n      arc.$previousStyle = {\n        backgroundColor: model.backgroundColor,\n        borderColor: model.borderColor,\n        borderWidth: model.borderWidth\n      };\n      model.backgroundColor = valueOrDefault(options.hoverBackgroundColor, getHoverColor(options.backgroundColor));\n      model.borderColor = valueOrDefault(options.hoverBorderColor, getHoverColor(options.borderColor));\n      model.borderWidth = valueOrDefault(options.hoverBorderWidth, options.borderWidth);\n    },\n    /**\r\n     * @private\r\n     */\n    _computeAngle: function (index) {\n      var me = this;\n      var count = this.getMeta().count;\n      var dataset = me.getDataset();\n      var meta = me.getMeta();\n      if (isNaN(dataset.data[index]) || meta.data[index].hidden) {\n        return 0;\n      }\n\n      // Scriptable options\n      var context = {\n        chart: me.chart,\n        dataIndex: index,\n        dataset: dataset,\n        datasetIndex: me.index\n      };\n      return resolve$3([me.chart.options.elements.arc.angle, 2 * Math.PI / count], context, index);\n    }\n  });\n  core_defaults._set('pie', helpers$1.clone(core_defaults.doughnut));\n  core_defaults._set('pie', {\n    cutoutPercentage: 0\n  });\n\n  // Pie charts are Doughnut chart with different defaults\n  var controller_pie = controller_doughnut;\n  var valueOrDefault$7 = helpers$1.valueOrDefault;\n  core_defaults._set('radar', {\n    spanGaps: false,\n    scale: {\n      type: 'radialLinear'\n    },\n    elements: {\n      line: {\n        fill: 'start',\n        tension: 0 // no bezier in radar\n      }\n    }\n  });\n  var controller_radar = core_datasetController.extend({\n    datasetElementType: elements.Line,\n    dataElementType: elements.Point,\n    linkScales: helpers$1.noop,\n    /**\r\n     * @private\r\n     */\n    _datasetElementOptions: ['backgroundColor', 'borderWidth', 'borderColor', 'borderCapStyle', 'borderDash', 'borderDashOffset', 'borderJoinStyle', 'fill'],\n    /**\r\n     * @private\r\n     */\n    _dataElementOptions: {\n      backgroundColor: 'pointBackgroundColor',\n      borderColor: 'pointBorderColor',\n      borderWidth: 'pointBorderWidth',\n      hitRadius: 'pointHitRadius',\n      hoverBackgroundColor: 'pointHoverBackgroundColor',\n      hoverBorderColor: 'pointHoverBorderColor',\n      hoverBorderWidth: 'pointHoverBorderWidth',\n      hoverRadius: 'pointHoverRadius',\n      pointStyle: 'pointStyle',\n      radius: 'pointRadius',\n      rotation: 'pointRotation'\n    },\n    /**\r\n     * @private\r\n     */\n    _getIndexScaleId: function () {\n      return this.chart.scale.id;\n    },\n    /**\r\n     * @private\r\n     */\n    _getValueScaleId: function () {\n      return this.chart.scale.id;\n    },\n    update: function (reset) {\n      var me = this;\n      var meta = me.getMeta();\n      var line = meta.dataset;\n      var points = meta.data || [];\n      var scale = me.chart.scale;\n      var config = me._config;\n      var i, ilen;\n\n      // Compatibility: If the properties are defined with only the old name, use those values\n      if (config.tension !== undefined && config.lineTension === undefined) {\n        config.lineTension = config.tension;\n      }\n\n      // Utility\n      line._scale = scale;\n      line._datasetIndex = me.index;\n      // Data\n      line._children = points;\n      line._loop = true;\n      // Model\n      line._model = me._resolveDatasetElementOptions(line);\n      line.pivot();\n\n      // Update Points\n      for (i = 0, ilen = points.length; i < ilen; ++i) {\n        me.updateElement(points[i], i, reset);\n      }\n\n      // Update bezier control points\n      me.updateBezierControlPoints();\n\n      // Now pivot the point for animation\n      for (i = 0, ilen = points.length; i < ilen; ++i) {\n        points[i].pivot();\n      }\n    },\n    updateElement: function (point, index, reset) {\n      var me = this;\n      var custom = point.custom || {};\n      var dataset = me.getDataset();\n      var scale = me.chart.scale;\n      var pointPosition = scale.getPointPositionForValue(index, dataset.data[index]);\n      var options = me._resolveDataElementOptions(point, index);\n      var lineModel = me.getMeta().dataset._model;\n      var x = reset ? scale.xCenter : pointPosition.x;\n      var y = reset ? scale.yCenter : pointPosition.y;\n\n      // Utility\n      point._scale = scale;\n      point._options = options;\n      point._datasetIndex = me.index;\n      point._index = index;\n\n      // Desired view properties\n      point._model = {\n        x: x,\n        // value not used in dataset scale, but we want a consistent API between scales\n        y: y,\n        skip: custom.skip || isNaN(x) || isNaN(y),\n        // Appearance\n        radius: options.radius,\n        pointStyle: options.pointStyle,\n        rotation: options.rotation,\n        backgroundColor: options.backgroundColor,\n        borderColor: options.borderColor,\n        borderWidth: options.borderWidth,\n        tension: valueOrDefault$7(custom.tension, lineModel ? lineModel.tension : 0),\n        // Tooltip\n        hitRadius: options.hitRadius\n      };\n    },\n    /**\r\n     * @private\r\n     */\n    _resolveDatasetElementOptions: function () {\n      var me = this;\n      var config = me._config;\n      var options = me.chart.options;\n      var values = core_datasetController.prototype._resolveDatasetElementOptions.apply(me, arguments);\n      values.spanGaps = valueOrDefault$7(config.spanGaps, options.spanGaps);\n      values.tension = valueOrDefault$7(config.lineTension, options.elements.line.tension);\n      return values;\n    },\n    updateBezierControlPoints: function () {\n      var me = this;\n      var meta = me.getMeta();\n      var area = me.chart.chartArea;\n      var points = meta.data || [];\n      var i, ilen, model, controlPoints;\n\n      // Only consider points that are drawn in case the spanGaps option is used\n      if (meta.dataset._model.spanGaps) {\n        points = points.filter(function (pt) {\n          return !pt._model.skip;\n        });\n      }\n      function capControlPoint(pt, min, max) {\n        return Math.max(Math.min(pt, max), min);\n      }\n      for (i = 0, ilen = points.length; i < ilen; ++i) {\n        model = points[i]._model;\n        controlPoints = helpers$1.splineCurve(helpers$1.previousItem(points, i, true)._model, model, helpers$1.nextItem(points, i, true)._model, model.tension);\n\n        // Prevent the bezier going outside of the bounds of the graph\n        model.controlPointPreviousX = capControlPoint(controlPoints.previous.x, area.left, area.right);\n        model.controlPointPreviousY = capControlPoint(controlPoints.previous.y, area.top, area.bottom);\n        model.controlPointNextX = capControlPoint(controlPoints.next.x, area.left, area.right);\n        model.controlPointNextY = capControlPoint(controlPoints.next.y, area.top, area.bottom);\n      }\n    },\n    setHoverStyle: function (point) {\n      var model = point._model;\n      var options = point._options;\n      var getHoverColor = helpers$1.getHoverColor;\n      point.$previousStyle = {\n        backgroundColor: model.backgroundColor,\n        borderColor: model.borderColor,\n        borderWidth: model.borderWidth,\n        radius: model.radius\n      };\n      model.backgroundColor = valueOrDefault$7(options.hoverBackgroundColor, getHoverColor(options.backgroundColor));\n      model.borderColor = valueOrDefault$7(options.hoverBorderColor, getHoverColor(options.borderColor));\n      model.borderWidth = valueOrDefault$7(options.hoverBorderWidth, options.borderWidth);\n      model.radius = valueOrDefault$7(options.hoverRadius, options.radius);\n    }\n  });\n  core_defaults._set('scatter', {\n    hover: {\n      mode: 'single'\n    },\n    scales: {\n      xAxes: [{\n        id: 'x-axis-1',\n        // need an ID so datasets can reference the scale\n        type: 'linear',\n        // scatter should not use a category axis\n        position: 'bottom'\n      }],\n      yAxes: [{\n        id: 'y-axis-1',\n        type: 'linear',\n        position: 'left'\n      }]\n    },\n    tooltips: {\n      callbacks: {\n        title: function () {\n          return ''; // doesn't make sense for scatter since data are formatted as a point\n        },\n        label: function (item) {\n          return '(' + item.xLabel + ', ' + item.yLabel + ')';\n        }\n      }\n    }\n  });\n  core_defaults._set('global', {\n    datasets: {\n      scatter: {\n        showLine: false\n      }\n    }\n  });\n\n  // Scatter charts use line controllers\n  var controller_scatter = controller_line;\n\n  // NOTE export a map in which the key represents the controller type, not\n  // the class, and so must be CamelCase in order to be correctly retrieved\n  // by the controller in core.controller.js (`controllers[meta.type]`).\n\n  var controllers = {\n    bar: controller_bar,\n    bubble: controller_bubble,\n    doughnut: controller_doughnut,\n    horizontalBar: controller_horizontalBar,\n    line: controller_line,\n    polarArea: controller_polarArea,\n    pie: controller_pie,\n    radar: controller_radar,\n    scatter: controller_scatter\n  };\n\n  /**\r\n   * Helper function to get relative position for an event\r\n   * @param {Event|IEvent} event - The event to get the position for\r\n   * @param {Chart} chart - The chart\r\n   * @returns {object} the event position\r\n   */\n  function getRelativePosition(e, chart) {\n    if (e.native) {\n      return {\n        x: e.x,\n        y: e.y\n      };\n    }\n    return helpers$1.getRelativePosition(e, chart);\n  }\n\n  /**\r\n   * Helper function to traverse all of the visible elements in the chart\r\n   * @param {Chart} chart - the chart\r\n   * @param {function} handler - the callback to execute for each visible item\r\n   */\n  function parseVisibleItems(chart, handler) {\n    var metasets = chart._getSortedVisibleDatasetMetas();\n    var metadata, i, j, ilen, jlen, element;\n    for (i = 0, ilen = metasets.length; i < ilen; ++i) {\n      metadata = metasets[i].data;\n      for (j = 0, jlen = metadata.length; j < jlen; ++j) {\n        element = metadata[j];\n        if (!element._view.skip) {\n          handler(element);\n        }\n      }\n    }\n  }\n\n  /**\r\n   * Helper function to get the items that intersect the event position\r\n   * @param {ChartElement[]} items - elements to filter\r\n   * @param {object} position - the point to be nearest to\r\n   * @return {ChartElement[]} the nearest items\r\n   */\n  function getIntersectItems(chart, position) {\n    var elements = [];\n    parseVisibleItems(chart, function (element) {\n      if (element.inRange(position.x, position.y)) {\n        elements.push(element);\n      }\n    });\n    return elements;\n  }\n\n  /**\r\n   * Helper function to get the items nearest to the event position considering all visible items in teh chart\r\n   * @param {Chart} chart - the chart to look at elements from\r\n   * @param {object} position - the point to be nearest to\r\n   * @param {boolean} intersect - if true, only consider items that intersect the position\r\n   * @param {function} distanceMetric - function to provide the distance between points\r\n   * @return {ChartElement[]} the nearest items\r\n   */\n  function getNearestItems(chart, position, intersect, distanceMetric) {\n    var minDistance = Number.POSITIVE_INFINITY;\n    var nearestItems = [];\n    parseVisibleItems(chart, function (element) {\n      if (intersect && !element.inRange(position.x, position.y)) {\n        return;\n      }\n      var center = element.getCenterPoint();\n      var distance = distanceMetric(position, center);\n      if (distance < minDistance) {\n        nearestItems = [element];\n        minDistance = distance;\n      } else if (distance === minDistance) {\n        // Can have multiple items at the same distance in which case we sort by size\n        nearestItems.push(element);\n      }\n    });\n    return nearestItems;\n  }\n\n  /**\r\n   * Get a distance metric function for two points based on the\r\n   * axis mode setting\r\n   * @param {string} axis - the axis mode. x|y|xy\r\n   */\n  function getDistanceMetricForAxis(axis) {\n    var useX = axis.indexOf('x') !== -1;\n    var useY = axis.indexOf('y') !== -1;\n    return function (pt1, pt2) {\n      var deltaX = useX ? Math.abs(pt1.x - pt2.x) : 0;\n      var deltaY = useY ? Math.abs(pt1.y - pt2.y) : 0;\n      return Math.sqrt(Math.pow(deltaX, 2) + Math.pow(deltaY, 2));\n    };\n  }\n  function indexMode(chart, e, options) {\n    var position = getRelativePosition(e, chart);\n    // Default axis for index mode is 'x' to match old behaviour\n    options.axis = options.axis || 'x';\n    var distanceMetric = getDistanceMetricForAxis(options.axis);\n    var items = options.intersect ? getIntersectItems(chart, position) : getNearestItems(chart, position, false, distanceMetric);\n    var elements = [];\n    if (!items.length) {\n      return [];\n    }\n    chart._getSortedVisibleDatasetMetas().forEach(function (meta) {\n      var element = meta.data[items[0]._index];\n\n      // don't count items that are skipped (null data)\n      if (element && !element._view.skip) {\n        elements.push(element);\n      }\n    });\n    return elements;\n  }\n\n  /**\r\n   * @interface IInteractionOptions\r\n   */\n  /**\r\n   * If true, only consider items that intersect the point\r\n   * @name IInterfaceOptions#boolean\r\n   * @type Boolean\r\n   */\n\n  /**\r\n   * Contains interaction related functions\r\n   * @namespace Chart.Interaction\r\n   */\n  var core_interaction = {\n    // Helper function for different modes\n    modes: {\n      single: function (chart, e) {\n        var position = getRelativePosition(e, chart);\n        var elements = [];\n        parseVisibleItems(chart, function (element) {\n          if (element.inRange(position.x, position.y)) {\n            elements.push(element);\n            return elements;\n          }\n        });\n        return elements.slice(0, 1);\n      },\n      /**\r\n       * @function Chart.Interaction.modes.label\r\n       * @deprecated since version 2.4.0\r\n       * @todo remove at version 3\r\n       * @private\r\n       */\n      label: indexMode,\n      /**\r\n       * Returns items at the same index. If the options.intersect parameter is true, we only return items if we intersect something\r\n       * If the options.intersect mode is false, we find the nearest item and return the items at the same index as that item\r\n       * @function Chart.Interaction.modes.index\r\n       * @since v2.4.0\r\n       * @param {Chart} chart - the chart we are returning items from\r\n       * @param {Event} e - the event we are find things at\r\n       * @param {IInteractionOptions} options - options to use during interaction\r\n       * @return {Chart.Element[]} Array of elements that are under the point. If none are found, an empty array is returned\r\n       */\n      index: indexMode,\n      /**\r\n       * Returns items in the same dataset. If the options.intersect parameter is true, we only return items if we intersect something\r\n       * If the options.intersect is false, we find the nearest item and return the items in that dataset\r\n       * @function Chart.Interaction.modes.dataset\r\n       * @param {Chart} chart - the chart we are returning items from\r\n       * @param {Event} e - the event we are find things at\r\n       * @param {IInteractionOptions} options - options to use during interaction\r\n       * @return {Chart.Element[]} Array of elements that are under the point. If none are found, an empty array is returned\r\n       */\n      dataset: function (chart, e, options) {\n        var position = getRelativePosition(e, chart);\n        options.axis = options.axis || 'xy';\n        var distanceMetric = getDistanceMetricForAxis(options.axis);\n        var items = options.intersect ? getIntersectItems(chart, position) : getNearestItems(chart, position, false, distanceMetric);\n        if (items.length > 0) {\n          items = chart.getDatasetMeta(items[0]._datasetIndex).data;\n        }\n        return items;\n      },\n      /**\r\n       * @function Chart.Interaction.modes.x-axis\r\n       * @deprecated since version 2.4.0. Use index mode and intersect == true\r\n       * @todo remove at version 3\r\n       * @private\r\n       */\n      'x-axis': function (chart, e) {\n        return indexMode(chart, e, {\n          intersect: false\n        });\n      },\n      /**\r\n       * Point mode returns all elements that hit test based on the event position\r\n       * of the event\r\n       * @function Chart.Interaction.modes.intersect\r\n       * @param {Chart} chart - the chart we are returning items from\r\n       * @param {Event} e - the event we are find things at\r\n       * @return {Chart.Element[]} Array of elements that are under the point. If none are found, an empty array is returned\r\n       */\n      point: function (chart, e) {\n        var position = getRelativePosition(e, chart);\n        return getIntersectItems(chart, position);\n      },\n      /**\r\n       * nearest mode returns the element closest to the point\r\n       * @function Chart.Interaction.modes.intersect\r\n       * @param {Chart} chart - the chart we are returning items from\r\n       * @param {Event} e - the event we are find things at\r\n       * @param {IInteractionOptions} options - options to use\r\n       * @return {Chart.Element[]} Array of elements that are under the point. If none are found, an empty array is returned\r\n       */\n      nearest: function (chart, e, options) {\n        var position = getRelativePosition(e, chart);\n        options.axis = options.axis || 'xy';\n        var distanceMetric = getDistanceMetricForAxis(options.axis);\n        return getNearestItems(chart, position, options.intersect, distanceMetric);\n      },\n      /**\r\n       * x mode returns the elements that hit-test at the current x coordinate\r\n       * @function Chart.Interaction.modes.x\r\n       * @param {Chart} chart - the chart we are returning items from\r\n       * @param {Event} e - the event we are find things at\r\n       * @param {IInteractionOptions} options - options to use\r\n       * @return {Chart.Element[]} Array of elements that are under the point. If none are found, an empty array is returned\r\n       */\n      x: function (chart, e, options) {\n        var position = getRelativePosition(e, chart);\n        var items = [];\n        var intersectsItem = false;\n        parseVisibleItems(chart, function (element) {\n          if (element.inXRange(position.x)) {\n            items.push(element);\n          }\n          if (element.inRange(position.x, position.y)) {\n            intersectsItem = true;\n          }\n        });\n\n        // If we want to trigger on an intersect and we don't have any items\n        // that intersect the position, return nothing\n        if (options.intersect && !intersectsItem) {\n          items = [];\n        }\n        return items;\n      },\n      /**\r\n       * y mode returns the elements that hit-test at the current y coordinate\r\n       * @function Chart.Interaction.modes.y\r\n       * @param {Chart} chart - the chart we are returning items from\r\n       * @param {Event} e - the event we are find things at\r\n       * @param {IInteractionOptions} options - options to use\r\n       * @return {Chart.Element[]} Array of elements that are under the point. If none are found, an empty array is returned\r\n       */\n      y: function (chart, e, options) {\n        var position = getRelativePosition(e, chart);\n        var items = [];\n        var intersectsItem = false;\n        parseVisibleItems(chart, function (element) {\n          if (element.inYRange(position.y)) {\n            items.push(element);\n          }\n          if (element.inRange(position.x, position.y)) {\n            intersectsItem = true;\n          }\n        });\n\n        // If we want to trigger on an intersect and we don't have any items\n        // that intersect the position, return nothing\n        if (options.intersect && !intersectsItem) {\n          items = [];\n        }\n        return items;\n      }\n    }\n  };\n  var extend = helpers$1.extend;\n  function filterByPosition(array, position) {\n    return helpers$1.where(array, function (v) {\n      return v.pos === position;\n    });\n  }\n  function sortByWeight(array, reverse) {\n    return array.sort(function (a, b) {\n      var v0 = reverse ? b : a;\n      var v1 = reverse ? a : b;\n      return v0.weight === v1.weight ? v0.index - v1.index : v0.weight - v1.weight;\n    });\n  }\n  function wrapBoxes(boxes) {\n    var layoutBoxes = [];\n    var i, ilen, box;\n    for (i = 0, ilen = (boxes || []).length; i < ilen; ++i) {\n      box = boxes[i];\n      layoutBoxes.push({\n        index: i,\n        box: box,\n        pos: box.position,\n        horizontal: box.isHorizontal(),\n        weight: box.weight\n      });\n    }\n    return layoutBoxes;\n  }\n  function setLayoutDims(layouts, params) {\n    var i, ilen, layout;\n    for (i = 0, ilen = layouts.length; i < ilen; ++i) {\n      layout = layouts[i];\n      // store width used instead of chartArea.w in fitBoxes\n      layout.width = layout.horizontal ? layout.box.fullWidth && params.availableWidth : params.vBoxMaxWidth;\n      // store height used instead of chartArea.h in fitBoxes\n      layout.height = layout.horizontal && params.hBoxMaxHeight;\n    }\n  }\n  function buildLayoutBoxes(boxes) {\n    var layoutBoxes = wrapBoxes(boxes);\n    var left = sortByWeight(filterByPosition(layoutBoxes, 'left'), true);\n    var right = sortByWeight(filterByPosition(layoutBoxes, 'right'));\n    var top = sortByWeight(filterByPosition(layoutBoxes, 'top'), true);\n    var bottom = sortByWeight(filterByPosition(layoutBoxes, 'bottom'));\n    return {\n      leftAndTop: left.concat(top),\n      rightAndBottom: right.concat(bottom),\n      chartArea: filterByPosition(layoutBoxes, 'chartArea'),\n      vertical: left.concat(right),\n      horizontal: top.concat(bottom)\n    };\n  }\n  function getCombinedMax(maxPadding, chartArea, a, b) {\n    return Math.max(maxPadding[a], chartArea[a]) + Math.max(maxPadding[b], chartArea[b]);\n  }\n  function updateDims(chartArea, params, layout) {\n    var box = layout.box;\n    var maxPadding = chartArea.maxPadding;\n    var newWidth, newHeight;\n    if (layout.size) {\n      // this layout was already counted for, lets first reduce old size\n      chartArea[layout.pos] -= layout.size;\n    }\n    layout.size = layout.horizontal ? box.height : box.width;\n    chartArea[layout.pos] += layout.size;\n    if (box.getPadding) {\n      var boxPadding = box.getPadding();\n      maxPadding.top = Math.max(maxPadding.top, boxPadding.top);\n      maxPadding.left = Math.max(maxPadding.left, boxPadding.left);\n      maxPadding.bottom = Math.max(maxPadding.bottom, boxPadding.bottom);\n      maxPadding.right = Math.max(maxPadding.right, boxPadding.right);\n    }\n    newWidth = params.outerWidth - getCombinedMax(maxPadding, chartArea, 'left', 'right');\n    newHeight = params.outerHeight - getCombinedMax(maxPadding, chartArea, 'top', 'bottom');\n    if (newWidth !== chartArea.w || newHeight !== chartArea.h) {\n      chartArea.w = newWidth;\n      chartArea.h = newHeight;\n\n      // return true if chart area changed in layout's direction\n      var sizes = layout.horizontal ? [newWidth, chartArea.w] : [newHeight, chartArea.h];\n      return sizes[0] !== sizes[1] && (!isNaN(sizes[0]) || !isNaN(sizes[1]));\n    }\n  }\n  function handleMaxPadding(chartArea) {\n    var maxPadding = chartArea.maxPadding;\n    function updatePos(pos) {\n      var change = Math.max(maxPadding[pos] - chartArea[pos], 0);\n      chartArea[pos] += change;\n      return change;\n    }\n    chartArea.y += updatePos('top');\n    chartArea.x += updatePos('left');\n    updatePos('right');\n    updatePos('bottom');\n  }\n  function getMargins(horizontal, chartArea) {\n    var maxPadding = chartArea.maxPadding;\n    function marginForPositions(positions) {\n      var margin = {\n        left: 0,\n        top: 0,\n        right: 0,\n        bottom: 0\n      };\n      positions.forEach(function (pos) {\n        margin[pos] = Math.max(chartArea[pos], maxPadding[pos]);\n      });\n      return margin;\n    }\n    return horizontal ? marginForPositions(['left', 'right']) : marginForPositions(['top', 'bottom']);\n  }\n  function fitBoxes(boxes, chartArea, params) {\n    var refitBoxes = [];\n    var i, ilen, layout, box, refit, changed;\n    for (i = 0, ilen = boxes.length; i < ilen; ++i) {\n      layout = boxes[i];\n      box = layout.box;\n      box.update(layout.width || chartArea.w, layout.height || chartArea.h, getMargins(layout.horizontal, chartArea));\n      if (updateDims(chartArea, params, layout)) {\n        changed = true;\n        if (refitBoxes.length) {\n          // Dimensions changed and there were non full width boxes before this\n          // -> we have to refit those\n          refit = true;\n        }\n      }\n      if (!box.fullWidth) {\n        // fullWidth boxes don't need to be re-fitted in any case\n        refitBoxes.push(layout);\n      }\n    }\n    return refit ? fitBoxes(refitBoxes, chartArea, params) || changed : changed;\n  }\n  function placeBoxes(boxes, chartArea, params) {\n    var userPadding = params.padding;\n    var x = chartArea.x;\n    var y = chartArea.y;\n    var i, ilen, layout, box;\n    for (i = 0, ilen = boxes.length; i < ilen; ++i) {\n      layout = boxes[i];\n      box = layout.box;\n      if (layout.horizontal) {\n        box.left = box.fullWidth ? userPadding.left : chartArea.left;\n        box.right = box.fullWidth ? params.outerWidth - userPadding.right : chartArea.left + chartArea.w;\n        box.top = y;\n        box.bottom = y + box.height;\n        box.width = box.right - box.left;\n        y = box.bottom;\n      } else {\n        box.left = x;\n        box.right = x + box.width;\n        box.top = chartArea.top;\n        box.bottom = chartArea.top + chartArea.h;\n        box.height = box.bottom - box.top;\n        x = box.right;\n      }\n    }\n    chartArea.x = x;\n    chartArea.y = y;\n  }\n  core_defaults._set('global', {\n    layout: {\n      padding: {\n        top: 0,\n        right: 0,\n        bottom: 0,\n        left: 0\n      }\n    }\n  });\n\n  /**\r\n   * @interface ILayoutItem\r\n   * @prop {string} position - The position of the item in the chart layout. Possible values are\r\n   * 'left', 'top', 'right', 'bottom', and 'chartArea'\r\n   * @prop {number} weight - The weight used to sort the item. Higher weights are further away from the chart area\r\n   * @prop {boolean} fullWidth - if true, and the item is horizontal, then push vertical boxes down\r\n   * @prop {function} isHorizontal - returns true if the layout item is horizontal (ie. top or bottom)\r\n   * @prop {function} update - Takes two parameters: width and height. Returns size of item\r\n   * @prop {function} getPadding -  Returns an object with padding on the edges\r\n   * @prop {number} width - Width of item. Must be valid after update()\r\n   * @prop {number} height - Height of item. Must be valid after update()\r\n   * @prop {number} left - Left edge of the item. Set by layout system and cannot be used in update\r\n   * @prop {number} top - Top edge of the item. Set by layout system and cannot be used in update\r\n   * @prop {number} right - Right edge of the item. Set by layout system and cannot be used in update\r\n   * @prop {number} bottom - Bottom edge of the item. Set by layout system and cannot be used in update\r\n   */\n\n  // The layout service is very self explanatory.  It's responsible for the layout within a chart.\n  // Scales, Legends and Plugins all rely on the layout service and can easily register to be placed anywhere they need\n  // It is this service's responsibility of carrying out that layout.\n  var core_layouts = {\n    defaults: {},\n    /**\r\n     * Register a box to a chart.\r\n     * A box is simply a reference to an object that requires layout. eg. Scales, Legend, Title.\r\n     * @param {Chart} chart - the chart to use\r\n     * @param {ILayoutItem} item - the item to add to be layed out\r\n     */\n    addBox: function (chart, item) {\n      if (!chart.boxes) {\n        chart.boxes = [];\n      }\n\n      // initialize item with default values\n      item.fullWidth = item.fullWidth || false;\n      item.position = item.position || 'top';\n      item.weight = item.weight || 0;\n      item._layers = item._layers || function () {\n        return [{\n          z: 0,\n          draw: function () {\n            item.draw.apply(item, arguments);\n          }\n        }];\n      };\n      chart.boxes.push(item);\n    },\n    /**\r\n     * Remove a layoutItem from a chart\r\n     * @param {Chart} chart - the chart to remove the box from\r\n     * @param {ILayoutItem} layoutItem - the item to remove from the layout\r\n     */\n    removeBox: function (chart, layoutItem) {\n      var index = chart.boxes ? chart.boxes.indexOf(layoutItem) : -1;\n      if (index !== -1) {\n        chart.boxes.splice(index, 1);\n      }\n    },\n    /**\r\n     * Sets (or updates) options on the given `item`.\r\n     * @param {Chart} chart - the chart in which the item lives (or will be added to)\r\n     * @param {ILayoutItem} item - the item to configure with the given options\r\n     * @param {object} options - the new item options.\r\n     */\n    configure: function (chart, item, options) {\n      var props = ['fullWidth', 'position', 'weight'];\n      var ilen = props.length;\n      var i = 0;\n      var prop;\n      for (; i < ilen; ++i) {\n        prop = props[i];\n        if (options.hasOwnProperty(prop)) {\n          item[prop] = options[prop];\n        }\n      }\n    },\n    /**\r\n     * Fits boxes of the given chart into the given size by having each box measure itself\r\n     * then running a fitting algorithm\r\n     * @param {Chart} chart - the chart\r\n     * @param {number} width - the width to fit into\r\n     * @param {number} height - the height to fit into\r\n     */\n    update: function (chart, width, height) {\n      if (!chart) {\n        return;\n      }\n      var layoutOptions = chart.options.layout || {};\n      var padding = helpers$1.options.toPadding(layoutOptions.padding);\n      var availableWidth = width - padding.width;\n      var availableHeight = height - padding.height;\n      var boxes = buildLayoutBoxes(chart.boxes);\n      var verticalBoxes = boxes.vertical;\n      var horizontalBoxes = boxes.horizontal;\n\n      // Essentially we now have any number of boxes on each of the 4 sides.\n      // Our canvas looks like the following.\n      // The areas L1 and L2 are the left axes. R1 is the right axis, T1 is the top axis and\n      // B1 is the bottom axis\n      // There are also 4 quadrant-like locations (left to right instead of clockwise) reserved for chart overlays\n      // These locations are single-box locations only, when trying to register a chartArea location that is already taken,\n      // an error will be thrown.\n      //\n      // |----------------------------------------------------|\n      // |                  T1 (Full Width)                   |\n      // |----------------------------------------------------|\n      // |    |    |                 T2                  |    |\n      // |    |----|-------------------------------------|----|\n      // |    |    | C1 |                           | C2 |    |\n      // |    |    |----|                           |----|    |\n      // |    |    |                                     |    |\n      // | L1 | L2 |           ChartArea (C0)            | R1 |\n      // |    |    |                                     |    |\n      // |    |    |----|                           |----|    |\n      // |    |    | C3 |                           | C4 |    |\n      // |    |----|-------------------------------------|----|\n      // |    |    |                 B1                  |    |\n      // |----------------------------------------------------|\n      // |                  B2 (Full Width)                   |\n      // |----------------------------------------------------|\n      //\n\n      var params = Object.freeze({\n        outerWidth: width,\n        outerHeight: height,\n        padding: padding,\n        availableWidth: availableWidth,\n        vBoxMaxWidth: availableWidth / 2 / verticalBoxes.length,\n        hBoxMaxHeight: availableHeight / 2\n      });\n      var chartArea = extend({\n        maxPadding: extend({}, padding),\n        w: availableWidth,\n        h: availableHeight,\n        x: padding.left,\n        y: padding.top\n      }, padding);\n      setLayoutDims(verticalBoxes.concat(horizontalBoxes), params);\n\n      // First fit vertical boxes\n      fitBoxes(verticalBoxes, chartArea, params);\n\n      // Then fit horizontal boxes\n      if (fitBoxes(horizontalBoxes, chartArea, params)) {\n        // if the area changed, re-fit vertical boxes\n        fitBoxes(verticalBoxes, chartArea, params);\n      }\n      handleMaxPadding(chartArea);\n\n      // Finally place the boxes to correct coordinates\n      placeBoxes(boxes.leftAndTop, chartArea, params);\n\n      // Move to opposite side of chart\n      chartArea.x += chartArea.w;\n      chartArea.y += chartArea.h;\n      placeBoxes(boxes.rightAndBottom, chartArea, params);\n      chart.chartArea = {\n        left: chartArea.left,\n        top: chartArea.top,\n        right: chartArea.left + chartArea.w,\n        bottom: chartArea.top + chartArea.h\n      };\n\n      // Finally update boxes in chartArea (radial scale for example)\n      helpers$1.each(boxes.chartArea, function (layout) {\n        var box = layout.box;\n        extend(box, chart.chartArea);\n        box.update(chartArea.w, chartArea.h);\n      });\n    }\n  };\n\n  /**\r\n   * Platform fallback implementation (minimal).\r\n   * @see https://github.com/chartjs/Chart.js/pull/4591#issuecomment-319575939\r\n   */\n\n  var platform_basic = {\n    acquireContext: function (item) {\n      if (item && item.canvas) {\n        // Support for any object associated to a canvas (including a context2d)\n        item = item.canvas;\n      }\n      return item && item.getContext('2d') || null;\n    }\n  };\n  var platform_dom = \"/*\\r\\n * DOM element rendering detection\\r\\n * https://davidwalsh.name/detect-node-insertion\\r\\n */\\r\\n@keyframes chartjs-render-animation {\\r\\n\\tfrom { opacity: 0.99; }\\r\\n\\tto { opacity: 1; }\\r\\n}\\r\\n\\r\\n.chartjs-render-monitor {\\r\\n\\tanimation: chartjs-render-animation 0.001s;\\r\\n}\\r\\n\\r\\n/*\\r\\n * DOM element resizing detection\\r\\n * https://github.com/marcj/css-element-queries\\r\\n */\\r\\n.chartjs-size-monitor,\\r\\n.chartjs-size-monitor-expand,\\r\\n.chartjs-size-monitor-shrink {\\r\\n\\tposition: absolute;\\r\\n\\tdirection: ltr;\\r\\n\\tleft: 0;\\r\\n\\ttop: 0;\\r\\n\\tright: 0;\\r\\n\\tbottom: 0;\\r\\n\\toverflow: hidden;\\r\\n\\tpointer-events: none;\\r\\n\\tvisibility: hidden;\\r\\n\\tz-index: -1;\\r\\n}\\r\\n\\r\\n.chartjs-size-monitor-expand > div {\\r\\n\\tposition: absolute;\\r\\n\\twidth: 1000000px;\\r\\n\\theight: 1000000px;\\r\\n\\tleft: 0;\\r\\n\\ttop: 0;\\r\\n}\\r\\n\\r\\n.chartjs-size-monitor-shrink > div {\\r\\n\\tposition: absolute;\\r\\n\\twidth: 200%;\\r\\n\\theight: 200%;\\r\\n\\tleft: 0;\\r\\n\\ttop: 0;\\r\\n}\\r\\n\";\n  var platform_dom$1 = /*#__PURE__*/Object.freeze({\n    __proto__: null,\n    'default': platform_dom\n  });\n  var stylesheet = getCjsExportFromNamespace(platform_dom$1);\n  var EXPANDO_KEY = '$chartjs';\n  var CSS_PREFIX = 'chartjs-';\n  var CSS_SIZE_MONITOR = CSS_PREFIX + 'size-monitor';\n  var CSS_RENDER_MONITOR = CSS_PREFIX + 'render-monitor';\n  var CSS_RENDER_ANIMATION = CSS_PREFIX + 'render-animation';\n  var ANIMATION_START_EVENTS = ['animationstart', 'webkitAnimationStart'];\n\n  /**\r\n   * DOM event types -> Chart.js event types.\r\n   * Note: only events with different types are mapped.\r\n   * @see https://developer.mozilla.org/en-US/docs/Web/Events\r\n   */\n  var EVENT_TYPES = {\n    touchstart: 'mousedown',\n    touchmove: 'mousemove',\n    touchend: 'mouseup',\n    pointerenter: 'mouseenter',\n    pointerdown: 'mousedown',\n    pointermove: 'mousemove',\n    pointerup: 'mouseup',\n    pointerleave: 'mouseout',\n    pointerout: 'mouseout'\n  };\n\n  /**\r\n   * The \"used\" size is the final value of a dimension property after all calculations have\r\n   * been performed. This method uses the computed style of `element` but returns undefined\r\n   * if the computed style is not expressed in pixels. That can happen in some cases where\r\n   * `element` has a size relative to its parent and this last one is not yet displayed,\r\n   * for example because of `display: none` on a parent node.\r\n   * @see https://developer.mozilla.org/en-US/docs/Web/CSS/used_value\r\n   * @returns {number} Size in pixels or undefined if unknown.\r\n   */\n  function readUsedSize(element, property) {\n    var value = helpers$1.getStyle(element, property);\n    var matches = value && value.match(/^(\\d+)(\\.\\d+)?px$/);\n    return matches ? Number(matches[1]) : undefined;\n  }\n\n  /**\r\n   * Initializes the canvas style and render size without modifying the canvas display size,\r\n   * since responsiveness is handled by the controller.resize() method. The config is used\r\n   * to determine the aspect ratio to apply in case no explicit height has been specified.\r\n   */\n  function initCanvas(canvas, config) {\n    var style = canvas.style;\n\n    // NOTE(SB) canvas.getAttribute('width') !== canvas.width: in the first case it\n    // returns null or '' if no explicit value has been set to the canvas attribute.\n    var renderHeight = canvas.getAttribute('height');\n    var renderWidth = canvas.getAttribute('width');\n\n    // Chart.js modifies some canvas values that we want to restore on destroy\n    canvas[EXPANDO_KEY] = {\n      initial: {\n        height: renderHeight,\n        width: renderWidth,\n        style: {\n          display: style.display,\n          height: style.height,\n          width: style.width\n        }\n      }\n    };\n\n    // Force canvas to display as block to avoid extra space caused by inline\n    // elements, which would interfere with the responsive resize process.\n    // https://github.com/chartjs/Chart.js/issues/2538\n    style.display = style.display || 'block';\n    if (renderWidth === null || renderWidth === '') {\n      var displayWidth = readUsedSize(canvas, 'width');\n      if (displayWidth !== undefined) {\n        canvas.width = displayWidth;\n      }\n    }\n    if (renderHeight === null || renderHeight === '') {\n      if (canvas.style.height === '') {\n        // If no explicit render height and style height, let's apply the aspect ratio,\n        // which one can be specified by the user but also by charts as default option\n        // (i.e. options.aspectRatio). If not specified, use canvas aspect ratio of 2.\n        canvas.height = canvas.width / (config.options.aspectRatio || 2);\n      } else {\n        var displayHeight = readUsedSize(canvas, 'height');\n        if (displayWidth !== undefined) {\n          canvas.height = displayHeight;\n        }\n      }\n    }\n    return canvas;\n  }\n\n  /**\r\n   * Detects support for options object argument in addEventListener.\r\n   * https://developer.mozilla.org/en-US/docs/Web/API/EventTarget/addEventListener#Safely_detecting_option_support\r\n   * @private\r\n   */\n  var supportsEventListenerOptions = function () {\n    var supports = false;\n    try {\n      var options = Object.defineProperty({}, 'passive', {\n        // eslint-disable-next-line getter-return\n        get: function () {\n          supports = true;\n        }\n      });\n      window.addEventListener('e', null, options);\n    } catch (e) {\n      // continue regardless of error\n    }\n    return supports;\n  }();\n\n  // Default passive to true as expected by Chrome for 'touchstart' and 'touchend' events.\n  // https://github.com/chartjs/Chart.js/issues/4287\n  var eventListenerOptions = supportsEventListenerOptions ? {\n    passive: true\n  } : false;\n  function addListener(node, type, listener) {\n    node.addEventListener(type, listener, eventListenerOptions);\n  }\n  function removeListener(node, type, listener) {\n    node.removeEventListener(type, listener, eventListenerOptions);\n  }\n  function createEvent(type, chart, x, y, nativeEvent) {\n    return {\n      type: type,\n      chart: chart,\n      native: nativeEvent || null,\n      x: x !== undefined ? x : null,\n      y: y !== undefined ? y : null\n    };\n  }\n  function fromNativeEvent(event, chart) {\n    var type = EVENT_TYPES[event.type] || event.type;\n    var pos = helpers$1.getRelativePosition(event, chart);\n    return createEvent(type, chart, pos.x, pos.y, event);\n  }\n  function throttled(fn, thisArg) {\n    var ticking = false;\n    var args = [];\n    return function () {\n      args = Array.prototype.slice.call(arguments);\n      thisArg = thisArg || this;\n      if (!ticking) {\n        ticking = true;\n        helpers$1.requestAnimFrame.call(window, function () {\n          ticking = false;\n          fn.apply(thisArg, args);\n        });\n      }\n    };\n  }\n  function createDiv(cls) {\n    var el = document.createElement('div');\n    el.className = cls || '';\n    return el;\n  }\n\n  // Implementation based on https://github.com/marcj/css-element-queries\n  function createResizer(handler) {\n    var maxSize = 1000000;\n\n    // NOTE(SB) Don't use innerHTML because it could be considered unsafe.\n    // https://github.com/chartjs/Chart.js/issues/5902\n    var resizer = createDiv(CSS_SIZE_MONITOR);\n    var expand = createDiv(CSS_SIZE_MONITOR + '-expand');\n    var shrink = createDiv(CSS_SIZE_MONITOR + '-shrink');\n    expand.appendChild(createDiv());\n    shrink.appendChild(createDiv());\n    resizer.appendChild(expand);\n    resizer.appendChild(shrink);\n    resizer._reset = function () {\n      expand.scrollLeft = maxSize;\n      expand.scrollTop = maxSize;\n      shrink.scrollLeft = maxSize;\n      shrink.scrollTop = maxSize;\n    };\n    var onScroll = function () {\n      resizer._reset();\n      handler();\n    };\n    addListener(expand, 'scroll', onScroll.bind(expand, 'expand'));\n    addListener(shrink, 'scroll', onScroll.bind(shrink, 'shrink'));\n    return resizer;\n  }\n\n  // https://davidwalsh.name/detect-node-insertion\n  function watchForRender(node, handler) {\n    var expando = node[EXPANDO_KEY] || (node[EXPANDO_KEY] = {});\n    var proxy = expando.renderProxy = function (e) {\n      if (e.animationName === CSS_RENDER_ANIMATION) {\n        handler();\n      }\n    };\n    helpers$1.each(ANIMATION_START_EVENTS, function (type) {\n      addListener(node, type, proxy);\n    });\n\n    // #4737: Chrome might skip the CSS animation when the CSS_RENDER_MONITOR class\n    // is removed then added back immediately (same animation frame?). Accessing the\n    // `offsetParent` property will force a reflow and re-evaluate the CSS animation.\n    // https://gist.github.com/paulirish/5d52fb081b3570c81e3a#box-metrics\n    // https://github.com/chartjs/Chart.js/issues/4737\n    expando.reflow = !!node.offsetParent;\n    node.classList.add(CSS_RENDER_MONITOR);\n  }\n  function unwatchForRender(node) {\n    var expando = node[EXPANDO_KEY] || {};\n    var proxy = expando.renderProxy;\n    if (proxy) {\n      helpers$1.each(ANIMATION_START_EVENTS, function (type) {\n        removeListener(node, type, proxy);\n      });\n      delete expando.renderProxy;\n    }\n    node.classList.remove(CSS_RENDER_MONITOR);\n  }\n  function addResizeListener(node, listener, chart) {\n    var expando = node[EXPANDO_KEY] || (node[EXPANDO_KEY] = {});\n\n    // Let's keep track of this added resizer and thus avoid DOM query when removing it.\n    var resizer = expando.resizer = createResizer(throttled(function () {\n      if (expando.resizer) {\n        var container = chart.options.maintainAspectRatio && node.parentNode;\n        var w = container ? container.clientWidth : 0;\n        listener(createEvent('resize', chart));\n        if (container && container.clientWidth < w && chart.canvas) {\n          // If the container size shrank during chart resize, let's assume\n          // scrollbar appeared. So we resize again with the scrollbar visible -\n          // effectively making chart smaller and the scrollbar hidden again.\n          // Because we are inside `throttled`, and currently `ticking`, scroll\n          // events are ignored during this whole 2 resize process.\n          // If we assumed wrong and something else happened, we are resizing\n          // twice in a frame (potential performance issue)\n          listener(createEvent('resize', chart));\n        }\n      }\n    }));\n\n    // The resizer needs to be attached to the node parent, so we first need to be\n    // sure that `node` is attached to the DOM before injecting the resizer element.\n    watchForRender(node, function () {\n      if (expando.resizer) {\n        var container = node.parentNode;\n        if (container && container !== resizer.parentNode) {\n          container.insertBefore(resizer, container.firstChild);\n        }\n\n        // The container size might have changed, let's reset the resizer state.\n        resizer._reset();\n      }\n    });\n  }\n  function removeResizeListener(node) {\n    var expando = node[EXPANDO_KEY] || {};\n    var resizer = expando.resizer;\n    delete expando.resizer;\n    unwatchForRender(node);\n    if (resizer && resizer.parentNode) {\n      resizer.parentNode.removeChild(resizer);\n    }\n  }\n\n  /**\r\n   * Injects CSS styles inline if the styles are not already present.\r\n   * @param {HTMLDocument|ShadowRoot} rootNode - the node to contain the <style>.\r\n   * @param {string} css - the CSS to be injected.\r\n   */\n  function injectCSS(rootNode, css) {\n    // https://stackoverflow.com/q/3922139\n    var expando = rootNode[EXPANDO_KEY] || (rootNode[EXPANDO_KEY] = {});\n    if (!expando.containsStyles) {\n      expando.containsStyles = true;\n      css = '/* Chart.js */\\n' + css;\n      var style = document.createElement('style');\n      style.setAttribute('type', 'text/css');\n      style.appendChild(document.createTextNode(css));\n      rootNode.appendChild(style);\n    }\n  }\n  var platform_dom$2 = {\n    /**\r\n     * When `true`, prevents the automatic injection of the stylesheet required to\r\n     * correctly detect when the chart is added to the DOM and then resized. This\r\n     * switch has been added to allow external stylesheet (`dist/Chart(.min)?.js`)\r\n     * to be manually imported to make this library compatible with any CSP.\r\n     * See https://github.com/chartjs/Chart.js/issues/5208\r\n     */\n    disableCSSInjection: false,\n    /**\r\n     * This property holds whether this platform is enabled for the current environment.\r\n     * Currently used by platform.js to select the proper implementation.\r\n     * @private\r\n     */\n    _enabled: typeof window !== 'undefined' && typeof document !== 'undefined',\n    /**\r\n     * Initializes resources that depend on platform options.\r\n     * @param {HTMLCanvasElement} canvas - The Canvas element.\r\n     * @private\r\n     */\n    _ensureLoaded: function (canvas) {\n      if (!this.disableCSSInjection) {\n        // If the canvas is in a shadow DOM, then the styles must also be inserted\n        // into the same shadow DOM.\n        // https://github.com/chartjs/Chart.js/issues/5763\n        var root = canvas.getRootNode ? canvas.getRootNode() : document;\n        var targetNode = root.host ? root : document.head;\n        injectCSS(targetNode, stylesheet);\n      }\n    },\n    acquireContext: function (item, config) {\n      if (typeof item === 'string') {\n        item = document.getElementById(item);\n      } else if (item.length) {\n        // Support for array based queries (such as jQuery)\n        item = item[0];\n      }\n      if (item && item.canvas) {\n        // Support for any object associated to a canvas (including a context2d)\n        item = item.canvas;\n      }\n\n      // To prevent canvas fingerprinting, some add-ons undefine the getContext\n      // method, for example: https://github.com/kkapsner/CanvasBlocker\n      // https://github.com/chartjs/Chart.js/issues/2807\n      var context = item && item.getContext && item.getContext('2d');\n\n      // `instanceof HTMLCanvasElement/CanvasRenderingContext2D` fails when the item is\n      // inside an iframe or when running in a protected environment. We could guess the\n      // types from their toString() value but let's keep things flexible and assume it's\n      // a sufficient condition if the item has a context2D which has item as `canvas`.\n      // https://github.com/chartjs/Chart.js/issues/3887\n      // https://github.com/chartjs/Chart.js/issues/4102\n      // https://github.com/chartjs/Chart.js/issues/4152\n      if (context && context.canvas === item) {\n        // Load platform resources on first chart creation, to make it possible to\n        // import the library before setting platform options.\n        this._ensureLoaded(item);\n        initCanvas(item, config);\n        return context;\n      }\n      return null;\n    },\n    releaseContext: function (context) {\n      var canvas = context.canvas;\n      if (!canvas[EXPANDO_KEY]) {\n        return;\n      }\n      var initial = canvas[EXPANDO_KEY].initial;\n      ['height', 'width'].forEach(function (prop) {\n        var value = initial[prop];\n        if (helpers$1.isNullOrUndef(value)) {\n          canvas.removeAttribute(prop);\n        } else {\n          canvas.setAttribute(prop, value);\n        }\n      });\n      helpers$1.each(initial.style || {}, function (value, key) {\n        canvas.style[key] = value;\n      });\n\n      // The canvas render size might have been changed (and thus the state stack discarded),\n      // we can't use save() and restore() to restore the initial state. So make sure that at\n      // least the canvas context is reset to the default state by setting the canvas width.\n      // https://www.w3.org/TR/2011/WD-html5-20110525/the-canvas-element.html\n      // eslint-disable-next-line no-self-assign\n      canvas.width = canvas.width;\n      delete canvas[EXPANDO_KEY];\n    },\n    addEventListener: function (chart, type, listener) {\n      var canvas = chart.canvas;\n      if (type === 'resize') {\n        // Note: the resize event is not supported on all browsers.\n        addResizeListener(canvas, listener, chart);\n        return;\n      }\n      var expando = listener[EXPANDO_KEY] || (listener[EXPANDO_KEY] = {});\n      var proxies = expando.proxies || (expando.proxies = {});\n      var proxy = proxies[chart.id + '_' + type] = function (event) {\n        listener(fromNativeEvent(event, chart));\n      };\n      addListener(canvas, type, proxy);\n    },\n    removeEventListener: function (chart, type, listener) {\n      var canvas = chart.canvas;\n      if (type === 'resize') {\n        // Note: the resize event is not supported on all browsers.\n        removeResizeListener(canvas);\n        return;\n      }\n      var expando = listener[EXPANDO_KEY] || {};\n      var proxies = expando.proxies || {};\n      var proxy = proxies[chart.id + '_' + type];\n      if (!proxy) {\n        return;\n      }\n      removeListener(canvas, type, proxy);\n    }\n  };\n\n  // DEPRECATIONS\n\n  /**\r\n   * Provided for backward compatibility, use EventTarget.addEventListener instead.\r\n   * EventTarget.addEventListener compatibility: Chrome, Opera 7, Safari, FF1.5+, IE9+\r\n   * @see https://developer.mozilla.org/en-US/docs/Web/API/EventTarget/addEventListener\r\n   * @function Chart.helpers.addEvent\r\n   * @deprecated since version 2.7.0\r\n   * @todo remove at version 3\r\n   * @private\r\n   */\n  helpers$1.addEvent = addListener;\n\n  /**\r\n   * Provided for backward compatibility, use EventTarget.removeEventListener instead.\r\n   * EventTarget.removeEventListener compatibility: Chrome, Opera 7, Safari, FF1.5+, IE9+\r\n   * @see https://developer.mozilla.org/en-US/docs/Web/API/EventTarget/removeEventListener\r\n   * @function Chart.helpers.removeEvent\r\n   * @deprecated since version 2.7.0\r\n   * @todo remove at version 3\r\n   * @private\r\n   */\n  helpers$1.removeEvent = removeListener;\n\n  // @TODO Make possible to select another platform at build time.\n  var implementation = platform_dom$2._enabled ? platform_dom$2 : platform_basic;\n\n  /**\r\n   * @namespace Chart.platform\r\n   * @see https://chartjs.gitbooks.io/proposals/content/Platform.html\r\n   * @since 2.4.0\r\n   */\n  var platform = helpers$1.extend({\n    /**\r\n     * @since 2.7.0\r\n     */\n    initialize: function () {},\n    /**\r\n     * Called at chart construction time, returns a context2d instance implementing\r\n     * the [W3C Canvas 2D Context API standard]{@link https://www.w3.org/TR/2dcontext/}.\r\n     * @param {*} item - The native item from which to acquire context (platform specific)\r\n     * @param {object} options - The chart options\r\n     * @returns {CanvasRenderingContext2D} context2d instance\r\n     */\n    acquireContext: function () {},\n    /**\r\n     * Called at chart destruction time, releases any resources associated to the context\r\n     * previously returned by the acquireContext() method.\r\n     * @param {CanvasRenderingContext2D} context - The context2d instance\r\n     * @returns {boolean} true if the method succeeded, else false\r\n     */\n    releaseContext: function () {},\n    /**\r\n     * Registers the specified listener on the given chart.\r\n     * @param {Chart} chart - Chart from which to listen for event\r\n     * @param {string} type - The ({@link IEvent}) type to listen for\r\n     * @param {function} listener - Receives a notification (an object that implements\r\n     * the {@link IEvent} interface) when an event of the specified type occurs.\r\n     */\n    addEventListener: function () {},\n    /**\r\n     * Removes the specified listener previously registered with addEventListener.\r\n     * @param {Chart} chart - Chart from which to remove the listener\r\n     * @param {string} type - The ({@link IEvent}) type to remove\r\n     * @param {function} listener - The listener function to remove from the event target.\r\n     */\n    removeEventListener: function () {}\n  }, implementation);\n  core_defaults._set('global', {\n    plugins: {}\n  });\n\n  /**\r\n   * The plugin service singleton\r\n   * @namespace Chart.plugins\r\n   * @since 2.1.0\r\n   */\n  var core_plugins = {\n    /**\r\n     * Globally registered plugins.\r\n     * @private\r\n     */\n    _plugins: [],\n    /**\r\n     * This identifier is used to invalidate the descriptors cache attached to each chart\r\n     * when a global plugin is registered or unregistered. In this case, the cache ID is\r\n     * incremented and descriptors are regenerated during following API calls.\r\n     * @private\r\n     */\n    _cacheId: 0,\n    /**\r\n     * Registers the given plugin(s) if not already registered.\r\n     * @param {IPlugin[]|IPlugin} plugins plugin instance(s).\r\n     */\n    register: function (plugins) {\n      var p = this._plugins;\n      [].concat(plugins).forEach(function (plugin) {\n        if (p.indexOf(plugin) === -1) {\n          p.push(plugin);\n        }\n      });\n      this._cacheId++;\n    },\n    /**\r\n     * Unregisters the given plugin(s) only if registered.\r\n     * @param {IPlugin[]|IPlugin} plugins plugin instance(s).\r\n     */\n    unregister: function (plugins) {\n      var p = this._plugins;\n      [].concat(plugins).forEach(function (plugin) {\n        var idx = p.indexOf(plugin);\n        if (idx !== -1) {\n          p.splice(idx, 1);\n        }\n      });\n      this._cacheId++;\n    },\n    /**\r\n     * Remove all registered plugins.\r\n     * @since 2.1.5\r\n     */\n    clear: function () {\n      this._plugins = [];\n      this._cacheId++;\n    },\n    /**\r\n     * Returns the number of registered plugins?\r\n     * @returns {number}\r\n     * @since 2.1.5\r\n     */\n    count: function () {\n      return this._plugins.length;\n    },\n    /**\r\n     * Returns all registered plugin instances.\r\n     * @returns {IPlugin[]} array of plugin objects.\r\n     * @since 2.1.5\r\n     */\n    getAll: function () {\n      return this._plugins;\n    },\n    /**\r\n     * Calls enabled plugins for `chart` on the specified hook and with the given args.\r\n     * This method immediately returns as soon as a plugin explicitly returns false. The\r\n     * returned value can be used, for instance, to interrupt the current action.\r\n     * @param {Chart} chart - The chart instance for which plugins should be called.\r\n     * @param {string} hook - The name of the plugin method to call (e.g. 'beforeUpdate').\r\n     * @param {Array} [args] - Extra arguments to apply to the hook call.\r\n     * @returns {boolean} false if any of the plugins return false, else returns true.\r\n     */\n    notify: function (chart, hook, args) {\n      var descriptors = this.descriptors(chart);\n      var ilen = descriptors.length;\n      var i, descriptor, plugin, params, method;\n      for (i = 0; i < ilen; ++i) {\n        descriptor = descriptors[i];\n        plugin = descriptor.plugin;\n        method = plugin[hook];\n        if (typeof method === 'function') {\n          params = [chart].concat(args || []);\n          params.push(descriptor.options);\n          if (method.apply(plugin, params) === false) {\n            return false;\n          }\n        }\n      }\n      return true;\n    },\n    /**\r\n     * Returns descriptors of enabled plugins for the given chart.\r\n     * @returns {object[]} [{ plugin, options }]\r\n     * @private\r\n     */\n    descriptors: function (chart) {\n      var cache = chart.$plugins || (chart.$plugins = {});\n      if (cache.id === this._cacheId) {\n        return cache.descriptors;\n      }\n      var plugins = [];\n      var descriptors = [];\n      var config = chart && chart.config || {};\n      var options = config.options && config.options.plugins || {};\n      this._plugins.concat(config.plugins || []).forEach(function (plugin) {\n        var idx = plugins.indexOf(plugin);\n        if (idx !== -1) {\n          return;\n        }\n        var id = plugin.id;\n        var opts = options[id];\n        if (opts === false) {\n          return;\n        }\n        if (opts === true) {\n          opts = helpers$1.clone(core_defaults.global.plugins[id]);\n        }\n        plugins.push(plugin);\n        descriptors.push({\n          plugin: plugin,\n          options: opts || {}\n        });\n      });\n      cache.descriptors = descriptors;\n      cache.id = this._cacheId;\n      return descriptors;\n    },\n    /**\r\n     * Invalidates cache for the given chart: descriptors hold a reference on plugin option,\r\n     * but in some cases, this reference can be changed by the user when updating options.\r\n     * https://github.com/chartjs/Chart.js/issues/5111#issuecomment-355934167\r\n     * @private\r\n     */\n    _invalidate: function (chart) {\n      delete chart.$plugins;\n    }\n  };\n  var core_scaleService = {\n    // Scale registration object. Extensions can register new scale types (such as log or DB scales) and then\n    // use the new chart options to grab the correct scale\n    constructors: {},\n    // Use a registration function so that we can move to an ES6 map when we no longer need to support\n    // old browsers\n\n    // Scale config defaults\n    defaults: {},\n    registerScaleType: function (type, scaleConstructor, scaleDefaults) {\n      this.constructors[type] = scaleConstructor;\n      this.defaults[type] = helpers$1.clone(scaleDefaults);\n    },\n    getScaleConstructor: function (type) {\n      return this.constructors.hasOwnProperty(type) ? this.constructors[type] : undefined;\n    },\n    getScaleDefaults: function (type) {\n      // Return the scale defaults merged with the global settings so that we always use the latest ones\n      return this.defaults.hasOwnProperty(type) ? helpers$1.merge(Object.create(null), [core_defaults.scale, this.defaults[type]]) : {};\n    },\n    updateScaleDefaults: function (type, additions) {\n      var me = this;\n      if (me.defaults.hasOwnProperty(type)) {\n        me.defaults[type] = helpers$1.extend(me.defaults[type], additions);\n      }\n    },\n    addScalesToLayout: function (chart) {\n      // Adds each scale to the chart.boxes array to be sized accordingly\n      helpers$1.each(chart.scales, function (scale) {\n        // Set ILayoutItem parameters for backwards compatibility\n        scale.fullWidth = scale.options.fullWidth;\n        scale.position = scale.options.position;\n        scale.weight = scale.options.weight;\n        core_layouts.addBox(chart, scale);\n      });\n    }\n  };\n  var valueOrDefault$8 = helpers$1.valueOrDefault;\n  var getRtlHelper = helpers$1.rtl.getRtlAdapter;\n  core_defaults._set('global', {\n    tooltips: {\n      enabled: true,\n      custom: null,\n      mode: 'nearest',\n      position: 'average',\n      intersect: true,\n      backgroundColor: 'rgba(0,0,0,0.8)',\n      titleFontStyle: 'bold',\n      titleSpacing: 2,\n      titleMarginBottom: 6,\n      titleFontColor: '#fff',\n      titleAlign: 'left',\n      bodySpacing: 2,\n      bodyFontColor: '#fff',\n      bodyAlign: 'left',\n      footerFontStyle: 'bold',\n      footerSpacing: 2,\n      footerMarginTop: 6,\n      footerFontColor: '#fff',\n      footerAlign: 'left',\n      yPadding: 6,\n      xPadding: 6,\n      caretPadding: 2,\n      caretSize: 5,\n      cornerRadius: 6,\n      multiKeyBackground: '#fff',\n      displayColors: true,\n      borderColor: 'rgba(0,0,0,0)',\n      borderWidth: 0,\n      callbacks: {\n        // Args are: (tooltipItems, data)\n        beforeTitle: helpers$1.noop,\n        title: function (tooltipItems, data) {\n          var title = '';\n          var labels = data.labels;\n          var labelCount = labels ? labels.length : 0;\n          if (tooltipItems.length > 0) {\n            var item = tooltipItems[0];\n            if (item.label) {\n              title = item.label;\n            } else if (item.xLabel) {\n              title = item.xLabel;\n            } else if (labelCount > 0 && item.index < labelCount) {\n              title = labels[item.index];\n            }\n          }\n          return title;\n        },\n        afterTitle: helpers$1.noop,\n        // Args are: (tooltipItems, data)\n        beforeBody: helpers$1.noop,\n        // Args are: (tooltipItem, data)\n        beforeLabel: helpers$1.noop,\n        label: function (tooltipItem, data) {\n          var label = data.datasets[tooltipItem.datasetIndex].label || '';\n          if (label) {\n            label += ': ';\n          }\n          if (!helpers$1.isNullOrUndef(tooltipItem.value)) {\n            label += tooltipItem.value;\n          } else {\n            label += tooltipItem.yLabel;\n          }\n          return label;\n        },\n        labelColor: function (tooltipItem, chart) {\n          var meta = chart.getDatasetMeta(tooltipItem.datasetIndex);\n          var activeElement = meta.data[tooltipItem.index];\n          var view = activeElement._view;\n          return {\n            borderColor: view.borderColor,\n            backgroundColor: view.backgroundColor\n          };\n        },\n        labelTextColor: function () {\n          return this._options.bodyFontColor;\n        },\n        afterLabel: helpers$1.noop,\n        // Args are: (tooltipItems, data)\n        afterBody: helpers$1.noop,\n        // Args are: (tooltipItems, data)\n        beforeFooter: helpers$1.noop,\n        footer: helpers$1.noop,\n        afterFooter: helpers$1.noop\n      }\n    }\n  });\n  var positioners = {\n    /**\r\n     * Average mode places the tooltip at the average position of the elements shown\r\n     * @function Chart.Tooltip.positioners.average\r\n     * @param elements {ChartElement[]} the elements being displayed in the tooltip\r\n     * @returns {object} tooltip position\r\n     */\n    average: function (elements) {\n      if (!elements.length) {\n        return false;\n      }\n      var i, len;\n      var x = 0;\n      var y = 0;\n      var count = 0;\n      for (i = 0, len = elements.length; i < len; ++i) {\n        var el = elements[i];\n        if (el && el.hasValue()) {\n          var pos = el.tooltipPosition();\n          x += pos.x;\n          y += pos.y;\n          ++count;\n        }\n      }\n      return {\n        x: x / count,\n        y: y / count\n      };\n    },\n    /**\r\n     * Gets the tooltip position nearest of the item nearest to the event position\r\n     * @function Chart.Tooltip.positioners.nearest\r\n     * @param elements {Chart.Element[]} the tooltip elements\r\n     * @param eventPosition {object} the position of the event in canvas coordinates\r\n     * @returns {object} the tooltip position\r\n     */\n    nearest: function (elements, eventPosition) {\n      var x = eventPosition.x;\n      var y = eventPosition.y;\n      var minDistance = Number.POSITIVE_INFINITY;\n      var i, len, nearestElement;\n      for (i = 0, len = elements.length; i < len; ++i) {\n        var el = elements[i];\n        if (el && el.hasValue()) {\n          var center = el.getCenterPoint();\n          var d = helpers$1.distanceBetweenPoints(eventPosition, center);\n          if (d < minDistance) {\n            minDistance = d;\n            nearestElement = el;\n          }\n        }\n      }\n      if (nearestElement) {\n        var tp = nearestElement.tooltipPosition();\n        x = tp.x;\n        y = tp.y;\n      }\n      return {\n        x: x,\n        y: y\n      };\n    }\n  };\n\n  // Helper to push or concat based on if the 2nd parameter is an array or not\n  function pushOrConcat(base, toPush) {\n    if (toPush) {\n      if (helpers$1.isArray(toPush)) {\n        // base = base.concat(toPush);\n        Array.prototype.push.apply(base, toPush);\n      } else {\n        base.push(toPush);\n      }\n    }\n    return base;\n  }\n\n  /**\r\n   * Returns array of strings split by newline\r\n   * @param {string} value - The value to split by newline.\r\n   * @returns {string[]} value if newline present - Returned from String split() method\r\n   * @function\r\n   */\n  function splitNewlines(str) {\n    if ((typeof str === 'string' || str instanceof String) && str.indexOf('\\n') > -1) {\n      return str.split('\\n');\n    }\n    return str;\n  }\n\n  /**\r\n   * Private helper to create a tooltip item model\r\n   * @param element - the chart element (point, arc, bar) to create the tooltip item for\r\n   * @return new tooltip item\r\n   */\n  function createTooltipItem(element) {\n    var xScale = element._xScale;\n    var yScale = element._yScale || element._scale; // handle radar || polarArea charts\n    var index = element._index;\n    var datasetIndex = element._datasetIndex;\n    var controller = element._chart.getDatasetMeta(datasetIndex).controller;\n    var indexScale = controller._getIndexScale();\n    var valueScale = controller._getValueScale();\n    return {\n      xLabel: xScale ? xScale.getLabelForIndex(index, datasetIndex) : '',\n      yLabel: yScale ? yScale.getLabelForIndex(index, datasetIndex) : '',\n      label: indexScale ? '' + indexScale.getLabelForIndex(index, datasetIndex) : '',\n      value: valueScale ? '' + valueScale.getLabelForIndex(index, datasetIndex) : '',\n      index: index,\n      datasetIndex: datasetIndex,\n      x: element._model.x,\n      y: element._model.y\n    };\n  }\n\n  /**\r\n   * Helper to get the reset model for the tooltip\r\n   * @param tooltipOpts {object} the tooltip options\r\n   */\n  function getBaseModel(tooltipOpts) {\n    var globalDefaults = core_defaults.global;\n    return {\n      // Positioning\n      xPadding: tooltipOpts.xPadding,\n      yPadding: tooltipOpts.yPadding,\n      xAlign: tooltipOpts.xAlign,\n      yAlign: tooltipOpts.yAlign,\n      // Drawing direction and text direction\n      rtl: tooltipOpts.rtl,\n      textDirection: tooltipOpts.textDirection,\n      // Body\n      bodyFontColor: tooltipOpts.bodyFontColor,\n      _bodyFontFamily: valueOrDefault$8(tooltipOpts.bodyFontFamily, globalDefaults.defaultFontFamily),\n      _bodyFontStyle: valueOrDefault$8(tooltipOpts.bodyFontStyle, globalDefaults.defaultFontStyle),\n      _bodyAlign: tooltipOpts.bodyAlign,\n      bodyFontSize: valueOrDefault$8(tooltipOpts.bodyFontSize, globalDefaults.defaultFontSize),\n      bodySpacing: tooltipOpts.bodySpacing,\n      // Title\n      titleFontColor: tooltipOpts.titleFontColor,\n      _titleFontFamily: valueOrDefault$8(tooltipOpts.titleFontFamily, globalDefaults.defaultFontFamily),\n      _titleFontStyle: valueOrDefault$8(tooltipOpts.titleFontStyle, globalDefaults.defaultFontStyle),\n      titleFontSize: valueOrDefault$8(tooltipOpts.titleFontSize, globalDefaults.defaultFontSize),\n      _titleAlign: tooltipOpts.titleAlign,\n      titleSpacing: tooltipOpts.titleSpacing,\n      titleMarginBottom: tooltipOpts.titleMarginBottom,\n      // Footer\n      footerFontColor: tooltipOpts.footerFontColor,\n      _footerFontFamily: valueOrDefault$8(tooltipOpts.footerFontFamily, globalDefaults.defaultFontFamily),\n      _footerFontStyle: valueOrDefault$8(tooltipOpts.footerFontStyle, globalDefaults.defaultFontStyle),\n      footerFontSize: valueOrDefault$8(tooltipOpts.footerFontSize, globalDefaults.defaultFontSize),\n      _footerAlign: tooltipOpts.footerAlign,\n      footerSpacing: tooltipOpts.footerSpacing,\n      footerMarginTop: tooltipOpts.footerMarginTop,\n      // Appearance\n      caretSize: tooltipOpts.caretSize,\n      cornerRadius: tooltipOpts.cornerRadius,\n      backgroundColor: tooltipOpts.backgroundColor,\n      opacity: 0,\n      legendColorBackground: tooltipOpts.multiKeyBackground,\n      displayColors: tooltipOpts.displayColors,\n      borderColor: tooltipOpts.borderColor,\n      borderWidth: tooltipOpts.borderWidth\n    };\n  }\n\n  /**\r\n   * Get the size of the tooltip\r\n   */\n  function getTooltipSize(tooltip, model) {\n    var ctx = tooltip._chart.ctx;\n    var height = model.yPadding * 2; // Tooltip Padding\n    var width = 0;\n\n    // Count of all lines in the body\n    var body = model.body;\n    var combinedBodyLength = body.reduce(function (count, bodyItem) {\n      return count + bodyItem.before.length + bodyItem.lines.length + bodyItem.after.length;\n    }, 0);\n    combinedBodyLength += model.beforeBody.length + model.afterBody.length;\n    var titleLineCount = model.title.length;\n    var footerLineCount = model.footer.length;\n    var titleFontSize = model.titleFontSize;\n    var bodyFontSize = model.bodyFontSize;\n    var footerFontSize = model.footerFontSize;\n    height += titleLineCount * titleFontSize; // Title Lines\n    height += titleLineCount ? (titleLineCount - 1) * model.titleSpacing : 0; // Title Line Spacing\n    height += titleLineCount ? model.titleMarginBottom : 0; // Title's bottom Margin\n    height += combinedBodyLength * bodyFontSize; // Body Lines\n    height += combinedBodyLength ? (combinedBodyLength - 1) * model.bodySpacing : 0; // Body Line Spacing\n    height += footerLineCount ? model.footerMarginTop : 0; // Footer Margin\n    height += footerLineCount * footerFontSize; // Footer Lines\n    height += footerLineCount ? (footerLineCount - 1) * model.footerSpacing : 0; // Footer Line Spacing\n\n    // Title width\n    var widthPadding = 0;\n    var maxLineWidth = function (line) {\n      width = Math.max(width, ctx.measureText(line).width + widthPadding);\n    };\n    ctx.font = helpers$1.fontString(titleFontSize, model._titleFontStyle, model._titleFontFamily);\n    helpers$1.each(model.title, maxLineWidth);\n\n    // Body width\n    ctx.font = helpers$1.fontString(bodyFontSize, model._bodyFontStyle, model._bodyFontFamily);\n    helpers$1.each(model.beforeBody.concat(model.afterBody), maxLineWidth);\n\n    // Body lines may include some extra width due to the color box\n    widthPadding = model.displayColors ? bodyFontSize + 2 : 0;\n    helpers$1.each(body, function (bodyItem) {\n      helpers$1.each(bodyItem.before, maxLineWidth);\n      helpers$1.each(bodyItem.lines, maxLineWidth);\n      helpers$1.each(bodyItem.after, maxLineWidth);\n    });\n\n    // Reset back to 0\n    widthPadding = 0;\n\n    // Footer width\n    ctx.font = helpers$1.fontString(footerFontSize, model._footerFontStyle, model._footerFontFamily);\n    helpers$1.each(model.footer, maxLineWidth);\n\n    // Add padding\n    width += 2 * model.xPadding;\n    return {\n      width: width,\n      height: height\n    };\n  }\n\n  /**\r\n   * Helper to get the alignment of a tooltip given the size\r\n   */\n  function determineAlignment(tooltip, size) {\n    var model = tooltip._model;\n    var chart = tooltip._chart;\n    var chartArea = tooltip._chart.chartArea;\n    var xAlign = 'center';\n    var yAlign = 'center';\n    if (model.y < size.height) {\n      yAlign = 'top';\n    } else if (model.y > chart.height - size.height) {\n      yAlign = 'bottom';\n    }\n    var lf, rf; // functions to determine left, right alignment\n    var olf, orf; // functions to determine if left/right alignment causes tooltip to go outside chart\n    var yf; // function to get the y alignment if the tooltip goes outside of the left or right edges\n    var midX = (chartArea.left + chartArea.right) / 2;\n    var midY = (chartArea.top + chartArea.bottom) / 2;\n    if (yAlign === 'center') {\n      lf = function (x) {\n        return x <= midX;\n      };\n      rf = function (x) {\n        return x > midX;\n      };\n    } else {\n      lf = function (x) {\n        return x <= size.width / 2;\n      };\n      rf = function (x) {\n        return x >= chart.width - size.width / 2;\n      };\n    }\n    olf = function (x) {\n      return x + size.width + model.caretSize + model.caretPadding > chart.width;\n    };\n    orf = function (x) {\n      return x - size.width - model.caretSize - model.caretPadding < 0;\n    };\n    yf = function (y) {\n      return y <= midY ? 'top' : 'bottom';\n    };\n    if (lf(model.x)) {\n      xAlign = 'left';\n\n      // Is tooltip too wide and goes over the right side of the chart.?\n      if (olf(model.x)) {\n        xAlign = 'center';\n        yAlign = yf(model.y);\n      }\n    } else if (rf(model.x)) {\n      xAlign = 'right';\n\n      // Is tooltip too wide and goes outside left edge of canvas?\n      if (orf(model.x)) {\n        xAlign = 'center';\n        yAlign = yf(model.y);\n      }\n    }\n    var opts = tooltip._options;\n    return {\n      xAlign: opts.xAlign ? opts.xAlign : xAlign,\n      yAlign: opts.yAlign ? opts.yAlign : yAlign\n    };\n  }\n\n  /**\r\n   * Helper to get the location a tooltip needs to be placed at given the initial position (via the vm) and the size and alignment\r\n   */\n  function getBackgroundPoint(vm, size, alignment, chart) {\n    // Background Position\n    var x = vm.x;\n    var y = vm.y;\n    var caretSize = vm.caretSize;\n    var caretPadding = vm.caretPadding;\n    var cornerRadius = vm.cornerRadius;\n    var xAlign = alignment.xAlign;\n    var yAlign = alignment.yAlign;\n    var paddingAndSize = caretSize + caretPadding;\n    var radiusAndPadding = cornerRadius + caretPadding;\n    if (xAlign === 'right') {\n      x -= size.width;\n    } else if (xAlign === 'center') {\n      x -= size.width / 2;\n      if (x + size.width > chart.width) {\n        x = chart.width - size.width;\n      }\n      if (x < 0) {\n        x = 0;\n      }\n    }\n    if (yAlign === 'top') {\n      y += paddingAndSize;\n    } else if (yAlign === 'bottom') {\n      y -= size.height + paddingAndSize;\n    } else {\n      y -= size.height / 2;\n    }\n    if (yAlign === 'center') {\n      if (xAlign === 'left') {\n        x += paddingAndSize;\n      } else if (xAlign === 'right') {\n        x -= paddingAndSize;\n      }\n    } else if (xAlign === 'left') {\n      x -= radiusAndPadding;\n    } else if (xAlign === 'right') {\n      x += radiusAndPadding;\n    }\n    return {\n      x: x,\n      y: y\n    };\n  }\n  function getAlignedX(vm, align) {\n    return align === 'center' ? vm.x + vm.width / 2 : align === 'right' ? vm.x + vm.width - vm.xPadding : vm.x + vm.xPadding;\n  }\n\n  /**\r\n   * Helper to build before and after body lines\r\n   */\n  function getBeforeAfterBodyLines(callback) {\n    return pushOrConcat([], splitNewlines(callback));\n  }\n  var exports$4 = core_element.extend({\n    initialize: function () {\n      this._model = getBaseModel(this._options);\n      this._lastActive = [];\n    },\n    // Get the title\n    // Args are: (tooltipItem, data)\n    getTitle: function () {\n      var me = this;\n      var opts = me._options;\n      var callbacks = opts.callbacks;\n      var beforeTitle = callbacks.beforeTitle.apply(me, arguments);\n      var title = callbacks.title.apply(me, arguments);\n      var afterTitle = callbacks.afterTitle.apply(me, arguments);\n      var lines = [];\n      lines = pushOrConcat(lines, splitNewlines(beforeTitle));\n      lines = pushOrConcat(lines, splitNewlines(title));\n      lines = pushOrConcat(lines, splitNewlines(afterTitle));\n      return lines;\n    },\n    // Args are: (tooltipItem, data)\n    getBeforeBody: function () {\n      return getBeforeAfterBodyLines(this._options.callbacks.beforeBody.apply(this, arguments));\n    },\n    // Args are: (tooltipItem, data)\n    getBody: function (tooltipItems, data) {\n      var me = this;\n      var callbacks = me._options.callbacks;\n      var bodyItems = [];\n      helpers$1.each(tooltipItems, function (tooltipItem) {\n        var bodyItem = {\n          before: [],\n          lines: [],\n          after: []\n        };\n        pushOrConcat(bodyItem.before, splitNewlines(callbacks.beforeLabel.call(me, tooltipItem, data)));\n        pushOrConcat(bodyItem.lines, callbacks.label.call(me, tooltipItem, data));\n        pushOrConcat(bodyItem.after, splitNewlines(callbacks.afterLabel.call(me, tooltipItem, data)));\n        bodyItems.push(bodyItem);\n      });\n      return bodyItems;\n    },\n    // Args are: (tooltipItem, data)\n    getAfterBody: function () {\n      return getBeforeAfterBodyLines(this._options.callbacks.afterBody.apply(this, arguments));\n    },\n    // Get the footer and beforeFooter and afterFooter lines\n    // Args are: (tooltipItem, data)\n    getFooter: function () {\n      var me = this;\n      var callbacks = me._options.callbacks;\n      var beforeFooter = callbacks.beforeFooter.apply(me, arguments);\n      var footer = callbacks.footer.apply(me, arguments);\n      var afterFooter = callbacks.afterFooter.apply(me, arguments);\n      var lines = [];\n      lines = pushOrConcat(lines, splitNewlines(beforeFooter));\n      lines = pushOrConcat(lines, splitNewlines(footer));\n      lines = pushOrConcat(lines, splitNewlines(afterFooter));\n      return lines;\n    },\n    update: function (changed) {\n      var me = this;\n      var opts = me._options;\n\n      // Need to regenerate the model because its faster than using extend and it is necessary due to the optimization in Chart.Element.transition\n      // that does _view = _model if ease === 1. This causes the 2nd tooltip update to set properties in both the view and model at the same time\n      // which breaks any animations.\n      var existingModel = me._model;\n      var model = me._model = getBaseModel(opts);\n      var active = me._active;\n      var data = me._data;\n\n      // In the case where active.length === 0 we need to keep these at existing values for good animations\n      var alignment = {\n        xAlign: existingModel.xAlign,\n        yAlign: existingModel.yAlign\n      };\n      var backgroundPoint = {\n        x: existingModel.x,\n        y: existingModel.y\n      };\n      var tooltipSize = {\n        width: existingModel.width,\n        height: existingModel.height\n      };\n      var tooltipPosition = {\n        x: existingModel.caretX,\n        y: existingModel.caretY\n      };\n      var i, len;\n      if (active.length) {\n        model.opacity = 1;\n        var labelColors = [];\n        var labelTextColors = [];\n        tooltipPosition = positioners[opts.position].call(me, active, me._eventPosition);\n        var tooltipItems = [];\n        for (i = 0, len = active.length; i < len; ++i) {\n          tooltipItems.push(createTooltipItem(active[i]));\n        }\n\n        // If the user provided a filter function, use it to modify the tooltip items\n        if (opts.filter) {\n          tooltipItems = tooltipItems.filter(function (a) {\n            return opts.filter(a, data);\n          });\n        }\n\n        // If the user provided a sorting function, use it to modify the tooltip items\n        if (opts.itemSort) {\n          tooltipItems = tooltipItems.sort(function (a, b) {\n            return opts.itemSort(a, b, data);\n          });\n        }\n\n        // Determine colors for boxes\n        helpers$1.each(tooltipItems, function (tooltipItem) {\n          labelColors.push(opts.callbacks.labelColor.call(me, tooltipItem, me._chart));\n          labelTextColors.push(opts.callbacks.labelTextColor.call(me, tooltipItem, me._chart));\n        });\n\n        // Build the Text Lines\n        model.title = me.getTitle(tooltipItems, data);\n        model.beforeBody = me.getBeforeBody(tooltipItems, data);\n        model.body = me.getBody(tooltipItems, data);\n        model.afterBody = me.getAfterBody(tooltipItems, data);\n        model.footer = me.getFooter(tooltipItems, data);\n\n        // Initial positioning and colors\n        model.x = tooltipPosition.x;\n        model.y = tooltipPosition.y;\n        model.caretPadding = opts.caretPadding;\n        model.labelColors = labelColors;\n        model.labelTextColors = labelTextColors;\n\n        // data points\n        model.dataPoints = tooltipItems;\n\n        // We need to determine alignment of the tooltip\n        tooltipSize = getTooltipSize(this, model);\n        alignment = determineAlignment(this, tooltipSize);\n        // Final Size and Position\n        backgroundPoint = getBackgroundPoint(model, tooltipSize, alignment, me._chart);\n      } else {\n        model.opacity = 0;\n      }\n      model.xAlign = alignment.xAlign;\n      model.yAlign = alignment.yAlign;\n      model.x = backgroundPoint.x;\n      model.y = backgroundPoint.y;\n      model.width = tooltipSize.width;\n      model.height = tooltipSize.height;\n\n      // Point where the caret on the tooltip points to\n      model.caretX = tooltipPosition.x;\n      model.caretY = tooltipPosition.y;\n      me._model = model;\n      if (changed && opts.custom) {\n        opts.custom.call(me, model);\n      }\n      return me;\n    },\n    drawCaret: function (tooltipPoint, size) {\n      var ctx = this._chart.ctx;\n      var vm = this._view;\n      var caretPosition = this.getCaretPosition(tooltipPoint, size, vm);\n      ctx.lineTo(caretPosition.x1, caretPosition.y1);\n      ctx.lineTo(caretPosition.x2, caretPosition.y2);\n      ctx.lineTo(caretPosition.x3, caretPosition.y3);\n    },\n    getCaretPosition: function (tooltipPoint, size, vm) {\n      var x1, x2, x3, y1, y2, y3;\n      var caretSize = vm.caretSize;\n      var cornerRadius = vm.cornerRadius;\n      var xAlign = vm.xAlign;\n      var yAlign = vm.yAlign;\n      var ptX = tooltipPoint.x;\n      var ptY = tooltipPoint.y;\n      var width = size.width;\n      var height = size.height;\n      if (yAlign === 'center') {\n        y2 = ptY + height / 2;\n        if (xAlign === 'left') {\n          x1 = ptX;\n          x2 = x1 - caretSize;\n          x3 = x1;\n          y1 = y2 + caretSize;\n          y3 = y2 - caretSize;\n        } else {\n          x1 = ptX + width;\n          x2 = x1 + caretSize;\n          x3 = x1;\n          y1 = y2 - caretSize;\n          y3 = y2 + caretSize;\n        }\n      } else {\n        if (xAlign === 'left') {\n          x2 = ptX + cornerRadius + caretSize;\n          x1 = x2 - caretSize;\n          x3 = x2 + caretSize;\n        } else if (xAlign === 'right') {\n          x2 = ptX + width - cornerRadius - caretSize;\n          x1 = x2 - caretSize;\n          x3 = x2 + caretSize;\n        } else {\n          x2 = vm.caretX;\n          x1 = x2 - caretSize;\n          x3 = x2 + caretSize;\n        }\n        if (yAlign === 'top') {\n          y1 = ptY;\n          y2 = y1 - caretSize;\n          y3 = y1;\n        } else {\n          y1 = ptY + height;\n          y2 = y1 + caretSize;\n          y3 = y1;\n          // invert drawing order\n          var tmp = x3;\n          x3 = x1;\n          x1 = tmp;\n        }\n      }\n      return {\n        x1: x1,\n        x2: x2,\n        x3: x3,\n        y1: y1,\n        y2: y2,\n        y3: y3\n      };\n    },\n    drawTitle: function (pt, vm, ctx) {\n      var title = vm.title;\n      var length = title.length;\n      var titleFontSize, titleSpacing, i;\n      if (length) {\n        var rtlHelper = getRtlHelper(vm.rtl, vm.x, vm.width);\n        pt.x = getAlignedX(vm, vm._titleAlign);\n        ctx.textAlign = rtlHelper.textAlign(vm._titleAlign);\n        ctx.textBaseline = 'middle';\n        titleFontSize = vm.titleFontSize;\n        titleSpacing = vm.titleSpacing;\n        ctx.fillStyle = vm.titleFontColor;\n        ctx.font = helpers$1.fontString(titleFontSize, vm._titleFontStyle, vm._titleFontFamily);\n        for (i = 0; i < length; ++i) {\n          ctx.fillText(title[i], rtlHelper.x(pt.x), pt.y + titleFontSize / 2);\n          pt.y += titleFontSize + titleSpacing; // Line Height and spacing\n\n          if (i + 1 === length) {\n            pt.y += vm.titleMarginBottom - titleSpacing; // If Last, add margin, remove spacing\n          }\n        }\n      }\n    },\n    drawBody: function (pt, vm, ctx) {\n      var bodyFontSize = vm.bodyFontSize;\n      var bodySpacing = vm.bodySpacing;\n      var bodyAlign = vm._bodyAlign;\n      var body = vm.body;\n      var drawColorBoxes = vm.displayColors;\n      var xLinePadding = 0;\n      var colorX = drawColorBoxes ? getAlignedX(vm, 'left') : 0;\n      var rtlHelper = getRtlHelper(vm.rtl, vm.x, vm.width);\n      var fillLineOfText = function (line) {\n        ctx.fillText(line, rtlHelper.x(pt.x + xLinePadding), pt.y + bodyFontSize / 2);\n        pt.y += bodyFontSize + bodySpacing;\n      };\n      var bodyItem, textColor, labelColors, lines, i, j, ilen, jlen;\n      var bodyAlignForCalculation = rtlHelper.textAlign(bodyAlign);\n      ctx.textAlign = bodyAlign;\n      ctx.textBaseline = 'middle';\n      ctx.font = helpers$1.fontString(bodyFontSize, vm._bodyFontStyle, vm._bodyFontFamily);\n      pt.x = getAlignedX(vm, bodyAlignForCalculation);\n\n      // Before body lines\n      ctx.fillStyle = vm.bodyFontColor;\n      helpers$1.each(vm.beforeBody, fillLineOfText);\n      xLinePadding = drawColorBoxes && bodyAlignForCalculation !== 'right' ? bodyAlign === 'center' ? bodyFontSize / 2 + 1 : bodyFontSize + 2 : 0;\n\n      // Draw body lines now\n      for (i = 0, ilen = body.length; i < ilen; ++i) {\n        bodyItem = body[i];\n        textColor = vm.labelTextColors[i];\n        labelColors = vm.labelColors[i];\n        ctx.fillStyle = textColor;\n        helpers$1.each(bodyItem.before, fillLineOfText);\n        lines = bodyItem.lines;\n        for (j = 0, jlen = lines.length; j < jlen; ++j) {\n          // Draw Legend-like boxes if needed\n          if (drawColorBoxes) {\n            var rtlColorX = rtlHelper.x(colorX);\n\n            // Fill a white rect so that colours merge nicely if the opacity is < 1\n            ctx.fillStyle = vm.legendColorBackground;\n            ctx.fillRect(rtlHelper.leftForLtr(rtlColorX, bodyFontSize), pt.y, bodyFontSize, bodyFontSize);\n\n            // Border\n            ctx.lineWidth = 1;\n            ctx.strokeStyle = labelColors.borderColor;\n            ctx.strokeRect(rtlHelper.leftForLtr(rtlColorX, bodyFontSize), pt.y, bodyFontSize, bodyFontSize);\n\n            // Inner square\n            ctx.fillStyle = labelColors.backgroundColor;\n            ctx.fillRect(rtlHelper.leftForLtr(rtlHelper.xPlus(rtlColorX, 1), bodyFontSize - 2), pt.y + 1, bodyFontSize - 2, bodyFontSize - 2);\n            ctx.fillStyle = textColor;\n          }\n          fillLineOfText(lines[j]);\n        }\n        helpers$1.each(bodyItem.after, fillLineOfText);\n      }\n\n      // Reset back to 0 for after body\n      xLinePadding = 0;\n\n      // After body lines\n      helpers$1.each(vm.afterBody, fillLineOfText);\n      pt.y -= bodySpacing; // Remove last body spacing\n    },\n    drawFooter: function (pt, vm, ctx) {\n      var footer = vm.footer;\n      var length = footer.length;\n      var footerFontSize, i;\n      if (length) {\n        var rtlHelper = getRtlHelper(vm.rtl, vm.x, vm.width);\n        pt.x = getAlignedX(vm, vm._footerAlign);\n        pt.y += vm.footerMarginTop;\n        ctx.textAlign = rtlHelper.textAlign(vm._footerAlign);\n        ctx.textBaseline = 'middle';\n        footerFontSize = vm.footerFontSize;\n        ctx.fillStyle = vm.footerFontColor;\n        ctx.font = helpers$1.fontString(footerFontSize, vm._footerFontStyle, vm._footerFontFamily);\n        for (i = 0; i < length; ++i) {\n          ctx.fillText(footer[i], rtlHelper.x(pt.x), pt.y + footerFontSize / 2);\n          pt.y += footerFontSize + vm.footerSpacing;\n        }\n      }\n    },\n    drawBackground: function (pt, vm, ctx, tooltipSize) {\n      ctx.fillStyle = vm.backgroundColor;\n      ctx.strokeStyle = vm.borderColor;\n      ctx.lineWidth = vm.borderWidth;\n      var xAlign = vm.xAlign;\n      var yAlign = vm.yAlign;\n      var x = pt.x;\n      var y = pt.y;\n      var width = tooltipSize.width;\n      var height = tooltipSize.height;\n      var radius = vm.cornerRadius;\n      ctx.beginPath();\n      ctx.moveTo(x + radius, y);\n      if (yAlign === 'top') {\n        this.drawCaret(pt, tooltipSize);\n      }\n      ctx.lineTo(x + width - radius, y);\n      ctx.quadraticCurveTo(x + width, y, x + width, y + radius);\n      if (yAlign === 'center' && xAlign === 'right') {\n        this.drawCaret(pt, tooltipSize);\n      }\n      ctx.lineTo(x + width, y + height - radius);\n      ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);\n      if (yAlign === 'bottom') {\n        this.drawCaret(pt, tooltipSize);\n      }\n      ctx.lineTo(x + radius, y + height);\n      ctx.quadraticCurveTo(x, y + height, x, y + height - radius);\n      if (yAlign === 'center' && xAlign === 'left') {\n        this.drawCaret(pt, tooltipSize);\n      }\n      ctx.lineTo(x, y + radius);\n      ctx.quadraticCurveTo(x, y, x + radius, y);\n      ctx.closePath();\n      ctx.fill();\n      if (vm.borderWidth > 0) {\n        ctx.stroke();\n      }\n    },\n    draw: function () {\n      var ctx = this._chart.ctx;\n      var vm = this._view;\n      if (vm.opacity === 0) {\n        return;\n      }\n      var tooltipSize = {\n        width: vm.width,\n        height: vm.height\n      };\n      var pt = {\n        x: vm.x,\n        y: vm.y\n      };\n\n      // IE11/Edge does not like very small opacities, so snap to 0\n      var opacity = Math.abs(vm.opacity < 1e-3) ? 0 : vm.opacity;\n\n      // Truthy/falsey value for empty tooltip\n      var hasTooltipContent = vm.title.length || vm.beforeBody.length || vm.body.length || vm.afterBody.length || vm.footer.length;\n      if (this._options.enabled && hasTooltipContent) {\n        ctx.save();\n        ctx.globalAlpha = opacity;\n\n        // Draw Background\n        this.drawBackground(pt, vm, ctx, tooltipSize);\n\n        // Draw Title, Body, and Footer\n        pt.y += vm.yPadding;\n        helpers$1.rtl.overrideTextDirection(ctx, vm.textDirection);\n\n        // Titles\n        this.drawTitle(pt, vm, ctx);\n\n        // Body\n        this.drawBody(pt, vm, ctx);\n\n        // Footer\n        this.drawFooter(pt, vm, ctx);\n        helpers$1.rtl.restoreTextDirection(ctx, vm.textDirection);\n        ctx.restore();\n      }\n    },\n    /**\r\n     * Handle an event\r\n     * @private\r\n     * @param {IEvent} event - The event to handle\r\n     * @returns {boolean} true if the tooltip changed\r\n     */\n    handleEvent: function (e) {\n      var me = this;\n      var options = me._options;\n      var changed = false;\n      me._lastActive = me._lastActive || [];\n\n      // Find Active Elements for tooltips\n      if (e.type === 'mouseout') {\n        me._active = [];\n      } else {\n        me._active = me._chart.getElementsAtEventForMode(e, options.mode, options);\n        if (options.reverse) {\n          me._active.reverse();\n        }\n      }\n\n      // Remember Last Actives\n      changed = !helpers$1.arrayEquals(me._active, me._lastActive);\n\n      // Only handle target event on tooltip change\n      if (changed) {\n        me._lastActive = me._active;\n        if (options.enabled || options.custom) {\n          me._eventPosition = {\n            x: e.x,\n            y: e.y\n          };\n          me.update(true);\n          me.pivot();\n        }\n      }\n      return changed;\n    }\n  });\n\n  /**\r\n   * @namespace Chart.Tooltip.positioners\r\n   */\n  var positioners_1 = positioners;\n  var core_tooltip = exports$4;\n  core_tooltip.positioners = positioners_1;\n  var valueOrDefault$9 = helpers$1.valueOrDefault;\n  core_defaults._set('global', {\n    elements: {},\n    events: ['mousemove', 'mouseout', 'click', 'touchstart', 'touchmove'],\n    hover: {\n      onHover: null,\n      mode: 'nearest',\n      intersect: true,\n      animationDuration: 400\n    },\n    onClick: null,\n    maintainAspectRatio: true,\n    responsive: true,\n    responsiveAnimationDuration: 0\n  });\n\n  /**\r\n   * Recursively merge the given config objects representing the `scales` option\r\n   * by incorporating scale defaults in `xAxes` and `yAxes` array items, then\r\n   * returns a deep copy of the result, thus doesn't alter inputs.\r\n   */\n  function mergeScaleConfig(/* config objects ... */\n  ) {\n    return helpers$1.merge(Object.create(null), [].slice.call(arguments), {\n      merger: function (key, target, source, options) {\n        if (key === 'xAxes' || key === 'yAxes') {\n          var slen = source[key].length;\n          var i, type, scale;\n          if (!target[key]) {\n            target[key] = [];\n          }\n          for (i = 0; i < slen; ++i) {\n            scale = source[key][i];\n            type = valueOrDefault$9(scale.type, key === 'xAxes' ? 'category' : 'linear');\n            if (i >= target[key].length) {\n              target[key].push({});\n            }\n            if (!target[key][i].type || scale.type && scale.type !== target[key][i].type) {\n              // new/untyped scale or type changed: let's apply the new defaults\n              // then merge source scale to correctly overwrite the defaults.\n              helpers$1.merge(target[key][i], [core_scaleService.getScaleDefaults(type), scale]);\n            } else {\n              // scales type are the same\n              helpers$1.merge(target[key][i], scale);\n            }\n          }\n        } else {\n          helpers$1._merger(key, target, source, options);\n        }\n      }\n    });\n  }\n\n  /**\r\n   * Recursively merge the given config objects as the root options by handling\r\n   * default scale options for the `scales` and `scale` properties, then returns\r\n   * a deep copy of the result, thus doesn't alter inputs.\r\n   */\n  function mergeConfig(/* config objects ... */\n  ) {\n    return helpers$1.merge(Object.create(null), [].slice.call(arguments), {\n      merger: function (key, target, source, options) {\n        var tval = target[key] || Object.create(null);\n        var sval = source[key];\n        if (key === 'scales') {\n          // scale config merging is complex. Add our own function here for that\n          target[key] = mergeScaleConfig(tval, sval);\n        } else if (key === 'scale') {\n          // used in polar area & radar charts since there is only one scale\n          target[key] = helpers$1.merge(tval, [core_scaleService.getScaleDefaults(sval.type), sval]);\n        } else {\n          helpers$1._merger(key, target, source, options);\n        }\n      }\n    });\n  }\n  function initConfig(config) {\n    config = config || Object.create(null);\n\n    // Do NOT use mergeConfig for the data object because this method merges arrays\n    // and so would change references to labels and datasets, preventing data updates.\n    var data = config.data = config.data || {};\n    data.datasets = data.datasets || [];\n    data.labels = data.labels || [];\n    config.options = mergeConfig(core_defaults.global, core_defaults[config.type], config.options || {});\n    return config;\n  }\n  function updateConfig(chart) {\n    var newOptions = chart.options;\n    helpers$1.each(chart.scales, function (scale) {\n      core_layouts.removeBox(chart, scale);\n    });\n    newOptions = mergeConfig(core_defaults.global, core_defaults[chart.config.type], newOptions);\n    chart.options = chart.config.options = newOptions;\n    chart.ensureScalesHaveIDs();\n    chart.buildOrUpdateScales();\n\n    // Tooltip\n    chart.tooltip._options = newOptions.tooltips;\n    chart.tooltip.initialize();\n  }\n  function nextAvailableScaleId(axesOpts, prefix, index) {\n    var id;\n    var hasId = function (obj) {\n      return obj.id === id;\n    };\n    do {\n      id = prefix + index++;\n    } while (helpers$1.findIndex(axesOpts, hasId) >= 0);\n    return id;\n  }\n  function positionIsHorizontal(position) {\n    return position === 'top' || position === 'bottom';\n  }\n  function compare2Level(l1, l2) {\n    return function (a, b) {\n      return a[l1] === b[l1] ? a[l2] - b[l2] : a[l1] - b[l1];\n    };\n  }\n  var Chart = function (item, config) {\n    this.construct(item, config);\n    return this;\n  };\n  helpers$1.extend(Chart.prototype, /** @lends Chart */{\n    /**\r\n     * @private\r\n     */\n    construct: function (item, config) {\n      var me = this;\n      config = initConfig(config);\n      var context = platform.acquireContext(item, config);\n      var canvas = context && context.canvas;\n      var height = canvas && canvas.height;\n      var width = canvas && canvas.width;\n      me.id = helpers$1.uid();\n      me.ctx = context;\n      me.canvas = canvas;\n      me.config = config;\n      me.width = width;\n      me.height = height;\n      me.aspectRatio = height ? width / height : null;\n      me.options = config.options;\n      me._bufferedRender = false;\n      me._layers = [];\n\n      /**\r\n       * Provided for backward compatibility, Chart and Chart.Controller have been merged,\r\n       * the \"instance\" still need to be defined since it might be called from plugins.\r\n       * @prop Chart#chart\r\n       * @deprecated since version 2.6.0\r\n       * @todo remove at version 3\r\n       * @private\r\n       */\n      me.chart = me;\n      me.controller = me; // chart.chart.controller #inception\n\n      // Add the chart instance to the global namespace\n      Chart.instances[me.id] = me;\n\n      // Define alias to the config data: `chart.data === chart.config.data`\n      Object.defineProperty(me, 'data', {\n        get: function () {\n          return me.config.data;\n        },\n        set: function (value) {\n          me.config.data = value;\n        }\n      });\n      if (!context || !canvas) {\n        // The given item is not a compatible context2d element, let's return before finalizing\n        // the chart initialization but after setting basic chart / controller properties that\n        // can help to figure out that the chart is not valid (e.g chart.canvas !== null);\n        // https://github.com/chartjs/Chart.js/issues/2807\n        console.error(\"Failed to create chart: can't acquire context from the given item\");\n        return;\n      }\n      me.initialize();\n      me.update();\n    },\n    /**\r\n     * @private\r\n     */\n    initialize: function () {\n      var me = this;\n\n      // Before init plugin notification\n      core_plugins.notify(me, 'beforeInit');\n      helpers$1.retinaScale(me, me.options.devicePixelRatio);\n      me.bindEvents();\n      if (me.options.responsive) {\n        // Initial resize before chart draws (must be silent to preserve initial animations).\n        me.resize(true);\n      }\n      me.initToolTip();\n\n      // After init plugin notification\n      core_plugins.notify(me, 'afterInit');\n      return me;\n    },\n    clear: function () {\n      helpers$1.canvas.clear(this);\n      return this;\n    },\n    stop: function () {\n      // Stops any current animation loop occurring\n      core_animations.cancelAnimation(this);\n      return this;\n    },\n    resize: function (silent) {\n      var me = this;\n      var options = me.options;\n      var canvas = me.canvas;\n      var aspectRatio = options.maintainAspectRatio && me.aspectRatio || null;\n\n      // the canvas render width and height will be casted to integers so make sure that\n      // the canvas display style uses the same integer values to avoid blurring effect.\n\n      // Set to 0 instead of canvas.size because the size defaults to 300x150 if the element is collapsed\n      var newWidth = Math.max(0, Math.floor(helpers$1.getMaximumWidth(canvas)));\n      var newHeight = Math.max(0, Math.floor(aspectRatio ? newWidth / aspectRatio : helpers$1.getMaximumHeight(canvas)));\n      if (me.width === newWidth && me.height === newHeight) {\n        return;\n      }\n      canvas.width = me.width = newWidth;\n      canvas.height = me.height = newHeight;\n      canvas.style.width = newWidth + 'px';\n      canvas.style.height = newHeight + 'px';\n      helpers$1.retinaScale(me, options.devicePixelRatio);\n      if (!silent) {\n        // Notify any plugins about the resize\n        var newSize = {\n          width: newWidth,\n          height: newHeight\n        };\n        core_plugins.notify(me, 'resize', [newSize]);\n\n        // Notify of resize\n        if (options.onResize) {\n          options.onResize(me, newSize);\n        }\n        me.stop();\n        me.update({\n          duration: options.responsiveAnimationDuration\n        });\n      }\n    },\n    ensureScalesHaveIDs: function () {\n      var options = this.options;\n      var scalesOptions = options.scales || {};\n      var scaleOptions = options.scale;\n      helpers$1.each(scalesOptions.xAxes, function (xAxisOptions, index) {\n        if (!xAxisOptions.id) {\n          xAxisOptions.id = nextAvailableScaleId(scalesOptions.xAxes, 'x-axis-', index);\n        }\n      });\n      helpers$1.each(scalesOptions.yAxes, function (yAxisOptions, index) {\n        if (!yAxisOptions.id) {\n          yAxisOptions.id = nextAvailableScaleId(scalesOptions.yAxes, 'y-axis-', index);\n        }\n      });\n      if (scaleOptions) {\n        scaleOptions.id = scaleOptions.id || 'scale';\n      }\n    },\n    /**\r\n     * Builds a map of scale ID to scale object for future lookup.\r\n     */\n    buildOrUpdateScales: function () {\n      var me = this;\n      var options = me.options;\n      var scales = me.scales || {};\n      var items = [];\n      var updated = Object.keys(scales).reduce(function (obj, id) {\n        obj[id] = false;\n        return obj;\n      }, {});\n      if (options.scales) {\n        items = items.concat((options.scales.xAxes || []).map(function (xAxisOptions) {\n          return {\n            options: xAxisOptions,\n            dtype: 'category',\n            dposition: 'bottom'\n          };\n        }), (options.scales.yAxes || []).map(function (yAxisOptions) {\n          return {\n            options: yAxisOptions,\n            dtype: 'linear',\n            dposition: 'left'\n          };\n        }));\n      }\n      if (options.scale) {\n        items.push({\n          options: options.scale,\n          dtype: 'radialLinear',\n          isDefault: true,\n          dposition: 'chartArea'\n        });\n      }\n      helpers$1.each(items, function (item) {\n        var scaleOptions = item.options;\n        var id = scaleOptions.id;\n        var scaleType = valueOrDefault$9(scaleOptions.type, item.dtype);\n        if (positionIsHorizontal(scaleOptions.position) !== positionIsHorizontal(item.dposition)) {\n          scaleOptions.position = item.dposition;\n        }\n        updated[id] = true;\n        var scale = null;\n        if (id in scales && scales[id].type === scaleType) {\n          scale = scales[id];\n          scale.options = scaleOptions;\n          scale.ctx = me.ctx;\n          scale.chart = me;\n        } else {\n          var scaleClass = core_scaleService.getScaleConstructor(scaleType);\n          if (!scaleClass) {\n            return;\n          }\n          scale = new scaleClass({\n            id: id,\n            type: scaleType,\n            options: scaleOptions,\n            ctx: me.ctx,\n            chart: me\n          });\n          scales[scale.id] = scale;\n        }\n        scale.mergeTicksOptions();\n\n        // TODO(SB): I think we should be able to remove this custom case (options.scale)\n        // and consider it as a regular scale part of the \"scales\"\" map only! This would\n        // make the logic easier and remove some useless? custom code.\n        if (item.isDefault) {\n          me.scale = scale;\n        }\n      });\n      // clear up discarded scales\n      helpers$1.each(updated, function (hasUpdated, id) {\n        if (!hasUpdated) {\n          delete scales[id];\n        }\n      });\n      me.scales = scales;\n      core_scaleService.addScalesToLayout(this);\n    },\n    buildOrUpdateControllers: function () {\n      var me = this;\n      var newControllers = [];\n      var datasets = me.data.datasets;\n      var i, ilen;\n      for (i = 0, ilen = datasets.length; i < ilen; i++) {\n        var dataset = datasets[i];\n        var meta = me.getDatasetMeta(i);\n        var type = dataset.type || me.config.type;\n        if (meta.type && meta.type !== type) {\n          me.destroyDatasetMeta(i);\n          meta = me.getDatasetMeta(i);\n        }\n        meta.type = type;\n        meta.order = dataset.order || 0;\n        meta.index = i;\n        if (meta.controller) {\n          meta.controller.updateIndex(i);\n          meta.controller.linkScales();\n        } else {\n          var ControllerClass = controllers[meta.type];\n          if (ControllerClass === undefined) {\n            throw new Error('\"' + meta.type + '\" is not a chart type.');\n          }\n          meta.controller = new ControllerClass(me, i);\n          newControllers.push(meta.controller);\n        }\n      }\n      return newControllers;\n    },\n    /**\r\n     * Reset the elements of all datasets\r\n     * @private\r\n     */\n    resetElements: function () {\n      var me = this;\n      helpers$1.each(me.data.datasets, function (dataset, datasetIndex) {\n        me.getDatasetMeta(datasetIndex).controller.reset();\n      }, me);\n    },\n    /**\r\n    * Resets the chart back to it's state before the initial animation\r\n    */\n    reset: function () {\n      this.resetElements();\n      this.tooltip.initialize();\n    },\n    update: function (config) {\n      var me = this;\n      var i, ilen;\n      if (!config || typeof config !== 'object') {\n        // backwards compatibility\n        config = {\n          duration: config,\n          lazy: arguments[1]\n        };\n      }\n      updateConfig(me);\n\n      // plugins options references might have change, let's invalidate the cache\n      // https://github.com/chartjs/Chart.js/issues/5111#issuecomment-355934167\n      core_plugins._invalidate(me);\n      if (core_plugins.notify(me, 'beforeUpdate') === false) {\n        return;\n      }\n\n      // In case the entire data object changed\n      me.tooltip._data = me.data;\n\n      // Make sure dataset controllers are updated and new controllers are reset\n      var newControllers = me.buildOrUpdateControllers();\n\n      // Make sure all dataset controllers have correct meta data counts\n      for (i = 0, ilen = me.data.datasets.length; i < ilen; i++) {\n        me.getDatasetMeta(i).controller.buildOrUpdateElements();\n      }\n      me.updateLayout();\n\n      // Can only reset the new controllers after the scales have been updated\n      if (me.options.animation && me.options.animation.duration) {\n        helpers$1.each(newControllers, function (controller) {\n          controller.reset();\n        });\n      }\n      me.updateDatasets();\n\n      // Need to reset tooltip in case it is displayed with elements that are removed\n      // after update.\n      me.tooltip.initialize();\n\n      // Last active contains items that were previously in the tooltip.\n      // When we reset the tooltip, we need to clear it\n      me.lastActive = [];\n\n      // Do this before render so that any plugins that need final scale updates can use it\n      core_plugins.notify(me, 'afterUpdate');\n      me._layers.sort(compare2Level('z', '_idx'));\n      if (me._bufferedRender) {\n        me._bufferedRequest = {\n          duration: config.duration,\n          easing: config.easing,\n          lazy: config.lazy\n        };\n      } else {\n        me.render(config);\n      }\n    },\n    /**\r\n     * Updates the chart layout unless a plugin returns `false` to the `beforeLayout`\r\n     * hook, in which case, plugins will not be called on `afterLayout`.\r\n     * @private\r\n     */\n    updateLayout: function () {\n      var me = this;\n      if (core_plugins.notify(me, 'beforeLayout') === false) {\n        return;\n      }\n      core_layouts.update(this, this.width, this.height);\n      me._layers = [];\n      helpers$1.each(me.boxes, function (box) {\n        // _configure is called twice, once in core.scale.update and once here.\n        // Here the boxes are fully updated and at their final positions.\n        if (box._configure) {\n          box._configure();\n        }\n        me._layers.push.apply(me._layers, box._layers());\n      }, me);\n      me._layers.forEach(function (item, index) {\n        item._idx = index;\n      });\n\n      /**\r\n       * Provided for backward compatibility, use `afterLayout` instead.\r\n       * @method IPlugin#afterScaleUpdate\r\n       * @deprecated since version 2.5.0\r\n       * @todo remove at version 3\r\n       * @private\r\n       */\n      core_plugins.notify(me, 'afterScaleUpdate');\n      core_plugins.notify(me, 'afterLayout');\n    },\n    /**\r\n     * Updates all datasets unless a plugin returns `false` to the `beforeDatasetsUpdate`\r\n     * hook, in which case, plugins will not be called on `afterDatasetsUpdate`.\r\n     * @private\r\n     */\n    updateDatasets: function () {\n      var me = this;\n      if (core_plugins.notify(me, 'beforeDatasetsUpdate') === false) {\n        return;\n      }\n      for (var i = 0, ilen = me.data.datasets.length; i < ilen; ++i) {\n        me.updateDataset(i);\n      }\n      core_plugins.notify(me, 'afterDatasetsUpdate');\n    },\n    /**\r\n     * Updates dataset at index unless a plugin returns `false` to the `beforeDatasetUpdate`\r\n     * hook, in which case, plugins will not be called on `afterDatasetUpdate`.\r\n     * @private\r\n     */\n    updateDataset: function (index) {\n      var me = this;\n      var meta = me.getDatasetMeta(index);\n      var args = {\n        meta: meta,\n        index: index\n      };\n      if (core_plugins.notify(me, 'beforeDatasetUpdate', [args]) === false) {\n        return;\n      }\n      meta.controller._update();\n      core_plugins.notify(me, 'afterDatasetUpdate', [args]);\n    },\n    render: function (config) {\n      var me = this;\n      if (!config || typeof config !== 'object') {\n        // backwards compatibility\n        config = {\n          duration: config,\n          lazy: arguments[1]\n        };\n      }\n      var animationOptions = me.options.animation;\n      var duration = valueOrDefault$9(config.duration, animationOptions && animationOptions.duration);\n      var lazy = config.lazy;\n      if (core_plugins.notify(me, 'beforeRender') === false) {\n        return;\n      }\n      var onComplete = function (animation) {\n        core_plugins.notify(me, 'afterRender');\n        helpers$1.callback(animationOptions && animationOptions.onComplete, [animation], me);\n      };\n      if (animationOptions && duration) {\n        var animation = new core_animation({\n          numSteps: duration / 16.66,\n          // 60 fps\n          easing: config.easing || animationOptions.easing,\n          render: function (chart, animationObject) {\n            var easingFunction = helpers$1.easing.effects[animationObject.easing];\n            var currentStep = animationObject.currentStep;\n            var stepDecimal = currentStep / animationObject.numSteps;\n            chart.draw(easingFunction(stepDecimal), stepDecimal, currentStep);\n          },\n          onAnimationProgress: animationOptions.onProgress,\n          onAnimationComplete: onComplete\n        });\n        core_animations.addAnimation(me, animation, duration, lazy);\n      } else {\n        me.draw();\n\n        // See https://github.com/chartjs/Chart.js/issues/3781\n        onComplete(new core_animation({\n          numSteps: 0,\n          chart: me\n        }));\n      }\n      return me;\n    },\n    draw: function (easingValue) {\n      var me = this;\n      var i, layers;\n      me.clear();\n      if (helpers$1.isNullOrUndef(easingValue)) {\n        easingValue = 1;\n      }\n      me.transition(easingValue);\n      if (me.width <= 0 || me.height <= 0) {\n        return;\n      }\n      if (core_plugins.notify(me, 'beforeDraw', [easingValue]) === false) {\n        return;\n      }\n\n      // Because of plugin hooks (before/afterDatasetsDraw), datasets can't\n      // currently be part of layers. Instead, we draw\n      // layers <= 0 before(default, backward compat), and the rest after\n      layers = me._layers;\n      for (i = 0; i < layers.length && layers[i].z <= 0; ++i) {\n        layers[i].draw(me.chartArea);\n      }\n      me.drawDatasets(easingValue);\n\n      // Rest of layers\n      for (; i < layers.length; ++i) {\n        layers[i].draw(me.chartArea);\n      }\n      me._drawTooltip(easingValue);\n      core_plugins.notify(me, 'afterDraw', [easingValue]);\n    },\n    /**\r\n     * @private\r\n     */\n    transition: function (easingValue) {\n      var me = this;\n      for (var i = 0, ilen = (me.data.datasets || []).length; i < ilen; ++i) {\n        if (me.isDatasetVisible(i)) {\n          me.getDatasetMeta(i).controller.transition(easingValue);\n        }\n      }\n      me.tooltip.transition(easingValue);\n    },\n    /**\r\n     * @private\r\n     */\n    _getSortedDatasetMetas: function (filterVisible) {\n      var me = this;\n      var datasets = me.data.datasets || [];\n      var result = [];\n      var i, ilen;\n      for (i = 0, ilen = datasets.length; i < ilen; ++i) {\n        if (!filterVisible || me.isDatasetVisible(i)) {\n          result.push(me.getDatasetMeta(i));\n        }\n      }\n      result.sort(compare2Level('order', 'index'));\n      return result;\n    },\n    /**\r\n     * @private\r\n     */\n    _getSortedVisibleDatasetMetas: function () {\n      return this._getSortedDatasetMetas(true);\n    },\n    /**\r\n     * Draws all datasets unless a plugin returns `false` to the `beforeDatasetsDraw`\r\n     * hook, in which case, plugins will not be called on `afterDatasetsDraw`.\r\n     * @private\r\n     */\n    drawDatasets: function (easingValue) {\n      var me = this;\n      var metasets, i;\n      if (core_plugins.notify(me, 'beforeDatasetsDraw', [easingValue]) === false) {\n        return;\n      }\n      metasets = me._getSortedVisibleDatasetMetas();\n      for (i = metasets.length - 1; i >= 0; --i) {\n        me.drawDataset(metasets[i], easingValue);\n      }\n      core_plugins.notify(me, 'afterDatasetsDraw', [easingValue]);\n    },\n    /**\r\n     * Draws dataset at index unless a plugin returns `false` to the `beforeDatasetDraw`\r\n     * hook, in which case, plugins will not be called on `afterDatasetDraw`.\r\n     * @private\r\n     */\n    drawDataset: function (meta, easingValue) {\n      var me = this;\n      var args = {\n        meta: meta,\n        index: meta.index,\n        easingValue: easingValue\n      };\n      if (core_plugins.notify(me, 'beforeDatasetDraw', [args]) === false) {\n        return;\n      }\n      meta.controller.draw(easingValue);\n      core_plugins.notify(me, 'afterDatasetDraw', [args]);\n    },\n    /**\r\n     * Draws tooltip unless a plugin returns `false` to the `beforeTooltipDraw`\r\n     * hook, in which case, plugins will not be called on `afterTooltipDraw`.\r\n     * @private\r\n     */\n    _drawTooltip: function (easingValue) {\n      var me = this;\n      var tooltip = me.tooltip;\n      var args = {\n        tooltip: tooltip,\n        easingValue: easingValue\n      };\n      if (core_plugins.notify(me, 'beforeTooltipDraw', [args]) === false) {\n        return;\n      }\n      tooltip.draw();\n      core_plugins.notify(me, 'afterTooltipDraw', [args]);\n    },\n    /**\r\n     * Get the single element that was clicked on\r\n     * @return An object containing the dataset index and element index of the matching element. Also contains the rectangle that was draw\r\n     */\n    getElementAtEvent: function (e) {\n      return core_interaction.modes.single(this, e);\n    },\n    getElementsAtEvent: function (e) {\n      return core_interaction.modes.label(this, e, {\n        intersect: true\n      });\n    },\n    getElementsAtXAxis: function (e) {\n      return core_interaction.modes['x-axis'](this, e, {\n        intersect: true\n      });\n    },\n    getElementsAtEventForMode: function (e, mode, options) {\n      var method = core_interaction.modes[mode];\n      if (typeof method === 'function') {\n        return method(this, e, options);\n      }\n      return [];\n    },\n    getDatasetAtEvent: function (e) {\n      return core_interaction.modes.dataset(this, e, {\n        intersect: true\n      });\n    },\n    getDatasetMeta: function (datasetIndex) {\n      var me = this;\n      var dataset = me.data.datasets[datasetIndex];\n      if (!dataset._meta) {\n        dataset._meta = {};\n      }\n      var meta = dataset._meta[me.id];\n      if (!meta) {\n        meta = dataset._meta[me.id] = {\n          type: null,\n          data: [],\n          dataset: null,\n          controller: null,\n          hidden: null,\n          // See isDatasetVisible() comment\n          xAxisID: null,\n          yAxisID: null,\n          order: dataset.order || 0,\n          index: datasetIndex\n        };\n      }\n      return meta;\n    },\n    getVisibleDatasetCount: function () {\n      var count = 0;\n      for (var i = 0, ilen = this.data.datasets.length; i < ilen; ++i) {\n        if (this.isDatasetVisible(i)) {\n          count++;\n        }\n      }\n      return count;\n    },\n    isDatasetVisible: function (datasetIndex) {\n      var meta = this.getDatasetMeta(datasetIndex);\n\n      // meta.hidden is a per chart dataset hidden flag override with 3 states: if true or false,\n      // the dataset.hidden value is ignored, else if null, the dataset hidden state is returned.\n      return typeof meta.hidden === 'boolean' ? !meta.hidden : !this.data.datasets[datasetIndex].hidden;\n    },\n    generateLegend: function () {\n      return this.options.legendCallback(this);\n    },\n    /**\r\n     * @private\r\n     */\n    destroyDatasetMeta: function (datasetIndex) {\n      var id = this.id;\n      var dataset = this.data.datasets[datasetIndex];\n      var meta = dataset._meta && dataset._meta[id];\n      if (meta) {\n        meta.controller.destroy();\n        delete dataset._meta[id];\n      }\n    },\n    destroy: function () {\n      var me = this;\n      var canvas = me.canvas;\n      var i, ilen;\n      me.stop();\n\n      // dataset controllers need to cleanup associated data\n      for (i = 0, ilen = me.data.datasets.length; i < ilen; ++i) {\n        me.destroyDatasetMeta(i);\n      }\n      if (canvas) {\n        me.unbindEvents();\n        helpers$1.canvas.clear(me);\n        platform.releaseContext(me.ctx);\n        me.canvas = null;\n        me.ctx = null;\n      }\n      core_plugins.notify(me, 'destroy');\n      delete Chart.instances[me.id];\n    },\n    toBase64Image: function () {\n      return this.canvas.toDataURL.apply(this.canvas, arguments);\n    },\n    initToolTip: function () {\n      var me = this;\n      me.tooltip = new core_tooltip({\n        _chart: me,\n        _chartInstance: me,\n        // deprecated, backward compatibility\n        _data: me.data,\n        _options: me.options.tooltips\n      }, me);\n    },\n    /**\r\n     * @private\r\n     */\n    bindEvents: function () {\n      var me = this;\n      var listeners = me._listeners = {};\n      var listener = function () {\n        me.eventHandler.apply(me, arguments);\n      };\n      helpers$1.each(me.options.events, function (type) {\n        platform.addEventListener(me, type, listener);\n        listeners[type] = listener;\n      });\n\n      // Elements used to detect size change should not be injected for non responsive charts.\n      // See https://github.com/chartjs/Chart.js/issues/2210\n      if (me.options.responsive) {\n        listener = function () {\n          me.resize();\n        };\n        platform.addEventListener(me, 'resize', listener);\n        listeners.resize = listener;\n      }\n    },\n    /**\r\n     * @private\r\n     */\n    unbindEvents: function () {\n      var me = this;\n      var listeners = me._listeners;\n      if (!listeners) {\n        return;\n      }\n      delete me._listeners;\n      helpers$1.each(listeners, function (listener, type) {\n        platform.removeEventListener(me, type, listener);\n      });\n    },\n    updateHoverStyle: function (elements, mode, enabled) {\n      var prefix = enabled ? 'set' : 'remove';\n      var element, i, ilen;\n      for (i = 0, ilen = elements.length; i < ilen; ++i) {\n        element = elements[i];\n        if (element) {\n          this.getDatasetMeta(element._datasetIndex).controller[prefix + 'HoverStyle'](element);\n        }\n      }\n      if (mode === 'dataset') {\n        this.getDatasetMeta(elements[0]._datasetIndex).controller['_' + prefix + 'DatasetHoverStyle']();\n      }\n    },\n    /**\r\n     * @private\r\n     */\n    eventHandler: function (e) {\n      var me = this;\n      var tooltip = me.tooltip;\n      if (core_plugins.notify(me, 'beforeEvent', [e]) === false) {\n        return;\n      }\n\n      // Buffer any update calls so that renders do not occur\n      me._bufferedRender = true;\n      me._bufferedRequest = null;\n      var changed = me.handleEvent(e);\n      // for smooth tooltip animations issue #4989\n      // the tooltip should be the source of change\n      // Animation check workaround:\n      // tooltip._start will be null when tooltip isn't animating\n      if (tooltip) {\n        changed = tooltip._start ? tooltip.handleEvent(e) : changed | tooltip.handleEvent(e);\n      }\n      core_plugins.notify(me, 'afterEvent', [e]);\n      var bufferedRequest = me._bufferedRequest;\n      if (bufferedRequest) {\n        // If we have an update that was triggered, we need to do a normal render\n        me.render(bufferedRequest);\n      } else if (changed && !me.animating) {\n        // If entering, leaving, or changing elements, animate the change via pivot\n        me.stop();\n\n        // We only need to render at this point. Updating will cause scales to be\n        // recomputed generating flicker & using more memory than necessary.\n        me.render({\n          duration: me.options.hover.animationDuration,\n          lazy: true\n        });\n      }\n      me._bufferedRender = false;\n      me._bufferedRequest = null;\n      return me;\n    },\n    /**\r\n     * Handle an event\r\n     * @private\r\n     * @param {IEvent} event the event to handle\r\n     * @return {boolean} true if the chart needs to re-render\r\n     */\n    handleEvent: function (e) {\n      var me = this;\n      var options = me.options || {};\n      var hoverOptions = options.hover;\n      var changed = false;\n      me.lastActive = me.lastActive || [];\n\n      // Find Active Elements for hover and tooltips\n      if (e.type === 'mouseout') {\n        me.active = [];\n      } else {\n        me.active = me.getElementsAtEventForMode(e, hoverOptions.mode, hoverOptions);\n      }\n\n      // Invoke onHover hook\n      // Need to call with native event here to not break backwards compatibility\n      helpers$1.callback(options.onHover || options.hover.onHover, [e.native, me.active], me);\n      if (e.type === 'mouseup' || e.type === 'click') {\n        if (options.onClick) {\n          // Use e.native here for backwards compatibility\n          options.onClick.call(me, e.native, me.active);\n        }\n      }\n\n      // Remove styling for last active (even if it may still be active)\n      if (me.lastActive.length) {\n        me.updateHoverStyle(me.lastActive, hoverOptions.mode, false);\n      }\n\n      // Built in hover styling\n      if (me.active.length && hoverOptions.mode) {\n        me.updateHoverStyle(me.active, hoverOptions.mode, true);\n      }\n      changed = !helpers$1.arrayEquals(me.active, me.lastActive);\n\n      // Remember Last Actives\n      me.lastActive = me.active;\n      return changed;\n    }\n  });\n\n  /**\r\n   * NOTE(SB) We actually don't use this container anymore but we need to keep it\r\n   * for backward compatibility. Though, it can still be useful for plugins that\r\n   * would need to work on multiple charts?!\r\n   */\n  Chart.instances = {};\n  var core_controller = Chart;\n\n  // DEPRECATIONS\n\n  /**\r\n   * Provided for backward compatibility, use Chart instead.\r\n   * @class Chart.Controller\r\n   * @deprecated since version 2.6\r\n   * @todo remove at version 3\r\n   * @private\r\n   */\n  Chart.Controller = Chart;\n\n  /**\r\n   * Provided for backward compatibility, not available anymore.\r\n   * @namespace Chart\r\n   * @deprecated since version 2.8\r\n   * @todo remove at version 3\r\n   * @private\r\n   */\n  Chart.types = {};\n\n  /**\r\n   * Provided for backward compatibility, not available anymore.\r\n   * @namespace Chart.helpers.configMerge\r\n   * @deprecated since version 2.8.0\r\n   * @todo remove at version 3\r\n   * @private\r\n   */\n  helpers$1.configMerge = mergeConfig;\n\n  /**\r\n   * Provided for backward compatibility, not available anymore.\r\n   * @namespace Chart.helpers.scaleMerge\r\n   * @deprecated since version 2.8.0\r\n   * @todo remove at version 3\r\n   * @private\r\n   */\n  helpers$1.scaleMerge = mergeScaleConfig;\n  var core_helpers = function () {\n    // -- Basic js utility methods\n\n    helpers$1.where = function (collection, filterCallback) {\n      if (helpers$1.isArray(collection) && Array.prototype.filter) {\n        return collection.filter(filterCallback);\n      }\n      var filtered = [];\n      helpers$1.each(collection, function (item) {\n        if (filterCallback(item)) {\n          filtered.push(item);\n        }\n      });\n      return filtered;\n    };\n    helpers$1.findIndex = Array.prototype.findIndex ? function (array, callback, scope) {\n      return array.findIndex(callback, scope);\n    } : function (array, callback, scope) {\n      scope = scope === undefined ? array : scope;\n      for (var i = 0, ilen = array.length; i < ilen; ++i) {\n        if (callback.call(scope, array[i], i, array)) {\n          return i;\n        }\n      }\n      return -1;\n    };\n    helpers$1.findNextWhere = function (arrayToSearch, filterCallback, startIndex) {\n      // Default to start of the array\n      if (helpers$1.isNullOrUndef(startIndex)) {\n        startIndex = -1;\n      }\n      for (var i = startIndex + 1; i < arrayToSearch.length; i++) {\n        var currentItem = arrayToSearch[i];\n        if (filterCallback(currentItem)) {\n          return currentItem;\n        }\n      }\n    };\n    helpers$1.findPreviousWhere = function (arrayToSearch, filterCallback, startIndex) {\n      // Default to end of the array\n      if (helpers$1.isNullOrUndef(startIndex)) {\n        startIndex = arrayToSearch.length;\n      }\n      for (var i = startIndex - 1; i >= 0; i--) {\n        var currentItem = arrayToSearch[i];\n        if (filterCallback(currentItem)) {\n          return currentItem;\n        }\n      }\n    };\n\n    // -- Math methods\n    helpers$1.isNumber = function (n) {\n      return !isNaN(parseFloat(n)) && isFinite(n);\n    };\n    helpers$1.almostEquals = function (x, y, epsilon) {\n      return Math.abs(x - y) < epsilon;\n    };\n    helpers$1.almostWhole = function (x, epsilon) {\n      var rounded = Math.round(x);\n      return rounded - epsilon <= x && rounded + epsilon >= x;\n    };\n    helpers$1.max = function (array) {\n      return array.reduce(function (max, value) {\n        if (!isNaN(value)) {\n          return Math.max(max, value);\n        }\n        return max;\n      }, Number.NEGATIVE_INFINITY);\n    };\n    helpers$1.min = function (array) {\n      return array.reduce(function (min, value) {\n        if (!isNaN(value)) {\n          return Math.min(min, value);\n        }\n        return min;\n      }, Number.POSITIVE_INFINITY);\n    };\n    helpers$1.sign = Math.sign ? function (x) {\n      return Math.sign(x);\n    } : function (x) {\n      x = +x; // convert to a number\n      if (x === 0 || isNaN(x)) {\n        return x;\n      }\n      return x > 0 ? 1 : -1;\n    };\n    helpers$1.toRadians = function (degrees) {\n      return degrees * (Math.PI / 180);\n    };\n    helpers$1.toDegrees = function (radians) {\n      return radians * (180 / Math.PI);\n    };\n\n    /**\r\n     * Returns the number of decimal places\r\n     * i.e. the number of digits after the decimal point, of the value of this Number.\r\n     * @param {number} x - A number.\r\n     * @returns {number} The number of decimal places.\r\n     * @private\r\n     */\n    helpers$1._decimalPlaces = function (x) {\n      if (!helpers$1.isFinite(x)) {\n        return;\n      }\n      var e = 1;\n      var p = 0;\n      while (Math.round(x * e) / e !== x) {\n        e *= 10;\n        p++;\n      }\n      return p;\n    };\n\n    // Gets the angle from vertical upright to the point about a centre.\n    helpers$1.getAngleFromPoint = function (centrePoint, anglePoint) {\n      var distanceFromXCenter = anglePoint.x - centrePoint.x;\n      var distanceFromYCenter = anglePoint.y - centrePoint.y;\n      var radialDistanceFromCenter = Math.sqrt(distanceFromXCenter * distanceFromXCenter + distanceFromYCenter * distanceFromYCenter);\n      var angle = Math.atan2(distanceFromYCenter, distanceFromXCenter);\n      if (angle < -0.5 * Math.PI) {\n        angle += 2.0 * Math.PI; // make sure the returned angle is in the range of (-PI/2, 3PI/2]\n      }\n      return {\n        angle: angle,\n        distance: radialDistanceFromCenter\n      };\n    };\n    helpers$1.distanceBetweenPoints = function (pt1, pt2) {\n      return Math.sqrt(Math.pow(pt2.x - pt1.x, 2) + Math.pow(pt2.y - pt1.y, 2));\n    };\n\n    /**\r\n     * Provided for backward compatibility, not available anymore\r\n     * @function Chart.helpers.aliasPixel\r\n     * @deprecated since version 2.8.0\r\n     * @todo remove at version 3\r\n     */\n    helpers$1.aliasPixel = function (pixelWidth) {\n      return pixelWidth % 2 === 0 ? 0 : 0.5;\n    };\n\n    /**\r\n     * Returns the aligned pixel value to avoid anti-aliasing blur\r\n     * @param {Chart} chart - The chart instance.\r\n     * @param {number} pixel - A pixel value.\r\n     * @param {number} width - The width of the element.\r\n     * @returns {number} The aligned pixel value.\r\n     * @private\r\n     */\n    helpers$1._alignPixel = function (chart, pixel, width) {\n      var devicePixelRatio = chart.currentDevicePixelRatio;\n      var halfWidth = width / 2;\n      return Math.round((pixel - halfWidth) * devicePixelRatio) / devicePixelRatio + halfWidth;\n    };\n    helpers$1.splineCurve = function (firstPoint, middlePoint, afterPoint, t) {\n      // Props to Rob Spencer at scaled innovation for his post on splining between points\n      // http://scaledinnovation.com/analytics/splines/aboutSplines.html\n\n      // This function must also respect \"skipped\" points\n\n      var previous = firstPoint.skip ? middlePoint : firstPoint;\n      var current = middlePoint;\n      var next = afterPoint.skip ? middlePoint : afterPoint;\n      var d01 = Math.sqrt(Math.pow(current.x - previous.x, 2) + Math.pow(current.y - previous.y, 2));\n      var d12 = Math.sqrt(Math.pow(next.x - current.x, 2) + Math.pow(next.y - current.y, 2));\n      var s01 = d01 / (d01 + d12);\n      var s12 = d12 / (d01 + d12);\n\n      // If all points are the same, s01 & s02 will be inf\n      s01 = isNaN(s01) ? 0 : s01;\n      s12 = isNaN(s12) ? 0 : s12;\n      var fa = t * s01; // scaling factor for triangle Ta\n      var fb = t * s12;\n      return {\n        previous: {\n          x: current.x - fa * (next.x - previous.x),\n          y: current.y - fa * (next.y - previous.y)\n        },\n        next: {\n          x: current.x + fb * (next.x - previous.x),\n          y: current.y + fb * (next.y - previous.y)\n        }\n      };\n    };\n    helpers$1.EPSILON = Number.EPSILON || 1e-14;\n    helpers$1.splineCurveMonotone = function (points) {\n      // This function calculates Bézier control points in a similar way than |splineCurve|,\n      // but preserves monotonicity of the provided data and ensures no local extremums are added\n      // between the dataset discrete points due to the interpolation.\n      // See : https://en.wikipedia.org/wiki/Monotone_cubic_interpolation\n\n      var pointsWithTangents = (points || []).map(function (point) {\n        return {\n          model: point._model,\n          deltaK: 0,\n          mK: 0\n        };\n      });\n\n      // Calculate slopes (deltaK) and initialize tangents (mK)\n      var pointsLen = pointsWithTangents.length;\n      var i, pointBefore, pointCurrent, pointAfter;\n      for (i = 0; i < pointsLen; ++i) {\n        pointCurrent = pointsWithTangents[i];\n        if (pointCurrent.model.skip) {\n          continue;\n        }\n        pointBefore = i > 0 ? pointsWithTangents[i - 1] : null;\n        pointAfter = i < pointsLen - 1 ? pointsWithTangents[i + 1] : null;\n        if (pointAfter && !pointAfter.model.skip) {\n          var slopeDeltaX = pointAfter.model.x - pointCurrent.model.x;\n\n          // In the case of two points that appear at the same x pixel, slopeDeltaX is 0\n          pointCurrent.deltaK = slopeDeltaX !== 0 ? (pointAfter.model.y - pointCurrent.model.y) / slopeDeltaX : 0;\n        }\n        if (!pointBefore || pointBefore.model.skip) {\n          pointCurrent.mK = pointCurrent.deltaK;\n        } else if (!pointAfter || pointAfter.model.skip) {\n          pointCurrent.mK = pointBefore.deltaK;\n        } else if (this.sign(pointBefore.deltaK) !== this.sign(pointCurrent.deltaK)) {\n          pointCurrent.mK = 0;\n        } else {\n          pointCurrent.mK = (pointBefore.deltaK + pointCurrent.deltaK) / 2;\n        }\n      }\n\n      // Adjust tangents to ensure monotonic properties\n      var alphaK, betaK, tauK, squaredMagnitude;\n      for (i = 0; i < pointsLen - 1; ++i) {\n        pointCurrent = pointsWithTangents[i];\n        pointAfter = pointsWithTangents[i + 1];\n        if (pointCurrent.model.skip || pointAfter.model.skip) {\n          continue;\n        }\n        if (helpers$1.almostEquals(pointCurrent.deltaK, 0, this.EPSILON)) {\n          pointCurrent.mK = pointAfter.mK = 0;\n          continue;\n        }\n        alphaK = pointCurrent.mK / pointCurrent.deltaK;\n        betaK = pointAfter.mK / pointCurrent.deltaK;\n        squaredMagnitude = Math.pow(alphaK, 2) + Math.pow(betaK, 2);\n        if (squaredMagnitude <= 9) {\n          continue;\n        }\n        tauK = 3 / Math.sqrt(squaredMagnitude);\n        pointCurrent.mK = alphaK * tauK * pointCurrent.deltaK;\n        pointAfter.mK = betaK * tauK * pointCurrent.deltaK;\n      }\n\n      // Compute control points\n      var deltaX;\n      for (i = 0; i < pointsLen; ++i) {\n        pointCurrent = pointsWithTangents[i];\n        if (pointCurrent.model.skip) {\n          continue;\n        }\n        pointBefore = i > 0 ? pointsWithTangents[i - 1] : null;\n        pointAfter = i < pointsLen - 1 ? pointsWithTangents[i + 1] : null;\n        if (pointBefore && !pointBefore.model.skip) {\n          deltaX = (pointCurrent.model.x - pointBefore.model.x) / 3;\n          pointCurrent.model.controlPointPreviousX = pointCurrent.model.x - deltaX;\n          pointCurrent.model.controlPointPreviousY = pointCurrent.model.y - deltaX * pointCurrent.mK;\n        }\n        if (pointAfter && !pointAfter.model.skip) {\n          deltaX = (pointAfter.model.x - pointCurrent.model.x) / 3;\n          pointCurrent.model.controlPointNextX = pointCurrent.model.x + deltaX;\n          pointCurrent.model.controlPointNextY = pointCurrent.model.y + deltaX * pointCurrent.mK;\n        }\n      }\n    };\n    helpers$1.nextItem = function (collection, index, loop) {\n      if (loop) {\n        return index >= collection.length - 1 ? collection[0] : collection[index + 1];\n      }\n      return index >= collection.length - 1 ? collection[collection.length - 1] : collection[index + 1];\n    };\n    helpers$1.previousItem = function (collection, index, loop) {\n      if (loop) {\n        return index <= 0 ? collection[collection.length - 1] : collection[index - 1];\n      }\n      return index <= 0 ? collection[0] : collection[index - 1];\n    };\n    // Implementation of the nice number algorithm used in determining where axis labels will go\n    helpers$1.niceNum = function (range, round) {\n      var exponent = Math.floor(helpers$1.log10(range));\n      var fraction = range / Math.pow(10, exponent);\n      var niceFraction;\n      if (round) {\n        if (fraction < 1.5) {\n          niceFraction = 1;\n        } else if (fraction < 3) {\n          niceFraction = 2;\n        } else if (fraction < 7) {\n          niceFraction = 5;\n        } else {\n          niceFraction = 10;\n        }\n      } else if (fraction <= 1.0) {\n        niceFraction = 1;\n      } else if (fraction <= 2) {\n        niceFraction = 2;\n      } else if (fraction <= 5) {\n        niceFraction = 5;\n      } else {\n        niceFraction = 10;\n      }\n      return niceFraction * Math.pow(10, exponent);\n    };\n    // Request animation polyfill - https://www.paulirish.com/2011/requestanimationframe-for-smart-animating/\n    helpers$1.requestAnimFrame = function () {\n      if (typeof window === 'undefined') {\n        return function (callback) {\n          callback();\n        };\n      }\n      return window.requestAnimationFrame || window.webkitRequestAnimationFrame || window.mozRequestAnimationFrame || window.oRequestAnimationFrame || window.msRequestAnimationFrame || function (callback) {\n        return window.setTimeout(callback, 1000 / 60);\n      };\n    }();\n    // -- DOM methods\n    helpers$1.getRelativePosition = function (evt, chart) {\n      var mouseX, mouseY;\n      var e = evt.originalEvent || evt;\n      var canvas = evt.target || evt.srcElement;\n      var boundingRect = canvas.getBoundingClientRect();\n      var touches = e.touches;\n      if (touches && touches.length > 0) {\n        mouseX = touches[0].clientX;\n        mouseY = touches[0].clientY;\n      } else {\n        mouseX = e.clientX;\n        mouseY = e.clientY;\n      }\n\n      // Scale mouse coordinates into canvas coordinates\n      // by following the pattern laid out by 'jerryj' in the comments of\n      // https://www.html5canvastutorials.com/advanced/html5-canvas-mouse-coordinates/\n      var paddingLeft = parseFloat(helpers$1.getStyle(canvas, 'padding-left'));\n      var paddingTop = parseFloat(helpers$1.getStyle(canvas, 'padding-top'));\n      var paddingRight = parseFloat(helpers$1.getStyle(canvas, 'padding-right'));\n      var paddingBottom = parseFloat(helpers$1.getStyle(canvas, 'padding-bottom'));\n      var width = boundingRect.right - boundingRect.left - paddingLeft - paddingRight;\n      var height = boundingRect.bottom - boundingRect.top - paddingTop - paddingBottom;\n\n      // We divide by the current device pixel ratio, because the canvas is scaled up by that amount in each direction. However\n      // the backend model is in unscaled coordinates. Since we are going to deal with our model coordinates, we go back here\n      mouseX = Math.round((mouseX - boundingRect.left - paddingLeft) / width * canvas.width / chart.currentDevicePixelRatio);\n      mouseY = Math.round((mouseY - boundingRect.top - paddingTop) / height * canvas.height / chart.currentDevicePixelRatio);\n      return {\n        x: mouseX,\n        y: mouseY\n      };\n    };\n\n    // Private helper function to convert max-width/max-height values that may be percentages into a number\n    function parseMaxStyle(styleValue, node, parentProperty) {\n      var valueInPixels;\n      if (typeof styleValue === 'string') {\n        valueInPixels = parseInt(styleValue, 10);\n        if (styleValue.indexOf('%') !== -1) {\n          // percentage * size in dimension\n          valueInPixels = valueInPixels / 100 * node.parentNode[parentProperty];\n        }\n      } else {\n        valueInPixels = styleValue;\n      }\n      return valueInPixels;\n    }\n\n    /**\r\n     * Returns if the given value contains an effective constraint.\r\n     * @private\r\n     */\n    function isConstrainedValue(value) {\n      return value !== undefined && value !== null && value !== 'none';\n    }\n\n    /**\r\n     * Returns the max width or height of the given DOM node in a cross-browser compatible fashion\r\n     * @param {HTMLElement} domNode - the node to check the constraint on\r\n     * @param {string} maxStyle - the style that defines the maximum for the direction we are using ('max-width' / 'max-height')\r\n     * @param {string} percentageProperty - property of parent to use when calculating width as a percentage\r\n     * @see {@link https://www.nathanaeljones.com/blog/2013/reading-max-width-cross-browser}\r\n     */\n    function getConstraintDimension(domNode, maxStyle, percentageProperty) {\n      var view = document.defaultView;\n      var parentNode = helpers$1._getParentNode(domNode);\n      var constrainedNode = view.getComputedStyle(domNode)[maxStyle];\n      var constrainedContainer = view.getComputedStyle(parentNode)[maxStyle];\n      var hasCNode = isConstrainedValue(constrainedNode);\n      var hasCContainer = isConstrainedValue(constrainedContainer);\n      var infinity = Number.POSITIVE_INFINITY;\n      if (hasCNode || hasCContainer) {\n        return Math.min(hasCNode ? parseMaxStyle(constrainedNode, domNode, percentageProperty) : infinity, hasCContainer ? parseMaxStyle(constrainedContainer, parentNode, percentageProperty) : infinity);\n      }\n      return 'none';\n    }\n    // returns Number or undefined if no constraint\n    helpers$1.getConstraintWidth = function (domNode) {\n      return getConstraintDimension(domNode, 'max-width', 'clientWidth');\n    };\n    // returns Number or undefined if no constraint\n    helpers$1.getConstraintHeight = function (domNode) {\n      return getConstraintDimension(domNode, 'max-height', 'clientHeight');\n    };\n    /**\r\n     * @private\r\n    \t */\n    helpers$1._calculatePadding = function (container, padding, parentDimension) {\n      padding = helpers$1.getStyle(container, padding);\n      return padding.indexOf('%') > -1 ? parentDimension * parseInt(padding, 10) / 100 : parseInt(padding, 10);\n    };\n    /**\r\n     * @private\r\n     */\n    helpers$1._getParentNode = function (domNode) {\n      var parent = domNode.parentNode;\n      if (parent && parent.toString() === '[object ShadowRoot]') {\n        parent = parent.host;\n      }\n      return parent;\n    };\n    helpers$1.getMaximumWidth = function (domNode) {\n      var container = helpers$1._getParentNode(domNode);\n      if (!container) {\n        return domNode.clientWidth;\n      }\n      var clientWidth = container.clientWidth;\n      var paddingLeft = helpers$1._calculatePadding(container, 'padding-left', clientWidth);\n      var paddingRight = helpers$1._calculatePadding(container, 'padding-right', clientWidth);\n      var w = clientWidth - paddingLeft - paddingRight;\n      var cw = helpers$1.getConstraintWidth(domNode);\n      return isNaN(cw) ? w : Math.min(w, cw);\n    };\n    helpers$1.getMaximumHeight = function (domNode) {\n      var container = helpers$1._getParentNode(domNode);\n      if (!container) {\n        return domNode.clientHeight;\n      }\n      var clientHeight = container.clientHeight;\n      var paddingTop = helpers$1._calculatePadding(container, 'padding-top', clientHeight);\n      var paddingBottom = helpers$1._calculatePadding(container, 'padding-bottom', clientHeight);\n      var h = clientHeight - paddingTop - paddingBottom;\n      var ch = helpers$1.getConstraintHeight(domNode);\n      return isNaN(ch) ? h : Math.min(h, ch);\n    };\n    helpers$1.getStyle = function (el, property) {\n      return el.currentStyle ? el.currentStyle[property] : document.defaultView.getComputedStyle(el, null).getPropertyValue(property);\n    };\n    helpers$1.retinaScale = function (chart, forceRatio) {\n      var pixelRatio = chart.currentDevicePixelRatio = forceRatio || typeof window !== 'undefined' && window.devicePixelRatio || 1;\n      if (pixelRatio === 1) {\n        return;\n      }\n      var canvas = chart.canvas;\n      var height = chart.height;\n      var width = chart.width;\n      canvas.height = height * pixelRatio;\n      canvas.width = width * pixelRatio;\n      chart.ctx.scale(pixelRatio, pixelRatio);\n\n      // If no style has been set on the canvas, the render size is used as display size,\n      // making the chart visually bigger, so let's enforce it to the \"correct\" values.\n      // See https://github.com/chartjs/Chart.js/issues/3575\n      if (!canvas.style.height && !canvas.style.width) {\n        canvas.style.height = height + 'px';\n        canvas.style.width = width + 'px';\n      }\n    };\n    // -- Canvas methods\n    helpers$1.fontString = function (pixelSize, fontStyle, fontFamily) {\n      return fontStyle + ' ' + pixelSize + 'px ' + fontFamily;\n    };\n    helpers$1.longestText = function (ctx, font, arrayOfThings, cache) {\n      cache = cache || {};\n      var data = cache.data = cache.data || {};\n      var gc = cache.garbageCollect = cache.garbageCollect || [];\n      if (cache.font !== font) {\n        data = cache.data = {};\n        gc = cache.garbageCollect = [];\n        cache.font = font;\n      }\n      ctx.font = font;\n      var longest = 0;\n      var ilen = arrayOfThings.length;\n      var i, j, jlen, thing, nestedThing;\n      for (i = 0; i < ilen; i++) {\n        thing = arrayOfThings[i];\n\n        // Undefined strings and arrays should not be measured\n        if (thing !== undefined && thing !== null && helpers$1.isArray(thing) !== true) {\n          longest = helpers$1.measureText(ctx, data, gc, longest, thing);\n        } else if (helpers$1.isArray(thing)) {\n          // if it is an array lets measure each element\n          // to do maybe simplify this function a bit so we can do this more recursively?\n          for (j = 0, jlen = thing.length; j < jlen; j++) {\n            nestedThing = thing[j];\n            // Undefined strings and arrays should not be measured\n            if (nestedThing !== undefined && nestedThing !== null && !helpers$1.isArray(nestedThing)) {\n              longest = helpers$1.measureText(ctx, data, gc, longest, nestedThing);\n            }\n          }\n        }\n      }\n      var gcLen = gc.length / 2;\n      if (gcLen > arrayOfThings.length) {\n        for (i = 0; i < gcLen; i++) {\n          delete data[gc[i]];\n        }\n        gc.splice(0, gcLen);\n      }\n      return longest;\n    };\n    helpers$1.measureText = function (ctx, data, gc, longest, string) {\n      var textWidth = data[string];\n      if (!textWidth) {\n        textWidth = data[string] = ctx.measureText(string).width;\n        gc.push(string);\n      }\n      if (textWidth > longest) {\n        longest = textWidth;\n      }\n      return longest;\n    };\n\n    /**\r\n     * @deprecated\r\n     */\n    helpers$1.numberOfLabelLines = function (arrayOfThings) {\n      var numberOfLines = 1;\n      helpers$1.each(arrayOfThings, function (thing) {\n        if (helpers$1.isArray(thing)) {\n          if (thing.length > numberOfLines) {\n            numberOfLines = thing.length;\n          }\n        }\n      });\n      return numberOfLines;\n    };\n    helpers$1.color = !chartjsColor ? function (value) {\n      console.error('Color.js not found!');\n      return value;\n    } : function (value) {\n      /* global CanvasGradient */\n      if (value instanceof CanvasGradient) {\n        value = core_defaults.global.defaultColor;\n      }\n      return chartjsColor(value);\n    };\n    helpers$1.getHoverColor = function (colorValue) {\n      /* global CanvasPattern */\n      return colorValue instanceof CanvasPattern || colorValue instanceof CanvasGradient ? colorValue : helpers$1.color(colorValue).saturate(0.5).darken(0.1).rgbString();\n    };\n  };\n  function abstract() {\n    throw new Error('This method is not implemented: either no adapter can ' + 'be found or an incomplete integration was provided.');\n  }\n\n  /**\r\n   * Date adapter (current used by the time scale)\r\n   * @namespace Chart._adapters._date\r\n   * @memberof Chart._adapters\r\n   * @private\r\n   */\n\n  /**\r\n   * Currently supported unit string values.\r\n   * @typedef {('millisecond'|'second'|'minute'|'hour'|'day'|'week'|'month'|'quarter'|'year')}\r\n   * @memberof Chart._adapters._date\r\n   * @name Unit\r\n   */\n\n  /**\r\n   * @class\r\n   */\n  function DateAdapter(options) {\n    this.options = options || {};\n  }\n  helpers$1.extend(DateAdapter.prototype, /** @lends DateAdapter */{\n    /**\r\n     * Returns a map of time formats for the supported formatting units defined\r\n     * in Unit as well as 'datetime' representing a detailed date/time string.\r\n     * @returns {{string: string}}\r\n     */\n    formats: abstract,\n    /**\r\n     * Parses the given `value` and return the associated timestamp.\r\n     * @param {any} value - the value to parse (usually comes from the data)\r\n     * @param {string} [format] - the expected data format\r\n     * @returns {(number|null)}\r\n     * @function\r\n     */\n    parse: abstract,\n    /**\r\n     * Returns the formatted date in the specified `format` for a given `timestamp`.\r\n     * @param {number} timestamp - the timestamp to format\r\n     * @param {string} format - the date/time token\r\n     * @return {string}\r\n     * @function\r\n     */\n    format: abstract,\n    /**\r\n     * Adds the specified `amount` of `unit` to the given `timestamp`.\r\n     * @param {number} timestamp - the input timestamp\r\n     * @param {number} amount - the amount to add\r\n     * @param {Unit} unit - the unit as string\r\n     * @return {number}\r\n     * @function\r\n     */\n    add: abstract,\n    /**\r\n     * Returns the number of `unit` between the given timestamps.\r\n     * @param {number} max - the input timestamp (reference)\r\n     * @param {number} min - the timestamp to substract\r\n     * @param {Unit} unit - the unit as string\r\n     * @return {number}\r\n     * @function\r\n     */\n    diff: abstract,\n    /**\r\n     * Returns start of `unit` for the given `timestamp`.\r\n     * @param {number} timestamp - the input timestamp\r\n     * @param {Unit} unit - the unit as string\r\n     * @param {number} [weekday] - the ISO day of the week with 1 being Monday\r\n     * and 7 being Sunday (only needed if param *unit* is `isoWeek`).\r\n     * @function\r\n     */\n    startOf: abstract,\n    /**\r\n     * Returns end of `unit` for the given `timestamp`.\r\n     * @param {number} timestamp - the input timestamp\r\n     * @param {Unit} unit - the unit as string\r\n     * @function\r\n     */\n    endOf: abstract,\n    // DEPRECATIONS\n\n    /**\r\n     * Provided for backward compatibility for scale.getValueForPixel(),\r\n     * this method should be overridden only by the moment adapter.\r\n     * @deprecated since version 2.8.0\r\n     * @todo remove at version 3\r\n     * @private\r\n     */\n    _create: function (value) {\n      return value;\n    }\n  });\n  DateAdapter.override = function (members) {\n    helpers$1.extend(DateAdapter.prototype, members);\n  };\n  var _date = DateAdapter;\n  var core_adapters = {\n    _date: _date\n  };\n\n  /**\r\n   * Namespace to hold static tick generation functions\r\n   * @namespace Chart.Ticks\r\n   */\n  var core_ticks = {\n    /**\r\n     * Namespace to hold formatters for different types of ticks\r\n     * @namespace Chart.Ticks.formatters\r\n     */\n    formatters: {\n      /**\r\n       * Formatter for value labels\r\n       * @method Chart.Ticks.formatters.values\r\n       * @param value the value to display\r\n       * @return {string|string[]} the label to display\r\n       */\n      values: function (value) {\n        return helpers$1.isArray(value) ? value : '' + value;\n      },\n      /**\r\n       * Formatter for linear numeric ticks\r\n       * @method Chart.Ticks.formatters.linear\r\n       * @param tickValue {number} the value to be formatted\r\n       * @param index {number} the position of the tickValue parameter in the ticks array\r\n       * @param ticks {number[]} the list of ticks being converted\r\n       * @return {string} string representation of the tickValue parameter\r\n       */\n      linear: function (tickValue, index, ticks) {\n        // If we have lots of ticks, don't use the ones\n        var delta = ticks.length > 3 ? ticks[2] - ticks[1] : ticks[1] - ticks[0];\n\n        // If we have a number like 2.5 as the delta, figure out how many decimal places we need\n        if (Math.abs(delta) > 1) {\n          if (tickValue !== Math.floor(tickValue)) {\n            // not an integer\n            delta = tickValue - Math.floor(tickValue);\n          }\n        }\n        var logDelta = helpers$1.log10(Math.abs(delta));\n        var tickString = '';\n        if (tickValue !== 0) {\n          var maxTick = Math.max(Math.abs(ticks[0]), Math.abs(ticks[ticks.length - 1]));\n          if (maxTick < 1e-4) {\n            // all ticks are small numbers; use scientific notation\n            var logTick = helpers$1.log10(Math.abs(tickValue));\n            var numExponential = Math.floor(logTick) - Math.floor(logDelta);\n            numExponential = Math.max(Math.min(numExponential, 20), 0);\n            tickString = tickValue.toExponential(numExponential);\n          } else {\n            var numDecimal = -1 * Math.floor(logDelta);\n            numDecimal = Math.max(Math.min(numDecimal, 20), 0); // toFixed has a max of 20 decimal places\n            tickString = tickValue.toFixed(numDecimal);\n          }\n        } else {\n          tickString = '0'; // never show decimal places for 0\n        }\n        return tickString;\n      },\n      logarithmic: function (tickValue, index, ticks) {\n        var remain = tickValue / Math.pow(10, Math.floor(helpers$1.log10(tickValue)));\n        if (tickValue === 0) {\n          return '0';\n        } else if (remain === 1 || remain === 2 || remain === 5 || index === 0 || index === ticks.length - 1) {\n          return tickValue.toExponential();\n        }\n        return '';\n      }\n    }\n  };\n  var isArray = helpers$1.isArray;\n  var isNullOrUndef = helpers$1.isNullOrUndef;\n  var valueOrDefault$a = helpers$1.valueOrDefault;\n  var valueAtIndexOrDefault = helpers$1.valueAtIndexOrDefault;\n  core_defaults._set('scale', {\n    display: true,\n    position: 'left',\n    offset: false,\n    // grid line settings\n    gridLines: {\n      display: true,\n      color: 'rgba(0,0,0,0.1)',\n      lineWidth: 1,\n      drawBorder: true,\n      drawOnChartArea: true,\n      drawTicks: true,\n      tickMarkLength: 10,\n      zeroLineWidth: 1,\n      zeroLineColor: 'rgba(0,0,0,0.25)',\n      zeroLineBorderDash: [],\n      zeroLineBorderDashOffset: 0.0,\n      offsetGridLines: false,\n      borderDash: [],\n      borderDashOffset: 0.0\n    },\n    // scale label\n    scaleLabel: {\n      // display property\n      display: false,\n      // actual label\n      labelString: '',\n      // top/bottom padding\n      padding: {\n        top: 4,\n        bottom: 4\n      }\n    },\n    // label settings\n    ticks: {\n      beginAtZero: false,\n      minRotation: 0,\n      maxRotation: 50,\n      mirror: false,\n      padding: 0,\n      reverse: false,\n      display: true,\n      autoSkip: true,\n      autoSkipPadding: 0,\n      labelOffset: 0,\n      // We pass through arrays to be rendered as multiline labels, we convert Others to strings here.\n      callback: core_ticks.formatters.values,\n      minor: {},\n      major: {}\n    }\n  });\n\n  /** Returns a new array containing numItems from arr */\n  function sample(arr, numItems) {\n    var result = [];\n    var increment = arr.length / numItems;\n    var i = 0;\n    var len = arr.length;\n    for (; i < len; i += increment) {\n      result.push(arr[Math.floor(i)]);\n    }\n    return result;\n  }\n  function getPixelForGridLine(scale, index, offsetGridLines) {\n    var length = scale.getTicks().length;\n    var validIndex = Math.min(index, length - 1);\n    var lineValue = scale.getPixelForTick(validIndex);\n    var start = scale._startPixel;\n    var end = scale._endPixel;\n    var epsilon = 1e-6; // 1e-6 is margin in pixels for accumulated error.\n    var offset;\n    if (offsetGridLines) {\n      if (length === 1) {\n        offset = Math.max(lineValue - start, end - lineValue);\n      } else if (index === 0) {\n        offset = (scale.getPixelForTick(1) - lineValue) / 2;\n      } else {\n        offset = (lineValue - scale.getPixelForTick(validIndex - 1)) / 2;\n      }\n      lineValue += validIndex < index ? offset : -offset;\n\n      // Return undefined if the pixel is out of the range\n      if (lineValue < start - epsilon || lineValue > end + epsilon) {\n        return;\n      }\n    }\n    return lineValue;\n  }\n  function garbageCollect(caches, length) {\n    helpers$1.each(caches, function (cache) {\n      var gc = cache.gc;\n      var gcLen = gc.length / 2;\n      var i;\n      if (gcLen > length) {\n        for (i = 0; i < gcLen; ++i) {\n          delete cache.data[gc[i]];\n        }\n        gc.splice(0, gcLen);\n      }\n    });\n  }\n\n  /**\r\n   * Returns {width, height, offset} objects for the first, last, widest, highest tick\r\n   * labels where offset indicates the anchor point offset from the top in pixels.\r\n   */\n  function computeLabelSizes(ctx, tickFonts, ticks, caches) {\n    var length = ticks.length;\n    var widths = [];\n    var heights = [];\n    var offsets = [];\n    var widestLabelSize = 0;\n    var highestLabelSize = 0;\n    var i, j, jlen, label, tickFont, fontString, cache, lineHeight, width, height, nestedLabel, widest, highest;\n    for (i = 0; i < length; ++i) {\n      label = ticks[i].label;\n      tickFont = ticks[i].major ? tickFonts.major : tickFonts.minor;\n      ctx.font = fontString = tickFont.string;\n      cache = caches[fontString] = caches[fontString] || {\n        data: {},\n        gc: []\n      };\n      lineHeight = tickFont.lineHeight;\n      width = height = 0;\n      // Undefined labels and arrays should not be measured\n      if (!isNullOrUndef(label) && !isArray(label)) {\n        width = helpers$1.measureText(ctx, cache.data, cache.gc, width, label);\n        height = lineHeight;\n      } else if (isArray(label)) {\n        // if it is an array let's measure each element\n        for (j = 0, jlen = label.length; j < jlen; ++j) {\n          nestedLabel = label[j];\n          // Undefined labels and arrays should not be measured\n          if (!isNullOrUndef(nestedLabel) && !isArray(nestedLabel)) {\n            width = helpers$1.measureText(ctx, cache.data, cache.gc, width, nestedLabel);\n            height += lineHeight;\n          }\n        }\n      }\n      widths.push(width);\n      heights.push(height);\n      offsets.push(lineHeight / 2);\n      widestLabelSize = Math.max(width, widestLabelSize);\n      highestLabelSize = Math.max(height, highestLabelSize);\n    }\n    garbageCollect(caches, length);\n    widest = widths.indexOf(widestLabelSize);\n    highest = heights.indexOf(highestLabelSize);\n    function valueAt(idx) {\n      return {\n        width: widths[idx] || 0,\n        height: heights[idx] || 0,\n        offset: offsets[idx] || 0\n      };\n    }\n    return {\n      first: valueAt(0),\n      last: valueAt(length - 1),\n      widest: valueAt(widest),\n      highest: valueAt(highest)\n    };\n  }\n  function getTickMarkLength(options) {\n    return options.drawTicks ? options.tickMarkLength : 0;\n  }\n  function getScaleLabelHeight(options) {\n    var font, padding;\n    if (!options.display) {\n      return 0;\n    }\n    font = helpers$1.options._parseFont(options);\n    padding = helpers$1.options.toPadding(options.padding);\n    return font.lineHeight + padding.height;\n  }\n  function parseFontOptions(options, nestedOpts) {\n    return helpers$1.extend(helpers$1.options._parseFont({\n      fontFamily: valueOrDefault$a(nestedOpts.fontFamily, options.fontFamily),\n      fontSize: valueOrDefault$a(nestedOpts.fontSize, options.fontSize),\n      fontStyle: valueOrDefault$a(nestedOpts.fontStyle, options.fontStyle),\n      lineHeight: valueOrDefault$a(nestedOpts.lineHeight, options.lineHeight)\n    }), {\n      color: helpers$1.options.resolve([nestedOpts.fontColor, options.fontColor, core_defaults.global.defaultFontColor])\n    });\n  }\n  function parseTickFontOptions(options) {\n    var minor = parseFontOptions(options, options.minor);\n    var major = options.major.enabled ? parseFontOptions(options, options.major) : minor;\n    return {\n      minor: minor,\n      major: major\n    };\n  }\n  function nonSkipped(ticksToFilter) {\n    var filtered = [];\n    var item, index, len;\n    for (index = 0, len = ticksToFilter.length; index < len; ++index) {\n      item = ticksToFilter[index];\n      if (typeof item._index !== 'undefined') {\n        filtered.push(item);\n      }\n    }\n    return filtered;\n  }\n  function getEvenSpacing(arr) {\n    var len = arr.length;\n    var i, diff;\n    if (len < 2) {\n      return false;\n    }\n    for (diff = arr[0], i = 1; i < len; ++i) {\n      if (arr[i] - arr[i - 1] !== diff) {\n        return false;\n      }\n    }\n    return diff;\n  }\n  function calculateSpacing(majorIndices, ticks, axisLength, ticksLimit) {\n    var evenMajorSpacing = getEvenSpacing(majorIndices);\n    var spacing = (ticks.length - 1) / ticksLimit;\n    var factors, factor, i, ilen;\n\n    // If the major ticks are evenly spaced apart, place the minor ticks\n    // so that they divide the major ticks into even chunks\n    if (!evenMajorSpacing) {\n      return Math.max(spacing, 1);\n    }\n    factors = helpers$1.math._factorize(evenMajorSpacing);\n    for (i = 0, ilen = factors.length - 1; i < ilen; i++) {\n      factor = factors[i];\n      if (factor > spacing) {\n        return factor;\n      }\n    }\n    return Math.max(spacing, 1);\n  }\n  function getMajorIndices(ticks) {\n    var result = [];\n    var i, ilen;\n    for (i = 0, ilen = ticks.length; i < ilen; i++) {\n      if (ticks[i].major) {\n        result.push(i);\n      }\n    }\n    return result;\n  }\n  function skipMajors(ticks, majorIndices, spacing) {\n    var count = 0;\n    var next = majorIndices[0];\n    var i, tick;\n    spacing = Math.ceil(spacing);\n    for (i = 0; i < ticks.length; i++) {\n      tick = ticks[i];\n      if (i === next) {\n        tick._index = i;\n        count++;\n        next = majorIndices[count * spacing];\n      } else {\n        delete tick.label;\n      }\n    }\n  }\n  function skip(ticks, spacing, majorStart, majorEnd) {\n    var start = valueOrDefault$a(majorStart, 0);\n    var end = Math.min(valueOrDefault$a(majorEnd, ticks.length), ticks.length);\n    var count = 0;\n    var length, i, tick, next;\n    spacing = Math.ceil(spacing);\n    if (majorEnd) {\n      length = majorEnd - majorStart;\n      spacing = length / Math.floor(length / spacing);\n    }\n    next = start;\n    while (next < 0) {\n      count++;\n      next = Math.round(start + count * spacing);\n    }\n    for (i = Math.max(start, 0); i < end; i++) {\n      tick = ticks[i];\n      if (i === next) {\n        tick._index = i;\n        count++;\n        next = Math.round(start + count * spacing);\n      } else {\n        delete tick.label;\n      }\n    }\n  }\n  var Scale = core_element.extend({\n    zeroLineIndex: 0,\n    /**\r\n     * Get the padding needed for the scale\r\n     * @method getPadding\r\n     * @private\r\n     * @returns {Padding} the necessary padding\r\n     */\n    getPadding: function () {\n      var me = this;\n      return {\n        left: me.paddingLeft || 0,\n        top: me.paddingTop || 0,\n        right: me.paddingRight || 0,\n        bottom: me.paddingBottom || 0\n      };\n    },\n    /**\r\n     * Returns the scale tick objects ({label, major})\r\n     * @since 2.7\r\n     */\n    getTicks: function () {\n      return this._ticks;\n    },\n    /**\r\n    * @private\r\n    */\n    _getLabels: function () {\n      var data = this.chart.data;\n      return this.options.labels || (this.isHorizontal() ? data.xLabels : data.yLabels) || data.labels || [];\n    },\n    // These methods are ordered by lifecyle. Utilities then follow.\n    // Any function defined here is inherited by all scale types.\n    // Any function can be extended by the scale type\n\n    /**\r\n     * Provided for backward compatibility, not available anymore\r\n     * @function Chart.Scale.mergeTicksOptions\r\n     * @deprecated since version 2.8.0\r\n     * @todo remove at version 3\r\n     */\n    mergeTicksOptions: function () {\n      // noop\n    },\n    beforeUpdate: function () {\n      helpers$1.callback(this.options.beforeUpdate, [this]);\n    },\n    /**\r\n     * @param {number} maxWidth - the max width in pixels\r\n     * @param {number} maxHeight - the max height in pixels\r\n     * @param {object} margins - the space between the edge of the other scales and edge of the chart\r\n     *   This space comes from two sources:\r\n     *     - padding - space that's required to show the labels at the edges of the scale\r\n     *     - thickness of scales or legends in another orientation\r\n     */\n    update: function (maxWidth, maxHeight, margins) {\n      var me = this;\n      var tickOpts = me.options.ticks;\n      var sampleSize = tickOpts.sampleSize;\n      var i, ilen, labels, ticks, samplingEnabled;\n\n      // Update Lifecycle - Probably don't want to ever extend or overwrite this function ;)\n      me.beforeUpdate();\n\n      // Absorb the master measurements\n      me.maxWidth = maxWidth;\n      me.maxHeight = maxHeight;\n      me.margins = helpers$1.extend({\n        left: 0,\n        right: 0,\n        top: 0,\n        bottom: 0\n      }, margins);\n      me._ticks = null;\n      me.ticks = null;\n      me._labelSizes = null;\n      me._maxLabelLines = 0;\n      me.longestLabelWidth = 0;\n      me.longestTextCache = me.longestTextCache || {};\n      me._gridLineItems = null;\n      me._labelItems = null;\n\n      // Dimensions\n      me.beforeSetDimensions();\n      me.setDimensions();\n      me.afterSetDimensions();\n\n      // Data min/max\n      me.beforeDataLimits();\n      me.determineDataLimits();\n      me.afterDataLimits();\n\n      // Ticks - `this.ticks` is now DEPRECATED!\n      // Internal ticks are now stored as objects in the PRIVATE `this._ticks` member\n      // and must not be accessed directly from outside this class. `this.ticks` being\n      // around for long time and not marked as private, we can't change its structure\n      // without unexpected breaking changes. If you need to access the scale ticks,\n      // use scale.getTicks() instead.\n\n      me.beforeBuildTicks();\n\n      // New implementations should return an array of objects but for BACKWARD COMPAT,\n      // we still support no return (`this.ticks` internally set by calling this method).\n      ticks = me.buildTicks() || [];\n\n      // Allow modification of ticks in callback.\n      ticks = me.afterBuildTicks(ticks) || ticks;\n\n      // Ensure ticks contains ticks in new tick format\n      if ((!ticks || !ticks.length) && me.ticks) {\n        ticks = [];\n        for (i = 0, ilen = me.ticks.length; i < ilen; ++i) {\n          ticks.push({\n            value: me.ticks[i],\n            major: false\n          });\n        }\n      }\n      me._ticks = ticks;\n\n      // Compute tick rotation and fit using a sampled subset of labels\n      // We generally don't need to compute the size of every single label for determining scale size\n      samplingEnabled = sampleSize < ticks.length;\n      labels = me._convertTicksToLabels(samplingEnabled ? sample(ticks, sampleSize) : ticks);\n\n      // _configure is called twice, once here, once from core.controller.updateLayout.\n      // Here we haven't been positioned yet, but dimensions are correct.\n      // Variables set in _configure are needed for calculateTickRotation, and\n      // it's ok that coordinates are not correct there, only dimensions matter.\n      me._configure();\n\n      // Tick Rotation\n      me.beforeCalculateTickRotation();\n      me.calculateTickRotation();\n      me.afterCalculateTickRotation();\n      me.beforeFit();\n      me.fit();\n      me.afterFit();\n\n      // Auto-skip\n      me._ticksToDraw = tickOpts.display && (tickOpts.autoSkip || tickOpts.source === 'auto') ? me._autoSkip(ticks) : ticks;\n      if (samplingEnabled) {\n        // Generate labels using all non-skipped ticks\n        labels = me._convertTicksToLabels(me._ticksToDraw);\n      }\n      me.ticks = labels; // BACKWARD COMPATIBILITY\n\n      // IMPORTANT: after this point, we consider that `this.ticks` will NEVER change!\n\n      me.afterUpdate();\n\n      // TODO(v3): remove minSize as a public property and return value from all layout boxes. It is unused\n      // make maxWidth and maxHeight private\n      return me.minSize;\n    },\n    /**\r\n     * @private\r\n     */\n    _configure: function () {\n      var me = this;\n      var reversePixels = me.options.ticks.reverse;\n      var startPixel, endPixel;\n      if (me.isHorizontal()) {\n        startPixel = me.left;\n        endPixel = me.right;\n      } else {\n        startPixel = me.top;\n        endPixel = me.bottom;\n        // by default vertical scales are from bottom to top, so pixels are reversed\n        reversePixels = !reversePixels;\n      }\n      me._startPixel = startPixel;\n      me._endPixel = endPixel;\n      me._reversePixels = reversePixels;\n      me._length = endPixel - startPixel;\n    },\n    afterUpdate: function () {\n      helpers$1.callback(this.options.afterUpdate, [this]);\n    },\n    //\n\n    beforeSetDimensions: function () {\n      helpers$1.callback(this.options.beforeSetDimensions, [this]);\n    },\n    setDimensions: function () {\n      var me = this;\n      // Set the unconstrained dimension before label rotation\n      if (me.isHorizontal()) {\n        // Reset position before calculating rotation\n        me.width = me.maxWidth;\n        me.left = 0;\n        me.right = me.width;\n      } else {\n        me.height = me.maxHeight;\n\n        // Reset position before calculating rotation\n        me.top = 0;\n        me.bottom = me.height;\n      }\n\n      // Reset padding\n      me.paddingLeft = 0;\n      me.paddingTop = 0;\n      me.paddingRight = 0;\n      me.paddingBottom = 0;\n    },\n    afterSetDimensions: function () {\n      helpers$1.callback(this.options.afterSetDimensions, [this]);\n    },\n    // Data limits\n    beforeDataLimits: function () {\n      helpers$1.callback(this.options.beforeDataLimits, [this]);\n    },\n    determineDataLimits: helpers$1.noop,\n    afterDataLimits: function () {\n      helpers$1.callback(this.options.afterDataLimits, [this]);\n    },\n    //\n    beforeBuildTicks: function () {\n      helpers$1.callback(this.options.beforeBuildTicks, [this]);\n    },\n    buildTicks: helpers$1.noop,\n    afterBuildTicks: function (ticks) {\n      var me = this;\n      // ticks is empty for old axis implementations here\n      if (isArray(ticks) && ticks.length) {\n        return helpers$1.callback(me.options.afterBuildTicks, [me, ticks]);\n      }\n      // Support old implementations (that modified `this.ticks` directly in buildTicks)\n      me.ticks = helpers$1.callback(me.options.afterBuildTicks, [me, me.ticks]) || me.ticks;\n      return ticks;\n    },\n    beforeTickToLabelConversion: function () {\n      helpers$1.callback(this.options.beforeTickToLabelConversion, [this]);\n    },\n    convertTicksToLabels: function () {\n      var me = this;\n      // Convert ticks to strings\n      var tickOpts = me.options.ticks;\n      me.ticks = me.ticks.map(tickOpts.userCallback || tickOpts.callback, this);\n    },\n    afterTickToLabelConversion: function () {\n      helpers$1.callback(this.options.afterTickToLabelConversion, [this]);\n    },\n    //\n\n    beforeCalculateTickRotation: function () {\n      helpers$1.callback(this.options.beforeCalculateTickRotation, [this]);\n    },\n    calculateTickRotation: function () {\n      var me = this;\n      var options = me.options;\n      var tickOpts = options.ticks;\n      var numTicks = me.getTicks().length;\n      var minRotation = tickOpts.minRotation || 0;\n      var maxRotation = tickOpts.maxRotation;\n      var labelRotation = minRotation;\n      var labelSizes, maxLabelWidth, maxLabelHeight, maxWidth, tickWidth, maxHeight, maxLabelDiagonal;\n      if (!me._isVisible() || !tickOpts.display || minRotation >= maxRotation || numTicks <= 1 || !me.isHorizontal()) {\n        me.labelRotation = minRotation;\n        return;\n      }\n      labelSizes = me._getLabelSizes();\n      maxLabelWidth = labelSizes.widest.width;\n      maxLabelHeight = labelSizes.highest.height - labelSizes.highest.offset;\n\n      // Estimate the width of each grid based on the canvas width, the maximum\n      // label width and the number of tick intervals\n      maxWidth = Math.min(me.maxWidth, me.chart.width - maxLabelWidth);\n      tickWidth = options.offset ? me.maxWidth / numTicks : maxWidth / (numTicks - 1);\n\n      // Allow 3 pixels x2 padding either side for label readability\n      if (maxLabelWidth + 6 > tickWidth) {\n        tickWidth = maxWidth / (numTicks - (options.offset ? 0.5 : 1));\n        maxHeight = me.maxHeight - getTickMarkLength(options.gridLines) - tickOpts.padding - getScaleLabelHeight(options.scaleLabel);\n        maxLabelDiagonal = Math.sqrt(maxLabelWidth * maxLabelWidth + maxLabelHeight * maxLabelHeight);\n        labelRotation = helpers$1.toDegrees(Math.min(Math.asin(Math.min((labelSizes.highest.height + 6) / tickWidth, 1)), Math.asin(Math.min(maxHeight / maxLabelDiagonal, 1)) - Math.asin(maxLabelHeight / maxLabelDiagonal)));\n        labelRotation = Math.max(minRotation, Math.min(maxRotation, labelRotation));\n      }\n      me.labelRotation = labelRotation;\n    },\n    afterCalculateTickRotation: function () {\n      helpers$1.callback(this.options.afterCalculateTickRotation, [this]);\n    },\n    //\n\n    beforeFit: function () {\n      helpers$1.callback(this.options.beforeFit, [this]);\n    },\n    fit: function () {\n      var me = this;\n      // Reset\n      var minSize = me.minSize = {\n        width: 0,\n        height: 0\n      };\n      var chart = me.chart;\n      var opts = me.options;\n      var tickOpts = opts.ticks;\n      var scaleLabelOpts = opts.scaleLabel;\n      var gridLineOpts = opts.gridLines;\n      var display = me._isVisible();\n      var isBottom = opts.position === 'bottom';\n      var isHorizontal = me.isHorizontal();\n\n      // Width\n      if (isHorizontal) {\n        minSize.width = me.maxWidth;\n      } else if (display) {\n        minSize.width = getTickMarkLength(gridLineOpts) + getScaleLabelHeight(scaleLabelOpts);\n      }\n\n      // height\n      if (!isHorizontal) {\n        minSize.height = me.maxHeight; // fill all the height\n      } else if (display) {\n        minSize.height = getTickMarkLength(gridLineOpts) + getScaleLabelHeight(scaleLabelOpts);\n      }\n\n      // Don't bother fitting the ticks if we are not showing the labels\n      if (tickOpts.display && display) {\n        var tickFonts = parseTickFontOptions(tickOpts);\n        var labelSizes = me._getLabelSizes();\n        var firstLabelSize = labelSizes.first;\n        var lastLabelSize = labelSizes.last;\n        var widestLabelSize = labelSizes.widest;\n        var highestLabelSize = labelSizes.highest;\n        var lineSpace = tickFonts.minor.lineHeight * 0.4;\n        var tickPadding = tickOpts.padding;\n        if (isHorizontal) {\n          // A horizontal axis is more constrained by the height.\n          var isRotated = me.labelRotation !== 0;\n          var angleRadians = helpers$1.toRadians(me.labelRotation);\n          var cosRotation = Math.cos(angleRadians);\n          var sinRotation = Math.sin(angleRadians);\n          var labelHeight = sinRotation * widestLabelSize.width + cosRotation * (highestLabelSize.height - (isRotated ? highestLabelSize.offset : 0)) + (isRotated ? 0 : lineSpace); // padding\n\n          minSize.height = Math.min(me.maxHeight, minSize.height + labelHeight + tickPadding);\n          var offsetLeft = me.getPixelForTick(0) - me.left;\n          var offsetRight = me.right - me.getPixelForTick(me.getTicks().length - 1);\n          var paddingLeft, paddingRight;\n\n          // Ensure that our ticks are always inside the canvas. When rotated, ticks are right aligned\n          // which means that the right padding is dominated by the font height\n          if (isRotated) {\n            paddingLeft = isBottom ? cosRotation * firstLabelSize.width + sinRotation * firstLabelSize.offset : sinRotation * (firstLabelSize.height - firstLabelSize.offset);\n            paddingRight = isBottom ? sinRotation * (lastLabelSize.height - lastLabelSize.offset) : cosRotation * lastLabelSize.width + sinRotation * lastLabelSize.offset;\n          } else {\n            paddingLeft = firstLabelSize.width / 2;\n            paddingRight = lastLabelSize.width / 2;\n          }\n\n          // Adjust padding taking into account changes in offsets\n          // and add 3 px to move away from canvas edges\n          me.paddingLeft = Math.max((paddingLeft - offsetLeft) * me.width / (me.width - offsetLeft), 0) + 3;\n          me.paddingRight = Math.max((paddingRight - offsetRight) * me.width / (me.width - offsetRight), 0) + 3;\n        } else {\n          // A vertical axis is more constrained by the width. Labels are the\n          // dominant factor here, so get that length first and account for padding\n          var labelWidth = tickOpts.mirror ? 0 :\n          // use lineSpace for consistency with horizontal axis\n          // tickPadding is not implemented for horizontal\n          widestLabelSize.width + tickPadding + lineSpace;\n          minSize.width = Math.min(me.maxWidth, minSize.width + labelWidth);\n          me.paddingTop = firstLabelSize.height / 2;\n          me.paddingBottom = lastLabelSize.height / 2;\n        }\n      }\n      me.handleMargins();\n      if (isHorizontal) {\n        me.width = me._length = chart.width - me.margins.left - me.margins.right;\n        me.height = minSize.height;\n      } else {\n        me.width = minSize.width;\n        me.height = me._length = chart.height - me.margins.top - me.margins.bottom;\n      }\n    },\n    /**\r\n     * Handle margins and padding interactions\r\n     * @private\r\n     */\n    handleMargins: function () {\n      var me = this;\n      if (me.margins) {\n        me.margins.left = Math.max(me.paddingLeft, me.margins.left);\n        me.margins.top = Math.max(me.paddingTop, me.margins.top);\n        me.margins.right = Math.max(me.paddingRight, me.margins.right);\n        me.margins.bottom = Math.max(me.paddingBottom, me.margins.bottom);\n      }\n    },\n    afterFit: function () {\n      helpers$1.callback(this.options.afterFit, [this]);\n    },\n    // Shared Methods\n    isHorizontal: function () {\n      var pos = this.options.position;\n      return pos === 'top' || pos === 'bottom';\n    },\n    isFullWidth: function () {\n      return this.options.fullWidth;\n    },\n    // Get the correct value. NaN bad inputs, If the value type is object get the x or y based on whether we are horizontal or not\n    getRightValue: function (rawValue) {\n      // Null and undefined values first\n      if (isNullOrUndef(rawValue)) {\n        return NaN;\n      }\n      // isNaN(object) returns true, so make sure NaN is checking for a number; Discard Infinite values\n      if ((typeof rawValue === 'number' || rawValue instanceof Number) && !isFinite(rawValue)) {\n        return NaN;\n      }\n\n      // If it is in fact an object, dive in one more level\n      if (rawValue) {\n        if (this.isHorizontal()) {\n          if (rawValue.x !== undefined) {\n            return this.getRightValue(rawValue.x);\n          }\n        } else if (rawValue.y !== undefined) {\n          return this.getRightValue(rawValue.y);\n        }\n      }\n\n      // Value is good, return it\n      return rawValue;\n    },\n    _convertTicksToLabels: function (ticks) {\n      var me = this;\n      var labels, i, ilen;\n      me.ticks = ticks.map(function (tick) {\n        return tick.value;\n      });\n      me.beforeTickToLabelConversion();\n\n      // New implementations should return the formatted tick labels but for BACKWARD\n      // COMPAT, we still support no return (`this.ticks` internally changed by calling\n      // this method and supposed to contain only string values).\n      labels = me.convertTicksToLabels(ticks) || me.ticks;\n      me.afterTickToLabelConversion();\n\n      // BACKWARD COMPAT: synchronize `_ticks` with labels (so potentially `this.ticks`)\n      for (i = 0, ilen = ticks.length; i < ilen; ++i) {\n        ticks[i].label = labels[i];\n      }\n      return labels;\n    },\n    /**\r\n     * @private\r\n     */\n    _getLabelSizes: function () {\n      var me = this;\n      var labelSizes = me._labelSizes;\n      if (!labelSizes) {\n        me._labelSizes = labelSizes = computeLabelSizes(me.ctx, parseTickFontOptions(me.options.ticks), me.getTicks(), me.longestTextCache);\n        me.longestLabelWidth = labelSizes.widest.width;\n      }\n      return labelSizes;\n    },\n    /**\r\n     * @private\r\n     */\n    _parseValue: function (value) {\n      var start, end, min, max;\n      if (isArray(value)) {\n        start = +this.getRightValue(value[0]);\n        end = +this.getRightValue(value[1]);\n        min = Math.min(start, end);\n        max = Math.max(start, end);\n      } else {\n        value = +this.getRightValue(value);\n        start = undefined;\n        end = value;\n        min = value;\n        max = value;\n      }\n      return {\n        min: min,\n        max: max,\n        start: start,\n        end: end\n      };\n    },\n    /**\r\n    * @private\r\n    */\n    _getScaleLabel: function (rawValue) {\n      var v = this._parseValue(rawValue);\n      if (v.start !== undefined) {\n        return '[' + v.start + ', ' + v.end + ']';\n      }\n      return +this.getRightValue(rawValue);\n    },\n    /**\r\n     * Used to get the value to display in the tooltip for the data at the given index\r\n     * @param index\r\n     * @param datasetIndex\r\n     */\n    getLabelForIndex: helpers$1.noop,\n    /**\r\n     * Returns the location of the given data point. Value can either be an index or a numerical value\r\n     * The coordinate (0, 0) is at the upper-left corner of the canvas\r\n     * @param value\r\n     * @param index\r\n     * @param datasetIndex\r\n     */\n    getPixelForValue: helpers$1.noop,\n    /**\r\n     * Used to get the data value from a given pixel. This is the inverse of getPixelForValue\r\n     * The coordinate (0, 0) is at the upper-left corner of the canvas\r\n     * @param pixel\r\n     */\n    getValueForPixel: helpers$1.noop,\n    /**\r\n     * Returns the location of the tick at the given index\r\n     * The coordinate (0, 0) is at the upper-left corner of the canvas\r\n     */\n    getPixelForTick: function (index) {\n      var me = this;\n      var offset = me.options.offset;\n      var numTicks = me._ticks.length;\n      var tickWidth = 1 / Math.max(numTicks - (offset ? 0 : 1), 1);\n      return index < 0 || index > numTicks - 1 ? null : me.getPixelForDecimal(index * tickWidth + (offset ? tickWidth / 2 : 0));\n    },\n    /**\r\n     * Utility for getting the pixel location of a percentage of scale\r\n     * The coordinate (0, 0) is at the upper-left corner of the canvas\r\n     */\n    getPixelForDecimal: function (decimal) {\n      var me = this;\n      if (me._reversePixels) {\n        decimal = 1 - decimal;\n      }\n      return me._startPixel + decimal * me._length;\n    },\n    getDecimalForPixel: function (pixel) {\n      var decimal = (pixel - this._startPixel) / this._length;\n      return this._reversePixels ? 1 - decimal : decimal;\n    },\n    /**\r\n     * Returns the pixel for the minimum chart value\r\n     * The coordinate (0, 0) is at the upper-left corner of the canvas\r\n     */\n    getBasePixel: function () {\n      return this.getPixelForValue(this.getBaseValue());\n    },\n    getBaseValue: function () {\n      var me = this;\n      var min = me.min;\n      var max = me.max;\n      return me.beginAtZero ? 0 : min < 0 && max < 0 ? max : min > 0 && max > 0 ? min : 0;\n    },\n    /**\r\n     * Returns a subset of ticks to be plotted to avoid overlapping labels.\r\n     * @private\r\n     */\n    _autoSkip: function (ticks) {\n      var me = this;\n      var tickOpts = me.options.ticks;\n      var axisLength = me._length;\n      var ticksLimit = tickOpts.maxTicksLimit || axisLength / me._tickSize() + 1;\n      var majorIndices = tickOpts.major.enabled ? getMajorIndices(ticks) : [];\n      var numMajorIndices = majorIndices.length;\n      var first = majorIndices[0];\n      var last = majorIndices[numMajorIndices - 1];\n      var i, ilen, spacing, avgMajorSpacing;\n\n      // If there are too many major ticks to display them all\n      if (numMajorIndices > ticksLimit) {\n        skipMajors(ticks, majorIndices, numMajorIndices / ticksLimit);\n        return nonSkipped(ticks);\n      }\n      spacing = calculateSpacing(majorIndices, ticks, axisLength, ticksLimit);\n      if (numMajorIndices > 0) {\n        for (i = 0, ilen = numMajorIndices - 1; i < ilen; i++) {\n          skip(ticks, spacing, majorIndices[i], majorIndices[i + 1]);\n        }\n        avgMajorSpacing = numMajorIndices > 1 ? (last - first) / (numMajorIndices - 1) : null;\n        skip(ticks, spacing, helpers$1.isNullOrUndef(avgMajorSpacing) ? 0 : first - avgMajorSpacing, first);\n        skip(ticks, spacing, last, helpers$1.isNullOrUndef(avgMajorSpacing) ? ticks.length : last + avgMajorSpacing);\n        return nonSkipped(ticks);\n      }\n      skip(ticks, spacing);\n      return nonSkipped(ticks);\n    },\n    /**\r\n     * @private\r\n     */\n    _tickSize: function () {\n      var me = this;\n      var optionTicks = me.options.ticks;\n\n      // Calculate space needed by label in axis direction.\n      var rot = helpers$1.toRadians(me.labelRotation);\n      var cos = Math.abs(Math.cos(rot));\n      var sin = Math.abs(Math.sin(rot));\n      var labelSizes = me._getLabelSizes();\n      var padding = optionTicks.autoSkipPadding || 0;\n      var w = labelSizes ? labelSizes.widest.width + padding : 0;\n      var h = labelSizes ? labelSizes.highest.height + padding : 0;\n\n      // Calculate space needed for 1 tick in axis direction.\n      return me.isHorizontal() ? h * cos > w * sin ? w / cos : h / sin : h * sin < w * cos ? h / cos : w / sin;\n    },\n    /**\r\n     * @private\r\n     */\n    _isVisible: function () {\n      var me = this;\n      var chart = me.chart;\n      var display = me.options.display;\n      var i, ilen, meta;\n      if (display !== 'auto') {\n        return !!display;\n      }\n\n      // When 'auto', the scale is visible if at least one associated dataset is visible.\n      for (i = 0, ilen = chart.data.datasets.length; i < ilen; ++i) {\n        if (chart.isDatasetVisible(i)) {\n          meta = chart.getDatasetMeta(i);\n          if (meta.xAxisID === me.id || meta.yAxisID === me.id) {\n            return true;\n          }\n        }\n      }\n      return false;\n    },\n    /**\r\n     * @private\r\n     */\n    _computeGridLineItems: function (chartArea) {\n      var me = this;\n      var chart = me.chart;\n      var options = me.options;\n      var gridLines = options.gridLines;\n      var position = options.position;\n      var offsetGridLines = gridLines.offsetGridLines;\n      var isHorizontal = me.isHorizontal();\n      var ticks = me._ticksToDraw;\n      var ticksLength = ticks.length + (offsetGridLines ? 1 : 0);\n      var tl = getTickMarkLength(gridLines);\n      var items = [];\n      var axisWidth = gridLines.drawBorder ? valueAtIndexOrDefault(gridLines.lineWidth, 0, 0) : 0;\n      var axisHalfWidth = axisWidth / 2;\n      var alignPixel = helpers$1._alignPixel;\n      var alignBorderValue = function (pixel) {\n        return alignPixel(chart, pixel, axisWidth);\n      };\n      var borderValue, i, tick, lineValue, alignedLineValue;\n      var tx1, ty1, tx2, ty2, x1, y1, x2, y2, lineWidth, lineColor, borderDash, borderDashOffset;\n      if (position === 'top') {\n        borderValue = alignBorderValue(me.bottom);\n        ty1 = me.bottom - tl;\n        ty2 = borderValue - axisHalfWidth;\n        y1 = alignBorderValue(chartArea.top) + axisHalfWidth;\n        y2 = chartArea.bottom;\n      } else if (position === 'bottom') {\n        borderValue = alignBorderValue(me.top);\n        y1 = chartArea.top;\n        y2 = alignBorderValue(chartArea.bottom) - axisHalfWidth;\n        ty1 = borderValue + axisHalfWidth;\n        ty2 = me.top + tl;\n      } else if (position === 'left') {\n        borderValue = alignBorderValue(me.right);\n        tx1 = me.right - tl;\n        tx2 = borderValue - axisHalfWidth;\n        x1 = alignBorderValue(chartArea.left) + axisHalfWidth;\n        x2 = chartArea.right;\n      } else {\n        borderValue = alignBorderValue(me.left);\n        x1 = chartArea.left;\n        x2 = alignBorderValue(chartArea.right) - axisHalfWidth;\n        tx1 = borderValue + axisHalfWidth;\n        tx2 = me.left + tl;\n      }\n      for (i = 0; i < ticksLength; ++i) {\n        tick = ticks[i] || {};\n\n        // autoskipper skipped this tick (#4635)\n        if (isNullOrUndef(tick.label) && i < ticks.length) {\n          continue;\n        }\n        if (i === me.zeroLineIndex && options.offset === offsetGridLines) {\n          // Draw the first index specially\n          lineWidth = gridLines.zeroLineWidth;\n          lineColor = gridLines.zeroLineColor;\n          borderDash = gridLines.zeroLineBorderDash || [];\n          borderDashOffset = gridLines.zeroLineBorderDashOffset || 0.0;\n        } else {\n          lineWidth = valueAtIndexOrDefault(gridLines.lineWidth, i, 1);\n          lineColor = valueAtIndexOrDefault(gridLines.color, i, 'rgba(0,0,0,0.1)');\n          borderDash = gridLines.borderDash || [];\n          borderDashOffset = gridLines.borderDashOffset || 0.0;\n        }\n        lineValue = getPixelForGridLine(me, tick._index || i, offsetGridLines);\n\n        // Skip if the pixel is out of the range\n        if (lineValue === undefined) {\n          continue;\n        }\n        alignedLineValue = alignPixel(chart, lineValue, lineWidth);\n        if (isHorizontal) {\n          tx1 = tx2 = x1 = x2 = alignedLineValue;\n        } else {\n          ty1 = ty2 = y1 = y2 = alignedLineValue;\n        }\n        items.push({\n          tx1: tx1,\n          ty1: ty1,\n          tx2: tx2,\n          ty2: ty2,\n          x1: x1,\n          y1: y1,\n          x2: x2,\n          y2: y2,\n          width: lineWidth,\n          color: lineColor,\n          borderDash: borderDash,\n          borderDashOffset: borderDashOffset\n        });\n      }\n      items.ticksLength = ticksLength;\n      items.borderValue = borderValue;\n      return items;\n    },\n    /**\r\n     * @private\r\n     */\n    _computeLabelItems: function () {\n      var me = this;\n      var options = me.options;\n      var optionTicks = options.ticks;\n      var position = options.position;\n      var isMirrored = optionTicks.mirror;\n      var isHorizontal = me.isHorizontal();\n      var ticks = me._ticksToDraw;\n      var fonts = parseTickFontOptions(optionTicks);\n      var tickPadding = optionTicks.padding;\n      var tl = getTickMarkLength(options.gridLines);\n      var rotation = -helpers$1.toRadians(me.labelRotation);\n      var items = [];\n      var i, ilen, tick, label, x, y, textAlign, pixel, font, lineHeight, lineCount, textOffset;\n      if (position === 'top') {\n        y = me.bottom - tl - tickPadding;\n        textAlign = !rotation ? 'center' : 'left';\n      } else if (position === 'bottom') {\n        y = me.top + tl + tickPadding;\n        textAlign = !rotation ? 'center' : 'right';\n      } else if (position === 'left') {\n        x = me.right - (isMirrored ? 0 : tl) - tickPadding;\n        textAlign = isMirrored ? 'left' : 'right';\n      } else {\n        x = me.left + (isMirrored ? 0 : tl) + tickPadding;\n        textAlign = isMirrored ? 'right' : 'left';\n      }\n      for (i = 0, ilen = ticks.length; i < ilen; ++i) {\n        tick = ticks[i];\n        label = tick.label;\n\n        // autoskipper skipped this tick (#4635)\n        if (isNullOrUndef(label)) {\n          continue;\n        }\n        pixel = me.getPixelForTick(tick._index || i) + optionTicks.labelOffset;\n        font = tick.major ? fonts.major : fonts.minor;\n        lineHeight = font.lineHeight;\n        lineCount = isArray(label) ? label.length : 1;\n        if (isHorizontal) {\n          x = pixel;\n          textOffset = position === 'top' ? ((!rotation ? 0.5 : 1) - lineCount) * lineHeight : (!rotation ? 0.5 : 0) * lineHeight;\n        } else {\n          y = pixel;\n          textOffset = (1 - lineCount) * lineHeight / 2;\n        }\n        items.push({\n          x: x,\n          y: y,\n          rotation: rotation,\n          label: label,\n          font: font,\n          textOffset: textOffset,\n          textAlign: textAlign\n        });\n      }\n      return items;\n    },\n    /**\r\n     * @private\r\n     */\n    _drawGrid: function (chartArea) {\n      var me = this;\n      var gridLines = me.options.gridLines;\n      if (!gridLines.display) {\n        return;\n      }\n      var ctx = me.ctx;\n      var chart = me.chart;\n      var alignPixel = helpers$1._alignPixel;\n      var axisWidth = gridLines.drawBorder ? valueAtIndexOrDefault(gridLines.lineWidth, 0, 0) : 0;\n      var items = me._gridLineItems || (me._gridLineItems = me._computeGridLineItems(chartArea));\n      var width, color, i, ilen, item;\n      for (i = 0, ilen = items.length; i < ilen; ++i) {\n        item = items[i];\n        width = item.width;\n        color = item.color;\n        if (width && color) {\n          ctx.save();\n          ctx.lineWidth = width;\n          ctx.strokeStyle = color;\n          if (ctx.setLineDash) {\n            ctx.setLineDash(item.borderDash);\n            ctx.lineDashOffset = item.borderDashOffset;\n          }\n          ctx.beginPath();\n          if (gridLines.drawTicks) {\n            ctx.moveTo(item.tx1, item.ty1);\n            ctx.lineTo(item.tx2, item.ty2);\n          }\n          if (gridLines.drawOnChartArea) {\n            ctx.moveTo(item.x1, item.y1);\n            ctx.lineTo(item.x2, item.y2);\n          }\n          ctx.stroke();\n          ctx.restore();\n        }\n      }\n      if (axisWidth) {\n        // Draw the line at the edge of the axis\n        var firstLineWidth = axisWidth;\n        var lastLineWidth = valueAtIndexOrDefault(gridLines.lineWidth, items.ticksLength - 1, 1);\n        var borderValue = items.borderValue;\n        var x1, x2, y1, y2;\n        if (me.isHorizontal()) {\n          x1 = alignPixel(chart, me.left, firstLineWidth) - firstLineWidth / 2;\n          x2 = alignPixel(chart, me.right, lastLineWidth) + lastLineWidth / 2;\n          y1 = y2 = borderValue;\n        } else {\n          y1 = alignPixel(chart, me.top, firstLineWidth) - firstLineWidth / 2;\n          y2 = alignPixel(chart, me.bottom, lastLineWidth) + lastLineWidth / 2;\n          x1 = x2 = borderValue;\n        }\n        ctx.lineWidth = axisWidth;\n        ctx.strokeStyle = valueAtIndexOrDefault(gridLines.color, 0);\n        ctx.beginPath();\n        ctx.moveTo(x1, y1);\n        ctx.lineTo(x2, y2);\n        ctx.stroke();\n      }\n    },\n    /**\r\n     * @private\r\n     */\n    _drawLabels: function () {\n      var me = this;\n      var optionTicks = me.options.ticks;\n      if (!optionTicks.display) {\n        return;\n      }\n      var ctx = me.ctx;\n      var items = me._labelItems || (me._labelItems = me._computeLabelItems());\n      var i, j, ilen, jlen, item, tickFont, label, y;\n      for (i = 0, ilen = items.length; i < ilen; ++i) {\n        item = items[i];\n        tickFont = item.font;\n\n        // Make sure we draw text in the correct color and font\n        ctx.save();\n        ctx.translate(item.x, item.y);\n        ctx.rotate(item.rotation);\n        ctx.font = tickFont.string;\n        ctx.fillStyle = tickFont.color;\n        ctx.textBaseline = 'middle';\n        ctx.textAlign = item.textAlign;\n        label = item.label;\n        y = item.textOffset;\n        if (isArray(label)) {\n          for (j = 0, jlen = label.length; j < jlen; ++j) {\n            // We just make sure the multiline element is a string here..\n            ctx.fillText('' + label[j], 0, y);\n            y += tickFont.lineHeight;\n          }\n        } else {\n          ctx.fillText(label, 0, y);\n        }\n        ctx.restore();\n      }\n    },\n    /**\r\n     * @private\r\n     */\n    _drawTitle: function () {\n      var me = this;\n      var ctx = me.ctx;\n      var options = me.options;\n      var scaleLabel = options.scaleLabel;\n      if (!scaleLabel.display) {\n        return;\n      }\n      var scaleLabelFontColor = valueOrDefault$a(scaleLabel.fontColor, core_defaults.global.defaultFontColor);\n      var scaleLabelFont = helpers$1.options._parseFont(scaleLabel);\n      var scaleLabelPadding = helpers$1.options.toPadding(scaleLabel.padding);\n      var halfLineHeight = scaleLabelFont.lineHeight / 2;\n      var position = options.position;\n      var rotation = 0;\n      var scaleLabelX, scaleLabelY;\n      if (me.isHorizontal()) {\n        scaleLabelX = me.left + me.width / 2; // midpoint of the width\n        scaleLabelY = position === 'bottom' ? me.bottom - halfLineHeight - scaleLabelPadding.bottom : me.top + halfLineHeight + scaleLabelPadding.top;\n      } else {\n        var isLeft = position === 'left';\n        scaleLabelX = isLeft ? me.left + halfLineHeight + scaleLabelPadding.top : me.right - halfLineHeight - scaleLabelPadding.top;\n        scaleLabelY = me.top + me.height / 2;\n        rotation = isLeft ? -0.5 * Math.PI : 0.5 * Math.PI;\n      }\n      ctx.save();\n      ctx.translate(scaleLabelX, scaleLabelY);\n      ctx.rotate(rotation);\n      ctx.textAlign = 'center';\n      ctx.textBaseline = 'middle';\n      ctx.fillStyle = scaleLabelFontColor; // render in correct colour\n      ctx.font = scaleLabelFont.string;\n      ctx.fillText(scaleLabel.labelString, 0, 0);\n      ctx.restore();\n    },\n    draw: function (chartArea) {\n      var me = this;\n      if (!me._isVisible()) {\n        return;\n      }\n      me._drawGrid(chartArea);\n      me._drawTitle();\n      me._drawLabels();\n    },\n    /**\r\n     * @private\r\n     */\n    _layers: function () {\n      var me = this;\n      var opts = me.options;\n      var tz = opts.ticks && opts.ticks.z || 0;\n      var gz = opts.gridLines && opts.gridLines.z || 0;\n      if (!me._isVisible() || tz === gz || me.draw !== me._draw) {\n        // backward compatibility: draw has been overridden by custom scale\n        return [{\n          z: tz,\n          draw: function () {\n            me.draw.apply(me, arguments);\n          }\n        }];\n      }\n      return [{\n        z: gz,\n        draw: function () {\n          me._drawGrid.apply(me, arguments);\n          me._drawTitle.apply(me, arguments);\n        }\n      }, {\n        z: tz,\n        draw: function () {\n          me._drawLabels.apply(me, arguments);\n        }\n      }];\n    },\n    /**\r\n     * @private\r\n     */\n    _getMatchingVisibleMetas: function (type) {\n      var me = this;\n      var isHorizontal = me.isHorizontal();\n      return me.chart._getSortedVisibleDatasetMetas().filter(function (meta) {\n        return (!type || meta.type === type) && (isHorizontal ? meta.xAxisID === me.id : meta.yAxisID === me.id);\n      });\n    }\n  });\n  Scale.prototype._draw = Scale.prototype.draw;\n  var core_scale = Scale;\n  var isNullOrUndef$1 = helpers$1.isNullOrUndef;\n  var defaultConfig = {\n    position: 'bottom'\n  };\n  var scale_category = core_scale.extend({\n    determineDataLimits: function () {\n      var me = this;\n      var labels = me._getLabels();\n      var ticksOpts = me.options.ticks;\n      var min = ticksOpts.min;\n      var max = ticksOpts.max;\n      var minIndex = 0;\n      var maxIndex = labels.length - 1;\n      var findIndex;\n      if (min !== undefined) {\n        // user specified min value\n        findIndex = labels.indexOf(min);\n        if (findIndex >= 0) {\n          minIndex = findIndex;\n        }\n      }\n      if (max !== undefined) {\n        // user specified max value\n        findIndex = labels.indexOf(max);\n        if (findIndex >= 0) {\n          maxIndex = findIndex;\n        }\n      }\n      me.minIndex = minIndex;\n      me.maxIndex = maxIndex;\n      me.min = labels[minIndex];\n      me.max = labels[maxIndex];\n    },\n    buildTicks: function () {\n      var me = this;\n      var labels = me._getLabels();\n      var minIndex = me.minIndex;\n      var maxIndex = me.maxIndex;\n\n      // If we are viewing some subset of labels, slice the original array\n      me.ticks = minIndex === 0 && maxIndex === labels.length - 1 ? labels : labels.slice(minIndex, maxIndex + 1);\n    },\n    getLabelForIndex: function (index, datasetIndex) {\n      var me = this;\n      var chart = me.chart;\n      if (chart.getDatasetMeta(datasetIndex).controller._getValueScaleId() === me.id) {\n        return me.getRightValue(chart.data.datasets[datasetIndex].data[index]);\n      }\n      return me._getLabels()[index];\n    },\n    _configure: function () {\n      var me = this;\n      var offset = me.options.offset;\n      var ticks = me.ticks;\n      core_scale.prototype._configure.call(me);\n      if (!me.isHorizontal()) {\n        // For backward compatibility, vertical category scale reverse is inverted.\n        me._reversePixels = !me._reversePixels;\n      }\n      if (!ticks) {\n        return;\n      }\n      me._startValue = me.minIndex - (offset ? 0.5 : 0);\n      me._valueRange = Math.max(ticks.length - (offset ? 0 : 1), 1);\n    },\n    // Used to get data value locations.  Value can either be an index or a numerical value\n    getPixelForValue: function (value, index, datasetIndex) {\n      var me = this;\n      var valueCategory, labels, idx;\n      if (!isNullOrUndef$1(index) && !isNullOrUndef$1(datasetIndex)) {\n        value = me.chart.data.datasets[datasetIndex].data[index];\n      }\n\n      // If value is a data object, then index is the index in the data array,\n      // not the index of the scale. We need to change that.\n      if (!isNullOrUndef$1(value)) {\n        valueCategory = me.isHorizontal() ? value.x : value.y;\n      }\n      if (valueCategory !== undefined || value !== undefined && isNaN(index)) {\n        labels = me._getLabels();\n        value = helpers$1.valueOrDefault(valueCategory, value);\n        idx = labels.indexOf(value);\n        index = idx !== -1 ? idx : index;\n        if (isNaN(index)) {\n          index = value;\n        }\n      }\n      return me.getPixelForDecimal((index - me._startValue) / me._valueRange);\n    },\n    getPixelForTick: function (index) {\n      var ticks = this.ticks;\n      return index < 0 || index > ticks.length - 1 ? null : this.getPixelForValue(ticks[index], index + this.minIndex);\n    },\n    getValueForPixel: function (pixel) {\n      var me = this;\n      var value = Math.round(me._startValue + me.getDecimalForPixel(pixel) * me._valueRange);\n      return Math.min(Math.max(value, 0), me.ticks.length - 1);\n    },\n    getBasePixel: function () {\n      return this.bottom;\n    }\n  });\n\n  // INTERNAL: static default options, registered in src/index.js\n  var _defaults = defaultConfig;\n  scale_category._defaults = _defaults;\n  var noop = helpers$1.noop;\n  var isNullOrUndef$2 = helpers$1.isNullOrUndef;\n\n  /**\r\n   * Generate a set of linear ticks\r\n   * @param generationOptions the options used to generate the ticks\r\n   * @param dataRange the range of the data\r\n   * @returns {number[]} array of tick values\r\n   */\n  function generateTicks(generationOptions, dataRange) {\n    var ticks = [];\n    // To get a \"nice\" value for the tick spacing, we will use the appropriately named\n    // \"nice number\" algorithm. See https://stackoverflow.com/questions/8506881/nice-label-algorithm-for-charts-with-minimum-ticks\n    // for details.\n\n    var MIN_SPACING = 1e-14;\n    var stepSize = generationOptions.stepSize;\n    var unit = stepSize || 1;\n    var maxNumSpaces = generationOptions.maxTicks - 1;\n    var min = generationOptions.min;\n    var max = generationOptions.max;\n    var precision = generationOptions.precision;\n    var rmin = dataRange.min;\n    var rmax = dataRange.max;\n    var spacing = helpers$1.niceNum((rmax - rmin) / maxNumSpaces / unit) * unit;\n    var factor, niceMin, niceMax, numSpaces;\n\n    // Beyond MIN_SPACING floating point numbers being to lose precision\n    // such that we can't do the math necessary to generate ticks\n    if (spacing < MIN_SPACING && isNullOrUndef$2(min) && isNullOrUndef$2(max)) {\n      return [rmin, rmax];\n    }\n    numSpaces = Math.ceil(rmax / spacing) - Math.floor(rmin / spacing);\n    if (numSpaces > maxNumSpaces) {\n      // If the calculated num of spaces exceeds maxNumSpaces, recalculate it\n      spacing = helpers$1.niceNum(numSpaces * spacing / maxNumSpaces / unit) * unit;\n    }\n    if (stepSize || isNullOrUndef$2(precision)) {\n      // If a precision is not specified, calculate factor based on spacing\n      factor = Math.pow(10, helpers$1._decimalPlaces(spacing));\n    } else {\n      // If the user specified a precision, round to that number of decimal places\n      factor = Math.pow(10, precision);\n      spacing = Math.ceil(spacing * factor) / factor;\n    }\n    niceMin = Math.floor(rmin / spacing) * spacing;\n    niceMax = Math.ceil(rmax / spacing) * spacing;\n\n    // If min, max and stepSize is set and they make an evenly spaced scale use it.\n    if (stepSize) {\n      // If very close to our whole number, use it.\n      if (!isNullOrUndef$2(min) && helpers$1.almostWhole(min / spacing, spacing / 1000)) {\n        niceMin = min;\n      }\n      if (!isNullOrUndef$2(max) && helpers$1.almostWhole(max / spacing, spacing / 1000)) {\n        niceMax = max;\n      }\n    }\n    numSpaces = (niceMax - niceMin) / spacing;\n    // If very close to our rounded value, use it.\n    if (helpers$1.almostEquals(numSpaces, Math.round(numSpaces), spacing / 1000)) {\n      numSpaces = Math.round(numSpaces);\n    } else {\n      numSpaces = Math.ceil(numSpaces);\n    }\n    niceMin = Math.round(niceMin * factor) / factor;\n    niceMax = Math.round(niceMax * factor) / factor;\n    ticks.push(isNullOrUndef$2(min) ? niceMin : min);\n    for (var j = 1; j < numSpaces; ++j) {\n      ticks.push(Math.round((niceMin + j * spacing) * factor) / factor);\n    }\n    ticks.push(isNullOrUndef$2(max) ? niceMax : max);\n    return ticks;\n  }\n  var scale_linearbase = core_scale.extend({\n    getRightValue: function (value) {\n      if (typeof value === 'string') {\n        return +value;\n      }\n      return core_scale.prototype.getRightValue.call(this, value);\n    },\n    handleTickRangeOptions: function () {\n      var me = this;\n      var opts = me.options;\n      var tickOpts = opts.ticks;\n\n      // If we are forcing it to begin at 0, but 0 will already be rendered on the chart,\n      // do nothing since that would make the chart weird. If the user really wants a weird chart\n      // axis, they can manually override it\n      if (tickOpts.beginAtZero) {\n        var minSign = helpers$1.sign(me.min);\n        var maxSign = helpers$1.sign(me.max);\n        if (minSign < 0 && maxSign < 0) {\n          // move the top up to 0\n          me.max = 0;\n        } else if (minSign > 0 && maxSign > 0) {\n          // move the bottom down to 0\n          me.min = 0;\n        }\n      }\n      var setMin = tickOpts.min !== undefined || tickOpts.suggestedMin !== undefined;\n      var setMax = tickOpts.max !== undefined || tickOpts.suggestedMax !== undefined;\n      if (tickOpts.min !== undefined) {\n        me.min = tickOpts.min;\n      } else if (tickOpts.suggestedMin !== undefined) {\n        if (me.min === null) {\n          me.min = tickOpts.suggestedMin;\n        } else {\n          me.min = Math.min(me.min, tickOpts.suggestedMin);\n        }\n      }\n      if (tickOpts.max !== undefined) {\n        me.max = tickOpts.max;\n      } else if (tickOpts.suggestedMax !== undefined) {\n        if (me.max === null) {\n          me.max = tickOpts.suggestedMax;\n        } else {\n          me.max = Math.max(me.max, tickOpts.suggestedMax);\n        }\n      }\n      if (setMin !== setMax) {\n        // We set the min or the max but not both.\n        // So ensure that our range is good\n        // Inverted or 0 length range can happen when\n        // ticks.min is set, and no datasets are visible\n        if (me.min >= me.max) {\n          if (setMin) {\n            me.max = me.min + 1;\n          } else {\n            me.min = me.max - 1;\n          }\n        }\n      }\n      if (me.min === me.max) {\n        me.max++;\n        if (!tickOpts.beginAtZero) {\n          me.min--;\n        }\n      }\n    },\n    getTickLimit: function () {\n      var me = this;\n      var tickOpts = me.options.ticks;\n      var stepSize = tickOpts.stepSize;\n      var maxTicksLimit = tickOpts.maxTicksLimit;\n      var maxTicks;\n      if (stepSize) {\n        maxTicks = Math.ceil(me.max / stepSize) - Math.floor(me.min / stepSize) + 1;\n      } else {\n        maxTicks = me._computeTickLimit();\n        maxTicksLimit = maxTicksLimit || 11;\n      }\n      if (maxTicksLimit) {\n        maxTicks = Math.min(maxTicksLimit, maxTicks);\n      }\n      return maxTicks;\n    },\n    _computeTickLimit: function () {\n      return Number.POSITIVE_INFINITY;\n    },\n    handleDirectionalChanges: noop,\n    buildTicks: function () {\n      var me = this;\n      var opts = me.options;\n      var tickOpts = opts.ticks;\n\n      // Figure out what the max number of ticks we can support it is based on the size of\n      // the axis area. For now, we say that the minimum tick spacing in pixels must be 40\n      // We also limit the maximum number of ticks to 11 which gives a nice 10 squares on\n      // the graph. Make sure we always have at least 2 ticks\n      var maxTicks = me.getTickLimit();\n      maxTicks = Math.max(2, maxTicks);\n      var numericGeneratorOptions = {\n        maxTicks: maxTicks,\n        min: tickOpts.min,\n        max: tickOpts.max,\n        precision: tickOpts.precision,\n        stepSize: helpers$1.valueOrDefault(tickOpts.fixedStepSize, tickOpts.stepSize)\n      };\n      var ticks = me.ticks = generateTicks(numericGeneratorOptions, me);\n      me.handleDirectionalChanges();\n\n      // At this point, we need to update our max and min given the tick values since we have expanded the\n      // range of the scale\n      me.max = helpers$1.max(ticks);\n      me.min = helpers$1.min(ticks);\n      if (tickOpts.reverse) {\n        ticks.reverse();\n        me.start = me.max;\n        me.end = me.min;\n      } else {\n        me.start = me.min;\n        me.end = me.max;\n      }\n    },\n    convertTicksToLabels: function () {\n      var me = this;\n      me.ticksAsNumbers = me.ticks.slice();\n      me.zeroLineIndex = me.ticks.indexOf(0);\n      core_scale.prototype.convertTicksToLabels.call(me);\n    },\n    _configure: function () {\n      var me = this;\n      var ticks = me.getTicks();\n      var start = me.min;\n      var end = me.max;\n      var offset;\n      core_scale.prototype._configure.call(me);\n      if (me.options.offset && ticks.length) {\n        offset = (end - start) / Math.max(ticks.length - 1, 1) / 2;\n        start -= offset;\n        end += offset;\n      }\n      me._startValue = start;\n      me._endValue = end;\n      me._valueRange = end - start;\n    }\n  });\n  var defaultConfig$1 = {\n    position: 'left',\n    ticks: {\n      callback: core_ticks.formatters.linear\n    }\n  };\n  var DEFAULT_MIN = 0;\n  var DEFAULT_MAX = 1;\n  function getOrCreateStack(stacks, stacked, meta) {\n    var key = [meta.type,\n    // we have a separate stack for stack=undefined datasets when the opts.stacked is undefined\n    stacked === undefined && meta.stack === undefined ? meta.index : '', meta.stack].join('.');\n    if (stacks[key] === undefined) {\n      stacks[key] = {\n        pos: [],\n        neg: []\n      };\n    }\n    return stacks[key];\n  }\n  function stackData(scale, stacks, meta, data) {\n    var opts = scale.options;\n    var stacked = opts.stacked;\n    var stack = getOrCreateStack(stacks, stacked, meta);\n    var pos = stack.pos;\n    var neg = stack.neg;\n    var ilen = data.length;\n    var i, value;\n    for (i = 0; i < ilen; ++i) {\n      value = scale._parseValue(data[i]);\n      if (isNaN(value.min) || isNaN(value.max) || meta.data[i].hidden) {\n        continue;\n      }\n      pos[i] = pos[i] || 0;\n      neg[i] = neg[i] || 0;\n      if (opts.relativePoints) {\n        pos[i] = 100;\n      } else if (value.min < 0 || value.max < 0) {\n        neg[i] += value.min;\n      } else {\n        pos[i] += value.max;\n      }\n    }\n  }\n  function updateMinMax(scale, meta, data) {\n    var ilen = data.length;\n    var i, value;\n    for (i = 0; i < ilen; ++i) {\n      value = scale._parseValue(data[i]);\n      if (isNaN(value.min) || isNaN(value.max) || meta.data[i].hidden) {\n        continue;\n      }\n      scale.min = Math.min(scale.min, value.min);\n      scale.max = Math.max(scale.max, value.max);\n    }\n  }\n  var scale_linear = scale_linearbase.extend({\n    determineDataLimits: function () {\n      var me = this;\n      var opts = me.options;\n      var chart = me.chart;\n      var datasets = chart.data.datasets;\n      var metasets = me._getMatchingVisibleMetas();\n      var hasStacks = opts.stacked;\n      var stacks = {};\n      var ilen = metasets.length;\n      var i, meta, data, values;\n      me.min = Number.POSITIVE_INFINITY;\n      me.max = Number.NEGATIVE_INFINITY;\n      if (hasStacks === undefined) {\n        for (i = 0; !hasStacks && i < ilen; ++i) {\n          meta = metasets[i];\n          hasStacks = meta.stack !== undefined;\n        }\n      }\n      for (i = 0; i < ilen; ++i) {\n        meta = metasets[i];\n        data = datasets[meta.index].data;\n        if (hasStacks) {\n          stackData(me, stacks, meta, data);\n        } else {\n          updateMinMax(me, meta, data);\n        }\n      }\n      helpers$1.each(stacks, function (stackValues) {\n        values = stackValues.pos.concat(stackValues.neg);\n        me.min = Math.min(me.min, helpers$1.min(values));\n        me.max = Math.max(me.max, helpers$1.max(values));\n      });\n      me.min = helpers$1.isFinite(me.min) && !isNaN(me.min) ? me.min : DEFAULT_MIN;\n      me.max = helpers$1.isFinite(me.max) && !isNaN(me.max) ? me.max : DEFAULT_MAX;\n\n      // Common base implementation to handle ticks.min, ticks.max, ticks.beginAtZero\n      me.handleTickRangeOptions();\n    },\n    // Returns the maximum number of ticks based on the scale dimension\n    _computeTickLimit: function () {\n      var me = this;\n      var tickFont;\n      if (me.isHorizontal()) {\n        return Math.ceil(me.width / 40);\n      }\n      tickFont = helpers$1.options._parseFont(me.options.ticks);\n      return Math.ceil(me.height / tickFont.lineHeight);\n    },\n    // Called after the ticks are built. We need\n    handleDirectionalChanges: function () {\n      if (!this.isHorizontal()) {\n        // We are in a vertical orientation. The top value is the highest. So reverse the array\n        this.ticks.reverse();\n      }\n    },\n    getLabelForIndex: function (index, datasetIndex) {\n      return this._getScaleLabel(this.chart.data.datasets[datasetIndex].data[index]);\n    },\n    // Utils\n    getPixelForValue: function (value) {\n      var me = this;\n      return me.getPixelForDecimal((+me.getRightValue(value) - me._startValue) / me._valueRange);\n    },\n    getValueForPixel: function (pixel) {\n      return this._startValue + this.getDecimalForPixel(pixel) * this._valueRange;\n    },\n    getPixelForTick: function (index) {\n      var ticks = this.ticksAsNumbers;\n      if (index < 0 || index > ticks.length - 1) {\n        return null;\n      }\n      return this.getPixelForValue(ticks[index]);\n    }\n  });\n\n  // INTERNAL: static default options, registered in src/index.js\n  var _defaults$1 = defaultConfig$1;\n  scale_linear._defaults = _defaults$1;\n  var valueOrDefault$b = helpers$1.valueOrDefault;\n  var log10 = helpers$1.math.log10;\n\n  /**\r\n   * Generate a set of logarithmic ticks\r\n   * @param generationOptions the options used to generate the ticks\r\n   * @param dataRange the range of the data\r\n   * @returns {number[]} array of tick values\r\n   */\n  function generateTicks$1(generationOptions, dataRange) {\n    var ticks = [];\n    var tickVal = valueOrDefault$b(generationOptions.min, Math.pow(10, Math.floor(log10(dataRange.min))));\n    var endExp = Math.floor(log10(dataRange.max));\n    var endSignificand = Math.ceil(dataRange.max / Math.pow(10, endExp));\n    var exp, significand;\n    if (tickVal === 0) {\n      exp = Math.floor(log10(dataRange.minNotZero));\n      significand = Math.floor(dataRange.minNotZero / Math.pow(10, exp));\n      ticks.push(tickVal);\n      tickVal = significand * Math.pow(10, exp);\n    } else {\n      exp = Math.floor(log10(tickVal));\n      significand = Math.floor(tickVal / Math.pow(10, exp));\n    }\n    var precision = exp < 0 ? Math.pow(10, Math.abs(exp)) : 1;\n    do {\n      ticks.push(tickVal);\n      ++significand;\n      if (significand === 10) {\n        significand = 1;\n        ++exp;\n        precision = exp >= 0 ? 1 : precision;\n      }\n      tickVal = Math.round(significand * Math.pow(10, exp) * precision) / precision;\n    } while (exp < endExp || exp === endExp && significand < endSignificand);\n    var lastTick = valueOrDefault$b(generationOptions.max, tickVal);\n    ticks.push(lastTick);\n    return ticks;\n  }\n  var defaultConfig$2 = {\n    position: 'left',\n    // label settings\n    ticks: {\n      callback: core_ticks.formatters.logarithmic\n    }\n  };\n\n  // TODO(v3): change this to positiveOrDefault\n  function nonNegativeOrDefault(value, defaultValue) {\n    return helpers$1.isFinite(value) && value >= 0 ? value : defaultValue;\n  }\n  var scale_logarithmic = core_scale.extend({\n    determineDataLimits: function () {\n      var me = this;\n      var opts = me.options;\n      var chart = me.chart;\n      var datasets = chart.data.datasets;\n      var isHorizontal = me.isHorizontal();\n      function IDMatches(meta) {\n        return isHorizontal ? meta.xAxisID === me.id : meta.yAxisID === me.id;\n      }\n      var datasetIndex, meta, value, data, i, ilen;\n\n      // Calculate Range\n      me.min = Number.POSITIVE_INFINITY;\n      me.max = Number.NEGATIVE_INFINITY;\n      me.minNotZero = Number.POSITIVE_INFINITY;\n      var hasStacks = opts.stacked;\n      if (hasStacks === undefined) {\n        for (datasetIndex = 0; datasetIndex < datasets.length; datasetIndex++) {\n          meta = chart.getDatasetMeta(datasetIndex);\n          if (chart.isDatasetVisible(datasetIndex) && IDMatches(meta) && meta.stack !== undefined) {\n            hasStacks = true;\n            break;\n          }\n        }\n      }\n      if (opts.stacked || hasStacks) {\n        var valuesPerStack = {};\n        for (datasetIndex = 0; datasetIndex < datasets.length; datasetIndex++) {\n          meta = chart.getDatasetMeta(datasetIndex);\n          var key = [meta.type,\n          // we have a separate stack for stack=undefined datasets when the opts.stacked is undefined\n          opts.stacked === undefined && meta.stack === undefined ? datasetIndex : '', meta.stack].join('.');\n          if (chart.isDatasetVisible(datasetIndex) && IDMatches(meta)) {\n            if (valuesPerStack[key] === undefined) {\n              valuesPerStack[key] = [];\n            }\n            data = datasets[datasetIndex].data;\n            for (i = 0, ilen = data.length; i < ilen; i++) {\n              var values = valuesPerStack[key];\n              value = me._parseValue(data[i]);\n              // invalid, hidden and negative values are ignored\n              if (isNaN(value.min) || isNaN(value.max) || meta.data[i].hidden || value.min < 0 || value.max < 0) {\n                continue;\n              }\n              values[i] = values[i] || 0;\n              values[i] += value.max;\n            }\n          }\n        }\n        helpers$1.each(valuesPerStack, function (valuesForType) {\n          if (valuesForType.length > 0) {\n            var minVal = helpers$1.min(valuesForType);\n            var maxVal = helpers$1.max(valuesForType);\n            me.min = Math.min(me.min, minVal);\n            me.max = Math.max(me.max, maxVal);\n          }\n        });\n      } else {\n        for (datasetIndex = 0; datasetIndex < datasets.length; datasetIndex++) {\n          meta = chart.getDatasetMeta(datasetIndex);\n          if (chart.isDatasetVisible(datasetIndex) && IDMatches(meta)) {\n            data = datasets[datasetIndex].data;\n            for (i = 0, ilen = data.length; i < ilen; i++) {\n              value = me._parseValue(data[i]);\n              // invalid, hidden and negative values are ignored\n              if (isNaN(value.min) || isNaN(value.max) || meta.data[i].hidden || value.min < 0 || value.max < 0) {\n                continue;\n              }\n              me.min = Math.min(value.min, me.min);\n              me.max = Math.max(value.max, me.max);\n              if (value.min !== 0) {\n                me.minNotZero = Math.min(value.min, me.minNotZero);\n              }\n            }\n          }\n        }\n      }\n      me.min = helpers$1.isFinite(me.min) ? me.min : null;\n      me.max = helpers$1.isFinite(me.max) ? me.max : null;\n      me.minNotZero = helpers$1.isFinite(me.minNotZero) ? me.minNotZero : null;\n\n      // Common base implementation to handle ticks.min, ticks.max\n      this.handleTickRangeOptions();\n    },\n    handleTickRangeOptions: function () {\n      var me = this;\n      var tickOpts = me.options.ticks;\n      var DEFAULT_MIN = 1;\n      var DEFAULT_MAX = 10;\n      me.min = nonNegativeOrDefault(tickOpts.min, me.min);\n      me.max = nonNegativeOrDefault(tickOpts.max, me.max);\n      if (me.min === me.max) {\n        if (me.min !== 0 && me.min !== null) {\n          me.min = Math.pow(10, Math.floor(log10(me.min)) - 1);\n          me.max = Math.pow(10, Math.floor(log10(me.max)) + 1);\n        } else {\n          me.min = DEFAULT_MIN;\n          me.max = DEFAULT_MAX;\n        }\n      }\n      if (me.min === null) {\n        me.min = Math.pow(10, Math.floor(log10(me.max)) - 1);\n      }\n      if (me.max === null) {\n        me.max = me.min !== 0 ? Math.pow(10, Math.floor(log10(me.min)) + 1) : DEFAULT_MAX;\n      }\n      if (me.minNotZero === null) {\n        if (me.min > 0) {\n          me.minNotZero = me.min;\n        } else if (me.max < 1) {\n          me.minNotZero = Math.pow(10, Math.floor(log10(me.max)));\n        } else {\n          me.minNotZero = DEFAULT_MIN;\n        }\n      }\n    },\n    buildTicks: function () {\n      var me = this;\n      var tickOpts = me.options.ticks;\n      var reverse = !me.isHorizontal();\n      var generationOptions = {\n        min: nonNegativeOrDefault(tickOpts.min),\n        max: nonNegativeOrDefault(tickOpts.max)\n      };\n      var ticks = me.ticks = generateTicks$1(generationOptions, me);\n\n      // At this point, we need to update our max and min given the tick values since we have expanded the\n      // range of the scale\n      me.max = helpers$1.max(ticks);\n      me.min = helpers$1.min(ticks);\n      if (tickOpts.reverse) {\n        reverse = !reverse;\n        me.start = me.max;\n        me.end = me.min;\n      } else {\n        me.start = me.min;\n        me.end = me.max;\n      }\n      if (reverse) {\n        ticks.reverse();\n      }\n    },\n    convertTicksToLabels: function () {\n      this.tickValues = this.ticks.slice();\n      core_scale.prototype.convertTicksToLabels.call(this);\n    },\n    // Get the correct tooltip label\n    getLabelForIndex: function (index, datasetIndex) {\n      return this._getScaleLabel(this.chart.data.datasets[datasetIndex].data[index]);\n    },\n    getPixelForTick: function (index) {\n      var ticks = this.tickValues;\n      if (index < 0 || index > ticks.length - 1) {\n        return null;\n      }\n      return this.getPixelForValue(ticks[index]);\n    },\n    /**\r\n     * Returns the value of the first tick.\r\n     * @param {number} value - The minimum not zero value.\r\n     * @return {number} The first tick value.\r\n     * @private\r\n     */\n    _getFirstTickValue: function (value) {\n      var exp = Math.floor(log10(value));\n      var significand = Math.floor(value / Math.pow(10, exp));\n      return significand * Math.pow(10, exp);\n    },\n    _configure: function () {\n      var me = this;\n      var start = me.min;\n      var offset = 0;\n      core_scale.prototype._configure.call(me);\n      if (start === 0) {\n        start = me._getFirstTickValue(me.minNotZero);\n        offset = valueOrDefault$b(me.options.ticks.fontSize, core_defaults.global.defaultFontSize) / me._length;\n      }\n      me._startValue = log10(start);\n      me._valueOffset = offset;\n      me._valueRange = (log10(me.max) - log10(start)) / (1 - offset);\n    },\n    getPixelForValue: function (value) {\n      var me = this;\n      var decimal = 0;\n      value = +me.getRightValue(value);\n      if (value > me.min && value > 0) {\n        decimal = (log10(value) - me._startValue) / me._valueRange + me._valueOffset;\n      }\n      return me.getPixelForDecimal(decimal);\n    },\n    getValueForPixel: function (pixel) {\n      var me = this;\n      var decimal = me.getDecimalForPixel(pixel);\n      return decimal === 0 && me.min === 0 ? 0 : Math.pow(10, me._startValue + (decimal - me._valueOffset) * me._valueRange);\n    }\n  });\n\n  // INTERNAL: static default options, registered in src/index.js\n  var _defaults$2 = defaultConfig$2;\n  scale_logarithmic._defaults = _defaults$2;\n  var valueOrDefault$c = helpers$1.valueOrDefault;\n  var valueAtIndexOrDefault$1 = helpers$1.valueAtIndexOrDefault;\n  var resolve$4 = helpers$1.options.resolve;\n  var defaultConfig$3 = {\n    display: true,\n    // Boolean - Whether to animate scaling the chart from the centre\n    animate: true,\n    position: 'chartArea',\n    angleLines: {\n      display: true,\n      color: 'rgba(0,0,0,0.1)',\n      lineWidth: 1,\n      borderDash: [],\n      borderDashOffset: 0.0\n    },\n    gridLines: {\n      circular: false\n    },\n    // label settings\n    ticks: {\n      // Boolean - Show a backdrop to the scale label\n      showLabelBackdrop: true,\n      // String - The colour of the label backdrop\n      backdropColor: 'rgba(255,255,255,0.75)',\n      // Number - The backdrop padding above & below the label in pixels\n      backdropPaddingY: 2,\n      // Number - The backdrop padding to the side of the label in pixels\n      backdropPaddingX: 2,\n      callback: core_ticks.formatters.linear\n    },\n    pointLabels: {\n      // Boolean - if true, show point labels\n      display: true,\n      // Number - Point label font size in pixels\n      fontSize: 10,\n      // Function - Used to convert point labels\n      callback: function (label) {\n        return label;\n      }\n    }\n  };\n  function getTickBackdropHeight(opts) {\n    var tickOpts = opts.ticks;\n    if (tickOpts.display && opts.display) {\n      return valueOrDefault$c(tickOpts.fontSize, core_defaults.global.defaultFontSize) + tickOpts.backdropPaddingY * 2;\n    }\n    return 0;\n  }\n  function measureLabelSize(ctx, lineHeight, label) {\n    if (helpers$1.isArray(label)) {\n      return {\n        w: helpers$1.longestText(ctx, ctx.font, label),\n        h: label.length * lineHeight\n      };\n    }\n    return {\n      w: ctx.measureText(label).width,\n      h: lineHeight\n    };\n  }\n  function determineLimits(angle, pos, size, min, max) {\n    if (angle === min || angle === max) {\n      return {\n        start: pos - size / 2,\n        end: pos + size / 2\n      };\n    } else if (angle < min || angle > max) {\n      return {\n        start: pos - size,\n        end: pos\n      };\n    }\n    return {\n      start: pos,\n      end: pos + size\n    };\n  }\n\n  /**\r\n   * Helper function to fit a radial linear scale with point labels\r\n   */\n  function fitWithPointLabels(scale) {\n    // Right, this is really confusing and there is a lot of maths going on here\n    // The gist of the problem is here: https://gist.github.com/nnnick/696cc9c55f4b0beb8fe9\n    //\n    // Reaction: https://dl.dropboxusercontent.com/u/34601363/toomuchscience.gif\n    //\n    // Solution:\n    //\n    // We assume the radius of the polygon is half the size of the canvas at first\n    // at each index we check if the text overlaps.\n    //\n    // Where it does, we store that angle and that index.\n    //\n    // After finding the largest index and angle we calculate how much we need to remove\n    // from the shape radius to move the point inwards by that x.\n    //\n    // We average the left and right distances to get the maximum shape radius that can fit in the box\n    // along with labels.\n    //\n    // Once we have that, we can find the centre point for the chart, by taking the x text protrusion\n    // on each side, removing that from the size, halving it and adding the left x protrusion width.\n    //\n    // This will mean we have a shape fitted to the canvas, as large as it can be with the labels\n    // and position it in the most space efficient manner\n    //\n    // https://dl.dropboxusercontent.com/u/34601363/yeahscience.gif\n\n    var plFont = helpers$1.options._parseFont(scale.options.pointLabels);\n\n    // Get maximum radius of the polygon. Either half the height (minus the text width) or half the width.\n    // Use this to calculate the offset + change. - Make sure L/R protrusion is at least 0 to stop issues with centre points\n    var furthestLimits = {\n      l: 0,\n      r: scale.width,\n      t: 0,\n      b: scale.height - scale.paddingTop\n    };\n    var furthestAngles = {};\n    var i, textSize, pointPosition;\n    scale.ctx.font = plFont.string;\n    scale._pointLabelSizes = [];\n    var valueCount = scale.chart.data.labels.length;\n    for (i = 0; i < valueCount; i++) {\n      pointPosition = scale.getPointPosition(i, scale.drawingArea + 5);\n      textSize = measureLabelSize(scale.ctx, plFont.lineHeight, scale.pointLabels[i]);\n      scale._pointLabelSizes[i] = textSize;\n\n      // Add quarter circle to make degree 0 mean top of circle\n      var angleRadians = scale.getIndexAngle(i);\n      var angle = helpers$1.toDegrees(angleRadians) % 360;\n      var hLimits = determineLimits(angle, pointPosition.x, textSize.w, 0, 180);\n      var vLimits = determineLimits(angle, pointPosition.y, textSize.h, 90, 270);\n      if (hLimits.start < furthestLimits.l) {\n        furthestLimits.l = hLimits.start;\n        furthestAngles.l = angleRadians;\n      }\n      if (hLimits.end > furthestLimits.r) {\n        furthestLimits.r = hLimits.end;\n        furthestAngles.r = angleRadians;\n      }\n      if (vLimits.start < furthestLimits.t) {\n        furthestLimits.t = vLimits.start;\n        furthestAngles.t = angleRadians;\n      }\n      if (vLimits.end > furthestLimits.b) {\n        furthestLimits.b = vLimits.end;\n        furthestAngles.b = angleRadians;\n      }\n    }\n    scale.setReductions(scale.drawingArea, furthestLimits, furthestAngles);\n  }\n  function getTextAlignForAngle(angle) {\n    if (angle === 0 || angle === 180) {\n      return 'center';\n    } else if (angle < 180) {\n      return 'left';\n    }\n    return 'right';\n  }\n  function fillText(ctx, text, position, lineHeight) {\n    var y = position.y + lineHeight / 2;\n    var i, ilen;\n    if (helpers$1.isArray(text)) {\n      for (i = 0, ilen = text.length; i < ilen; ++i) {\n        ctx.fillText(text[i], position.x, y);\n        y += lineHeight;\n      }\n    } else {\n      ctx.fillText(text, position.x, y);\n    }\n  }\n  function adjustPointPositionForLabelHeight(angle, textSize, position) {\n    if (angle === 90 || angle === 270) {\n      position.y -= textSize.h / 2;\n    } else if (angle > 270 || angle < 90) {\n      position.y -= textSize.h;\n    }\n  }\n  function drawPointLabels(scale) {\n    var ctx = scale.ctx;\n    var opts = scale.options;\n    var pointLabelOpts = opts.pointLabels;\n    var tickBackdropHeight = getTickBackdropHeight(opts);\n    var outerDistance = scale.getDistanceFromCenterForValue(opts.ticks.reverse ? scale.min : scale.max);\n    var plFont = helpers$1.options._parseFont(pointLabelOpts);\n    ctx.save();\n    ctx.font = plFont.string;\n    ctx.textBaseline = 'middle';\n    for (var i = scale.chart.data.labels.length - 1; i >= 0; i--) {\n      // Extra pixels out for some label spacing\n      var extra = i === 0 ? tickBackdropHeight / 2 : 0;\n      var pointLabelPosition = scale.getPointPosition(i, outerDistance + extra + 5);\n\n      // Keep this in loop since we may support array properties here\n      var pointLabelFontColor = valueAtIndexOrDefault$1(pointLabelOpts.fontColor, i, core_defaults.global.defaultFontColor);\n      ctx.fillStyle = pointLabelFontColor;\n      var angleRadians = scale.getIndexAngle(i);\n      var angle = helpers$1.toDegrees(angleRadians);\n      ctx.textAlign = getTextAlignForAngle(angle);\n      adjustPointPositionForLabelHeight(angle, scale._pointLabelSizes[i], pointLabelPosition);\n      fillText(ctx, scale.pointLabels[i], pointLabelPosition, plFont.lineHeight);\n    }\n    ctx.restore();\n  }\n  function drawRadiusLine(scale, gridLineOpts, radius, index) {\n    var ctx = scale.ctx;\n    var circular = gridLineOpts.circular;\n    var valueCount = scale.chart.data.labels.length;\n    var lineColor = valueAtIndexOrDefault$1(gridLineOpts.color, index - 1);\n    var lineWidth = valueAtIndexOrDefault$1(gridLineOpts.lineWidth, index - 1);\n    var pointPosition;\n    if (!circular && !valueCount || !lineColor || !lineWidth) {\n      return;\n    }\n    ctx.save();\n    ctx.strokeStyle = lineColor;\n    ctx.lineWidth = lineWidth;\n    if (ctx.setLineDash) {\n      ctx.setLineDash(gridLineOpts.borderDash || []);\n      ctx.lineDashOffset = gridLineOpts.borderDashOffset || 0.0;\n    }\n    ctx.beginPath();\n    if (circular) {\n      // Draw circular arcs between the points\n      ctx.arc(scale.xCenter, scale.yCenter, radius, 0, Math.PI * 2);\n    } else {\n      // Draw straight lines connecting each index\n      pointPosition = scale.getPointPosition(0, radius);\n      ctx.moveTo(pointPosition.x, pointPosition.y);\n      for (var i = 1; i < valueCount; i++) {\n        pointPosition = scale.getPointPosition(i, radius);\n        ctx.lineTo(pointPosition.x, pointPosition.y);\n      }\n    }\n    ctx.closePath();\n    ctx.stroke();\n    ctx.restore();\n  }\n  function numberOrZero(param) {\n    return helpers$1.isNumber(param) ? param : 0;\n  }\n  var scale_radialLinear = scale_linearbase.extend({\n    setDimensions: function () {\n      var me = this;\n\n      // Set the unconstrained dimension before label rotation\n      me.width = me.maxWidth;\n      me.height = me.maxHeight;\n      me.paddingTop = getTickBackdropHeight(me.options) / 2;\n      me.xCenter = Math.floor(me.width / 2);\n      me.yCenter = Math.floor((me.height - me.paddingTop) / 2);\n      me.drawingArea = Math.min(me.height - me.paddingTop, me.width) / 2;\n    },\n    determineDataLimits: function () {\n      var me = this;\n      var chart = me.chart;\n      var min = Number.POSITIVE_INFINITY;\n      var max = Number.NEGATIVE_INFINITY;\n      helpers$1.each(chart.data.datasets, function (dataset, datasetIndex) {\n        if (chart.isDatasetVisible(datasetIndex)) {\n          var meta = chart.getDatasetMeta(datasetIndex);\n          helpers$1.each(dataset.data, function (rawValue, index) {\n            var value = +me.getRightValue(rawValue);\n            if (isNaN(value) || meta.data[index].hidden) {\n              return;\n            }\n            min = Math.min(value, min);\n            max = Math.max(value, max);\n          });\n        }\n      });\n      me.min = min === Number.POSITIVE_INFINITY ? 0 : min;\n      me.max = max === Number.NEGATIVE_INFINITY ? 0 : max;\n\n      // Common base implementation to handle ticks.min, ticks.max, ticks.beginAtZero\n      me.handleTickRangeOptions();\n    },\n    // Returns the maximum number of ticks based on the scale dimension\n    _computeTickLimit: function () {\n      return Math.ceil(this.drawingArea / getTickBackdropHeight(this.options));\n    },\n    convertTicksToLabels: function () {\n      var me = this;\n      scale_linearbase.prototype.convertTicksToLabels.call(me);\n\n      // Point labels\n      me.pointLabels = me.chart.data.labels.map(function () {\n        var label = helpers$1.callback(me.options.pointLabels.callback, arguments, me);\n        return label || label === 0 ? label : '';\n      });\n    },\n    getLabelForIndex: function (index, datasetIndex) {\n      return +this.getRightValue(this.chart.data.datasets[datasetIndex].data[index]);\n    },\n    fit: function () {\n      var me = this;\n      var opts = me.options;\n      if (opts.display && opts.pointLabels.display) {\n        fitWithPointLabels(me);\n      } else {\n        me.setCenterPoint(0, 0, 0, 0);\n      }\n    },\n    /**\r\n     * Set radius reductions and determine new radius and center point\r\n     * @private\r\n     */\n    setReductions: function (largestPossibleRadius, furthestLimits, furthestAngles) {\n      var me = this;\n      var radiusReductionLeft = furthestLimits.l / Math.sin(furthestAngles.l);\n      var radiusReductionRight = Math.max(furthestLimits.r - me.width, 0) / Math.sin(furthestAngles.r);\n      var radiusReductionTop = -furthestLimits.t / Math.cos(furthestAngles.t);\n      var radiusReductionBottom = -Math.max(furthestLimits.b - (me.height - me.paddingTop), 0) / Math.cos(furthestAngles.b);\n      radiusReductionLeft = numberOrZero(radiusReductionLeft);\n      radiusReductionRight = numberOrZero(radiusReductionRight);\n      radiusReductionTop = numberOrZero(radiusReductionTop);\n      radiusReductionBottom = numberOrZero(radiusReductionBottom);\n      me.drawingArea = Math.min(Math.floor(largestPossibleRadius - (radiusReductionLeft + radiusReductionRight) / 2), Math.floor(largestPossibleRadius - (radiusReductionTop + radiusReductionBottom) / 2));\n      me.setCenterPoint(radiusReductionLeft, radiusReductionRight, radiusReductionTop, radiusReductionBottom);\n    },\n    setCenterPoint: function (leftMovement, rightMovement, topMovement, bottomMovement) {\n      var me = this;\n      var maxRight = me.width - rightMovement - me.drawingArea;\n      var maxLeft = leftMovement + me.drawingArea;\n      var maxTop = topMovement + me.drawingArea;\n      var maxBottom = me.height - me.paddingTop - bottomMovement - me.drawingArea;\n      me.xCenter = Math.floor((maxLeft + maxRight) / 2 + me.left);\n      me.yCenter = Math.floor((maxTop + maxBottom) / 2 + me.top + me.paddingTop);\n    },\n    getIndexAngle: function (index) {\n      var chart = this.chart;\n      var angleMultiplier = 360 / chart.data.labels.length;\n      var options = chart.options || {};\n      var startAngle = options.startAngle || 0;\n\n      // Start from the top instead of right, so remove a quarter of the circle\n      var angle = (index * angleMultiplier + startAngle) % 360;\n      return (angle < 0 ? angle + 360 : angle) * Math.PI * 2 / 360;\n    },\n    getDistanceFromCenterForValue: function (value) {\n      var me = this;\n      if (helpers$1.isNullOrUndef(value)) {\n        return NaN;\n      }\n\n      // Take into account half font size + the yPadding of the top value\n      var scalingFactor = me.drawingArea / (me.max - me.min);\n      if (me.options.ticks.reverse) {\n        return (me.max - value) * scalingFactor;\n      }\n      return (value - me.min) * scalingFactor;\n    },\n    getPointPosition: function (index, distanceFromCenter) {\n      var me = this;\n      var thisAngle = me.getIndexAngle(index) - Math.PI / 2;\n      return {\n        x: Math.cos(thisAngle) * distanceFromCenter + me.xCenter,\n        y: Math.sin(thisAngle) * distanceFromCenter + me.yCenter\n      };\n    },\n    getPointPositionForValue: function (index, value) {\n      return this.getPointPosition(index, this.getDistanceFromCenterForValue(value));\n    },\n    getBasePosition: function (index) {\n      var me = this;\n      var min = me.min;\n      var max = me.max;\n      return me.getPointPositionForValue(index || 0, me.beginAtZero ? 0 : min < 0 && max < 0 ? max : min > 0 && max > 0 ? min : 0);\n    },\n    /**\r\n     * @private\r\n     */\n    _drawGrid: function () {\n      var me = this;\n      var ctx = me.ctx;\n      var opts = me.options;\n      var gridLineOpts = opts.gridLines;\n      var angleLineOpts = opts.angleLines;\n      var lineWidth = valueOrDefault$c(angleLineOpts.lineWidth, gridLineOpts.lineWidth);\n      var lineColor = valueOrDefault$c(angleLineOpts.color, gridLineOpts.color);\n      var i, offset, position;\n      if (opts.pointLabels.display) {\n        drawPointLabels(me);\n      }\n      if (gridLineOpts.display) {\n        helpers$1.each(me.ticks, function (label, index) {\n          if (index !== 0) {\n            offset = me.getDistanceFromCenterForValue(me.ticksAsNumbers[index]);\n            drawRadiusLine(me, gridLineOpts, offset, index);\n          }\n        });\n      }\n      if (angleLineOpts.display && lineWidth && lineColor) {\n        ctx.save();\n        ctx.lineWidth = lineWidth;\n        ctx.strokeStyle = lineColor;\n        if (ctx.setLineDash) {\n          ctx.setLineDash(resolve$4([angleLineOpts.borderDash, gridLineOpts.borderDash, []]));\n          ctx.lineDashOffset = resolve$4([angleLineOpts.borderDashOffset, gridLineOpts.borderDashOffset, 0.0]);\n        }\n        for (i = me.chart.data.labels.length - 1; i >= 0; i--) {\n          offset = me.getDistanceFromCenterForValue(opts.ticks.reverse ? me.min : me.max);\n          position = me.getPointPosition(i, offset);\n          ctx.beginPath();\n          ctx.moveTo(me.xCenter, me.yCenter);\n          ctx.lineTo(position.x, position.y);\n          ctx.stroke();\n        }\n        ctx.restore();\n      }\n    },\n    /**\r\n     * @private\r\n     */\n    _drawLabels: function () {\n      var me = this;\n      var ctx = me.ctx;\n      var opts = me.options;\n      var tickOpts = opts.ticks;\n      if (!tickOpts.display) {\n        return;\n      }\n      var startAngle = me.getIndexAngle(0);\n      var tickFont = helpers$1.options._parseFont(tickOpts);\n      var tickFontColor = valueOrDefault$c(tickOpts.fontColor, core_defaults.global.defaultFontColor);\n      var offset, width;\n      ctx.save();\n      ctx.font = tickFont.string;\n      ctx.translate(me.xCenter, me.yCenter);\n      ctx.rotate(startAngle);\n      ctx.textAlign = 'center';\n      ctx.textBaseline = 'middle';\n      helpers$1.each(me.ticks, function (label, index) {\n        if (index === 0 && !tickOpts.reverse) {\n          return;\n        }\n        offset = me.getDistanceFromCenterForValue(me.ticksAsNumbers[index]);\n        if (tickOpts.showLabelBackdrop) {\n          width = ctx.measureText(label).width;\n          ctx.fillStyle = tickOpts.backdropColor;\n          ctx.fillRect(-width / 2 - tickOpts.backdropPaddingX, -offset - tickFont.size / 2 - tickOpts.backdropPaddingY, width + tickOpts.backdropPaddingX * 2, tickFont.size + tickOpts.backdropPaddingY * 2);\n        }\n        ctx.fillStyle = tickFontColor;\n        ctx.fillText(label, 0, -offset);\n      });\n      ctx.restore();\n    },\n    /**\r\n     * @private\r\n     */\n    _drawTitle: helpers$1.noop\n  });\n\n  // INTERNAL: static default options, registered in src/index.js\n  var _defaults$3 = defaultConfig$3;\n  scale_radialLinear._defaults = _defaults$3;\n  var deprecated$1 = helpers$1._deprecated;\n  var resolve$5 = helpers$1.options.resolve;\n  var valueOrDefault$d = helpers$1.valueOrDefault;\n\n  // Integer constants are from the ES6 spec.\n  var MIN_INTEGER = Number.MIN_SAFE_INTEGER || -9007199254740991;\n  var MAX_INTEGER = Number.MAX_SAFE_INTEGER || 9007199254740991;\n  var INTERVALS = {\n    millisecond: {\n      common: true,\n      size: 1,\n      steps: 1000\n    },\n    second: {\n      common: true,\n      size: 1000,\n      steps: 60\n    },\n    minute: {\n      common: true,\n      size: 60000,\n      steps: 60\n    },\n    hour: {\n      common: true,\n      size: 3600000,\n      steps: 24\n    },\n    day: {\n      common: true,\n      size: 86400000,\n      steps: 30\n    },\n    week: {\n      common: false,\n      size: 604800000,\n      steps: 4\n    },\n    month: {\n      common: true,\n      size: 2.628e9,\n      steps: 12\n    },\n    quarter: {\n      common: false,\n      size: 7.884e9,\n      steps: 4\n    },\n    year: {\n      common: true,\n      size: 3.154e10\n    }\n  };\n  var UNITS = Object.keys(INTERVALS);\n  function sorter(a, b) {\n    return a - b;\n  }\n  function arrayUnique(items) {\n    var hash = {};\n    var out = [];\n    var i, ilen, item;\n    for (i = 0, ilen = items.length; i < ilen; ++i) {\n      item = items[i];\n      if (!hash[item]) {\n        hash[item] = true;\n        out.push(item);\n      }\n    }\n    return out;\n  }\n  function getMin(options) {\n    return helpers$1.valueOrDefault(options.time.min, options.ticks.min);\n  }\n  function getMax(options) {\n    return helpers$1.valueOrDefault(options.time.max, options.ticks.max);\n  }\n\n  /**\r\n   * Returns an array of {time, pos} objects used to interpolate a specific `time` or position\r\n   * (`pos`) on the scale, by searching entries before and after the requested value. `pos` is\r\n   * a decimal between 0 and 1: 0 being the start of the scale (left or top) and 1 the other\r\n   * extremity (left + width or top + height). Note that it would be more optimized to directly\r\n   * store pre-computed pixels, but the scale dimensions are not guaranteed at the time we need\r\n   * to create the lookup table. The table ALWAYS contains at least two items: min and max.\r\n   *\r\n   * @param {number[]} timestamps - timestamps sorted from lowest to highest.\r\n   * @param {string} distribution - If 'linear', timestamps will be spread linearly along the min\r\n   * and max range, so basically, the table will contains only two items: {min, 0} and {max, 1}.\r\n   * If 'series', timestamps will be positioned at the same distance from each other. In this\r\n   * case, only timestamps that break the time linearity are registered, meaning that in the\r\n   * best case, all timestamps are linear, the table contains only min and max.\r\n   */\n  function buildLookupTable(timestamps, min, max, distribution) {\n    if (distribution === 'linear' || !timestamps.length) {\n      return [{\n        time: min,\n        pos: 0\n      }, {\n        time: max,\n        pos: 1\n      }];\n    }\n    var table = [];\n    var items = [min];\n    var i, ilen, prev, curr, next;\n    for (i = 0, ilen = timestamps.length; i < ilen; ++i) {\n      curr = timestamps[i];\n      if (curr > min && curr < max) {\n        items.push(curr);\n      }\n    }\n    items.push(max);\n    for (i = 0, ilen = items.length; i < ilen; ++i) {\n      next = items[i + 1];\n      prev = items[i - 1];\n      curr = items[i];\n\n      // only add points that breaks the scale linearity\n      if (prev === undefined || next === undefined || Math.round((next + prev) / 2) !== curr) {\n        table.push({\n          time: curr,\n          pos: i / (ilen - 1)\n        });\n      }\n    }\n    return table;\n  }\n\n  // @see adapted from https://www.anujgakhar.com/2014/03/01/binary-search-in-javascript/\n  function lookup(table, key, value) {\n    var lo = 0;\n    var hi = table.length - 1;\n    var mid, i0, i1;\n    while (lo >= 0 && lo <= hi) {\n      mid = lo + hi >> 1;\n      i0 = table[mid - 1] || null;\n      i1 = table[mid];\n      if (!i0) {\n        // given value is outside table (before first item)\n        return {\n          lo: null,\n          hi: i1\n        };\n      } else if (i1[key] < value) {\n        lo = mid + 1;\n      } else if (i0[key] > value) {\n        hi = mid - 1;\n      } else {\n        return {\n          lo: i0,\n          hi: i1\n        };\n      }\n    }\n\n    // given value is outside table (after last item)\n    return {\n      lo: i1,\n      hi: null\n    };\n  }\n\n  /**\r\n   * Linearly interpolates the given source `value` using the table items `skey` values and\r\n   * returns the associated `tkey` value. For example, interpolate(table, 'time', 42, 'pos')\r\n   * returns the position for a timestamp equal to 42. If value is out of bounds, values at\r\n   * index [0, 1] or [n - 1, n] are used for the interpolation.\r\n   */\n  function interpolate$1(table, skey, sval, tkey) {\n    var range = lookup(table, skey, sval);\n\n    // Note: the lookup table ALWAYS contains at least 2 items (min and max)\n    var prev = !range.lo ? table[0] : !range.hi ? table[table.length - 2] : range.lo;\n    var next = !range.lo ? table[1] : !range.hi ? table[table.length - 1] : range.hi;\n    var span = next[skey] - prev[skey];\n    var ratio = span ? (sval - prev[skey]) / span : 0;\n    var offset = (next[tkey] - prev[tkey]) * ratio;\n    return prev[tkey] + offset;\n  }\n  function toTimestamp(scale, input) {\n    var adapter = scale._adapter;\n    var options = scale.options.time;\n    var parser = options.parser;\n    var format = parser || options.format;\n    var value = input;\n    if (typeof parser === 'function') {\n      value = parser(value);\n    }\n\n    // Only parse if its not a timestamp already\n    if (!helpers$1.isFinite(value)) {\n      value = typeof format === 'string' ? adapter.parse(value, format) : adapter.parse(value);\n    }\n    if (value !== null) {\n      return +value;\n    }\n\n    // Labels are in an incompatible format and no `parser` has been provided.\n    // The user might still use the deprecated `format` option for parsing.\n    if (!parser && typeof format === 'function') {\n      value = format(input);\n\n      // `format` could return something else than a timestamp, if so, parse it\n      if (!helpers$1.isFinite(value)) {\n        value = adapter.parse(value);\n      }\n    }\n    return value;\n  }\n  function parse(scale, input) {\n    if (helpers$1.isNullOrUndef(input)) {\n      return null;\n    }\n    var options = scale.options.time;\n    var value = toTimestamp(scale, scale.getRightValue(input));\n    if (value === null) {\n      return value;\n    }\n    if (options.round) {\n      value = +scale._adapter.startOf(value, options.round);\n    }\n    return value;\n  }\n\n  /**\r\n   * Figures out what unit results in an appropriate number of auto-generated ticks\r\n   */\n  function determineUnitForAutoTicks(minUnit, min, max, capacity) {\n    var ilen = UNITS.length;\n    var i, interval, factor;\n    for (i = UNITS.indexOf(minUnit); i < ilen - 1; ++i) {\n      interval = INTERVALS[UNITS[i]];\n      factor = interval.steps ? interval.steps : MAX_INTEGER;\n      if (interval.common && Math.ceil((max - min) / (factor * interval.size)) <= capacity) {\n        return UNITS[i];\n      }\n    }\n    return UNITS[ilen - 1];\n  }\n\n  /**\r\n   * Figures out what unit to format a set of ticks with\r\n   */\n  function determineUnitForFormatting(scale, numTicks, minUnit, min, max) {\n    var i, unit;\n    for (i = UNITS.length - 1; i >= UNITS.indexOf(minUnit); i--) {\n      unit = UNITS[i];\n      if (INTERVALS[unit].common && scale._adapter.diff(max, min, unit) >= numTicks - 1) {\n        return unit;\n      }\n    }\n    return UNITS[minUnit ? UNITS.indexOf(minUnit) : 0];\n  }\n  function determineMajorUnit(unit) {\n    for (var i = UNITS.indexOf(unit) + 1, ilen = UNITS.length; i < ilen; ++i) {\n      if (INTERVALS[UNITS[i]].common) {\n        return UNITS[i];\n      }\n    }\n  }\n\n  /**\r\n   * Generates a maximum of `capacity` timestamps between min and max, rounded to the\r\n   * `minor` unit using the given scale time `options`.\r\n   * Important: this method can return ticks outside the min and max range, it's the\r\n   * responsibility of the calling code to clamp values if needed.\r\n   */\n  function generate(scale, min, max, capacity) {\n    var adapter = scale._adapter;\n    var options = scale.options;\n    var timeOpts = options.time;\n    var minor = timeOpts.unit || determineUnitForAutoTicks(timeOpts.minUnit, min, max, capacity);\n    var stepSize = resolve$5([timeOpts.stepSize, timeOpts.unitStepSize, 1]);\n    var weekday = minor === 'week' ? timeOpts.isoWeekday : false;\n    var first = min;\n    var ticks = [];\n    var time;\n\n    // For 'week' unit, handle the first day of week option\n    if (weekday) {\n      first = +adapter.startOf(first, 'isoWeek', weekday);\n    }\n\n    // Align first ticks on unit\n    first = +adapter.startOf(first, weekday ? 'day' : minor);\n\n    // Prevent browser from freezing in case user options request millions of milliseconds\n    if (adapter.diff(max, min, minor) > 100000 * stepSize) {\n      throw min + ' and ' + max + ' are too far apart with stepSize of ' + stepSize + ' ' + minor;\n    }\n    for (time = first; time < max; time = +adapter.add(time, stepSize, minor)) {\n      ticks.push(time);\n    }\n    if (time === max || options.bounds === 'ticks') {\n      ticks.push(time);\n    }\n    return ticks;\n  }\n\n  /**\r\n   * Returns the start and end offsets from edges in the form of {start, end}\r\n   * where each value is a relative width to the scale and ranges between 0 and 1.\r\n   * They add extra margins on the both sides by scaling down the original scale.\r\n   * Offsets are added when the `offset` option is true.\r\n   */\n  function computeOffsets(table, ticks, min, max, options) {\n    var start = 0;\n    var end = 0;\n    var first, last;\n    if (options.offset && ticks.length) {\n      first = interpolate$1(table, 'time', ticks[0], 'pos');\n      if (ticks.length === 1) {\n        start = 1 - first;\n      } else {\n        start = (interpolate$1(table, 'time', ticks[1], 'pos') - first) / 2;\n      }\n      last = interpolate$1(table, 'time', ticks[ticks.length - 1], 'pos');\n      if (ticks.length === 1) {\n        end = last;\n      } else {\n        end = (last - interpolate$1(table, 'time', ticks[ticks.length - 2], 'pos')) / 2;\n      }\n    }\n    return {\n      start: start,\n      end: end,\n      factor: 1 / (start + 1 + end)\n    };\n  }\n  function setMajorTicks(scale, ticks, map, majorUnit) {\n    var adapter = scale._adapter;\n    var first = +adapter.startOf(ticks[0].value, majorUnit);\n    var last = ticks[ticks.length - 1].value;\n    var major, index;\n    for (major = first; major <= last; major = +adapter.add(major, 1, majorUnit)) {\n      index = map[major];\n      if (index >= 0) {\n        ticks[index].major = true;\n      }\n    }\n    return ticks;\n  }\n  function ticksFromTimestamps(scale, values, majorUnit) {\n    var ticks = [];\n    var map = {};\n    var ilen = values.length;\n    var i, value;\n    for (i = 0; i < ilen; ++i) {\n      value = values[i];\n      map[value] = i;\n      ticks.push({\n        value: value,\n        major: false\n      });\n    }\n\n    // We set the major ticks separately from the above loop because calling startOf for every tick\n    // is expensive when there is a large number of ticks\n    return ilen === 0 || !majorUnit ? ticks : setMajorTicks(scale, ticks, map, majorUnit);\n  }\n  var defaultConfig$4 = {\n    position: 'bottom',\n    /**\r\n     * Data distribution along the scale:\r\n     * - 'linear': data are spread according to their time (distances can vary),\r\n     * - 'series': data are spread at the same distance from each other.\r\n     * @see https://github.com/chartjs/Chart.js/pull/4507\r\n     * @since 2.7.0\r\n     */\n    distribution: 'linear',\n    /**\r\n     * Scale boundary strategy (bypassed by min/max time options)\r\n     * - `data`: make sure data are fully visible, ticks outside are removed\r\n     * - `ticks`: make sure ticks are fully visible, data outside are truncated\r\n     * @see https://github.com/chartjs/Chart.js/pull/4556\r\n     * @since 2.7.0\r\n     */\n    bounds: 'data',\n    adapters: {},\n    time: {\n      parser: false,\n      // false == a pattern string from https://momentjs.com/docs/#/parsing/string-format/ or a custom callback that converts its argument to a moment\n      unit: false,\n      // false == automatic or override with week, month, year, etc.\n      round: false,\n      // none, or override with week, month, year, etc.\n      displayFormat: false,\n      // DEPRECATED\n      isoWeekday: false,\n      // override week start day - see https://momentjs.com/docs/#/get-set/iso-weekday/\n      minUnit: 'millisecond',\n      displayFormats: {}\n    },\n    ticks: {\n      autoSkip: false,\n      /**\r\n       * Ticks generation input values:\r\n       * - 'auto': generates \"optimal\" ticks based on scale size and time options.\r\n       * - 'data': generates ticks from data (including labels from data {t|x|y} objects).\r\n       * - 'labels': generates ticks from user given `data.labels` values ONLY.\r\n       * @see https://github.com/chartjs/Chart.js/pull/4507\r\n       * @since 2.7.0\r\n       */\n      source: 'auto',\n      major: {\n        enabled: false\n      }\n    }\n  };\n  var scale_time = core_scale.extend({\n    initialize: function () {\n      this.mergeTicksOptions();\n      core_scale.prototype.initialize.call(this);\n    },\n    update: function () {\n      var me = this;\n      var options = me.options;\n      var time = options.time || (options.time = {});\n      var adapter = me._adapter = new core_adapters._date(options.adapters.date);\n\n      // DEPRECATIONS: output a message only one time per update\n      deprecated$1('time scale', time.format, 'time.format', 'time.parser');\n      deprecated$1('time scale', time.min, 'time.min', 'ticks.min');\n      deprecated$1('time scale', time.max, 'time.max', 'ticks.max');\n\n      // Backward compatibility: before introducing adapter, `displayFormats` was\n      // supposed to contain *all* unit/string pairs but this can't be resolved\n      // when loading the scale (adapters are loaded afterward), so let's populate\n      // missing formats on update\n      helpers$1.mergeIf(time.displayFormats, adapter.formats());\n      return core_scale.prototype.update.apply(me, arguments);\n    },\n    /**\r\n     * Allows data to be referenced via 't' attribute\r\n     */\n    getRightValue: function (rawValue) {\n      if (rawValue && rawValue.t !== undefined) {\n        rawValue = rawValue.t;\n      }\n      return core_scale.prototype.getRightValue.call(this, rawValue);\n    },\n    determineDataLimits: function () {\n      var me = this;\n      var chart = me.chart;\n      var adapter = me._adapter;\n      var options = me.options;\n      var unit = options.time.unit || 'day';\n      var min = MAX_INTEGER;\n      var max = MIN_INTEGER;\n      var timestamps = [];\n      var datasets = [];\n      var labels = [];\n      var i, j, ilen, jlen, data, timestamp, labelsAdded;\n      var dataLabels = me._getLabels();\n      for (i = 0, ilen = dataLabels.length; i < ilen; ++i) {\n        labels.push(parse(me, dataLabels[i]));\n      }\n      for (i = 0, ilen = (chart.data.datasets || []).length; i < ilen; ++i) {\n        if (chart.isDatasetVisible(i)) {\n          data = chart.data.datasets[i].data;\n\n          // Let's consider that all data have the same format.\n          if (helpers$1.isObject(data[0])) {\n            datasets[i] = [];\n            for (j = 0, jlen = data.length; j < jlen; ++j) {\n              timestamp = parse(me, data[j]);\n              timestamps.push(timestamp);\n              datasets[i][j] = timestamp;\n            }\n          } else {\n            datasets[i] = labels.slice(0);\n            if (!labelsAdded) {\n              timestamps = timestamps.concat(labels);\n              labelsAdded = true;\n            }\n          }\n        } else {\n          datasets[i] = [];\n        }\n      }\n      if (labels.length) {\n        min = Math.min(min, labels[0]);\n        max = Math.max(max, labels[labels.length - 1]);\n      }\n      if (timestamps.length) {\n        timestamps = ilen > 1 ? arrayUnique(timestamps).sort(sorter) : timestamps.sort(sorter);\n        min = Math.min(min, timestamps[0]);\n        max = Math.max(max, timestamps[timestamps.length - 1]);\n      }\n      min = parse(me, getMin(options)) || min;\n      max = parse(me, getMax(options)) || max;\n\n      // In case there is no valid min/max, set limits based on unit time option\n      min = min === MAX_INTEGER ? +adapter.startOf(Date.now(), unit) : min;\n      max = max === MIN_INTEGER ? +adapter.endOf(Date.now(), unit) + 1 : max;\n\n      // Make sure that max is strictly higher than min (required by the lookup table)\n      me.min = Math.min(min, max);\n      me.max = Math.max(min + 1, max);\n\n      // PRIVATE\n      me._table = [];\n      me._timestamps = {\n        data: timestamps,\n        datasets: datasets,\n        labels: labels\n      };\n    },\n    buildTicks: function () {\n      var me = this;\n      var min = me.min;\n      var max = me.max;\n      var options = me.options;\n      var tickOpts = options.ticks;\n      var timeOpts = options.time;\n      var timestamps = me._timestamps;\n      var ticks = [];\n      var capacity = me.getLabelCapacity(min);\n      var source = tickOpts.source;\n      var distribution = options.distribution;\n      var i, ilen, timestamp;\n      if (source === 'data' || source === 'auto' && distribution === 'series') {\n        timestamps = timestamps.data;\n      } else if (source === 'labels') {\n        timestamps = timestamps.labels;\n      } else {\n        timestamps = generate(me, min, max, capacity);\n      }\n      if (options.bounds === 'ticks' && timestamps.length) {\n        min = timestamps[0];\n        max = timestamps[timestamps.length - 1];\n      }\n\n      // Enforce limits with user min/max options\n      min = parse(me, getMin(options)) || min;\n      max = parse(me, getMax(options)) || max;\n\n      // Remove ticks outside the min/max range\n      for (i = 0, ilen = timestamps.length; i < ilen; ++i) {\n        timestamp = timestamps[i];\n        if (timestamp >= min && timestamp <= max) {\n          ticks.push(timestamp);\n        }\n      }\n      me.min = min;\n      me.max = max;\n\n      // PRIVATE\n      // determineUnitForFormatting relies on the number of ticks so we don't use it when\n      // autoSkip is enabled because we don't yet know what the final number of ticks will be\n      me._unit = timeOpts.unit || (tickOpts.autoSkip ? determineUnitForAutoTicks(timeOpts.minUnit, me.min, me.max, capacity) : determineUnitForFormatting(me, ticks.length, timeOpts.minUnit, me.min, me.max));\n      me._majorUnit = !tickOpts.major.enabled || me._unit === 'year' ? undefined : determineMajorUnit(me._unit);\n      me._table = buildLookupTable(me._timestamps.data, min, max, distribution);\n      me._offsets = computeOffsets(me._table, ticks, min, max, options);\n      if (tickOpts.reverse) {\n        ticks.reverse();\n      }\n      return ticksFromTimestamps(me, ticks, me._majorUnit);\n    },\n    getLabelForIndex: function (index, datasetIndex) {\n      var me = this;\n      var adapter = me._adapter;\n      var data = me.chart.data;\n      var timeOpts = me.options.time;\n      var label = data.labels && index < data.labels.length ? data.labels[index] : '';\n      var value = data.datasets[datasetIndex].data[index];\n      if (helpers$1.isObject(value)) {\n        label = me.getRightValue(value);\n      }\n      if (timeOpts.tooltipFormat) {\n        return adapter.format(toTimestamp(me, label), timeOpts.tooltipFormat);\n      }\n      if (typeof label === 'string') {\n        return label;\n      }\n      return adapter.format(toTimestamp(me, label), timeOpts.displayFormats.datetime);\n    },\n    /**\r\n     * Function to format an individual tick mark\r\n     * @private\r\n     */\n    tickFormatFunction: function (time, index, ticks, format) {\n      var me = this;\n      var adapter = me._adapter;\n      var options = me.options;\n      var formats = options.time.displayFormats;\n      var minorFormat = formats[me._unit];\n      var majorUnit = me._majorUnit;\n      var majorFormat = formats[majorUnit];\n      var tick = ticks[index];\n      var tickOpts = options.ticks;\n      var major = majorUnit && majorFormat && tick && tick.major;\n      var label = adapter.format(time, format ? format : major ? majorFormat : minorFormat);\n      var nestedTickOpts = major ? tickOpts.major : tickOpts.minor;\n      var formatter = resolve$5([nestedTickOpts.callback, nestedTickOpts.userCallback, tickOpts.callback, tickOpts.userCallback]);\n      return formatter ? formatter(label, index, ticks) : label;\n    },\n    convertTicksToLabels: function (ticks) {\n      var labels = [];\n      var i, ilen;\n      for (i = 0, ilen = ticks.length; i < ilen; ++i) {\n        labels.push(this.tickFormatFunction(ticks[i].value, i, ticks));\n      }\n      return labels;\n    },\n    /**\r\n     * @private\r\n     */\n    getPixelForOffset: function (time) {\n      var me = this;\n      var offsets = me._offsets;\n      var pos = interpolate$1(me._table, 'time', time, 'pos');\n      return me.getPixelForDecimal((offsets.start + pos) * offsets.factor);\n    },\n    getPixelForValue: function (value, index, datasetIndex) {\n      var me = this;\n      var time = null;\n      if (index !== undefined && datasetIndex !== undefined) {\n        time = me._timestamps.datasets[datasetIndex][index];\n      }\n      if (time === null) {\n        time = parse(me, value);\n      }\n      if (time !== null) {\n        return me.getPixelForOffset(time);\n      }\n    },\n    getPixelForTick: function (index) {\n      var ticks = this.getTicks();\n      return index >= 0 && index < ticks.length ? this.getPixelForOffset(ticks[index].value) : null;\n    },\n    getValueForPixel: function (pixel) {\n      var me = this;\n      var offsets = me._offsets;\n      var pos = me.getDecimalForPixel(pixel) / offsets.factor - offsets.end;\n      var time = interpolate$1(me._table, 'pos', pos, 'time');\n\n      // DEPRECATION, we should return time directly\n      return me._adapter._create(time);\n    },\n    /**\r\n     * @private\r\n     */\n    _getLabelSize: function (label) {\n      var me = this;\n      var ticksOpts = me.options.ticks;\n      var tickLabelWidth = me.ctx.measureText(label).width;\n      var angle = helpers$1.toRadians(me.isHorizontal() ? ticksOpts.maxRotation : ticksOpts.minRotation);\n      var cosRotation = Math.cos(angle);\n      var sinRotation = Math.sin(angle);\n      var tickFontSize = valueOrDefault$d(ticksOpts.fontSize, core_defaults.global.defaultFontSize);\n      return {\n        w: tickLabelWidth * cosRotation + tickFontSize * sinRotation,\n        h: tickLabelWidth * sinRotation + tickFontSize * cosRotation\n      };\n    },\n    /**\r\n     * Crude approximation of what the label width might be\r\n     * @private\r\n     */\n    getLabelWidth: function (label) {\n      return this._getLabelSize(label).w;\n    },\n    /**\r\n     * @private\r\n     */\n    getLabelCapacity: function (exampleTime) {\n      var me = this;\n      var timeOpts = me.options.time;\n      var displayFormats = timeOpts.displayFormats;\n\n      // pick the longest format (milliseconds) for guestimation\n      var format = displayFormats[timeOpts.unit] || displayFormats.millisecond;\n      var exampleLabel = me.tickFormatFunction(exampleTime, 0, ticksFromTimestamps(me, [exampleTime], me._majorUnit), format);\n      var size = me._getLabelSize(exampleLabel);\n      var capacity = Math.floor(me.isHorizontal() ? me.width / size.w : me.height / size.h);\n      if (me.options.offset) {\n        capacity--;\n      }\n      return capacity > 0 ? capacity : 1;\n    }\n  });\n\n  // INTERNAL: static default options, registered in src/index.js\n  var _defaults$4 = defaultConfig$4;\n  scale_time._defaults = _defaults$4;\n  var scales = {\n    category: scale_category,\n    linear: scale_linear,\n    logarithmic: scale_logarithmic,\n    radialLinear: scale_radialLinear,\n    time: scale_time\n  };\n  var FORMATS = {\n    datetime: 'MMM D, YYYY, h:mm:ss a',\n    millisecond: 'h:mm:ss.SSS a',\n    second: 'h:mm:ss a',\n    minute: 'h:mm a',\n    hour: 'hA',\n    day: 'MMM D',\n    week: 'll',\n    month: 'MMM YYYY',\n    quarter: '[Q]Q - YYYY',\n    year: 'YYYY'\n  };\n  core_adapters._date.override(typeof moment === 'function' ? {\n    _id: 'moment',\n    // DEBUG ONLY\n\n    formats: function () {\n      return FORMATS;\n    },\n    parse: function (value, format) {\n      if (typeof value === 'string' && typeof format === 'string') {\n        value = moment(value, format);\n      } else if (!(value instanceof moment)) {\n        value = moment(value);\n      }\n      return value.isValid() ? value.valueOf() : null;\n    },\n    format: function (time, format) {\n      return moment(time).format(format);\n    },\n    add: function (time, amount, unit) {\n      return moment(time).add(amount, unit).valueOf();\n    },\n    diff: function (max, min, unit) {\n      return moment(max).diff(moment(min), unit);\n    },\n    startOf: function (time, unit, weekday) {\n      time = moment(time);\n      if (unit === 'isoWeek') {\n        return time.isoWeekday(weekday).valueOf();\n      }\n      return time.startOf(unit).valueOf();\n    },\n    endOf: function (time, unit) {\n      return moment(time).endOf(unit).valueOf();\n    },\n    // DEPRECATIONS\n\n    /**\r\n     * Provided for backward compatibility with scale.getValueForPixel().\r\n     * @deprecated since version 2.8.0\r\n     * @todo remove at version 3\r\n     * @private\r\n     */\n    _create: function (time) {\n      return moment(time);\n    }\n  } : {});\n  core_defaults._set('global', {\n    plugins: {\n      filler: {\n        propagate: true\n      }\n    }\n  });\n  var mappers = {\n    dataset: function (source) {\n      var index = source.fill;\n      var chart = source.chart;\n      var meta = chart.getDatasetMeta(index);\n      var visible = meta && chart.isDatasetVisible(index);\n      var points = visible && meta.dataset._children || [];\n      var length = points.length || 0;\n      return !length ? null : function (point, i) {\n        return i < length && points[i]._view || null;\n      };\n    },\n    boundary: function (source) {\n      var boundary = source.boundary;\n      var x = boundary ? boundary.x : null;\n      var y = boundary ? boundary.y : null;\n      if (helpers$1.isArray(boundary)) {\n        return function (point, i) {\n          return boundary[i];\n        };\n      }\n      return function (point) {\n        return {\n          x: x === null ? point.x : x,\n          y: y === null ? point.y : y\n        };\n      };\n    }\n  };\n\n  // @todo if (fill[0] === '#')\n  function decodeFill(el, index, count) {\n    var model = el._model || {};\n    var fill = model.fill;\n    var target;\n    if (fill === undefined) {\n      fill = !!model.backgroundColor;\n    }\n    if (fill === false || fill === null) {\n      return false;\n    }\n    if (fill === true) {\n      return 'origin';\n    }\n    target = parseFloat(fill, 10);\n    if (isFinite(target) && Math.floor(target) === target) {\n      if (fill[0] === '-' || fill[0] === '+') {\n        target = index + target;\n      }\n      if (target === index || target < 0 || target >= count) {\n        return false;\n      }\n      return target;\n    }\n    switch (fill) {\n      // compatibility\n      case 'bottom':\n        return 'start';\n      case 'top':\n        return 'end';\n      case 'zero':\n        return 'origin';\n      // supported boundaries\n      case 'origin':\n      case 'start':\n      case 'end':\n        return fill;\n      // invalid fill values\n      default:\n        return false;\n    }\n  }\n  function computeLinearBoundary(source) {\n    var model = source.el._model || {};\n    var scale = source.el._scale || {};\n    var fill = source.fill;\n    var target = null;\n    var horizontal;\n    if (isFinite(fill)) {\n      return null;\n    }\n\n    // Backward compatibility: until v3, we still need to support boundary values set on\n    // the model (scaleTop, scaleBottom and scaleZero) because some external plugins and\n    // controllers might still use it (e.g. the Smith chart).\n\n    if (fill === 'start') {\n      target = model.scaleBottom === undefined ? scale.bottom : model.scaleBottom;\n    } else if (fill === 'end') {\n      target = model.scaleTop === undefined ? scale.top : model.scaleTop;\n    } else if (model.scaleZero !== undefined) {\n      target = model.scaleZero;\n    } else if (scale.getBasePixel) {\n      target = scale.getBasePixel();\n    }\n    if (target !== undefined && target !== null) {\n      if (target.x !== undefined && target.y !== undefined) {\n        return target;\n      }\n      if (helpers$1.isFinite(target)) {\n        horizontal = scale.isHorizontal();\n        return {\n          x: horizontal ? target : null,\n          y: horizontal ? null : target\n        };\n      }\n    }\n    return null;\n  }\n  function computeCircularBoundary(source) {\n    var scale = source.el._scale;\n    var options = scale.options;\n    var length = scale.chart.data.labels.length;\n    var fill = source.fill;\n    var target = [];\n    var start, end, center, i, point;\n    if (!length) {\n      return null;\n    }\n    start = options.ticks.reverse ? scale.max : scale.min;\n    end = options.ticks.reverse ? scale.min : scale.max;\n    center = scale.getPointPositionForValue(0, start);\n    for (i = 0; i < length; ++i) {\n      point = fill === 'start' || fill === 'end' ? scale.getPointPositionForValue(i, fill === 'start' ? start : end) : scale.getBasePosition(i);\n      if (options.gridLines.circular) {\n        point.cx = center.x;\n        point.cy = center.y;\n        point.angle = scale.getIndexAngle(i) - Math.PI / 2;\n      }\n      target.push(point);\n    }\n    return target;\n  }\n  function computeBoundary(source) {\n    var scale = source.el._scale || {};\n    if (scale.getPointPositionForValue) {\n      return computeCircularBoundary(source);\n    }\n    return computeLinearBoundary(source);\n  }\n  function resolveTarget(sources, index, propagate) {\n    var source = sources[index];\n    var fill = source.fill;\n    var visited = [index];\n    var target;\n    if (!propagate) {\n      return fill;\n    }\n    while (fill !== false && visited.indexOf(fill) === -1) {\n      if (!isFinite(fill)) {\n        return fill;\n      }\n      target = sources[fill];\n      if (!target) {\n        return false;\n      }\n      if (target.visible) {\n        return fill;\n      }\n      visited.push(fill);\n      fill = target.fill;\n    }\n    return false;\n  }\n  function createMapper(source) {\n    var fill = source.fill;\n    var type = 'dataset';\n    if (fill === false) {\n      return null;\n    }\n    if (!isFinite(fill)) {\n      type = 'boundary';\n    }\n    return mappers[type](source);\n  }\n  function isDrawable(point) {\n    return point && !point.skip;\n  }\n  function drawArea(ctx, curve0, curve1, len0, len1) {\n    var i, cx, cy, r;\n    if (!len0 || !len1) {\n      return;\n    }\n\n    // building first area curve (normal)\n    ctx.moveTo(curve0[0].x, curve0[0].y);\n    for (i = 1; i < len0; ++i) {\n      helpers$1.canvas.lineTo(ctx, curve0[i - 1], curve0[i]);\n    }\n    if (curve1[0].angle !== undefined) {\n      cx = curve1[0].cx;\n      cy = curve1[0].cy;\n      r = Math.sqrt(Math.pow(curve1[0].x - cx, 2) + Math.pow(curve1[0].y - cy, 2));\n      for (i = len1 - 1; i > 0; --i) {\n        ctx.arc(cx, cy, r, curve1[i].angle, curve1[i - 1].angle, true);\n      }\n      return;\n    }\n\n    // joining the two area curves\n    ctx.lineTo(curve1[len1 - 1].x, curve1[len1 - 1].y);\n\n    // building opposite area curve (reverse)\n    for (i = len1 - 1; i > 0; --i) {\n      helpers$1.canvas.lineTo(ctx, curve1[i], curve1[i - 1], true);\n    }\n  }\n  function doFill(ctx, points, mapper, view, color, loop) {\n    var count = points.length;\n    var span = view.spanGaps;\n    var curve0 = [];\n    var curve1 = [];\n    var len0 = 0;\n    var len1 = 0;\n    var i, ilen, index, p0, p1, d0, d1, loopOffset;\n    ctx.beginPath();\n    for (i = 0, ilen = count; i < ilen; ++i) {\n      index = i % count;\n      p0 = points[index]._view;\n      p1 = mapper(p0, index, view);\n      d0 = isDrawable(p0);\n      d1 = isDrawable(p1);\n      if (loop && loopOffset === undefined && d0) {\n        loopOffset = i + 1;\n        ilen = count + loopOffset;\n      }\n      if (d0 && d1) {\n        len0 = curve0.push(p0);\n        len1 = curve1.push(p1);\n      } else if (len0 && len1) {\n        if (!span) {\n          drawArea(ctx, curve0, curve1, len0, len1);\n          len0 = len1 = 0;\n          curve0 = [];\n          curve1 = [];\n        } else {\n          if (d0) {\n            curve0.push(p0);\n          }\n          if (d1) {\n            curve1.push(p1);\n          }\n        }\n      }\n    }\n    drawArea(ctx, curve0, curve1, len0, len1);\n    ctx.closePath();\n    ctx.fillStyle = color;\n    ctx.fill();\n  }\n  var plugin_filler = {\n    id: 'filler',\n    afterDatasetsUpdate: function (chart, options) {\n      var count = (chart.data.datasets || []).length;\n      var propagate = options.propagate;\n      var sources = [];\n      var meta, i, el, source;\n      for (i = 0; i < count; ++i) {\n        meta = chart.getDatasetMeta(i);\n        el = meta.dataset;\n        source = null;\n        if (el && el._model && el instanceof elements.Line) {\n          source = {\n            visible: chart.isDatasetVisible(i),\n            fill: decodeFill(el, i, count),\n            chart: chart,\n            el: el\n          };\n        }\n        meta.$filler = source;\n        sources.push(source);\n      }\n      for (i = 0; i < count; ++i) {\n        source = sources[i];\n        if (!source) {\n          continue;\n        }\n        source.fill = resolveTarget(sources, i, propagate);\n        source.boundary = computeBoundary(source);\n        source.mapper = createMapper(source);\n      }\n    },\n    beforeDatasetsDraw: function (chart) {\n      var metasets = chart._getSortedVisibleDatasetMetas();\n      var ctx = chart.ctx;\n      var meta, i, el, view, points, mapper, color;\n      for (i = metasets.length - 1; i >= 0; --i) {\n        meta = metasets[i].$filler;\n        if (!meta || !meta.visible) {\n          continue;\n        }\n        el = meta.el;\n        view = el._view;\n        points = el._children || [];\n        mapper = meta.mapper;\n        color = view.backgroundColor || core_defaults.global.defaultColor;\n        if (mapper && color && points.length) {\n          helpers$1.canvas.clipArea(ctx, chart.chartArea);\n          doFill(ctx, points, mapper, view, color, el._loop);\n          helpers$1.canvas.unclipArea(ctx);\n        }\n      }\n    }\n  };\n  var getRtlHelper$1 = helpers$1.rtl.getRtlAdapter;\n  var noop$1 = helpers$1.noop;\n  var valueOrDefault$e = helpers$1.valueOrDefault;\n  core_defaults._set('global', {\n    legend: {\n      display: true,\n      position: 'top',\n      align: 'center',\n      fullWidth: true,\n      reverse: false,\n      weight: 1000,\n      // a callback that will handle\n      onClick: function (e, legendItem) {\n        var index = legendItem.datasetIndex;\n        var ci = this.chart;\n        var meta = ci.getDatasetMeta(index);\n\n        // See controller.isDatasetVisible comment\n        meta.hidden = meta.hidden === null ? !ci.data.datasets[index].hidden : null;\n\n        // We hid a dataset ... rerender the chart\n        ci.update();\n      },\n      onHover: null,\n      onLeave: null,\n      labels: {\n        boxWidth: 40,\n        padding: 10,\n        // Generates labels shown in the legend\n        // Valid properties to return:\n        // text : text to display\n        // fillStyle : fill of coloured box\n        // strokeStyle: stroke of coloured box\n        // hidden : if this legend item refers to a hidden item\n        // lineCap : cap style for line\n        // lineDash\n        // lineDashOffset :\n        // lineJoin :\n        // lineWidth :\n        generateLabels: function (chart) {\n          var datasets = chart.data.datasets;\n          var options = chart.options.legend || {};\n          var usePointStyle = options.labels && options.labels.usePointStyle;\n          return chart._getSortedDatasetMetas().map(function (meta) {\n            var style = meta.controller.getStyle(usePointStyle ? 0 : undefined);\n            return {\n              text: datasets[meta.index].label,\n              fillStyle: style.backgroundColor,\n              hidden: !chart.isDatasetVisible(meta.index),\n              lineCap: style.borderCapStyle,\n              lineDash: style.borderDash,\n              lineDashOffset: style.borderDashOffset,\n              lineJoin: style.borderJoinStyle,\n              lineWidth: style.borderWidth,\n              strokeStyle: style.borderColor,\n              pointStyle: style.pointStyle,\n              rotation: style.rotation,\n              // Below is extra data used for toggling the datasets\n              datasetIndex: meta.index\n            };\n          }, this);\n        }\n      }\n    },\n    legendCallback: function (chart) {\n      var list = document.createElement('ul');\n      var datasets = chart.data.datasets;\n      var i, ilen, listItem, listItemSpan;\n      list.setAttribute('class', chart.id + '-legend');\n      for (i = 0, ilen = datasets.length; i < ilen; i++) {\n        listItem = list.appendChild(document.createElement('li'));\n        listItemSpan = listItem.appendChild(document.createElement('span'));\n        listItemSpan.style.backgroundColor = datasets[i].backgroundColor;\n        if (datasets[i].label) {\n          listItem.appendChild(document.createTextNode(datasets[i].label));\n        }\n      }\n      return list.outerHTML;\n    }\n  });\n\n  /**\r\n   * Helper function to get the box width based on the usePointStyle option\r\n   * @param {object} labelopts - the label options on the legend\r\n   * @param {number} fontSize - the label font size\r\n   * @return {number} width of the color box area\r\n   */\n  function getBoxWidth(labelOpts, fontSize) {\n    return labelOpts.usePointStyle && labelOpts.boxWidth > fontSize ? fontSize : labelOpts.boxWidth;\n  }\n\n  /**\r\n   * IMPORTANT: this class is exposed publicly as Chart.Legend, backward compatibility required!\r\n   */\n  var Legend = core_element.extend({\n    initialize: function (config) {\n      var me = this;\n      helpers$1.extend(me, config);\n\n      // Contains hit boxes for each dataset (in dataset order)\n      me.legendHitBoxes = [];\n\n      /**\r\n      \t * @private\r\n      \t */\n      me._hoveredItem = null;\n\n      // Are we in doughnut mode which has a different data type\n      me.doughnutMode = false;\n    },\n    // These methods are ordered by lifecycle. Utilities then follow.\n    // Any function defined here is inherited by all legend types.\n    // Any function can be extended by the legend type\n\n    beforeUpdate: noop$1,\n    update: function (maxWidth, maxHeight, margins) {\n      var me = this;\n\n      // Update Lifecycle - Probably don't want to ever extend or overwrite this function ;)\n      me.beforeUpdate();\n\n      // Absorb the master measurements\n      me.maxWidth = maxWidth;\n      me.maxHeight = maxHeight;\n      me.margins = margins;\n\n      // Dimensions\n      me.beforeSetDimensions();\n      me.setDimensions();\n      me.afterSetDimensions();\n      // Labels\n      me.beforeBuildLabels();\n      me.buildLabels();\n      me.afterBuildLabels();\n\n      // Fit\n      me.beforeFit();\n      me.fit();\n      me.afterFit();\n      //\n      me.afterUpdate();\n      return me.minSize;\n    },\n    afterUpdate: noop$1,\n    //\n\n    beforeSetDimensions: noop$1,\n    setDimensions: function () {\n      var me = this;\n      // Set the unconstrained dimension before label rotation\n      if (me.isHorizontal()) {\n        // Reset position before calculating rotation\n        me.width = me.maxWidth;\n        me.left = 0;\n        me.right = me.width;\n      } else {\n        me.height = me.maxHeight;\n\n        // Reset position before calculating rotation\n        me.top = 0;\n        me.bottom = me.height;\n      }\n\n      // Reset padding\n      me.paddingLeft = 0;\n      me.paddingTop = 0;\n      me.paddingRight = 0;\n      me.paddingBottom = 0;\n\n      // Reset minSize\n      me.minSize = {\n        width: 0,\n        height: 0\n      };\n    },\n    afterSetDimensions: noop$1,\n    //\n\n    beforeBuildLabels: noop$1,\n    buildLabels: function () {\n      var me = this;\n      var labelOpts = me.options.labels || {};\n      var legendItems = helpers$1.callback(labelOpts.generateLabels, [me.chart], me) || [];\n      if (labelOpts.filter) {\n        legendItems = legendItems.filter(function (item) {\n          return labelOpts.filter(item, me.chart.data);\n        });\n      }\n      if (me.options.reverse) {\n        legendItems.reverse();\n      }\n      me.legendItems = legendItems;\n    },\n    afterBuildLabels: noop$1,\n    //\n\n    beforeFit: noop$1,\n    fit: function () {\n      var me = this;\n      var opts = me.options;\n      var labelOpts = opts.labels;\n      var display = opts.display;\n      var ctx = me.ctx;\n      var labelFont = helpers$1.options._parseFont(labelOpts);\n      var fontSize = labelFont.size;\n\n      // Reset hit boxes\n      var hitboxes = me.legendHitBoxes = [];\n      var minSize = me.minSize;\n      var isHorizontal = me.isHorizontal();\n      if (isHorizontal) {\n        minSize.width = me.maxWidth; // fill all the width\n        minSize.height = display ? 10 : 0;\n      } else {\n        minSize.width = display ? 10 : 0;\n        minSize.height = me.maxHeight; // fill all the height\n      }\n\n      // Increase sizes here\n      if (!display) {\n        me.width = minSize.width = me.height = minSize.height = 0;\n        return;\n      }\n      ctx.font = labelFont.string;\n      if (isHorizontal) {\n        // Labels\n\n        // Width of each line of legend boxes. Labels wrap onto multiple lines when there are too many to fit on one\n        var lineWidths = me.lineWidths = [0];\n        var totalHeight = 0;\n        ctx.textAlign = 'left';\n        ctx.textBaseline = 'middle';\n        helpers$1.each(me.legendItems, function (legendItem, i) {\n          var boxWidth = getBoxWidth(labelOpts, fontSize);\n          var width = boxWidth + fontSize / 2 + ctx.measureText(legendItem.text).width;\n          if (i === 0 || lineWidths[lineWidths.length - 1] + width + 2 * labelOpts.padding > minSize.width) {\n            totalHeight += fontSize + labelOpts.padding;\n            lineWidths[lineWidths.length - (i > 0 ? 0 : 1)] = 0;\n          }\n\n          // Store the hitbox width and height here. Final position will be updated in `draw`\n          hitboxes[i] = {\n            left: 0,\n            top: 0,\n            width: width,\n            height: fontSize\n          };\n          lineWidths[lineWidths.length - 1] += width + labelOpts.padding;\n        });\n        minSize.height += totalHeight;\n      } else {\n        var vPadding = labelOpts.padding;\n        var columnWidths = me.columnWidths = [];\n        var columnHeights = me.columnHeights = [];\n        var totalWidth = labelOpts.padding;\n        var currentColWidth = 0;\n        var currentColHeight = 0;\n        helpers$1.each(me.legendItems, function (legendItem, i) {\n          var boxWidth = getBoxWidth(labelOpts, fontSize);\n          var itemWidth = boxWidth + fontSize / 2 + ctx.measureText(legendItem.text).width;\n\n          // If too tall, go to new column\n          if (i > 0 && currentColHeight + fontSize + 2 * vPadding > minSize.height) {\n            totalWidth += currentColWidth + labelOpts.padding;\n            columnWidths.push(currentColWidth); // previous column width\n            columnHeights.push(currentColHeight);\n            currentColWidth = 0;\n            currentColHeight = 0;\n          }\n\n          // Get max width\n          currentColWidth = Math.max(currentColWidth, itemWidth);\n          currentColHeight += fontSize + vPadding;\n\n          // Store the hitbox width and height here. Final position will be updated in `draw`\n          hitboxes[i] = {\n            left: 0,\n            top: 0,\n            width: itemWidth,\n            height: fontSize\n          };\n        });\n        totalWidth += currentColWidth;\n        columnWidths.push(currentColWidth);\n        columnHeights.push(currentColHeight);\n        minSize.width += totalWidth;\n      }\n      me.width = minSize.width;\n      me.height = minSize.height;\n    },\n    afterFit: noop$1,\n    // Shared Methods\n    isHorizontal: function () {\n      return this.options.position === 'top' || this.options.position === 'bottom';\n    },\n    // Actually draw the legend on the canvas\n    draw: function () {\n      var me = this;\n      var opts = me.options;\n      var labelOpts = opts.labels;\n      var globalDefaults = core_defaults.global;\n      var defaultColor = globalDefaults.defaultColor;\n      var lineDefault = globalDefaults.elements.line;\n      var legendHeight = me.height;\n      var columnHeights = me.columnHeights;\n      var legendWidth = me.width;\n      var lineWidths = me.lineWidths;\n      if (!opts.display) {\n        return;\n      }\n      var rtlHelper = getRtlHelper$1(opts.rtl, me.left, me.minSize.width);\n      var ctx = me.ctx;\n      var fontColor = valueOrDefault$e(labelOpts.fontColor, globalDefaults.defaultFontColor);\n      var labelFont = helpers$1.options._parseFont(labelOpts);\n      var fontSize = labelFont.size;\n      var cursor;\n\n      // Canvas setup\n      ctx.textAlign = rtlHelper.textAlign('left');\n      ctx.textBaseline = 'middle';\n      ctx.lineWidth = 0.5;\n      ctx.strokeStyle = fontColor; // for strikethrough effect\n      ctx.fillStyle = fontColor; // render in correct colour\n      ctx.font = labelFont.string;\n      var boxWidth = getBoxWidth(labelOpts, fontSize);\n      var hitboxes = me.legendHitBoxes;\n\n      // current position\n      var drawLegendBox = function (x, y, legendItem) {\n        if (isNaN(boxWidth) || boxWidth <= 0) {\n          return;\n        }\n\n        // Set the ctx for the box\n        ctx.save();\n        var lineWidth = valueOrDefault$e(legendItem.lineWidth, lineDefault.borderWidth);\n        ctx.fillStyle = valueOrDefault$e(legendItem.fillStyle, defaultColor);\n        ctx.lineCap = valueOrDefault$e(legendItem.lineCap, lineDefault.borderCapStyle);\n        ctx.lineDashOffset = valueOrDefault$e(legendItem.lineDashOffset, lineDefault.borderDashOffset);\n        ctx.lineJoin = valueOrDefault$e(legendItem.lineJoin, lineDefault.borderJoinStyle);\n        ctx.lineWidth = lineWidth;\n        ctx.strokeStyle = valueOrDefault$e(legendItem.strokeStyle, defaultColor);\n        if (ctx.setLineDash) {\n          // IE 9 and 10 do not support line dash\n          ctx.setLineDash(valueOrDefault$e(legendItem.lineDash, lineDefault.borderDash));\n        }\n        if (labelOpts && labelOpts.usePointStyle) {\n          // Recalculate x and y for drawPoint() because its expecting\n          // x and y to be center of figure (instead of top left)\n          var radius = boxWidth * Math.SQRT2 / 2;\n          var centerX = rtlHelper.xPlus(x, boxWidth / 2);\n          var centerY = y + fontSize / 2;\n\n          // Draw pointStyle as legend symbol\n          helpers$1.canvas.drawPoint(ctx, legendItem.pointStyle, radius, centerX, centerY, legendItem.rotation);\n        } else {\n          // Draw box as legend symbol\n          ctx.fillRect(rtlHelper.leftForLtr(x, boxWidth), y, boxWidth, fontSize);\n          if (lineWidth !== 0) {\n            ctx.strokeRect(rtlHelper.leftForLtr(x, boxWidth), y, boxWidth, fontSize);\n          }\n        }\n        ctx.restore();\n      };\n      var fillText = function (x, y, legendItem, textWidth) {\n        var halfFontSize = fontSize / 2;\n        var xLeft = rtlHelper.xPlus(x, boxWidth + halfFontSize);\n        var yMiddle = y + halfFontSize;\n        ctx.fillText(legendItem.text, xLeft, yMiddle);\n        if (legendItem.hidden) {\n          // Strikethrough the text if hidden\n          ctx.beginPath();\n          ctx.lineWidth = 2;\n          ctx.moveTo(xLeft, yMiddle);\n          ctx.lineTo(rtlHelper.xPlus(xLeft, textWidth), yMiddle);\n          ctx.stroke();\n        }\n      };\n      var alignmentOffset = function (dimension, blockSize) {\n        switch (opts.align) {\n          case 'start':\n            return labelOpts.padding;\n          case 'end':\n            return dimension - blockSize;\n          default:\n            // center\n            return (dimension - blockSize + labelOpts.padding) / 2;\n        }\n      };\n\n      // Horizontal\n      var isHorizontal = me.isHorizontal();\n      if (isHorizontal) {\n        cursor = {\n          x: me.left + alignmentOffset(legendWidth, lineWidths[0]),\n          y: me.top + labelOpts.padding,\n          line: 0\n        };\n      } else {\n        cursor = {\n          x: me.left + labelOpts.padding,\n          y: me.top + alignmentOffset(legendHeight, columnHeights[0]),\n          line: 0\n        };\n      }\n      helpers$1.rtl.overrideTextDirection(me.ctx, opts.textDirection);\n      var itemHeight = fontSize + labelOpts.padding;\n      helpers$1.each(me.legendItems, function (legendItem, i) {\n        var textWidth = ctx.measureText(legendItem.text).width;\n        var width = boxWidth + fontSize / 2 + textWidth;\n        var x = cursor.x;\n        var y = cursor.y;\n        rtlHelper.setWidth(me.minSize.width);\n\n        // Use (me.left + me.minSize.width) and (me.top + me.minSize.height)\n        // instead of me.right and me.bottom because me.width and me.height\n        // may have been changed since me.minSize was calculated\n        if (isHorizontal) {\n          if (i > 0 && x + width + labelOpts.padding > me.left + me.minSize.width) {\n            y = cursor.y += itemHeight;\n            cursor.line++;\n            x = cursor.x = me.left + alignmentOffset(legendWidth, lineWidths[cursor.line]);\n          }\n        } else if (i > 0 && y + itemHeight > me.top + me.minSize.height) {\n          x = cursor.x = x + me.columnWidths[cursor.line] + labelOpts.padding;\n          cursor.line++;\n          y = cursor.y = me.top + alignmentOffset(legendHeight, columnHeights[cursor.line]);\n        }\n        var realX = rtlHelper.x(x);\n        drawLegendBox(realX, y, legendItem);\n        hitboxes[i].left = rtlHelper.leftForLtr(realX, hitboxes[i].width);\n        hitboxes[i].top = y;\n\n        // Fill the actual label\n        fillText(realX, y, legendItem, textWidth);\n        if (isHorizontal) {\n          cursor.x += width + labelOpts.padding;\n        } else {\n          cursor.y += itemHeight;\n        }\n      });\n      helpers$1.rtl.restoreTextDirection(me.ctx, opts.textDirection);\n    },\n    /**\r\n     * @private\r\n     */\n    _getLegendItemAt: function (x, y) {\n      var me = this;\n      var i, hitBox, lh;\n      if (x >= me.left && x <= me.right && y >= me.top && y <= me.bottom) {\n        // See if we are touching one of the dataset boxes\n        lh = me.legendHitBoxes;\n        for (i = 0; i < lh.length; ++i) {\n          hitBox = lh[i];\n          if (x >= hitBox.left && x <= hitBox.left + hitBox.width && y >= hitBox.top && y <= hitBox.top + hitBox.height) {\n            // Touching an element\n            return me.legendItems[i];\n          }\n        }\n      }\n      return null;\n    },\n    /**\r\n     * Handle an event\r\n     * @private\r\n     * @param {IEvent} event - The event to handle\r\n     */\n    handleEvent: function (e) {\n      var me = this;\n      var opts = me.options;\n      var type = e.type === 'mouseup' ? 'click' : e.type;\n      var hoveredItem;\n      if (type === 'mousemove') {\n        if (!opts.onHover && !opts.onLeave) {\n          return;\n        }\n      } else if (type === 'click') {\n        if (!opts.onClick) {\n          return;\n        }\n      } else {\n        return;\n      }\n\n      // Chart event already has relative position in it\n      hoveredItem = me._getLegendItemAt(e.x, e.y);\n      if (type === 'click') {\n        if (hoveredItem && opts.onClick) {\n          // use e.native for backwards compatibility\n          opts.onClick.call(me, e.native, hoveredItem);\n        }\n      } else {\n        if (opts.onLeave && hoveredItem !== me._hoveredItem) {\n          if (me._hoveredItem) {\n            opts.onLeave.call(me, e.native, me._hoveredItem);\n          }\n          me._hoveredItem = hoveredItem;\n        }\n        if (opts.onHover && hoveredItem) {\n          // use e.native for backwards compatibility\n          opts.onHover.call(me, e.native, hoveredItem);\n        }\n      }\n    }\n  });\n  function createNewLegendAndAttach(chart, legendOpts) {\n    var legend = new Legend({\n      ctx: chart.ctx,\n      options: legendOpts,\n      chart: chart\n    });\n    core_layouts.configure(chart, legend, legendOpts);\n    core_layouts.addBox(chart, legend);\n    chart.legend = legend;\n  }\n  var plugin_legend = {\n    id: 'legend',\n    /**\r\n     * Backward compatibility: since 2.1.5, the legend is registered as a plugin, making\r\n     * Chart.Legend obsolete. To avoid a breaking change, we export the Legend as part of\r\n     * the plugin, which one will be re-exposed in the chart.js file.\r\n     * https://github.com/chartjs/Chart.js/pull/2640\r\n     * @private\r\n     */\n    _element: Legend,\n    beforeInit: function (chart) {\n      var legendOpts = chart.options.legend;\n      if (legendOpts) {\n        createNewLegendAndAttach(chart, legendOpts);\n      }\n    },\n    beforeUpdate: function (chart) {\n      var legendOpts = chart.options.legend;\n      var legend = chart.legend;\n      if (legendOpts) {\n        helpers$1.mergeIf(legendOpts, core_defaults.global.legend);\n        if (legend) {\n          core_layouts.configure(chart, legend, legendOpts);\n          legend.options = legendOpts;\n        } else {\n          createNewLegendAndAttach(chart, legendOpts);\n        }\n      } else if (legend) {\n        core_layouts.removeBox(chart, legend);\n        delete chart.legend;\n      }\n    },\n    afterEvent: function (chart, e) {\n      var legend = chart.legend;\n      if (legend) {\n        legend.handleEvent(e);\n      }\n    }\n  };\n  var noop$2 = helpers$1.noop;\n  core_defaults._set('global', {\n    title: {\n      display: false,\n      fontStyle: 'bold',\n      fullWidth: true,\n      padding: 10,\n      position: 'top',\n      text: '',\n      weight: 2000 // by default greater than legend (1000) to be above\n    }\n  });\n\n  /**\r\n   * IMPORTANT: this class is exposed publicly as Chart.Legend, backward compatibility required!\r\n   */\n  var Title = core_element.extend({\n    initialize: function (config) {\n      var me = this;\n      helpers$1.extend(me, config);\n\n      // Contains hit boxes for each dataset (in dataset order)\n      me.legendHitBoxes = [];\n    },\n    // These methods are ordered by lifecycle. Utilities then follow.\n\n    beforeUpdate: noop$2,\n    update: function (maxWidth, maxHeight, margins) {\n      var me = this;\n\n      // Update Lifecycle - Probably don't want to ever extend or overwrite this function ;)\n      me.beforeUpdate();\n\n      // Absorb the master measurements\n      me.maxWidth = maxWidth;\n      me.maxHeight = maxHeight;\n      me.margins = margins;\n\n      // Dimensions\n      me.beforeSetDimensions();\n      me.setDimensions();\n      me.afterSetDimensions();\n      // Labels\n      me.beforeBuildLabels();\n      me.buildLabels();\n      me.afterBuildLabels();\n\n      // Fit\n      me.beforeFit();\n      me.fit();\n      me.afterFit();\n      //\n      me.afterUpdate();\n      return me.minSize;\n    },\n    afterUpdate: noop$2,\n    //\n\n    beforeSetDimensions: noop$2,\n    setDimensions: function () {\n      var me = this;\n      // Set the unconstrained dimension before label rotation\n      if (me.isHorizontal()) {\n        // Reset position before calculating rotation\n        me.width = me.maxWidth;\n        me.left = 0;\n        me.right = me.width;\n      } else {\n        me.height = me.maxHeight;\n\n        // Reset position before calculating rotation\n        me.top = 0;\n        me.bottom = me.height;\n      }\n\n      // Reset padding\n      me.paddingLeft = 0;\n      me.paddingTop = 0;\n      me.paddingRight = 0;\n      me.paddingBottom = 0;\n\n      // Reset minSize\n      me.minSize = {\n        width: 0,\n        height: 0\n      };\n    },\n    afterSetDimensions: noop$2,\n    //\n\n    beforeBuildLabels: noop$2,\n    buildLabels: noop$2,\n    afterBuildLabels: noop$2,\n    //\n\n    beforeFit: noop$2,\n    fit: function () {\n      var me = this;\n      var opts = me.options;\n      var minSize = me.minSize = {};\n      var isHorizontal = me.isHorizontal();\n      var lineCount, textSize;\n      if (!opts.display) {\n        me.width = minSize.width = me.height = minSize.height = 0;\n        return;\n      }\n      lineCount = helpers$1.isArray(opts.text) ? opts.text.length : 1;\n      textSize = lineCount * helpers$1.options._parseFont(opts).lineHeight + opts.padding * 2;\n      me.width = minSize.width = isHorizontal ? me.maxWidth : textSize;\n      me.height = minSize.height = isHorizontal ? textSize : me.maxHeight;\n    },\n    afterFit: noop$2,\n    // Shared Methods\n    isHorizontal: function () {\n      var pos = this.options.position;\n      return pos === 'top' || pos === 'bottom';\n    },\n    // Actually draw the title block on the canvas\n    draw: function () {\n      var me = this;\n      var ctx = me.ctx;\n      var opts = me.options;\n      if (!opts.display) {\n        return;\n      }\n      var fontOpts = helpers$1.options._parseFont(opts);\n      var lineHeight = fontOpts.lineHeight;\n      var offset = lineHeight / 2 + opts.padding;\n      var rotation = 0;\n      var top = me.top;\n      var left = me.left;\n      var bottom = me.bottom;\n      var right = me.right;\n      var maxWidth, titleX, titleY;\n      ctx.fillStyle = helpers$1.valueOrDefault(opts.fontColor, core_defaults.global.defaultFontColor); // render in correct colour\n      ctx.font = fontOpts.string;\n\n      // Horizontal\n      if (me.isHorizontal()) {\n        titleX = left + (right - left) / 2; // midpoint of the width\n        titleY = top + offset;\n        maxWidth = right - left;\n      } else {\n        titleX = opts.position === 'left' ? left + offset : right - offset;\n        titleY = top + (bottom - top) / 2;\n        maxWidth = bottom - top;\n        rotation = Math.PI * (opts.position === 'left' ? -0.5 : 0.5);\n      }\n      ctx.save();\n      ctx.translate(titleX, titleY);\n      ctx.rotate(rotation);\n      ctx.textAlign = 'center';\n      ctx.textBaseline = 'middle';\n      var text = opts.text;\n      if (helpers$1.isArray(text)) {\n        var y = 0;\n        for (var i = 0; i < text.length; ++i) {\n          ctx.fillText(text[i], 0, y, maxWidth);\n          y += lineHeight;\n        }\n      } else {\n        ctx.fillText(text, 0, 0, maxWidth);\n      }\n      ctx.restore();\n    }\n  });\n  function createNewTitleBlockAndAttach(chart, titleOpts) {\n    var title = new Title({\n      ctx: chart.ctx,\n      options: titleOpts,\n      chart: chart\n    });\n    core_layouts.configure(chart, title, titleOpts);\n    core_layouts.addBox(chart, title);\n    chart.titleBlock = title;\n  }\n  var plugin_title = {\n    id: 'title',\n    /**\r\n     * Backward compatibility: since 2.1.5, the title is registered as a plugin, making\r\n     * Chart.Title obsolete. To avoid a breaking change, we export the Title as part of\r\n     * the plugin, which one will be re-exposed in the chart.js file.\r\n     * https://github.com/chartjs/Chart.js/pull/2640\r\n     * @private\r\n     */\n    _element: Title,\n    beforeInit: function (chart) {\n      var titleOpts = chart.options.title;\n      if (titleOpts) {\n        createNewTitleBlockAndAttach(chart, titleOpts);\n      }\n    },\n    beforeUpdate: function (chart) {\n      var titleOpts = chart.options.title;\n      var titleBlock = chart.titleBlock;\n      if (titleOpts) {\n        helpers$1.mergeIf(titleOpts, core_defaults.global.title);\n        if (titleBlock) {\n          core_layouts.configure(chart, titleBlock, titleOpts);\n          titleBlock.options = titleOpts;\n        } else {\n          createNewTitleBlockAndAttach(chart, titleOpts);\n        }\n      } else if (titleBlock) {\n        core_layouts.removeBox(chart, titleBlock);\n        delete chart.titleBlock;\n      }\n    }\n  };\n  var plugins = {};\n  var filler = plugin_filler;\n  var legend = plugin_legend;\n  var title = plugin_title;\n  plugins.filler = filler;\n  plugins.legend = legend;\n  plugins.title = title;\n\n  /**\r\n   * @namespace Chart\r\n   */\n\n  core_controller.helpers = helpers$1;\n\n  // @todo dispatch these helpers into appropriated helpers/helpers.* file and write unit tests!\n  core_helpers();\n  core_controller._adapters = core_adapters;\n  core_controller.Animation = core_animation;\n  core_controller.animationService = core_animations;\n  core_controller.controllers = controllers;\n  core_controller.DatasetController = core_datasetController;\n  core_controller.defaults = core_defaults;\n  core_controller.Element = core_element;\n  core_controller.elements = elements;\n  core_controller.Interaction = core_interaction;\n  core_controller.layouts = core_layouts;\n  core_controller.platform = platform;\n  core_controller.plugins = core_plugins;\n  core_controller.Scale = core_scale;\n  core_controller.scaleService = core_scaleService;\n  core_controller.Ticks = core_ticks;\n  core_controller.Tooltip = core_tooltip;\n\n  // Register built-in scales\n\n  core_controller.helpers.each(scales, function (scale, type) {\n    core_controller.scaleService.registerScaleType(type, scale, scale._defaults);\n  });\n\n  // Load to register built-in adapters (as side effects)\n\n  // Loading built-in plugins\n\n  for (var k in plugins) {\n    if (plugins.hasOwnProperty(k)) {\n      core_controller.plugins.register(plugins[k]);\n    }\n  }\n  core_controller.platform.initialize();\n  var src = core_controller;\n  if (typeof window !== 'undefined') {\n    window.Chart = core_controller;\n  }\n\n  // DEPRECATIONS\n\n  /**\r\n   * Provided for backward compatibility, not available anymore\r\n   * @namespace Chart.Chart\r\n   * @deprecated since version 2.8.0\r\n   * @todo remove at version 3\r\n   * @private\r\n   */\n  core_controller.Chart = core_controller;\n\n  /**\r\n   * Provided for backward compatibility, not available anymore\r\n   * @namespace Chart.Legend\r\n   * @deprecated since version 2.1.5\r\n   * @todo remove at version 3\r\n   * @private\r\n   */\n  core_controller.Legend = plugins.legend._element;\n\n  /**\r\n   * Provided for backward compatibility, not available anymore\r\n   * @namespace Chart.Title\r\n   * @deprecated since version 2.1.5\r\n   * @todo remove at version 3\r\n   * @private\r\n   */\n  core_controller.Title = plugins.title._element;\n\n  /**\r\n   * Provided for backward compatibility, use Chart.plugins instead\r\n   * @namespace Chart.pluginService\r\n   * @deprecated since version 2.1.5\r\n   * @todo remove at version 3\r\n   * @private\r\n   */\n  core_controller.pluginService = core_controller.plugins;\n\n  /**\r\n   * Provided for backward compatibility, inheriting from Chart.PlugingBase has no\r\n   * effect, instead simply create/register plugins via plain JavaScript objects.\r\n   * @interface Chart.PluginBase\r\n   * @deprecated since version 2.5.0\r\n   * @todo remove at version 3\r\n   * @private\r\n   */\n  core_controller.PluginBase = core_controller.Element.extend({});\n\n  /**\r\n   * Provided for backward compatibility, use Chart.helpers.canvas instead.\r\n   * @namespace Chart.canvasHelpers\r\n   * @deprecated since version 2.6.0\r\n   * @todo remove at version 3\r\n   * @private\r\n   */\n  core_controller.canvasHelpers = core_controller.helpers.canvas;\n\n  /**\r\n   * Provided for backward compatibility, use Chart.layouts instead.\r\n   * @namespace Chart.layoutService\r\n   * @deprecated since version 2.7.3\r\n   * @todo remove at version 3\r\n   * @private\r\n   */\n  core_controller.layoutService = core_controller.layouts;\n\n  /**\r\n   * Provided for backward compatibility, not available anymore.\r\n   * @namespace Chart.LinearScaleBase\r\n   * @deprecated since version 2.8\r\n   * @todo remove at version 3\r\n   * @private\r\n   */\n  core_controller.LinearScaleBase = scale_linearbase;\n\n  /**\r\n   * Provided for backward compatibility, instead we should create a new Chart\r\n   * by setting the type in the config (`new Chart(id, {type: '{chart-type}'}`).\r\n   * @deprecated since version 2.8.0\r\n   * @todo remove at version 3\r\n   */\n  core_controller.helpers.each(['Bar', 'Bubble', 'Doughnut', 'Line', 'PolarArea', 'Radar', 'Scatter'], function (klass) {\n    core_controller[klass] = function (ctx, cfg) {\n      return new core_controller(ctx, core_controller.helpers.merge(cfg || {}, {\n        type: klass.charAt(0).toLowerCase() + klass.slice(1)\n      }));\n    };\n  });\n  return src;\n});", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}