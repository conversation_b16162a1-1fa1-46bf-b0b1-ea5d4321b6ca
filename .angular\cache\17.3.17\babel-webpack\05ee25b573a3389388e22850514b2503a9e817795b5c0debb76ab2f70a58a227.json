{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.count = void 0;\nvar reduce_1 = require(\"./reduce\");\nfunction count(predicate) {\n  return reduce_1.reduce(function (total, value, i) {\n    return !predicate || predicate(value, i) ? total + 1 : total;\n  }, 0);\n}\nexports.count = count;\n//# sourceMappingURL=count.js.map", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}