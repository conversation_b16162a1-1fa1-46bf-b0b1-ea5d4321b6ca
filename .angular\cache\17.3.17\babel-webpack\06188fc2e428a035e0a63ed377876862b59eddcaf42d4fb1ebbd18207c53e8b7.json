{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.subscribeOn = void 0;\nvar lift_1 = require(\"../util/lift\");\nfunction subscribeOn(scheduler, delay) {\n  if (delay === void 0) {\n    delay = 0;\n  }\n  return lift_1.operate(function (source, subscriber) {\n    subscriber.add(scheduler.schedule(function () {\n      return source.subscribe(subscriber);\n    }, delay));\n  });\n}\nexports.subscribeOn = subscribeOn;\n//# sourceMappingURL=subscribeOn.js.map", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}