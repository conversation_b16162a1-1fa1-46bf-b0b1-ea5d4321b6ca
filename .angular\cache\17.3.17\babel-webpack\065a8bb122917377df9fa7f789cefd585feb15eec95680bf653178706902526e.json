{"ast": null, "code": "import { HttpClient } from '@angular/common/http';\nimport { ActivatedRoute, Router } from '@angular/router';\nimport { WebapiService } from '../webapi.service';\nimport html2canvas from 'html2canvas';\nimport * as jspdf from 'jspdf';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"../webapi.service\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"../../../node_modules/@angular/forms/index\";\nimport * as i6 from \"../reversepipe.pipe\";\nconst _c0 = (a0, a1) => ({\n  \"margin-top\": a0,\n  \"margin-bottom\": a1\n});\nconst _c1 = a0 => ({\n  \"color\": a0\n});\nfunction PrintsaleoderlistComponent_div_32_th_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 80);\n    i0.ɵɵtext(1, \"\\u0E19\\u0E49\\u0E33\\u0E2B\\u0E19\\u0E31\\u0E01\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PrintsaleoderlistComponent_div_32_th_56_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 80);\n    i0.ɵɵtext(1, \"Weight\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PrintsaleoderlistComponent_div_32_tr_63_td_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 86);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"number\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r1 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(2, 1, item_r1.totalweight, \"1.2-2\"));\n  }\n}\nfunction PrintsaleoderlistComponent_div_32_tr_63_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\", 81)(1, \"td\", 82);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\", 83);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\", 84);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(7, PrintsaleoderlistComponent_div_32_tr_63_td_7_Template, 3, 4, \"td\", 85);\n    i0.ɵɵelementStart(8, \"td\", 86);\n    i0.ɵɵtext(9);\n    i0.ɵɵpipe(10, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"td\", 87);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"td\", 87);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"td\", 87);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"td\", 88);\n    i0.ɵɵtext(18);\n    i0.ɵɵpipe(19, \"number\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r1 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(17, _c1, ctx_r1.getdisplayline(item_r1.lineindex)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r1.lineindex);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"\", item_r1.ItemId, \" \", item_r1.Name, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r1.SalesQty);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.test);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(10, 11, item_r1.PriceUnit, \"1.2-2\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(item_r1.IVZ_Percent1_CT);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r1.IVZ_Percent2_CT);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r1.IVZ_Percent3_CT);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(19, 14, item_r1.LineAmount, \"1.2-2\"));\n  }\n}\nfunction PrintsaleoderlistComponent_div_32_td_68_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"td\", 64);\n  }\n}\nfunction PrintsaleoderlistComponent_div_32_td_78_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"td\", 64);\n  }\n}\nfunction PrintsaleoderlistComponent_div_32_td_88_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"td\", 64);\n  }\n}\nfunction PrintsaleoderlistComponent_div_32_td_98_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"td\", 64);\n  }\n}\nfunction PrintsaleoderlistComponent_div_32_td_108_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"td\", 64);\n  }\n}\nfunction PrintsaleoderlistComponent_div_32_td_118_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"td\", 64);\n  }\n}\nfunction PrintsaleoderlistComponent_div_32_td_128_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"td\", 67);\n  }\n}\nfunction PrintsaleoderlistComponent_div_32_tfoot_135_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tfoot\", 89)(1, \"tr\")(2, \"td\", 90);\n    i0.ɵɵtext(3, \"\\u0E2B\\u0E21\\u0E32\\u0E22\\u0E40\\u0E2B\\u0E15\\u0E38* \");\n    i0.ɵɵelementStart(4, \"label\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"td\", 91);\n    i0.ɵɵtext(7, \"\\u0E19\\u0E49\\u0E33\\u0E2B\\u0E19\\u0E31\\u0E01\\u0E23\\u0E27\\u0E21\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"td\", 92);\n    i0.ɵɵtext(9, \"\\u0E23\\u0E32\\u0E04\\u0E32\\u0E23\\u0E27\\u0E21\\u0E17\\u0E31\\u0E49\\u0E07\\u0E2A\\u0E34\\u0E49\\u0E19(TOTAL)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"td\", 93);\n    i0.ɵɵtext(11);\n    i0.ɵɵpipe(12, \"myCurrency\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"tr\")(14, \"td\", 94);\n    i0.ɵɵtext(15);\n    i0.ɵɵpipe(16, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"td\", 92);\n    i0.ɵɵtext(18, \"\\u0E08\\u0E33\\u0E19\\u0E27\\u0E19\\u0E20\\u0E32\\u0E29\\u0E35\\u0E21\\u0E39\\u0E25\\u0E04\\u0E48\\u0E32\\u0E40\\u0E1E\\u0E34\\u0E48\\u0E21\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"td\", 93)(20, \"span\", 95);\n    i0.ɵɵtext(21, \"0\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(22);\n    i0.ɵɵpipe(23, \"myCurrency\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(24, \"tr\")(25, \"td\", 96);\n    i0.ɵɵtext(26);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"td\", 97);\n    i0.ɵɵtext(28, \"\\u0E23\\u0E27\\u0E21\\u0E17\\u0E31\\u0E49\\u0E07\\u0E2A\\u0E34\\u0E49\\u0E19(TOTAL)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"td\", 98);\n    i0.ɵɵtext(30);\n    i0.ɵɵpipe(31, \"myCurrency\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const itemhead_r3 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r1.remark);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(22, _c1, ctx_r1.getdisplayfoot(itemhead_r3.closefoot)));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(24, _c1, ctx_r1.getdisplayfoot(itemhead_r3.closefoot)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(12, 13, ctx_r1.sumtotal));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(26, _c1, ctx_r1.getdisplayfoot(itemhead_r3.closefoot)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(16, 15, ctx_r1.Totalweight, \"1.2-2\"), \" KG.\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(28, _c1, ctx_r1.getdisplayfoot(itemhead_r3.closefoot)));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(30, _c1, ctx_r1.getdisplayvat(ctx_r1.vat, itemhead_r3.numpage)));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(23, 18, ctx_r1.vat));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(32, _c1, ctx_r1.getdisplayfoot(itemhead_r3.closefoot)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.pricethai);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(34, _c1, ctx_r1.getdisplayfoot(itemhead_r3.closefoot)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(31, 20, ctx_r1.total));\n  }\n}\nfunction PrintsaleoderlistComponent_div_32_tfoot_136_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \"\\u0E08\\u0E33\\u0E19\\u0E27\\u0E19\\u0E20\\u0E32\\u0E29\\u0E35\\u0E21\\u0E39\\u0E25\\u0E04\\u0E48\\u0E32\\u0E40\\u0E1E\\u0E34\\u0E48\\u0E21\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PrintsaleoderlistComponent_div_32_tfoot_136_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tfoot\", 89)(1, \"tr\")(2, \"td\", 90);\n    i0.ɵɵtext(3, \"\\u0E2B\\u0E21\\u0E32\\u0E22\\u0E40\\u0E2B\\u0E15\\u0E38* \");\n    i0.ɵɵelementStart(4, \"label\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"td\", 99);\n    i0.ɵɵtext(7, \"\\u0E23\\u0E32\\u0E04\\u0E32\\u0E23\\u0E27\\u0E21\\u0E17\\u0E31\\u0E49\\u0E07\\u0E2A\\u0E34\\u0E49\\u0E19(TOTAL)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"td\", 93);\n    i0.ɵɵtext(9);\n    i0.ɵɵpipe(10, \"number\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"tr\")(12, \"td\", 99);\n    i0.ɵɵtemplate(13, PrintsaleoderlistComponent_div_32_tfoot_136_div_13_Template, 2, 0, \"div\", 100);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"td\", 93)(15, \"span\", 95);\n    i0.ɵɵtext(16, \"0\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(17);\n    i0.ɵɵpipe(18, \"myCurrency\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"tr\")(20, \"td\", 101);\n    i0.ɵɵtext(21);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"td\", 102);\n    i0.ɵɵtext(23, \"\\u0E23\\u0E27\\u0E21\\u0E17\\u0E31\\u0E49\\u0E07\\u0E2A\\u0E34\\u0E49\\u0E19(TOTAL)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"td\", 98);\n    i0.ɵɵtext(25);\n    i0.ɵɵpipe(26, \"number\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const itemhead_r3 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r1.remark);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(19, _c1, ctx_r1.getdisplayfoot(itemhead_r3.closefoot)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(10, 11, ctx_r1.sumtotal, \"1.2-2\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.salehderprint[0].vattype == \"VAT\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(21, _c1, ctx_r1.getdisplayfoot(itemhead_r3.closefoot)));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(23, _c1, ctx_r1.getdisplayvat(ctx_r1.vat, itemhead_r3.numpage)));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(18, 14, ctx_r1.vat), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(25, _c1, ctx_r1.getdisplayfoot(itemhead_r3.closefoot)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.pricethai);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(27, _c1, ctx_r1.getdisplayfoot(itemhead_r3.closefoot)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(26, 16, ctx_r1.total, \"1.2-2\"));\n  }\n}\nfunction PrintsaleoderlistComponent_div_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35)(1, \"div\", 36)(2, \"div\", 37)(3, \"div\", 38);\n    i0.ɵɵelement(4, \"img\", 39);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 37)(6, \"div\", 38)(7, \"table\", 40)(8, \"thead\", 41)(9, \"th\", 42);\n    i0.ɵɵtext(10, \"\\u0E43\\u0E1A\\u0E40\\u0E2A\\u0E19\\u0E2D\\u0E23\\u0E32\\u0E04\\u0E32/QUOTATION\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"tbody\", 43)(12, \"tr\")(13, \"td\", 44);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"td\", 45);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"tr\")(18, \"td\", 46);\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"td\", 47);\n    i0.ɵɵtext(21);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(22, \"tr\")(23, \"td\", 47);\n    i0.ɵɵtext(24, \"TEL. \");\n    i0.ɵɵelementEnd()()()()()();\n    i0.ɵɵelementStart(25, \"div\", 37)(26, \"div\", 48)(27, \"label\", 49);\n    i0.ɵɵtext(28, \"\\u0E02\\u0E2D\\u0E40\\u0E2A\\u0E19\\u0E2D\\u0E23\\u0E32\\u0E04\\u0E32\\u0E41\\u0E25\\u0E30\\u0E40\\u0E07\\u0E37\\u0E48\\u0E2D\\u0E19\\u0E44\\u0E02\\u0E2A\\u0E33\\u0E2B\\u0E23\\u0E31\\u0E1A\\u0E17\\u0E48\\u0E32\\u0E19\\u0E14\\u0E31\\u0E07\\u0E19\\u0E35\\u0E49\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(29, \"br\");\n    i0.ɵɵelementStart(30, \"label\");\n    i0.ɵɵtext(31, \"We are please to submit you the following described here in at price, items and terms stated :\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(32, \"div\", 50)(33, \"table\", 51)(34, \"thead\", 52)(35, \"tr\", 53)(36, \"th\", 54);\n    i0.ɵɵtext(37, \"\\u0E25\\u0E33\\u0E14\\u0E31\\u0E1A\\u0E17\\u0E35\\u0E48\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(38, \"th\", 55);\n    i0.ɵɵtext(39, \"\\u0E23\\u0E32\\u0E22\\u0E01\\u0E32\\u0E23\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(40, \"th\", 56);\n    i0.ɵɵtext(41, \"\\u0E08\\u0E33\\u0E19\\u0E27\\u0E19\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(42, PrintsaleoderlistComponent_div_32_th_42_Template, 2, 0, \"th\", 57);\n    i0.ɵɵelementStart(43, \"th\", 55);\n    i0.ɵɵtext(44, \"\\u0E23\\u0E32\\u0E04\\u0E32\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(45, \"th\", 58);\n    i0.ɵɵtext(46, \"\\u0E2A\\u0E48\\u0E27\\u0E19\\u0E25\\u0E14\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(47, \"th\", 59);\n    i0.ɵɵtext(48, \"\\u0E08\\u0E33\\u0E19\\u0E27\\u0E19\\u0E40\\u0E07\\u0E34\\u0E19\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(49, \"tr\", 60)(50, \"th\", 54);\n    i0.ɵɵtext(51, \"ITEM\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(52, \"th\", 55);\n    i0.ɵɵtext(53, \"DESCRIPTION\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(54, \"th\", 56);\n    i0.ɵɵtext(55, \"Quantity\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(56, PrintsaleoderlistComponent_div_32_th_56_Template, 2, 0, \"th\", 57);\n    i0.ɵɵelementStart(57, \"th\", 55);\n    i0.ɵɵtext(58, \"Price\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(59, \"th\", 58);\n    i0.ɵɵtext(60, \"Discount\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(61, \"th\");\n    i0.ɵɵtext(62, \"Amount\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(63, PrintsaleoderlistComponent_div_32_tr_63_Template, 20, 19, \"tr\", 61);\n    i0.ɵɵelementStart(64, \"tr\", 62);\n    i0.ɵɵelement(65, \"td\", 63)(66, \"td\", 64)(67, \"td\", 64);\n    i0.ɵɵtemplate(68, PrintsaleoderlistComponent_div_32_td_68_Template, 1, 0, \"td\", 65);\n    i0.ɵɵelement(69, \"td\", 64)(70, \"td\", 64)(71, \"td\", 64)(72, \"td\", 64)(73, \"td\", 64);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(74, \"tr\", 62);\n    i0.ɵɵelement(75, \"td\", 63)(76, \"td\", 64)(77, \"td\", 64);\n    i0.ɵɵtemplate(78, PrintsaleoderlistComponent_div_32_td_78_Template, 1, 0, \"td\", 65);\n    i0.ɵɵelement(79, \"td\", 64)(80, \"td\", 64)(81, \"td\", 64)(82, \"td\", 64)(83, \"td\", 64);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(84, \"tr\", 62);\n    i0.ɵɵelement(85, \"td\", 63)(86, \"td\", 64)(87, \"td\", 64);\n    i0.ɵɵtemplate(88, PrintsaleoderlistComponent_div_32_td_88_Template, 1, 0, \"td\", 65);\n    i0.ɵɵelement(89, \"td\", 64)(90, \"td\", 64)(91, \"td\", 64)(92, \"td\", 64)(93, \"td\", 64);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(94, \"tr\", 62);\n    i0.ɵɵelement(95, \"td\", 63)(96, \"td\", 64)(97, \"td\", 64);\n    i0.ɵɵtemplate(98, PrintsaleoderlistComponent_div_32_td_98_Template, 1, 0, \"td\", 65);\n    i0.ɵɵelement(99, \"td\", 64)(100, \"td\", 64)(101, \"td\", 64)(102, \"td\", 64)(103, \"td\", 64);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(104, \"tr\", 62);\n    i0.ɵɵelement(105, \"td\", 63)(106, \"td\", 64)(107, \"td\", 64);\n    i0.ɵɵtemplate(108, PrintsaleoderlistComponent_div_32_td_108_Template, 1, 0, \"td\", 65);\n    i0.ɵɵelement(109, \"td\", 64)(110, \"td\", 64)(111, \"td\", 64)(112, \"td\", 64)(113, \"td\", 64);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(114, \"tr\", 62);\n    i0.ɵɵelement(115, \"td\", 63)(116, \"td\", 64)(117, \"td\", 64);\n    i0.ɵɵtemplate(118, PrintsaleoderlistComponent_div_32_td_118_Template, 1, 0, \"td\", 65);\n    i0.ɵɵelement(119, \"td\", 64)(120, \"td\", 64)(121, \"td\", 64)(122, \"td\", 64)(123, \"td\", 64);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(124, \"tr\", 62);\n    i0.ɵɵelement(125, \"td\", 66)(126, \"td\", 67)(127, \"td\", 67);\n    i0.ɵɵtemplate(128, PrintsaleoderlistComponent_div_32_td_128_Template, 1, 0, \"td\", 68);\n    i0.ɵɵelement(129, \"td\", 67)(130, \"td\", 67)(131, \"td\", 67)(132, \"td\", 67)(133, \"td\", 67);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(134, \"tbody\", 69);\n    i0.ɵɵtemplate(135, PrintsaleoderlistComponent_div_32_tfoot_135_Template, 32, 36, \"tfoot\", 70)(136, PrintsaleoderlistComponent_div_32_tfoot_136_Template, 27, 29, \"tfoot\", 70);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(137, \"div\", 71)(138, \"div\", 72)(139, \"div\", 73)(140, \"table\", 74)(141, \"tbody\")(142, \"tr\")(143, \"td\")(144, \"label\", 75);\n    i0.ɵɵtext(145, \" \\u00A0 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(146, \"label\", 75);\n    i0.ɵɵtext(147, \" \\u00A0\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(148, \"hr\", 76);\n    i0.ɵɵelementStart(149, \"label\", 77);\n    i0.ɵɵtext(150, \"Purchaser/\\u0E1C\\u0E39\\u0E49\\u0E2D\\u0E19\\u0E38\\u0E21\\u0E31\\u0E15\\u0E34\\u0E0B\\u0E37\\u0E49\\u0E2D\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(151, \"label\", 78);\n    i0.ɵɵtext(152, \"Date____/____/____\");\n    i0.ɵɵelementEnd()()()()()();\n    i0.ɵɵelementStart(153, \"div\", 73)(154, \"table\", 74)(155, \"tbody\")(156, \"tr\")(157, \"td\")(158, \"p\", 79);\n    i0.ɵɵtext(159);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(160, \"p\", 79);\n    i0.ɵɵtext(161);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(162, \"hr\", 76);\n    i0.ɵɵelementStart(163, \"label\", 77);\n    i0.ɵɵtext(164, \"Sale/\\u0E1E\\u0E19\\u0E31\\u0E01\\u0E07\\u0E32\\u0E19\\u0E02\\u0E32\\u0E22\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(165, \"label\", 78);\n    i0.ɵɵtext(166, \"Date____/____/____\");\n    i0.ɵɵelementEnd()()()()()();\n    i0.ɵɵelementStart(167, \"div\", 73)(168, \"table\", 74)(169, \"tbody\")(170, \"tr\")(171, \"td\")(172, \"label\", 75);\n    i0.ɵɵtext(173, \" \\u00A0\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(174, \"label\", 75);\n    i0.ɵɵtext(175, \"\\u00A0\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(176, \"hr\", 76);\n    i0.ɵɵelementStart(177, \"label\", 77);\n    i0.ɵɵtext(178, \"Manager/\\u0E1C\\u0E39\\u0E49\\u0E08\\u0E31\\u0E14\\u0E01\\u0E32\\u0E23\\u0E1D\\u0E48\\u0E32\\u0E22\\u0E02\\u0E32\\u0E22\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(179, \"label\", 78);\n    i0.ɵɵtext(180, \"Date____/____/____\");\n    i0.ɵɵelementEnd()()()()()()()()()()();\n  }\n  if (rf & 2) {\n    const itemhead_r3 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction2(19, _c0, ctx_r1.getnumpage(itemhead_r3.numpage), ctx_r1.getnumpageMAX(itemhead_r3.numpage)));\n    i0.ɵɵadvance(14);\n    i0.ɵɵtextInterpolate1(\" Company : \", ctx_r1.getnamecustomer, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\\u0E40\\u0E25\\u0E02\\u0E17\\u0E35\\u0E48/ No. :\", ctx_r1.salehderprint[0].id, \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" Address : \", ctx_r1.getaddresscustomer, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\\u0E27\\u0E31\\u0E19\\u0E17\\u0E35\\u0E48 Date :\", ctx_r1.salehderprint[0].dateid, \"\");\n    i0.ɵɵadvance(21);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.test);\n    i0.ɵɵadvance(14);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.test);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngForOf\", itemhead_r3.datasale);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.test);\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.test);\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.test);\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.test);\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.test);\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.test);\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.test);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.test);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.test);\n    i0.ɵɵadvance(23);\n    i0.ɵɵtextInterpolate(ctx_r1.datalogin[0].name_user);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"Mobile : \", ctx_r1.datalogin[0].mobile, \"\");\n  }\n}\nexport let PrintsaleoderlistComponent = /*#__PURE__*/(() => {\n  class PrintsaleoderlistComponent {\n    getnumpage(Numpage) {\n      if (Numpage == 1) {\n        return '5px';\n      } else {\n        return '0px';\n      }\n    }\n    getnumpageMAX(Numpage) {\n      this.numpage();\n      if (Numpage == this.Maxpage) {\n        return '0px';\n      } else if (Numpage != this.Maxpage && this.pageHeightmin == 295) {\n        return '63px';\n      } else if (Numpage != this.Maxpage && this.pageHeightmin == 290) {\n        return '63px';\n      } else {\n        return '50px';\n      }\n    }\n    getdisplayline(value) {\n      if (value === 'W') {\n        return '#FFFFFF';\n      } else {\n        return '#000000';\n      }\n    }\n    numpage() {\n      this.Maxpage = this.pagepdf.length;\n    }\n    getdisplayvat(value, value2) {\n      this.numpage();\n      if (value == 0 && value2 == this.Maxpage) {\n        return '#000000';\n      } else if (value == 0 && value2 == this.Maxpage) {\n        return '#FFFFFF';\n      } else if (value > 0 && value2 < this.Maxpage) {\n        return '#ffffff';\n      } else if (value > 0 && value2 == this.Maxpage) {\n        return '#ffffff';\n      }\n    }\n    getdisplayfoot(value) {\n      if (value == true) {\n        return '#000000';\n      } else {\n        return '#FFFFFF';\n      }\n    }\n    constructor(http, route, router, service) {\n      this.http = http;\n      this.route = route;\n      this.router = router;\n      this.service = service;\n      this.pageHeightmin = 300;\n      this.test = true;\n      this.marbot = '400';\n      // lineheight='0px'; //40\n      // pageHeightmin =305;\n      this.widthpage = 'auto';\n      this.salelistcut = [];\n      this.pagepdf = [];\n      this.salehderprint = [];\n      //pageHeightmin =300;\n      this.salelistview = [];\n      this.salelist = [];\n      this.pricethai = '';\n      this.closefoottabel = 'none';\n      this.getnamecustomer = '';\n      this.getaddresscustomer = '';\n      this.remark = '';\n      this.url = service.geturlservice();\n      this.idsoedit = this.route.snapshot.queryParams.idso;\n      //alert('H='+window.screen.availHeight+' / W='+window.screen.availWidth);\n      this.datalogin = JSON.parse(sessionStorage.getItem('login'));\n      if (this.datalogin == null) {\n        this.router.navigate(['login']);\n      } else {}\n      //alert(this.datalogin[0].name_user+ '//'+this.datalogin[0].mobile)\n    }\n    ngOnInit() {\n      this.loaddataprint(this.idsoedit);\n      this.test = false;\n    }\n    loadform(ck) {\n      if (ck == true) {\n        this.test = true;\n      } else {\n        this.test = false;\n      }\n    }\n    captureScreen() {\n      /* alert(this.pageHeightmin)\n      alert(JSON.stringify(this.dataprint))*/\n      var data = document.getElementById('fontpage');\n      html2canvas(data).then(canvas => {\n        var imgWidth = 210; //208\n        var pageHeight = this.pageHeightmin;\n        var imgHeight = canvas.height * imgWidth / canvas.width;\n        var heightLeft = imgHeight;\n        var del = Math.floor(heightLeft / pageHeight) + 1;\n        const contentDataURL = canvas.toDataURL('image/jpeg', 1.0);\n        let pdf = new jspdf('p', 'mm', 'a4');\n        var position = 0;\n        pdf.addImage(contentDataURL, 'JPEG', 0, position, imgWidth, imgHeight);\n        heightLeft -= pageHeight;\n        while (heightLeft >= 0) {\n          position = heightLeft - imgHeight;\n          pdf.addPage();\n          pdf.addImage(contentDataURL, 'JPEG', 0, position, imgWidth, imgHeight);\n          heightLeft -= pageHeight;\n        }\n        /*pdf.deletePage(del);*/\n        pdf.save('SalesOder.PDF');\n      });\n    }\n    pcprint() {\n      this.pageHeightmin = 300;\n      this.marbot = '35%';\n      // this.lineheight='40px';\n      this.widthpage = '1500px';\n      var imgWidth = window.screen.availHeight / 4 + 10;\n      //alert(imgWidth+'/'+window.screen.availHeight);\n      var data = document.getElementById('fontpage');\n      let pdf = new jspdf();\n      html2canvas(data, {\n        width: 1500,\n        height: 6000\n      }).then(canvas => {\n        //208\n        var pageHeight = this.pageHeightmin;\n        var imgHeight = canvas.height * imgWidth / canvas.width;\n        var test = canvas.height * imgWidth / canvas.width;\n        var heightLeft = imgHeight;\n        //var del = Math.floor(heightLeft/pageHeight)+1\n        const contentDataURL = canvas.toDataURL('image/jpeg', 1.0);\n        var position = 0;\n        pdf.addImage(contentDataURL, 'JPEG', 0, position, imgWidth, imgHeight);\n        heightLeft -= pageHeight;\n        //alert(position+'/'+imgWidth+'/'+imgHeight);\n        if (this.pagepdf.length > 1) {\n          position = heightLeft - imgHeight;\n          pdf.addPage();\n          pdf.addImage(contentDataURL, 'JPEG', 0, position, imgWidth, imgHeight);\n          //heightLeft -= pageHeight;\n          //alert('INIF'+position+'/'+imgWidth+'/'+imgHeight);\n        }\n        pdf.save('SalesOder.PDF');\n        //pdf.deletePage(del);\n      });\n    }\n    tabletprint() {\n      this.marbot = '50%';\n      //  this.lineheight='40px';\n      this.pageHeightmin = 310;\n      this.widthpage = '1500px';\n      /* alert(this.pageHeightmin)\n      alert(JSON.stringify(this.dataprint))*/\n      var data = document.getElementById('fontpage');\n      html2canvas(data).then(canvas => {\n        var imgWidth = 208; //208\n        var pageHeight = this.pageHeightmin;\n        var imgHeight = canvas.height * imgWidth / canvas.width;\n        var heightLeft = imgHeight;\n        // var del = Math.floor(heightLeft/pageHeight)+1\n        const contentDataURL = canvas.toDataURL('image/jpeg', 1.0);\n        let pdf = new jspdf('p', 'mm', 'a4');\n        var position = 0;\n        pdf.addImage(contentDataURL, 'JPEG', 0, position, imgWidth, imgHeight);\n        heightLeft -= pageHeight;\n        while (heightLeft >= 0) {\n          position = heightLeft - imgHeight;\n          pdf.addPage();\n          pdf.addImage(contentDataURL, 'JPEG', 0, position, imgWidth, imgHeight);\n          heightLeft -= pageHeight;\n        }\n        /*pdf.deletePage(del);*/\n        pdf.save('SalesOder.PDF');\n      });\n    }\n    mobileprint() {}\n    loaddataprint(value) {\n      this.getaddresscustomer = '';\n      this.getnamecustomer = '';\n      this.http.get(this.url + 'find_saleheader/' + value).subscribe(res => {\n        //alert(res[0].remark);\n        this.remark = res[0].remark;\n        this.Maxpage = res.length;\n        this.salehderprint = res;\n        this.getsaloderlist(value);\n        this.getnamecustomer = res[0].InvName;\n        if (res[0].address != null || res[0].address != undefined) {\n          this.getaddresscustomer = res[0].address[0];\n        } else {\n          this.getaddresscustomer = '';\n        }\n        this.total = res[0].amount;\n        this.vat = parseFloat((res[0].amount / 1.07 * 0.07).toFixed(2));\n        if (res[0].TaxGroup === 'NoVAT' || res[0].vattype == \"VAT ไม่ยื่น\" && res[0].TaxGroup == \"DOM\") {\n          this.vat = 0;\n        }\n        //var sunmix=this.vat+res[0].amount\n        var sunmix = res[0].amount - this.vat;\n        this.sumtotal = parseFloat(sunmix.toFixed(2));\n        //alert(JSON.stringify(res));\n        this.pricethai = this.ArabicNumberToText(this.total.toFixed(2));\n      }, error => {\n        //return;\n      });\n    }\n    getsaloderlist(valueid) {\n      var numfor = 0;\n      var allline = 18;\n      var salelinenum = 0;\n      var linefor = 0;\n      var countpage = 1;\n      var ssline = 0;\n      var numemty = 0;\n      this.salelistview = [];\n      this.salelist = [];\n      this.http.get(this.url + 'find_saleline/' + valueid).subscribe(res => {\n        if (res.length > 0) {\n          this.Totalweight = 0;\n          for (var i = 0; i < res.length; i++) {\n            this.Totalweight += res[i].totalweight;\n          }\n          var numline = 1;\n          ssline = res.length % 18;\n          if (res.length > 18) {\n            countpage = Math.ceil(res.length / 18);\n          }\n          numemty = 18 - ssline;\n          for (var i = 0; i < res.length; i++) {\n            this.salelist.push({\n              lineindex: numline.toString(),\n              idline: res[i].idline,\n              id: res[i].id,\n              linenum: res[i].linenum,\n              CurrencyCode: res[i].CurrencyCode,\n              CustAccount: res[i].CustAccount,\n              CustGroup: res[i].CustGroup,\n              ShippingDateRequested: res[i].ShippingDateRequested,\n              ItemId: res[i].ItemId,\n              Name: res[i].Name,\n              SalesQty: res[i].SalesQty,\n              SalesType: res[i].SalesType,\n              InventSiteId: res[i].InventSiteId,\n              InventLocationId: res[i].InventLocationId,\n              PriceUnit: res[i].PriceUnit,\n              SalesUnit: res[i].SalesUnit,\n              SalesGroup: res[i].SalesGroup,\n              SalesCategory: res[i].SalesCategory,\n              TaxItemGroup: res[i].TaxItemGroup,\n              TaxGroup: res[i].TaxGroup,\n              CostPrice: res[i].CostPrice,\n              DefaultDimension: res[i].DefaultDimension,\n              IVZ_Percent1_CT: res[i].IVZ_Percent1_CT + '%',\n              IVZ_Percent2_CT: res[i].IVZ_Percent2_CT + '%',\n              IVZ_Percent3_CT: res[i].IVZ_Percent3_CT + '%',\n              BarCodeType: res[i].BarCodeType,\n              BarCode: res[i].BarCode,\n              LinePercent: res[i].LinePercent,\n              totaldisc: res[i].totaldisc,\n              LineAmount: res[i].LineAmount,\n              SalesId: res[i].SalesId,\n              packqty: res[i].packqty,\n              totalweight: res[i].totalweight,\n              disc1: res[i].disc1,\n              disc2: res[i].disc2,\n              disc3: res[i].disc3,\n              pkggroup: res[i].pkggroup,\n              checkpcs: res[i].checkpcs,\n              state: res[i].state,\n              stoderlisr: res[i].stoderlisr,\n              disstate: res[i].disstate,\n              eidtable: res[i].eidtable,\n              statest: res[i].statest\n            });\n            numline++;\n          }\n          for (var x = 0; x < numemty; x++) {\n            this.salelist.push({\n              lineindex: 'W',\n              idline: '',\n              id: '',\n              linenum: '',\n              CurrencyCode: '',\n              CustAccount: '',\n              CustGroup: '',\n              ShippingDateRequested: '',\n              ItemId: '',\n              Name: '',\n              SalesQty: '',\n              SalesType: '',\n              InventSiteId: '',\n              InventLocationId: '',\n              PriceUnit: '',\n              SalesUnit: '',\n              SalesGroup: '',\n              SalesCategory: '',\n              TaxItemGroup: '',\n              TaxGroup: '',\n              CostPrice: '',\n              DefaultDimension: '',\n              IVZ_Percent1_CT: '',\n              IVZ_Percent2_CT: '',\n              IVZ_Percent3_CT: '',\n              BarCodeType: '',\n              BarCode: '',\n              LinePercent: '',\n              totaldisc: '',\n              LineAmount: '',\n              SalesId: '',\n              packqty: '',\n              totalweight: '',\n              disc1: '',\n              disc2: '',\n              disc3: '',\n              pkggroup: '',\n              checkpcs: '',\n              state: '',\n              stoderlisr: '',\n              disstate: '',\n              eidtable: '',\n              statest: ''\n            });\n          }\n          var numin = 0;\n          var numli = 18;\n          for (var p = 0; p < countpage; p++) {\n            for (var z = numin; z < numli; z++) {\n              this.salelistcut.push({\n                lineindex: this.salelist[z].lineindex,\n                idline: this.salelist[z].idline,\n                id: this.salelist[z].id,\n                linenum: this.salelist[z].linenum,\n                CurrencyCode: this.salelist[z].CurrencyCode,\n                CustAccount: this.salelist[z].CustAccount,\n                CustGroup: this.salelist[z].CustGroup,\n                ShippingDateRequested: this.salelist[z].ShippingDateRequested,\n                ItemId: this.salelist[z].ItemId,\n                Name: this.salelist[z].Name,\n                SalesQty: this.salelist[z].SalesQty,\n                SalesType: this.salelist[z].SalesType,\n                InventSiteId: this.salelist[z].InventSiteId,\n                InventLocationId: this.salelist[z].InventLocationId,\n                PriceUnit: this.salelist[z].PriceUnit,\n                SalesUnit: this.salelist[z].SalesUnit,\n                SalesGroup: this.salelist[z].SalesGroup,\n                SalesCategory: this.salelist[z].SalesCategory,\n                TaxItemGroup: this.salelist[z].TaxItemGroup,\n                TaxGroup: this.salelist[z].TaxGroup,\n                CostPrice: this.salelist[z].CostPrice,\n                DefaultDimension: this.salelist[z].DefaultDimension,\n                IVZ_Percent1_CT: this.salelist[z].IVZ_Percent1_CT,\n                IVZ_Percent2_CT: this.salelist[z].IVZ_Percent2_CT,\n                IVZ_Percent3_CT: this.salelist[z].IVZ_Percent3_CT,\n                BarCodeType: this.salelist[z].BarCodeType,\n                BarCode: this.salelist[z].BarCode,\n                LinePercent: this.salelist[z].LinePercent,\n                totaldisc: this.salelist[z].totaldisc,\n                LineAmount: this.salelist[z].LineAmount,\n                SalesId: this.salelist[z].SalesId,\n                packqty: this.salelist[z].packqty,\n                totalweight: this.salelist[z].totalweight,\n                disc1: this.salelist[z].disc1,\n                disc2: this.salelist[z].disc2,\n                disc3: this.salelist[z].disc3,\n                pkggroup: this.salelist[z].pkggroup,\n                checkpcs: this.salelist[z].checkpcs,\n                state: this.salelist[z].state,\n                stoderlisr: this.salelist[z].stoderlisr,\n                disstate: this.salelist[z].disstate,\n                eidtable: this.salelist[z].eidtable,\n                statest: this.salelist[z].statest\n              });\n            }\n            numin += 18;\n            numli += 18;\n            this.pagepdf.push({\n              numpage: p + 1,\n              datasale: this.salelistcut,\n              closefoot: false\n            });\n            this.salelistcut = [];\n          }\n          //alert(JSON.stringify(this.pagepdf));\n        }\n        this.pagepdf[this.pagepdf.length - 1].closefoot = true;\n      });\n    }\n    blacktosaleoderlist() {\n      this.router.navigate(['solist1']);\n    }\n    ThaiNumberToText(Number) {\n      Number = Number.replace(/๐/gi, '0');\n      Number = Number.replace(/๑/gi, '1');\n      Number = Number.replace(/๒/gi, '2');\n      Number = Number.replace(/๓/gi, '3');\n      Number = Number.replace(/๔/gi, '4');\n      Number = Number.replace(/๕/gi, '5');\n      Number = Number.replace(/๖/gi, '6');\n      Number = Number.replace(/๗/gi, '7');\n      Number = Number.replace(/๘/gi, '8');\n      Number = Number.replace(/๙/gi, '9');\n      return this.ArabicNumberToText(Number);\n    }\n    ArabicNumberToText(Number) {\n      var Number = this.CheckNumber(Number);\n      var NumberArray = new Array(\"ศูนย์\", \"หนึ่ง\", \"สอง\", \"สาม\", \"สี่\", \"ห้า\", \"หก\", \"เจ็ด\", \"แปด\", \"เก้า\", \"สิบ\");\n      var DigitArray = new Array(\"\", \"สิบ\", \"ร้อย\", \"พัน\", \"หมื่น\", \"แสน\", \"ล้าน\");\n      var BahtText = \"\";\n      if (isNaN(Number)) {\n        return \"ข้อมูลนำเข้าไม่ถูกต้อง\";\n      } else {\n        if (Number - 0 > 9999999.9999) {\n          return \"ข้อมูลนำเข้าเกินขอบเขตที่ตั้งไว้\";\n        } else {\n          Number = Number.split(\".\");\n          if (Number[1].length > 0) {\n            Number[1] = Number[1].substring(0, 2);\n          }\n          var NumberLen = Number[0].length - 0;\n          for (var i = 0; i < NumberLen; i++) {\n            var tmp = Number[0].substring(i, i + 1) - 0;\n            if (tmp != 0) {\n              if (i == NumberLen - 1 && tmp == 1) {\n                BahtText += \"เอ็ด\";\n              } else if (i == NumberLen - 2 && tmp == 2) {\n                BahtText += \"ยี่\";\n              } else if (i == NumberLen - 2 && tmp == 1) {\n                BahtText += \"\";\n              } else {\n                BahtText += NumberArray[tmp];\n              }\n              BahtText += DigitArray[NumberLen - i - 1];\n            }\n          }\n          BahtText += \"บาท\";\n          if (Number[1] == \"0\" || Number[1] == \"00\") {\n            BahtText += \"ถ้วน\";\n          } else {\n            var DecimalLen = Number[1].length - 0;\n            var s = parseInt(Number[1]);\n            for (var i = 0; i < DecimalLen; i++) {\n              var tmp = Number[1].substring(i, i + 1) - 0;\n              if (tmp != 0) {\n                if (i == DecimalLen - 1 && tmp == 1 && s != 1) {\n                  BahtText += \"เอ็ด\";\n                } else if (i == DecimalLen - 2 && tmp == 2) {\n                  BahtText += \"ยี่\";\n                } else if (i == DecimalLen - 2 && tmp == 1) {\n                  BahtText += \"\";\n                } else {\n                  BahtText += NumberArray[tmp];\n                }\n                BahtText += DigitArray[DecimalLen - i - 1];\n              }\n            }\n            BahtText += \"สตางค์\";\n          }\n          return BahtText;\n        }\n      }\n    }\n    CheckNumber(Number) {\n      var decimal = false;\n      Number = Number.toString();\n      Number = Number.replace(/ |,|บาท|฿/gi, '');\n      for (var i = 0; i < Number.length; i++) {\n        if (Number[i] == '.') {\n          decimal = true;\n        }\n      }\n      if (decimal == false) {\n        Number = Number + '.00';\n      }\n      return Number;\n    }\n    static {\n      this.ɵfac = function PrintsaleoderlistComponent_Factory(t) {\n        return new (t || PrintsaleoderlistComponent)(i0.ɵɵdirectiveInject(i1.HttpClient), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.WebapiService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: PrintsaleoderlistComponent,\n        selectors: [[\"app-printsaleoderlist\"]],\n        decls: 58,\n        vars: 6,\n        consts: [[1, \"col-md-12\", \"col-sm-12\", \"col-lg-12\", \"text-center\", 2, \"margin-top\", \"15px\"], [\"role\", \"group\", \"aria-label\", \"Basic example\", 1, \"btn-group\", 2, \"z-index\", \"10\"], [\"data-toggle\", \"modal\", \"data-target\", \"#getaddress\", \"aria-expanded\", \"true\", 1, \"btn\", \"btn-outline-info\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-primary\", 3, \"click\"], [1, \"btn\", \"btn-outline-danger\", 3, \"click\"], [1, \"col-md-12\", \"col-sm-12\", \"col-lg-12\", \"text-center\", 2, \"z-index\", \"10\", \"margin-bottom\", \"0px\", \"margin-top\", \"3px\"], [1, \"col-auto\", \"my-1\"], [1, \"custom-control\", \"custom-checkbox\", \"mr-sm-2\"], [\"type\", \"checkbox\", \"id\", \"customControlAutosizing\", 1, \"custom-control-input\", 3, \"ngModelChange\", \"click\", \"ngModel\"], [\"for\", \"customControlAutosizing\", 1, \"custom-control-label\"], [1, \"custom-select\", \"custom-select-sm\", \"col-sm-2\", \"col-md-2\", \"col-lg-2\", 3, \"ngModelChange\", \"ngModel\"], [\"disabled\", \"\", \"value\", \"\"], [\"value\", \"305\"], [\"value\", \"300\"], [\"value\", \"295\"], [\"value\", \"290\"], [\"align\", \"center\", 1, \"container-fluid\"], [\"id\", \"fontpage\", \"align\", \"center\", 2, \"width\", \"1100px\", \"margin-top\", \"0px\"], [\"style\", \"padding-top: 1px; margin-bottom: 510px; width: 100%;\", 3, \"ngStyle\", 4, \"ngFor\", \"ngForOf\"], [\"id\", \"getaddress\", \"tabindex\", \"-1\", \"role\", \"dialog\", \"aria-labelledby\", \"exampleModalLabel\", \"aria-hidden\", \"true\", 1, \"modal\", \"fade\"], [\"role\", \"document\", 1, \"modal-dialog\"], [1, \"modal-content\"], [1, \"modal-header\"], [\"id\", \"exampleModalLabel\", 1, \"modal-title\"], [\"type\", \"button\", \"data-dismiss\", \"modal\", \"aria-label\", \"Close\", 1, \"close\"], [\"aria-hidden\", \"true\"], [1, \"modal-body\"], [1, \"from-froup\"], [\"for\", \"getnamecustomer\"], [\"name\", \"getnamecustomer\", \"id\", \"getnamecustomer\", \"cols\", \"20\", \"rows\", \"3\", 1, \"form-control\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"getaddresscustomer\"], [\"name\", \"getaddresscustomer\", \"id\", \"getaddresscustomer\", \"cols\", \"30\", \"rows\", \"5\", 1, \"form-control\", 3, \"ngModelChange\", \"ngModel\"], [1, \"modal-footer\"], [\"type\", \"button\", \"data-dismiss\", \"modal\", 1, \"btn\", \"btn-secondary\"], [\"type\", \"button\", \"data-dismiss\", \"modal\", 1, \"btn\", \"btn-primary\"], [2, \"padding-top\", \"1px\", \"margin-bottom\", \"510px\", \"width\", \"100%\", 3, \"ngStyle\"], [2, \"padding-left\", \"5px\", \"padding-right\", \"5px\"], [1, \"row\"], [1, \"col-md-12\", \"col-lg-12\", \"col-sm-12\"], [\"src\", \"assets/img/BGP.png\", \"width\", \"100%\", 2, \"padding-top\", \"5px\", \"margin-top\", \"10px\"], [2, \"margin-top\", \"1.5%\", \"height\", \"235px\", \"width\", \"100%\"], [1, \"coltbtop\", \"coltbbot\", \"coltbleft\", \"coltbrigth\"], [\"colspan\", \"4\", 1, \"text-center\", \"coltbtop\", \"coltbbot\", \"coltbleft\", \"coltbrigth\", 2, \"background-color\", \"#e0e6e7\"], [1, \"coltbbot\", 2, \"border\", \"2px\"], [1, \"coltbleft\"], [\"width\", \"300px\", 1, \"coltbbot\", \"coltbleft\", \"coltbrigth\"], [\"rowspan\", \"2\", 1, \"coltbbot\", \"coltbleft\", \"coltbrigth\"], [1, \"coltbbot\", \"coltbrigth\"], [1, \"col-md-12\", \"col-lg-12\", \"col-sm-12\", \"text-left\"], [2, \"margin-top\", \"15px\"], [1, \"col-md-12\", \"col-lg-12\", \"col-sm-12\", 2, \"padding-right\", \"0px\", \"padding-left\", \"0px\"], [\"cellpadding\", \"7\", 2, \"margin-top\", \"1%\", \"height\", \"295px\", \"width\", \"100%\"], [2, \"border\", \"1\", \"border-color\", \"black\"], [1, \"text-center\", \"coltbtop\", \"coltbleft\", \"coltbrigth\"], [1, \"coltbrigth\", 2, \"width\", \"53px\", \"padding\", \"0px\"], [1, \"coltbrigth\"], [1, \"coltbrigth\", 2, \"width\", \"50px\"], [\"width\", \"5%\", \"class\", \"coltbrigth\", 4, \"ngIf\"], [\"colspan\", \"3\", 1, \"coltbrigth\"], [2, \"width\", \"100px\"], [1, \"text-center\", \"coltbbot\", \"coltbleft\", \"coltbrigth\"], [\"class\", \"coltbleft coltbrigth\", 4, \"ngFor\", \"ngForOf\"], [2, \"background-color\", \"#fff\"], [\"align\", \"center\", 1, \"coltbrigth\", \"text-center\", 2, \"padding\", \"5px\", \"border-right\", \"1px solid black\", \"border-left\", \"1px solid black\"], [\"align\", \"center\", 1, \"text-center\", 2, \"padding\", \"5px\", \"border-right\", \"1px solid black\"], [\"align\", \"center\", \"style\", \"padding: 5px; border-right: 1px solid black;\", \"class\", \"text-center\", 4, \"ngIf\"], [\"align\", \"center\", 1, \"coltbrigth\", \"text-center\", 2, \"padding\", \"5px\", \"border-bottom\", \"1px solid black\", \"border-right\", \"1px solid black\", \"border-left\", \"1px solid black\"], [\"align\", \"center\", 1, \"text-center\", 2, \"padding\", \"5px\", \"border-bottom\", \"1px solid black\", \"border-right\", \"1px solid black\"], [\"align\", \"center\", \"style\", \"padding: 5px; border-bottom: 1px solid black; border-right: 1px solid black;\", \"class\", \"text-center\", 4, \"ngIf\"], [1, \"coltbbot\"], [\"class\", \" coltbbot  coltbrigth coltbleft\", 4, \"ngIf\"], [\"align\", \"center\", 2, \"margin-top\", \"47px\"], [1, \"col-12\", \"col-md-12\", \"col-lg-12\", \"col-sm-12\", \"form-inline\", 2, \"margin-top\", \"15px\"], [1, \"col-4\", \"col-md-4\", \"col-sm-4\", \"col-lg-4\", 2, \"margin-bottom\", \"5px\"], [2, \"width\", \"100%\", \"border-right\", \"1px solid black\", \"border-left\", \"1px solid black\", \"border-top\", \"1px solid black\", \"border-bottom\", \"1px solid black\"], [1, \"text-center\"], [2, \"border-top\", \"1px solid #343a40\", \"margin-top\", \"-0.03rem\"], [1, \"text-center\", 2, \"margin-top\", \"35px\"], [1, \"text-center\", 2, \"margin-bottom\", \"25px\"], [1, \"text-center\", 2, \"margin-bottom\", \"0px\"], [\"width\", \"5%\", 1, \"coltbrigth\"], [1, \"coltbleft\", \"coltbrigth\"], [\"scope\", \"col\", 1, \"coltbrigth\", \"text-center\", 3, \"ngStyle\"], [\"scope\", \"col\", 1, \"coltbrigth\", \"text-left\"], [\"scope\", \"col\", 1, \"coltbrigth\", \"text-right\"], [\"scope\", \"col\", \"style\", \"width:80px;\", \"class\", \"coltbrigth text-right\", 4, \"ngIf\"], [\"scope\", \"col\", 1, \"coltbrigth\", \"text-right\", 2, \"width\", \"80px\"], [\"scope\", \"col\", 1, \"coltbrigth\", \"text-right\", 2, \"width\", \"65px\"], [\"scope\", \"col\", 1, \"text-right\"], [1, \"coltbbot\", \"coltbrigth\", \"coltbleft\"], [\"colspan\", \"3\", \"rowspan\", \"2\", 1, \"coltbbot\", \"coltbrigth\"], [\"colspan\", \"2\", \"rowspan\", \"1\", 1, \"text-center\", \"coltbbot\", \"coltbrigth\", 3, \"ngStyle\"], [\"colspan\", \"3\", 1, \"text-center\", \"coltbbot\", \"coltbrigth\"], [1, \"text-right\", \"coltbbot\", 3, \"ngStyle\"], [\"colspan\", \"2\", \"rowspan\", \"1\", 1, \"text-right\", \"coltbbot\", \"coltbrigth\", 3, \"ngStyle\"], [3, \"ngStyle\"], [\"colspan\", \"5\", 1, \"text-left\", \"coltbbot\", \"coltbrigth\", 3, \"ngStyle\"], [\"colspan\", \"3\", 1, \"text-center\", \"coltbrigth\"], [1, \"text-right\", 3, \"ngStyle\"], [\"colspan\", \"4\", 1, \"text-center\", \"coltbbot\", \"coltbrigth\"], [4, \"ngIf\"], [\"colspan\", \"3\", 1, \"text-left\", \"coltbbot\", \"coltbrigth\", 3, \"ngStyle\"], [\"colspan\", \"4\", 1, \"text-center\", \"coltbrigth\"]],\n        template: function PrintsaleoderlistComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"button\", 2);\n            i0.ɵɵtext(3, \"\\u0E40\\u0E1B\\u0E25\\u0E35\\u0E48\\u0E22\\u0E19\\u0E0A\\u0E37\\u0E48\\u0E2D\\u0E25\\u0E39\\u0E01\\u0E04\\u0E49\\u0E32\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(4, \"button\", 3);\n            i0.ɵɵlistener(\"click\", function PrintsaleoderlistComponent_Template_button_click_4_listener() {\n              return ctx.captureScreen();\n            });\n            i0.ɵɵtext(5, \"\\u0E42\\u0E2B\\u0E25\\u0E14PDF\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(6, \"button\", 4);\n            i0.ɵɵlistener(\"click\", function PrintsaleoderlistComponent_Template_button_click_6_listener() {\n              return ctx.blacktosaleoderlist();\n            });\n            i0.ɵɵtext(7, \" Close\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(8, \"div\", 5)(9, \"div\", 6)(10, \"div\", 7)(11, \"input\", 8);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function PrintsaleoderlistComponent_Template_input_ngModelChange_11_listener($event) {\n              i0.ɵɵtwoWayBindingSet(ctx.test, $event) || (ctx.test = $event);\n              return $event;\n            });\n            i0.ɵɵlistener(\"click\", function PrintsaleoderlistComponent_Template_input_click_11_listener($event) {\n              return ctx.loadform($event.target.checked);\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(12, \"label\", 9);\n            i0.ɵɵtext(13, \"\\u0E41\\u0E2A\\u0E14\\u0E07\\u0E19\\u0E49\\u0E33\\u0E2B\\u0E19\\u0E31\\u0E01\\u0E2A\\u0E34\\u0E19\\u0E04\\u0E49\\u0E32\");\n            i0.ɵɵelementEnd()()()()();\n            i0.ɵɵelementStart(14, \"div\", 5)(15, \"select\", 10);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function PrintsaleoderlistComponent_Template_select_ngModelChange_15_listener($event) {\n              i0.ɵɵtwoWayBindingSet(ctx.pageHeightmin, $event) || (ctx.pageHeightmin = $event);\n              return $event;\n            });\n            i0.ɵɵelementStart(16, \"option\", 11);\n            i0.ɵɵtext(17, \"--\\u0E01\\u0E23\\u0E13\\u0E35\\u0E42\\u0E2B\\u0E25\\u0E14 PDF \\u0E41\\u0E25\\u0E49\\u0E27\\u0E40\\u0E01\\u0E34\\u0E19\\u0E2B\\u0E19\\u0E49\\u0E32--\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(18, \"option\", 12);\n            i0.ɵɵtext(19, \"\\u0E40\\u0E01\\u0E34\\u0E19\\u0E2B\\u0E19\\u0E49\\u0E32\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(20, \"option\", 13);\n            i0.ɵɵtext(21, \"\\u0E04\\u0E48\\u0E32\\u0E40\\u0E23\\u0E34\\u0E48\\u0E21\\u0E15\\u0E49\\u0E19\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(22, \"option\", 14);\n            i0.ɵɵtext(23, \"\\u0E44\\u0E21\\u0E48\\u0E40\\u0E15\\u0E47\\u0E21\\u0E2B\\u0E19\\u0E49\\u0E32\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(24, \"option\", 13);\n            i0.ɵɵtext(25, \"Computer\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(26, \"option\", 14);\n            i0.ɵɵtext(27, \"Notebook\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(28, \"option\", 15);\n            i0.ɵɵtext(29, \"Notebook \\u0E44\\u0E21\\u0E48\\u0E40\\u0E15\\u0E47\\u0E21\\u0E2B\\u0E19\\u0E49\\u0E32\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(30, \"div\", 16)(31, \"div\", 17);\n            i0.ɵɵtemplate(32, PrintsaleoderlistComponent_div_32_Template, 181, 22, \"div\", 18);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(33, \"div\", 19)(34, \"div\", 20)(35, \"div\", 21)(36, \"div\", 22)(37, \"h5\", 23);\n            i0.ɵɵtext(38, \"\\u0E40\\u0E1B\\u0E25\\u0E35\\u0E48\\u0E22\\u0E19\\u0E0A\\u0E37\\u0E48\\u0E2D\\u0E25\\u0E39\\u0E01\\u0E04\\u0E49\\u0E32 \\u0E17\\u0E35\\u0E48 \\u0E2D\\u0E22\\u0E39\\u0E48\\u0E25\\u0E39\\u0E01\\u0E04\\u0E49\\u0E32 \");\n            i0.ɵɵelement(39, \"br\");\n            i0.ɵɵtext(40);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(41, \"button\", 24)(42, \"span\", 25);\n            i0.ɵɵtext(43, \"\\u00D7\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(44, \"div\", 26)(45, \"div\", 27)(46, \"label\", 28);\n            i0.ɵɵtext(47, \"\\u0E0A\\u0E37\\u0E48\\u0E2D\\u0E25\\u0E39\\u0E01\\u0E04\\u0E49\\u0E32\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(48, \"textarea\", 29);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function PrintsaleoderlistComponent_Template_textarea_ngModelChange_48_listener($event) {\n              i0.ɵɵtwoWayBindingSet(ctx.getnamecustomer, $event) || (ctx.getnamecustomer = $event);\n              return $event;\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(49, \"div\", 27)(50, \"label\", 30);\n            i0.ɵɵtext(51, \"\\u0E17\\u0E35\\u0E48\\u0E2D\\u0E22\\u0E39\\u0E48\\u0E25\\u0E39\\u0E01\\u0E04\\u0E49\\u0E32\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(52, \"textarea\", 31);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function PrintsaleoderlistComponent_Template_textarea_ngModelChange_52_listener($event) {\n              i0.ɵɵtwoWayBindingSet(ctx.getaddresscustomer, $event) || (ctx.getaddresscustomer = $event);\n              return $event;\n            });\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(53, \"div\", 32)(54, \"button\", 33);\n            i0.ɵɵtext(55, \"Close\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(56, \"button\", 34);\n            i0.ɵɵtext(57, \"\\u0E15\\u0E01\\u0E25\\u0E07\");\n            i0.ɵɵelementEnd()()()()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(11);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.test);\n            i0.ɵɵadvance(4);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.pageHeightmin);\n            i0.ɵɵadvance(17);\n            i0.ɵɵproperty(\"ngForOf\", ctx.pagepdf);\n            i0.ɵɵadvance(8);\n            i0.ɵɵtextInterpolate1(\" (\\u0E40\\u0E25\\u0E02\\u0E17\\u0E35\\u0E48 : \", ctx.salehderprint[0].id, \" ) \");\n            i0.ɵɵadvance(8);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.getnamecustomer);\n            i0.ɵɵadvance(4);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.getaddresscustomer);\n          }\n        },\n        dependencies: [i4.NgForOf, i4.NgIf, i4.NgStyle, i5.NgSelectOption, i5.ɵNgSelectMultipleOption, i5.DefaultValueAccessor, i5.CheckboxControlValueAccessor, i5.SelectControlValueAccessor, i5.NgControlStatus, i5.NgModel, i4.DecimalPipe, i6.ReversepipePipe],\n        styles: [\".coltbtop[_ngcontent-%COMP%]{border-top:1px solid black}.coltbbot[_ngcontent-%COMP%]{border-bottom:1px solid black}.coltbrigth[_ngcontent-%COMP%]{border-right:1px solid black}.coltbleft[_ngcontent-%COMP%]{border-left:1px solid black}.footertable[_ngcontent-%COMP%]{position:absolute;bottom:-200px;right:16px;center:15px}#fontpage[_ngcontent-%COMP%]{font-size:14px;font-family:Segoe UI,Tahoma,Geneva,Verdana,sans-serif}.fontpage2[_ngcontent-%COMP%]{font-size:20px;font-family:Segoe UI,Tahoma,Geneva,Verdana,sans-serif}\"]\n      });\n    }\n  }\n  return PrintsaleoderlistComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}