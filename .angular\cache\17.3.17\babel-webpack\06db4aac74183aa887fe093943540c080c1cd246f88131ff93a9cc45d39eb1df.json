{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nvar core_1 = require(\"@angular/core\");\nvar OffClickDirective = function () {\n  function OffClickDirective() {}\n  /* tslint:enable */\n  OffClickDirective.prototype.onClick = function ($event) {\n    $event.stopPropagation();\n  };\n  OffClickDirective.prototype.ngOnInit = function () {\n    var _this = this;\n    setTimeout(function () {\n      if (typeof document !== 'undefined') {\n        document.addEventListener('click', _this.offClickHandler);\n      }\n    }, 0);\n  };\n  OffClickDirective.prototype.ngOnDestroy = function () {\n    if (typeof document !== 'undefined') {\n      document.removeEventListener('click', this.offClickHandler);\n    }\n  };\n  OffClickDirective.decorators = [{\n    type: core_1.Directive,\n    args: [{\n      selector: '[offClick]'\n    }]\n  }];\n  /** @nocollapse */\n  OffClickDirective.ctorParameters = function () {\n    return [];\n  };\n  OffClickDirective.propDecorators = {\n    'offClickHandler': [{\n      type: core_1.Input,\n      args: ['offClick']\n    }],\n    'onClick': [{\n      type: core_1.HostListener,\n      args: ['click', ['$event']]\n    }]\n  };\n  return OffClickDirective;\n}();\nexports.OffClickDirective = OffClickDirective;", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}