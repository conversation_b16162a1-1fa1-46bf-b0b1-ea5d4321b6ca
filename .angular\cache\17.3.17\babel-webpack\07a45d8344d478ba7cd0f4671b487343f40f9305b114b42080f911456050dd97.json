{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.takeUntil = void 0;\nvar lift_1 = require(\"../util/lift\");\nvar OperatorSubscriber_1 = require(\"./OperatorSubscriber\");\nvar innerFrom_1 = require(\"../observable/innerFrom\");\nvar noop_1 = require(\"../util/noop\");\nfunction takeUntil(notifier) {\n  return lift_1.operate(function (source, subscriber) {\n    innerFrom_1.innerFrom(notifier).subscribe(OperatorSubscriber_1.createOperatorSubscriber(subscriber, function () {\n      return subscriber.complete();\n    }, noop_1.noop));\n    !subscriber.closed && source.subscribe(subscriber);\n  });\n}\nexports.takeUntil = takeUntil;\n//# sourceMappingURL=takeUntil.js.map", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}