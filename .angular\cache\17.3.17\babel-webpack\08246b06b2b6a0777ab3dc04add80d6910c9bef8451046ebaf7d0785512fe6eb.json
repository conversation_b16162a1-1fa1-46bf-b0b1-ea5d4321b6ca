{"ast": null, "code": "import { HttpEventType } from '@angular/common/http';\nimport { Angular5Csv } from 'angular5-csv/dist/Angular5-csv';\nimport { debounceTime, map } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"ngx-bootstrap/modal\";\nimport * as i2 from \"@angular/common/http\";\nimport * as i3 from \"./../webapi.service\";\nimport * as i4 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i5 from \"@angular/router\";\nimport * as i6 from \"ngx-bootstrap/tooltip\";\nimport * as i7 from \"@angular/common\";\nimport * as i8 from \"../../../node_modules/@angular/forms/index\";\nimport * as i9 from \"ngx-bootstrap/datepicker\";\nimport * as i10 from \"ngx-bootstrap/tabs\";\nimport * as i11 from \"../topmenu/topmenu.component\";\nconst _c0 = () => ({\n  dateInputFormat: \"DD/MM/YYYY\"\n});\nconst _c1 = a0 => ({\n  \"display\": a0,\n  \"opacity\": 1\n});\nconst _c2 = a0 => ({\n  \"color\": a0\n});\nfunction Solist1Component_div_6_option_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 50);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r4 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", item_r4.groupid);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\" \", item_r4.groupid, \" (\", item_r4.name, \") \");\n  }\n}\nfunction Solist1Component_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 45)(1, \"select\", 46);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function Solist1Component_div_6_Template_select_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.salegroup, $event) || (ctx_r2.salegroup = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementStart(2, \"option\", 47);\n    i0.ɵɵtext(3, \"--\\u0E40\\u0E25\\u0E37\\u0E2D\\u0E01\\u0E23\\u0E2B\\u0E31\\u0E2A\\u0E1E\\u0E19\\u0E31\\u0E01\\u0E07\\u0E32\\u0E19\\u0E02\\u0E32\\u0E22--\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"option\", 48);\n    i0.ɵɵtext(5, \"\\u0E40\\u0E25\\u0E37\\u0E2D\\u0E01\\u0E23\\u0E32\\u0E22\\u0E01\\u0E32\\u0E23\\u0E17\\u0E31\\u0E49\\u0E07\\u0E2B\\u0E21\\u0E14\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, Solist1Component_div_6_option_6_Template, 2, 3, \"option\", 49);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.salegroup);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.DateGroupsaleman);\n  }\n}\nfunction Solist1Component_div_7_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 52);\n    i0.ɵɵlistener(\"click\", function Solist1Component_div_7_ng_template_1_Template_div_click_0_listener() {\n      const r_r7 = i0.ɵɵrestoreView(_r6).result;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.getcustomersalefunction(r_r7.name));\n    });\n    i0.ɵɵelementStart(1, \"label\", 53);\n    i0.ɵɵlistener(\"mousedown\", function Solist1Component_div_7_ng_template_1_Template_label_mousedown_1_listener() {\n      const r_r7 = i0.ɵɵrestoreView(_r6).result;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.getcustomersalefunction(r_r7.name));\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const r_r7 = ctx.result;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"\", r_r7.name, \" (\", r_r7.accountnum, \")\");\n  }\n}\nfunction Solist1Component_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 45);\n    i0.ɵɵtemplate(1, Solist1Component_div_7_ng_template_1_Template, 3, 2, \"ng-template\", null, 3, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementStart(3, \"input\", 51);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function Solist1Component_div_7_Template_input_ngModelChange_3_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.getcustomer, $event) || (ctx_r2.getcustomer = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const rt_r8 = i0.ɵɵreference(2);\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵpropertyInterpolate(\"placeholder\", ctx_r2.company);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.getcustomer);\n    i0.ɵɵproperty(\"ngbTypeahead\", ctx_r2.search)(\"resultTemplate\", rt_r8)(\"inputFormatter\", ctx_r2.formatter);\n  }\n}\nfunction Solist1Component_th_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 18);\n    i0.ɵɵtext(1, \"\\u0E21\\u0E39\\u0E25\\u0E04\\u0E48\\u0E32\\u0E2A\\u0E34\\u0E19\\u0E04\\u0E49\\u0E32\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction Solist1Component_th_39_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 18);\n    i0.ɵɵtext(1, \"\\u0E21\\u0E39\\u0E25\\u0E04\\u0E48\\u0E32\\u0E2A\\u0E38\\u0E17\\u0E18\\u0E34\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction Solist1Component_th_54_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 18);\n    i0.ɵɵtext(1, \"Print\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction Solist1Component_tr_60_td_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 67);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"number\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r10 = i0.ɵɵnextContext().$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(5, _c2, ctx_r2.getColorFile(item_r10.filetype)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(2, 2, item_r10.amount, \"1.2-2\"));\n  }\n}\nfunction Solist1Component_tr_60_td_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 67);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"number\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r10 = i0.ɵɵnextContext().$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(5, _c2, ctx_r2.getColorFile(item_r10.filetype)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(2, 2, item_r10.price, \"1.2-2\"));\n  }\n}\nfunction Solist1Component_tr_60_td_40_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"td\")(1, \"button\", 68);\n    i0.ɵɵlistener(\"click\", function Solist1Component_tr_60_td_40_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const item_r10 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.loaddataprint(item_r10.id));\n    });\n    i0.ɵɵtext(2, \"Quotation\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction Solist1Component_tr_60_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\", 54)(1, \"td\", 55);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\", 56);\n    i0.ɵɵlistener(\"click\", function Solist1Component_tr_60_Template_td_click_3_listener() {\n      const item_r10 = i0.ɵɵrestoreView(_r9).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      const template_r11 = i0.ɵɵreference(105);\n      return i0.ɵɵresetView(ctx_r2.OpenUploadPDF(template_r11, item_r10.id));\n    });\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\", 55);\n    i0.ɵɵtext(6);\n    i0.ɵɵpipe(7, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"td\", 57);\n    i0.ɵɵpipe(9, \"date\");\n    i0.ɵɵtext(10);\n    i0.ɵɵpipe(11, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"td\", 55);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"td\", 58);\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"td\", 58);\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"td\", 59);\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"td\", 58);\n    i0.ɵɵtext(21);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(22, Solist1Component_tr_60_td_22_Template, 3, 7, \"td\", 60)(23, Solist1Component_tr_60_td_23_Template, 3, 7, \"td\", 60);\n    i0.ɵɵelementStart(24, \"td\", 55);\n    i0.ɵɵtext(25);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"td\", 55);\n    i0.ɵɵtext(27);\n    i0.ɵɵpipe(28, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"td\", 55);\n    i0.ɵɵtext(30);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"td\", 55);\n    i0.ɵɵtext(32);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"td\", 55);\n    i0.ɵɵtext(34);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(35, \"td\", 55);\n    i0.ɵɵtext(36);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(37, \"td\", 61)(38, \"button\", 62);\n    i0.ɵɵlistener(\"click\", function Solist1Component_tr_60_Template_button_click_38_listener() {\n      const item_r10 = i0.ɵɵrestoreView(_r9).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      const templateView_r12 = i0.ɵɵreference(109);\n      return i0.ɵɵresetView(ctx_r2.getsaloderlist(item_r10.id, false, templateView_r12, item_r10.filetype, item_r10.filename));\n    });\n    i0.ɵɵtext(39, \" View \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(40, Solist1Component_tr_60_td_40_Template, 3, 0, \"td\", 63);\n    i0.ɵɵelementStart(41, \"td\", 64)(42, \"label\", 65);\n    i0.ɵɵlistener(\"click\", function Solist1Component_tr_60_Template_label_click_42_listener() {\n      const item_r10 = i0.ɵɵrestoreView(_r9).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.editviewsore(item_r10.id));\n    });\n    i0.ɵɵtext(43, \"\\u0E41\\u0E01\\u0E49\\u0E44\\u0E02\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(44, \"td\", 64)(45, \"label\", 66);\n    i0.ɵɵlistener(\"click\", function Solist1Component_tr_60_Template_label_click_45_listener() {\n      const item_r10 = i0.ɵɵrestoreView(_r9).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.deletesaloder(item_r10.id));\n    });\n    i0.ɵɵtext(46, \"\\u0E25\\u0E1A\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const item_r10 = ctx.$implicit;\n    const i_r14 = ctx.index;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(49, _c2, ctx_r2.getColorFile(item_r10.filetype)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i_r14 + 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(51, _c2, ctx_r2.getColorFile(item_r10.filetype)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r10.id);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(53, _c2, ctx_r2.getColorFile(item_r10.filetype)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(7, 35, item_r10.ShippingDateRequested, \"dd/MM/yyyy\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate1(\"tooltip\", \"\\u0E41\\u0E01\\u0E49\\u0E44\\u0E02\\u0E04\\u0E23\\u0E31\\u0E49\\u0E07\\u0E25\\u0E48\\u0E32\\u0E2A\\u0E38\\u0E14 \", i0.ɵɵpipeBind3(9, 38, item_r10.lastupdate, \"dd/MM/yyyy HH:mm:ss\", \"UTC\"), \"\");\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(55, _c2, ctx_r2.getColorFile(item_r10.filetype)));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind3(11, 42, item_r10.timeedit, \"HH:mm:ss\", \"UTC\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(57, _c2, ctx_r2.getColorFile(item_r10.filetype)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r10.SalesId);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(59, _c2, ctx_r2.getColorFile(item_r10.filetype)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r10.salesname);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(61, _c2, ctx_r2.getColorFile(item_r10.filetype)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r10.InvName);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(63, _c2, ctx_r2.getColorFile(item_r10.filetype)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r10.regnum);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(65, _c2, ctx_r2.getColorFile(item_r10.filetype)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r10.DeliveryName);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.enablecustomer);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.enablecustomer);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(67, _c2, ctx_r2.getColorFile(item_r10.filetype)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r10.vattype);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(69, _c2, ctx_r2.getColorFile(item_r10.filetype)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(28, 46, item_r10.totalweight, \"1.2-2\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(71, _c2, ctx_r2.getColorFile(item_r10.filetype)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r10.DlvMode);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(73, _c2, ctx_r2.getColorFile(item_r10.filetype)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r10.paymenttype);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(75, _c2, ctx_r2.getColorFile(item_r10.filetype)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r10.CustomerRef);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(77, _c2, ctx_r2.getColorFile(item_r10.filetype)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r10.remark);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.showtablecustomer);\n  }\n}\nfunction Solist1Component_th_96_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 39);\n    i0.ɵɵtext(1, \"\\u0E23\\u0E32\\u0E04\\u0E32\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction Solist1Component_th_97_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 69);\n    i0.ɵɵtext(1, \"\\u0E2A\\u0E48\\u0E27\\u0E19\\u0E25\\u0E14\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction Solist1Component_th_98_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 39);\n    i0.ɵɵtext(1, \"\\u0E21\\u0E39\\u0E25\\u0E04\\u0E48\\u0E32\\u0E2A\\u0E38\\u0E17\\u0E18\\u0E34\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction Solist1Component_tr_100_td_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 70);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"number\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r15 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(2, 1, item_r15.PriceUnit, \"1.2-2\"));\n  }\n}\nfunction Solist1Component_tr_100_td_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 75);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"number\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r15 = i0.ɵɵnextContext().$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(5, _c2, ctx_r2.getColordis1(item_r15.disstate)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind2(2, 2, item_r15.IVZ_Percent1_CT, \"1.2-2\"), \"%\");\n  }\n}\nfunction Solist1Component_tr_100_td_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 75);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"number\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r15 = i0.ɵɵnextContext().$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(5, _c2, ctx_r2.getColordis2(item_r15.disstate)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind2(2, 2, item_r15.IVZ_Percent2_CT, \"1.2-2\"), \"%\");\n  }\n}\nfunction Solist1Component_tr_100_td_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 75);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"number\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r15 = i0.ɵɵnextContext().$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(5, _c2, ctx_r2.getColordis3(item_r15.disstate)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind2(2, 2, item_r15.IVZ_Percent3_CT, \"1.2-2\"), \"%\");\n  }\n}\nfunction Solist1Component_tr_100_td_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 76);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"number\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r15 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(2, 1, item_r15.LineAmount, \"1.2-2\"));\n  }\n}\nfunction Solist1Component_tr_100_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 70);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\", 71);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\", 70);\n    i0.ɵɵtext(6);\n    i0.ɵɵpipe(7, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"td\", 70);\n    i0.ɵɵtext(9);\n    i0.ɵɵpipe(10, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"td\", 70);\n    i0.ɵɵtext(12);\n    i0.ɵɵpipe(13, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(14, Solist1Component_tr_100_td_14_Template, 3, 4, \"td\", 72)(15, Solist1Component_tr_100_td_15_Template, 3, 7, \"td\", 73)(16, Solist1Component_tr_100_td_16_Template, 3, 7, \"td\", 73)(17, Solist1Component_tr_100_td_17_Template, 3, 7, \"td\", 73)(18, Solist1Component_tr_100_td_18_Template, 3, 4, \"td\", 74);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r15 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r15.ItemId);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r15.Name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(7, 10, item_r15.packqty, \"1.2-2\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(10, 13, item_r15.SalesQty, \"1.2-2\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(13, 16, item_r15.totalweight, \"1.2-2\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.enablecustomer);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.enablecustomer);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.enablecustomer);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.enablecustomer);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.enablecustomer);\n  }\n}\nfunction Solist1Component_ng_template_104_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 33)(1, \"h4\", 77);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 78);\n    i0.ɵɵlistener(\"click\", function Solist1Component_ng_template_104_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.modalRef.hide());\n    });\n    i0.ɵɵelementStart(4, \"span\", 36);\n    i0.ɵɵtext(5, \"\\u00D7\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(6, \"div\", 27)(7, \"div\", 79)(8, \"label\", 80);\n    i0.ɵɵtext(9, \"Input file PDF\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"input\", 81);\n    i0.ɵɵlistener(\"change\", function Solist1Component_ng_template_104_Template_input_change_10_listener($event) {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.handleFileInput($event.target.files));\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(11, \"div\", 43)(12, \"button\", 82);\n    i0.ɵɵlistener(\"click\", function Solist1Component_ng_template_104_Template_button_click_12_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r2 = i0.ɵɵnextContext();\n      const template_r11 = i0.ɵɵreference(105);\n      const templateShow_r17 = i0.ɵɵreference(107);\n      return i0.ɵɵresetView(ctx_r2.onUpload(ctx_r2.showIDso, templateShow_r17, template_r11));\n    });\n    i0.ɵɵtext(13, \"Upload\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"button\", 83);\n    i0.ɵɵlistener(\"click\", function Solist1Component_ng_template_104_Template_button_click_14_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.modalRef.hide());\n    });\n    i0.ɵɵtext(15, \"Close\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"UploadPDF : \", ctx_r2.showIDso, \"\");\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"disabled\", ctx_r2.selectedFile == null);\n  }\n}\nfunction Solist1Component_ng_template_106_button_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 87);\n    i0.ɵɵlistener(\"click\", function Solist1Component_ng_template_106_button_3_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.confirm());\n    });\n    i0.ɵɵtext(1, \"\\u0E1B\\u0E34\\u0E14\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction Solist1Component_ng_template_106_button_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r19 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 88);\n    i0.ɵɵlistener(\"click\", function Solist1Component_ng_template_106_button_4_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      const template_r11 = i0.ɵɵreference(105);\n      return i0.ɵɵresetView(ctx_r2.decline(template_r11));\n    });\n    i0.ɵɵtext(1, \"\\u0E17\\u0E33\\u0E23\\u0E32\\u0E22\\u0E01\\u0E32\\u0E23\\u0E43\\u0E2B\\u0E21\\u0E48\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction Solist1Component_ng_template_106_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 84);\n    i0.ɵɵtext(1);\n    i0.ɵɵelement(2, \"br\");\n    i0.ɵɵtemplate(3, Solist1Component_ng_template_106_button_3_Template, 2, 0, \"button\", 85)(4, Solist1Component_ng_template_106_button_4_Template, 2, 0, \"button\", 86);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.textload, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.btnPDF);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.btnREpdf);\n  }\n}\nfunction Solist1Component_ng_template_108_th_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 39);\n    i0.ɵɵtext(1, \"\\u0E23\\u0E32\\u0E04\\u0E32\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction Solist1Component_ng_template_108_th_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 69);\n    i0.ɵɵtext(1, \"\\u0E2A\\u0E48\\u0E27\\u0E19\\u0E25\\u0E14\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction Solist1Component_ng_template_108_th_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 39);\n    i0.ɵɵtext(1, \"\\u0E21\\u0E39\\u0E25\\u0E04\\u0E48\\u0E32\\u0E2A\\u0E38\\u0E17\\u0E18\\u0E34\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction Solist1Component_ng_template_108_tr_28_td_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 70);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"number\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r21 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(2, 1, item_r21.PriceUnit, \"1.2-2\"));\n  }\n}\nfunction Solist1Component_ng_template_108_tr_28_td_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 75);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"number\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r21 = i0.ɵɵnextContext().$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(5, _c2, ctx_r2.getColordis1(item_r21.disstate)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind2(2, 2, item_r21.IVZ_Percent1_CT, \"1.2-2\"), \"%\");\n  }\n}\nfunction Solist1Component_ng_template_108_tr_28_td_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 75);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"number\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r21 = i0.ɵɵnextContext().$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(5, _c2, ctx_r2.getColordis2(item_r21.disstate)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind2(2, 2, item_r21.IVZ_Percent2_CT, \"1.2-2\"), \"%\");\n  }\n}\nfunction Solist1Component_ng_template_108_tr_28_td_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 75);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"number\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r21 = i0.ɵɵnextContext().$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(5, _c2, ctx_r2.getColordis3(item_r21.disstate)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind2(2, 2, item_r21.IVZ_Percent3_CT, \"1.2-2\"), \"%\");\n  }\n}\nfunction Solist1Component_ng_template_108_tr_28_td_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 76);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"number\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r21 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(2, 1, item_r21.LineAmount, \"1.2-2\"));\n  }\n}\nfunction Solist1Component_ng_template_108_tr_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 70);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\", 71);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\", 70);\n    i0.ɵɵtext(6);\n    i0.ɵɵpipe(7, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"td\", 70);\n    i0.ɵɵtext(9);\n    i0.ɵɵpipe(10, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"td\", 70);\n    i0.ɵɵtext(12);\n    i0.ɵɵpipe(13, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(14, Solist1Component_ng_template_108_tr_28_td_14_Template, 3, 4, \"td\", 72)(15, Solist1Component_ng_template_108_tr_28_td_15_Template, 3, 7, \"td\", 73)(16, Solist1Component_ng_template_108_tr_28_td_16_Template, 3, 7, \"td\", 73)(17, Solist1Component_ng_template_108_tr_28_td_17_Template, 3, 7, \"td\", 73)(18, Solist1Component_ng_template_108_tr_28_td_18_Template, 3, 4, \"td\", 74);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r21 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r21.ItemId);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r21.Name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(7, 10, item_r21.packqty, \"1.2-2\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(10, 13, item_r21.SalesQty, \"1.2-2\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(13, 16, item_r21.totalweight, \"1.2-2\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.enablecustomer);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.enablecustomer);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.enablecustomer);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.enablecustomer);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.enablecustomer);\n  }\n}\nfunction Solist1Component_ng_template_108_tab_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tab\", 94)(1, \"div\", 95)(2, \"form\", 96, 5);\n    i0.ɵɵelement(4, \"img\", 97);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"src\", ctx_r2.nameUrl, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction Solist1Component_ng_template_108_a_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 98);\n    i0.ɵɵtext(1, \"View PDF\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵpropertyInterpolate(\"href\", ctx_r2.nameUrl, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction Solist1Component_ng_template_108_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r20 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 33)(1, \"h4\", 77);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 78);\n    i0.ɵɵlistener(\"click\", function Solist1Component_ng_template_108_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.modalRef.hide());\n    });\n    i0.ɵɵelementStart(4, \"span\", 36);\n    i0.ɵɵtext(5, \"\\u00D7\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(6, \"div\", 89)(7, \"tabset\", null, 4)(9, \"tab\", 90)(10, \"div\", 91)(11, \"table\", 16)(12, \"thead\")(13, \"tr\")(14, \"th\", 38);\n    i0.ɵɵtext(15, \"\\u0E23\\u0E2B\\u0E31\\u0E2A\\u0E2A\\u0E34\\u0E19\\u0E04\\u0E49\\u0E32\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"th\", 38);\n    i0.ɵɵtext(17, \"\\u0E0A\\u0E37\\u0E48\\u0E2D\\u0E2A\\u0E34\\u0E19\\u0E04\\u0E49\\u0E32\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"th\", 39);\n    i0.ɵɵtext(19, \"Pack\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"th\", 39);\n    i0.ɵɵtext(21, \"\\u0E08\\u0E33\\u0E19\\u0E27\\u0E19\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"th\", 39);\n    i0.ɵɵtext(23, \"\\u0E19\\u0E49\\u0E33\\u0E2B\\u0E19\\u0E31\\u0E01\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(24, Solist1Component_ng_template_108_th_24_Template, 2, 0, \"th\", 40)(25, Solist1Component_ng_template_108_th_25_Template, 2, 0, \"th\", 41)(26, Solist1Component_ng_template_108_th_26_Template, 2, 0, \"th\", 40);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(27, \"tbody\");\n    i0.ɵɵtemplate(28, Solist1Component_ng_template_108_tr_28_Template, 19, 19, \"tr\", 42);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵtemplate(29, Solist1Component_ng_template_108_tab_29_Template, 5, 1, \"tab\", 92);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(30, \"div\", 43);\n    i0.ɵɵtemplate(31, Solist1Component_ng_template_108_a_31_Template, 2, 1, \"a\", 93);\n    i0.ɵɵelementStart(32, \"button\", 83);\n    i0.ɵɵlistener(\"click\", function Solist1Component_ng_template_108_Template_button_click_32_listener() {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.modalRef.hide());\n    });\n    i0.ɵɵtext(33, \"Close\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"Sale Oder Detail : \", ctx_r2.showIDso, \"\");\n    i0.ɵɵadvance(22);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.enablecustomer);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.enablecustomer);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.enablecustomer);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.salelistview);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.Cktype && ctx_r2.CkNull);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.Cktype && ctx_r2.CkNull);\n  }\n}\nexport let Solist1Component = /*#__PURE__*/(() => {\n  class Solist1Component {\n    getColordis1(disst) {\n      var st = disst;\n      var fi = st.substring(0, 1);\n      switch (fi) {\n        case '1':\n          return 'red';\n      }\n    }\n    getColordis2(disst) {\n      var st = disst;\n      var fi = st.substring(1, 2);\n      switch (fi) {\n        case '1':\n          return 'red';\n      }\n    }\n    getColordis3(disst) {\n      var st = disst;\n      var fi = st.substring(2, 3);\n      switch (fi) {\n        case '1':\n          return 'red';\n      }\n    }\n    constructor(modalService, http, service, calendar, router) {\n      this.modalService = modalService;\n      this.http = http;\n      this.service = service;\n      this.calendar = calendar;\n      this.router = router;\n      this.mdlSampleIsOpen = false;\n      this.alt = '';\n      this.checkreload = true;\n      this.savedata = [];\n      this.options = {\n        fieldSeparator: ',',\n        quoteStrings: '\"',\n        decimalseparator: '.',\n        showLabels: true,\n        showTitle: true,\n        useBom: true,\n        noDownload: false,\n        headers: [\"วันที่\", \"พนักงานขาย\", \"ลูกค้า\", \"มูลค่าสินค้า\", \"มูลค่าสุทธิ\", \"VAT/No VAT\", \"น้ำหนักรวม\", \"ประเภทขนส่ง\", \"เงินสด/เครดิต\", \"Note ภายใน\", \"หมายเหตุ\"]\n      };\n      this.fromdate = '';\n      this.salegroup = '';\n      this.customer = '';\n      this.DateGroupsaleman = [];\n      this.datalogin = [];\n      this.todate = '';\n      this.chackuser = false;\n      this.salelistview = [];\n      this.salehderprint = [];\n      this.showtablecustomer = true;\n      this.discustomer = false;\n      this.loaddata = [];\n      this.permisstiondata = [];\n      this.exportbtn = true;\n      this.searchbtn = true;\n      this.customers = [];\n      this.enablecustomer = false;\n      this.company = 'ค้นหาลูกค้า';\n      this.config = {\n        ignoreBackdropClick: true,\n        class: 'modal-md'\n      };\n      this.configview = {\n        ignoreBackdropClick: true,\n        class: 'modal-lg'\n      };\n      this.textload = \"\";\n      this.selectedFile = null;\n      this.btnPDF = false;\n      this.btnREpdf = false;\n      this.search = text$ =>\n      //Autocomplete ลูกค้า\n      text$.pipe(debounceTime(200), map(term => term === '' ? [] : this.customers.filter(v => v.name.toLowerCase().indexOf(term.toLowerCase()) > -1 || v.accountnum.toLowerCase().indexOf(term.toLowerCase()) > -1).slice(0, 50)));\n      this.formatter = x => x.name + ' (' + x.accountnum + ')';\n      localStorage.removeItem('DataSOderreview');\n      this.url = service.geturlservice();\n      this.Name = JSON.parse(sessionStorage.getItem('login'));\n      console.log(this.Name);\n      this.fromDate = calendar.getPrev(calendar.getToday(), 'd', 30);\n      this.toDate = calendar.getNext(calendar.getToday(), 'm', 3);\n      this.datalogin = JSON.parse(sessionStorage.getItem('login'));\n      this.Datatodate = new Date(this.toDate.year, this.toDate.month - 1, this.toDate.day);\n      this.Datafromdate = new Date(this.fromDate.year, this.fromDate.month - 1, this.fromDate.day);\n      this.getdate();\n      this.loaddata = JSON.parse(localStorage.getItem('DataSOderlist'));\n      this.permisstiondata = JSON.parse(sessionStorage.getItem('menu'));\n      if (this.datalogin == null || this.permisstiondata == null) {\n        this.router.navigate(['login']);\n      } else {\n        this.getuser();\n        this.exportbtn = !this.permisstiondata[4].flag_print;\n        this.searchbtn = !this.permisstiondata[4].flag_action;\n      }\n    }\n    getuser() {\n      if (this.datalogin[0].id_group_user == '153Admin' || this.datalogin[0].id_group_user == '1860Administrator') {\n        this.groupsale = '';\n        this.testclose = true;\n      } else {\n        this.testclose = false;\n        this.salegroup = this.datalogin[0].salegroup;\n      }\n    }\n    getdate() {\n      this.fromdate = `${this.Datafromdate.getFullYear()}-${this.Datafromdate.getMonth() + 1}-${this.Datafromdate.getDate()}`;\n      this.todate = `${this.Datatodate.getFullYear()}-${this.Datatodate.getMonth() + 1}-${this.Datatodate.getDate()}`;\n    }\n    ngOnInit() {\n      this.getgroupsaleman();\n      this.getcostomerauto();\n      if (this.Name[0].salegroup === 'admin') {\n        this.chackuser = true;\n      }\n      if (this.Name[0].accountnum != null || this.Name[0].accountnum != undefined) {\n        this.enablecustomer = false;\n      } else {\n        this.enablecustomer = true;\n      }\n    }\n    getcostomerauto() {\n      var idsale = this.datalogin[0].salegroup;\n      if (this.datalogin[0].salegroup === 'admin') {\n        idsale = '%20';\n      } else {\n        idsale = this.datalogin[0].salegroup;\n      }\n      this.http.get(this.url + 'customerauto/' + idsale).subscribe(res => {\n        this.customers = res;\n      });\n    }\n    Searchsolist() {\n      if (this.getcustomer == undefined) {\n        this.customer = '';\n      } else {\n        this.customer = this.getcustomer.name;\n      }\n      this.getdate();\n      var datasalegroup = '';\n      if (this.customer == '') {\n        this.customer = '%20';\n      }\n      if (this.Name[0].accountnum != undefined) {\n        //alert(this.Name[0].accountnum);\n        this.discustomer = true;\n        this.showtablecustomer = false;\n        this.customer = this.Name[0].accountnum;\n      }\n      if (this.fromdate == '') {\n        this.fromdate = `${this.fromDate}`;\n      }\n      if (this.todate == '') {\n        this.todate = `${this.toDate}`;\n      }\n      if (this.salegroup == '') {\n        if (this.datalogin[0].id_group_user == '153Admin' || this.datalogin[0].id_group_user == '1860Administrator') {\n          datasalegroup = `${this.salegroup}`;\n        } else {\n          datasalegroup = `${this.datalogin[0].salegroup}`;\n        }\n      }\n      if (this.salegroup !== '') {\n        if (this.datalogin[0].id_group_user == '153Admin' || this.datalogin[0].id_group_user == '1860Administrator') {\n          datasalegroup = `${this.salegroup}`;\n        } else {\n          datasalegroup = this.datalogin[0].salegroup;\n        }\n      }\n      if (datasalegroup == '') {\n        datasalegroup = '%20';\n      }\n      if (this.loaddata != null && JSON.stringify(this.loaddata) != '[]') {\n        datasalegroup = this.loaddata[0].datasalegroup;\n        this.customer = this.loaddata[0].customer;\n        this.fromdate = this.loaddata[0].fromdate;\n        this.todate = this.loaddata[0].todate;\n        this.Datatodate = new Date(this.loaddata[0].todate);\n        this.Datafromdate = new Date(this.loaddata[0].fromdate);\n        this.loaddata = [];\n        this.company = this.customer;\n      }\n      this.solist = [];\n      this.http.get(this.url + 'solist/' + this.fromdate + '/' + this.todate + '/' + datasalegroup + '/' + this.customer + '/0').subscribe(res => {\n        if (res.length > 0) {\n          this.savedata = [{\n            fromdate: this.fromdate,\n            todate: this.todate,\n            datasalegroup: datasalegroup,\n            customer: this.customer\n          }];\n          if (datasalegroup == '%20') {\n            this.salegroup = '';\n          }\n          if (this.customer == '%20') {\n            this.customer = '';\n          }\n          this.solist = res;\n        } else {\n          if (datasalegroup == '%20') {\n            this.salegroup = '';\n          }\n          if (this.customer == '%20') {\n            this.customer = '';\n          }\n          this.solist = [];\n          alert('ไม่พบข้อมูลที่ค้นหา');\n          //this.openModal(true,'ไม่พบข้อมูลที่ค้นหา',false);\n        }\n      });\n    }\n    deletesaloder(value) {\n      if (confirm('ต้องการลบ รายการที่เลือกใช่หรือไม่')) {\n        if (value != undefined) {\n          var urlpost = `${this.url}${'delete_sale_line'}/${value}/${this.datalogin[0].salegroup}`;\n          this.http.post(urlpost, '').subscribe(res => {\n            // this.setinheader= setInterval(this.savesaleoderlistsave(),);\n            alert('ลบข้อมูลเสร็จสิ้น');\n            this.Searchsolist();\n          });\n        } else {}\n      } else {\n        return;\n      }\n    }\n    editviewsore(valueid) {\n      localStorage.setItem('DataSOderlist', JSON.stringify(this.savedata));\n      this.savedata = [];\n      this.loaddata = [];\n      this.router.navigate(['/sorecord'], {\n        queryParams: {\n          idso: valueid\n        }\n      });\n    }\n    gettype(type) {\n      if (type === \"application/pdf\") {\n        return true;\n      } else {\n        return false;\n      }\n    }\n    gettypeNull(type) {\n      if (type == \"\") {\n        return false;\n      } else {\n        return true;\n      }\n    }\n    getColorFile(type) {\n      if (type !== \"\") {\n        return '#0317ee';\n      }\n    }\n    getsaloderlist(valueid, check, template, type, nameFile) {\n      this.Cktype = this.gettype(type);\n      this.CkNull = this.gettypeNull(type);\n      this.showIDso = valueid;\n      this.nameUrl = 'http://119.59.112.47/assets/PDF/' + nameFile;\n      this.modalRef = this.modalService.show(template, this.configview);\n      this.salelistview = [];\n      this.http.get(this.url + 'find_saleline/' + valueid).subscribe(res => {\n        if (res.length > 0) {\n          this.salelistview = res;\n          if (check == true) {\n            this.printpdf();\n          }\n        }\n      });\n    }\n    getgroupsaleman() {\n      this.DateGroupsaleman = [];\n      this.http.get(this.url + 'salesman').subscribe(res => {\n        if (res.length > 0) {\n          this.Searchsolist();\n          this.DateGroupsaleman = res;\n        } else {\n          alert('ไม่สามารถติดต่อข้อมูลรหัส saleman ได้');\n        }\n      });\n    }\n    exportdataexcel() {\n      if (this.solist == undefined) {\n        alert('ไม่พบข้อมูล');\n        //this.openModal(true,'ไม่พบข้อมูล',false);\n      } else {\n        new Angular5Csv(this.solist, 'CustomerList', this.options);\n      }\n    }\n    ThaiNumberToText(Number) {\n      Number = Number.replace(/๐/gi, '0');\n      Number = Number.replace(/๑/gi, '1');\n      Number = Number.replace(/๒/gi, '2');\n      Number = Number.replace(/๓/gi, '3');\n      Number = Number.replace(/๔/gi, '4');\n      Number = Number.replace(/๕/gi, '5');\n      Number = Number.replace(/๖/gi, '6');\n      Number = Number.replace(/๗/gi, '7');\n      Number = Number.replace(/๘/gi, '8');\n      Number = Number.replace(/๙/gi, '9');\n      return this.ArabicNumberToText(Number);\n    }\n    ArabicNumberToText(Number) {\n      var Number = this.CheckNumber(Number);\n      var NumberArray = new Array(\"ศูนย์\", \"หนึ่ง\", \"สอง\", \"สาม\", \"สี่\", \"ห้า\", \"หก\", \"เจ็ด\", \"แปด\", \"เก้า\", \"สิบ\");\n      var DigitArray = new Array(\"\", \"สิบ\", \"ร้อย\", \"พัน\", \"หมื่น\", \"แสน\", \"ล้าน\");\n      var BahtText = \"\";\n      if (isNaN(Number)) {\n        return \"ข้อมูลนำเข้าไม่ถูกต้อง\";\n      } else {\n        if (Number - 0 > 9999999.9999) {\n          return \"ข้อมูลนำเข้าเกินขอบเขตที่ตั้งไว้\";\n        } else {\n          Number = Number.split(\".\");\n          if (Number[1].length > 0) {\n            Number[1] = Number[1].substring(0, 2);\n          }\n          var NumberLen = Number[0].length - 0;\n          for (var i = 0; i < NumberLen; i++) {\n            var tmp = Number[0].substring(i, i + 1) - 0;\n            if (tmp != 0) {\n              if (i == NumberLen - 1 && tmp == 1) {\n                BahtText += \"เอ็ด\";\n              } else if (i == NumberLen - 2 && tmp == 2) {\n                BahtText += \"ยี่\";\n              } else if (i == NumberLen - 2 && tmp == 1) {\n                BahtText += \"\";\n              } else {\n                BahtText += NumberArray[tmp];\n              }\n              BahtText += DigitArray[NumberLen - i - 1];\n            }\n          }\n          BahtText += \"บาท\";\n          if (Number[1] == \"0\" || Number[1] == \"00\") {\n            BahtText += \"ถ้วน\";\n          } else {\n            var DecimalLen = Number[1].length - 0;\n            for (var i = 0; i < DecimalLen; i++) {\n              var tmp = Number[1].substring(i, i + 1) - 0;\n              if (tmp != 0) {\n                if (i == DecimalLen - 1 && tmp == 1) {\n                  BahtText += \"เอ็ด\";\n                } else if (i == DecimalLen - 2 && tmp == 2) {\n                  BahtText += \"ยี่\";\n                } else if (i == DecimalLen - 2 && tmp == 1) {\n                  BahtText += \"\";\n                } else {\n                  BahtText += NumberArray[tmp];\n                }\n                BahtText += DigitArray[DecimalLen - i - 1];\n              }\n            }\n            BahtText += \"สตางค์\";\n          }\n          return BahtText;\n        }\n      }\n    }\n    CheckNumber(Number) {\n      var decimal = false;\n      Number = Number.toString();\n      Number = Number.replace(/ |,|บาท|฿/gi, '');\n      for (var i = 0; i < Number.length; i++) {\n        if (Number[i] == '.') {\n          decimal = true;\n        }\n      }\n      if (decimal == false) {\n        Number = Number + '.00';\n      }\n      return Number;\n    }\n    loaddataprint(value) {\n      this.router.navigate(['printsaleoderlist'], {\n        queryParams: {\n          idso: value\n        }\n      });\n      /* this.http.get<any>(this.url+'find_saleheader/'+value).subscribe(res=>{\n         this.salehderprint=res;\n         //alert(this.salehderprint[0].dateid);\n         //alert(JSON.stringify(res));\n         this.getsaloderlist(value,true);\n                   },error=>{\n             //return;\n           });*/\n    }\n    //สั่งพิมใบ สั่งซื้อ\n    printpdf() {\n      //this.salelistview=[];\n      //this.salehderprint=[];\n      var linesale = '';\n      var linesalepage2 = '';\n      var item = 0;\n      if (this.salelistview.length > 0 && this.salelistview.length <= 18) {\n        for (var i = 0; i < this.salelistview.length; i++) {\n          item++;\n          linesale += `\n    <tr style=\"line-height: 6px\" class=\"coltbleft coltbrigth\">\n    <td scope=\"col\" class=\"coltbrigth text-center\">` + item + `</td>\n    <td scope=\"col\"  class=\"coltbrigth text-left\">` + this.salelistview[i].ItemId + ' ' + this.salelistview[i].Name + `</td>\n    <td scope=\"col\"  width=\"5%\" class=\"coltbrigth text-right\">` + parseInt(this.salelistview[i].SalesQty).toLocaleString() + `</td>\n    <td scope=\"col\" width=\"10%\" class=\"coltbrigth text-right\">` + parseInt(this.salelistview[i].PriceUnit).toLocaleString(undefined, {\n            minimumFractionDigits: 2,\n            maximumFractionDigits: 2\n          }) + `</td>\n    <td scope=\"col\" class=\"coltbrigth text-right\">` + this.salelistview[i].IVZ_Percent1_CT + `%</td>\n    <td scope=\"col\" class=\"coltbrigth text-right\">` + this.salelistview[i].IVZ_Percent2_CT + `%</td>\n    <td scope=\"col\" class=\"coltbrigth text-right\">` + this.salelistview[i].IVZ_Percent2_CT + `%</td>\n    <td scope=\"col\" class=\"text-right\">` + parseInt(this.salelistview[i].LineAmount).toLocaleString(undefined, {\n            minimumFractionDigits: 2,\n            maximumFractionDigits: 2\n          }) + `</td>\n  </tr>`;\n        }\n        var nu = 32 - item;\n        for (var i = 0; i < nu; i++) {\n          linesale += `\n    <tr  style=\"line-height: 11px\" class=\"coltbleft coltbrigth\">\n    <td scope=\"col\" class=\"coltbrigth text-center\">&nbsp;</td>\n    <td scope=\"col\" class=\"coltbrigth text-left\">&nbsp;</td>\n    <td scope=\"col\" width=\"5%\" class=\"coltbrigth text-center\">&nbsp;</td>\n    <td scope=\"col\" class=\"coltbrigth text-center\">&nbsp;</td>\n    <td scope=\"col\" class=\"coltbrigth text-center\">&nbsp;</td>\n    <td scope=\"col\" class=\"coltbrigth text-center\">&nbsp;</td>\n    <td scope=\"col\" class=\"coltbrigth text-center\">&nbsp;</td>\n    <td scope=\"col\" class=\"text-right\"></td>\n  </tr>`;\n        }\n      } else if (this.salelistview.length >= 19) {\n        alert(1.1);\n        var item2 = 19;\n        for (var i = 0; i < 18; i++) {\n          item++;\n          linesale += `\n      <tr style=\"line-height: 38px\" class=\"coltbleft coltbrigth\">\n        <td scope=\"col\" class=\"coltbrigth text-center\">` + item + `</td>\n        <td scope=\"col\" class=\"coltbrigth text-left\">` + this.salelistview[i].ItemId + ' ' + this.salelistview[i].Name + `</td>\n        <td scope=\"col\" width=\"5%\" class=\"coltbrigth text-right\">` + parseInt(this.salelistview[i].SalesQty).toLocaleString() + `</td>\n        <td scope=\"col\" class=\"coltbrigth text-right\">` + parseInt(this.salelistview[i].PriceUnit).toLocaleString(undefined, {\n            minimumFractionDigits: 2,\n            maximumFractionDigits: 2\n          }) + `</td>\n        <td scope=\"col\" class=\"coltbrigth text-right\">` + this.salelistview[i].IVZ_Percent1_CT + `%</td>\n        <td scope=\"col\" class=\"coltbrigth text-right\">` + this.salelistview[i].IVZ_Percent2_CT + `%</td>\n        <td scope=\"col\" class=\"coltbrigth text-right\">` + this.salelistview[i].IVZ_Percent2_CT + `%</td>\n        <td scope=\"col\" class=\"text-right\">` + parseInt(this.salelistview[i].LineAmount).toLocaleString(undefined, {\n            minimumFractionDigits: 2,\n            maximumFractionDigits: 2\n          }) + `</td>\n      </tr>`;\n        }\n        for (var i = 19; i < this.salelistview.length; i++) {\n          alert(1.2);\n          linesalepage2 += `\n        <tr style=\"line-height: 8px\" class=\"coltbleft coltbrigth\">\n        <td scope=\"col\" class=\"coltbrigth text-center\">` + item + `</td>\n        <td scope=\"col\" class=\"coltbrigth text-left\">` + this.salelistview[i].ItemId + ' ' + this.salelistview[i].Name + `</td>\n        <td scope=\"col\" width=\"5%\" class=\"coltbrigth text-right\">` + parseInt(this.salelistview[i].SalesQty).toLocaleString() + `</td>\n        <td scope=\"col\" class=\"coltbrigth text-right\">` + parseInt(this.salelistview[i].PriceUnit).toLocaleString(undefined, {\n            minimumFractionDigits: 2,\n            maximumFractionDigits: 2\n          }) + `</td>\n        <td scope=\"col\" class=\"coltbrigth text-right\">` + this.salelistview[i].IVZ_Percent1_CT + `%</td>\n        <td scope=\"col\" class=\"coltbrigth text-right\">` + this.salelistview[i].IVZ_Percent2_CT + `%</td>\n        <td scope=\"col\" class=\"coltbrigth text-right\">` + this.salelistview[i].IVZ_Percent2_CT + `%</td>\n        <td scope=\"col\" class=\"text-right\">` + parseInt(this.salelistview[i].LineAmount).toLocaleString(undefined, {\n            minimumFractionDigits: 2,\n            maximumFractionDigits: 2\n          }) + `</td>\n      </tr>`;\n          item2++;\n        }\n        var nu = 32 - item2;\n        for (var i = 0; i < nu; i++) {\n          alert(1.3);\n          linesalepage2 += `\n    <tr  style=\"line-height: 5px\" class=\"coltbleft coltbrigth\">\n    <td scope=\"col\" class=\"coltbrigth text-center\">&nbsp;</td>\n    <tdscope=\"col\" class=\"coltbrigth text-left\">&nbsp;</td>\n    <td scope=\"col\" width=\"5%\" class=\"coltbrigth text-center\">&nbsp;</td>\n    <td scope=\"col\" class=\"coltbrigth text-center\">&nbsp;</td>\n    <td scope=\"col\" class=\"coltbrigth text-center\">&nbsp;</td>\n    <td scope=\"col\" class=\"coltbrigth text-center\">&nbsp;</td>\n    <td scope=\"col\" class=\"coltbrigth text-center\">&nbsp;</td>\n    <td scope=\"col\" class=\"text-right\"></td>\n  </tr>`;\n        }\n      }\n      var tex = parseInt(this.salehderprint[0].amount) * 7 / 100;\n      var sum = tex + parseInt(this.salehderprint[0].amount);\n      var bathstr = this.ArabicNumberToText(sum);\n      var haederimg = `<body id=\"fontpage\"  onload=\"window.print();window.close()\">\n <!-- <body> -->\n <div id=\"fontpage\" class=\"container-fluid\">\n\n <div class=\"row\">\n <div class=\"col-md-12 offset-0\">\n <img src=\"../../assets/img/BPG.jpg\" width=\"100%\">\n <!-- <img style=\"margin-left: 10%\" src=\"../../assets/img/logo.png\"> -->\n </div>\n </div>\n         <div class=\"row\">\n           <div class=\"col-md-12 offset-0\">\n        <table   width=\"100%\" style=\"margin-top: 3%\">\n          <thead class=\"coltbtop coltbbot coltbleft coltbrigth\">\n            <th colspan=\"4\"  class=\"text-center\" style=\"background-color: #e0e6e7\">ใบเสนอราคา/QUOTATION</th>\n          </thead>\n          <tbody class=\"coltbbot\"  style=\"border: 2px;\" >\n           <tr>\n             <td class=\"coltbleft\">  Company : ` + this.salehderprint[0].SalesName + `</td>\n\n             <td width=\"250px\" class=\"coltbbot coltbleft coltbrigth\">เลขที่/ No. :` + this.salehderprint[0].id + `</td>\n           </tr>\n           <tr>\n             <td class=\"coltbbot coltbleft coltbrigth\"  rowspan=\"2\"> Address : ` + this.salehderprint[0].address + `</td>\n\n             <td  class=\" coltbbot coltbrigth coltbleft\">วันที่ Date :` + this.salehderprint[0].dateid + `</td>\n           </tr>\n           <tr>\n\n\n             <td  class=\" coltbbot coltbrigth  coltbleft\">TEL. </td>\n           </tr>\n          </tbody>\n        </table>\n           </div>\n         </div>\n\n         <div class=\"row\">\n           <div class=\"col-md-12 offset-0\">\n             <label >ขอเสนอราคาและเงื่อนไขสำหรับท่านดังนี้</label><br>\n             <label >We are please to submit you the following described here in at price, items and terms stated :</label>\n           </div>\n           </div>`;\n      var haeder = ` <html>\n <head>\n   <title>ใบเสนอราคา / QUOTATION</title>\n   <meta http-equiv=\"Content-Type\" content=\"text/html; charset=UTF-8\" />\n\n<meta http-equiv=\"Content-Language\" content=\"en-us\">\n<meta http-equiv=\"Content-Script-Type\" content=\"text/javascript\">\n<link rel=\"stylesheet\" href=\"https://stackpath.bootstrapcdn.com/bootstrap/4.1.3/css/bootstrap.min.css\" integrity=\"sha384-MCw98/SFnGE8fJT3GXwEOngsV7Zt27NXFoaoApmYm81iuXoPkFOJwJ8ERdknLPMO\" crossorigin=\"anonymous\">\n   <script src=\"https://code.jquery.com/jquery-3.3.1.js\" ></script>\n<script src=\"https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.14.3/umd/popper.min.js\" ></script>\n<script src=\"https://stackpath.bootstrapcdn.com/bootstrap/4.1.3/js/bootstrap.js\"></script>\n   <style>\n.coltbtop{\nborder-top: 1px solid black;\n}\n.coltbbot{\nborder-bottom: 1px solid black;\n}\n.coltbrigth{\nborder-right: 1px solid black;\n}\n.coltbleft{\nborder-left: 1px solid black;\n}\n.footertable\n{\n    position:absolute;\n    bottom: 8px;\n    right: 16px;\n    center:15px;\n}\n#fontpage{\n  font-size: 20px;\n  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;\n}\n   </style>\n </head>`;\n      var sale = `   <div id=\"fontpage\"  class=\"container-fluid footertable \" >\n <div class=\"row\" style=\"margin-top: 2%; margin-bottom: 2%; margin-left: 10%\">\n   <div class=\"col-12 form-inline\">\n     <div class=\"col-4\">\n       <table  border=\"1\">\n         <tbody>\n           <tr>\n             <td>\n             <label class=\"text-center\"> </label><BR>\n               <label class=\"text-center\">__________________________________</label><BR>\n                 <label class=\"text-center\" >Purchaser/ผู้อนุมัติซื้อ</label>\n                 <label  class=\"text-center\">Date____/____/____</label>\n             </td>\n           </tr>\n         </tbody>\n       </table>\n     </div>\n     <div class=\"col-4\">\n       <table  border=\"1\">\n         <tbody>\n           <tr>\n             <td>\n             <label class=\"text-center\"> </label><BR>\n               <label class=\"text-center\">__________________________________</label><BR>\n                 <label class=\"text-center\" >Sale/พนักงานขาย</label>\n                 <label  class=\"text-center\">Date____/____/____</label>\n             </td>\n           </tr>\n         </tbody>\n       </table>\n     </div>\n     <div class=\"col-4\" >\n       <table  border=\"1\">\n         <tbody>\n           <tr>\n             <td>\n             <label class=\"text-center\"> </label><BR>\n               <label class=\"text-center\">__________________________________</label><BR>\n                 <label class=\"text-center\" >Manager/ผู้จัดการฝ่ายขาย</label>\n                 <label  class=\"text-center\">Date____/____/____</label>\n             </td>\n           </tr>\n         </tbody>\n       </table>\n     </div>\n   </div>\n   </div>\n</div>`;\n      var foot = `     <tfoot id=\"fontpage\" class=\"coltbtop coltbbot  coltbrigth coltbleft\">\n <tr >\n     <td  class=\" coltbbot coltbrigth \" colspan=\"3\" rowspan=\"2\">หมายเหตุ* ${this.salehderprint[0].remark}</td>\n\n     <td class=\"text-center coltbbot coltbrigth\" colspan=\"4\">ราคารวมทั้งสิ้น(TOTAL)</td>\n     <td class=\"text-right coltbbot\">` + parseInt(this.salehderprint[0].amount).toLocaleString(undefined, {\n        minimumFractionDigits: 2,\n        maximumFractionDigits: 2\n      }) + `</td>\n   </tr>\n   <tr>\n\n     <td  class=\"text-center coltbbot coltbrigth\" colspan=\"4\">จำนวนภาษีมูลค่าเพิ่ม</td>\n     <td class=\"text-right coltbbot\">` + tex.toLocaleString(undefined, {\n        minimumFractionDigits: 2,\n        maximumFractionDigits: 2\n      }) + `</td>\n   </tr>\n   <tr>\n     <td   class=\"text-center coltbbot coltbrigth\" colspan=\"3\">` + bathstr + `</td>\n\n     <td  class=\"text-center coltbrigth\" colspan=\"4\">รวมทั้งสิ้น(TOTAL)</td>\n\n     <td class=\"text-right\">` + sum.toLocaleString(undefined, {\n        minimumFractionDigits: 2,\n        maximumFractionDigits: 2\n      }) + `</td>\n   </tr>\n</tfoot>`;\n      let popupWin;\n      var pageweb = `\n  ${haeder}\n${haederimg}\n          <div id=\"fontpage\" class=\"row\">\n            <div class=\"col-md-12 offset-0\">\n              <table cellpadding=\"7\"  width=\"100%\"  style=\"margin-top: 1%\">\n              <thead  style=\"border: 1; border-color: black\" >\n              <tr class=\"text-center coltbtop coltbleft  coltbrigth\" >\n                  <th width=\"5%\" class=\"coltbrigth\">ลำดับที่</th>\n                  <th class=\"coltbrigth\">รายการ</th>\n                  <th width=\"5%\" class=\"coltbrigth\">จำนวน</th>\n                  <th class=\"coltbrigth\">ราคา</th>\n                  <th colspan=\"3\" class=\"coltbrigth\">ส่วนลด</th>\n                  <th >จำนวนเงิน</th>\n\n              </tr>\n              <tr class=\"text-center coltbbot coltbleft coltbrigth\">\n                  <th width=\"5%\" class=\"coltbrigth\">ITEM</th>\n                  <th class=\"coltbrigth\">DESCRIPTION</th>\n                  <th width=\"5%\" class=\"coltbrigth\">Quantity</th>\n                  <th class=\"coltbrigth\">Price</th>\n                  <th colspan=\"3\" class=\"coltbrigth\">Discount</th>\n                  <th>Amount</th>\n              </tr>\n\n\n            </thead>\n                <tbody class=\"coltbbot\">\n                ${linesale}\n                </tbody>\n              </table>\n                </div>\n          </div>\n          <div class=\"row\" style=\"margin-top: 2%; margin-left: 6%\">\n\n\n          ${sale}\n      </div>\n  </div>\n</body>\n</html>`;\n      if (this.salelistview.length < 19) {\n        var pageweb = `\n    ${haeder}\n  ${haederimg}\n            <div id=\"fontpage\" class=\"row\">\n              <div class=\"col-md-12 offset-0\">\n                <table cellpadding=\"7\"  width=\"100%\"  style=\"margin-top: 1%\">\n                  <thead  style=\"border: 1; border-color: black\" >\n                    <tr class=\"text-center coltbtop coltbleft  coltbrigth\" >\n                        <th width=\"5%\" class=\"coltbrigth\">ลำดับที่</th>\n                        <th class=\"coltbrigth\">รายการ</th>\n                        <th width=\"5%\" class=\"coltbrigth\">จำนวน</th>\n                        <th class=\"coltbrigth\">ราคา</th>\n                        <th colspan=\"3\" class=\"coltbrigth\">ส่วนลด</th>\n                        <th >จำนวนเงิน</th>\n\n                    </tr>\n                    <tr class=\"text-center coltbbot coltbleft coltbrigth\">\n                        <th width=\"5%\" class=\"coltbrigth\">ITEM</th>\n                        <th class=\"coltbrigth\">DESCRIPTION</th>\n                        <th width=\"5%\" class=\"coltbrigth\">Quantity</th>\n                        <th class=\"coltbrigth\">Price</th>\n                        <th colspan=\"3\" class=\"coltbrigth\">Discount</th>\n                        <th>Amount</th>\n                    </tr>\n\n\n                  </thead>\n                  <tbody class=\"coltbbot\">\n\n                  ${linesale}\n\n\n                  </tbody>\n                  ${foot}\n                </table>\n                  </div>\n            </div>\n            <div class=\"row\" style=\"margin-top: 2%; margin-left: 6%\">\n\n\n            ${sale}\n        </div>\n    </div>\n  </body>\n  </html>`;\n      } else {\n        alert(2);\n        pageweb += `\n${haeder}\n${haederimg}\n<div id=\"fontpage\" class=\"row\">\n              <div class=\"col-md-12 offset-0\">\n                <table cellpadding=\"7\"  width=\"100%\"  style=\"margin-top: 1%\">\n                  <thead  style=\"border: 1; border-color: black\" >\n                    <tr class=\"text-center coltbtop coltbleft  coltbrigth\" >\n                        <th width=\"5%\" class=\"coltbrigth\">ลำดับที่</th>\n                        <th class=\"coltbrigth\">รายการ</th>\n                        <th width=\"5%\" class=\"coltbrigth\">จำนวน</th>\n                        <th class=\"coltbrigth\">ราคา</th>\n                        <th colspan=\"3\" class=\"coltbrigth\">ส่วนลด</th>\n                        <th >จำนวนเงิน</th>\n\n                    </tr>\n                    <tr class=\"text-center coltbbot coltbleft coltbrigth\">\n                        <th width=\"5%\" class=\"coltbrigth\">ITEM</th>\n                        <th class=\"coltbrigth\">DESCRIPTION</th>\n                        <th width=\"5%\" class=\"coltbrigth\">Quantity</th>\n                        <th class=\"coltbrigth\">Price</th>\n                        <th colspan=\"3\" class=\"coltbrigth\">Discount</th>\n                        <th>Amount</th>\n                    </tr>\n\n\n                  </thead>\n                  <tbody class=\"coltbbot\">\n\n                  ${linesalepage2}\n\n\n                  </tbody>\n                  ${foot}\n                </table>\n                  </div>\n            </div>\n            <div class=\"row\" style=\"margin-top: 2%; margin-left: 6%\">\n\n\n            ${sale}\n        </div>\n    </div>\n  </body>\n  </html>`;\n      }\n      /*popupWin = window.open();\n      popupWin.document.open();\n      popupWin.document.write(pageweb);\n           \n      popupWin.document.close();*/\n      console.log(pageweb);\n      item = 0;\n    }\n    openModal(open, text, load) {\n      this.mdlSampleIsOpen = open;\n      this.alt = text;\n      this.checkreload = load;\n    }\n    closemodel(cl) {\n      this.mdlSampleIsOpen = cl;\n      if (this.checkreload == true) {\n        location.reload();\n      }\n    }\n    handleFileInput(file) {\n      this.textload = \"\";\n      if (file.item(0).type == \"application/pdf\" || file.item(0).type == \"image/jpeg\") {\n        this.selectedFile = file.item(0);\n        this.textload = this.selectedFile.name;\n      } else {\n        alert('ชนิดไฟล์ไม่ถูกต้อง กรุณาเลือกเป็นไฟล์ .PDF หรือ เป็นไฟล์ .jpeg');\n        this.textload = \"\";\n      }\n    }\n    OpenUploadPDF(template, idSo) {\n      this.btnPDF = false;\n      this.btnREpdf = false;\n      this.showIDso = \"\";\n      this.showIDso = idSo;\n      this.selectedFile = null;\n      this.modalRef = this.modalService.show(template, this.config);\n    }\n    onUpload(showIDso, templateShow, template) {\n      this.modalRef.hide();\n      this.textload = \"กำลังอัพโหลดไฟล์ โปรดรอ.\";\n      this.openModal2(templateShow);\n      const fd = new FormData();\n      fd.append('PDF', this.selectedFile, this.selectedFile.name);\n      this.http.post(this.url + showIDso + '/uploadPDF', fd, {\n        reportProgress: true,\n        observe: 'events'\n      }).subscribe(event => {\n        if (event.type === HttpEventType.UploadProgress) {} else if (event.type === HttpEventType.Response) {\n          console.log(event);\n          if (event.body.success === true) {\n            this.textload = \"อัพโหลดไฟล์เสร็จสิ้น\";\n            this.btnPDF = true;\n            this.btnREpdf = false;\n            /*  success: true,\n            _path : DIRPDF,\n            _name : res.req.file.filename,\n            _mimetype : res.req.file.mimetype\n              */\n            this.updataFlie(showIDso, event.body._name, event.body._mimetype);\n            /*   alert(JSON.stringify(event.body))\n               alert('upload เสร็จสิ้น : >>>'+event.body.success )*/\n          } else {\n            this.textload = \"เกิดปัญหาในการ upload กรุณาทำรายการใหม่\";\n            this.btnPDF = false;\n            this.btnREpdf = true;\n            //alert('เกิดปัญหาในการ upload กรุณาทำรายการใหม่' )\n          }\n        }\n      });\n    }\n    updataFlie(_idSo, _name, _mimetype) {\n      //updataDPF_idSo\n      this.http.post(this.url + 'updataDPF_idSo', {\n        idSo: _idSo,\n        _name: _name,\n        _mimetype: _mimetype\n      }).subscribe(res => {\n        //  alert(res)\n        if (res == true) {\n          this.textload = \"ทำรายการเสร็จสิ้น\";\n          this.btnPDF = true;\n          this.btnREpdf = false;\n        } else {\n          this.textload = \"เกิดปัญหาในการ เพิ่มรายการ SaleOrder\";\n          this.btnPDF = false;\n          this.btnREpdf = true;\n        }\n      });\n    }\n    openModal2(templateShow) {\n      this.modalRef = this.modalService.show(templateShow, {\n        class: 'modal-sm',\n        backdrop: \"static\"\n      });\n    }\n    confirm() {\n      this.modalRef.hide();\n      this.Searchsolist();\n    }\n    decline(template) {\n      this.selectedFile = null;\n      this.modalRef.hide();\n      this.modalRef = this.modalService.show(template, this.config);\n    }\n    static {\n      this.ɵfac = function Solist1Component_Factory(t) {\n        return new (t || Solist1Component)(i0.ɵɵdirectiveInject(i1.BsModalService), i0.ɵɵdirectiveInject(i2.HttpClient), i0.ɵɵdirectiveInject(i3.WebapiService), i0.ɵɵdirectiveInject(i4.NgbCalendar), i0.ɵɵdirectiveInject(i5.Router));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: Solist1Component,\n        selectors: [[\"app-solist1\"]],\n        decls: 110,\n        vars: 22,\n        consts: [[\"template\", \"\"], [\"templateShow\", \"\"], [\"templateView\", \"\"], [\"rt\", \"\"], [\"staticTabs\", \"\"], [\"imageForm\", \"ngForm\"], [2, \"padding-top\", \"60px\"], [1, \"container-fluid\", 2, \"padding-right\", \"5px\", \"padding-left\", \"5px\"], [1, \"p-sm-1\", \"bg-secondary\", \"text-white\", \"text-center\"], [1, \"form-row\"], [\"class\", \"col-md-2 mb-2\", 4, \"ngIf\"], [1, \"col-xs-12\", \"col-12\", \"col-md-2\", \"form-group\"], [\"type\", \"text\", \"placeholder\", \"DD/MM/YYYY\", \"bsDatepicker\", \"\", 1, \"form-control\", \"form-control-sm\", 3, \"ngModelChange\", \"bsConfig\", \"ngModel\"], [1, \"col-md-3\", \"mb-3\", \"col-12\", \"text-center\", \"text-sm-center\", \"text-md-left\", \"text-lg-left\", 2, \"padding-right\", \"0px\", \"padding-left\", \"0px\"], [\"type\", \"submit\", 1, \"btn\", \"btn-primary\", \"btn-sm\", \"font-weight-light\", 2, \"width\", \"60px\", 3, \"click\", \"disabled\"], [\"type\", \"submit\", 1, \"btn\", \"btn-primary\", \"btn-sm\", \"font-weight-light\", 2, \"margin-left\", \"3px\", \"width\", \"65px\", 3, \"click\", \"disabled\"], [1, \"table\", \"table-hover\", \"table-bordered\", \"table-sm\"], [1, \"text-sm-center\", \"bg-light\"], [\"scope\", \"col\", 1, \"font-weight-normal\"], [\"scope\", \"col\", 1, \"font-weight-light\"], [\"class\", \"font-weight-normal\", \"scope\", \"col\", 4, \"ngIf\"], [\"class\", \"text-sm-left\", 4, \"ngFor\", \"ngForOf\"], [\"id\", \"mdlSample\", \"role\", \"dialog\", 1, \"modal\", \"fade\", 3, \"ngStyle\"], [1, \"modal-dialog\", \"modal-md\"], [1, \"modal-content\"], [1, \"modal-header\", \"colhaederal\"], [1, \"modal-title\"], [1, \"modal-body\"], [\"align\", \"right\", 1, \"modal-footer\"], [\"type\", \"button\", \"id\", \"btnClose\", 1, \"btn\", \"btn-danger\", 3, \"click\"], [1, \"fa\", \"fa-times\", \"fa-fw\"], [\"id\", \"ViewDetailSaleoderlist\", \"tabindex\", \"-1\", \"role\", \"dialog\", \"aria-labelledby\", \"exampleModalLabel\", \"aria-hidden\", \"true\", 1, \"modal\", \"fade\", \"bd-example-modal-lg\"], [\"role\", \"document\", 1, \"modal-dialog\", \"modal-lg\"], [1, \"modal-header\"], [\"id\", \"exampleModalLabel\", 1, \"modal-title\"], [\"type\", \"button\", \"data-dismiss\", \"modal\", \"aria-label\", \"Close\", 1, \"close\"], [\"aria-hidden\", \"true\"], [1, \"modal-body\", \"modal-lg\"], [1, \"text-center\", \"font-weight-normal\"], [1, \"font-weight-normal\"], [\"class\", \"font-weight-normal\", 4, \"ngIf\"], [\"class\", \"font-weight-normal text-center\", \"colspan\", \"3\", 4, \"ngIf\"], [4, \"ngFor\", \"ngForOf\"], [1, \"modal-footer\"], [\"type\", \"button\", \"data-dismiss\", \"modal\", 1, \"btn\", \"btn-secondary\"], [1, \"col-md-2\", \"mb-2\"], [\"multiple\", \"\", 1, \"custom-select\", \"custom-select-sm\", 3, \"ngModelChange\", \"ngModel\"], [\"selected\", \"\", \"value\", \"\"], [\"value\", \"%20\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [3, \"value\"], [\"id\", \"typeahead-template\", \"type\", \"text\", \"name\", \"getcustomer\", 1, \"form-control\", \"form-control-sm\", 3, \"ngModelChange\", \"placeholder\", \"ngModel\", \"ngbTypeahead\", \"resultTemplate\", \"inputFormatter\"], [3, \"click\"], [3, \"mousedown\"], [1, \"text-sm-left\"], [1, \"text-sm-center\", \"font-weight-normal\", 3, \"ngStyle\"], [1, \"text-sm-center\", \"font-weight-normal\", 3, \"click\", \"ngStyle\"], [1, \"text-sm-center\", \"font-weight-normal\", 3, \"tooltip\", \"ngStyle\"], [1, \"font-weight-normal\", 3, \"ngStyle\"], [1, \"text-center-sm\", 3, \"ngStyle\"], [\"class\", \"text-sm-right font-weight-normal\", 3, \"ngStyle\", 4, \"ngIf\"], [1, \"text-sm-center\", \"font-weight-normal\"], [1, \"btn\", \"btn-link\", \"font-weight-normal\", 2, \"padding\", \"0pt\", 3, \"click\"], [4, \"ngIf\"], [1, \"text-center\"], [1, \"text-info\", \"btn-link\", \"btn-sm\", 3, \"click\"], [1, \"text-danger\", \"btn-link\", \"btn-sm\", 3, \"click\"], [1, \"text-sm-right\", \"font-weight-normal\", 3, \"ngStyle\"], [\"type\", \"button\", 1, \"btn\", \"btn-link\", \"font-weight-normal\", 2, \"width\", \"65px\", \"height\", \"26px\", \"padding\", \"0px\", 3, \"click\"], [\"colspan\", \"3\", 1, \"font-weight-normal\", \"text-center\"], [1, \"text-center\", \"font-weight-sm\"], [1, \"text-left\", \"font-weight-sm\"], [\"class\", \"text-center font-weight-sm\", 4, \"ngIf\"], [\"class\", \"text-right font-weight-sm\", 3, \"ngStyle\", 4, \"ngIf\"], [\"class\", \"text-right font-weight-sm\", 4, \"ngIf\"], [1, \"text-right\", \"font-weight-sm\", 3, \"ngStyle\"], [1, \"text-right\", \"font-weight-sm\"], [1, \"modal-title\", \"pull-left\"], [\"type\", \"button\", \"aria-label\", \"Close\", 1, \"close\", \"pull-right\", 3, \"click\"], [1, \"form-group\", 2, \"margin-bottom\", \"0rem\"], [\"for\", \"exampleFormControlFile1\"], [\"type\", \"file\", \"accept\", \"application/pdf, image/*\", \"id\", \"exampleFormControlFile1\", 1, \"form-control-file\", 3, \"change\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", 3, \"click\", \"disabled\"], [\"type\", \"button\", 1, \"btn\", \"btn-secondary\", 3, \"click\"], [1, \"modal-body\", \"text-center\"], [\"type\", \"button\", \"class\", \"btn btn-default\", 3, \"click\", 4, \"ngIf\"], [\"type\", \"button\", \"class\", \"btn btn-primary\", 3, \"click\", 4, \"ngIf\"], [\"type\", \"button\", 1, \"btn\", \"btn-default\", 3, \"click\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", 3, \"click\"], [1, \"modal-body\", 2, \"padding\", \"3px\"], [\"heading\", \"Sale Oder list\", 2, \"padding-top\", \"5px\"], [2, \"overflow-x\", \"auto\"], [\"heading\", \"File\", 4, \"ngIf\"], [\"target\", \"_blank\", \"type\", \"button\", \"class\", \"btn btn-primary\", 3, \"href\", 4, \"ngIf\"], [\"heading\", \"File\"], [1, \"card\", \"card-body\"], [2, \"text-align\", \"center\"], [2, \"width\", \"100%\", 3, \"src\"], [\"target\", \"_blank\", \"type\", \"button\", 1, \"btn\", \"btn-primary\", 3, \"href\"]],\n        template: function Solist1Component_Template(rf, ctx) {\n          if (rf & 1) {\n            const _r1 = i0.ɵɵgetCurrentView();\n            i0.ɵɵelement(0, \"app-topmenu\");\n            i0.ɵɵelementStart(1, \"section\", 6)(2, \"div\", 7)(3, \"h5\", 8);\n            i0.ɵɵtext(4, \"Sale Order List\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(5, \"div\", 9);\n            i0.ɵɵtemplate(6, Solist1Component_div_6_Template, 7, 2, \"div\", 10)(7, Solist1Component_div_7_Template, 4, 5, \"div\", 10);\n            i0.ɵɵelementStart(8, \"div\", 11)(9, \"input\", 12);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function Solist1Component_Template_input_ngModelChange_9_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.Datafromdate, $event) || (ctx.Datafromdate = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(10, \"div\", 11)(11, \"input\", 12);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function Solist1Component_Template_input_ngModelChange_11_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.Datatodate, $event) || (ctx.Datatodate = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(12, \"div\", 13)(13, \"button\", 14);\n            i0.ɵɵlistener(\"click\", function Solist1Component_Template_button_click_13_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.Searchsolist());\n            });\n            i0.ɵɵtext(14, \"Search\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(15, \"button\", 15);\n            i0.ɵɵlistener(\"click\", function Solist1Component_Template_button_click_15_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.exportdataexcel());\n            });\n            i0.ɵɵtext(16, \"Export\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(17, \"table\", 16)(18, \"thead\")(19, \"tr\", 17)(20, \"th\", 18);\n            i0.ɵɵtext(21, \"Item\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(22, \"th\", 19);\n            i0.ɵɵtext(23, \"\\u0E40\\u0E25\\u0E02\\u0E17\\u0E35\\u0E48 SO\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(24, \"th\", 18);\n            i0.ɵɵtext(25, \"\\u0E27\\u0E31\\u0E19\\u0E17\\u0E35\\u0E48\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(26, \"th\", 18);\n            i0.ɵɵtext(27, \"\\u0E40\\u0E27\\u0E25\\u0E32\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(28, \"th\", 18);\n            i0.ɵɵtext(29, \"\\u0E1E\\u0E19\\u0E31\\u0E01\\u0E07\\u0E32\\u0E19\\u0E02\\u0E32\\u0E22\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(30, \"th\", 18);\n            i0.ɵɵtext(31, \"\\u0E25\\u0E39\\u0E01\\u0E04\\u0E49\\u0E32\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(32, \"th\", 18);\n            i0.ɵɵtext(33, \"\\u0E0A\\u0E37\\u0E48\\u0E2D INV\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(34, \"th\", 19);\n            i0.ɵɵtext(35, \"TAX ID\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(36, \"th\", 18);\n            i0.ɵɵtext(37, \"\\u0E0A\\u0E37\\u0E48\\u0E2D\\u0E02\\u0E19\\u0E2A\\u0E48\\u0E07\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(38, Solist1Component_th_38_Template, 2, 0, \"th\", 20)(39, Solist1Component_th_39_Template, 2, 0, \"th\", 20);\n            i0.ɵɵelementStart(40, \"th\", 18);\n            i0.ɵɵtext(41, \"VAT/No VAT\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(42, \"th\", 18);\n            i0.ɵɵtext(43, \"\\u0E19\\u0E49\\u0E33\\u0E2B\\u0E19\\u0E31\\u0E01\\u0E23\\u0E27\\u0E21\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(44, \"th\", 18);\n            i0.ɵɵtext(45, \"\\u0E1B\\u0E23\\u0E30\\u0E40\\u0E20\\u0E17\\u0E02\\u0E19\\u0E2A\\u0E48\\u0E07\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(46, \"th\", 18);\n            i0.ɵɵtext(47, \"\\u0E40\\u0E07\\u0E34\\u0E19\\u0E2A\\u0E14/\\u0E40\\u0E04\\u0E23\\u0E14\\u0E34\\u0E15\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(48, \"th\", 18);\n            i0.ɵɵtext(49, \"Note \\u0E20\\u0E32\\u0E22\\u0E43\\u0E19\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(50, \"th\", 18);\n            i0.ɵɵtext(51, \"\\u0E2B\\u0E21\\u0E32\\u0E22\\u0E40\\u0E2B\\u0E15\\u0E38\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(52, \"th\", 18);\n            i0.ɵɵtext(53, \"View\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(54, Solist1Component_th_54_Template, 2, 0, \"th\", 20);\n            i0.ɵɵelementStart(55, \"th\", 18);\n            i0.ɵɵtext(56, \"Edit\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(57, \"th\", 18);\n            i0.ɵɵtext(58, \"Delete\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(59, \"tbody\");\n            i0.ɵɵtemplate(60, Solist1Component_tr_60_Template, 47, 79, \"tr\", 21);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(61, \"div\", 22)(62, \"div\", 23)(63, \"div\", 24)(64, \"div\", 25)(65, \"h4\", 26);\n            i0.ɵɵtext(66, \"Report\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(67, \"div\", 27);\n            i0.ɵɵtext(68);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(69, \"div\", 28)(70, \"button\", 29);\n            i0.ɵɵlistener(\"click\", function Solist1Component_Template_button_click_70_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.closemodel(false));\n            });\n            i0.ɵɵelement(71, \"i\", 30);\n            i0.ɵɵtext(72, \" \\u0E1B\\u0E34\\u0E14\");\n            i0.ɵɵelementEnd()()()()();\n            i0.ɵɵelementStart(73, \"div\", 31)(74, \"div\", 32)(75, \"div\", 24)(76, \"div\", 33)(77, \"h5\", 34);\n            i0.ɵɵtext(78, \"Sale Oder Detail\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(79, \"button\", 35)(80, \"span\", 36);\n            i0.ɵɵtext(81, \"\\u00D7\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(82, \"div\", 37)(83, \"table\", 16)(84, \"thead\")(85, \"tr\")(86, \"th\", 38);\n            i0.ɵɵtext(87, \"\\u0E23\\u0E2B\\u0E31\\u0E2A\\u0E2A\\u0E34\\u0E19\\u0E04\\u0E49\\u0E32\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(88, \"th\", 38);\n            i0.ɵɵtext(89, \"\\u0E0A\\u0E37\\u0E48\\u0E2D\\u0E2A\\u0E34\\u0E19\\u0E04\\u0E49\\u0E32\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(90, \"th\", 39);\n            i0.ɵɵtext(91, \"Pack\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(92, \"th\", 39);\n            i0.ɵɵtext(93, \"\\u0E08\\u0E33\\u0E19\\u0E27\\u0E19\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(94, \"th\", 39);\n            i0.ɵɵtext(95, \"\\u0E19\\u0E49\\u0E33\\u0E2B\\u0E19\\u0E31\\u0E01\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(96, Solist1Component_th_96_Template, 2, 0, \"th\", 40)(97, Solist1Component_th_97_Template, 2, 0, \"th\", 41)(98, Solist1Component_th_98_Template, 2, 0, \"th\", 40);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(99, \"tbody\");\n            i0.ɵɵtemplate(100, Solist1Component_tr_100_Template, 19, 19, \"tr\", 42);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(101, \"div\", 43)(102, \"button\", 44);\n            i0.ɵɵtext(103, \"Close\");\n            i0.ɵɵelementEnd()()()()()();\n            i0.ɵɵtemplate(104, Solist1Component_ng_template_104_Template, 16, 2, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor)(106, Solist1Component_ng_template_106_Template, 5, 3, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor)(108, Solist1Component_ng_template_108_Template, 34, 7, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor);\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(6);\n            i0.ɵɵproperty(\"ngIf\", ctx.testclose);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.enablecustomer);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"bsConfig\", i0.ɵɵpureFunction0(18, _c0));\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.Datafromdate);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"bsConfig\", i0.ɵɵpureFunction0(19, _c0));\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.Datatodate);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"disabled\", ctx.searchbtn);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"disabled\", ctx.exportbtn);\n            i0.ɵɵadvance(23);\n            i0.ɵɵproperty(\"ngIf\", ctx.enablecustomer);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.enablecustomer);\n            i0.ɵɵadvance(15);\n            i0.ɵɵproperty(\"ngIf\", ctx.showtablecustomer);\n            i0.ɵɵadvance(6);\n            i0.ɵɵproperty(\"ngForOf\", ctx.solist);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(20, _c1, ctx.mdlSampleIsOpen ? \"block\" : \"none\"));\n            i0.ɵɵadvance(7);\n            i0.ɵɵtextInterpolate(ctx.alt);\n            i0.ɵɵadvance(28);\n            i0.ɵɵproperty(\"ngIf\", ctx.enablecustomer);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.enablecustomer);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.enablecustomer);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngForOf\", ctx.salelistview);\n          }\n        },\n        dependencies: [i6.TooltipDirective, i7.NgForOf, i7.NgIf, i7.NgStyle, i8.ɵNgNoValidate, i8.NgSelectOption, i8.ɵNgSelectMultipleOption, i8.DefaultValueAccessor, i8.SelectMultipleControlValueAccessor, i8.NgControlStatus, i8.NgControlStatusGroup, i8.NgModel, i8.NgForm, i4.NgbTypeahead, i9.BsDatepickerDirective, i9.BsDatepickerInputDirective, i10.TabDirective, i10.TabsetComponent, i11.TopmenuComponent, i7.DecimalPipe, i7.DatePipe]\n      });\n    }\n  }\n  return Solist1Component;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}