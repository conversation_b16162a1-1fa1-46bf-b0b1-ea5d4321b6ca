{"ast": null, "code": "import { WebapiService } from './../webapi.service';\nimport { TemplateRef } from '@angular/core';\nimport { HttpClient, HttpEventType } from '@angular/common/http';\nimport { NgbCalendar } from '@ng-bootstrap/ng-bootstrap';\nimport { Ng2ImgMaxService } from 'ng2-img-max';\nimport { debounceTime, map } from 'rxjs/operators';\nimport { Router, ActivatedRoute } from '@angular/router';\nimport { BsModalService } from 'ngx-bootstrap/modal';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"ngx-bootstrap/modal\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"ng2-img-max\";\nimport * as i4 from \"@angular/common/http\";\nimport * as i5 from \"./../webapi.service\";\nimport * as i6 from \"@ng-bootstrap/ng-bootstrap\";\nconst _c0 = () => ({\n  dateInputFormat: \"DD/MM/YYYY\"\n});\nconst _c1 = a0 => ({\n  \"display\": a0,\n  \"opacity\": 1\n});\nconst _c2 = (a0, a1) => ({\n  \"color\": a0,\n  \"background-color\": a1\n});\nconst _c3 = a0 => ({\n  \"color\": a0\n});\nfunction InvoicecreditlistComponent_div_6_option_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 76);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r4 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", item_r4.groupid);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\" \", item_r4.groupid, \" (\", item_r4.name, \") \");\n  }\n}\nfunction InvoicecreditlistComponent_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 9)(1, \"select\", 72);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function InvoicecreditlistComponent_div_6_Template_select_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.salegroup, $event) || (ctx_r2.salegroup = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementStart(2, \"option\", 73);\n    i0.ɵɵtext(3, \"--\\u0E40\\u0E25\\u0E37\\u0E2D\\u0E01\\u0E23\\u0E2B\\u0E31\\u0E2A\\u0E1E\\u0E19\\u0E31\\u0E01\\u0E07\\u0E32\\u0E19\\u0E02\\u0E32\\u0E22--\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"option\", 74);\n    i0.ɵɵtext(5, \"\\u0E40\\u0E25\\u0E37\\u0E2D\\u0E01\\u0E23\\u0E32\\u0E22\\u0E01\\u0E32\\u0E23\\u0E17\\u0E31\\u0E49\\u0E07\\u0E2B\\u0E21\\u0E14\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, InvoicecreditlistComponent_div_6_option_6_Template, 2, 3, \"option\", 75);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.salegroup);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.DateGroupsaleman);\n  }\n}\nfunction InvoicecreditlistComponent_ng_template_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 77);\n    i0.ɵɵlistener(\"click\", function InvoicecreditlistComponent_ng_template_8_Template_div_click_0_listener() {\n      const r_r6 = i0.ɵɵrestoreView(_r5).result;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.getcustomersalefunction(r_r6.name));\n    });\n    i0.ɵɵelementStart(1, \"label\", 78);\n    i0.ɵɵlistener(\"mousedown\", function InvoicecreditlistComponent_ng_template_8_Template_label_mousedown_1_listener() {\n      const r_r6 = i0.ɵɵrestoreView(_r5).result;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.getcustomersalefunction(r_r6.name));\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const r_r6 = ctx.result;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"\", r_r6.name, \" (\", r_r6.accountnum, \")\");\n  }\n}\nfunction InvoicecreditlistComponent_tr_50_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\", 26)(1, \"td\", 79);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\", 79);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\", 79);\n    i0.ɵɵtext(6);\n    i0.ɵɵpipe(7, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"td\", 79);\n    i0.ɵɵtext(9);\n    i0.ɵɵpipe(10, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"td\", 79);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"td\", 79);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"td\", 80);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"td\", 81);\n    i0.ɵɵtext(18);\n    i0.ɵɵpipe(19, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"td\", 81);\n    i0.ɵɵtext(21);\n    i0.ɵɵpipe(22, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"td\", 81);\n    i0.ɵɵtext(24);\n    i0.ɵɵpipe(25, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"td\", 82)(27, \"button\", 83);\n    i0.ɵɵlistener(\"click\", function InvoicecreditlistComponent_tr_50_Template_button_click_27_listener() {\n      const item_r8 = i0.ɵɵrestoreView(_r7).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.GetBillNo(item_r8));\n    });\n    i0.ɵɵtext(28, \" Upload \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const item_r8 = ctx.$implicit;\n    const i_r9 = ctx.index;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction2(35, _c2, ctx_r2.getColor(item_r8.invoicedate, item_r8.invoiceamount, item_r8.sumMax, item_r8.summin), ctx_r2.getColorminBG(item_r8.typeCK)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i_r9 + 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(38, _c3, ctx_r2.getColor(item_r8.invoicedate, item_r8.invoiceamount, item_r8.sumMax, item_r8.summin)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r8.billno);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(40, _c3, ctx_r2.getColor(item_r8.invoicedate, item_r8.invoiceamount, item_r8.sumMax, item_r8.summin)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(7, 20, item_r8.billingnotesdate, \"dd/MM/yyyy\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(42, _c3, ctx_r2.getColor(item_r8.invoicedate, item_r8.invoiceamount, item_r8.sumMax, item_r8.summin)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(10, 23, item_r8.duedate, \"dd/MM/yyyy\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(44, _c3, ctx_r2.getColor(item_r8.invoicedate, item_r8.invoiceamount, item_r8.sumMax, item_r8.summin)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r8.salegroup);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(46, _c3, ctx_r2.getColor(item_r8.invoicedate, item_r8.invoiceamount, item_r8.sumMax, item_r8.summin)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r8.orderaccount);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(48, _c3, ctx_r2.getColor(item_r8.invoicedate, item_r8.invoiceamount, item_r8.sumMax, item_r8.summin)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r8.invoicingname);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(50, _c3, ctx_r2.getColor(item_r8.invoicedate, item_r8.invoiceamount, item_r8.sumMax, item_r8.summin)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(19, 26, item_r8.salesbalance, \"1.2-2\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(52, _c3, ctx_r2.getColor(item_r8.invoicedate, item_r8.invoiceamount, item_r8.sumMax, item_r8.summin)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(22, 29, item_r8.sumtax, \"1.2-2\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(54, _c3, ctx_r2.getColor(item_r8.invoicedate, item_r8.invoiceamount, item_r8.sumMax, item_r8.summin)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(25, 32, item_r8.invoiceamount, \"1.2-2\"));\n  }\n}\nfunction InvoicecreditlistComponent_tr_104_button_25_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 89);\n    i0.ɵɵlistener(\"click\", function InvoicecreditlistComponent_tr_104_button_25_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const itembill_r13 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      const Modalview_r14 = i0.ɵɵreference(188);\n      return i0.ɵɵresetView(ctx_r2.openModalIMG(Modalview_r14, itembill_r13.imgurl, itembill_r13.typeCK));\n    });\n    i0.ɵɵtext(1, \" View \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction InvoicecreditlistComponent_tr_104_button_26_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 90);\n    i0.ɵɵlistener(\"click\", function InvoicecreditlistComponent_tr_104_button_26_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r15);\n      const itembill_r13 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      const Modalview_r14 = i0.ɵɵreference(188);\n      return i0.ɵɵresetView(ctx_r2.openModalIMG(Modalview_r14, itembill_r13.imgurl, itembill_r13.typeCK));\n    });\n    i0.ɵɵtext(1, \" View \");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const itembill_r13 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"disabled\", itembill_r13.typeCK !== \"1\");\n  }\n}\nfunction InvoicecreditlistComponent_tr_104_td_27_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"td\", 82)(1, \"button\", 91);\n    i0.ɵɵlistener(\"click\", function InvoicecreditlistComponent_tr_104_td_27_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const itembill_r13 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.openpdf(itembill_r13.invoiceid));\n    });\n    i0.ɵɵtext(2, \" print \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction InvoicecreditlistComponent_tr_104_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"th\", 84);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"th\", 84);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\", 84);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\", 84);\n    i0.ɵɵtext(8);\n    i0.ɵɵpipe(9, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"td\", 84);\n    i0.ɵɵtext(11);\n    i0.ɵɵpipe(12, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"td\", 81);\n    i0.ɵɵtext(14);\n    i0.ɵɵpipe(15, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"th\", 81);\n    i0.ɵɵtext(17);\n    i0.ɵɵpipe(18, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"th\", 81);\n    i0.ɵɵtext(20);\n    i0.ɵɵpipe(21, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"td\", 82)(23, \"input\", 85);\n    i0.ɵɵlistener(\"click\", function InvoicecreditlistComponent_tr_104_Template_input_click_23_listener($event) {\n      const i_r11 = i0.ɵɵrestoreView(_r10).index;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.checkIfAllSelected($event.target.checked, i_r11));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(24, \"td\", 82);\n    i0.ɵɵtemplate(25, InvoicecreditlistComponent_tr_104_button_25_Template, 2, 0, \"button\", 86)(26, InvoicecreditlistComponent_tr_104_button_26_Template, 2, 1, \"button\", 87);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(27, InvoicecreditlistComponent_tr_104_td_27_Template, 3, 0, \"td\", 88);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const itembill_r13 = ctx.$implicit;\n    const i_r11 = ctx.index;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction2(35, _c2, ctx_r2.getColorinv(itembill_r13.invoicedate, itembill_r13.invoiceamount), ctx_r2.getColorminBG(itembill_r13.typeCK)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i_r11 + 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(38, _c3, ctx_r2.getColorinv(itembill_r13.invoicedate, itembill_r13.invoiceamount)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(itembill_r13.salesid);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(40, _c3, ctx_r2.getColorinv(itembill_r13.invoicedate, itembill_r13.invoiceamount)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(itembill_r13.invoiceid);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(42, _c3, ctx_r2.getColorinv(itembill_r13.invoicedate, itembill_r13.invoiceamount)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(9, 20, itembill_r13.invoicedate, \"dd/MM/yyyy\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(44, _c3, ctx_r2.getColorinv(itembill_r13.invoicedate, itembill_r13.invoiceamount)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(12, 23, itembill_r13.duedate, \"dd/MM/yyyy\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(46, _c3, ctx_r2.getColorinv(itembill_r13.invoicedate, itembill_r13.invoiceamount)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(15, 26, itembill_r13.salesbalance, \"1.2-2\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(48, _c3, ctx_r2.getColorinv(itembill_r13.invoicedate, itembill_r13.invoiceamount)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(18, 29, itembill_r13.sumtax, \"1.2-2\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(50, _c3, ctx_r2.getColorinv(itembill_r13.invoicedate, itembill_r13.invoiceamount)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(21, 32, itembill_r13.invoiceamount, \"1.2-2\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"checked\", itembill_r13.check);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.closeED);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.closeED);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.closeED);\n  }\n}\nfunction InvoicecreditlistComponent_div_125_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 44)(1, \"div\", 92)(2, \"select\", 93);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function InvoicecreditlistComponent_div_125_Template_select_ngModelChange_2_listener($event) {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.chDraft, $event) || (ctx_r2.chDraft = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementStart(3, \"option\", 94);\n    i0.ɵɵtext(4, \"\\u0E40\\u0E25\\u0E37\\u0E2D\\u0E01\\u0E1B\\u0E23\\u0E30\\u0E40\\u0E20\\u0E17\\u0E2A\\u0E16\\u0E32\\u0E19\\u0E30\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"option\", 95);\n    i0.ɵɵtext(6, \"\\u0E23\\u0E32\\u0E22\\u0E01\\u0E32\\u0E23\\u0E40\\u0E2A\\u0E23\\u0E47\\u0E08\\u0E2A\\u0E34\\u0E49\\u0E19\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"option\", 74);\n    i0.ɵɵtext(8, \"Draft \\u0E23\\u0E32\\u0E22\\u0E01\\u0E32\\u0E23\\u0E19\\u0E35\\u0E49\\u0E44\\u0E27\\u0E49\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.chDraft);\n  }\n}\nfunction InvoicecreditlistComponent_ng_template_182_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"b\");\n    i0.ɵɵtext(1, \"Images Billno\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction InvoicecreditlistComponent_ng_template_183_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 96);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵpropertyInterpolate(\"src\", ctx_r2.ImageBillno, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction InvoicecreditlistComponent_ng_template_187_ng_template_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"b\");\n    i0.ɵɵtext(1, \"Images Billno\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction InvoicecreditlistComponent_ng_template_187_ng_template_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 96);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵpropertyInterpolate(\"src\", ctx_r2.ImageBillno, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction InvoicecreditlistComponent_ng_template_187_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 68);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementStart(2, \"div\", 97)(3, \"ngb-tabset\")(4, \"ngb-tab\");\n    i0.ɵɵtemplate(5, InvoicecreditlistComponent_ng_template_187_ng_template_5_Template, 2, 0, \"ng-template\", 69)(6, InvoicecreditlistComponent_ng_template_187_ng_template_6_Template, 1, 1, \"ng-template\", 98);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(7, \"div\", 71)(8, \"button\", 65);\n    i0.ɵɵlistener(\"click\", function InvoicecreditlistComponent_ng_template_187_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.modalRefshow.hide());\n    });\n    i0.ɵɵtext(9, \" \\u0E1B\\u0E34\\u0E14\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"\", ctx_r2.altBill, \" \");\n  }\n}\nexport let InvoicecreditlistComponent = /*#__PURE__*/(() => {\n  class InvoicecreditlistComponent {\n    constructor(modalService, router, ng2ImgMax, http, service, calendar, route) {\n      this.modalService = modalService;\n      this.router = router;\n      this.ng2ImgMax = ng2ImgMax;\n      this.http = http;\n      this.service = service;\n      this.calendar = calendar;\n      this.route = route;\n      this.config = {\n        ignoreBackdropClick: true,\n        class: 'modal-md'\n      };\n      this.configview = {\n        ignoreBackdropClick: true,\n        class: 'modal-lg '\n      };\n      this.load = \" สถานะ :  Upload : 0 %\";\n      this.max = 2000;\n      this.showbillno = '';\n      this.idinvi = [];\n      this.allid = [];\n      this.billnoname = [];\n      this.fromdate = '';\n      this.todate = '';\n      this.salegroup = '';\n      this.billno = '';\n      this.setInvoice = '';\n      this.selectedFile = null;\n      this.imgInvoice = '';\n      this.textload = '';\n      this.imageUrl = \"assets/img/default-image.png\";\n      this.billnoINV = [{\n        id: '',\n        billnoINVnum: ''\n      }];\n      this.checkedall = false;\n      this.mdlSampleIsOpen = false;\n      this.alt = '';\n      this.checkreload = true;\n      this.mdlSampleIsOpen2 = false;\n      this.alt2 = '';\n      this.checkreload2 = true;\n      this.mdlSampleIsOpensuccess = false;\n      this.altsuccess = '';\n      this.checkreloadsuccess = true;\n      this.total = 0;\n      this.selectAll2 = false;\n      this.productprice = 0;\n      this.sumvat = 0;\n      this.sumprice = 0;\n      this.trueproductprice = 0;\n      this.truesumvat = 0;\n      this.truesumprice = 0;\n      this.dateshipping = '';\n      this.dateshippingto = '';\n      this.datalogin = [];\n      this.testclose = false;\n      this.toDateimg = new Date();\n      this.DataED = '';\n      this.closeNoED = false;\n      this.closeED = false;\n      this.mdlSampleIsOpenIMG = false;\n      this.altimg = '';\n      this.ImageBillno = '';\n      this.urlimg = '';\n      this.customers = [];\n      this.chDraft = \"0\";\n      this.listInv = '';\n      /////\n      this.search = text$ =>\n      //Autocomplete ลูกค้า\n      text$.pipe(debounceTime(200), map(term => term === '' ? [] : this.customers.filter(v => v.name.toLowerCase().indexOf(term.toLowerCase()) > -1 || v.accountnum.toLowerCase().indexOf(term.toLowerCase()) > -1).slice(0, 50)));\n      this.formatter = x => x.name + ' (' + x.accountnum + ')';\n      localStorage.removeItem('DataSOderreview');\n      localStorage.removeItem('DataSOderlist');\n      this.url = service.geturlservice();\n      this.urlimg = service.geturlserviceIMG();\n      this.fromDate = calendar.getPrev(calendar.getToday(), 'd', 365);\n      this.toDate = calendar.getToday();\n      this.datalogin = JSON.parse(sessionStorage.getItem('login'));\n      if (this.datalogin == null) {\n        this.router.navigate(['login']);\n      }\n      // this.groupsale=localStorage.getItem('salegroup');\n      this.getuser();\n      this.getgroupsaleman();\n      // this.Datatodate= new Date(this.toDate.year,this.toDate.month-1,this.toDate.day);\n      console.log(sessionStorage.getItem('cecreditDateTo'));\n      if (sessionStorage.getItem('cecreditDateTo') != null && sessionStorage.getItem('cecreditDateFrom') != null) {\n        this.Datatodate = new Date(sessionStorage.getItem('cecreditDateTo'));\n        this.Datafromdate = new Date(sessionStorage.getItem('cecreditDateFrom'));\n      } else {\n        this.fromDate = calendar.getPrev(calendar.getToday(), 'd', 365);\n        this.toDate = calendar.getToday();\n        this.Datatodate = new Date(this.toDate.year, this.toDate.month - 1, this.toDate.day);\n        this.Datafromdate = new Date(this.toDate.year, this.toDate.month - 1, 1);\n      }\n      //  this.Datafromdate= new Date(this.fromDate.year, this.fromDate.month-1, 1);\n      // this.Datafromdate= new Date(this.toDate.year, this.toDate.month-1, 1);\n      if (this.route.snapshot.queryParams.todate == undefined) {\n        this.getbackto();\n      } else {\n        this.getdatetotoback();\n      }\n      if (this.route.snapshot.queryParams.todate == undefined) {\n        this.getbackto();\n      } else {\n        this.getdatetotoback();\n      }\n      if (this.route.snapshot.queryParams.fromdate == undefined) {\n        this.getbackfromdate();\n      } else {\n        this.getdatefromdatetoback();\n      }\n      if (this.route.snapshot.queryParams.pacodesale == undefined) {} else {\n        this.salegroup = this.route.snapshot.queryParams.pacodesale;\n      }\n      if (this.route.snapshot.queryParams.patxtcustomer == undefined) {\n        this.txtcustomer = 'ค้นหาลูกค้า';\n      } else {\n        if (this.route.snapshot.queryParams.patxtcustomer == '%20') {\n          this.txtcustomer = 'ค้นหาลูกค้า';\n        } else {\n          this.txtcustomer = this.route.snapshot.queryParams.patxtcustomer;\n          this.paCustomer = this.route.snapshot.queryParams.pacustomer;\n        }\n      }\n      if (this.route.snapshot.queryParams.pabill == undefined) {} else {\n        this.billno = this.route.snapshot.queryParams.pabill;\n      }\n      //fromdate=2018-2-1&todate=2019-2-1&pacodesale=1&pacustomer=%2520&pabill=&datatoback=bill\n      if (this.route.snapshot.queryParams.datatoback == undefined) {} else {\n        this.getDatatoback();\n      }\n    }\n    getDatatoback() {\n      this.route.snapshot.queryParams.datatoback;\n      this.invoicecredit = [];\n      this.sumprice = 0.00;\n      this.sumvat = 0.00;\n      this.productprice = 0.00;\n      this.DataED = '0';\n      this.sumprice = 0.00;\n      this.sumvat = 0.00;\n      this.productprice = 0.00;\n      this.Datatoback = this.route.snapshot.queryParams.datatoback;\n      this.http.get(this.route.snapshot.queryParams.datatoback).subscribe(res => {\n        if (res.length > 0) {\n          this.invoicecredit = res;\n          this.sumprice = 0.00;\n          this.sumvat = 0.00;\n          this.productprice = 0.00;\n          this.sum();\n          /*this.getdate();*/\n        } else {\n          this.sumprice = 0.00;\n          this.sumvat = 0.00;\n          this.productprice = 0.00;\n          alert('ไม่พบข้อมูล');\n        }\n        /*   this.getdate();*/\n      }, error => {\n        alert('เกิดปัญหาในการ Process ข้อมูล กรุณารายการใหม่อีกครั้ง');\n      });\n    }\n    getdatefromdatetoback() {\n      this.Datafromdate = new Date(this.route.snapshot.queryParams.fromdate);\n    }\n    getdatetotoback() {\n      this.Datatodate = new Date(this.route.snapshot.queryParams.todate);\n      this.Datafromdate = new Date(this.route.snapshot.queryParams.fromdate);\n    }\n    getbackto() {\n      this.todate = `${this.Datatodate.getFullYear()}-${this.Datatodate.getMonth() + 1}-${this.Datatodate.getDate()}`;\n    }\n    getbackfromdate() {\n      this.fromdate = `${this.Datafromdate.getFullYear()}-${this.Datafromdate.getMonth() + 1}-${this.Datafromdate.getDate()}`;\n    }\n    getColor(country, sum, summax, summin) {\n      if (summax == summin && sum < 0) {\n        return '#000';\n      }\n      var day = new Date(country);\n      const datetest = {\n        day: 1,\n        month: day.getMonth() + 1,\n        year: day.getFullYear()\n      };\n      let today = Number(new Date());\n      let day1 = this.calendar.getNext(datetest, 'm', 1);\n      let day2 = this.calendar.getPrev(day1, 'd', 1);\n      let dateNumer2 = Number(new Date(day2.year, day2.month - 1, day2.day));\n      let daynumbersum = today - dateNumer2;\n      // var invdate60 = (dateNumer+ + 5184000000 ) \n      //86400000 1day\n      //6048000000 70วัน\n      //7776000000 90วัน\n      /*alert(today);\n      //2592000000 30วัน\n      // 5184000000  60วัน\n      alert(invdate);     return 'green';*/\n      //alert(invdate  +'/'+ dateNum+'/'+invdate60)\n      if (daynumbersum > 7776000000) {\n        return 'red';\n      } else if (daynumbersum <= 7776000000 && daynumbersum >= 6048000000) {\n        return '#0e0dde';\n      } else {\n        return 'green';\n      }\n    }\n    getColorminBG(CK) {\n      if (CK == 1) {\n        return '#969595';\n      } else {\n        return '';\n      }\n    }\n    getColorinv(country, sum) {\n      if (sum < 0) {\n        return '#000';\n      }\n      var day = new Date(country);\n      const datetest = {\n        day: 1,\n        month: day.getMonth() + 1,\n        year: day.getFullYear()\n      };\n      let today = Number(new Date());\n      let day1 = this.calendar.getNext(datetest, 'm', 1);\n      let day2 = this.calendar.getPrev(day1, 'd', 1);\n      let dateNumer2 = Number(new Date(day2.year, day2.month - 1, day2.day));\n      let daynumbersum = today - dateNumer2;\n      // var invdate60 = (dateNumer+ + 5184000000 ) \n      //86400000 1day\n      /*alert(today);\n      //2592000000 30วัน\n      // 5184000000  60วัน\n      alert(invdate);     return 'green';*/\n      //alert(invdate  +'/'+ dateNum+'/'+invdate60)\n      if (daynumbersum > 7776000000) {\n        return 'red';\n      } else if (daynumbersum <= 7776000000 && daynumbersum >= 6048000000) {\n        return '#0e0dde';\n      } else {\n        return 'green';\n      }\n    }\n    //////\n    //ดึงรหัสลูกค้ามาใช้ใน Autocomplete\n    getcostomerauto() {\n      var idsale = this.datalogin[0].salegroup;\n      if (this.datalogin[0].id_group_user == '153Admin' || this.datalogin[0].id_group_user == '1860Administrator') {\n        idsale = '%20';\n      } else {\n        idsale = this.datalogin[0].salegroup;\n      }\n      this.http.get(this.url + 'customerauto/' + idsale).subscribe(res => {\n        this.customers = res;\n      });\n    }\n    getgroupsaleman() {\n      this.DateGroupsaleman = [];\n      this.http.get(this.url + 'salesman').subscribe(res => {\n        if (res.length > 0) {\n          this.DateGroupsaleman = res;\n          this.getcostomerauto();\n        } else {\n          alert('ไม่สามารถติดต่อข้อมูลรหัส saleman ได้');\n        }\n      });\n    }\n    getuser() {\n      if (this.datalogin[0].id_group_user == '153Admin' || this.datalogin[0].id_group_user == '1860Administrator') {\n        this.salegroup = '';\n        this.testclose = true;\n      } else {\n        this.testclose = false;\n        this.salegroup = this.datalogin[0].salegroup;\n      }\n    }\n    ngOnInit() {}\n    getRandomInt(max) {\n      return Math.floor(Math.random() * Math.floor(max));\n    }\n    openModal(open, text, load) {\n      this.mdlSampleIsOpen = open;\n      this.alt = text;\n      this.checkreload = load;\n    }\n    openModal2(open, text, load) {\n      this.mdlSampleIsOpen2 = open;\n      this.alt2 = text;\n      this.checkreload2 = load;\n    }\n    closemodel(cl) {\n      this.mdlSampleIsOpen2 = cl;\n      if (this.checkreload2 == false) {}\n    }\n    openModalsuccess(open, text, load) {\n      this.mdlSampleIsOpensuccess = open;\n      this.altsuccess = text;\n      this.checkreloadsuccess = load;\n    }\n    closemodelsuccess(cl) {\n      this.mdlSampleIsOpensuccess = cl;\n      if (this.checkreloadsuccess == false) {\n        if (this.closeED == true) {\n          this.SearchinvoicecreditED();\n        } else {\n          this.Searchinvoicecredit();\n        }\n      }\n    }\n    selectAll(checked) {\n      this.truesumprice = 0.00;\n      this.truesumvat = 0.00;\n      this.trueproductprice = 0.00;\n      for (var i = 0; i < this.idinvi.length; i++) {\n        this.idinvi[i].check = checked;\n      }\n      this.Getbillbo();\n      this.total = this.allid.length;\n      this.sumTrue();\n    }\n    checkIfAllSelected(checked, index) {\n      this.idinvi[index].check = checked;\n      this.total = 0;\n      this.Getbillbo();\n      this.total = this.allid.length;\n      this.getcaheck();\n      this.sumTrue();\n    }\n    sum() {\n      if (this.invoicecredit.length > 0) {\n        for (var i = 0; i < this.invoicecredit.length; i++) {\n          this.productprice += this.invoicecredit[i].salesbalance;\n          this.sumvat += this.invoicecredit[i].sumtax;\n          this.sumprice += this.invoicecredit[i].invoiceamount;\n        }\n      }\n    }\n    sumTrue() {\n      this.truesumprice = 0.00;\n      this.truesumvat = 0.00;\n      this.trueproductprice = 0.00;\n      for (var i = 0; i < this.idinvi.length; i++) {\n        if (this.idinvi[i].check == true) {\n          this.trueproductprice += this.idinvi[i].salesbalance;\n          this.truesumvat += this.idinvi[i].sumtax;\n          this.truesumprice += this.idinvi[i].invoiceamount;\n        }\n      }\n    }\n    getdate() {\n      this.fromdate = `${this.Datafromdate.getFullYear()}-${this.Datafromdate.getMonth() + 1}-${this.Datafromdate.getDate()}`;\n      this.todate = `${this.Datatodate.getFullYear()}-${this.Datatodate.getMonth() + 1}-${this.Datatodate.getDate()}`;\n    }\n    getcaheck() {\n      var ch = 0;\n      for (var i = 0; i < this.idinvi.length; i++) {\n        if (this.idinvi[i].check == true) {\n          ch++;\n        } else {\n          ch--;\n        }\n      }\n      if (this.idinvi.length == ch) {\n        this.testcheck = true;\n      } else {\n        this.testcheck = false;\n      }\n    }\n    GetBillNo(item) {\n      this.Filelist = undefined;\n      this.chDraft = '0';\n      this.getdate();\n      this.selectedFile = '';\n      this.testcheck = false;\n      const billno = item.billno;\n      const idcus = item.orderaccount;\n      if (this.fromdate == '') {\n        this.fromdate = `${this.fromDate}`;\n      }\n      if (this.todate == '') {\n        this.todate = `${this.toDate}`;\n      }\n      this.textload = '';\n      this.total = 0;\n      this.truesumprice = 0.00;\n      this.truesumvat = 0.00;\n      this.trueproductprice = 0.00;\n      this.imageUrl = \"assets/img/default-image.png\";\n      this.idinvi = [];\n      this.allid = [];\n      this.http.get(this.url + 'get_billno/' + billno + '/' + idcus + '/' + this.fromdate + '/' + this.todate + '/' + this.DataED).subscribe(res => {\n        if (res.length > 0) {\n          this.billnoDataSearch = res;\n          for (var i = 0; i < res.length; i++) {\n            this.idinvi.push({\n              id: res[i].id,\n              salesid: res[i].salesid,\n              invoiceid: res[i].invoiceid,\n              invoicedate: res[i].invoicedate,\n              duedate: res[i].duedate,\n              salesbalance: res[i].salesbalance,\n              sumtax: res[i].sumtax,\n              invoiceamount: res[i].invoiceamount,\n              billno: res[i].billno,\n              imgurl: res[i].attachedfile,\n              check: false,\n              typeCK: res[i].typeCK\n            });\n          }\n          this.showbillno = this.idinvi[0].billno;\n        } else {\n          alert('ไม่พบข้อมูลที่ค้นหา');\n          this.idinvi = [];\n        }\n      });\n    }\n    handleFileInput(file) {\n      this.load = '';\n      this.selectedFile = '';\n      if (file.item(0).type == \"image/jpeg\") {\n        //this.openModal(true,'กำลังปรับขนาดไฟล์',false)&& file.item(0).size <= (1024*1024*5)\n        let image = file.item(0);\n        this.selectedFile = image;\n        this.textload = this.selectedFile.name;\n        var reader = new FileReader();\n        reader.onload = event => {\n          this.imageUrl = event.target.result;\n        };\n        reader.readAsDataURL(this.selectedFile);\n        /* this.ng2ImgMax.resizeImage(image, 1024, 768).subscribe(\n           result => {\n             this.selectedFile = image;\n             this.textload= this.selectedFile.name;\n             //Show image preview\n             var reader = new FileReader();\n             reader.onload = (event:any) => {\n               this.imageUrl = event.target.result;\n             }\n             reader.readAsDataURL(this.selectedFile);\n             this.openModal(false,'',false)\n           },\n           error => {\n             this.openModal2(true,error,false)\n           }/\n         );*\n                     \n           /*  this.selectedFile = file.item(0);*/\n      } else {\n        this.load = '';\n        this.openModal2(true, 'ชนิดไฟล์รูปภาพไม่ถูกต้อง กรุณาเลือกชนิดไฟล์เป็น .jpeg ', false);\n        this.imageUrl = \"assets/img/default-image.png\";\n      }\n      /*  this.selectedFile = file.item(0);\n        this.textload= this.selectedFile.name;\n        //Show image preview\n        var reader = new FileReader();\n        reader.onload = (event:any) => {\n          this.imageUrl = event.target.result;\n        }\n        reader.readAsDataURL(this.selectedFile);*/\n    }\n    /* if (file.item(0).type ==\"image/jpeg\" && file.item(0).size <= (1024*1024*5) ) {\n       // this.openModal2(true,'กำลังปรับขนาดไฟล์',false)\n        let image = file.item(0);\n        this.selectedFile = image;\n        this.textload= this.selectedFile.name;\n        var reader = new FileReader();\n            reader.onload = (event:any) => {\n              this.imageUrl = event.target.result;\n            }\n            reader.readAsDataURL(this.selectedFile);\n       \n      } else {\n        this.load='';\n        alert('ชนิดไฟล์รูปภาพไม่ถูกต้อง กรุณาเลือกชนิดไฟล์เป็น .jpeg และ ไฟล์ขนาดไม่เกิน 5120 kb');\n        //this.openModal(true,'ชนิดไฟล์รูปภาพไม่ถูกต้อง กรุณาเลือกชนิดไฟล์เป็น .jpeg และ ไฟล์ขนาดไม่เกิน 5120 kb',false)\n          }\n         this.imageUrl =  \"assets/img/default-image.png\";\n         this.textload= \"\";\n         return false;\n        }*/\n    SearchinvoicecreditED() {\n      this.txtcustomer = 'ค้นหาลูกค้า';\n      this.closeED = true;\n      this.DataED = '1';\n      this.getdate();\n      this.invoicecredit = [];\n      this.sumprice = 0.00;\n      this.sumvat = 0.00;\n      this.productprice = 0.00;\n      this.load = '';\n      var datacodeSo = '';\n      var dataCustomer = '';\n      if (this.billno == '' && this.Customer == undefined && this.salegroup == '') {\n        this.openModal2(true, 'กรุณาป้อนข้อมูลอย่างน้อย 1 ช่อง ถ้าไม่ระบุวันที่ วันที่จะย้อนหลังเป็นเวลา 365 วัน', false);\n      } else {\n        if (this.fromdate == '') {\n          this.fromdate = `${this.fromdate}`;\n          sessionStorage.setItem('cecreditDateFrom', this.fromdate);\n        } else {\n          sessionStorage.setItem('cecreditDateFrom', this.fromdate);\n        }\n        if (this.todate == '') {\n          this.todate = `${this.toDate}`;\n          sessionStorage.setItem('cecreditDateTo', this.todate);\n        } else {\n          sessionStorage.setItem('cecreditDateTo', this.todate);\n        }\n        if (this.billno == '') {\n          this.billno = '%20';\n        }\n        if (this.Customer == undefined) {\n          dataCustomer = '%20';\n        } else {\n          dataCustomer = `${this.Customer.accountnum}`;\n        }\n        //  alert(this.salegroup +'//'+this.datalogin[0].salegroup)\n        if (this.salegroup == '') {\n          if (this.datalogin[0].id_group_user == '153Admin' || this.datalogin[0].id_group_user == '1860Administrator') {\n            datacodeSo = `${this.salegroup}`;\n          } else {\n            datacodeSo = `${this.datalogin[0].salegroup}`;\n          }\n        }\n        if (this.salegroup !== '') {\n          if (this.datalogin[0].id_group_user == '153Admin' || this.datalogin[0].id_group_user == '1860Administrator') {\n            datacodeSo = `${this.salegroup}`;\n          } else {\n            datacodeSo = `${this.datalogin[0].salegroup}`;\n          }\n        }\n        if (datacodeSo == '') {\n          datacodeSo = '%20';\n        }\n        if (dataCustomer == 'undefined') {\n          dataCustomer = '%20';\n        }\n        /* alert(this.url + 'credit_invoice/'+ this.fromdate + '/' + this.todate +'/' + this.salegroup + '/' + this.billno + '/' + this.customer);*/\n        this.http.get(this.url + 'credit_invoice/' + this.fromdate + '/' + this.todate + '/' + datacodeSo + '/' + this.billno + '/' + dataCustomer + '/' + this.DataED).subscribe(res => {\n          if (res.length > 0) {\n            this.invoicecredit = res;\n            this.sumprice = 0.00;\n            this.sumvat = 0.00;\n            this.productprice = 0.00;\n            this.sum();\n            if (this.datalogin[0].id_group_user == '153Admin' || this.datalogin[0].id_group_user == '1860Administrator') {\n              /* this.salegroup='';*/\n            }\n            if (datacodeSo == '%20') {\n              datacodeSo = '';\n              this.salegroup = '';\n            }\n            if (dataCustomer == '%20') {\n              dataCustomer = '';\n            }\n            if (this.billno == '%20') {\n              this.billno = '';\n            }\n          } else {\n            if (this.datalogin[0].id_group_user == '153Admin' || this.datalogin[0].id_group_user == '1860Administrator') {\n              /* this.salegroup='';*/\n            }\n            if (datacodeSo == '%20') {\n              datacodeSo = '';\n              this.salegroup = '';\n            }\n            if (dataCustomer == '%20') {\n              dataCustomer = '';\n            }\n            if (this.billno == '%20') {\n              this.billno = '';\n            }\n            this.openModal2(true, 'ไม่พบข้อมูล', false);\n          }\n        }, error => {\n          this.openModal2(true, 'เกิดปัญหาในการ Process ข้อมูล กรุณารายการใหม่อีกครั้ง', false);\n        });\n      }\n    }\n    Searchinvoicecredit() {\n      this.txtcustomer = 'ค้นหาลูกค้า';\n      this.closeED = false;\n      this.DataED = '0';\n      this.getdate();\n      this.invoicecredit = [];\n      this.sumprice = 0.00;\n      this.sumvat = 0.00;\n      this.productprice = 0.00;\n      this.load = '';\n      var datacodeSo = '';\n      var dataCustomer = '';\n      if (this.billno == '' && this.Customer == undefined && this.salegroup == '') {\n        this.openModal2(true, 'กรุณาป้อนข้อมูลอย่างน้อย 1 ช่อง ถ้าไม่ระบุวันที่ วันที่จะย้อนหลังเป็นเวลา 365 วัน', false);\n      } else {\n        if (this.fromdate == '') {\n          this.fromdate = `${this.fromdate}`;\n          sessionStorage.setItem('cecreditDateFrom', this.fromdate);\n        } else {\n          sessionStorage.setItem('cecreditDateFrom', this.fromdate);\n        }\n        if (this.todate == '') {\n          this.todate = `${this.toDate}`;\n          sessionStorage.setItem('cecreditDateTo', this.todate);\n        } else {\n          sessionStorage.setItem('cecreditDateTo', this.todate);\n        }\n        if (this.billno == '') {\n          this.billno = '%20';\n        }\n        if (this.Customer == undefined) {\n          if (this.route.snapshot.queryParams.pacustomer == undefined) {\n            dataCustomer = '%20';\n            this.paCustomer = '%20';\n            this.patxtcustomer = '%20';\n          } else {\n            dataCustomer = this.route.snapshot.queryParams.pacustomer;\n            this.paCustomer = this.route.snapshot.queryParams.pacustomer;\n          }\n        } else {\n          dataCustomer = `${this.Customer.accountnum}`;\n          this.paCustomer = `${this.Customer.accountnum}`;\n          this.patxtcustomer = `${this.Customer.name}`;\n        }\n        if (this.salegroup == '') {\n          if (this.datalogin[0].id_group_user == '153Admin' || this.datalogin[0].id_group_user == '1860Administrator') {\n            datacodeSo = `${this.salegroup}`;\n            this.paCodeSale = `${this.salegroup}`;\n          } else {\n            datacodeSo = `${this.datalogin[0].salegroup}`;\n            this.paCodeSale = `${this.datalogin[0].salegroup}`;\n          }\n        }\n        if (this.salegroup !== '') {\n          if (this.datalogin[0].id_group_user == '153Admin' || this.datalogin[0].id_group_user == '1860Administrator') {\n            datacodeSo = `${this.salegroup}`;\n            this.paCodeSale = `${this.salegroup}`;\n          } else {\n            datacodeSo = `${this.datalogin[0].salegroup}`;\n            this.paCodeSale = `${this.datalogin[0].salegroup}`;\n          }\n        }\n        if (this.paCodeSale == '') {\n          this.paCodeSale = '%20';\n        }\n        if (this.paCustomer == 'undefined') {\n          this.paCustomer = '%20';\n        }\n        /* alert(this.url + 'credit_invoice/'+ this.fromdate + '/' + this.todate +'/' + this.salegroup + '/' + this.billno + '/' + this.customer);*/\n        this.Datatoback = this.url + 'credit_invoice/' + this.fromdate + '/' + this.todate + '/' + this.paCodeSale + '/' + this.billno + '/' + this.paCustomer + '/' + this.DataED;\n        this.http.get(this.url + 'credit_invoice/' + this.fromdate + '/' + this.todate + '/' + this.paCodeSale + '/' + this.billno + '/' + this.paCustomer + '/' + this.DataED).subscribe(res => {\n          // alert(JSON.stringify(this.invoicecredit))\n          if (res.length > 0) {\n            this.invoicecredit = res;\n            this.sumprice = 0.00;\n            this.sumvat = 0.00;\n            this.productprice = 0.00;\n            this.sum();\n            if (this.datalogin[0].id_group_user == '153Admin' || this.datalogin[0].id_group_user == '1860Administrator') {\n              /* this.salegroup='';*/\n            }\n            if (this.paCodeSale == '%20') {\n              this.paCodeSale = '';\n              this.salegroup = '';\n            }\n            dataCustomer = '';\n            if (this.billno == '%20') {\n              this.billno = '';\n            }\n          } else {\n            if (this.datalogin[0].id_group_user == '153Admin' || this.datalogin[0].id_group_user == '1860Administrator') {\n              /* this.salegroup='';*/\n            }\n            if (this.paCodeSale == '%20') {\n              this.paCodeSale = '';\n              this.salegroup = '';\n            }\n            if (this.paCustomer == '%20') {\n              this.paCustomer = '';\n            }\n            if (this.billno == '%20') {\n              this.billno = '';\n            }\n            this.openModal2(true, 'ไม่พบข้อมูล', false);\n          }\n        }, error => {\n          this.openModal2(true, 'เกิดปัญหาในการ Process ข้อมูล กรุณารายการใหม่อีกครั้ง', false);\n        });\n      }\n    }\n    clear() {\n      this.imageUrl = \"assets/img/default-image.png\";\n      this.textload = \"\";\n      this.selectedFile = \"\";\n      this.setInvoice = \"\";\n    }\n    newsaveinvoice(billno, typeCK) {\n      //ฟังชั่นใหม่\n      // alert(JSON.stringify(this.allid))\n      this.http.post(this.url + 'update_invoiceNEW', {\n        //  Data: this.allid,\n        Data: this.listInv,\n        nameimg: billno,\n        CK: typeCK\n      }).subscribe(res => {\n        if (res == true) {\n          this.openModal(false, '', false);\n          this.openModal2(false, '', false);\n          this.load = '';\n          this.invoicecredit = [];\n          this.openModalsuccess(true, 'บันทึกข้อมูลเสร็จสิ้น', false);\n          // this.load ='รายการที่ : '+ this.allid.length.toString() + '-' + res.toString();\n          //  alert('โอเคครับ')\n          // console.log(this.allid.length + '--------' + res.toString())\n        }\n      }, error => {\n        //เกิดปัญหาในการ Process ข้อมูล\n        this.openModalsuccess(true, 'เกิดปัญหาในการ Process ข้อมูล', false);\n      });\n    }\n    saveinvoice(billno) {\n      //ฟังชั่นเก่า\n      if (this.allid.length != 0) {\n        this.http.post(this.url + 'update_invoice/' + this.allid[0].id + '/' + billno, '').subscribe(res => {\n          if (res == true) {\n            this.deletelallid(0);\n            this.load = 'รายการที่ : ' + this.allid.length.toString() + '-' + res.toString();\n            console.log(this.allid.length + '--------' + res.toString());\n          }\n        }, error => {\n          this.openModal2(true, 'เกิดปัญหาในการ Process ข้อมูล', false);\n        });\n      } else {\n        if (this.allid.length == 0) {\n          clearInterval(this.setInterval);\n          this.openModal(false, '', false);\n          this.load = '';\n          this.invoicecredit = [];\n          this.openModalsuccess(true, 'บันทึกข้อมูลเสร็จสิ้น', false);\n        }\n      }\n    }\n    Getbillbo() {\n      this.allid = [];\n      this.listInv = '';\n      for (var i = 0; i < this.idinvi.length; i++) {\n        if (this.idinvi[i].check == true) {\n          this.allid.push({\n            id: this.idinvi[i].invoiceid,\n            billno: this.idinvi[i].billno,\n            Productprice: this.idinvi[i].salesbalance,\n            Sumvat: this.idinvi[i].sumtax,\n            Sumprice: this.idinvi[i].invoiceamount\n          });\n          this.listInv = this.listInv + ',' + this.idinvi[i].invoiceid;\n        }\n      }\n    }\n    deletelallid(value) {\n      this.allid.splice(value, 1);\n    }\n    onUpload(typeCK, total) {\n      var datatypeCK = 0;\n      if (typeCK == \"1\") {\n        datatypeCK = 1;\n      } else if (typeCK == \"0\") {\n        datatypeCK = 2;\n      } else {\n        alert(\"เลือกประเภทสถานะก่อนทำรายการ\");\n        return false;\n      }\n      if (total < 1) {\n        alert(\"ทำรายการไม่ถูกต้อง กรุณาทำรายการใหม่\");\n        return false;\n      }\n      var billname = '';\n      this.Getbillbo();\n      var Random = this.getRandomInt(this.max);\n      billname = `${this.allid[0].billno}-${this.toDate}-${Random}.jpg`;\n      this.http.get(this.url + 'get_billnoname/' + billname).subscribe(res => {\n        if (res.length < 1) {\n          if (this.allid.length > 0) {\n            /* const fd = new FormData();\n                 var urlimg =`${this.url}/${billname}/bill/upload`;\n                   fd.append('image', this.selectedFile, this.selectedFile.name)\n                   this.http.post<any>(urlimg,fd).subscribe(res => {\n                          if (res.length ==undefined ) {\n                           alert('3 = '+JSON.stringify(res.success));\n                             alert(billname);*/\n            this.openModal(true, 'กำลังบันทึกข้อมูล', false);\n            this.load = \"สถานะ : Upload : 0 %\";\n            this.imgupload(billname, datatypeCK);\n            /*   }else{\n                   alert('Failed to save.');\n                 }\n                \n                 });  */\n          } else {\n            this.openModal(true, 'กรุณาเลือก InvoiceID ก่อน', false);\n            this.idinvi = [];\n            this.allid = [];\n          }\n        } else {\n          this.openModal(true, 'กรุณาทำรายการใหม่อีกครั้ง', false);\n          this.invoicecredit = [];\n        }\n      }, error => {\n        this.openModal2(true, 'เกิดปัญหาในการ Process ข้อมูล', false);\n      });\n    }\n    imgupload(billname, datatypeCK) {\n      this.sumprice = 0.00;\n      this.sumvat = 0.00;\n      this.productprice = 0.00;\n      if (this.selectedFile == '') {\n        billname = \"\";\n        this.newsaveinvoice(billname, datatypeCK);\n      } else {\n        const fd = new FormData();\n        var urlimg = `${this.url}${billname}/bill/upload`;\n        fd.append('image', this.selectedFile, this.selectedFile.name);\n        this.http.post(urlimg, fd, {\n          reportProgress: true,\n          observe: 'events'\n        }).subscribe(event => {\n          if (event.type === HttpEventType.UploadProgress) {\n            console.log('Upload' + Math.round(event.loaded / event.total * 100) + ' %');\n            this.load = 'สถานะ : Upload : ' + Math.round(event.loaded / event.total * 100) + ' %';\n          } else if (event.type === HttpEventType.Response) {\n            console.log(event);\n            if (event.body.success === true) {\n              //this.setInterval=setInterval(() => this.saveinvoice(billname), 400);     \n              this.newsaveinvoice(billname, datatypeCK);\n            } else {\n              this.openModal(false, '', false);\n              this.openModal2(true, 'ไม่สามารถบันทึกข้อมูลได้', false);\n            }\n          }\n        });\n      }\n    }\n    openModalIMG3(open, text, load, texturl) {\n      this.mdlSampleIsOpenIMG = open;\n      this.altimg = text;\n      // this.setImgBill(texturl)\n    }\n    closemodelIMG(cl) {\n      this.mdlSampleIsOpenIMG = cl;\n      if (this.checkreload == false) {}\n    }\n    openModalIMG(template, texturl, CK) {\n      // this.altimg=text;\n      this.setImgBill(texturl, CK);\n      this.modalRefshow = this.modalService.show(template, {\n        class: 'modal-lg'\n      });\n    }\n    setImgBill(nameimage, CK) {\n      // if (CK == '1') {\n      //   nameimage = this.imageUrl;\n      //   this.ImageBillno = nameimage;\n      // } else {\n      this.ImageBillno = this.urlimg + nameimage;\n      // }\n    }\n    openpdf(valueid) {\n      var page = 'bill';\n      //this.router.navigate(['/PDFprint', { queryParams: { idINV: valueid }}]);this.fromdate + '/' + this.todate\n      this.router.navigate(['/PDFprint'], {\n        queryParams: {\n          idINV: valueid,\n          fromdate: this.fromdate,\n          todate: this.todate,\n          pacodesale: this.paCodeSale,\n          pacustomer: this.paCustomer,\n          pabill: this.billno,\n          Datatoback: this.Datatoback,\n          page: page,\n          patxtcustomer: this.patxtcustomer\n        }\n      });\n      /* this.router.navigate(['PDFprint']);*/\n    }\n    cancel() {\n      this.Customer = undefined;\n    }\n    static {\n      this.ɵfac = function InvoicecreditlistComponent_Factory(t) {\n        return new (t || InvoicecreditlistComponent)(i0.ɵɵdirectiveInject(i1.BsModalService), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.Ng2ImgMaxService), i0.ɵɵdirectiveInject(i4.HttpClient), i0.ɵɵdirectiveInject(i5.WebapiService), i0.ɵɵdirectiveInject(i6.NgbCalendar), i0.ɵɵdirectiveInject(i2.ActivatedRoute));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: InvoicecreditlistComponent,\n        selectors: [[\"app-invoicecreditlist\"]],\n        decls: 189,\n        vars: 67,\n        consts: [[\"rt\", \"\"], [\"Image\", \"\"], [\"imageForm\", \"ngForm\"], [\"Modalview\", \"\"], [2, \"padding-top\", \"60px\"], [1, \"container-fluid\", 2, \"padding-right\", \"5px\", \"padding-left\", \"5px\"], [1, \"p-sm-1\", \"bg-secondary\", \"text-white\", \"text-center\"], [1, \"form-row\"], [\"class\", \"col-md-2 mb-2\", 4, \"ngIf\"], [1, \"col-md-2\", \"mb-2\"], [2, \"position\", \"relative\"], [\"id\", \"typeahead-template\", \"type\", \"text\", \"name\", \"model\", 1, \"form-control\", \"form-control-sm\", 3, \"ngModelChange\", \"placeholder\", \"ngModel\", \"ngbTypeahead\", \"resultTemplate\", \"inputFormatter\"], [2, \"position\", \"absolute\", \"z-index\", \"10\", \"width\", \"15%\", \"height\", \"100%\", \"top\", \"1px\", \"right\", \"1px\", \"font-size\", \"18px\", \"cursor\", \"pointer\", \"text-align\", \"center\", 3, \"click\"], [\"id\", \"billno\", \"type\", \"text\", \"name\", \"billno\", \"placeholder\", \"\\u0E40\\u0E25\\u0E02\\u0E17\\u0E35\\u0E48\\u0E1A\\u0E34\\u0E25\", 1, \"form-control\", \"form-control-sm\", 3, \"ngModelChange\", \"ngModel\"], [1, \"col-xs-12\", \"col-12\", \"col-md-2\", \"form-group\"], [\"type\", \"text\", \"placeholder\", \"DD/MM/YYYY\", \"bsDatepicker\", \"\", 1, \"form-control\", \"form-control-sm\", 3, \"ngModelChange\", \"bsConfig\", \"ngModel\"], [1, \"col-md-2\", \"mb-3\", \"col-12\", \"text-center\", \"text-sm-center\", \"text-md-center\", \"text-lg-left\", 2, \"padding-right\", \"0px\", \"padding-left\", \"0px\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", \"btn-sm\", \"font-weight-light\", 2, \"width\", \"55px\", 3, \"click\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", \"btn-sm\", \"font-weight-light\", 2, \"margin-left\", \"3px\", \"width\", \"68px\", 3, \"click\"], [1, \"table\", \"table-hover\", \"table-bordered\", \"table-sm\"], [1, \"text-sm-center\", \"bg-light\"], [\"scope\", \"col\", 1, \"text-sm-center\", \"font-weight-normal\", 2, \"text-align\", \"center\"], [\"scope\", \"col\", 1, \"text-sm-center\", \"font-weight-normal\"], [\"scope\", \"col\", 1, \"text-sm-center\", \"font-weight-normal\", 2, \"width\", \"35%\", \"text-align\", \"center\"], [\"scope\", \"col\", \"width\", \"60px\", 1, \"text-sm-center\"], [\"class\", \"text-sm-left\", 4, \"ngFor\", \"ngForOf\"], [1, \"text-sm-left\"], [1, \"text-sm-center\", \"font-weight-light\"], [1, \"text-sm-right\", \"font-weight-normal\", 2, \"text-align\", \"right\"], [\"id\", \"ModalupFile\", \"tabindex\", \"-1\", \"role\", \"dialog\", \"aria-labelledby\", \"exampleModalLabel\", \"aria-hidden\", \"true\", 1, \"modal\", \"fade\"], [\"role\", \"document\", 1, \"modal-dialog\", \"modal-lg\"], [1, \"modal-content\"], [1, \"modal-header\"], [\"id\", \"exampleModalLabel\", 1, \"modal-title\", 2, \"color\", \"red\"], [\"type\", \"button\", \"data-dismiss\", \"modal\", \"aria-label\", \"Close\", 1, \"close\", 3, \"click\"], [\"aria-hidden\", \"true\"], [1, \"modal-body\"], [2, \"overflow-y\", \"auto\"], [\"nowrap\", \"\", \"scope\", \"col\", 1, \"text-sm-center\", \"font-weight-normal\", 2, \"text-align\", \"center\"], [\"scope\", \"col\", \"width\", \"30px\", 1, \"text-sm-center\"], [\"type\", \"checkbox\", 3, \"ngModelChange\", \"click\", \"ngModel\"], [4, \"ngFor\", \"ngForOf\"], [\"colspan\", \"5\", 1, \"text-sm-right\", \"text-md-right\", \"text-right\", \"font-weight-normal\", 2, \"text-align\", \"right\"], [1, \"text-sm-center\", \"font-weight-normal\"], [1, \"form-group\", \"col-sm-6\", \"offset-sm-3\"], [1, \"custom-file\", 2, \"margin-bottom\", \"10px\"], [\"type\", \"file\", \"accept\", \"image/*\", \"id\", \"inputGroupFile01\", \"aria-describedby\", \"inputGroupFileAddon01\", 1, \"custom-file-input\", 3, \"ngModelChange\", \"change\", \"ngModel\"], [\"for\", \"inputGroupFile01\", 1, \"custom-file-label\", 3, \"ngModelChange\", \"ngModel\"], [\"class\", \"form-group col-sm-6 offset-sm-3\", 4, \"ngIf\"], [\"id\", \"collapseExample\", 1, \"collapse\"], [1, \"card\", \"card-body\"], [2, \"text-align\", \"center\"], [2, \"width\", \"70%\", 3, \"src\"], [1, \"modal-footer\"], [\"role\", \"alert\", 1, \"alert\", \"alert-success\", 2, \"width\", \"200px\", \"top\", \"7px\"], [\"type\", \"button\", \"data-dismiss\", \"modal\", 1, \"btn\", \"btn-secondary\", 3, \"click\"], [\"data-toggle\", \"collapse\", \"href\", \"#collapseExample\", \"role\", \"button\", \"aria-expanded\", \"false\", \"aria-controls\", \"collapseExample\", 1, \"btn\", \"btn-primary\", 3, \"disabled\"], [\"type\", \"button\", \"data-dismiss\", \"modal\", 1, \"btn\", \"btn-primary\", 3, \"click\", \"disabled\"], [\"id\", \"mdlSample\", \"role\", \"dialog\", 1, \"modal\", \"fade\", 3, \"ngStyle\"], [1, \"modal-dialog\", \"modal-md\"], [1, \"modal-header\", \"colhaederal\"], [1, \"modal-title\"], [\"align\", \"right\", 1, \"modal-footer\"], [\"type\", \"button\", \"id\", \"btnClose\", \"disabled\", \"\", 1, \"btn\", \"btn-danger\", 3, \"click\"], [1, \"fa\", \"fa-times\", \"fa-fw\"], [\"type\", \"button\", \"id\", \"btnClose\", 1, \"btn\", \"btn-danger\", 3, \"click\"], [\"role\", \"document\", 1, \"modal-dialog\", \"modal-lg\", 2, \"top\", \"50%\"], [1, \"modal-content\", 2, \"top\", \"30px\"], [1, \"modal-body\", 2, \"padding\", \"5px\"], [\"ngbTabTitle\", \"\", \"target\", \"_blank\"], [\"ngbTabContent\", \"\", 2, \"overflow-y\", \"auto\"], [\"align\", \"right\", 1, \"modal-footer\", 2, \"padding\", \"5px\"], [\"multiple\", \"\", 1, \"custom-select\", \"custom-select-sm\", 3, \"ngModelChange\", \"ngModel\"], [\"selected\", \"\", \"disabled\", \"\", \"value\", \"\"], [\"value\", \"1\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [3, \"value\"], [3, \"click\"], [3, \"mousedown\"], [1, \"text-sm-center\", \"font-weight-normal\", 2, \"text-align\", \"center\", 3, \"ngStyle\"], [1, \"text-sm-left\", \"font-weight-normal\", 2, \"text-align\", \"left\", 3, \"ngStyle\"], [1, \"text-sm-right\", \"font-weight-normal\", 2, \"text-align\", \"right\", 3, \"ngStyle\"], [1, \"text-center\"], [\"data-toggle\", \"modal\", \"data-target\", \"#ModalupFile\", \"aria-expanded\", \"true\", \"aria-controls\", \"collapseOne\", 1, \"btn\", \"btn\", \"btn-warning\", 2, \"padding\", \"0pt\", 3, \"click\"], [\"nowrap\", \"\", 1, \"text-sm-center\", \"font-weight-normal\", 2, \"text-align\", \"center\", 3, \"ngStyle\"], [\"type\", \"checkbox\", 3, \"click\", \"checked\"], [\"class\", \"btn btn-sm btn-warning\", \"style\", \"padding: 0px;\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"btn btn btn-warning\", \"style\", \"padding: 0px;\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [\"class\", \"text-center\", 4, \"ngIf\"], [1, \"btn\", \"btn-sm\", \"btn-warning\", 2, \"padding\", \"0px\", 3, \"click\"], [1, \"btn\", \"btn\", \"btn-warning\", 2, \"padding\", \"0px\", 3, \"click\", \"disabled\"], [\"data-dismiss\", \"modal\", 1, \"btn\", \"btn\", \"btn-warning\", 2, \"padding\", \"0px\", 3, \"click\"], [1, \"form-group\", \"col-sm-12\"], [1, \"custom-select\", \"custom-select-sm\", 3, \"ngModelChange\", \"ngModel\"], [\"value\", \"A\"], [\"value\", \"0\"], [2, \"margin\", \"5px\", \"width\", \"99%\", \"height\", \"99%\", 3, \"src\"], [1, \"card\", \"card-body\", 2, \"padding\", \"5px\"], [\"ngbTabContent\", \"\"]],\n        template: function InvoicecreditlistComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            const _r1 = i0.ɵɵgetCurrentView();\n            i0.ɵɵelement(0, \"app-topmenu\");\n            i0.ɵɵelementStart(1, \"section\", 4)(2, \"div\", 5)(3, \"h5\", 6);\n            i0.ɵɵtext(4, \"Credit Invoice List\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(5, \"div\", 7);\n            i0.ɵɵtemplate(6, InvoicecreditlistComponent_div_6_Template, 7, 2, \"div\", 8);\n            i0.ɵɵelementStart(7, \"div\", 9);\n            i0.ɵɵtemplate(8, InvoicecreditlistComponent_ng_template_8_Template, 3, 2, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n            i0.ɵɵelementStart(10, \"div\", 10)(11, \"input\", 11);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function InvoicecreditlistComponent_Template_input_ngModelChange_11_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.Customer, $event) || (ctx.Customer = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(12, \"div\", 12);\n            i0.ɵɵlistener(\"click\", function InvoicecreditlistComponent_Template_div_click_12_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.cancel());\n            });\n            i0.ɵɵtext(13, \"x\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(14, \"div\", 9)(15, \"input\", 13);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function InvoicecreditlistComponent_Template_input_ngModelChange_15_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.billno, $event) || (ctx.billno = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(16, \"div\", 14)(17, \"input\", 15);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function InvoicecreditlistComponent_Template_input_ngModelChange_17_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.Datafromdate, $event) || (ctx.Datafromdate = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(18, \"div\", 14)(19, \"input\", 15);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function InvoicecreditlistComponent_Template_input_ngModelChange_19_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.Datatodate, $event) || (ctx.Datatodate = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(20, \"div\", 16)(21, \"button\", 17);\n            i0.ɵɵlistener(\"click\", function InvoicecreditlistComponent_Template_button_click_21_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.Searchinvoicecredit());\n            });\n            i0.ɵɵtext(22, \"Search\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(23, \"button\", 18);\n            i0.ɵɵlistener(\"click\", function InvoicecreditlistComponent_Template_button_click_23_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.SearchinvoicecreditED());\n            });\n            i0.ɵɵtext(24, \"Edit Bill\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(25, \"table\", 19)(26, \"thead\")(27, \"tr\", 20)(28, \"th\", 21);\n            i0.ɵɵtext(29, \"Item\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(30, \"th\", 21);\n            i0.ɵɵtext(31, \"Bill No\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(32, \"th\", 21);\n            i0.ɵɵtext(33, \"Bill Date\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(34, \"th\", 21);\n            i0.ɵɵtext(35, \"Due Date\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(36, \"th\", 21);\n            i0.ɵɵtext(37, \"\\u0E1E\\u0E19\\u0E31\\u0E01\\u0E07\\u0E32\\u0E19\\u0E02\\u0E32\\u0E22\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(38, \"th\", 22);\n            i0.ɵɵtext(39, \"\\u0E23\\u0E2B\\u0E31\\u0E2A\\u0E25\\u0E39\\u0E01\\u0E04\\u0E49\\u0E32\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(40, \"th\", 23);\n            i0.ɵɵtext(41, \"\\u0E0A\\u0E37\\u0E48\\u0E2D\\u0E25\\u0E39\\u0E01\\u0E04\\u0E49\\u0E32\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(42, \"th\", 21);\n            i0.ɵɵtext(43, \"\\u0E21\\u0E39\\u0E25\\u0E04\\u0E48\\u0E32\\u0E2A\\u0E34\\u0E19\\u0E04\\u0E49\\u0E32\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(44, \"th\", 21);\n            i0.ɵɵtext(45, \"VAT\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(46, \"th\", 21);\n            i0.ɵɵtext(47, \"\\u0E21\\u0E39\\u0E25\\u0E04\\u0E48\\u0E32\\u0E23\\u0E27\\u0E21\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(48, \"th\", 24);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(49, \"tbody\");\n            i0.ɵɵtemplate(50, InvoicecreditlistComponent_tr_50_Template, 29, 56, \"tr\", 25);\n            i0.ɵɵelementStart(51, \"tr\", 26);\n            i0.ɵɵelement(52, \"th\", 27)(53, \"td\", 27)(54, \"td\", 27)(55, \"td\", 27)(56, \"td\", 27)(57, \"td\", 27);\n            i0.ɵɵelementStart(58, \"td\", 28);\n            i0.ɵɵtext(59, \"\\u0E21\\u0E39\\u0E25\\u0E04\\u0E48\\u0E32\\u0E23\\u0E27\\u0E21\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(60, \"td\", 28);\n            i0.ɵɵtext(61);\n            i0.ɵɵpipe(62, \"number\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(63, \"td\", 28);\n            i0.ɵɵtext(64);\n            i0.ɵɵpipe(65, \"number\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(66, \"td\", 28);\n            i0.ɵɵtext(67);\n            i0.ɵɵpipe(68, \"number\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(69, \"td\", 27);\n            i0.ɵɵelementEnd()()()()();\n            i0.ɵɵelementStart(70, \"div\", 29)(71, \"div\", 30)(72, \"div\", 31)(73, \"div\", 32)(74, \"h5\", 33);\n            i0.ɵɵtext(75);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(76, \"button\", 34);\n            i0.ɵɵlistener(\"click\", function InvoicecreditlistComponent_Template_button_click_76_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.clear());\n            });\n            i0.ɵɵelementStart(77, \"span\", 35);\n            i0.ɵɵtext(78, \"\\u00D7\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(79, \"div\", 36)(80, \"div\", 37)(81, \"table\", 19)(82, \"thead\")(83, \"tr\", 20);\n            i0.ɵɵelement(84, \"th\", 22);\n            i0.ɵɵelementStart(85, \"th\", 38);\n            i0.ɵɵtext(86, \"SO No \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(87, \"th\", 38);\n            i0.ɵɵtext(88, \"Invoice No \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(89, \"th\", 38);\n            i0.ɵɵtext(90, \"Invoice Date \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(91, \"th\", 38);\n            i0.ɵɵtext(92, \"Due Date \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(93, \"th\", 21);\n            i0.ɵɵtext(94, \"\\u0E21\\u0E39\\u0E25\\u0E04\\u0E48\\u0E32\\u0E2A\\u0E34\\u0E19\\u0E04\\u0E49\\u0E32\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(95, \"th\", 21);\n            i0.ɵɵtext(96, \"VAT\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(97, \"th\", 21);\n            i0.ɵɵtext(98, \"\\u0E21\\u0E39\\u0E25\\u0E04\\u0E48\\u0E32\\u0E23\\u0E27\\u0E21\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(99, \"th\", 39)(100, \"input\", 40);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function InvoicecreditlistComponent_Template_input_ngModelChange_100_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.testcheck, $event) || (ctx.testcheck = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵlistener(\"click\", function InvoicecreditlistComponent_Template_input_click_100_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.selectAll($event.target.checked));\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelement(101, \"th\", 22)(102, \"th\", 22);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(103, \"tbody\");\n            i0.ɵɵtemplate(104, InvoicecreditlistComponent_tr_104_Template, 28, 52, \"tr\", 41);\n            i0.ɵɵelementStart(105, \"td\", 42);\n            i0.ɵɵtext(106, \"\\u0E21\\u0E39\\u0E25\\u0E04\\u0E48\\u0E32\\u0E23\\u0E27\\u0E21\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(107, \"td\", 28);\n            i0.ɵɵtext(108);\n            i0.ɵɵpipe(109, \"number\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(110, \"td\", 28);\n            i0.ɵɵtext(111);\n            i0.ɵɵpipe(112, \"number\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(113, \"td\", 28);\n            i0.ɵɵtext(114);\n            i0.ɵɵpipe(115, \"number\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(116, \"td\", 43)(117, \"td\", 43)(118, \"td\", 43);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(119, \"div\", 44)(120, \"div\", 45)(121, \"input\", 46, 1);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function InvoicecreditlistComponent_Template_input_ngModelChange_121_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.Filelist, $event) || (ctx.Filelist = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵlistener(\"change\", function InvoicecreditlistComponent_Template_input_change_121_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.handleFileInput($event.target.files));\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(123, \"label\", 47);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function InvoicecreditlistComponent_Template_label_ngModelChange_123_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.textload, $event) || (ctx.textload = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵtext(124);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵtemplate(125, InvoicecreditlistComponent_div_125_Template, 9, 1, \"div\", 48);\n            i0.ɵɵelementStart(126, \"div\", 49)(127, \"div\", 50)(128, \"form\", 51, 2);\n            i0.ɵɵelement(130, \"img\", 52);\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(131, \"div\", 53)(132, \"div\", 54);\n            i0.ɵɵtext(133);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(134, \"button\", 55);\n            i0.ɵɵlistener(\"click\", function InvoicecreditlistComponent_Template_button_click_134_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.clear());\n            });\n            i0.ɵɵtext(135, \"Close\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(136, \"button\", 56);\n            i0.ɵɵtext(137, \" ViewImage \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(138, \"button\", 57);\n            i0.ɵɵlistener(\"click\", function InvoicecreditlistComponent_Template_button_click_138_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.onUpload(ctx.chDraft, ctx.total));\n            });\n            i0.ɵɵtext(139, \"Upload\");\n            i0.ɵɵelementEnd()()()()();\n            i0.ɵɵelementStart(140, \"div\", 58)(141, \"div\", 59)(142, \"div\", 31)(143, \"div\", 60)(144, \"h4\", 61);\n            i0.ɵɵtext(145, \"Report\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(146, \"div\", 36);\n            i0.ɵɵtext(147);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(148, \"div\", 62)(149, \"button\", 63);\n            i0.ɵɵlistener(\"click\", function InvoicecreditlistComponent_Template_button_click_149_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.closemodel(false));\n            });\n            i0.ɵɵelement(150, \"i\", 64);\n            i0.ɵɵtext(151, \" \\u0E1B\\u0E34\\u0E14\");\n            i0.ɵɵelementEnd()()()()();\n            i0.ɵɵelementStart(152, \"div\", 58)(153, \"div\", 59)(154, \"div\", 31)(155, \"div\", 60)(156, \"h4\", 61);\n            i0.ɵɵtext(157, \"Report\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(158, \"div\", 36);\n            i0.ɵɵtext(159);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(160, \"div\", 62)(161, \"button\", 65);\n            i0.ɵɵlistener(\"click\", function InvoicecreditlistComponent_Template_button_click_161_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.closemodel(false));\n            });\n            i0.ɵɵelement(162, \"i\", 64);\n            i0.ɵɵtext(163, \" \\u0E1B\\u0E34\\u0E14\");\n            i0.ɵɵelementEnd()()()()();\n            i0.ɵɵelementStart(164, \"div\", 58)(165, \"div\", 59)(166, \"div\", 31)(167, \"div\", 60)(168, \"h4\", 61);\n            i0.ɵɵtext(169, \"Report\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(170, \"div\", 36);\n            i0.ɵɵtext(171);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(172, \"div\", 62)(173, \"button\", 65);\n            i0.ɵɵlistener(\"click\", function InvoicecreditlistComponent_Template_button_click_173_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.closemodelsuccess(false));\n            });\n            i0.ɵɵelement(174, \"i\", 64);\n            i0.ɵɵtext(175, \" \\u0E1B\\u0E34\\u0E14\");\n            i0.ɵɵelementEnd()()()()();\n            i0.ɵɵelementStart(176, \"div\", 58)(177, \"div\", 66)(178, \"div\", 67)(179, \"div\", 68)(180, \"ngb-tabset\")(181, \"ngb-tab\");\n            i0.ɵɵtemplate(182, InvoicecreditlistComponent_ng_template_182_Template, 2, 0, \"ng-template\", 69)(183, InvoicecreditlistComponent_ng_template_183_Template, 1, 1, \"ng-template\", 70);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(184, \"div\", 71)(185, \"button\", 65);\n            i0.ɵɵlistener(\"click\", function InvoicecreditlistComponent_Template_button_click_185_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.closemodelIMG(false));\n            });\n            i0.ɵɵtext(186, \" \\u0E1B\\u0E34\\u0E14\");\n            i0.ɵɵelementEnd()()()()();\n            i0.ɵɵtemplate(187, InvoicecreditlistComponent_ng_template_187_Template, 10, 1, \"ng-template\", null, 3, i0.ɵɵtemplateRefExtractor);\n          }\n          if (rf & 2) {\n            const rt_r19 = i0.ɵɵreference(9);\n            i0.ɵɵadvance(6);\n            i0.ɵɵproperty(\"ngIf\", ctx.testclose);\n            i0.ɵɵadvance(5);\n            i0.ɵɵpropertyInterpolate(\"placeholder\", ctx.txtcustomer);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.Customer);\n            i0.ɵɵproperty(\"ngbTypeahead\", ctx.search)(\"resultTemplate\", rt_r19)(\"inputFormatter\", ctx.formatter);\n            i0.ɵɵadvance(4);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.billno);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"bsConfig\", i0.ɵɵpureFunction0(57, _c0));\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.Datafromdate);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"bsConfig\", i0.ɵɵpureFunction0(58, _c0));\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.Datatodate);\n            i0.ɵɵadvance(31);\n            i0.ɵɵproperty(\"ngForOf\", ctx.invoicecredit);\n            i0.ɵɵadvance(11);\n            i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind2(62, 39, ctx.productprice, \"1.2-2\"), \" \");\n            i0.ɵɵadvance(3);\n            i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(65, 42, ctx.sumvat, \"1.2-2\"));\n            i0.ɵɵadvance(3);\n            i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind2(68, 45, ctx.sumprice, \"1.2-2\"), \" \");\n            i0.ɵɵadvance(8);\n            i0.ɵɵtextInterpolate1(\"Upload File Bill No : \", ctx.showbillno, \" \");\n            i0.ɵɵadvance(25);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.testcheck);\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"ngForOf\", ctx.idinvi);\n            i0.ɵɵadvance(4);\n            i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(109, 48, ctx.trueproductprice, \"1.2-2\"));\n            i0.ɵɵadvance(3);\n            i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind2(112, 51, ctx.truesumvat, \"1.2-2\"), \" \");\n            i0.ɵɵadvance(3);\n            i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind2(115, 54, ctx.truesumprice, \"1.2-2\"), \" \");\n            i0.ɵɵadvance(7);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.Filelist);\n            i0.ɵɵadvance(2);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.textload);\n            i0.ɵɵadvance();\n            i0.ɵɵtextInterpolate1(\"\", ctx.textload, \" \");\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.total != 0);\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"src\", ctx.imageUrl, i0.ɵɵsanitizeUrl);\n            i0.ɵɵadvance(3);\n            i0.ɵɵtextInterpolate1(\" \\u0E23\\u0E32\\u0E22\\u0E17\\u0E35\\u0E48\\u0E40\\u0E25\\u0E37\\u0E2D\\u0E01\\u0E17\\u0E31\\u0E49\\u0E07\\u0E2B\\u0E21\\u0E14 : \", ctx.total, \" \");\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"disabled\", ctx.selectedFile == \"\");\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"disabled\", ctx.total == 0 || ctx.chDraft == \"A\" || ctx.Filelist == undefined && ctx.chDraft == \"0\" && ctx.selectedFile == \"\");\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(59, _c1, ctx.mdlSampleIsOpen ? \"block\" : \"none\"));\n            i0.ɵɵadvance(7);\n            i0.ɵɵtextInterpolate2(\"\", ctx.alt, \" \", ctx.load, \" \");\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(61, _c1, ctx.mdlSampleIsOpen2 ? \"block\" : \"none\"));\n            i0.ɵɵadvance(7);\n            i0.ɵɵtextInterpolate2(\"\", ctx.alt2, \" \", ctx.load, \" \");\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(63, _c1, ctx.mdlSampleIsOpensuccess ? \"block\" : \"none\"));\n            i0.ɵɵadvance(7);\n            i0.ɵɵtextInterpolate2(\"\", ctx.altsuccess, \" \", ctx.load, \" \");\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(65, _c1, ctx.mdlSampleIsOpenIMG ? \"block\" : \"none\"));\n          }\n        }\n      });\n    }\n  }\n  return InvoicecreditlistComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}