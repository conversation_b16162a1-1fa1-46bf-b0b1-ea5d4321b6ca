{"ast": null, "code": "\"use strict\";\n\nvar __read = this && this.__read || function (o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o),\n    r,\n    ar = [],\n    e;\n  try {\n    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n  } catch (error) {\n    e = {\n      error: error\n    };\n  } finally {\n    try {\n      if (r && !r.done && (m = i[\"return\"])) m.call(i);\n    } finally {\n      if (e) throw e.error;\n    }\n  }\n  return ar;\n};\nvar __spreadArray = this && this.__spreadArray || function (to, from) {\n  for (var i = 0, il = from.length, j = to.length; i < il; i++, j++) to[j] = from[i];\n  return to;\n};\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.combineLatest = void 0;\nvar combineLatest_1 = require(\"../observable/combineLatest\");\nvar lift_1 = require(\"../util/lift\");\nvar argsOrArgArray_1 = require(\"../util/argsOrArgArray\");\nvar mapOneOrManyArgs_1 = require(\"../util/mapOneOrManyArgs\");\nvar pipe_1 = require(\"../util/pipe\");\nvar args_1 = require(\"../util/args\");\nfunction combineLatest() {\n  var args = [];\n  for (var _i = 0; _i < arguments.length; _i++) {\n    args[_i] = arguments[_i];\n  }\n  var resultSelector = args_1.popResultSelector(args);\n  return resultSelector ? pipe_1.pipe(combineLatest.apply(void 0, __spreadArray([], __read(args))), mapOneOrManyArgs_1.mapOneOrManyArgs(resultSelector)) : lift_1.operate(function (source, subscriber) {\n    combineLatest_1.combineLatestInit(__spreadArray([source], __read(argsOrArgArray_1.argsOrArgArray(args))))(subscriber);\n  });\n}\nexports.combineLatest = combineLatest;\n//# sourceMappingURL=combineLatest.js.map", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}