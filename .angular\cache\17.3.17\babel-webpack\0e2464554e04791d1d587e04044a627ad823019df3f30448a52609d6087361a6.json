{"ast": null, "code": "import { SoComponent } from '../so/so.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"ngx-bootstrap/modal\";\nimport * as i2 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i3 from \"@angular/common/http\";\nimport * as i4 from \"@angular/forms\";\nimport * as i5 from \"../../../node_modules/@angular/router\";\nimport * as i6 from \"../webapi.service\";\nimport * as i7 from \"ngx-cookie-service\";\nimport * as i8 from \"@angular/common\";\nimport * as i9 from \"../../../node_modules/@angular/forms/index\";\nconst _c0 = [\"autoShownModal\"];\nconst _c1 = a0 => ({\n  \"display\": a0,\n  \"opacity\": 1\n});\nconst _c2 = () => ({\n  show: true,\n  ignoreBackdropClick: true\n});\nconst _c3 = a0 => ({\n  \"color\": a0\n});\nfunction LoginComponent_div_48_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 36, 2);\n    i0.ɵɵlistener(\"onHidden\", function LoginComponent_div_48_Template_div_onHidden_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.onHidden());\n    });\n    i0.ɵɵelementStart(2, \"div\", 37)(3, \"div\", 28)(4, \"div\", 31);\n    i0.ɵɵelement(5, \"img\", 38);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"config\", i0.ɵɵpureFunction0(1, _c2));\n  }\n}\nfunction LoginComponent_ng_template_49_p_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 44);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(2, _c3, ctx_r3.getColorHost(ctx_r3.CkHost)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r3.CkHost);\n  }\n}\nfunction LoginComponent_ng_template_49_P_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"P\", 44);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(2, _c3, ctx_r3.getColorHost(ctx_r3.CkHost)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.textshow, \"\");\n  }\n}\nfunction LoginComponent_ng_template_49_button_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 45);\n    i0.ɵɵlistener(\"click\", function LoginComponent_ng_template_49_button_3_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.syncdataHost());\n    });\n    i0.ɵɵtext(1, \"syncdata\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r3.CksumHost < 14);\n  }\n}\nfunction LoginComponent_ng_template_49_button_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 46);\n    i0.ɵɵlistener(\"click\", function LoginComponent_ng_template_49_button_4_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.CKhost());\n    });\n    i0.ɵɵtext(1, \"\\u0E15\\u0E34\\u0E14\\u0E15\\u0E48\\u0E2D\\u0E10\\u0E32\\u0E19\\u0E02\\u0E49\\u0E2D\\u0E21\\u0E39\\u0E25\\u0E43\\u0E2B\\u0E21\\u0E48\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent_ng_template_49_button_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 47);\n    i0.ɵɵlistener(\"click\", function LoginComponent_ng_template_49_button_5_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.syncdatamodalRef.hide());\n    });\n    i0.ɵɵtext(1, \"\\u0E1B\\u0E34\\u0E14\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent_ng_template_49_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 39);\n    i0.ɵɵtemplate(1, LoginComponent_ng_template_49_p_1_Template, 2, 4, \"p\", 40)(2, LoginComponent_ng_template_49_P_2_Template, 2, 4, \"P\", 40)(3, LoginComponent_ng_template_49_button_3_Template, 2, 1, \"button\", 41)(4, LoginComponent_ng_template_49_button_4_Template, 2, 0, \"button\", 42)(5, LoginComponent_ng_template_49_button_5_Template, 2, 0, \"button\", 43);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r3.CKbtntrue);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.CKbtntrue);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.CksumHost > 14 && !ctx_r3.CKbtntrue);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.CksumHost < 14);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r3.CKbtntrue);\n  }\n}\n/*[routerLink]=\"['/customerlist']\"*/\nexport let LoginComponent = /*#__PURE__*/(() => {\n  class LoginComponent {\n    showModal() {\n      this.isModalShown = true;\n    }\n    hideModal() {\n      this.autoShownModal.hide();\n    }\n    onHidden() {\n      this.isModalShown = false;\n    }\n    constructor(modalService2, modalService, http, fb, trou, service, cookie) {\n      this.modalService2 = modalService2;\n      this.modalService = modalService;\n      this.http = http;\n      this.fb = fb;\n      this.trou = trou;\n      this.service = service;\n      this.cookie = cookie;\n      this.mdlSampleIsOpen = false;\n      this.alt = '';\n      this.checkreload = true;\n      this.rememberlogin = false;\n      this.customerlogin = false;\n      this.salslogin = false;\n      this.isModalShown = false;\n      this.CksumHost = 0;\n      this.CkHost = '';\n      this.HostTrue = false;\n      this.uncheckableRadioModel = 'Middle';\n      this.cookieValue = 'UNKNOWN';\n      this.Username = '';\n      this.Password = '';\n      this.loggedin = false;\n      this.setdata = {};\n      this.numberbad = 0;\n      this.numnobad = false;\n      this.numall = 0;\n      this.clr = [];\n      this.permissionloaddata = [];\n      this.pageweb = [];\n      this.configviewsync = {\n        ignoreBackdropClick: true,\n        class: 'modal-sm'\n      };\n      this.urlhost = '';\n      this.CKbtntrue = false;\n      this.textshow = '';\n      localStorage.removeItem('DataSOderreview');\n      localStorage.removeItem('DataSOderlist');\n      /* sessionStorage.clear();\n       localStorage.clear();*/\n      //this.urlhost = service.geturlHost();\n      this.Username = this.getCookie('username');\n      this.Password = this.getCookie('password');\n      this.service.getuserlogin(this.clr);\n      if (this.getCookie('checkremember') === 'true') {\n        this.rememberlogin = true;\n      } else {\n        this.deleteCookie('checkremember');\n      }\n      if (this.getCookie('CK1') === 'S') {\n        this.salslogin = true;\n        this.customerlogin = false;\n        console.log(1);\n      } else if (this.getCookie('CK1') === 'C') {\n        this.salslogin = false;\n        this.customerlogin = true;\n        console.log(2);\n      } else {\n        this.salslogin = false;\n        this.customerlogin = false;\n        console.log(3);\n        this.deleteCookie('CK1');\n      }\n      this.validate = '';\n      this.url = this.service.geturlservice();\n      this.checkreqserver();\n      /* if(this.numnobad <10){*/\n      /* var i\n       for(i=0;i<=10;i++){\n         this.http.get(this.url+'menulist_top1').subscribe(res=>{\n             alert('1');\n             return;\n             },error=>{\n               \n             })\n       }*/\n      /*  this.setInterval= setInterval(()=>  this.http.get(this.url+'menulist_top1').subscribe(res=>{\n        this.numnobad++;\n          },error=>{\n            \n          }),200);\n      }else{\n      clearInterval(this.setInterval);\n      }*/\n    }\n    /* setv (){\n      if(localStorage.getItem('CK')==null || localStorage.getItem('CK')==\"\"){\n        localStorage.setItem(\"CK\",null)\n      }else{\n       var dataCK =  localStorage.getItem('CK');\n       console.log(dataCK)\n        if(dataCK==\"S\"){\n          this.salslogin==true\n          this.customerlogin==false\n    \n          console.log(this.salslogin);\n         console.log(1)\n        }else if(dataCK==\"C\"){\n          this.salslogin==false\n         this.customerlogin==true\n         console.log(2)\n        }else{\n          console.log(3)\n          this.salslogin==false\n         this.customerlogin==false\n        }\n    \n      }\n     }*/\n    setcookielofin(name, value) {\n      var cookie = name + '=' + value + ';';\n      var date = new Date();\n      date.setDate(date.getDate() + 365);\n      cookie += 'expires=' + date.toString() + ';';\n      document.cookie = cookie;\n    }\n    getCookie(cname) {\n      var name = cname + \"=\";\n      var ca = document.cookie.split(';');\n      for (var i = 0; i < ca.length; i++) {\n        var c = ca[i];\n        while (c.charAt(0) == ' ') {\n          c = c.substring(1);\n        }\n        if (c.indexOf(name) == 0) {\n          return c.substring(name.length, c.length);\n        }\n      }\n      return \"\";\n    }\n    deleteCookie(name) {\n      var cookie = name + '=;';\n      cookie += 'expires=' + new Date().toString() + ';';\n      document.cookie = cookie;\n    }\n    open() {\n      const modalRef = this.modalService.open(SoComponent, {\n        size: 'lg',\n        windowClass: 'modal-Top100',\n        backdrop: \"static\"\n      });\n      modalRef.componentInstance.name = this.name;\n      modalRef.componentInstance.idcustomer = this.idcustomer;\n      modalRef.componentInstance.idusercustomer = this.idusercustomer;\n    }\n    checkreqserver() {\n      this.CksumHost = 0;\n      this.CkHost = '.';\n      for (var i = 0; i < 40; i++) {\n        this.http.get(this.url + 'menulist_top1').subscribe(res => {\n          this.numnobad = true;\n          this.CksumHost = this.CksumHost + 1;\n          //alert(JSON.stringify(res))\n          // console.log(this.CksumHost)\n          this.CKNumHost(this.CksumHost);\n        }, error => {\n          this.numnobad = false;\n        });\n        if (this.numnobad == true) {\n          continue;\n        } else {}\n      }\n    }\n    CKNumHost(data) {\n      this.CkHost = '';\n      this.HostTrue = false;\n      if (data > 14) {\n        this.onHidden();\n      } else {}\n    }\n    getColor() {\n      if (this.numnobad == true) {\n        return 'green';\n      } else {\n        return 'red';\n      }\n    }\n    ngOnInit() {\n      this.validate = '';\n      this.numps = 0;\n      this.loginfail = '';\n    }\n    setloginst(value) {\n      this.loggedin = value;\n    }\n    get isLogin() {\n      return this.loggedin;\n    }\n    onSubmit(value) {\n      this.Username = value.user_detail.Username;\n      this.Password = value.user_detail.Password;\n      if (this.salslogin == true && this.customerlogin == false) {\n        this.http.post(this.url + 'login', {\n          id: value.user_detail.Username,\n          pw: value.user_detail.Password\n        }).subscribe(res => {\n          if (JSON.stringify(res) === '[]') {\n            this.openModal(true, 'ผู้ใช้งาน หรือ รหัสผ่านไม่ถูกต้อง', false);\n            this.trou.navigate(['']);\n          } else if (res[0].login_user === value.user_detail.Username) {\n            this.saveuser = res;\n            if (this.rememberlogin == true) {\n              this.setcookielofin('username', this.Username);\n              this.setcookielofin('password', this.Password);\n            }\n            /* localStorage.setItem('idgroupuser', res[0].id_group_user);\n              localStorage.setItem('Name', res[0].name_user); */\n            this.logindata = res;\n            this.service.getuserlogin(res);\n            /*this.http.get<any>(this.url + 'find_permission/' + res[0].id_group_user).subscribe((res: menulist[]) => {\n                            \n             this.menulistdata=res;\n             localStorage.setItem('menulogin',JSON.stringify(res));\n            this.service.getmenulogin(res);\n                             \n             },error =>{\n               console.log(error);\n             }); */\n            sessionStorage.setItem('login', JSON.stringify(res));\n            sessionStorage.setItem('salegroup', res[0].salegroup);\n            this.loadpermisstion(res[0].id_group_user, res[0].id_group_user);\n          } else {\n            alert(this.loginfail);\n            this.trou.navigate(['']);\n          }\n        }, error => {\n          status = error.status;\n          this.openModal(true, 'เกิดปัญหาในการ Process ข้อมูล กรุณาลองใหม่อีกครั้ง', false);\n        });\n      } else if (this.salslogin == false && this.customerlogin == true) {\n        this.http.post(this.url + 'login_customer', {\n          id: value.user_detail.Username,\n          pw: value.user_detail.Password\n        }).subscribe(res => {\n          if (JSON.stringify(res) === '[]') {\n            this.openModal(true, 'ผู้ใช้งาน หรือ รหัสผ่านไม่ถูกต้อง', false);\n            this.trou.navigate(['']);\n          } else if (res[0].login_user === value.user_detail.Username) {\n            this.saveuser = res;\n            if (this.rememberlogin == true) {\n              this.setcookielofin('username', this.Username);\n              this.setcookielofin('password', this.Password);\n            }\n            /* localStorage.setItem('idgroupuser', res[0].id_group_user);\n              localStorage.setItem('Name', res[0].name_user); */\n            this.logindata = res;\n            this.service.getuserlogin(res);\n            localStorage.setItem('salegroup', res[0].salegroup);\n            sessionStorage.setItem('login', JSON.stringify(res));\n            //alert(JSON.stringify(res));\n            /*this.http.get<any>(this.url + 'find_permission/' + res[0].id_group_user).subscribe((res: menulist[]) => {\n                            \n             this.menulistdata=res;\n             localStorage.setItem('menulogin',JSON.stringify(res));\n            this.service.getmenulogin(res);\n                             \n             },error =>{\n               console.log(error);\n             }); */\n            if (res[0].checklogin == 0) {\n              this.name = res[0].name_user.toString();\n              this.idusercustomer = res[0].id_user.toString();\n              this.open();\n            } else {\n              this.loadpermisstion(res[0].id_group_user, res[0].id_group_user);\n            }\n          } else {\n            alert(this.loginfail);\n            this.trou.navigate(['']);\n          }\n        }, error => {\n          status = error.status;\n          this.openModal(true, 'เกิดปัญหาในการ Process ข้อมูล กรุณาลองใหม่อีกครั้ง', false);\n        });\n      }\n    }\n    loadpermisstion(value, group) {\n      this.http.post(this.url + 'find_permission', {\n        id_user_group: value\n      }).subscribe(res => {\n        if (res.length > 0) {\n          this.service.setpromisstiondata(res);\n          sessionStorage.setItem('menu', JSON.stringify(res));\n          this.permissionloaddata = res;\n          for (var i = 0; i < this.permissionloaddata.length; i++) {\n            if (this.permissionloaddata[i].flag_view == true) {\n              this.pageweb.push({\n                pagename: this.permissionloaddata[i].des_menu\n              });\n            }\n          }\n          if (group === '153Admin' && this.pageweb.length == 15 || group != 'saleadmin') {\n            this.trou.navigate(['sorecord']);\n          } else {\n            if (this.permissionloaddata.length > 0) {\n              var pagelink = '';\n              if (this.pageweb[0].pagename === 'Customer') {\n                pagelink = 'customerlist';\n              } else if (this.pageweb[0].pagename === 'Product List') {\n                pagelink = 'productlist';\n              } else if (this.pageweb[0].pagename === 'Price Master') {\n                pagelink = 'pricemaster';\n              } else if (this.pageweb[0].pagename === 'Sale Order') {\n                pagelink = 'sorecord';\n              } else if (this.pageweb[0].pagename === 'Sale Order List') {\n                pagelink = 'solist1';\n              } else if (this.pageweb[0].pagename === 'Sale Order Review') {\n                pagelink = 'soreview';\n              } else if (this.pageweb[0].pagename === 'Sale Order History') {\n                pagelink = 'sohistory';\n              } else if (this.pageweb[0].pagename === 'Cash Invoice List') {\n                pagelink = 'invoicecashlist';\n              } else if (this.pageweb[0].pagename === 'Credit Invoice List') {\n                pagelink = 'invoicecreditlist';\n              } else if (this.pageweb[0].pagename === 'Complete Invloice List') {\n                pagelink = 'completeinvoice';\n              } else if (this.pageweb[0].pagename === 'Master Package') {\n                pagelink = 'masterpackage';\n              } else if (this.pageweb[0].pagename === 'Grouping Product') {\n                pagelink = 'productgroup';\n              } else if (this.pageweb[0].pagename === 'Cover Page') {\n                pagelink = 'coverpage';\n              } else if (this.pageweb[0].pagename === 'User Accounts') {\n                pagelink = 'users';\n              } else if (this.pageweb[0].pagename === 'Permission Setting') {\n                pagelink = 'permission';\n              }\n              this.trou.navigate([pagelink]);\n            } else {}\n          }\n        }\n      }, error => {\n        console.log(error);\n      });\n    }\n    clickcustomerlogin(value) {\n      this.customerlogin = value;\n      if (value == true) {\n        this.salslogin = false;\n        this.setcookielofin('CK1', 'C');\n      }\n    }\n    clickcsaleslogin(value) {\n      this.salslogin = value;\n      if (value == true) {\n        this.customerlogin = false;\n        this.setcookielofin('CK1', 'S');\n      }\n    }\n    clickremembermelogin(value) {\n      this.rememberlogin = value;\n      this.setcookielofin('checkremember', value);\n      if (value == false) {\n        this.deleteCookie('username');\n        this.deleteCookie('password');\n        // this.cookie.delete('username');\n        // this.cookie.delete('password');\n      }\n    }\n    openModal(open, text, load) {\n      this.mdlSampleIsOpen = open;\n      this.alt = text;\n      this.checkreload = load;\n    }\n    closemodel(cl) {\n      this.mdlSampleIsOpen = cl;\n      if (this.checkreload == true) {\n        location.reload();\n      }\n    }\n    OpenCKSync(template) {\n      this.CKhost();\n      this.syncdatamodalRef = this.modalService2.show(template, this.configviewsync);\n    }\n    CKhost() {\n      this.CksumHost = 0;\n      this.CkHost = '.';\n      for (var a = 0; a < 10; a++) {\n        this.CkHost = this.CkHost + '.';\n        if (a < 10) {\n          for (var i = 0; i < 20; i++) {\n            this.http.get(this.urlhost + 'menulist_top1').subscribe(res => {\n              this.CksumHost = this.CksumHost + 1;\n              //alert(JSON.stringify(res))\n              // console.log(this.CksumHost)\n              this.CKNumHost2(this.CksumHost);\n            }, error => {\n              this.CKNumHost(this.CksumHost);\n            });\n            if (this.CksumHost > 14) {\n              return;\n            }\n          }\n        }\n      }\n    }\n    CKNumHost2(data) {\n      this.CkHost = '';\n      this.HostTrue = false;\n      if (data > 14) {\n        this.HostTrue = true;\n        this.CkHost = 'ติดต่อฐานข้อมูลได้';\n      } else {\n        this.HostTrue = false;\n        this.CkHost = 'ติดต่อฐานข้อมูลไม่ได้';\n      }\n    }\n    getColorHost(data) {\n      this.HostTrue = false;\n      if (data === 'ติดต่อฐานข้อมูลได้' || data === 'โปรดรอ') {\n        this.HostTrue = true;\n        return 'Green';\n      } else if (data === 'ติดต่อฐานข้อมูลไม่ได้') {\n        this.HostTrue = false;\n        return 'red';\n      } else {\n        this.HostTrue = false;\n        return 'red';\n      }\n    }\n    syncdataHost() {\n      this.CKhost();\n      if (this.HostTrue == true) {\n        this.textshow = 'โปรดรอ.';\n        this.CKbtntrue = true;\n        const Http = new XMLHttpRequest();\n        const url = 'http://localhost:9090/sync_SO/service.asmx/Import';\n        Http.open(\"POST\", url);\n        Http.send();\n        Http.onreadystatechange = e => {\n          if (Http.readyState == 4 && Http.status == 200) {\n            if (confirm('นำเข้าข้อมูล เสร็จสิ้น')) {\n              alert('นำเข้าข้อมูล เสร็จสิ้น');\n              this.syncdatamodalRef.hide();\n            } else {}\n          }\n        };\n      } else {\n        alert('การเชื่อมต่อถูกตัดขาด');\n        this.CkHost = 'การเชื่อมต่อถูกตัดขาด';\n      }\n    }\n    static {\n      this.ɵfac = function LoginComponent_Factory(t) {\n        return new (t || LoginComponent)(i0.ɵɵdirectiveInject(i1.BsModalService), i0.ɵɵdirectiveInject(i2.NgbModal), i0.ɵɵdirectiveInject(i3.HttpClient), i0.ɵɵdirectiveInject(i4.FormBuilder), i0.ɵɵdirectiveInject(i5.Router), i0.ɵɵdirectiveInject(i6.WebapiService), i0.ɵɵdirectiveInject(i7.CookieService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: LoginComponent,\n        selectors: [[\"app-login\"]],\n        viewQuery: function LoginComponent_Query(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵviewQuery(_c0, 5);\n          }\n          if (rf & 2) {\n            let _t;\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.autoShownModal = _t.first);\n          }\n        },\n        decls: 51,\n        vars: 11,\n        consts: [[\"form\", \"ngForm\"], [\"syncdata\", \"\"], [\"autoShownModal\", \"bs-modal\"], [\"ng-app\", \"loginpage\"], [\"name\", \"viewport\", \"content\", \"width=device-width, initial-scale=1\"], [1, \"container\"], [1, \"row\"], [1, \"col-md-4\", \"offset-md-4\"], [\"src\", \"assets/img/LOGO--NANO-(-24-05-2018).png\", \"width\", \"99%\", 2, \"margin-top\", \"10%\"], [1, \"col-md-4\", \"offset-md-4\", \"form-control\"], [\"role\", \"form\", \"action\", \"\", 3, \"submit\"], [\"ngModelGroup\", \"user_detail\"], [1, \"form-group\"], [\"for\", \"first_name\"], [\"ngModel\", \"\", \"name\", \"Username\", \"type\", \"text\", \"id\", \"Username\", \"placeholder\", \"Username\", \"required\", \"\", 1, \"form-control\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"last_name\"], [\"ngModel\", \"\", \"name\", \"Password\", \"type\", \"password\", \"id\", \"Password\", \"placeholder\", \"Password\", \"required\", \"\", 1, \"form-control\", 3, \"ngModelChange\", \"ngModel\"], [1, \"form-inline\", \"form-check\", \"form-group\"], [\"type\", \"checkbox\", \"name\", \"\", \"id\", \"salescheck\", 1, \"form-control\", 3, \"click\", \"checked\"], [\"for\", \"salescheck\", 1, \"form-check-label\"], [\"type\", \"checkbox\", \"name\", \"\", \"id\", \"customercheck\", 1, \"form-control\", 2, \"margin-left\", \"3px\", 3, \"click\", \"checked\"], [\"for\", \"customercheck\", 1, \"form-check-label\"], [1, \"form-group\", \"form-check\"], [\"type\", \"checkbox\", \"name\", \"\", \"value\", \"Remember Me\", \"id\", \"exampleCheck1\", 1, \"form-check-input\", 3, \"click\", \"checked\"], [\"for\", \"exampleCheck1\", 1, \"form-check-label\"], [\"type\", \"submit\", 1, \"btn\", \"btn-success\", \"w-100\", 2, \"margin-bottom\", \"20px\"], [\"id\", \"mdlSample\", \"role\", \"dialog\", 1, \"modal\", \"fade\", 3, \"ngStyle\"], [1, \"modal-dialog\", \"modal-md\"], [1, \"modal-content\"], [1, \"modal-header\", \"colhaederal\"], [1, \"modal-title\"], [1, \"modal-body\"], [\"align\", \"right\", 1, \"modal-footer\"], [\"type\", \"button\", \"id\", \"btnClose\", 1, \"btn\", \"btn-danger\", 3, \"click\"], [1, \"fa\", \"fa-times\", \"fa-fw\"], [\"bsModal\", \"\", \"class\", \"modal fade\", \"tabindex\", \"-1\", \"role\", \"dialog\", \"aria-labelledby\", \"dialog-auto-name\", 3, \"config\", \"onHidden\", 4, \"ngIf\"], [\"bsModal\", \"\", \"tabindex\", \"-1\", \"role\", \"dialog\", \"aria-labelledby\", \"dialog-auto-name\", 1, \"modal\", \"fade\", 3, \"onHidden\", \"config\"], [1, \"modal-dialog\", \"modal-sm\"], [\"src\", \"assets/img/Spinner-1s-200px.gif\", 2, \"width\", \"100%\"], [1, \"modal-body\", \"text-center\"], [3, \"ngStyle\", 4, \"ngIf\"], [\"type\", \"button\", \"class\", \"btn btn-primary\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [\"type\", \"button\", \"class\", \"btn btn-primary\", 3, \"click\", 4, \"ngIf\"], [\"type\", \"button\", \"class\", \"btn btn-default\", 3, \"click\", 4, \"ngIf\"], [3, \"ngStyle\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", 3, \"click\", \"disabled\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", 3, \"click\"], [\"type\", \"button\", 1, \"btn\", \"btn-default\", 3, \"click\"]],\n        template: function LoginComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            const _r1 = i0.ɵɵgetCurrentView();\n            i0.ɵɵelementStart(0, \"html\", 3)(1, \"head\");\n            i0.ɵɵelement(2, \"meta\", 4);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(3, \"body\")(4, \"div\", 5)(5, \"div\", 6)(6, \"div\", 7);\n            i0.ɵɵelement(7, \"img\", 8);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(8, \"div\", 9)(9, \"form\", 10, 0);\n            i0.ɵɵlistener(\"submit\", function LoginComponent_Template_form_submit_9_listener() {\n              i0.ɵɵrestoreView(_r1);\n              const form_r2 = i0.ɵɵreference(10);\n              return i0.ɵɵresetView(ctx.onSubmit(form_r2.value));\n            });\n            i0.ɵɵelement(11, \"br\");\n            i0.ɵɵelementStart(12, \"fieldset\", 11)(13, \"div\", 12)(14, \"label\", 13);\n            i0.ɵɵtext(15, \"Username\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(16, \"input\", 14);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function LoginComponent_Template_input_ngModelChange_16_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.Username, $event) || (ctx.Username = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(17, \"div\", 12)(18, \"label\", 15);\n            i0.ɵɵtext(19, \"Password\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(20, \"input\", 16);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function LoginComponent_Template_input_ngModelChange_20_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.Password, $event) || (ctx.Password = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(21, \"div\", 17)(22, \"input\", 18);\n            i0.ɵɵlistener(\"click\", function LoginComponent_Template_input_click_22_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.clickcsaleslogin($event.target.checked));\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(23, \"label\", 19);\n            i0.ɵɵtext(24, \"Sales\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(25, \"input\", 20);\n            i0.ɵɵlistener(\"click\", function LoginComponent_Template_input_click_25_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.clickcustomerlogin($event.target.checked));\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(26, \"label\", 21);\n            i0.ɵɵtext(27, \"Customer\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(28, \"div\", 22)(29, \"input\", 23);\n            i0.ɵɵlistener(\"click\", function LoginComponent_Template_input_click_29_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.clickremembermelogin($event.target.checked));\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(30, \"label\", 24);\n            i0.ɵɵtext(31, \"Remember me\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(32, \"button\", 25);\n            i0.ɵɵtext(33, \"Login\");\n            i0.ɵɵelementEnd()()()()();\n            i0.ɵɵelementStart(34, \"div\", 26)(35, \"div\", 27)(36, \"div\", 28)(37, \"div\", 29)(38, \"h4\", 30);\n            i0.ɵɵtext(39, \"Report\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(40, \"div\", 31);\n            i0.ɵɵtext(41);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(42, \"div\", 32)(43, \"button\", 33);\n            i0.ɵɵlistener(\"click\", function LoginComponent_Template_button_click_43_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.closemodel(false));\n            });\n            i0.ɵɵelement(44, \"i\", 34);\n            i0.ɵɵtext(45, \" \\u0E1B\\u0E34\\u0E14\");\n            i0.ɵɵelementEnd()()()()();\n            i0.ɵɵelementStart(46, \"pre\");\n            i0.ɵɵtext(47);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵtemplate(48, LoginComponent_div_48_Template, 6, 2, \"div\", 35)(49, LoginComponent_ng_template_49_Template, 6, 5, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(16);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.Username);\n            i0.ɵɵadvance(4);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.Password);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"checked\", ctx.salslogin);\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"checked\", ctx.customerlogin);\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"checked\", ctx.rememberlogin);\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(9, _c1, ctx.mdlSampleIsOpen ? \"block\" : \"none\"));\n            i0.ɵɵadvance(7);\n            i0.ɵɵtextInterpolate(ctx.alt);\n            i0.ɵɵadvance(6);\n            i0.ɵɵtextInterpolate(ctx.closeResult);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.isModalShown);\n          }\n        },\n        dependencies: [i8.NgIf, i8.NgStyle, i9.ɵNgNoValidate, i9.DefaultValueAccessor, i9.NgControlStatus, i9.NgControlStatusGroup, i9.RequiredValidator, i9.NgModel, i9.NgModelGroup, i9.NgForm, i1.ModalDirective],\n        styles: [\".textpassred[_ngcontent-%COMP%]{color:red}.textuser[_ngcontent-%COMP%]{color:#ff8000}.faillogin[_ngcontent-%COMP%]{font-size:15px;color:red}.modal-Top100[_ngcontent-%COMP%]{top:-100px}\"]\n      });\n    }\n  }\n  return LoginComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}