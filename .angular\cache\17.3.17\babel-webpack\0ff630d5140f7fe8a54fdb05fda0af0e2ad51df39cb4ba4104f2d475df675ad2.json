{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.scanInternals = void 0;\nvar OperatorSubscriber_1 = require(\"./OperatorSubscriber\");\nfunction scanInternals(accumulator, seed, hasSeed, emitOnNext, emitBeforeComplete) {\n  return function (source, subscriber) {\n    var hasState = hasSeed;\n    var state = seed;\n    var index = 0;\n    source.subscribe(OperatorSubscriber_1.createOperatorSubscriber(subscriber, function (value) {\n      var i = index++;\n      state = hasState ? accumulator(state, value, i) : (hasState = true, value);\n      emitOnNext && subscriber.next(state);\n    }, emitBeforeComplete && function () {\n      hasState && subscriber.next(state);\n      subscriber.complete();\n    }));\n  };\n}\nexports.scanInternals = scanInternals;\n//# sourceMappingURL=scanInternals.js.map", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}