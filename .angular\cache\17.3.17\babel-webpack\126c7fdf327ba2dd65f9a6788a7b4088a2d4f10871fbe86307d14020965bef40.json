{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Javanese [jv]\n//! author : <PERSON><PERSON> : https://github.com/lantip\n//! reference: http://jv.wikipedia.org/wiki/Ba<PERSON>_<PERSON>awa\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  var jv = moment.defineLocale('jv', {\n    months: 'Januari_Februari_Maret_April_Mei_Juni_Juli_Agustus_September_Oktober_Nopember_Desember'.split('_'),\n    monthsShort: 'Jan_Feb_Mar_Apr_Mei_Jun_Jul_Ags_Sep_Okt_Nop_Des'.split('_'),\n    weekdays: '<PERSON><PERSON>_<PERSON><PERSON>_Se<PERSON>o_Re<PERSON>_Ke<PERSON>_Jemuwah_Septu'.split('_'),\n    weekdaysShort: 'Min_Sen_Sel_Reb_Kem_Jem_Sep'.split('_'),\n    weekdaysMin: 'Mg_Sn_Sl_Rb_Km_Jm_Sp'.split('_'),\n    longDateFormat: {\n      LT: 'HH.mm',\n      LTS: 'HH.mm.ss',\n      L: 'DD/MM/YYYY',\n      LL: 'D MMMM YYYY',\n      LLL: 'D MMMM YYYY [pukul] HH.mm',\n      LLLL: 'dddd, D MMMM YYYY [pukul] HH.mm'\n    },\n    meridiemParse: /enjing|siyang|sonten|ndalu/,\n    meridiemHour: function (hour, meridiem) {\n      if (hour === 12) {\n        hour = 0;\n      }\n      if (meridiem === 'enjing') {\n        return hour;\n      } else if (meridiem === 'siyang') {\n        return hour >= 11 ? hour : hour + 12;\n      } else if (meridiem === 'sonten' || meridiem === 'ndalu') {\n        return hour + 12;\n      }\n    },\n    meridiem: function (hours, minutes, isLower) {\n      if (hours < 11) {\n        return 'enjing';\n      } else if (hours < 15) {\n        return 'siyang';\n      } else if (hours < 19) {\n        return 'sonten';\n      } else {\n        return 'ndalu';\n      }\n    },\n    calendar: {\n      sameDay: '[Dinten puniko pukul] LT',\n      nextDay: '[Mbenjang pukul] LT',\n      nextWeek: 'dddd [pukul] LT',\n      lastDay: '[Kala wingi pukul] LT',\n      lastWeek: 'dddd [kepengker pukul] LT',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: 'wonten ing %s',\n      past: '%s ingkang kepengker',\n      s: 'sawetawis detik',\n      ss: '%d detik',\n      m: 'setunggal menit',\n      mm: '%d menit',\n      h: 'setunggal jam',\n      hh: '%d jam',\n      d: 'sedinten',\n      dd: '%d dinten',\n      M: 'sewulan',\n      MM: '%d wulan',\n      y: 'setaun',\n      yy: '%d taun'\n    },\n    week: {\n      dow: 1,\n      // Monday is the first day of the week.\n      doy: 7 // The week that contains Jan 7th is the first week of the year.\n    }\n  });\n  return jv;\n});", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}