{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.operate = exports.hasLift = void 0;\nvar isFunction_1 = require(\"./isFunction\");\nfunction hasLift(source) {\n  return isFunction_1.isFunction(source === null || source === void 0 ? void 0 : source.lift);\n}\nexports.hasLift = hasLift;\nfunction operate(init) {\n  return function (source) {\n    if (hasLift(source)) {\n      return source.lift(function (liftedSource) {\n        try {\n          return init(liftedSource, this);\n        } catch (err) {\n          this.error(err);\n        }\n      });\n    }\n    throw new TypeError('Unable to lift unknown Observable type');\n  };\n}\nexports.operate = operate;\n//# sourceMappingURL=lift.js.map", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}