{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.argsArgArrayOrObject = void 0;\nvar isArray = Array.isArray;\nvar getPrototypeOf = Object.getPrototypeOf,\n  objectProto = Object.prototype,\n  getKeys = Object.keys;\nfunction argsArgArrayOrObject(args) {\n  if (args.length === 1) {\n    var first_1 = args[0];\n    if (isArray(first_1)) {\n      return {\n        args: first_1,\n        keys: null\n      };\n    }\n    if (isPOJO(first_1)) {\n      var keys = getKeys(first_1);\n      return {\n        args: keys.map(function (key) {\n          return first_1[key];\n        }),\n        keys: keys\n      };\n    }\n  }\n  return {\n    args: args,\n    keys: null\n  };\n}\nexports.argsArgArrayOrObject = argsArgArrayOrObject;\nfunction isPOJO(obj) {\n  return obj && typeof obj === 'object' && getPrototypeOf(obj) === objectProto;\n}\n//# sourceMappingURL=argsArgArrayOrObject.js.map", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}