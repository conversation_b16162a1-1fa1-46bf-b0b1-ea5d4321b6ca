{"ast": null, "code": "import { FormControl, Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"../webapi.service\";\nimport * as i5 from \"@angular/forms\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"../../../node_modules/@angular/forms/index\";\nfunction SoComponent_label_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"label\", 20);\n    i0.ɵɵtext(1, \" *format Email Error\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SoComponent_label_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"label\", 20);\n    i0.ɵɵtext(1, \" *format Mobile Error\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SoComponent_label_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"label\", 20);\n    i0.ɵɵtext(1, \"*can be 7 - 15 characters long. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SoComponent_label_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"label\", 20);\n    i0.ɵɵtext(1, \" *Password is required.\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SoComponent_label_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"label\", 20);\n    i0.ɵɵtext(1, \"*can be 7 - 15 characters long. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SoComponent_label_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"label\", 20);\n    i0.ɵɵtext(1, \" *Repeat password is required.\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SoComponent_p_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 21);\n    i0.ɵɵtext(1, \"Password does not match\");\n    i0.ɵɵelementEnd();\n  }\n}\nexport let SoComponent = /*#__PURE__*/(() => {\n  class SoComponent {\n    constructor(http, activeModal, trou, modalService, service, formBuilder) {\n      this.http = http;\n      this.activeModal = activeModal;\n      this.trou = trou;\n      this.modalService = modalService;\n      this.service = service;\n      this.formBuilder = formBuilder;\n      this.staticAlertClosed = true;\n      this.Data = service.setuserlogin();\n      this.urlserver = service.geturlservice();\n      this.Datanameuser = this.Data[0].name_user;\n      this.DataIduser = this.Data[0].id_user;\n      this.DataIdcustomer = this.Data[0].accountnum;\n      this.passwordFormGroup = this.formBuilder.group({\n        password: new FormControl('', [Validators.required, Validators.minLength(7), Validators.maxLength(15)]),\n        repeatPassword: new FormControl('', [Validators.required, Validators.minLength(7), Validators.maxLength(15)])\n      }, {\n        validator: RegistrationValidator.validate.bind(this)\n      });\n      this.registrationFormGroup = this.formBuilder.group({\n        Tel: [],\n        email: [],\n        passwordFormGroup: this.passwordFormGroup\n      });\n    }\n    ngOnInit() {}\n    test() {\n      alert(JSON.stringify(this.Data));\n    }\n    onClickRegisterCustomer() {\n      this.datauser = this.registrationFormGroup.value.edusername;\n      this.dataEmail = this.registrationFormGroup.value.email;\n      this.datatel = this.registrationFormGroup.value.Tel;\n      const datapassword = this.passwordFormGroup.value.password;\n      this.Datanameuser = this.Data[0].name_user;\n      this.DataIduser = this.Data[0].id_user;\n      this.DataIdcustomer = this.Data[0].accountnum;\n      if (this.dataEmail == '' || this.dataEmail === undefined) {\n        this.dataEmail = '%20';\n      }\n      if (this.datatel == '' || this.datatel === undefined) {\n        this.datatel = '%20';\n      }\n      const url = `${this.urlserver}update_user_customer_1/${this.DataIduser}/${this.dataEmail}/${this.datatel}/${datapassword}/${this.DataIdcustomer}`;\n      this.http.post(url, '').subscribe(res => {\n        if (res === true) {\n          alert('บันทึกรายการเสร็จสิ้น');\n          this.activeModal.close();\n        }\n      }, error => {\n        alert('เกิดปัญหาในการ Process ข้อมูล กรุณาทำรายการใหม่อีกครั้ง');\n      });\n    }\n    static {\n      this.ɵfac = function SoComponent_Factory(t) {\n        return new (t || SoComponent)(i0.ɵɵdirectiveInject(i1.HttpClient), i0.ɵɵdirectiveInject(i2.NgbActiveModal), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i2.NgbModal), i0.ɵɵdirectiveInject(i4.WebapiService), i0.ɵɵdirectiveInject(i5.FormBuilder));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: SoComponent,\n        selectors: [[\"app-so\"]],\n        decls: 38,\n        vars: 14,\n        consts: [[1, \"modal-header\"], [1, \"modal-title\"], [1, \"modal-body\"], [3, \"formGroup\"], [1, \"form-group\"], [\"for\", \"email\"], [\"class\", \"bg-alert\", 4, \"ngIf\"], [\"type\", \"text\", \"pattern\", \"^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\\\\.[a-zA-Z0-9.]+$\", \"name\", \"email\", \"placeholder\", \"Email\", \"formControlName\", \"email\", 1, \"form-control\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"Tel\"], [\"type\", \"text\", \"pattern\", \"^((\\\\\\\\+91-?)|0)?[0-9]{10}$\", \"name\", \"Tel\", \"placeholder\", \"0801234567\", \"formControlName\", \"Tel\", 1, \"form-control\", 3, \"ngModelChange\", \"ngModel\"], [1, \"form-group\", 2, \"margin-bottom\", \"5px\"], [\"type\", \"text\", \"readonly\", \"\", 1, \"form-control\", 3, \"value\"], [\"for\", \"password\"], [\"required\", \"\", \"type\", \"password\", \"name\", \"password\", \"placeholder\", \"Enter Password can be 7-15 characters long.\", \"formControlName\", \"password\", 1, \"form-control\"], [\"for\", \"repeatPassword\"], [\"required\", \"\", \"type\", \"password\", \"name\", \"repeatPassword\", \"placeholder\", \"Enter RepeatPassword can be 7-15 characters long.\", \"formControlName\", \"repeatPassword\", 1, \"form-control\"], [\"class\", \"alert  bg-danger\", 4, \"ngIf\"], [1, \"modal-footer\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-primary\", 3, \"click\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-primary\", 3, \"click\", \"disabled\"], [1, \"bg-alert\"], [1, \"alert\", \"bg-danger\"]],\n        template: function SoComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"h4\", 1);\n            i0.ɵɵtext(2, \"\\u0E22\\u0E34\\u0E19\\u0E14\\u0E35\\u0E15\\u0E49\\u0E2D\\u0E19\\u0E23\\u0E31\\u0E1A\\u0E40\\u0E02\\u0E49\\u0E32\\u0E2A\\u0E39\\u0E48\\u0E23\\u0E30\\u0E1A\\u0E1A\\u0E2A\\u0E21\\u0E32\\u0E0A\\u0E34\\u0E01 NaNo Product\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(3, \"div\", 2)(4, \"p\");\n            i0.ɵɵtext(5);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(6, \"div\", 3)(7, \"div\", 4)(8, \"div\", 4)(9, \"label\", 5);\n            i0.ɵɵtext(10, \"Email : \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(11, SoComponent_label_11_Template, 2, 0, \"label\", 6);\n            i0.ɵɵelementStart(12, \"input\", 7);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function SoComponent_Template_input_ngModelChange_12_listener($event) {\n              i0.ɵɵtwoWayBindingSet(ctx.email, $event) || (ctx.email = $event);\n              return $event;\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(13, \"label\", 8);\n            i0.ɵɵtext(14, \"Mobile : \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(15, SoComponent_label_15_Template, 2, 0, \"label\", 6);\n            i0.ɵɵelementStart(16, \"input\", 9);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function SoComponent_Template_input_ngModelChange_16_listener($event) {\n              i0.ɵɵtwoWayBindingSet(ctx.Tel, $event) || (ctx.Tel = $event);\n              return $event;\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(17, \"div\", 10)(18, \"div\", 3)(19, \"label\", 8);\n            i0.ɵɵtext(20, \"Username : \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(21, \"input\", 11);\n            i0.ɵɵelementStart(22, \"label\", 12);\n            i0.ɵɵtext(23, \"Password : \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(24, SoComponent_label_24_Template, 2, 0, \"label\", 6)(25, SoComponent_label_25_Template, 2, 0, \"label\", 6);\n            i0.ɵɵelement(26, \"input\", 13);\n            i0.ɵɵelementStart(27, \"label\", 14);\n            i0.ɵɵtext(28, \"Repeat Password : \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(29, SoComponent_label_29_Template, 2, 0, \"label\", 6)(30, SoComponent_label_30_Template, 2, 0, \"label\", 6);\n            i0.ɵɵelement(31, \"input\", 15);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵtemplate(32, SoComponent_p_32_Template, 2, 0, \"p\", 16);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(33, \"div\", 17)(34, \"button\", 18);\n            i0.ɵɵlistener(\"click\", function SoComponent_Template_button_click_34_listener() {\n              return ctx.activeModal.close();\n            });\n            i0.ɵɵtext(35, \"Close\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(36, \"button\", 19);\n            i0.ɵɵlistener(\"click\", function SoComponent_Template_button_click_36_listener() {\n              return ctx.onClickRegisterCustomer();\n            });\n            i0.ɵɵtext(37, \"Save changes\");\n            i0.ɵɵelementEnd()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(5);\n            i0.ɵɵtextInterpolate1(\" \", ctx.Datanameuser, \" \");\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"formGroup\", ctx.registrationFormGroup);\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"ngIf\", (ctx.registrationFormGroup.controls.email.errors == null ? null : ctx.registrationFormGroup.controls.email.errors.pattern) && ctx.registrationFormGroup.controls.email.touched);\n            i0.ɵɵadvance();\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.email);\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"ngIf\", (ctx.registrationFormGroup.controls.Tel.errors == null ? null : ctx.registrationFormGroup.controls.Tel.errors.pattern) && ctx.registrationFormGroup.controls.Tel.touched);\n            i0.ɵɵadvance();\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.Tel);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"formGroup\", ctx.passwordFormGroup);\n            i0.ɵɵadvance(3);\n            i0.ɵɵpropertyInterpolate(\"value\", ctx.DataIdcustomer);\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"ngIf\", ctx.passwordFormGroup.controls.password.errors && ctx.passwordFormGroup.controls.password.touched);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", (ctx.passwordFormGroup.controls.password.errors == null ? null : ctx.passwordFormGroup.controls.password.errors.required) && ctx.passwordFormGroup.controls.password.touched);\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"ngIf\", ctx.passwordFormGroup.controls.repeatPassword.errors && ctx.passwordFormGroup.controls.repeatPassword.touched);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", (ctx.passwordFormGroup.controls.repeatPassword.errors == null ? null : ctx.passwordFormGroup.controls.repeatPassword.errors.required) && ctx.passwordFormGroup.controls.repeatPassword.touched);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngIf\", ctx.passwordFormGroup.errors == null ? null : ctx.passwordFormGroup.errors.doesMatchPassword);\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"disabled\", !ctx.registrationFormGroup.valid);\n          }\n        },\n        dependencies: [i6.NgIf, i7.DefaultValueAccessor, i7.NgControlStatus, i7.NgControlStatusGroup, i7.RequiredValidator, i7.PatternValidator, i7.FormGroupDirective, i7.FormControlName],\n        styles: [\".modal-Top100[_ngcontent-%COMP%]{top:-100px}\"]\n      });\n    }\n  }\n  return SoComponent;\n})();\nexport class RegistrationValidator {\n  static validate(registrationFormGroup) {\n    let password = registrationFormGroup.controls.password.value;\n    let repeatPassword = registrationFormGroup.controls.repeatPassword.value;\n    if (repeatPassword.length <= 0) {\n      return null;\n    }\n    if (repeatPassword !== password) {\n      return {\n        doesMatchPassword: true\n      };\n    }\n    return null;\n  }\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}