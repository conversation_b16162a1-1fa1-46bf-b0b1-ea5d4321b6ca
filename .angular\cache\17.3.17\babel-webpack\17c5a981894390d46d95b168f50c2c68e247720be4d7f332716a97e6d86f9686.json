{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Mongolian [mn]\n//! author : Javkhlantugs Nyamdorj : https://github.com/javkhaanj7\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  function translate(number, withoutSuffix, key, isFuture) {\n    switch (key) {\n      case 's':\n        return withoutSuffix ? 'хэдхэн секунд' : 'хэдхэн секундын';\n      case 'ss':\n        return number + (withoutSuffix ? ' секунд' : ' секундын');\n      case 'm':\n      case 'mm':\n        return number + (withoutSuffix ? ' минут' : ' минутын');\n      case 'h':\n      case 'hh':\n        return number + (withoutSuffix ? ' цаг' : ' цагийн');\n      case 'd':\n      case 'dd':\n        return number + (withoutSuffix ? ' өдөр' : ' өдрийн');\n      case 'M':\n      case 'MM':\n        return number + (withoutSuffix ? ' сар' : ' сарын');\n      case 'y':\n      case 'yy':\n        return number + (withoutSuffix ? ' жил' : ' жилийн');\n      default:\n        return number;\n    }\n  }\n  var mn = moment.defineLocale('mn', {\n    months: 'Нэгдүгээр сар_Хоёрдугаар сар_Гуравдугаар сар_Дөрөвдүгээр сар_Тавдугаар сар_Зургадугаар сар_Долдугаар сар_Наймдугаар сар_Есдүгээр сар_Аравдугаар сар_Арван нэгдүгээр сар_Арван хоёрдугаар сар'.split('_'),\n    monthsShort: '1 сар_2 сар_3 сар_4 сар_5 сар_6 сар_7 сар_8 сар_9 сар_10 сар_11 сар_12 сар'.split('_'),\n    monthsParseExact: true,\n    weekdays: 'Ням_Даваа_Мягмар_Лхагва_Пүрэв_Баасан_Бямба'.split('_'),\n    weekdaysShort: 'Ням_Дав_Мяг_Лха_Пүр_Баа_Бям'.split('_'),\n    weekdaysMin: 'Ня_Да_Мя_Лх_Пү_Ба_Бя'.split('_'),\n    weekdaysParseExact: true,\n    longDateFormat: {\n      LT: 'HH:mm',\n      LTS: 'HH:mm:ss',\n      L: 'YYYY-MM-DD',\n      LL: 'YYYY оны MMMMын D',\n      LLL: 'YYYY оны MMMMын D HH:mm',\n      LLLL: 'dddd, YYYY оны MMMMын D HH:mm'\n    },\n    meridiemParse: /ҮӨ|ҮХ/i,\n    isPM: function (input) {\n      return input === 'ҮХ';\n    },\n    meridiem: function (hour, minute, isLower) {\n      if (hour < 12) {\n        return 'ҮӨ';\n      } else {\n        return 'ҮХ';\n      }\n    },\n    calendar: {\n      sameDay: '[Өнөөдөр] LT',\n      nextDay: '[Маргааш] LT',\n      nextWeek: '[Ирэх] dddd LT',\n      lastDay: '[Өчигдөр] LT',\n      lastWeek: '[Өнгөрсөн] dddd LT',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: '%s дараа',\n      past: '%s өмнө',\n      s: translate,\n      ss: translate,\n      m: translate,\n      mm: translate,\n      h: translate,\n      hh: translate,\n      d: translate,\n      dd: translate,\n      M: translate,\n      MM: translate,\n      y: translate,\n      yy: translate\n    },\n    dayOfMonthOrdinalParse: /\\d{1,2} өдөр/,\n    ordinal: function (number, period) {\n      switch (period) {\n        case 'd':\n        case 'D':\n        case 'DDD':\n          return number + ' өдөр';\n        default:\n          return number;\n      }\n    }\n  });\n  return mn;\n});", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}