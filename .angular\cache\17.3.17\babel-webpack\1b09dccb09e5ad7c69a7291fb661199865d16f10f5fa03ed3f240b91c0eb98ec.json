{"ast": null, "code": "import { WebapiService } from './../webapi.service';\nimport { HttpClient } from '@angular/common/http';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"./../webapi.service\";\nconst _c0 = () => [5, 10, 25];\nfunction PermissionComponent_button_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 36);\n    i0.ɵɵlistener(\"click\", function PermissionComponent_button_10_Template_button_click_0_listener() {\n      const group_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.getgroupid(group_r2.id_group_user));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const group_r2 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", group_r2.des_group, \" \");\n  }\n}\nfunction PermissionComponent_tr_26_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\", 37)(4, \"input\", 38);\n    i0.ɵɵlistener(\"click\", function PermissionComponent_tr_26_Template_input_click_4_listener($event) {\n      const item_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.clickrowstableview(item_r5.id_menu, $event.target.checked));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"td\", 37)(6, \"input\", 38);\n    i0.ɵɵlistener(\"click\", function PermissionComponent_tr_26_Template_input_click_6_listener($event) {\n      const item_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.clickrowstableexport(item_r5.id_menu, $event.target.checked));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"td\", 37)(8, \"input\", 38);\n    i0.ɵɵlistener(\"click\", function PermissionComponent_tr_26_Template_input_click_8_listener($event) {\n      const item_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.clickrowstablevaction(item_r5.id_menu, $event.target.checked));\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const item_r5 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r5.des_menu);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"checked\", item_r5.flag_view)(\"disabled\", ctx_r2.ennablecheck);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"checked\", item_r5.flag_print)(\"disabled\", ctx_r2.ennablecheck);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"checked\", item_r5.flag_action)(\"disabled\", ctx_r2.ennablecheck);\n  }\n}\nexport let PermissionComponent = /*#__PURE__*/(() => {\n  class PermissionComponent {\n    constructor(http, servive) {\n      this.http = http;\n      this.servive = servive;\n      this.ennablecheck = true;\n      this.disabled = true;\n      localStorage.removeItem('DataSOderreview');\n      localStorage.removeItem('DataSOderlist');\n      this.url = this.servive.geturlservice();\n    }\n    ngOnInit() {\n      this.getmenulist();\n      this.getusergroup();\n      this.idgroup = '';\n      this.tstese = '';\n      /*this.groupusers = this._sowebservice.getallgroup();*/\n    }\n    getusergroup() {\n      this.http.get(this.url + 'usergroup').subscribe(res => {\n        this.groupusers = res;\n      });\n    }\n    getmenulist() {\n      this.http.get('../../assets/menulist.json').subscribe(res => {\n        this.menulist = res;\n        this.countmenu = this.menulist.length;\n        /*alert(this.menulist.length);*/\n      });\n    }\n    getgroupid(value) {\n      this.idgroup = value;\n      this.ennablecheck = false;\n      this.http.post(this.url + 'find_permission', {\n        id_user_group: value\n      }).subscribe(res => {\n        if (res == '') {\n          this.getmenulist();\n        } else {\n          this.menulist = res;\n        }\n      });\n    }\n    testclick(vaule) {\n      this.tstese = vaule;\n    }\n    clickrowstableview(value, checked) {\n      const urlpostview = `${this.url}${'view_permission'}/${this.idgroup}/${value}/${checked}`;\n      this.http.post(urlpostview, '').subscribe(res => {});\n    }\n    clickrowstableexport(value, checked) {\n      const urlpostview = `${this.url}${'print_permission'}/${this.idgroup}/${value}/${checked}`;\n      this.http.post(urlpostview, '').subscribe(res => {});\n    }\n    clickrowstablevaction(value, checked) {\n      const urlpostview = `${this.url}${'action_permission'}/${this.idgroup}/${value}/${checked}`;\n      this.http.post(urlpostview, '').subscribe(res => {});\n    }\n    static {\n      this.ɵfac = function PermissionComponent_Factory(t) {\n        return new (t || PermissionComponent)(i0.ɵɵdirectiveInject(i1.HttpClient), i0.ɵɵdirectiveInject(i2.WebapiService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: PermissionComponent,\n        selectors: [[\"app-permission\"]],\n        decls: 74,\n        vars: 4,\n        consts: [[2, \"padding-top\", \"60px\"], [1, \"p-sm-1\", \"bg-secondary\", \"text-white\", \"text-center\"], [1, \"container-fluid\"], [1, \"row\"], [1, \"col-sm-3\"], [1, \"p-sm-1\", \"bg-secondary\", \"text-white\", \"font-weight-light\"], [1, \"btn-group-vertical\", \"list-group\"], [\"style\", \"text-align: left; margin-top: 4px; padding: 10px;\", \"type\", \"button\", \"class\", \"btn btn-outline-success \", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"col-sm-9\"], [1, \"table\", \"table-striped\"], [2, \"width\", \"40%\"], [1, \"text-center\", 2, \"width\", \"20%\"], [4, \"ngFor\", \"ngForOf\"], [\"colspan\", \"4\"], [3, \"rowsOnPageSet\"], [1, \"modal-footer\", \"h-auto\"], [\"id\", \"exampleModal\", \"tabindex\", \"-1\", \"role\", \"dialog\", \"aria-labelledby\", \"exampleModalLabel\", \"aria-hidden\", \"true\", 1, \"modal\", \"fade\"], [\"role\", \"document\", 1, \"modal-dialog\"], [1, \"modal-content\"], [1, \"modal-header\"], [\"id\", \"exampleModalLabel\", 1, \"modal-title\"], [\"type\", \"button\", \"data-dismiss\", \"modal\", \"aria-label\", \"Close\", 1, \"close\"], [\"aria-hidden\", \"true\"], [1, \"modal-body\"], [1, \"form-group\"], [\"type\", \"text\", \"id\", \"validationTooltip01\", \"placeholder\", \"User Name\", \"required\", \"\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"id\", \"validationTooltip01\", \"placeholder\", \"Email Address\", \"required\", \"\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"id\", \"validationTooltip01\", \"placeholder\", \"Mobile\", \"required\", \"\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"id\", \"validationTooltip01\", \"placeholder\", \"Log In Name\", \"required\", \"\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"id\", \"validationTooltip01\", \"placeholder\", \"Password\", \"required\", \"\", 1, \"form-control\", \"form-control-sm\"], [1, \"modal-footer\"], [\"type\", \"button\", \"data-dismiss\", \"modal\", 1, \"btn\", \"btn-secondary\", \"btn-sm\"], [\"type\", \"button\", 1, \"btn\", \"btn-success\", \"btn-sm\"], [\"id\", \"exampleModal1\", \"tabindex\", \"-1\", \"role\", \"dialog\", \"aria-labelledby\", \"exampleModalLabel\", \"aria-hidden\", \"true\", 1, \"modal\", \"fade\"], [1, \"modal-footer\", \"fixed-bottom\"], [\"type\", \"button\", 1, \"btn\", \"btn-danger\", \"btn-sm\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-success\", 2, \"text-align\", \"left\", \"margin-top\", \"4px\", \"padding\", \"10px\", 3, \"click\"], [1, \"text-center\"], [\"type\", \"checkbox\", 3, \"click\", \"checked\", \"disabled\"]],\n        template: function PermissionComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelement(0, \"app-topmenu\");\n            i0.ɵɵelementStart(1, \"section\", 0)(2, \"h5\", 1);\n            i0.ɵɵtext(3, \"User Permission\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(4, \"div\", 2)(5, \"div\", 3)(6, \"div\", 4)(7, \"h5\", 5);\n            i0.ɵɵtext(8, \"User Group\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(9, \"div\", 6);\n            i0.ɵɵtemplate(10, PermissionComponent_button_10_Template, 2, 1, \"button\", 7);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(11, \"div\", 8)(12, \"h5\", 5);\n            i0.ɵɵtext(13, \"Permission Setting\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(14, \"table\", 9)(15, \"thead\")(16, \"tr\")(17, \"th\", 10);\n            i0.ɵɵtext(18, \" Page/Menu \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(19, \"th\", 11);\n            i0.ɵɵtext(20, \" View \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(21, \"th\", 11);\n            i0.ɵɵtext(22, \" Export \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(23, \"th\", 11);\n            i0.ɵɵtext(24, \" Action \");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(25, \"tbody\");\n            i0.ɵɵtemplate(26, PermissionComponent_tr_26_Template, 9, 7, \"tr\", 12);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(27, \"tfoot\")(28, \"tr\")(29, \"td\", 13);\n            i0.ɵɵelement(30, \"mfBootstrapPaginator\", 14);\n            i0.ɵɵelementEnd()()()()()()();\n            i0.ɵɵelement(31, \"div\", 15);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(32, \"section\")(33, \"div\", 16)(34, \"div\", 17)(35, \"div\", 18)(36, \"div\", 19)(37, \"h5\", 20);\n            i0.ɵɵtext(38, \"User Info\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(39, \"button\", 21)(40, \"span\", 22);\n            i0.ɵɵtext(41, \"\\u00D7\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(42, \"div\", 23)(43, \"form\")(44, \"div\", 24);\n            i0.ɵɵelement(45, \"input\", 25);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(46, \"div\", 24);\n            i0.ɵɵelement(47, \"input\", 26);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(48, \"div\", 24);\n            i0.ɵɵelement(49, \"input\", 27);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(50, \"div\", 24);\n            i0.ɵɵelement(51, \"input\", 28);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(52, \"div\", 24);\n            i0.ɵɵelement(53, \"input\", 29);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(54, \"div\", 30)(55, \"button\", 31);\n            i0.ɵɵtext(56, \"Close\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(57, \"button\", 32);\n            i0.ɵɵtext(58, \"Apply\");\n            i0.ɵɵelementEnd()()()()()();\n            i0.ɵɵelementStart(59, \"section\")(60, \"div\", 33)(61, \"div\", 17)(62, \"div\", 18)(63, \"div\", 19)(64, \"h5\", 20);\n            i0.ɵɵtext(65, \"Delete Data Confirmation\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(66, \"button\", 21)(67, \"span\", 22);\n            i0.ɵɵtext(68, \"\\u00D7\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(69, \"div\", 34)(70, \"button\", 31);\n            i0.ɵɵtext(71, \"Close\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(72, \"button\", 35);\n            i0.ɵɵtext(73, \"Delete\");\n            i0.ɵɵelementEnd()()()()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(10);\n            i0.ɵɵproperty(\"ngForOf\", ctx.groupusers);\n            i0.ɵɵadvance(16);\n            i0.ɵɵproperty(\"ngForOf\", ctx.menulist);\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"rowsOnPageSet\", i0.ɵɵpureFunction0(3, _c0));\n          }\n        }\n      });\n    }\n  }\n  return PermissionComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}