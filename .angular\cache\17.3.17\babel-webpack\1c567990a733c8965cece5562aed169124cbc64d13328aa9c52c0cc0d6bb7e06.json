{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Chinese (Macau) [zh-mo]\n//! author : Ben : https://github.com/ben-lin\n//! author : <PERSON> : https://github.com/hehachris\n//! author : <PERSON> : https://github.com/le0tan\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  var zhMo = moment.defineLocale('zh-mo', {\n    months: '一月_二月_三月_四月_五月_六月_七月_八月_九月_十月_十一月_十二月'.split('_'),\n    monthsShort: '1月_2月_3月_4月_5月_6月_7月_8月_9月_10月_11月_12月'.split('_'),\n    weekdays: '星期日_星期一_星期二_星期三_星期四_星期五_星期六'.split('_'),\n    weekdaysShort: '週日_週一_週二_週三_週四_週五_週六'.split('_'),\n    weekdaysMin: '日_一_二_三_四_五_六'.split('_'),\n    longDateFormat: {\n      LT: 'HH:mm',\n      LTS: 'HH:mm:ss',\n      L: 'DD/MM/YYYY',\n      LL: 'YYYY年M月D日',\n      LLL: 'YYYY年M月D日 HH:mm',\n      LLLL: 'YYYY年M月D日dddd HH:mm',\n      l: 'D/M/YYYY',\n      ll: 'YYYY年M月D日',\n      lll: 'YYYY年M月D日 HH:mm',\n      llll: 'YYYY年M月D日dddd HH:mm'\n    },\n    meridiemParse: /凌晨|早上|上午|中午|下午|晚上/,\n    meridiemHour: function (hour, meridiem) {\n      if (hour === 12) {\n        hour = 0;\n      }\n      if (meridiem === '凌晨' || meridiem === '早上' || meridiem === '上午') {\n        return hour;\n      } else if (meridiem === '中午') {\n        return hour >= 11 ? hour : hour + 12;\n      } else if (meridiem === '下午' || meridiem === '晚上') {\n        return hour + 12;\n      }\n    },\n    meridiem: function (hour, minute, isLower) {\n      var hm = hour * 100 + minute;\n      if (hm < 600) {\n        return '凌晨';\n      } else if (hm < 900) {\n        return '早上';\n      } else if (hm < 1130) {\n        return '上午';\n      } else if (hm < 1230) {\n        return '中午';\n      } else if (hm < 1800) {\n        return '下午';\n      } else {\n        return '晚上';\n      }\n    },\n    calendar: {\n      sameDay: '[今天] LT',\n      nextDay: '[明天] LT',\n      nextWeek: '[下]dddd LT',\n      lastDay: '[昨天] LT',\n      lastWeek: '[上]dddd LT',\n      sameElse: 'L'\n    },\n    dayOfMonthOrdinalParse: /\\d{1,2}(日|月|週)/,\n    ordinal: function (number, period) {\n      switch (period) {\n        case 'd':\n        case 'D':\n        case 'DDD':\n          return number + '日';\n        case 'M':\n          return number + '月';\n        case 'w':\n        case 'W':\n          return number + '週';\n        default:\n          return number;\n      }\n    },\n    relativeTime: {\n      future: '%s內',\n      past: '%s前',\n      s: '幾秒',\n      ss: '%d 秒',\n      m: '1 分鐘',\n      mm: '%d 分鐘',\n      h: '1 小時',\n      hh: '%d 小時',\n      d: '1 天',\n      dd: '%d 天',\n      M: '1 個月',\n      MM: '%d 個月',\n      y: '1 年',\n      yy: '%d 年'\n    }\n  });\n  return zhMo;\n});", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}