{"ast": null, "code": "import html2canvas from 'html2canvas';\nimport * as jspdf from 'jspdf';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"../webapi.service\";\nimport * as i3 from \"@angular/router\";\nconst _c0 = [\"content\"];\nconst _c1 = a0 => ({\n  \"margin-top\": a0\n});\nconst _c2 = a0 => ({\n  \"display\": a0\n});\nconst _c3 = a0 => ({\n  \"color\": a0\n});\nconst _c4 = a0 => ({\n  \"margin-bottom\": a0\n});\nfunction PDFprintComponent_div_23_table_1_tr_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\", 64)(1, \"div\", 65)(2, \"table\")(3, \"tr\")(4, \"td\", 66);\n    i0.ɵɵtext(5, \" \\u0E02\\u0E32\\u0E22\\u0E43\\u0E2B\\u0E49 : \");\n    i0.ɵɵelement(6, \"br\");\n    i0.ɵɵtext(7, \" Soid To. \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"td\", 67);\n    i0.ɵɵtext(9);\n    i0.ɵɵelement(10, \"br\");\n    i0.ɵɵtext(11);\n    i0.ɵɵelement(12, \"br\")(13, \"br\");\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(15, \"div\", 68)(16, \"table\")(17, \"tr\")(18, \"td\", 69);\n    i0.ɵɵtext(19, \" \\u0E2A\\u0E16\\u0E32\\u0E19\\u0E17\\u0E35\\u0E48\\u0E2A\\u0E48\\u0E07 : \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"td\", 70);\n    i0.ɵɵtext(21);\n    i0.ɵɵelement(22, \"br\")(23, \"br\");\n    i0.ɵɵtext(24);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const item_r1 = ctx.$implicit;\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate1(\" \", item_r1.nameINVherder, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", item_r1.addressINV, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", item_r1.regnum, \" \");\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\" \", item_r1.addressDEL, \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \\u0E40\\u0E1A\\u0E2D\\u0E23\\u0E4C\\u0E42\\u0E17\\u0E23\\u0E25\\u0E39\\u0E01\\u0E04\\u0E49\\u0E32 : \", item_r1.telnumber, \" \");\n  }\n}\nfunction PDFprintComponent_div_23_table_1_tr_47_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\", 71)(1, \"td\", 47);\n    i0.ɵɵtext(2);\n    i0.ɵɵelement(3, \"br\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\", 47);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\", 47);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\", 47);\n    i0.ɵɵtext(10);\n    i0.ɵɵpipe(11, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"td\", 47);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"td\", 48);\n    i0.ɵɵtext(15);\n    i0.ɵɵpipe(16, \"date\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const itemX_r2 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(itemX_r2.salesid);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(itemX_r2.orderaccount);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(itemX_r2.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(itemX_r2.paymenttype);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(11, 7, itemX_r2.duedate, \"dd/MM/yyyy\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(itemX_r2.invoiceid);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(16, 10, itemX_r2.invoicedate, \"dd/MM/yyyy\"));\n  }\n}\nfunction PDFprintComponent_div_23_table_1_tr_91_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\", 71)(1, \"td\", 72);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\", 47);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\", 73);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\", 74);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\", 75);\n    i0.ɵɵtext(10);\n    i0.ɵɵpipe(11, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"td\", 75);\n    i0.ɵɵtext(13);\n    i0.ɵɵpipe(14, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"td\", 76);\n    i0.ɵɵpipe(16, \"number\");\n    i0.ɵɵtext(17);\n    i0.ɵɵpipe(18, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"td\", 76);\n    i0.ɵɵpipe(20, \"number\");\n    i0.ɵɵtext(21);\n    i0.ɵɵpipe(22, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"td\", 76);\n    i0.ɵɵpipe(24, \"number\");\n    i0.ɵɵelementStart(25, \"sup\");\n    i0.ɵɵtext(26);\n    i0.ɵɵpipe(27, \"number\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(28, \"td\", 77);\n    i0.ɵɵtext(29);\n    i0.ɵɵpipe(30, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"td\", 78);\n    i0.ɵɵtext(32);\n    i0.ɵɵpipe(33, \"number\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const x_r3 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(42, _c3, ctx_r3.getColor(x_r3.Number)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(x_r3.Number);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(x_r3.PO);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", x_r3.itemid, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(x_r3.productname);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(11, 15, x_r3.qtyline, \"1.0-0\"), \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(14, 18, x_r3.salesprice, \"1.2-2\"), \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(44, _c3, ctx_r3.getColorivz(i0.ɵɵpipeBind2(16, 21, x_r3.ivz_percent1_ct, \"1.0-0\"))));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(18, 24, x_r3.ivz_percent1_ct));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(46, _c3, ctx_r3.getColorivz(i0.ɵɵpipeBind2(20, 26, x_r3.ivz_percent2_ct, \"1.0-0\"))));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(22, 29, x_r3.ivz_percent2_ct));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(48, _c3, ctx_r3.getColorivz(i0.ɵɵpipeBind2(24, 31, x_r3.ivz_percent3_ct, \"1.0-0\"))));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(27, 34, x_r3.ivz_percent3_ct));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(30, 36, x_r3.totalTH, \"1.3-3\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(33, 39, x_r3.lineamountmst, \"1.2-2\"));\n  }\n}\nfunction PDFprintComponent_div_23_table_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"table\", 15)(1, \"tr\")(2, \"td\", 16)(3, \"table\", 17)(4, \"td\", 18)(5, \"strong\", 19);\n    i0.ɵɵtext(6, \"\\u0E15\\u0E49\\u0E19\\u0E09\\u0E1A\\u0E31\\u0E1A\\u0E43\\u0E1A\\u0E2A\\u0E48\\u0E07\\u0E02\\u0E2D\\u0E07\\u0E0A\\u0E31\\u0E48\\u0E27\\u0E04\\u0E23\\u0E32\\u0E27\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\", 20);\n    i0.ɵɵtext(8, \"ORIGINAL DELIVERY BILL\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(9, \"div\", 21);\n    i0.ɵɵelement(10, \"img\", 22);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(11, \"tr\")(12, \"td\")(13, \"div\", 23)(14, \"div\", 24);\n    i0.ɵɵtext(15, \" \\u0E2A\\u0E33\\u0E19\\u0E31\\u0E01\\u0E07\\u0E32\\u0E19\\u0E43\\u0E2B\\u0E0D\\u0E48 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"table\", 25)(17, \"tbody\");\n    i0.ɵɵtemplate(18, PDFprintComponent_div_23_table_1_tr_18_Template, 25, 5, \"tr\", 26);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"div\", 27)(20, \"table\", 28)(21, \"tbody\")(22, \"tr\", 29)(23, \"td\", 30);\n    i0.ɵɵtext(24, \"\\u0E23\\u0E2B\\u0E31\\u0E2A\\u0E25\\u0E39\\u0E01\\u0E04\\u0E49\\u0E32\");\n    i0.ɵɵelement(25, \"br\");\n    i0.ɵɵtext(26, \"Customer Code\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"td\", 31);\n    i0.ɵɵtext(28, \"\\u0E1E\\u0E19\\u0E31\\u0E01\\u0E07\\u0E32\\u0E19\\u0E02\\u0E32\\u0E22\");\n    i0.ɵɵelement(29, \"br\");\n    i0.ɵɵtext(30, \"Salesman\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"td\", 32);\n    i0.ɵɵtext(32, \"\\u0E40\\u0E07\\u0E37\\u0E48\\u0E2D\\u0E19\\u0E44\\u0E02\\u0E01\\u0E32\\u0E23\\u0E0A\\u0E33\\u0E23\\u0E30\\u0E40\\u0E07\\u0E34\\u0E19\");\n    i0.ɵɵelement(33, \"br\");\n    i0.ɵɵtext(34, \"Payment Condtion\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(35, \"td\", 33);\n    i0.ɵɵtext(36, \"\\u0E27\\u0E31\\u0E19\\u0E04\\u0E23\\u0E1A\\u0E01\\u0E33\\u0E2B\\u0E19\\u0E14\");\n    i0.ɵɵelement(37, \"br\");\n    i0.ɵɵtext(38, \"Due Date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(39, \"td\", 33);\n    i0.ɵɵtext(40, \"\\u0E40\\u0E25\\u0E02\\u0E17\\u0E35\\u0E48\");\n    i0.ɵɵelement(41, \"br\");\n    i0.ɵɵtext(42, \"No.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(43, \"td\", 34);\n    i0.ɵɵtext(44, \"\\u0E27\\u0E31\\u0E19\\u0E17\\u0E35\\u0E48\");\n    i0.ɵɵelement(45, \"br\");\n    i0.ɵɵtext(46, \"Date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(47, PDFprintComponent_div_23_table_1_tr_47_Template, 17, 13, \"tr\", 35);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(48, \"div\", 36)(49, \"table\", 37)(50, \"thead\")(51, \"tr\", 38)(52, \"td\", 39);\n    i0.ɵɵtext(53, \"\\u0E25\\u0E33\\u0E14\\u0E31\\u0E1A\");\n    i0.ɵɵelement(54, \"br\");\n    i0.ɵɵtext(55, \"Itme\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(56, \"td\", 40);\n    i0.ɵɵtext(57, \"\\u0E40\\u0E25\\u0E02\\u0E17\\u0E35\\u0E48\\u0E43\\u0E1A\\u0E2A\\u0E31\\u0E48\\u0E07\");\n    i0.ɵɵelement(58, \"br\");\n    i0.ɵɵtext(59, \"\\u0E2A\\u0E34\\u0E19\\u0E04\\u0E49\\u0E32\");\n    i0.ɵɵelement(60, \"br\");\n    i0.ɵɵtext(61, \"P.O.No.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(62, \"td\", 41);\n    i0.ɵɵtext(63, \"\\u0E23\\u0E2B\\u0E31\\u0E2A\\u0E2A\\u0E34\\u0E19\\u0E04\\u0E49\\u0E32\");\n    i0.ɵɵelement(64, \"br\");\n    i0.ɵɵtext(65, \"Part Code\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(66, \"td\", 42);\n    i0.ɵɵtext(67, \"\\u0E23\\u0E32\\u0E22\\u0E01\\u0E32\\u0E23\");\n    i0.ɵɵelement(68, \"br\");\n    i0.ɵɵtext(69, \"Description\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(70, \"td\", 43);\n    i0.ɵɵtext(71, \"\\u0E08\\u0E33\\u0E19\\u0E27\\u0E19\");\n    i0.ɵɵelement(72, \"br\");\n    i0.ɵɵtext(73, \"Quantity\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(74, \"td\", 43);\n    i0.ɵɵtext(75, \"\\u0E2B\\u0E19\\u0E48\\u0E27\\u0E22\\u0E25\\u0E30\");\n    i0.ɵɵelement(76, \"br\");\n    i0.ɵɵtext(77, \"Unit Price\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(78, \"td\", 44);\n    i0.ɵɵtext(79, \"\\u0E2A\\u0E48\\u0E27\\u0E19\\u0E25\\u0E14\");\n    i0.ɵɵelement(80, \"br\");\n    i0.ɵɵtext(81, \"Disc %\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(82, \"td\", 42);\n    i0.ɵɵtext(83, \"\\u0E23\\u0E32\\u0E04\\u0E32\\u0E2A\\u0E38\\u0E17\\u0E18\\u0E34\");\n    i0.ɵɵelement(84, \"br\");\n    i0.ɵɵtext(85, \"\\u0E15\\u0E48\\u0E2D\\u0E0A\\u0E34\\u0E49\\u0E19\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(86, \"td\", 42);\n    i0.ɵɵtext(87, \"\\u0E08\\u0E33\\u0E19\\u0E27\\u0E19\\u0E40\\u0E07\\u0E34\\u0E19\");\n    i0.ɵɵelement(88, \"br\");\n    i0.ɵɵtext(89, \"Amount\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(90, \"tbody\", 45);\n    i0.ɵɵtemplate(91, PDFprintComponent_div_23_table_1_tr_91_Template, 34, 50, \"tr\", 35);\n    i0.ɵɵelementStart(92, \"tr\", 46);\n    i0.ɵɵelement(93, \"td\", 47)(94, \"td\", 47)(95, \"td\", 47)(96, \"td\", 47)(97, \"td\", 47)(98, \"td\", 47)(99, \"td\", 47)(100, \"td\", 47)(101, \"td\", 47)(102, \"td\", 47)(103, \"td\", 48);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(104, \"tr\", 46);\n    i0.ɵɵelement(105, \"td\", 47)(106, \"td\", 47)(107, \"td\", 47)(108, \"td\", 47)(109, \"td\", 47)(110, \"td\", 47)(111, \"td\", 47)(112, \"td\", 47)(113, \"td\", 47)(114, \"td\", 47)(115, \"td\", 48);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(116, \"tr\", 46);\n    i0.ɵɵelement(117, \"td\", 47)(118, \"td\", 47)(119, \"td\", 47)(120, \"td\", 47)(121, \"td\", 47)(122, \"td\", 47)(123, \"td\", 47)(124, \"td\", 47)(125, \"td\", 47)(126, \"td\", 47)(127, \"td\", 48);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(128, \"tr\", 46);\n    i0.ɵɵelement(129, \"td\", 47)(130, \"td\", 47)(131, \"td\", 47)(132, \"td\", 47)(133, \"td\", 47)(134, \"td\", 47)(135, \"td\", 47)(136, \"td\", 47)(137, \"td\", 47)(138, \"td\", 47)(139, \"td\", 48);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(140, \"tr\", 46);\n    i0.ɵɵelement(141, \"td\", 47)(142, \"td\", 47)(143, \"td\", 47)(144, \"td\", 47)(145, \"td\", 47)(146, \"td\", 47)(147, \"td\", 47)(148, \"td\", 47)(149, \"td\", 47)(150, \"td\", 47)(151, \"td\", 48);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(152, \"tr\", 46);\n    i0.ɵɵelement(153, \"td\", 47)(154, \"td\", 47)(155, \"td\", 47)(156, \"td\", 47)(157, \"td\", 47)(158, \"td\", 47)(159, \"td\", 47)(160, \"td\", 47)(161, \"td\", 47)(162, \"td\", 47)(163, \"td\", 48);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(164, \"tr\", 46);\n    i0.ɵɵelement(165, \"td\", 47)(166, \"td\", 47)(167, \"td\", 47)(168, \"td\", 47)(169, \"td\", 47)(170, \"td\", 47)(171, \"td\", 47)(172, \"td\", 47)(173, \"td\", 47)(174, \"td\", 47)(175, \"td\", 48);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(176, \"tr\", 49)(177, \"td\", 50)(178, \"div\", 51);\n    i0.ɵɵtext(179);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(180, \"td\", 52);\n    i0.ɵɵtext(181, \"\\u0E08\\u0E33\\u0E19\\u0E27\\u0E19\\u0E40\\u0E07\\u0E34\\u0E19\\u0E23\\u0E27\\u0E21\\u0E17\\u0E31\\u0E49\\u0E07\\u0E2A\\u0E34\\u0E49\\u0E19\");\n    i0.ɵɵelement(182, \"br\");\n    i0.ɵɵtext(183, \"(Grand Total Amount)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(184, \"td\", 53)(185, \"div\", 54);\n    i0.ɵɵtext(186);\n    i0.ɵɵpipe(187, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(188, \"div\", 54);\n    i0.ɵɵtext(189);\n    i0.ɵɵpipe(190, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(191, \"div\", 55);\n    i0.ɵɵtext(192);\n    i0.ɵɵpipe(193, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(194, \"div\", 56);\n    i0.ɵɵtext(195, \" sssss \");\n    i0.ɵɵelementEnd()()()()()();\n    i0.ɵɵelementStart(196, \"div\", 57)(197, \"table\", 58)(198, \"tbody\")(199, \"tr\", 59)(200, \"td\", 60);\n    i0.ɵɵelement(201, \"br\")(202, \"br\")(203, \"br\");\n    i0.ɵɵtext(204, \".................................................................................\");\n    i0.ɵɵelement(205, \"br\");\n    i0.ɵɵtext(206, \"\\u0E1C\\u0E39\\u0E49\\u0E2D\\u0E19\\u0E38\\u0E21\\u0E31\\u0E15\\u0E34 Autherized\");\n    i0.ɵɵelement(207, \"br\")(208, \"br\");\n    i0.ɵɵtext(209, \"\\u0E27\\u0E31\\u0E19\\u0E17\\u0E35\\u0E48 Date ....................................\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(210, \"td\", 61)(211, \"div\", 62);\n    i0.ɵɵtext(212, \"\\u0E1A\\u0E23\\u0E34\\u0E29\\u0E31\\u0E17\\u0E2F \\u0E02\\u0E2D\\u0E2A\\u0E07\\u0E27\\u0E19\\u0E2A\\u0E34\\u0E17\\u0E18\\u0E34\\u0E4C\\u0E43\\u0E19\\u0E01\\u0E32\\u0E23\\u0E40\\u0E1B\\u0E25\\u0E37\\u0E48\\u0E22\\u0E19\\u0E2A\\u0E34\\u0E19\\u0E04\\u0E49\\u0E32 \");\n    i0.ɵɵelement(213, \"br\");\n    i0.ɵɵtext(214, \"\\u0E20\\u0E32\\u0E22\\u0E43\\u0E19 7 \\u0E27\\u0E31\\u0E19 \\u0E19\\u0E31\\u0E1A\\u0E08\\u0E32\\u0E01\\u0E27\\u0E31\\u0E19\\u0E17\\u0E35\\u0E48\\u0E2A\\u0E48\\u0E07\\u0E2A\\u0E34\\u0E19\\u0E04\\u0E49\\u0E32\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(215, \"td\", 63);\n    i0.ɵɵelement(216, \"br\")(217, \"br\")(218, \"br\");\n    i0.ɵɵtext(219, \".................................................................................\");\n    i0.ɵɵelement(220, \"br\");\n    i0.ɵɵtext(221, \"\\u0E1C\\u0E39\\u0E49\\u0E23\\u0E31\\u0E1A\\u0E02\\u0E2D\\u0E07 Receive Signature\");\n    i0.ɵɵelement(222, \"br\")(223, \"br\");\n    i0.ɵɵtext(224, \"\\u0E27\\u0E31\\u0E19\\u0E17\\u0E35\\u0E48 Date ....................................\");\n    i0.ɵɵelementEnd()()()()()()()()();\n  }\n  if (rf & 2) {\n    const y_r5 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(22, _c1, ctx_r3.gettop2(ctx_r3.pageHeightmin)));\n    i0.ɵɵadvance(15);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.DataHerderINV);\n    i0.ɵɵadvance(29);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.DataHerderINV);\n    i0.ɵɵadvance(44);\n    i0.ɵɵproperty(\"ngForOf\", y_r5.data);\n    i0.ɵɵadvance(87);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(24, _c2, ctx_r3.getnumpage(y_r5.page)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"(\", ctx_r3.SumtotalTH, \")\");\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(26, _c2, ctx_r3.getnumpage(y_r5.page)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(187, 13, ctx_r3.Sumtotal, \"1.2-2\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(28, _c2, ctx_r3.getnumpage(y_r5.page)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(190, 16, ctx_r3.taxnew, \"1.2-2\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(30, _c2, ctx_r3.getnumpage(y_r5.page)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(193, 19, ctx_r3.Sumtotal, \"1.2-2\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(32, _c2, ctx_r3.getnumpage(y_r5.page)));\n  }\n}\nfunction PDFprintComponent_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, PDFprintComponent_div_23_table_1_Template, 225, 34, \"table\", 14);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.dataprint);\n  }\n}\nfunction PDFprintComponent_div_24_table_1_tr_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\", 64)(1, \"div\", 65)(2, \"table\")(3, \"tr\")(4, \"td\", 66);\n    i0.ɵɵtext(5, \" \\u0E02\\u0E32\\u0E22\\u0E43\\u0E2B\\u0E49 : \");\n    i0.ɵɵelement(6, \"br\");\n    i0.ɵɵtext(7, \" Soid To. \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"td\", 67);\n    i0.ɵɵtext(9);\n    i0.ɵɵelement(10, \"br\");\n    i0.ɵɵtext(11);\n    i0.ɵɵelement(12, \"br\")(13, \"br\");\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(15, \"div\", 68)(16, \"table\")(17, \"tr\")(18, \"td\", 69);\n    i0.ɵɵtext(19, \" \\u0E2A\\u0E16\\u0E32\\u0E19\\u0E17\\u0E35\\u0E48\\u0E2A\\u0E48\\u0E07 : \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"td\", 70);\n    i0.ɵɵtext(21);\n    i0.ɵɵelement(22, \"br\")(23, \"br\");\n    i0.ɵɵtext(24);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const item_r6 = ctx.$implicit;\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate1(\" \", item_r6.nameINVherder, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", item_r6.addressINV, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", item_r6.regnum, \" \");\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\" \", item_r6.addressDEL, \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \\u0E40\\u0E1A\\u0E2D\\u0E23\\u0E4C\\u0E42\\u0E17\\u0E23\\u0E25\\u0E39\\u0E01\\u0E04\\u0E49\\u0E32 : \", item_r6.telnumber, \" \");\n  }\n}\nfunction PDFprintComponent_div_24_table_1_tr_52_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\", 71)(1, \"td\", 47);\n    i0.ɵɵtext(2);\n    i0.ɵɵelement(3, \"br\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\", 47);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\", 47);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\", 47);\n    i0.ɵɵtext(10);\n    i0.ɵɵpipe(11, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"td\", 47);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"td\", 48);\n    i0.ɵɵtext(15);\n    i0.ɵɵpipe(16, \"date\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const itemX_r7 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(itemX_r7.salesid);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(itemX_r7.orderaccount);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(itemX_r7.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(itemX_r7.paymenttype);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(11, 7, itemX_r7.duedate, \"dd/MM/yyyy\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(itemX_r7.invoiceid);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(16, 10, itemX_r7.invoicedate, \"dd/MM/yyyy\"));\n  }\n}\nfunction PDFprintComponent_div_24_table_1_tr_96_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\", 71)(1, \"td\", 72);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\", 47);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\", 73);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\", 74);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\", 75);\n    i0.ɵɵtext(10);\n    i0.ɵɵpipe(11, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"td\", 75);\n    i0.ɵɵtext(13);\n    i0.ɵɵpipe(14, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"td\", 76);\n    i0.ɵɵpipe(16, \"number\");\n    i0.ɵɵtext(17);\n    i0.ɵɵpipe(18, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"td\", 76);\n    i0.ɵɵpipe(20, \"number\");\n    i0.ɵɵtext(21);\n    i0.ɵɵpipe(22, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"td\", 76);\n    i0.ɵɵpipe(24, \"number\");\n    i0.ɵɵelementStart(25, \"sup\");\n    i0.ɵɵtext(26);\n    i0.ɵɵpipe(27, \"number\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(28, \"td\", 77);\n    i0.ɵɵtext(29);\n    i0.ɵɵpipe(30, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"td\", 78);\n    i0.ɵɵtext(32);\n    i0.ɵɵpipe(33, \"number\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const x_r8 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(42, _c3, ctx_r3.getColor(x_r8.Number)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(x_r8.Number);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(x_r8.PO);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", x_r8.itemid, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(x_r8.productname);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(11, 15, x_r8.qtyline, \"1.0-0\"), \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(14, 18, x_r8.salesprice, \"1.2-2\"), \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(44, _c3, ctx_r3.getColorivz(i0.ɵɵpipeBind2(16, 21, x_r8.ivz_percent1_ct, \"1.0-0\"))));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(18, 24, x_r8.ivz_percent1_ct));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(46, _c3, ctx_r3.getColorivz(i0.ɵɵpipeBind2(20, 26, x_r8.ivz_percent2_ct, \"1.0-0\"))));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(22, 29, x_r8.ivz_percent2_ct));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(48, _c3, ctx_r3.getColorivz(i0.ɵɵpipeBind2(24, 31, x_r8.ivz_percent3_ct, \"1.0-0\"))));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(27, 34, x_r8.ivz_percent3_ct));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(30, 36, x_r8.totalTH, \"1.3-3\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(33, 39, x_r8.lineamountmst, \"1.2-2\"));\n  }\n}\nfunction PDFprintComponent_div_24_table_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"table\", 80)(1, \"tr\")(2, \"td\")(3, \"table\", 81)(4, \"td\", 18);\n    i0.ɵɵelement(5, \"img\", 82);\n    i0.ɵɵelementStart(6, \"strong\", 19);\n    i0.ɵɵtext(7, \"\\u0E2A\\u0E33\\u0E40\\u0E19\\u0E32\\u0E43\\u0E1A\\u0E01\\u0E33\\u0E01\\u0E31\\u0E1A\\u0E20\\u0E32\\u0E29\\u0E35 / \\u0E43\\u0E1A\\u0E2A\\u0E48\\u0E07\\u0E2A\\u0E34\\u0E19\\u0E04\\u0E49\\u0E32 / \\u0E43\\u0E1A\\u0E41\\u0E08\\u0E49\\u0E07\\u0E2B\\u0E19\\u0E35\\u0E49\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\", 83);\n    i0.ɵɵtext(9, \"COPY TAX INVOICE / DELIVERY ORDER / INVOICE\");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(10, \"tr\")(11, \"td\")(12, \"div\", 84)(13, \"table\", 85)(14, \"tr\")(15, \"td\", 86);\n    i0.ɵɵtext(16, \" \\u0E2A\\u0E33\\u0E19\\u0E31\\u0E01\\u0E07\\u0E32\\u0E19\\u0E43\\u0E2B\\u0E0D\\u0E48\");\n    i0.ɵɵelement(17, \"br\");\n    i0.ɵɵtext(18, \" \\u0E40\\u0E25\\u0E02\\u0E1B\\u0E23\\u0E30\\u0E08\\u0E33\\u0E15\\u0E31\\u0E27\\u0E1C\\u0E39\\u0E49\\u0E40\\u0E2A\\u0E35\\u0E22\\u0E20\\u0E32\\u0E29\\u0E35\\u0E2D\\u0E32\\u0E01\\u0E23 0105529040739 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"td\", 87);\n    i0.ɵɵtext(20, \" (\\u0E44\\u0E21\\u0E48\\u0E43\\u0E0A\\u0E48\\u0E43\\u0E1A\\u0E01\\u0E33\\u0E01\\u0E31\\u0E1A\\u0E20\\u0E32\\u0E29\\u0E35) \");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(21, \"table\", 25)(22, \"tbody\");\n    i0.ɵɵtemplate(23, PDFprintComponent_div_24_table_1_tr_23_Template, 25, 5, \"tr\", 26);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(24, \"div\", 88)(25, \"table\", 28)(26, \"tbody\")(27, \"tr\", 29)(28, \"td\", 30);\n    i0.ɵɵtext(29, \"\\u0E23\\u0E2B\\u0E31\\u0E2A\\u0E25\\u0E39\\u0E01\\u0E04\\u0E49\\u0E32\");\n    i0.ɵɵelement(30, \"br\");\n    i0.ɵɵtext(31, \"Customer Code\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"td\", 31);\n    i0.ɵɵtext(33, \"\\u0E1E\\u0E19\\u0E31\\u0E01\\u0E07\\u0E32\\u0E19\\u0E02\\u0E32\\u0E22\");\n    i0.ɵɵelement(34, \"br\");\n    i0.ɵɵtext(35, \"Salesman\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(36, \"td\", 32);\n    i0.ɵɵtext(37, \"\\u0E40\\u0E07\\u0E37\\u0E48\\u0E2D\\u0E19\\u0E44\\u0E02\\u0E01\\u0E32\\u0E23\\u0E0A\\u0E33\\u0E23\\u0E30\\u0E40\\u0E07\\u0E34\\u0E19\");\n    i0.ɵɵelement(38, \"br\");\n    i0.ɵɵtext(39, \"Payment Condtion\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(40, \"td\", 33);\n    i0.ɵɵtext(41, \"\\u0E27\\u0E31\\u0E19\\u0E04\\u0E23\\u0E1A\\u0E01\\u0E33\\u0E2B\\u0E19\\u0E14\");\n    i0.ɵɵelement(42, \"br\");\n    i0.ɵɵtext(43, \"Due Date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(44, \"td\", 33);\n    i0.ɵɵtext(45, \"\\u0E40\\u0E25\\u0E02\\u0E17\\u0E35\\u0E48\");\n    i0.ɵɵelement(46, \"br\");\n    i0.ɵɵtext(47, \"No.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(48, \"td\", 34);\n    i0.ɵɵtext(49, \"\\u0E27\\u0E31\\u0E19\\u0E17\\u0E35\\u0E48\");\n    i0.ɵɵelement(50, \"br\");\n    i0.ɵɵtext(51, \"Date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(52, PDFprintComponent_div_24_table_1_tr_52_Template, 17, 13, \"tr\", 35);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(53, \"div\", 89)(54, \"table\", 37)(55, \"thead\")(56, \"tr\", 90)(57, \"td\", 39);\n    i0.ɵɵtext(58, \"\\u0E25\\u0E33\\u0E14\\u0E31\\u0E1A\");\n    i0.ɵɵelement(59, \"br\");\n    i0.ɵɵtext(60, \"Itme\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(61, \"td\", 91);\n    i0.ɵɵtext(62, \"\\u0E40\\u0E25\\u0E02\\u0E17\\u0E35\\u0E48\\u0E43\\u0E1A\\u0E2A\\u0E31\\u0E48\\u0E07\");\n    i0.ɵɵelement(63, \"br\");\n    i0.ɵɵtext(64, \"\\u0E2A\\u0E34\\u0E19\\u0E04\\u0E49\\u0E32\");\n    i0.ɵɵelement(65, \"br\");\n    i0.ɵɵtext(66, \"P.O.No.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(67, \"td\", 92);\n    i0.ɵɵtext(68, \"\\u0E23\\u0E2B\\u0E31\\u0E2A\\u0E2A\\u0E34\\u0E19\\u0E04\\u0E49\\u0E32\");\n    i0.ɵɵelement(69, \"br\");\n    i0.ɵɵtext(70, \"Part Code\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(71, \"td\", 42);\n    i0.ɵɵtext(72, \"\\u0E23\\u0E32\\u0E22\\u0E01\\u0E32\\u0E23\");\n    i0.ɵɵelement(73, \"br\");\n    i0.ɵɵtext(74, \"Description\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(75, \"td\", 93);\n    i0.ɵɵtext(76, \"\\u0E08\\u0E33\\u0E19\\u0E27\\u0E19\");\n    i0.ɵɵelement(77, \"br\");\n    i0.ɵɵtext(78, \"Quantity\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(79, \"td\", 43);\n    i0.ɵɵtext(80, \"\\u0E2B\\u0E19\\u0E48\\u0E27\\u0E22\\u0E25\\u0E30\");\n    i0.ɵɵelement(81, \"br\");\n    i0.ɵɵtext(82, \"Unit Price\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(83, \"td\", 44);\n    i0.ɵɵtext(84, \"\\u0E2A\\u0E48\\u0E27\\u0E19\\u0E25\\u0E14\");\n    i0.ɵɵelement(85, \"br\");\n    i0.ɵɵtext(86, \"Disc %\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(87, \"td\", 42);\n    i0.ɵɵtext(88, \"\\u0E23\\u0E32\\u0E04\\u0E32\\u0E2A\\u0E38\\u0E17\\u0E18\\u0E34\");\n    i0.ɵɵelement(89, \"br\");\n    i0.ɵɵtext(90, \"\\u0E15\\u0E48\\u0E2D\\u0E0A\\u0E34\\u0E49\\u0E19\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(91, \"td\", 42);\n    i0.ɵɵtext(92, \"\\u0E08\\u0E33\\u0E19\\u0E27\\u0E19\\u0E40\\u0E07\\u0E34\\u0E19\");\n    i0.ɵɵelement(93, \"br\");\n    i0.ɵɵtext(94, \"Amount\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(95, \"tbody\", 94);\n    i0.ɵɵtemplate(96, PDFprintComponent_div_24_table_1_tr_96_Template, 34, 50, \"tr\", 35);\n    i0.ɵɵelementStart(97, \"tr\", 46);\n    i0.ɵɵelement(98, \"td\", 47)(99, \"td\", 47)(100, \"td\", 47)(101, \"td\", 47)(102, \"td\", 47)(103, \"td\", 47)(104, \"td\", 47)(105, \"td\", 47)(106, \"td\", 47)(107, \"td\", 47)(108, \"td\", 48);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(109, \"tr\", 46);\n    i0.ɵɵelement(110, \"td\", 47)(111, \"td\", 47)(112, \"td\", 47)(113, \"td\", 47)(114, \"td\", 47)(115, \"td\", 47)(116, \"td\", 47)(117, \"td\", 47)(118, \"td\", 47)(119, \"td\", 47)(120, \"td\", 48);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(121, \"tr\", 95)(122, \"td\", 96)(123, \"div\", 51);\n    i0.ɵɵtext(124);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(125, \"td\", 97)(126, \"div\", 98);\n    i0.ɵɵtext(127, \" \\u0E23\\u0E32\\u0E04\\u0E32\\u0E23\\u0E27\\u0E21\\u0E17\\u0E31\\u0E49\\u0E07\\u0E2A\\u0E34\\u0E49\\u0E19 (TOTAL) \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(128, \"div\", 98);\n    i0.ɵɵtext(129, \" \\u0E08\\u0E33\\u0E19\\u0E27\\u0E19\\u0E20\\u0E32\\u0E29\\u0E35\\u0E21\\u0E39\\u0E25\\u0E04\\u0E48\\u0E32\\u0E40\\u0E1E\\u0E34\\u0E48\\u0E21 (VAT) \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(130, \"div\", 98);\n    i0.ɵɵtext(131, \" \\u0E23\\u0E27\\u0E21\\u0E40\\u0E07\\u0E34\\u0E19\\u0E17\\u0E31\\u0E49\\u0E07\\u0E2A\\u0E34\\u0E49\\u0E19 (TOTAL) \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(132, \"div\", 99);\n    i0.ɵɵtext(133, \" 55555 \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(134, \"td\", 100)(135, \"div\", 101);\n    i0.ɵɵtext(136);\n    i0.ɵɵpipe(137, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(138, \"div\", 101);\n    i0.ɵɵtext(139);\n    i0.ɵɵpipe(140, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(141, \"div\", 102);\n    i0.ɵɵtext(142);\n    i0.ɵɵpipe(143, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(144, \"div\", 103);\n    i0.ɵɵtext(145, \" 55555 \");\n    i0.ɵɵelementEnd()()()()()();\n    i0.ɵɵelementStart(146, \"div\", 104)(147, \"table\", 105)(148, \"tbody\")(149, \"tr\", 59)(150, \"td\", 106);\n    i0.ɵɵtext(151, \"\\u0E23\\u0E31\\u0E1A\\u0E2A\\u0E34\\u0E19\\u0E04\\u0E49\\u0E32\\u0E14\\u0E31\\u0E07\\u0E23\\u0E32\\u0E22\\u0E01\\u0E32\\u0E23\\u0E02\\u0E49\\u0E32\\u0E07\\u0E1A\\u0E19\\u0E44\\u0E27\\u0E49\\u0E16\\u0E39\\u0E01\\u0E15\\u0E49\\u0E2D\\u0E07\\u0E43\\u0E19\\u0E2A\\u0E20\\u0E32\\u0E1E\\u0E40\\u0E23\\u0E35\\u0E22\\u0E1A\\u0E23\\u0E49\\u0E2D\\u0E22\\u0E41\\u0E25\\u0E49\\u0E27\");\n    i0.ɵɵelement(152, \"br\");\n    i0.ɵɵtext(153, \"Received the above goods in good order & condition\");\n    i0.ɵɵelement(154, \"br\")(155, \"br\")(156, \"br\")(157, \"br\");\n    i0.ɵɵtext(158, \".................................................................................\");\n    i0.ɵɵelement(159, \"br\");\n    i0.ɵɵtext(160, \"\\u0E25\\u0E07\\u0E19\\u0E32\\u0E21\\u0E41\\u0E25\\u0E30\\u0E1B\\u0E23\\u0E30\\u0E17\\u0E31\\u0E1A\\u0E15\\u0E23\\u0E32\\u00A0\\u00A0\\u00A0\\u00A0\\u00A0\\u00A0\\u00A0\\u00A0\\u00A0\\u00A0\\u00A0\\u00A0\\u00A0\\u00A0\\u00A0\\u00A0\\u00A0\\u00A0\\u00A0\\u00A0\\u00A0\\u00A0\\u00A0\\u00A0\\u0E1C\\u0E39\\u0E49\\u0E23\\u0E31\\u0E1A\\u0E02\\u0E2D\\u0E07\");\n    i0.ɵɵelement(161, \"br\")(162, \"br\");\n    i0.ɵɵelementStart(163, \"div\", 21);\n    i0.ɵɵtext(164, \"\\u0E27\\u0E31\\u0E19\\u0E17\\u0E35\\u0E48 DATE ............../............../..............\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(165, \"td\", 107);\n    i0.ɵɵtext(166, \"\\u0E43\\u0E19\\u0E19\\u0E32\\u0E21 \\u0E1A\\u0E23\\u0E34\\u0E29\\u0E31\\u0E17\\u0E1A\\u0E32\\u0E07\\u0E1A\\u0E2D\\u0E19\\u0E1E\\u0E25\\u0E32\\u0E2A\\u0E15\\u0E34\\u0E04 \\u0E01\\u0E23\\u0E38\\u0E4A\\u0E1B\\u0E08\\u0E33\\u0E01\\u0E31\\u0E14\");\n    i0.ɵɵelement(167, \"br\")(168, \"br\")(169, \"br\")(170, \"br\")(171, \"br\");\n    i0.ɵɵtext(172, \"........................................................................\");\n    i0.ɵɵelement(173, \"br\");\n    i0.ɵɵtext(174, \"\\u0E1C\\u0E39\\u0E49\\u0E19\\u0E33\\u0E2A\\u0E48\\u0E07 DELIVERER\");\n    i0.ɵɵelement(175, \"br\")(176, \"br\");\n    i0.ɵɵelementStart(177, \"div\", 108);\n    i0.ɵɵtext(178, \"\\u0E1C\\u0E34\\u0E14 \\u0E15\\u0E01 \\u0E22\\u0E01\\u0E40\\u0E27\\u0E49\\u0E19\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(179, \"td\", 109)(180, \"div\", 110);\n    i0.ɵɵtext(181, \"\\u0E1C\\u0E39\\u0E49\\u0E15\\u0E23\\u0E27\\u0E08\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(182, \"br\")(183, \"br\")(184, \"br\")(185, \"br\")(186, \"br\")(187, \"br\")(188, \"br\");\n    i0.ɵɵelementStart(189, \"div\", 111);\n    i0.ɵɵtext(190, \"........................................................... \\u0E1A\\u0E31\\u0E19\\u0E17\\u0E36\\u0E01\");\n    i0.ɵɵelementEnd()()()()()()()()()();\n  }\n  if (rf & 2) {\n    const y_r9 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(25, _c1, ctx_r3.gettopDOM(y_r9.page, ctx_r3.pageHeightmin)));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(27, _c4, ctx_r3.gettop(ctx_r3.pageHeightmin)));\n    i0.ɵɵadvance(18);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.DataHerderINV);\n    i0.ɵɵadvance(29);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.DataHerderINV);\n    i0.ɵɵadvance(44);\n    i0.ɵɵproperty(\"ngForOf\", y_r9.data);\n    i0.ɵɵadvance(27);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(29, _c2, ctx_r3.getnumpage(y_r9.page)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"(\", ctx_r3.SumtotalTH, \")\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(31, _c2, ctx_r3.getnumpage(y_r9.page)));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(33, _c2, ctx_r3.getnumpage(y_r9.page)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(137, 16, ctx_r3.salesbalance, \"1.2-2\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(35, _c2, ctx_r3.getnumpage(y_r9.page)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(140, 19, ctx_r3.sumtaxamount, \"1.2-2\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(37, _c2, ctx_r3.getnumpage(y_r9.page)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(143, 22, ctx_r3.Sumtotal, \"1.2-2\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(39, _c2, ctx_r3.getnumpage(y_r9.page)));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(41, _c4, ctx_r3.gettopDOMBtoom(y_r9.page)));\n  }\n}\nfunction PDFprintComponent_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, PDFprintComponent_div_24_table_1_Template, 191, 43, \"table\", 79);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.dataprint);\n  }\n}\nexport let PDFprintComponent = /*#__PURE__*/(() => {\n  class PDFprintComponent {\n    //idINV=TI18-04472&fromdate=2019-2-1&todate=2019-2-15&pacodesale=1&pacustomer=%2520&pabill=&Datatoback=bill\n    //idINV=TI18-03489&fromdate=2017-10-16&todate=2018-10-16&CodeSo=1&Customer=%2520\n    constructor(http, service, route, router) {\n      this.http = http;\n      this.service = service;\n      this.route = route;\n      this.router = router;\n      this.DataLine = [];\n      this.dataprint = [];\n      this.Data = [];\n      this.Data2 = [];\n      this.num = 0;\n      this.DataHerderINV = [];\n      this.DataINV = [];\n      this.sumtaxamount = 0;\n      this.salesbalance = 0;\n      this.Sumtotal = 0;\n      this.pageHeightmin = 300;\n      this.taxnew = 0;\n      localStorage.removeItem('DataSOderreview');\n      localStorage.removeItem('DataSOderlist');\n      this.idINV = this.route.snapshot.queryParams['idINV'];\n      this.fromdate = this.route.snapshot.queryParams['fromdate'];\n      this.todate = this.route.snapshot.queryParams['todate'];\n      this.CodeSo = this.route.snapshot.queryParams['CodeSo'];\n      this.Customer = this.route.snapshot.queryParams['Customer'];\n      this.CodeInvoice = this.route.snapshot.queryParams['paINV'];\n      this.Customerid = this.route.snapshot.queryParams['idcustomer'];\n      this.Datatoback = this.route.snapshot.queryParams['Datatoback'];\n      this.page = this.route.snapshot.queryParams['page'];\n      this.pacodesale = this.route.snapshot.queryParams['pacodesale'];\n      this.pabill = this.route.snapshot.queryParams['pabill'];\n      this.pacustomer = this.route.snapshot.queryParams['pacustomer'];\n      this.patxtcustomer = this.route.snapshot.queryParams['patxtcustomer'] || '';\n      this.url = service.geturlservice();\n      if (this.idINV === undefined) {\n        alert('รูปแบบข้อมูลผิดพลาด');\n        this.router.navigate(['/invoicecashlist'], {\n          queryParams: {\n            fromdate: this.fromdate,\n            todate: this.todate,\n            CodeSo: this.CodeSo,\n            Customer: this.Customer,\n            CodeINV: this.CodeInvoice,\n            customerid: this.Customerid,\n            datatoback: this.Datatoback\n          }\n        });\n      } else {\n        this.DataSearchINV();\n      }\n      /*  for(var i=1 ;i<=100;i++){\n          this.Data.push({\n            idApprove: i,\n            ApproveName: i\n          });\n        }  this.GetData();*/\n      this.datalogin = JSON.parse(sessionStorage.getItem('login'));\n      if (this.datalogin == null) {\n        this.router.navigate(['login']);\n      } else {}\n    }\n    ngOnInit() {}\n    getDue(country) {\n      let invdate = Number(new Date(country));\n      var invdate7 = invdate + 2592000000;\n      var DateDue = new Date(invdate7);\n      var strDateDue = DateDue.toString();\n      return strDateDue;\n    }\n    DataSearchINV() {\n      this.DataHerderINV = [];\n      this.http.get(this.url + 'PDF_invoice_herder/' + this.idINV).subscribe(res => {\n        if (res.length < 1) {\n          alert('ไม่พบข้อมูลส่วนหัว');\n          this.router.navigate(['/invoicecashlist'], {\n            queryParams: {\n              fromdate: this.fromdate,\n              todate: this.todate,\n              CodeSo: this.CodeSo,\n              Customer: this.Customer,\n              CodeINV: this.CodeInvoice,\n              customerid: this.Customerid,\n              datatoback: this.Datatoback\n            }\n          });\n        } else {\n          for (var i = 0; i < res.length; i++) {\n            var Dataitemid = '';\n            var Dataitemname = '';\n            var DataPO = '';\n            /*var datedue = this.getDue(res[i].invoicedate)\n            alert(datedue);*/\n            this.DataHerderINV.push({\n              name: res[i].name,\n              invoiceid: res[i].invoiceid,\n              salesid: res[i].salesid,\n              duedate: res[i].duedate,\n              invoicedate: res[i].invoicedate,\n              invoicingname: res[i].invoicingname,\n              addressINV: res[i].addressINV,\n              addressDEL: res[i].addressDEL,\n              telnumber: res[i].telnumber,\n              regnum: res[i].regnum,\n              taxgroup: res[i].taxgroup,\n              salesbalance: res[i].salesbalance,\n              sumtax: res[i].sumtax,\n              invoiceamount: res[i].invoiceamount,\n              paymenttype: res[i].paymenttype,\n              orderaccount: res[i].orderaccount,\n              nameINVherder: res[i].nameInvoiceHeader\n            });\n            this.Sumtotal = res[i].invoiceamount;\n            this.sumtaxamount = res[i].sumtax;\n            this.salesbalance = res[i].salesbalance;\n            this.SearchINVData();\n            this.gettaxgroup();\n          }\n        }\n      }, error => {\n        alert('เกิดปัญหาในการ Process ข้อมูล กรุณารายการใหม่อีกครั้ง');\n      });\n    }\n    SearchINVData() {\n      this.DataINV = [];\n      this.http.get(this.url + 'PDF_invoice/' + this.idINV).subscribe(res => {\n        if (res.length < 1) {\n          alert('ไม่พบข้อมูลรายละเอียด invoice');\n          this.router.navigate(['/invoicecashlist'], {\n            queryParams: {\n              fromdate: this.fromdate,\n              todate: this.todate,\n              CodeSo: this.CodeSo,\n              Customer: this.Customer,\n              CodeINV: this.CodeInvoice,\n              customerid: this.Customerid,\n              datatoback: this.Datatoback\n            }\n          });\n        } else {\n          for (var i = 0; i < res.length; i++) {\n            var Dataitemid = '';\n            var Dataitemname = '';\n            var DataPO = '';\n            if (res[i].lineamountmst == 0) {\n              DataPO = 'ของแถม';\n            }\n            this.DataINV.push({\n              Number: (i + 1).toString(),\n              PO: DataPO,\n              salesid: res[i].salesid,\n              invoiceid: res[i].invoiceid,\n              linenum: res[i].linenum,\n              itemid: res[i].itemid,\n              productname: res[i].name,\n              qtyline: res[i].qtyline,\n              salesprice: res[i].salesprice,\n              ivz_percent1_ct: res[i].ivz_percent1_ct,\n              ivz_percent2_ct: res[i].ivz_percent2_ct,\n              ivz_percent3_ct: res[i].ivz_percent3_ct,\n              salesunit: res[i].salesunit,\n              taxamount: res[i].taxamount,\n              totalline: res[i].totalline,\n              lineamountmst: res[i].lineamountmst,\n              totalTH: res[i].totalTH2\n            });\n          }\n          this.GetData();\n        }\n      }, error => {\n        alert('เกิดปัญหาในการ Process ข้อมูล กรุณารายการใหม่อีกครั้ง');\n      });\n    }\n    gettaxgroup() {\n      if (this.DataHerderINV[0].taxgroup === 'DOM') {\n        this.taxgroup = false; //แดงเลือดหมู\n      } else {\n        this.taxgroup = true; // เขียว\n      }\n    }\n    gettopDOMBtoom(y) {\n      if (y != this.Numpage) {\n        return '20px';\n      } else {\n        return '20px';\n      }\n    }\n    gettopDOM(y, pageHeightmin) {\n      if (y == 1) {\n        return '3px';\n      } else if (y == this.Numpage && pageHeightmin == 302) {\n        return '15px';\n      } else {\n        return '25px';\n      }\n    }\n    gettop(v) {\n      if (v == 302) {\n        return '13px';\n      } else {\n        return '30px';\n      }\n    }\n    gettop2(v) {\n      if (v == 302) {\n        return '60px';\n      } else {\n        return '76px';\n      }\n    }\n    captureScreen() {\n      /* alert(this.pageHeightmin)\n      alert(JSON.stringify(this.dataprint))*/\n      var data = document.getElementById('convert');\n      html2canvas(data).then(canvas => {\n        var imgWidth = 210; //208\n        var pageHeight = this.pageHeightmin;\n        var imgHeight = canvas.height * imgWidth / canvas.width;\n        var heightLeft = imgHeight;\n        var date = new Date();\n        var del = Math.floor(heightLeft / pageHeight) + 1;\n        const contentDataURL = canvas.toDataURL('image/jpeg', 1.0);\n        let pdf = new jspdf('p', 'mm', 'a4');\n        var position = 0;\n        pdf.addImage(contentDataURL, 'JPEG', 0, position, imgWidth, imgHeight);\n        heightLeft -= pageHeight;\n        while (heightLeft >= 0) {\n          position = heightLeft - imgHeight;\n          pdf.addPage();\n          pdf.addImage(contentDataURL, 'JPEG', 0, position, imgWidth, imgHeight);\n          heightLeft -= pageHeight;\n        }\n        /*pdf.deletePage(del);*/\n        pdf.save(this.idINV + '|dd-MM-yyyy :' + date.getDate() + '-' + (date.getMonth() + 1) + '-' + date.getFullYear() + '.PDF');\n      });\n    }\n    /* GetData(){\n       alert(this.DataINV.length)\n       var x=this.DataINV.length;\n       var y =x/18;\n       var sum =Math.ceil(y);\n       var a =0;\n       var s=x%18;\n      var g =18-s\n       if(g>0){\n     for (var i=0;i<g;i++){\n       this.DataLine.push({\n         salesid\t:\"11111\",\n         invoiceid\t:\"11111\",\n         linenum\t:\"11111\",\n         itemid\t:\"11111\",\n         qtyline\t:\"11111\",\n         salesprice :\"11111\",\n         ivz_percent1_ct :\"11111\",\n         ivz_percent2_ct :\"11111\",\n         ivz_percent3_ct:\"11111\",\n         salesunit\t:\"11111\",\n         taxamount\t:\"11111\",\n         lineamountmst\t:\"11111\",\n       });\n     }\n       }\n       for(var z=0;z<sum;z++){\n       for (var i=this.num;i<=17+this.num;i++){\n           this.DataLine.push({\n             salesid\t: this.DataINV[i].salesid,\n         invoiceid\t:this.DataINV[i].invoiceid,\n         linenum\t:this.DataINV[i].linenum,\n         itemid\t:this.DataINV[i].itemid,\n         qtyline\t:this.DataINV[i].qtyline,\n         salesprice :this.DataINV[i].salesprice,\n         ivz_percent1_ct :this.DataINV[i].ivz_percent1_ct,\n         ivz_percent2_ct :this.DataINV[i].ivz_percent2_ct,\n         ivz_percent3_ct:this.DataINV[i].ivz_percent3_ct,\n         salesunit\t:this.DataINV[i].salesunit,\n         taxamount\t:this.DataINV[i].taxamount,\n         lineamountmst\t:this.DataINV[i].lineamountmst,\n           })\n         }\n           }\n       this.dataprint.push({\n         page:a+1,\n         data:this.DataLine\n       })\n       this.DataLine=[];\n       this.num +=18;\n       }*/\n    GetData() {\n      var x = this.DataINV.length;\n      var y = x / 18;\n      var sum = Math.ceil(y);\n      var a = 0;\n      var s = x % 18;\n      var g = 18 - s;\n      if (g > 0) {\n        for (var i = 0; i < g; i++) {\n          this.DataINV.push({\n            Number: \"GG\",\n            PO: \"\",\n            salesid: \"\",\n            invoiceid: \"\",\n            linenum: \"\",\n            itemid: \"\",\n            productname: \"\",\n            qtyline: \"\",\n            salesprice: \"\",\n            ivz_percent1_ct: \"\",\n            ivz_percent2_ct: \"\",\n            ivz_percent3_ct: \"\",\n            salesunit: \"\",\n            taxamount: \"\",\n            totalline: \"\",\n            lineamountmst: \"\",\n            totalTH: \"\"\n          });\n        }\n      }\n      for (var z = 0; z < sum; z++) {\n        for (var i = this.num; i <= 17 + this.num; i++) {\n          if (this.DataINV[i].salesid === undefined) {\n            this.DataLine.push({\n              Number: \"GG\",\n              PO: \"\",\n              salesid: \"\",\n              invoiceid: \"\",\n              linenum: \"\",\n              itemid: \"\",\n              productname: \"\",\n              qtyline: \"\",\n              salesprice: \"\",\n              ivz_percent1_ct: \"\",\n              ivz_percent2_ct: \"\",\n              ivz_percent3_ct: \"\",\n              salesunit: \"\",\n              taxamount: \"\",\n              totalline: \"\",\n              lineamountmst: \"\",\n              totalTH: \"\"\n            });\n          } else {\n            this.DataLine.push({\n              Number: this.DataINV[i].Number,\n              PO: this.DataINV[i].PO,\n              salesid: this.DataINV[i].salesid,\n              invoiceid: this.DataINV[i].invoiceid,\n              linenum: this.DataINV[i].linenum,\n              itemid: this.DataINV[i].itemid,\n              productname: this.DataINV[i].productname,\n              qtyline: this.DataINV[i].qtyline,\n              salesprice: this.DataINV[i].salesprice,\n              ivz_percent1_ct: this.DataINV[i].ivz_percent1_ct,\n              ivz_percent2_ct: this.DataINV[i].ivz_percent2_ct,\n              ivz_percent3_ct: this.DataINV[i].ivz_percent3_ct,\n              salesunit: this.DataINV[i].salesunit,\n              taxamount: this.DataINV[i].taxamount,\n              totalline: this.DataINV[i].totalline,\n              lineamountmst: this.DataINV[i].lineamountmst,\n              totalTH: this.DataINV[i].totalTH\n            });\n          }\n        }\n        this.dataprint.push({\n          page: z + 1,\n          data: this.DataLine\n        });\n        this.DataLine = [];\n        this.num += 18;\n        this.getsumtotalTH();\n      }\n    }\n    numpage() {\n      this.Numpage = this.dataprint.length;\n    }\n    getnumpage(Numpage) {\n      if (Numpage != this.Numpage) {\n        return 'none';\n      }\n    }\n    getsumtotalTH() {\n      this.numpage();\n      if (this.Sumtotal == 0) {\n        this.SumtotalTH = \"ศูนย์บาทถ้วน\";\n      } else {\n        // this.Sumtotal=100.11\n        this.SumtotalTH = this.ArabicNumberToText(this.Sumtotal.toFixed(2).replace('-', ''));\n      }\n    }\n    toback() {\n      if (this.page == 'bill') {\n        // idINV=TI18-04472&fromdate=2019-2-1&todate=2019-2-15&pacodesale=1&pacustomer=%2520&pabill=&Datatoback=bill\n        this.router.navigate(['/invoicecreditlist'], {\n          queryParams: {\n            fromdate: this.fromdate,\n            todate: this.todate,\n            pacodesale: this.pacodesale,\n            pacustomer: this.pacustomer,\n            pabill: this.pabill,\n            datatoback: this.Datatoback,\n            patxtcustomer: this.patxtcustomer\n          }\n        });\n      } else {\n        this.router.navigate(['/invoicecashlist'], {\n          queryParams: {\n            fromdate: this.fromdate,\n            todate: this.todate,\n            CodeSo: this.CodeSo,\n            Customer: this.Customer,\n            CodeINV: this.CodeInvoice,\n            customerid: this.Customerid,\n            datatoback: this.Datatoback\n          }\n        });\n      }\n    }\n    ArabicNumberToText(Number) {\n      var Number = this.CheckNumber(Number);\n      var NumberArray = new Array(\"ศูนย์\", \"หนึ่ง\", \"สอง\", \"สาม\", \"สี่\", \"ห้า\", \"หก\", \"เจ็ด\", \"แปด\", \"เก้า\", \"สิบ\");\n      var DigitArray = new Array(\"\", \"สิบ\", \"ร้อย\", \"พัน\", \"หมื่น\", \"แสน\", \"ล้าน\");\n      var BahtText = \"\";\n      if (isNaN(Number)) {\n        return \"ข้อมูลนำเข้าไม่ถูกต้อง\";\n      } else {\n        if (Number - 0 > 9999999.9999) {\n          return \"ข้อมูลนำเข้าเกินขอบเขตที่ตั้งไว้\";\n        } else {\n          Number = Number.split(\".\");\n          if (Number[1].length > 0) {\n            Number[1] = Number[1].substring(0, 2);\n          }\n          var NumberLen = Number[0].length - 0;\n          for (var i = 0; i < NumberLen; i++) {\n            var tmp = Number[0].substring(i, i + 1) - 0;\n            if (tmp != 0) {\n              if (i == NumberLen - 1 && tmp == 1) {\n                BahtText += \"เอ็ด\";\n              } else if (i == NumberLen - 2 && tmp == 2) {\n                BahtText += \"ยี่\";\n              } else if (i == NumberLen - 2 && tmp == 1) {\n                BahtText += \"\";\n              } else {\n                BahtText += NumberArray[tmp];\n              }\n              BahtText += DigitArray[NumberLen - i - 1];\n            }\n          }\n          BahtText += \"บาท\";\n          if (Number[1] == \"0\" || Number[1] == \"00\") {\n            BahtText += \"ถ้วน\";\n          } else {\n            var DecimalLen = Number[1].length - 0;\n            var s = parseInt(Number[1]);\n            for (var i = 0; i < DecimalLen; i++) {\n              var tmp = Number[1].substring(i, i + 1) - 0;\n              if (tmp != 0) {\n                if (i == DecimalLen - 1 && tmp == 1 && s != 1) {\n                  //  alert('S'+s+'/'+i);\n                  BahtText += \"เอ็ด\";\n                } else if (i == DecimalLen - 2 && tmp == 2) {\n                  // alert(2);\n                  BahtText += \"ยี่\";\n                } else if (i == DecimalLen - 2 && tmp == 1) {\n                  //alert(3);\n                  BahtText += \"\";\n                } else {\n                  //  alert(4);\n                  BahtText += NumberArray[tmp];\n                }\n                //  alert(NumberArray[tmp]);\n                BahtText += DigitArray[DecimalLen - i - 1];\n              }\n            }\n            BahtText += \"สตางค์\";\n          }\n          return BahtText;\n        }\n      }\n    }\n    CheckNumber(Number) {\n      var decimal = false;\n      Number = Number.toString();\n      Number = Number.replace(/ |,|บาท|฿/gi, '');\n      for (var i = 0; i < Number.length; i++) {\n        if (Number[i] == '.') {\n          decimal = true;\n        }\n      }\n      if (decimal == false) {\n        Number = Number + '.00';\n      }\n      return Number;\n    }\n    getColor(country) {\n      switch (country) {\n        case 'GG':\n          return '#ffffff';\n      }\n    }\n    getColorivz(country) {\n      switch (country) {\n        case '0':\n          return '#ffffff';\n      }\n    }\n    static {\n      this.ɵfac = function PDFprintComponent_Factory(t) {\n        return new (t || PDFprintComponent)(i0.ɵɵdirectiveInject(i1.HttpClient), i0.ɵɵdirectiveInject(i2.WebapiService), i0.ɵɵdirectiveInject(i3.ActivatedRoute), i0.ɵɵdirectiveInject(i3.Router));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: PDFprintComponent,\n        selectors: [[\"app-pdfprint\"]],\n        viewQuery: function PDFprintComponent_Query(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵviewQuery(_c0, 5);\n          }\n          if (rf & 2) {\n            let _t;\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.content = _t.first);\n          }\n        },\n        decls: 25,\n        vars: 3,\n        consts: [[1, \"col-sm-12\", \"col-md-12\", \"col-lg-12\", \"text-center\", 2, \"margin-top\", \"50px\", \"margin-bottom\", \"20px\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", \"btn-sm\", \"font-weight-light\", 2, \"margin-right\", \"5px\", 3, \"click\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", \"btn-sm\", \"font-weight-light\", 3, \"click\"], [1, \"col-sm-12\", \"col-md-12\", \"col-lg-12\", \"text-center\", 2, \"margin-bottom\", \"20px\"], [1, \"custom-select\", \"custom-select-sm\", \"col-sm-2\", \"col-md-2\", \"col-lg-2\", 3, \"ngModelChange\", \"ngModel\"], [\"disabled\", \"\", \"value\", \"\"], [\"value\", \"305\"], [\"value\", \"300\"], [\"value\", \"295\"], [\"value\", \"290\"], [\"value\", \"302\"], [\"align\", \"center\", 1, \"container-fluid\"], [\"id\", \"convert\", 2, \"width\", \"1100px\", \"padding-left\", \"5px\", \"padding-right\", \"5px\"], [4, \"ngIf\"], [\"style\", \"width: 100%; height: 295px; margin-bottom: 20px; margin-top: 0px;  \", 4, \"ngFor\", \"ngForOf\"], [2, \"width\", \"100%\", \"height\", \"295px\", \"margin-bottom\", \"20px\", \"margin-top\", \"0px\"], [2, \"padding\", \"5px 8px 8px\", \"position\", \"relative\"], [\"cellspacing\", \"0\", \"cellpadding\", \"2\", \"align\", \"center\", \"border\", \"0\", 2, \"font-family\", \"Tahoma,segoe UI\", \"font-size\", \"12pt\", \"padding\", \"5px\", \"width\", \"100%\", \"margin-bottom\", \"43px\", 3, \"ngStyle\"], [\"align\", \"center\"], [2, \"font-size\", \"28pt\"], [2, \"font-size\", \"25pt\"], [\"valign\", \"bottom\"], [\"src\", \"assets/img/1herder.jpg\", 2, \"position\", \"absolute\", \"top\", \"85px\", \"width\", \"55px\", \"right\", \"80px\"], [2, \"font-family\", \"Segoe UI\", \"; font-size\", \"15px\", \"color\", \"rgb(0, 0, 0)\"], [2, \"font-size\", \"13px\", \"width\", \"150px\", \"font-family\", \"segui ui\", \";font-size\", \"9pt\", \"color\", \"black\"], [\"width\", \"100%\", \"cellspacing\", \"0\", \"cellpadding\", \"2\", \"align\", \"center\", 2, \"width\", \"100%\", \"margin-bottom\", \"5px\"], [\"height\", \"150px\", 4, \"ngFor\", \"ngForOf\"], [1, \"rcorners\", 2, \"width\", \"100%\", \"background-color\", \"rgba(40, 197, 40, 0.911)\"], [\"align\", \"center\", 2, \"width\", \"100%\", \"font-family\", \"Segoe UI\", \"font-size\", \"12pt\"], [2, \"color\", \"#fff\"], [\"align\", \"center\", 1, \"font-weight-normal\", \"text-center\", 2, \"padding\", \"5px\", \"border-right\", \"1px solid black\"], [\"align\", \"center\", 1, \"font-weight-normal\", \"text-center\", 2, \"padding\", \"5px\", \"border-right\", \"1px solid black\", \"width\", \"250px\"], [\"align\", \"center\", 1, \"font-weight-normal\", \"text-center\", 2, \"padding\", \"5px\", \"border-right\", \"1px solid black\", \"width\", \"150px\"], [\"align\", \"center\", 1, \"font-weight-normal\", \"text-center\", 2, \"padding\", \"5px\", \"border-right\", \"1px solid black\", \"width\", \"180px\"], [\"align\", \"center\", 1, \"font-weight-normal\", \"text-center\", 2, \"padding\", \"5px\", \"width\", \"180px\"], [\"style\", \"color: rgb(0, 0, 0);background-color: #fff\", 4, \"ngFor\", \"ngForOf\"], [1, \"rcorners\", 2, \"width\", \"100%\", \"background-color\", \"rgba(40, 197, 40, 0.911)\", \"margin-top\", \"3px\", \"font-family\", \"Segoe UI\", \"font-size\", \"10pt\"], [\"width\", \"100%\", \"cellspacing\", \"0\", \"cellpadding\", \"2\", \"align\", \"center\", \"border\", \"0\", 2, \"width\", \"100%\"], [2, \"color\", \"#fff\", \"background-color\", \"rgba(40, 197, 40, 0.911)\", \"font-family\", \"Segoe UI\", \"font-size\", \"12pt\"], [\"align\", \"center\", 1, \"text-center\", 2, \"padding\", \"2px\", \"border-right\", \"1px solid black\", \"width\", \"45px\"], [\"align\", \"center\", 1, \"text-center\", 2, \"padding\", \"2px\", \"width\", \"90px\"], [\"align\", \"center\", 1, \"text-center\", 2, \"padding\", \"2px\", \"border-left\", \"1px solid black\", \"width\", \"115px\"], [\"align\", \"center\", 1, \"text-center\", 2, \"padding\", \"2px\", \"border-left\", \"1px solid black\"], [\"align\", \"center\", 1, \"text-center\", 2, \"padding\", \"2px\", \"border-left\", \"1px solid black\", \"width\", \"80px\"], [\"align\", \"center\", \"colspan\", \"3\", 1, \"text-center\", 2, \"padding\", \"2px\", \"border-left\", \"1px solid black\"], [2, \"background-color\", \"#fff\", \"font-family\", \"Segoe UI\", \"font-size\", \"11pt\"], [2, \"background-color\", \"#fff\"], [\"align\", \"center\", 1, \"text-center\", 2, \"padding\", \"5px\", \"border-right\", \"1px solid black\"], [\"align\", \"center\", 1, \"text-center\", 2, \"padding\", \"5px\"], [2, \"color\", \"rgb(0, 0, 0)\", \"height\", \"100px\", \"background-color\", \"#fff\"], [\"align\", \"center\", \"valign\", \"top\", \"colspan\", \"5\", \"rowspan\", \"3\", 1, \"text-left\", 2, \"font-family\", \"Segoe UI\", \"font-size\", \"12pt\", \"padding\", \"15px\", \"text-align\", \"left\", \"border-right\", \"1px solid black\", \"border-top\", \"1px solid black\"], [3, \"ngStyle\"], [\"align\", \"center\", \"colspan\", \"4\", \"rowspan\", \"3\", 1, \"text-center\", 2, \"padding\", \"15px\", \"text-align\", \"center\", \"border-right\", \"1px solid black\", \"border-top\", \"1px solid black\", \"font-family\", \"Tohama\", \"font-size\", \"14pt\"], [\"align\", \"center\", \"colspan\", \"2\", 1, \"text-right\", 2, \"padding\", \"0px\", \"text-align\", \"right\", \"border-top\", \"1px solid black\"], [2, \"width\", \"100%\", \"padding\", \"3px\", \"font-family\", \"Tohama\", \"font-size\", \"11pt\", 3, \"ngStyle\"], [2, \"width\", \"100%\", \"padding\", \"3px\", \"font-family\", \"Tohama\", \"font-size\", \"11pt\", \"border-block-end\", \"1px solid\", \"border-top\", \"1px solid\", \"margin-bottom\", \"2px\", 3, \"ngStyle\"], [2, \"color\", \"#fff\", \"width\", \"100%\", \"border-top\", \"1px solid black\", \"font-family\", \"Tohama\", \"font-size\", \"11pt\", 3, \"ngStyle\"], [1, \"rcorners\", 2, \"width\", \"100%\", \";margin-top\", \"3px\", \"text-align\", \"center\"], [\"width\", \"100%\", \"cellspacing\", \"0\", \"cellpadding\", \"2\", \"align\", \"center\", 2, \"width\", \"100%\"], [2, \"color\", \"rgb(0, 0, 0)\"], [\"align\", \"center\", \"width\", \"30%\", 1, \"text-align:\", \"center;\", 2, \"padding\", \"10px\", \"border-right\", \"1px solid black\", \"font-family\", \"Segoe UI\", \"font-size\", \"11pt\"], [\"align\", \"center\", \"width\", \"40%\", 2, \"padding\", \"15px\", \"border-right\", \"1px solid black\"], [1, \"text-align:\", \"center;\", 2, \"font-family\", \"Segoe UI\", \"font-size\", \"15pt\"], [\"align\", \"center\", \"width\", \"30%\", 1, \"text-align:\", \"center;\", 2, \"font-family\", \"Segoe UI\", \"font-size\", \"11pt\"], [\"height\", \"150px\"], [1, \"rcorners\", 2, \"height\", \"150px\", \"float\", \"left\", \"width\", \"49.9%\"], [\"width\", \"7%\", \"valign\", \"top\", 1, \"text-right\", 2, \"font-family\", \"segui ui\", \"font-size\", \"12pt\", \"padding\", \"5px\", \"padding-top\", \"10px\"], [\"width\", \"43%\", \"valign\", \"top\", 1, \"text-left\", 2, \"font-family\", \"segui ui\", \"font-size\", \"12pt\", \"padding\", \"5px\", \"padding-top\", \"10px\"], [1, \"rcorners\", 2, \"height\", \"150px\", \"float\", \"right\", \"width\", \"49.9%\"], [\"width\", \"8%\", \"valign\", \"top\", 1, \"font-weight-normal\", \"text-right\", 2, \"font-family\", \"segui ui\", \"font-size\", \"12pt\", \"padding\", \"5px\", \"padding-top\", \"10px\"], [\"width\", \"42%\", \"valign\", \"top\", 1, \"font-weight-normal\", \"text-left\", 2, \"font-family\", \"segui ui\", \"font-size\", \"12pt\", \"padding\", \"5px\", \"padding-top\", \"10px\"], [2, \"color\", \"rgb(0, 0, 0)\", \"background-color\", \"#fff\"], [\"align\", \"center\", 1, \"text-center\", 2, \"padding\", \"5px\", \"padding\", \"5px\", \"border-right\", \"1px solid black\", 3, \"ngStyle\"], [\"align\", \"center\", 1, \"text-left\", 2, \"padding\", \"5px\", \"border-right\", \"1px solid black\"], [\"align\", \"center\", 1, \"text-left\", 2, \"padding\", \"5px\", \"border-right\", \"1px solid black\", \"font-size\", \"9.8pt\"], [\"align\", \"center\", 1, \"text-right\", 2, \"padding\", \"5px\", \"border-right\", \"1px solid black\"], [\"align\", \"center\", 1, \"text-right\", 2, \"padding\", \"5px\", \"border-right\", \"1px solid black\", \"width\", \"38px\", 3, \"ngStyle\"], [\"align\", \"center\", 1, \"text-right\", 2, \"padding\", \"5px\", \"border-right\", \"1px solid black\", \"width\", \"90px\"], [\"align\", \"center\", 1, \"text-right\", 2, \"padding\", \"5px\", \"width\", \"90px\"], [\"style\", \"width: 100%; height: 295px; \", 4, \"ngFor\", \"ngForOf\"], [2, \"width\", \"100%\", \"height\", \"295px\"], [\"width\", \"100%\", \"align\", \"center\", \"border\", \"0\", 2, \"font-family\", \"segui ui\", \"font-size\", \"12pt\", 3, \"ngStyle\"], [\"src\", \"assets/img/Herder.jpg\", \"width\", \"100%\", 3, \"ngStyle\"], [2, \"font-size\", \"22pt\"], [2, \"font-family\", \"Arial,Helvetica,sans-serif\", \"font-size\", \"15px\", \"color\", \"rgb(0, 0, 0)\"], [\"width\", \"100%\"], [\"width\", \"50%\", 1, \"text-left\", 2, \"font-family\", \"segui ui\", \"font-size\", \"12pt\", \"color\", \"black\"], [\"width\", \"50%\", \"valign\", \"bottom\", 1, \"text-right\", 2, \"font-family\", \"segui ui\", \"font-size\", \"12pt\", \"color\", \"black\"], [1, \"rcorners\", 2, \"width\", \"100%\", \"background-color\", \"#8b2323\"], [1, \"rcorners\", 2, \"width\", \"100%\", \"background-color\", \"#8b2323\", \"margin-top\", \"3px\", \"font-family\", \"Segoe UI\", \"font-size\", \"10pt\"], [2, \"color\", \"#fff\", \"background-color\", \"#8b2323\", \"font-family\", \"Segoe UI\", \"font-size\", \"12pt\"], [\"align\", \"center\", 1, \"text-center\", 2, \"padding\", \"2px\", \"width\", \"80px\"], [\"align\", \"center\", 1, \"text-center\", 2, \"padding\", \"2px\", \"border-left\", \"1px solid black\", \"width\", \"130px\"], [\"align\", \"center\", 1, \"text-center\", 2, \"padding\", \"2px\", \"border-left\", \"1px solid black\", \"width\", \"70px\"], [2, \"background-color\", \"#fff\", \"font-family\", \"Segoe UI\", \"font-size\", \"10pt\"], [2, \"color\", \"rgb(0, 0, 0)\", \"height\", \"100px\"], [\"align\", \"center\", \"valign\", \"top\", \"colspan\", \"5\", \"rowspan\", \"3\", 1, \"text-left\", 2, \"padding\", \"15px\", \"text-align\", \"left\", \"border-right\", \"1px solid black\", \"border-top\", \"1px solid black\"], [\"align\", \"center\", \"colspan\", \"4\", \"rowspan\", \"3\", 1, \"text-right\", 2, \"padding\", \"0px\", \"text-align\", \"center\", \"border-right\", \"1px solid black\", \"border-top\", \"1px solid black\"], [2, \"width\", \"100%\", \"padding\", \"3px\", \"font-size\", \"13px\"], [2, \"color\", \"#fff\", \"width\", \"100%\", \"font-family\", \"Tohama\", \"font-size\", \"7pt\", 3, \"ngStyle\"], [\"align\", \"center\", \"colspan\", \"2\", 2, \"padding\", \"0px\", \"text-align\", \"right\", \"border-top\", \"1px solid black\"], [2, \"width\", \"100%\", \"padding\", \"3px\", \"font-size\", \"13px\", 3, \"ngStyle\"], [2, \"font-size\", \"13px\", \"width\", \"100%\", \"padding\", \"3px\", \"border-block-end\", \"1px solid\", \"border-top\", \"1px solid\", \"margin-bottom\", \"2px\", 3, \"ngStyle\"], [2, \"color\", \"#fff\", \"width\", \"100%\", \"border-top\", \"1px solid black\", \"font-family\", \"Tohama\", \"font-size\", \"7pt\", 3, \"ngStyle\"], [1, \"rcorners\", 2, \"width\", \"100%\", \"margin-top\", \"3px\", \"text-align\", \"center\", 3, \"ngStyle\"], [\"width\", \"100%\", \"cellspacing\", \"0\", \"cellpadding\", \"2\", \"align\", \"center\", 2, \"width\", \"100%\", \"text-align\", \"center\"], [\"align\", \"center\", \"valign\", \"top\", 2, \"padding\", \"15px\", \"width\", \"40%\", \"border-right\", \"1px solid black\", \"font-family\", \"Tohama\", \"font-size\", \"11pt\"], [\"align\", \"center\", \"valign\", \"top\", 2, \"padding\", \"15px\", \"width\", \"30%\", \"font-family\", \"Tohama\", \"font-size\", \"11pt\"], [\"valign\", \"bottom\", \"align\", \"left\"], [\"align\", \"center\", 2, \"padding\", \"15px\", \"width\", \"30%\", \"border-left\", \"1px solid black\", \"font-family\", \"Tohama\", \"font-size\", \"11pt\"], [\"valign\", \"top\", \"align\", \"left\"], [\"valign\", \"bottom\", \"align\", \"right\"]],\n        template: function PDFprintComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"button\", 1);\n            i0.ɵɵlistener(\"click\", function PDFprintComponent_Template_button_click_1_listener() {\n              return ctx.captureScreen();\n            });\n            i0.ɵɵtext(2, \"Download INV PDF\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(3, \"button\", 2);\n            i0.ɵɵlistener(\"click\", function PDFprintComponent_Template_button_click_3_listener() {\n              return ctx.toback();\n            });\n            i0.ɵɵtext(4, \"\\u0E01\\u0E25\\u0E31\\u0E1A\\u0E2B\\u0E19\\u0E49\\u0E32\\u0E2B\\u0E25\\u0E31\\u0E01\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(5, \"div\", 3)(6, \"select\", 4);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function PDFprintComponent_Template_select_ngModelChange_6_listener($event) {\n              i0.ɵɵtwoWayBindingSet(ctx.pageHeightmin, $event) || (ctx.pageHeightmin = $event);\n              return $event;\n            });\n            i0.ɵɵelementStart(7, \"option\", 5);\n            i0.ɵɵtext(8, \"--\\u0E01\\u0E23\\u0E13\\u0E35\\u0E42\\u0E2B\\u0E25\\u0E14 PDF \\u0E41\\u0E25\\u0E49\\u0E27\\u0E40\\u0E01\\u0E34\\u0E19\\u0E2B\\u0E19\\u0E49\\u0E32--\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(9, \"option\", 6);\n            i0.ɵɵtext(10, \"\\u0E40\\u0E01\\u0E34\\u0E19\\u0E2B\\u0E19\\u0E49\\u0E32\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(11, \"option\", 7);\n            i0.ɵɵtext(12, \"\\u0E04\\u0E48\\u0E32\\u0E40\\u0E23\\u0E34\\u0E48\\u0E21\\u0E15\\u0E49\\u0E19\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(13, \"option\", 8);\n            i0.ɵɵtext(14, \"\\u0E44\\u0E21\\u0E48\\u0E40\\u0E15\\u0E47\\u0E21\\u0E2B\\u0E19\\u0E49\\u0E32\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(15, \"option\", 8);\n            i0.ɵɵtext(16, \"Galaxy Tab S3\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(17, \"option\", 9);\n            i0.ɵɵtext(18, \"I5 SE\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(19, \"option\", 10);\n            i0.ɵɵtext(20, \"Microsoft Edge\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(21, \"div\", 11)(22, \"div\", 12);\n            i0.ɵɵtemplate(23, PDFprintComponent_div_23_Template, 2, 1, \"div\", 13)(24, PDFprintComponent_div_24_Template, 2, 1, \"div\", 13);\n            i0.ɵɵelementEnd()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(6);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.pageHeightmin);\n            i0.ɵɵadvance(17);\n            i0.ɵɵproperty(\"ngIf\", ctx.taxgroup);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", !ctx.taxgroup);\n          }\n        },\n        styles: [\".bordered[_ngcontent-%COMP%]{border:1px solid rgb(0,0,0);border-radius:6px;-moz-border-radius:6px 6px 6px 6px;-webkit-border-radius:6px 6px 6px 6px;box-sizing:content-box}.table2[_ngcontent-%COMP%]{border-spacing:0}.bordered[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]:first-child{border-radius:6px 0 0;-moz-border-radius:6px 0 0 0;-webkit-border-radius:6px 0 0 0}.bordered[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]:last-child{border-radius:0 6px 0 0;-moz-border-radius:0 6px 0 0;-webkit-border-radius:0 6px 0 0}.bordered[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]:first-child, .bordered[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]:first-child{border-left:medium none}.bordered[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]{background-color:#000;background-image:-moz-linear-gradient(center top,rgb(0,0,0),rgb(0,0,0));background-image:-webkit-gradient(linear,0 0,0 bottom,from(black),to(black),color-stop(.4,black));border-top:medium none;box-shadow:0 1px #000 inset;text-shadow:0 1px 0 rgb(0,0,0)}.bordered[_ngcontent-%COMP%]   td[_ngcontent-%COMP%], .bordered[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]{border-left:1px solid black;border-top:1px solid black;padding:10px;text-align:left}\"]\n      });\n    }\n  }\n  return PDFprintComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}