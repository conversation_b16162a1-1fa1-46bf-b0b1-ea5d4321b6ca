{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.createInvalidObservableTypeError = void 0;\nfunction createInvalidObservableTypeError(input) {\n  return new TypeError(\"You provided \" + (input !== null && typeof input === 'object' ? 'an invalid object' : \"'\" + input + \"'\") + \" where a stream was expected. You can provide an Observable, Promise, ReadableStream, Array, AsyncIterable, or Iterable.\");\n}\nexports.createInvalidObservableTypeError = createInvalidObservableTypeError;\n//# sourceMappingURL=throwUnobservableError.js.map", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}