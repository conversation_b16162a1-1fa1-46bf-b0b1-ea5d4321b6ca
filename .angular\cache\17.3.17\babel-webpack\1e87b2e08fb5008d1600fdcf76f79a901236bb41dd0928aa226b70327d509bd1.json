{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.startWith = void 0;\nvar concat_1 = require(\"../observable/concat\");\nvar args_1 = require(\"../util/args\");\nvar lift_1 = require(\"../util/lift\");\nfunction startWith() {\n  var values = [];\n  for (var _i = 0; _i < arguments.length; _i++) {\n    values[_i] = arguments[_i];\n  }\n  var scheduler = args_1.popScheduler(values);\n  return lift_1.operate(function (source, subscriber) {\n    (scheduler ? concat_1.concat(values, source, scheduler) : concat_1.concat(values, source)).subscribe(subscriber);\n  });\n}\nexports.startWith = startWith;\n//# sourceMappingURL=startWith.js.map", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}