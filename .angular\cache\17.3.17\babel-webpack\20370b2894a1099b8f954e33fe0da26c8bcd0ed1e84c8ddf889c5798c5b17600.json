{"ast": null, "code": "\"use strict\";\n\nvar __extends = this && this.__extends || function () {\n  var extendStatics = function (d, b) {\n    extendStatics = Object.setPrototypeOf || {\n      __proto__: []\n    } instanceof Array && function (d, b) {\n      d.__proto__ = b;\n    } || function (d, b) {\n      for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p];\n    };\n    return extendStatics(d, b);\n  };\n  return function (d, b) {\n    if (typeof b !== \"function\" && b !== null) throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n    extendStatics(d, b);\n    function __() {\n      this.constructor = d;\n    }\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n  };\n}();\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.BehaviorSubject = void 0;\nvar Subject_1 = require(\"./Subject\");\nvar BehaviorSubject = function (_super) {\n  __extends(BehaviorSubject, _super);\n  function BehaviorSubject(_value) {\n    var _this = _super.call(this) || this;\n    _this._value = _value;\n    return _this;\n  }\n  Object.defineProperty(BehaviorSubject.prototype, \"value\", {\n    get: function () {\n      return this.getValue();\n    },\n    enumerable: false,\n    configurable: true\n  });\n  BehaviorSubject.prototype._subscribe = function (subscriber) {\n    var subscription = _super.prototype._subscribe.call(this, subscriber);\n    !subscription.closed && subscriber.next(this._value);\n    return subscription;\n  };\n  BehaviorSubject.prototype.getValue = function () {\n    var _a = this,\n      hasError = _a.hasError,\n      thrownError = _a.thrownError,\n      _value = _a._value;\n    if (hasError) {\n      throw thrownError;\n    }\n    this._throwIfClosed();\n    return _value;\n  };\n  BehaviorSubject.prototype.next = function (value) {\n    _super.prototype.next.call(this, this._value = value);\n  };\n  return BehaviorSubject;\n}(Subject_1.Subject);\nexports.BehaviorSubject = BehaviorSubject;\n//# sourceMappingURL=BehaviorSubject.js.map", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}