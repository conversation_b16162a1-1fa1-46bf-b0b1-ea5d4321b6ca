{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.defaultIfEmpty = void 0;\nvar lift_1 = require(\"../util/lift\");\nvar OperatorSubscriber_1 = require(\"./OperatorSubscriber\");\nfunction defaultIfEmpty(defaultValue) {\n  return lift_1.operate(function (source, subscriber) {\n    var hasValue = false;\n    source.subscribe(OperatorSubscriber_1.createOperatorSubscriber(subscriber, function (value) {\n      hasValue = true;\n      subscriber.next(value);\n    }, function () {\n      if (!hasValue) {\n        subscriber.next(defaultValue);\n      }\n      subscriber.complete();\n    }));\n  });\n}\nexports.defaultIfEmpty = defaultIfEmpty;\n//# sourceMappingURL=defaultIfEmpty.js.map", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}