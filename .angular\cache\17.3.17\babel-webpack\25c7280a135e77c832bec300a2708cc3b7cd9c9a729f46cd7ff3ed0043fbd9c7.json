{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.NotFoundError = void 0;\nvar createErrorClass_1 = require(\"./createErrorClass\");\nexports.NotFoundError = createErrorClass_1.createErrorClass(function (_super) {\n  return function NotFoundErrorImpl(message) {\n    _super(this);\n    this.name = 'NotFoundError';\n    this.message = message;\n  };\n});\n//# sourceMappingURL=NotFoundError.js.map", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}