{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.publishBehavior = void 0;\nvar BehaviorSubject_1 = require(\"../BehaviorSubject\");\nvar ConnectableObservable_1 = require(\"../observable/ConnectableObservable\");\nfunction publishBehavior(initialValue) {\n  return function (source) {\n    var subject = new BehaviorSubject_1.BehaviorSubject(initialValue);\n    return new ConnectableObservable_1.ConnectableObservable(source, function () {\n      return subject;\n    });\n  };\n}\nexports.publishBehavior = publishBehavior;\n//# sourceMappingURL=publishBehavior.js.map", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}