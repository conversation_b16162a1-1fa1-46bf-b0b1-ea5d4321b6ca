{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.isScheduler = void 0;\nvar isFunction_1 = require(\"./isFunction\");\nfunction isScheduler(value) {\n  return value && isFunction_1.isFunction(value.schedule);\n}\nexports.isScheduler = isScheduler;\n//# sourceMappingURL=isScheduler.js.map", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}