{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.mergeScan = void 0;\nvar lift_1 = require(\"../util/lift\");\nvar mergeInternals_1 = require(\"./mergeInternals\");\nfunction mergeScan(accumulator, seed, concurrent) {\n  if (concurrent === void 0) {\n    concurrent = Infinity;\n  }\n  return lift_1.operate(function (source, subscriber) {\n    var state = seed;\n    return mergeInternals_1.mergeInternals(source, subscriber, function (value, index) {\n      return accumulator(state, value, index);\n    }, concurrent, function (value) {\n      state = value;\n    }, false, undefined, function () {\n      return state = null;\n    });\n  });\n}\nexports.mergeScan = mergeScan;\n//# sourceMappingURL=mergeScan.js.map", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}