{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.skipUntil = void 0;\nvar lift_1 = require(\"../util/lift\");\nvar OperatorSubscriber_1 = require(\"./OperatorSubscriber\");\nvar innerFrom_1 = require(\"../observable/innerFrom\");\nvar noop_1 = require(\"../util/noop\");\nfunction skipUntil(notifier) {\n  return lift_1.operate(function (source, subscriber) {\n    var taking = false;\n    var skipSubscriber = OperatorSubscriber_1.createOperatorSubscriber(subscriber, function () {\n      skipSubscriber === null || skipSubscriber === void 0 ? void 0 : skipSubscriber.unsubscribe();\n      taking = true;\n    }, noop_1.noop);\n    innerFrom_1.innerFrom(notifier).subscribe(skipSubscriber);\n    source.subscribe(OperatorSubscriber_1.createOperatorSubscriber(subscriber, function (value) {\n      return taking && subscriber.next(value);\n    }));\n  });\n}\nexports.skipUntil = skipUntil;\n//# sourceMappingURL=skipUntil.js.map", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}