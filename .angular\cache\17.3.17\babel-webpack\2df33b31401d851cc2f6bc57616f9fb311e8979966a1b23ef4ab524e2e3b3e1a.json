{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.pluck = void 0;\nvar map_1 = require(\"./map\");\nfunction pluck() {\n  var properties = [];\n  for (var _i = 0; _i < arguments.length; _i++) {\n    properties[_i] = arguments[_i];\n  }\n  var length = properties.length;\n  if (length === 0) {\n    throw new Error('list of properties cannot be empty.');\n  }\n  return map_1.map(function (x) {\n    var currentProp = x;\n    for (var i = 0; i < length; i++) {\n      var p = currentProp === null || currentProp === void 0 ? void 0 : currentProp[properties[i]];\n      if (typeof p !== 'undefined') {\n        currentProp = p;\n      } else {\n        return undefined;\n      }\n    }\n    return currentProp;\n  });\n}\nexports.pluck = pluck;\n//# sourceMappingURL=pluck.js.map", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}