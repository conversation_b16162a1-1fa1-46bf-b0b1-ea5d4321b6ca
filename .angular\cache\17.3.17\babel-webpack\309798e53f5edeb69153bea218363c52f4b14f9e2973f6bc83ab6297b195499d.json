{"ast": null, "code": "import { HttpClient } from '@angular/common/http';\nimport { Router } from '@angular/router';\nimport { NgbCalendar } from '@ng-bootstrap/ng-bootstrap';\nimport { Angular5Csv } from 'angular5-csv/dist/Angular5-csv';\nimport { BsModalService } from 'ngx-bootstrap/modal';\nimport { debounceTime, map } from 'rxjs/operators';\nimport { WebapiService } from './../webapi.service';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"ngx-bootstrap/modal\";\nimport * as i2 from \"@angular/common/http\";\nimport * as i3 from \"./../webapi.service\";\nimport * as i4 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i5 from \"@angular/router\";\nimport * as i6 from \"ngx-bootstrap/tooltip\";\nimport * as i7 from \"@angular/common\";\nimport * as i8 from \"../../../node_modules/@angular/forms/index\";\nimport * as i9 from \"ngx-bootstrap/datepicker\";\nimport * as i10 from \"ngx-bootstrap/tabs\";\nimport * as i11 from \"../topmenu/topmenu.component\";\nconst _c0 = () => ({\n  dateInputFormat: \"DD/MM/YYYY\"\n});\nconst _c1 = a0 => ({\n  \"display\": a0,\n  \"opacity\": 1\n});\nconst _c2 = a0 => ({\n  \"color\": a0\n});\nfunction SohistoryComponent_div_6_option_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 75);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r4 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", item_r4.groupid);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\" \", item_r4.groupid, \" (\", item_r4.name, \") \");\n  }\n}\nfunction SohistoryComponent_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 9)(1, \"select\", 71);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SohistoryComponent_div_6_Template_select_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.salegroup, $event) || (ctx_r2.salegroup = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementStart(2, \"option\", 72);\n    i0.ɵɵtext(3, \"--\\u0E40\\u0E25\\u0E37\\u0E2D\\u0E01\\u0E23\\u0E2B\\u0E31\\u0E2A\\u0E1E\\u0E19\\u0E31\\u0E01\\u0E07\\u0E32\\u0E19\\u0E02\\u0E32\\u0E22--\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"option\", 73);\n    i0.ɵɵtext(5, \"\\u0E40\\u0E25\\u0E37\\u0E2D\\u0E01\\u0E23\\u0E32\\u0E22\\u0E01\\u0E32\\u0E23\\u0E17\\u0E31\\u0E49\\u0E07\\u0E2B\\u0E21\\u0E14\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, SohistoryComponent_div_6_option_6_Template, 2, 3, \"option\", 74);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.salegroup);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.DateGroupsaleman);\n  }\n}\nfunction SohistoryComponent_ng_template_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"label\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const r_r5 = ctx.result;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"\", r_r5.name, \" (\", r_r5.accountnum, \")\");\n  }\n}\nfunction SohistoryComponent_button_58_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"button\", 76);\n    i0.ɵɵtext(1, \"Delete History\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SohistoryComponent_tr_98_td_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 79);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r7 = i0.ɵɵnextContext().$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(3, _c2, ctx_r2.getColorsdaft(item_r7.stcheck)))(\"ngStyle\", i0.ɵɵpureFunction1(5, _c2, ctx_r2.getColorFile(item_r7.filetype)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r7.textstatus);\n  }\n}\nfunction SohistoryComponent_tr_98_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\", 77)(1, \"td\", 78);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\", 78);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\", 78);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\", 79);\n    i0.ɵɵtext(8);\n    i0.ɵɵpipe(9, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"td\", 80);\n    i0.ɵɵpipe(11, \"date\");\n    i0.ɵɵtext(12);\n    i0.ɵɵpipe(13, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"td\", 79);\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"td\", 81);\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"td\", 81);\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"td\", 78);\n    i0.ɵɵtext(21);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"td\", 81);\n    i0.ɵɵtext(23);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"td\", 82);\n    i0.ɵɵtext(25);\n    i0.ɵɵpipe(26, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"td\", 82);\n    i0.ɵɵtext(28);\n    i0.ɵɵpipe(29, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"td\", 79);\n    i0.ɵɵtext(31);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"td\", 79);\n    i0.ɵɵtext(33);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(34, \"td\", 79);\n    i0.ɵɵtext(35);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(36, \"td\", 82);\n    i0.ɵɵtext(37);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(38, \"td\", 82);\n    i0.ɵɵtext(39);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(40, SohistoryComponent_tr_98_td_40_Template, 2, 7, \"td\", 83);\n    i0.ɵɵelementStart(41, \"td\", 84)(42, \"button\", 85);\n    i0.ɵɵlistener(\"click\", function SohistoryComponent_tr_98_Template_button_click_42_listener() {\n      const item_r7 = i0.ɵɵrestoreView(_r6).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      const templateView_r8 = i0.ɵɵreference(168);\n      return i0.ɵɵresetView(ctx_r2.getsaloderhistory(item_r7.id, templateView_r8, item_r7.filetype, item_r7.filename));\n    });\n    i0.ɵɵtext(43, \" View \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const item_r7 = ctx.$implicit;\n    const i_r9 = ctx.index;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(53, _c2, ctx_r2.getColorFile(item_r7.filetype)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i_r9 + 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(55, _c2, ctx_r2.getColorFile(item_r7.filetype)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r7.id);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(57, _c2, ctx_r2.getColorFile(item_r7.filetype)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r7.AXID);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(59, _c2, ctx_r2.getColorFile(item_r7.filetype)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(9, 36, item_r7.ShippingDateRequested, \"dd/MM/yyyy\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate1(\"tooltip\", \"\\u0E41\\u0E01\\u0E49\\u0E44\\u0E02\\u0E04\\u0E23\\u0E31\\u0E49\\u0E07\\u0E25\\u0E48\\u0E32\\u0E2A\\u0E38\\u0E14 \", i0.ɵɵpipeBind3(11, 39, item_r7.lastupdate, \"dd/MM/yyyy HH:mm:ss\", \"UTC\"), \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind3(13, 43, item_r7.timeedit, \"HH:mm:ss\", \"UTC\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(61, _c2, ctx_r2.getColorFile(item_r7.filetype)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r7.SalesId);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(63, _c2, ctx_r2.getColorFile(item_r7.filetype)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r7.salesname);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(65, _c2, ctx_r2.getColorFile(item_r7.filetype)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r7.InvName);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(67, _c2, ctx_r2.getColorFile(item_r7.filetype)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r7.regnum);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(69, _c2, ctx_r2.getColorFile(item_r7.filetype)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r7.DeliveryName);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(71, _c2, ctx_r2.getColorFile(item_r7.filetype)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(26, 47, item_r7.amount, \"1.2-2\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(73, _c2, ctx_r2.getColorFile(item_r7.filetype)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(29, 50, item_r7.price, \"1.2-2\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(75, _c2, ctx_r2.getColorFile(item_r7.filetype)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r7.vattype);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(77, _c2, ctx_r2.getColorFile(item_r7.filetype)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r7.DlvMode);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(79, _c2, ctx_r2.getColorFile(item_r7.filetype)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r7.paymenttype);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(81, _c2, ctx_r2.getColorFile(item_r7.filetype)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r7.CustomerRef);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(83, _c2, ctx_r2.getColorFile(item_r7.filetype)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r7.remark);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r7.stcheck == 0);\n  }\n}\nfunction SohistoryComponent_tr_99_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 86);\n    i0.ɵɵtext(2, \" \\u0E44\\u0E21\\u0E48\\u0E21\\u0E35\\u0E23\\u0E32\\u0E22\\u0E01\\u0E32\\u0E23 \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction SohistoryComponent_tr_144_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 87);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\", 88);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\", 87);\n    i0.ɵɵtext(6);\n    i0.ɵɵpipe(7, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"td\", 87);\n    i0.ɵɵtext(9);\n    i0.ɵɵpipe(10, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"td\", 87);\n    i0.ɵɵtext(12);\n    i0.ɵɵpipe(13, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"td\", 87);\n    i0.ɵɵtext(15);\n    i0.ɵɵpipe(16, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"td\", 89);\n    i0.ɵɵtext(18);\n    i0.ɵɵpipe(19, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"td\", 89);\n    i0.ɵɵtext(21);\n    i0.ɵɵpipe(22, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"td\", 89);\n    i0.ɵɵtext(24);\n    i0.ɵɵpipe(25, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"td\", 90);\n    i0.ɵɵtext(27);\n    i0.ɵɵpipe(28, \"number\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r10 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r10.ItemId);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r10.Name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(7, 13, item_r10.packqty, \"1.2-2\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(10, 16, item_r10.SalesQty, \"1.2-2\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(13, 19, item_r10.totalweight, \"1.2-2\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(16, 22, item_r10.PriceUnit, \"1.2-2\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(37, _c2, ctx_r2.getColordis1(item_r10.disstate)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind2(19, 25, item_r10.IVZ_Percent1_CT, \"1.2-2\"), \"%\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(39, _c2, ctx_r2.getColordis2(item_r10.disstate)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind2(22, 28, item_r10.IVZ_Percent2_CT, \"1.2-2\"), \"%\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(41, _c2, ctx_r2.getColordis3(item_r10.disstate)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind2(25, 31, item_r10.IVZ_Percent3_CT, \"1.2-2\"), \"%\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(28, 34, item_r10.LineAmount, \"1.2-2\"));\n  }\n}\nfunction SohistoryComponent_ng_template_167_tr_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 87);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\", 88);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\", 87);\n    i0.ɵɵtext(6);\n    i0.ɵɵpipe(7, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"td\", 87);\n    i0.ɵɵtext(9);\n    i0.ɵɵpipe(10, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"td\", 87);\n    i0.ɵɵtext(12);\n    i0.ɵɵpipe(13, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"td\", 87);\n    i0.ɵɵtext(15);\n    i0.ɵɵpipe(16, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"td\", 89);\n    i0.ɵɵtext(18);\n    i0.ɵɵpipe(19, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"td\", 89);\n    i0.ɵɵtext(21);\n    i0.ɵɵpipe(22, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"td\", 89);\n    i0.ɵɵtext(24);\n    i0.ɵɵpipe(25, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"td\", 90);\n    i0.ɵɵtext(27);\n    i0.ɵɵpipe(28, \"number\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r12 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r12.ItemId);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r12.Name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(7, 13, item_r12.packqty, \"1.2-2\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(10, 16, item_r12.SalesQty, \"1.2-2\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(13, 19, item_r12.totalweight, \"1.2-2\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(16, 22, item_r12.PriceUnit, \"1.2-2\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(37, _c2, ctx_r2.getColordis1(item_r12.disstate)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind2(19, 25, item_r12.IVZ_Percent1_CT, \"1.2-2\"), \"%\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(39, _c2, ctx_r2.getColordis2(item_r12.disstate)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind2(22, 28, item_r12.IVZ_Percent2_CT, \"1.2-2\"), \"%\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(41, _c2, ctx_r2.getColordis3(item_r12.disstate)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind2(25, 31, item_r12.IVZ_Percent3_CT, \"1.2-2\"), \"%\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(28, 34, item_r12.LineAmount, \"1.2-2\"));\n  }\n}\nfunction SohistoryComponent_ng_template_167_tab_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tab\", 98)(1, \"div\", 99)(2, \"form\", 100, 3);\n    i0.ɵɵelement(4, \"img\", 101);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"src\", ctx_r2.nameUrl, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction SohistoryComponent_ng_template_167_a_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 102);\n    i0.ɵɵtext(1, \"View PDF\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵpropertyInterpolate(\"href\", ctx_r2.nameUrl, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction SohistoryComponent_ng_template_167_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 54)(1, \"h4\", 91);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 92);\n    i0.ɵɵlistener(\"click\", function SohistoryComponent_ng_template_167_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.modalRef.hide());\n    });\n    i0.ɵɵelementStart(4, \"span\", 57);\n    i0.ɵɵtext(5, \"\\u00D7\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(6, \"div\", 93)(7, \"tabset\", null, 2)(9, \"tab\", 94)(10, \"div\", 59)(11, \"table\", 37)(12, \"thead\")(13, \"tr\")(14, \"th\", 60);\n    i0.ɵɵtext(15, \"\\u0E23\\u0E2B\\u0E31\\u0E2A\\u0E2A\\u0E34\\u0E19\\u0E04\\u0E49\\u0E32\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"th\", 60);\n    i0.ɵɵtext(17, \"\\u0E0A\\u0E37\\u0E48\\u0E2D\\u0E2A\\u0E34\\u0E19\\u0E04\\u0E49\\u0E32\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"th\", 61);\n    i0.ɵɵtext(19, \"Pack\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"th\", 61);\n    i0.ɵɵtext(21, \"\\u0E08\\u0E33\\u0E19\\u0E27\\u0E19\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"th\", 61);\n    i0.ɵɵtext(23, \"\\u0E19\\u0E49\\u0E33\\u0E2B\\u0E19\\u0E31\\u0E01\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"th\", 61);\n    i0.ɵɵtext(25, \"\\u0E23\\u0E32\\u0E04\\u0E32\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"th\", 62);\n    i0.ɵɵtext(27, \"\\u0E2A\\u0E48\\u0E27\\u0E19\\u0E25\\u0E14\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"th\", 61);\n    i0.ɵɵtext(29, \"\\u0E21\\u0E39\\u0E25\\u0E04\\u0E48\\u0E32\\u0E2A\\u0E38\\u0E17\\u0E18\\u0E34\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(30, \"tbody\");\n    i0.ɵɵtemplate(31, SohistoryComponent_ng_template_167_tr_31_Template, 29, 43, \"tr\", 63);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵtemplate(32, SohistoryComponent_ng_template_167_tab_32_Template, 5, 1, \"tab\", 95);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(33, \"div\", 64);\n    i0.ɵɵtemplate(34, SohistoryComponent_ng_template_167_a_34_Template, 2, 1, \"a\", 96);\n    i0.ɵɵelementStart(35, \"button\", 97);\n    i0.ɵɵlistener(\"click\", function SohistoryComponent_ng_template_167_Template_button_click_35_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.modalRef.hide());\n    });\n    i0.ɵɵtext(36, \"Close\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"Sale Oder Detail : \", ctx_r2.showIDso, \"\");\n    i0.ɵɵadvance(29);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.salelistview);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.Cktype && ctx_r2.CkNull);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.Cktype && ctx_r2.CkNull);\n  }\n}\nexport let SohistoryComponent = /*#__PURE__*/(() => {\n  class SohistoryComponent {\n    getColorst(disst) {\n      if (disst == 0) {\n        return '#05C1FF';\n      } else if (disst == 1) {\n        return '#FF9505';\n      } else if (disst == 2) {\n        return '#C305FF';\n      } else if (disst == 3) {\n        return '#FF05D5';\n      } else if (disst == 4) {\n        return '#0540FF';\n      } else if (disst == 5) {\n        return '#0AC103';\n      }\n    }\n    getColorsdaft(disst) {\n      if (disst == 0) {\n        return '#7FFF00';\n      }\n    }\n    getColordis1(disst) {\n      var st = disst;\n      var fi = st.substring(0, 1);\n      switch (fi) {\n        case '1':\n          return 'red';\n      }\n    }\n    getColordis2(disst) {\n      var st = disst;\n      var fi = st.substring(1, 2);\n      switch (fi) {\n        case '1':\n          return 'red';\n      }\n    }\n    getColordis3(disst) {\n      var st = disst;\n      var fi = st.substring(2, 3);\n      switch (fi) {\n        case '1':\n          return 'red';\n      }\n    }\n    constructor(modalService, http, service, calendar, router) {\n      this.modalService = modalService;\n      this.http = http;\n      this.service = service;\n      this.calendar = calendar;\n      this.router = router;\n      this.mdlSampleIsOpen = false;\n      this.alt = '';\n      this.checkreload = true;\n      this.options = {\n        fieldSeparator: ',',\n        quoteStrings: '\"',\n        decimalseparator: '.',\n        showLabels: true,\n        showTitle: true,\n        useBom: true,\n        noDownload: false,\n        headers: [\"เลขที่ SO\", \"วันที่\", \"พนักงานขาย\", 'ลูกค้า', 'มูลค่าสินค้า', 'มูลค่าสุทธิ', 'VAT/No VAT', \"ประเภทขนส่ง\", \"เงินสด/เครดิต\", \"Note ภายใน\", \"หมายเหตุ\", \"สถานะ\"]\n      };\n      this.sohitorylistfilter = [];\n      this.fromdate = '';\n      this.todate = '';\n      this.salegroup = '';\n      this.customer = '';\n      this.DateGroupsaleman = [];\n      this.Name = [];\n      this.typesync = '111';\n      this.dateshipping = '';\n      this.dateshippingto = '';\n      this.datalogin = [];\n      this.chackuser = false;\n      this.salelistview = [];\n      this.discustomer = false;\n      this.permisstiondata = [];\n      this.exportbtn = true;\n      this.searchbtn = true;\n      this.company = 'ค้นหาลูกค้า';\n      this.customers = [];\n      this.config = {\n        ignoreBackdropClick: false,\n        class: 'modal-lg'\n      };\n      this.nameUrl = '';\n      this.paymenttype = 'All';\n      this.Ckviewsohitorylistfilter = false;\n      this.deletehistorybtn = false;\n      this.search = text$ =>\n      //Autocomplete ลูกค้า\n      text$.pipe(debounceTime(200), map(term => term === '' ? [] : this.customers.filter(v => v.name.toLowerCase().indexOf(term.toLowerCase()) > -1 || v.accountnum.toLowerCase().indexOf(term.toLowerCase()) > -1).slice(0, 50)));\n      this.formatter = x => x.name + ' (' + x.accountnum + ')';\n      localStorage.removeItem('DataSOderreview');\n      localStorage.removeItem('DataSOderlist');\n      this.url = service.geturlservice();\n      this.Name = JSON.parse(sessionStorage.getItem('login'));\n      this.fromDate = calendar.getPrev(calendar.getToday(), 'd', 30);\n      this.toDate = calendar.getNext(calendar.getToday(), 'm', 3);\n      this.datalogin = JSON.parse(sessionStorage.getItem('login'));\n      this.fromdate = '';\n      this.todate = '';\n      this.Datatodate = new Date(this.toDate.year, this.toDate.month - 1, this.toDate.day);\n      this.Datafromdate = new Date(this.fromDate.year, this.fromDate.month - 1, this.fromDate.day);\n      this.getdate();\n      this.permisstiondata = JSON.parse(sessionStorage.getItem('menu'));\n      if (this.permisstiondata == null) {\n        this.router.navigate(['login']);\n      } else {\n        this.getuser();\n        this.searchsohistory();\n        this.exportbtn = !this.permisstiondata[6].flag_print;\n        this.searchbtn = !this.permisstiondata[6].flag_action;\n      }\n    }\n    getuser() {\n      if (this.datalogin[0].id_group_user == '153Admin' || this.datalogin[0].id_group_user == '1860Administrator') {\n        this.groupsale = '';\n        this.testclose = true;\n        this.deletehistorybtn = true;\n      } else {\n        this.testclose = false;\n        this.groupsale = this.datalogin[0].salegroup;\n      }\n    }\n    searchselect() {\n      // alert(this.typesync+'//'+this.paymenttype)\n      if (this.typesync === '111' && this.paymenttype === 'All') {\n        this.sohitorylistfilter = this.sohitorylist;\n      } else if (this.typesync == '0' || this.typesync == '1' || this.typesync == '2' || this.typesync == '3' || this.typesync == '4') {\n        if (this.paymenttype === 'All') {\n          this.sohitorylistfilter = this.sohitorylist.filter(v => v.state.toLowerCase().indexOf(this.typesync.toLowerCase()) > -1).slice(0, 2000);\n        } else {\n          this.sohitorylistfilter = this.sohitorylist.filter(v => v.state.toLowerCase().indexOf(this.typesync.toLowerCase()) > -1).filter(v => v.paymenttype.toLowerCase().indexOf(this.paymenttype.toLowerCase()) > -1).slice(0, 2000);\n        }\n        //this.sohitorylistfilter=this.sohitorylist.filter(v => v.state.toLowerCase().indexOf(this.typesync.toLowerCase()) && v.paymenttype.toLowerCase().indexOf(this.paymenttype.toLowerCase()) > -1).slice(0, 50);\n      } else {\n        var se = '';\n        if (this.typesync == '5') {\n          se = '0'; //\n        } else if (this.typesync == '6') {\n          se = '1';\n        } else if (this.typesync == '7') {\n          se = '3';\n        }\n        if (this.paymenttype === 'All') {\n          this.sohitorylistfilter = this.sohitorylist.filter(v => v.stcheck.toLowerCase().indexOf(se.toLowerCase()) > -1).slice(0, 2000);\n        } else {\n          this.sohitorylistfilter = this.sohitorylist.filter(v => v.stcheck.toLowerCase().indexOf(se.toLowerCase()) > -1).filter(v => v.paymenttype.toLowerCase().indexOf(this.paymenttype.toLowerCase()) > -1).slice(0, 2000);\n        }\n        this.Cknum();\n        //  this.sohitorylistfilter=this.sohitorylist.filter(v => v.stcheck.toLowerCase().indexOf(se.toLowerCase()) && v.paymenttype.toLowerCase().indexOf(this.paymenttype.toLowerCase()) > -1).slice(0, 50);\n      }\n    }\n    //paymenttype\n    searchpaymenttype() {\n      //alert(this.typesync+'//'+this.paymenttype)\n      if (this.typesync === '111' && this.paymenttype === 'All') {\n        this.sohitorylistfilter = this.sohitorylist;\n      } else if (this.typesync == '0' || this.typesync == '1' || this.typesync == '2' || this.typesync == '3' || this.typesync == '4') {\n        if (this.paymenttype === 'All') {\n          this.sohitorylistfilter = this.sohitorylist.filter(v => v.state.toLowerCase().indexOf(this.typesync.toLowerCase()) > -1).slice(0, 2000);\n        } else {\n          this.sohitorylistfilter = this.sohitorylist.filter(v => v.state.toLowerCase().indexOf(this.typesync.toLowerCase()) > -1).filter(v => v.paymenttype.toLowerCase().indexOf(this.paymenttype.toLowerCase()) > -1).slice(0, 2000);\n        }\n        //this.sohitorylistfilter=this.sohitorylist.filter(v => v.state.toLowerCase().indexOf(this.typesync.toLowerCase()) && v.paymenttype.toLowerCase().indexOf(this.paymenttype.toLowerCase()) > -1).slice(0, 50);\n      } else {\n        var se = '';\n        if (this.typesync == '5') {\n          se = '0'; //\n        } else if (this.typesync == '6') {\n          se = '1';\n        } else if (this.typesync == '7') {\n          se = '3';\n        }\n        if (this.paymenttype === 'All') {\n          this.sohitorylistfilter = this.sohitorylist.filter(v => v.stcheck.toLowerCase().indexOf(se.toLowerCase()) > -1).slice(0, 2000);\n        } else {\n          this.sohitorylistfilter = this.sohitorylist.filter(v => v.stcheck.toLowerCase().indexOf(se.toLowerCase()) > -1).filter(v => v.paymenttype.toLowerCase().indexOf(this.paymenttype.toLowerCase()) > -1).slice(0, 2000);\n        }\n      }\n      this.Cknum();\n    }\n    Cknum() {\n      if (this.sohitorylistfilter.length < 1) {\n        this.Ckviewsohitorylistfilter = true;\n      } else {\n        this.Ckviewsohitorylistfilter = false;\n      }\n    }\n    /* <option  class=\"text-black-50\" value=\"111\">ทุกรายการ</option>\n     <option  class=\"text-black-50\" value=\"0\">รอเข้าระบบ AX</option>\n    <option  class=\"text-black-50\" value=\"1\">Openorder</option>\n    <option class=\"text-black-50\" value=\"2\">Delivered</option>\n    <option class=\"text-black-50\" value=\"3\">Invoiced</option>\n    <option class=\"text-black-50\" value=\"4\">Canceled</option>\n    <option class=\"text-black-50\" value=\"5\">Order Draft</option>\n    <option class=\"text-black-50\" value=\"6\">รอ Sync</option>\n    <option class=\"text-black-50\" value=\"7\">กำลัง Sync</option> */\n    //Autocomplete ลูกค้า\n    /*text$.pipe(\n      debounceTime(200),\n      map(term => term === '' ? []\n        : this.customers.filter(v => v.name.toLowerCase().indexOf(term.toLowerCase()) > -1 ||  v.accountnum.toLowerCase().indexOf(term.toLowerCase()) > -1).slice(0, 50))\n    );\n    formatter = (x: {name: string,accountnum :string}) => x.name + ' ('+x.accountnum+')'; */\n    deletehistory() {\n      //alert(this.Deletefromdate);\n      if (this.Deletefromdate == undefined || this.Deletetodate == undefined) {\n        alert('กรุณาเลือกวันที่ ต้องการลบ Order History');\n        return;\n      }\n      var fmonth = this.Deletefromdate.getMonth() + 1;\n      var fday = this.Deletefromdate.getDate();\n      var fmonthst = '';\n      var fdayst = '';\n      var tmonth = this.Deletetodate.getMonth() + 1;\n      var tday = this.Deletetodate.getDate();\n      var tmonthst = '';\n      var tdayst = '';\n      if (fmonth <= 9) {\n        fmonthst = '0' + fmonth;\n      } else {\n        fmonthst = fmonth;\n      }\n      if (fday <= 9) {\n        fdayst = '0' + fday;\n      } else {\n        fdayst = fday;\n      }\n      if (tmonth <= 9) {\n        tmonthst = '0' + tmonth;\n      } else {\n        tmonthst = tmonth;\n      }\n      if (tday <= 9) {\n        tdayst = '0' + tday;\n      } else {\n        tdayst = tday;\n      }\n      var fromdaate = this.Deletefromdate.getFullYear() + '-' + fmonthst + '-' + fdayst;\n      var todate = this.Deletetodate.getFullYear() + '-' + tmonthst + '-' + tdayst;\n      if (confirm('ต้องการ ลบ Order History ใช่ หรือ ไม่')) {\n        const Http = new XMLHttpRequest();\n        const url = 'syncso/Service.asmx/DeleteHistory?D1=' + fromdaate + '&D2=' + todate;\n        Http.open(\"GET\", url);\n        Http.send();\n        Http.onreadystatechange = e => {\n          if (Http.readyState == 4 && Http.status == 200) {\n            alert('ลบ Order History เสร็จสิ้น');\n          }\n        };\n      }\n    }\n    getdate() {\n      this.fromdate = `${this.Datafromdate.getFullYear()}-${this.Datafromdate.getMonth() + 1}-${this.Datafromdate.getDate()}`;\n      this.todate = `${this.Datatodate.getFullYear()}-${this.Datatodate.getMonth() + 1}-${this.Datatodate.getDate()}`;\n    }\n    ngOnInit() {\n      if (this.Name[0].salegroup == 'admin') {\n        this.chackuser = true;\n      }\n      this.getuser();\n      this.getgroupsaleman();\n      this.getcostomerauto();\n      //this.Interval= setInterval(()=> this.Searchsohistory(this.datalogin[0].salegroup),300);\n    }\n    getcostomerauto() {\n      var idsale = this.datalogin[0].salegroup;\n      if (this.datalogin[0].id_group_user == '153Admin' || this.datalogin[0].id_group_user == '1860Administrator') {\n        idsale = '%20';\n      } else {\n        idsale = this.datalogin[0].salegroup;\n      }\n      this.http.get(this.url + 'customerauto/' + idsale).subscribe(res => {\n        this.customers = res;\n      });\n    }\n    gettype(type) {\n      if (type === \"application/pdf\") {\n        return true;\n      } else {\n        return false;\n      }\n    }\n    gettypeNull(type) {\n      if (type == \"\") {\n        return false;\n      } else {\n        return true;\n      }\n    }\n    getColorFile(type) {\n      if (type !== \"\") {\n        return '#0317ee';\n      }\n    }\n    getsaloderhistory(valueid, template, filetype, filename) {\n      this.salelistview = [];\n      this.modalRef = this.modalService.show(template, this.config);\n      this.http.get(this.url + 'find_saleline/' + valueid).subscribe(res => {\n        if (res.length > 0) {\n          this.salelistview = [];\n          this.salelistview = res;\n          this.Cktype = this.gettype(filetype);\n          this.CkNull = this.gettypeNull(filetype);\n          this.nameUrl = 'http://119.59.112.47/assets/PDF/' + filename;\n        }\n      });\n    }\n    Searchsohistory(saleid) {\n      this.getdate();\n      var datasalegroup = '';\n      if (this.fromdate == '') {\n        this.fromdate = `${this.fromDate}`;\n      }\n      if (this.todate == '') {\n        this.todate = `${this.toDate}`;\n      }\n      if (saleid == '') {\n        if (this.datalogin[0].salegroup == 'admin') {\n          datasalegroup = '%20';\n        } else {\n          datasalegroup = `${this.datalogin[0].salegroup}`;\n        }\n      }\n      if (saleid !== '') {\n        if (this.datalogin[0].salegroup == 'admin') {\n          datasalegroup = '%20';\n        } else {\n          datasalegroup = `${this.datalogin[0].salegroup}`;\n        }\n      }\n      if (datasalegroup == '') {\n        datasalegroup = '%20';\n      }\n      if (this.customer == '') {\n        this.customer = '%20';\n      }\n      if (this.customer == undefined) {\n        this.customer = '%20';\n      }\n      this.sohitorylist = [];\n      this.http.get(this.url + 'solist/' + this.fromdate + '/' + this.todate + '/' + datasalegroup + '/' + this.customer + '/4').subscribe(res => {\n        if (res.length > 0) {\n          if (datasalegroup == '%20') {\n            this.salegroup = '';\n          }\n          if (this.customer == '%20') {\n            this.customer = '';\n          }\n          this.sohitorylist = res;\n          this.sohitorylistfilter = this.sohitorylist;\n        } else {\n          if (datasalegroup == '%20') {\n            this.salegroup = '';\n          }\n          if (this.customer == '%20') {\n            this.customer = '';\n          }\n          this.sohitorylist = [];\n          alert('ไม่พบข้อมูลที่ค้นหา');\n          //this.openModal(true,'ไม่พบข้อมูลที่ค้นหา',false)\n        }\n        clearInterval(this.Interval);\n      });\n    }\n    searchsohistory() {\n      this.getdate();\n      if (this.getcustomer == undefined) {\n        this.customer = '';\n      } else {\n        this.customer = this.getcustomer.name;\n      }\n      var datasalegroup = '';\n      if (this.fromdate == '') {\n        this.fromdate = `${this.fromDate}`;\n      }\n      if (this.todate == '') {\n        this.todate = `${this.toDate}`;\n      }\n      if (this.groupsale == '') {\n        if (this.datalogin[0].id_group_user == '153Admin' || this.datalogin[0].id_group_user == '1860Administrator') {\n          datasalegroup = `${this.salegroup}`;\n        } else {\n          datasalegroup = `${this.datalogin[0].salegroup}`;\n        }\n      }\n      if (this.groupsale !== '') {\n        if (this.datalogin[0].id_group_user == '153Admin' || this.datalogin[0].id_group_user == '1860Administrator') {\n          datasalegroup = `${this.salegroup}`;\n        } else {\n          datasalegroup = `${this.datalogin[0].salegroup}`;\n        }\n      }\n      if (datasalegroup == '') {\n        datasalegroup = '%20';\n      }\n      if (this.customer == '') {\n        this.customer = '%20';\n      }\n      if (this.Name[0].accountnum != undefined) {\n        this.discustomer = true;\n        this.customer = this.Name[0].accountnum;\n      }\n      if (this.customer == undefined) {\n        this.customer = '%20';\n      }\n      this.sohitorylist = [];\n      this.http.get(this.url + 'solist/' + this.fromdate + '/' + this.todate + '/' + datasalegroup + '/' + this.customer + '/4').subscribe(res => {\n        if (res.length > 0) {\n          if (datasalegroup == '%20') {\n            this.salegroup = '';\n          }\n          if (this.customer == '%20') {\n            this.customer = '';\n          }\n          this.sohitorylist = res;\n          this.sohitorylistfilter = this.sohitorylist;\n        } else {\n          this.sohitorylist = [];\n          if (datasalegroup == '%20') {\n            this.salegroup = '';\n          }\n          if (this.customer == '%20') {\n            this.customer = '';\n          }\n          alert('ไม่พบข้อมูลที่ค้นหา');\n          //this.openModal(true,'ไม่พบข้อมูลที่ค้นหา',false)\n        }\n        clearInterval(this.Interval);\n      });\n    }\n    getgroupsaleman() {\n      this.DateGroupsaleman = [];\n      this.http.get(this.url + 'salesman').subscribe(res => {\n        if (res.length > 0) {\n          this.DateGroupsaleman = res;\n        } else {\n          alert('ไม่สามารถติดต่อข้อมูลรหัส saleman ได้');\n        }\n      });\n    }\n    exportdataexcel() {\n      if (this.sohitorylist == undefined) {\n        alert('ไม่พบข้อมูล');\n        //this.openModal(true,'ไม่พบข้อมูล',false);\n      } else {\n        new Angular5Csv(this.sohitorylist, 'Sohistory', this.options);\n      }\n    }\n    openModal(open, text, load) {\n      this.mdlSampleIsOpen = open;\n      this.alt = text;\n      this.checkreload = load;\n    }\n    closemodel(cl) {\n      this.mdlSampleIsOpen = cl;\n      if (this.checkreload == true) {\n        location.reload();\n      }\n    }\n    static {\n      this.ɵfac = function SohistoryComponent_Factory(t) {\n        return new (t || SohistoryComponent)(i0.ɵɵdirectiveInject(i1.BsModalService), i0.ɵɵdirectiveInject(i2.HttpClient), i0.ɵɵdirectiveInject(i3.WebapiService), i0.ɵɵdirectiveInject(i4.NgbCalendar), i0.ɵɵdirectiveInject(i5.Router));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: SohistoryComponent,\n        selectors: [[\"app-sohistory\"]],\n        decls: 169,\n        vars: 30,\n        consts: [[\"rt\", \"\"], [\"templateView\", \"\"], [\"staticTabs\", \"\"], [\"imageForm\", \"ngForm\"], [2, \"padding-top\", \"60px\"], [1, \"container-fluid\", \"col-md-12\", \"col-xs-12\", \"col-sm-12\", \"col-12\", 2, \"padding-right\", \"5px\", \"padding-left\", \"5px\"], [1, \"p-sm-1\", \"bg-secondary\", \"text-white\", \"text-center\", \"font-weight-light\", \"col-md-12\", \"col-xs-12\", \"col-sm-12\", \"col-12\"], [1, \"form-row\"], [\"class\", \"col-md-2 mb-2\", 4, \"ngIf\"], [1, \"col-md-2\", \"mb-2\"], [\"id\", \"typeahead-template\", \"type\", \"text\", \"name\", \"getcustomer\", 1, \"form-control\", \"form-control-sm\", 3, \"ngModelChange\", \"placeholder\", \"ngModel\", \"ngbTypeahead\", \"resultTemplate\", \"inputFormatter\"], [1, \"col-xs-12\", \"col-12\", \"col-md-2\", \"form-group\"], [\"type\", \"text\", \"placeholder\", \"DD/MM/YYYY\", \"bsDatepicker\", \"\", 1, \"form-control\", \"form-control-sm\", \"form-control-md\", 3, \"ngModelChange\", \"bsConfig\", \"ngModel\"], [1, \"col-xs-12\", \"col-12\", \"col-md-1\", \"form-group\"], [\"name\", \"\", \"id\", \"\", 1, \"custom-select\", \"custom-select-sm\", 3, \"change\", \"ngModelChange\", \"ngModel\"], [\"value\", \"111\", 1, \"text-black-50\"], [\"value\", \"1\", 1, \"text-black-50\"], [\"value\", \"2\", 1, \"text-black-50\"], [\"value\", \"3\", 1, \"text-black-50\"], [\"value\", \"4\", 1, \"text-black-50\"], [\"value\", \"5\", 1, \"text-black-50\"], [\"value\", \"6\", 1, \"text-black-50\"], [1, \"custom-select\", \"custom-select-sm\", 3, \"change\", \"ngModelChange\", \"ngModel\"], [\"value\", \"All\", 1, \"text-black-50\"], [\"value\", \"TT\", 1, \"text-black-50\"], [\"value\", \"N01\", 1, \"text-black-50\"], [\"value\", \"N07\", 1, \"text-black-50\"], [\"value\", \"COD\", 1, \"text-black-50\"], [\"value\", \"N15\", 1, \"text-black-50\"], [\"value\", \"N30\", 1, \"text-black-50\"], [\"value\", \"N60\", 1, \"text-black-50\"], [\"value\", \"N90\", 1, \"text-black-50\"], [\"value\", \"N120\", 1, \"text-black-50\"], [1, \"col-md-2\", \"mb-2\", \"col-12\", \"text-center\", \"text-sm-center\", \"text-md-left\", \"text-lg-left\", 2, \"padding-right\", \"0px\", \"padding-left\", \"0px\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", \"btn-sm\", \"font-weight-light\", 2, \"width\", \"60px\", 3, \"click\", \"disabled\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", \"btn-sm\", \"font-weight-light\", 2, \"margin-left\", \"3px\", \"width\", \"68px\", 3, \"click\", \"disabled\"], [\"style\", \"margin-left: 3px; width: 100px;\", \"data-toggle\", \"modal\", \"data-target\", \"#DeleteHistory\", \"aria-expanded\", \"true\", \"class\", \"btn btn-primary btn-sm font-weight-light\", \"type\", \"button\", 4, \"ngIf\"], [1, \"table\", \"table-hover\", \"table-bordered\", \"table-sm\"], [1, \"text-sm-center\", \"bg-light\"], [\"scope\", \"col\", 1, \"font-weight-normal\"], [\"scope\", \"col\", 1, \"font-weight-light\"], [\"class\", \"text-sm-left\", 4, \"ngFor\", \"ngForOf\"], [4, \"ngIf\"], [\"id\", \"mdlSample\", \"role\", \"dialog\", 1, \"modal\", \"fade\", 3, \"ngStyle\"], [1, \"modal-dialog\", \"modal-md\"], [1, \"modal-content\"], [1, \"modal-header\", \"colhaederal\"], [1, \"modal-title\"], [1, \"modal-body\"], [\"align\", \"right\", 1, \"modal-footer\"], [\"type\", \"button\", \"id\", \"btnClose\", 1, \"btn\", \"btn-danger\", 3, \"click\"], [1, \"fa\", \"fa-times\", \"fa-fw\"], [\"id\", \"ViewDetailSaleoder\", \"tabindex\", \"-1\", \"role\", \"dialog\", \"aria-labelledby\", \"exampleModalLabel\", \"aria-hidden\", \"true\", 1, \"modal\", \"fade\", \"bd-example-modal-lg\"], [\"role\", \"document\", 1, \"modal-dialog\", \"modal-lg\"], [1, \"modal-header\"], [\"id\", \"exampleModalLabel\", 1, \"modal-title\"], [\"type\", \"button\", \"data-dismiss\", \"modal\", \"aria-label\", \"Close\", 1, \"close\"], [\"aria-hidden\", \"true\"], [1, \"modal-body\", \"modal-lg\"], [2, \"overflow-x\", \"auto\"], [1, \"text-center\", \"font-weight-normal\"], [1, \"font-weight-normal\"], [\"colspan\", \"3\", 1, \"font-weight-normal\", \"text-center\"], [4, \"ngFor\", \"ngForOf\"], [1, \"modal-footer\"], [\"type\", \"button\", \"data-dismiss\", \"modal\", 1, \"btn\", \"btn-secondary\"], [\"id\", \"DeleteHistory\", \"tabindex\", \"-1\", \"role\", \"dialog\", \"aria-labelledby\", \"exampleModalLabel\", \"aria-hidden\", \"true\", 1, \"modal\", \"fade\", \"bd-example-modal-sm\"], [\"role\", \"document\", 1, \"modal-dialog\", \"modal-sm\"], [1, \"modal-body\", \"modal-sm\"], [1, \"col-xs-12\", \"col-12\", \"col-md-12\", \"form-group\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", 3, \"click\"], [\"multiple\", \"\", 1, \"custom-select\", \"custom-select-sm\", \"custom-select-md\", 3, \"ngModelChange\", \"ngModel\"], [\"selected\", \"\", \"value\", \"\"], [\"value\", \"%20\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [3, \"value\"], [\"data-toggle\", \"modal\", \"data-target\", \"#DeleteHistory\", \"aria-expanded\", \"true\", \"type\", \"button\", 1, \"btn\", \"btn-primary\", \"btn-sm\", \"font-weight-light\", 2, \"margin-left\", \"3px\", \"width\", \"100px\"], [1, \"text-sm-left\"], [1, \"text-sm-center\", \"font-weight-light\", 3, \"ngStyle\"], [1, \"text-sm-center\", \"font-weight-normal\", 3, \"ngStyle\"], [1, \"text-sm-center\", \"font-weight-normal\", 3, \"tooltip\"], [1, \"font-weight-normal\", 3, \"ngStyle\"], [1, \"text-sm-right\", \"font-weight-normal\", 3, \"ngStyle\"], [\"class\", \" text-sm-center font-weight-normal\", 3, \"ngStyle\", 4, \"ngIf\"], [1, \"text-sm-center\", \"font-weight-normal\"], [1, \"btn\", \"btn-link\", \"font-weight-normal\", 2, \"padding\", \"0pt\", 3, \"click\"], [\"colspan\", \"18\", 1, \"text-center\", \"font-weight-normal\", 2, \"text-align\", \"center\"], [1, \"text-center\", \"font-weight-sm\"], [1, \"text-left\", \"font-weight-sm\"], [1, \"text-right\", \"font-weight-sm\", 3, \"ngStyle\"], [1, \"text-right\", \"font-weight-sm\"], [1, \"modal-title\", \"pull-left\"], [\"type\", \"button\", \"aria-label\", \"Close\", 1, \"close\", \"pull-right\", 3, \"click\"], [1, \"modal-body\", 2, \"padding\", \"3px\"], [\"heading\", \"Sale Oder list\", 2, \"padding-top\", \"5px\"], [\"heading\", \"File\", 4, \"ngIf\"], [\"target\", \"_blank\", \"type\", \"button\", \"class\", \"btn btn-primary\", 3, \"href\", 4, \"ngIf\"], [\"type\", \"button\", 1, \"btn\", \"btn-secondary\", 3, \"click\"], [\"heading\", \"File\"], [1, \"card\", \"card-body\"], [2, \"text-align\", \"center\"], [2, \"width\", \"100%\", 3, \"src\"], [\"target\", \"_blank\", \"type\", \"button\", 1, \"btn\", \"btn-primary\", 3, \"href\"]],\n        template: function SohistoryComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            const _r1 = i0.ɵɵgetCurrentView();\n            i0.ɵɵelement(0, \"app-topmenu\");\n            i0.ɵɵelementStart(1, \"section\", 4)(2, \"div\", 5)(3, \"h5\", 6);\n            i0.ɵɵtext(4, \"Sale Order History\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(5, \"div\", 7);\n            i0.ɵɵtemplate(6, SohistoryComponent_div_6_Template, 7, 2, \"div\", 8);\n            i0.ɵɵelementStart(7, \"div\", 9);\n            i0.ɵɵtemplate(8, SohistoryComponent_ng_template_8_Template, 3, 2, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n            i0.ɵɵelementStart(10, \"input\", 10);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function SohistoryComponent_Template_input_ngModelChange_10_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.getcustomer, $event) || (ctx.getcustomer = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(11, \"div\", 11)(12, \"input\", 12);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function SohistoryComponent_Template_input_ngModelChange_12_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.Datafromdate, $event) || (ctx.Datafromdate = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(13, \"div\", 11)(14, \"input\", 12);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function SohistoryComponent_Template_input_ngModelChange_14_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.Datatodate, $event) || (ctx.Datatodate = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(15, \"div\", 13)(16, \"select\", 14);\n            i0.ɵɵlistener(\"change\", function SohistoryComponent_Template_select_change_16_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.searchselect());\n            });\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function SohistoryComponent_Template_select_ngModelChange_16_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.typesync, $event) || (ctx.typesync = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementStart(17, \"option\", 15);\n            i0.ɵɵtext(18, \"\\u0E17\\u0E38\\u0E01\\u0E23\\u0E32\\u0E22\\u0E01\\u0E32\\u0E23\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(19, \"option\", 16);\n            i0.ɵɵtext(20, \"Openorder\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(21, \"option\", 17);\n            i0.ɵɵtext(22, \"Delivered\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(23, \"option\", 18);\n            i0.ɵɵtext(24, \"Invoiced\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(25, \"option\", 19);\n            i0.ɵɵtext(26, \"Canceled\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(27, \"option\", 20);\n            i0.ɵɵtext(28, \"Order Draft\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(29, \"option\", 21);\n            i0.ɵɵtext(30, \"\\u0E23\\u0E2D\\u0E40\\u0E02\\u0E49\\u0E32\\u0E23\\u0E30\\u0E1A\\u0E1A AX\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(31, \"div\", 13)(32, \"select\", 22);\n            i0.ɵɵlistener(\"change\", function SohistoryComponent_Template_select_change_32_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.searchpaymenttype());\n            });\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function SohistoryComponent_Template_select_ngModelChange_32_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.paymenttype, $event) || (ctx.paymenttype = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementStart(33, \"option\", 23);\n            i0.ɵɵtext(34, \"\\u0E17\\u0E38\\u0E01\\u0E23\\u0E32\\u0E22\\u0E01\\u0E32\\u0E23\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(35, \"option\", 24);\n            i0.ɵɵtext(36, \"TT\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(37, \"option\", 25);\n            i0.ɵɵtext(38, \"N01\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(39, \"option\", 26);\n            i0.ɵɵtext(40, \"N07\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(41, \"option\", 27);\n            i0.ɵɵtext(42, \"COD\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(43, \"option\", 28);\n            i0.ɵɵtext(44, \"N15\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(45, \"option\", 29);\n            i0.ɵɵtext(46, \"N30\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(47, \"option\", 30);\n            i0.ɵɵtext(48, \"N60\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(49, \"option\", 31);\n            i0.ɵɵtext(50, \"N90\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(51, \"option\", 32);\n            i0.ɵɵtext(52, \"N120\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(53, \"div\", 33)(54, \"button\", 34);\n            i0.ɵɵlistener(\"click\", function SohistoryComponent_Template_button_click_54_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.searchsohistory());\n            });\n            i0.ɵɵtext(55, \"Search\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(56, \"button\", 35);\n            i0.ɵɵlistener(\"click\", function SohistoryComponent_Template_button_click_56_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.exportdataexcel());\n            });\n            i0.ɵɵtext(57, \"Export\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(58, SohistoryComponent_button_58_Template, 2, 0, \"button\", 36);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(59, \"table\", 37)(60, \"thead\")(61, \"tr\", 38)(62, \"th\", 39);\n            i0.ɵɵtext(63, \"Item\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(64, \"th\", 40);\n            i0.ɵɵtext(65, \"\\u0E40\\u0E25\\u0E02\\u0E17\\u0E35\\u0E48 SO\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(66, \"th\", 40);\n            i0.ɵɵtext(67, \"\\u0E40\\u0E25\\u0E02\\u0E17\\u0E35\\u0E48 SO AX\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(68, \"th\", 39);\n            i0.ɵɵtext(69, \"\\u0E27\\u0E31\\u0E19\\u0E17\\u0E35\\u0E48\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(70, \"th\", 39);\n            i0.ɵɵtext(71, \"\\u0E40\\u0E27\\u0E25\\u0E32\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(72, \"th\", 39);\n            i0.ɵɵtext(73, \"\\u0E1E\\u0E19\\u0E31\\u0E01\\u0E07\\u0E32\\u0E19\\u0E02\\u0E32\\u0E22\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(74, \"th\", 39);\n            i0.ɵɵtext(75, \"\\u0E25\\u0E39\\u0E01\\u0E04\\u0E49\\u0E32\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(76, \"th\", 39);\n            i0.ɵɵtext(77, \"\\u0E0A\\u0E37\\u0E48\\u0E2D INV\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(78, \"th\", 40);\n            i0.ɵɵtext(79, \"TAX ID\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(80, \"th\", 39);\n            i0.ɵɵtext(81, \"\\u0E0A\\u0E37\\u0E48\\u0E2D\\u0E02\\u0E19\\u0E2A\\u0E48\\u0E07\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(82, \"th\", 39);\n            i0.ɵɵtext(83, \"\\u0E21\\u0E39\\u0E25\\u0E04\\u0E48\\u0E32\\u0E2A\\u0E34\\u0E19\\u0E04\\u0E49\\u0E32\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(84, \"th\", 39);\n            i0.ɵɵtext(85, \"\\u0E21\\u0E39\\u0E25\\u0E04\\u0E48\\u0E32\\u0E2A\\u0E38\\u0E17\\u0E18\\u0E34\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(86, \"th\", 39);\n            i0.ɵɵtext(87, \"VAT/No VAT\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(88, \"th\", 39);\n            i0.ɵɵtext(89, \"\\u0E1B\\u0E23\\u0E30\\u0E40\\u0E20\\u0E17\\u0E02\\u0E19\\u0E2A\\u0E48\\u0E07\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(90, \"th\", 39);\n            i0.ɵɵtext(91, \"\\u0E40\\u0E07\\u0E34\\u0E19\\u0E2A\\u0E14/\\u0E40\\u0E04\\u0E23\\u0E14\\u0E34\\u0E15\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(92, \"th\", 39);\n            i0.ɵɵtext(93, \"Note \\u0E20\\u0E32\\u0E22\\u0E43\\u0E19\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(94, \"th\", 39);\n            i0.ɵɵtext(95, \"\\u0E2B\\u0E21\\u0E32\\u0E22\\u0E40\\u0E2B\\u0E15\\u0E38\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(96, \"th\", 39);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(97, \"tbody\");\n            i0.ɵɵtemplate(98, SohistoryComponent_tr_98_Template, 44, 85, \"tr\", 41)(99, SohistoryComponent_tr_99_Template, 3, 0, \"tr\", 42);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(100, \"div\", 43)(101, \"div\", 44)(102, \"div\", 45)(103, \"div\", 46)(104, \"h4\", 47);\n            i0.ɵɵtext(105, \"Report\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(106, \"div\", 48);\n            i0.ɵɵtext(107);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(108, \"div\", 49)(109, \"button\", 50);\n            i0.ɵɵlistener(\"click\", function SohistoryComponent_Template_button_click_109_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.closemodel(false));\n            });\n            i0.ɵɵelement(110, \"i\", 51);\n            i0.ɵɵtext(111, \" \\u0E1B\\u0E34\\u0E14\");\n            i0.ɵɵelementEnd()()()()();\n            i0.ɵɵelementStart(112, \"body\")(113, \"div\", 52)(114, \"div\", 53)(115, \"div\", 45)(116, \"div\", 54)(117, \"h5\", 55);\n            i0.ɵɵtext(118, \"Sale Oder Detail\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(119, \"button\", 56)(120, \"span\", 57);\n            i0.ɵɵtext(121, \"\\u00D7\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(122, \"div\", 58)(123, \"div\", 59)(124, \"table\", 37)(125, \"thead\")(126, \"tr\")(127, \"th\", 60);\n            i0.ɵɵtext(128, \"\\u0E23\\u0E2B\\u0E31\\u0E2A\\u0E2A\\u0E34\\u0E19\\u0E04\\u0E49\\u0E32\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(129, \"th\", 60);\n            i0.ɵɵtext(130, \"\\u0E0A\\u0E37\\u0E48\\u0E2D\\u0E2A\\u0E34\\u0E19\\u0E04\\u0E49\\u0E32\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(131, \"th\", 61);\n            i0.ɵɵtext(132, \"Pack\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(133, \"th\", 61);\n            i0.ɵɵtext(134, \"\\u0E08\\u0E33\\u0E19\\u0E27\\u0E19\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(135, \"th\", 61);\n            i0.ɵɵtext(136, \"\\u0E19\\u0E49\\u0E33\\u0E2B\\u0E19\\u0E31\\u0E01\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(137, \"th\", 61);\n            i0.ɵɵtext(138, \"\\u0E23\\u0E32\\u0E04\\u0E32\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(139, \"th\", 62);\n            i0.ɵɵtext(140, \"\\u0E2A\\u0E48\\u0E27\\u0E19\\u0E25\\u0E14\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(141, \"th\", 61);\n            i0.ɵɵtext(142, \"\\u0E21\\u0E39\\u0E25\\u0E04\\u0E48\\u0E32\\u0E2A\\u0E38\\u0E17\\u0E18\\u0E34\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(143, \"tbody\");\n            i0.ɵɵtemplate(144, SohistoryComponent_tr_144_Template, 29, 43, \"tr\", 63);\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(145, \"div\", 64)(146, \"button\", 65);\n            i0.ɵɵtext(147, \"Close\");\n            i0.ɵɵelementEnd()()()()();\n            i0.ɵɵelementStart(148, \"div\", 66)(149, \"div\", 67)(150, \"div\", 45)(151, \"div\", 54)(152, \"h5\", 55);\n            i0.ɵɵtext(153, \"DeleteHistory\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(154, \"button\", 56)(155, \"span\", 57);\n            i0.ɵɵtext(156, \"\\u00D7\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(157, \"div\", 68)(158, \"div\", 69)(159, \"input\", 12);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function SohistoryComponent_Template_input_ngModelChange_159_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.Deletefromdate, $event) || (ctx.Deletefromdate = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(160, \"div\", 69)(161, \"input\", 12);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function SohistoryComponent_Template_input_ngModelChange_161_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.Deletetodate, $event) || (ctx.Deletetodate = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(162, \"div\", 64)(163, \"button\", 65);\n            i0.ɵɵtext(164, \"Close\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(165, \"button\", 70);\n            i0.ɵɵlistener(\"click\", function SohistoryComponent_Template_button_click_165_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.deletehistory());\n            });\n            i0.ɵɵtext(166, \"Delete\");\n            i0.ɵɵelementEnd()()()()()()();\n            i0.ɵɵtemplate(167, SohistoryComponent_ng_template_167_Template, 37, 4, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n          }\n          if (rf & 2) {\n            const rt_r13 = i0.ɵɵreference(9);\n            i0.ɵɵadvance(6);\n            i0.ɵɵproperty(\"ngIf\", ctx.testclose);\n            i0.ɵɵadvance(4);\n            i0.ɵɵpropertyInterpolate(\"placeholder\", ctx.company);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.getcustomer);\n            i0.ɵɵproperty(\"ngbTypeahead\", ctx.search)(\"resultTemplate\", rt_r13)(\"inputFormatter\", ctx.formatter);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"bsConfig\", i0.ɵɵpureFunction0(24, _c0));\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.Datafromdate);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"bsConfig\", i0.ɵɵpureFunction0(25, _c0));\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.Datatodate);\n            i0.ɵɵadvance(2);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.typesync);\n            i0.ɵɵadvance(16);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.paymenttype);\n            i0.ɵɵadvance(22);\n            i0.ɵɵproperty(\"disabled\", ctx.searchbtn);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"disabled\", ctx.exportbtn);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngIf\", ctx.deletehistorybtn);\n            i0.ɵɵadvance(40);\n            i0.ɵɵproperty(\"ngForOf\", ctx.sohitorylistfilter);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.Ckviewsohitorylistfilter);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(26, _c1, ctx.mdlSampleIsOpen ? \"block\" : \"none\"));\n            i0.ɵɵadvance(7);\n            i0.ɵɵtextInterpolate(ctx.alt);\n            i0.ɵɵadvance(37);\n            i0.ɵɵproperty(\"ngForOf\", ctx.salelistview);\n            i0.ɵɵadvance(15);\n            i0.ɵɵproperty(\"bsConfig\", i0.ɵɵpureFunction0(28, _c0));\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.Deletefromdate);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"bsConfig\", i0.ɵɵpureFunction0(29, _c0));\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.Deletetodate);\n          }\n        },\n        dependencies: [i6.TooltipDirective, i7.NgForOf, i7.NgIf, i7.NgStyle, i8.ɵNgNoValidate, i8.NgSelectOption, i8.ɵNgSelectMultipleOption, i8.DefaultValueAccessor, i8.SelectControlValueAccessor, i8.SelectMultipleControlValueAccessor, i8.NgControlStatus, i8.NgControlStatusGroup, i8.NgModel, i8.NgForm, i4.NgbTypeahead, i9.BsDatepickerDirective, i9.BsDatepickerInputDirective, i10.TabDirective, i10.TabsetComponent, i11.TopmenuComponent, i7.DecimalPipe, i7.DatePipe]\n      });\n    }\n  }\n  return SohistoryComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}