{"ast": null, "code": "import { HttpClient } from '@angular/common/http';\nimport { Router } from '@angular/router';\nimport { Angular5Csv } from 'angular5-csv/dist/Angular5-csv';\nimport { debounceTime, map } from 'rxjs/operators';\nimport { WebapiService } from '../webapi.service';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/common/http\";\nimport * as i3 from \"../webapi.service\";\nconst _c0 = a0 => ({\n  \"display\": a0,\n  \"opacity\": 1\n});\nfunction ProductlistComponent_ng_template_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"label\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const r_r2 = ctx.result;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(r_r2.itemid);\n  }\n}\nfunction ProductlistComponent_ng_template_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"label\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const r_r3 = ctx.result;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(r_r3.name);\n  }\n}\nfunction ProductlistComponent_ng_template_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"b\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(2, \"!\");\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r3.name);\n  }\n}\nfunction ProductlistComponent_tr_48_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"th\", 31);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\", 31);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\", 31);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\", 31);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\", 31);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"td\", 31);\n    i0.ɵɵtext(12);\n    i0.ɵɵpipe(13, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"td\", 31);\n    i0.ɵɵtext(15);\n    i0.ɵɵpipe(16, \"number\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r6 = ctx.$implicit;\n    const i_r7 = ctx.index;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i_r7 + 1);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r6.inventlocationid);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r6.catname);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r6.itemid);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r6.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(13, 7, item_r6.taxpackagingqty, \"1.2-2\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(16, 10, item_r6.netweight, \"1.2-2\"));\n  }\n}\nexport let ProductlistComponent = /*#__PURE__*/(() => {\n  class ProductlistComponent {\n    constructor(router, http, service) {\n      this.router = router;\n      this.http = http;\n      this.service = service;\n      this.mdlSampleIsOpen = false;\n      this.alt = '';\n      this.checkreload = true;\n      this.options = {\n        fieldSeparator: ',',\n        quoteStrings: '\"',\n        decimalseparator: '.',\n        showLabels: true,\n        showTitle: true,\n        useBom: true,\n        noDownload: false,\n        headers: [\"คลัง\", \"รหัสกลุ่มสินค้า\", \"รหัสสินค้า\", \"กลุ่มสินค้า\", \"ชื่อสินค้า\", \"ราคาตั้ง\", \"จำนวนต่อ Pack\", \"หน่วยนับ\", \"หน่วยของแพ็ค\", \"น้ำหนัก\"]\n      };\n      this.Inventlocationid = '';\n      this.itemgroupid = '';\n      this.itemid = '';\n      this.catname = '';\n      this.nameproduct = '';\n      this.productlistauto = [];\n      this.permisstiondata = [];\n      this.exportbtn = true;\n      this.searchbtn = true;\n      this.search = text$ => text$.pipe(debounceTime(200), map(term => term === '' ? [] : this.productlistauto.filter(v => v.itemid.toLowerCase().indexOf(term.toLowerCase()) > -1).slice(0, 10)));\n      this.formatter = x => x.itemid;\n      this.searchname = text$ => text$.pipe(debounceTime(200), map(term => term === '' ? [] : this.productlistauto.filter(v => v.name.toLowerCase().indexOf(term.toLowerCase()) > -1).slice(0, 10)));\n      this.formattername = x => x.name;\n      localStorage.removeItem('DataSOderreview');\n      localStorage.removeItem('DataSOderlist');\n      this.name = 'กำลังนำเข้าข้อมูลกรุณารอสักครู่.....';\n      this.url = service.geturlservice();\n      /*this.permisstiondata=JSON.parse(sessionStorage.getItem('menu'))\n      this.salegroup=JSON.stringify(sessionStorage.getItem('salegroup'))\n      this.exportbtn=!this.permisstiondata[1].flag_print;\n      this.searchbtn=!this.permisstiondata[1].flag_action;*/\n      this.salegroup = JSON.stringify(sessionStorage.getItem('salegroup'));\n      this.permisstiondata = JSON.parse(sessionStorage.getItem('menu'));\n      if (this.permisstiondata == null) {\n        this.router.navigate(['login']);\n      } else {\n        this.permisstiondata = JSON.parse(sessionStorage.getItem('menu'));\n        this.salegroup = JSON.stringify(sessionStorage.getItem('salegroup'));\n        this.exportbtn = !this.permisstiondata[1].flag_print;\n        this.searchbtn = !this.permisstiondata[1].flag_action;\n      }\n    }\n    ngOnInit() {\n      this.getproductauto(this.salegroup);\n    }\n    getproductauto(accountnum) {\n      this.http.get(this.url + 'productauto/admin').subscribe(res => {\n        if (res.length > 0) {\n          this.productlistauto = res;\n        }\n      });\n    }\n    toggleWithGreeting(tooltip, greeting) {\n      if (tooltip.isOpen()) {\n        tooltip.close();\n      } else {\n        tooltip.open({\n          greeting\n        });\n      }\n    }\n    syncdataproduct(tool) {\n      this.toggleWithGreeting(tool, '');\n      const Http = new XMLHttpRequest();\n      const url = 'syncso/Service.asmx/PullingData?iFile=Product';\n      Http.open(\"GET\", url);\n      Http.send();\n      Http.onreadystatechange = e => {\n        if (Http.readyState == 4 && Http.status == 200) {\n          this.name = 'นำเข้าข้อมูล เสร็จสิ้น';\n          if (confirm('นำเข้าข้อมูล เสร็จสิ้น')) {\n            this.toggleWithGreeting(tool, '');\n          } else {\n            this.toggleWithGreeting(tool, '');\n          }\n        }\n      };\n    }\n    Searchporductlist() {\n      this.productlist = [];\n      var Inventlocationid = '';\n      var itemgroupid = '';\n      var itemid = '';\n      var catname = '';\n      var nameproduct = '';\n      if (this.itemcode == undefined || this.itemcode == '') {\n        this.itemid = '';\n      } else {\n        this.itemid = this.itemcode.itemid;\n      }\n      if (this.itemname == undefined || this.itemname == '') {\n        this.nameproduct = '';\n      } else {\n        this.nameproduct = this.itemname.itemid;\n      }\n      if (this.Inventlocationid == '') {\n        Inventlocationid = '%20';\n      } else {\n        Inventlocationid = this.Inventlocationid;\n      }\n      if (this.itemgroupid == '') {\n        itemgroupid = '%20';\n      } else {\n        itemgroupid = this.itemgroupid;\n      }\n      if (this.itemid == '') {\n        itemid = '%20';\n      } else {\n        itemid = this.itemid;\n      }\n      if (this.catname == '') {\n        catname = '%20';\n      } else {\n        catname = this.catname;\n      }\n      if (this.nameproduct == '') {\n        nameproduct = '%20';\n      } else {\n        nameproduct = this.nameproduct;\n      }\n      // var inventlocationid = req.body.inventlocationid;\n      // var itemgroupid = req.body.itemgroupid; +  + '/' +  + '/' +  + '/' +  + '/' +\n      // var itemid = req.body.itemid;\n      // var catname = req.body.catname;\n      // var name = req.body.name;\n      var body = {\n        inventlocationid: Inventlocationid == '%20' ? '' : Inventlocationid,\n        itemgroupid: itemgroupid == '%20' ? '' : itemgroupid,\n        itemid: itemid == '%20' ? '' : itemid,\n        catname: catname == '%20' ? '' : catname,\n        name: nameproduct == '%20' ? '' : nameproduct\n      };\n      this.http.post(this.url + 'product_list', body).subscribe(res => {\n        if (res.length > 0) {\n          this.productlist = res;\n          if (Inventlocationid == '%20') {\n            this.Inventlocationid = '';\n          }\n          if (itemgroupid == '%20') {\n            this.itemgroupid = '';\n          }\n          if (itemid == '%20') {\n            this.itemid = '';\n          }\n          if (catname == '%20') {\n            this.catname = '';\n          }\n          if (nameproduct == '%20') {\n            this.nameproduct = '';\n          }\n        } else {\n          if (Inventlocationid == '%20') {\n            this.Inventlocationid = '';\n          }\n          if (itemgroupid == '%20') {\n            this.itemgroupid = '';\n          }\n          if (itemid == '%20') {\n            this.itemid = '';\n          }\n          if (catname == '%20') {\n            this.catname = '';\n          }\n          if (nameproduct == '%20') {\n            this.nameproduct = '';\n          }\n          this.openModal(true, 'ไม่พบข้อมูลที่ค้นหา', false);\n        }\n      });\n    }\n    exportdataexcel() {\n      if (this.productlist == undefined) {\n        this.openModal(true, 'ไม่พบข้อมูล', false);\n      }\n      new Angular5Csv(this.productlist, 'ProduceList', this.options);\n    }\n    openModal(open, text, load) {\n      this.mdlSampleIsOpen = open;\n      this.alt = text;\n      this.checkreload = load;\n    }\n    closemodel(cl) {\n      this.mdlSampleIsOpen = cl;\n      if (this.checkreload == true) {\n        location.reload();\n      }\n    }\n    static {\n      this.ɵfac = function ProductlistComponent_Factory(t) {\n        return new (t || ProductlistComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.HttpClient), i0.ɵɵdirectiveInject(i3.WebapiService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: ProductlistComponent,\n        selectors: [[\"app-productlist\"]],\n        decls: 61,\n        vars: 20,\n        consts: [[\"rt\", \"\"], [\"rtname\", \"\"], [\"tipContent\", \"\"], [\"t2\", \"ngbTooltip\"], [1, \"container-fluid\", 2, \"padding-right\", \"5px\", \"padding-left\", \"5px\"], [2, \"padding-top\", \"60px\"], [1, \"p-sm-1\", \"bg-secondary\", \"text-white\", \"text-center\"], [1, \"form-row\"], [1, \"col-md-2\", \"mb-2\"], [\"id\", \"Inventlocationid\", \"type\", \"text\", \"name\", \"Inventlocationid\", \"placeholder\", \"\\u0E04\\u0E49\\u0E19\\u0E2B\\u0E32\\u0E04\\u0E25\\u0E31\\u0E07\", 1, \"form-control\", \"form-control-sm\", 3, \"ngModelChange\", \"ngModel\"], [\"id\", \"itemgroupid\", \"type\", \"text\", \"name\", \"itemgroupid\", \"placeholder\", \"\\u0E04\\u0E49\\u0E19\\u0E2B\\u0E32\\u0E23\\u0E2B\\u0E31\\u0E2A\\u0E01\\u0E25\\u0E38\\u0E48\\u0E21\\u0E2A\\u0E34\\u0E19\\u0E04\\u0E49\\u0E32\", 1, \"form-control\", \"form-control-sm\", 3, \"ngModelChange\", \"ngModel\"], [\"id\", \"typeahead-template\", \"placeholder\", \"\\u0E23\\u0E2B\\u0E31\\u0E2A\\u0E2A\\u0E34\\u0E19\\u0E04\\u0E49\\u0E32\", \"type\", \"text\", \"name\", \"itemcode\", 1, \"form-control\", \"form-control-sm\", 3, \"ngModelChange\", \"ngModel\", \"ngbTypeahead\", \"resultTemplate\", \"inputFormatter\"], [\"id\", \"catname\", \"type\", \"text\", \"name\", \"catname\", \"placeholder\", \"\\u0E04\\u0E49\\u0E19\\u0E2B\\u0E32\\u0E01\\u0E25\\u0E38\\u0E48\\u0E21\\u0E2A\\u0E34\\u0E19\\u0E04\\u0E49\\u0E32\", 1, \"form-control\", \"form-control-sm\", 3, \"ngModelChange\", \"ngModel\"], [\"id\", \"typeahead-template\", \"placeholder\", \"\\u0E0A\\u0E37\\u0E48\\u0E2D\\u0E2A\\u0E34\\u0E19\\u0E04\\u0E49\\u0E32\", \"type\", \"text\", \"name\", \"itemname\", 1, \"form-control\", \"form-control-sm\", 3, \"ngModelChange\", \"ngModel\", \"ngbTypeahead\", \"resultTemplate\", \"inputFormatter\"], [1, \"col-md-2\", \"mb-3\", \"col-12\", \"text-center\", \"text-sm-center\", \"text-md-center\", \"text-lg-left\", 2, \"padding-right\", \"0px\", \"padding-left\", \"0px\"], [\"type\", \"submit\", 1, \"btn\", \"btn-primary\", \"btn-sm\", \"font-weight-light\", 2, \"width\", \"60px\", 3, \"click\", \"disabled\"], [\"type\", \"submit\", 1, \"btn\", \"btn-primary\", \"btn-sm\", \"font-weight-light\", 2, \"margin-left\", \"3px\", \"width\", \"60px\", 3, \"click\", \"disabled\"], [\"triggers\", \"manual\", 1, \"btn\", \"btn-primary\", \"btn-sm\", \"font-weight-light\", 2, \"margin-left\", \"3px\", \"width\", \"60px\", 3, \"click\", \"disabled\", \"ngbTooltip\"], [1, \"table\", \"table-hover\", \"table-bordered\", \"table-sm\"], [1, \"text-sm-center\", \"font-weight-light\", \"bg-light\"], [\"scope\", \"col\", 1, \"font-weight-normal\"], [4, \"ngFor\", \"ngForOf\"], [\"id\", \"mdlSample\", \"role\", \"dialog\", 1, \"modal\", \"fade\", 3, \"ngStyle\"], [1, \"modal-dialog\", \"modal-md\"], [1, \"modal-content\"], [1, \"modal-header\", \"colhaederal\"], [1, \"modal-title\"], [1, \"modal-body\"], [\"align\", \"right\", 1, \"modal-footer\"], [\"type\", \"button\", \"id\", \"btnClose\", 1, \"btn\", \"btn-danger\", 3, \"click\"], [1, \"fa\", \"fa-times\", \"fa-fw\"], [1, \"text-sm-center\", \"font-weight-normal\"]],\n        template: function ProductlistComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            const _r1 = i0.ɵɵgetCurrentView();\n            i0.ɵɵelement(0, \"app-topmenu\");\n            i0.ɵɵelementStart(1, \"div\", 4)(2, \"section\", 5)(3, \"h5\", 6);\n            i0.ɵɵtext(4, \"Product List\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(5, \"div\", 7)(6, \"div\", 8)(7, \"input\", 9);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function ProductlistComponent_Template_input_ngModelChange_7_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.Inventlocationid, $event) || (ctx.Inventlocationid = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(8, \"div\", 8)(9, \"input\", 10);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function ProductlistComponent_Template_input_ngModelChange_9_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.itemgroupid, $event) || (ctx.itemgroupid = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(10, \"div\", 8);\n            i0.ɵɵtemplate(11, ProductlistComponent_ng_template_11_Template, 2, 1, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n            i0.ɵɵelementStart(13, \"input\", 11);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function ProductlistComponent_Template_input_ngModelChange_13_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.itemcode, $event) || (ctx.itemcode = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(14, \"div\", 8)(15, \"input\", 12);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function ProductlistComponent_Template_input_ngModelChange_15_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.catname, $event) || (ctx.catname = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(16, \"div\", 8);\n            i0.ɵɵtemplate(17, ProductlistComponent_ng_template_17_Template, 2, 1, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n            i0.ɵɵelementStart(19, \"input\", 13);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function ProductlistComponent_Template_input_ngModelChange_19_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.itemname, $event) || (ctx.itemname = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(20, \"div\", 14)(21, \"button\", 15);\n            i0.ɵɵlistener(\"click\", function ProductlistComponent_Template_button_click_21_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.Searchporductlist());\n            });\n            i0.ɵɵtext(22, \"Search\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(23, \"button\", 16);\n            i0.ɵɵlistener(\"click\", function ProductlistComponent_Template_button_click_23_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.exportdataexcel());\n            });\n            i0.ɵɵtext(24, \"Export\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(25, ProductlistComponent_ng_template_25_Template, 3, 1, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor);\n            i0.ɵɵelementStart(27, \"button\", 17, 3);\n            i0.ɵɵlistener(\"click\", function ProductlistComponent_Template_button_click_27_listener() {\n              i0.ɵɵrestoreView(_r1);\n              const t2_r5 = i0.ɵɵreference(28);\n              return i0.ɵɵresetView(ctx.syncdataproduct(t2_r5));\n            });\n            i0.ɵɵtext(29, \"Import\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(30, \"table\", 18)(31, \"thead\")(32, \"tr\", 19)(33, \"th\", 20);\n            i0.ɵɵtext(34, \"\\u0E25\\u0E33\\u0E14\\u0E31\\u0E1A\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(35, \"th\", 20);\n            i0.ɵɵtext(36, \"\\u0E04\\u0E25\\u0E31\\u0E07\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(37, \"th\", 20);\n            i0.ɵɵtext(38, \"\\u0E01\\u0E25\\u0E38\\u0E48\\u0E21\\u0E2A\\u0E34\\u0E19\\u0E04\\u0E49\\u0E32\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(39, \"th\", 20);\n            i0.ɵɵtext(40, \"\\u0E23\\u0E2B\\u0E31\\u0E2A\\u0E2A\\u0E34\\u0E19\\u0E04\\u0E49\\u0E32\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(41, \"th\", 20);\n            i0.ɵɵtext(42, \"\\u0E0A\\u0E37\\u0E48\\u0E2D\\u0E2A\\u0E34\\u0E19\\u0E04\\u0E49\\u0E32\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(43, \"th\", 20);\n            i0.ɵɵtext(44, \"\\u0E08\\u0E33\\u0E19\\u0E27\\u0E19\\u0E15\\u0E48\\u0E2D Pack\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(45, \"th\", 20);\n            i0.ɵɵtext(46, \"\\u0E19\\u0E49\\u0E33\\u0E2B\\u0E19\\u0E31\\u0E01\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(47, \"tbody\");\n            i0.ɵɵtemplate(48, ProductlistComponent_tr_48_Template, 17, 13, \"tr\", 21);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(49, \"div\", 22)(50, \"div\", 23)(51, \"div\", 24)(52, \"div\", 25)(53, \"h4\", 26);\n            i0.ɵɵtext(54, \"Report\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(55, \"div\", 27);\n            i0.ɵɵtext(56);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(57, \"div\", 28)(58, \"button\", 29);\n            i0.ɵɵlistener(\"click\", function ProductlistComponent_Template_button_click_58_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.closemodel(false));\n            });\n            i0.ɵɵelement(59, \"i\", 30);\n            i0.ɵɵtext(60, \" \\u0E1B\\u0E34\\u0E14\");\n            i0.ɵɵelementEnd()()()()()()();\n          }\n          if (rf & 2) {\n            const rt_r8 = i0.ɵɵreference(12);\n            const rtname_r9 = i0.ɵɵreference(18);\n            const tipContent_r10 = i0.ɵɵreference(26);\n            i0.ɵɵadvance(7);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.Inventlocationid);\n            i0.ɵɵadvance(2);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.itemgroupid);\n            i0.ɵɵadvance(4);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.itemcode);\n            i0.ɵɵproperty(\"ngbTypeahead\", ctx.search)(\"resultTemplate\", rt_r8)(\"inputFormatter\", ctx.formatter);\n            i0.ɵɵadvance(2);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.catname);\n            i0.ɵɵadvance(4);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.itemname);\n            i0.ɵɵproperty(\"ngbTypeahead\", ctx.searchname)(\"resultTemplate\", rtname_r9)(\"inputFormatter\", ctx.formattername);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"disabled\", ctx.searchbtn);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"disabled\", ctx.exportbtn);\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"disabled\", ctx.searchbtn)(\"ngbTooltip\", tipContent_r10);\n            i0.ɵɵadvance(21);\n            i0.ɵɵproperty(\"ngForOf\", ctx.productlist);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(18, _c0, ctx.mdlSampleIsOpen ? \"block\" : \"none\"));\n            i0.ɵɵadvance(7);\n            i0.ɵɵtextInterpolate(ctx.alt);\n          }\n        }\n      });\n    }\n  }\n  return ProductlistComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}