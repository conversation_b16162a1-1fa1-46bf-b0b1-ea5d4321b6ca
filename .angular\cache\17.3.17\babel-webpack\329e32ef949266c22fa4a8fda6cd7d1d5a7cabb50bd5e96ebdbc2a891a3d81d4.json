{"ast": null, "code": "/*\n * classList.js: Cross-browser full element.classList implementation.\n * 1.1.20150312\n *\n * By <PERSON>, http://eligrey.com\n * License: Dedicated to the public domain.\n *   See https://github.com/eligrey/classList.js/blob/master/LICENSE.md\n */\n\n/*global self, document, DOMException */\n\n/*! @source http://purl.eligrey.com/github/classList.js/blob/master/classList.js */\n\nif (\"document\" in self) {\n  // Full polyfill for browsers with no classList support\n  // Including IE < Edge missing SVGElement.classList\n  if (!(\"classList\" in document.createElement(\"_\")) || document.createElementNS && !(\"classList\" in document.createElementNS(\"http://www.w3.org/2000/svg\", \"g\"))) {\n    (function (view) {\n      \"use strict\";\n\n      if (!('Element' in view)) return;\n      var classListProp = \"classList\",\n        protoProp = \"prototype\",\n        elemCtrProto = view.Element[protoProp],\n        objCtr = Object,\n        strTrim = String[protoProp].trim || function () {\n          return this.replace(/^\\s+|\\s+$/g, \"\");\n        },\n        arrIndexOf = Array[protoProp].indexOf || function (item) {\n          var i = 0,\n            len = this.length;\n          for (; i < len; i++) {\n            if (i in this && this[i] === item) {\n              return i;\n            }\n          }\n          return -1;\n        }\n        // Vendors: please allow content code to instantiate DOMExceptions\n        ,\n        DOMEx = function (type, message) {\n          this.name = type;\n          this.code = DOMException[type];\n          this.message = message;\n        },\n        checkTokenAndGetIndex = function (classList, token) {\n          if (token === \"\") {\n            throw new DOMEx(\"SYNTAX_ERR\", \"An invalid or illegal string was specified\");\n          }\n          if (/\\s/.test(token)) {\n            throw new DOMEx(\"INVALID_CHARACTER_ERR\", \"String contains an invalid character\");\n          }\n          return arrIndexOf.call(classList, token);\n        },\n        ClassList = function (elem) {\n          var trimmedClasses = strTrim.call(elem.getAttribute(\"class\") || \"\"),\n            classes = trimmedClasses ? trimmedClasses.split(/\\s+/) : [],\n            i = 0,\n            len = classes.length;\n          for (; i < len; i++) {\n            this.push(classes[i]);\n          }\n          this._updateClassName = function () {\n            elem.setAttribute(\"class\", this.toString());\n          };\n        },\n        classListProto = ClassList[protoProp] = [],\n        classListGetter = function () {\n          return new ClassList(this);\n        };\n      // Most DOMException implementations don't allow calling DOMException's toString()\n      // on non-DOMExceptions. Error's toString() is sufficient here.\n      DOMEx[protoProp] = Error[protoProp];\n      classListProto.item = function (i) {\n        return this[i] || null;\n      };\n      classListProto.contains = function (token) {\n        token += \"\";\n        return checkTokenAndGetIndex(this, token) !== -1;\n      };\n      classListProto.add = function () {\n        var tokens = arguments,\n          i = 0,\n          l = tokens.length,\n          token,\n          updated = false;\n        do {\n          token = tokens[i] + \"\";\n          if (checkTokenAndGetIndex(this, token) === -1) {\n            this.push(token);\n            updated = true;\n          }\n        } while (++i < l);\n        if (updated) {\n          this._updateClassName();\n        }\n      };\n      classListProto.remove = function () {\n        var tokens = arguments,\n          i = 0,\n          l = tokens.length,\n          token,\n          updated = false,\n          index;\n        do {\n          token = tokens[i] + \"\";\n          index = checkTokenAndGetIndex(this, token);\n          while (index !== -1) {\n            this.splice(index, 1);\n            updated = true;\n            index = checkTokenAndGetIndex(this, token);\n          }\n        } while (++i < l);\n        if (updated) {\n          this._updateClassName();\n        }\n      };\n      classListProto.toggle = function (token, force) {\n        token += \"\";\n        var result = this.contains(token),\n          method = result ? force !== true && \"remove\" : force !== false && \"add\";\n        if (method) {\n          this[method](token);\n        }\n        if (force === true || force === false) {\n          return force;\n        } else {\n          return !result;\n        }\n      };\n      classListProto.toString = function () {\n        return this.join(\" \");\n      };\n      if (objCtr.defineProperty) {\n        var classListPropDesc = {\n          get: classListGetter,\n          enumerable: true,\n          configurable: true\n        };\n        try {\n          objCtr.defineProperty(elemCtrProto, classListProp, classListPropDesc);\n        } catch (ex) {\n          // IE 8 doesn't support enumerable:true\n          if (ex.number === -0x7FF5EC54) {\n            classListPropDesc.enumerable = false;\n            objCtr.defineProperty(elemCtrProto, classListProp, classListPropDesc);\n          }\n        }\n      } else if (objCtr[protoProp].__defineGetter__) {\n        elemCtrProto.__defineGetter__(classListProp, classListGetter);\n      }\n    })(self);\n  } else {\n    // There is full or partial native classList support, so just check if we need\n    // to normalize the add/remove and toggle APIs.\n\n    (function () {\n      \"use strict\";\n\n      var testElement = document.createElement(\"_\");\n      testElement.classList.add(\"c1\", \"c2\");\n\n      // Polyfill for IE 10/11 and Firefox <26, where classList.add and\n      // classList.remove exist but support only one argument at a time.\n      if (!testElement.classList.contains(\"c2\")) {\n        var createMethod = function (method) {\n          var original = DOMTokenList.prototype[method];\n          DOMTokenList.prototype[method] = function (token) {\n            var i,\n              len = arguments.length;\n            for (i = 0; i < len; i++) {\n              token = arguments[i];\n              original.call(this, token);\n            }\n          };\n        };\n        createMethod('add');\n        createMethod('remove');\n      }\n      testElement.classList.toggle(\"c3\", false);\n\n      // Polyfill for IE 10 and Firefox <24, where classList.toggle does not\n      // support the second argument.\n      if (testElement.classList.contains(\"c3\")) {\n        var _toggle = DOMTokenList.prototype.toggle;\n        DOMTokenList.prototype.toggle = function (token, force) {\n          if (1 in arguments && !this.contains(token) === !force) {\n            return force;\n          } else {\n            return _toggle.call(this, token);\n          }\n        };\n      }\n      testElement = null;\n    })();\n  }\n}", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}