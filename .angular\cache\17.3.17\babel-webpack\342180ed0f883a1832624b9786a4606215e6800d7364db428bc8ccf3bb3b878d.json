{"ast": null, "code": "function mod(n, x) {\n  return (n % x + x) % x;\n}\nfunction absFloor(num) {\n  return num < 0 ? Math.ceil(num) || 0 : Math.floor(num);\n}\nfunction isString(str) {\n  return typeof str === 'string';\n}\nfunction isDate(value) {\n  return value instanceof Date || Object.prototype.toString.call(value) === '[object Date]';\n}\nfunction isBoolean(value) {\n  return value === true || value === false;\n}\nfunction isDateValid(date) {\n  return date && date.getTime && !isNaN(date.getTime());\n}\n// eslint-disable-next-line @typescript-eslint/ban-types\nfunction isFunction(fn) {\n  return fn instanceof Function || Object.prototype.toString.call(fn) === '[object Function]';\n}\nfunction isNumber(value) {\n  return typeof value === 'number' || Object.prototype.toString.call(value) === '[object Number]';\n}\nfunction isArray(input) {\n  return input instanceof Array || Object.prototype.toString.call(input) === '[object Array]';\n}\nfunction hasOwnProp(a /*object*/, b) {\n  return Object.prototype.hasOwnProperty.call(a, b);\n}\nfunction isObject(input /*object*/) {\n  // IE8 will treat undefined and null as object if it wasn't for\n  // input != null\n  return input != null && Object.prototype.toString.call(input) === '[object Object]';\n}\nfunction isObjectEmpty(obj) {\n  if (Object.getOwnPropertyNames) {\n    return Object.getOwnPropertyNames(obj).length === 0;\n  }\n  let k;\n  for (k in obj) {\n    // eslint-disable-next-line no-prototype-builtins\n    if (obj.hasOwnProperty(k)) {\n      return false;\n    }\n  }\n  return true;\n}\nfunction isUndefined(input) {\n  return input === void 0;\n}\nfunction toInt(argumentForCoercion) {\n  const coercedNumber = +argumentForCoercion;\n  let value = 0;\n  if (coercedNumber !== 0 && isFinite(coercedNumber)) {\n    value = absFloor(coercedNumber);\n  }\n  return value;\n}\nconst aliases = {};\nconst _mapUnits = {\n  date: 'day',\n  hour: 'hours',\n  minute: 'minutes',\n  second: 'seconds',\n  millisecond: 'milliseconds'\n};\nfunction addUnitAlias(unit, shorthand) {\n  const lowerCase = unit.toLowerCase();\n  let _unit = unit;\n  if (lowerCase in _mapUnits) {\n    _unit = _mapUnits[lowerCase];\n  }\n  aliases[lowerCase] = aliases[`${lowerCase}s`] = aliases[shorthand] = _unit;\n}\nfunction normalizeUnits(units) {\n  return isString(units) ? aliases[units] || aliases[units.toLowerCase()] : undefined;\n}\nfunction normalizeObjectUnits(inputObject) {\n  const normalizedInput = {};\n  let normalizedProp;\n  let prop;\n  for (prop in inputObject) {\n    if (hasOwnProp(inputObject, prop)) {\n      normalizedProp = normalizeUnits(prop);\n      if (normalizedProp) {\n        normalizedInput[normalizedProp] = inputObject[prop];\n      }\n    }\n  }\n  return normalizedInput;\n}\n\n// place in new Date([array])\nconst YEAR = 0;\nconst MONTH = 1;\nconst DATE = 2;\nconst HOUR = 3;\nconst MINUTE = 4;\nconst SECOND = 5;\nconst MILLISECOND = 6;\nconst WEEK = 7;\nconst WEEKDAY = 8;\nfunction zeroFill(num, targetLength, forceSign) {\n  const absNumber = `${Math.abs(num)}`;\n  const zerosToFill = targetLength - absNumber.length;\n  const sign = num >= 0;\n  const _sign = sign ? forceSign ? '+' : '' : '-';\n  // todo: this is crazy slow\n  const _zeros = Math.pow(10, Math.max(0, zerosToFill)).toString().substr(1);\n  return _sign + _zeros + absNumber;\n}\nconst formatFunctions = {};\nconst formatTokenFunctions = {};\nconst formattingTokens = /(\\[[^\\[]*\\])|(\\\\)?([Hh]mm(ss)?|Mo|MM?M?M?|Do|DDDo|DD?D?D?|ddd?d?|do?|w[o|w]?|W[o|W]?|Qo?|YYYYYY|YYYYY|YYYY|YY|gg(ggg?)?|GG(GGG?)?|e|E|a|A|hh?|HH?|kk?|mm?|ss?|S{1,9}|x|X|zz?|ZZ?|.)/g;\n// token:    'M'\n// padded:   ['MM', 2]\n// ordinal:  'Mo'\n// callback: function () { this.month() + 1 }\nfunction addFormatToken(token, padded, ordinal, callback) {\n  if (token) {\n    formatTokenFunctions[token] = callback;\n  }\n  if (padded) {\n    formatTokenFunctions[padded[0]] = function () {\n      return zeroFill(callback.apply(null, arguments), padded[1], padded[2]);\n    };\n  }\n  if (ordinal) {\n    formatTokenFunctions[ordinal] = function (date, opts) {\n      return opts.locale.ordinal(callback.apply(null, arguments), token);\n    };\n  }\n}\nfunction makeFormatFunction(format) {\n  const array = format.match(formattingTokens);\n  const length = array.length;\n  const formatArr = new Array(length);\n  for (let i = 0; i < length; i++) {\n    formatArr[i] = formatTokenFunctions[array[i]] ? formatTokenFunctions[array[i]] : removeFormattingTokens(array[i]);\n  }\n  return function (date, locale, isUTC, offset = 0) {\n    let output = '';\n    for (let j = 0; j < length; j++) {\n      output += isFunction(formatArr[j]) ? formatArr[j].call(null, date, {\n        format,\n        locale,\n        isUTC,\n        offset\n      }) : formatArr[j];\n    }\n    return output;\n  };\n}\nfunction removeFormattingTokens(input) {\n  if (input.match(/\\[[\\s\\S]/)) {\n    return input.replace(/^\\[|\\]$/g, '');\n  }\n  return input.replace(/\\\\/g, '');\n}\n\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nfunction createUTCDate(y, m, d) {\n  // eslint-disable-next-line prefer-rest-params\n  const date = new Date(Date.UTC.apply(null, arguments));\n  // the Date.UTC function remaps years 0-99 to 1900-1999\n  if (y < 100 && y >= 0 && isFinite(date.getUTCFullYear())) {\n    date.setUTCFullYear(y);\n  }\n  return date;\n}\nfunction createDate(y, m = 0, d = 1, h = 0, M = 0, s = 0, ms = 0) {\n  const date = new Date(y, m, d, h, M, s, ms);\n  // the date constructor remaps years 0-99 to 1900-1999\n  if (y < 100 && y >= 0 && isFinite(date.getFullYear())) {\n    date.setFullYear(y);\n  }\n  return date;\n}\nfunction getHours(date, isUTC = false) {\n  return isUTC ? date.getUTCHours() : date.getHours();\n}\nfunction getMinutes(date, isUTC = false) {\n  return isUTC ? date.getUTCMinutes() : date.getMinutes();\n}\nfunction getSeconds(date, isUTC = false) {\n  return isUTC ? date.getUTCSeconds() : date.getSeconds();\n}\nfunction getMilliseconds(date, isUTC = false) {\n  return isUTC ? date.getUTCMilliseconds() : date.getMilliseconds();\n}\nfunction getTime(date) {\n  return date.getTime();\n}\nfunction getDay(date, isUTC = false) {\n  return isUTC ? date.getUTCDay() : date.getDay();\n}\nfunction getDate(date, isUTC = false) {\n  return isUTC ? date.getUTCDate() : date.getDate();\n}\nfunction getMonth(date, isUTC = false) {\n  return isUTC ? date.getUTCMonth() : date.getMonth();\n}\nfunction getFullYear(date, isUTC = false) {\n  return isUTC ? date.getUTCFullYear() : date.getFullYear();\n}\nfunction getUnixTime(date) {\n  return Math.floor(date.valueOf() / 1000);\n}\nfunction unix(date) {\n  return Math.floor(date.valueOf() / 1000);\n}\nfunction getFirstDayOfMonth(date) {\n  return createDate(date.getFullYear(), date.getMonth(), 1, date.getHours(), date.getMinutes(), date.getSeconds());\n}\nfunction daysInMonth$1(date) {\n  return _daysInMonth(date.getFullYear(), date.getMonth());\n}\nfunction _daysInMonth(year, month) {\n  return new Date(Date.UTC(year, month + 1, 0)).getUTCDate();\n}\nfunction isFirstDayOfWeek(date, firstDayOfWeek) {\n  return date.getDay() === Number(firstDayOfWeek);\n}\nfunction isSameMonth(date1, date2) {\n  if (!date1 || !date2) {\n    return false;\n  }\n  return isSameYear(date1, date2) && getMonth(date1) === getMonth(date2);\n}\nfunction isSameYear(date1, date2) {\n  if (!date1 || !date2) {\n    return false;\n  }\n  return getFullYear(date1) === getFullYear(date2);\n}\nfunction isSameDay$1(date1, date2) {\n  if (!date1 || !date2) {\n    return false;\n  }\n  return isSameYear(date1, date2) && isSameMonth(date1, date2) && getDate(date1) === getDate(date2);\n}\nconst match1 = /\\d/; //       0 - 9\nconst match2 = /\\d\\d/; //      00 - 99\nconst match3 = /\\d{3}/; //     000 - 999\nconst match4 = /\\d{4}/; //    0000 - 9999\nconst match6 = /[+-]?\\d{6}/; // -999999 - 999999\nconst match1to2 = /\\d\\d?/; //       0 - 99\nconst match3to4 = /\\d\\d\\d\\d?/; //     999 - 9999\nconst match5to6 = /\\d\\d\\d\\d\\d\\d?/; //   99999 - 999999\nconst match1to3 = /\\d{1,3}/; //       0 - 999\nconst match1to4 = /\\d{1,4}/; //       0 - 9999\nconst match1to6 = /[+-]?\\d{1,6}/; // -999999 - 999999\nconst matchUnsigned = /\\d+/; //       0 - inf\nconst matchSigned = /[+-]?\\d+/; //    -inf - inf\nconst matchOffset = /Z|[+-]\\d\\d:?\\d\\d/gi; // +00:00 -00:00 +0000 -0000 or Z\nconst matchShortOffset = /Z|[+-]\\d\\d(?::?\\d\\d)?/gi; // +00 -00 +00:00 -00:00 +0000 -0000 or Z\nconst matchTimestamp = /[+-]?\\d+(\\.\\d{1,3})?/; // 123456789 123456789.123\n// any word (or two) characters or numbers including two/three word month in arabic.\n// includes scottish gaelic two word and hyphenated months\nconst matchWord = /[0-9]{0,256}['a-z\\u00A0-\\u05FF\\u0700-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF]{1,256}|[\\u0600-\\u06FF\\/]{1,256}(\\s*?[\\u0600-\\u06FF]{1,256}){1,2}/i;\nconst regexes = {};\nfunction addRegexToken(token, regex, strictRegex) {\n  if (isFunction(regex)) {\n    regexes[token] = regex;\n    return;\n  }\n  regexes[token] = function (isStrict, locale) {\n    return isStrict && strictRegex ? strictRegex : regex;\n  };\n}\nfunction getParseRegexForToken(token, locale) {\n  const _strict = false;\n  if (!hasOwnProp(regexes, token)) {\n    return new RegExp(unescapeFormat(token));\n  }\n  return regexes[token](_strict, locale);\n}\n// Code from http://stackoverflow.com/questions/3561493/is-there-a-regexp-escape-function-in-javascript\nfunction unescapeFormat(str) {\n  return regexEscape(str.replace('\\\\', '').replace(/\\\\(\\[)|\\\\(\\])|\\[([^\\]\\[]*)\\]|\\\\(.)/g, (matched, p1, p2, p3, p4) => p1 || p2 || p3 || p4));\n}\nfunction regexEscape(str) {\n  return str.replace(/[-\\/\\\\^$*+?.()|[\\]{}]/g, '\\\\$&');\n}\nconst tokens = {};\nfunction addParseToken(token, callback) {\n  const _token = isString(token) ? [token] : token;\n  let func = callback;\n  if (isNumber(callback)) {\n    func = function (input, array, config) {\n      array[callback] = toInt(input);\n      return config;\n    };\n  }\n  if (isArray(_token) && isFunction(func)) {\n    let i;\n    for (i = 0; i < _token.length; i++) {\n      tokens[_token[i]] = func;\n    }\n  }\n}\nfunction addWeekParseToken(token, callback) {\n  addParseToken(token, function (input, array, config, _token) {\n    config._w = config._w || {};\n    return callback(input, config._w, config, _token);\n  });\n}\nfunction addTimeToArrayFromToken(token, input, config) {\n  if (input != null && hasOwnProp(tokens, token)) {\n    tokens[token](input, config._a, config, token);\n  }\n  return config;\n}\nconst priorities = {};\nfunction addUnitPriority(unit, priority) {\n  priorities[unit] = priority;\n}\n/*\nexport function getPrioritizedUnits(unitsObj) {\n  const units = [];\n  let unit;\n  for (unit in unitsObj) {\n    if (unitsObj.hasOwnProperty(unit)) {\n      units.push({ unit, priority: priorities[unit] });\n    }\n  }\n  units.sort(function (a, b) {\n    return a.priority - b.priority;\n  });\n\n  return units;\n}\n*/\n\nfunction initDayOfMonth() {\n  // FORMATTING\n  addFormatToken('D', ['DD', 2, false], 'Do', function (date, opts) {\n    return getDate(date, opts.isUTC).toString(10);\n  });\n  // ALIASES\n  addUnitAlias('date', 'D');\n  // PRIOROITY\n  addUnitPriority('date', 9);\n  // PARSING\n  addRegexToken('D', match1to2);\n  addRegexToken('DD', match1to2, match2);\n  addRegexToken('Do', function (isStrict, locale) {\n    return locale._dayOfMonthOrdinalParse || locale._ordinalParse;\n  });\n  addParseToken(['D', 'DD'], DATE);\n  addParseToken('Do', function (input, array, config) {\n    array[DATE] = toInt(input.match(match1to2)[0]);\n    return config;\n  });\n}\nfunction defaultParsingFlags() {\n  // We need to deep clone this object.\n  return {\n    empty: false,\n    unusedTokens: [],\n    unusedInput: [],\n    overflow: -2,\n    charsLeftOver: 0,\n    nullInput: false,\n    invalidMonth: null,\n    invalidFormat: false,\n    userInvalidated: false,\n    iso: false,\n    parsedDateParts: [],\n    meridiem: null,\n    rfc2822: false,\n    weekdayMismatch: false\n  };\n}\nfunction getParsingFlags(config) {\n  if (config._pf == null) {\n    config._pf = defaultParsingFlags();\n  }\n  return config._pf;\n}\n\n// FORMATTING\nfunction getYear(date, opts) {\n  if (opts.locale.getFullYear) {\n    return opts.locale.getFullYear(date, opts.isUTC).toString();\n  }\n  return getFullYear(date, opts.isUTC).toString();\n}\nfunction initYear() {\n  addFormatToken('Y', null, null, function (date, opts) {\n    const y = getFullYear(date, opts.isUTC);\n    return y <= 9999 ? y.toString(10) : `+${y}`;\n  });\n  addFormatToken(null, ['YY', 2, false], null, function (date, opts) {\n    return (getFullYear(date, opts.isUTC) % 100).toString(10);\n  });\n  addFormatToken(null, ['YYYY', 4, false], null, getYear);\n  addFormatToken(null, ['YYYYY', 5, false], null, getYear);\n  addFormatToken(null, ['YYYYYY', 6, true], null, getYear);\n  // ALIASES\n  addUnitAlias('year', 'y');\n  // PRIORITIES\n  addUnitPriority('year', 1);\n  // PARSING\n  addRegexToken('Y', matchSigned);\n  addRegexToken('YY', match1to2, match2);\n  addRegexToken('YYYY', match1to4, match4);\n  addRegexToken('YYYYY', match1to6, match6);\n  addRegexToken('YYYYYY', match1to6, match6);\n  addParseToken(['YYYYY', 'YYYYYY'], YEAR);\n  addParseToken('YYYY', function (input, array, config) {\n    array[YEAR] = input.length === 2 ? parseTwoDigitYear(input) : toInt(input);\n    return config;\n  });\n  addParseToken('YY', function (input, array, config) {\n    array[YEAR] = parseTwoDigitYear(input);\n    return config;\n  });\n  addParseToken('Y', function (input, array, config) {\n    array[YEAR] = parseInt(input, 10);\n    return config;\n  });\n}\nfunction parseTwoDigitYear(input) {\n  return toInt(input) + (toInt(input) > 68 ? 1900 : 2000);\n}\nfunction daysInYear(year) {\n  return isLeapYear(year) ? 366 : 365;\n}\nfunction isLeapYear(year) {\n  return year % 4 === 0 && year % 100 !== 0 || year % 400 === 0;\n}\n\n// todo: this is duplicate, source in date-getters.ts\nfunction daysInMonth(year, month) {\n  if (isNaN(year) || isNaN(month)) {\n    return NaN;\n  }\n  const modMonth = mod(month, 12);\n  const _year = year + (month - modMonth) / 12;\n  return modMonth === 1 ? isLeapYear(_year) ? 29 : 28 : 31 - modMonth % 7 % 2;\n}\nfunction initMonth() {\n  // FORMATTING\n  addFormatToken('M', ['MM', 2, false], 'Mo', function (date, opts) {\n    return (getMonth(date, opts.isUTC) + 1).toString(10);\n  });\n  addFormatToken('MMM', null, null, function (date, opts) {\n    return opts.locale.monthsShort(date, opts.format, opts.isUTC);\n  });\n  addFormatToken('MMMM', null, null, function (date, opts) {\n    return opts.locale.months(date, opts.format, opts.isUTC);\n  });\n  // ALIASES\n  addUnitAlias('month', 'M');\n  // PRIORITY\n  addUnitPriority('month', 8);\n  // PARSING\n  addRegexToken('M', match1to2);\n  addRegexToken('MM', match1to2, match2);\n  addRegexToken('MMM', function (isStrict, locale) {\n    return locale.monthsShortRegex(isStrict);\n  });\n  addRegexToken('MMMM', function (isStrict, locale) {\n    return locale.monthsRegex(isStrict);\n  });\n  addParseToken(['M', 'MM'], function (input, array, config) {\n    array[MONTH] = toInt(input) - 1;\n    return config;\n  });\n  addParseToken(['MMM', 'MMMM'], function (input, array, config, token) {\n    const month = config._locale.monthsParse(input, token, config._strict);\n    // if we didn't find a month name, mark the date as invalid.\n    if (month != null) {\n      array[MONTH] = month;\n    } else {\n      getParsingFlags(config).invalidMonth = !!input;\n    }\n    return config;\n  });\n}\nconst defaultTimeUnit = {\n  year: 0,\n  month: 0,\n  day: 0,\n  hour: 0,\n  minute: 0,\n  seconds: 0\n};\nfunction shiftDate(date, unit) {\n  const _unit = Object.assign({}, defaultTimeUnit, unit);\n  const year = date.getFullYear() + (_unit.year || 0);\n  const month = date.getMonth() + (_unit.month || 0);\n  let day = date.getDate() + (_unit.day || 0);\n  if (_unit.month && !_unit.day) {\n    day = Math.min(day, daysInMonth(year, month));\n  }\n  return createDate(year, month, day, date.getHours() + (_unit.hour || 0), date.getMinutes() + (_unit.minute || 0), date.getSeconds() + (_unit.seconds || 0));\n}\nfunction setFullDate(date, unit) {\n  return createDate(getNum(date.getFullYear(), unit.year), getNum(date.getMonth(), unit.month), 1,\n  // day, to avoid issue with wrong months selection at the end of current month (#5371)\n  getNum(date.getHours(), unit.hour), getNum(date.getMinutes(), unit.minute), getNum(date.getSeconds(), unit.seconds), getNum(date.getMilliseconds(), unit.milliseconds));\n}\nfunction getNum(def, num) {\n  return isNumber(num) ? num : def;\n}\nfunction setFullYear(date, value, isUTC) {\n  const _month = getMonth(date, isUTC);\n  const _date = getDate(date, isUTC);\n  const _year = getFullYear(date, isUTC);\n  if (isLeapYear(_year) && _month === 1 && _date === 29) {\n    const _daysInMonth = daysInMonth(value, _month);\n    isUTC ? date.setUTCFullYear(value, _month, _daysInMonth) : date.setFullYear(value, _month, _daysInMonth);\n  }\n  isUTC ? date.setUTCFullYear(value) : date.setFullYear(value);\n  return date;\n}\nfunction setMonth(date, value, isUTC) {\n  const dayOfMonth = Math.min(getDate(date), daysInMonth(getFullYear(date), value));\n  isUTC ? date.setUTCMonth(value, dayOfMonth) : date.setMonth(value, dayOfMonth);\n  return date;\n}\nfunction setDay(date, value, isUTC) {\n  isUTC ? date.setUTCDate(value) : date.setDate(value);\n  return date;\n}\nfunction setHours(date, value, isUTC) {\n  isUTC ? date.setUTCHours(value) : date.setHours(value);\n  return date;\n}\nfunction setMinutes(date, value, isUTC) {\n  isUTC ? date.setUTCMinutes(value) : date.setMinutes(value);\n  return date;\n}\nfunction setSeconds(date, value, isUTC) {\n  isUTC ? date.setUTCSeconds(value) : date.setSeconds(value);\n  return date;\n}\nfunction setMilliseconds(date, value, isUTC) {\n  isUTC ? date.setUTCMilliseconds(value) : date.setMilliseconds(value);\n  return date;\n}\nfunction setDate(date, value, isUTC) {\n  isUTC ? date.setUTCDate(value) : date.setDate(value);\n  return date;\n}\nfunction setTime(date, value) {\n  date.setTime(value);\n  return date;\n}\n\n// fastest way to clone date\n// https://jsperf.com/clone-date-object2\nfunction cloneDate(date) {\n  return new Date(date.getTime());\n}\nfunction startOf(date, unit, isUTC) {\n  const _date = cloneDate(date);\n  // the following switch intentionally omits break keywords\n  // to utilize falling through the cases.\n  switch (unit) {\n    case 'year':\n      setMonth(_date, 0, isUTC);\n    /* falls through */\n    case 'quarter':\n    case 'month':\n      setDate(_date, 1, isUTC);\n    /* falls through */\n    case 'week':\n    case 'isoWeek':\n    case 'day':\n    case 'date':\n      setHours(_date, 0, isUTC);\n    /* falls through */\n    case 'hours':\n      setMinutes(_date, 0, isUTC);\n    /* falls through */\n    case 'minutes':\n      setSeconds(_date, 0, isUTC);\n    /* falls through */\n    case 'seconds':\n      setMilliseconds(_date, 0, isUTC);\n  }\n  // weeks are a special case\n  if (unit === 'week') {\n    setLocaleDayOfWeek(_date, 0, {\n      isUTC\n    });\n  }\n  if (unit === 'isoWeek') {\n    setISODayOfWeek(_date, 1);\n  }\n  // quarters are also special\n  if (unit === 'quarter') {\n    setMonth(_date, Math.floor(getMonth(_date, isUTC) / 3) * 3, isUTC);\n  }\n  return _date;\n}\nfunction endOf(date, unit, isUTC) {\n  let _unit = unit;\n  // 'date' is an alias for 'day', so it should be considered as such.\n  if (_unit === 'date') {\n    _unit = 'day';\n  }\n  const start = startOf(date, _unit, isUTC);\n  const _step = add(start, 1, _unit === 'isoWeek' ? 'week' : _unit, isUTC);\n  const res = subtract(_step, 1, 'milliseconds', isUTC);\n  return res;\n}\nfunction initDayOfYear() {\n  // FORMATTING\n  addFormatToken('DDD', ['DDDD', 3, false], 'DDDo', function (date) {\n    return getDayOfYear(date).toString(10);\n  });\n  // ALIASES\n  addUnitAlias('dayOfYear', 'DDD');\n  // PRIORITY\n  addUnitPriority('dayOfYear', 4);\n  addRegexToken('DDD', match1to3);\n  addRegexToken('DDDD', match3);\n  addParseToken(['DDD', 'DDDD'], function (input, array, config) {\n    config._dayOfYear = toInt(input);\n    return config;\n  });\n}\nfunction getDayOfYear(date, isUTC) {\n  const date1 = +startOf(date, 'day', isUTC);\n  const date2 = +startOf(date, 'year', isUTC);\n  const someDate = date1 - date2;\n  const oneDay = 1000 * 60 * 60 * 24;\n  return Math.round(someDate / oneDay) + 1;\n}\nfunction setDayOfYear(date, input) {\n  const dayOfYear = getDayOfYear(date);\n  return add(date, input - dayOfYear, 'day');\n}\n\n/**\n *\n * @param {number} year\n * @param {number} dow - start-of-first-week\n * @param {number} doy - start-of-year\n * @returns {number}\n */\nfunction firstWeekOffset(year, dow, doy) {\n  // first-week day -- which january is always in the first week (4 for iso, 1 for other)\n  const fwd = dow - doy + 7;\n  // first-week day local weekday -- which local weekday is fwd\n  const fwdlw = (createUTCDate(year, 0, fwd).getUTCDay() - dow + 7) % 7;\n  return -fwdlw + fwd - 1;\n}\n// https://en.wikipedia.org/wiki/ISO_week_date#Calculating_a_date_given_the_year.2C_week_number_and_weekday\nfunction dayOfYearFromWeeks(year, week, weekday, dow, doy) {\n  const localWeekday = (7 + weekday - dow) % 7;\n  const weekOffset = firstWeekOffset(year, dow, doy);\n  const dayOfYear = 1 + 7 * (week - 1) + localWeekday + weekOffset;\n  let resYear;\n  let resDayOfYear;\n  if (dayOfYear <= 0) {\n    resYear = year - 1;\n    resDayOfYear = daysInYear(resYear) + dayOfYear;\n  } else if (dayOfYear > daysInYear(year)) {\n    resYear = year + 1;\n    resDayOfYear = dayOfYear - daysInYear(year);\n  } else {\n    resYear = year;\n    resDayOfYear = dayOfYear;\n  }\n  return {\n    year: resYear,\n    dayOfYear: resDayOfYear\n  };\n}\nfunction weekOfYear(date, dow, doy, isUTC) {\n  const weekOffset = firstWeekOffset(getFullYear(date, isUTC), dow, doy);\n  const week = Math.floor((getDayOfYear(date, isUTC) - weekOffset - 1) / 7) + 1;\n  let resWeek;\n  let resYear;\n  if (week < 1) {\n    resYear = getFullYear(date, isUTC) - 1;\n    resWeek = week + weeksInYear(resYear, dow, doy);\n  } else if (week > weeksInYear(getFullYear(date, isUTC), dow, doy)) {\n    resWeek = week - weeksInYear(getFullYear(date, isUTC), dow, doy);\n    resYear = getFullYear(date, isUTC) + 1;\n  } else {\n    resYear = getFullYear(date, isUTC);\n    resWeek = week;\n  }\n  return {\n    week: resWeek,\n    year: resYear\n  };\n}\nfunction weeksInYear(year, dow, doy) {\n  const weekOffset = firstWeekOffset(year, dow, doy);\n  const weekOffsetNext = firstWeekOffset(year + 1, dow, doy);\n  return (daysInYear(year) - weekOffset + weekOffsetNext) / 7;\n}\nconst MONTHS_IN_FORMAT = /D[oD]?(\\[[^\\[\\]]*\\]|\\s)+MMMM?/;\nconst defaultLocaleMonths = 'January_February_March_April_May_June_July_August_September_October_November_December'.split('_');\nconst defaultLocaleMonthsShort = 'Jan_Feb_Mar_Apr_May_Jun_Jul_Aug_Sep_Oct_Nov_Dec'.split('_');\nconst defaultLocaleWeekdays = 'Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday'.split('_');\nconst defaultLocaleWeekdaysShort = 'Sun_Mon_Tue_Wed_Thu_Fri_Sat'.split('_');\nconst defaultLocaleWeekdaysMin = 'Su_Mo_Tu_We_Th_Fr_Sa'.split('_');\nconst defaultLongDateFormat = {\n  LTS: 'h:mm:ss A',\n  LT: 'h:mm A',\n  L: 'MM/DD/YYYY',\n  LL: 'MMMM D, YYYY',\n  LLL: 'MMMM D, YYYY h:mm A',\n  LLLL: 'dddd, MMMM D, YYYY h:mm A'\n};\nconst defaultOrdinal = '%d';\nconst defaultDayOfMonthOrdinalParse = /\\d{1,2}/;\nconst defaultMonthsShortRegex = matchWord;\nconst defaultMonthsRegex = matchWord;\nclass Locale {\n  constructor(config) {\n    if (config) {\n      this.set(config);\n    }\n  }\n  set(config) {\n    let confKey;\n    for (confKey in config) {\n      // eslint-disable-next-line no-prototype-builtins\n      if (!config.hasOwnProperty(confKey)) {\n        continue;\n      }\n      const prop = config[confKey];\n      const key = isFunction(prop) ? confKey : `_${confKey}`;\n      this[key] = prop;\n    }\n    this._config = config;\n  }\n  calendar(key, date, now) {\n    const output = this._calendar[key] || this._calendar[\"sameElse\"];\n    return isFunction(output) ? output.call(null, date, now) : output;\n  }\n  longDateFormat(key) {\n    const format = this._longDateFormat[key];\n    const formatUpper = this._longDateFormat[key.toUpperCase()];\n    if (format || !formatUpper) {\n      return format;\n    }\n    this._longDateFormat[key] = formatUpper.replace(/MMMM|MM|DD|dddd/g, function (val) {\n      return val.slice(1);\n    });\n    return this._longDateFormat[key];\n  }\n  get invalidDate() {\n    return this._invalidDate;\n  }\n  set invalidDate(val) {\n    this._invalidDate = val;\n  }\n  ordinal(num, token) {\n    return this._ordinal.replace('%d', num.toString(10));\n  }\n  preparse(str, format) {\n    return str;\n  }\n  getFullYear(date, isUTC = false) {\n    return getFullYear(date, isUTC);\n  }\n  postformat(str) {\n    return str;\n  }\n  relativeTime(num, withoutSuffix, str, isFuture) {\n    const output = this._relativeTime[str];\n    return isFunction(output) ? output(num, withoutSuffix, str, isFuture) : output.replace(/%d/i, num.toString(10));\n  }\n  pastFuture(diff, output) {\n    const format = this._relativeTime[diff > 0 ? 'future' : 'past'];\n    return isFunction(format) ? format(output) : format.replace(/%s/i, output);\n  }\n  months(date, format, isUTC = false) {\n    if (!date) {\n      return isArray(this._months) ? this._months : this._months.standalone;\n    }\n    if (isArray(this._months)) {\n      return this._months[getMonth(date, isUTC)];\n    }\n    const key = (this._months.isFormat || MONTHS_IN_FORMAT).test(format) ? 'format' : 'standalone';\n    return this._months[key][getMonth(date, isUTC)];\n  }\n  monthsShort(date, format, isUTC = false) {\n    if (!date) {\n      return isArray(this._monthsShort) ? this._monthsShort : this._monthsShort.standalone;\n    }\n    if (isArray(this._monthsShort)) {\n      return this._monthsShort[getMonth(date, isUTC)];\n    }\n    const key = MONTHS_IN_FORMAT.test(format) ? 'format' : 'standalone';\n    return this._monthsShort[key][getMonth(date, isUTC)];\n  }\n  monthsParse(monthName, format, strict) {\n    let date;\n    let regex;\n    if (this._monthsParseExact) {\n      return this.handleMonthStrictParse(monthName, format, strict);\n    }\n    if (!this._monthsParse) {\n      this._monthsParse = [];\n      this._longMonthsParse = [];\n      this._shortMonthsParse = [];\n    }\n    // TODO: add sorting\n    // Sorting makes sure if one month (or abbr) is a prefix of another\n    // see sorting in computeMonthsParse\n    let i;\n    for (i = 0; i < 12; i++) {\n      // make the regex if we don't have it already\n      date = new Date(Date.UTC(2000, i));\n      if (strict && !this._longMonthsParse[i]) {\n        const _months = this.months(date, '', true).replace('.', '');\n        const _shortMonths = this.monthsShort(date, '', true).replace('.', '');\n        this._longMonthsParse[i] = new RegExp(`^${_months}$`, 'i');\n        this._shortMonthsParse[i] = new RegExp(`^${_shortMonths}$`, 'i');\n      }\n      if (!strict && !this._monthsParse[i]) {\n        regex = `^${this.months(date, '', true)}|^${this.monthsShort(date, '', true)}`;\n        this._monthsParse[i] = new RegExp(regex.replace('.', ''), 'i');\n      }\n      // testing the regex\n      if (strict && format === 'MMMM' && this._longMonthsParse[i].test(monthName)) {\n        return i;\n      }\n      if (strict && format === 'MMM' && this._shortMonthsParse[i].test(monthName)) {\n        return i;\n      }\n      if (!strict && this._monthsParse[i].test(monthName)) {\n        return i;\n      }\n    }\n  }\n  monthsRegex(isStrict) {\n    if (this._monthsParseExact) {\n      if (!hasOwnProp(this, '_monthsRegex')) {\n        this.computeMonthsParse();\n      }\n      if (isStrict) {\n        return this._monthsStrictRegex;\n      }\n      return this._monthsRegex;\n    }\n    if (!hasOwnProp(this, '_monthsRegex')) {\n      this._monthsRegex = defaultMonthsRegex;\n    }\n    return this._monthsStrictRegex && isStrict ? this._monthsStrictRegex : this._monthsRegex;\n  }\n  monthsShortRegex(isStrict) {\n    if (this._monthsParseExact) {\n      if (!hasOwnProp(this, '_monthsRegex')) {\n        this.computeMonthsParse();\n      }\n      if (isStrict) {\n        return this._monthsShortStrictRegex;\n      }\n      return this._monthsShortRegex;\n    }\n    if (!hasOwnProp(this, '_monthsShortRegex')) {\n      this._monthsShortRegex = defaultMonthsShortRegex;\n    }\n    return this._monthsShortStrictRegex && isStrict ? this._monthsShortStrictRegex : this._monthsShortRegex;\n  }\n  /** Week */\n  week(date, isUTC) {\n    return weekOfYear(date, this._week.dow, this._week.doy, isUTC).week;\n  }\n  firstDayOfWeek() {\n    return this._week.dow;\n  }\n  firstDayOfYear() {\n    return this._week.doy;\n  }\n  weekdays(date, format, isUTC) {\n    if (!date) {\n      return isArray(this._weekdays) ? this._weekdays : this._weekdays.standalone;\n    }\n    if (isArray(this._weekdays)) {\n      return this._weekdays[getDay(date, isUTC)];\n    }\n    const _key = this._weekdays.isFormat.test(format) ? 'format' : 'standalone';\n    return this._weekdays[_key][getDay(date, isUTC)];\n  }\n  weekdaysMin(date, format, isUTC) {\n    return date ? this._weekdaysMin[getDay(date, isUTC)] : this._weekdaysMin;\n  }\n  weekdaysShort(date, format, isUTC) {\n    return date ? this._weekdaysShort[getDay(date, isUTC)] : this._weekdaysShort;\n  }\n  // proto.weekdaysParse  =        localeWeekdaysParse;\n  weekdaysParse(weekdayName, format, strict) {\n    let i;\n    let regex;\n    if (this._weekdaysParseExact) {\n      return this.handleWeekStrictParse(weekdayName, format, strict);\n    }\n    if (!this._weekdaysParse) {\n      this._weekdaysParse = [];\n      this._minWeekdaysParse = [];\n      this._shortWeekdaysParse = [];\n      this._fullWeekdaysParse = [];\n    }\n    for (i = 0; i < 7; i++) {\n      // make the regex if we don't have it already\n      // fix: here is the issue\n      const date = setDayOfWeek(new Date(Date.UTC(2000, 1)), i, null, true);\n      if (strict && !this._fullWeekdaysParse[i]) {\n        this._fullWeekdaysParse[i] = new RegExp(`^${this.weekdays(date, '', true).replace('.', '\\.?')}$`, 'i');\n        this._shortWeekdaysParse[i] = new RegExp(`^${this.weekdaysShort(date, '', true).replace('.', '\\.?')}$`, 'i');\n        this._minWeekdaysParse[i] = new RegExp(`^${this.weekdaysMin(date, '', true).replace('.', '\\.?')}$`, 'i');\n      }\n      if (!this._weekdaysParse[i]) {\n        regex = `^${this.weekdays(date, '', true)}|^${this.weekdaysShort(date, '', true)}|^${this.weekdaysMin(date, '', true)}`;\n        this._weekdaysParse[i] = new RegExp(regex.replace('.', ''), 'i');\n      }\n      if (!isArray(this._fullWeekdaysParse) || !isArray(this._shortWeekdaysParse) || !isArray(this._minWeekdaysParse) || !isArray(this._weekdaysParse)) {\n        return;\n      }\n      // testing the regex\n      if (strict && format === 'dddd' && this._fullWeekdaysParse[i].test(weekdayName)) {\n        return i;\n      } else if (strict && format === 'ddd' && this._shortWeekdaysParse[i].test(weekdayName)) {\n        return i;\n      } else if (strict && format === 'dd' && this._minWeekdaysParse[i].test(weekdayName)) {\n        return i;\n      } else if (!strict && this._weekdaysParse[i].test(weekdayName)) {\n        return i;\n      }\n    }\n  }\n  // proto.weekdaysRegex       =        weekdaysRegex;\n  weekdaysRegex(isStrict) {\n    if (this._weekdaysParseExact) {\n      if (!hasOwnProp(this, '_weekdaysRegex')) {\n        this.computeWeekdaysParse();\n      }\n      if (isStrict) {\n        return this._weekdaysStrictRegex;\n      } else {\n        return this._weekdaysRegex;\n      }\n    } else {\n      if (!hasOwnProp(this, '_weekdaysRegex')) {\n        this._weekdaysRegex = matchWord;\n      }\n      return this._weekdaysStrictRegex && isStrict ? this._weekdaysStrictRegex : this._weekdaysRegex;\n    }\n  }\n  // proto.weekdaysShortRegex  =        weekdaysShortRegex;\n  // proto.weekdaysMinRegex    =        weekdaysMinRegex;\n  weekdaysShortRegex(isStrict) {\n    if (this._weekdaysParseExact) {\n      if (!hasOwnProp(this, '_weekdaysRegex')) {\n        this.computeWeekdaysParse();\n      }\n      if (isStrict) {\n        return this._weekdaysShortStrictRegex;\n      } else {\n        return this._weekdaysShortRegex;\n      }\n    } else {\n      if (!hasOwnProp(this, '_weekdaysShortRegex')) {\n        this._weekdaysShortRegex = matchWord;\n      }\n      return this._weekdaysShortStrictRegex && isStrict ? this._weekdaysShortStrictRegex : this._weekdaysShortRegex;\n    }\n  }\n  weekdaysMinRegex(isStrict) {\n    if (this._weekdaysParseExact) {\n      if (!hasOwnProp(this, '_weekdaysRegex')) {\n        this.computeWeekdaysParse();\n      }\n      if (isStrict) {\n        return this._weekdaysMinStrictRegex;\n      } else {\n        return this._weekdaysMinRegex;\n      }\n    } else {\n      if (!hasOwnProp(this, '_weekdaysMinRegex')) {\n        this._weekdaysMinRegex = matchWord;\n      }\n      return this._weekdaysMinStrictRegex && isStrict ? this._weekdaysMinStrictRegex : this._weekdaysMinRegex;\n    }\n  }\n  isPM(input) {\n    // IE8 Quirks Mode & IE7 Standards Mode do not allow accessing strings like arrays\n    // Using charAt should be more compatible.\n    return input.toLowerCase().charAt(0) === 'p';\n  }\n  meridiem(hours, minutes, isLower) {\n    if (hours > 11) {\n      return isLower ? 'pm' : 'PM';\n    }\n    return isLower ? 'am' : 'AM';\n  }\n  formatLongDate(key) {\n    this._longDateFormat = this._longDateFormat ? this._longDateFormat : defaultLongDateFormat;\n    const format = this._longDateFormat[key];\n    const formatUpper = this._longDateFormat[key.toUpperCase()];\n    if (format || !formatUpper) {\n      return format;\n    }\n    this._longDateFormat[key] = formatUpper.replace(/MMMM|MM|DD|dddd/g, val => {\n      return val.slice(1);\n    });\n    return this._longDateFormat[key];\n  }\n  handleMonthStrictParse(monthName, format, strict) {\n    const llc = monthName.toLocaleLowerCase();\n    let i;\n    let ii;\n    let mom;\n    if (!this._monthsParse) {\n      // this is not used\n      this._monthsParse = [];\n      this._longMonthsParse = [];\n      this._shortMonthsParse = [];\n      for (i = 0; i < 12; ++i) {\n        mom = new Date(2000, i);\n        this._shortMonthsParse[i] = this.monthsShort(mom, '').toLocaleLowerCase();\n        this._longMonthsParse[i] = this.months(mom, '').toLocaleLowerCase();\n      }\n    }\n    if (strict) {\n      if (format === 'MMM') {\n        ii = this._shortMonthsParse.indexOf(llc);\n        return ii !== -1 ? ii : null;\n      }\n      ii = this._longMonthsParse.indexOf(llc);\n      return ii !== -1 ? ii : null;\n    }\n    if (format === 'MMM') {\n      ii = this._shortMonthsParse.indexOf(llc);\n      if (ii !== -1) {\n        return ii;\n      }\n      ii = this._longMonthsParse.indexOf(llc);\n      return ii !== -1 ? ii : null;\n    }\n    ii = this._longMonthsParse.indexOf(llc);\n    if (ii !== -1) {\n      return ii;\n    }\n    ii = this._shortMonthsParse.indexOf(llc);\n    return ii !== -1 ? ii : null;\n  }\n  handleWeekStrictParse(weekdayName, format, strict) {\n    let ii;\n    const llc = weekdayName.toLocaleLowerCase();\n    if (!this._weekdaysParse) {\n      this._weekdaysParse = [];\n      this._shortWeekdaysParse = [];\n      this._minWeekdaysParse = [];\n      let i;\n      for (i = 0; i < 7; ++i) {\n        const date = setDayOfWeek(new Date(Date.UTC(2000, 1)), i, null, true);\n        this._minWeekdaysParse[i] = this.weekdaysMin(date).toLocaleLowerCase();\n        this._shortWeekdaysParse[i] = this.weekdaysShort(date).toLocaleLowerCase();\n        this._weekdaysParse[i] = this.weekdays(date, '').toLocaleLowerCase();\n      }\n    }\n    if (!isArray(this._weekdaysParse) || !isArray(this._shortWeekdaysParse) || !isArray(this._minWeekdaysParse)) {\n      return;\n    }\n    if (strict) {\n      if (format === 'dddd') {\n        ii = this._weekdaysParse.indexOf(llc);\n        return ii !== -1 ? ii : null;\n      } else if (format === 'ddd') {\n        ii = this._shortWeekdaysParse.indexOf(llc);\n        return ii !== -1 ? ii : null;\n      } else {\n        ii = this._minWeekdaysParse.indexOf(llc);\n        return ii !== -1 ? ii : null;\n      }\n    } else {\n      if (format === 'dddd') {\n        ii = this._weekdaysParse.indexOf(llc);\n        if (ii !== -1) {\n          return ii;\n        }\n        ii = this._shortWeekdaysParse.indexOf(llc);\n        if (ii !== -1) {\n          return ii;\n        }\n        ii = this._minWeekdaysParse.indexOf(llc);\n        return ii !== -1 ? ii : null;\n      } else if (format === 'ddd') {\n        ii = this._shortWeekdaysParse.indexOf(llc);\n        if (ii !== -1) {\n          return ii;\n        }\n        ii = this._weekdaysParse.indexOf(llc);\n        if (ii !== -1) {\n          return ii;\n        }\n        ii = this._minWeekdaysParse.indexOf(llc);\n        return ii !== -1 ? ii : null;\n      } else {\n        ii = this._minWeekdaysParse.indexOf(llc);\n        if (ii !== -1) {\n          return ii;\n        }\n        ii = this._weekdaysParse.indexOf(llc);\n        if (ii !== -1) {\n          return ii;\n        }\n        ii = this._shortWeekdaysParse.indexOf(llc);\n        return ii !== -1 ? ii : null;\n      }\n    }\n  }\n  computeMonthsParse() {\n    const shortPieces = [];\n    const longPieces = [];\n    const mixedPieces = [];\n    let date;\n    let i;\n    for (i = 0; i < 12; i++) {\n      // make the regex if we don't have it already\n      date = new Date(2000, i);\n      shortPieces.push(this.monthsShort(date, ''));\n      longPieces.push(this.months(date, ''));\n      mixedPieces.push(this.months(date, ''));\n      mixedPieces.push(this.monthsShort(date, ''));\n    }\n    // Sorting makes sure if one month (or abbr) is a prefix of another it\n    // will match the longer piece.\n    shortPieces.sort(cmpLenRev);\n    longPieces.sort(cmpLenRev);\n    mixedPieces.sort(cmpLenRev);\n    for (i = 0; i < 12; i++) {\n      shortPieces[i] = regexEscape(shortPieces[i]);\n      longPieces[i] = regexEscape(longPieces[i]);\n    }\n    for (i = 0; i < 24; i++) {\n      mixedPieces[i] = regexEscape(mixedPieces[i]);\n    }\n    this._monthsRegex = new RegExp(`^(${mixedPieces.join('|')})`, 'i');\n    this._monthsShortRegex = this._monthsRegex;\n    this._monthsStrictRegex = new RegExp(`^(${longPieces.join('|')})`, 'i');\n    this._monthsShortStrictRegex = new RegExp(`^(${shortPieces.join('|')})`, 'i');\n  }\n  computeWeekdaysParse() {\n    const minPieces = [];\n    const shortPieces = [];\n    const longPieces = [];\n    const mixedPieces = [];\n    let i;\n    for (i = 0; i < 7; i++) {\n      // make the regex if we don't have it already\n      // let mom = createUTC([2000, 1]).day(i);\n      const date = setDayOfWeek(new Date(Date.UTC(2000, 1)), i, null, true);\n      const minp = this.weekdaysMin(date);\n      const shortp = this.weekdaysShort(date);\n      const longp = this.weekdays(date);\n      minPieces.push(minp);\n      shortPieces.push(shortp);\n      longPieces.push(longp);\n      mixedPieces.push(minp);\n      mixedPieces.push(shortp);\n      mixedPieces.push(longp);\n    }\n    // Sorting makes sure if one weekday (or abbr) is a prefix of another it\n    // will match the longer piece.\n    minPieces.sort(cmpLenRev);\n    shortPieces.sort(cmpLenRev);\n    longPieces.sort(cmpLenRev);\n    mixedPieces.sort(cmpLenRev);\n    for (i = 0; i < 7; i++) {\n      shortPieces[i] = regexEscape(shortPieces[i]);\n      longPieces[i] = regexEscape(longPieces[i]);\n      mixedPieces[i] = regexEscape(mixedPieces[i]);\n    }\n    this._weekdaysRegex = new RegExp(`^(${mixedPieces.join('|')})`, 'i');\n    this._weekdaysShortRegex = this._weekdaysRegex;\n    this._weekdaysMinRegex = this._weekdaysRegex;\n    this._weekdaysStrictRegex = new RegExp(`^(${longPieces.join('|')})`, 'i');\n    this._weekdaysShortStrictRegex = new RegExp(`^(${shortPieces.join('|')})`, 'i');\n    this._weekdaysMinStrictRegex = new RegExp(`^(${minPieces.join('|')})`, 'i');\n  }\n}\nfunction cmpLenRev(a, b) {\n  return b.length - a.length;\n}\nconst defaultCalendar = {\n  sameDay: '[Today at] LT',\n  nextDay: '[Tomorrow at] LT',\n  nextWeek: 'dddd [at] LT',\n  lastDay: '[Yesterday at] LT',\n  lastWeek: '[Last] dddd [at] LT',\n  sameElse: 'L'\n};\nconst defaultInvalidDate = 'Invalid date';\nconst defaultLocaleWeek = {\n  dow: 0,\n  doy: 6 // The week that contains Jan 1st is the first week of the year.\n};\nconst defaultLocaleMeridiemParse = /[ap]\\.?m?\\.?/i;\nconst defaultRelativeTime = {\n  future: 'in %s',\n  past: '%s ago',\n  s: 'a few seconds',\n  ss: '%d seconds',\n  m: 'a minute',\n  mm: '%d minutes',\n  h: 'an hour',\n  hh: '%d hours',\n  d: 'a day',\n  dd: '%d days',\n  M: 'a month',\n  MM: '%d months',\n  y: 'a year',\n  yy: '%d years'\n};\nconst baseConfig = {\n  calendar: defaultCalendar,\n  longDateFormat: defaultLongDateFormat,\n  invalidDate: defaultInvalidDate,\n  ordinal: defaultOrdinal,\n  dayOfMonthOrdinalParse: defaultDayOfMonthOrdinalParse,\n  relativeTime: defaultRelativeTime,\n  months: defaultLocaleMonths,\n  monthsShort: defaultLocaleMonthsShort,\n  week: defaultLocaleWeek,\n  weekdays: defaultLocaleWeekdays,\n  weekdaysMin: defaultLocaleWeekdaysMin,\n  weekdaysShort: defaultLocaleWeekdaysShort,\n  meridiemParse: defaultLocaleMeridiemParse\n};\n\n// compare two arrays, return the number of differences\nfunction compareArrays(array1, array2, dontConvert) {\n  const len = Math.min(array1.length, array2.length);\n  const lengthDiff = Math.abs(array1.length - array2.length);\n  let diffs = 0;\n  let i;\n  for (i = 0; i < len; i++) {\n    if (dontConvert && array1[i] !== array2[i] || !dontConvert && toInt(array1[i]) !== toInt(array2[i])) {\n      diffs++;\n    }\n  }\n  return diffs + lengthDiff;\n}\n\n// FORMATTING\nfunction initWeek() {\n  addFormatToken('w', ['ww', 2, false], 'wo', function (date, opts) {\n    return getWeek(date, opts.locale).toString(10);\n  });\n  addFormatToken('W', ['WW', 2, false], 'Wo', function (date) {\n    return getISOWeek(date).toString(10);\n  });\n  // ALIASES\n  addUnitAlias('week', 'w');\n  addUnitAlias('isoWeek', 'W');\n  // PRIORITIES\n  addUnitPriority('week', 5);\n  addUnitPriority('isoWeek', 5);\n  // PARSING\n  addRegexToken('w', match1to2);\n  addRegexToken('ww', match1to2, match2);\n  addRegexToken('W', match1to2);\n  addRegexToken('WW', match1to2, match2);\n  addWeekParseToken(['w', 'ww', 'W', 'WW'], function (input, week, config, token) {\n    week[token.substr(0, 1)] = toInt(input);\n    return config;\n  });\n  // export function getSetWeek (input) {\n  //   var week = this.localeData().week(this);\n  //   return input == null ? week : this.add((input - week) * 7, 'd');\n  // }\n}\nfunction setWeek(date, input, locale = getLocale()) {\n  const week = getWeek(date, locale);\n  return add(date, (input - week) * 7, 'day');\n}\nfunction getWeek(date, locale = getLocale(), isUTC) {\n  return locale.week(date, isUTC);\n}\n// export function getSetISOWeek (input) {\n//   var week = weekOfYear(this, 1, 4).week;\n//   return input == null ? week : this.add((input - week) * 7, 'd');\n// }\nfunction setISOWeek(date, input) {\n  const week = getISOWeek(date);\n  return add(date, (input - week) * 7, 'day');\n}\nfunction getISOWeek(date, isUTC) {\n  return weekOfYear(date, 1, 4, isUTC).week;\n}\n\n// FORMATTING\nfunction initWeekYear() {\n  addFormatToken(null, ['gg', 2, false], null, function (date, opts) {\n    // return this.weekYear() % 100;\n    return (getWeekYear(date, opts.locale) % 100).toString();\n  });\n  addFormatToken(null, ['GG', 2, false], null, function (date) {\n    // return this.isoWeekYear() % 100;\n    return (getISOWeekYear(date) % 100).toString();\n  });\n  addWeekYearFormatToken('gggg', _getWeekYearFormatCb);\n  addWeekYearFormatToken('ggggg', _getWeekYearFormatCb);\n  addWeekYearFormatToken('GGGG', _getISOWeekYearFormatCb);\n  addWeekYearFormatToken('GGGGG', _getISOWeekYearFormatCb);\n  // ALIASES\n  addUnitAlias('weekYear', 'gg');\n  addUnitAlias('isoWeekYear', 'GG');\n  // PRIORITY\n  addUnitPriority('weekYear', 1);\n  addUnitPriority('isoWeekYear', 1);\n  // PARSING\n  addRegexToken('G', matchSigned);\n  addRegexToken('g', matchSigned);\n  addRegexToken('GG', match1to2, match2);\n  addRegexToken('gg', match1to2, match2);\n  addRegexToken('GGGG', match1to4, match4);\n  addRegexToken('gggg', match1to4, match4);\n  addRegexToken('GGGGG', match1to6, match6);\n  addRegexToken('ggggg', match1to6, match6);\n  addWeekParseToken(['gggg', 'ggggg', 'GGGG', 'GGGGG'], function (input, week, config, token) {\n    week[token.substr(0, 2)] = toInt(input);\n    return config;\n  });\n  addWeekParseToken(['gg', 'GG'], function (input, week, config, token) {\n    week[token] = parseTwoDigitYear(input);\n    return config;\n  });\n}\nfunction addWeekYearFormatToken(token, getter) {\n  addFormatToken(null, [token, token.length, false], null, getter);\n}\nfunction _getWeekYearFormatCb(date, opts) {\n  return getWeekYear(date, opts.locale).toString();\n}\nfunction _getISOWeekYearFormatCb(date) {\n  return getISOWeekYear(date).toString();\n}\n// MOMENTS\nfunction getSetWeekYear(date, input, locale = getLocale(), isUTC) {\n  return getSetWeekYearHelper(date, input,\n  // this.week(),\n  getWeek(date, locale, isUTC),\n  // this.weekday(),\n  getLocaleDayOfWeek(date, locale, isUTC), locale.firstDayOfWeek(), locale.firstDayOfYear(), isUTC);\n}\nfunction getWeekYear(date, locale = getLocale(), isUTC) {\n  return weekOfYear(date, locale.firstDayOfWeek(), locale.firstDayOfYear(), isUTC).year;\n}\nfunction getSetISOWeekYear(date, input, isUTC) {\n  return getSetWeekYearHelper(date, input, getISOWeek(date, isUTC), getISODayOfWeek(date, isUTC), 1, 4);\n}\nfunction getISOWeekYear(date, isUTC) {\n  return weekOfYear(date, 1, 4, isUTC).year;\n}\nfunction getISOWeeksInYear(date, isUTC) {\n  return weeksInYear(getFullYear(date, isUTC), 1, 4);\n}\nfunction getWeeksInYear(date, isUTC, locale = getLocale()) {\n  return weeksInYear(getFullYear(date, isUTC), locale.firstDayOfWeek(), locale.firstDayOfYear());\n}\nfunction getSetWeekYearHelper(date, input, week, weekday, dow, doy, isUTC) {\n  if (!input) {\n    return getWeekYear(date, void 0, isUTC);\n  }\n  const weeksTarget = weeksInYear(input, dow, doy);\n  const _week = week > weeksTarget ? weeksTarget : week;\n  return setWeekAll(date, input, _week, weekday, dow, doy);\n}\nfunction setWeekAll(date, weekYear, week, weekday, dow, doy) {\n  const dayOfYearData = dayOfYearFromWeeks(weekYear, week, weekday, dow, doy);\n  const _date = createUTCDate(dayOfYearData.year, 0, dayOfYearData.dayOfYear);\n  setFullYear(date, getFullYear(_date, true), true);\n  setMonth(date, getMonth(_date, true), true);\n  setDate(date, getDate(_date, true), true);\n  return date;\n}\n\n// todo: add support for timezones\nfunction initTimezone() {\n  // FORMATTING\n  addFormatToken('z', null, null, function (date, opts) {\n    return opts.isUTC ? 'UTC' : '';\n  });\n  addFormatToken('zz', null, null, function (date, opts) {\n    return opts.isUTC ? 'Coordinated Universal Time' : '';\n  });\n}\n// MOMENTS\nfunction getZoneAbbr(isUTC) {\n  return isUTC ? 'UTC' : '';\n}\nfunction getZoneName(isUTC) {\n  return isUTC ? 'Coordinated Universal Time' : '';\n}\nfunction initTimestamp() {\n  // FORMATTING\n  addFormatToken('X', null, null, function (date) {\n    return unix(date).toString(10);\n  });\n  addFormatToken('x', null, null, function (date) {\n    return date.valueOf().toString(10);\n  });\n  // PARSING\n  addRegexToken('x', matchSigned);\n  addRegexToken('X', matchTimestamp);\n  addParseToken('X', function (input, array, config) {\n    config._d = new Date(parseFloat(input) * 1000);\n    return config;\n  });\n  addParseToken('x', function (input, array, config) {\n    config._d = new Date(toInt(input));\n    return config;\n  });\n}\nfunction initSecond() {\n  // FORMATTING\n  addFormatToken('s', ['ss', 2, false], null, function (date, opts) {\n    return getSeconds(date, opts.isUTC).toString(10);\n  });\n  // ALIASES\n  addUnitAlias('second', 's');\n  // PRIORITY\n  addUnitPriority('second', 15);\n  // PARSING\n  addRegexToken('s', match1to2);\n  addRegexToken('ss', match1to2, match2);\n  addParseToken(['s', 'ss'], SECOND);\n}\nfunction initQuarter() {\n  // FORMATTING\n  addFormatToken('Q', null, 'Qo', function (date, opts) {\n    return getQuarter(date, opts.isUTC).toString(10);\n  });\n  // ALIASES\n  addUnitAlias('quarter', 'Q');\n  // PRIORITY\n  addUnitPriority('quarter', 7);\n  // PARSING\n  addRegexToken('Q', match1);\n  addParseToken('Q', function (input, array, config) {\n    array[MONTH] = (toInt(input) - 1) * 3;\n    return config;\n  });\n}\n// MOMENTS\nfunction getQuarter(date, isUTC = false) {\n  return Math.ceil((getMonth(date, isUTC) + 1) / 3);\n}\nfunction setQuarter(date, quarter, isUTC) {\n  return setMonth(date, (quarter - 1) * 3 + getMonth(date, isUTC) % 3, isUTC);\n}\n// export function getSetQuarter(input) {\n//   return input == null\n//     ? Math.ceil((this.month() + 1) / 3)\n//     : this.month((input - 1) * 3 + this.month() % 3);\n// }\n\n// FORMATTING\nfunction addOffsetFormatToken(token, separator) {\n  addFormatToken(token, null, null, function (date, config) {\n    let offset = getUTCOffset(date, {\n      _isUTC: config.isUTC,\n      _offset: config.offset\n    });\n    let sign = '+';\n    if (offset < 0) {\n      offset = -offset;\n      sign = '-';\n    }\n    return sign + zeroFill(~~(offset / 60), 2) + separator + zeroFill(~~offset % 60, 2);\n  });\n}\nfunction initOffset() {\n  addOffsetFormatToken('Z', ':');\n  addOffsetFormatToken('ZZ', '');\n  // PARSING\n  addRegexToken('Z', matchShortOffset);\n  addRegexToken('ZZ', matchShortOffset);\n  addParseToken(['Z', 'ZZ'], function (input, array, config) {\n    config._useUTC = true;\n    config._tzm = offsetFromString(matchShortOffset, input);\n    return config;\n  });\n}\n// HELPERS\n// timezone chunker\n// '+10:00' > ['10',  '00']\n// '-1530'  > ['-15', '30']\nconst chunkOffset = /([\\+\\-]|\\d\\d)/gi;\nfunction offsetFromString(matcher, str) {\n  const matches = (str || '').match(matcher);\n  if (matches === null) {\n    return null;\n  }\n  const chunk = matches[matches.length - 1];\n  const parts = chunk.match(chunkOffset) || ['-', '0', '0'];\n  const minutes = parseInt(parts[1], 10) * 60 + toInt(parts[2]);\n  const _min = parts[0] === '+' ? minutes : -minutes;\n  return minutes === 0 ? 0 : _min;\n}\n// Return a moment from input, that is local/utc/zone equivalent to model.\nfunction cloneWithOffset(input, date, config = {}) {\n  if (!config._isUTC) {\n    return input;\n  }\n  const res = cloneDate(date);\n  // todo: input._d - res._d + ((res._offset || 0) - (input._offset || 0))*60000\n  const offsetDiff = (config._offset || 0) * 60000;\n  const diff = input.valueOf() - res.valueOf() + offsetDiff;\n  // Use low-level api, because this fn is low-level api.\n  res.setTime(res.valueOf() + diff);\n  // todo: add timezone handling\n  // hooks.updateOffset(res, false);\n  return res;\n}\nfunction getDateOffset(date) {\n  // On Firefox.24 Date#getTimezoneOffset returns a floating point.\n  // https://github.com/moment/moment/pull/1871\n  return -Math.round(date.getTimezoneOffset() / 15) * 15;\n}\n// HOOKS\n// This function will be called whenever a moment is mutated.\n// It is intended to keep the offset in sync with the timezone.\n// todo: it's from moment timezones\n// hooks.updateOffset = function () {\n// };\n// MOMENTS\n// keepLocalTime = true means only change the timezone, without\n// affecting the local hour. So 5:31:26 +0300 --[utcOffset(2, true)]-->\n// 5:31:26 +0200 It is possible that 5:31:26 doesn't exist with offset\n// +0200, so we adjust the time as needed, to be valid.\n//\n// Keeping the time actually adds/subtracts (one hour)\n// from the actual represented time. That is why we call updateOffset\n// a second time. In case it wants us to change the offset again\n// _changeInProgress == true case, then we have to adjust, because\n// there is no such time in the given timezone.\nfunction getUTCOffset(date, config = {}) {\n  const _offset = config._offset || 0;\n  return config._isUTC ? _offset : getDateOffset(date);\n}\nfunction setUTCOffset(date, input, keepLocalTime, keepMinutes, config = {}) {\n  const offset = config._offset || 0;\n  let localAdjust;\n  let _input = input;\n  let _date = date;\n  if (isString(_input)) {\n    _input = offsetFromString(matchShortOffset, _input);\n    if (_input === null) {\n      return _date;\n    }\n  } else if (isNumber(_input) && Math.abs(_input) < 16 && !keepMinutes) {\n    _input = _input * 60;\n  }\n  if (!config._isUTC && keepLocalTime) {\n    localAdjust = getDateOffset(_date);\n  }\n  config._offset = _input;\n  config._isUTC = true;\n  if (localAdjust != null) {\n    _date = add(_date, localAdjust, 'minutes');\n  }\n  if (offset !== _input) {\n    if (!keepLocalTime || config._changeInProgress) {\n      _date = add(_date, _input - offset, 'minutes', config._isUTC);\n      // addSubtract(this, createDuration(_input - offset, 'm'), 1, false);\n    } else if (!config._changeInProgress) {\n      config._changeInProgress = true;\n      // todo: add timezone handling\n      // hooks.updateOffset(this, true);\n      config._changeInProgress = null;\n    }\n  }\n  return _date;\n}\n/*\nexport function getSetZone(input, keepLocalTime) {\n  if (input != null) {\n    if (typeof input !== 'string') {\n      input = -input;\n    }\n\n    this.utcOffset(input, keepLocalTime);\n\n    return this;\n  } else {\n    return -this.utcOffset();\n  }\n}\n*/\nfunction setOffsetToUTC(date, keepLocalTime) {\n  return setUTCOffset(date, 0, keepLocalTime);\n}\nfunction isDaylightSavingTime(date) {\n  return getUTCOffset(date) > getUTCOffset(setMonth(cloneDate(date), 0)) || getUTCOffset(date) > getUTCOffset(setMonth(cloneDate(date), 5));\n}\n/*export function setOffsetToLocal(date: Date, isUTC?: boolean, keepLocalTime?: boolean) {\n  if (this._isUTC) {\n    this.utcOffset(0, keepLocalTime);\n    this._isUTC = false;\n\n    if (keepLocalTime) {\n      this.subtract(getDateOffset(this), 'm');\n    }\n  }\n  return this;\n}*/\nfunction setOffsetToParsedOffset(date, input, config = {}) {\n  if (config._tzm != null) {\n    return setUTCOffset(date, config._tzm, false, true, config);\n  }\n  if (isString(input)) {\n    const tZone = offsetFromString(matchOffset, input);\n    if (tZone != null) {\n      return setUTCOffset(date, tZone, false, false, config);\n    }\n    return setUTCOffset(date, 0, true, false, config);\n  }\n  return date;\n}\nfunction hasAlignedHourOffset(date, input) {\n  const _input = input ? getUTCOffset(input, {\n    _isUTC: false\n  }) : 0;\n  return (getUTCOffset(date) - _input) % 60 === 0;\n}\n// DEPRECATED\n/*export function isDaylightSavingTimeShifted() {\n  if (!isUndefined(this._isDSTShifted)) {\n    return this._isDSTShifted;\n  }\n\n  const c = {};\n\n  copyConfig(c, this);\n  c = prepareConfig(c);\n\n  if (c._a) {\n    const other = c._isUTC ? createUTC(c._a) : createLocal(c._a);\n    this._isDSTShifted = this.isValid() &&\n      compareArrays(c._a, other.toArray()) > 0;\n  } else {\n    this._isDSTShifted = false;\n  }\n\n  return this._isDSTShifted;\n}*/\n// in Khronos\n/*export function isLocal() {\n  return this.isValid() ? !this._isUTC : false;\n}\n\nexport function isUtcOffset() {\n  return this.isValid() ? this._isUTC : false;\n}\n\nexport function isUtc() {\n  return this.isValid() ? this._isUTC && this._offset === 0 : false;\n}*/\n\nfunction initMinute() {\n  // FORMATTING\n  addFormatToken('m', ['mm', 2, false], null, function (date, opts) {\n    return getMinutes(date, opts.isUTC).toString(10);\n  });\n  // ALIASES\n  addUnitAlias('minute', 'm');\n  // PRIORITY\n  addUnitPriority('minute', 14);\n  // PARSING\n  addRegexToken('m', match1to2);\n  addRegexToken('mm', match1to2, match2);\n  addParseToken(['m', 'mm'], MINUTE);\n}\n\n// FORMATTING\nfunction initMillisecond() {\n  addFormatToken('S', null, null, function (date, opts) {\n    return (~~(getMilliseconds(date, opts.isUTC) / 100)).toString(10);\n  });\n  addFormatToken(null, ['SS', 2, false], null, function (date, opts) {\n    return (~~(getMilliseconds(date, opts.isUTC) / 10)).toString(10);\n  });\n  addFormatToken(null, ['SSS', 3, false], null, function (date, opts) {\n    return getMilliseconds(date, opts.isUTC).toString(10);\n  });\n  addFormatToken(null, ['SSSS', 4, false], null, function (date, opts) {\n    return (getMilliseconds(date, opts.isUTC) * 10).toString(10);\n  });\n  addFormatToken(null, ['SSSSS', 5, false], null, function (date, opts) {\n    return (getMilliseconds(date, opts.isUTC) * 100).toString(10);\n  });\n  addFormatToken(null, ['SSSSSS', 6, false], null, function (date, opts) {\n    return (getMilliseconds(date, opts.isUTC) * 1000).toString(10);\n  });\n  addFormatToken(null, ['SSSSSSS', 7, false], null, function (date, opts) {\n    return (getMilliseconds(date, opts.isUTC) * 10000).toString(10);\n  });\n  addFormatToken(null, ['SSSSSSSS', 8, false], null, function (date, opts) {\n    return (getMilliseconds(date, opts.isUTC) * 100000).toString(10);\n  });\n  addFormatToken(null, ['SSSSSSSSS', 9, false], null, function (date, opts) {\n    return (getMilliseconds(date, opts.isUTC) * 1000000).toString(10);\n  });\n  // ALIASES\n  addUnitAlias('millisecond', 'ms');\n  // PRIORITY\n  addUnitPriority('millisecond', 16);\n  // PARSING\n  addRegexToken('S', match1to3, match1);\n  addRegexToken('SS', match1to3, match2);\n  addRegexToken('SSS', match1to3, match3);\n  let token;\n  for (token = 'SSSS'; token.length <= 9; token += 'S') {\n    addRegexToken(token, matchUnsigned);\n  }\n  function parseMs(input, array, config) {\n    array[MILLISECOND] = toInt(parseFloat(`0.${input}`) * 1000);\n    return config;\n  }\n  for (token = 'S'; token.length <= 9; token += 'S') {\n    addParseToken(token, parseMs);\n  }\n  // MOMENTS\n}\nfunction initHour() {\n  // FORMATTING\n  function hFormat(date, isUTC) {\n    return getHours(date, isUTC) % 12 || 12;\n  }\n  function kFormat(date, isUTC) {\n    return getHours(date, isUTC) || 24;\n  }\n  addFormatToken('H', ['HH', 2, false], null, function (date, opts) {\n    return getHours(date, opts.isUTC).toString(10);\n  });\n  addFormatToken('h', ['hh', 2, false], null, function (date, opts) {\n    return hFormat(date, opts.isUTC).toString(10);\n  });\n  addFormatToken('k', ['kk', 2, false], null, function (date, opts) {\n    return kFormat(date, opts.isUTC).toString(10);\n  });\n  addFormatToken('hmm', null, null, function (date, opts) {\n    const _h = hFormat(date, opts.isUTC);\n    const _mm = zeroFill(getMinutes(date, opts.isUTC), 2);\n    return `${_h}${_mm}`;\n  });\n  addFormatToken('hmmss', null, null, function (date, opts) {\n    const _h = hFormat(date, opts.isUTC);\n    const _mm = zeroFill(getMinutes(date, opts.isUTC), 2);\n    const _ss = zeroFill(getSeconds(date, opts.isUTC), 2);\n    return `${_h}${_mm}${_ss}`;\n  });\n  addFormatToken('Hmm', null, null, function (date, opts) {\n    const _H = getHours(date, opts.isUTC);\n    const _mm = zeroFill(getMinutes(date, opts.isUTC), 2);\n    return `${_H}${_mm}`;\n  });\n  addFormatToken('Hmmss', null, null, function (date, opts) {\n    const _H = getHours(date, opts.isUTC);\n    const _mm = zeroFill(getMinutes(date, opts.isUTC), 2);\n    const _ss = zeroFill(getSeconds(date, opts.isUTC), 2);\n    return `${_H}${_mm}${_ss}`;\n  });\n  function meridiem(token, lowercase) {\n    addFormatToken(token, null, null, function (date, opts) {\n      return opts.locale.meridiem(getHours(date, opts.isUTC), getMinutes(date, opts.isUTC), lowercase);\n    });\n  }\n  meridiem('a', true);\n  meridiem('A', false);\n  // ALIASES\n  addUnitAlias('hour', 'h');\n  // PRIORITY\n  addUnitPriority('hour', 13);\n  // PARSING\n  function matchMeridiem(isStrict, locale) {\n    return locale._meridiemParse;\n  }\n  addRegexToken('a', matchMeridiem);\n  addRegexToken('A', matchMeridiem);\n  addRegexToken('H', match1to2);\n  addRegexToken('h', match1to2);\n  addRegexToken('k', match1to2);\n  addRegexToken('HH', match1to2, match2);\n  addRegexToken('hh', match1to2, match2);\n  addRegexToken('kk', match1to2, match2);\n  addRegexToken('hmm', match3to4);\n  addRegexToken('hmmss', match5to6);\n  addRegexToken('Hmm', match3to4);\n  addRegexToken('Hmmss', match5to6);\n  addParseToken(['H', 'HH'], HOUR);\n  addParseToken(['k', 'kk'], function (input, array, config) {\n    const kInput = toInt(input);\n    array[HOUR] = kInput === 24 ? 0 : kInput;\n    return config;\n  });\n  addParseToken(['a', 'A'], function (input, array, config) {\n    config._isPm = config._locale.isPM(input);\n    config._meridiem = input;\n    return config;\n  });\n  addParseToken(['h', 'hh'], function (input, array, config) {\n    array[HOUR] = toInt(input);\n    getParsingFlags(config).bigHour = true;\n    return config;\n  });\n  addParseToken('hmm', function (input, array, config) {\n    const pos = input.length - 2;\n    array[HOUR] = toInt(input.substr(0, pos));\n    array[MINUTE] = toInt(input.substr(pos));\n    getParsingFlags(config).bigHour = true;\n    return config;\n  });\n  addParseToken('hmmss', function (input, array, config) {\n    const pos1 = input.length - 4;\n    const pos2 = input.length - 2;\n    array[HOUR] = toInt(input.substr(0, pos1));\n    array[MINUTE] = toInt(input.substr(pos1, 2));\n    array[SECOND] = toInt(input.substr(pos2));\n    getParsingFlags(config).bigHour = true;\n    return config;\n  });\n  addParseToken('Hmm', function (input, array, config) {\n    const pos = input.length - 2;\n    array[HOUR] = toInt(input.substr(0, pos));\n    array[MINUTE] = toInt(input.substr(pos));\n    return config;\n  });\n  addParseToken('Hmmss', function (input, array, config) {\n    const pos1 = input.length - 4;\n    const pos2 = input.length - 2;\n    array[HOUR] = toInt(input.substr(0, pos1));\n    array[MINUTE] = toInt(input.substr(pos1, 2));\n    array[SECOND] = toInt(input.substr(pos2));\n    return config;\n  });\n}\n\n// internal storage for locale config files\nconst locales = {};\nconst localeFamilies = {};\nlet globalLocale;\nfunction normalizeLocale(key) {\n  return key ? key.toLowerCase().replace('_', '-') : key;\n}\n// pick the locale from the array\n// try ['en-au', 'en-gb'] as 'en-au', 'en-gb', 'en', as in move through the list trying each\n// substring from most specific to least,\n// but move to the next array item if it's a more specific variant than the current root\nfunction chooseLocale(names) {\n  let next;\n  let locale;\n  let i = 0;\n  while (i < names.length) {\n    const split = normalizeLocale(names[i]).split('-');\n    let j = split.length;\n    next = normalizeLocale(names[i + 1]);\n    next = next ? next.split('-') : null;\n    while (j > 0) {\n      locale = loadLocale(split.slice(0, j).join('-'));\n      if (locale) {\n        return locale;\n      }\n      if (next && next.length >= j && compareArrays(split, next, true) >= j - 1) {\n        // the next array item is better than a shallower substring of this one\n        break;\n      }\n      j--;\n    }\n    i++;\n  }\n  return null;\n}\nfunction mergeConfigs(parentConfig, childConfig) {\n  const res = Object.assign({}, parentConfig);\n  for (const childProp in childConfig) {\n    if (!hasOwnProp(childConfig, childProp)) {\n      continue;\n    }\n    if (isObject(parentConfig[childProp]) && isObject(childConfig[childProp])) {\n      res[childProp] = {};\n      Object.assign(res[childProp], parentConfig[childProp]);\n      Object.assign(res[childProp], childConfig[childProp]);\n    } else if (childConfig[childProp] != null) {\n      res[childProp] = childConfig[childProp];\n    } else {\n      delete res[childProp];\n    }\n  }\n  for (const parentProp in parentConfig) {\n    if (hasOwnProp(parentConfig, parentProp) && !hasOwnProp(childConfig, parentProp) && isObject(parentConfig[parentProp])) {\n      // make sure changes to properties don't modify parent config\n      res[parentProp] = Object.assign({}, res[parentProp]);\n    }\n  }\n  return res;\n}\nfunction loadLocale(name) {\n  // no way!\n  /* var oldLocale = null;\n   // TODO: Find a better way to register and load all the locales in Node\n   if (!locales[name] && (typeof module !== 'undefined') &&\n     module && module.exports) {\n     try {\n       oldLocale = globalLocale._abbr;\n       var aliasedRequire = require;\n       aliasedRequire('./locale/' + name);\n       getSetGlobalLocale(oldLocale);\n     } catch (e) {}\n   }*/\n  if (!locales[name]) {\n    console.error(`Khronos locale error: please load locale \"${name}\" before using it`);\n    // throw new Error(`Khronos locale error: please load locale \"${name}\" before using it`);\n  }\n  return locales[name];\n}\n// This function will load locale and then set the global locale.  If\n// no arguments are passed in, it will simply return the current global\n// locale key.\nfunction getSetGlobalLocale(key, values) {\n  let data;\n  if (key) {\n    if (isUndefined(values)) {\n      data = getLocale(key);\n    } else if (isString(key)) {\n      data = defineLocale(key, values);\n    }\n    if (data) {\n      globalLocale = data;\n    }\n  }\n  return globalLocale && globalLocale._abbr;\n}\nfunction defineLocale(name, config) {\n  if (config === null) {\n    // useful for testing\n    delete locales[name];\n    globalLocale = getLocale('en');\n    return null;\n  }\n  if (!config) {\n    return;\n  }\n  let parentConfig = baseConfig;\n  config.abbr = name;\n  if (config.parentLocale != null) {\n    if (locales[config.parentLocale] != null) {\n      parentConfig = locales[config.parentLocale]._config;\n    } else {\n      if (!localeFamilies[config.parentLocale]) {\n        localeFamilies[config.parentLocale] = [];\n      }\n      localeFamilies[config.parentLocale].push({\n        name,\n        config\n      });\n      return null;\n    }\n  }\n  locales[name] = new Locale(mergeConfigs(parentConfig, config));\n  if (localeFamilies[name]) {\n    localeFamilies[name].forEach(function (x) {\n      defineLocale(x.name, x.config);\n    });\n  }\n  // backwards compat for now: also set the locale\n  // make sure we set the locale AFTER all child locales have been\n  // created, so we won't end up with the child locale set.\n  getSetGlobalLocale(name);\n  return locales[name];\n}\nfunction updateLocale(name, config) {\n  let _config = config;\n  if (_config != null) {\n    let parentConfig = baseConfig;\n    // MERGE\n    const tmpLocale = loadLocale(name);\n    if (tmpLocale != null) {\n      parentConfig = tmpLocale._config;\n    }\n    _config = mergeConfigs(parentConfig, _config);\n    const locale = new Locale(_config);\n    locale.parentLocale = locales[name];\n    locales[name] = locale;\n    // backwards compat for now: also set the locale\n    getSetGlobalLocale(name);\n  } else {\n    // pass null for config to unupdate, useful for tests\n    if (locales[name] != null) {\n      if (locales[name].parentLocale != null) {\n        locales[name] = locales[name].parentLocale;\n      } else if (locales[name] != null) {\n        delete locales[name];\n      }\n    }\n  }\n  return locales[name];\n}\n// returns locale data\nfunction getLocale(key) {\n  setDefaultLocale();\n  if (!key) {\n    return globalLocale;\n  }\n  // let locale;\n  const _key = isArray(key) ? key : [key];\n  return chooseLocale(_key);\n}\nfunction listLocales() {\n  return Object.keys(locales);\n}\nfunction setDefaultLocale() {\n  if (locales[`en`]) {\n    return undefined;\n  }\n  getSetGlobalLocale('en', {\n    dayOfMonthOrdinalParse: /\\d{1,2}(th|st|nd|rd)/,\n    ordinal(num) {\n      const b = num % 10;\n      const output = toInt(num % 100 / 10) === 1 ? 'th' : b === 1 ? 'st' : b === 2 ? 'nd' : b === 3 ? 'rd' : 'th';\n      return num + output;\n    }\n  });\n  initWeek();\n  initWeekYear();\n  initYear();\n  initTimezone();\n  initTimestamp();\n  initSecond();\n  initQuarter();\n  initOffset();\n  initMonth();\n  initMinute();\n  initMillisecond();\n  initHour();\n  initDayOfYear();\n  initDayOfWeek();\n  initDayOfMonth();\n}\nconst ordering = ['year', 'quarter', 'month', 'week', 'day', 'hours', 'minutes', 'seconds', 'milliseconds'];\nconst orderingHash = ordering.reduce((mem, order) => {\n  mem[order] = true;\n  return mem;\n}, {});\nfunction isDurationValid(duration) {\n  const durationKeys = Object.keys(duration);\n  if (durationKeys.some(key => {\n    return key in orderingHash && duration[key] === null || isNaN(duration[key]);\n  })) {\n    return false;\n  }\n  // for (let key in duration) {\n  //   if (!(indexOf.call(ordering, key) !== -1 && (duration[key] == null || !isNaN(duration[key])))) {\n  //     return false;\n  //   }\n  // }\n  let unitHasDecimal = false;\n  for (let i = 0; i < ordering.length; ++i) {\n    if (duration[ordering[i]]) {\n      // only allow non-integers for smallest unit\n      if (unitHasDecimal) {\n        return false;\n      }\n      if (duration[ordering[i]] !== toInt(duration[ordering[i]])) {\n        unitHasDecimal = true;\n      }\n    }\n  }\n  return true;\n}\n// export function isValid() {\n//   return this._isValid;\n// }\n//\n// export function createInvalid(): Duration {\n//   return createDuration(NaN);\n// }\n\nfunction absCeil(number) {\n  return number < 0 ? Math.floor(number) : Math.ceil(number);\n}\nfunction bubble(dur) {\n  let milliseconds = dur._milliseconds;\n  let days = dur._days;\n  let months = dur._months;\n  const data = dur._data;\n  // if we have a mix of positive and negative values, bubble down first\n  // check: https://github.com/moment/moment/issues/2166\n  if (!(milliseconds >= 0 && days >= 0 && months >= 0 || milliseconds <= 0 && days <= 0 && months <= 0)) {\n    milliseconds += absCeil(monthsToDays(months) + days) * 864e5;\n    days = 0;\n    months = 0;\n  }\n  // The following code bubbles up values, see the tests for\n  // examples of what that means.\n  data.milliseconds = milliseconds % 1000;\n  const seconds = absFloor(milliseconds / 1000);\n  data.seconds = seconds % 60;\n  const minutes = absFloor(seconds / 60);\n  data.minutes = minutes % 60;\n  const hours = absFloor(minutes / 60);\n  data.hours = hours % 24;\n  days += absFloor(hours / 24);\n  // convert days to months\n  const monthsFromDays = absFloor(daysToMonths(days));\n  months += monthsFromDays;\n  days -= absCeil(monthsToDays(monthsFromDays));\n  // 12 months -> 1 year\n  const years = absFloor(months / 12);\n  months %= 12;\n  data.day = days;\n  data.month = months;\n  data.year = years;\n  return dur;\n}\nfunction daysToMonths(day) {\n  // 400 years have 146097 days (taking into account leap year rules)\n  // 400 years have 12 months === 4800\n  return day * 4800 / 146097;\n}\nfunction monthsToDays(month) {\n  // the reverse of daysToMonths\n  return month * 146097 / 4800;\n}\nlet round = Math.round;\nconst thresholds = {\n  ss: 44,\n  s: 45,\n  m: 45,\n  h: 22,\n  d: 26,\n  M: 11 // months to year\n};\n// helper function for moment.fn.from, moment.fn.fromNow, and moment.duration.fn.humanize\nfunction substituteTimeAgo(str, num, withoutSuffix, isFuture, locale) {\n  return locale.relativeTime(num || 1, !!withoutSuffix, str, isFuture);\n}\nfunction relativeTime(posNegDuration, withoutSuffix, locale) {\n  const duration = createDuration(posNegDuration).abs();\n  const seconds = round(duration.as('s'));\n  const minutes = round(duration.as('m'));\n  const hours = round(duration.as('h'));\n  const days = round(duration.as('d'));\n  const months = round(duration.as('M'));\n  const years = round(duration.as('y'));\n  const a = seconds <= thresholds[\"ss\"] && ['s', seconds] || seconds < thresholds[\"s\"] && ['ss', seconds] || minutes <= 1 && ['m'] || minutes < thresholds[\"m\"] && ['mm', minutes] || hours <= 1 && ['h'] || hours < thresholds[\"h\"] && ['hh', hours] || days <= 1 && ['d'] || days < thresholds[\"d\"] && ['dd', days] || months <= 1 && ['M'] || months < thresholds[\"M\"] && ['MM', months] || years <= 1 && ['y'] || ['yy', years];\n  const b = [a[0], a[1], withoutSuffix, +posNegDuration > 0, locale];\n  // a[2] = withoutSuffix;\n  // a[3] = +posNegDuration > 0;\n  // a[4] = locale;\n  return substituteTimeAgo.apply(null, b);\n}\n// This function allows you to set the rounding function for relative time strings\nfunction getSetRelativeTimeRounding(roundingFunction) {\n  if (roundingFunction === undefined) {\n    return round;\n  }\n  if (typeof roundingFunction === 'function') {\n    round = roundingFunction;\n    return true;\n  }\n  return false;\n}\n// This function allows you to set a threshold for relative time strings\nfunction getSetRelativeTimeThreshold(threshold, limit) {\n  if (thresholds[threshold] === undefined) {\n    return false;\n  }\n  if (limit === undefined) {\n    return thresholds[threshold];\n  }\n  thresholds[threshold] = limit;\n  if (threshold === 's') {\n    thresholds[\"ss\"] = limit - 1;\n  }\n  return true;\n}\n// export function humanize(withSuffix) {\n//   if (!this.isValid()) {\n//     return this.localeData().invalidDate();\n//   }\n//\n//   const locale = this.localeData();\n//   let output = relativeTime(this, !withSuffix, locale);\n//\n//   if (withSuffix) {\n//     output = locale.pastFuture(+this, output);\n//   }\n//\n//   return locale.postformat(output);\n// }\n\nclass Duration {\n  constructor(duration, config = {}) {\n    this._data = {};\n    this._locale = getLocale();\n    this._locale = config && config._locale || getLocale();\n    // const normalizedInput = normalizeObjectUnits(duration);\n    const normalizedInput = duration;\n    const years = normalizedInput.year || 0;\n    const quarters = normalizedInput.quarter || 0;\n    const months = normalizedInput.month || 0;\n    const weeks = normalizedInput.week || 0;\n    const days = normalizedInput.day || 0;\n    const hours = normalizedInput.hours || 0;\n    const minutes = normalizedInput.minutes || 0;\n    const seconds = normalizedInput.seconds || 0;\n    const milliseconds = normalizedInput.milliseconds || 0;\n    this._isValid = isDurationValid(normalizedInput);\n    // representation for dateAddRemove\n    this._milliseconds = +milliseconds + seconds * 1000 + minutes * 60 * 1000 +\n    // 1000 * 60\n    hours * 1000 * 60 * 60; // using 1000 * 60 * 60\n    // instead of 36e5 to avoid floating point rounding errors https://github.com/moment/moment/issues/2978\n    // Because of dateAddRemove treats 24 hours as different from a\n    // day when working around DST, we need to store them separately\n    this._days = +days + weeks * 7;\n    // It is impossible to translate months into days without knowing\n    // which months you are are talking about, so we have to store\n    // it separately.\n    this._months = +months + quarters * 3 + years * 12;\n    // this._data = {};\n    // this._locale = getLocale();\n    // this._bubble();\n    return bubble(this);\n  }\n  isValid() {\n    return this._isValid;\n  }\n  humanize(withSuffix) {\n    // throw new Error(`TODO: implement`);\n    if (!this.isValid()) {\n      return this.localeData().invalidDate;\n    }\n    const locale = this.localeData();\n    let output = relativeTime(this, !withSuffix, locale);\n    if (withSuffix) {\n      output = locale.pastFuture(+this, output);\n    }\n    return locale.postformat(output);\n  }\n  localeData() {\n    return this._locale;\n  }\n  locale(localeKey) {\n    if (!localeKey) {\n      return this._locale._abbr;\n    }\n    this._locale = getLocale(localeKey) || this._locale;\n    return this;\n  }\n  abs() {\n    const mathAbs = Math.abs;\n    const data = this._data;\n    this._milliseconds = mathAbs(this._milliseconds);\n    this._days = mathAbs(this._days);\n    this._months = mathAbs(this._months);\n    data.milliseconds = mathAbs(data.milliseconds);\n    data.seconds = mathAbs(data.seconds);\n    data.minutes = mathAbs(data.minutes);\n    data.hours = mathAbs(data.hours);\n    data.month = mathAbs(data.month);\n    data.year = mathAbs(data.year);\n    return this;\n  }\n  as(_units) {\n    if (!this.isValid()) {\n      return NaN;\n    }\n    let days;\n    let months;\n    const milliseconds = this._milliseconds;\n    const units = normalizeUnits(_units);\n    if (units === 'month' || units === 'year') {\n      days = this._days + milliseconds / 864e5;\n      months = this._months + daysToMonths(days);\n      return units === 'month' ? months : months / 12;\n    }\n    // handle milliseconds separately because of floating point math errors (issue #1867)\n    days = this._days + Math.round(monthsToDays(this._months));\n    switch (units) {\n      case 'week':\n        return days / 7 + milliseconds / 6048e5;\n      case 'day':\n        return days + milliseconds / 864e5;\n      case 'hours':\n        return days * 24 + milliseconds / 36e5;\n      case 'minutes':\n        return days * 1440 + milliseconds / 6e4;\n      case 'seconds':\n        return days * 86400 + milliseconds / 1000;\n      // Math.floor prevents floating point math errors here\n      case 'milliseconds':\n        return Math.floor(days * 864e5) + milliseconds;\n      default:\n        throw new Error(`Unknown unit ${units}`);\n    }\n  }\n  valueOf() {\n    if (!this.isValid()) {\n      return NaN;\n    }\n    return this._milliseconds + this._days * 864e5 + this._months % 12 * 2592e6 + toInt(this._months / 12) * 31536e6;\n  }\n}\nfunction isDuration(obj) {\n  return obj instanceof Duration;\n}\nfunction isValid(config) {\n  if (config._isValid == null) {\n    const flags = getParsingFlags(config);\n    const parsedParts = Array.prototype.some.call(flags.parsedDateParts, function (i) {\n      return i != null;\n    });\n    let isNowValid = !isNaN(config._d && config._d.getTime()) && flags.overflow < 0 && !flags.empty && !flags.invalidMonth && !flags.invalidWeekday && !flags.weekdayMismatch && !flags.nullInput && !flags.invalidFormat && !flags.userInvalidated && (!flags.meridiem || flags.meridiem && parsedParts);\n    if (config._strict) {\n      isNowValid = isNowValid && flags.charsLeftOver === 0 && flags.unusedTokens.length === 0 && flags.bigHour === undefined;\n    }\n    if (Object.isFrozen == null || !Object.isFrozen(config)) {\n      config._isValid = isNowValid;\n    } else {\n      return isNowValid;\n    }\n  }\n  return config._isValid;\n}\nfunction createInvalid(config, flags) {\n  config._d = new Date(NaN);\n  Object.assign(getParsingFlags(config), flags || {\n    userInvalidated: true\n  });\n  return config;\n}\nfunction markInvalid(config) {\n  config._isValid = false;\n  return config;\n}\n\n// iso 8601 regex\n// 0000-00-00 0000-W00 or 0000-W00-0 + T + 00 or 00:00 or 00:00:00 or 00:00:00.000 + +00:00 or +0000 or +00)\nconst extendedIsoRegex = /^\\s*((?:[+-]\\d{6}|\\d{4})-(?:\\d\\d-\\d\\d|W\\d\\d-\\d|W\\d\\d|\\d\\d\\d|\\d\\d))(?:(T| )(\\d\\d(?::\\d\\d(?::\\d\\d(?:[.,]\\d+)?)?)?)([\\+\\-]\\d\\d(?::?\\d\\d)?|\\s*Z)?)?$/;\nconst basicIsoRegex = /^\\s*((?:[+-]\\d{6}|\\d{4})(?:\\d\\d\\d\\d|W\\d\\d\\d|W\\d\\d|\\d\\d\\d|\\d\\d))(?:(T| )(\\d\\d(?:\\d\\d(?:\\d\\d(?:[.,]\\d+)?)?)?)([\\+\\-]\\d\\d(?::?\\d\\d)?|\\s*Z)?)?$/;\nconst tzRegex = /Z|[+-]\\d\\d(?::?\\d\\d)?/;\nconst isoDates = [['YYYYYY-MM-DD', /[+-]\\d{6}-\\d\\d-\\d\\d/, true], ['YYYY-MM-DD', /\\d{4}-\\d\\d-\\d\\d/, true], ['GGGG-[W]WW-E', /\\d{4}-W\\d\\d-\\d/, true], ['GGGG-[W]WW', /\\d{4}-W\\d\\d/, false], ['YYYY-DDD', /\\d{4}-\\d{3}/, true], ['YYYY-MM', /\\d{4}-\\d\\d/, false], ['YYYYYYMMDD', /[+-]\\d{10}/, true], ['YYYYMMDD', /\\d{8}/, true],\n// YYYYMM is NOT allowed by the standard\n['GGGG[W]WWE', /\\d{4}W\\d{3}/, true], ['GGGG[W]WW', /\\d{4}W\\d{2}/, false], ['YYYYDDD', /\\d{7}/, true]];\n// iso time formats and regexes\nconst isoTimes = [['HH:mm:ss.SSSS', /\\d\\d:\\d\\d:\\d\\d\\.\\d+/], ['HH:mm:ss,SSSS', /\\d\\d:\\d\\d:\\d\\d,\\d+/], ['HH:mm:ss', /\\d\\d:\\d\\d:\\d\\d/], ['HH:mm', /\\d\\d:\\d\\d/], ['HHmmss.SSSS', /\\d\\d\\d\\d\\d\\d\\.\\d+/], ['HHmmss,SSSS', /\\d\\d\\d\\d\\d\\d,\\d+/], ['HHmmss', /\\d\\d\\d\\d\\d\\d/], ['HHmm', /\\d\\d\\d\\d/], ['HH', /\\d\\d/]];\nconst aspNetJsonRegex = /^\\/?Date\\((\\-?\\d+)/i;\nconst obsOffsets = {\n  UT: 0,\n  GMT: 0,\n  EDT: -4 * 60,\n  EST: -5 * 60,\n  CDT: -5 * 60,\n  CST: -6 * 60,\n  MDT: -6 * 60,\n  MST: -7 * 60,\n  PDT: -7 * 60,\n  PST: -8 * 60\n};\n// RFC 2822 regex: For details see https://tools.ietf.org/html/rfc2822#section-3.3\nconst rfc2822 = /^(?:(Mon|Tue|Wed|Thu|Fri|Sat|Sun),?\\s)?(\\d{1,2})\\s(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)\\s(\\d{2,4})\\s(\\d\\d):(\\d\\d)(?::(\\d\\d))?\\s(?:(UT|GMT|[ECMP][SD]T)|([Zz])|([+-]\\d{4}))$/;\n// date from iso format\nfunction configFromISO(config) {\n  if (!isString(config._i)) {\n    return config;\n  }\n  const input = config._i;\n  const match = extendedIsoRegex.exec(input) || basicIsoRegex.exec(input);\n  let allowTime;\n  let dateFormat;\n  let timeFormat;\n  let tzFormat;\n  if (!match) {\n    config._isValid = false;\n    return config;\n  }\n  // getParsingFlags(config).iso = true;\n  let i;\n  let l;\n  for (i = 0, l = isoDates.length; i < l; i++) {\n    if (isoDates[i][1].exec(match[1])) {\n      dateFormat = isoDates[i][0];\n      allowTime = isoDates[i][2] !== false;\n      break;\n    }\n  }\n  if (dateFormat == null) {\n    config._isValid = false;\n    return config;\n  }\n  if (match[3]) {\n    for (i = 0, l = isoTimes.length; i < l; i++) {\n      if (isoTimes[i][1].exec(match[3])) {\n        // match[2] should be 'T' or space\n        timeFormat = (match[2] || ' ') + isoTimes[i][0];\n        break;\n      }\n    }\n    if (timeFormat == null) {\n      config._isValid = false;\n      return config;\n    }\n  }\n  if (!allowTime && timeFormat != null) {\n    config._isValid = false;\n    return config;\n  }\n  if (match[4]) {\n    if (tzRegex.exec(match[4])) {\n      tzFormat = 'Z';\n    } else {\n      config._isValid = false;\n      return config;\n    }\n  }\n  config._f = dateFormat + (timeFormat || '') + (tzFormat || '');\n  return configFromStringAndFormat(config);\n}\nfunction extractFromRFC2822Strings(yearStr, monthStr, dayStr, hourStr, minuteStr, secondStr) {\n  const result = [untruncateYear(yearStr), defaultLocaleMonthsShort.indexOf(monthStr), parseInt(dayStr, 10), parseInt(hourStr, 10), parseInt(minuteStr, 10)];\n  if (secondStr) {\n    result.push(parseInt(secondStr, 10));\n  }\n  return result;\n}\nfunction untruncateYear(yearStr) {\n  const year = parseInt(yearStr, 10);\n  return year <= 49 ? year + 2000 : year;\n}\nfunction preprocessRFC2822(str) {\n  // Remove comments and folding whitespace and replace multiple-spaces with a single space\n  return str.replace(/\\([^)]*\\)|[\\n\\t]/g, ' ').replace(/(\\s\\s+)/g, ' ').trim();\n}\nfunction checkWeekday(weekdayStr, parsedInput, config) {\n  if (weekdayStr) {\n    // TODO: Replace the vanilla JS Date object with an indepentent day-of-week check.\n    const weekdayProvided = defaultLocaleWeekdaysShort.indexOf(weekdayStr);\n    const weekdayActual = new Date(parsedInput[0], parsedInput[1], parsedInput[2]).getDay();\n    if (weekdayProvided !== weekdayActual) {\n      getParsingFlags(config).weekdayMismatch = true;\n      config._isValid = false;\n      return false;\n    }\n  }\n  return true;\n}\nfunction calculateOffset(obsOffset, militaryOffset, numOffset) {\n  if (obsOffset) {\n    return obsOffsets[obsOffset];\n  } else if (militaryOffset) {\n    // the only allowed military tz is Z\n    return 0;\n  } else {\n    const hm = parseInt(numOffset, 10);\n    const m = hm % 100;\n    const h = (hm - m) / 100;\n    return h * 60 + m;\n  }\n}\n// date and time from ref 2822 format\nfunction configFromRFC2822(config) {\n  if (!isString(config._i)) {\n    return config;\n  }\n  const match = rfc2822.exec(preprocessRFC2822(config._i));\n  if (!match) {\n    return markInvalid(config);\n  }\n  const parsedArray = extractFromRFC2822Strings(match[4], match[3], match[2], match[5], match[6], match[7]);\n  if (!checkWeekday(match[1], parsedArray, config)) {\n    return config;\n  }\n  config._a = parsedArray;\n  config._tzm = calculateOffset(match[8], match[9], match[10]);\n  config._d = createUTCDate.apply(null, config._a);\n  config._d.setUTCMinutes(config._d.getUTCMinutes() - config._tzm);\n  getParsingFlags(config).rfc2822 = true;\n  return config;\n}\n// date from iso format or fallback\nfunction configFromString(config) {\n  if (!isString(config._i)) {\n    return config;\n  }\n  const matched = aspNetJsonRegex.exec(config._i);\n  if (matched !== null) {\n    config._d = new Date(+matched[1]);\n    return config;\n  }\n  // todo: update logic processing\n  // isISO -> configFromISO\n  // isRFC -> configFromRFC\n  configFromISO(config);\n  if (config._isValid === false) {\n    delete config._isValid;\n  } else {\n    return config;\n  }\n  configFromRFC2822(config);\n  if (config._isValid === false) {\n    delete config._isValid;\n  } else {\n    return config;\n  }\n  // Final attempt, use Input Fallback\n  // hooks.createFromInputFallback(config);\n  return createInvalid(config);\n}\n// hooks.createFromInputFallback = deprecate(\n//     'value provided is not in a recognized RFC2822 or ISO format. moment construction falls back to js Date(), ' +\n//     'which is not reliable across all browsers and versions. Non RFC2822/ISO date formats are ' +\n//     'discouraged and will be removed in an upcoming major release. Please refer to ' +\n//     'http://momentjs.com/guides/#/warnings/js-date/ for more info.',\n//     function (config) {\n//         config._d = new Date(config._i + (config._useUTC ? ' UTC' : ''));\n//     }\n// );\n\n// moment.js\n// version : 2.18.1\n// authors : Tim Wood, Iskren Chernev, Moment.js contributors\n// license : MIT\n// momentjs.com\nfunction formatDate(date, format, locale, isUTC, offset = 0) {\n  const _locale = getLocale(locale || 'en');\n  if (!_locale) {\n    throw new Error(`Locale \"${locale}\" is not defined, please add it with \"defineLocale(...)\"`);\n  }\n  const _format = format || (isUTC ? 'YYYY-MM-DDTHH:mm:ss[Z]' : 'YYYY-MM-DDTHH:mm:ssZ');\n  const output = formatMoment(date, _format, _locale, isUTC, offset);\n  if (!output) {\n    return output;\n  }\n  return _locale.postformat(output);\n}\n// format date using native date object\nfunction formatMoment(date, _format, locale, isUTC, offset = 0) {\n  if (!isDateValid(date)) {\n    return locale.invalidDate;\n  }\n  const format = expandFormat(_format, locale);\n  formatFunctions[format] = formatFunctions[format] || makeFormatFunction(format);\n  return formatFunctions[format](date, locale, isUTC, offset);\n}\nfunction expandFormat(_format, locale) {\n  let format = _format;\n  let i = 5;\n  const localFormattingTokens = /(\\[[^\\[]*\\])|(\\\\)?(LTS|LT|LL?L?L?|l{1,4})/g;\n  const replaceLongDateFormatTokens = input => {\n    return locale.formatLongDate(input) || input;\n  };\n  localFormattingTokens.lastIndex = 0;\n  while (i >= 0 && localFormattingTokens.test(format)) {\n    format = format.replace(localFormattingTokens, replaceLongDateFormatTokens);\n    localFormattingTokens.lastIndex = 0;\n    i -= 1;\n  }\n  return format;\n}\n\n// Pick the first defined of two or three arguments.\nfunction defaults(a, b, c) {\n  if (a != null) {\n    return a;\n  }\n  if (b != null) {\n    return b;\n  }\n  return c;\n}\nfunction currentDateArray(config) {\n  const nowValue = new Date();\n  if (config._useUTC) {\n    return [nowValue.getUTCFullYear(), nowValue.getUTCMonth(), nowValue.getUTCDate()];\n  }\n  return [nowValue.getFullYear(), nowValue.getMonth(), nowValue.getDate()];\n}\n// convert an array to a date.\n// the array should mirror the parameters below\n// note: all values past the year are optional and will default to the lowest possible value.\n// [year, month, day , hour, minute, second, millisecond]\nfunction configFromArray(config) {\n  const input = [];\n  let i;\n  let date;\n  let yearToUse;\n  if (config._d) {\n    return config;\n  }\n  const currentDate = currentDateArray(config);\n  // compute day of the year from weeks and weekdays\n  if (config._w && config._a[DATE] == null && config._a[MONTH] == null) {\n    dayOfYearFromWeekInfo(config);\n  }\n  // if the day of the year is set, figure out what it is\n  if (config._dayOfYear != null) {\n    yearToUse = defaults(config._a[YEAR], currentDate[YEAR]);\n    if (config._dayOfYear > daysInYear(yearToUse) || config._dayOfYear === 0) {\n      getParsingFlags(config)._overflowDayOfYear = true;\n    }\n    date = new Date(Date.UTC(yearToUse, 0, config._dayOfYear));\n    config._a[MONTH] = date.getUTCMonth();\n    config._a[DATE] = date.getUTCDate();\n  }\n  // Default to current date.\n  // * if no year, month, day of month are given, default to today\n  // * if day of month is given, default month and year\n  // * if month is given, default only year\n  // * if year is given, don't default anything\n  for (i = 0; i < 3 && config._a[i] == null; ++i) {\n    config._a[i] = input[i] = currentDate[i];\n  }\n  // Zero out whatever was not defaulted, including time\n  for (; i < 7; i++) {\n    config._a[i] = input[i] = config._a[i] == null ? i === 2 ? 1 : 0 : config._a[i];\n  }\n  // Check for 24:00:00.000\n  if (config._a[HOUR] === 24 && config._a[MINUTE] === 0 && config._a[SECOND] === 0 && config._a[MILLISECOND] === 0) {\n    config._nextDay = true;\n    config._a[HOUR] = 0;\n  }\n  // eslint-disable-next-line prefer-spread\n  config._d = (config._useUTC ? createUTCDate : createDate).apply(null, input);\n  const expectedWeekday = config._useUTC ? config._d.getUTCDay() : config._d.getDay();\n  // Apply timezone offset from input. The actual utcOffset can be changed\n  // with parseZone.\n  if (config._tzm != null) {\n    config._d.setUTCMinutes(config._d.getUTCMinutes() - config._tzm);\n  }\n  if (config._nextDay) {\n    config._a[HOUR] = 24;\n  }\n  // check for mismatching day of week\n  if (config._w && typeof config._w[\"d\"] !== 'undefined' && config._w[\"d\"] !== expectedWeekday) {\n    getParsingFlags(config).weekdayMismatch = true;\n  }\n  return config;\n}\nfunction dayOfYearFromWeekInfo(config) {\n  let weekYear, week, weekday, dow, doy, temp, weekdayOverflow;\n  const w = config._w;\n  if (w[\"GG\"] != null || w[\"W\"] != null || w[\"E\"] != null) {\n    dow = 1;\n    doy = 4;\n    // TODO: We need to take the current isoWeekYear, but that depends on\n    // how we interpret now (local, utc, fixed offset). So create\n    // a now version of current config (take local/utc/offset flags, and\n    // create now).\n    weekYear = defaults(w[\"GG\"], config._a[YEAR], weekOfYear(new Date(), 1, 4).year);\n    week = defaults(w[\"W\"], 1);\n    weekday = defaults(w[\"E\"], 1);\n    if (weekday < 1 || weekday > 7) {\n      weekdayOverflow = true;\n    }\n  } else {\n    dow = config._locale._week.dow;\n    doy = config._locale._week.doy;\n    const curWeek = weekOfYear(new Date(), dow, doy);\n    weekYear = defaults(w[\"gg\"], config._a[YEAR], curWeek.year);\n    // Default to current week.\n    week = defaults(w[\"w\"], curWeek.week);\n    if (w[\"d\"] != null) {\n      // weekday -- low day numbers are considered next week\n      weekday = w[\"d\"];\n      if (weekday < 0 || weekday > 6) {\n        weekdayOverflow = true;\n      }\n    } else if (w[\"e\"] != null) {\n      // local weekday -- counting starts from beginning of week\n      weekday = w[\"e\"] + dow;\n      if (w[\"e\"] < 0 || w[\"e\"] > 6) {\n        weekdayOverflow = true;\n      }\n    } else {\n      // default to beginning of week\n      weekday = dow;\n    }\n  }\n  if (week < 1 || week > weeksInYear(weekYear, dow, doy)) {\n    getParsingFlags(config)._overflowWeeks = true;\n  } else if (weekdayOverflow != null) {\n    getParsingFlags(config)._overflowWeekday = true;\n  } else {\n    temp = dayOfYearFromWeeks(weekYear, week, weekday, dow, doy);\n    config._a[YEAR] = temp.year;\n    config._dayOfYear = temp.dayOfYear;\n  }\n  return config;\n}\nfunction checkOverflow(config) {\n  let overflow;\n  const a = config._a;\n  if (a && getParsingFlags(config).overflow === -2) {\n    // todo: fix this sh*t\n    overflow = a[MONTH] < 0 || a[MONTH] > 11 ? MONTH : a[DATE] < 1 || a[DATE] > daysInMonth(a[YEAR], a[MONTH]) ? DATE : a[HOUR] < 0 || a[HOUR] > 24 || a[HOUR] === 24 && (a[MINUTE] !== 0 || a[SECOND] !== 0 || a[MILLISECOND] !== 0) ? HOUR : a[MINUTE] < 0 || a[MINUTE] > 59 ? MINUTE : a[SECOND] < 0 || a[SECOND] > 59 ? SECOND : a[MILLISECOND] < 0 || a[MILLISECOND] > 999 ? MILLISECOND : -1;\n    if (getParsingFlags(config)._overflowDayOfYear && (overflow < YEAR || overflow > DATE)) {\n      overflow = DATE;\n    }\n    if (getParsingFlags(config)._overflowWeeks && overflow === -1) {\n      overflow = WEEK;\n    }\n    if (getParsingFlags(config)._overflowWeekday && overflow === -1) {\n      overflow = WEEKDAY;\n    }\n    getParsingFlags(config).overflow = overflow;\n  }\n  return config;\n}\n\n// constant that refers to the ISO standard\n// hooks.ISO_8601 = function () {};\nconst ISO_8601 = 'ISO_8601';\n// constant that refers to the RFC 2822 form\n// hooks.RFC_2822 = function () {};\nconst RFC_2822 = 'RFC_2822';\n// date from string and format string\nfunction configFromStringAndFormat(config) {\n  // TODO: Move this to another part of the creation flow to prevent circular deps\n  if (config._f === ISO_8601) {\n    return configFromISO(config);\n  }\n  if (config._f === RFC_2822) {\n    return configFromRFC2822(config);\n  }\n  config._a = [];\n  getParsingFlags(config).empty = true;\n  if (isArray(config._f) || !config._i && config._i !== 0) {\n    return config;\n  }\n  // This array is used to make a Date, either with `new Date` or `Date.UTC`\n  let input = config._i.toString();\n  let totalParsedInputLength = 0;\n  const inputLength = input.length;\n  const tokens = expandFormat(config._f, config._locale).match(formattingTokens) || [];\n  let i;\n  let token;\n  let parsedInput;\n  let skipped;\n  for (i = 0; i < tokens.length; i++) {\n    token = tokens[i];\n    parsedInput = (input.match(getParseRegexForToken(token, config._locale)) || [])[0];\n    if (parsedInput) {\n      skipped = input.substr(0, input.indexOf(parsedInput));\n      if (skipped.length > 0) {\n        getParsingFlags(config).unusedInput.push(skipped);\n      }\n      input = input.slice(input.indexOf(parsedInput) + parsedInput.length);\n      totalParsedInputLength += parsedInput.length;\n    }\n    // don't parse if it's not a known token\n    if (formatTokenFunctions[token]) {\n      if (parsedInput) {\n        getParsingFlags(config).empty = false;\n      } else {\n        getParsingFlags(config).unusedTokens.push(token);\n      }\n      addTimeToArrayFromToken(token, parsedInput, config);\n    } else if (config._strict && !parsedInput) {\n      getParsingFlags(config).unusedTokens.push(token);\n    }\n  }\n  // add remaining unparsed input length to the string\n  getParsingFlags(config).charsLeftOver = inputLength - totalParsedInputLength;\n  if (input.length > 0) {\n    getParsingFlags(config).unusedInput.push(input);\n  }\n  // clear _12h flag if hour is <= 12\n  if (config._a[HOUR] <= 12 && getParsingFlags(config).bigHour === true && config._a[HOUR] > 0) {\n    getParsingFlags(config).bigHour = void 0;\n  }\n  getParsingFlags(config).parsedDateParts = config._a.slice(0);\n  getParsingFlags(config).meridiem = config._meridiem;\n  // handle meridiem\n  config._a[HOUR] = meridiemFixWrap(config._locale, config._a[HOUR], config._meridiem);\n  configFromArray(config);\n  return checkOverflow(config);\n}\nfunction meridiemFixWrap(locale, _hour, meridiem) {\n  let hour = _hour;\n  if (meridiem == null) {\n    // nothing to do\n    return hour;\n  }\n  if (locale.meridiemHour != null) {\n    return locale.meridiemHour(hour, meridiem);\n  }\n  if (locale.isPM == null) {\n    // this is not supposed to happen\n    return hour;\n  }\n  // Fallback\n  const isPm = locale.isPM(meridiem);\n  if (isPm && hour < 12) {\n    hour += 12;\n  }\n  if (!isPm && hour === 12) {\n    hour = 0;\n  }\n  return hour;\n}\n\n// date from string and array of format strings\nfunction configFromStringAndArray(config) {\n  let tempConfig;\n  let bestMoment;\n  let scoreToBeat;\n  let currentScore;\n  if (!config._f || config._f.length === 0) {\n    getParsingFlags(config).invalidFormat = true;\n    return createInvalid(config);\n  }\n  let i;\n  for (i = 0; i < config._f.length; i++) {\n    currentScore = 0;\n    tempConfig = Object.assign({}, config);\n    if (config._useUTC != null) {\n      tempConfig._useUTC = config._useUTC;\n    }\n    tempConfig._f = config._f[i];\n    configFromStringAndFormat(tempConfig);\n    if (!isValid(tempConfig)) {\n      continue;\n    }\n    // if there is any input that was not parsed add a penalty for that format\n    currentScore += getParsingFlags(tempConfig).charsLeftOver;\n    // or tokens\n    currentScore += getParsingFlags(tempConfig).unusedTokens.length * 10;\n    getParsingFlags(tempConfig).score = currentScore;\n    if (scoreToBeat == null || currentScore < scoreToBeat) {\n      scoreToBeat = currentScore;\n      bestMoment = tempConfig;\n    }\n  }\n  return Object.assign(config, bestMoment || tempConfig);\n}\nfunction configFromObject(config) {\n  if (config._d) {\n    return config;\n  }\n  const input = config._i;\n  if (isObject(input)) {\n    const i = normalizeObjectUnits(input);\n    config._a = [i.year, i.month, i.day, i.hours, i.minutes, i.seconds, i.milliseconds]\n    // todo: obsolete -> remove it\n    .map(obj => isString(obj) ? parseInt(obj, 10) : obj);\n  }\n  return configFromArray(config);\n}\nfunction createFromConfig(config) {\n  const res = checkOverflow(prepareConfig(config));\n  // todo: remove, in moment.js it's never called cuz of moment constructor\n  res._d = new Date(res._d != null ? res._d.getTime() : NaN);\n  if (!isValid(Object.assign({}, res, {\n    _isValid: null\n  }))) {\n    res._d = new Date(NaN);\n  }\n  // todo: update offset\n  /*if (res._nextDay) {\n    // Adding is smart enough around DST\n    res._d = add(res._d, 1, 'day');\n    res._nextDay = undefined;\n  }*/\n  return res;\n}\nfunction prepareConfig(config) {\n  let input = config._i;\n  const format = config._f;\n  config._locale = config._locale || getLocale(config._l);\n  if (input === null || format === undefined && input === '') {\n    return createInvalid(config, {\n      nullInput: true\n    });\n  }\n  if (isString(input)) {\n    config._i = input = config._locale.preparse(input, format);\n  }\n  if (isDate(input)) {\n    config._d = cloneDate(input);\n    return config;\n  }\n  // todo: add check for recursion\n  if (isArray(format)) {\n    configFromStringAndArray(config);\n  } else if (format) {\n    configFromStringAndFormat(config);\n  } else {\n    configFromInput(config);\n  }\n  if (!isValid(config)) {\n    config._d = null;\n  }\n  return config;\n}\nfunction configFromInput(config) {\n  const input = config._i;\n  if (isUndefined(input)) {\n    config._d = new Date();\n  } else if (isDate(input)) {\n    config._d = cloneDate(input);\n  } else if (isString(input)) {\n    configFromString(config);\n  } else if (isArray(input) && input.length) {\n    const _arr = input.slice(0);\n    config._a = _arr.map(obj => isString(obj) ? parseInt(obj, 10) : obj);\n    configFromArray(config);\n  } else if (isObject(input)) {\n    configFromObject(config);\n  } else if (isNumber(input)) {\n    // from milliseconds\n    config._d = new Date(input);\n  } else {\n    //   hooks.createFromInputFallback(config);\n    return createInvalid(config);\n  }\n  return config;\n}\nfunction createLocalOrUTC(input, format, localeKey, strict, isUTC) {\n  const config = {};\n  let _input = input;\n  // params switch -> skip; testing it well\n  // if (localeKey === true || localeKey === false) {\n  //     strict = localeKey;\n  //     localeKey = undefined;\n  // }\n  // todo: fail fast and return not valid date\n  if (isObject(_input) && isObjectEmpty(_input) || isArray(_input) && _input.length === 0) {\n    _input = undefined;\n  }\n  // object construction must be done this way.\n  // https://github.com/moment/moment/issues/1423\n  // config._isAMomentObject = true;\n  config._useUTC = config._isUTC = isUTC;\n  config._l = localeKey;\n  config._i = _input;\n  config._f = format;\n  config._strict = strict;\n  return createFromConfig(config);\n}\nfunction parseDate(input, format, localeKey, strict, isUTC) {\n  if (isDate(input)) {\n    return input;\n  }\n  const config = createLocalOrUTC(input, format, localeKey, strict, isUTC);\n  return config._d;\n}\nfunction utcAsLocal(date) {\n  if (!(date instanceof Date)) {\n    return null;\n  }\n  return new Date(date.getUTCFullYear(), date.getUTCMonth(), date.getUTCDate(), date.getUTCHours(), date.getUTCMinutes(), date.getUTCSeconds(), date.getUTCMilliseconds());\n}\nfunction absRound(num) {\n  return num < 0 ? Math.round(num * -1) * -1 : Math.round(num);\n}\nfunction isAfter(date1, date2, units = 'milliseconds') {\n  if (!date1 || !date2) {\n    return false;\n  }\n  if (units === 'milliseconds') {\n    return date1.valueOf() > date2.valueOf();\n  }\n  return date2.valueOf() < startOf(date1, units).valueOf();\n}\nfunction isBefore(date1, date2, units = 'milliseconds') {\n  if (!date1 || !date2) {\n    return false;\n  }\n  if (units === 'milliseconds') {\n    return date1.valueOf() < date2.valueOf();\n  }\n  return endOf(date1, units).valueOf() < date2.valueOf();\n}\nfunction isDisabledDay(date, daysDisabled) {\n  if (typeof daysDisabled === 'undefined' || !daysDisabled || !daysDisabled.length) {\n    return false;\n  }\n  return daysDisabled.some(day => day === date.getDay());\n}\nfunction isBetween(date, from, to, units, inclusivity = '()') {\n  const leftBound = inclusivity[0] === '(' ? isAfter(date, from, units) : !isBefore(date, from, units);\n  const rightBound = inclusivity[1] === ')' ? isBefore(date, to, units) : !isAfter(date, to, units);\n  return leftBound && rightBound;\n}\nfunction isSame(date1, date2, units = 'milliseconds') {\n  if (!date1 || !date2) {\n    return false;\n  }\n  if (units === 'milliseconds') {\n    return date1.valueOf() === date2.valueOf();\n  }\n  const inputMs = date2.valueOf();\n  return startOf(date1, units).valueOf() <= inputMs && inputMs <= endOf(date1, units).valueOf();\n}\nfunction isSameDay(date1, date2) {\n  return date1.getDay() == date2.getDay();\n}\nfunction isSameOrAfter(date1, date2, units) {\n  return isSame(date1, date2, units) || isAfter(date1, date2, units);\n}\nfunction isSameOrBefore(date1, date2, units) {\n  return isSame(date1, date2, units) || isBefore(date1, date2, units);\n}\n\n// ASP.NET json date format regex\nconst aspNetRegex = /^(\\-|\\+)?(?:(\\d*)[. ])?(\\d+)\\:(\\d+)(?:\\:(\\d+)(\\.\\d*)?)?$/;\n// from http://docs.closure-library.googlecode.com/git/closure_goog_date_date.js.source.html\n// somewhat more in line with 4.4.3.2 2004 spec, but allows decimal anywhere\n// and further modified to allow for strings containing both week and day\nconst isoRegex = /^(-|\\+)?P(?:([-+]?[0-9,.]*)Y)?(?:([-+]?[0-9,.]*)M)?(?:([-+]?[0-9,.]*)W)?(?:([-+]?[0-9,.]*)D)?(?:T(?:([-+]?[0-9,.]*)H)?(?:([-+]?[0-9,.]*)M)?(?:([-+]?[0-9,.]*)S)?)?$/;\nfunction createDuration(input, key, config = {}) {\n  const duration = convertDuration(input, key);\n  // matching against regexp is expensive, do it on demand\n  return new Duration(duration, config);\n}\nfunction convertDuration(input, key) {\n  // checks for null or undefined\n  if (input == null) {\n    return {};\n  }\n  if (isDuration(input)) {\n    return {\n      milliseconds: input._milliseconds,\n      day: input._days,\n      month: input._months\n    };\n  }\n  if (isNumber(input)) {\n    // duration = {};\n    return key ? {\n      [key]: input\n    } : {\n      milliseconds: input\n    };\n  }\n  if (isString(input)) {\n    let match = aspNetRegex.exec(input);\n    if (match) {\n      const sign = match[1] === '-' ? -1 : 1;\n      return {\n        year: 0,\n        day: toInt(match[DATE]) * sign,\n        hours: toInt(match[HOUR]) * sign,\n        minutes: toInt(match[MINUTE]) * sign,\n        seconds: toInt(match[SECOND]) * sign,\n        // the millisecond decimal point is included in the match\n        milliseconds: toInt(absRound(toInt(match[MILLISECOND]) * 1000)) * sign\n      };\n    }\n    match = isoRegex.exec(input);\n    if (match) {\n      const sign = match[1] === '-' ? -1 : match[1] === '+' ? 1 : 1;\n      return {\n        year: parseIso(match[2], sign),\n        month: parseIso(match[3], sign),\n        week: parseIso(match[4], sign),\n        day: parseIso(match[5], sign),\n        hours: parseIso(match[6], sign),\n        minutes: parseIso(match[7], sign),\n        seconds: parseIso(match[8], sign)\n      };\n    }\n  }\n  if (isObject(input) && ('from' in input || 'to' in input)) {\n    const diffRes = momentsDifference(parseDate(input.from), parseDate(input.to));\n    return {\n      milliseconds: diffRes.milliseconds,\n      month: diffRes.months\n    };\n  }\n  return input;\n}\n// createDuration.fn = Duration.prototype;\n// createDuration.invalid = invalid;\nfunction parseIso(inp, sign) {\n  // We'd normally use ~~inp for this, but unfortunately it also\n  // converts floats to ints.\n  // inp may be undefined, so careful calling replace on it.\n  const res = inp && parseFloat(inp.replace(',', '.'));\n  // apply sign while we're at it\n  return (isNaN(res) ? 0 : res) * sign;\n}\nfunction positiveMomentsDifference(base, other) {\n  const res = {\n    milliseconds: 0,\n    months: 0\n  };\n  res.months = getMonth(other) - getMonth(base) + (getFullYear(other) - getFullYear(base)) * 12;\n  const _basePlus = add(cloneDate(base), res.months, 'month');\n  if (isAfter(_basePlus, other)) {\n    --res.months;\n  }\n  res.milliseconds = +other - +add(cloneDate(base), res.months, 'month');\n  return res;\n}\nfunction momentsDifference(base, other) {\n  if (!(isDateValid(base) && isDateValid(other))) {\n    return {\n      milliseconds: 0,\n      months: 0\n    };\n  }\n  let res;\n  const _other = cloneWithOffset(other, base, {\n    _offset: base.getTimezoneOffset()\n  });\n  if (isBefore(base, _other)) {\n    res = positiveMomentsDifference(base, _other);\n  } else {\n    res = positiveMomentsDifference(_other, base);\n    res.milliseconds = -res.milliseconds;\n    res.months = -res.months;\n  }\n  return res;\n}\nfunction add(date, val, period, isUTC) {\n  const dur = createDuration(val, period);\n  return addSubtract(date, dur, 1, isUTC);\n}\nfunction subtract(date, val, period, isUTC) {\n  const dur = createDuration(val, period);\n  return addSubtract(date, dur, -1, isUTC);\n}\nfunction addSubtract(date, duration, isAdding, isUTC) {\n  const milliseconds = duration._milliseconds;\n  const days = absRound(duration._days);\n  const months = absRound(duration._months);\n  // todo: add timezones support\n  // const _updateOffset = updateOffset == null ? true : updateOffset;\n  if (months) {\n    setMonth(date, getMonth(date, isUTC) + months * isAdding, isUTC);\n  }\n  if (days) {\n    setDate(date, getDate(date, isUTC) + days * isAdding, isUTC);\n  }\n  if (milliseconds) {\n    setTime(date, getTime(date) + milliseconds * isAdding);\n  }\n  return cloneDate(date);\n  // todo: add timezones support\n  // if (_updateOffset) {\n  //   hooks.updateOffset(date, days || months);\n  // }\n}\nfunction initDayOfWeek() {\n  // FORMATTING\n  addFormatToken('d', null, 'do', function (date, opts) {\n    return getDay(date, opts.isUTC).toString(10);\n  });\n  addFormatToken('dd', null, null, function (date, opts) {\n    return opts.locale.weekdaysMin(date, opts.format, opts.isUTC);\n  });\n  addFormatToken('ddd', null, null, function (date, opts) {\n    return opts.locale.weekdaysShort(date, opts.format, opts.isUTC);\n  });\n  addFormatToken('dddd', null, null, function (date, opts) {\n    return opts.locale.weekdays(date, opts.format, opts.isUTC);\n  });\n  addFormatToken('e', null, null, function (date, opts) {\n    return getLocaleDayOfWeek(date, opts.locale, opts.isUTC).toString(10);\n    // return getDay(date, opts.isUTC).toString(10);\n  });\n  addFormatToken('E', null, null, function (date, opts) {\n    return getISODayOfWeek(date, opts.isUTC).toString(10);\n  });\n  // ALIASES\n  addUnitAlias('day', 'd');\n  addUnitAlias('weekday', 'e');\n  addUnitAlias('isoWeekday', 'E');\n  // PRIORITY\n  addUnitPriority('day', 11);\n  addUnitPriority('weekday', 11);\n  addUnitPriority('isoWeekday', 11);\n  // PARSING\n  addRegexToken('d', match1to2);\n  addRegexToken('e', match1to2);\n  addRegexToken('E', match1to2);\n  addRegexToken('dd', function (isStrict, locale) {\n    return locale.weekdaysMinRegex(isStrict);\n  });\n  addRegexToken('ddd', function (isStrict, locale) {\n    return locale.weekdaysShortRegex(isStrict);\n  });\n  addRegexToken('dddd', function (isStrict, locale) {\n    return locale.weekdaysRegex(isStrict);\n  });\n  addWeekParseToken(['dd', 'ddd', 'dddd'], function (input, week, config, token) {\n    const weekday = config._locale.weekdaysParse(input, token, config._strict);\n    // if we didn't get a weekday name, mark the date as invalid\n    if (weekday != null) {\n      week[\"d\"] = weekday;\n    } else {\n      getParsingFlags(config).invalidWeekday = !!input;\n    }\n    return config;\n  });\n  addWeekParseToken(['d', 'e', 'E'], function (input, week, config, token) {\n    week[token] = toInt(input);\n    return config;\n  });\n}\n// HELPERS\nfunction parseWeekday(input, locale) {\n  if (!isString(input)) {\n    return input;\n  }\n  const _num = parseInt(input, 10);\n  if (!isNaN(_num)) {\n    return _num;\n  }\n  const _weekDay = locale.weekdaysParse(input);\n  if (isNumber(_weekDay)) {\n    return _weekDay;\n  }\n  return null;\n}\nfunction parseIsoWeekday(input, locale = getLocale()) {\n  if (isString(input)) {\n    return locale.weekdaysParse(input) % 7 || 7;\n  }\n  return isNumber(input) && isNaN(input) ? null : input;\n}\n// MOMENTS\nfunction getSetDayOfWeek(date, input, opts) {\n  if (!input) {\n    return getDayOfWeek(date, opts.isUTC);\n  }\n  return setDayOfWeek(date, input, opts.locale, opts.isUTC);\n}\nfunction setDayOfWeek(date, input, locale = getLocale(), isUTC) {\n  const day = getDay(date, isUTC);\n  const _input = parseWeekday(input, locale);\n  return add(date, _input - day, 'day');\n}\nfunction getDayOfWeek(date, isUTC) {\n  return getDay(date, isUTC);\n}\n/********************************************/\n// todo: utc\n// getSetLocaleDayOfWeek\nfunction getLocaleDayOfWeek(date, locale = getLocale(), isUTC) {\n  return (getDay(date, isUTC) + 7 - locale.firstDayOfWeek()) % 7;\n}\nfunction setLocaleDayOfWeek(date, input, opts = {}) {\n  const weekday = getLocaleDayOfWeek(date, opts.locale, opts.isUTC);\n  return add(date, input - weekday, 'day');\n}\n// getSetISODayOfWeek\nfunction getISODayOfWeek(date, isUTC) {\n  return getDay(date, isUTC) || 7;\n}\nfunction setISODayOfWeek(date, input, opts = {}) {\n  // behaves the same as moment#day except\n  // as a getter, returns 7 instead of 0 (1-7 range instead of 0-6)\n  // as a setter, sunday should belong to the previous week.\n  const weekday = parseIsoWeekday(input, opts.locale);\n  return setDayOfWeek(date, getDayOfWeek(date) % 7 ? weekday : weekday - 7);\n}\n\n//! moment.js locale configuration\n//! locale : Arabic [ar]\n//! author : Abdel Said: https://github.com/abdelsaid\n//! author : Ahmed Elkhatib\n//! author : forabi https://github.com/forabi\nconst symbolMap$1 = {\n  1: '١',\n  2: '٢',\n  3: '٣',\n  4: '٤',\n  5: '٥',\n  6: '٦',\n  7: '٧',\n  8: '٨',\n  9: '٩',\n  0: '٠'\n};\nconst numberMap$1 = {\n  '١': '1',\n  '٢': '2',\n  '٣': '3',\n  '٤': '4',\n  '٥': '5',\n  '٦': '6',\n  '٧': '7',\n  '٨': '8',\n  '٩': '9',\n  '٠': '0'\n};\nconst pluralForm = function (num) {\n  return num === 0 ? 0 : num === 1 ? 1 : num === 2 ? 2 : num % 100 >= 3 && num % 100 <= 10 ? 3 : num % 100 >= 11 ? 4 : 5;\n};\nconst plurals = {\n  s: ['أقل من ثانية', 'ثانية واحدة', ['ثانيتان', 'ثانيتين'], '%d ثوان', '%d ثانية', '%d ثانية'],\n  m: ['أقل من دقيقة', 'دقيقة واحدة', ['دقيقتان', 'دقيقتين'], '%d دقائق', '%d دقيقة', '%d دقيقة'],\n  h: ['أقل من ساعة', 'ساعة واحدة', ['ساعتان', 'ساعتين'], '%d ساعات', '%d ساعة', '%d ساعة'],\n  d: ['أقل من يوم', 'يوم واحد', ['يومان', 'يومين'], '%d أيام', '%d يومًا', '%d يوم'],\n  M: ['أقل من شهر', 'شهر واحد', ['شهران', 'شهرين'], '%d أشهر', '%d شهرا', '%d شهر'],\n  y: ['أقل من عام', 'عام واحد', ['عامان', 'عامين'], '%d أعوام', '%d عامًا', '%d عام']\n};\nconst pluralize = function (u) {\n  return function (num, withoutSuffix) {\n    const f = pluralForm(num);\n    let str = plurals[u][pluralForm(num)];\n    if (f === 2) {\n      str = str[withoutSuffix ? 0 : 1];\n    }\n    return str.replace(/%d/i, num.toString());\n  };\n};\nconst months$2 = ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'];\nconst arLocale = {\n  abbr: 'ar',\n  months: months$2,\n  monthsShort: months$2,\n  weekdays: 'الأحد_الإثنين_الثلاثاء_الأربعاء_الخميس_الجمعة_السبت'.split('_'),\n  weekdaysShort: 'أحد_إثنين_ثلاثاء_أربعاء_خميس_جمعة_سبت'.split('_'),\n  weekdaysMin: 'ح_ن_ث_ر_خ_ج_س'.split('_'),\n  weekdaysParseExact: true,\n  longDateFormat: {\n    LT: 'HH:mm',\n    LTS: 'HH:mm:ss',\n    L: 'D/\\u200FM/\\u200FYYYY',\n    LL: 'D MMMM YYYY',\n    LLL: 'D MMMM YYYY HH:mm',\n    LLLL: 'dddd D MMMM YYYY HH:mm'\n  },\n  meridiemParse: /ص|م/,\n  isPM(input) {\n    return 'م' === input;\n  },\n  meridiem(hour, minute, isLower) {\n    if (hour < 12) {\n      return 'ص';\n    } else {\n      return 'م';\n    }\n  },\n  calendar: {\n    sameDay: '[اليوم عند الساعة] LT',\n    nextDay: '[غدًا عند الساعة] LT',\n    nextWeek: 'dddd [عند الساعة] LT',\n    lastDay: '[أمس عند الساعة] LT',\n    lastWeek: 'dddd [عند الساعة] LT',\n    sameElse: 'L'\n  },\n  relativeTime: {\n    future: 'بعد %s',\n    past: 'منذ %s',\n    s: pluralize('s'),\n    ss: pluralize('s'),\n    m: pluralize('m'),\n    mm: pluralize('m'),\n    h: pluralize('h'),\n    hh: pluralize('h'),\n    d: pluralize('d'),\n    dd: pluralize('d'),\n    M: pluralize('M'),\n    MM: pluralize('M'),\n    y: pluralize('y'),\n    yy: pluralize('y')\n  },\n  preparse(str) {\n    return str.replace(/[١٢٣٤٥٦٧٨٩٠]/g, function (match) {\n      return numberMap$1[match];\n    }).replace(/،/g, ',');\n  },\n  postformat(str) {\n    return str.replace(/\\d/g, function (match) {\n      return symbolMap$1[match];\n    }).replace(/,/g, '،');\n  },\n  week: {\n    dow: 6,\n    doy: 12 // The week that contains Jan 1st is the first week of the year.\n  }\n};\n\n//! moment.js locale configuration\n//! locale : Bulgarian [bg]\n//! author : Iskren Ivov Chernev : https://github.com/ichernev\n//! author : Kunal Marwaha : https://github.com/marwahaha\n//! author : Matt Grande : https://github.com/mattgrande\n//! author : Isaac Cambron : https://github.com/icambron\n//! author : Venelin Manchev : https://github.com/vmanchev\nconst bgLocale = {\n  abbr: 'bg',\n  months: 'януари_февруари_март_април_май_юни_юли_август_септември_октомври_ноември_декември'.split('_'),\n  monthsShort: 'янр_фев_мар_апр_май_юни_юли_авг_сеп_окт_ное_дек'.split('_'),\n  weekdays: 'неделя_понеделник_вторник_сряда_четвъртък_петък_събота'.split('_'),\n  weekdaysShort: 'нед_пон_вто_сря_чет_пет_съб'.split('_'),\n  weekdaysMin: 'нд_пн_вт_ср_чт_пт_сб'.split('_'),\n  longDateFormat: {\n    LT: 'H:mm',\n    LTS: 'H:mm:ss',\n    L: 'D.MM.YYYY',\n    LL: 'D MMMM YYYY',\n    LLL: 'D MMMM YYYY H:mm',\n    LLLL: 'dddd, D MMMM YYYY H:mm'\n  },\n  calendar: {\n    sameDay: '[Днес в] LT',\n    nextDay: '[Утре в] LT',\n    nextWeek: 'dddd [в] LT',\n    lastDay: '[Вчера в] LT',\n    lastWeek: function (d) {\n      switch (d) {\n        case 0:\n        case 3:\n        case 6:\n          return '[В изминалата] dddd [в] LT';\n        case 1:\n        case 2:\n        case 4:\n        case 5:\n          return '[В изминалия] dddd [в] LT';\n      }\n    },\n    sameElse: 'L'\n  },\n  relativeTime: {\n    future: 'след %s',\n    past: 'преди %s',\n    s: 'няколко секунди',\n    ss: '%d секунди',\n    m: 'минута',\n    mm: '%d минути',\n    h: 'час',\n    hh: '%d часа',\n    d: 'ден',\n    dd: '%d дни',\n    M: 'месец',\n    MM: '%d месеца',\n    y: 'година',\n    yy: '%d години'\n  },\n  dayOfMonthOrdinalParse: /\\d{1,2}-(ев|ен|ти|ви|ри|ми)/,\n  ordinal: function (_num) {\n    const number = Number(_num);\n    let lastDigit = number % 10,\n      last2Digits = number % 100;\n    if (number === 0) {\n      return number + '-ев';\n    } else if (last2Digits === 0) {\n      return number + '-ен';\n    } else if (last2Digits > 10 && last2Digits < 20) {\n      return number + '-ти';\n    } else if (lastDigit === 1) {\n      return number + '-ви';\n    } else if (lastDigit === 2) {\n      return number + '-ри';\n    } else if (lastDigit === 7 || lastDigit === 8) {\n      return number + '-ми';\n    } else {\n      return number + '-ти';\n    }\n  },\n  week: {\n    dow: 1,\n    doy: 7 // The week that contains Jan 1st is the first week of the year.\n  }\n};\n\n//! moment.js locale configuration\n//! locale : Catalan [ca]\n//! author : Xavier Arbat : https://github.com/XavisaurusRex\nlet monthsShortDot$5 = 'gen._feb._mar._abr._mai._jun._jul._ago._set._oct._nov._des.'.split('_'),\n  monthsShort$7 = 'ene_feb_mar_abr_mai_jun_jul_ago_set_oct_nov_des'.split('_');\nlet monthsParse$6 = [/^gen/i, /^feb/i, /^mar/i, /^abr/i, /^mai/i, /^jun/i, /^jul/i, /^ago/i, /^set/i, /^oct/i, /^nov/i, /^des/i];\nlet monthsRegex$5 = /^(gener|febrer|març|abril|maig|juny|juliol|agost|setembre|octubre|novembre|desembre|gen\\.?|feb\\.?|mar\\.?|abr\\.?|mai\\.?|jun\\.?|jul\\.?|ago\\.?|set\\.?|oct\\.?|nov\\.?|des\\.?)/i;\nconst caLocale = {\n  abbr: 'ca',\n  months: 'gener_febrer_març_abril_maig_juny_juliol_agost_setembre_octubre_novembre_desembre'.split('_'),\n  monthsShort(date, format, isUTC) {\n    if (!date) {\n      return monthsShortDot$5;\n    }\n    if (/-MMM-/.test(format)) {\n      return monthsShort$7[getMonth(date, isUTC)];\n    }\n    return monthsShortDot$5[getMonth(date, isUTC)];\n  },\n  monthsRegex: monthsRegex$5,\n  monthsShortRegex: monthsRegex$5,\n  monthsStrictRegex: /^(gener|febrer|març|abril|maig|juny|juliol|agost|setembre|octubre|novembre|desembre)/i,\n  monthsShortStrictRegex: /^(gen\\.?|feb\\.?|mar\\.?|abr\\.?|mai\\.?|jun\\.?|jul\\.?|ago\\.?|set\\.?|oct\\.?|nov\\.?|des\\.?)/i,\n  monthsParse: monthsParse$6,\n  longMonthsParse: monthsParse$6,\n  shortMonthsParse: monthsParse$6,\n  weekdays: 'diumenge_dilluns_dimarts_dimecres_dijous_divendres_dissabte'.split('_'),\n  weekdaysShort: 'diu._dil._dim._dix._dij._div._dis.'.split('_'),\n  weekdaysMin: 'dg_dl_dt_dc_dj_dv_ds'.split('_'),\n  weekdaysParseExact: true,\n  longDateFormat: {\n    LT: 'H:mm',\n    LTS: 'H:mm:ss',\n    L: 'DD/MM/YYYY',\n    LL: 'D [de] MMMM [de] YYYY',\n    LLL: 'D [de] MMMM [de] YYYY H:mm',\n    LLLL: 'dddd, D [de] MMMM [de] YYYY H:mm'\n  },\n  calendar: {\n    sameDay(date) {\n      return '[avui a ' + ('la' + (getHours(date) !== 1) ? 'les' : '') + '] LT';\n    },\n    nextDay(date) {\n      return '[dema a ' + ('la' + (getHours(date) !== 1) ? 'les' : '') + '] LT';\n    },\n    nextWeek(date) {\n      return 'dddd [a ' + ('la' + (getHours(date) !== 1) ? 'les' : '') + '] LT';\n    },\n    lastDay(date) {\n      return '[ahir a ' + ('la' + (getHours(date) !== 1) ? 'les' : '') + '] LT';\n    },\n    lastWeek(date) {\n      return '[el] dddd [' + ('passada la ' + (getHours(date) !== 1) ? 'passades les' : '') + '] LT';\n    },\n    sameElse: 'L'\n  },\n  relativeTime: {\n    future: 'en %s',\n    past: 'fa %s',\n    s: 'uns segons',\n    ss: '%d segons',\n    m: 'un minut',\n    mm: '%d minuts',\n    h: 'una hora',\n    hh: '%d hores',\n    d: 'un dia',\n    dd: '%d dies',\n    M: 'un mes',\n    MM: '%d mesos',\n    y: 'un any',\n    yy: '%d anys'\n  },\n  dayOfMonthOrdinalParse: /\\d{1,2}(er|on|er|rt|é)/,\n  ordinal(_num) {\n    const num = Number(_num);\n    const output = num > 4 ? 'é' : num === 1 || num === 3 ? 'r' : num === 2 ? 'n' : num === 4 ? 't' : 'é';\n    return num + output;\n  },\n  week: {\n    dow: 1,\n    doy: 4 // The week that contains Jan 4th is the first week of the year.\n  }\n};\n\n//! moment.js locale configuration\n//! locale : Czech [cs]\n//! author : petrbela : https://github.com/petrbela\nconst months$1 = 'leden_únor_březen_duben_květen_červen_červenec_srpen_září_říjen_listopad_prosinec'.split('_');\nconst monthsShort$6 = 'led_úno_bře_dub_kvě_čvn_čvc_srp_zář_říj_lis_pro'.split('_');\nfunction plural$4(num) {\n  return num > 1 && num < 5 && ~~(num / 10) !== 1;\n}\nfunction translate$6(num, withoutSuffix, key, isFuture) {\n  const result = num + ' ';\n  switch (key) {\n    case 's':\n      // a few seconds / in a few seconds / a few seconds ago\n      return withoutSuffix || isFuture ? 'pár sekund' : 'pár sekundami';\n    case 'ss':\n      // 9 seconds / in 9 seconds / 9 seconds ago\n      if (withoutSuffix || isFuture) {\n        return result + (plural$4(num) ? 'sekundy' : 'sekund');\n      } else {\n        return result + 'sekundami';\n      }\n    // break;\n    case 'm':\n      // a minute / in a minute / a minute ago\n      return withoutSuffix ? 'minuta' : isFuture ? 'minutu' : 'minutou';\n    case 'mm':\n      // 9 minutes / in 9 minutes / 9 minutes ago\n      if (withoutSuffix || isFuture) {\n        return result + (plural$4(num) ? 'minuty' : 'minut');\n      } else {\n        return result + 'minutami';\n      }\n    // break;\n    case 'h':\n      // an hour / in an hour / an hour ago\n      return withoutSuffix ? 'hodina' : isFuture ? 'hodinu' : 'hodinou';\n    case 'hh':\n      // 9 hours / in 9 hours / 9 hours ago\n      if (withoutSuffix || isFuture) {\n        return result + (plural$4(num) ? 'hodiny' : 'hodin');\n      } else {\n        return result + 'hodinami';\n      }\n    // break;\n    case 'd':\n      // a day / in a day / a day ago\n      return withoutSuffix || isFuture ? 'den' : 'dnem';\n    case 'dd':\n      // 9 days / in 9 days / 9 days ago\n      if (withoutSuffix || isFuture) {\n        return result + (plural$4(num) ? 'dny' : 'dní');\n      } else {\n        return result + 'dny';\n      }\n    // break;\n    case 'M':\n      // a month / in a month / a month ago\n      return withoutSuffix || isFuture ? 'měsíc' : 'měsícem';\n    case 'MM':\n      // 9 months / in 9 months / 9 months ago\n      if (withoutSuffix || isFuture) {\n        return result + (plural$4(num) ? 'měsíce' : 'měsíců');\n      } else {\n        return result + 'měsíci';\n      }\n    // break;\n    case 'y':\n      // a year / in a year / a year ago\n      return withoutSuffix || isFuture ? 'rok' : 'rokem';\n    case 'yy':\n      // 9 years / in 9 years / 9 years ago\n      if (withoutSuffix || isFuture) {\n        return result + (plural$4(num) ? 'roky' : 'let');\n      } else {\n        return result + 'lety';\n      }\n    // break;\n  }\n}\nconst csLocale = {\n  abbr: 'cs',\n  months: months$1,\n  monthsShort: monthsShort$6,\n  monthsParse: function (months, monthsShort) {\n    let i,\n      _monthsParse = [];\n    for (i = 0; i < 12; i++) {\n      // use custom parser to solve problem with July (červenec)\n      _monthsParse[i] = new RegExp('^' + months[i] + '$|^' + monthsShort[i] + '$', 'i');\n    }\n    return _monthsParse;\n  }(months$1, monthsShort$6),\n  shortMonthsParse: function (monthsShort) {\n    let i,\n      _shortMonthsParse = [];\n    for (i = 0; i < 12; i++) {\n      _shortMonthsParse[i] = new RegExp('^' + monthsShort[i] + '$', 'i');\n    }\n    return _shortMonthsParse;\n  }(monthsShort$6),\n  longMonthsParse: function (months) {\n    let i,\n      _longMonthsParse = [];\n    for (i = 0; i < 12; i++) {\n      _longMonthsParse[i] = new RegExp('^' + months[i] + '$', 'i');\n    }\n    return _longMonthsParse;\n  }(months$1),\n  weekdays: 'neděle_pondělí_úterý_středa_čtvrtek_pátek_sobota'.split('_'),\n  weekdaysShort: 'ne_po_út_st_čt_pá_so'.split('_'),\n  weekdaysMin: 'ne_po_út_st_čt_pá_so'.split('_'),\n  longDateFormat: {\n    LT: 'H:mm',\n    LTS: 'H:mm:ss',\n    L: 'DD.MM.YYYY',\n    LL: 'D. MMMM YYYY',\n    LLL: 'D. MMMM YYYY H:mm',\n    LLLL: 'dddd D. MMMM YYYY H:mm',\n    l: 'D. M. YYYY'\n  },\n  calendar: {\n    sameDay: '[dnes v] LT',\n    nextDay: '[zítra v] LT',\n    nextWeek(date) {\n      switch (getDayOfWeek(date)) {\n        case 0:\n          return '[v neděli v] LT';\n        case 1:\n        case 2:\n          return '[v] dddd [v] LT';\n        case 3:\n          return '[ve středu v] LT';\n        case 4:\n          return '[ve čtvrtek v] LT';\n        case 5:\n          return '[v pátek v] LT';\n        case 6:\n          return '[v sobotu v] LT';\n      }\n    },\n    lastDay: '[včera v] LT',\n    lastWeek(date) {\n      switch (getDayOfWeek(date)) {\n        case 0:\n          return '[minulou neděli v] LT';\n        case 1:\n        case 2:\n          return '[minulé] dddd [v] LT';\n        case 3:\n          return '[minulou středu v] LT';\n        case 4:\n        case 5:\n          return '[minulý] dddd [v] LT';\n        case 6:\n          return '[minulou sobotu v] LT';\n      }\n    },\n    sameElse: 'L'\n  },\n  relativeTime: {\n    future: 'za %s',\n    past: 'před %s',\n    s: translate$6,\n    ss: translate$6,\n    m: translate$6,\n    mm: translate$6,\n    h: translate$6,\n    hh: translate$6,\n    d: translate$6,\n    dd: translate$6,\n    M: translate$6,\n    MM: translate$6,\n    y: translate$6,\n    yy: translate$6\n  },\n  dayOfMonthOrdinalParse: /\\d{1,2}\\./,\n  ordinal: '%d.',\n  week: {\n    dow: 1,\n    doy: 4 // The week that contains Jan 4th is the first week of the year.\n  }\n};\n\n//! moment.js locale configuration\n//! locale : Danish (Denmark) [da]\n//! author : Per Hansen : https://github.com/perhp\nconst daLocale = {\n  abbr: 'da',\n  months: 'Januar_Februar_Marts_April_Maj_Juni_Juli_August_September_Oktober_November_December'.split('_'),\n  monthsShort: 'Jan_Feb_Mar_Apr_Maj_Jun_Jul_Aug_Sep_Okt_Nov_Dec'.split('_'),\n  weekdays: 'Søndag_Mandag_Tirsdag_Onsdag_Torsdag_Fredag_Lørdag'.split('_'),\n  weekdaysShort: 'Søn_Man_Tir_Ons_Tor_Fre_Lør'.split('_'),\n  weekdaysMin: 'Sø_Ma_Ti_On_To_Fr_Lø'.split('_'),\n  longDateFormat: {\n    LT: 'HH:mm',\n    LTS: 'HH:mm:ss',\n    L: 'DD/MM/YYYY',\n    LL: 'D. MMMM YYYY',\n    LLL: 'D. MMMM YYYY HH:mm',\n    LLLL: 'dddd [d.] D. MMMM YYYY [kl.] HH:mm'\n  },\n  calendar: {\n    sameDay: '[i dag kl.] LT',\n    nextDay: '[i morgen kl.] LT',\n    nextWeek: 'på dddd [kl.] LT',\n    lastDay: '[i går kl.] LT',\n    lastWeek: '[i] dddd[s kl.] LT',\n    sameElse: 'L'\n  },\n  relativeTime: {\n    future: 'om %s',\n    past: '%s siden',\n    s: 'få sekunder',\n    m: 'et minut',\n    mm: '%d minutter',\n    h: 'en time',\n    hh: '%d timer',\n    d: 'en dag',\n    dd: '%d dage',\n    M: 'en måned',\n    MM: '%d måneder',\n    y: 'et år',\n    yy: '%d år'\n  },\n  dayOfMonthOrdinalParse: /\\d{1,2}\\./,\n  ordinal: '%d.',\n  week: {\n    dow: 1,\n    doy: 4 // The week that contains Jan 4th is the first week of the year.\n  }\n};\n\n//! moment.js locale configuration\n//! locale : German [de]\n//! author : lluchs : https://github.com/lluchs\n//! author: Menelion Elensúle: https://github.com/Oire\n//! author : Mikolaj Dadela : https://github.com/mik01aj\nfunction processRelativeTime$2(num, withoutSuffix, key, isFuture) {\n  const format = {\n    'm': ['eine Minute', 'einer Minute'],\n    'h': ['eine Stunde', 'einer Stunde'],\n    'd': ['ein Tag', 'einem Tag'],\n    'dd': [num + ' Tage', num + ' Tagen'],\n    'M': ['ein Monat', 'einem Monat'],\n    'MM': [num + ' Monate', num + ' Monaten'],\n    'y': ['ein Jahr', 'einem Jahr'],\n    'yy': [num + ' Jahre', num + ' Jahren']\n  };\n  return withoutSuffix ? format[key][0] : format[key][1];\n}\nconst deLocale = {\n  abbr: 'de',\n  months: 'Januar_Februar_März_April_Mai_Juni_Juli_August_September_Oktober_November_Dezember'.split('_'),\n  monthsShort: 'Jan._Feb._März_Apr._Mai_Juni_Juli_Aug._Sep._Okt._Nov._Dez.'.split('_'),\n  monthsParseExact: true,\n  weekdays: 'Sonntag_Montag_Dienstag_Mittwoch_Donnerstag_Freitag_Samstag'.split('_'),\n  weekdaysShort: 'So._Mo._Di._Mi._Do._Fr._Sa.'.split('_'),\n  weekdaysMin: 'So_Mo_Di_Mi_Do_Fr_Sa'.split('_'),\n  weekdaysParseExact: true,\n  longDateFormat: {\n    LT: 'HH:mm',\n    LTS: 'HH:mm:ss',\n    L: 'DD.MM.YYYY',\n    LL: 'D. MMMM YYYY',\n    LLL: 'D. MMMM YYYY HH:mm',\n    LLLL: 'dddd, D. MMMM YYYY HH:mm'\n  },\n  calendar: {\n    sameDay: '[heute um] LT [Uhr]',\n    sameElse: 'L',\n    nextDay: '[morgen um] LT [Uhr]',\n    nextWeek: 'dddd [um] LT [Uhr]',\n    lastDay: '[gestern um] LT [Uhr]',\n    lastWeek: '[letzten] dddd [um] LT [Uhr]'\n  },\n  relativeTime: {\n    future: 'in %s',\n    past: 'vor %s',\n    s: 'ein paar Sekunden',\n    ss: '%d Sekunden',\n    m: processRelativeTime$2,\n    mm: '%d Minuten',\n    h: processRelativeTime$2,\n    hh: '%d Stunden',\n    d: processRelativeTime$2,\n    dd: processRelativeTime$2,\n    M: processRelativeTime$2,\n    MM: processRelativeTime$2,\n    y: processRelativeTime$2,\n    yy: processRelativeTime$2\n  },\n  dayOfMonthOrdinalParse: /\\d{1,2}\\./,\n  ordinal: '%d.',\n  week: {\n    dow: 1,\n    doy: 4 // The week that contains Jan 4th is the first week of the year.\n  }\n};\n\n//! moment.js locale configuration\n//! locale : English (United Kingdom) [en-gb]\n//! author : Chris Gedrim : https://github.com/chrisgedrim\nconst enGbLocale = {\n  abbr: 'en-gb',\n  months: 'January_February_March_April_May_June_July_August_September_October_November_December'.split('_'),\n  monthsShort: 'Jan_Feb_Mar_Apr_May_Jun_Jul_Aug_Sep_Oct_Nov_Dec'.split('_'),\n  weekdays: 'Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday'.split('_'),\n  weekdaysShort: 'Sun_Mon_Tue_Wed_Thu_Fri_Sat'.split('_'),\n  weekdaysMin: 'Su_Mo_Tu_We_Th_Fr_Sa'.split('_'),\n  longDateFormat: {\n    LT: 'HH:mm',\n    LTS: 'HH:mm:ss',\n    L: 'DD/MM/YYYY',\n    LL: 'D MMMM YYYY',\n    LLL: 'D MMMM YYYY HH:mm',\n    LLLL: 'dddd, D MMMM YYYY HH:mm'\n  },\n  calendar: {\n    sameDay: '[Today at] LT',\n    nextDay: '[Tomorrow at] LT',\n    nextWeek: 'dddd [at] LT',\n    lastDay: '[Yesterday at] LT',\n    lastWeek: '[Last] dddd [at] LT',\n    sameElse: 'L'\n  },\n  relativeTime: {\n    future: 'in %s',\n    past: '%s ago',\n    s: 'a few seconds',\n    ss: '%d seconds',\n    m: 'a minute',\n    mm: '%d minutes',\n    h: 'an hour',\n    hh: '%d hours',\n    d: 'a day',\n    dd: '%d days',\n    M: 'a month',\n    MM: '%d months',\n    y: 'a year',\n    yy: '%d years'\n  },\n  dayOfMonthOrdinalParse: /\\d{1,2}(st|nd|rd|th)/,\n  ordinal(_num) {\n    const num = Number(_num);\n    const b = num % 10,\n      output = ~~(num % 100 / 10) === 1 ? 'th' : b === 1 ? 'st' : b === 2 ? 'nd' : b === 3 ? 'rd' : 'th';\n    return num + output;\n  },\n  week: {\n    dow: 1,\n    doy: 4 // The week that contains Jan 4th is the first week of the year.\n  }\n};\n\n//! moment.js locale configuration\n//! locale : Spanish (Dominican Republic) [es-do]\nlet monthsShortDot$4 = 'ene._feb._mar._abr._may._jun._jul._ago._sep._oct._nov._dic.'.split('_'),\n  monthsShort$5 = 'ene_feb_mar_abr_may_jun_jul_ago_sep_oct_nov_dic'.split('_');\nlet monthsParse$5 = [/^ene/i, /^feb/i, /^mar/i, /^abr/i, /^may/i, /^jun/i, /^jul/i, /^ago/i, /^sep/i, /^oct/i, /^nov/i, /^dic/i];\nlet monthsRegex$4 = /^(enero|febrero|marzo|abril|mayo|junio|julio|agosto|septiembre|octubre|noviembre|diciembre|ene\\.?|feb\\.?|mar\\.?|abr\\.?|may\\.?|jun\\.?|jul\\.?|ago\\.?|sep\\.?|oct\\.?|nov\\.?|dic\\.?)/i;\nconst esDoLocale = {\n  abbr: 'es-do',\n  months: 'enero_febrero_marzo_abril_mayo_junio_julio_agosto_septiembre_octubre_noviembre_diciembre'.split('_'),\n  monthsShort(date, format, isUTC) {\n    if (!date) {\n      return monthsShortDot$4;\n    } else if (/-MMM-/.test(format)) {\n      return monthsShort$5[getMonth(date, isUTC)];\n    } else {\n      return monthsShortDot$4[getMonth(date, isUTC)];\n    }\n  },\n  monthsRegex: monthsRegex$4,\n  monthsShortRegex: monthsRegex$4,\n  monthsStrictRegex: /^(enero|febrero|marzo|abril|mayo|junio|julio|agosto|septiembre|octubre|noviembre|diciembre)/i,\n  monthsShortStrictRegex: /^(ene\\.?|feb\\.?|mar\\.?|abr\\.?|may\\.?|jun\\.?|jul\\.?|ago\\.?|sep\\.?|oct\\.?|nov\\.?|dic\\.?)/i,\n  monthsParse: monthsParse$5,\n  longMonthsParse: monthsParse$5,\n  shortMonthsParse: monthsParse$5,\n  weekdays: 'domingo_lunes_martes_miércoles_jueves_viernes_sábado'.split('_'),\n  weekdaysShort: 'dom._lun._mar._mié._jue._vie._sáb.'.split('_'),\n  weekdaysMin: 'do_lu_ma_mi_ju_vi_sá'.split('_'),\n  weekdaysParseExact: true,\n  longDateFormat: {\n    LT: 'h:mm A',\n    LTS: 'h:mm:ss A',\n    L: 'DD/MM/YYYY',\n    LL: 'D [de] MMMM [de] YYYY',\n    LLL: 'D [de] MMMM [de] YYYY h:mm A',\n    LLLL: 'dddd, D [de] MMMM [de] YYYY h:mm A'\n  },\n  calendar: {\n    sameDay(date) {\n      return '[hoy a la' + (getHours(date) !== 1 ? 's' : '') + '] LT';\n    },\n    nextDay(date) {\n      return '[mañana a la' + (getHours(date) !== 1 ? 's' : '') + '] LT';\n    },\n    nextWeek(date) {\n      return 'dddd [a la' + (getHours(date) !== 1 ? 's' : '') + '] LT';\n    },\n    lastDay(date) {\n      return '[ayer a la' + (getHours(date) !== 1 ? 's' : '') + '] LT';\n    },\n    lastWeek(date) {\n      return '[el] dddd [pasado a la' + (getHours(date) !== 1 ? 's' : '') + '] LT';\n    },\n    sameElse: 'L'\n  },\n  relativeTime: {\n    future: 'en %s',\n    past: 'hace %s',\n    s: 'unos segundos',\n    ss: '%d segundos',\n    m: 'un minuto',\n    mm: '%d minutos',\n    h: 'una hora',\n    hh: '%d horas',\n    d: 'un día',\n    dd: '%d días',\n    M: 'un mes',\n    MM: '%d meses',\n    y: 'un año',\n    yy: '%d años'\n  },\n  dayOfMonthOrdinalParse: /\\d{1,2}º/,\n  ordinal: '%dº',\n  week: {\n    dow: 1,\n    doy: 4 // The week that contains Jan 4th is the first week of the year.\n  }\n};\n\n//! moment.js locale configuration\n//! locale : Spanish [es]\n//! author : Julio Napurí : https://github.com/julionc\nlet monthsShortDot$3 = 'ene._feb._mar._abr._may._jun._jul._ago._sep._oct._nov._dic.'.split('_'),\n  monthsShort$4 = 'ene_feb_mar_abr_may_jun_jul_ago_sep_oct_nov_dic'.split('_');\nlet monthsParse$4 = [/^ene/i, /^feb/i, /^mar/i, /^abr/i, /^may/i, /^jun/i, /^jul/i, /^ago/i, /^sep/i, /^oct/i, /^nov/i, /^dic/i];\nlet monthsRegex$3 = /^(enero|febrero|marzo|abril|mayo|junio|julio|agosto|septiembre|octubre|noviembre|diciembre|ene\\.?|feb\\.?|mar\\.?|abr\\.?|may\\.?|jun\\.?|jul\\.?|ago\\.?|sep\\.?|oct\\.?|nov\\.?|dic\\.?)/i;\nconst esLocale = {\n  abbr: 'es',\n  months: 'enero_febrero_marzo_abril_mayo_junio_julio_agosto_septiembre_octubre_noviembre_diciembre'.split('_'),\n  monthsShort(date, format, isUTC) {\n    if (!date) {\n      return monthsShortDot$3;\n    }\n    if (/-MMM-/.test(format)) {\n      return monthsShort$4[getMonth(date, isUTC)];\n    }\n    return monthsShortDot$3[getMonth(date, isUTC)];\n  },\n  monthsRegex: monthsRegex$3,\n  monthsShortRegex: monthsRegex$3,\n  monthsStrictRegex: /^(enero|febrero|marzo|abril|mayo|junio|julio|agosto|septiembre|octubre|noviembre|diciembre)/i,\n  monthsShortStrictRegex: /^(ene\\.?|feb\\.?|mar\\.?|abr\\.?|may\\.?|jun\\.?|jul\\.?|ago\\.?|sep\\.?|oct\\.?|nov\\.?|dic\\.?)/i,\n  monthsParse: monthsParse$4,\n  longMonthsParse: monthsParse$4,\n  shortMonthsParse: monthsParse$4,\n  weekdays: 'domingo_lunes_martes_miércoles_jueves_viernes_sábado'.split('_'),\n  weekdaysShort: 'dom._lun._mar._mié._jue._vie._sáb.'.split('_'),\n  weekdaysMin: 'do_lu_ma_mi_ju_vi_sá'.split('_'),\n  weekdaysParseExact: true,\n  longDateFormat: {\n    LT: 'H:mm',\n    LTS: 'H:mm:ss',\n    L: 'DD/MM/YYYY',\n    LL: 'D [de] MMMM [de] YYYY',\n    LLL: 'D [de] MMMM [de] YYYY H:mm',\n    LLLL: 'dddd, D [de] MMMM [de] YYYY H:mm'\n  },\n  calendar: {\n    sameDay(date) {\n      return '[hoy a la' + (getHours(date) !== 1 ? 's' : '') + '] LT';\n    },\n    nextDay(date) {\n      return '[mañana a la' + (getHours(date) !== 1 ? 's' : '') + '] LT';\n    },\n    nextWeek(date) {\n      return 'dddd [a la' + (getHours(date) !== 1 ? 's' : '') + '] LT';\n    },\n    lastDay(date) {\n      return '[ayer a la' + (getHours(date) !== 1 ? 's' : '') + '] LT';\n    },\n    lastWeek(date) {\n      return '[el] dddd [pasado a la' + (getHours(date) !== 1 ? 's' : '') + '] LT';\n    },\n    sameElse: 'L'\n  },\n  relativeTime: {\n    future: 'en %s',\n    past: 'hace %s',\n    s: 'unos segundos',\n    ss: '%d segundos',\n    m: 'un minuto',\n    mm: '%d minutos',\n    h: 'una hora',\n    hh: '%d horas',\n    d: 'un día',\n    dd: '%d días',\n    M: 'un mes',\n    MM: '%d meses',\n    y: 'un año',\n    yy: '%d años'\n  },\n  dayOfMonthOrdinalParse: /\\d{1,2}º/,\n  ordinal: '%dº',\n  week: {\n    dow: 1,\n    doy: 4 // The week that contains Jan 4th is the first week of the year.\n  }\n};\n\n//! moment.js locale configuration\n//! locale : Spanish (Puerto Rico) [es-pr]\nlet monthsShortDot$2 = 'ene._feb._mar._abr._may._jun._jul._ago._sep._oct._nov._dic.'.split('_');\nlet monthsShort$3 = 'ene_feb_mar_abr_may_jun_jul_ago_sep_oct_nov_dic'.split('_');\nconst esPrLocale = {\n  abbr: 'es-pr',\n  months: 'enero_febrero_marzo_abril_mayo_junio_julio_agosto_septiembre_octubre_noviembre_diciembre'.split('_'),\n  monthsShort(date, format, isUTC) {\n    if (!date) {\n      return monthsShortDot$2;\n    } else if (/-MMM-/.test(format)) {\n      return monthsShort$3[getMonth(date, isUTC)];\n    } else {\n      return monthsShortDot$2[getMonth(date, isUTC)];\n    }\n  },\n  monthsParseExact: true,\n  weekdays: 'domingo_lunes_martes_miércoles_jueves_viernes_sábado'.split('_'),\n  weekdaysShort: 'dom._lun._mar._mié._jue._vie._sáb.'.split('_'),\n  weekdaysMin: 'do_lu_ma_mi_ju_vi_sá'.split('_'),\n  weekdaysParseExact: true,\n  longDateFormat: {\n    LT: 'h:mm A',\n    LTS: 'h:mm:ss A',\n    L: 'MM/DD/YYYY',\n    LL: 'D [de] MMMM [de] YYYY',\n    LLL: 'D [de] MMMM [de] YYYY h:mm A',\n    LLLL: 'dddd, D [de] MMMM [de] YYYY h:mm A'\n  },\n  calendar: {\n    sameDay(date) {\n      return '[hoy a la' + (getHours(date) !== 1 ? 's' : '') + '] LT';\n    },\n    nextDay(date) {\n      return '[mañana a la' + (getHours(date) !== 1 ? 's' : '') + '] LT';\n    },\n    nextWeek(date) {\n      return 'dddd [a la' + (getHours(date) !== 1 ? 's' : '') + '] LT';\n    },\n    lastDay(date) {\n      return '[ayer a la' + (getHours(date) !== 1 ? 's' : '') + '] LT';\n    },\n    lastWeek(date) {\n      return '[el] dddd [pasado a la' + (getHours(date) !== 1 ? 's' : '') + '] LT';\n    },\n    sameElse: 'L'\n  },\n  relativeTime: {\n    future: 'en %s',\n    past: 'hace %s',\n    s: 'unos segundos',\n    ss: '%d segundos',\n    m: 'un minuto',\n    mm: '%d minutos',\n    h: 'una hora',\n    hh: '%d horas',\n    d: 'un día',\n    dd: '%d días',\n    M: 'un mes',\n    MM: '%d meses',\n    y: 'un año',\n    yy: '%d años'\n  },\n  dayOfMonthOrdinalParse: /\\d{1,2}º/,\n  ordinal: '%dº',\n  week: {\n    dow: 0,\n    doy: 6 // The week that contains Jan 1st is the first week of the year.\n  }\n};\n\n//! moment.js locale configuration\n//! locale : Spanish (United States) [es-us]\n//! author : bustta : https://github.com/bustta\nlet monthsShortDot$1 = 'ene._feb._mar._abr._may._jun._jul._ago._sep._oct._nov._dic.'.split('_');\nlet monthsShort$2 = 'ene_feb_mar_abr_may_jun_jul_ago_sep_oct_nov_dic'.split('_');\nconst esUsLocale = {\n  abbr: 'es-us',\n  months: 'enero_febrero_marzo_abril_mayo_junio_julio_agosto_septiembre_octubre_noviembre_diciembre'.split('_'),\n  monthsShort(date, format, isUTC) {\n    if (!date) {\n      return monthsShortDot$1;\n    } else if (/-MMM-/.test(format)) {\n      return monthsShort$2[getMonth(date, isUTC)];\n    } else {\n      return monthsShortDot$1[getMonth(date, isUTC)];\n    }\n  },\n  monthsParseExact: true,\n  weekdays: 'domingo_lunes_martes_miércoles_jueves_viernes_sábado'.split('_'),\n  weekdaysShort: 'dom._lun._mar._mié._jue._vie._sáb.'.split('_'),\n  weekdaysMin: 'do_lu_ma_mi_ju_vi_sá'.split('_'),\n  weekdaysParseExact: true,\n  longDateFormat: {\n    LT: 'h:mm A',\n    LTS: 'h:mm:ss A',\n    L: 'MM/DD/YYYY',\n    LL: 'MMMM [de] D [de] YYYY',\n    LLL: 'MMMM [de] D [de] YYYY h:mm A',\n    LLLL: 'dddd, MMMM [de] D [de] YYYY h:mm A'\n  },\n  calendar: {\n    sameDay(date) {\n      return '[hoy a la' + (getHours(date) !== 1 ? 's' : '') + '] LT';\n    },\n    nextDay(date) {\n      return '[mañana a la' + (getHours(date) !== 1 ? 's' : '') + '] LT';\n    },\n    nextWeek(date) {\n      return 'dddd [a la' + (getHours(date) !== 1 ? 's' : '') + '] LT';\n    },\n    lastDay(date) {\n      return '[ayer a la' + (getHours(date) !== 1 ? 's' : '') + '] LT';\n    },\n    lastWeek(date) {\n      return '[el] dddd [pasado a la' + (getHours(date) !== 1 ? 's' : '') + '] LT';\n    },\n    sameElse: 'L'\n  },\n  relativeTime: {\n    future: 'en %s',\n    past: 'hace %s',\n    s: 'unos segundos',\n    ss: '%d segundos',\n    m: 'un minuto',\n    mm: '%d minutos',\n    h: 'una hora',\n    hh: '%d horas',\n    d: 'un día',\n    dd: '%d días',\n    M: 'un mes',\n    MM: '%d meses',\n    y: 'un año',\n    yy: '%d años'\n  },\n  dayOfMonthOrdinalParse: /\\d{1,2}º/,\n  ordinal: '%dº',\n  week: {\n    dow: 0,\n    doy: 6 // The week that contains Jan 1st is the first week of the year.\n  }\n};\n\n//! moment.js locale configuration\n//! locale : Estonian [et]\n//! author : Chris Gedrim : https://github.com/a90machado\nconst processRelativeTime$1 = function (num, withoutSuffix, key, isFuture) {\n  const format = {\n    s: ['mõne sekundi', 'mõni sekund', 'paar sekundit'],\n    ss: [num + 'sekundi', num + 'sekundit'],\n    m: ['ühe minuti', 'üks minut'],\n    mm: [num + ' minuti', num + ' minutit'],\n    h: ['ühe tunni', 'tund aega', 'üks tund'],\n    hh: [num + ' tunni', num + ' tundi'],\n    d: ['ühe päeva', 'üks päev'],\n    M: ['kuu aja', 'kuu aega', 'üks kuu'],\n    MM: [num + ' kuu', num + ' kuud'],\n    y: ['ühe aasta', 'aasta', 'üks aasta'],\n    yy: [num + ' aasta', num + ' aastat']\n  };\n  if (withoutSuffix) {\n    return format[key][2] ? format[key][2] : format[key][1];\n  }\n  return isFuture ? format[key][0] : format[key][1];\n};\nconst etLocale = {\n  abbr: 'et',\n  months: 'jaanuar_veebruar_märts_aprill_mai_juuni_juuli_august_september_oktoober_november_detsember'.split('_'),\n  monthsShort: 'jaan_veebr_märts_apr_mai_juuni_juuli_aug_sept_okt_nov_dets'.split('_'),\n  weekdays: 'pühapäev_esmaspäev_teisipäev_kolmapäev_neljapäev_reede_laupäev'.split('_'),\n  weekdaysShort: 'P_E_T_K_N_R_L'.split('_'),\n  weekdaysMin: 'P_E_T_K_N_R_L'.split('_'),\n  longDateFormat: {\n    LT: 'H:mm',\n    LTS: 'H:mm:ss',\n    L: 'DD.MM.YYYY',\n    LL: 'D. MMMM YYYY',\n    LLL: 'D. MMMM YYYY H:mm',\n    LLLL: 'dddd, D. MMMM YYYY H:mm'\n  },\n  calendar: {\n    sameDay: '[Täna,] LT',\n    nextDay: '[Homme,] LT',\n    nextWeek: '[Järgmine] dddd LT',\n    lastDay: '[Eile,] LT',\n    lastWeek: '[Eelmine] dddd LT',\n    sameElse: 'L'\n  },\n  relativeTime: {\n    future: '%s pärast',\n    past: '%s tagasi',\n    s: processRelativeTime$1,\n    ss: processRelativeTime$1,\n    m: processRelativeTime$1,\n    mm: processRelativeTime$1,\n    h: processRelativeTime$1,\n    hh: processRelativeTime$1,\n    d: processRelativeTime$1,\n    dd: '%d päeva',\n    M: processRelativeTime$1,\n    MM: processRelativeTime$1,\n    y: processRelativeTime$1,\n    yy: processRelativeTime$1\n  },\n  dayOfMonthOrdinalParse: /\\d{1,2}./,\n  ordinal: '%d.',\n  week: {\n    dow: 1,\n    doy: 4 // The week that contains Jan 4th is the first week of the year.\n  }\n};\n\n//! moment.js locale configuration\n// https://github.com/moment/moment/blob/develop/locale/fi.js\nvar numbersPast = 'nolla yksi kaksi kolme neljä viisi kuusi seitsemän kahdeksan yhdeksän'.split(' '),\n  numbersFuture = ['nolla', 'yhden', 'kahden', 'kolmen', 'neljän', 'viiden', 'kuuden', numbersPast[7], numbersPast[8], numbersPast[9]];\nfunction translate$5(num, withoutSuffix, key, isFuture) {\n  var result = '';\n  switch (key) {\n    case 's':\n      return isFuture ? 'muutaman sekunnin' : 'muutama sekunti';\n    case 'ss':\n      return isFuture ? 'sekunnin' : 'sekuntia';\n    case 'm':\n      return isFuture ? 'minuutin' : 'minuutti';\n    case 'mm':\n      result = isFuture ? 'minuutin' : 'minuuttia';\n      break;\n    case 'h':\n      return isFuture ? 'tunnin' : 'tunti';\n    case 'hh':\n      result = isFuture ? 'tunnin' : 'tuntia';\n      break;\n    case 'd':\n      return isFuture ? 'päivän' : 'päivä';\n    case 'dd':\n      result = isFuture ? 'päivän' : 'päivää';\n      break;\n    case 'M':\n      return isFuture ? 'kuukauden' : 'kuukausi';\n    case 'MM':\n      result = isFuture ? 'kuukauden' : 'kuukautta';\n      break;\n    case 'y':\n      return isFuture ? 'vuoden' : 'vuosi';\n    case 'yy':\n      result = isFuture ? 'vuoden' : 'vuotta';\n      break;\n  }\n  result = verbalNumber(num, isFuture) + ' ' + result;\n  return result;\n}\nfunction verbalNumber(num, isFuture) {\n  return num < 10 ? isFuture ? numbersFuture[num] : numbersPast[num] : num;\n}\nconst fiLocale = {\n  abbr: 'fi',\n  months: 'tammikuu_helmikuu_maaliskuu_huhtikuu_toukokuu_kesäkuu_heinäkuu_elokuu_syyskuu_lokakuu_marraskuu_joulukuu'.split('_'),\n  monthsShort: 'tammi_helmi_maalis_huhti_touko_kesä_heinä_elo_syys_loka_marras_joulu'.split('_'),\n  weekdays: 'sunnuntai_maanantai_tiistai_keskiviikko_torstai_perjantai_lauantai'.split('_'),\n  weekdaysShort: 'su_ma_ti_ke_to_pe_la'.split('_'),\n  weekdaysMin: 'su_ma_ti_ke_to_pe_la'.split('_'),\n  longDateFormat: {\n    LT: 'HH.mm',\n    LTS: 'HH.mm.ss',\n    L: 'DD.MM.YYYY',\n    LL: 'Do MMMM[ta] YYYY',\n    LLL: 'Do MMMM[ta] YYYY, [klo] HH.mm',\n    LLLL: 'dddd, Do MMMM[ta] YYYY, [klo] HH.mm',\n    l: 'D.M.YYYY',\n    ll: 'Do MMM YYYY',\n    lll: 'Do MMM YYYY, [klo] HH.mm',\n    llll: 'ddd, Do MMM YYYY, [klo] HH.mm'\n  },\n  calendar: {\n    sameDay: '[tänään] [klo] LT',\n    nextDay: '[huomenna] [klo] LT',\n    nextWeek: 'dddd [klo] LT',\n    lastDay: '[eilen] [klo] LT',\n    lastWeek: '[viime] dddd[na] [klo] LT',\n    sameElse: 'L'\n  },\n  relativeTime: {\n    future: '%s päästä',\n    past: '%s sitten',\n    s: translate$5,\n    ss: translate$5,\n    m: translate$5,\n    mm: translate$5,\n    h: translate$5,\n    hh: translate$5,\n    d: translate$5,\n    dd: translate$5,\n    M: translate$5,\n    MM: translate$5,\n    y: translate$5,\n    yy: translate$5\n  },\n  dayOfMonthOrdinalParse: /\\d{1,2}\\./,\n  ordinal: '%d.',\n  week: {\n    dow: 1,\n    doy: 4 // The week that contains Jan 4th is the first week of the year.\n  }\n};\n\n//! moment.js locale configuration\n//! locale : French [fr]\n//! author : John Fischer : https://github.com/jfroffice\nconst frLocale = {\n  abbr: 'fr',\n  months: 'janvier_février_mars_avril_mai_juin_juillet_août_septembre_octobre_novembre_décembre'.split('_'),\n  monthsShort: 'janv._févr._mars_avr._mai_juin_juil._août_sept._oct._nov._déc.'.split('_'),\n  monthsParseExact: true,\n  weekdays: 'dimanche_lundi_mardi_mercredi_jeudi_vendredi_samedi'.split('_'),\n  weekdaysShort: 'dim._lun._mar._mer._jeu._ven._sam.'.split('_'),\n  weekdaysMin: 'di_lu_ma_me_je_ve_sa'.split('_'),\n  weekdaysParseExact: true,\n  longDateFormat: {\n    LT: 'HH:mm',\n    LTS: 'HH:mm:ss',\n    L: 'DD/MM/YYYY',\n    LL: 'D MMMM YYYY',\n    LLL: 'D MMMM YYYY HH:mm',\n    LLLL: 'dddd D MMMM YYYY HH:mm'\n  },\n  calendar: {\n    sameDay: '[Aujourd’hui à] LT',\n    nextDay: '[Demain à] LT',\n    nextWeek: 'dddd [à] LT',\n    lastDay: '[Hier à] LT',\n    lastWeek: 'dddd [dernier à] LT',\n    sameElse: 'L'\n  },\n  relativeTime: {\n    future: 'dans %s',\n    past: 'il y a %s',\n    s: 'quelques secondes',\n    ss: '%d secondes',\n    m: 'une minute',\n    mm: '%d minutes',\n    h: 'une heure',\n    hh: '%d heures',\n    d: 'un jour',\n    dd: '%d jours',\n    M: 'un mois',\n    MM: '%d mois',\n    y: 'un an',\n    yy: '%d ans'\n  },\n  dayOfMonthOrdinalParse: /\\d{1,2}(er|)/,\n  ordinal(_num, period) {\n    const num = Number(_num);\n    switch (period) {\n      // TODO: Return 'e' when day of month > 1. Move this case inside\n      // block for masculine words below.\n      // See https://github.com/moment/moment/issues/3375\n      case 'D':\n        return num + (num === 1 ? 'er' : '');\n      // Words with masculine grammatical gender: mois, trimestre, jour\n      default:\n      case 'M':\n      case 'Q':\n      case 'DDD':\n      case 'd':\n        return num + (num === 1 ? 'er' : 'e');\n      // Words with feminine grammatical gender: semaine\n      case 'w':\n      case 'W':\n        return num + (num === 1 ? 're' : 'e');\n    }\n  },\n  week: {\n    dow: 1,\n    doy: 4 // The week that contains Jan 4th is the first week of the year.\n  }\n};\n\n//! moment.js locale configuration\n//! locale : Galician [gl]\n//! author : Darío Beiró : https://github.com/quinobravo\nlet monthsShortDot = 'xan._feb._mar._abr._mai._xuñ._xul._ago._set._out._nov._dec.'.split('_'),\n  monthsShort$1 = 'xan_feb_mar_abr_mai_xuñ_xul_ago_set_out_nov_dec'.split('_');\nlet monthsParse$3 = [/^xan/i, /^feb/i, /^mar/i, /^abr/i, /^mai/i, /^xuñ/i, /^xul/i, /^ago/i, /^set/i, /^out/i, /^nov/i, /^dec/i];\nlet monthsRegex$2 = /^(xaneiro|febreiro|marzo|abril|maio|xuño|xullo|agosto|setembro|outubro|novembro|decembro|xan\\.?|feb\\.?|mar\\.?|abr\\.?|mai\\.?|xuñ\\.?|xul\\.?|ago\\.?|set\\.?|out\\.?|nov\\.?|dec\\.?)/i;\nconst glLocale = {\n  abbr: 'gl',\n  months: 'xaneiro_febreiro_marzo_abril_maio_xuño_xullo_agosto_setembro_outubro_novembro_decembro'.split('_'),\n  monthsShort(date, format, isUTC) {\n    if (!date) {\n      return monthsShortDot;\n    }\n    if (/-MMM-/.test(format)) {\n      return monthsShort$1[getMonth(date, isUTC)];\n    }\n    return monthsShortDot[getMonth(date, isUTC)];\n  },\n  monthsRegex: monthsRegex$2,\n  monthsShortRegex: monthsRegex$2,\n  monthsStrictRegex: /^(xaneiro|febreiro|marzo|abril|maio|xuño|xullo|agosto|setembro|outubro|novembro|decembro)/i,\n  monthsShortStrictRegex: /^(xan\\.?|feb\\.?|mar\\.?|abr\\.?|mai\\.?|xuñ\\.?|xul\\.?|ago\\.?|set\\.?|out\\.?|nov\\.?|dec\\.?)/i,\n  monthsParse: monthsParse$3,\n  longMonthsParse: monthsParse$3,\n  shortMonthsParse: monthsParse$3,\n  weekdays: 'domingo_luns_martes_mércores_xoves_venres_sábado'.split('_'),\n  weekdaysShort: 'dom._lun._mar._mér._xov._ven._sáb.'.split('_'),\n  weekdaysMin: 'do_lu_ma_mé_xo_ve_sá'.split('_'),\n  weekdaysParseExact: true,\n  longDateFormat: {\n    LT: 'H:mm',\n    LTS: 'H:mm:ss',\n    L: 'DD/MM/YYYY',\n    LL: 'D [de] MMMM [de] YYYY',\n    LLL: 'D [de] MMMM [de] YYYY H:mm',\n    LLLL: 'dddd, D [de] MMMM [de] YYYY H:mm'\n  },\n  calendar: {\n    sameDay(date) {\n      return '[hoxe á' + (getHours(date) !== 1 ? 's' : '') + '] LT';\n    },\n    nextDay(date) {\n      return '[mañan á' + (getHours(date) !== 1 ? 's' : '') + '] LT';\n    },\n    nextWeek(date) {\n      return 'dddd [á' + (getHours(date) !== 1 ? 's' : '') + '] LT';\n    },\n    lastDay(date) {\n      return '[onte á' + (getHours(date) !== 1 ? 's' : '') + '] LT';\n    },\n    lastWeek(date) {\n      return '[o] dddd [pasado á' + (getHours(date) !== 1 ? 's' : '') + '] LT';\n    },\n    sameElse: 'L'\n  },\n  relativeTime: {\n    future: 'en %s',\n    past: 'fai %s',\n    s: 'uns segundos',\n    ss: '%d segundos',\n    m: 'un minuto',\n    mm: '%d minutos',\n    h: 'unha hora',\n    hh: '%d horas',\n    d: 'un día',\n    dd: '%d días',\n    M: 'un mes',\n    MM: '%d meses',\n    y: 'un ano',\n    yy: '%d anos'\n  },\n  dayOfMonthOrdinalParse: /\\d{1,2}º/,\n  ordinal: '%dº',\n  week: {\n    dow: 1,\n    doy: 4 // The week that contains Jan 4th is the first week of the year.\n  }\n};\n\n//! moment.js locale configuration\n//! locale : Hebrew [he]\n//! author : Tomer Cohen : https://github.com/tomer\n//! author : Moshe Simantov : https://github.com/DevelopmentIL\n//! author : Tal Ater : https://github.com/TalAter\nconst heLocale = {\n  abbr: 'he',\n  months: 'ינואר_פברואר_מרץ_אפריל_מאי_יוני_יולי_אוגוסט_ספטמבר_אוקטובר_נובמבר_דצמבר'.split('_'),\n  monthsShort: 'ינו׳_פבר׳_מרץ_אפר׳_מאי_יוני_יולי_אוג׳_ספט׳_אוק׳_נוב׳_דצמ׳'.split('_'),\n  weekdays: 'ראשון_שני_שלישי_רביעי_חמישי_שישי_שבת'.split('_'),\n  weekdaysShort: 'א׳_ב׳_ג׳_ד׳_ה׳_ו׳_ש׳'.split('_'),\n  weekdaysMin: 'א_ב_ג_ד_ה_ו_ש'.split('_'),\n  longDateFormat: {\n    LT: 'HH:mm',\n    LTS: 'HH:mm:ss',\n    L: 'DD/MM/YYYY',\n    LL: 'D [ב]MMMM YYYY',\n    LLL: 'D [ב]MMMM YYYY HH:mm',\n    LLLL: 'dddd, D [ב]MMMM YYYY HH:mm',\n    l: 'D/M/YYYY',\n    ll: 'D MMM YYYY',\n    lll: 'D MMM YYYY HH:mm',\n    llll: 'ddd, D MMM YYYY HH:mm'\n  },\n  calendar: {\n    sameDay: '[היום ב־]LT',\n    nextDay: '[מחר ב־]LT',\n    nextWeek: 'dddd [בשעה] LT',\n    lastDay: '[אתמול ב־]LT',\n    lastWeek: '[ביום] dddd [האחרון בשעה] LT',\n    sameElse: 'L'\n  },\n  relativeTime: {\n    future: 'בעוד %s',\n    past: 'לפני %s',\n    s: 'מספר שניות',\n    ss: '%d שניות',\n    m: 'דקה',\n    mm: '%d דקות',\n    h: 'שעה',\n    hh(num) {\n      if (num === 2) {\n        return 'שעתיים';\n      }\n      return num + ' שעות';\n    },\n    d: 'יום',\n    dd(num) {\n      if (num === 2) {\n        return 'יומיים';\n      }\n      return num + ' ימים';\n    },\n    M: 'חודש',\n    MM(num) {\n      if (num === 2) {\n        return 'חודשיים';\n      }\n      return num + ' חודשים';\n    },\n    y: 'שנה',\n    yy(num) {\n      if (num === 2) {\n        return 'שנתיים';\n      } else if (num % 10 === 0 && num !== 10) {\n        return num + ' שנה';\n      }\n      return num + ' שנים';\n    }\n  },\n  meridiemParse: /אחה\"צ|לפנה\"צ|אחרי הצהריים|לפני הצהריים|לפנות בוקר|בבוקר|בערב/i,\n  isPM(input) {\n    return /^(אחה\"צ|אחרי הצהריים|בערב)$/.test(input);\n  },\n  meridiem(hour, minute, isLower) {\n    if (hour < 5) {\n      return 'לפנות בוקר';\n    } else if (hour < 10) {\n      return 'בבוקר';\n    } else if (hour < 12) {\n      return isLower ? 'לפנה\"צ' : 'לפני הצהריים';\n    } else if (hour < 18) {\n      return isLower ? 'אחה\"צ' : 'אחרי הצהריים';\n    } else {\n      return 'בערב';\n    }\n  }\n};\n\n//! moment.js locale configuration\n//! locale : Hindi [hi]\n//! author : Mayank Singhal : https://github.com/mayanksinghal\nlet symbolMap = {\n    1: '१',\n    2: '२',\n    3: '३',\n    4: '४',\n    5: '५',\n    6: '६',\n    7: '७',\n    8: '८',\n    9: '९',\n    0: '०'\n  },\n  numberMap = {\n    '१': '1',\n    '२': '2',\n    '३': '3',\n    '४': '4',\n    '५': '5',\n    '६': '6',\n    '७': '7',\n    '८': '8',\n    '९': '9',\n    '०': '0'\n  };\nconst hiLocale = {\n  abbr: 'hi',\n  months: 'जनवरी_फ़रवरी_मार्च_अप्रैल_मई_जून_जुलाई_अगस्त_सितम्बर_अक्टूबर_नवम्बर_दिसम्बर'.split('_'),\n  monthsShort: 'जन._फ़र._मार्च_अप्रै._मई_जून_जुल._अग._सित._अक्टू._नव._दिस.'.split('_'),\n  monthsParseExact: true,\n  weekdays: 'रविवार_सोमवार_मंगलवार_बुधवार_गुरूवार_शुक्रवार_शनिवार'.split('_'),\n  weekdaysShort: 'रवि_सोम_मंगल_बुध_गुरू_शुक्र_शनि'.split('_'),\n  weekdaysMin: 'र_सो_मं_बु_गु_शु_श'.split('_'),\n  longDateFormat: {\n    LT: 'A h:mm बजे',\n    LTS: 'A h:mm:ss बजे',\n    L: 'DD/MM/YYYY',\n    LL: 'D MMMM YYYY',\n    LLL: 'D MMMM YYYY, A h:mm बजे',\n    LLLL: 'dddd, D MMMM YYYY, A h:mm बजे'\n  },\n  calendar: {\n    sameDay: '[आज] LT',\n    nextDay: '[कल] LT',\n    nextWeek: 'dddd, LT',\n    lastDay: '[कल] LT',\n    lastWeek: '[पिछले] dddd, LT',\n    sameElse: 'L'\n  },\n  relativeTime: {\n    future: '%s में',\n    past: '%s पहले',\n    s: 'कुछ ही क्षण',\n    ss: '%d सेकंड',\n    m: 'एक मिनट',\n    mm: '%d मिनट',\n    h: 'एक घंटा',\n    hh: '%d घंटे',\n    d: 'एक दिन',\n    dd: '%d दिन',\n    M: 'एक महीने',\n    MM: '%d महीने',\n    y: 'एक वर्ष',\n    yy: '%d वर्ष'\n  },\n  preparse(str) {\n    return str.replace(/[१२३४५६७८९०]/g, function (match) {\n      return numberMap[match];\n    });\n  },\n  postformat(str) {\n    return str.replace(/\\d/g, function (match) {\n      return symbolMap[match];\n    });\n  },\n  // Hindi notation for meridiems are quite fuzzy in practice. While there exists\n  // a rigid notion of a 'Pahar' it is not used as rigidly in modern Hindi.\n  meridiemParse: /रात|सुबह|दोपहर|शाम/,\n  meridiemHour(hour, meridiem) {\n    if (hour === 12) {\n      hour = 0;\n    }\n    if (meridiem === 'रात') {\n      return hour < 4 ? hour : hour + 12;\n    } else if (meridiem === 'सुबह') {\n      return hour;\n    } else if (meridiem === 'दोपहर') {\n      return hour >= 10 ? hour : hour + 12;\n    } else if (meridiem === 'शाम') {\n      return hour + 12;\n    }\n  },\n  meridiem(hour, minute, isLower) {\n    if (hour < 4) {\n      return 'रात';\n    } else if (hour < 10) {\n      return 'सुबह';\n    } else if (hour < 17) {\n      return 'दोपहर';\n    } else if (hour < 20) {\n      return 'शाम';\n    } else {\n      return 'रात';\n    }\n  },\n  week: {\n    dow: 0,\n    doy: 6 // The week that contains Jan 1st is the first week of the year.\n  }\n};\n\n//! moment.js locale configuration\n//! locale : Hungarian [hu]\n//! author : Adam Brunner : https://github.com/adambrunner\nlet weekEndings = 'vasárnap hétfőn kedden szerdán csütörtökön pénteken szombaton'.split(' ');\nfunction translate$4(num, withoutSuffix, key, isFuture) {\n  switch (key) {\n    case 's':\n      return isFuture || withoutSuffix ? 'néhány másodperc' : 'néhány másodperce';\n    case 'ss':\n      return num + (isFuture || withoutSuffix ? ' másodperc' : ' másodperce');\n    case 'm':\n      return 'egy' + (isFuture || withoutSuffix ? ' perc' : ' perce');\n    case 'mm':\n      return num + (isFuture || withoutSuffix ? ' perc' : ' perce');\n    case 'h':\n      return 'egy' + (isFuture || withoutSuffix ? ' óra' : ' órája');\n    case 'hh':\n      return num + (isFuture || withoutSuffix ? ' óra' : ' órája');\n    case 'd':\n      return 'egy' + (isFuture || withoutSuffix ? ' nap' : ' napja');\n    case 'dd':\n      return num + (isFuture || withoutSuffix ? ' nap' : ' napja');\n    case 'M':\n      return 'egy' + (isFuture || withoutSuffix ? ' hónap' : ' hónapja');\n    case 'MM':\n      return num + (isFuture || withoutSuffix ? ' hónap' : ' hónapja');\n    case 'y':\n      return 'egy' + (isFuture || withoutSuffix ? ' év' : ' éve');\n    case 'yy':\n      return num + (isFuture || withoutSuffix ? ' év' : ' éve');\n  }\n  return '';\n}\nfunction week(date, isFuture) {\n  return (isFuture ? '' : '[múlt] ') + '[' + weekEndings[getDayOfWeek(date)] + '] LT[-kor]';\n}\nconst huLocale = {\n  abbr: 'hu',\n  months: 'január_február_március_április_május_június_július_augusztus_szeptember_október_november_december'.split('_'),\n  monthsShort: 'jan_feb_márc_ápr_máj_jún_júl_aug_szept_okt_nov_dec'.split('_'),\n  weekdays: 'vasárnap_hétfő_kedd_szerda_csütörtök_péntek_szombat'.split('_'),\n  weekdaysShort: 'vas_hét_kedd_sze_csüt_pén_szo'.split('_'),\n  weekdaysMin: 'v_h_k_sze_cs_p_szo'.split('_'),\n  longDateFormat: {\n    LT: 'H:mm',\n    LTS: 'H:mm:ss',\n    L: 'YYYY.MM.DD.',\n    LL: 'YYYY. MMMM D.',\n    LLL: 'YYYY. MMMM D. H:mm',\n    LLLL: 'YYYY. MMMM D., dddd H:mm'\n  },\n  meridiemParse: /de|du/i,\n  isPM(input) {\n    return input.charAt(1).toLowerCase() === 'u';\n  },\n  meridiem(hours, minutes, isLower) {\n    if (hours < 12) {\n      return isLower === true ? 'de' : 'DE';\n    } else {\n      return isLower === true ? 'du' : 'DU';\n    }\n  },\n  calendar: {\n    sameDay: '[ma] LT[-kor]',\n    nextDay: '[holnap] LT[-kor]',\n    nextWeek(date) {\n      return week(date, true);\n    },\n    lastDay: '[tegnap] LT[-kor]',\n    lastWeek(date) {\n      return week(date, false);\n    },\n    sameElse: 'L'\n  },\n  relativeTime: {\n    future: '%s múlva',\n    past: '%s',\n    s: translate$4,\n    ss: translate$4,\n    m: translate$4,\n    mm: translate$4,\n    h: translate$4,\n    hh: translate$4,\n    d: translate$4,\n    dd: translate$4,\n    M: translate$4,\n    MM: translate$4,\n    y: translate$4,\n    yy: translate$4\n  },\n  dayOfMonthOrdinalParse: /\\d{1,2}\\./,\n  ordinal: '%d.',\n  week: {\n    dow: 1,\n    doy: 4 // The week that contains Jan 4th is the first week of the year.\n  }\n};\n\n//! moment.js locale configuration\n//! locale : Croatian [hr]\n//! author : Danijel Grmec : https://github.com/cobaltsis\nconst hrLocale = {\n  abbr: 'hr',\n  months: 'Siječanj_Veljača_Ožujak_Travanj_Svibanj_Lipanj_Srpanj_Kolovoz_Rujan_Listopad_Studeni_Prosinac'.split('_'),\n  monthsShort: 'Sij_Velj_Ožu_Tra_Svi_Lip_Srp_Kol_Ruj_Lis_Stu_Pro'.split('_'),\n  weekdays: 'Nedjelja_Ponedjeljak_Utorak_Srijeda_Četvrtak_Petak_Subota'.split('_'),\n  weekdaysShort: 'Ned_Pon_Uto_Sri_Čet_Pet_Sub'.split('_'),\n  weekdaysMin: 'Ne_Po_Ut_Sr_Če_Pe_Su'.split('_'),\n  longDateFormat: {\n    LT: 'HH:mm',\n    LTS: 'HH:mm:ss',\n    L: 'DD/MM/YYYY',\n    LL: 'D MMMM YYYY',\n    LLL: 'D MMMM YYYY HH:mm',\n    LLLL: 'dddd, D MMMM YYYY HH:mm'\n  },\n  calendar: {\n    sameDay: '[Danas u] LT',\n    nextDay: '[Sutra u] LT',\n    nextWeek: 'dddd [u] LT',\n    lastDay: '[Jučer u] LT',\n    lastWeek: '[Zadnji] dddd [u] LT',\n    sameElse: 'L'\n  },\n  invalidDate: 'Neispravan datum',\n  relativeTime: {\n    future: 'za %s',\n    past: '%s prije',\n    s: 'nekoliko sekundi',\n    ss: '%d sekundi',\n    m: 'minuta',\n    mm: '%d minuta',\n    h: 'sat',\n    hh: '%d sati',\n    d: 'dan',\n    dd: '%d dana',\n    M: 'mjesec',\n    MM: '%d mjeseci',\n    y: 'godina',\n    yy: '%d godina'\n  },\n  dayOfMonthOrdinalParse: /\\d{1,2}(st|nd|rd|th)/,\n  ordinal(_num) {\n    const num = Number(_num);\n    const b = num % 10,\n      output = ~~(num % 100 / 10) === 1 ? '.' : b === 1 ? '.' : b === 2 ? '.' : b === 3 ? '.' : '.';\n    return num + output;\n  },\n  week: {\n    dow: 1,\n    doy: 4 // The week that contains Jan 4th is the first week of the year.\n  }\n};\n\n//! moment.js locale configuration\n//! locale : Indonesia [id]\n//! author : Romy Kusuma : https://github.com/rkusuma\n//! reference: https://github.com/moment/moment/blob/develop/locale/id.js\nconst idLocale = {\n  abbr: 'id',\n  months: 'Januari_Februari_Maret_April_Mei_Juni_Juli_Agustus_September_Oktober_November_Desember'.split('_'),\n  monthsShort: 'Jan_Feb_Mar_Apr_Mei_Jun_Jul_Ags_Sep_Okt_Nov_Des'.split('_'),\n  weekdays: 'Minggu_Senin_Selasa_Rabu_Kamis_Jumat_Sabtu'.split('_'),\n  weekdaysShort: 'Min_Sen_Sel_Rab_Kam_Jum_Sab'.split('_'),\n  weekdaysMin: 'Mg_Sn_Sl_Rb_Km_Jm_Sb'.split('_'),\n  longDateFormat: {\n    LT: 'HH.mm',\n    LTS: 'HH.mm.ss',\n    L: 'DD/MM/YYYY',\n    LL: 'D MMMM YYYY',\n    LLL: 'D MMMM YYYY [pukul] HH.mm',\n    LLLL: 'dddd, D MMMM YYYY [pukul] HH.mm'\n  },\n  meridiemParse: /pagi|siang|sore|malam/,\n  meridiemHour(hour, meridiem) {\n    if (hour === 12) {\n      hour = 0;\n    }\n    if (meridiem === 'pagi') {\n      return hour;\n    } else if (meridiem === 'siang') {\n      return hour >= 11 ? hour : hour + 12;\n    } else if (meridiem === 'sore' || meridiem === 'malam') {\n      return hour + 12;\n    }\n  },\n  meridiem(hours, minutes, isLower) {\n    if (hours < 11) {\n      return 'pagi';\n    } else if (hours < 15) {\n      return 'siang';\n    } else if (hours < 19) {\n      return 'sore';\n    } else {\n      return 'malam';\n    }\n  },\n  calendar: {\n    sameDay: '[Hari ini pukul] LT',\n    nextDay: '[Besok pukul] LT',\n    nextWeek: 'dddd [pukul] LT',\n    lastDay: '[Kemarin pukul] LT',\n    lastWeek: 'dddd [lalu pukul] LT',\n    sameElse: 'L'\n  },\n  relativeTime: {\n    future: 'dalam %s',\n    past: '%s yang lalu',\n    s: 'beberapa detik',\n    ss: '%d detik',\n    m: 'semenit',\n    mm: '%d menit',\n    h: 'sejam',\n    hh: '%d jam',\n    d: 'sehari',\n    dd: '%d hari',\n    M: 'sebulan',\n    MM: '%d bulan',\n    y: 'setahun',\n    yy: '%d tahun'\n  },\n  week: {\n    dow: 1,\n    doy: 7 // The week that contains Jan 1st is the first week of the year.\n  }\n};\n\n//! moment.js locale configuration\n//! locale : Italian [it]\n//! author : Lorenzo : https://github.com/aliem\n//! author: Mattia Larentis: https://github.com/nostalgiaz\nconst itLocale = {\n  abbr: 'it',\n  months: 'gennaio_febbraio_marzo_aprile_maggio_giugno_luglio_agosto_settembre_ottobre_novembre_dicembre'.split('_'),\n  monthsShort: 'gen_feb_mar_apr_mag_giu_lug_ago_set_ott_nov_dic'.split('_'),\n  weekdays: 'domenica_lunedì_martedì_mercoledì_giovedì_venerdì_sabato'.split('_'),\n  weekdaysShort: 'dom_lun_mar_mer_gio_ven_sab'.split('_'),\n  weekdaysMin: 'do_lu_ma_me_gi_ve_sa'.split('_'),\n  longDateFormat: {\n    LT: 'HH:mm',\n    LTS: 'HH:mm:ss',\n    L: 'DD/MM/YYYY',\n    LL: 'D MMMM YYYY',\n    LLL: 'D MMMM YYYY HH:mm',\n    LLLL: 'dddd D MMMM YYYY HH:mm'\n  },\n  calendar: {\n    sameDay: '[Oggi alle] LT',\n    nextDay: '[Domani alle] LT',\n    nextWeek: 'dddd [alle] LT',\n    lastDay: '[Ieri alle] LT',\n    lastWeek(date) {\n      switch (getDayOfWeek(date)) {\n        case 0:\n          return '[la scorsa] dddd [alle] LT';\n        default:\n          return '[lo scorso] dddd [alle] LT';\n      }\n    },\n    sameElse: 'L'\n  },\n  relativeTime: {\n    future(num) {\n      return (/^[0-9].+$/.test(num.toString(10)) ? 'tra' : 'in') + ' ' + num;\n    },\n    past: '%s fa',\n    s: 'alcuni secondi',\n    ss: '%d secondi',\n    m: 'un minuto',\n    mm: '%d minuti',\n    h: 'un\\'ora',\n    hh: '%d ore',\n    d: 'un giorno',\n    dd: '%d giorni',\n    M: 'un mese',\n    MM: '%d mesi',\n    y: 'un anno',\n    yy: '%d anni'\n  },\n  dayOfMonthOrdinalParse: /\\d{1,2}º/,\n  ordinal: '%dº',\n  week: {\n    dow: 1,\n    doy: 4 // The week that contains Jan 4th is the first week of the year.\n  }\n};\n\n//! moment.js locale configuration\n//! locale : Japanese [ja]\n//! author : LI Long : https://github.com/baryon\nconst jaLocale = {\n  abbr: 'ja',\n  months: '1月_2月_3月_4月_5月_6月_7月_8月_9月_10月_11月_12月'.split('_'),\n  monthsShort: '1月_2月_3月_4月_5月_6月_7月_8月_9月_10月_11月_12月'.split('_'),\n  weekdays: '日曜日_月曜日_火曜日_水曜日_木曜日_金曜日_土曜日'.split('_'),\n  weekdaysShort: '日_月_火_水_木_金_土'.split('_'),\n  weekdaysMin: '日_月_火_水_木_金_土'.split('_'),\n  longDateFormat: {\n    LT: 'HH:mm',\n    LTS: 'HH:mm:ss',\n    L: 'YYYY/MM/DD',\n    LL: 'YYYY年M月D日',\n    LLL: 'YYYY年M月D日 HH:mm',\n    LLLL: 'YYYY年M月D日 HH:mm dddd',\n    l: 'YYYY/MM/DD',\n    ll: 'YYYY年M月D日',\n    lll: 'YYYY年M月D日 HH:mm',\n    llll: 'YYYY年M月D日 HH:mm dddd'\n  },\n  meridiemParse: /午前|午後/i,\n  isPM(input) {\n    return input === '午後';\n  },\n  meridiem(hour, minute, isLower) {\n    if (hour < 12) {\n      return '午前';\n    } else {\n      return '午後';\n    }\n  },\n  calendar: {\n    sameDay: '[今日] LT',\n    nextDay: '[明日] LT',\n    nextWeek: '[来週]dddd LT',\n    lastDay: '[昨日] LT',\n    lastWeek: '[前週]dddd LT',\n    sameElse: 'L'\n  },\n  dayOfMonthOrdinalParse: /\\d{1,2}日/,\n  ordinal(num, period) {\n    switch (period) {\n      case 'd':\n      case 'D':\n      case 'DDD':\n        return num + '日';\n      default:\n        return num.toString(10);\n    }\n  },\n  relativeTime: {\n    future: '%s後',\n    past: '%s前',\n    s: '数秒',\n    ss: '%d秒',\n    m: '1分',\n    mm: '%d分',\n    h: '1時間',\n    hh: '%d時間',\n    d: '1日',\n    dd: '%d日',\n    M: '1ヶ月',\n    MM: '%dヶ月',\n    y: '1年',\n    yy: '%d年'\n  }\n};\n\n//! moment.js locale configuration\n//! locale : Georgian [ka]\n//! author : Irakli Janiashvili : https://github.com/irakli-janiashvili\n//! author : Levan Tskipuri : https://github.com/tskipa\nconst kaLocale = {\n  abbr: 'ka',\n  months: {\n    format: 'იანვარს_თებერვალს_მარტს_აპრილის_მაისს_ივნისს_ივლისს_აგვისტს_სექტემბერს_ოქტომბერს_ნოემბერს_დეკემბერს'.split('_'),\n    standalone: 'იანვარი_თებერვალი_მარტი_აპრილი_მაისი_ივნისი_ივლისი_აგვისტო_სექტემბერი_ოქტომბერი_ნოემბერი_დეკემბერი'.split('_')\n  },\n  monthsShort: 'იან_თებ_მარ_აპრ_მაი_ივნ_ივლ_აგვ_სექ_ოქტ_ნოე_დეკ'.split('_'),\n  weekdays: {\n    standalone: 'კვირა_ორშაბათი_სამშაბათი_ოთხშაბათი_ხუთშაბათი_პარასკევი_შაბათი'.split('_'),\n    format: 'კვირას_ორშაბათს_სამშაბათს_ოთხშაბათს_ხუთშაბათს_პარასკევს_შაბათს'.split('_'),\n    isFormat: /(წინა|შემდეგ)/\n  },\n  weekdaysShort: 'კვი_ორშ_სამ_ოთხ_ხუთ_პარ_შაბ'.split('_'),\n  weekdaysMin: 'კვ_ორ_სა_ოთ_ხუ_პა_შა'.split('_'),\n  longDateFormat: {\n    LT: 'h:mm A',\n    LTS: 'h:mm:ss A',\n    L: 'DD/MM/YYYY',\n    LL: 'D MMMM YYYY',\n    LLL: 'D MMMM YYYY h:mm A',\n    LLLL: 'dddd, D MMMM YYYY h:mm A'\n  },\n  calendar: {\n    sameDay: '[დღეს] LT[-ზე]',\n    nextDay: '[ხვალ] LT[-ზე]',\n    lastDay: '[გუშინ] LT[-ზე]',\n    nextWeek: '[შემდეგ] dddd LT[-ზე]',\n    lastWeek: '[წინა] dddd LT-ზე',\n    sameElse: 'L'\n  },\n  relativeTime: {\n    future(s) {\n      var st = s.toString();\n      return /(წამი|წუთი|საათი|წელი)/.test(st) ? st.replace(/ი$/, 'ში') : st + 'ში';\n    },\n    past(s) {\n      var st = s.toString();\n      if (/(წამი|წუთი|საათი|დღე|თვე)/.test(st)) {\n        return st.replace(/(ი|ე)$/, 'ის წინ');\n      }\n      if (/წელი/.test(st)) {\n        return st.replace(/წელი$/, 'წლის წინ');\n      }\n    },\n    s: 'რამდენიმე წამი',\n    ss: '%d წამი',\n    m: 'წუთი',\n    mm: '%d წუთი',\n    h: 'საათი',\n    hh: '%d საათი',\n    d: 'დღე',\n    dd: '%d დღე',\n    M: 'თვე',\n    MM: '%d თვე',\n    y: 'წელი',\n    yy: '%d წელი'\n  },\n  dayOfMonthOrdinalParse: /0|1-ლი|მე-\\d{1,2}|\\d{1,2}-ე/,\n  ordinal(_num, _period) {\n    const num = Number(_num);\n    if (num === 0) {\n      return num.toString();\n    }\n    if (num === 1) {\n      return num + '-ლი';\n    }\n    if (num < 20 || num <= 100 && num % 20 === 0 || num % 100 === 0) {\n      return 'მე-' + num;\n    }\n    return num + '-ე';\n  },\n  week: {\n    dow: 1,\n    doy: 4\n  }\n};\n\n// ! moment.js locale configuration\n// ! locale : Kazakh [kk]\n// ! authors : Nurlan Rakhimzhanov : https://github.com/nurlan\nconst suffixes$1 = {\n  0: '-ші',\n  1: '-ші',\n  2: '-ші',\n  3: '-ші',\n  4: '-ші',\n  5: '-ші',\n  6: '-шы',\n  7: '-ші',\n  8: '-ші',\n  9: '-шы',\n  10: '-шы',\n  20: '-шы',\n  30: '-шы',\n  40: '-шы',\n  50: '-ші',\n  60: '-шы',\n  70: '-ші',\n  80: '-ші',\n  90: '-шы',\n  100: '-ші'\n};\nconst kkLocale = {\n  abbr: 'kk',\n  months: 'қаңтар_ақпан_наурыз_сәуір_мамыр_маусым_шілде_тамыз_қыркүйек_қазан_қараша_желтоқсан'.split('_'),\n  monthsShort: 'қаң_ақп_нау_сәу_мам_мау_шіл_там_қыр_қаз_қар_жел'.split('_'),\n  weekdays: 'жексенбі_дүйсенбі_сейсенбі_сәрсенбі_бейсенбі_жұма_сенбі'.split('_'),\n  weekdaysShort: 'жек_дүй_сей_сәр_бей_жұм_сен'.split('_'),\n  weekdaysMin: 'жк_дй_сй_ср_бй_жм_сн'.split('_'),\n  longDateFormat: {\n    LT: 'HH:mm',\n    LTS: 'HH:mm:ss',\n    L: 'DD.MM.YYYY',\n    LL: 'D MMMM YYYY',\n    LLL: 'D MMMM YYYY HH:mm',\n    LLLL: 'dddd, D MMMM YYYY HH:mm'\n  },\n  calendar: {\n    sameDay: '[Бүгін сағат] LT',\n    nextDay: '[Ертең сағат] LT',\n    nextWeek: 'dddd [сағат] LT',\n    lastDay: '[Кеше сағат] LT',\n    lastWeek: '[Өткен аптаның] dddd [сағат] LT',\n    sameElse: 'L'\n  },\n  relativeTime: {\n    future: '%s ішінде',\n    past: '%s бұрын',\n    s: 'бірнеше секунд',\n    ss: '%d секунд',\n    m: 'бір минут',\n    mm: '%d минут',\n    h: 'бір сағат',\n    hh: '%d сағат',\n    d: 'бір күн',\n    dd: '%d күн',\n    M: 'бір ай',\n    MM: '%d ай',\n    y: 'бір жыл',\n    yy: '%d жыл'\n  },\n  dayOfMonthOrdinalParse: /\\d{1,2}-(ші|шы)/,\n  ordinal(_num) {\n    const a = _num % 10;\n    const b = _num >= 100 ? 100 : null;\n    return _num + (suffixes$1[_num] || suffixes$1[a] || suffixes$1[b]);\n  },\n  week: {\n    dow: 1,\n    doy: 7 // The week that contains Jan 7th is the first week of the year.\n  }\n};\n\n//! moment.js locale configuration\n//! locale : Korean [ko]\n//! author : Kyungwook, Park : https://github.com/kyungw00k\n//! author : Jeeeyul Lee <<EMAIL>>\nconst koLocale = {\n  abbr: 'ko',\n  months: '1월_2월_3월_4월_5월_6월_7월_8월_9월_10월_11월_12월'.split('_'),\n  monthsShort: '1월_2월_3월_4월_5월_6월_7월_8월_9월_10월_11월_12월'.split('_'),\n  weekdays: '일요일_월요일_화요일_수요일_목요일_금요일_토요일'.split('_'),\n  weekdaysShort: '일_월_화_수_목_금_토'.split('_'),\n  weekdaysMin: '일_월_화_수_목_금_토'.split('_'),\n  longDateFormat: {\n    LT: 'A h:mm',\n    LTS: 'A h:mm:ss',\n    L: 'YYYY.MM.DD',\n    LL: 'YYYY년 MMMM D일',\n    LLL: 'YYYY년 MMMM D일 A h:mm',\n    LLLL: 'YYYY년 MMMM D일 dddd A h:mm',\n    l: 'YYYY.MM.DD',\n    ll: 'YYYY년 MMMM D일',\n    lll: 'YYYY년 MMMM D일 A h:mm',\n    llll: 'YYYY년 MMMM D일 dddd A h:mm'\n  },\n  calendar: {\n    sameDay: '오늘 LT',\n    nextDay: '내일 LT',\n    nextWeek: 'dddd LT',\n    lastDay: '어제 LT',\n    lastWeek: '지난주 dddd LT',\n    sameElse: 'L'\n  },\n  relativeTime: {\n    future: '%s 후',\n    past: '%s 전',\n    s: '몇 초',\n    ss: '%d초',\n    m: '1분',\n    mm: '%d분',\n    h: '한 시간',\n    hh: '%d시간',\n    d: '하루',\n    dd: '%d일',\n    M: '한 달',\n    MM: '%d달',\n    y: '일 년',\n    yy: '%d년'\n  },\n  dayOfMonthOrdinalParse: /\\d{1,2}(일|월|주)/,\n  ordinal: function (num, period) {\n    switch (period) {\n      case 'd':\n      case 'D':\n      case 'DDD':\n        return num + '일';\n      case 'M':\n        return num + '월';\n      case 'w':\n      case 'W':\n        return num + '주';\n      default:\n        return num.toString(10);\n    }\n  },\n  meridiemParse: /오전|오후/,\n  isPM: function (token) {\n    return token === '오후';\n  },\n  meridiem: function (hour, minute, isUpper) {\n    return hour < 12 ? '오전' : '오후';\n  }\n};\n\n//! moment.js locale configuration\n//! locale : Lithuanian [lt]\n//! author : Stanislavas Guk : https://github.com/ixoster\nconst units = {\n  ss: 'sekundė_sekundžių_sekundes',\n  m: 'minutė_minutės_minutę',\n  mm: 'minutės_minučių_minutes',\n  h: 'valanda_valandos_valandą',\n  hh: 'valandos_valandų_valandas',\n  d: 'diena_dienos_dieną',\n  dd: 'dienos_dienų_dienas',\n  M: 'mėnuo_mėnesio_mėnesį',\n  MM: 'mėnesiai_mėnesių_mėnesius',\n  y: 'metai_metų_metus',\n  yy: 'metai_metų_metus'\n};\nfunction translateSeconds(num, withoutSuffix, key, isFuture) {\n  if (withoutSuffix) {\n    return 'kelios sekundės';\n  } else {\n    return isFuture ? 'kelių sekundžių' : 'kelias sekundes';\n  }\n}\nfunction translateSingular(num, withoutSuffix, key, isFuture) {\n  return withoutSuffix ? forms(key)[0] : isFuture ? forms(key)[1] : forms(key)[2];\n}\nfunction special(num) {\n  return num % 10 === 0 || num > 10 && num < 20;\n}\nfunction forms(key) {\n  return units[key].split('_');\n}\nfunction translate$3(num, withoutSuffix, key, isFuture) {\n  let result = num + ' ';\n  if (num === 1) {\n    return result + translateSingular(num, withoutSuffix, key[0], isFuture);\n  } else if (withoutSuffix) {\n    return result + (special(num) ? forms(key)[1] : forms(key)[0]);\n  } else {\n    if (isFuture) {\n      return result + forms(key)[1];\n    } else {\n      return result + (special(num) ? forms(key)[1] : forms(key)[2]);\n    }\n  }\n}\nconst ltLocale = {\n  abbr: 'lt',\n  months: {\n    format: 'sausio_vasario_kovo_balandžio_gegužės_birželio_liepos_rugpjūčio_rugsėjo_spalio_lapkričio_gruodžio'.split('_'),\n    standalone: 'sausis_vasaris_kovas_balandis_gegužė_birželis_liepa_rugpjūtis_rugsėjis_spalis_lapkritis_gruodis'.split('_'),\n    isFormat: /D[oD]?(\\[[^\\[\\]]*\\]|\\s)+MMMM?|MMMM?(\\[[^\\[\\]]*\\]|\\s)+D[oD]?/\n  },\n  monthsShort: 'sau_vas_kov_bal_geg_bir_lie_rgp_rgs_spa_lap_grd'.split('_'),\n  weekdays: {\n    format: 'sekmadienį_pirmadienį_antradienį_trečiadienį_ketvirtadienį_penktadienį_šeštadienį'.split('_'),\n    standalone: 'sekmadienis_pirmadienis_antradienis_trečiadienis_ketvirtadienis_penktadienis_šeštadienis'.split('_'),\n    isFormat: /dddd HH:mm/\n  },\n  weekdaysShort: 'Sek_Pir_Ant_Tre_Ket_Pen_Šeš'.split('_'),\n  weekdaysMin: 'S_P_A_T_K_Pn_Š'.split('_'),\n  weekdaysParseExact: true,\n  longDateFormat: {\n    LT: 'HH:mm',\n    LTS: 'HH:mm:ss',\n    L: 'YYYY-MM-DD',\n    LL: 'YYYY [m.] MMMM D [d.]',\n    LLL: 'YYYY [m.] MMMM D [d.], HH:mm [val.]',\n    LLLL: 'YYYY [m.] MMMM D [d.], dddd, HH:mm [val.]',\n    l: 'YYYY-MM-DD',\n    ll: 'YYYY [m.] MMMM D [d.]',\n    lll: 'YYYY [m.] MMMM D [d.], HH:mm [val.]',\n    llll: 'YYYY [m.] MMMM D [d.], ddd, HH:mm [val.]'\n  },\n  calendar: {\n    sameDay: '[Šiandien] LT',\n    nextDay: '[Rytoj] LT',\n    nextWeek: 'dddd LT',\n    lastDay: '[Vakar] LT',\n    lastWeek: '[Praėjusį] dddd LT',\n    sameElse: 'L'\n  },\n  relativeTime: {\n    future: 'po %s',\n    past: 'prieš %s',\n    s: translateSeconds,\n    ss: translate$3,\n    m: translateSingular,\n    mm: translate$3,\n    h: translateSingular,\n    hh: translate$3,\n    d: translateSingular,\n    dd: translate$3,\n    M: translateSingular,\n    MM: translate$3,\n    y: translateSingular,\n    yy: translate$3\n  },\n  dayOfMonthOrdinalParse: /\\d{1,2}-oji/,\n  ordinal(num) {\n    return num + '-oji';\n  },\n  week: {\n    dow: 1,\n    doy: 4 // The week that contains Jan 4th is the first week of the year.\n  }\n};\n\n//! moment.js locale configuration\n//! locale : Latvian [lv]\n//! author : Matiss Janis Aboltins : https://github.com/matissjanis\nconst lvLocale = {\n  abbr: 'lv',\n  months: 'Janvāris_Februāris_Marts_Aprīlis_Maijs_Jūnijs_Jūlijs_Augusts_Septembris_Oktobris_Novembris_Decembris'.split('_'),\n  monthsShort: 'Jan_Feb_Mar_Apr_Mai_Jūn_Jūl_Aug_Sep_Okt_Nov_Dec'.split('_'),\n  weekdays: 'Svētdiena_Pirmdiena_Otrdiena_Trešdiena_Ceturtdiena_Piektdiena_Sestdiena'.split('_'),\n  weekdaysShort: 'Svētd_Pirmd_Otrd_Trešd_Ceturtd_Piektd_Sestd'.split('_'),\n  weekdaysMin: 'Sv_Pi_Ot_Tr_Ce_Pk_Se'.split('_'),\n  longDateFormat: {\n    LT: 'HH:mm',\n    LTS: 'HH:mm:ss',\n    L: 'DD/MM/YYYY',\n    LL: 'D MMMM YYYY',\n    LLL: 'D MMMM YYYY HH:mm',\n    LLLL: 'dddd, D MMMM YYYY HH:mm'\n  },\n  calendar: {\n    sameDay: '[Today at] LT',\n    nextDay: '[Tomorrow at] LT',\n    nextWeek: 'dddd [at] LT',\n    lastDay: '[Yesterday at] LT',\n    lastWeek: '[Last] dddd [at] LT',\n    sameElse: 'L'\n  },\n  relativeTime: {\n    future: 'pēc %s',\n    past: 'pirms %s',\n    s: 'dažām sekundēm',\n    ss: '%d sekundēm',\n    m: 'minūtes',\n    mm: '%d minūtēm',\n    h: 'stundas',\n    hh: '%d stundām',\n    d: 'dienas',\n    dd: '%d dienām',\n    M: 'mēneša',\n    MM: '%d mēnešiem',\n    y: 'gada',\n    yy: '%d gadiem'\n  },\n  dayOfMonthOrdinalParse: /\\d{1,2}\\./,\n  ordinal(num) {\n    return num + '.';\n  },\n  week: {\n    dow: 1,\n    doy: 4 // The week that contains Jan 4th is the first week of the year.\n  }\n};\n\n//! moment.js locale configuration\n//! locale : Mongolian [mn]\n//! author : Javkhlantugs Nyamdorj : https://github.com/javkhaanj7\nfunction translate$2(num, withoutSuffix, key, isFuture) {\n  switch (key) {\n    case 's':\n      return withoutSuffix ? 'хэдхэн секунд' : 'хэдхэн секундын';\n    case 'ss':\n      return num + (withoutSuffix ? ' секунд' : ' секундын');\n    case 'm':\n    case 'mm':\n      return num + (withoutSuffix ? ' минут' : ' минутын');\n    case 'h':\n    case 'hh':\n      return num + (withoutSuffix ? ' цаг' : ' цагийн');\n    case 'd':\n    case 'dd':\n      return num + (withoutSuffix ? ' өдөр' : ' өдрийн');\n    case 'M':\n    case 'MM':\n      return num + (withoutSuffix ? ' сар' : ' сарын');\n    case 'y':\n    case 'yy':\n      return num + (withoutSuffix ? ' жил' : ' жилийн');\n    default:\n      return num.toString(10);\n  }\n}\nconst mnLocale = {\n  abbr: 'mn',\n  months: 'Нэгдүгээр сар_Хоёрдугаар сар_Гуравдугаар сар_Дөрөвдүгээр сар_Тавдугаар сар_Зургадугаар сар_Долдугаар сар_Наймдугаар сар_Есдүгээр сар_Аравдугаар сар_Арван нэгдүгээр сар_Арван хоёрдугаар сар'.split('_'),\n  monthsShort: '1 сар_2 сар_3 сар_4 сар_5 сар_6 сар_7 сар_8 сар_9 сар_10 сар_11 сар_12 сар'.split('_'),\n  monthsParseExact: true,\n  weekdays: 'Ням_Даваа_Мягмар_Лхагва_Пүрэв_Баасан_Бямба'.split('_'),\n  weekdaysShort: 'Ням_Дав_Мяг_Лха_Пүр_Баа_Бям'.split('_'),\n  weekdaysMin: 'Ня_Да_Мя_Лх_Пү_Ба_Бя'.split('_'),\n  weekdaysParseExact: true,\n  longDateFormat: {\n    LT: 'HH:mm',\n    LTS: 'HH:mm:ss',\n    L: 'YYYY-MM-DD',\n    LL: 'YYYY оны MMMMын D',\n    LLL: 'YYYY оны MMMMын D HH:mm',\n    LLLL: 'dddd, YYYY оны MMMMын D HH:mm'\n  },\n  meridiemParse: /ҮӨ|ҮХ/i,\n  isPM: function (input) {\n    return input === 'ҮХ';\n  },\n  meridiem: function (hour, minute, isLower) {\n    if (hour < 12) {\n      return 'ҮӨ';\n    } else {\n      return 'ҮХ';\n    }\n  },\n  calendar: {\n    sameDay: '[Өнөөдөр] LT',\n    nextDay: '[Маргааш] LT',\n    nextWeek: '[Ирэх] dddd LT',\n    lastDay: '[Өчигдөр] LT',\n    lastWeek: '[Өнгөрсөн] dddd LT',\n    sameElse: 'L'\n  },\n  relativeTime: {\n    future: '%s дараа',\n    past: '%s өмнө',\n    s: translate$2,\n    ss: translate$2,\n    m: translate$2,\n    mm: translate$2,\n    h: translate$2,\n    hh: translate$2,\n    d: translate$2,\n    dd: translate$2,\n    M: translate$2,\n    MM: translate$2,\n    y: translate$2,\n    yy: translate$2\n  },\n  dayOfMonthOrdinalParse: /\\d{1,2} өдөр/,\n  ordinal: function (num, period) {\n    switch (period) {\n      case 'd':\n      case 'D':\n      case 'DDD':\n        return num + ' өдөр';\n      default:\n        return num.toString(10);\n    }\n  }\n};\n\n//! moment.js locale configuration\n//! locale : Norwegian Bokmål [nb]\n//! authors : Espen Hovlandsdal : https://github.com/rexxars\n//!           Sigurd Gartmann : https://github.com/sigurdga\nconst nbLocale = {\n  abbr: 'nb',\n  months: 'januar_februar_mars_april_mai_juni_juli_august_september_oktober_november_desember'.split('_'),\n  monthsShort: 'jan._feb._mars_april_mai_juni_juli_aug._sep._okt._nov._des.'.split('_'),\n  monthsParseExact: true,\n  weekdays: 'søndag_mandag_tirsdag_onsdag_torsdag_fredag_lørdag'.split('_'),\n  weekdaysShort: 'sø._ma._ti._on._to._fr._lø.'.split('_'),\n  weekdaysMin: 'sø_ma_ti_on_to_fr_lø'.split('_'),\n  weekdaysParseExact: true,\n  longDateFormat: {\n    LT: 'HH:mm',\n    LTS: 'HH:mm:ss',\n    L: 'DD.MM.YYYY',\n    LL: 'D. MMMM YYYY',\n    LLL: 'D. MMMM YYYY [kl.] HH:mm',\n    LLLL: 'dddd D. MMMM YYYY [kl.] HH:mm'\n  },\n  calendar: {\n    sameDay: '[i dag kl.] LT',\n    nextDay: '[i morgen kl.] LT',\n    nextWeek: 'dddd [kl.] LT',\n    lastDay: '[i går kl.] LT',\n    lastWeek: '[forrige] dddd [kl.] LT',\n    sameElse: 'L'\n  },\n  relativeTime: {\n    future: 'om %s',\n    past: '%s siden',\n    s: 'noen sekunder',\n    ss: '%d sekunder',\n    m: 'ett minutt',\n    mm: '%d minutter',\n    h: 'en time',\n    hh: '%d timer',\n    d: 'en dag',\n    dd: '%d dager',\n    M: 'en måned',\n    MM: '%d måneder',\n    y: 'ett år',\n    yy: '%d år'\n  },\n  dayOfMonthOrdinalParse: /\\d{1,2}\\./,\n  ordinal: '%d.',\n  week: {\n    dow: 1,\n    doy: 4 // The week that contains Jan 4th is the first week of the year.\n  }\n};\n\n//! moment.js locale configuration\n//! locale : Dutch (Belgium) [nl-be]\n//! author : Joris Röling : https://github.com/jorisroling\n//! author : Jacob Middag : https://github.com/middagj\nlet monthsShortWithDots$1 = 'jan._feb._mrt._apr._mei_jun._jul._aug._sep._okt._nov._dec.'.split('_');\nlet monthsShortWithoutDots$1 = 'jan_feb_mrt_apr_mei_jun_jul_aug_sep_okt_nov_dec'.split('_');\nlet monthsParse$2 = [/^jan/i, /^feb/i, /^maart|mrt.?$/i, /^apr/i, /^mei$/i, /^jun[i.]?$/i, /^jul[i.]?$/i, /^aug/i, /^sep/i, /^okt/i, /^nov/i, /^dec/i];\nlet monthsRegex$1 = /^(januari|februari|maart|april|mei|april|ju[nl]i|augustus|september|oktober|november|december|jan\\.?|feb\\.?|mrt\\.?|apr\\.?|ju[nl]\\.?|aug\\.?|sep\\.?|okt\\.?|nov\\.?|dec\\.?)/i;\nconst nlBeLocale = {\n  abbr: 'nl-be',\n  months: 'januari_februari_maart_april_mei_juni_juli_augustus_september_oktober_november_december'.split('_'),\n  monthsShort(date, format, isUTC) {\n    if (!date) {\n      return monthsShortWithDots$1;\n    } else if (/-MMM-/.test(format)) {\n      return monthsShortWithoutDots$1[getMonth(date, isUTC)];\n    } else {\n      return monthsShortWithDots$1[getMonth(date, isUTC)];\n    }\n  },\n  monthsRegex: monthsRegex$1,\n  monthsShortRegex: monthsRegex$1,\n  monthsStrictRegex: /^(januari|februari|maart|mei|ju[nl]i|april|augustus|september|oktober|november|december)/i,\n  monthsShortStrictRegex: /^(jan\\.?|feb\\.?|mrt\\.?|apr\\.?|mei|ju[nl]\\.?|aug\\.?|sep\\.?|okt\\.?|nov\\.?|dec\\.?)/i,\n  monthsParse: monthsParse$2,\n  longMonthsParse: monthsParse$2,\n  shortMonthsParse: monthsParse$2,\n  weekdays: 'zondag_maandag_dinsdag_woensdag_donderdag_vrijdag_zaterdag'.split('_'),\n  weekdaysShort: 'zo._ma._di._wo._do._vr._za.'.split('_'),\n  weekdaysMin: 'zo_ma_di_wo_do_vr_za'.split('_'),\n  weekdaysParseExact: true,\n  longDateFormat: {\n    LT: 'HH:mm',\n    LTS: 'HH:mm:ss',\n    L: 'DD/MM/YYYY',\n    LL: 'D MMMM YYYY',\n    LLL: 'D MMMM YYYY HH:mm',\n    LLLL: 'dddd D MMMM YYYY HH:mm'\n  },\n  calendar: {\n    sameDay: '[vandaag om] LT',\n    nextDay: '[morgen om] LT',\n    nextWeek: 'dddd [om] LT',\n    lastDay: '[gisteren om] LT',\n    lastWeek: '[afgelopen] dddd [om] LT',\n    sameElse: 'L'\n  },\n  relativeTime: {\n    future: 'over %s',\n    past: '%s geleden',\n    s: 'een paar seconden',\n    ss: '%d seconden',\n    m: 'één minuut',\n    mm: '%d minuten',\n    h: 'één uur',\n    hh: '%d uur',\n    d: 'één dag',\n    dd: '%d dagen',\n    M: 'één maand',\n    MM: '%d maanden',\n    y: 'één jaar',\n    yy: '%d jaar'\n  },\n  dayOfMonthOrdinalParse: /\\d{1,2}(ste|de)/,\n  ordinal(_num) {\n    const num = Number(_num);\n    return num + (num === 1 || num === 8 || num >= 20 ? 'ste' : 'de');\n  },\n  week: {\n    dow: 1,\n    doy: 4 // The week that contains Jan 4th is the first week of the year.\n  }\n};\n\n//! moment.js locale configuration\n//! locale : Dutch [nl]\n//! author : Joris Röling : https://github.com/jorisroling\n//! author : Jacob Middag : https://github.com/middagj\nlet monthsShortWithDots = 'jan._feb._mrt._apr._mei_jun._jul._aug._sep._okt._nov._dec.'.split('_'),\n  monthsShortWithoutDots = 'jan_feb_mrt_apr_mei_jun_jul_aug_sep_okt_nov_dec'.split('_');\nlet monthsParse$1 = [/^jan/i, /^feb/i, /^maart|mrt.?$/i, /^apr/i, /^mei$/i, /^jun[i.]?$/i, /^jul[i.]?$/i, /^aug/i, /^sep/i, /^okt/i, /^nov/i, /^dec/i];\nlet monthsRegex = /^(januari|februari|maart|april|mei|april|ju[nl]i|augustus|september|oktober|november|december|jan\\.?|feb\\.?|mrt\\.?|apr\\.?|ju[nl]\\.?|aug\\.?|sep\\.?|okt\\.?|nov\\.?|dec\\.?)/i;\nconst nlLocale = {\n  abbr: 'nl',\n  months: 'januari_februari_maart_april_mei_juni_juli_augustus_september_oktober_november_december'.split('_'),\n  monthsShort(date, format, isUTC) {\n    if (!date) {\n      return monthsShortWithDots;\n    } else if (/-MMM-/.test(format)) {\n      return monthsShortWithoutDots[getMonth(date, isUTC)];\n    } else {\n      return monthsShortWithDots[getMonth(date, isUTC)];\n    }\n  },\n  monthsRegex,\n  monthsShortRegex: monthsRegex,\n  monthsStrictRegex: /^(januari|februari|maart|mei|ju[nl]i|april|augustus|september|oktober|november|december)/i,\n  monthsShortStrictRegex: /^(jan\\.?|feb\\.?|mrt\\.?|apr\\.?|mei|ju[nl]\\.?|aug\\.?|sep\\.?|okt\\.?|nov\\.?|dec\\.?)/i,\n  monthsParse: monthsParse$1,\n  longMonthsParse: monthsParse$1,\n  shortMonthsParse: monthsParse$1,\n  weekdays: 'zondag_maandag_dinsdag_woensdag_donderdag_vrijdag_zaterdag'.split('_'),\n  weekdaysShort: 'zo._ma._di._wo._do._vr._za.'.split('_'),\n  weekdaysMin: 'zo_ma_di_wo_do_vr_za'.split('_'),\n  weekdaysParseExact: true,\n  longDateFormat: {\n    LT: 'HH:mm',\n    LTS: 'HH:mm:ss',\n    L: 'DD-MM-YYYY',\n    LL: 'D MMMM YYYY',\n    LLL: 'D MMMM YYYY HH:mm',\n    LLLL: 'dddd D MMMM YYYY HH:mm'\n  },\n  calendar: {\n    sameDay: '[vandaag om] LT',\n    nextDay: '[morgen om] LT',\n    nextWeek: 'dddd [om] LT',\n    lastDay: '[gisteren om] LT',\n    lastWeek: '[afgelopen] dddd [om] LT',\n    sameElse: 'L'\n  },\n  relativeTime: {\n    future: 'over %s',\n    past: '%s geleden',\n    s: 'een paar seconden',\n    ss: '%d seconden',\n    m: 'één minuut',\n    mm: '%d minuten',\n    h: 'één uur',\n    hh: '%d uur',\n    d: 'één dag',\n    dd: '%d dagen',\n    M: 'één maand',\n    MM: '%d maanden',\n    y: 'één jaar',\n    yy: '%d jaar'\n  },\n  dayOfMonthOrdinalParse: /\\d{1,2}(ste|de)/,\n  ordinal(_num) {\n    const num = Number(_num);\n    return num + (num === 1 || num === 8 || num >= 20 ? 'ste' : 'de');\n  },\n  week: {\n    dow: 1,\n    doy: 4 // The week that contains Jan 4th is the first week of the year.\n  }\n};\n\n//! moment.js locale configuration\n//! locale : Polish [pl]\n//! author : Rafal Hirsz : https://github.com/evoL\nlet monthsNominative = 'styczeń_luty_marzec_kwiecień_maj_czerwiec_lipiec_sierpień_wrzesień_październik_listopad_grudzień'.split('_');\nlet monthsSubjective = 'stycznia_lutego_marca_kwietnia_maja_czerwca_lipca_sierpnia_września_października_listopada_grudnia'.split('_');\nfunction plural$3(num) {\n  return num % 10 < 5 && num % 10 > 1 && ~~(num / 10) % 10 !== 1;\n}\nfunction translate$1(num, withoutSuffix, key) {\n  let result = num + ' ';\n  switch (key) {\n    case 'ss':\n      return result + (plural$3(num) ? 'sekundy' : 'sekund');\n    case 'm':\n      return withoutSuffix ? 'minuta' : 'minutę';\n    case 'mm':\n      return result + (plural$3(num) ? 'minuty' : 'minut');\n    case 'h':\n      return withoutSuffix ? 'godzina' : 'godzinę';\n    case 'hh':\n      return result + (plural$3(num) ? 'godziny' : 'godzin');\n    case 'MM':\n      return result + (plural$3(num) ? 'miesiące' : 'miesięcy');\n    case 'yy':\n      return result + (plural$3(num) ? 'lata' : 'lat');\n  }\n}\nconst plLocale = {\n  abbr: 'pl',\n  months(date, format, isUTC) {\n    if (!date) {\n      return monthsNominative;\n    } else if (format === '') {\n      // Hack: if format empty we know this is used to generate\n      // RegExp by moment. Give then back both valid forms of months\n      // in RegExp ready format.\n      return '(' + monthsSubjective[getMonth(date, isUTC)] + '|' + monthsNominative[getMonth(date, isUTC)] + ')';\n    } else if (/D MMMM/.test(format)) {\n      return monthsSubjective[getMonth(date, isUTC)];\n    } else {\n      return monthsNominative[getMonth(date, isUTC)];\n    }\n  },\n  monthsShort: 'sty_lut_mar_kwi_maj_cze_lip_sie_wrz_paź_lis_gru'.split('_'),\n  weekdays: 'niedziela_poniedziałek_wtorek_środa_czwartek_piątek_sobota'.split('_'),\n  weekdaysShort: 'ndz_pon_wt_śr_czw_pt_sob'.split('_'),\n  weekdaysMin: 'Nd_Pn_Wt_Śr_Cz_Pt_So'.split('_'),\n  longDateFormat: {\n    LT: 'HH:mm',\n    LTS: 'HH:mm:ss',\n    L: 'DD.MM.YYYY',\n    LL: 'D MMMM YYYY',\n    LLL: 'D MMMM YYYY HH:mm',\n    LLLL: 'dddd, D MMMM YYYY HH:mm'\n  },\n  calendar: {\n    sameDay: '[Dziś o] LT',\n    nextDay: '[Jutro o] LT',\n    nextWeek(date) {\n      switch (getDayOfWeek(date)) {\n        case 0:\n          return '[W niedzielę o] LT';\n        case 2:\n          return '[We wtorek o] LT';\n        case 3:\n          return '[W środę o] LT';\n        case 5:\n          return '[W piątek o] LT';\n        case 6:\n          return '[W sobotę o] LT';\n        default:\n          return '[W] dddd [o] LT';\n      }\n    },\n    lastDay: '[Wczoraj o] LT',\n    lastWeek(date) {\n      switch (getDayOfWeek(date)) {\n        case 0:\n          return '[W zeszłą niedzielę o] LT';\n        case 3:\n          return '[W zeszłą środę o] LT';\n        case 4:\n          return '[W zeszłą czwartek o] LT';\n        case 5:\n          return '[W zeszłą piątek o] LT';\n        case 6:\n          return '[W zeszłą sobotę o] LT';\n        default:\n          return '[W zeszły] dddd [o] LT';\n      }\n    },\n    sameElse: 'L'\n  },\n  relativeTime: {\n    future: 'za %s',\n    past: '%s temu',\n    s: 'kilka sekund',\n    ss: translate$1,\n    m: translate$1,\n    mm: translate$1,\n    h: translate$1,\n    hh: translate$1,\n    d: '1 dzień',\n    dd: '%d dni',\n    M: 'miesiąc',\n    MM: translate$1,\n    y: 'rok',\n    yy: translate$1\n  },\n  dayOfMonthOrdinalParse: /\\d{1,2}\\./,\n  ordinal: '%d.',\n  week: {\n    dow: 1,\n    doy: 4 // The week that contains Jan 4th is the first week of the year.\n  }\n};\n\n//! moment.js locale configuration\n//! locale : Portuguese (Brazil) [pt-br]\n//! author : Caio Ribeiro Pereira : https://github.com/caio-ribeiro-pereira\nconst ptBrLocale = {\n  abbr: 'pt-br',\n  months: 'Janeiro_Fevereiro_Março_Abril_Maio_Junho_Julho_Agosto_Setembro_Outubro_Novembro_Dezembro'.split('_'),\n  monthsShort: 'Jan_Fev_Mar_Abr_Mai_Jun_Jul_Ago_Set_Out_Nov_Dez'.split('_'),\n  weekdays: 'Domingo_Segunda-feira_Terça-feira_Quarta-feira_Quinta-feira_Sexta-feira_Sábado'.split('_'),\n  weekdaysShort: 'Dom_Seg_Ter_Qua_Qui_Sex_Sáb'.split('_'),\n  weekdaysMin: 'Do_2ª_3ª_4ª_5ª_6ª_Sá'.split('_'),\n  weekdaysParseExact: true,\n  longDateFormat: {\n    LT: 'HH:mm',\n    LTS: 'HH:mm:ss',\n    L: 'DD/MM/YYYY',\n    LL: 'D [de] MMMM [de] YYYY',\n    LLL: 'D [de] MMMM [de] YYYY [às] HH:mm',\n    LLLL: 'dddd, D [de] MMMM [de] YYYY [às] HH:mm'\n  },\n  calendar: {\n    sameDay: '[Hoje às] LT',\n    nextDay: '[Amanhã às] LT',\n    nextWeek: 'dddd [às] LT',\n    lastDay: '[Ontem às] LT',\n    lastWeek(date) {\n      return getDayOfWeek(date) === 0 || getDayOfWeek(date) === 6 ? '[Último] dddd [às] LT' :\n      // Saturday + Sunday\n      '[Última] dddd [às] LT'; // Monday - Friday\n    },\n    sameElse: 'L'\n  },\n  relativeTime: {\n    future: 'em %s',\n    past: '%s atrás',\n    s: 'poucos segundos',\n    ss: '%d segundos',\n    m: 'um minuto',\n    mm: '%d minutos',\n    h: 'uma hora',\n    hh: '%d horas',\n    d: 'um dia',\n    dd: '%d dias',\n    M: 'um mês',\n    MM: '%d meses',\n    y: 'um ano',\n    yy: '%d anos'\n  },\n  dayOfMonthOrdinalParse: /\\d{1,2}º/,\n  ordinal: '%dº'\n};\n\n// ! moment.js locale configuration\n// ! locale : Romanian [ro]\n//! author : Vlad Gurdiga : https://github.com/gurdiga\n//! author : Valentin Agachi : https://github.com/avaly\n// ! author : Earle white: https://github.com/5earle\nfunction relativeTimeWithPlural$2(num, withoutSuffix, key) {\n  let format = {\n    ss: 'secunde',\n    mm: 'minute',\n    hh: 'ore',\n    dd: 'zile',\n    MM: 'luni',\n    yy: 'ani'\n  };\n  let separator = ' ';\n  if (num % 100 >= 20 || num >= 100 && num % 100 === 0) {\n    separator = ' de ';\n  }\n  return num + separator + format[key];\n}\nconst roLocale = {\n  abbr: 'ro',\n  months: 'ianuarie_februarie_martie_aprilie_mai_iunie_iulie_august_septembrie_octombrie_noiembrie_decembrie'.split('_'),\n  monthsShort: 'ian._febr._mart._apr._mai_iun._iul._aug._sept._oct._nov._dec.'.split('_'),\n  monthsParseExact: true,\n  weekdays: 'duminică_luni_marți_miercuri_joi_vineri_sâmbătă'.split('_'),\n  weekdaysShort: 'Dum_Lun_Mar_Mie_Joi_Vin_Sâm'.split('_'),\n  weekdaysMin: 'Du_Lu_Ma_Mi_Jo_Vi_Sâ'.split('_'),\n  longDateFormat: {\n    LT: 'H:mm',\n    LTS: 'H:mm:ss',\n    L: 'DD.MM.YYYY',\n    LL: 'D MMMM YYYY',\n    LLL: 'D MMMM YYYY H:mm',\n    LLLL: 'dddd, D MMMM YYYY H:mm'\n  },\n  calendar: {\n    sameDay: '[azi la] LT',\n    nextDay: '[mâine la] LT',\n    nextWeek: 'dddd [la] LT',\n    lastDay: '[ieri la] LT',\n    lastWeek: '[fosta] dddd [la] LT',\n    sameElse: 'L'\n  },\n  relativeTime: {\n    future: 'peste %s',\n    past: '%s în urmă',\n    s: 'câteva secunde',\n    ss: relativeTimeWithPlural$2,\n    m: 'un minut',\n    mm: relativeTimeWithPlural$2,\n    h: 'o oră',\n    hh: relativeTimeWithPlural$2,\n    d: 'o zi',\n    dd: relativeTimeWithPlural$2,\n    M: 'o lună',\n    MM: relativeTimeWithPlural$2,\n    y: 'un an',\n    yy: relativeTimeWithPlural$2\n  },\n  week: {\n    dow: 1,\n    doy: 7 // The week that contains Jan 1st is the first week of the year.\n  }\n};\n\n//! moment.js locale configuration\n//! locale : Russian [ru]\n//! author : Viktorminator : https://github.com/Viktorminator\n//! Author : Menelion Elensúle : https://github.com/Oire\n//! author : Коренберг Марк : https://github.com/socketpair\nfunction plural$2(word, num) {\n  let forms = word.split('_');\n  return num % 10 === 1 && num % 100 !== 11 ? forms[0] : num % 10 >= 2 && num % 10 <= 4 && (num % 100 < 10 || num % 100 >= 20) ? forms[1] : forms[2];\n}\nfunction relativeTimeWithPlural$1(num, withoutSuffix, key) {\n  let format = {\n    ss: withoutSuffix ? 'секунда_секунды_секунд' : 'секунду_секунды_секунд',\n    mm: withoutSuffix ? 'минута_минуты_минут' : 'минуту_минуты_минут',\n    hh: 'час_часа_часов',\n    dd: 'день_дня_дней',\n    MM: 'месяц_месяца_месяцев',\n    yy: 'год_года_лет'\n  };\n  if (key === 'm') {\n    return withoutSuffix ? 'минута' : 'минуту';\n  }\n  return num + ' ' + plural$2(format[key], +num);\n}\nlet monthsParse = [/^янв/i, /^фев/i, /^мар/i, /^апр/i, /^ма[йя]/i, /^июн/i, /^июл/i, /^авг/i, /^сен/i, /^окт/i, /^ноя/i, /^дек/i];\n// http://new.gramota.ru/spravka/rules/139-prop : § 103\n// Сокращения месяцев: http://new.gramota.ru/spravka/buro/search-answer?s=242637\n// CLDR data:          http://www.unicode.org/cldr/charts/28/summary/ru.html#1753\nconst ruLocale = {\n  abbr: 'ru',\n  months: {\n    format: 'января_февраля_марта_апреля_мая_июня_июля_августа_сентября_октября_ноября_декабря'.split('_'),\n    standalone: 'январь_февраль_март_апрель_май_июнь_июль_август_сентябрь_октябрь_ноябрь_декабрь'.split('_')\n  },\n  monthsShort: {\n    // по CLDR именно \"июл.\" и \"июн.\", но какой смысл менять букву на точку ?\n    format: 'янв._февр._мар._апр._мая_июня_июля_авг._сент._окт._нояб._дек.'.split('_'),\n    standalone: 'янв._февр._март_апр._май_июнь_июль_авг._сент._окт._нояб._дек.'.split('_')\n  },\n  weekdays: {\n    standalone: 'воскресенье_понедельник_вторник_среда_четверг_пятница_суббота'.split('_'),\n    format: 'воскресенье_понедельник_вторник_среду_четверг_пятницу_субботу'.split('_'),\n    isFormat: /\\[ ?[Вв] ?(?:прошлую|следующую|эту)? ?\\] ?dddd/\n  },\n  weekdaysShort: 'вс_пн_вт_ср_чт_пт_сб'.split('_'),\n  weekdaysMin: 'вс_пн_вт_ср_чт_пт_сб'.split('_'),\n  monthsParse,\n  longMonthsParse: monthsParse,\n  shortMonthsParse: monthsParse,\n  // полные названия с падежами, по три буквы, для некоторых, по 4 буквы, сокращения с точкой и без точки\n  monthsRegex: /^(январ[ья]|янв\\.?|феврал[ья]|февр?\\.?|марта?|мар\\.?|апрел[ья]|апр\\.?|ма[йя]|июн[ья]|июн\\.?|июл[ья]|июл\\.?|августа?|авг\\.?|сентябр[ья]|сент?\\.?|октябр[ья]|окт\\.?|ноябр[ья]|нояб?\\.?|декабр[ья]|дек\\.?)/i,\n  // копия предыдущего\n  monthsShortRegex: /^(январ[ья]|янв\\.?|феврал[ья]|февр?\\.?|марта?|мар\\.?|апрел[ья]|апр\\.?|ма[йя]|июн[ья]|июн\\.?|июл[ья]|июл\\.?|августа?|авг\\.?|сентябр[ья]|сент?\\.?|октябр[ья]|окт\\.?|ноябр[ья]|нояб?\\.?|декабр[ья]|дек\\.?)/i,\n  // полные названия с падежами\n  monthsStrictRegex: /^(январ[яь]|феврал[яь]|марта?|апрел[яь]|ма[яй]|июн[яь]|июл[яь]|августа?|сентябр[яь]|октябр[яь]|ноябр[яь]|декабр[яь])/i,\n  // Выражение, которое соотвествует только сокращённым формам\n  monthsShortStrictRegex: /^(янв\\.|февр?\\.|мар[т.]|апр\\.|ма[яй]|июн[ья.]|июл[ья.]|авг\\.|сент?\\.|окт\\.|нояб?\\.|дек\\.)/i,\n  longDateFormat: {\n    LT: 'H:mm',\n    LTS: 'H:mm:ss',\n    L: 'DD.MM.YYYY',\n    LL: 'D MMMM YYYY г.',\n    LLL: 'D MMMM YYYY г., H:mm',\n    LLLL: 'dddd, D MMMM YYYY г., H:mm'\n  },\n  calendar: {\n    sameDay: '[Сегодня в] LT',\n    nextDay: '[Завтра в] LT',\n    lastDay: '[Вчера в] LT',\n    nextWeek(date, now) {\n      if (getWeek(now) !== getWeek(date)) {\n        switch (getDayOfWeek(date)) {\n          case 0:\n            return '[В следующее] dddd [в] LT';\n          case 1:\n          case 2:\n          case 4:\n            return '[В следующий] dddd [в] LT';\n          case 3:\n          case 5:\n          case 6:\n            return '[В следующую] dddd [в] LT';\n        }\n      } else {\n        if (getDayOfWeek(date) === 2) {\n          return '[Во] dddd [в] LT';\n        } else {\n          return '[В] dddd [в] LT';\n        }\n      }\n    },\n    lastWeek(date, now) {\n      if (getWeek(now) !== getWeek(date)) {\n        switch (getDayOfWeek(date)) {\n          case 0:\n            return '[В прошлое] dddd [в] LT';\n          case 1:\n          case 2:\n          case 4:\n            return '[В прошлый] dddd [в] LT';\n          case 3:\n          case 5:\n          case 6:\n            return '[В прошлую] dddd [в] LT';\n        }\n      } else {\n        if (getDayOfWeek(date) === 2) {\n          return '[Во] dddd [в] LT';\n        } else {\n          return '[В] dddd [в] LT';\n        }\n      }\n    },\n    sameElse: 'L'\n  },\n  relativeTime: {\n    future: 'через %s',\n    past: '%s назад',\n    s: 'несколько секунд',\n    ss: relativeTimeWithPlural$1,\n    m: relativeTimeWithPlural$1,\n    mm: relativeTimeWithPlural$1,\n    h: 'час',\n    hh: relativeTimeWithPlural$1,\n    d: 'день',\n    dd: relativeTimeWithPlural$1,\n    M: 'месяц',\n    MM: relativeTimeWithPlural$1,\n    y: 'год',\n    yy: relativeTimeWithPlural$1\n  },\n  meridiemParse: /ночи|утра|дня|вечера/i,\n  isPM(input) {\n    return /^(дня|вечера)$/.test(input);\n  },\n  meridiem(hour, minute, isLower) {\n    if (hour < 4) {\n      return 'ночи';\n    } else if (hour < 12) {\n      return 'утра';\n    } else if (hour < 17) {\n      return 'дня';\n    } else {\n      return 'вечера';\n    }\n  },\n  dayOfMonthOrdinalParse: /\\d{1,2}-(й|го|я)/,\n  ordinal(_num, period) {\n    const num = Number(_num);\n    switch (period) {\n      case 'M':\n      case 'd':\n      case 'DDD':\n        return num + '-й';\n      case 'D':\n        return num + '-го';\n      case 'w':\n      case 'W':\n        return num + '-я';\n      default:\n        return num.toString(10);\n    }\n  },\n  week: {\n    dow: 1,\n    doy: 4 // The week that contains Jan 4th is the first week of the year.\n  }\n};\n\n//! moment.js locale configuration\n//! locale : Slovak [sk]\n//! author : Jozef Pažin : https://github.com/atiris\nconst months = 'január_február_marec_apríl_máj_jún_júl_august_september_október_november_december'.split('_');\nconst monthsShort = 'jan_feb_mar_apr_máj_jún_júl_aug_sep_okt_nov_dec'.split('_');\nfunction plural$1(num) {\n  return num > 1 && num < 5 && ~~(num / 10) !== 1;\n}\nfunction translate(num, withoutSuffix, key, isFuture) {\n  const result = num + ' ';\n  switch (key) {\n    case 's':\n      // a few seconds / in a few seconds / a few seconds ago\n      return withoutSuffix || isFuture ? 'pár sekúnd' : 'pár sekundami';\n    case 'ss':\n      // 9 seconds / in 9 seconds / 9 seconds ago\n      if (withoutSuffix || isFuture) {\n        return result + (plural$1(num) ? 'sekundy' : 'sekúnd');\n      } else {\n        return result + 'sekundami';\n      }\n    // break;\n    case 'm':\n      // a minute / in a minute / a minute ago\n      return withoutSuffix ? 'minúta' : isFuture ? 'minútu' : 'minútou';\n    case 'mm':\n      // 9 minutes / in 9 minutes / 9 minutes ago\n      if (withoutSuffix || isFuture) {\n        return result + (plural$1(num) ? 'minúty' : 'minút');\n      } else {\n        return result + 'minútami';\n      }\n    // break;\n    case 'h':\n      // an hour / in an hour / an hour ago\n      return withoutSuffix ? 'hodina' : isFuture ? 'hodinu' : 'hodinou';\n    case 'hh':\n      // 9 hours / in 9 hours / 9 hours ago\n      if (withoutSuffix || isFuture) {\n        return result + (plural$1(num) ? 'hodiny' : 'hodín');\n      } else {\n        return result + 'hodinami';\n      }\n    // break;\n    case 'd':\n      // a day / in a day / a day ago\n      return withoutSuffix || isFuture ? 'deň' : 'dňom';\n    case 'dd':\n      // 9 days / in 9 days / 9 days ago\n      if (withoutSuffix || isFuture) {\n        return result + (plural$1(num) ? 'dni' : 'dní');\n      } else {\n        return result + 'dňami';\n      }\n    // break;\n    case 'M':\n      // a month / in a month / a month ago\n      return withoutSuffix || isFuture ? 'mesiac' : 'mesiacom';\n    case 'MM':\n      // 9 months / in 9 months / 9 months ago\n      if (withoutSuffix || isFuture) {\n        return result + (plural$1(num) ? 'mesiace' : 'mesiacov');\n      } else {\n        return result + 'mesiacmi';\n      }\n    // break;\n    case 'y':\n      // a year / in a year / a year ago\n      return withoutSuffix || isFuture ? 'rok' : 'rokom';\n    case 'yy':\n      // 9 years / in 9 years / 9 years ago\n      if (withoutSuffix || isFuture) {\n        return result + (plural$1(num) ? 'roky' : 'rokov');\n      } else {\n        return result + 'rokmi';\n      }\n    // break;\n  }\n}\nconst skLocale = {\n  abbr: 'sk',\n  months,\n  monthsShort,\n  weekdays: 'nedeľa_pondelok_utorok_streda_štvrtok_piatok_sobota'.split('_'),\n  weekdaysShort: 'ne_po_ut_st_št_pi_so'.split('_'),\n  weekdaysMin: 'ne_po_ut_st_št_pi_so'.split('_'),\n  longDateFormat: {\n    LT: 'H:mm',\n    LTS: 'H:mm:ss',\n    L: 'DD.MM.YYYY',\n    LL: 'D. MMMM YYYY',\n    LLL: 'D. MMMM YYYY H:mm',\n    LLLL: 'dddd D. MMMM YYYY H:mm',\n    l: 'D. M. YYYY'\n  },\n  calendar: {\n    sameDay: '[dnes o] LT',\n    nextDay: '[zajtra o] LT',\n    nextWeek(date) {\n      switch (getDayOfWeek(date)) {\n        case 0:\n          return '[v nedeľu o] LT';\n        case 1:\n        case 2:\n          return '[v] dddd [o] LT';\n        case 3:\n          return '[v stredu o] LT';\n        case 4:\n          return '[vo štvrtok o] LT';\n        case 5:\n          return '[v piatok o] LT';\n        case 6:\n          return '[v sobotu o] LT';\n      }\n    },\n    lastDay: '[včera o] LT',\n    lastWeek(date) {\n      switch (getDayOfWeek(date)) {\n        case 0:\n          return '[minulú nedeľu o] LT';\n        case 1:\n        case 2:\n          return '[minulý] dddd [o] LT';\n        case 3:\n          return '[minulú stredu o] LT';\n        case 4:\n        case 5:\n          return '[minulý] dddd [o] LT';\n        case 6:\n          return '[minulú sobotu o] LT';\n      }\n    },\n    sameElse: 'L'\n  },\n  relativeTime: {\n    future: 'o %s',\n    past: 'pred %s',\n    s: translate,\n    ss: translate,\n    m: translate,\n    mm: translate,\n    h: translate,\n    hh: translate,\n    d: translate,\n    dd: translate,\n    M: translate,\n    MM: translate,\n    y: translate,\n    yy: translate\n  },\n  dayOfMonthOrdinalParse: /\\d{1,2}\\./,\n  ordinal: '%d.',\n  week: {\n    dow: 1,\n    doy: 4 // The week that contains Jan 4th is the first week of the year.\n  }\n};\n\n//! moment.js locale configuration\n//! locale : Slovenian [sl]\n//! author : mihan : https://github.com/mihan\nfunction processRelativeTime(number, withoutSuffix, key, isFuture) {\n  var result = number + ' ';\n  switch (key) {\n    case 's':\n      return withoutSuffix || isFuture ? 'nekaj sekund' : 'nekaj sekundami';\n    case 'ss':\n      if (number === 1) {\n        result += withoutSuffix ? 'sekundo' : 'sekundi';\n      } else if (number === 2) {\n        result += withoutSuffix || isFuture ? 'sekundi' : 'sekundah';\n      } else if (number < 5) {\n        result += withoutSuffix || isFuture ? 'sekunde' : 'sekundah';\n      } else {\n        result += withoutSuffix || isFuture ? 'sekund' : 'sekund';\n      }\n      return result;\n    case 'm':\n      return withoutSuffix ? 'ena minuta' : 'eno minuto';\n    case 'mm':\n      if (number === 1) {\n        result += withoutSuffix ? 'minuta' : 'minuto';\n      } else if (number === 2) {\n        result += withoutSuffix || isFuture ? 'minuti' : 'minutama';\n      } else if (number < 5) {\n        result += withoutSuffix || isFuture ? 'minute' : 'minutami';\n      } else {\n        result += withoutSuffix || isFuture ? 'minut' : 'minutami';\n      }\n      return result;\n    case 'h':\n      return withoutSuffix ? 'ena ura' : 'eno uro';\n    case 'hh':\n      if (number === 1) {\n        result += withoutSuffix ? 'ura' : 'uro';\n      } else if (number === 2) {\n        result += withoutSuffix || isFuture ? 'uri' : 'urama';\n      } else if (number < 5) {\n        result += withoutSuffix || isFuture ? 'ure' : 'urami';\n      } else {\n        result += withoutSuffix || isFuture ? 'ur' : 'urami';\n      }\n      return result;\n    case 'd':\n      return withoutSuffix || isFuture ? 'en dan' : 'enim dnem';\n    case 'dd':\n      if (number === 1) {\n        result += withoutSuffix || isFuture ? 'dan' : 'dnem';\n      } else if (number === 2) {\n        result += withoutSuffix || isFuture ? 'dni' : 'dnevoma';\n      } else {\n        result += withoutSuffix || isFuture ? 'dni' : 'dnevi';\n      }\n      return result;\n    case 'M':\n      return withoutSuffix || isFuture ? 'en mesec' : 'enim mesecem';\n    case 'MM':\n      if (number === 1) {\n        result += withoutSuffix || isFuture ? 'mesec' : 'mesecem';\n      } else if (number === 2) {\n        result += withoutSuffix || isFuture ? 'meseca' : 'mesecema';\n      } else if (number < 5) {\n        result += withoutSuffix || isFuture ? 'mesece' : 'meseci';\n      } else {\n        result += withoutSuffix || isFuture ? 'mesecev' : 'meseci';\n      }\n      return result;\n    case 'y':\n      return withoutSuffix || isFuture ? 'eno leto' : 'enim letom';\n    case 'yy':\n      if (number === 1) {\n        result += withoutSuffix || isFuture ? 'leto' : 'letom';\n      } else if (number === 2) {\n        result += withoutSuffix || isFuture ? 'leti' : 'letoma';\n      } else if (number < 5) {\n        result += withoutSuffix || isFuture ? 'leta' : 'leti';\n      } else {\n        result += withoutSuffix || isFuture ? 'let' : 'leti';\n      }\n      return result;\n  }\n}\nconst slLocale = {\n  abbr: 'sl',\n  months: 'januar_februar_marec_april_maj_junij_julij_avgust_september_oktober_november_december'.split('_'),\n  monthsShort: 'jan._feb._mar._apr._maj._jun._jul._avg._sep._okt._nov._dec.'.split('_'),\n  monthsParseExact: true,\n  weekdays: 'nedelja_ponedeljek_torek_sreda_četrtek_petek_sobota'.split('_'),\n  weekdaysShort: 'ned._pon._tor._sre._čet._pet._sob.'.split('_'),\n  weekdaysMin: 'ne_po_to_sr_če_pe_so'.split('_'),\n  weekdaysParseExact: true,\n  longDateFormat: {\n    LT: 'H:mm',\n    LTS: 'H:mm:ss',\n    L: 'DD.MM.YYYY',\n    LL: 'D. MMMM YYYY',\n    LLL: 'D. MMMM YYYY H:mm',\n    LLLL: 'dddd, D. MMMM YYYY H:mm'\n  },\n  calendar: {\n    sameDay: '[danes ob] LT',\n    nextDay: '[jutri ob] LT',\n    nextWeek(date) {\n      switch (getDayOfWeek(date)) {\n        case 0:\n          return '[v] [nedeljo] [ob] LT';\n        case 3:\n          return '[v] [sredo] [ob] LT';\n        case 6:\n          return '[v] [soboto] [ob] LT';\n        case 1:\n        case 2:\n        case 4:\n        case 5:\n          return '[v] dddd [ob] LT';\n      }\n    },\n    lastDay: '[včeraj ob] LT',\n    lastWeek(date) {\n      switch (getDayOfWeek(date)) {\n        case 0:\n          return '[prejšnjo] [nedeljo] [ob] LT';\n        case 3:\n          return '[prejšnjo] [sredo] [ob] LT';\n        case 6:\n          return '[prejšnjo] [soboto] [ob] LT';\n        case 1:\n        case 2:\n        case 4:\n        case 5:\n          return '[prejšnji] dddd [ob] LT';\n      }\n    },\n    sameElse: 'L'\n  },\n  relativeTime: {\n    future: 'čez %s',\n    past: 'pred %s',\n    s: processRelativeTime,\n    ss: processRelativeTime,\n    m: processRelativeTime,\n    mm: processRelativeTime,\n    h: processRelativeTime,\n    hh: processRelativeTime,\n    d: processRelativeTime,\n    dd: processRelativeTime,\n    M: processRelativeTime,\n    MM: processRelativeTime,\n    y: processRelativeTime,\n    yy: processRelativeTime\n  },\n  dayOfMonthOrdinalParse: /\\d{1,2}\\./,\n  ordinal: '%d.',\n  week: {\n    dow: 1,\n    doy: 7 // The week that contains Jan 1st is the first week of the year.\n  }\n};\n\n//! moment.js locale configuration\n//! locale : Albanian [sq]\n//! author : Agon Cecelia : https://github.com/agoncecelia\nconst sqLocale = {\n  abbr: 'sq',\n  months: 'Janar_Shkurt_Mars_Prill_Maj_Qershor_Korrik_Gusht_Shtator_Tetor_Nëntor_Dhjetor'.split('_'),\n  monthsShort: 'Jan_Shk_Mar_Pri_Maj_Qer_Kor_Gus_Sht_Tet_Nën_Dhj'.split('_'),\n  weekdays: 'E Dielë_E Hënë_E Martë_E Mërkurë_E Enjte_E Premte_E Shtunë'.split('_'),\n  weekdaysShort: 'Die_Hën_Mar_Mër_Enj_Pre_Sht'.split('_'),\n  weekdaysMin: 'Di_He_Ma_Me_En_Pr_Sh'.split('_'),\n  longDateFormat: {\n    LT: 'HH:mm',\n    LTS: 'HH:mm:ss',\n    L: 'DD/MM/YYYY',\n    LL: 'D MMMM YYYY',\n    LLL: 'D MMMM YYYY HH:mm',\n    LLLL: 'dddd, D MMMM YYYY HH:mm'\n  },\n  calendar: {\n    sameDay: '[Sot në] LT',\n    nextDay: '[Nesër në] LT',\n    nextWeek: 'dddd [në] LT',\n    lastDay: '[Dje në] LT',\n    lastWeek: 'dddd [e kaluar në] LT',\n    sameElse: 'L'\n  },\n  relativeTime: {\n    future: 'në %s',\n    past: 'para %sve',\n    s: 'disa sekonda',\n    ss: '%d sekonda',\n    m: 'një minut',\n    mm: '%d minuta',\n    h: 'një orë',\n    hh: '%d orë',\n    d: 'një ditë',\n    dd: '%d ditë',\n    M: 'një muaj',\n    MM: '%d muaj',\n    y: 'një vit',\n    yy: '%d vite'\n  },\n  dayOfMonthOrdinalParse: /\\d{1,2}\\./,\n  ordinal: '%d.',\n  week: {\n    dow: 1,\n    doy: 4 // The week that contains Jan 4th is the first week of the year.\n  }\n};\n\n//! moment.js locale configuration\n//! locale : Swedish [sv]\n//! author : Jens Alm : https://github.com/ulmus\nconst svLocale = {\n  abbr: 'sv',\n  months: 'januari_februari_mars_april_maj_juni_juli_augusti_september_oktober_november_december'.split('_'),\n  monthsShort: 'jan_feb_mar_apr_maj_jun_jul_aug_sep_okt_nov_dec'.split('_'),\n  weekdays: 'söndag_måndag_tisdag_onsdag_torsdag_fredag_lördag'.split('_'),\n  weekdaysShort: 'sön_mån_tis_ons_tor_fre_lör'.split('_'),\n  weekdaysMin: 'sö_må_ti_on_to_fr_lö'.split('_'),\n  longDateFormat: {\n    LT: 'HH:mm',\n    LTS: 'HH:mm:ss',\n    L: 'YYYY-MM-DD',\n    LL: 'D MMMM YYYY',\n    LLL: 'D MMMM YYYY [kl.] HH:mm',\n    LLLL: 'dddd D MMMM YYYY [kl.] HH:mm',\n    lll: 'D MMM YYYY HH:mm',\n    llll: 'ddd D MMM YYYY HH:mm'\n  },\n  calendar: {\n    sameDay: '[Idag] LT',\n    nextDay: '[Imorgon] LT',\n    lastDay: '[Igår] LT',\n    nextWeek: '[På] dddd LT',\n    lastWeek: '[I] dddd[s] LT',\n    sameElse: 'L'\n  },\n  relativeTime: {\n    future: 'om %s',\n    past: 'för %s sedan',\n    s: 'några sekunder',\n    ss: '%d sekunder',\n    m: 'en minut',\n    mm: '%d minuter',\n    h: 'en timme',\n    hh: '%d timmar',\n    d: 'en dag',\n    dd: '%d dagar',\n    M: 'en månad',\n    MM: '%d månader',\n    y: 'ett år',\n    yy: '%d år'\n  },\n  dayOfMonthOrdinalParse: /\\d{1,2}(e|a)/,\n  ordinal(_num) {\n    const num = Number(_num);\n    let b = num % 10,\n      output = ~~(num % 100 / 10) === 1 ? 'e' : b === 1 ? 'a' : b === 2 ? 'a' : b === 3 ? 'e' : 'e';\n    return num + output;\n  },\n  week: {\n    dow: 1,\n    doy: 4 // The week that contains Jan 4th is the first week of the year.\n  }\n};\n\n// moment.js locale configuration\n// locale : Thai [th]\n// author : Watcharapol Sanitwong : https://github.com/tumit\nconst thLocale = {\n  abbr: 'th',\n  months: 'มกราคม_กุมภาพันธ์_มีนาคม_เมษายน_พฤษภาคม_มิถุนายน_กรกฎาคม_สิงหาคม_กันยายน_ตุลาคม_พฤศจิกายน_ธันวาคม'.split('_'),\n  monthsShort: 'ม.ค._ก.พ._มี.ค._เม.ย._พ.ค._มิ.ย._ก.ค._ส.ค._ก.ย._ต.ค._พ.ย._ธ.ค.'.split('_'),\n  monthsParseExact: true,\n  weekdays: 'อาทิตย์_จันทร์_อังคาร_พุธ_พฤหัสบดี_ศุกร์_เสาร์'.split('_'),\n  weekdaysShort: 'อา._จ._อ._พ._พฤ._ศ._ส.'.split('_'),\n  weekdaysMin: 'อา._จ._อ._พ._พฤ._ศ._ส.'.split('_'),\n  weekdaysParseExact: true,\n  longDateFormat: {\n    LT: 'H:mm',\n    LTS: 'H:mm:ss',\n    L: 'DD/MM/YYYY',\n    LL: 'D MMMM YYYY',\n    LLL: 'D MMMM YYYY เวลา H:mm',\n    LLLL: 'วันddddที่ D MMMM YYYY เวลา H:mm'\n  },\n  meridiemParse: /ก่อนเที่ยง|หลังเที่ยง/,\n  isPM(input) {\n    return input === 'หลังเที่ยง';\n  },\n  meridiem(hour, minute, isLower) {\n    if (hour < 12) {\n      return 'ก่อนเที่ยง';\n    } else {\n      return 'หลังเที่ยง';\n    }\n  },\n  calendar: {\n    sameDay: '[วันนี้ เวลา] LT',\n    nextDay: '[พรุ่งนี้ เวลา] LT',\n    nextWeek: 'dddd[หน้า เวลา] LT',\n    lastDay: '[เมื่อวานนี้ เวลา] LT',\n    lastWeek: '[วัน]dddd[ที่แล้ว เวลา] LT',\n    sameElse: 'L'\n  },\n  relativeTime: {\n    future: 'อีก %s',\n    past: '%sที่แล้ว',\n    s: 'ไม่กี่วินาที',\n    ss: '%d วินาที',\n    m: '1 นาที',\n    mm: '%d นาที',\n    h: '1 ชั่วโมง',\n    hh: '%d ชั่วโมง',\n    d: '1 วัน',\n    dd: '%d วัน',\n    M: '1 เดือน',\n    MM: '%d เดือน',\n    y: '1 ปี',\n    yy: '%d ปี'\n  }\n};\n\n// moment.js locale configuration\n// locale : Thai-Buddhist Era [th-be]\n// author : Watcharapol Sanitwong : https://github.com/tumit\nconst thBeLocale = {\n  abbr: 'th-be',\n  months: 'มกราคม_กุมภาพันธ์_มีนาคม_เมษายน_พฤษภาคม_มิถุนายน_กรกฎาคม_สิงหาคม_กันยายน_ตุลาคม_พฤศจิกายน_ธันวาคม'.split('_'),\n  monthsShort: 'ม.ค._ก.พ._มี.ค._เม.ย._พ.ค._มิ.ย._ก.ค._ส.ค._ก.ย._ต.ค._พ.ย._ธ.ค.'.split('_'),\n  monthsParseExact: true,\n  weekdays: 'อาทิตย์_จันทร์_อังคาร_พุธ_พฤหัสบดี_ศุกร์_เสาร์'.split('_'),\n  weekdaysShort: 'อา._จ._อ._พ._พฤ._ศ._ส.'.split('_'),\n  weekdaysMin: 'อา._จ._อ._พ._พฤ._ศ._ส.'.split('_'),\n  weekdaysParseExact: true,\n  longDateFormat: {\n    LT: 'H:mm',\n    LTS: 'H:mm:ss',\n    L: 'DD/MM/YYYY',\n    LL: 'D MMMM YYYY',\n    LLL: 'D MMMM YYYY เวลา H:mm',\n    LLLL: 'วันddddที่ D MMMM YYYY เวลา H:mm'\n  },\n  meridiemParse: /ก่อนเที่ยง|หลังเที่ยง/,\n  isPM(input) {\n    return input === 'หลังเที่ยง';\n  },\n  meridiem(hour, minute, isLower) {\n    if (hour < 12) {\n      return 'ก่อนเที่ยง';\n    } else {\n      return 'หลังเที่ยง';\n    }\n  },\n  calendar: {\n    sameDay: '[วันนี้ เวลา] LT',\n    nextDay: '[พรุ่งนี้ เวลา] LT',\n    nextWeek: 'dddd[หน้า เวลา] LT',\n    lastDay: '[เมื่อวานนี้ เวลา] LT',\n    lastWeek: '[วัน]dddd[ที่แล้ว เวลา] LT',\n    sameElse: 'L'\n  },\n  relativeTime: {\n    future: 'อีก %s',\n    past: '%sที่แล้ว',\n    s: 'ไม่กี่วินาที',\n    ss: '%d วินาที',\n    m: '1 นาที',\n    mm: '%d นาที',\n    h: '1 ชั่วโมง',\n    hh: '%d ชั่วโมง',\n    d: '1 วัน',\n    dd: '%d วัน',\n    M: '1 เดือน',\n    MM: '%d เดือน',\n    y: '1 ปี',\n    yy: '%d ปี'\n  },\n  preparse(str, format) {\n    const _format = thBeLocale.longDateFormat[format] ? thBeLocale.longDateFormat[format] : format;\n    // endsWith('YYYY')\n    if (_format.indexOf('YYYY', _format.length - 'YYYY'.length) !== -1) {\n      const ddMM = str.substr(0, str.length - 4);\n      const yyyy = parseInt(str.substr(str.length - 4), 10) - 543;\n      return ddMM + yyyy;\n    }\n    return str;\n  },\n  getFullYear(date, isUTC = false) {\n    return 543 + (isUTC ? date.getUTCFullYear() : date.getFullYear());\n  }\n};\n\n//! moment.js locale configuration\n//! locale : Turkish [tr]\n//! authors : Erhan Gundogan : https://github.com/erhangundogan,\n//!           Burak Yiğit Kaya: https://github.com/BYK\nlet suffixes = {\n  1: '\\'inci',\n  5: '\\'inci',\n  8: '\\'inci',\n  70: '\\'inci',\n  80: '\\'inci',\n  2: '\\'nci',\n  7: '\\'nci',\n  20: '\\'nci',\n  50: '\\'nci',\n  3: '\\'üncü',\n  4: '\\'üncü',\n  100: '\\'üncü',\n  6: '\\'ncı',\n  9: '\\'uncu',\n  10: '\\'uncu',\n  30: '\\'uncu',\n  60: '\\'ıncı',\n  90: '\\'ıncı'\n};\nconst trLocale = {\n  abbr: 'tr',\n  months: 'Ocak_Şubat_Mart_Nisan_Mayıs_Haziran_Temmuz_Ağustos_Eylül_Ekim_Kasım_Aralık'.split('_'),\n  monthsShort: 'Oca_Şub_Mar_Nis_May_Haz_Tem_Ağu_Eyl_Eki_Kas_Ara'.split('_'),\n  weekdays: 'Pazar_Pazartesi_Salı_Çarşamba_Perşembe_Cuma_Cumartesi'.split('_'),\n  weekdaysShort: 'Paz_Pts_Sal_Çar_Per_Cum_Cts'.split('_'),\n  weekdaysMin: 'Pz_Pt_Sa_Ça_Pe_Cu_Ct'.split('_'),\n  longDateFormat: {\n    LT: 'HH:mm',\n    LTS: 'HH:mm:ss',\n    L: 'DD.MM.YYYY',\n    LL: 'D MMMM YYYY',\n    LLL: 'D MMMM YYYY HH:mm',\n    LLLL: 'dddd, D MMMM YYYY HH:mm'\n  },\n  calendar: {\n    sameDay: '[bugün saat] LT',\n    nextDay: '[yarın saat] LT',\n    nextWeek: '[gelecek] dddd [saat] LT',\n    lastDay: '[dün] LT',\n    lastWeek: '[geçen] dddd [saat] LT',\n    sameElse: 'L'\n  },\n  relativeTime: {\n    future: '%s sonra',\n    past: '%s önce',\n    s: 'birkaç saniye',\n    ss: '%d saniye',\n    m: 'bir dakika',\n    mm: '%d dakika',\n    h: 'bir saat',\n    hh: '%d saat',\n    d: 'bir gün',\n    dd: '%d gün',\n    M: 'bir ay',\n    MM: '%d ay',\n    y: 'bir yıl',\n    yy: '%d yıl'\n  },\n  dayOfMonthOrdinalParse: /\\d{1,2}'(inci|nci|üncü|ncı|uncu|ıncı)/,\n  ordinal(_num) {\n    const num = Number(_num);\n    if (num === 0) {\n      // special case for zero\n      return num + '\\'ıncı';\n    }\n    let a = num % 10,\n      b = num % 100 - a,\n      c = num >= 100 ? 100 : null;\n    return num + (suffixes[a] || suffixes[b] || suffixes[c]);\n  },\n  week: {\n    dow: 1,\n    doy: 7 // The week that contains Jan 1st is the first week of the year.\n  }\n};\n\n//! moment.js locale configuration\n//! locale : Ukrainian [uk]\n//! author : zemlanin : https://github.com/zemlanin\n//! Author : Menelion Elensúle : https://github.com/Oire\nfunction plural(word, num) {\n  let forms = word.split('_');\n  return num % 10 === 1 && num % 100 !== 11 ? forms[0] : num % 10 >= 2 && num % 10 <= 4 && (num % 100 < 10 || num % 100 >= 20) ? forms[1] : forms[2];\n}\nfunction relativeTimeWithPlural(num, withoutSuffix, key) {\n  let format = {\n    ss: withoutSuffix ? 'секунда_секунди_секунд' : 'секунду_секунди_секунд',\n    mm: withoutSuffix ? 'хвилина_хвилини_хвилин' : 'хвилину_хвилини_хвилин',\n    hh: withoutSuffix ? 'година_години_годин' : 'годину_години_годин',\n    dd: 'день_дні_днів',\n    MM: 'місяць_місяці_місяців',\n    yy: 'рік_роки_років'\n  };\n  if (key === 'm') {\n    return withoutSuffix ? 'хвилина' : 'хвилину';\n  }\n  if (key === 'h') {\n    return withoutSuffix ? 'година' : 'годину';\n  }\n  return num + ' ' + plural(format[key], +num);\n}\nfunction weekdaysCaseReplace(date, format, isUTC) {\n  let weekdays = {\n    nominative: 'неділя_понеділок_вівторок_середа_четвер_п’ятниця_субота'.split('_'),\n    accusative: 'неділю_понеділок_вівторок_середу_четвер_п’ятницю_суботу'.split('_'),\n    genitive: 'неділі_понеділка_вівторка_середи_четверга_п’ятниці_суботи'.split('_')\n  };\n  if (!date) {\n    return weekdays.nominative;\n  }\n  let nounCase = /(\\[[ВвУу]\\]) ?dddd/.test(format) ? 'accusative' : /\\[?(?:минулої|наступної)? ?\\] ?dddd/.test(format) ? 'genitive' : 'nominative';\n  return weekdays[nounCase][getDayOfWeek(date, isUTC)];\n}\nfunction processHoursFunction(str) {\n  return function (date) {\n    return str + 'о' + (getHours(date) === 11 ? 'б' : '') + '] LT';\n  };\n}\nconst ukLocale = {\n  abbr: 'uk',\n  months: {\n    format: 'січня_лютого_березня_квітня_травня_червня_липня_серпня_вересня_жовтня_листопада_грудня'.split('_'),\n    standalone: 'січень_лютий_березень_квітень_травень_червень_липень_серпень_вересень_жовтень_листопад_грудень'.split('_')\n  },\n  monthsShort: 'січ_лют_бер_квіт_трав_черв_лип_серп_вер_жовт_лист_груд'.split('_'),\n  weekdays: weekdaysCaseReplace,\n  weekdaysShort: 'нд_пн_вт_ср_чт_пт_сб'.split('_'),\n  weekdaysMin: 'нд_пн_вт_ср_чт_пт_сб'.split('_'),\n  longDateFormat: {\n    LT: 'HH:mm',\n    LTS: 'HH:mm:ss',\n    L: 'DD.MM.YYYY',\n    LL: 'D MMMM YYYY р.',\n    LLL: 'D MMMM YYYY р., HH:mm',\n    LLLL: 'dddd, D MMMM YYYY р., HH:mm'\n  },\n  calendar: {\n    sameDay: processHoursFunction('[Сьогодні '),\n    nextDay: processHoursFunction('[Завтра '),\n    lastDay: processHoursFunction('[Вчора '),\n    nextWeek: processHoursFunction('[У] dddd ['),\n    lastWeek(date) {\n      switch (getDayOfWeek(date)) {\n        case 0:\n        case 3:\n        case 5:\n        case 6:\n          return processHoursFunction('[Минулої] dddd [')(date);\n        case 1:\n        case 2:\n        case 4:\n          return processHoursFunction('[Минулого] dddd [')(date);\n      }\n    },\n    sameElse: 'L'\n  },\n  relativeTime: {\n    future: 'за %s',\n    past: '%s тому',\n    s: 'декілька секунд',\n    ss: relativeTimeWithPlural,\n    m: relativeTimeWithPlural,\n    mm: relativeTimeWithPlural,\n    h: 'годину',\n    hh: relativeTimeWithPlural,\n    d: 'день',\n    dd: relativeTimeWithPlural,\n    M: 'місяць',\n    MM: relativeTimeWithPlural,\n    y: 'рік',\n    yy: relativeTimeWithPlural\n  },\n  // M. E.: those two are virtually unused but a user might want to implement them for his/her website for some reason\n  meridiemParse: /ночі|ранку|дня|вечора/,\n  isPM(input) {\n    return /^(дня|вечора)$/.test(input);\n  },\n  meridiem(hour, minute, isLower) {\n    if (hour < 4) {\n      return 'ночі';\n    } else if (hour < 12) {\n      return 'ранку';\n    } else if (hour < 17) {\n      return 'дня';\n    } else {\n      return 'вечора';\n    }\n  },\n  dayOfMonthOrdinalParse: /\\d{1,2}-(й|го)/,\n  ordinal(_num, period) {\n    const num = Number(_num);\n    switch (period) {\n      case 'M':\n      case 'd':\n      case 'DDD':\n      case 'w':\n      case 'W':\n        return num + '-й';\n      case 'D':\n        return num + '-го';\n      default:\n        return num.toString();\n    }\n  },\n  week: {\n    dow: 1,\n    doy: 7 // The week that contains Jan 1st is the first week of the year.\n  }\n};\n\n//! moment.js locale configuration\n//! locale : Việt Nam [vi]\n//! author : Chris Gedrim : https://github.com/chrisgedrim\nconst viLocale = {\n  abbr: 'vi',\n  months: 'tháng 1_tháng 2_tháng 3_tháng 4_tháng 5_tháng 6_tháng 7_tháng 8_tháng 9_tháng 10_tháng 11_tháng 12'.split('_'),\n  monthsShort: 'Th01_Th02_Th03_Th04_Th05_Th06_Th07_Th08_Th09_Th10_Th11_Th12'.split('_'),\n  monthsParseExact: true,\n  weekdays: 'chủ nhật_thứ hai_thứ ba_thứ tư_thứ năm_thứ sáu_thứ bảy'.split('_'),\n  weekdaysShort: 'CN_T2_T3_T4_T5_T6_T7'.split('_'),\n  weekdaysMin: 'CN_T2_T3_T4_T5_T6_T7'.split('_'),\n  weekdaysParseExact: true,\n  meridiemParse: /sa|ch/i,\n  isPM(input) {\n    return /^ch$/i.test(input);\n  },\n  meridiem(hours, minutes, isLower) {\n    if (hours < 12) {\n      return isLower ? 'sa' : 'SA';\n    } else {\n      return isLower ? 'ch' : 'CH';\n    }\n  },\n  longDateFormat: {\n    LT: 'HH:mm',\n    LTS: 'HH:mm:ss',\n    L: 'DD/MM/YYYY',\n    LL: 'D MMMM [năm] YYYY',\n    LLL: 'D MMMM [năm] YYYY HH:mm',\n    LLLL: 'dddd, D MMMM [năm] YYYY HH:mm',\n    l: 'DD/M/YYYY',\n    ll: 'D MMM YYYY',\n    lll: 'D MMM YYYY HH:mm',\n    llll: 'ddd, D MMM YYYY HH:mm'\n  },\n  calendar: {\n    sameDay: '[Hôm nay lúc] LT',\n    nextDay: '[Ngày mai lúc] LT',\n    nextWeek: 'dddd [tuần tới lúc] LT',\n    lastDay: '[Hôm qua lúc] LT',\n    lastWeek: 'dddd [tuần trước lúc] LT',\n    sameElse: 'L'\n  },\n  relativeTime: {\n    future: '%s tới',\n    past: '%s trước',\n    s: 'vài giây',\n    ss: '%d giây',\n    m: 'một phút',\n    mm: '%d phút',\n    h: 'một giờ',\n    hh: '%d giờ',\n    d: 'một ngày',\n    dd: '%d ngày',\n    M: 'một tháng',\n    MM: '%d tháng',\n    y: 'một năm',\n    yy: '%d năm'\n  },\n  dayOfMonthOrdinalParse: /\\d{1,2}/,\n  ordinal(_num) {\n    return '' + _num;\n  },\n  week: {\n    dow: 1,\n    doy: 4 // Tuần chứa ngày 4 tháng 1 là tuần đầu tiên trong năm.\n  }\n};\n\n//! moment.js locale configuration\n//! locale : Chinese (China) [zh-cn]\n//! author : suupic : https://github.com/suupic\n//! author : Zeno Zeng : https://github.com/zenozeng\nconst zhCnLocale = {\n  abbr: 'zh-cn',\n  months: '一月_二月_三月_四月_五月_六月_七月_八月_九月_十月_十一月_十二月'.split('_'),\n  monthsShort: '1月_2月_3月_4月_5月_6月_7月_8月_9月_10月_11月_12月'.split('_'),\n  weekdays: '星期日_星期一_星期二_星期三_星期四_星期五_星期六'.split('_'),\n  weekdaysShort: '周日_周一_周二_周三_周四_周五_周六'.split('_'),\n  weekdaysMin: '日_一_二_三_四_五_六'.split('_'),\n  longDateFormat: {\n    LT: 'HH:mm',\n    LTS: 'HH:mm:ss',\n    L: 'YYYY/MM/DD',\n    LL: 'YYYY年M月D日',\n    LLL: 'YYYY年M月D日Ah点mm分',\n    LLLL: 'YYYY年M月D日ddddAh点mm分',\n    l: 'YYYY/M/D',\n    ll: 'YYYY年M月D日',\n    lll: 'YYYY年M月D日 HH:mm',\n    llll: 'YYYY年M月D日dddd HH:mm'\n  },\n  meridiemParse: /凌晨|早上|上午|中午|下午|晚上/,\n  meridiemHour(hour, meridiem) {\n    if (hour === 12) {\n      hour = 0;\n    }\n    if (meridiem === '凌晨' || meridiem === '早上' || meridiem === '上午') {\n      return hour;\n    } else if (meridiem === '下午' || meridiem === '晚上') {\n      return hour + 12;\n    } else {\n      // '中午'\n      return hour >= 11 ? hour : hour + 12;\n    }\n  },\n  meridiem(hour, minute, isLower) {\n    let hm = hour * 100 + minute;\n    if (hm < 600) {\n      return '凌晨';\n    } else if (hm < 900) {\n      return '早上';\n    } else if (hm < 1130) {\n      return '上午';\n    } else if (hm < 1230) {\n      return '中午';\n    } else if (hm < 1800) {\n      return '下午';\n    } else {\n      return '晚上';\n    }\n  },\n  calendar: {\n    sameDay: '[今天]LT',\n    nextDay: '[明天]LT',\n    nextWeek: '[下]ddddLT',\n    lastDay: '[昨天]LT',\n    lastWeek: '[上]ddddLT',\n    sameElse: 'L'\n  },\n  dayOfMonthOrdinalParse: /\\d{1,2}(日|月|周)/,\n  ordinal(_num, period) {\n    const num = Number(_num);\n    switch (period) {\n      case 'd':\n      case 'D':\n      case 'DDD':\n        return num + '日';\n      case 'M':\n        return num + '月';\n      case 'w':\n      case 'W':\n        return num + '周';\n      default:\n        return num.toString();\n    }\n  },\n  relativeTime: {\n    future: '%s内',\n    past: '%s前',\n    s: '几秒',\n    ss: '%d 秒',\n    m: '1 分钟',\n    mm: '%d 分钟',\n    h: '1 小时',\n    hh: '%d 小时',\n    d: '1 天',\n    dd: '%d 天',\n    M: '1 个月',\n    MM: '%d 个月',\n    y: '1 年',\n    yy: '%d 年'\n  },\n  week: {\n    // GB/T 7408-1994《数据元和交换格式·信息交换·日期和时间表示法》与ISO 8601:1988等效\n    dow: 1,\n    doy: 4 // The week that contains Jan 4th is the first week of the year.\n  }\n};\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { add, arLocale, bgLocale, caLocale, csLocale, daLocale, deLocale, defineLocale, enGbLocale, endOf, esDoLocale, esLocale, esPrLocale, esUsLocale, etLocale, fiLocale, formatDate, frLocale, getDay, getFirstDayOfMonth, getFullYear, getLocale, getMonth, getSetGlobalLocale, glLocale, heLocale, hiLocale, hrLocale, huLocale, idLocale, isAfter, isArray, isBefore, isDate, isDateValid, isDisabledDay, isFirstDayOfWeek, isSame, isSameDay$1 as isSameDay, isSameMonth, isSameYear, itLocale, jaLocale, kaLocale, kkLocale, koLocale, listLocales, ltLocale, lvLocale, mnLocale, nbLocale, nlBeLocale, nlLocale, parseDate, plLocale, ptBrLocale, roLocale, ruLocale, setFullDate, shiftDate, skLocale, slLocale, sqLocale, startOf, subtract, svLocale, thBeLocale, thLocale, trLocale, ukLocale, updateLocale, utcAsLocal, viLocale, zhCnLocale };\n//# sourceMappingURL=ngx-bootstrap-chronos.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}