{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.captureError = exports.errorContext = void 0;\nvar config_1 = require(\"../config\");\nvar context = null;\nfunction errorContext(cb) {\n  if (config_1.config.useDeprecatedSynchronousErrorHandling) {\n    var isRoot = !context;\n    if (isRoot) {\n      context = {\n        errorThrown: false,\n        error: null\n      };\n    }\n    cb();\n    if (isRoot) {\n      var _a = context,\n        errorThrown = _a.errorThrown,\n        error = _a.error;\n      context = null;\n      if (errorThrown) {\n        throw error;\n      }\n    }\n  } else {\n    cb();\n  }\n}\nexports.errorContext = errorContext;\nfunction captureError(err) {\n  if (config_1.config.useDeprecatedSynchronousErrorHandling && context) {\n    context.errorThrown = true;\n    context.error = err;\n  }\n}\nexports.captureError = captureError;\n//# sourceMappingURL=errorContext.js.map", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}