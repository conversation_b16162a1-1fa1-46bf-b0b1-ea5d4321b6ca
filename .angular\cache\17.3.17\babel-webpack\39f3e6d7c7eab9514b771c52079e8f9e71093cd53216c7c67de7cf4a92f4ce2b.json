{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.combineLatestAll = void 0;\nvar combineLatest_1 = require(\"../observable/combineLatest\");\nvar joinAllInternals_1 = require(\"./joinAllInternals\");\nfunction combineLatestAll(project) {\n  return joinAllInternals_1.joinAllInternals(combineLatest_1.combineLatest, project);\n}\nexports.combineLatestAll = combineLatestAll;\n//# sourceMappingURL=combineLatestAll.js.map", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}