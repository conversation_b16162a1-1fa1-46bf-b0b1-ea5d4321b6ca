{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.map = void 0;\nvar lift_1 = require(\"../util/lift\");\nvar OperatorSubscriber_1 = require(\"./OperatorSubscriber\");\nfunction map(project, thisArg) {\n  return lift_1.operate(function (source, subscriber) {\n    var index = 0;\n    source.subscribe(OperatorSubscriber_1.createOperatorSubscriber(subscriber, function (value) {\n      subscriber.next(project.call(thisArg, value, index++));\n    }));\n  });\n}\nexports.map = map;\n//# sourceMappingURL=map.js.map", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}