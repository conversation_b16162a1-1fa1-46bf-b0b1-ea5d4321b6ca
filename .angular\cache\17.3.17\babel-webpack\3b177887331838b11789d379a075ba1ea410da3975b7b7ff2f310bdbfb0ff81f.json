{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nvar core_1 = require(\"@angular/core\");\nvar _ = require(\"lodash\");\nvar rxjs_1 = require(\"rxjs\");\nvar DataTable = function () {\n  function DataTable(differs) {\n    this.differs = differs;\n    this.inputData = [];\n    this.sortBy = \"\";\n    this.sortOrder = \"asc\";\n    this.sortByChange = new core_1.EventEmitter();\n    this.sortOrderChange = new core_1.EventEmitter();\n    this.rowsOnPage = 1000;\n    this.activePage = 1;\n    this.mustRecalculateData = false;\n    this.onSortChange = new rxjs_1.ReplaySubject(1);\n    this.onPageChange = new core_1.EventEmitter();\n    this.diff = differs.find([]).create(null);\n  }\n  DataTable.prototype.getSort = function () {\n    return {\n      sortBy: this.sortBy,\n      sortOrder: this.sortOrder\n    };\n  };\n  DataTable.prototype.setSort = function (sortBy, sortOrder) {\n    if (this.sortBy !== sortBy || this.sortOrder !== sortOrder) {\n      this.sortBy = sortBy;\n      this.sortOrder = _.includes([\"asc\", \"desc\"], sortOrder) ? sortOrder : \"asc\";\n      this.mustRecalculateData = true;\n      this.onSortChange.next({\n        sortBy: sortBy,\n        sortOrder: sortOrder\n      });\n      this.sortByChange.emit(this.sortBy);\n      this.sortOrderChange.emit(this.sortOrder);\n    }\n  };\n  DataTable.prototype.getPage = function () {\n    return {\n      activePage: this.activePage,\n      rowsOnPage: this.rowsOnPage,\n      dataLength: this.inputData.length\n    };\n  };\n  DataTable.prototype.setPage = function (activePage, rowsOnPage) {\n    if (this.rowsOnPage !== rowsOnPage || this.activePage !== activePage) {\n      this.activePage = this.activePage !== activePage ? activePage : this.calculateNewActivePage(this.rowsOnPage, rowsOnPage);\n      this.rowsOnPage = rowsOnPage;\n      this.mustRecalculateData = true;\n      this.onPageChange.emit({\n        activePage: this.activePage,\n        rowsOnPage: this.rowsOnPage,\n        dataLength: this.inputData ? this.inputData.length : 0\n      });\n    }\n  };\n  DataTable.prototype.calculateNewActivePage = function (previousRowsOnPage, currentRowsOnPage) {\n    var firstRowOnPage = (this.activePage - 1) * previousRowsOnPage + 1;\n    var newActivePage = Math.ceil(firstRowOnPage / currentRowsOnPage);\n    return newActivePage;\n  };\n  DataTable.prototype.recalculatePage = function () {\n    var lastPage = Math.ceil(this.inputData.length / this.rowsOnPage);\n    this.activePage = lastPage < this.activePage ? lastPage : this.activePage;\n    this.activePage = this.activePage || 1;\n    this.onPageChange.emit({\n      activePage: this.activePage,\n      rowsOnPage: this.rowsOnPage,\n      dataLength: this.inputData.length\n    });\n  };\n  DataTable.prototype.ngOnChanges = function (changes) {\n    if (changes[\"rowsOnPage\"]) {\n      this.rowsOnPage = changes[\"rowsOnPage\"].previousValue;\n      this.setPage(this.activePage, changes[\"rowsOnPage\"].currentValue);\n      this.mustRecalculateData = true;\n    }\n    if (changes[\"sortBy\"] || changes[\"sortOrder\"]) {\n      if (!_.includes([\"asc\", \"desc\"], this.sortOrder)) {\n        console.warn(\"angular2-datatable: value for input mfSortOrder must be one of ['asc', 'desc'], but is:\", this.sortOrder);\n        this.sortOrder = \"asc\";\n      }\n      if (this.sortBy) {\n        this.onSortChange.next({\n          sortBy: this.sortBy,\n          sortOrder: this.sortOrder\n        });\n      }\n      this.mustRecalculateData = true;\n    }\n    if (changes[\"inputData\"]) {\n      this.inputData = changes[\"inputData\"].currentValue || [];\n      this.recalculatePage();\n      this.mustRecalculateData = true;\n    }\n  };\n  DataTable.prototype.ngDoCheck = function () {\n    var changes = this.diff.diff(this.inputData);\n    if (changes) {\n      this.recalculatePage();\n      this.mustRecalculateData = true;\n    }\n    if (this.mustRecalculateData) {\n      this.fillData();\n      this.mustRecalculateData = false;\n    }\n  };\n  DataTable.prototype.fillData = function () {\n    this.activePage = this.activePage;\n    this.rowsOnPage = this.rowsOnPage;\n    var offset = (this.activePage - 1) * this.rowsOnPage;\n    var data = this.inputData;\n    var sortBy = this.sortBy;\n    if (typeof sortBy === 'string' || sortBy instanceof String) {\n      data = _.orderBy(data, this.caseInsensitiveIteratee(sortBy), [this.sortOrder]);\n    } else {\n      data = _.orderBy(data, sortBy, [this.sortOrder]);\n    }\n    data = _.slice(data, offset, offset + this.rowsOnPage);\n    this.data = data;\n  };\n  DataTable.prototype.caseInsensitiveIteratee = function (sortBy) {\n    return function (row) {\n      var value = row;\n      for (var _i = 0, _a = sortBy.split('.'); _i < _a.length; _i++) {\n        var sortByProperty = _a[_i];\n        if (value) {\n          value = value[sortByProperty];\n        }\n      }\n      if (value && typeof value === 'string' || value instanceof String) {\n        return value.toLowerCase();\n      }\n      return value;\n    };\n  };\n  DataTable.decorators = [{\n    type: core_1.Directive,\n    args: [{\n      selector: 'table[mfData]',\n      exportAs: 'mfDataTable'\n    }]\n  }];\n  DataTable.ctorParameters = function () {\n    return [{\n      type: core_1.IterableDiffers\n    }];\n  };\n  DataTable.propDecorators = {\n    \"inputData\": [{\n      type: core_1.Input,\n      args: [\"mfData\"]\n    }],\n    \"sortBy\": [{\n      type: core_1.Input,\n      args: [\"mfSortBy\"]\n    }],\n    \"sortOrder\": [{\n      type: core_1.Input,\n      args: [\"mfSortOrder\"]\n    }],\n    \"sortByChange\": [{\n      type: core_1.Output,\n      args: [\"mfSortByChange\"]\n    }],\n    \"sortOrderChange\": [{\n      type: core_1.Output,\n      args: [\"mfSortOrderChange\"]\n    }],\n    \"rowsOnPage\": [{\n      type: core_1.Input,\n      args: [\"mfRowsOnPage\"]\n    }],\n    \"activePage\": [{\n      type: core_1.Input,\n      args: [\"mfActivePage\"]\n    }]\n  };\n  return DataTable;\n}();\nexports.DataTable = DataTable;\n//# sourceMappingURL=DataTable.js.map", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}