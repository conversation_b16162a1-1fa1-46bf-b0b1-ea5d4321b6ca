{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Romanian [ro]\n//! author : <PERSON> : https://github.com/gurdiga\n//! author : <PERSON><PERSON> : https://github.com/avaly\n//! author : <PERSON> : https://github.com/cepem\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  function relativeTimeWithPlural(number, withoutSuffix, key) {\n    var format = {\n        ss: 'secunde',\n        mm: 'minute',\n        hh: 'ore',\n        dd: 'zile',\n        ww: 'săptămâni',\n        MM: 'luni',\n        yy: 'ani'\n      },\n      separator = ' ';\n    if (number % 100 >= 20 || number >= 100 && number % 100 === 0) {\n      separator = ' de ';\n    }\n    return number + separator + format[key];\n  }\n  var ro = moment.defineLocale('ro', {\n    months: 'ianuarie_februarie_martie_aprilie_mai_iunie_iulie_august_septembrie_octombrie_noiembrie_decembrie'.split('_'),\n    monthsShort: 'ian._feb._mart._apr._mai_iun._iul._aug._sept._oct._nov._dec.'.split('_'),\n    monthsParseExact: true,\n    weekdays: 'duminică_luni_marți_miercuri_joi_vineri_sâmbătă'.split('_'),\n    weekdaysShort: 'Dum_Lun_Mar_Mie_Joi_Vin_Sâm'.split('_'),\n    weekdaysMin: 'Du_Lu_Ma_Mi_Jo_Vi_Sâ'.split('_'),\n    longDateFormat: {\n      LT: 'H:mm',\n      LTS: 'H:mm:ss',\n      L: 'DD.MM.YYYY',\n      LL: 'D MMMM YYYY',\n      LLL: 'D MMMM YYYY H:mm',\n      LLLL: 'dddd, D MMMM YYYY H:mm'\n    },\n    calendar: {\n      sameDay: '[azi la] LT',\n      nextDay: '[mâine la] LT',\n      nextWeek: 'dddd [la] LT',\n      lastDay: '[ieri la] LT',\n      lastWeek: '[fosta] dddd [la] LT',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: 'peste %s',\n      past: '%s în urmă',\n      s: 'câteva secunde',\n      ss: relativeTimeWithPlural,\n      m: 'un minut',\n      mm: relativeTimeWithPlural,\n      h: 'o oră',\n      hh: relativeTimeWithPlural,\n      d: 'o zi',\n      dd: relativeTimeWithPlural,\n      w: 'o săptămână',\n      ww: relativeTimeWithPlural,\n      M: 'o lună',\n      MM: relativeTimeWithPlural,\n      y: 'un an',\n      yy: relativeTimeWithPlural\n    },\n    week: {\n      dow: 1,\n      // Monday is the first day of the week.\n      doy: 7 // The week that contains Jan 7th is the first week of the year.\n    }\n  });\n  return ro;\n});", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}