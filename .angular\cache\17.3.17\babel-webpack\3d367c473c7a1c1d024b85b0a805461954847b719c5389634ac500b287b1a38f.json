{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nvar core_1 = require(\"@angular/core\");\nvar DataTable_1 = require(\"./DataTable\");\nvar DefaultSorter = function () {\n  function DefaultSorter(mfTable) {\n    this.mfTable = mfTable;\n    this.isSortedByMeAsc = false;\n    this.isSortedByMeDesc = false;\n  }\n  DefaultSorter.prototype.ngOnInit = function () {\n    var _this = this;\n    this.mfTable.onSortChange.subscribe(function (event) {\n      _this.isSortedByMeAsc = event.sortBy == _this.sortBy && event.sortOrder == \"asc\";\n      _this.isSortedByMeDesc = event.sortBy == _this.sortBy && event.sortOrder == \"desc\";\n    });\n  };\n  DefaultSorter.prototype.sort = function () {\n    if (this.isSortedByMeAsc) {\n      this.mfTable.setSort(this.sortBy, \"desc\");\n    } else {\n      this.mfTable.setSort(this.sortBy, \"asc\");\n    }\n  };\n  DefaultSorter.decorators = [{\n    type: core_1.Component,\n    args: [{\n      selector: \"mfDefaultSorter\",\n      template: \"\\n        <a style=\\\"cursor: pointer\\\" (click)=\\\"sort()\\\" class=\\\"text-nowrap\\\">\\n            <ng-content></ng-content>\\n            <span *ngIf=\\\"isSortedByMeAsc\\\" class=\\\"glyphicon glyphicon-triangle-top\\\" aria-hidden=\\\"true\\\"></span>\\n            <span *ngIf=\\\"isSortedByMeDesc\\\" class=\\\"glyphicon glyphicon-triangle-bottom\\\" aria-hidden=\\\"true\\\"></span>\\n        </a>\"\n    }]\n  }];\n  DefaultSorter.ctorParameters = function () {\n    return [{\n      type: DataTable_1.DataTable\n    }];\n  };\n  DefaultSorter.propDecorators = {\n    \"sortBy\": [{\n      type: core_1.Input,\n      args: [\"by\"]\n    }]\n  };\n  return DefaultSorter;\n}();\nexports.DefaultSorter = DefaultSorter;\n//# sourceMappingURL=DefaultSorter.js.map", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}