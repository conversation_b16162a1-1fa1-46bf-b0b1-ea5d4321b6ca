{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.connect = void 0;\nvar Subject_1 = require(\"../Subject\");\nvar innerFrom_1 = require(\"../observable/innerFrom\");\nvar lift_1 = require(\"../util/lift\");\nvar fromSubscribable_1 = require(\"../observable/fromSubscribable\");\nvar DEFAULT_CONFIG = {\n  connector: function () {\n    return new Subject_1.Subject();\n  }\n};\nfunction connect(selector, config) {\n  if (config === void 0) {\n    config = DEFAULT_CONFIG;\n  }\n  var connector = config.connector;\n  return lift_1.operate(function (source, subscriber) {\n    var subject = connector();\n    innerFrom_1.innerFrom(selector(fromSubscribable_1.fromSubscribable(subject))).subscribe(subscriber);\n    subscriber.add(source.subscribe(subject));\n  });\n}\nexports.connect = connect;\n//# sourceMappingURL=connect.js.map", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}