{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.isPromise = void 0;\nvar isFunction_1 = require(\"./isFunction\");\nfunction isPromise(value) {\n  return isFunction_1.isFunction(value === null || value === void 0 ? void 0 : value.then);\n}\nexports.isPromise = isPromise;\n//# sourceMappingURL=isPromise.js.map", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}