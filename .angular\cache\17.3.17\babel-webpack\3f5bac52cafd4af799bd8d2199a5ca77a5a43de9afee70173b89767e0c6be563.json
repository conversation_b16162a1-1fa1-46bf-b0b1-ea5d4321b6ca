{"ast": null, "code": "import { map } from './map';\nimport { innerFrom } from '../observable/innerFrom';\nimport { operate } from '../util/lift';\nimport { mergeInternals } from './mergeInternals';\nimport { isFunction } from '../util/isFunction';\nexport function mergeMap(project, resultSelector, concurrent = Infinity) {\n  if (isFunction(resultSelector)) {\n    return mergeMap((a, i) => map((b, ii) => resultSelector(a, b, i, ii))(innerFrom(project(a, i))), concurrent);\n  } else if (typeof resultSelector === 'number') {\n    concurrent = resultSelector;\n  }\n  return operate((source, subscriber) => mergeInternals(source, subscriber, project, concurrent));\n}\n//# sourceMappingURL=mergeMap.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}