{"ast": null, "code": "import { debounceTime, map } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"./../webapi.service\";\nimport * as i3 from \"../../../node_modules/@angular/router\";\nconst _c0 = a0 => ({\n  \"display\": a0,\n  \"opacity\": 1\n});\nfunction ProductgroupComponent_ng_template_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"label\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const r_r2 = ctx.result;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(r_r2.itemid);\n  }\n}\nfunction ProductgroupComponent_tr_31_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"th\", 42);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"th\", 42);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\", 43);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\", 42);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\", 44)(10, \"button\", 45);\n    i0.ɵɵlistener(\"click\", function ProductgroupComponent_tr_31_Template_button_click_10_listener() {\n      const item_r4 = i0.ɵɵrestoreView(_r3).$implicit;\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.geteditgrouping(item_r4.id, item_r4.itemid, item_r4.numgroup));\n    });\n    i0.ɵɵtext(11, \" Edit \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const item_r4 = ctx.$implicit;\n    const i_r6 = ctx.index;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i_r6 + 1);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r4.itemid);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r4.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r4.numgroup);\n  }\n}\nfunction ProductgroupComponent_ng_template_44_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"label\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const r_r7 = ctx.result;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(r_r7.itemid);\n  }\n}\nexport let ProductgroupComponent = /*#__PURE__*/(() => {\n  class ProductgroupComponent {\n    constructor(http, service, router) {\n      this.http = http;\n      this.service = service;\n      this.router = router;\n      this.mdlSampleIsOpen = false;\n      this.alt = '';\n      this.checkreload = true;\n      this.urlApi = 'http://119.59.112.478:1433';\n      this.url = '';\n      this.itemidedit = '';\n      this.editproductgroup = '';\n      this.itemid = '';\n      this.productgroup = '';\n      this.productgrouplist = [];\n      this.productgrouplistse = [];\n      this.numcheckid = 0;\n      /*wb:Workbook = new Workbook();*/\n      this.dataimport = [];\n      this.permisstiondata = [];\n      this.exportbtn = true;\n      this.searchbtn = true;\n      this.search = text$ => text$.pipe(debounceTime(200), map(term => term === '' ? [] : this.productlist.filter(v => v.itemid.toLowerCase().indexOf(term.toLowerCase()) > -1).slice(0, 10)));\n      this.formatter = x => x.itemid;\n      localStorage.removeItem('DataSOderreview');\n      localStorage.removeItem('DataSOderlist');\n      this.url = service.geturlservice();\n      this.permisstiondata = JSON.parse(sessionStorage.getItem('menu'));\n      if (this.permisstiondata == null) {\n        this.router.navigate(['login']);\n      } else {\n        this.exportbtn = !this.permisstiondata[11].flag_print;\n        this.searchbtn = !this.permisstiondata[11].flag_action;\n      }\n    }\n    ngOnInit() {\n      this.http.get(this.url + 'productauto/admin').subscribe(res => {\n        if (res.length > 0) {\n          this.productlist = res;\n          this.getdataproductgroup();\n        }\n      });\n    }\n    checkaitemid(valueid, page) {\n      if (valueid != undefined) {}\n    }\n    searchproductgroup(value) {\n      if (value.length > 0) {\n        this.productgrouplistse = this.productgrouplist.filter(v => v.itemid.toLowerCase().indexOf(value.toLowerCase()) > -1).slice(0, 50);\n      } else {\n        this.productgrouplistse = this.productgrouplist;\n      }\n    }\n    addproductgroup() {\n      if (this.model == undefined) {\n        this.openModal(true, 'กรุณาป้อนรหัสสิ้นค้า', false);\n      } else if (this.productgroup == '') {\n        this.openModal(true, 'กรุณาป้อนกลุ่มสินค้า', false);\n      } else {\n        var item;\n        var st = '';\n        if (this.model.itemid == undefined) {\n          item = this.model;\n        } else {\n          item = this.model.itemid;\n        }\n        st = item.toString();\n        this.http.get(this.url + 'checkitemidtran/' + st.toUpperCase() + '/G').subscribe(res => {\n          if (res[0].num >= 1) {\n            this.openModal(true, 'รหัสสินค้า' + st.toUpperCase() + 'มีในระบบแล้ว', false);\n          } else {\n            var urlpost = `${this.url}/${'add_product_group'}/${st.toUpperCase()}/${this.productgroup}`;\n            this.http.post(urlpost, '').subscribe(res => {\n              if (res == true) {\n                this.getdataproductgroup();\n                this.openModal(true, 'บันทึกข้อมูลเสร็จสิ้น', true);\n                this.productgroup = '';\n                this.model = [];\n              }\n            });\n          }\n        });\n      }\n    }\n    /*getexcel(){\n      this.wb.xlsx.readFile(\"./e.xlsx\").then(function(){\n        //sheet object\n        let sheet:Worksheet = this.wb.getWorksheet(\"Sheet1\");\n    \n        let totalRowsIncludingEmptyRows:number = sheet.rowCount\n        console.log(\"total number of rows : \"+totalRowsIncludingEmptyRows)\n        let emptyCell = sheet.getRow(1).getCell(2).value\n        console.log(\"Empty cell's value : \"+emptyCell)\n        let valueCell = sheet.getRow(2).getCell(2).value\n        console.log(\"Value cell's value : \"+valueCell)\n        let nullCell = sheet.getRow(3).getCell(2).value\n        console.log(\"null cell's value : \"+nullCell)\n      });\n    }*/\n    geteditgrouping(id, itemid, numgroup) {\n      this.idgrouping = id;\n      this.itemidedit = itemid;\n      this.editproductgroup = numgroup;\n    }\n    updategrouping() {\n      var idcheck;\n      if (this.model == undefined) {\n        idcheck = this.itemidedit;\n      } else {\n        idcheck = this.model.itemid;\n      }\n      var urlpost = `${this.url}/${'update_product_group'}/${this.idgrouping}/${idcheck}/${this.editproductgroup}`;\n      this.http.post(urlpost, '').subscribe(res => {\n        if (res == true) {\n          this.getdataproductgroup();\n          this.openModal(true, 'บันทึกข้อมูลเสร็จสิ้น', true);\n        }\n      });\n    }\n    getdataproductgroup() {\n      this.http.get(this.url + 'get_product_group').subscribe(res => {\n        if (res.length > 0) {\n          this.productgrouplist = res;\n          this.productgrouplistse = this.productgrouplist;\n        }\n      });\n    }\n    deletegrouping() {\n      var idcheck;\n      if (this.model == undefined) {\n        idcheck = this.itemidedit;\n      } else {\n        idcheck = this.model.itemid;\n      }\n      var urlpost = `${this.url}${'deletepackproduct'}/${idcheck}/${'G'}`;\n      this.http.post(urlpost, '').subscribe(res => {\n        if (res == true) {\n          this.getdataproductgroup();\n          this.openModal(true, 'ลบข้อมูลเสร็จสิ้น', true);\n        }\n      });\n    }\n    openModal(open, text, load) {\n      this.mdlSampleIsOpen = open;\n      this.alt = text;\n      this.checkreload = load;\n    }\n    closemodel(cl) {\n      this.mdlSampleIsOpen = false;\n      if (this.checkreload == true) {\n        this.router.navigate(['productgroup']);\n        this.getdataproductgroup();\n      }\n    }\n    static {\n      this.ɵfac = function ProductgroupComponent_Factory(t) {\n        return new (t || ProductgroupComponent)(i0.ɵɵdirectiveInject(i1.HttpClient), i0.ɵɵdirectiveInject(i2.WebapiService), i0.ɵɵdirectiveInject(i3.Router));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: ProductgroupComponent,\n        selectors: [[\"app-productgroup\"]],\n        decls: 69,\n        vars: 19,\n        consts: [[\"rt\", \"\"], [2, \"padding-top\", \"60px\"], [1, \"container-fluid\"], [1, \"p-sm-1\", \"bg-secondary\", \"text-white\", \"text-center\"], [\"novalidate\", \"\", 1, \"needs-validation\"], [1, \"form-row\"], [1, \"col-md-7\", \"mb-2\"], [\"id\", \"typeahead-template\", \"placeholder\", \"\\u0E23\\u0E2B\\u0E31\\u0E2A\\u0E2A\\u0E34\\u0E19\\u0E04\\u0E49\\u0E32\", \"type\", \"text\", \"name\", \"model\", 1, \"form-control\", \"form-control-sm\", 3, \"ngModelChange\", \"ngModel\", \"ngbTypeahead\", \"resultTemplate\", \"inputFormatter\"], [1, \"col-md-2\", \"mb-2\"], [\"type\", \"text\", \"name\", \"productgroup\", \"id\", \"productgroup\", \"placeholder\", \"\\u0E01\\u0E25\\u0E38\\u0E48\\u0E21\\u0E2A\\u0E34\\u0E19\\u0E04\\u0E49\\u0E32\", 1, \"form-control\", \"form-control-sm\", 3, \"ngModelChange\", \"ngModel\"], [1, \"col-md-1\", \"mb-2\", \"col-12\", \"text-center\", \"text-sm-center\", \"text-md-center\", \"text-lg-left\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", \"btn-sm\", \"font-weight-light\", 3, \"click\", \"disabled\"], [1, \"col-md-2\", \"mb-2\", \"float-right\"], [\"placeholder\", \"\\u0E04\\u0E49\\u0E19\\u0E2B\\u0E32\\u0E23\\u0E2B\\u0E31\\u0E2A\\u0E2A\\u0E34\\u0E19\\u0E04\\u0E49\\u0E32\", 1, \"form-control\", \"form-control-sm\", 3, \"keyup\"], [1, \"table\", \"table-hover\", \"table-bordered\", \"table-sm\"], [1, \"text-sm-center\", \"bg-light\"], [\"scope\", \"col\", 1, \"font-weight-normal\"], [\"scope\", \"col\", 1, \"font-weight-normal\", 2, \"width\", \"10%\"], [4, \"ngFor\", \"ngForOf\"], [\"id\", \"editgroupingproducr\", \"tabindex\", \"-1\", \"role\", \"dialog\", \"aria-labelledby\", \"exampleModalLabel\", \"aria-hidden\", \"true\", 1, \"modal\", \"fade\"], [\"role\", \"document\", 1, \"modal-dialog\"], [1, \"modal-content\"], [1, \"modal-header\"], [\"id\", \"exampleModalLabel\", 1, \"modal-title\"], [\"type\", \"button\", \"data-dismiss\", \"modal\", \"aria-label\", \"Close\", 1, \"close\"], [\"aria-hidden\", \"true\"], [1, \"modal-body\"], [1, \"input-group\"], [1, \"col-md-12\", \"mb-2\"], [\"id\", \"typeahead-template\", \"type\", \"text\", \"name\", \"model\", 1, \"form-control\", \"form-control-sm\", 3, \"ngModelChange\", \"placeholder\", \"ngModel\", \"ngbTypeahead\", \"resultTemplate\", \"inputFormatter\"], [\"type\", \"text\", \"name\", \"editproductgroup\", \"id\", \"editproductgroup\", \"placeholder\", \"\\u0E01\\u0E25\\u0E38\\u0E48\\u0E21\\u0E2A\\u0E34\\u0E19\\u0E04\\u0E49\\u0E32\", 1, \"form-control\", \"form-control-sm\", 3, \"ngModelChange\", \"ngModel\"], [1, \"modal-footer\"], [\"type\", \"button\", \"data-dismiss\", \"modal\", 1, \"btn\", \"btn-secondary\"], [\"type\", \"button\", \"data-dismiss\", \"modal\", 1, \"btn\", \"btn-primary\", 3, \"click\", \"disabled\"], [\"type\", \"button\", \"data-dismiss\", \"modal\", 1, \"btn\", \"btn-danger\", 3, \"click\", \"disabled\"], [\"id\", \"mdlSample\", \"role\", \"dialog\", 1, \"modal\", \"fade\", 3, \"ngStyle\"], [1, \"modal-dialog\", \"modal-md\"], [1, \"modal-header\", \"colhaederal\"], [1, \"modal-title\"], [\"align\", \"right\", 1, \"modal-footer\"], [\"type\", \"button\", \"id\", \"btnClose\", 1, \"btn\", \"btn-danger\", 3, \"click\"], [1, \"fa\", \"fa-times\", \"fa-fw\"], [1, \"text-sm-center\", \"font-weight-normal\"], [1, \"text-sm-left\", \"font-weight-normal\"], [1, \"text-center\"], [\"data-toggle\", \"modal\", \"data-target\", \"#editgroupingproducr\", \"aria-expanded\", \"true\", \"aria-controls\", \"collapseOne\", 1, \"btn\", \"btn-link\", \"font-weight-light\", 2, \"padding\", \"0pt\", 3, \"click\"]],\n        template: function ProductgroupComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            const _r1 = i0.ɵɵgetCurrentView();\n            i0.ɵɵelement(0, \"app-topmenu\");\n            i0.ɵɵelementStart(1, \"section\", 1)(2, \"div\", 2)(3, \"h5\", 3);\n            i0.ɵɵtext(4, \"Grouping Product\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(5, \"form\", 4)(6, \"div\", 5)(7, \"div\", 6);\n            i0.ɵɵtemplate(8, ProductgroupComponent_ng_template_8_Template, 2, 1, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n            i0.ɵɵelementStart(10, \"input\", 7);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function ProductgroupComponent_Template_input_ngModelChange_10_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.model, $event) || (ctx.model = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(11, \"div\", 8)(12, \"input\", 9);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function ProductgroupComponent_Template_input_ngModelChange_12_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.productgroup, $event) || (ctx.productgroup = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(13, \"div\", 10)(14, \"button\", 11);\n            i0.ɵɵlistener(\"click\", function ProductgroupComponent_Template_button_click_14_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.addproductgroup());\n            });\n            i0.ɵɵtext(15, \"\\u0E40\\u0E1E\\u0E34\\u0E48\\u0E21\\u0E23\\u0E32\\u0E22\\u0E01\\u0E32\\u0E23\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(16, \"div\", 12)(17, \"input\", 13);\n            i0.ɵɵlistener(\"keyup\", function ProductgroupComponent_Template_input_keyup_17_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.searchproductgroup($event.target.value));\n            });\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(18, \"table\", 14)(19, \"thead\")(20, \"tr\", 15)(21, \"th\", 16);\n            i0.ɵɵtext(22, \"Item\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(23, \"th\", 16);\n            i0.ɵɵtext(24, \"\\u0E23\\u0E2B\\u0E31\\u0E2A\\u0E2A\\u0E34\\u0E19\\u0E04\\u0E49\\u0E32\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(25, \"th\", 16);\n            i0.ɵɵtext(26, \"\\u0E2A\\u0E34\\u0E19\\u0E04\\u0E49\\u0E32\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(27, \"th\", 17);\n            i0.ɵɵtext(28, \"\\u0E01\\u0E25\\u0E38\\u0E48\\u0E21\\u0E2A\\u0E34\\u0E19\\u0E04\\u0E49\\u0E32\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(29, \"th\", 17);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(30, \"tbody\");\n            i0.ɵɵtemplate(31, ProductgroupComponent_tr_31_Template, 12, 4, \"tr\", 18);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(32, \"div\", 19)(33, \"div\", 20)(34, \"div\", 21)(35, \"div\", 22)(36, \"h5\", 23);\n            i0.ɵɵtext(37, \"Edit Grouping Product\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(38, \"button\", 24)(39, \"span\", 25);\n            i0.ɵɵtext(40, \"\\u00D7\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(41, \"div\", 26)(42, \"div\", 27)(43, \"div\", 28);\n            i0.ɵɵtemplate(44, ProductgroupComponent_ng_template_44_Template, 2, 1, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n            i0.ɵɵelementStart(46, \"input\", 29);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function ProductgroupComponent_Template_input_ngModelChange_46_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.model, $event) || (ctx.model = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(47, \"div\", 27)(48, \"div\", 28)(49, \"input\", 30);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function ProductgroupComponent_Template_input_ngModelChange_49_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.editproductgroup, $event) || (ctx.editproductgroup = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(50, \"div\", 31)(51, \"button\", 32);\n            i0.ɵɵtext(52, \"Close\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(53, \"button\", 33);\n            i0.ɵɵlistener(\"click\", function ProductgroupComponent_Template_button_click_53_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.updategrouping());\n            });\n            i0.ɵɵtext(54, \"Update\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(55, \"button\", 34);\n            i0.ɵɵlistener(\"click\", function ProductgroupComponent_Template_button_click_55_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.deletegrouping());\n            });\n            i0.ɵɵtext(56, \"Delete\");\n            i0.ɵɵelementEnd()()()()();\n            i0.ɵɵelementStart(57, \"div\", 35)(58, \"div\", 36)(59, \"div\", 21)(60, \"div\", 37)(61, \"h4\", 38);\n            i0.ɵɵtext(62, \"Report\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(63, \"div\", 26);\n            i0.ɵɵtext(64);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(65, \"div\", 39)(66, \"button\", 40);\n            i0.ɵɵlistener(\"click\", function ProductgroupComponent_Template_button_click_66_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.closemodel(false));\n            });\n            i0.ɵɵelement(67, \"i\", 41);\n            i0.ɵɵtext(68, \" \\u0E1B\\u0E34\\u0E14\");\n            i0.ɵɵelementEnd()()()()()();\n          }\n          if (rf & 2) {\n            const rt_r8 = i0.ɵɵreference(9);\n            i0.ɵɵadvance(10);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.model);\n            i0.ɵɵproperty(\"ngbTypeahead\", ctx.search)(\"resultTemplate\", rt_r8)(\"inputFormatter\", ctx.formatter);\n            i0.ɵɵadvance(2);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.productgroup);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"disabled\", ctx.searchbtn);\n            i0.ɵɵadvance(17);\n            i0.ɵɵproperty(\"ngForOf\", ctx.productgrouplistse);\n            i0.ɵɵadvance(15);\n            i0.ɵɵpropertyInterpolate(\"placeholder\", ctx.itemidedit);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.model);\n            i0.ɵɵproperty(\"ngbTypeahead\", ctx.search)(\"resultTemplate\", rt_r8)(\"inputFormatter\", ctx.formatter);\n            i0.ɵɵadvance(3);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.editproductgroup);\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"disabled\", ctx.searchbtn);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"disabled\", ctx.searchbtn);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(17, _c0, ctx.mdlSampleIsOpen ? \"block\" : \"none\"));\n            i0.ɵɵadvance(7);\n            i0.ɵɵtextInterpolate(ctx.alt);\n          }\n        },\n        styles: [\".autocomplete[_ngcontent-%COMP%]{position:relative;display:inline-block}.autocomplete-items[_ngcontent-%COMP%]{position:absolute;border:1px solid #d4d4d4;border-bottom:none;border-top:none;z-index:99;top:100%;left:0;right:0}.autocomplete-items[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]{padding:10px;cursor:pointer;background-color:#fff;border-bottom:1px solid #d4d4d4}.autocomplete-items[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]:hover{background-color:#e9e9e9}.autocomplete-active[_ngcontent-%COMP%]{background-color:#1e90ff!important;color:#fff}\"]\n      });\n    }\n  }\n  return ProductgroupComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}