{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.using = void 0;\nvar Observable_1 = require(\"../Observable\");\nvar innerFrom_1 = require(\"./innerFrom\");\nvar empty_1 = require(\"./empty\");\nfunction using(resourceFactory, observableFactory) {\n  return new Observable_1.Observable(function (subscriber) {\n    var resource = resourceFactory();\n    var result = observableFactory(resource);\n    var source = result ? innerFrom_1.innerFrom(result) : empty_1.EMPTY;\n    source.subscribe(subscriber);\n    return function () {\n      if (resource) {\n        resource.unsubscribe();\n      }\n    };\n  });\n}\nexports.using = using;\n//# sourceMappingURL=using.js.map", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}