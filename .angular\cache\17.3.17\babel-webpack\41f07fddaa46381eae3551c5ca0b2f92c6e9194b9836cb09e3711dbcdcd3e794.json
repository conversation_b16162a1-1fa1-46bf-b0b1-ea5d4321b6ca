{"ast": null, "code": "/* jshint browser: true */\n\n(function () {\n  // We'll copy the properties below into the mirror div.\n  // Note that some browsers, such as Firefox, do not concatenate properties\n  // into their shorthand (e.g. padding-top, padding-bottom etc. -> padding),\n  // so we have to list every single property explicitly.\n  var properties = ['direction',\n  // RTL support\n  'boxSizing', 'width',\n  // on Chrome and IE, exclude the scrollbar, so the mirror div wraps exactly as the textarea does\n  'height', 'overflowX', 'overflowY',\n  // copy the scrollbar for IE\n\n  'borderTopWidth', 'borderRightWidth', 'borderBottomWidth', 'borderLeftWidth', 'borderStyle', 'paddingTop', 'paddingRight', 'paddingBottom', 'paddingLeft',\n  // https://developer.mozilla.org/en-US/docs/Web/CSS/font\n  'fontStyle', 'fontVariant', 'fontWeight', 'fontStretch', 'fontSize', 'fontSizeAdjust', 'lineHeight', 'fontFamily', 'textAlign', 'textTransform', 'textIndent', 'textDecoration',\n  // might not make a difference, but better be safe\n\n  'letterSpacing', 'wordSpacing', 'tabSize', 'MozTabSize'];\n  var isBrowser = typeof window !== 'undefined';\n  var isFirefox = isBrowser && window.mozInnerScreenX != null;\n  function getCaretCoordinates(element, position, options) {\n    if (!isBrowser) {\n      throw new Error('textarea-caret-position#getCaretCoordinates should only be called in a browser');\n    }\n    var debug = options && options.debug || false;\n    if (debug) {\n      var el = document.querySelector('#input-textarea-caret-position-mirror-div');\n      if (el) el.parentNode.removeChild(el);\n    }\n\n    // The mirror div will replicate the textarea's style\n    var div = document.createElement('div');\n    div.id = 'input-textarea-caret-position-mirror-div';\n    document.body.appendChild(div);\n    var style = div.style;\n    var computed = window.getComputedStyle ? window.getComputedStyle(element) : element.currentStyle; // currentStyle for IE < 9\n    var isInput = element.nodeName === 'INPUT';\n\n    // Default textarea styles\n    style.whiteSpace = 'pre-wrap';\n    if (!isInput) style.wordWrap = 'break-word'; // only for textarea-s\n\n    // Position off-screen\n    style.position = 'absolute'; // required to return coordinates properly\n    if (!debug) style.visibility = 'hidden'; // not 'display: none' because we want rendering\n\n    // Transfer the element's properties to the div\n    properties.forEach(function (prop) {\n      if (isInput && prop === 'lineHeight') {\n        // Special case for <input>s because text is rendered centered and line height may be != height\n        style.lineHeight = computed.height;\n      } else {\n        style[prop] = computed[prop];\n      }\n    });\n    if (isFirefox) {\n      // Firefox lies about the overflow property for textareas: https://bugzilla.mozilla.org/show_bug.cgi?id=984275\n      if (element.scrollHeight > parseInt(computed.height)) style.overflowY = 'scroll';\n    } else {\n      style.overflow = 'hidden'; // for Chrome to not render a scrollbar; IE keeps overflowY = 'scroll'\n    }\n    div.textContent = element.value.substring(0, position);\n    // The second special handling for input type=\"text\" vs textarea:\n    // spaces need to be replaced with non-breaking spaces - http://stackoverflow.com/a/13402035/1269037\n    if (isInput) div.textContent = div.textContent.replace(/\\s/g, '\\u00a0');\n    var span = document.createElement('span');\n    // Wrapping must be replicated *exactly*, including when a long word gets\n    // onto the next line, with whitespace at the end of the line before (#7).\n    // The  *only* reliable way to do that is to copy the *entire* rest of the\n    // textarea's content into the <span> created at the caret position.\n    // For inputs, just '.' would be enough, but no need to bother.\n    span.textContent = element.value.substring(position) || '.'; // || because a completely empty faux span doesn't render at all\n    div.appendChild(span);\n    var coordinates = {\n      top: span.offsetTop + parseInt(computed['borderTopWidth']),\n      left: span.offsetLeft + parseInt(computed['borderLeftWidth']),\n      height: parseInt(computed['lineHeight'])\n    };\n    if (debug) {\n      span.style.backgroundColor = '#aaa';\n    } else {\n      document.body.removeChild(div);\n    }\n    return coordinates;\n  }\n  if (typeof module != 'undefined' && typeof module.exports != 'undefined') {\n    module.exports = getCaretCoordinates;\n  } else if (isBrowser) {\n    window.getCaretCoordinates = getCaretCoordinates;\n  }\n})();", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}