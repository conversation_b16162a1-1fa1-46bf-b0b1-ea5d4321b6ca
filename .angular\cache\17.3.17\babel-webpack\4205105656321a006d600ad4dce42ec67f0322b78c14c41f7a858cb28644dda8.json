{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Directive, Input, Injectable, Component, HostBinding, EventEmitter, Output, NgModule } from '@angular/core';\nimport * as i2 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nconst _c0 = [\"*\"];\nconst _c1 = a0 => [\"nav-item\", a0];\nfunction TabsetComponent_li_1_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"span\", 7);\n    i0.ɵɵlistener(\"click\", function TabsetComponent_li_1_span_4_Template_span_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const tabz_r4 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      $event.preventDefault();\n      return i0.ɵɵresetView(ctx_r2.removeTab(tabz_r4));\n    });\n    i0.ɵɵtext(1, \" \\u274C\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TabsetComponent_li_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\", 3);\n    i0.ɵɵlistener(\"keydown\", function TabsetComponent_li_1_Template_li_keydown_0_listener($event) {\n      const i_r2 = i0.ɵɵrestoreView(_r1).index;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.keyNavActions($event, i_r2));\n    });\n    i0.ɵɵelementStart(1, \"a\", 4);\n    i0.ɵɵlistener(\"click\", function TabsetComponent_li_1_Template_a_click_1_listener() {\n      const tabz_r4 = i0.ɵɵrestoreView(_r1).$implicit;\n      return i0.ɵɵresetView(tabz_r4.active = true);\n    });\n    i0.ɵɵelementStart(2, \"span\", 5);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, TabsetComponent_li_1_span_4_Template, 2, 0, \"span\", 6);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const tabz_r4 = ctx.$implicit;\n    i0.ɵɵclassProp(\"active\", tabz_r4.active)(\"disabled\", tabz_r4.disabled);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(15, _c1, tabz_r4.customClass || \"\"));\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"active\", tabz_r4.active)(\"disabled\", tabz_r4.disabled);\n    i0.ɵɵattribute(\"aria-controls\", tabz_r4.id ? tabz_r4.id : \"\")(\"aria-selected\", !!tabz_r4.active)(\"id\", tabz_r4.id ? tabz_r4.id + \"-link\" : \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTransclude\", tabz_r4.headingRef);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(tabz_r4.heading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", tabz_r4.removable);\n  }\n}\nlet NgTranscludeDirective = /*#__PURE__*/(() => {\n  class NgTranscludeDirective {\n    set ngTransclude(templateRef) {\n      this._ngTransclude = templateRef;\n      if (templateRef) {\n        this.viewRef.createEmbeddedView(templateRef);\n      }\n    }\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    get ngTransclude() {\n      return this._ngTransclude;\n    }\n    constructor(viewRef) {\n      this.viewRef = viewRef;\n    }\n    static {\n      this.ɵfac = function NgTranscludeDirective_Factory(t) {\n        return new (t || NgTranscludeDirective)(i0.ɵɵdirectiveInject(i0.ViewContainerRef));\n      };\n    }\n    static {\n      this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n        type: NgTranscludeDirective,\n        selectors: [[\"\", \"ngTransclude\", \"\"]],\n        inputs: {\n          ngTransclude: \"ngTransclude\"\n        }\n      });\n    }\n  }\n  return NgTranscludeDirective;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet TabsetConfig = /*#__PURE__*/(() => {\n  class TabsetConfig {\n    constructor() {\n      /** provides default navigation context class: 'tabs' or 'pills' */\n      this.type = 'tabs';\n      /** provides possibility to set keyNavigations enable or disable, by default is enable */\n      this.isKeysAllowed = true;\n      /** aria label for tab list */\n      this.ariaLabel = 'Tabs';\n    }\n    static {\n      this.ɵfac = function TabsetConfig_Factory(t) {\n        return new (t || TabsetConfig)();\n      };\n    }\n    static {\n      this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n        token: TabsetConfig,\n        factory: TabsetConfig.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return TabsetConfig;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n// todo: add active event to tab\n// todo: fix? mixing static and dynamic tabs position tabs in order of creation\nlet TabsetComponent = /*#__PURE__*/(() => {\n  class TabsetComponent {\n    /** if true tabs will be placed vertically */\n    get vertical() {\n      return this._vertical;\n    }\n    set vertical(value) {\n      this._vertical = value;\n      this.setClassMap();\n    }\n    /** if true tabs fill the container and have a consistent width */\n    get justified() {\n      return this._justified;\n    }\n    set justified(value) {\n      this._justified = value;\n      this.setClassMap();\n    }\n    /** navigation context class: 'tabs' or 'pills' */\n    get type() {\n      return this._type;\n    }\n    set type(value) {\n      this._type = value;\n      this.setClassMap();\n    }\n    get isKeysAllowed() {\n      return this._isKeysAllowed;\n    }\n    set isKeysAllowed(value) {\n      this._isKeysAllowed = value;\n    }\n    constructor(config, renderer, elementRef) {\n      this.renderer = renderer;\n      this.elementRef = elementRef;\n      this.clazz = true;\n      this.tabs = [];\n      this.classMap = {};\n      /** aria label for tab list */\n      this.ariaLabel = 'Tabs';\n      this.isDestroyed = false;\n      this._vertical = false;\n      this._justified = false;\n      this._type = 'tabs';\n      this._isKeysAllowed = true;\n      Object.assign(this, config);\n    }\n    ngOnDestroy() {\n      this.isDestroyed = true;\n    }\n    addTab(tab) {\n      this.tabs.push(tab);\n      tab.active = this.tabs.length === 1 && !tab.active;\n    }\n    removeTab(tab, options = {\n      reselect: true,\n      emit: true\n    }) {\n      const index = this.tabs.indexOf(tab);\n      if (index === -1 || this.isDestroyed) {\n        return;\n      }\n      // Select a new tab if the tab to be removed is selected and not destroyed\n      if (options.reselect && tab.active && this.hasAvailableTabs(index)) {\n        const newActiveIndex = this.getClosestTabIndex(index);\n        this.tabs[newActiveIndex].active = true;\n      }\n      if (options.emit) {\n        tab.removed.emit(tab);\n      }\n      this.tabs.splice(index, 1);\n      if (tab.elementRef.nativeElement.parentNode) {\n        this.renderer.removeChild(tab.elementRef.nativeElement.parentNode, tab.elementRef.nativeElement);\n      }\n    }\n    keyNavActions(event, index) {\n      if (!this.isKeysAllowed) {\n        return;\n      }\n      const list = Array.from(this.elementRef.nativeElement.querySelectorAll('.nav-link'));\n      // const activeElList = list.filter((el: HTMLElement) => !el.classList.contains('disabled'));\n      if (event.keyCode === 13 || event.key === 'Enter' || event.keyCode === 32 || event.key === 'Space') {\n        event.preventDefault();\n        const currentTab = list[index % list.length];\n        currentTab.click();\n        return;\n      }\n      if (event.keyCode === 39 || event.key === 'RightArrow') {\n        let nextTab;\n        let shift = 1;\n        do {\n          nextTab = list[(index + shift) % list.length];\n          shift++;\n        } while (nextTab.classList.contains('disabled'));\n        nextTab.focus();\n        return;\n      }\n      if (event.keyCode === 37 || event.key === 'LeftArrow') {\n        let previousTab;\n        let shift = 1;\n        let i = index;\n        do {\n          if (i - shift < 0) {\n            i = list.length - 1;\n            previousTab = list[i];\n            shift = 0;\n          } else {\n            previousTab = list[i - shift];\n          }\n          shift++;\n        } while (previousTab.classList.contains('disabled'));\n        previousTab.focus();\n        return;\n      }\n      if (event.keyCode === 36 || event.key === 'Home') {\n        event.preventDefault();\n        let firstTab;\n        let shift = 0;\n        do {\n          firstTab = list[shift % list.length];\n          shift++;\n        } while (firstTab.classList.contains('disabled'));\n        firstTab.focus();\n        return;\n      }\n      if (event.keyCode === 35 || event.key === 'End') {\n        event.preventDefault();\n        let lastTab;\n        let shift = 1;\n        let i = index;\n        do {\n          if (i - shift < 0) {\n            i = list.length - 1;\n            lastTab = list[i];\n            shift = 0;\n          } else {\n            lastTab = list[i - shift];\n          }\n          shift++;\n        } while (lastTab.classList.contains('disabled'));\n        lastTab.focus();\n        return;\n      }\n      if (event.keyCode === 46 || event.key === 'Delete') {\n        if (this.tabs[index].removable) {\n          this.removeTab(this.tabs[index]);\n          if (list[index + 1]) {\n            list[(index + 1) % list.length].focus();\n            return;\n          }\n          if (list[list.length - 1]) {\n            list[0].focus();\n          }\n        }\n      }\n    }\n    getClosestTabIndex(index) {\n      const tabsLength = this.tabs.length;\n      if (!tabsLength) {\n        return -1;\n      }\n      for (let step = 1; step <= tabsLength; step += 1) {\n        const prevIndex = index - step;\n        const nextIndex = index + step;\n        if (this.tabs[prevIndex] && !this.tabs[prevIndex].disabled) {\n          return prevIndex;\n        }\n        if (this.tabs[nextIndex] && !this.tabs[nextIndex].disabled) {\n          return nextIndex;\n        }\n      }\n      return -1;\n    }\n    hasAvailableTabs(index) {\n      const tabsLength = this.tabs.length;\n      if (!tabsLength) {\n        return false;\n      }\n      for (let i = 0; i < tabsLength; i += 1) {\n        if (!this.tabs[i].disabled && i !== index) {\n          return true;\n        }\n      }\n      return false;\n    }\n    setClassMap() {\n      this.classMap = {\n        'nav-stacked': this.vertical,\n        'flex-column': this.vertical,\n        'nav-justified': this.justified,\n        [`nav-${this.type}`]: true\n      };\n    }\n    static {\n      this.ɵfac = function TabsetComponent_Factory(t) {\n        return new (t || TabsetComponent)(i0.ɵɵdirectiveInject(TabsetConfig), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ElementRef));\n      };\n    }\n    static {\n      this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n        type: TabsetComponent,\n        selectors: [[\"tabset\"]],\n        hostVars: 2,\n        hostBindings: function TabsetComponent_HostBindings(rf, ctx) {\n          if (rf & 2) {\n            i0.ɵɵclassProp(\"tab-container\", ctx.clazz);\n          }\n        },\n        inputs: {\n          vertical: \"vertical\",\n          justified: \"justified\",\n          type: \"type\"\n        },\n        ngContentSelectors: _c0,\n        decls: 4,\n        vars: 3,\n        consts: [[\"role\", \"tablist\", 1, \"nav\", 3, \"click\", \"ngClass\"], [3, \"ngClass\", \"active\", \"disabled\", \"keydown\", 4, \"ngFor\", \"ngForOf\"], [1, \"tab-content\"], [3, \"keydown\", \"ngClass\"], [\"href\", \"javascript:void(0);\", \"role\", \"tab\", 1, \"nav-link\", 3, \"click\"], [3, \"ngTransclude\"], [\"class\", \"bs-remove-tab\", 3, \"click\", 4, \"ngIf\"], [1, \"bs-remove-tab\", 3, \"click\"]],\n        template: function TabsetComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵprojectionDef();\n            i0.ɵɵelementStart(0, \"ul\", 0);\n            i0.ɵɵlistener(\"click\", function TabsetComponent_Template_ul_click_0_listener($event) {\n              return $event.preventDefault();\n            });\n            i0.ɵɵtemplate(1, TabsetComponent_li_1_Template, 5, 17, \"li\", 1);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(2, \"div\", 2);\n            i0.ɵɵprojection(3);\n            i0.ɵɵelementEnd();\n          }\n          if (rf & 2) {\n            i0.ɵɵproperty(\"ngClass\", ctx.classMap);\n            i0.ɵɵattribute(\"aria-label\", ctx.ariaLabel);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngForOf\", ctx.tabs);\n          }\n        },\n        dependencies: [i2.NgClass, i2.NgForOf, i2.NgIf, NgTranscludeDirective],\n        styles: [\"[_nghost-%COMP%]   .nav-tabs[_ngcontent-%COMP%]   .nav-item.disabled[_ngcontent-%COMP%]   a.disabled[_ngcontent-%COMP%]{cursor:default}\"]\n      });\n    }\n  }\n  return TabsetComponent;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet TabDirective = /*#__PURE__*/(() => {\n  class TabDirective {\n    /** if set, will be added to the tab's class attribute. Multiple classes are supported. */\n    get customClass() {\n      return this._customClass;\n    }\n    set customClass(customClass) {\n      if (this.customClass) {\n        this.customClass.split(' ').forEach(cssClass => {\n          this.renderer.removeClass(this.elementRef.nativeElement, cssClass);\n        });\n      }\n      this._customClass = customClass ? customClass.trim() : '';\n      if (this.customClass) {\n        this.customClass.split(' ').forEach(cssClass => {\n          this.renderer.addClass(this.elementRef.nativeElement, cssClass);\n        });\n      }\n    }\n    /** tab active state toggle */\n    get active() {\n      return this._active;\n    }\n    set active(active) {\n      if (this._active === active) {\n        return;\n      }\n      if (this.disabled && active || !active) {\n        if (this._active && !active) {\n          this.deselect.emit(this);\n          this._active = active;\n        }\n        return;\n      }\n      this._active = active;\n      this.selectTab.emit(this);\n      this.tabset.tabs.forEach(tab => {\n        if (tab !== this) {\n          tab.active = false;\n        }\n      });\n    }\n    get ariaLabelledby() {\n      return this.id ? `${this.id}-link` : '';\n    }\n    constructor(tabset, elementRef, renderer) {\n      this.elementRef = elementRef;\n      this.renderer = renderer;\n      /** if true tab can not be activated */\n      this.disabled = false;\n      /** if true tab can be removable, additional button will appear */\n      this.removable = false;\n      /** fired when tab became active, $event:Tab equals to selected instance of Tab component */\n      this.selectTab = new EventEmitter();\n      /** fired when tab became inactive, $event:Tab equals to deselected instance of Tab component */\n      this.deselect = new EventEmitter();\n      /** fired before tab will be removed, $event:Tab equals to instance of removed tab */\n      this.removed = new EventEmitter();\n      this.addClass = true;\n      this.role = 'tabpanel';\n      this._active = false;\n      this._customClass = '';\n      this.tabset = tabset;\n      this.tabset.addTab(this);\n    }\n    ngOnInit() {\n      this.removable = !!this.removable;\n    }\n    ngOnDestroy() {\n      this.tabset.removeTab(this, {\n        reselect: false,\n        emit: false\n      });\n    }\n    static {\n      this.ɵfac = function TabDirective_Factory(t) {\n        return new (t || TabDirective)(i0.ɵɵdirectiveInject(TabsetComponent), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.Renderer2));\n      };\n    }\n    static {\n      this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n        type: TabDirective,\n        selectors: [[\"tab\"], [\"\", \"tab\", \"\"]],\n        hostVars: 7,\n        hostBindings: function TabDirective_HostBindings(rf, ctx) {\n          if (rf & 2) {\n            i0.ɵɵattribute(\"id\", ctx.id)(\"role\", ctx.role)(\"aria-labelledby\", ctx.ariaLabelledby);\n            i0.ɵɵclassProp(\"active\", ctx.active)(\"tab-pane\", ctx.addClass);\n          }\n        },\n        inputs: {\n          heading: \"heading\",\n          id: \"id\",\n          disabled: \"disabled\",\n          removable: \"removable\",\n          customClass: \"customClass\",\n          active: \"active\"\n        },\n        outputs: {\n          selectTab: \"selectTab\",\n          deselect: \"deselect\",\n          removed: \"removed\"\n        },\n        exportAs: [\"tab\"]\n      });\n    }\n  }\n  return TabDirective;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/** Should be used to mark <ng-template> element as a template for tab heading */\nlet TabHeadingDirective = /*#__PURE__*/(() => {\n  class TabHeadingDirective {\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    constructor(templateRef, tab) {\n      tab.headingRef = templateRef;\n    }\n    static {\n      this.ɵfac = function TabHeadingDirective_Factory(t) {\n        return new (t || TabHeadingDirective)(i0.ɵɵdirectiveInject(i0.TemplateRef), i0.ɵɵdirectiveInject(TabDirective));\n      };\n    }\n    static {\n      this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n        type: TabHeadingDirective,\n        selectors: [[\"\", \"tabHeading\", \"\"]]\n      });\n    }\n  }\n  return TabHeadingDirective;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet TabsModule = /*#__PURE__*/(() => {\n  class TabsModule {\n    static forRoot() {\n      return {\n        ngModule: TabsModule,\n        providers: []\n      };\n    }\n    static {\n      this.ɵfac = function TabsModule_Factory(t) {\n        return new (t || TabsModule)();\n      };\n    }\n    static {\n      this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n        type: TabsModule\n      });\n    }\n    static {\n      this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n        imports: [CommonModule]\n      });\n    }\n  }\n  return TabsModule;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { NgTranscludeDirective, TabDirective, TabHeadingDirective, TabsModule, TabsetComponent, TabsetConfig };\n//# sourceMappingURL=ngx-bootstrap-tabs.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}