{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.mergeMap = void 0;\nvar map_1 = require(\"./map\");\nvar innerFrom_1 = require(\"../observable/innerFrom\");\nvar lift_1 = require(\"../util/lift\");\nvar mergeInternals_1 = require(\"./mergeInternals\");\nvar isFunction_1 = require(\"../util/isFunction\");\nfunction mergeMap(project, resultSelector, concurrent) {\n  if (concurrent === void 0) {\n    concurrent = Infinity;\n  }\n  if (isFunction_1.isFunction(resultSelector)) {\n    return mergeMap(function (a, i) {\n      return map_1.map(function (b, ii) {\n        return resultSelector(a, b, i, ii);\n      })(innerFrom_1.innerFrom(project(a, i)));\n    }, concurrent);\n  } else if (typeof resultSelector === 'number') {\n    concurrent = resultSelector;\n  }\n  return lift_1.operate(function (source, subscriber) {\n    return mergeInternals_1.mergeInternals(source, subscriber, project, concurrent);\n  });\n}\nexports.mergeMap = mergeMap;\n//# sourceMappingURL=mergeMap.js.map", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}