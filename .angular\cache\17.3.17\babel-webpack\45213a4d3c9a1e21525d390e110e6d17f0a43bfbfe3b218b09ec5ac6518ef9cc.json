{"ast": null, "code": "/*! DataTables Bootstrap 4 integration\n * ©2011-2017 SpryMedia Ltd - datatables.net/license\n */\n\nimport jQuery from 'jquery';\nimport DataTable from 'datatables.net';\n\n// Allow reassignment of the $ variable\nlet $ = jQuery;\n\n/**\n * DataTables integration for Bootstrap 4. This requires Bootstrap 4 and\n * DataTables 1.10 or newer.\n *\n * This file sets the defaults and adds options to DataTables to style its\n * controls using Bootstrap. See https://datatables.net/manual/styling/bootstrap\n * for further information.\n */\n\n/* Set the defaults for DataTables initialisation */\n$.extend(true, DataTable.defaults, {\n  dom: \"<'row'<'col-sm-12 col-md-6'l><'col-sm-12 col-md-6'f>>\" + \"<'row'<'col-sm-12'tr>>\" + \"<'row'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7'p>>\",\n  renderer: 'bootstrap'\n});\n\n/* Default class modification */\n$.extend(DataTable.ext.classes, {\n  sWrapper: \"dataTables_wrapper dt-bootstrap4\",\n  sFilterInput: \"form-control form-control-sm\",\n  sLengthSelect: \"custom-select custom-select-sm form-control form-control-sm\",\n  sProcessing: \"dataTables_processing card\",\n  sPageButton: \"paginate_button page-item\"\n});\n\n/* Bootstrap paging button renderer */\nDataTable.ext.renderer.pageButton.bootstrap = function (settings, host, idx, buttons, page, pages) {\n  var api = new DataTable.Api(settings);\n  var classes = settings.oClasses;\n  var lang = settings.oLanguage.oPaginate;\n  var aria = settings.oLanguage.oAria.paginate || {};\n  var btnDisplay, btnClass;\n  var attach = function (container, buttons) {\n    var i, ien, node, button;\n    var clickHandler = function (e) {\n      e.preventDefault();\n      if (!$(e.currentTarget).hasClass('disabled') && api.page() != e.data.action) {\n        api.page(e.data.action).draw('page');\n      }\n    };\n    for (i = 0, ien = buttons.length; i < ien; i++) {\n      button = buttons[i];\n      if (Array.isArray(button)) {\n        attach(container, button);\n      } else {\n        btnDisplay = '';\n        btnClass = '';\n        switch (button) {\n          case 'ellipsis':\n            btnDisplay = '&#x2026;';\n            btnClass = 'disabled';\n            break;\n          case 'first':\n            btnDisplay = lang.sFirst;\n            btnClass = button + (page > 0 ? '' : ' disabled');\n            break;\n          case 'previous':\n            btnDisplay = lang.sPrevious;\n            btnClass = button + (page > 0 ? '' : ' disabled');\n            break;\n          case 'next':\n            btnDisplay = lang.sNext;\n            btnClass = button + (page < pages - 1 ? '' : ' disabled');\n            break;\n          case 'last':\n            btnDisplay = lang.sLast;\n            btnClass = button + (page < pages - 1 ? '' : ' disabled');\n            break;\n          default:\n            btnDisplay = button + 1;\n            btnClass = page === button ? 'active' : '';\n            break;\n        }\n        if (btnDisplay) {\n          var disabled = btnClass.indexOf('disabled') !== -1;\n          node = $('<li>', {\n            'class': classes.sPageButton + ' ' + btnClass,\n            'id': idx === 0 && typeof button === 'string' ? settings.sTableId + '_' + button : null\n          }).append($('<a>', {\n            'href': disabled ? null : '#',\n            'aria-controls': settings.sTableId,\n            'aria-disabled': disabled ? 'true' : null,\n            'aria-label': aria[button],\n            'role': 'link',\n            'aria-current': btnClass === 'active' ? 'page' : null,\n            'data-dt-idx': button,\n            'tabindex': disabled ? -1 : settings.iTabIndex,\n            'class': 'page-link'\n          }).html(btnDisplay)).appendTo(container);\n          settings.oApi._fnBindAction(node, {\n            action: button\n          }, clickHandler);\n        }\n      }\n    }\n  };\n\n  // IE9 throws an 'unknown error' if document.activeElement is used\n  // inside an iframe or frame. \n  var activeEl;\n  try {\n    // Because this approach is destroying and recreating the paging\n    // elements, focus is lost on the select button which is bad for\n    // accessibility. So we want to restore focus once the draw has\n    // completed\n    activeEl = $(host).find(document.activeElement).data('dt-idx');\n  } catch (e) {}\n  attach($(host).empty().html('<ul class=\"pagination\"/>').children('ul'), buttons);\n  if (activeEl !== undefined) {\n    $(host).find('[data-dt-idx=' + activeEl + ']').trigger('focus');\n  }\n};\nexport default DataTable;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}