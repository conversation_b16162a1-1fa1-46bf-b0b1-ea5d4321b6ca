{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport let ViewimagesComponent = /*#__PURE__*/(() => {\n  class ViewimagesComponent {\n    constructor() {}\n    ngOnInit() {}\n    static {\n      this.ɵfac = function ViewimagesComponent_Factory(t) {\n        return new (t || ViewimagesComponent)();\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: ViewimagesComponent,\n        selectors: [[\"app-viewimages\"]],\n        decls: 4,\n        vars: 0,\n        consts: [[2, \"width\", \"500px\", \"height\", \"500px\"], [\"src\", \"http://localhost:4200/assets/imageBill/BI18-02526-2018-8-15-684.jpg\", 2, \"width\", \"500px\", \"height\", \"500px\"]],\n        template: function ViewimagesComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"p\");\n            i0.ɵɵtext(1, \"View img\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(2, \"div\", 0);\n            i0.ɵɵelement(3, \"img\", 1);\n            i0.ɵɵelementEnd();\n          }\n        }\n      });\n    }\n  }\n  return ViewimagesComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}