{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { EventEmitter, Pipe, Directive, Input, Output, Component, ChangeDetectionStrategy, ViewEncapsulation, NgModule } from '@angular/core';\nimport * as i2 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nfunction PaginationControlsComponent_ul_3_li_1_a_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"a\", 12);\n    i0.ɵɵlistener(\"keyup.enter\", function PaginationControlsComponent_ul_3_li_1_a_1_Template_a_keyup_enter_0_listener() {\n      i0.ɵɵrestoreView(_r2);\n      i0.ɵɵnextContext(3);\n      const p_r3 = i0.ɵɵreference(1);\n      return i0.ɵɵresetView(p_r3.previous());\n    })(\"click\", function PaginationControlsComponent_ul_3_li_1_a_1_Template_a_click_0_listener() {\n      i0.ɵɵrestoreView(_r2);\n      i0.ɵɵnextContext(3);\n      const p_r3 = i0.ɵɵreference(1);\n      return i0.ɵɵresetView(p_r3.previous());\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementStart(2, \"span\", 13);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.previousLabel, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r3.screenReaderPageLabel);\n  }\n}\nfunction PaginationControlsComponent_ul_3_li_1_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 14);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementStart(2, \"span\", 13);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.previousLabel, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r3.screenReaderPageLabel);\n  }\n}\nfunction PaginationControlsComponent_ul_3_li_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 9);\n    i0.ɵɵtemplate(1, PaginationControlsComponent_ul_3_li_1_a_1_Template, 4, 2, \"a\", 10)(2, PaginationControlsComponent_ul_3_li_1_span_2_Template, 4, 2, \"span\", 11);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext(2);\n    const p_r3 = i0.ɵɵreference(1);\n    i0.ɵɵclassProp(\"disabled\", p_r3.isFirstPage());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", 1 < p_r3.getCurrent());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", p_r3.isFirstPage());\n  }\n}\nfunction PaginationControlsComponent_ul_3_li_4_a_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"a\", 12);\n    i0.ɵɵlistener(\"keyup.enter\", function PaginationControlsComponent_ul_3_li_4_a_1_Template_a_keyup_enter_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const page_r6 = i0.ɵɵnextContext().$implicit;\n      i0.ɵɵnextContext(2);\n      const p_r3 = i0.ɵɵreference(1);\n      return i0.ɵɵresetView(p_r3.setCurrent(page_r6.value));\n    })(\"click\", function PaginationControlsComponent_ul_3_li_4_a_1_Template_a_click_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const page_r6 = i0.ɵɵnextContext().$implicit;\n      i0.ɵɵnextContext(2);\n      const p_r3 = i0.ɵɵreference(1);\n      return i0.ɵɵresetView(p_r3.setCurrent(page_r6.value));\n    });\n    i0.ɵɵelementStart(1, \"span\", 13);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"number\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const page_r6 = i0.ɵɵnextContext().$implicit;\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r3.screenReaderPageLabel, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(page_r6.label === \"...\" ? page_r6.label : i0.ɵɵpipeBind2(5, 2, page_r6.label, \"\"));\n  }\n}\nfunction PaginationControlsComponent_ul_3_li_4_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"span\", 16)(2, \"span\", 13);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\");\n    i0.ɵɵtext(5);\n    i0.ɵɵpipe(6, \"number\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const page_r6 = i0.ɵɵnextContext().$implicit;\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r3.screenReaderCurrentLabel, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(page_r6.label === \"...\" ? page_r6.label : i0.ɵɵpipeBind2(6, 2, page_r6.label, \"\"));\n  }\n}\nfunction PaginationControlsComponent_ul_3_li_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\");\n    i0.ɵɵtemplate(1, PaginationControlsComponent_ul_3_li_4_a_1_Template, 6, 5, \"a\", 10)(2, PaginationControlsComponent_ul_3_li_4_ng_container_2_Template, 7, 5, \"ng-container\", 15);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const page_r6 = ctx.$implicit;\n    i0.ɵɵnextContext(2);\n    const p_r3 = i0.ɵɵreference(1);\n    i0.ɵɵclassProp(\"current\", p_r3.getCurrent() === page_r6.value)(\"ellipsis\", page_r6.label === \"...\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", p_r3.getCurrent() !== page_r6.value);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", p_r3.getCurrent() === page_r6.value);\n  }\n}\nfunction PaginationControlsComponent_ul_3_li_5_a_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"a\", 12);\n    i0.ɵɵlistener(\"keyup.enter\", function PaginationControlsComponent_ul_3_li_5_a_1_Template_a_keyup_enter_0_listener() {\n      i0.ɵɵrestoreView(_r7);\n      i0.ɵɵnextContext(3);\n      const p_r3 = i0.ɵɵreference(1);\n      return i0.ɵɵresetView(p_r3.next());\n    })(\"click\", function PaginationControlsComponent_ul_3_li_5_a_1_Template_a_click_0_listener() {\n      i0.ɵɵrestoreView(_r7);\n      i0.ɵɵnextContext(3);\n      const p_r3 = i0.ɵɵreference(1);\n      return i0.ɵɵresetView(p_r3.next());\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementStart(2, \"span\", 13);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.nextLabel, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r3.screenReaderPageLabel);\n  }\n}\nfunction PaginationControlsComponent_ul_3_li_5_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 14);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementStart(2, \"span\", 13);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.nextLabel, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r3.screenReaderPageLabel);\n  }\n}\nfunction PaginationControlsComponent_ul_3_li_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 17);\n    i0.ɵɵtemplate(1, PaginationControlsComponent_ul_3_li_5_a_1_Template, 4, 2, \"a\", 10)(2, PaginationControlsComponent_ul_3_li_5_span_2_Template, 4, 2, \"span\", 11);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext(2);\n    const p_r3 = i0.ɵɵreference(1);\n    i0.ɵɵclassProp(\"disabled\", p_r3.isLastPage());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !p_r3.isLastPage());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", p_r3.isLastPage());\n  }\n}\nfunction PaginationControlsComponent_ul_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ul\", 4);\n    i0.ɵɵtemplate(1, PaginationControlsComponent_ul_3_li_1_Template, 3, 4, \"li\", 5);\n    i0.ɵɵelementStart(2, \"li\", 6);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, PaginationControlsComponent_ul_3_li_4_Template, 3, 6, \"li\", 7)(5, PaginationControlsComponent_ul_3_li_5_Template, 3, 4, \"li\", 8);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    const p_r3 = i0.ɵɵreference(1);\n    i0.ɵɵclassProp(\"responsive\", ctx_r3.responsive);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.directionLinks);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\" \", p_r3.getCurrent(), \" / \", p_r3.getLastPage(), \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", p_r3.pages)(\"ngForTrackBy\", ctx_r3.trackByIndex);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.directionLinks);\n  }\n}\nclass PaginationService {\n  constructor() {\n    this.change = new EventEmitter();\n    this.instances = {};\n    this.DEFAULT_ID = 'DEFAULT_PAGINATION_ID';\n  }\n  defaultId() {\n    return this.DEFAULT_ID;\n  }\n  /**\r\n   * Register a PaginationInstance with this service. Returns a\r\n   * boolean value signifying whether the instance is new or\r\n   * updated (true = new or updated, false = unchanged).\r\n   */\n  register(instance) {\n    if (instance.id == null) {\n      instance.id = this.DEFAULT_ID;\n    }\n    if (!this.instances[instance.id]) {\n      this.instances[instance.id] = instance;\n      return true;\n    } else {\n      return this.updateInstance(instance);\n    }\n  }\n  /**\r\n   * Check each property of the instance and update any that have changed. Return\r\n   * true if any changes were made, else return false.\r\n   */\n  updateInstance(instance) {\n    let changed = false;\n    for (let prop in this.instances[instance.id]) {\n      if (instance[prop] !== this.instances[instance.id][prop]) {\n        this.instances[instance.id][prop] = instance[prop];\n        changed = true;\n      }\n    }\n    return changed;\n  }\n  /**\r\n   * Returns the current page number.\r\n   */\n  getCurrentPage(id) {\n    if (this.instances[id]) {\n      return this.instances[id].currentPage;\n    }\n    return 1;\n  }\n  /**\r\n   * Sets the current page number.\r\n   */\n  setCurrentPage(id, page) {\n    if (this.instances[id]) {\n      let instance = this.instances[id];\n      let maxPage = Math.ceil(instance.totalItems / instance.itemsPerPage);\n      if (page <= maxPage && 1 <= page) {\n        this.instances[id].currentPage = page;\n        this.change.emit(id);\n      }\n    }\n  }\n  /**\r\n   * Sets the value of instance.totalItems\r\n   */\n  setTotalItems(id, totalItems) {\n    if (this.instances[id] && 0 <= totalItems) {\n      this.instances[id].totalItems = totalItems;\n      this.change.emit(id);\n    }\n  }\n  /**\r\n   * Sets the value of instance.itemsPerPage.\r\n   */\n  setItemsPerPage(id, itemsPerPage) {\n    if (this.instances[id]) {\n      this.instances[id].itemsPerPage = itemsPerPage;\n      this.change.emit(id);\n    }\n  }\n  /**\r\n   * Returns a clone of the pagination instance object matching the id. If no\r\n   * id specified, returns the instance corresponding to the default id.\r\n   */\n  getInstance(id = this.DEFAULT_ID) {\n    if (this.instances[id]) {\n      return this.clone(this.instances[id]);\n    }\n    return {};\n  }\n  /**\r\n   * Perform a shallow clone of an object.\r\n   */\n  clone(obj) {\n    var target = {};\n    for (var i in obj) {\n      if (obj.hasOwnProperty(i)) {\n        target[i] = obj[i];\n      }\n    }\n    return target;\n  }\n}\nconst LARGE_NUMBER = Number.MAX_SAFE_INTEGER;\nlet PaginatePipe = /*#__PURE__*/(() => {\n  class PaginatePipe {\n    constructor(service) {\n      this.service = service;\n      // store the values from the last time the pipe was invoked\n      this.state = {};\n    }\n    transform(collection, args) {\n      // When an observable is passed through the AsyncPipe, it will output\n      // `null` until the subscription resolves. In this case, we want to\n      // use the cached data from the `state` object to prevent the NgFor\n      // from flashing empty until the real values arrive.\n      if (!(collection instanceof Array)) {\n        let _id = args.id || this.service.defaultId();\n        if (this.state[_id]) {\n          return this.state[_id].slice;\n        } else {\n          return collection;\n        }\n      }\n      let serverSideMode = args.totalItems && args.totalItems !== collection.length;\n      let instance = this.createInstance(collection, args);\n      let id = instance.id;\n      let start, end;\n      let perPage = instance.itemsPerPage;\n      let emitChange = this.service.register(instance);\n      if (!serverSideMode && collection instanceof Array) {\n        perPage = +perPage || LARGE_NUMBER;\n        start = (instance.currentPage - 1) * perPage;\n        end = start + perPage;\n        let isIdentical = this.stateIsIdentical(id, collection, start, end);\n        if (isIdentical) {\n          return this.state[id].slice;\n        } else {\n          let slice = collection.slice(start, end);\n          this.saveState(id, collection, slice, start, end);\n          this.service.change.emit(id);\n          return slice;\n        }\n      } else {\n        if (emitChange) {\n          this.service.change.emit(id);\n        }\n        // save the state for server-side collection to avoid null\n        // flash as new data loads.\n        this.saveState(id, collection, collection, start, end);\n        return collection;\n      }\n    }\n    /**\r\n     * Create an PaginationInstance object, using defaults for any optional properties not supplied.\r\n     */\n    createInstance(collection, config) {\n      this.checkConfig(config);\n      return {\n        id: config.id != null ? config.id : this.service.defaultId(),\n        itemsPerPage: +config.itemsPerPage || 0,\n        currentPage: +config.currentPage || 1,\n        totalItems: +config.totalItems || collection.length\n      };\n    }\n    /**\r\n     * Ensure the argument passed to the filter contains the required properties.\r\n     */\n    checkConfig(config) {\n      const required = ['itemsPerPage', 'currentPage'];\n      const missing = required.filter(prop => !(prop in config));\n      if (0 < missing.length) {\n        throw new Error(`PaginatePipe: Argument is missing the following required properties: ${missing.join(', ')}`);\n      }\n    }\n    /**\r\n     * To avoid returning a brand new array each time the pipe is run, we store the state of the sliced\r\n     * array for a given id. This means that the next time the pipe is run on this collection & id, we just\r\n     * need to check that the collection, start and end points are all identical, and if so, return the\r\n     * last sliced array.\r\n     */\n    saveState(id, collection, slice, start, end) {\n      this.state[id] = {\n        collection,\n        size: collection.length,\n        slice,\n        start,\n        end\n      };\n    }\n    /**\r\n     * For a given id, returns true if the collection, size, start and end values are identical.\r\n     */\n    stateIsIdentical(id, collection, start, end) {\n      let state = this.state[id];\n      if (!state) {\n        return false;\n      }\n      let isMetaDataIdentical = state.size === collection.length && state.start === start && state.end === end;\n      if (!isMetaDataIdentical) {\n        return false;\n      }\n      return state.slice.every((element, index) => element === collection[start + index]);\n    }\n  }\n  PaginatePipe.ɵfac = function PaginatePipe_Factory(t) {\n    return new (t || PaginatePipe)(i0.ɵɵdirectiveInject(PaginationService, 16));\n  };\n  PaginatePipe.ɵpipe = /* @__PURE__ */i0.ɵɵdefinePipe({\n    name: \"paginate\",\n    type: PaginatePipe,\n    pure: false\n  });\n  return PaginatePipe;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\r\n * The default template and styles for the pagination links are borrowed directly\r\n * from Zurb Foundation 6: http://foundation.zurb.com/sites/docs/pagination.html\r\n */\nconst DEFAULT_TEMPLATE = `\n    <pagination-template  #p=\"paginationApi\"\n                         [id]=\"id\"\n                         [maxSize]=\"maxSize\"\n                         (pageChange)=\"pageChange.emit($event)\"\n                         (pageBoundsCorrection)=\"pageBoundsCorrection.emit($event)\">\n    <nav role=\"navigation\" [attr.aria-label]=\"screenReaderPaginationLabel\">\n    <ul class=\"ngx-pagination\" \n        [class.responsive]=\"responsive\"\n        *ngIf=\"!(autoHide && p.pages.length <= 1)\">\n\n        <li class=\"pagination-previous\" [class.disabled]=\"p.isFirstPage()\" *ngIf=\"directionLinks\"> \n            <a tabindex=\"0\" *ngIf=\"1 < p.getCurrent()\" (keyup.enter)=\"p.previous()\" (click)=\"p.previous()\">\n                {{ previousLabel }} <span class=\"show-for-sr\">{{ screenReaderPageLabel }}</span>\n            </a>\n            <span *ngIf=\"p.isFirstPage()\" aria-disabled=\"true\">\n                {{ previousLabel }} <span class=\"show-for-sr\">{{ screenReaderPageLabel }}</span>\n            </span>\n        </li> \n\n        <li class=\"small-screen\">\n            {{ p.getCurrent() }} / {{ p.getLastPage() }}\n        </li>\n\n        <li [class.current]=\"p.getCurrent() === page.value\" \n            [class.ellipsis]=\"page.label === '...'\"\n            *ngFor=\"let page of p.pages; trackBy: trackByIndex\">\n            <a tabindex=\"0\" (keyup.enter)=\"p.setCurrent(page.value)\" (click)=\"p.setCurrent(page.value)\" *ngIf=\"p.getCurrent() !== page.value\">\n                <span class=\"show-for-sr\">{{ screenReaderPageLabel }} </span>\n                <span>{{ (page.label === '...') ? page.label : (page.label | number:'') }}</span>\n            </a>\n            <ng-container *ngIf=\"p.getCurrent() === page.value\">\n              <span aria-live=\"polite\">\n                <span class=\"show-for-sr\">{{ screenReaderCurrentLabel }} </span>\n                <span>{{ (page.label === '...') ? page.label : (page.label | number:'') }}</span> \n              </span>\n            </ng-container>\n        </li>\n\n        <li class=\"pagination-next\" [class.disabled]=\"p.isLastPage()\" *ngIf=\"directionLinks\">\n            <a tabindex=\"0\" *ngIf=\"!p.isLastPage()\" (keyup.enter)=\"p.next()\" (click)=\"p.next()\">\n                 {{ nextLabel }} <span class=\"show-for-sr\">{{ screenReaderPageLabel }}</span>\n            </a>\n            <span *ngIf=\"p.isLastPage()\" aria-disabled=\"true\">\n                 {{ nextLabel }} <span class=\"show-for-sr\">{{ screenReaderPageLabel }}</span>\n            </span>\n        </li>\n\n    </ul>\n    </nav>\n    </pagination-template>\n    `;\nconst DEFAULT_STYLES = `\n.ngx-pagination {\n  margin-left: 0;\n  margin-bottom: 1rem; }\n  .ngx-pagination::before, .ngx-pagination::after {\n    content: ' ';\n    display: table; }\n  .ngx-pagination::after {\n    clear: both; }\n  .ngx-pagination li {\n    -moz-user-select: none;\n    -webkit-user-select: none;\n    -ms-user-select: none;\n    margin-right: 0.0625rem;\n    border-radius: 0; }\n  .ngx-pagination li {\n    display: inline-block; }\n  .ngx-pagination a,\n  .ngx-pagination button {\n    color: #0a0a0a; \n    display: block;\n    padding: 0.1875rem 0.625rem;\n    border-radius: 0; }\n    .ngx-pagination a:hover,\n    .ngx-pagination button:hover {\n      background: #e6e6e6; }\n  .ngx-pagination .current {\n    padding: 0.1875rem 0.625rem;\n    background: #2199e8;\n    color: #fefefe;\n    cursor: default; }\n  .ngx-pagination .disabled {\n    padding: 0.1875rem 0.625rem;\n    color: #cacaca;\n    cursor: default; } \n    .ngx-pagination .disabled:hover {\n      background: transparent; }\n  .ngx-pagination a, .ngx-pagination button {\n    cursor: pointer; }\n\n.ngx-pagination .pagination-previous a::before,\n.ngx-pagination .pagination-previous.disabled::before { \n  content: '«';\n  display: inline-block;\n  margin-right: 0.5rem; }\n\n.ngx-pagination .pagination-next a::after,\n.ngx-pagination .pagination-next.disabled::after {\n  content: '»';\n  display: inline-block;\n  margin-left: 0.5rem; }\n\n.ngx-pagination .show-for-sr {\n  position: absolute !important;\n  width: 1px;\n  height: 1px;\n  overflow: hidden;\n  clip: rect(0, 0, 0, 0); }\n.ngx-pagination .small-screen {\n  display: none; }\n@media screen and (max-width: 601px) {\n  .ngx-pagination.responsive .small-screen {\n    display: inline-block; } \n  .ngx-pagination.responsive li:not(.small-screen):not(.pagination-previous):not(.pagination-next) {\n    display: none; }\n}\n  `;\n\n/**\r\n * This directive is what powers all pagination controls components, including the default one.\r\n * It exposes an API which is hooked up to the PaginationService to keep the PaginatePipe in sync\r\n * with the pagination controls.\r\n */\nlet PaginationControlsDirective = /*#__PURE__*/(() => {\n  class PaginationControlsDirective {\n    constructor(service, changeDetectorRef) {\n      this.service = service;\n      this.changeDetectorRef = changeDetectorRef;\n      this.maxSize = 7;\n      this.pageChange = new EventEmitter();\n      this.pageBoundsCorrection = new EventEmitter();\n      this.pages = [];\n      this.changeSub = this.service.change.subscribe(id => {\n        if (this.id === id) {\n          this.updatePageLinks();\n          this.changeDetectorRef.markForCheck();\n          this.changeDetectorRef.detectChanges();\n        }\n      });\n    }\n    ngOnInit() {\n      if (this.id === undefined) {\n        this.id = this.service.defaultId();\n      }\n      this.updatePageLinks();\n    }\n    ngOnChanges(changes) {\n      this.updatePageLinks();\n    }\n    ngOnDestroy() {\n      this.changeSub.unsubscribe();\n    }\n    /**\r\n     * Go to the previous page\r\n     */\n    previous() {\n      this.checkValidId();\n      this.setCurrent(this.getCurrent() - 1);\n    }\n    /**\r\n     * Go to the next page\r\n     */\n    next() {\n      this.checkValidId();\n      this.setCurrent(this.getCurrent() + 1);\n    }\n    /**\r\n     * Returns true if current page is first page\r\n     */\n    isFirstPage() {\n      return this.getCurrent() === 1;\n    }\n    /**\r\n     * Returns true if current page is last page\r\n     */\n    isLastPage() {\n      return this.getLastPage() === this.getCurrent();\n    }\n    /**\r\n     * Set the current page number.\r\n     */\n    setCurrent(page) {\n      this.pageChange.emit(page);\n    }\n    /**\r\n     * Get the current page number.\r\n     */\n    getCurrent() {\n      return this.service.getCurrentPage(this.id);\n    }\n    /**\r\n     * Returns the last page number\r\n     */\n    getLastPage() {\n      let inst = this.service.getInstance(this.id);\n      if (inst.totalItems < 1) {\n        // when there are 0 or fewer (an error case) items, there are no \"pages\" as such,\n        // but it makes sense to consider a single, empty page as the last page.\n        return 1;\n      }\n      return Math.ceil(inst.totalItems / inst.itemsPerPage);\n    }\n    getTotalItems() {\n      return this.service.getInstance(this.id).totalItems;\n    }\n    checkValidId() {\n      if (this.service.getInstance(this.id).id == null) {\n        console.warn(`PaginationControlsDirective: the specified id \"${this.id}\" does not match any registered PaginationInstance`);\n      }\n    }\n    /**\r\n     * Updates the page links and checks that the current page is valid. Should run whenever the\r\n     * PaginationService.change stream emits a value matching the current ID, or when any of the\r\n     * input values changes.\r\n     */\n    updatePageLinks() {\n      let inst = this.service.getInstance(this.id);\n      const correctedCurrentPage = this.outOfBoundCorrection(inst);\n      if (correctedCurrentPage !== inst.currentPage) {\n        setTimeout(() => {\n          this.pageBoundsCorrection.emit(correctedCurrentPage);\n          this.pages = this.createPageArray(inst.currentPage, inst.itemsPerPage, inst.totalItems, this.maxSize);\n        });\n      } else {\n        this.pages = this.createPageArray(inst.currentPage, inst.itemsPerPage, inst.totalItems, this.maxSize);\n      }\n    }\n    /**\r\n     * Checks that the instance.currentPage property is within bounds for the current page range.\r\n     * If not, return a correct value for currentPage, or the current value if OK.\r\n     */\n    outOfBoundCorrection(instance) {\n      const totalPages = Math.ceil(instance.totalItems / instance.itemsPerPage);\n      if (totalPages < instance.currentPage && 0 < totalPages) {\n        return totalPages;\n      } else if (instance.currentPage < 1) {\n        return 1;\n      }\n      return instance.currentPage;\n    }\n    /**\r\n     * Returns an array of Page objects to use in the pagination controls.\r\n     */\n    createPageArray(currentPage, itemsPerPage, totalItems, paginationRange) {\n      // paginationRange could be a string if passed from attribute, so cast to number.\n      paginationRange = +paginationRange;\n      let pages = [];\n      // Return 1 as default page number\n      // Make sense to show 1 instead of empty when there are no items\n      const totalPages = Math.max(Math.ceil(totalItems / itemsPerPage), 1);\n      const halfWay = Math.ceil(paginationRange / 2);\n      const isStart = currentPage <= halfWay;\n      const isEnd = totalPages - halfWay < currentPage;\n      const isMiddle = !isStart && !isEnd;\n      let ellipsesNeeded = paginationRange < totalPages;\n      let i = 1;\n      while (i <= totalPages && i <= paginationRange) {\n        let label;\n        let pageNumber = this.calculatePageNumber(i, currentPage, paginationRange, totalPages);\n        let openingEllipsesNeeded = i === 2 && (isMiddle || isEnd);\n        let closingEllipsesNeeded = i === paginationRange - 1 && (isMiddle || isStart);\n        if (ellipsesNeeded && (openingEllipsesNeeded || closingEllipsesNeeded)) {\n          label = '...';\n        } else {\n          label = pageNumber;\n        }\n        pages.push({\n          label: label,\n          value: pageNumber\n        });\n        i++;\n      }\n      return pages;\n    }\n    /**\r\n     * Given the position in the sequence of pagination links [i],\r\n     * figure out what page number corresponds to that position.\r\n     */\n    calculatePageNumber(i, currentPage, paginationRange, totalPages) {\n      let halfWay = Math.ceil(paginationRange / 2);\n      if (i === paginationRange) {\n        return totalPages;\n      } else if (i === 1) {\n        return i;\n      } else if (paginationRange < totalPages) {\n        if (totalPages - halfWay < currentPage) {\n          return totalPages - paginationRange + i;\n        } else if (halfWay < currentPage) {\n          return currentPage - halfWay + i;\n        } else {\n          return i;\n        }\n      } else {\n        return i;\n      }\n    }\n  }\n  PaginationControlsDirective.ɵfac = function PaginationControlsDirective_Factory(t) {\n    return new (t || PaginationControlsDirective)(i0.ɵɵdirectiveInject(PaginationService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n  };\n  PaginationControlsDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: PaginationControlsDirective,\n    selectors: [[\"pagination-template\"], [\"\", \"pagination-template\", \"\"]],\n    inputs: {\n      id: \"id\",\n      maxSize: \"maxSize\"\n    },\n    outputs: {\n      pageChange: \"pageChange\",\n      pageBoundsCorrection: \"pageBoundsCorrection\"\n    },\n    exportAs: [\"paginationApi\"],\n    features: [i0.ɵɵNgOnChangesFeature]\n  });\n  return PaginationControlsDirective;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nfunction coerceToBoolean(input) {\n  return !!input && input !== 'false';\n}\n/**\r\n * The default pagination controls component. Actually just a default implementation of a custom template.\r\n */\nlet PaginationControlsComponent = /*#__PURE__*/(() => {\n  class PaginationControlsComponent {\n    constructor() {\n      this.maxSize = 7;\n      this.previousLabel = 'Previous';\n      this.nextLabel = 'Next';\n      this.screenReaderPaginationLabel = 'Pagination';\n      this.screenReaderPageLabel = 'page';\n      this.screenReaderCurrentLabel = `You're on page`;\n      this.pageChange = new EventEmitter();\n      this.pageBoundsCorrection = new EventEmitter();\n      this._directionLinks = true;\n      this._autoHide = false;\n      this._responsive = false;\n    }\n    get directionLinks() {\n      return this._directionLinks;\n    }\n    set directionLinks(value) {\n      this._directionLinks = coerceToBoolean(value);\n    }\n    get autoHide() {\n      return this._autoHide;\n    }\n    set autoHide(value) {\n      this._autoHide = coerceToBoolean(value);\n    }\n    get responsive() {\n      return this._responsive;\n    }\n    set responsive(value) {\n      this._responsive = coerceToBoolean(value);\n    }\n    trackByIndex(index) {\n      return index;\n    }\n  }\n  PaginationControlsComponent.ɵfac = function PaginationControlsComponent_Factory(t) {\n    return new (t || PaginationControlsComponent)();\n  };\n  PaginationControlsComponent.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: PaginationControlsComponent,\n    selectors: [[\"pagination-controls\"]],\n    inputs: {\n      id: \"id\",\n      maxSize: \"maxSize\",\n      directionLinks: \"directionLinks\",\n      autoHide: \"autoHide\",\n      responsive: \"responsive\",\n      previousLabel: \"previousLabel\",\n      nextLabel: \"nextLabel\",\n      screenReaderPaginationLabel: \"screenReaderPaginationLabel\",\n      screenReaderPageLabel: \"screenReaderPageLabel\",\n      screenReaderCurrentLabel: \"screenReaderCurrentLabel\"\n    },\n    outputs: {\n      pageChange: \"pageChange\",\n      pageBoundsCorrection: \"pageBoundsCorrection\"\n    },\n    decls: 4,\n    vars: 4,\n    consts: [[\"p\", \"paginationApi\"], [3, \"pageChange\", \"pageBoundsCorrection\", \"id\", \"maxSize\"], [\"role\", \"navigation\"], [\"class\", \"ngx-pagination\", 3, \"responsive\", 4, \"ngIf\"], [1, \"ngx-pagination\"], [\"class\", \"pagination-previous\", 3, \"disabled\", 4, \"ngIf\"], [1, \"small-screen\"], [3, \"current\", \"ellipsis\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [\"class\", \"pagination-next\", 3, \"disabled\", 4, \"ngIf\"], [1, \"pagination-previous\"], [\"tabindex\", \"0\", 3, \"keyup.enter\", \"click\", 4, \"ngIf\"], [\"aria-disabled\", \"true\", 4, \"ngIf\"], [\"tabindex\", \"0\", 3, \"keyup.enter\", \"click\"], [1, \"show-for-sr\"], [\"aria-disabled\", \"true\"], [4, \"ngIf\"], [\"aria-live\", \"polite\"], [1, \"pagination-next\"]],\n    template: function PaginationControlsComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        const _r1 = i0.ɵɵgetCurrentView();\n        i0.ɵɵelementStart(0, \"pagination-template\", 1, 0);\n        i0.ɵɵlistener(\"pageChange\", function PaginationControlsComponent_Template_pagination_template_pageChange_0_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.pageChange.emit($event));\n        })(\"pageBoundsCorrection\", function PaginationControlsComponent_Template_pagination_template_pageBoundsCorrection_0_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.pageBoundsCorrection.emit($event));\n        });\n        i0.ɵɵelementStart(2, \"nav\", 2);\n        i0.ɵɵtemplate(3, PaginationControlsComponent_ul_3_Template, 6, 8, \"ul\", 3);\n        i0.ɵɵelementEnd()();\n      }\n      if (rf & 2) {\n        const p_r3 = i0.ɵɵreference(1);\n        i0.ɵɵproperty(\"id\", ctx.id)(\"maxSize\", ctx.maxSize);\n        i0.ɵɵadvance(2);\n        i0.ɵɵattribute(\"aria-label\", ctx.screenReaderPaginationLabel);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", !(ctx.autoHide && p_r3.pages.length <= 1));\n      }\n    },\n    dependencies: [PaginationControlsDirective, i2.NgIf, i2.NgForOf, i2.DecimalPipe],\n    styles: [\".ngx-pagination{margin-left:0;margin-bottom:1rem}.ngx-pagination:before,.ngx-pagination:after{content:\\\" \\\";display:table}.ngx-pagination:after{clear:both}.ngx-pagination li{-moz-user-select:none;-webkit-user-select:none;-ms-user-select:none;margin-right:.0625rem;border-radius:0}.ngx-pagination li{display:inline-block}.ngx-pagination a,.ngx-pagination button{color:#0a0a0a;display:block;padding:.1875rem .625rem;border-radius:0}.ngx-pagination a:hover,.ngx-pagination button:hover{background:#e6e6e6}.ngx-pagination .current{padding:.1875rem .625rem;background:#2199e8;color:#fefefe;cursor:default}.ngx-pagination .disabled{padding:.1875rem .625rem;color:#cacaca;cursor:default}.ngx-pagination .disabled:hover{background:transparent}.ngx-pagination a,.ngx-pagination button{cursor:pointer}.ngx-pagination .pagination-previous a:before,.ngx-pagination .pagination-previous.disabled:before{content:\\\"\\\\ab\\\";display:inline-block;margin-right:.5rem}.ngx-pagination .pagination-next a:after,.ngx-pagination .pagination-next.disabled:after{content:\\\"\\\\bb\\\";display:inline-block;margin-left:.5rem}.ngx-pagination .show-for-sr{position:absolute!important;width:1px;height:1px;overflow:hidden;clip:rect(0,0,0,0)}.ngx-pagination .small-screen{display:none}@media screen and (max-width: 601px){.ngx-pagination.responsive .small-screen{display:inline-block}.ngx-pagination.responsive li:not(.small-screen):not(.pagination-previous):not(.pagination-next){display:none}}\\n\"],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n  return PaginationControlsComponent;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet NgxPaginationModule = /*#__PURE__*/(() => {\n  class NgxPaginationModule {}\n  NgxPaginationModule.ɵfac = function NgxPaginationModule_Factory(t) {\n    return new (t || NgxPaginationModule)();\n  };\n  NgxPaginationModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: NgxPaginationModule\n  });\n  NgxPaginationModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    providers: [PaginationService],\n    imports: [[CommonModule]]\n  });\n  return NgxPaginationModule;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/*\r\n * Public API Surface of ngx-pagination\r\n */\n\n/**\r\n * Generated bundle index. Do not edit.\r\n */\n\nexport { NgxPaginationModule, PaginatePipe, PaginationControlsComponent, PaginationControlsDirective, PaginationService };\n//# sourceMappingURL=ngx-pagination.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}