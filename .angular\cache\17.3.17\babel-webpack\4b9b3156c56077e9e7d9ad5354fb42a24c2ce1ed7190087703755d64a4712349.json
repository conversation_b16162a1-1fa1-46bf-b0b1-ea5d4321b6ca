{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.onErrorResumeNext = void 0;\nvar Observable_1 = require(\"../Observable\");\nvar argsOrArgArray_1 = require(\"../util/argsOrArgArray\");\nvar OperatorSubscriber_1 = require(\"../operators/OperatorSubscriber\");\nvar noop_1 = require(\"../util/noop\");\nvar innerFrom_1 = require(\"./innerFrom\");\nfunction onErrorResumeNext() {\n  var sources = [];\n  for (var _i = 0; _i < arguments.length; _i++) {\n    sources[_i] = arguments[_i];\n  }\n  var nextSources = argsOrArgArray_1.argsOrArgArray(sources);\n  return new Observable_1.Observable(function (subscriber) {\n    var sourceIndex = 0;\n    var subscribeNext = function () {\n      if (sourceIndex < nextSources.length) {\n        var nextSource = void 0;\n        try {\n          nextSource = innerFrom_1.innerFrom(nextSources[sourceIndex++]);\n        } catch (err) {\n          subscribeNext();\n          return;\n        }\n        var innerSubscriber = new OperatorSubscriber_1.OperatorSubscriber(subscriber, undefined, noop_1.noop, noop_1.noop);\n        nextSource.subscribe(innerSubscriber);\n        innerSubscriber.add(subscribeNext);\n      } else {\n        subscriber.complete();\n      }\n    };\n    subscribeNext();\n  });\n}\nexports.onErrorResumeNext = onErrorResumeNext;\n//# sourceMappingURL=onErrorResumeNext.js.map", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}