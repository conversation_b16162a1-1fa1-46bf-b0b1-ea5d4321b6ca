{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.UnsubscriptionError = void 0;\nvar createErrorClass_1 = require(\"./createErrorClass\");\nexports.UnsubscriptionError = createErrorClass_1.createErrorClass(function (_super) {\n  return function UnsubscriptionErrorImpl(errors) {\n    _super(this);\n    this.message = errors ? errors.length + \" errors occurred during unsubscription:\\n\" + errors.map(function (err, i) {\n      return i + 1 + \") \" + err.toString();\n    }).join('\\n  ') : '';\n    this.name = 'UnsubscriptionError';\n    this.errors = errors;\n  };\n});\n//# sourceMappingURL=UnsubscriptionError.js.map", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}