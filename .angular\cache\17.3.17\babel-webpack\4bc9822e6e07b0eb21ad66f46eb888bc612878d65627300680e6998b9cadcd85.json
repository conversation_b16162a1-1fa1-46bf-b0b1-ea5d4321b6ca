{"ast": null, "code": "!function (t) {\n  \"function\" == typeof define && define.amd ? define(t) : t();\n}(function () {\n  \"use strict\";\n\n  /** @license\n     * jsPDF - PDF Document creation from JavaScript\n     * Version 1.5.3 Built on 2018-12-27T14:11:42.696Z\n     *                      CommitID d93d28db14\n     *\n     * Copyright (c) 2010-2016 <PERSON> <<EMAIL>>, https://github.com/MrRio/jsPDF\n     *               2010 <PERSON>, https://github.com/acspike\n     *               2012 Willow Systems Corporation, willow-systems.com\n     *               2012 <PERSON>, https://github.com/pablohess\n     *               2012 <PERSON><PERSON><PERSON>, https://github.com/fjenett\n     *               2013 <PERSON>, https://github.com/warrenweckesser\n     *               2013 Youssef <PERSON>, https://github.com/lifof\n     *               2013 <PERSON>, https://github.com/lsdriscoll\n     *               2013 <PERSON>, https://github.com/stefslon\n     *               2013 <PERSON>, https://github.com/jmorel\n     *               2013 <PERSON>, https://github.com/chris-rock\n     *               2014 <PERSON>, https://github.com/juanpgaviria\n     *               2014 <PERSON>, https://github.com/dollaruw\n     *               2014 Diego <PERSON>n, https://github.com/diegocr\n     *               2014 Steven Spungin, https://github.com/Flamenco\n     *               2014 Kenneth Glassey, https://github.com/Gavvers\n     *\n     * Licensed under the MIT License\n     *\n     * Contributor(s):\n     *    siefkenj, ahwolf, rickygu, Midnith, saintclair, eaparango,\n     *    kim3er, mfo, alnorth, Flamenco\n     */\n  function se(t) {\n    return (se = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (t) {\n      return typeof t;\n    } : function (t) {\n      return t && \"function\" == typeof Symbol && t.constructor === Symbol && t !== Symbol.prototype ? \"symbol\" : typeof t;\n    })(t);\n  }\n  !function (t) {\n    if (\"object\" !== se(t.console)) {\n      t.console = {};\n      for (var e, n, r = t.console, i = function () {}, o = [\"memory\"], a = \"assert,clear,count,debug,dir,dirxml,error,exception,group,groupCollapsed,groupEnd,info,log,markTimeline,profile,profiles,profileEnd,show,table,time,timeEnd,timeline,timelineEnd,timeStamp,trace,warn\".split(\",\"); e = o.pop();) r[e] || (r[e] = {});\n      for (; n = a.pop();) r[n] || (r[n] = i);\n    }\n    var s,\n      l,\n      h,\n      u,\n      c = \"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=\";\n    void 0 === t.btoa && (t.btoa = function (t) {\n      var e,\n        n,\n        r,\n        i,\n        o,\n        a = 0,\n        s = 0,\n        l = \"\",\n        h = [];\n      if (!t) return t;\n      for (; e = (o = t.charCodeAt(a++) << 16 | t.charCodeAt(a++) << 8 | t.charCodeAt(a++)) >> 18 & 63, n = o >> 12 & 63, r = o >> 6 & 63, i = 63 & o, h[s++] = c.charAt(e) + c.charAt(n) + c.charAt(r) + c.charAt(i), a < t.length;);\n      l = h.join(\"\");\n      var u = t.length % 3;\n      return (u ? l.slice(0, u - 3) : l) + \"===\".slice(u || 3);\n    }), void 0 === t.atob && (t.atob = function (t) {\n      var e,\n        n,\n        r,\n        i,\n        o,\n        a,\n        s = 0,\n        l = 0,\n        h = [];\n      if (!t) return t;\n      for (t += \"\"; e = (a = c.indexOf(t.charAt(s++)) << 18 | c.indexOf(t.charAt(s++)) << 12 | (i = c.indexOf(t.charAt(s++))) << 6 | (o = c.indexOf(t.charAt(s++)))) >> 16 & 255, n = a >> 8 & 255, r = 255 & a, h[l++] = 64 == i ? String.fromCharCode(e) : 64 == o ? String.fromCharCode(e, n) : String.fromCharCode(e, n, r), s < t.length;);\n      return h.join(\"\");\n    }), Array.prototype.map || (Array.prototype.map = function (t) {\n      if (null == this || \"function\" != typeof t) throw new TypeError();\n      for (var e = Object(this), n = e.length >>> 0, r = new Array(n), i = 1 < arguments.length ? arguments[1] : void 0, o = 0; o < n; o++) o in e && (r[o] = t.call(i, e[o], o, e));\n      return r;\n    }), Array.isArray || (Array.isArray = function (t) {\n      return \"[object Array]\" === Object.prototype.toString.call(t);\n    }), Array.prototype.forEach || (Array.prototype.forEach = function (t, e) {\n      if (null == this || \"function\" != typeof t) throw new TypeError();\n      for (var n = Object(this), r = n.length >>> 0, i = 0; i < r; i++) i in n && t.call(e, n[i], i, n);\n    }), Array.prototype.find || Object.defineProperty(Array.prototype, \"find\", {\n      value: function (t) {\n        if (null == this) throw new TypeError('\"this\" is null or not defined');\n        var e = Object(this),\n          n = e.length >>> 0;\n        if (\"function\" != typeof t) throw new TypeError(\"predicate must be a function\");\n        for (var r = arguments[1], i = 0; i < n;) {\n          var o = e[i];\n          if (t.call(r, o, i, e)) return o;\n          i++;\n        }\n      },\n      configurable: !0,\n      writable: !0\n    }), Object.keys || (Object.keys = (s = Object.prototype.hasOwnProperty, l = !{\n      toString: null\n    }.propertyIsEnumerable(\"toString\"), u = (h = [\"toString\", \"toLocaleString\", \"valueOf\", \"hasOwnProperty\", \"isPrototypeOf\", \"propertyIsEnumerable\", \"constructor\"]).length, function (t) {\n      if (\"object\" !== se(t) && (\"function\" != typeof t || null === t)) throw new TypeError();\n      var e,\n        n,\n        r = [];\n      for (e in t) s.call(t, e) && r.push(e);\n      if (l) for (n = 0; n < u; n++) s.call(t, h[n]) && r.push(h[n]);\n      return r;\n    })), \"function\" != typeof Object.assign && (Object.assign = function (t) {\n      if (null == t) throw new TypeError(\"Cannot convert undefined or null to object\");\n      t = Object(t);\n      for (var e = 1; e < arguments.length; e++) {\n        var n = arguments[e];\n        if (null != n) for (var r in n) Object.prototype.hasOwnProperty.call(n, r) && (t[r] = n[r]);\n      }\n      return t;\n    }), String.prototype.trim || (String.prototype.trim = function () {\n      return this.replace(/^\\s+|\\s+$/g, \"\");\n    }), String.prototype.trimLeft || (String.prototype.trimLeft = function () {\n      return this.replace(/^\\s+/g, \"\");\n    }), String.prototype.trimRight || (String.prototype.trimRight = function () {\n      return this.replace(/\\s+$/g, \"\");\n    }), Number.isInteger = Number.isInteger || function (t) {\n      return \"number\" == typeof t && isFinite(t) && Math.floor(t) === t;\n    };\n  }(\"undefined\" != typeof self && self || \"undefined\" != typeof window && window || \"undefined\" != typeof global && global || Function('return typeof this === \"object\" && this.content')() || Function(\"return this\")());\n  var t,\n    e,\n    n,\n    _,\n    l,\n    F,\n    P,\n    p,\n    d,\n    k,\n    a,\n    o,\n    s,\n    h,\n    u,\n    c,\n    r,\n    i,\n    f,\n    g,\n    m,\n    y,\n    v,\n    w,\n    b,\n    x,\n    I,\n    C,\n    B,\n    N,\n    L,\n    A,\n    S,\n    j,\n    E,\n    M,\n    O,\n    q,\n    T,\n    R,\n    D,\n    U,\n    z,\n    H,\n    W,\n    V,\n    G,\n    Y,\n    J,\n    X,\n    K,\n    Z,\n    Q,\n    $,\n    tt,\n    et,\n    nt,\n    rt,\n    it,\n    ot,\n    at,\n    st,\n    lt = function (ie) {\n      function oe(o) {\n        if (\"object\" !== se(o)) throw new Error(\"Invalid Context passed to initialize PubSub (jsPDF-module)\");\n        var a = {};\n        this.subscribe = function (t, e, n) {\n          if (n = n || !1, \"string\" != typeof t || \"function\" != typeof e || \"boolean\" != typeof n) throw new Error(\"Invalid arguments passed to PubSub.subscribe (jsPDF-module)\");\n          a.hasOwnProperty(t) || (a[t] = {});\n          var r = Math.random().toString(35);\n          return a[t][r] = [e, !!n], r;\n        }, this.unsubscribe = function (t) {\n          for (var e in a) if (a[e][t]) return delete a[e][t], 0 === Object.keys(a[e]).length && delete a[e], !0;\n          return !1;\n        }, this.publish = function (t) {\n          if (a.hasOwnProperty(t)) {\n            var e = Array.prototype.slice.call(arguments, 1),\n              n = [];\n            for (var r in a[t]) {\n              var i = a[t][r];\n              try {\n                i[0].apply(o, e);\n              } catch (t) {\n                ie.console && console.error(\"jsPDF PubSub Error\", t.message, t);\n              }\n              i[1] && n.push(r);\n            }\n            n.length && n.forEach(this.unsubscribe);\n          }\n        }, this.getTopics = function () {\n          return a;\n        };\n      }\n      function ae(t, e, i, n) {\n        var r = {},\n          o = [],\n          a = 1;\n        \"object\" === se(t) && (t = (r = t).orientation, e = r.unit || e, i = r.format || i, n = r.compress || r.compressPdf || n, o = r.filters || (!0 === n ? [\"FlateEncode\"] : o), a = \"number\" == typeof r.userUnit ? Math.abs(r.userUnit) : 1), e = e || \"mm\", t = (\"\" + (t || \"P\")).toLowerCase();\n        var s = r.putOnlyUsedFonts || !0,\n          K = {},\n          l = {\n            internal: {},\n            __private__: {}\n          };\n        l.__private__.PubSub = oe;\n        var h = \"1.3\",\n          u = l.__private__.getPdfVersion = function () {\n            return h;\n          },\n          c = (l.__private__.setPdfVersion = function (t) {\n            h = t;\n          }, {\n            a0: [2383.94, 3370.39],\n            a1: [1683.78, 2383.94],\n            a2: [1190.55, 1683.78],\n            a3: [841.89, 1190.55],\n            a4: [595.28, 841.89],\n            a5: [419.53, 595.28],\n            a6: [297.64, 419.53],\n            a7: [209.76, 297.64],\n            a8: [147.4, 209.76],\n            a9: [104.88, 147.4],\n            a10: [73.7, 104.88],\n            b0: [2834.65, 4008.19],\n            b1: [2004.09, 2834.65],\n            b2: [1417.32, 2004.09],\n            b3: [1000.63, 1417.32],\n            b4: [708.66, 1000.63],\n            b5: [498.9, 708.66],\n            b6: [354.33, 498.9],\n            b7: [249.45, 354.33],\n            b8: [175.75, 249.45],\n            b9: [124.72, 175.75],\n            b10: [87.87, 124.72],\n            c0: [2599.37, 3676.54],\n            c1: [1836.85, 2599.37],\n            c2: [1298.27, 1836.85],\n            c3: [918.43, 1298.27],\n            c4: [649.13, 918.43],\n            c5: [459.21, 649.13],\n            c6: [323.15, 459.21],\n            c7: [229.61, 323.15],\n            c8: [161.57, 229.61],\n            c9: [113.39, 161.57],\n            c10: [79.37, 113.39],\n            dl: [311.81, 623.62],\n            letter: [612, 792],\n            \"government-letter\": [576, 756],\n            legal: [612, 1008],\n            \"junior-legal\": [576, 360],\n            ledger: [1224, 792],\n            tabloid: [792, 1224],\n            \"credit-card\": [153, 243]\n          }),\n          f = (l.__private__.getPageFormats = function () {\n            return c;\n          }, l.__private__.getPageFormat = function (t) {\n            return c[t];\n          });\n        \"string\" == typeof i && (i = f(i)), i = i || f(\"a4\");\n        var p,\n          Z = l.f2 = l.__private__.f2 = function (t) {\n            if (isNaN(t)) throw new Error(\"Invalid argument passed to jsPDF.f2\");\n            return t.toFixed(2);\n          },\n          Q = l.__private__.f3 = function (t) {\n            if (isNaN(t)) throw new Error(\"Invalid argument passed to jsPDF.f3\");\n            return t.toFixed(3);\n          },\n          d = \"00000000000000000000000000000000\",\n          g = l.__private__.getFileId = function () {\n            return d;\n          },\n          m = l.__private__.setFileId = function (t) {\n            return t = t || \"12345678901234567890123456789012\".split(\"\").map(function () {\n              return \"ABCDEF0123456789\".charAt(Math.floor(16 * Math.random()));\n            }).join(\"\"), d = t;\n          };\n        l.setFileId = function (t) {\n          return m(t), this;\n        }, l.getFileId = function () {\n          return g();\n        };\n        var y = l.__private__.convertDateToPDFDate = function (t) {\n            var e = t.getTimezoneOffset(),\n              n = e < 0 ? \"+\" : \"-\",\n              r = Math.floor(Math.abs(e / 60)),\n              i = Math.abs(e % 60),\n              o = [n, P(r), \"'\", P(i), \"'\"].join(\"\");\n            return [\"D:\", t.getFullYear(), P(t.getMonth() + 1), P(t.getDate()), P(t.getHours()), P(t.getMinutes()), P(t.getSeconds()), o].join(\"\");\n          },\n          v = l.__private__.convertPDFDateToDate = function (t) {\n            var e = parseInt(t.substr(2, 4), 10),\n              n = parseInt(t.substr(6, 2), 10) - 1,\n              r = parseInt(t.substr(8, 2), 10),\n              i = parseInt(t.substr(10, 2), 10),\n              o = parseInt(t.substr(12, 2), 10),\n              a = parseInt(t.substr(14, 2), 10);\n            parseInt(t.substr(16, 2), 10), parseInt(t.substr(20, 2), 10);\n            return new Date(e, n, r, i, o, a, 0);\n          },\n          w = l.__private__.setCreationDate = function (t) {\n            var e;\n            if (void 0 === t && (t = new Date()), \"object\" === se(t) && \"[object Date]\" === Object.prototype.toString.call(t)) e = y(t);else {\n              if (!/^D:(20[0-2][0-9]|203[0-7]|19[7-9][0-9])(0[0-9]|1[0-2])([0-2][0-9]|3[0-1])(0[0-9]|1[0-9]|2[0-3])(0[0-9]|[1-5][0-9])(0[0-9]|[1-5][0-9])(\\+0[0-9]|\\+1[0-4]|\\-0[0-9]|\\-1[0-1])\\'(0[0-9]|[1-5][0-9])\\'?$/.test(t)) throw new Error(\"Invalid argument passed to jsPDF.setCreationDate\");\n              e = t;\n            }\n            return p = e;\n          },\n          b = l.__private__.getCreationDate = function (t) {\n            var e = p;\n            return \"jsDate\" === t && (e = v(p)), e;\n          };\n        l.setCreationDate = function (t) {\n          return w(t), this;\n        }, l.getCreationDate = function (t) {\n          return b(t);\n        };\n        var x,\n          N,\n          L,\n          A,\n          S,\n          $,\n          _,\n          F,\n          P = l.__private__.padd2 = function (t) {\n            return (\"0\" + parseInt(t)).slice(-2);\n          },\n          k = !1,\n          I = [],\n          C = [],\n          B = 0,\n          tt = (l.__private__.setCustomOutputDestination = function (t) {\n            N = t;\n          }, l.__private__.resetCustomOutputDestination = function (t) {\n            N = void 0;\n          }, l.__private__.out = function (t) {\n            var e;\n            return t = \"string\" == typeof t ? t : t.toString(), (e = void 0 === N ? k ? I[x] : C : N).push(t), k || (B += t.length + 1), e;\n          }),\n          j = l.__private__.write = function (t) {\n            return tt(1 === arguments.length ? t.toString() : Array.prototype.join.call(arguments, \" \"));\n          },\n          E = l.__private__.getArrayBuffer = function (t) {\n            for (var e = t.length, n = new ArrayBuffer(e), r = new Uint8Array(n); e--;) r[e] = t.charCodeAt(e);\n            return n;\n          },\n          M = [[\"Helvetica\", \"helvetica\", \"normal\", \"WinAnsiEncoding\"], [\"Helvetica-Bold\", \"helvetica\", \"bold\", \"WinAnsiEncoding\"], [\"Helvetica-Oblique\", \"helvetica\", \"italic\", \"WinAnsiEncoding\"], [\"Helvetica-BoldOblique\", \"helvetica\", \"bolditalic\", \"WinAnsiEncoding\"], [\"Courier\", \"courier\", \"normal\", \"WinAnsiEncoding\"], [\"Courier-Bold\", \"courier\", \"bold\", \"WinAnsiEncoding\"], [\"Courier-Oblique\", \"courier\", \"italic\", \"WinAnsiEncoding\"], [\"Courier-BoldOblique\", \"courier\", \"bolditalic\", \"WinAnsiEncoding\"], [\"Times-Roman\", \"times\", \"normal\", \"WinAnsiEncoding\"], [\"Times-Bold\", \"times\", \"bold\", \"WinAnsiEncoding\"], [\"Times-Italic\", \"times\", \"italic\", \"WinAnsiEncoding\"], [\"Times-BoldItalic\", \"times\", \"bolditalic\", \"WinAnsiEncoding\"], [\"ZapfDingbats\", \"zapfdingbats\", \"normal\", null], [\"Symbol\", \"symbol\", \"normal\", null]],\n          et = (l.__private__.getStandardFonts = function (t) {\n            return M;\n          }, r.fontSize || 16),\n          O = (l.__private__.setFontSize = l.setFontSize = function (t) {\n            return et = t, this;\n          }, l.__private__.getFontSize = l.getFontSize = function () {\n            return et;\n          }),\n          nt = r.R2L || !1,\n          q = (l.__private__.setR2L = l.setR2L = function (t) {\n            return nt = t, this;\n          }, l.__private__.getR2L = l.getR2L = function (t) {\n            return nt;\n          }, l.__private__.setZoomMode = function (t) {\n            var e = [void 0, null, \"fullwidth\", \"fullheight\", \"fullpage\", \"original\"];\n            if (/^\\d*\\.?\\d*\\%$/.test(t)) L = t;else if (isNaN(t)) {\n              if (-1 === e.indexOf(t)) throw new Error('zoom must be Integer (e.g. 2), a percentage Value (e.g. 300%) or fullwidth, fullheight, fullpage, original. \"' + t + '\" is not recognized.');\n              L = t;\n            } else L = parseInt(t, 10);\n          }),\n          T = (l.__private__.getZoomMode = function () {\n            return L;\n          }, l.__private__.setPageMode = function (t) {\n            if (-1 == [void 0, null, \"UseNone\", \"UseOutlines\", \"UseThumbs\", \"FullScreen\"].indexOf(t)) throw new Error('Page mode must be one of UseNone, UseOutlines, UseThumbs, or FullScreen. \"' + t + '\" is not recognized.');\n            A = t;\n          }),\n          R = (l.__private__.getPageMode = function () {\n            return A;\n          }, l.__private__.setLayoutMode = function (t) {\n            if (-1 == [void 0, null, \"continuous\", \"single\", \"twoleft\", \"tworight\", \"two\"].indexOf(t)) throw new Error('Layout mode must be one of continuous, single, twoleft, tworight. \"' + t + '\" is not recognized.');\n            S = t;\n          }),\n          D = (l.__private__.getLayoutMode = function () {\n            return S;\n          }, l.__private__.setDisplayMode = l.setDisplayMode = function (t, e, n) {\n            return q(t), R(e), T(n), this;\n          }, {\n            title: \"\",\n            subject: \"\",\n            author: \"\",\n            keywords: \"\",\n            creator: \"\"\n          }),\n          U = (l.__private__.getDocumentProperty = function (t) {\n            if (-1 === Object.keys(D).indexOf(t)) throw new Error(\"Invalid argument passed to jsPDF.getDocumentProperty\");\n            return D[t];\n          }, l.__private__.getDocumentProperties = function (t) {\n            return D;\n          }, l.__private__.setDocumentProperties = l.setProperties = l.setDocumentProperties = function (t) {\n            for (var e in D) D.hasOwnProperty(e) && t[e] && (D[e] = t[e]);\n            return this;\n          }, l.__private__.setDocumentProperty = function (t, e) {\n            if (-1 === Object.keys(D).indexOf(t)) throw new Error(\"Invalid arguments passed to jsPDF.setDocumentProperty\");\n            return D[t] = e;\n          }, 0),\n          z = [],\n          rt = {},\n          H = {},\n          W = 0,\n          V = [],\n          G = [],\n          it = new oe(l),\n          Y = r.hotfixes || [],\n          J = l.__private__.newObject = function () {\n            var t = X();\n            return ot(t, !0), t;\n          },\n          X = l.__private__.newObjectDeferred = function () {\n            return z[++U] = function () {\n              return B;\n            }, U;\n          },\n          ot = function (t, e) {\n            return e = \"boolean\" == typeof e && e, z[t] = B, e && tt(t + \" 0 obj\"), t;\n          },\n          at = l.__private__.newAdditionalObject = function () {\n            var t = {\n              objId: X(),\n              content: \"\"\n            };\n            return G.push(t), t;\n          },\n          st = X(),\n          lt = X(),\n          ht = l.__private__.decodeColorString = function (t) {\n            var e = t.split(\" \");\n            if (2 === e.length && (\"g\" === e[1] || \"G\" === e[1])) {\n              var n = parseFloat(e[0]);\n              e = [n, n, n, \"r\"];\n            }\n            for (var r = \"#\", i = 0; i < 3; i++) r += (\"0\" + Math.floor(255 * parseFloat(e[i])).toString(16)).slice(-2);\n            return r;\n          },\n          ut = l.__private__.encodeColorString = function (t) {\n            var e;\n            \"string\" == typeof t && (t = {\n              ch1: t\n            });\n            var n = t.ch1,\n              r = t.ch2,\n              i = t.ch3,\n              o = t.ch4,\n              a = (t.precision, \"draw\" === t.pdfColorType ? [\"G\", \"RG\", \"K\"] : [\"g\", \"rg\", \"k\"]);\n            if (\"string\" == typeof n && \"#\" !== n.charAt(0)) {\n              var s = new RGBColor(n);\n              if (s.ok) n = s.toHex();else if (!/^\\d*\\.?\\d*$/.test(n)) throw new Error('Invalid color \"' + n + '\" passed to jsPDF.encodeColorString.');\n            }\n            if (\"string\" == typeof n && /^#[0-9A-Fa-f]{3}$/.test(n) && (n = \"#\" + n[1] + n[1] + n[2] + n[2] + n[3] + n[3]), \"string\" == typeof n && /^#[0-9A-Fa-f]{6}$/.test(n)) {\n              var l = parseInt(n.substr(1), 16);\n              n = l >> 16 & 255, r = l >> 8 & 255, i = 255 & l;\n            }\n            if (void 0 === r || void 0 === o && n === r && r === i) {\n              if (\"string\" == typeof n) e = n + \" \" + a[0];else switch (t.precision) {\n                case 2:\n                  e = Z(n / 255) + \" \" + a[0];\n                  break;\n                case 3:\n                default:\n                  e = Q(n / 255) + \" \" + a[0];\n              }\n            } else if (void 0 === o || \"object\" === se(o)) {\n              if (o && !isNaN(o.a) && 0 === o.a) return e = [\"1.000\", \"1.000\", \"1.000\", a[1]].join(\" \");\n              if (\"string\" == typeof n) e = [n, r, i, a[1]].join(\" \");else switch (t.precision) {\n                case 2:\n                  e = [Z(n / 255), Z(r / 255), Z(i / 255), a[1]].join(\" \");\n                  break;\n                default:\n                case 3:\n                  e = [Q(n / 255), Q(r / 255), Q(i / 255), a[1]].join(\" \");\n              }\n            } else if (\"string\" == typeof n) e = [n, r, i, o, a[2]].join(\" \");else switch (t.precision) {\n              case 2:\n                e = [Z(n / 255), Z(r / 255), Z(i / 255), Z(o / 255), a[2]].join(\" \");\n                break;\n              case 3:\n              default:\n                e = [Q(n / 255), Q(r / 255), Q(i / 255), Q(o / 255), a[2]].join(\" \");\n            }\n            return e;\n          },\n          ct = l.__private__.getFilters = function () {\n            return o;\n          },\n          ft = l.__private__.putStream = function (t) {\n            var e = (t = t || {}).data || \"\",\n              n = t.filters || ct(),\n              r = t.alreadyAppliedFilters || [],\n              i = t.addLength1 || !1,\n              o = e.length,\n              a = {};\n            !0 === n && (n = [\"FlateEncode\"]);\n            var s = t.additionalKeyValues || [],\n              l = (a = void 0 !== ae.API.processDataByFilters ? ae.API.processDataByFilters(e, n) : {\n                data: e,\n                reverseChain: []\n              }).reverseChain + (Array.isArray(r) ? r.join(\" \") : r.toString());\n            0 !== a.data.length && (s.push({\n              key: \"Length\",\n              value: a.data.length\n            }), !0 === i && s.push({\n              key: \"Length1\",\n              value: o\n            })), 0 != l.length && (l.split(\"/\").length - 1 == 1 ? s.push({\n              key: \"Filter\",\n              value: l\n            }) : s.push({\n              key: \"Filter\",\n              value: \"[\" + l + \"]\"\n            })), tt(\"<<\");\n            for (var h = 0; h < s.length; h++) tt(\"/\" + s[h].key + \" \" + s[h].value);\n            tt(\">>\"), 0 !== a.data.length && (tt(\"stream\"), tt(a.data), tt(\"endstream\"));\n          },\n          pt = l.__private__.putPage = function (t) {\n            t.mediaBox;\n            var e = t.number,\n              n = t.data,\n              r = t.objId,\n              i = t.contentsObjId;\n            ot(r, !0);\n            V[x].mediaBox.topRightX, V[x].mediaBox.bottomLeftX, V[x].mediaBox.topRightY, V[x].mediaBox.bottomLeftY;\n            tt(\"<</Type /Page\"), tt(\"/Parent \" + t.rootDictionaryObjId + \" 0 R\"), tt(\"/Resources \" + t.resourceDictionaryObjId + \" 0 R\"), tt(\"/MediaBox [\" + parseFloat(Z(t.mediaBox.bottomLeftX)) + \" \" + parseFloat(Z(t.mediaBox.bottomLeftY)) + \" \" + Z(t.mediaBox.topRightX) + \" \" + Z(t.mediaBox.topRightY) + \"]\"), null !== t.cropBox && tt(\"/CropBox [\" + Z(t.cropBox.bottomLeftX) + \" \" + Z(t.cropBox.bottomLeftY) + \" \" + Z(t.cropBox.topRightX) + \" \" + Z(t.cropBox.topRightY) + \"]\"), null !== t.bleedBox && tt(\"/BleedBox [\" + Z(t.bleedBox.bottomLeftX) + \" \" + Z(t.bleedBox.bottomLeftY) + \" \" + Z(t.bleedBox.topRightX) + \" \" + Z(t.bleedBox.topRightY) + \"]\"), null !== t.trimBox && tt(\"/TrimBox [\" + Z(t.trimBox.bottomLeftX) + \" \" + Z(t.trimBox.bottomLeftY) + \" \" + Z(t.trimBox.topRightX) + \" \" + Z(t.trimBox.topRightY) + \"]\"), null !== t.artBox && tt(\"/ArtBox [\" + Z(t.artBox.bottomLeftX) + \" \" + Z(t.artBox.bottomLeftY) + \" \" + Z(t.artBox.topRightX) + \" \" + Z(t.artBox.topRightY) + \"]\"), \"number\" == typeof t.userUnit && 1 !== t.userUnit && tt(\"/UserUnit \" + t.userUnit), it.publish(\"putPage\", {\n              objId: r,\n              pageContext: V[e],\n              pageNumber: e,\n              page: n\n            }), tt(\"/Contents \" + i + \" 0 R\"), tt(\">>\"), tt(\"endobj\");\n            var o = n.join(\"\\n\");\n            return ot(i, !0), ft({\n              data: o,\n              filters: ct()\n            }), tt(\"endobj\"), r;\n          },\n          dt = l.__private__.putPages = function () {\n            var t,\n              e,\n              n = [];\n            for (t = 1; t <= W; t++) V[t].objId = X(), V[t].contentsObjId = X();\n            for (t = 1; t <= W; t++) n.push(pt({\n              number: t,\n              data: I[t],\n              objId: V[t].objId,\n              contentsObjId: V[t].contentsObjId,\n              mediaBox: V[t].mediaBox,\n              cropBox: V[t].cropBox,\n              bleedBox: V[t].bleedBox,\n              trimBox: V[t].trimBox,\n              artBox: V[t].artBox,\n              userUnit: V[t].userUnit,\n              rootDictionaryObjId: st,\n              resourceDictionaryObjId: lt\n            }));\n            ot(st, !0), tt(\"<</Type /Pages\");\n            var r = \"/Kids [\";\n            for (e = 0; e < W; e++) r += n[e] + \" 0 R \";\n            tt(r + \"]\"), tt(\"/Count \" + W), tt(\">>\"), tt(\"endobj\"), it.publish(\"postPutPages\");\n          },\n          gt = function () {\n            !function () {\n              for (var t in rt) rt.hasOwnProperty(t) && (!1 === s || !0 === s && K.hasOwnProperty(t)) && (e = rt[t], it.publish(\"putFont\", {\n                font: e,\n                out: tt,\n                newObject: J,\n                putStream: ft\n              }), !0 !== e.isAlreadyPutted && (e.objectNumber = J(), tt(\"<<\"), tt(\"/Type /Font\"), tt(\"/BaseFont /\" + e.postScriptName), tt(\"/Subtype /Type1\"), \"string\" == typeof e.encoding && tt(\"/Encoding /\" + e.encoding), tt(\"/FirstChar 32\"), tt(\"/LastChar 255\"), tt(\">>\"), tt(\"endobj\")));\n              var e;\n            }(), it.publish(\"putResources\"), ot(lt, !0), tt(\"<<\"), function () {\n              for (var t in tt(\"/ProcSet [/PDF /Text /ImageB /ImageC /ImageI]\"), tt(\"/Font <<\"), rt) rt.hasOwnProperty(t) && (!1 === s || !0 === s && K.hasOwnProperty(t)) && tt(\"/\" + t + \" \" + rt[t].objectNumber + \" 0 R\");\n              tt(\">>\"), tt(\"/XObject <<\"), it.publish(\"putXobjectDict\"), tt(\">>\");\n            }(), tt(\">>\"), tt(\"endobj\"), it.publish(\"postPutResources\");\n          },\n          mt = function (t, e, n) {\n            H.hasOwnProperty(e) || (H[e] = {}), H[e][n] = t;\n          },\n          yt = function (t, e, n, r, i) {\n            i = i || !1;\n            var o = \"F\" + (Object.keys(rt).length + 1).toString(10),\n              a = {\n                id: o,\n                postScriptName: t,\n                fontName: e,\n                fontStyle: n,\n                encoding: r,\n                isStandardFont: i,\n                metadata: {}\n              };\n            return it.publish(\"addFont\", {\n              font: a,\n              instance: this\n            }), void 0 !== o && (rt[o] = a, mt(o, e, n)), o;\n          },\n          vt = l.__private__.pdfEscape = l.pdfEscape = function (t, e) {\n            return function (t, e) {\n              var n, r, i, o, a, s, l, h, u;\n              if (i = (e = e || {}).sourceEncoding || \"Unicode\", a = e.outputEncoding, (e.autoencode || a) && rt[$].metadata && rt[$].metadata[i] && rt[$].metadata[i].encoding && (o = rt[$].metadata[i].encoding, !a && rt[$].encoding && (a = rt[$].encoding), !a && o.codePages && (a = o.codePages[0]), \"string\" == typeof a && (a = o[a]), a)) {\n                for (l = !1, s = [], n = 0, r = t.length; n < r; n++) (h = a[t.charCodeAt(n)]) ? s.push(String.fromCharCode(h)) : s.push(t[n]), s[n].charCodeAt(0) >> 8 && (l = !0);\n                t = s.join(\"\");\n              }\n              for (n = t.length; void 0 === l && 0 !== n;) t.charCodeAt(n - 1) >> 8 && (l = !0), n--;\n              if (!l) return t;\n              for (s = e.noBOM ? [] : [254, 255], n = 0, r = t.length; n < r; n++) {\n                if ((u = (h = t.charCodeAt(n)) >> 8) >> 8) throw new Error(\"Character at position \" + n + \" of string '\" + t + \"' exceeds 16bits. Cannot be encoded into UCS-2 BE\");\n                s.push(u), s.push(h - (u << 8));\n              }\n              return String.fromCharCode.apply(void 0, s);\n            }(t, e).replace(/\\\\/g, \"\\\\\\\\\").replace(/\\(/g, \"\\\\(\").replace(/\\)/g, \"\\\\)\");\n          },\n          wt = l.__private__.beginPage = function (t, e) {\n            var n,\n              r = \"string\" == typeof e && e.toLowerCase();\n            if (\"string\" == typeof t && (n = f(t.toLowerCase())) && (t = n[0], e = n[1]), Array.isArray(t) && (e = t[1], t = t[0]), (isNaN(t) || isNaN(e)) && (t = i[0], e = i[1]), r) {\n              switch (r.substr(0, 1)) {\n                case \"l\":\n                  t < e && (r = \"s\");\n                  break;\n                case \"p\":\n                  e < t && (r = \"s\");\n              }\n              \"s\" === r && (n = t, t = e, e = n);\n            }\n            (14400 < t || 14400 < e) && (console.warn(\"A page in a PDF can not be wider or taller than 14400 userUnit. jsPDF limits the width/height to 14400\"), t = Math.min(14400, t), e = Math.min(14400, e)), i = [t, e], k = !0, I[++W] = [], V[W] = {\n              objId: 0,\n              contentsObjId: 0,\n              userUnit: Number(a),\n              artBox: null,\n              bleedBox: null,\n              cropBox: null,\n              trimBox: null,\n              mediaBox: {\n                bottomLeftX: 0,\n                bottomLeftY: 0,\n                topRightX: Number(t),\n                topRightY: Number(e)\n              }\n            }, xt(W);\n          },\n          bt = function () {\n            wt.apply(this, arguments), Dt(Rt), tt(Jt), 0 !== te && tt(te + \" J\"), 0 !== ne && tt(ne + \" j\"), it.publish(\"addPage\", {\n              pageNumber: W\n            });\n          },\n          xt = function (t) {\n            0 < t && t <= W && (x = t);\n          },\n          Nt = l.__private__.getNumberOfPages = l.getNumberOfPages = function () {\n            return I.length - 1;\n          },\n          Lt = function (t, e, n) {\n            var r,\n              i = void 0;\n            return n = n || {}, t = void 0 !== t ? t : rt[$].fontName, e = void 0 !== e ? e : rt[$].fontStyle, r = t.toLowerCase(), void 0 !== H[r] && void 0 !== H[r][e] ? i = H[r][e] : void 0 !== H[t] && void 0 !== H[t][e] ? i = H[t][e] : !1 === n.disableWarning && console.warn(\"Unable to look up font label for font '\" + t + \"', '\" + e + \"'. Refer to getFontList() for available fonts.\"), i || n.noFallback || null == (i = H.times[e]) && (i = H.times.normal), i;\n          },\n          At = l.__private__.putInfo = function () {\n            for (var t in J(), tt(\"<<\"), tt(\"/Producer (jsPDF \" + ae.version + \")\"), D) D.hasOwnProperty(t) && D[t] && tt(\"/\" + t.substr(0, 1).toUpperCase() + t.substr(1) + \" (\" + vt(D[t]) + \")\");\n            tt(\"/CreationDate (\" + p + \")\"), tt(\">>\"), tt(\"endobj\");\n          },\n          St = l.__private__.putCatalog = function (t) {\n            var e = (t = t || {}).rootDictionaryObjId || st;\n            switch (J(), tt(\"<<\"), tt(\"/Type /Catalog\"), tt(\"/Pages \" + e + \" 0 R\"), L || (L = \"fullwidth\"), L) {\n              case \"fullwidth\":\n                tt(\"/OpenAction [3 0 R /FitH null]\");\n                break;\n              case \"fullheight\":\n                tt(\"/OpenAction [3 0 R /FitV null]\");\n                break;\n              case \"fullpage\":\n                tt(\"/OpenAction [3 0 R /Fit]\");\n                break;\n              case \"original\":\n                tt(\"/OpenAction [3 0 R /XYZ null null 1]\");\n                break;\n              default:\n                var n = \"\" + L;\n                \"%\" === n.substr(n.length - 1) && (L = parseInt(L) / 100), \"number\" == typeof L && tt(\"/OpenAction [3 0 R /XYZ null null \" + Z(L) + \"]\");\n            }\n            switch (S || (S = \"continuous\"), S) {\n              case \"continuous\":\n                tt(\"/PageLayout /OneColumn\");\n                break;\n              case \"single\":\n                tt(\"/PageLayout /SinglePage\");\n                break;\n              case \"two\":\n              case \"twoleft\":\n                tt(\"/PageLayout /TwoColumnLeft\");\n                break;\n              case \"tworight\":\n                tt(\"/PageLayout /TwoColumnRight\");\n            }\n            A && tt(\"/PageMode /\" + A), it.publish(\"putCatalog\"), tt(\">>\"), tt(\"endobj\");\n          },\n          _t = l.__private__.putTrailer = function () {\n            tt(\"trailer\"), tt(\"<<\"), tt(\"/Size \" + (U + 1)), tt(\"/Root \" + U + \" 0 R\"), tt(\"/Info \" + (U - 1) + \" 0 R\"), tt(\"/ID [ <\" + d + \"> <\" + d + \"> ]\"), tt(\">>\");\n          },\n          Ft = l.__private__.putHeader = function () {\n            tt(\"%PDF-\" + h), tt(\"%ºß¬à\");\n          },\n          Pt = l.__private__.putXRef = function () {\n            var t = 1,\n              e = \"0000000000\";\n            for (tt(\"xref\"), tt(\"0 \" + (U + 1)), tt(\"0000000000 65535 f \"), t = 1; t <= U; t++) {\n              \"function\" == typeof z[t] ? tt((e + z[t]()).slice(-10) + \" 00000 n \") : void 0 !== z[t] ? tt((e + z[t]).slice(-10) + \" 00000 n \") : tt(\"0000000000 00000 n \");\n            }\n          },\n          kt = l.__private__.buildDocument = function () {\n            k = !1, B = U = 0, C = [], z = [], G = [], st = X(), lt = X(), it.publish(\"buildDocument\"), Ft(), dt(), function () {\n              it.publish(\"putAdditionalObjects\");\n              for (var t = 0; t < G.length; t++) {\n                var e = G[t];\n                ot(e.objId, !0), tt(e.content), tt(\"endobj\");\n              }\n              it.publish(\"postPutAdditionalObjects\");\n            }(), gt(), At(), St();\n            var t = B;\n            return Pt(), _t(), tt(\"startxref\"), tt(\"\" + t), tt(\"%%EOF\"), k = !0, C.join(\"\\n\");\n          },\n          It = l.__private__.getBlob = function (t) {\n            return new Blob([E(t)], {\n              type: \"application/pdf\"\n            });\n          },\n          Ct = l.output = l.__private__.output = ((F = function (t, e) {\n            e = e || {};\n            var n = kt();\n            switch (\"string\" == typeof e ? e = {\n              filename: e\n            } : e.filename = e.filename || \"generated.pdf\", t) {\n              case void 0:\n                return n;\n              case \"save\":\n                l.save(e.filename);\n                break;\n              case \"arraybuffer\":\n                return E(n);\n              case \"blob\":\n                return It(n);\n              case \"bloburi\":\n              case \"bloburl\":\n                if (void 0 !== ie.URL && \"function\" == typeof ie.URL.createObjectURL) return ie.URL && ie.URL.createObjectURL(It(n)) || void 0;\n                console.warn(\"bloburl is not supported by your system, because URL.createObjectURL is not supported by your browser.\");\n                break;\n              case \"datauristring\":\n              case \"dataurlstring\":\n                return \"data:application/pdf;filename=\" + e.filename + \";base64,\" + btoa(n);\n              case \"dataurlnewwindow\":\n                var r = '<html><style>html, body { padding: 0; margin: 0; } iframe { width: 100%; height: 100%; border: 0;}  </style><body><iframe src=\"' + this.output(\"datauristring\") + '\"></iframe></body></html>',\n                  i = ie.open();\n                if (null !== i && i.document.write(r), i || \"undefined\" == typeof safari) return i;\n              case \"datauri\":\n              case \"dataurl\":\n                return ie.document.location.href = \"data:application/pdf;filename=\" + e.filename + \";base64,\" + btoa(n);\n              default:\n                return null;\n            }\n          }).foo = function () {\n            try {\n              return F.apply(this, arguments);\n            } catch (t) {\n              var e = t.stack || \"\";\n              ~e.indexOf(\" at \") && (e = e.split(\" at \")[1]);\n              var n = \"Error in function \" + e.split(\"\\n\")[0].split(\"<\")[0] + \": \" + t.message;\n              if (!ie.console) throw new Error(n);\n              ie.console.error(n, t), ie.alert && alert(n);\n            }\n          }, (F.foo.bar = F).foo),\n          Bt = function (t) {\n            return !0 === Array.isArray(Y) && -1 < Y.indexOf(t);\n          };\n        switch (e) {\n          case \"pt\":\n            _ = 1;\n            break;\n          case \"mm\":\n            _ = 72 / 25.4;\n            break;\n          case \"cm\":\n            _ = 72 / 2.54;\n            break;\n          case \"in\":\n            _ = 72;\n            break;\n          case \"px\":\n            _ = 1 == Bt(\"px_scaling\") ? .75 : 96 / 72;\n            break;\n          case \"pc\":\n          case \"em\":\n            _ = 12;\n            break;\n          case \"ex\":\n            _ = 6;\n            break;\n          default:\n            throw new Error(\"Invalid unit: \" + e);\n        }\n        w(), m();\n        var jt = l.__private__.getPageInfo = function (t) {\n            if (isNaN(t) || t % 1 != 0) throw new Error(\"Invalid argument passed to jsPDF.getPageInfo\");\n            return {\n              objId: V[t].objId,\n              pageNumber: t,\n              pageContext: V[t]\n            };\n          },\n          Et = l.__private__.getPageInfoByObjId = function (t) {\n            for (var e in V) if (V[e].objId === t) break;\n            if (isNaN(t) || t % 1 != 0) throw new Error(\"Invalid argument passed to jsPDF.getPageInfoByObjId\");\n            return jt(e);\n          },\n          Mt = l.__private__.getCurrentPageInfo = function () {\n            return {\n              objId: V[x].objId,\n              pageNumber: x,\n              pageContext: V[x]\n            };\n          };\n        l.addPage = function () {\n          return bt.apply(this, arguments), this;\n        }, l.setPage = function () {\n          return xt.apply(this, arguments), this;\n        }, l.insertPage = function (t) {\n          return this.addPage(), this.movePage(x, t), this;\n        }, l.movePage = function (t, e) {\n          if (e < t) {\n            for (var n = I[t], r = V[t], i = t; e < i; i--) I[i] = I[i - 1], V[i] = V[i - 1];\n            I[e] = n, V[e] = r, this.setPage(e);\n          } else if (t < e) {\n            for (n = I[t], r = V[t], i = t; i < e; i++) I[i] = I[i + 1], V[i] = V[i + 1];\n            I[e] = n, V[e] = r, this.setPage(e);\n          }\n          return this;\n        }, l.deletePage = function () {\n          return function (t) {\n            0 < t && t <= W && (I.splice(t, 1), --W < x && (x = W), this.setPage(x));\n          }.apply(this, arguments), this;\n        };\n        l.__private__.text = l.text = function (t, e, n, i) {\n          var r;\n          \"number\" != typeof t || \"number\" != typeof e || \"string\" != typeof n && !Array.isArray(n) || (r = n, n = e, e = t, t = r);\n          var o = arguments[3],\n            a = arguments[4],\n            s = arguments[5];\n          if (\"object\" === se(o) && null !== o || (\"string\" == typeof a && (s = a, a = null), \"string\" == typeof o && (s = o, o = null), \"number\" == typeof o && (a = o, o = null), i = {\n            flags: o,\n            angle: a,\n            align: s\n          }), (o = o || {}).noBOM = o.noBOM || !0, o.autoencode = o.autoencode || !0, isNaN(e) || isNaN(n) || null == t) throw new Error(\"Invalid arguments passed to jsPDF.text\");\n          if (0 === t.length) return c;\n          var l,\n            h = \"\",\n            u = \"number\" == typeof i.lineHeightFactor ? i.lineHeightFactor : Tt,\n            c = i.scope || this;\n          function f(t) {\n            for (var e, n = t.concat(), r = [], i = n.length; i--;) \"string\" == typeof (e = n.shift()) ? r.push(e) : Array.isArray(t) && 1 === e.length ? r.push(e[0]) : r.push([e[0], e[1], e[2]]);\n            return r;\n          }\n          function p(t, e) {\n            var n;\n            if (\"string\" == typeof t) n = e(t)[0];else if (Array.isArray(t)) {\n              for (var r, i, o = t.concat(), a = [], s = o.length; s--;) \"string\" == typeof (r = o.shift()) ? a.push(e(r)[0]) : Array.isArray(r) && \"string\" === r[0] && (i = e(r[0], r[1], r[2]), a.push([i[0], i[1], i[2]]));\n              n = a;\n            }\n            return n;\n          }\n          var d = !1,\n            g = !0;\n          if (\"string\" == typeof t) d = !0;else if (Array.isArray(t)) {\n            for (var m, y = t.concat(), v = [], w = y.length; w--;) (\"string\" != typeof (m = y.shift()) || Array.isArray(m) && \"string\" != typeof m[0]) && (g = !1);\n            d = g;\n          }\n          if (!1 === d) throw new Error('Type of text must be string or Array. \"' + t + '\" is not recognized.');\n          var b = rt[$].encoding;\n          \"WinAnsiEncoding\" !== b && \"StandardEncoding\" !== b || (t = p(t, function (t, e, n) {\n            return [(r = t, r = r.split(\"\\t\").join(Array(i.TabLen || 9).join(\" \")), vt(r, o)), e, n];\n            var r;\n          })), \"string\" == typeof t && (t = t.match(/[\\r?\\n]/) ? t.split(/\\r\\n|\\r|\\n/g) : [t]);\n          var x = et / c.internal.scaleFactor,\n            N = x * (Tt - 1);\n          switch (i.baseline) {\n            case \"bottom\":\n              n -= N;\n              break;\n            case \"top\":\n              n += x - N;\n              break;\n            case \"hanging\":\n              n += x - 2 * N;\n              break;\n            case \"middle\":\n              n += x / 2 - N;\n          }\n          0 < (O = i.maxWidth || 0) && (\"string\" == typeof t ? t = c.splitTextToSize(t, O) : \"[object Array]\" === Object.prototype.toString.call(t) && (t = c.splitTextToSize(t.join(\" \"), O)));\n          var L = {\n            text: t,\n            x: e,\n            y: n,\n            options: i,\n            mutex: {\n              pdfEscape: vt,\n              activeFontKey: $,\n              fonts: rt,\n              activeFontSize: et\n            }\n          };\n          it.publish(\"preProcessText\", L), t = L.text;\n          a = (i = L.options).angle;\n          var A = c.internal.scaleFactor,\n            S = [];\n          if (a) {\n            a *= Math.PI / 180;\n            var _ = Math.cos(a),\n              F = Math.sin(a);\n            S = [Z(_), Z(F), Z(-1 * F), Z(_)];\n          }\n          void 0 !== (M = i.charSpace) && (h += Q(M * A) + \" Tc\\n\");\n          i.lang;\n          var P = -1,\n            k = void 0 !== i.renderingMode ? i.renderingMode : i.stroke,\n            I = c.internal.getCurrentPageInfo().pageContext;\n          switch (k) {\n            case 0:\n            case !1:\n            case \"fill\":\n              P = 0;\n              break;\n            case 1:\n            case !0:\n            case \"stroke\":\n              P = 1;\n              break;\n            case 2:\n            case \"fillThenStroke\":\n              P = 2;\n              break;\n            case 3:\n            case \"invisible\":\n              P = 3;\n              break;\n            case 4:\n            case \"fillAndAddForClipping\":\n              P = 4;\n              break;\n            case 5:\n            case \"strokeAndAddPathForClipping\":\n              P = 5;\n              break;\n            case 6:\n            case \"fillThenStrokeAndAddToPathForClipping\":\n              P = 6;\n              break;\n            case 7:\n            case \"addToPathForClipping\":\n              P = 7;\n          }\n          var C = void 0 !== I.usedRenderingMode ? I.usedRenderingMode : -1;\n          -1 !== P ? h += P + \" Tr\\n\" : -1 !== C && (h += \"0 Tr\\n\"), -1 !== P && (I.usedRenderingMode = P);\n          s = i.align || \"left\";\n          var B = et * u,\n            j = c.internal.pageSize.getWidth(),\n            E = (A = c.internal.scaleFactor, rt[$]),\n            M = i.charSpace || Qt,\n            O = i.maxWidth || 0,\n            q = (o = {}, []);\n          if (\"[object Array]\" === Object.prototype.toString.call(t)) {\n            var T, R;\n            v = f(t);\n            \"left\" !== s && (R = v.map(function (t) {\n              return c.getStringUnitWidth(t, {\n                font: E,\n                charSpace: M,\n                fontSize: et\n              }) * et / A;\n            }));\n            var D,\n              U = Math.max.apply(Math, R),\n              z = 0;\n            if (\"right\" === s) {\n              e -= R[0], t = [];\n              var H = 0;\n              for (w = v.length; H < w; H++) U - R[H], T = 0 === H ? (D = Wt(e), Vt(n)) : (D = (z - R[H]) * A, -B), t.push([v[H], D, T]), z = R[H];\n            } else if (\"center\" === s) {\n              e -= R[0] / 2, t = [];\n              for (H = 0, w = v.length; H < w; H++) (U - R[H]) / 2, T = 0 === H ? (D = Wt(e), Vt(n)) : (D = (z - R[H]) / 2 * A, -B), t.push([v[H], D, T]), z = R[H];\n            } else if (\"left\" === s) {\n              t = [];\n              for (H = 0, w = v.length; H < w; H++) T = 0 === H ? Vt(n) : -B, D = 0 === H ? Wt(e) : 0, t.push(v[H]);\n            } else {\n              if (\"justify\" !== s) throw new Error('Unrecognized alignment option, use \"left\", \"center\", \"right\" or \"justify\".');\n              t = [];\n              for (O = 0 !== O ? O : j, H = 0, w = v.length; H < w; H++) T = 0 === H ? Vt(n) : -B, D = 0 === H ? Wt(e) : 0, H < w - 1 && q.push(((O - R[H]) / (v[H].split(\" \").length - 1) * A).toFixed(2)), t.push([v[H], D, T]);\n            }\n          }\n          !0 === (\"boolean\" == typeof i.R2L ? i.R2L : nt) && (t = p(t, function (t, e, n) {\n            return [t.split(\"\").reverse().join(\"\"), e, n];\n          }));\n          L = {\n            text: t,\n            x: e,\n            y: n,\n            options: i,\n            mutex: {\n              pdfEscape: vt,\n              activeFontKey: $,\n              fonts: rt,\n              activeFontSize: et\n            }\n          };\n          it.publish(\"postProcessText\", L), t = L.text, l = L.mutex.isHex;\n          v = f(t);\n          t = [];\n          var W,\n            V,\n            G,\n            Y = 0,\n            J = (w = v.length, \"\");\n          for (H = 0; H < w; H++) J = \"\", Array.isArray(v[H]) ? (W = parseFloat(v[H][1]), V = parseFloat(v[H][2]), G = (l ? \"<\" : \"(\") + v[H][0] + (l ? \">\" : \")\"), Y = 1) : (W = Wt(e), V = Vt(n), G = (l ? \"<\" : \"(\") + v[H] + (l ? \">\" : \")\")), void 0 !== q && void 0 !== q[H] && (J = q[H] + \" Tw\\n\"), 0 !== S.length && 0 === H ? t.push(J + S.join(\" \") + \" \" + W.toFixed(2) + \" \" + V.toFixed(2) + \" Tm\\n\" + G) : 1 === Y || 0 === Y && 0 === H ? t.push(J + W.toFixed(2) + \" \" + V.toFixed(2) + \" Td\\n\" + G) : t.push(J + G);\n          t = 0 === Y ? t.join(\" Tj\\nT* \") : t.join(\" Tj\\n\"), t += \" Tj\\n\";\n          var X = \"BT\\n/\" + $ + \" \" + et + \" Tf\\n\" + (et * u).toFixed(2) + \" TL\\n\" + Kt + \"\\n\";\n          return X += h, X += t, tt(X += \"ET\"), K[$] = !0, c;\n        }, l.__private__.lstext = l.lstext = function (t, e, n, r) {\n          return console.warn(\"jsPDF.lstext is deprecated\"), this.text(t, e, n, {\n            charSpace: r\n          });\n        }, l.__private__.clip = l.clip = function (t) {\n          tt(\"evenodd\" === t ? \"W*\" : \"W\"), tt(\"n\");\n        }, l.__private__.clip_fixed = l.clip_fixed = function (t) {\n          console.log(\"clip_fixed is deprecated\"), l.clip(t);\n        };\n        var Ot = l.__private__.isValidStyle = function (t) {\n            var e = !1;\n            return -1 !== [void 0, null, \"S\", \"F\", \"DF\", \"FD\", \"f\", \"f*\", \"B\", \"B*\"].indexOf(t) && (e = !0), e;\n          },\n          qt = l.__private__.getStyle = function (t) {\n            var e = \"S\";\n            return \"F\" === t ? e = \"f\" : \"FD\" === t || \"DF\" === t ? e = \"B\" : \"f\" !== t && \"f*\" !== t && \"B\" !== t && \"B*\" !== t || (e = t), e;\n          };\n        l.__private__.line = l.line = function (t, e, n, r) {\n          if (isNaN(t) || isNaN(e) || isNaN(n) || isNaN(r)) throw new Error(\"Invalid arguments passed to jsPDF.line\");\n          return this.lines([[n - t, r - e]], t, e);\n        }, l.__private__.lines = l.lines = function (t, e, n, r, i, o) {\n          var a, s, l, h, u, c, f, p, d, g, m, y;\n          if (\"number\" == typeof t && (y = n, n = e, e = t, t = y), r = r || [1, 1], o = o || !1, isNaN(e) || isNaN(n) || !Array.isArray(t) || !Array.isArray(r) || !Ot(i) || \"boolean\" != typeof o) throw new Error(\"Invalid arguments passed to jsPDF.lines\");\n          for (tt(Q(Wt(e)) + \" \" + Q(Vt(n)) + \" m \"), a = r[0], s = r[1], h = t.length, g = e, m = n, l = 0; l < h; l++) 2 === (u = t[l]).length ? (g = u[0] * a + g, m = u[1] * s + m, tt(Q(Wt(g)) + \" \" + Q(Vt(m)) + \" l\")) : (c = u[0] * a + g, f = u[1] * s + m, p = u[2] * a + g, d = u[3] * s + m, g = u[4] * a + g, m = u[5] * s + m, tt(Q(Wt(c)) + \" \" + Q(Vt(f)) + \" \" + Q(Wt(p)) + \" \" + Q(Vt(d)) + \" \" + Q(Wt(g)) + \" \" + Q(Vt(m)) + \" c\"));\n          return o && tt(\" h\"), null !== i && tt(qt(i)), this;\n        }, l.__private__.rect = l.rect = function (t, e, n, r, i) {\n          if (isNaN(t) || isNaN(e) || isNaN(n) || isNaN(r) || !Ot(i)) throw new Error(\"Invalid arguments passed to jsPDF.rect\");\n          return tt([Z(Wt(t)), Z(Vt(e)), Z(n * _), Z(-r * _), \"re\"].join(\" \")), null !== i && tt(qt(i)), this;\n        }, l.__private__.triangle = l.triangle = function (t, e, n, r, i, o, a) {\n          if (isNaN(t) || isNaN(e) || isNaN(n) || isNaN(r) || isNaN(i) || isNaN(o) || !Ot(a)) throw new Error(\"Invalid arguments passed to jsPDF.triangle\");\n          return this.lines([[n - t, r - e], [i - n, o - r], [t - i, e - o]], t, e, [1, 1], a, !0), this;\n        }, l.__private__.roundedRect = l.roundedRect = function (t, e, n, r, i, o, a) {\n          if (isNaN(t) || isNaN(e) || isNaN(n) || isNaN(r) || isNaN(i) || isNaN(o) || !Ot(a)) throw new Error(\"Invalid arguments passed to jsPDF.roundedRect\");\n          var s = 4 / 3 * (Math.SQRT2 - 1);\n          return this.lines([[n - 2 * i, 0], [i * s, 0, i, o - o * s, i, o], [0, r - 2 * o], [0, o * s, -i * s, o, -i, o], [2 * i - n, 0], [-i * s, 0, -i, -o * s, -i, -o], [0, 2 * o - r], [0, -o * s, i * s, -o, i, -o]], t + i, e, [1, 1], a), this;\n        }, l.__private__.ellipse = l.ellipse = function (t, e, n, r, i) {\n          if (isNaN(t) || isNaN(e) || isNaN(n) || isNaN(r) || !Ot(i)) throw new Error(\"Invalid arguments passed to jsPDF.ellipse\");\n          var o = 4 / 3 * (Math.SQRT2 - 1) * n,\n            a = 4 / 3 * (Math.SQRT2 - 1) * r;\n          return tt([Z(Wt(t + n)), Z(Vt(e)), \"m\", Z(Wt(t + n)), Z(Vt(e - a)), Z(Wt(t + o)), Z(Vt(e - r)), Z(Wt(t)), Z(Vt(e - r)), \"c\"].join(\" \")), tt([Z(Wt(t - o)), Z(Vt(e - r)), Z(Wt(t - n)), Z(Vt(e - a)), Z(Wt(t - n)), Z(Vt(e)), \"c\"].join(\" \")), tt([Z(Wt(t - n)), Z(Vt(e + a)), Z(Wt(t - o)), Z(Vt(e + r)), Z(Wt(t)), Z(Vt(e + r)), \"c\"].join(\" \")), tt([Z(Wt(t + o)), Z(Vt(e + r)), Z(Wt(t + n)), Z(Vt(e + a)), Z(Wt(t + n)), Z(Vt(e)), \"c\"].join(\" \")), null !== i && tt(qt(i)), this;\n        }, l.__private__.circle = l.circle = function (t, e, n, r) {\n          if (isNaN(t) || isNaN(e) || isNaN(n) || !Ot(r)) throw new Error(\"Invalid arguments passed to jsPDF.circle\");\n          return this.ellipse(t, e, n, n, r);\n        };\n        l.setFont = function (t, e) {\n          return $ = Lt(t, e, {\n            disableWarning: !1\n          }), this;\n        }, l.setFontStyle = l.setFontType = function (t) {\n          return $ = Lt(void 0, t), this;\n        };\n        l.__private__.getFontList = l.getFontList = function () {\n          var t,\n            e,\n            n,\n            r = {};\n          for (t in H) if (H.hasOwnProperty(t)) for (e in r[t] = n = [], H[t]) H[t].hasOwnProperty(e) && n.push(e);\n          return r;\n        };\n        l.addFont = function (t, e, n, r) {\n          yt.call(this, t, e, n, r = r || \"Identity-H\");\n        };\n        var Tt,\n          Rt = r.lineWidth || .200025,\n          Dt = l.__private__.setLineWidth = l.setLineWidth = function (t) {\n            return tt((t * _).toFixed(2) + \" w\"), this;\n          },\n          Ut = (l.__private__.setLineDash = ae.API.setLineDash = function (t, e) {\n            if (t = t || [], e = e || 0, isNaN(e) || !Array.isArray(t)) throw new Error(\"Invalid arguments passed to jsPDF.setLineDash\");\n            return t = t.map(function (t) {\n              return (t * _).toFixed(3);\n            }).join(\" \"), e = parseFloat((e * _).toFixed(3)), tt(\"[\" + t + \"] \" + e + \" d\"), this;\n          }, l.__private__.getLineHeight = l.getLineHeight = function () {\n            return et * Tt;\n          }),\n          zt = (Ut = l.__private__.getLineHeight = l.getLineHeight = function () {\n            return et * Tt;\n          }, l.__private__.setLineHeightFactor = l.setLineHeightFactor = function (t) {\n            return \"number\" == typeof (t = t || 1.15) && (Tt = t), this;\n          }),\n          Ht = l.__private__.getLineHeightFactor = l.getLineHeightFactor = function () {\n            return Tt;\n          };\n        zt(r.lineHeight);\n        var Wt = l.__private__.getHorizontalCoordinate = function (t) {\n            return t * _;\n          },\n          Vt = l.__private__.getVerticalCoordinate = function (t) {\n            return V[x].mediaBox.topRightY - V[x].mediaBox.bottomLeftY - t * _;\n          },\n          Gt = l.__private__.getHorizontalCoordinateString = function (t) {\n            return Z(t * _);\n          },\n          Yt = l.__private__.getVerticalCoordinateString = function (t) {\n            return Z(V[x].mediaBox.topRightY - V[x].mediaBox.bottomLeftY - t * _);\n          },\n          Jt = r.strokeColor || \"0 G\",\n          Xt = (l.__private__.getStrokeColor = l.getDrawColor = function () {\n            return ht(Jt);\n          }, l.__private__.setStrokeColor = l.setDrawColor = function (t, e, n, r) {\n            return Jt = ut({\n              ch1: t,\n              ch2: e,\n              ch3: n,\n              ch4: r,\n              pdfColorType: \"draw\",\n              precision: 2\n            }), tt(Jt), this;\n          }, r.fillColor || \"0 g\"),\n          Kt = (l.__private__.getFillColor = l.getFillColor = function () {\n            return ht(Xt);\n          }, l.__private__.setFillColor = l.setFillColor = function (t, e, n, r) {\n            return Xt = ut({\n              ch1: t,\n              ch2: e,\n              ch3: n,\n              ch4: r,\n              pdfColorType: \"fill\",\n              precision: 2\n            }), tt(Xt), this;\n          }, r.textColor || \"0 g\"),\n          Zt = l.__private__.getTextColor = l.getTextColor = function () {\n            return ht(Kt);\n          },\n          Qt = (l.__private__.setTextColor = l.setTextColor = function (t, e, n, r) {\n            return Kt = ut({\n              ch1: t,\n              ch2: e,\n              ch3: n,\n              ch4: r,\n              pdfColorType: \"text\",\n              precision: 3\n            }), this;\n          }, r.charSpace || 0),\n          $t = l.__private__.getCharSpace = l.getCharSpace = function () {\n            return Qt;\n          },\n          te = (l.__private__.setCharSpace = l.setCharSpace = function (t) {\n            if (isNaN(t)) throw new Error(\"Invalid argument passed to jsPDF.setCharSpace\");\n            return Qt = t, this;\n          }, 0);\n        l.CapJoinStyles = {\n          0: 0,\n          butt: 0,\n          but: 0,\n          miter: 0,\n          1: 1,\n          round: 1,\n          rounded: 1,\n          circle: 1,\n          2: 2,\n          projecting: 2,\n          project: 2,\n          square: 2,\n          bevel: 2\n        };\n        l.__private__.setLineCap = l.setLineCap = function (t) {\n          var e = l.CapJoinStyles[t];\n          if (void 0 === e) throw new Error(\"Line cap style of '\" + t + \"' is not recognized. See or extend .CapJoinStyles property for valid styles\");\n          return tt((te = e) + \" J\"), this;\n        };\n        var ee,\n          ne = 0;\n        l.__private__.setLineJoin = l.setLineJoin = function (t) {\n          var e = l.CapJoinStyles[t];\n          if (void 0 === e) throw new Error(\"Line join style of '\" + t + \"' is not recognized. See or extend .CapJoinStyles property for valid styles\");\n          return tt((ne = e) + \" j\"), this;\n        }, l.__private__.setMiterLimit = l.setMiterLimit = function (t) {\n          if (t = t || 0, isNaN(t)) throw new Error(\"Invalid argument passed to jsPDF.setMiterLimit\");\n          return ee = parseFloat(Z(t * _)), tt(ee + \" M\"), this;\n        };\n        for (var re in l.save = function (r, t) {\n          if (r = r || \"generated.pdf\", (t = t || {}).returnPromise = t.returnPromise || !1, !1 !== t.returnPromise) return new Promise(function (t, e) {\n            try {\n              var n = le(It(kt()), r);\n              \"function\" == typeof le.unload && ie.setTimeout && setTimeout(le.unload, 911), t(n);\n            } catch (t) {\n              e(t.message);\n            }\n          });\n          le(It(kt()), r), \"function\" == typeof le.unload && ie.setTimeout && setTimeout(le.unload, 911);\n        }, ae.API) ae.API.hasOwnProperty(re) && (\"events\" === re && ae.API.events.length ? function (t, e) {\n          var n, r, i;\n          for (i = e.length - 1; -1 !== i; i--) n = e[i][0], r = e[i][1], t.subscribe.apply(t, [n].concat(\"function\" == typeof r ? [r] : r));\n        }(it, ae.API.events) : l[re] = ae.API[re]);\n        return l.internal = {\n          pdfEscape: vt,\n          getStyle: qt,\n          getFont: function () {\n            return rt[Lt.apply(l, arguments)];\n          },\n          getFontSize: O,\n          getCharSpace: $t,\n          getTextColor: Zt,\n          getLineHeight: Ut,\n          getLineHeightFactor: Ht,\n          write: j,\n          getHorizontalCoordinate: Wt,\n          getVerticalCoordinate: Vt,\n          getCoordinateString: Gt,\n          getVerticalCoordinateString: Yt,\n          collections: {},\n          newObject: J,\n          newAdditionalObject: at,\n          newObjectDeferred: X,\n          newObjectDeferredBegin: ot,\n          getFilters: ct,\n          putStream: ft,\n          events: it,\n          scaleFactor: _,\n          pageSize: {\n            getWidth: function () {\n              return (V[x].mediaBox.topRightX - V[x].mediaBox.bottomLeftX) / _;\n            },\n            setWidth: function (t) {\n              V[x].mediaBox.topRightX = t * _ + V[x].mediaBox.bottomLeftX;\n            },\n            getHeight: function () {\n              return (V[x].mediaBox.topRightY - V[x].mediaBox.bottomLeftY) / _;\n            },\n            setHeight: function (t) {\n              V[x].mediaBox.topRightY = t * _ + V[x].mediaBox.bottomLeftY;\n            }\n          },\n          output: Ct,\n          getNumberOfPages: Nt,\n          pages: I,\n          out: tt,\n          f2: Z,\n          f3: Q,\n          getPageInfo: jt,\n          getPageInfoByObjId: Et,\n          getCurrentPageInfo: Mt,\n          getPDFVersion: u,\n          hasHotfix: Bt\n        }, Object.defineProperty(l.internal.pageSize, \"width\", {\n          get: function () {\n            return (V[x].mediaBox.topRightX - V[x].mediaBox.bottomLeftX) / _;\n          },\n          set: function (t) {\n            V[x].mediaBox.topRightX = t * _ + V[x].mediaBox.bottomLeftX;\n          },\n          enumerable: !0,\n          configurable: !0\n        }), Object.defineProperty(l.internal.pageSize, \"height\", {\n          get: function () {\n            return (V[x].mediaBox.topRightY - V[x].mediaBox.bottomLeftY) / _;\n          },\n          set: function (t) {\n            V[x].mediaBox.topRightY = t * _ + V[x].mediaBox.bottomLeftY;\n          },\n          enumerable: !0,\n          configurable: !0\n        }), function (t) {\n          for (var e = 0, n = M.length; e < n; e++) {\n            var r = yt(t[e][0], t[e][1], t[e][2], M[e][3], !0);\n            K[r] = !0;\n            var i = t[e][0].split(\"-\");\n            mt(r, i[0], i[1] || \"\");\n          }\n          it.publish(\"addFonts\", {\n            fonts: rt,\n            dictionary: H\n          });\n        }(M), $ = \"F1\", bt(i, t), it.publish(\"initialized\"), l;\n      }\n      return ae.API = {\n        events: []\n      }, ae.version = \"1.5.3\", \"function\" == typeof define && define.amd ? define(\"jsPDF\", function () {\n        return ae;\n      }) : \"undefined\" != typeof module && module.exports ? (module.exports = ae, module.exports.jsPDF = ae) : ie.jsPDF = ae, ae;\n    }(\"undefined\" != typeof self && self || \"undefined\" != typeof window && window || \"undefined\" != typeof global && global || Function('return typeof this === \"object\" && this.content')() || Function(\"return this\")());\n  /**\n     * @license\n     * Copyright (c) 2016 Alexander Weidt,\n     * https://github.com/BiggA94\n     * \n     * Licensed under the MIT License. http://opensource.org/licenses/mit-license\n     */\n  (function (t, e) {\n    var A,\n      n = 1,\n      S = function (t) {\n        return t.replace(/\\\\/g, \"\\\\\\\\\").replace(/\\(/g, \"\\\\(\").replace(/\\)/g, \"\\\\)\");\n      },\n      y = function (t) {\n        return t.replace(/\\\\\\\\/g, \"\\\\\").replace(/\\\\\\(/g, \"(\").replace(/\\\\\\)/g, \")\");\n      },\n      _ = function (t) {\n        if (isNaN(t)) throw new Error(\"Invalid argument passed to jsPDF.f2\");\n        return t.toFixed(2);\n      },\n      s = function (t) {\n        if (isNaN(t)) throw new Error(\"Invalid argument passed to jsPDF.f2\");\n        return t.toFixed(5);\n      };\n    t.__acroform__ = {};\n    var r = function (t, e) {\n        t.prototype = Object.create(e.prototype), t.prototype.constructor = t;\n      },\n      v = function (t) {\n        return t * n;\n      },\n      w = function (t) {\n        return t / n;\n      },\n      l = function (t) {\n        var e = new j(),\n          n = Y.internal.getHeight(t) || 0,\n          r = Y.internal.getWidth(t) || 0;\n        return e.BBox = [0, 0, Number(_(r)), Number(_(n))], e;\n      },\n      i = t.__acroform__.setBit = function (t, e) {\n        if (t = t || 0, e = e || 0, isNaN(t) || isNaN(e)) throw new Error(\"Invalid arguments passed to jsPDF.API.__acroform__.setBit\");\n        return t |= 1 << e;\n      },\n      o = t.__acroform__.clearBit = function (t, e) {\n        if (t = t || 0, e = e || 0, isNaN(t) || isNaN(e)) throw new Error(\"Invalid arguments passed to jsPDF.API.__acroform__.clearBit\");\n        return t &= ~(1 << e);\n      },\n      a = t.__acroform__.getBit = function (t, e) {\n        if (isNaN(t) || isNaN(e)) throw new Error(\"Invalid arguments passed to jsPDF.API.__acroform__.getBit\");\n        return 0 == (t & 1 << e) ? 0 : 1;\n      },\n      b = t.__acroform__.getBitForPdf = function (t, e) {\n        if (isNaN(t) || isNaN(e)) throw new Error(\"Invalid arguments passed to jsPDF.API.__acroform__.getBitForPdf\");\n        return a(t, e - 1);\n      },\n      x = t.__acroform__.setBitForPdf = function (t, e) {\n        if (isNaN(t) || isNaN(e)) throw new Error(\"Invalid arguments passed to jsPDF.API.__acroform__.setBitForPdf\");\n        return i(t, e - 1);\n      },\n      N = t.__acroform__.clearBitForPdf = function (t, e, n) {\n        if (isNaN(t) || isNaN(e)) throw new Error(\"Invalid arguments passed to jsPDF.API.__acroform__.clearBitForPdf\");\n        return o(t, e - 1);\n      },\n      c = t.__acroform__.calculateCoordinates = function (t) {\n        var e = this.internal.getHorizontalCoordinate,\n          n = this.internal.getVerticalCoordinate,\n          r = t[0],\n          i = t[1],\n          o = t[2],\n          a = t[3],\n          s = {};\n        return s.lowerLeft_X = e(r) || 0, s.lowerLeft_Y = n(i + a) || 0, s.upperRight_X = e(r + o) || 0, s.upperRight_Y = n(i) || 0, [Number(_(s.lowerLeft_X)), Number(_(s.lowerLeft_Y)), Number(_(s.upperRight_X)), Number(_(s.upperRight_Y))];\n      },\n      f = function (t) {\n        if (t.appearanceStreamContent) return t.appearanceStreamContent;\n        if (t.V || t.DV) {\n          var e = [],\n            n = t.V || t.DV,\n            r = h(t, n),\n            i = A.internal.getFont(t.fontName, t.fontStyle).id;\n          e.push(\"/Tx BMC\"), e.push(\"q\"), e.push(\"BT\"), e.push(A.__private__.encodeColorString(t.color)), e.push(\"/\" + i + \" \" + _(r.fontSize) + \" Tf\"), e.push(\"1 0 0 1 0 0 Tm\"), e.push(r.text), e.push(\"ET\"), e.push(\"Q\"), e.push(\"EMC\");\n          var o = new l(t);\n          return o.stream = e.join(\"\\n\"), o;\n        }\n      },\n      h = function (i, t) {\n        var e = i.maxFontSize || 12,\n          n = (i.fontName, {\n            text: \"\",\n            fontSize: \"\"\n          }),\n          o = (t = \")\" == (t = \"(\" == t.substr(0, 1) ? t.substr(1) : t).substr(t.length - 1) ? t.substr(0, t.length - 1) : t).split(\" \"),\n          r = (A.__private__.encodeColorString(i.color), e),\n          a = Y.internal.getHeight(i) || 0;\n        a = a < 0 ? -a : a;\n        var s = Y.internal.getWidth(i) || 0;\n        s = s < 0 ? -s : s;\n        var l = function (t, e, n) {\n          if (t + 1 < o.length) {\n            var r = e + \" \" + o[t + 1];\n            return F(r, i, n).width <= s - 4;\n          }\n          return !1;\n        };\n        r++;\n        t: for (;;) {\n          t = \"\";\n          var h = F(\"3\", i, --r).height,\n            u = i.multiline ? a - r : (a - h) / 2,\n            c = -2,\n            f = u += 2,\n            p = 0,\n            d = 0,\n            g = 0;\n          if (r <= 0) {\n            t = \"(...) Tj\\n\", t += \"% Width of Text: \" + F(t, i, r = 12).width + \", FieldWidth:\" + s + \"\\n\";\n            break;\n          }\n          g = F(o[0] + \" \", i, r).width;\n          var m = \"\",\n            y = 0;\n          for (var v in o) if (o.hasOwnProperty(v)) {\n            m = \" \" == (m += o[v] + \" \").substr(m.length - 1) ? m.substr(0, m.length - 1) : m;\n            var w = parseInt(v);\n            g = F(m + \" \", i, r).width;\n            var b = l(w, m, r),\n              x = v >= o.length - 1;\n            if (b && !x) {\n              m += \" \";\n              continue;\n            }\n            if (b || x) {\n              if (x) d = w;else if (i.multiline && a < (h + 2) * (y + 2) + 2) continue t;\n            } else {\n              if (!i.multiline) continue t;\n              if (a < (h + 2) * (y + 2) + 2) continue t;\n              d = w;\n            }\n            for (var N = \"\", L = p; L <= d; L++) N += o[L] + \" \";\n            switch (N = \" \" == N.substr(N.length - 1) ? N.substr(0, N.length - 1) : N, g = F(N, i, r).width, i.textAlign) {\n              case \"right\":\n                c = s - g - 2;\n                break;\n              case \"center\":\n                c = (s - g) / 2;\n                break;\n              case \"left\":\n              default:\n                c = 2;\n            }\n            t += _(c) + \" \" + _(f) + \" Td\\n\", t += \"(\" + S(N) + \") Tj\\n\", t += -_(c) + \" 0 Td\\n\", f = -(r + 2), g = 0, p = d + 1, y++, m = \"\";\n          } else ;\n          break;\n        }\n        return n.text = t, n.fontSize = r, n;\n      },\n      F = function (t, e, n) {\n        var r = A.internal.getFont(e.fontName, e.fontStyle),\n          i = A.getStringUnitWidth(t, {\n            font: r,\n            fontSize: parseFloat(n),\n            charSpace: 0\n          }) * parseFloat(n);\n        return {\n          height: A.getStringUnitWidth(\"3\", {\n            font: r,\n            fontSize: parseFloat(n),\n            charSpace: 0\n          }) * parseFloat(n) * 1.5,\n          width: i\n        };\n      },\n      u = {\n        fields: [],\n        xForms: [],\n        acroFormDictionaryRoot: null,\n        printedOut: !1,\n        internal: null,\n        isInitialized: !1\n      },\n      p = function () {\n        A.internal.acroformPlugin.acroFormDictionaryRoot.objId = void 0;\n        var t = A.internal.acroformPlugin.acroFormDictionaryRoot.Fields;\n        for (var e in t) if (t.hasOwnProperty(e)) {\n          var n = t[e];\n          n.objId = void 0, n.hasAnnotation && d.call(A, n);\n        }\n      },\n      d = function (t) {\n        var e = {\n          type: \"reference\",\n          object: t\n        };\n        void 0 === A.internal.getPageInfo(t.page).pageContext.annotations.find(function (t) {\n          return t.type === e.type && t.object === e.object;\n        }) && A.internal.getPageInfo(t.page).pageContext.annotations.push(e);\n      },\n      g = function () {\n        if (void 0 === A.internal.acroformPlugin.acroFormDictionaryRoot) throw new Error(\"putCatalogCallback: Root missing.\");\n        A.internal.write(\"/AcroForm \" + A.internal.acroformPlugin.acroFormDictionaryRoot.objId + \" 0 R\");\n      },\n      m = function () {\n        A.internal.events.unsubscribe(A.internal.acroformPlugin.acroFormDictionaryRoot._eventID), delete A.internal.acroformPlugin.acroFormDictionaryRoot._eventID, A.internal.acroformPlugin.printedOut = !0;\n      },\n      L = function (t) {\n        var e = !t;\n        t || (A.internal.newObjectDeferredBegin(A.internal.acroformPlugin.acroFormDictionaryRoot.objId, !0), A.internal.acroformPlugin.acroFormDictionaryRoot.putStream());\n        t = t || A.internal.acroformPlugin.acroFormDictionaryRoot.Kids;\n        for (var n in t) if (t.hasOwnProperty(n)) {\n          var r = t[n],\n            i = [],\n            o = r.Rect;\n          if (r.Rect && (r.Rect = c.call(this, r.Rect)), A.internal.newObjectDeferredBegin(r.objId, !0), r.DA = Y.createDefaultAppearanceStream(r), \"object\" === se(r) && \"function\" == typeof r.getKeyValueListForStream && (i = r.getKeyValueListForStream()), r.Rect = o, r.hasAppearanceStream && !r.appearanceStreamContent) {\n            var a = f.call(this, r);\n            i.push({\n              key: \"AP\",\n              value: \"<</N \" + a + \">>\"\n            }), A.internal.acroformPlugin.xForms.push(a);\n          }\n          if (r.appearanceStreamContent) {\n            var s = \"\";\n            for (var l in r.appearanceStreamContent) if (r.appearanceStreamContent.hasOwnProperty(l)) {\n              var h = r.appearanceStreamContent[l];\n              if (s += \"/\" + l + \" \", s += \"<<\", 1 <= Object.keys(h).length || Array.isArray(h)) for (var n in h) {\n                var u;\n                if (h.hasOwnProperty(n)) \"function\" == typeof (u = h[n]) && (u = u.call(this, r)), s += \"/\" + n + \" \" + u + \" \", 0 <= A.internal.acroformPlugin.xForms.indexOf(u) || A.internal.acroformPlugin.xForms.push(u);\n              } else \"function\" == typeof (u = h) && (u = u.call(this, r)), s += \"/\" + n + \" \" + u, 0 <= A.internal.acroformPlugin.xForms.indexOf(u) || A.internal.acroformPlugin.xForms.push(u);\n              s += \">>\";\n            }\n            i.push({\n              key: \"AP\",\n              value: \"<<\\n\" + s + \">>\"\n            });\n          }\n          A.internal.putStream({\n            additionalKeyValues: i\n          }), A.internal.out(\"endobj\");\n        }\n        e && P.call(this, A.internal.acroformPlugin.xForms);\n      },\n      P = function (t) {\n        for (var e in t) if (t.hasOwnProperty(e)) {\n          var n = e,\n            r = t[e];\n          A.internal.newObjectDeferredBegin(r && r.objId, !0), \"object\" === se(r) && \"function\" == typeof r.putStream && r.putStream(), delete t[n];\n        }\n      },\n      k = function () {\n        if (void 0 !== this.internal && (void 0 === this.internal.acroformPlugin || !1 === this.internal.acroformPlugin.isInitialized)) {\n          if (A = this, M.FieldNum = 0, this.internal.acroformPlugin = JSON.parse(JSON.stringify(u)), this.internal.acroformPlugin.acroFormDictionaryRoot) throw new Error(\"Exception while creating AcroformDictionary\");\n          n = A.internal.scaleFactor, A.internal.acroformPlugin.acroFormDictionaryRoot = new E(), A.internal.acroformPlugin.acroFormDictionaryRoot._eventID = A.internal.events.subscribe(\"postPutResources\", m), A.internal.events.subscribe(\"buildDocument\", p), A.internal.events.subscribe(\"putCatalog\", g), A.internal.events.subscribe(\"postPutPages\", L), A.internal.acroformPlugin.isInitialized = !0;\n        }\n      },\n      I = t.__acroform__.arrayToPdfArray = function (t) {\n        if (Array.isArray(t)) {\n          for (var e = \"[\", n = 0; n < t.length; n++) switch (0 !== n && (e += \" \"), se(t[n])) {\n            case \"boolean\":\n            case \"number\":\n            case \"object\":\n              e += t[n].toString();\n              break;\n            case \"string\":\n              \"/\" !== t[n].substr(0, 1) ? e += \"(\" + S(t[n].toString()) + \")\" : e += t[n].toString();\n          }\n          return e += \"]\";\n        }\n        throw new Error(\"Invalid argument passed to jsPDF.__acroform__.arrayToPdfArray\");\n      };\n    var C = function (t) {\n        return (t = t || \"\").toString(), t = \"(\" + S(t) + \")\";\n      },\n      B = function () {\n        var e;\n        Object.defineProperty(this, \"objId\", {\n          configurable: !0,\n          get: function () {\n            if (e || (e = A.internal.newObjectDeferred()), !e) throw new Error(\"AcroFormPDFObject: Couldn't create Object ID\");\n            return e;\n          },\n          set: function (t) {\n            e = t;\n          }\n        });\n      };\n    B.prototype.toString = function () {\n      return this.objId + \" 0 R\";\n    }, B.prototype.putStream = function () {\n      var t = this.getKeyValueListForStream();\n      A.internal.putStream({\n        data: this.stream,\n        additionalKeyValues: t\n      }), A.internal.out(\"endobj\");\n    }, B.prototype.getKeyValueListForStream = function () {\n      return function (t) {\n        var e = [],\n          n = Object.getOwnPropertyNames(t).filter(function (t) {\n            return \"content\" != t && \"appearanceStreamContent\" != t && \"_\" != t.substring(0, 1);\n          });\n        for (var r in n) if (!1 === Object.getOwnPropertyDescriptor(t, n[r]).configurable) {\n          var i = n[r],\n            o = t[i];\n          o && (Array.isArray(o) ? e.push({\n            key: i,\n            value: I(o)\n          }) : o instanceof B ? e.push({\n            key: i,\n            value: o.objId + \" 0 R\"\n          }) : \"function\" != typeof o && e.push({\n            key: i,\n            value: o\n          }));\n        }\n        return e;\n      }(this);\n    };\n    var j = function () {\n      B.call(this), Object.defineProperty(this, \"Type\", {\n        value: \"/XObject\",\n        configurable: !1,\n        writeable: !0\n      }), Object.defineProperty(this, \"Subtype\", {\n        value: \"/Form\",\n        configurable: !1,\n        writeable: !0\n      }), Object.defineProperty(this, \"FormType\", {\n        value: 1,\n        configurable: !1,\n        writeable: !0\n      });\n      var e,\n        n = [];\n      Object.defineProperty(this, \"BBox\", {\n        configurable: !1,\n        writeable: !0,\n        get: function () {\n          return n;\n        },\n        set: function (t) {\n          n = t;\n        }\n      }), Object.defineProperty(this, \"Resources\", {\n        value: \"2 0 R\",\n        configurable: !1,\n        writeable: !0\n      }), Object.defineProperty(this, \"stream\", {\n        enumerable: !1,\n        configurable: !0,\n        set: function (t) {\n          e = t.trim();\n        },\n        get: function () {\n          return e || null;\n        }\n      });\n    };\n    r(j, B);\n    var E = function () {\n      B.call(this);\n      var e,\n        t = [];\n      Object.defineProperty(this, \"Kids\", {\n        enumerable: !1,\n        configurable: !0,\n        get: function () {\n          return 0 < t.length ? t : void 0;\n        }\n      }), Object.defineProperty(this, \"Fields\", {\n        enumerable: !1,\n        configurable: !1,\n        get: function () {\n          return t;\n        }\n      }), Object.defineProperty(this, \"DA\", {\n        enumerable: !1,\n        configurable: !1,\n        get: function () {\n          if (e) return \"(\" + e + \")\";\n        },\n        set: function (t) {\n          e = t;\n        }\n      });\n    };\n    r(E, B);\n    var M = function t() {\n      B.call(this);\n      var e = 4;\n      Object.defineProperty(this, \"F\", {\n        enumerable: !1,\n        configurable: !1,\n        get: function () {\n          return e;\n        },\n        set: function (t) {\n          if (isNaN(t)) throw new Error('Invalid value \"' + t + '\" for attribute F supplied.');\n          e = t;\n        }\n      }), Object.defineProperty(this, \"showWhenPrinted\", {\n        enumerable: !0,\n        configurable: !0,\n        get: function () {\n          return Boolean(b(e, 3));\n        },\n        set: function (t) {\n          !0 === Boolean(t) ? this.F = x(e, 3) : this.F = N(e, 3);\n        }\n      });\n      var n = 0;\n      Object.defineProperty(this, \"Ff\", {\n        enumerable: !1,\n        configurable: !1,\n        get: function () {\n          return n;\n        },\n        set: function (t) {\n          if (isNaN(t)) throw new Error('Invalid value \"' + t + '\" for attribute Ff supplied.');\n          n = t;\n        }\n      });\n      var r = [];\n      Object.defineProperty(this, \"Rect\", {\n        enumerable: !1,\n        configurable: !1,\n        get: function () {\n          if (0 !== r.length) return r;\n        },\n        set: function (t) {\n          r = void 0 !== t ? t : [];\n        }\n      }), Object.defineProperty(this, \"x\", {\n        enumerable: !0,\n        configurable: !0,\n        get: function () {\n          return !r || isNaN(r[0]) ? 0 : w(r[0]);\n        },\n        set: function (t) {\n          r[0] = v(t);\n        }\n      }), Object.defineProperty(this, \"y\", {\n        enumerable: !0,\n        configurable: !0,\n        get: function () {\n          return !r || isNaN(r[1]) ? 0 : w(r[1]);\n        },\n        set: function (t) {\n          r[1] = v(t);\n        }\n      }), Object.defineProperty(this, \"width\", {\n        enumerable: !0,\n        configurable: !0,\n        get: function () {\n          return !r || isNaN(r[2]) ? 0 : w(r[2]);\n        },\n        set: function (t) {\n          r[2] = v(t);\n        }\n      }), Object.defineProperty(this, \"height\", {\n        enumerable: !0,\n        configurable: !0,\n        get: function () {\n          return !r || isNaN(r[3]) ? 0 : w(r[3]);\n        },\n        set: function (t) {\n          r[3] = v(t);\n        }\n      });\n      var i = \"\";\n      Object.defineProperty(this, \"FT\", {\n        enumerable: !0,\n        configurable: !1,\n        get: function () {\n          return i;\n        },\n        set: function (t) {\n          switch (t) {\n            case \"/Btn\":\n            case \"/Tx\":\n            case \"/Ch\":\n            case \"/Sig\":\n              i = t;\n              break;\n            default:\n              throw new Error('Invalid value \"' + t + '\" for attribute FT supplied.');\n          }\n        }\n      });\n      var o = null;\n      Object.defineProperty(this, \"T\", {\n        enumerable: !0,\n        configurable: !1,\n        get: function () {\n          if (!o || o.length < 1) {\n            if (this instanceof H) return;\n            o = \"FieldObject\" + t.FieldNum++;\n          }\n          return \"(\" + S(o) + \")\";\n        },\n        set: function (t) {\n          o = t.toString();\n        }\n      }), Object.defineProperty(this, \"fieldName\", {\n        configurable: !0,\n        enumerable: !0,\n        get: function () {\n          return o;\n        },\n        set: function (t) {\n          o = t;\n        }\n      });\n      var a = \"helvetica\";\n      Object.defineProperty(this, \"fontName\", {\n        enumerable: !0,\n        configurable: !0,\n        get: function () {\n          return a;\n        },\n        set: function (t) {\n          a = t;\n        }\n      });\n      var s = \"normal\";\n      Object.defineProperty(this, \"fontStyle\", {\n        enumerable: !0,\n        configurable: !0,\n        get: function () {\n          return s;\n        },\n        set: function (t) {\n          s = t;\n        }\n      });\n      var l = 0;\n      Object.defineProperty(this, \"fontSize\", {\n        enumerable: !0,\n        configurable: !0,\n        get: function () {\n          return w(l);\n        },\n        set: function (t) {\n          l = v(t);\n        }\n      });\n      var h = 50;\n      Object.defineProperty(this, \"maxFontSize\", {\n        enumerable: !0,\n        configurable: !0,\n        get: function () {\n          return w(h);\n        },\n        set: function (t) {\n          h = v(t);\n        }\n      });\n      var u = \"black\";\n      Object.defineProperty(this, \"color\", {\n        enumerable: !0,\n        configurable: !0,\n        get: function () {\n          return u;\n        },\n        set: function (t) {\n          u = t;\n        }\n      });\n      var c = \"/F1 0 Tf 0 g\";\n      Object.defineProperty(this, \"DA\", {\n        enumerable: !0,\n        configurable: !1,\n        get: function () {\n          if (!(!c || this instanceof H || this instanceof V)) return C(c);\n        },\n        set: function (t) {\n          t = t.toString(), c = t;\n        }\n      });\n      var f = null;\n      Object.defineProperty(this, \"DV\", {\n        enumerable: !1,\n        configurable: !1,\n        get: function () {\n          if (f) return this instanceof D == !1 ? C(f) : f;\n        },\n        set: function (t) {\n          t = t.toString(), f = this instanceof D == !1 ? \"(\" === t.substr(0, 1) ? y(t.substr(1, t.length - 2)) : y(t) : t;\n        }\n      }), Object.defineProperty(this, \"defaultValue\", {\n        enumerable: !0,\n        configurable: !0,\n        get: function () {\n          return this instanceof D == !0 ? y(f.substr(1, f.length - 1)) : f;\n        },\n        set: function (t) {\n          t = t.toString(), f = this instanceof D == !0 ? \"/\" + t : t;\n        }\n      });\n      var p = null;\n      Object.defineProperty(this, \"V\", {\n        enumerable: !1,\n        configurable: !1,\n        get: function () {\n          if (p) return this instanceof D == !1 ? C(p) : p;\n        },\n        set: function (t) {\n          t = t.toString(), p = this instanceof D == !1 ? \"(\" === t.substr(0, 1) ? y(t.substr(1, t.length - 2)) : y(t) : t;\n        }\n      }), Object.defineProperty(this, \"value\", {\n        enumerable: !0,\n        configurable: !0,\n        get: function () {\n          return this instanceof D == !0 ? y(p.substr(1, p.length - 1)) : p;\n        },\n        set: function (t) {\n          t = t.toString(), p = this instanceof D == !0 ? \"/\" + t : t;\n        }\n      }), Object.defineProperty(this, \"hasAnnotation\", {\n        enumerable: !0,\n        configurable: !0,\n        get: function () {\n          return this.Rect;\n        }\n      }), Object.defineProperty(this, \"Type\", {\n        enumerable: !0,\n        configurable: !1,\n        get: function () {\n          return this.hasAnnotation ? \"/Annot\" : null;\n        }\n      }), Object.defineProperty(this, \"Subtype\", {\n        enumerable: !0,\n        configurable: !1,\n        get: function () {\n          return this.hasAnnotation ? \"/Widget\" : null;\n        }\n      });\n      var d,\n        g = !1;\n      Object.defineProperty(this, \"hasAppearanceStream\", {\n        enumerable: !0,\n        configurable: !0,\n        writeable: !0,\n        get: function () {\n          return g;\n        },\n        set: function (t) {\n          t = Boolean(t), g = t;\n        }\n      }), Object.defineProperty(this, \"page\", {\n        enumerable: !0,\n        configurable: !0,\n        writeable: !0,\n        get: function () {\n          if (d) return d;\n        },\n        set: function (t) {\n          d = t;\n        }\n      }), Object.defineProperty(this, \"readOnly\", {\n        enumerable: !0,\n        configurable: !0,\n        get: function () {\n          return Boolean(b(this.Ff, 1));\n        },\n        set: function (t) {\n          !0 === Boolean(t) ? this.Ff = x(this.Ff, 1) : this.Ff = N(this.Ff, 1);\n        }\n      }), Object.defineProperty(this, \"required\", {\n        enumerable: !0,\n        configurable: !0,\n        get: function () {\n          return Boolean(b(this.Ff, 2));\n        },\n        set: function (t) {\n          !0 === Boolean(t) ? this.Ff = x(this.Ff, 2) : this.Ff = N(this.Ff, 2);\n        }\n      }), Object.defineProperty(this, \"noExport\", {\n        enumerable: !0,\n        configurable: !0,\n        get: function () {\n          return Boolean(b(this.Ff, 3));\n        },\n        set: function (t) {\n          !0 === Boolean(t) ? this.Ff = x(this.Ff, 3) : this.Ff = N(this.Ff, 3);\n        }\n      });\n      var m = null;\n      Object.defineProperty(this, \"Q\", {\n        enumerable: !0,\n        configurable: !1,\n        get: function () {\n          if (null !== m) return m;\n        },\n        set: function (t) {\n          if (-1 === [0, 1, 2].indexOf(t)) throw new Error('Invalid value \"' + t + '\" for attribute Q supplied.');\n          m = t;\n        }\n      }), Object.defineProperty(this, \"textAlign\", {\n        get: function () {\n          var t = \"left\";\n          switch (m) {\n            case 0:\n            default:\n              t = \"left\";\n              break;\n            case 1:\n              t = \"center\";\n              break;\n            case 2:\n              t = \"right\";\n          }\n          return t;\n        },\n        configurable: !0,\n        enumerable: !0,\n        set: function (t) {\n          switch (t) {\n            case \"right\":\n            case 2:\n              m = 2;\n              break;\n            case \"center\":\n            case 1:\n              m = 1;\n              break;\n            case \"left\":\n            case 0:\n            default:\n              m = 0;\n          }\n        }\n      });\n    };\n    r(M, B);\n    var O = function () {\n      M.call(this), this.FT = \"/Ch\", this.V = \"()\", this.fontName = \"zapfdingbats\";\n      var e = 0;\n      Object.defineProperty(this, \"TI\", {\n        enumerable: !0,\n        configurable: !1,\n        get: function () {\n          return e;\n        },\n        set: function (t) {\n          e = t;\n        }\n      }), Object.defineProperty(this, \"topIndex\", {\n        enumerable: !0,\n        configurable: !0,\n        get: function () {\n          return e;\n        },\n        set: function (t) {\n          e = t;\n        }\n      });\n      var r = [];\n      Object.defineProperty(this, \"Opt\", {\n        enumerable: !0,\n        configurable: !1,\n        get: function () {\n          return I(r);\n        },\n        set: function (t) {\n          var e, n;\n          n = [], \"string\" == typeof (e = t) && (n = function (t, e, n) {\n            n || (n = 1);\n            for (var r, i = []; r = e.exec(t);) i.push(r[n]);\n            return i;\n          }(e, /\\((.*?)\\)/g)), r = n;\n        }\n      }), this.getOptions = function () {\n        return r;\n      }, this.setOptions = function (t) {\n        r = t, this.sort && r.sort();\n      }, this.addOption = function (t) {\n        t = (t = t || \"\").toString(), r.push(t), this.sort && r.sort();\n      }, this.removeOption = function (t, e) {\n        for (e = e || !1, t = (t = t || \"\").toString(); -1 !== r.indexOf(t) && (r.splice(r.indexOf(t), 1), !1 !== e););\n      }, Object.defineProperty(this, \"combo\", {\n        enumerable: !0,\n        configurable: !0,\n        get: function () {\n          return Boolean(b(this.Ff, 18));\n        },\n        set: function (t) {\n          !0 === Boolean(t) ? this.Ff = x(this.Ff, 18) : this.Ff = N(this.Ff, 18);\n        }\n      }), Object.defineProperty(this, \"edit\", {\n        enumerable: !0,\n        configurable: !0,\n        get: function () {\n          return Boolean(b(this.Ff, 19));\n        },\n        set: function (t) {\n          !0 === this.combo && (!0 === Boolean(t) ? this.Ff = x(this.Ff, 19) : this.Ff = N(this.Ff, 19));\n        }\n      }), Object.defineProperty(this, \"sort\", {\n        enumerable: !0,\n        configurable: !0,\n        get: function () {\n          return Boolean(b(this.Ff, 20));\n        },\n        set: function (t) {\n          !0 === Boolean(t) ? (this.Ff = x(this.Ff, 20), r.sort()) : this.Ff = N(this.Ff, 20);\n        }\n      }), Object.defineProperty(this, \"multiSelect\", {\n        enumerable: !0,\n        configurable: !0,\n        get: function () {\n          return Boolean(b(this.Ff, 22));\n        },\n        set: function (t) {\n          !0 === Boolean(t) ? this.Ff = x(this.Ff, 22) : this.Ff = N(this.Ff, 22);\n        }\n      }), Object.defineProperty(this, \"doNotSpellCheck\", {\n        enumerable: !0,\n        configurable: !0,\n        get: function () {\n          return Boolean(b(this.Ff, 23));\n        },\n        set: function (t) {\n          !0 === Boolean(t) ? this.Ff = x(this.Ff, 23) : this.Ff = N(this.Ff, 23);\n        }\n      }), Object.defineProperty(this, \"commitOnSelChange\", {\n        enumerable: !0,\n        configurable: !0,\n        get: function () {\n          return Boolean(b(this.Ff, 27));\n        },\n        set: function (t) {\n          !0 === Boolean(t) ? this.Ff = x(this.Ff, 27) : this.Ff = N(this.Ff, 27);\n        }\n      }), this.hasAppearanceStream = !1;\n    };\n    r(O, M);\n    var q = function () {\n      O.call(this), this.fontName = \"helvetica\", this.combo = !1;\n    };\n    r(q, O);\n    var T = function () {\n      q.call(this), this.combo = !0;\n    };\n    r(T, q);\n    var R = function () {\n      T.call(this), this.edit = !0;\n    };\n    r(R, T);\n    var D = function () {\n      M.call(this), this.FT = \"/Btn\", Object.defineProperty(this, \"noToggleToOff\", {\n        enumerable: !0,\n        configurable: !0,\n        get: function () {\n          return Boolean(b(this.Ff, 15));\n        },\n        set: function (t) {\n          !0 === Boolean(t) ? this.Ff = x(this.Ff, 15) : this.Ff = N(this.Ff, 15);\n        }\n      }), Object.defineProperty(this, \"radio\", {\n        enumerable: !0,\n        configurable: !0,\n        get: function () {\n          return Boolean(b(this.Ff, 16));\n        },\n        set: function (t) {\n          !0 === Boolean(t) ? this.Ff = x(this.Ff, 16) : this.Ff = N(this.Ff, 16);\n        }\n      }), Object.defineProperty(this, \"pushButton\", {\n        enumerable: !0,\n        configurable: !0,\n        get: function () {\n          return Boolean(b(this.Ff, 17));\n        },\n        set: function (t) {\n          !0 === Boolean(t) ? this.Ff = x(this.Ff, 17) : this.Ff = N(this.Ff, 17);\n        }\n      }), Object.defineProperty(this, \"radioIsUnison\", {\n        enumerable: !0,\n        configurable: !0,\n        get: function () {\n          return Boolean(b(this.Ff, 26));\n        },\n        set: function (t) {\n          !0 === Boolean(t) ? this.Ff = x(this.Ff, 26) : this.Ff = N(this.Ff, 26);\n        }\n      });\n      var e,\n        n = {};\n      Object.defineProperty(this, \"MK\", {\n        enumerable: !1,\n        configurable: !1,\n        get: function () {\n          if (0 !== Object.keys(n).length) {\n            var t,\n              e = [];\n            for (t in e.push(\"<<\"), n) e.push(\"/\" + t + \" (\" + n[t] + \")\");\n            return e.push(\">>\"), e.join(\"\\n\");\n          }\n        },\n        set: function (t) {\n          \"object\" === se(t) && (n = t);\n        }\n      }), Object.defineProperty(this, \"caption\", {\n        enumerable: !0,\n        configurable: !0,\n        get: function () {\n          return n.CA || \"\";\n        },\n        set: function (t) {\n          \"string\" == typeof t && (n.CA = t);\n        }\n      }), Object.defineProperty(this, \"AS\", {\n        enumerable: !1,\n        configurable: !1,\n        get: function () {\n          return e;\n        },\n        set: function (t) {\n          e = t;\n        }\n      }), Object.defineProperty(this, \"appearanceState\", {\n        enumerable: !0,\n        configurable: !0,\n        get: function () {\n          return e.substr(1, e.length - 1);\n        },\n        set: function (t) {\n          e = \"/\" + t;\n        }\n      });\n    };\n    r(D, M);\n    var U = function () {\n      D.call(this), this.pushButton = !0;\n    };\n    r(U, D);\n    var z = function () {\n      D.call(this), this.radio = !0, this.pushButton = !1;\n      var e = [];\n      Object.defineProperty(this, \"Kids\", {\n        enumerable: !0,\n        configurable: !1,\n        get: function () {\n          return e;\n        },\n        set: function (t) {\n          e = void 0 !== t ? t : [];\n        }\n      });\n    };\n    r(z, D);\n    var H = function () {\n      var e, n;\n      M.call(this), Object.defineProperty(this, \"Parent\", {\n        enumerable: !1,\n        configurable: !1,\n        get: function () {\n          return e;\n        },\n        set: function (t) {\n          e = t;\n        }\n      }), Object.defineProperty(this, \"optionName\", {\n        enumerable: !1,\n        configurable: !0,\n        get: function () {\n          return n;\n        },\n        set: function (t) {\n          n = t;\n        }\n      });\n      var r,\n        i = {};\n      Object.defineProperty(this, \"MK\", {\n        enumerable: !1,\n        configurable: !1,\n        get: function () {\n          var t,\n            e = [];\n          for (t in e.push(\"<<\"), i) e.push(\"/\" + t + \" (\" + i[t] + \")\");\n          return e.push(\">>\"), e.join(\"\\n\");\n        },\n        set: function (t) {\n          \"object\" === se(t) && (i = t);\n        }\n      }), Object.defineProperty(this, \"caption\", {\n        enumerable: !0,\n        configurable: !0,\n        get: function () {\n          return i.CA || \"\";\n        },\n        set: function (t) {\n          \"string\" == typeof t && (i.CA = t);\n        }\n      }), Object.defineProperty(this, \"AS\", {\n        enumerable: !1,\n        configurable: !1,\n        get: function () {\n          return r;\n        },\n        set: function (t) {\n          r = t;\n        }\n      }), Object.defineProperty(this, \"appearanceState\", {\n        enumerable: !0,\n        configurable: !0,\n        get: function () {\n          return r.substr(1, r.length - 1);\n        },\n        set: function (t) {\n          r = \"/\" + t;\n        }\n      }), this.optionName = name, this.caption = \"l\", this.appearanceState = \"Off\", this._AppearanceType = Y.RadioButton.Circle, this.appearanceStreamContent = this._AppearanceType.createAppearanceStream(name);\n    };\n    r(H, M), z.prototype.setAppearance = function (t) {\n      if (!(\"createAppearanceStream\" in t && \"getCA\" in t)) throw new Error(\"Couldn't assign Appearance to RadioButton. Appearance was Invalid!\");\n      for (var e in this.Kids) if (this.Kids.hasOwnProperty(e)) {\n        var n = this.Kids[e];\n        n.appearanceStreamContent = t.createAppearanceStream(n.optionName), n.caption = t.getCA();\n      }\n    }, z.prototype.createOption = function (t) {\n      this.Kids.length;\n      var e = new H();\n      return e.Parent = this, e.optionName = t, this.Kids.push(e), J.call(this, e), e;\n    };\n    var W = function () {\n      D.call(this), this.fontName = \"zapfdingbats\", this.caption = \"3\", this.appearanceState = \"On\", this.value = \"On\", this.textAlign = \"center\", this.appearanceStreamContent = Y.CheckBox.createAppearanceStream();\n    };\n    r(W, D);\n    var V = function () {\n      M.call(this), this.FT = \"/Tx\", Object.defineProperty(this, \"multiline\", {\n        enumerable: !0,\n        configurable: !0,\n        get: function () {\n          return Boolean(b(this.Ff, 13));\n        },\n        set: function (t) {\n          !0 === Boolean(t) ? this.Ff = x(this.Ff, 13) : this.Ff = N(this.Ff, 13);\n        }\n      }), Object.defineProperty(this, \"fileSelect\", {\n        enumerable: !0,\n        configurable: !0,\n        get: function () {\n          return Boolean(b(this.Ff, 21));\n        },\n        set: function (t) {\n          !0 === Boolean(t) ? this.Ff = x(this.Ff, 21) : this.Ff = N(this.Ff, 21);\n        }\n      }), Object.defineProperty(this, \"doNotSpellCheck\", {\n        enumerable: !0,\n        configurable: !0,\n        get: function () {\n          return Boolean(b(this.Ff, 23));\n        },\n        set: function (t) {\n          !0 === Boolean(t) ? this.Ff = x(this.Ff, 23) : this.Ff = N(this.Ff, 23);\n        }\n      }), Object.defineProperty(this, \"doNotScroll\", {\n        enumerable: !0,\n        configurable: !0,\n        get: function () {\n          return Boolean(b(this.Ff, 24));\n        },\n        set: function (t) {\n          !0 === Boolean(t) ? this.Ff = x(this.Ff, 24) : this.Ff = N(this.Ff, 24);\n        }\n      }), Object.defineProperty(this, \"comb\", {\n        enumerable: !0,\n        configurable: !0,\n        get: function () {\n          return Boolean(b(this.Ff, 25));\n        },\n        set: function (t) {\n          !0 === Boolean(t) ? this.Ff = x(this.Ff, 25) : this.Ff = N(this.Ff, 25);\n        }\n      }), Object.defineProperty(this, \"richText\", {\n        enumerable: !0,\n        configurable: !0,\n        get: function () {\n          return Boolean(b(this.Ff, 26));\n        },\n        set: function (t) {\n          !0 === Boolean(t) ? this.Ff = x(this.Ff, 26) : this.Ff = N(this.Ff, 26);\n        }\n      });\n      var e = null;\n      Object.defineProperty(this, \"MaxLen\", {\n        enumerable: !0,\n        configurable: !1,\n        get: function () {\n          return e;\n        },\n        set: function (t) {\n          e = t;\n        }\n      }), Object.defineProperty(this, \"maxLength\", {\n        enumerable: !0,\n        configurable: !0,\n        get: function () {\n          return e;\n        },\n        set: function (t) {\n          Number.isInteger(t) && (e = t);\n        }\n      }), Object.defineProperty(this, \"hasAppearanceStream\", {\n        enumerable: !0,\n        configurable: !0,\n        get: function () {\n          return this.V || this.DV;\n        }\n      });\n    };\n    r(V, M);\n    var G = function () {\n      V.call(this), Object.defineProperty(this, \"password\", {\n        enumerable: !0,\n        configurable: !0,\n        get: function () {\n          return Boolean(b(this.Ff, 14));\n        },\n        set: function (t) {\n          !0 === Boolean(t) ? this.Ff = x(this.Ff, 14) : this.Ff = N(this.Ff, 14);\n        }\n      }), this.password = !0;\n    };\n    r(G, V);\n    var Y = {\n      CheckBox: {\n        createAppearanceStream: function () {\n          return {\n            N: {\n              On: Y.CheckBox.YesNormal\n            },\n            D: {\n              On: Y.CheckBox.YesPushDown,\n              Off: Y.CheckBox.OffPushDown\n            }\n          };\n        },\n        YesPushDown: function (t) {\n          var e = l(t),\n            n = [],\n            r = A.internal.getFont(t.fontName, t.fontStyle).id,\n            i = A.__private__.encodeColorString(t.color),\n            o = h(t, t.caption);\n          return n.push(\"0.749023 g\"), n.push(\"0 0 \" + _(Y.internal.getWidth(t)) + \" \" + _(Y.internal.getHeight(t)) + \" re\"), n.push(\"f\"), n.push(\"BMC\"), n.push(\"q\"), n.push(\"0 0 1 rg\"), n.push(\"/\" + r + \" \" + _(o.fontSize) + \" Tf \" + i), n.push(\"BT\"), n.push(o.text), n.push(\"ET\"), n.push(\"Q\"), n.push(\"EMC\"), e.stream = n.join(\"\\n\"), e;\n        },\n        YesNormal: function (t) {\n          var e = l(t),\n            n = A.internal.getFont(t.fontName, t.fontStyle).id,\n            r = A.__private__.encodeColorString(t.color),\n            i = [],\n            o = Y.internal.getHeight(t),\n            a = Y.internal.getWidth(t),\n            s = h(t, t.caption);\n          return i.push(\"1 g\"), i.push(\"0 0 \" + _(a) + \" \" + _(o) + \" re\"), i.push(\"f\"), i.push(\"q\"), i.push(\"0 0 1 rg\"), i.push(\"0 0 \" + _(a - 1) + \" \" + _(o - 1) + \" re\"), i.push(\"W\"), i.push(\"n\"), i.push(\"0 g\"), i.push(\"BT\"), i.push(\"/\" + n + \" \" + _(s.fontSize) + \" Tf \" + r), i.push(s.text), i.push(\"ET\"), i.push(\"Q\"), e.stream = i.join(\"\\n\"), e;\n        },\n        OffPushDown: function (t) {\n          var e = l(t),\n            n = [];\n          return n.push(\"0.749023 g\"), n.push(\"0 0 \" + _(Y.internal.getWidth(t)) + \" \" + _(Y.internal.getHeight(t)) + \" re\"), n.push(\"f\"), e.stream = n.join(\"\\n\"), e;\n        }\n      },\n      RadioButton: {\n        Circle: {\n          createAppearanceStream: function (t) {\n            var e = {\n              D: {\n                Off: Y.RadioButton.Circle.OffPushDown\n              },\n              N: {}\n            };\n            return e.N[t] = Y.RadioButton.Circle.YesNormal, e.D[t] = Y.RadioButton.Circle.YesPushDown, e;\n          },\n          getCA: function () {\n            return \"l\";\n          },\n          YesNormal: function (t) {\n            var e = l(t),\n              n = [],\n              r = Y.internal.getWidth(t) <= Y.internal.getHeight(t) ? Y.internal.getWidth(t) / 4 : Y.internal.getHeight(t) / 4;\n            r = Number((.9 * r).toFixed(5));\n            var i = Y.internal.Bezier_C,\n              o = Number((r * i).toFixed(5));\n            return n.push(\"q\"), n.push(\"1 0 0 1 \" + s(Y.internal.getWidth(t) / 2) + \" \" + s(Y.internal.getHeight(t) / 2) + \" cm\"), n.push(r + \" 0 m\"), n.push(r + \" \" + o + \" \" + o + \" \" + r + \" 0 \" + r + \" c\"), n.push(\"-\" + o + \" \" + r + \" -\" + r + \" \" + o + \" -\" + r + \" 0 c\"), n.push(\"-\" + r + \" -\" + o + \" -\" + o + \" -\" + r + \" 0 -\" + r + \" c\"), n.push(o + \" -\" + r + \" \" + r + \" -\" + o + \" \" + r + \" 0 c\"), n.push(\"f\"), n.push(\"Q\"), e.stream = n.join(\"\\n\"), e;\n          },\n          YesPushDown: function (t) {\n            var e = l(t),\n              n = [],\n              r = Y.internal.getWidth(t) <= Y.internal.getHeight(t) ? Y.internal.getWidth(t) / 4 : Y.internal.getHeight(t) / 4,\n              i = (r = Number((.9 * r).toFixed(5)), Number((2 * r).toFixed(5))),\n              o = Number((i * Y.internal.Bezier_C).toFixed(5)),\n              a = Number((r * Y.internal.Bezier_C).toFixed(5));\n            return n.push(\"0.749023 g\"), n.push(\"q\"), n.push(\"1 0 0 1 \" + s(Y.internal.getWidth(t) / 2) + \" \" + s(Y.internal.getHeight(t) / 2) + \" cm\"), n.push(i + \" 0 m\"), n.push(i + \" \" + o + \" \" + o + \" \" + i + \" 0 \" + i + \" c\"), n.push(\"-\" + o + \" \" + i + \" -\" + i + \" \" + o + \" -\" + i + \" 0 c\"), n.push(\"-\" + i + \" -\" + o + \" -\" + o + \" -\" + i + \" 0 -\" + i + \" c\"), n.push(o + \" -\" + i + \" \" + i + \" -\" + o + \" \" + i + \" 0 c\"), n.push(\"f\"), n.push(\"Q\"), n.push(\"0 g\"), n.push(\"q\"), n.push(\"1 0 0 1 \" + s(Y.internal.getWidth(t) / 2) + \" \" + s(Y.internal.getHeight(t) / 2) + \" cm\"), n.push(r + \" 0 m\"), n.push(r + \" \" + a + \" \" + a + \" \" + r + \" 0 \" + r + \" c\"), n.push(\"-\" + a + \" \" + r + \" -\" + r + \" \" + a + \" -\" + r + \" 0 c\"), n.push(\"-\" + r + \" -\" + a + \" -\" + a + \" -\" + r + \" 0 -\" + r + \" c\"), n.push(a + \" -\" + r + \" \" + r + \" -\" + a + \" \" + r + \" 0 c\"), n.push(\"f\"), n.push(\"Q\"), e.stream = n.join(\"\\n\"), e;\n          },\n          OffPushDown: function (t) {\n            var e = l(t),\n              n = [],\n              r = Y.internal.getWidth(t) <= Y.internal.getHeight(t) ? Y.internal.getWidth(t) / 4 : Y.internal.getHeight(t) / 4,\n              i = (r = Number((.9 * r).toFixed(5)), Number((2 * r).toFixed(5))),\n              o = Number((i * Y.internal.Bezier_C).toFixed(5));\n            return n.push(\"0.749023 g\"), n.push(\"q\"), n.push(\"1 0 0 1 \" + s(Y.internal.getWidth(t) / 2) + \" \" + s(Y.internal.getHeight(t) / 2) + \" cm\"), n.push(i + \" 0 m\"), n.push(i + \" \" + o + \" \" + o + \" \" + i + \" 0 \" + i + \" c\"), n.push(\"-\" + o + \" \" + i + \" -\" + i + \" \" + o + \" -\" + i + \" 0 c\"), n.push(\"-\" + i + \" -\" + o + \" -\" + o + \" -\" + i + \" 0 -\" + i + \" c\"), n.push(o + \" -\" + i + \" \" + i + \" -\" + o + \" \" + i + \" 0 c\"), n.push(\"f\"), n.push(\"Q\"), e.stream = n.join(\"\\n\"), e;\n          }\n        },\n        Cross: {\n          createAppearanceStream: function (t) {\n            var e = {\n              D: {\n                Off: Y.RadioButton.Cross.OffPushDown\n              },\n              N: {}\n            };\n            return e.N[t] = Y.RadioButton.Cross.YesNormal, e.D[t] = Y.RadioButton.Cross.YesPushDown, e;\n          },\n          getCA: function () {\n            return \"8\";\n          },\n          YesNormal: function (t) {\n            var e = l(t),\n              n = [],\n              r = Y.internal.calculateCross(t);\n            return n.push(\"q\"), n.push(\"1 1 \" + _(Y.internal.getWidth(t) - 2) + \" \" + _(Y.internal.getHeight(t) - 2) + \" re\"), n.push(\"W\"), n.push(\"n\"), n.push(_(r.x1.x) + \" \" + _(r.x1.y) + \" m\"), n.push(_(r.x2.x) + \" \" + _(r.x2.y) + \" l\"), n.push(_(r.x4.x) + \" \" + _(r.x4.y) + \" m\"), n.push(_(r.x3.x) + \" \" + _(r.x3.y) + \" l\"), n.push(\"s\"), n.push(\"Q\"), e.stream = n.join(\"\\n\"), e;\n          },\n          YesPushDown: function (t) {\n            var e = l(t),\n              n = Y.internal.calculateCross(t),\n              r = [];\n            return r.push(\"0.749023 g\"), r.push(\"0 0 \" + _(Y.internal.getWidth(t)) + \" \" + _(Y.internal.getHeight(t)) + \" re\"), r.push(\"f\"), r.push(\"q\"), r.push(\"1 1 \" + _(Y.internal.getWidth(t) - 2) + \" \" + _(Y.internal.getHeight(t) - 2) + \" re\"), r.push(\"W\"), r.push(\"n\"), r.push(_(n.x1.x) + \" \" + _(n.x1.y) + \" m\"), r.push(_(n.x2.x) + \" \" + _(n.x2.y) + \" l\"), r.push(_(n.x4.x) + \" \" + _(n.x4.y) + \" m\"), r.push(_(n.x3.x) + \" \" + _(n.x3.y) + \" l\"), r.push(\"s\"), r.push(\"Q\"), e.stream = r.join(\"\\n\"), e;\n          },\n          OffPushDown: function (t) {\n            var e = l(t),\n              n = [];\n            return n.push(\"0.749023 g\"), n.push(\"0 0 \" + _(Y.internal.getWidth(t)) + \" \" + _(Y.internal.getHeight(t)) + \" re\"), n.push(\"f\"), e.stream = n.join(\"\\n\"), e;\n          }\n        }\n      },\n      createDefaultAppearanceStream: function (t) {\n        var e = A.internal.getFont(t.fontName, t.fontStyle).id,\n          n = A.__private__.encodeColorString(t.color);\n        return \"/\" + e + \" \" + t.fontSize + \" Tf \" + n;\n      }\n    };\n    Y.internal = {\n      Bezier_C: .551915024494,\n      calculateCross: function (t) {\n        var e = Y.internal.getWidth(t),\n          n = Y.internal.getHeight(t),\n          r = Math.min(e, n);\n        return {\n          x1: {\n            x: (e - r) / 2,\n            y: (n - r) / 2 + r\n          },\n          x2: {\n            x: (e - r) / 2 + r,\n            y: (n - r) / 2\n          },\n          x3: {\n            x: (e - r) / 2,\n            y: (n - r) / 2\n          },\n          x4: {\n            x: (e - r) / 2 + r,\n            y: (n - r) / 2 + r\n          }\n        };\n      }\n    }, Y.internal.getWidth = function (t) {\n      var e = 0;\n      return \"object\" === se(t) && (e = v(t.Rect[2])), e;\n    }, Y.internal.getHeight = function (t) {\n      var e = 0;\n      return \"object\" === se(t) && (e = v(t.Rect[3])), e;\n    };\n    var J = t.addField = function (t) {\n      if (k.call(this), !(t instanceof M)) throw new Error(\"Invalid argument passed to jsPDF.addField.\");\n      return function (t) {\n        A.internal.acroformPlugin.printedOut && (A.internal.acroformPlugin.printedOut = !1, A.internal.acroformPlugin.acroFormDictionaryRoot = null), A.internal.acroformPlugin.acroFormDictionaryRoot || k.call(A), A.internal.acroformPlugin.acroFormDictionaryRoot.Fields.push(t);\n      }.call(this, t), t.page = A.internal.getCurrentPageInfo().pageNumber, this;\n    };\n    t.addButton = function (t) {\n      if (t instanceof D == !1) throw new Error(\"Invalid argument passed to jsPDF.addButton.\");\n      return J.call(this, t);\n    }, t.addTextField = function (t) {\n      if (t instanceof V == !1) throw new Error(\"Invalid argument passed to jsPDF.addTextField.\");\n      return J.call(this, t);\n    }, t.addChoiceField = function (t) {\n      if (t instanceof O == !1) throw new Error(\"Invalid argument passed to jsPDF.addChoiceField.\");\n      return J.call(this, t);\n    };\n    \"object\" == se(e) && void 0 === e.ChoiceField && void 0 === e.ListBox && void 0 === e.ComboBox && void 0 === e.EditBox && void 0 === e.Button && void 0 === e.PushButton && void 0 === e.RadioButton && void 0 === e.CheckBox && void 0 === e.TextField && void 0 === e.PasswordField ? (e.ChoiceField = O, e.ListBox = q, e.ComboBox = T, e.EditBox = R, e.Button = D, e.PushButton = U, e.RadioButton = z, e.CheckBox = W, e.TextField = V, e.PasswordField = G, e.AcroForm = {\n      Appearance: Y\n    }) : console.warn(\"AcroForm-Classes are not populated into global-namespace, because the class-Names exist already.\"), t.AcroFormChoiceField = O, t.AcroFormListBox = q, t.AcroFormComboBox = T, t.AcroFormEditBox = R, t.AcroFormButton = D, t.AcroFormPushButton = U, t.AcroFormRadioButton = z, t.AcroFormCheckBox = W, t.AcroFormTextField = V, t.AcroFormPasswordField = G, t.AcroFormAppearance = Y, t.AcroForm = {\n      ChoiceField: O,\n      ListBox: q,\n      ComboBox: T,\n      EditBox: R,\n      Button: D,\n      PushButton: U,\n      RadioButton: z,\n      CheckBox: W,\n      TextField: V,\n      PasswordField: G,\n      Appearance: Y\n    };\n  })((window.tmp = lt).API, \"undefined\" != typeof window && window || \"undefined\" != typeof global && global),\n  /** @license\n     * jsPDF addImage plugin\n     * Copyright (c) 2012 Jason Siefken, https://github.com/siefkenj/\n     *               2013 Chris Dowling, https://github.com/gingerchris\n     *               2013 Trinh Ho, https://github.com/ineedfat\n     *               2013 Edwin Alejandro Perez, https://github.com/eaparango\n     *               2013 Norah Smith, https://github.com/burnburnrocket\n     *               2014 Diego Casorran, https://github.com/diegocr\n     *               2014 James Robb, https://github.com/jamesbrobb\n     *\n     * \n     */\n  function (x) {\n    var N = \"addImage_\",\n      l = {\n        PNG: [[137, 80, 78, 71]],\n        TIFF: [[77, 77, 0, 42], [73, 73, 42, 0]],\n        JPEG: [[255, 216, 255, 224, void 0, void 0, 74, 70, 73, 70, 0], [255, 216, 255, 225, void 0, void 0, 69, 120, 105, 102, 0, 0]],\n        JPEG2000: [[0, 0, 0, 12, 106, 80, 32, 32]],\n        GIF87a: [[71, 73, 70, 56, 55, 97]],\n        GIF89a: [[71, 73, 70, 56, 57, 97]],\n        BMP: [[66, 77], [66, 65], [67, 73], [67, 80], [73, 67], [80, 84]]\n      },\n      h = x.getImageFileTypeByImageData = function (t, e) {\n        var n, r;\n        e = e || \"UNKNOWN\";\n        var i,\n          o,\n          a,\n          s = \"UNKNOWN\";\n        for (a in x.isArrayBufferView(t) && (t = x.arrayBufferToBinaryString(t)), l) for (i = l[a], n = 0; n < i.length; n += 1) {\n          for (o = !0, r = 0; r < i[n].length; r += 1) if (void 0 !== i[n][r] && i[n][r] !== t.charCodeAt(r)) {\n            o = !1;\n            break;\n          }\n          if (!0 === o) {\n            s = a;\n            break;\n          }\n        }\n        return \"UNKNOWN\" === s && \"UNKNOWN\" !== e && (console.warn('FileType of Image not recognized. Processing image as \"' + e + '\".'), s = e), s;\n      },\n      n = function t(e) {\n        for (var n = this.internal.newObject(), r = this.internal.write, i = this.internal.putStream, o = (0, this.internal.getFilters)(); -1 !== o.indexOf(\"FlateEncode\");) o.splice(o.indexOf(\"FlateEncode\"), 1);\n        e.n = n;\n        var a = [];\n        if (a.push({\n          key: \"Type\",\n          value: \"/XObject\"\n        }), a.push({\n          key: \"Subtype\",\n          value: \"/Image\"\n        }), a.push({\n          key: \"Width\",\n          value: e.w\n        }), a.push({\n          key: \"Height\",\n          value: e.h\n        }), e.cs === this.color_spaces.INDEXED ? a.push({\n          key: \"ColorSpace\",\n          value: \"[/Indexed /DeviceRGB \" + (e.pal.length / 3 - 1) + \" \" + (\"smask\" in e ? n + 2 : n + 1) + \" 0 R]\"\n        }) : (a.push({\n          key: \"ColorSpace\",\n          value: \"/\" + e.cs\n        }), e.cs === this.color_spaces.DEVICE_CMYK && a.push({\n          key: \"Decode\",\n          value: \"[1 0 1 0 1 0 1 0]\"\n        })), a.push({\n          key: \"BitsPerComponent\",\n          value: e.bpc\n        }), \"dp\" in e && a.push({\n          key: \"DecodeParms\",\n          value: \"<<\" + e.dp + \">>\"\n        }), \"trns\" in e && e.trns.constructor == Array) {\n          for (var s = \"\", l = 0, h = e.trns.length; l < h; l++) s += e.trns[l] + \" \" + e.trns[l] + \" \";\n          a.push({\n            key: \"Mask\",\n            value: \"[\" + s + \"]\"\n          });\n        }\n        \"smask\" in e && a.push({\n          key: \"SMask\",\n          value: n + 1 + \" 0 R\"\n        });\n        var u = void 0 !== e.f ? [\"/\" + e.f] : void 0;\n        if (i({\n          data: e.data,\n          additionalKeyValues: a,\n          alreadyAppliedFilters: u\n        }), r(\"endobj\"), \"smask\" in e) {\n          var c = \"/Predictor \" + e.p + \" /Colors 1 /BitsPerComponent \" + e.bpc + \" /Columns \" + e.w,\n            f = {\n              w: e.w,\n              h: e.h,\n              cs: \"DeviceGray\",\n              bpc: e.bpc,\n              dp: c,\n              data: e.smask\n            };\n          \"f\" in e && (f.f = e.f), t.call(this, f);\n        }\n        e.cs === this.color_spaces.INDEXED && (this.internal.newObject(), i({\n          data: this.arrayBufferToBinaryString(new Uint8Array(e.pal))\n        }), r(\"endobj\"));\n      },\n      L = function () {\n        var t = this.internal.collections[N + \"images\"];\n        for (var e in t) n.call(this, t[e]);\n      },\n      A = function () {\n        var t,\n          e = this.internal.collections[N + \"images\"],\n          n = this.internal.write;\n        for (var r in e) n(\"/I\" + (t = e[r]).i, t.n, \"0\", \"R\");\n      },\n      S = function (t) {\n        return \"function\" == typeof x[\"process\" + t.toUpperCase()];\n      },\n      _ = function (t) {\n        return \"object\" === se(t) && 1 === t.nodeType;\n      },\n      F = function (t, e) {\n        if (\"IMG\" === t.nodeName && t.hasAttribute(\"src\")) {\n          var n = \"\" + t.getAttribute(\"src\");\n          if (0 === n.indexOf(\"data:image/\")) return unescape(n);\n          var r = x.loadFile(n);\n          if (void 0 !== r) return btoa(r);\n        }\n        if (\"CANVAS\" === t.nodeName) {\n          var i = t;\n          return t.toDataURL(\"image/jpeg\", 1);\n        }\n        (i = document.createElement(\"canvas\")).width = t.clientWidth || t.width, i.height = t.clientHeight || t.height;\n        var o = i.getContext(\"2d\");\n        if (!o) throw \"addImage requires canvas to be supported by browser.\";\n        return o.drawImage(t, 0, 0, i.width, i.height), i.toDataURL(\"png\" == (\"\" + e).toLowerCase() ? \"image/png\" : \"image/jpeg\");\n      },\n      P = function (t, e) {\n        var n;\n        if (e) for (var r in e) if (t === e[r].alias) {\n          n = e[r];\n          break;\n        }\n        return n;\n      };\n    x.color_spaces = {\n      DEVICE_RGB: \"DeviceRGB\",\n      DEVICE_GRAY: \"DeviceGray\",\n      DEVICE_CMYK: \"DeviceCMYK\",\n      CAL_GREY: \"CalGray\",\n      CAL_RGB: \"CalRGB\",\n      LAB: \"Lab\",\n      ICC_BASED: \"ICCBased\",\n      INDEXED: \"Indexed\",\n      PATTERN: \"Pattern\",\n      SEPARATION: \"Separation\",\n      DEVICE_N: \"DeviceN\"\n    }, x.decode = {\n      DCT_DECODE: \"DCTDecode\",\n      FLATE_DECODE: \"FlateDecode\",\n      LZW_DECODE: \"LZWDecode\",\n      JPX_DECODE: \"JPXDecode\",\n      JBIG2_DECODE: \"JBIG2Decode\",\n      ASCII85_DECODE: \"ASCII85Decode\",\n      ASCII_HEX_DECODE: \"ASCIIHexDecode\",\n      RUN_LENGTH_DECODE: \"RunLengthDecode\",\n      CCITT_FAX_DECODE: \"CCITTFaxDecode\"\n    }, x.image_compression = {\n      NONE: \"NONE\",\n      FAST: \"FAST\",\n      MEDIUM: \"MEDIUM\",\n      SLOW: \"SLOW\"\n    }, x.sHashCode = function (t) {\n      var e,\n        n = 0;\n      if (0 === (t = t || \"\").length) return n;\n      for (e = 0; e < t.length; e++) n = (n << 5) - n + t.charCodeAt(e), n |= 0;\n      return n;\n    }, x.isString = function (t) {\n      return \"string\" == typeof t;\n    }, x.validateStringAsBase64 = function (t) {\n      (t = t || \"\").toString().trim();\n      var e = !0;\n      return 0 === t.length && (e = !1), t.length % 4 != 0 && (e = !1), !1 === /^[A-Za-z0-9+\\/]+$/.test(t.substr(0, t.length - 2)) && (e = !1), !1 === /^[A-Za-z0-9\\/][A-Za-z0-9+\\/]|[A-Za-z0-9+\\/]=|==$/.test(t.substr(-2)) && (e = !1), e;\n    }, x.extractInfoFromBase64DataURI = function (t) {\n      return /^data:([\\w]+?\\/([\\w]+?));\\S*;*base64,(.+)$/g.exec(t);\n    }, x.extractImageFromDataUrl = function (t) {\n      var e = (t = t || \"\").split(\"base64,\"),\n        n = null;\n      if (2 === e.length) {\n        var r = /^data:(\\w*\\/\\w*);*(charset=[\\w=-]*)*;*$/.exec(e[0]);\n        Array.isArray(r) && (n = {\n          mimeType: r[1],\n          charset: r[2],\n          data: e[1]\n        });\n      }\n      return n;\n    }, x.supportsArrayBuffer = function () {\n      return \"undefined\" != typeof ArrayBuffer && \"undefined\" != typeof Uint8Array;\n    }, x.isArrayBuffer = function (t) {\n      return !!this.supportsArrayBuffer() && t instanceof ArrayBuffer;\n    }, x.isArrayBufferView = function (t) {\n      return !!this.supportsArrayBuffer() && \"undefined\" != typeof Uint32Array && (t instanceof Int8Array || t instanceof Uint8Array || \"undefined\" != typeof Uint8ClampedArray && t instanceof Uint8ClampedArray || t instanceof Int16Array || t instanceof Uint16Array || t instanceof Int32Array || t instanceof Uint32Array || t instanceof Float32Array || t instanceof Float64Array);\n    }, x.binaryStringToUint8Array = function (t) {\n      for (var e = t.length, n = new Uint8Array(e), r = 0; r < e; r++) n[r] = t.charCodeAt(r);\n      return n;\n    }, x.arrayBufferToBinaryString = function (t) {\n      if (\"function\" == typeof atob) return atob(this.arrayBufferToBase64(t));\n    }, x.arrayBufferToBase64 = function (t) {\n      for (var e, n = \"\", r = \"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/\", i = new Uint8Array(t), o = i.byteLength, a = o % 3, s = o - a, l = 0; l < s; l += 3) n += r[(16515072 & (e = i[l] << 16 | i[l + 1] << 8 | i[l + 2])) >> 18] + r[(258048 & e) >> 12] + r[(4032 & e) >> 6] + r[63 & e];\n      return 1 == a ? n += r[(252 & (e = i[s])) >> 2] + r[(3 & e) << 4] + \"==\" : 2 == a && (n += r[(64512 & (e = i[s] << 8 | i[s + 1])) >> 10] + r[(1008 & e) >> 4] + r[(15 & e) << 2] + \"=\"), n;\n    }, x.createImageInfo = function (t, e, n, r, i, o, a, s, l, h, u, c, f) {\n      var p = {\n        alias: s,\n        w: e,\n        h: n,\n        cs: r,\n        bpc: i,\n        i: a,\n        data: t\n      };\n      return o && (p.f = o), l && (p.dp = l), h && (p.trns = h), u && (p.pal = u), c && (p.smask = c), f && (p.p = f), p;\n    }, x.addImage = function (t, e, n, r, i, o, a, s, l) {\n      var h = \"\";\n      if (\"string\" != typeof e) {\n        var u = o;\n        o = i, i = r, r = n, n = e, e = u;\n      }\n      if (\"object\" === se(t) && !_(t) && \"imageData\" in t) {\n        var c = t;\n        t = c.imageData, e = c.format || e || \"UNKNOWN\", n = c.x || n || 0, r = c.y || r || 0, i = c.w || i, o = c.h || o, a = c.alias || a, s = c.compression || s, l = c.rotation || c.angle || l;\n      }\n      var f = this.internal.getFilters();\n      if (void 0 === s && -1 !== f.indexOf(\"FlateEncode\") && (s = \"SLOW\"), \"string\" == typeof t && (t = unescape(t)), isNaN(n) || isNaN(r)) throw console.error(\"jsPDF.addImage: Invalid coordinates\", arguments), new Error(\"Invalid coordinates passed to jsPDF.addImage\");\n      var p,\n        d,\n        g,\n        m,\n        y,\n        v,\n        w,\n        b = function () {\n          var t = this.internal.collections[N + \"images\"];\n          return t || (this.internal.collections[N + \"images\"] = t = {}, this.internal.events.subscribe(\"putResources\", L), this.internal.events.subscribe(\"putXobjectDict\", A)), t;\n        }.call(this);\n      if (!((p = P(t, b)) || (_(t) && (t = F(t, e)), (null == (w = a) || 0 === w.length) && (a = \"string\" == typeof (v = t) ? x.sHashCode(v) : x.isArrayBufferView(v) ? x.sHashCode(x.arrayBufferToBinaryString(v)) : null), p = P(a, b)))) {\n        if (this.isString(t) && (\"\" !== (h = this.convertStringToImageData(t)) ? t = h : void 0 !== (h = x.loadFile(t)) && (t = h)), e = this.getImageFileTypeByImageData(t, e), !S(e)) throw new Error(\"addImage does not support files of type '\" + e + \"', please ensure that a plugin for '\" + e + \"' support is added.\");\n        if (this.supportsArrayBuffer() && (t instanceof Uint8Array || (d = t, t = this.binaryStringToUint8Array(t))), !(p = this[\"process\" + e.toUpperCase()](t, (y = 0, (m = b) && (y = Object.keys ? Object.keys(m).length : function (t) {\n          var e = 0;\n          for (var n in t) t.hasOwnProperty(n) && e++;\n          return e;\n        }(m)), y), a, ((g = s) && \"string\" == typeof g && (g = g.toUpperCase()), g in x.image_compression ? g : x.image_compression.NONE), d))) throw new Error(\"An unknown error occurred whilst processing the image\");\n      }\n      return function (t, e, n, r, i, o, a, s) {\n        var l = function (t, e, n) {\n            return t || e || (e = t = -96), t < 0 && (t = -1 * n.w * 72 / t / this.internal.scaleFactor), e < 0 && (e = -1 * n.h * 72 / e / this.internal.scaleFactor), 0 === t && (t = e * n.w / n.h), 0 === e && (e = t * n.h / n.w), [t, e];\n          }.call(this, n, r, i),\n          h = this.internal.getCoordinateString,\n          u = this.internal.getVerticalCoordinateString;\n        if (n = l[0], r = l[1], a[o] = i, s) {\n          s *= Math.PI / 180;\n          var c = Math.cos(s),\n            f = Math.sin(s),\n            p = function (t) {\n              return t.toFixed(4);\n            },\n            d = [p(c), p(f), p(-1 * f), p(c), 0, 0, \"cm\"];\n        }\n        this.internal.write(\"q\"), s ? (this.internal.write([1, \"0\", \"0\", 1, h(t), u(e + r), \"cm\"].join(\" \")), this.internal.write(d.join(\" \")), this.internal.write([h(n), \"0\", \"0\", h(r), \"0\", \"0\", \"cm\"].join(\" \"))) : this.internal.write([h(n), \"0\", \"0\", h(r), h(t), u(e + r), \"cm\"].join(\" \")), this.internal.write(\"/I\" + i.i + \" Do\"), this.internal.write(\"Q\");\n      }.call(this, n, r, i, o, p, p.i, b, l), this;\n    }, x.convertStringToImageData = function (t) {\n      var e,\n        n = \"\";\n      if (this.isString(t)) {\n        var r;\n        e = null !== (r = this.extractImageFromDataUrl(t)) ? r.data : t;\n        try {\n          n = atob(e);\n        } catch (t) {\n          throw x.validateStringAsBase64(e) ? new Error(\"atob-Error in jsPDF.convertStringToImageData \" + t.message) : new Error(\"Supplied Data is not a valid base64-String jsPDF.convertStringToImageData \");\n        }\n      }\n      return n;\n    };\n    var u = function (t, e) {\n      return t.subarray(e, e + 5);\n    };\n    x.processJPEG = function (t, e, n, r, i, o) {\n      var a,\n        s = this.decode.DCT_DECODE;\n      if (!this.isString(t) && !this.isArrayBuffer(t) && !this.isArrayBufferView(t)) return null;\n      if (this.isString(t) && (a = function (t) {\n        var e;\n        if (\"JPEG\" !== h(t)) throw new Error(\"getJpegSize requires a binary string jpeg file\");\n        for (var n = 256 * t.charCodeAt(4) + t.charCodeAt(5), r = 4, i = t.length; r < i;) {\n          if (r += n, 255 !== t.charCodeAt(r)) throw new Error(\"getJpegSize could not find the size of the image\");\n          if (192 === t.charCodeAt(r + 1) || 193 === t.charCodeAt(r + 1) || 194 === t.charCodeAt(r + 1) || 195 === t.charCodeAt(r + 1) || 196 === t.charCodeAt(r + 1) || 197 === t.charCodeAt(r + 1) || 198 === t.charCodeAt(r + 1) || 199 === t.charCodeAt(r + 1)) return e = 256 * t.charCodeAt(r + 5) + t.charCodeAt(r + 6), [256 * t.charCodeAt(r + 7) + t.charCodeAt(r + 8), e, t.charCodeAt(r + 9)];\n          r += 2, n = 256 * t.charCodeAt(r) + t.charCodeAt(r + 1);\n        }\n      }(t)), this.isArrayBuffer(t) && (t = new Uint8Array(t)), this.isArrayBufferView(t) && (a = function (t) {\n        if (65496 != (t[0] << 8 | t[1])) throw new Error(\"Supplied data is not a JPEG\");\n        for (var e, n = t.length, r = (t[4] << 8) + t[5], i = 4; i < n;) {\n          if (r = ((e = u(t, i += r))[2] << 8) + e[3], (192 === e[1] || 194 === e[1]) && 255 === e[0] && 7 < r) return {\n            width: ((e = u(t, i + 5))[2] << 8) + e[3],\n            height: (e[0] << 8) + e[1],\n            numcomponents: e[4]\n          };\n          i += 2;\n        }\n        throw new Error(\"getJpegSizeFromBytes could not find the size of the image\");\n      }(t), t = i || this.arrayBufferToBinaryString(t)), void 0 === o) switch (a.numcomponents) {\n        case 1:\n          o = this.color_spaces.DEVICE_GRAY;\n          break;\n        case 4:\n          o = this.color_spaces.DEVICE_CMYK;\n          break;\n        default:\n        case 3:\n          o = this.color_spaces.DEVICE_RGB;\n      }\n      return this.createImageInfo(t, a.width, a.height, o, 8, s, e, n);\n    }, x.processJPG = function () {\n      return this.processJPEG.apply(this, arguments);\n    }, x.getImageProperties = function (t) {\n      var e,\n        n,\n        r = \"\";\n      if (_(t) && (t = F(t)), this.isString(t) && (\"\" !== (r = this.convertStringToImageData(t)) ? t = r : void 0 !== (r = x.loadFile(t)) && (t = r)), n = this.getImageFileTypeByImageData(t), !S(n)) throw new Error(\"addImage does not support files of type '\" + n + \"', please ensure that a plugin for '\" + n + \"' support is added.\");\n      if (this.supportsArrayBuffer() && (t instanceof Uint8Array || (t = this.binaryStringToUint8Array(t))), !(e = this[\"process\" + n.toUpperCase()](t))) throw new Error(\"An unknown error occurred whilst processing the image\");\n      return {\n        fileType: n,\n        width: e.w,\n        height: e.h,\n        colorSpace: e.cs,\n        compressionMode: e.f,\n        bitsPerComponent: e.bpc\n      };\n    };\n  }(lt.API),\n  /**\n     * @license\n     * Copyright (c) 2014 Steven Spungin (TwelveTone LLC)  <EMAIL>\n     *\n     * Licensed under the MIT License.\n     * http://opensource.org/licenses/mit-license\n     */\n  t = lt.API, lt.API.events.push([\"addPage\", function (t) {\n    this.internal.getPageInfo(t.pageNumber).pageContext.annotations = [];\n  }]), t.events.push([\"putPage\", function (t) {\n    for (var e = this.internal.getPageInfoByObjId(t.objId), n = t.pageContext.annotations, r = function (t) {\n        if (void 0 !== t && \"\" != t) return !0;\n      }, i = !1, o = 0; o < n.length && !i; o++) switch ((l = n[o]).type) {\n      case \"link\":\n        if (r(l.options.url) || r(l.options.pageNumber)) {\n          i = !0;\n          break;\n        }\n      case \"reference\":\n      case \"text\":\n      case \"freetext\":\n        i = !0;\n    }\n    if (0 != i) {\n      this.internal.write(\"/Annots [\"), this.internal.pageSize.height;\n      var a = this.internal.getCoordinateString,\n        s = this.internal.getVerticalCoordinateString;\n      for (o = 0; o < n.length; o++) {\n        var l;\n        switch ((l = n[o]).type) {\n          case \"reference\":\n            this.internal.write(\" \" + l.object.objId + \" 0 R \");\n            break;\n          case \"text\":\n            var h = this.internal.newAdditionalObject(),\n              u = this.internal.newAdditionalObject(),\n              c = l.title || \"Note\";\n            m = \"<</Type /Annot /Subtype /Text \" + (p = \"/Rect [\" + a(l.bounds.x) + \" \" + s(l.bounds.y + l.bounds.h) + \" \" + a(l.bounds.x + l.bounds.w) + \" \" + s(l.bounds.y) + \"] \") + \"/Contents (\" + l.contents + \")\", m += \" /Popup \" + u.objId + \" 0 R\", m += \" /P \" + e.objId + \" 0 R\", m += \" /T (\" + c + \") >>\", h.content = m;\n            var f = h.objId + \" 0 R\";\n            m = \"<</Type /Annot /Subtype /Popup \" + (p = \"/Rect [\" + a(l.bounds.x + 30) + \" \" + s(l.bounds.y + l.bounds.h) + \" \" + a(l.bounds.x + l.bounds.w + 30) + \" \" + s(l.bounds.y) + \"] \") + \" /Parent \" + f, l.open && (m += \" /Open true\"), m += \" >>\", u.content = m, this.internal.write(h.objId, \"0 R\", u.objId, \"0 R\");\n            break;\n          case \"freetext\":\n            var p = \"/Rect [\" + a(l.bounds.x) + \" \" + s(l.bounds.y) + \" \" + a(l.bounds.x + l.bounds.w) + \" \" + s(l.bounds.y + l.bounds.h) + \"] \",\n              d = l.color || \"#000000\";\n            m = \"<</Type /Annot /Subtype /FreeText \" + p + \"/Contents (\" + l.contents + \")\", m += \" /DS(font: Helvetica,sans-serif 12.0pt; text-align:left; color:#\" + d + \")\", m += \" /Border [0 0 0]\", m += \" >>\", this.internal.write(m);\n            break;\n          case \"link\":\n            if (l.options.name) {\n              var g = this.annotations._nameMap[l.options.name];\n              l.options.pageNumber = g.page, l.options.top = g.y;\n            } else l.options.top || (l.options.top = 0);\n            p = \"/Rect [\" + a(l.x) + \" \" + s(l.y) + \" \" + a(l.x + l.w) + \" \" + s(l.y + l.h) + \"] \";\n            var m = \"\";\n            if (l.options.url) m = \"<</Type /Annot /Subtype /Link \" + p + \"/Border [0 0 0] /A <</S /URI /URI (\" + l.options.url + \") >>\";else if (l.options.pageNumber) switch (m = \"<</Type /Annot /Subtype /Link \" + p + \"/Border [0 0 0] /Dest [\" + this.internal.getPageInfo(l.options.pageNumber).objId + \" 0 R\", l.options.magFactor = l.options.magFactor || \"XYZ\", l.options.magFactor) {\n              case \"Fit\":\n                m += \" /Fit]\";\n                break;\n              case \"FitH\":\n                m += \" /FitH \" + l.options.top + \"]\";\n                break;\n              case \"FitV\":\n                l.options.left = l.options.left || 0, m += \" /FitV \" + l.options.left + \"]\";\n                break;\n              case \"XYZ\":\n              default:\n                var y = s(l.options.top);\n                l.options.left = l.options.left || 0, void 0 === l.options.zoom && (l.options.zoom = 0), m += \" /XYZ \" + l.options.left + \" \" + y + \" \" + l.options.zoom + \"]\";\n            }\n            \"\" != m && (m += \" >>\", this.internal.write(m));\n        }\n      }\n      this.internal.write(\"]\");\n    }\n  }]), t.createAnnotation = function (t) {\n    var e = this.internal.getCurrentPageInfo();\n    switch (t.type) {\n      case \"link\":\n        this.link(t.bounds.x, t.bounds.y, t.bounds.w, t.bounds.h, t);\n        break;\n      case \"text\":\n      case \"freetext\":\n        e.pageContext.annotations.push(t);\n    }\n  }, t.link = function (t, e, n, r, i) {\n    this.internal.getCurrentPageInfo().pageContext.annotations.push({\n      x: t,\n      y: e,\n      w: n,\n      h: r,\n      options: i,\n      type: \"link\"\n    });\n  }, t.textWithLink = function (t, e, n, r) {\n    var i = this.getTextWidth(t),\n      o = this.internal.getLineHeight() / this.internal.scaleFactor;\n    return this.text(t, e, n), n += .2 * o, this.link(e, n - o, i, o, r), i;\n  }, t.getTextWidth = function (t) {\n    var e = this.internal.getFontSize();\n    return this.getStringUnitWidth(t) * e / this.internal.scaleFactor;\n  },\n  /**\n     * @license\n     * Copyright (c) 2017 Aras Abbasi \n     *\n     * Licensed under the MIT License.\n     * http://opensource.org/licenses/mit-license\n     */\n  function (t) {\n    var h = {\n        1569: [65152],\n        1570: [65153, 65154],\n        1571: [65155, 65156],\n        1572: [65157, 65158],\n        1573: [65159, 65160],\n        1574: [65161, 65162, 65163, 65164],\n        1575: [65165, 65166],\n        1576: [65167, 65168, 65169, 65170],\n        1577: [65171, 65172],\n        1578: [65173, 65174, 65175, 65176],\n        1579: [65177, 65178, 65179, 65180],\n        1580: [65181, 65182, 65183, 65184],\n        1581: [65185, 65186, 65187, 65188],\n        1582: [65189, 65190, 65191, 65192],\n        1583: [65193, 65194],\n        1584: [65195, 65196],\n        1585: [65197, 65198],\n        1586: [65199, 65200],\n        1587: [65201, 65202, 65203, 65204],\n        1588: [65205, 65206, 65207, 65208],\n        1589: [65209, 65210, 65211, 65212],\n        1590: [65213, 65214, 65215, 65216],\n        1591: [65217, 65218, 65219, 65220],\n        1592: [65221, 65222, 65223, 65224],\n        1593: [65225, 65226, 65227, 65228],\n        1594: [65229, 65230, 65231, 65232],\n        1601: [65233, 65234, 65235, 65236],\n        1602: [65237, 65238, 65239, 65240],\n        1603: [65241, 65242, 65243, 65244],\n        1604: [65245, 65246, 65247, 65248],\n        1605: [65249, 65250, 65251, 65252],\n        1606: [65253, 65254, 65255, 65256],\n        1607: [65257, 65258, 65259, 65260],\n        1608: [65261, 65262],\n        1609: [65263, 65264, 64488, 64489],\n        1610: [65265, 65266, 65267, 65268],\n        1649: [64336, 64337],\n        1655: [64477],\n        1657: [64358, 64359, 64360, 64361],\n        1658: [64350, 64351, 64352, 64353],\n        1659: [64338, 64339, 64340, 64341],\n        1662: [64342, 64343, 64344, 64345],\n        1663: [64354, 64355, 64356, 64357],\n        1664: [64346, 64347, 64348, 64349],\n        1667: [64374, 64375, 64376, 64377],\n        1668: [64370, 64371, 64372, 64373],\n        1670: [64378, 64379, 64380, 64381],\n        1671: [64382, 64383, 64384, 64385],\n        1672: [64392, 64393],\n        1676: [64388, 64389],\n        1677: [64386, 64387],\n        1678: [64390, 64391],\n        1681: [64396, 64397],\n        1688: [64394, 64395],\n        1700: [64362, 64363, 64364, 64365],\n        1702: [64366, 64367, 64368, 64369],\n        1705: [64398, 64399, 64400, 64401],\n        1709: [64467, 64468, 64469, 64470],\n        1711: [64402, 64403, 64404, 64405],\n        1713: [64410, 64411, 64412, 64413],\n        1715: [64406, 64407, 64408, 64409],\n        1722: [64414, 64415],\n        1723: [64416, 64417, 64418, 64419],\n        1726: [64426, 64427, 64428, 64429],\n        1728: [64420, 64421],\n        1729: [64422, 64423, 64424, 64425],\n        1733: [64480, 64481],\n        1734: [64473, 64474],\n        1735: [64471, 64472],\n        1736: [64475, 64476],\n        1737: [64482, 64483],\n        1739: [64478, 64479],\n        1740: [64508, 64509, 64510, 64511],\n        1744: [64484, 64485, 64486, 64487],\n        1746: [64430, 64431],\n        1747: [64432, 64433]\n      },\n      a = {\n        65247: {\n          65154: 65269,\n          65156: 65271,\n          65160: 65273,\n          65166: 65275\n        },\n        65248: {\n          65154: 65270,\n          65156: 65272,\n          65160: 65274,\n          65166: 65276\n        },\n        65165: {\n          65247: {\n            65248: {\n              65258: 65010\n            }\n          }\n        },\n        1617: {\n          1612: 64606,\n          1613: 64607,\n          1614: 64608,\n          1615: 64609,\n          1616: 64610\n        }\n      },\n      e = {\n        1612: 64606,\n        1613: 64607,\n        1614: 64608,\n        1615: 64609,\n        1616: 64610\n      },\n      n = [1570, 1571, 1573, 1575];\n    t.__arabicParser__ = {};\n    var r = t.__arabicParser__.isInArabicSubstitutionA = function (t) {\n        return void 0 !== h[t.charCodeAt(0)];\n      },\n      u = t.__arabicParser__.isArabicLetter = function (t) {\n        return \"string\" == typeof t && /^[\\u0600-\\u06FF\\u0750-\\u077F\\u08A0-\\u08FF\\uFB50-\\uFDFF\\uFE70-\\uFEFF]+$/.test(t);\n      },\n      i = t.__arabicParser__.isArabicEndLetter = function (t) {\n        return u(t) && r(t) && h[t.charCodeAt(0)].length <= 2;\n      },\n      o = t.__arabicParser__.isArabicAlfLetter = function (t) {\n        return u(t) && 0 <= n.indexOf(t.charCodeAt(0));\n      },\n      s = (t.__arabicParser__.arabicLetterHasIsolatedForm = function (t) {\n        return u(t) && r(t) && 1 <= h[t.charCodeAt(0)].length;\n      }, t.__arabicParser__.arabicLetterHasFinalForm = function (t) {\n        return u(t) && r(t) && 2 <= h[t.charCodeAt(0)].length;\n      }),\n      l = (t.__arabicParser__.arabicLetterHasInitialForm = function (t) {\n        return u(t) && r(t) && 3 <= h[t.charCodeAt(0)].length;\n      }, t.__arabicParser__.arabicLetterHasMedialForm = function (t) {\n        return u(t) && r(t) && 4 == h[t.charCodeAt(0)].length;\n      }),\n      c = t.__arabicParser__.resolveLigatures = function (t) {\n        var e = 0,\n          n = a,\n          r = 0,\n          i = \"\",\n          o = 0;\n        for (e = 0; e < t.length; e += 1) void 0 !== n[t.charCodeAt(e)] ? (o++, \"number\" == typeof (n = n[t.charCodeAt(e)]) && (r = -1 !== (r = f(t.charAt(e), t.charAt(e - o), t.charAt(e + 1))) ? r : 0, i += String.fromCharCode(n), n = a, o = 0), e === t.length - 1 && (n = a, i += t.charAt(e - (o - 1)), e -= o - 1, o = 0)) : (n = a, i += t.charAt(e - o), e -= o, o = 0);\n        return i;\n      },\n      f = (t.__arabicParser__.isArabicDiacritic = function (t) {\n        return void 0 !== t && void 0 !== e[t.charCodeAt(0)];\n      }, t.__arabicParser__.getCorrectForm = function (t, e, n) {\n        return u(t) ? !1 === r(t) ? -1 : !s(t) || !u(e) && !u(n) || !u(n) && i(e) || i(t) && !u(e) || i(t) && o(e) || i(t) && i(e) ? 0 : l(t) && u(e) && !i(e) && u(n) && s(n) ? 3 : i(t) || !u(n) ? 1 : 2 : -1;\n      }),\n      p = t.__arabicParser__.processArabic = t.processArabic = function (t) {\n        var e = 0,\n          n = 0,\n          r = 0,\n          i = \"\",\n          o = \"\",\n          a = \"\",\n          s = (t = t || \"\").split(\"\\\\s+\"),\n          l = [];\n        for (e = 0; e < s.length; e += 1) {\n          for (l.push(\"\"), n = 0; n < s[e].length; n += 1) i = s[e][n], o = s[e][n - 1], a = s[e][n + 1], u(i) ? (r = f(i, o, a), l[e] += -1 !== r ? String.fromCharCode(h[i.charCodeAt(0)][r]) : i) : l[e] += i;\n          l[e] = c(l[e]);\n        }\n        return l.join(\" \");\n      };\n    t.events.push([\"preProcessText\", function (t) {\n      var e = t.text,\n        n = (t.x, t.y, t.options || {}),\n        r = (t.mutex, n.lang, []);\n      if (\"[object Array]\" === Object.prototype.toString.call(e)) {\n        var i = 0;\n        for (r = [], i = 0; i < e.length; i += 1) \"[object Array]\" === Object.prototype.toString.call(e[i]) ? r.push([p(e[i][0]), e[i][1], e[i][2]]) : r.push([p(e[i])]);\n        t.text = r;\n      } else t.text = p(e);\n    }]);\n  }(lt.API), lt.API.autoPrint = function (t) {\n    var e;\n    switch ((t = t || {}).variant = t.variant || \"non-conform\", t.variant) {\n      case \"javascript\":\n        this.addJS(\"print({});\");\n        break;\n      case \"non-conform\":\n      default:\n        this.internal.events.subscribe(\"postPutResources\", function () {\n          e = this.internal.newObject(), this.internal.out(\"<<\"), this.internal.out(\"/S /Named\"), this.internal.out(\"/Type /Action\"), this.internal.out(\"/N /Print\"), this.internal.out(\">>\"), this.internal.out(\"endobj\");\n        }), this.internal.events.subscribe(\"putCatalog\", function () {\n          this.internal.out(\"/OpenAction \" + e + \" 0 R\");\n        });\n    }\n    return this;\n  },\n  /**\n     * @license\n     * Copyright (c) 2014 Steven Spungin (TwelveTone LLC)  <EMAIL>\n     *\n     * Licensed under the MIT License.\n     * http://opensource.org/licenses/mit-license\n     */\n  e = lt.API, (n = function () {\n    var e = void 0;\n    Object.defineProperty(this, \"pdf\", {\n      get: function () {\n        return e;\n      },\n      set: function (t) {\n        e = t;\n      }\n    });\n    var n = 150;\n    Object.defineProperty(this, \"width\", {\n      get: function () {\n        return n;\n      },\n      set: function (t) {\n        n = isNaN(t) || !1 === Number.isInteger(t) || t < 0 ? 150 : t, this.getContext(\"2d\").pageWrapXEnabled && (this.getContext(\"2d\").pageWrapX = n + 1);\n      }\n    });\n    var r = 300;\n    Object.defineProperty(this, \"height\", {\n      get: function () {\n        return r;\n      },\n      set: function (t) {\n        r = isNaN(t) || !1 === Number.isInteger(t) || t < 0 ? 300 : t, this.getContext(\"2d\").pageWrapYEnabled && (this.getContext(\"2d\").pageWrapY = r + 1);\n      }\n    });\n    var i = [];\n    Object.defineProperty(this, \"childNodes\", {\n      get: function () {\n        return i;\n      },\n      set: function (t) {\n        i = t;\n      }\n    });\n    var o = {};\n    Object.defineProperty(this, \"style\", {\n      get: function () {\n        return o;\n      },\n      set: function (t) {\n        o = t;\n      }\n    }), Object.defineProperty(this, \"parentNode\", {\n      get: function () {\n        return !1;\n      }\n    });\n  }).prototype.getContext = function (t, e) {\n    var n;\n    if (\"2d\" !== (t = t || \"2d\")) return null;\n    for (n in e) this.pdf.context2d.hasOwnProperty(n) && (this.pdf.context2d[n] = e[n]);\n    return (this.pdf.context2d._canvas = this).pdf.context2d;\n  }, n.prototype.toDataURL = function () {\n    throw new Error(\"toDataURL is not implemented.\");\n  }, e.events.push([\"initialized\", function () {\n    this.canvas = new n(), this.canvas.pdf = this;\n  }]),\n  /** \n     * @license\n     * ====================================================================\n     * Copyright (c) 2013 Youssef Beddad, <EMAIL>\n     *               2013 Eduardo Menezes de Morais, <EMAIL>\n     *               2013 Lee Driscoll, https://github.com/lsdriscoll\n     *               2014 Juan Pablo Gaviria, https://github.com/juanpgaviria\n     *               2014 James Hall, <EMAIL>\n     *               2014 Diego Casorran, https://github.com/diegocr\n     *\n     * \n     * ====================================================================\n     */\n  _ = lt.API, F = {\n    x: void 0,\n    y: void 0,\n    w: void 0,\n    h: void 0,\n    ln: void 0\n  }, P = 1, p = function (t, e, n, r, i) {\n    F = {\n      x: t,\n      y: e,\n      w: n,\n      h: r,\n      ln: i\n    };\n  }, d = function () {\n    return F;\n  }, k = {\n    left: 0,\n    top: 0,\n    bottom: 0\n  }, _.setHeaderFunction = function (t) {\n    l = t;\n  }, _.getTextDimensions = function (t, e) {\n    var n = this.table_font_size || this.internal.getFontSize(),\n      r = (this.internal.getFont().fontStyle, (e = e || {}).scaleFactor || this.internal.scaleFactor),\n      i = 0,\n      o = 0,\n      a = 0;\n    if (\"string\" == typeof t) 0 != (i = this.getStringUnitWidth(t) * n) && (o = 1);else {\n      if (\"[object Array]\" !== Object.prototype.toString.call(t)) throw new Error(\"getTextDimensions expects text-parameter to be of type String or an Array of Strings.\");\n      for (var s = 0; s < t.length; s++) i < (a = this.getStringUnitWidth(t[s]) * n) && (i = a);\n      0 !== i && (o = t.length);\n    }\n    return {\n      w: i /= r,\n      h: Math.max((o * n * this.getLineHeightFactor() - n * (this.getLineHeightFactor() - 1)) / r, 0)\n    };\n  }, _.cellAddPage = function () {\n    var t = this.margins || k;\n    this.addPage(), p(t.left, t.top, void 0, void 0), P += 1;\n  }, _.cellInitialize = function () {\n    F = {\n      x: void 0,\n      y: void 0,\n      w: void 0,\n      h: void 0,\n      ln: void 0\n    }, P = 1;\n  }, _.cell = function (t, e, n, r, i, o, a) {\n    var s = d(),\n      l = !1;\n    if (void 0 !== s.ln) if (s.ln === o) t = s.x + s.w, e = s.y;else {\n      var h = this.margins || k;\n      s.y + s.h + r + 13 >= this.internal.pageSize.getHeight() - h.bottom && (this.cellAddPage(), l = !0, this.printHeaders && this.tableHeaderRow && this.printHeaderRow(o, !0)), e = d().y + d().h, l && (e = 23);\n    }\n    if (void 0 !== i[0]) if (this.printingHeaderRow ? this.rect(t, e, n, r, \"FD\") : this.rect(t, e, n, r), \"right\" === a) {\n      i instanceof Array || (i = [i]);\n      for (var u = 0; u < i.length; u++) {\n        var c = i[u],\n          f = this.getStringUnitWidth(c) * this.internal.getFontSize() / this.internal.scaleFactor;\n        this.text(c, t + n - f - 3, e + this.internal.getLineHeight() * (u + 1));\n      }\n    } else this.text(i, t + 3, e + this.internal.getLineHeight());\n    return p(t, e, n, r, o), this;\n  }, _.arrayMax = function (t, e) {\n    var n,\n      r,\n      i,\n      o = t[0];\n    for (n = 0, r = t.length; n < r; n += 1) i = t[n], e ? -1 === e(o, i) && (o = i) : o < i && (o = i);\n    return o;\n  }, _.table = function (t, e, n, r, i) {\n    if (!n) throw \"No data for PDF table\";\n    var o,\n      a,\n      s,\n      l,\n      h,\n      u,\n      c,\n      f,\n      p,\n      d,\n      g = [],\n      m = [],\n      y = {},\n      v = {},\n      w = [],\n      b = [],\n      x = !1,\n      N = !0,\n      L = 12,\n      A = k;\n    if (A.width = this.internal.pageSize.getWidth(), i && (!0 === i.autoSize && (x = !0), !1 === i.printHeaders && (N = !1), i.fontSize && (L = i.fontSize), i.css && void 0 !== i.css[\"font-size\"] && (L = 16 * i.css[\"font-size\"]), i.margins && (A = i.margins)), this.lnMod = 0, F = {\n      x: void 0,\n      y: void 0,\n      w: void 0,\n      h: void 0,\n      ln: void 0\n    }, P = 1, this.printHeaders = N, this.margins = A, this.setFontSize(L), this.table_font_size = L, null == r) g = Object.keys(n[0]);else if (r[0] && \"string\" != typeof r[0]) for (a = 0, s = r.length; a < s; a += 1) o = r[a], g.push(o.name), m.push(o.prompt), v[o.name] = o.width * (19.049976 / 25.4);else g = r;\n    if (x) for (d = function (t) {\n      return t[o];\n    }, a = 0, s = g.length; a < s; a += 1) {\n      for (y[o = g[a]] = n.map(d), w.push(this.getTextDimensions(m[a] || o, {\n        scaleFactor: 1\n      }).w), c = 0, l = (u = y[o]).length; c < l; c += 1) h = u[c], w.push(this.getTextDimensions(h, {\n        scaleFactor: 1\n      }).w);\n      v[o] = _.arrayMax(w), w = [];\n    }\n    if (N) {\n      var S = this.calculateLineHeight(g, v, m.length ? m : g);\n      for (a = 0, s = g.length; a < s; a += 1) o = g[a], b.push([t, e, v[o], S, String(m.length ? m[a] : o)]);\n      this.setTableHeaderRow(b), this.printHeaderRow(1, !1);\n    }\n    for (a = 0, s = n.length; a < s; a += 1) for (f = n[a], S = this.calculateLineHeight(g, v, f), c = 0, p = g.length; c < p; c += 1) o = g[c], this.cell(t, e, v[o], S, f[o], a + 2, o.align);\n    return this.lastCellPos = F, this.table_x = t, this.table_y = e, this;\n  }, _.calculateLineHeight = function (t, e, n) {\n    for (var r, i = 0, o = 0; o < t.length; o++) {\n      n[r = t[o]] = this.splitTextToSize(String(n[r]), e[r] - 3);\n      var a = this.internal.getLineHeight() * n[r].length + 3;\n      i < a && (i = a);\n    }\n    return i;\n  }, _.setTableHeaderRow = function (t) {\n    this.tableHeaderRow = t;\n  }, _.printHeaderRow = function (t, e) {\n    if (!this.tableHeaderRow) throw \"Property tableHeaderRow does not exist.\";\n    var n, r, i, o;\n    if (this.printingHeaderRow = !0, void 0 !== l) {\n      var a = l(this, P);\n      p(a[0], a[1], a[2], a[3], -1);\n    }\n    this.setFontStyle(\"bold\");\n    var s = [];\n    for (i = 0, o = this.tableHeaderRow.length; i < o; i += 1) this.setFillColor(200, 200, 200), n = this.tableHeaderRow[i], e && (this.margins.top = 13, n[1] = this.margins && this.margins.top || 0, s.push(n)), r = [].concat(n), this.cell.apply(this, r.concat(t));\n    0 < s.length && this.setTableHeaderRow(s), this.setFontStyle(\"normal\"), this.printingHeaderRow = !1;\n  },\n  /**\n     * jsPDF Context2D PlugIn Copyright (c) 2014 Steven Spungin (TwelveTone LLC) <EMAIL>\n     *\n     * Licensed under the MIT License. http://opensource.org/licenses/mit-license\n     */\n  function (t, e) {\n    var l,\n      i,\n      o,\n      h,\n      u,\n      c = function (t) {\n        return t = t || {}, this.isStrokeTransparent = t.isStrokeTransparent || !1, this.strokeOpacity = t.strokeOpacity || 1, this.strokeStyle = t.strokeStyle || \"#000000\", this.fillStyle = t.fillStyle || \"#000000\", this.isFillTransparent = t.isFillTransparent || !1, this.fillOpacity = t.fillOpacity || 1, this.font = t.font || \"10px sans-serif\", this.textBaseline = t.textBaseline || \"alphabetic\", this.textAlign = t.textAlign || \"left\", this.lineWidth = t.lineWidth || 1, this.lineJoin = t.lineJoin || \"miter\", this.lineCap = t.lineCap || \"butt\", this.path = t.path || [], this.transform = void 0 !== t.transform ? t.transform.clone() : new M(), this.globalCompositeOperation = t.globalCompositeOperation || \"normal\", this.globalAlpha = t.globalAlpha || 1, this.clip_path = t.clip_path || [], this.currentPoint = t.currentPoint || new j(), this.miterLimit = t.miterLimit || 10, this.lastPoint = t.lastPoint || new j(), this.ignoreClearRect = \"boolean\" != typeof t.ignoreClearRect || t.ignoreClearRect, this;\n      };\n    t.events.push([\"initialized\", function () {\n      this.context2d = new n(this), l = this.internal.f2, this.internal.f3, i = this.internal.getCoordinateString, o = this.internal.getVerticalCoordinateString, h = this.internal.getHorizontalCoordinate, u = this.internal.getVerticalCoordinate;\n    }]);\n    var n = function (t) {\n      Object.defineProperty(this, \"canvas\", {\n        get: function () {\n          return {\n            parentNode: !1,\n            style: !1\n          };\n        }\n      }), Object.defineProperty(this, \"pdf\", {\n        get: function () {\n          return t;\n        }\n      });\n      var e = !1;\n      Object.defineProperty(this, \"pageWrapXEnabled\", {\n        get: function () {\n          return e;\n        },\n        set: function (t) {\n          e = Boolean(t);\n        }\n      });\n      var n = !1;\n      Object.defineProperty(this, \"pageWrapYEnabled\", {\n        get: function () {\n          return n;\n        },\n        set: function (t) {\n          n = Boolean(t);\n        }\n      });\n      var r = 0;\n      Object.defineProperty(this, \"posX\", {\n        get: function () {\n          return r;\n        },\n        set: function (t) {\n          isNaN(t) || (r = t);\n        }\n      });\n      var i = 0;\n      Object.defineProperty(this, \"posY\", {\n        get: function () {\n          return i;\n        },\n        set: function (t) {\n          isNaN(t) || (i = t);\n        }\n      });\n      var o = !1;\n      Object.defineProperty(this, \"autoPaging\", {\n        get: function () {\n          return o;\n        },\n        set: function (t) {\n          o = Boolean(t);\n        }\n      });\n      var a = 0;\n      Object.defineProperty(this, \"lastBreak\", {\n        get: function () {\n          return a;\n        },\n        set: function (t) {\n          a = t;\n        }\n      });\n      var s = [];\n      Object.defineProperty(this, \"pageBreaks\", {\n        get: function () {\n          return s;\n        },\n        set: function (t) {\n          s = t;\n        }\n      });\n      var l = new c();\n      Object.defineProperty(this, \"ctx\", {\n        get: function () {\n          return l;\n        },\n        set: function (t) {\n          t instanceof c && (l = t);\n        }\n      }), Object.defineProperty(this, \"path\", {\n        get: function () {\n          return l.path;\n        },\n        set: function (t) {\n          l.path = t;\n        }\n      });\n      var h = [];\n      Object.defineProperty(this, \"ctxStack\", {\n        get: function () {\n          return h;\n        },\n        set: function (t) {\n          h = t;\n        }\n      }), Object.defineProperty(this, \"fillStyle\", {\n        get: function () {\n          return this.ctx.fillStyle;\n        },\n        set: function (t) {\n          var e;\n          e = f(t), this.ctx.fillStyle = e.style, this.ctx.isFillTransparent = 0 === e.a, this.ctx.fillOpacity = e.a, this.pdf.setFillColor(e.r, e.g, e.b, {\n            a: e.a\n          }), this.pdf.setTextColor(e.r, e.g, e.b, {\n            a: e.a\n          });\n        }\n      }), Object.defineProperty(this, \"strokeStyle\", {\n        get: function () {\n          return this.ctx.strokeStyle;\n        },\n        set: function (t) {\n          var e = f(t);\n          this.ctx.strokeStyle = e.style, this.ctx.isStrokeTransparent = 0 === e.a, this.ctx.strokeOpacity = e.a, 0 === e.a ? this.pdf.setDrawColor(255, 255, 255) : (e.a, this.pdf.setDrawColor(e.r, e.g, e.b));\n        }\n      }), Object.defineProperty(this, \"lineCap\", {\n        get: function () {\n          return this.ctx.lineCap;\n        },\n        set: function (t) {\n          -1 !== [\"butt\", \"round\", \"square\"].indexOf(t) && (this.ctx.lineCap = t, this.pdf.setLineCap(t));\n        }\n      }), Object.defineProperty(this, \"lineWidth\", {\n        get: function () {\n          return this.ctx.lineWidth;\n        },\n        set: function (t) {\n          isNaN(t) || (this.ctx.lineWidth = t, this.pdf.setLineWidth(t));\n        }\n      }), Object.defineProperty(this, \"lineJoin\", {\n        get: function () {\n          return this.ctx.lineJoin;\n        },\n        set: function (t) {\n          -1 !== [\"bevel\", \"round\", \"miter\"].indexOf(t) && (this.ctx.lineJoin = t, this.pdf.setLineJoin(t));\n        }\n      }), Object.defineProperty(this, \"miterLimit\", {\n        get: function () {\n          return this.ctx.miterLimit;\n        },\n        set: function (t) {\n          isNaN(t) || (this.ctx.miterLimit = t, this.pdf.setMiterLimit(t));\n        }\n      }), Object.defineProperty(this, \"textBaseline\", {\n        get: function () {\n          return this.ctx.textBaseline;\n        },\n        set: function (t) {\n          this.ctx.textBaseline = t;\n        }\n      }), Object.defineProperty(this, \"textAlign\", {\n        get: function () {\n          return this.ctx.textAlign;\n        },\n        set: function (t) {\n          -1 !== [\"right\", \"end\", \"center\", \"left\", \"start\"].indexOf(t) && (this.ctx.textAlign = t);\n        }\n      }), Object.defineProperty(this, \"font\", {\n        get: function () {\n          return this.ctx.font;\n        },\n        set: function (t) {\n          var e;\n          if (this.ctx.font = t, null !== (e = /^\\s*(?=(?:(?:[-a-z]+\\s*){0,2}(italic|oblique))?)(?=(?:(?:[-a-z]+\\s*){0,2}(small-caps))?)(?=(?:(?:[-a-z]+\\s*){0,2}(bold(?:er)?|lighter|[1-9]00))?)(?:(?:normal|\\1|\\2|\\3)\\s*){0,3}((?:xx?-)?(?:small|large)|medium|smaller|larger|[.\\d]+(?:\\%|in|[cem]m|ex|p[ctx]))(?:\\s*\\/\\s*(normal|[.\\d]+(?:\\%|in|[cem]m|ex|p[ctx])))?\\s*([-_,\\\"\\'\\sa-z]+?)\\s*$/i.exec(t))) {\n            var n = e[1],\n              r = (e[2], e[3]),\n              i = e[4],\n              o = e[5],\n              a = e[6];\n            i = \"px\" === o ? Math.floor(parseFloat(i)) : \"em\" === o ? Math.floor(parseFloat(i) * this.pdf.getFontSize()) : Math.floor(parseFloat(i)), this.pdf.setFontSize(i);\n            var s = \"\";\n            (\"bold\" === r || 700 <= parseInt(r, 10) || \"bold\" === n) && (s = \"bold\"), \"italic\" === n && (s += \"italic\"), 0 === s.length && (s = \"normal\");\n            for (var l = \"\", h = a.toLowerCase().replace(/\"|'/g, \"\").split(/\\s*,\\s*/), u = {\n                arial: \"Helvetica\",\n                verdana: \"Helvetica\",\n                helvetica: \"Helvetica\",\n                \"sans-serif\": \"Helvetica\",\n                fixed: \"Courier\",\n                monospace: \"Courier\",\n                terminal: \"Courier\",\n                courier: \"Courier\",\n                times: \"Times\",\n                cursive: \"Times\",\n                fantasy: \"Times\",\n                serif: \"Times\"\n              }, c = 0; c < h.length; c++) {\n              if (void 0 !== this.pdf.internal.getFont(h[c], s, {\n                noFallback: !0,\n                disableWarning: !0\n              })) {\n                l = h[c];\n                break;\n              }\n              if (\"bolditalic\" === s && void 0 !== this.pdf.internal.getFont(h[c], \"bold\", {\n                noFallback: !0,\n                disableWarning: !0\n              })) l = h[c], s = \"bold\";else if (void 0 !== this.pdf.internal.getFont(h[c], \"normal\", {\n                noFallback: !0,\n                disableWarning: !0\n              })) {\n                l = h[c], s = \"normal\";\n                break;\n              }\n            }\n            if (\"\" === l) for (c = 0; c < h.length; c++) if (u[h[c]]) {\n              l = u[h[c]];\n              break;\n            }\n            l = \"\" === l ? \"Times\" : l, this.pdf.setFont(l, s);\n          }\n        }\n      }), Object.defineProperty(this, \"globalCompositeOperation\", {\n        get: function () {\n          return this.ctx.globalCompositeOperation;\n        },\n        set: function (t) {\n          this.ctx.globalCompositeOperation = t;\n        }\n      }), Object.defineProperty(this, \"globalAlpha\", {\n        get: function () {\n          return this.ctx.globalAlpha;\n        },\n        set: function (t) {\n          this.ctx.globalAlpha = t;\n        }\n      }), Object.defineProperty(this, \"ignoreClearRect\", {\n        get: function () {\n          return this.ctx.ignoreClearRect;\n        },\n        set: function (t) {\n          this.ctx.ignoreClearRect = Boolean(t);\n        }\n      });\n    };\n    n.prototype.fill = function () {\n      r.call(this, \"fill\", !1);\n    }, n.prototype.stroke = function () {\n      r.call(this, \"stroke\", !1);\n    }, n.prototype.beginPath = function () {\n      this.path = [{\n        type: \"begin\"\n      }];\n    }, n.prototype.moveTo = function (t, e) {\n      if (isNaN(t) || isNaN(e)) throw console.error(\"jsPDF.context2d.moveTo: Invalid arguments\", arguments), new Error(\"Invalid arguments passed to jsPDF.context2d.moveTo\");\n      var n = this.ctx.transform.applyToPoint(new j(t, e));\n      this.path.push({\n        type: \"mt\",\n        x: n.x,\n        y: n.y\n      }), this.ctx.lastPoint = new j(t, e);\n    }, n.prototype.closePath = function () {\n      var t = new j(0, 0),\n        e = 0;\n      for (e = this.path.length - 1; -1 !== e; e--) if (\"begin\" === this.path[e].type && \"object\" === se(this.path[e + 1]) && \"number\" == typeof this.path[e + 1].x) {\n        t = new j(this.path[e + 1].x, this.path[e + 1].y), this.path.push({\n          type: \"lt\",\n          x: t.x,\n          y: t.y\n        });\n        break;\n      }\n      \"object\" === se(this.path[e + 2]) && \"number\" == typeof this.path[e + 2].x && this.path.push(JSON.parse(JSON.stringify(this.path[e + 2]))), this.path.push({\n        type: \"close\"\n      }), this.ctx.lastPoint = new j(t.x, t.y);\n    }, n.prototype.lineTo = function (t, e) {\n      if (isNaN(t) || isNaN(e)) throw console.error(\"jsPDF.context2d.lineTo: Invalid arguments\", arguments), new Error(\"Invalid arguments passed to jsPDF.context2d.lineTo\");\n      var n = this.ctx.transform.applyToPoint(new j(t, e));\n      this.path.push({\n        type: \"lt\",\n        x: n.x,\n        y: n.y\n      }), this.ctx.lastPoint = new j(n.x, n.y);\n    }, n.prototype.clip = function () {\n      this.ctx.clip_path = JSON.parse(JSON.stringify(this.path)), r.call(this, null, !0);\n    }, n.prototype.quadraticCurveTo = function (t, e, n, r) {\n      if (isNaN(n) || isNaN(r) || isNaN(t) || isNaN(e)) throw console.error(\"jsPDF.context2d.quadraticCurveTo: Invalid arguments\", arguments), new Error(\"Invalid arguments passed to jsPDF.context2d.quadraticCurveTo\");\n      var i = this.ctx.transform.applyToPoint(new j(n, r)),\n        o = this.ctx.transform.applyToPoint(new j(t, e));\n      this.path.push({\n        type: \"qct\",\n        x1: o.x,\n        y1: o.y,\n        x: i.x,\n        y: i.y\n      }), this.ctx.lastPoint = new j(i.x, i.y);\n    }, n.prototype.bezierCurveTo = function (t, e, n, r, i, o) {\n      if (isNaN(i) || isNaN(o) || isNaN(t) || isNaN(e) || isNaN(n) || isNaN(r)) throw console.error(\"jsPDF.context2d.bezierCurveTo: Invalid arguments\", arguments), new Error(\"Invalid arguments passed to jsPDF.context2d.bezierCurveTo\");\n      var a = this.ctx.transform.applyToPoint(new j(i, o)),\n        s = this.ctx.transform.applyToPoint(new j(t, e)),\n        l = this.ctx.transform.applyToPoint(new j(n, r));\n      this.path.push({\n        type: \"bct\",\n        x1: s.x,\n        y1: s.y,\n        x2: l.x,\n        y2: l.y,\n        x: a.x,\n        y: a.y\n      }), this.ctx.lastPoint = new j(a.x, a.y);\n    }, n.prototype.arc = function (t, e, n, r, i, o) {\n      if (isNaN(t) || isNaN(e) || isNaN(n) || isNaN(r) || isNaN(i)) throw console.error(\"jsPDF.context2d.arc: Invalid arguments\", arguments), new Error(\"Invalid arguments passed to jsPDF.context2d.arc\");\n      if (o = Boolean(o), !this.ctx.transform.isIdentity) {\n        var a = this.ctx.transform.applyToPoint(new j(t, e));\n        t = a.x, e = a.y;\n        var s = this.ctx.transform.applyToPoint(new j(0, n)),\n          l = this.ctx.transform.applyToPoint(new j(0, 0));\n        n = Math.sqrt(Math.pow(s.x - l.x, 2) + Math.pow(s.y - l.y, 2));\n      }\n      Math.abs(i - r) >= 2 * Math.PI && (r = 0, i = 2 * Math.PI), this.path.push({\n        type: \"arc\",\n        x: t,\n        y: e,\n        radius: n,\n        startAngle: r,\n        endAngle: i,\n        counterclockwise: o\n      });\n    }, n.prototype.arcTo = function (t, e, n, r, i) {\n      throw new Error(\"arcTo not implemented.\");\n    }, n.prototype.rect = function (t, e, n, r) {\n      if (isNaN(t) || isNaN(e) || isNaN(n) || isNaN(r)) throw console.error(\"jsPDF.context2d.rect: Invalid arguments\", arguments), new Error(\"Invalid arguments passed to jsPDF.context2d.rect\");\n      this.moveTo(t, e), this.lineTo(t + n, e), this.lineTo(t + n, e + r), this.lineTo(t, e + r), this.lineTo(t, e), this.lineTo(t + n, e), this.lineTo(t, e);\n    }, n.prototype.fillRect = function (t, e, n, r) {\n      if (isNaN(t) || isNaN(e) || isNaN(n) || isNaN(r)) throw console.error(\"jsPDF.context2d.fillRect: Invalid arguments\", arguments), new Error(\"Invalid arguments passed to jsPDF.context2d.fillRect\");\n      if (!N.call(this)) {\n        var i = {};\n        \"butt\" !== this.lineCap && (i.lineCap = this.lineCap, this.lineCap = \"butt\"), \"miter\" !== this.lineJoin && (i.lineJoin = this.lineJoin, this.lineJoin = \"miter\"), this.beginPath(), this.rect(t, e, n, r), this.fill(), i.hasOwnProperty(\"lineCap\") && (this.lineCap = i.lineCap), i.hasOwnProperty(\"lineJoin\") && (this.lineJoin = i.lineJoin);\n      }\n    }, n.prototype.strokeRect = function (t, e, n, r) {\n      if (isNaN(t) || isNaN(e) || isNaN(n) || isNaN(r)) throw console.error(\"jsPDF.context2d.strokeRect: Invalid arguments\", arguments), new Error(\"Invalid arguments passed to jsPDF.context2d.strokeRect\");\n      L.call(this) || (this.beginPath(), this.rect(t, e, n, r), this.stroke());\n    }, n.prototype.clearRect = function (t, e, n, r) {\n      if (isNaN(t) || isNaN(e) || isNaN(n) || isNaN(r)) throw console.error(\"jsPDF.context2d.clearRect: Invalid arguments\", arguments), new Error(\"Invalid arguments passed to jsPDF.context2d.clearRect\");\n      this.ignoreClearRect || (this.fillStyle = \"#ffffff\", this.fillRect(t, e, n, r));\n    }, n.prototype.save = function (t) {\n      t = \"boolean\" != typeof t || t;\n      for (var e = this.pdf.internal.getCurrentPageInfo().pageNumber, n = 0; n < this.pdf.internal.getNumberOfPages(); n++) this.pdf.setPage(n + 1), this.pdf.internal.out(\"q\");\n      if (this.pdf.setPage(e), t) {\n        this.ctx.fontSize = this.pdf.internal.getFontSize();\n        var r = new c(this.ctx);\n        this.ctxStack.push(this.ctx), this.ctx = r;\n      }\n    }, n.prototype.restore = function (t) {\n      t = \"boolean\" != typeof t || t;\n      for (var e = this.pdf.internal.getCurrentPageInfo().pageNumber, n = 0; n < this.pdf.internal.getNumberOfPages(); n++) this.pdf.setPage(n + 1), this.pdf.internal.out(\"Q\");\n      this.pdf.setPage(e), t && 0 !== this.ctxStack.length && (this.ctx = this.ctxStack.pop(), this.fillStyle = this.ctx.fillStyle, this.strokeStyle = this.ctx.strokeStyle, this.font = this.ctx.font, this.lineCap = this.ctx.lineCap, this.lineWidth = this.ctx.lineWidth, this.lineJoin = this.ctx.lineJoin);\n    }, n.prototype.toDataURL = function () {\n      throw new Error(\"toDataUrl not implemented.\");\n    };\n    var f = function (t) {\n        var e, n, r, i;\n        if (!0 === t.isCanvasGradient && (t = t.getColor()), !t) return {\n          r: 0,\n          g: 0,\n          b: 0,\n          a: 0,\n          style: t\n        };\n        if (/transparent|rgba\\s*\\(\\s*(\\d+)\\s*,\\s*(\\d+)\\s*,\\s*(\\d+)\\s*,\\s*0+\\s*\\)/.test(t)) i = r = n = e = 0;else {\n          var o = /rgb\\s*\\(\\s*(\\d+)\\s*,\\s*(\\d+)\\s*,\\s*(\\d+)\\s*\\)/.exec(t);\n          if (null !== o) e = parseInt(o[1]), n = parseInt(o[2]), r = parseInt(o[3]), i = 1;else if (null !== (o = /rgba\\s*\\(\\s*(\\d+)\\s*,\\s*(\\d+)\\s*,\\s*(\\d+)\\s*,\\s*([\\d\\.]+)\\s*\\)/.exec(t))) e = parseInt(o[1]), n = parseInt(o[2]), r = parseInt(o[3]), i = parseFloat(o[4]);else {\n            if (i = 1, \"string\" == typeof t && \"#\" !== t.charAt(0)) {\n              var a = new RGBColor(t);\n              t = a.ok ? a.toHex() : \"#000000\";\n            }\n            4 === t.length ? (e = t.substring(1, 2), e += e, n = t.substring(2, 3), n += n, r = t.substring(3, 4), r += r) : (e = t.substring(1, 3), n = t.substring(3, 5), r = t.substring(5, 7)), e = parseInt(e, 16), n = parseInt(n, 16), r = parseInt(r, 16);\n          }\n        }\n        return {\n          r: e,\n          g: n,\n          b: r,\n          a: i,\n          style: t\n        };\n      },\n      N = function () {\n        return this.ctx.isFillTransparent || 0 == this.globalAlpha;\n      },\n      L = function () {\n        return Boolean(this.ctx.isStrokeTransparent || 0 == this.globalAlpha);\n      };\n    n.prototype.fillText = function (t, e, n, r) {\n      if (isNaN(e) || isNaN(n) || \"string\" != typeof t) throw console.error(\"jsPDF.context2d.fillText: Invalid arguments\", arguments), new Error(\"Invalid arguments passed to jsPDF.context2d.fillText\");\n      if (r = isNaN(r) ? void 0 : r, !N.call(this)) {\n        n = a.call(this, n);\n        var i = B(this.ctx.transform.rotation),\n          o = this.ctx.transform.scaleX;\n        s.call(this, {\n          text: t,\n          x: e,\n          y: n,\n          scale: o,\n          angle: i,\n          align: this.textAlign,\n          maxWidth: r\n        });\n      }\n    }, n.prototype.strokeText = function (t, e, n, r) {\n      if (isNaN(e) || isNaN(n) || \"string\" != typeof t) throw console.error(\"jsPDF.context2d.strokeText: Invalid arguments\", arguments), new Error(\"Invalid arguments passed to jsPDF.context2d.strokeText\");\n      if (!L.call(this)) {\n        r = isNaN(r) ? void 0 : r, n = a.call(this, n);\n        var i = B(this.ctx.transform.rotation),\n          o = this.ctx.transform.scaleX;\n        s.call(this, {\n          text: t,\n          x: e,\n          y: n,\n          scale: o,\n          renderingMode: \"stroke\",\n          angle: i,\n          align: this.textAlign,\n          maxWidth: r\n        });\n      }\n    }, n.prototype.measureText = function (t) {\n      if (\"string\" != typeof t) throw console.error(\"jsPDF.context2d.measureText: Invalid arguments\", arguments), new Error(\"Invalid arguments passed to jsPDF.context2d.measureText\");\n      var e = this.pdf,\n        n = this.pdf.internal.scaleFactor,\n        r = e.internal.getFontSize(),\n        i = e.getStringUnitWidth(t) * r / e.internal.scaleFactor;\n      return new function (t) {\n        var e = (t = t || {}).width || 0;\n        return Object.defineProperty(this, \"width\", {\n          get: function () {\n            return e;\n          }\n        }), this;\n      }({\n        width: i *= Math.round(96 * n / 72 * 1e4) / 1e4\n      });\n    }, n.prototype.scale = function (t, e) {\n      if (isNaN(t) || isNaN(e)) throw console.error(\"jsPDF.context2d.scale: Invalid arguments\", arguments), new Error(\"Invalid arguments passed to jsPDF.context2d.scale\");\n      var n = new M(t, 0, 0, e, 0, 0);\n      this.ctx.transform = this.ctx.transform.multiply(n);\n    }, n.prototype.rotate = function (t) {\n      if (isNaN(t)) throw console.error(\"jsPDF.context2d.rotate: Invalid arguments\", arguments), new Error(\"Invalid arguments passed to jsPDF.context2d.rotate\");\n      var e = new M(Math.cos(t), Math.sin(t), -Math.sin(t), Math.cos(t), 0, 0);\n      this.ctx.transform = this.ctx.transform.multiply(e);\n    }, n.prototype.translate = function (t, e) {\n      if (isNaN(t) || isNaN(e)) throw console.error(\"jsPDF.context2d.translate: Invalid arguments\", arguments), new Error(\"Invalid arguments passed to jsPDF.context2d.translate\");\n      var n = new M(1, 0, 0, 1, t, e);\n      this.ctx.transform = this.ctx.transform.multiply(n);\n    }, n.prototype.transform = function (t, e, n, r, i, o) {\n      if (isNaN(t) || isNaN(e) || isNaN(n) || isNaN(r) || isNaN(i) || isNaN(o)) throw console.error(\"jsPDF.context2d.transform: Invalid arguments\", arguments), new Error(\"Invalid arguments passed to jsPDF.context2d.transform\");\n      var a = new M(t, e, n, r, i, o);\n      this.ctx.transform = this.ctx.transform.multiply(a);\n    }, n.prototype.setTransform = function (t, e, n, r, i, o) {\n      t = isNaN(t) ? 1 : t, e = isNaN(e) ? 0 : e, n = isNaN(n) ? 0 : n, r = isNaN(r) ? 1 : r, i = isNaN(i) ? 0 : i, o = isNaN(o) ? 0 : o, this.ctx.transform = new M(t, e, n, r, i, o);\n    }, n.prototype.drawImage = function (t, e, n, r, i, o, a, s, l) {\n      var h = this.pdf.getImageProperties(t),\n        u = 1,\n        c = 1,\n        f = 1,\n        p = 1;\n      void 0 !== r && void 0 !== s && (f = s / r, p = l / i, u = h.width / r * s / r, c = h.height / i * l / i), void 0 === o && (o = e, a = n, n = e = 0), void 0 !== r && void 0 === s && (s = r, l = i), void 0 === r && void 0 === s && (s = h.width, l = h.height);\n      var d = this.ctx.transform.decompose(),\n        g = B(d.rotate.shx);\n      d.scale.sx, d.scale.sy;\n      for (var m, y = new M(), v = ((y = (y = (y = y.multiply(d.translate)).multiply(d.skew)).multiply(d.scale)).applyToPoint(new j(s, l)), y.applyToRectangle(new E(o - e * f, a - n * p, r * u, i * c))), w = F.call(this, v), b = [], x = 0; x < w.length; x += 1) -1 === b.indexOf(w[x]) && b.push(w[x]);\n      if (b.sort(), this.autoPaging) for (var N = b[0], L = b[b.length - 1], A = N; A < L + 1; A++) {\n        if (this.pdf.setPage(A), 0 !== this.ctx.clip_path.length) {\n          var S = this.path;\n          m = JSON.parse(JSON.stringify(this.ctx.clip_path)), this.path = P(m, this.posX, -1 * this.pdf.internal.pageSize.height * (A - 1) + this.posY), k.call(this, \"fill\", !0), this.path = S;\n        }\n        var _ = JSON.parse(JSON.stringify(v));\n        _ = P([_], this.posX, -1 * this.pdf.internal.pageSize.height * (A - 1) + this.posY)[0], this.pdf.addImage(t, \"jpg\", _.x, _.y, _.w, _.h, null, null, g);\n      } else this.pdf.addImage(t, \"jpg\", v.x, v.y, v.w, v.h, null, null, g);\n    };\n    var F = function (t, e, n) {\n        var r = [];\n        switch (e = e || this.pdf.internal.pageSize.width, n = n || this.pdf.internal.pageSize.height, t.type) {\n          default:\n          case \"mt\":\n          case \"lt\":\n            r.push(Math.floor((t.y + this.posY) / n) + 1);\n            break;\n          case \"arc\":\n            r.push(Math.floor((t.y + this.posY - t.radius) / n) + 1), r.push(Math.floor((t.y + this.posY + t.radius) / n) + 1);\n            break;\n          case \"qct\":\n            var i = w(this.ctx.lastPoint.x, this.ctx.lastPoint.y, t.x1, t.y1, t.x, t.y);\n            r.push(Math.floor(i.y / n) + 1), r.push(Math.floor((i.y + i.h) / n) + 1);\n            break;\n          case \"bct\":\n            var o = b(this.ctx.lastPoint.x, this.ctx.lastPoint.y, t.x1, t.y1, t.x2, t.y2, t.x, t.y);\n            r.push(Math.floor(o.y / n) + 1), r.push(Math.floor((o.y + o.h) / n) + 1);\n            break;\n          case \"rect\":\n            r.push(Math.floor((t.y + this.posY) / n) + 1), r.push(Math.floor((t.y + t.h + this.posY) / n) + 1);\n        }\n        for (var a = 0; a < r.length; a += 1) for (; this.pdf.internal.getNumberOfPages() < r[a];) v.call(this);\n        return r;\n      },\n      v = function () {\n        var t = this.fillStyle,\n          e = this.strokeStyle,\n          n = this.font,\n          r = this.lineCap,\n          i = this.lineWidth,\n          o = this.lineJoin;\n        this.pdf.addPage(), this.fillStyle = t, this.strokeStyle = e, this.font = n, this.lineCap = r, this.lineWidth = i, this.lineJoin = o;\n      },\n      P = function (t, e, n) {\n        for (var r = 0; r < t.length; r++) switch (t[r].type) {\n          case \"bct\":\n            t[r].x2 += e, t[r].y2 += n;\n          case \"qct\":\n            t[r].x1 += e, t[r].y1 += n;\n          case \"mt\":\n          case \"lt\":\n          case \"arc\":\n          default:\n            t[r].x += e, t[r].y += n;\n        }\n        return t;\n      },\n      r = function (t, e) {\n        for (var n, r, i = this.fillStyle, o = this.strokeStyle, a = (this.font, this.lineCap), s = this.lineWidth, l = this.lineJoin, h = JSON.parse(JSON.stringify(this.path)), u = JSON.parse(JSON.stringify(this.path)), c = [], f = 0; f < u.length; f++) if (void 0 !== u[f].x) for (var p = F.call(this, u[f]), d = 0; d < p.length; d += 1) -1 === c.indexOf(p[d]) && c.push(p[d]);\n        for (f = 0; f < c.length; f++) for (; this.pdf.internal.getNumberOfPages() < c[f];) v.call(this);\n        if (c.sort(), this.autoPaging) {\n          var g = c[0],\n            m = c[c.length - 1];\n          for (f = g; f < m + 1; f++) {\n            if (this.pdf.setPage(f), this.fillStyle = i, this.strokeStyle = o, this.lineCap = a, this.lineWidth = s, this.lineJoin = l, 0 !== this.ctx.clip_path.length) {\n              var y = this.path;\n              n = JSON.parse(JSON.stringify(this.ctx.clip_path)), this.path = P(n, this.posX, -1 * this.pdf.internal.pageSize.height * (f - 1) + this.posY), k.call(this, t, !0), this.path = y;\n            }\n            r = JSON.parse(JSON.stringify(h)), this.path = P(r, this.posX, -1 * this.pdf.internal.pageSize.height * (f - 1) + this.posY), !1 !== e && 0 !== f || k.call(this, t, e);\n          }\n        } else k.call(this, t, e);\n        this.path = h;\n      },\n      k = function (t, e) {\n        if ((\"stroke\" !== t || e || !L.call(this)) && (\"stroke\" === t || e || !N.call(this))) {\n          var n = [];\n          this.ctx.globalAlpha;\n          this.ctx.fillOpacity < 1 && this.ctx.fillOpacity;\n          for (var r, i = this.path, o = 0; o < i.length; o++) {\n            var a = i[o];\n            switch (a.type) {\n              case \"begin\":\n                n.push({\n                  begin: !0\n                });\n                break;\n              case \"close\":\n                n.push({\n                  close: !0\n                });\n                break;\n              case \"mt\":\n                n.push({\n                  start: a,\n                  deltas: [],\n                  abs: []\n                });\n                break;\n              case \"lt\":\n                var s = n.length;\n                if (!isNaN(i[o - 1].x)) {\n                  var l = [a.x - i[o - 1].x, a.y - i[o - 1].y];\n                  if (0 < s) for (; 0 <= s; s--) if (!0 !== n[s - 1].close && !0 !== n[s - 1].begin) {\n                    n[s - 1].deltas.push(l), n[s - 1].abs.push(a);\n                    break;\n                  }\n                }\n                break;\n              case \"bct\":\n                l = [a.x1 - i[o - 1].x, a.y1 - i[o - 1].y, a.x2 - i[o - 1].x, a.y2 - i[o - 1].y, a.x - i[o - 1].x, a.y - i[o - 1].y];\n                n[n.length - 1].deltas.push(l);\n                break;\n              case \"qct\":\n                var h = i[o - 1].x + 2 / 3 * (a.x1 - i[o - 1].x),\n                  u = i[o - 1].y + 2 / 3 * (a.y1 - i[o - 1].y),\n                  c = a.x + 2 / 3 * (a.x1 - a.x),\n                  f = a.y + 2 / 3 * (a.y1 - a.y),\n                  p = a.x,\n                  d = a.y;\n                l = [h - i[o - 1].x, u - i[o - 1].y, c - i[o - 1].x, f - i[o - 1].y, p - i[o - 1].x, d - i[o - 1].y];\n                n[n.length - 1].deltas.push(l);\n                break;\n              case \"arc\":\n                n.push({\n                  deltas: [],\n                  abs: [],\n                  arc: !0\n                }), Array.isArray(n[n.length - 1].abs) && n[n.length - 1].abs.push(a);\n            }\n          }\n          r = e ? null : \"stroke\" === t ? \"stroke\" : \"fill\";\n          for (o = 0; o < n.length; o++) {\n            if (n[o].arc) for (var g = n[o].abs, m = 0; m < g.length; m++) {\n              var y = g[m];\n              if (void 0 !== y.startAngle) {\n                var v = B(y.startAngle),\n                  w = B(y.endAngle),\n                  b = y.x,\n                  x = y.y;\n                A.call(this, b, x, y.radius, v, w, y.counterclockwise, r, e);\n              } else I.call(this, y.x, y.y);\n            }\n            if (!n[o].arc && !0 !== n[o].close && !0 !== n[o].begin) {\n              b = n[o].start.x, x = n[o].start.y;\n              C.call(this, n[o].deltas, b, x, null, null);\n            }\n          }\n          r && S.call(this, r), e && _.call(this);\n        }\n      },\n      a = function (t) {\n        var e = this.pdf.internal.getFontSize() / this.pdf.internal.scaleFactor,\n          n = e * (this.pdf.internal.getLineHeightFactor() - 1);\n        switch (this.ctx.textBaseline) {\n          case \"bottom\":\n            return t - n;\n          case \"top\":\n            return t + e - n;\n          case \"hanging\":\n            return t + e - 2 * n;\n          case \"middle\":\n            return t + e / 2 - n;\n          case \"ideographic\":\n            return t;\n          case \"alphabetic\":\n          default:\n            return t;\n        }\n      };\n    n.prototype.createLinearGradient = function () {\n      var t = function () {};\n      return t.colorStops = [], t.addColorStop = function (t, e) {\n        this.colorStops.push([t, e]);\n      }, t.getColor = function () {\n        return 0 === this.colorStops.length ? \"#000000\" : this.colorStops[0][1];\n      }, t.isCanvasGradient = !0, t;\n    }, n.prototype.createPattern = function () {\n      return this.createLinearGradient();\n    }, n.prototype.createRadialGradient = function () {\n      return this.createLinearGradient();\n    };\n    var A = function (t, e, n, r, i, o, a, s) {\n        this.pdf.internal.scaleFactor;\n        for (var l = y(r), h = y(i), u = g.call(this, n, l, h, o), c = 0; c < u.length; c++) {\n          var f = u[c];\n          0 === c && p.call(this, f.x1 + t, f.y1 + e), d.call(this, t, e, f.x2, f.y2, f.x3, f.y3, f.x4, f.y4);\n        }\n        s ? _.call(this) : S.call(this, a);\n      },\n      S = function (t) {\n        switch (t) {\n          case \"stroke\":\n            this.pdf.internal.out(\"S\");\n            break;\n          case \"fill\":\n            this.pdf.internal.out(\"f\");\n        }\n      },\n      _ = function () {\n        this.pdf.clip();\n      },\n      p = function (t, e) {\n        this.pdf.internal.out(i(t) + \" \" + o(e) + \" m\");\n      },\n      s = function (t) {\n        var e;\n        switch (t.align) {\n          case \"right\":\n          case \"end\":\n            e = \"right\";\n            break;\n          case \"center\":\n            e = \"center\";\n            break;\n          case \"left\":\n          case \"start\":\n          default:\n            e = \"left\";\n        }\n        var n = this.ctx.transform.applyToPoint(new j(t.x, t.y)),\n          r = this.ctx.transform.decompose(),\n          i = new M();\n        i = (i = (i = i.multiply(r.translate)).multiply(r.skew)).multiply(r.scale);\n        for (var o, a = this.pdf.getTextDimensions(t.text), s = this.ctx.transform.applyToRectangle(new E(t.x, t.y, a.w, a.h)), l = i.applyToRectangle(new E(t.x, t.y - a.h, a.w, a.h)), h = F.call(this, l), u = [], c = 0; c < h.length; c += 1) -1 === u.indexOf(h[c]) && u.push(h[c]);\n        if (u.sort(), !0 === this.autoPaging) for (var f = u[0], p = u[u.length - 1], d = f; d < p + 1; d++) {\n          if (this.pdf.setPage(d), 0 !== this.ctx.clip_path.length) {\n            var g = this.path;\n            o = JSON.parse(JSON.stringify(this.ctx.clip_path)), this.path = P(o, this.posX, -1 * this.pdf.internal.pageSize.height * (d - 1) + this.posY), k.call(this, \"fill\", !0), this.path = g;\n          }\n          var m = JSON.parse(JSON.stringify(s));\n          if (m = P([m], this.posX, -1 * this.pdf.internal.pageSize.height * (d - 1) + this.posY)[0], .01 <= t.scale) {\n            var y = this.pdf.internal.getFontSize();\n            this.pdf.setFontSize(y * t.scale);\n          }\n          this.pdf.text(t.text, m.x, m.y, {\n            angle: t.angle,\n            align: e,\n            renderingMode: t.renderingMode,\n            maxWidth: t.maxWidth\n          }), .01 <= t.scale && this.pdf.setFontSize(y);\n        } else {\n          if (.01 <= t.scale) {\n            y = this.pdf.internal.getFontSize();\n            this.pdf.setFontSize(y * t.scale);\n          }\n          this.pdf.text(t.text, n.x + this.posX, n.y + this.posY, {\n            angle: t.angle,\n            align: e,\n            renderingMode: t.renderingMode,\n            maxWidth: t.maxWidth\n          }), .01 <= t.scale && this.pdf.setFontSize(y);\n        }\n      },\n      I = function (t, e, n, r) {\n        n = n || 0, r = r || 0, this.pdf.internal.out(i(t + n) + \" \" + o(e + r) + \" l\");\n      },\n      C = function (t, e, n) {\n        return this.pdf.lines(t, e, n, null, null);\n      },\n      d = function (t, e, n, r, i, o, a, s) {\n        this.pdf.internal.out([l(h(n + t)), l(u(r + e)), l(h(i + t)), l(u(o + e)), l(h(a + t)), l(u(s + e)), \"c\"].join(\" \"));\n      },\n      g = function (t, e, n, r) {\n        var i = 2 * Math.PI,\n          o = e;\n        (o < i || i < o) && (o %= i);\n        var a = n;\n        (a < i || i < a) && (a %= i);\n        for (var s = [], l = Math.PI / 2, h = r ? -1 : 1, u = e, c = Math.min(i, Math.abs(a - o)); 1e-5 < c;) {\n          var f = u + h * Math.min(c, l);\n          s.push(m.call(this, t, u, f)), c -= Math.abs(f - u), u = f;\n        }\n        return s;\n      },\n      m = function (t, e, n) {\n        var r = (n - e) / 2,\n          i = t * Math.cos(r),\n          o = t * Math.sin(r),\n          a = i,\n          s = -o,\n          l = a * a + s * s,\n          h = l + a * i + s * o,\n          u = 4 / 3 * (Math.sqrt(2 * l * h) - h) / (a * o - s * i),\n          c = a - u * s,\n          f = s + u * a,\n          p = c,\n          d = -f,\n          g = r + e,\n          m = Math.cos(g),\n          y = Math.sin(g);\n        return {\n          x1: t * Math.cos(e),\n          y1: t * Math.sin(e),\n          x2: c * m - f * y,\n          y2: c * y + f * m,\n          x3: p * m - d * y,\n          y3: p * y + d * m,\n          x4: t * Math.cos(n),\n          y4: t * Math.sin(n)\n        };\n      },\n      B = function (t) {\n        return 180 * t / Math.PI;\n      },\n      y = function (t) {\n        return t * Math.PI / 180;\n      },\n      w = function (t, e, n, r, i, o) {\n        var a = t + .5 * (n - t),\n          s = e + .5 * (r - e),\n          l = i + .5 * (n - i),\n          h = o + .5 * (r - o),\n          u = Math.min(t, i, a, l),\n          c = Math.max(t, i, a, l),\n          f = Math.min(e, o, s, h),\n          p = Math.max(e, o, s, h);\n        return new E(u, f, c - u, p - f);\n      },\n      b = function (t, e, n, r, i, o, a, s) {\n        for (var l, h, u, c, f, p, d, g, m, y, v, w, b, x = n - t, N = r - e, L = i - n, A = o - r, S = a - i, _ = s - o, F = 0; F < 41; F++) g = (p = (h = t + (l = F / 40) * x) + l * ((c = n + l * L) - h)) + l * (c + l * (i + l * S - c) - p), m = (d = (u = e + l * N) + l * ((f = r + l * A) - u)) + l * (f + l * (o + l * _ - f) - d), b = 0 == F ? (w = y = g, v = m) : (y = Math.min(y, g), v = Math.min(v, m), w = Math.max(w, g), Math.max(b, m));\n        return new E(Math.round(y), Math.round(v), Math.round(w - y), Math.round(b - v));\n      },\n      j = function (t, e) {\n        var n = t || 0;\n        Object.defineProperty(this, \"x\", {\n          enumerable: !0,\n          get: function () {\n            return n;\n          },\n          set: function (t) {\n            isNaN(t) || (n = parseFloat(t));\n          }\n        });\n        var r = e || 0;\n        Object.defineProperty(this, \"y\", {\n          enumerable: !0,\n          get: function () {\n            return r;\n          },\n          set: function (t) {\n            isNaN(t) || (r = parseFloat(t));\n          }\n        });\n        var i = \"pt\";\n        return Object.defineProperty(this, \"type\", {\n          enumerable: !0,\n          get: function () {\n            return i;\n          },\n          set: function (t) {\n            i = t.toString();\n          }\n        }), this;\n      },\n      E = function (t, e, n, r) {\n        j.call(this, t, e), this.type = \"rect\";\n        var i = n || 0;\n        Object.defineProperty(this, \"w\", {\n          enumerable: !0,\n          get: function () {\n            return i;\n          },\n          set: function (t) {\n            isNaN(t) || (i = parseFloat(t));\n          }\n        });\n        var o = r || 0;\n        return Object.defineProperty(this, \"h\", {\n          enumerable: !0,\n          get: function () {\n            return o;\n          },\n          set: function (t) {\n            isNaN(t) || (o = parseFloat(t));\n          }\n        }), this;\n      },\n      M = function (t, e, n, r, i, o) {\n        var a = [];\n        return Object.defineProperty(this, \"sx\", {\n          get: function () {\n            return a[0];\n          },\n          set: function (t) {\n            a[0] = Math.round(1e5 * t) / 1e5;\n          }\n        }), Object.defineProperty(this, \"shy\", {\n          get: function () {\n            return a[1];\n          },\n          set: function (t) {\n            a[1] = Math.round(1e5 * t) / 1e5;\n          }\n        }), Object.defineProperty(this, \"shx\", {\n          get: function () {\n            return a[2];\n          },\n          set: function (t) {\n            a[2] = Math.round(1e5 * t) / 1e5;\n          }\n        }), Object.defineProperty(this, \"sy\", {\n          get: function () {\n            return a[3];\n          },\n          set: function (t) {\n            a[3] = Math.round(1e5 * t) / 1e5;\n          }\n        }), Object.defineProperty(this, \"tx\", {\n          get: function () {\n            return a[4];\n          },\n          set: function (t) {\n            a[4] = Math.round(1e5 * t) / 1e5;\n          }\n        }), Object.defineProperty(this, \"ty\", {\n          get: function () {\n            return a[5];\n          },\n          set: function (t) {\n            a[5] = Math.round(1e5 * t) / 1e5;\n          }\n        }), Object.defineProperty(this, \"rotation\", {\n          get: function () {\n            return Math.atan2(this.shx, this.sx);\n          }\n        }), Object.defineProperty(this, \"scaleX\", {\n          get: function () {\n            return this.decompose().scale.sx;\n          }\n        }), Object.defineProperty(this, \"scaleY\", {\n          get: function () {\n            return this.decompose().scale.sy;\n          }\n        }), Object.defineProperty(this, \"isIdentity\", {\n          get: function () {\n            return 1 === this.sx && 0 === this.shy && 0 === this.shx && 1 === this.sy && 0 === this.tx && 0 === this.ty;\n          }\n        }), this.sx = isNaN(t) ? 1 : t, this.shy = isNaN(e) ? 0 : e, this.shx = isNaN(n) ? 0 : n, this.sy = isNaN(r) ? 1 : r, this.tx = isNaN(i) ? 0 : i, this.ty = isNaN(o) ? 0 : o, this;\n      };\n    M.prototype.multiply = function (t) {\n      var e = t.sx * this.sx + t.shy * this.shx,\n        n = t.sx * this.shy + t.shy * this.sy,\n        r = t.shx * this.sx + t.sy * this.shx,\n        i = t.shx * this.shy + t.sy * this.sy,\n        o = t.tx * this.sx + t.ty * this.shx + this.tx,\n        a = t.tx * this.shy + t.ty * this.sy + this.ty;\n      return new M(e, n, r, i, o, a);\n    }, M.prototype.decompose = function () {\n      var t = this.sx,\n        e = this.shy,\n        n = this.shx,\n        r = this.sy,\n        i = this.tx,\n        o = this.ty,\n        a = Math.sqrt(t * t + e * e),\n        s = (t /= a) * n + (e /= a) * r;\n      n -= t * s, r -= e * s;\n      var l = Math.sqrt(n * n + r * r);\n      return s /= l, t * (r /= l) < e * (n /= l) && (t = -t, e = -e, s = -s, a = -a), {\n        scale: new M(a, 0, 0, l, 0, 0),\n        translate: new M(1, 0, 0, 1, i, o),\n        rotate: new M(t, e, -e, t, 0, 0),\n        skew: new M(1, 0, s, 1, 0, 0)\n      };\n    }, M.prototype.applyToPoint = function (t) {\n      var e = t.x * this.sx + t.y * this.shx + this.tx,\n        n = t.x * this.shy + t.y * this.sy + this.ty;\n      return new j(e, n);\n    }, M.prototype.applyToRectangle = function (t) {\n      var e = this.applyToPoint(t),\n        n = this.applyToPoint(new j(t.x + t.w, t.y + t.h));\n      return new E(e.x, e.y, n.x - e.x, n.y - e.y);\n    }, M.prototype.clone = function () {\n      var t = this.sx,\n        e = this.shy,\n        n = this.shx,\n        r = this.sy,\n        i = this.tx,\n        o = this.ty;\n      return new M(t, e, n, r, i, o);\n    };\n  }(lt.API, \"undefined\" != typeof self && self || \"undefined\" != typeof window && window || \"undefined\" != typeof global && global || Function('return typeof this === \"object\" && this.content')() || Function(\"return this\")()),\n  /**\n     * jsPDF filters PlugIn\n     * Copyright (c) 2014 Aras Abbasi \n     *\n     * Licensed under the MIT License.\n     * http://opensource.org/licenses/mit-license\n     */\n  a = lt.API, o = function (t) {\n    var r, e, n, i, o, a, s, l, h, u;\n    for (/[^\\x00-\\xFF]/.test(t), e = [], n = 0, i = (t += r = \"\\0\\0\\0\\0\".slice(t.length % 4 || 4)).length; n < i; n += 4) 0 !== (o = (t.charCodeAt(n) << 24) + (t.charCodeAt(n + 1) << 16) + (t.charCodeAt(n + 2) << 8) + t.charCodeAt(n + 3)) ? (a = (o = ((o = ((o = ((o = (o - (u = o % 85)) / 85) - (h = o % 85)) / 85) - (l = o % 85)) / 85) - (s = o % 85)) / 85) % 85, e.push(a + 33, s + 33, l + 33, h + 33, u + 33)) : e.push(122);\n    return function (t, e) {\n      for (var n = r.length; 0 < n; n--) t.pop();\n    }(e), String.fromCharCode.apply(String, e) + \"~>\";\n  }, s = function (t) {\n    var r,\n      e,\n      n,\n      i,\n      o,\n      a = String,\n      s = \"length\",\n      l = \"charCodeAt\",\n      h = \"slice\",\n      u = \"replace\";\n    for (t[h](-2), t = t[h](0, -2)[u](/\\s/g, \"\")[u](\"z\", \"!!!!!\"), n = [], i = 0, o = (t += r = \"uuuuu\"[h](t[s] % 5 || 5))[s]; i < o; i += 5) e = 52200625 * (t[l](i) - 33) + 614125 * (t[l](i + 1) - 33) + 7225 * (t[l](i + 2) - 33) + 85 * (t[l](i + 3) - 33) + (t[l](i + 4) - 33), n.push(255 & e >> 24, 255 & e >> 16, 255 & e >> 8, 255 & e);\n    return function (t, e) {\n      for (var n = r[s]; 0 < n; n--) t.pop();\n    }(n), a.fromCharCode.apply(a, n);\n  }, h = function (t) {\n    for (var e = \"\", n = 0; n < t.length; n += 1) e += (\"0\" + t.charCodeAt(n).toString(16)).slice(-2);\n    return e += \">\";\n  }, u = function (t) {\n    var e = new RegExp(/^([0-9A-Fa-f]{2})+$/);\n    if (-1 !== (t = t.replace(/\\s/g, \"\")).indexOf(\">\") && (t = t.substr(0, t.indexOf(\">\"))), t.length % 2 && (t += \"0\"), !1 === e.test(t)) return \"\";\n    for (var n = \"\", r = 0; r < t.length; r += 2) n += String.fromCharCode(\"0x\" + (t[r] + t[r + 1]));\n    return n;\n  }, c = function (t, e) {\n    e = Object.assign({\n      predictor: 1,\n      colors: 1,\n      bitsPerComponent: 8,\n      columns: 1\n    }, e);\n    for (var n, r, i = [], o = t.length; o--;) i[o] = t.charCodeAt(o);\n    return n = a.adler32cs.from(t), (r = new Deflater(6)).append(new Uint8Array(i)), t = r.flush(), (i = new Uint8Array(t.length + 6)).set(new Uint8Array([120, 156])), i.set(t, 2), i.set(new Uint8Array([255 & n, n >> 8 & 255, n >> 16 & 255, n >> 24 & 255]), t.length + 2), t = String.fromCharCode.apply(null, i);\n  }, a.processDataByFilters = function (t, e) {\n    var n = 0,\n      r = t || \"\",\n      i = [];\n    for (\"string\" == typeof (e = e || []) && (e = [e]), n = 0; n < e.length; n += 1) switch (e[n]) {\n      case \"ASCII85Decode\":\n      case \"/ASCII85Decode\":\n        r = s(r), i.push(\"/ASCII85Encode\");\n        break;\n      case \"ASCII85Encode\":\n      case \"/ASCII85Encode\":\n        r = o(r), i.push(\"/ASCII85Decode\");\n        break;\n      case \"ASCIIHexDecode\":\n      case \"/ASCIIHexDecode\":\n        r = u(r), i.push(\"/ASCIIHexEncode\");\n        break;\n      case \"ASCIIHexEncode\":\n      case \"/ASCIIHexEncode\":\n        r = h(r), i.push(\"/ASCIIHexDecode\");\n        break;\n      case \"FlateEncode\":\n      case \"/FlateEncode\":\n        r = c(r), i.push(\"/FlateDecode\");\n        break;\n      default:\n        throw 'The filter: \"' + e[n] + '\" is not implemented';\n    }\n    return {\n      data: r,\n      reverseChain: i.reverse().join(\" \")\n    };\n  }, (\n  /**\n     * jsPDF fileloading PlugIn\n     * Copyright (c) 2018 Aras Abbasi (<EMAIL>)\n     *\n     * Licensed under the MIT License.\n     * http://opensource.org/licenses/mit-license\n     */\n  r = lt.API).loadFile = function (t, e, n) {\n    var r;\n    e = e || !0, n = n || function () {};\n    try {\n      r = function (t, e, n) {\n        var r = new XMLHttpRequest(),\n          i = [],\n          o = 0,\n          a = function (t) {\n            var e = t.length,\n              n = String.fromCharCode;\n            for (o = 0; o < e; o += 1) i.push(n(255 & t.charCodeAt(o)));\n            return i.join(\"\");\n          };\n        if (r.open(\"GET\", t, !e), r.overrideMimeType(\"text/plain; charset=x-user-defined\"), !1 === e && (r.onload = function () {\n          return a(this.responseText);\n        }), r.send(null), 200 === r.status) return e ? a(r.responseText) : void 0;\n        console.warn('Unable to load file \"' + t + '\"');\n      }(t, e);\n    } catch (t) {\n      r = void 0;\n    }\n    return r;\n  }, r.loadImageFile = r.loadFile,\n  /**\n     * Copyright (c) 2018 Erik Koopmans\n     * Released under the MIT License.\n     *\n     * Licensed under the MIT License.\n     * http://opensource.org/licenses/mit-license\n     */\n  i = lt.API, f = \"undefined\" != typeof window && window || \"undefined\" != typeof global && global, g = function (t) {\n    var e = se(t);\n    return \"undefined\" === e ? \"undefined\" : \"string\" === e || t instanceof String ? \"string\" : \"number\" === e || t instanceof Number ? \"number\" : \"function\" === e || t instanceof Function ? \"function\" : t && t.constructor === Array ? \"array\" : t && 1 === t.nodeType ? \"element\" : \"object\" === e ? \"object\" : \"unknown\";\n  }, m = function (t, e) {\n    var n = document.createElement(t);\n    if (e.className && (n.className = e.className), e.innerHTML) {\n      n.innerHTML = e.innerHTML;\n      for (var r = n.getElementsByTagName(\"script\"), i = r.length; 0 < i--; null) r[i].parentNode.removeChild(r[i]);\n    }\n    for (var o in e.style) n.style[o] = e.style[o];\n    return n;\n  }, (((y = function t(e) {\n    var n = Object.assign(t.convert(Promise.resolve()), JSON.parse(JSON.stringify(t.template))),\n      r = t.convert(Promise.resolve(), n);\n    return r = (r = r.setProgress(1, t, 1, [t])).set(e);\n  }).prototype = Object.create(Promise.prototype)).constructor = y).convert = function (t, e) {\n    return t.__proto__ = e || y.prototype, t;\n  }, y.template = {\n    prop: {\n      src: null,\n      container: null,\n      overlay: null,\n      canvas: null,\n      img: null,\n      pdf: null,\n      pageSize: null,\n      callback: function () {}\n    },\n    progress: {\n      val: 0,\n      state: null,\n      n: 0,\n      stack: []\n    },\n    opt: {\n      filename: \"file.pdf\",\n      margin: [0, 0, 0, 0],\n      enableLinks: !0,\n      x: 0,\n      y: 0,\n      html2canvas: {},\n      jsPDF: {}\n    }\n  }, y.prototype.from = function (t, e) {\n    return this.then(function () {\n      switch (e = e || function (t) {\n        switch (g(t)) {\n          case \"string\":\n            return \"string\";\n          case \"element\":\n            return \"canvas\" === t.nodeName.toLowerCase ? \"canvas\" : \"element\";\n          default:\n            return \"unknown\";\n        }\n      }(t)) {\n        case \"string\":\n          return this.set({\n            src: m(\"div\", {\n              innerHTML: t\n            })\n          });\n        case \"element\":\n          return this.set({\n            src: t\n          });\n        case \"canvas\":\n          return this.set({\n            canvas: t\n          });\n        case \"img\":\n          return this.set({\n            img: t\n          });\n        default:\n          return this.error(\"Unknown source type.\");\n      }\n    });\n  }, y.prototype.to = function (t) {\n    switch (t) {\n      case \"container\":\n        return this.toContainer();\n      case \"canvas\":\n        return this.toCanvas();\n      case \"img\":\n        return this.toImg();\n      case \"pdf\":\n        return this.toPdf();\n      default:\n        return this.error(\"Invalid target.\");\n    }\n  }, y.prototype.toContainer = function () {\n    return this.thenList([function () {\n      return this.prop.src || this.error(\"Cannot duplicate - no source HTML.\");\n    }, function () {\n      return this.prop.pageSize || this.setPageSize();\n    }]).then(function () {\n      var t = {\n          position: \"relative\",\n          display: \"inline-block\",\n          width: Math.max(this.prop.src.clientWidth, this.prop.src.scrollWidth, this.prop.src.offsetWidth) + \"px\",\n          left: 0,\n          right: 0,\n          top: 0,\n          margin: \"auto\",\n          backgroundColor: \"white\"\n        },\n        e = function t(e, n) {\n          for (var r = 3 === e.nodeType ? document.createTextNode(e.nodeValue) : e.cloneNode(!1), i = e.firstChild; i; i = i.nextSibling) !0 !== n && 1 === i.nodeType && \"SCRIPT\" === i.nodeName || r.appendChild(t(i, n));\n          return 1 === e.nodeType && (\"CANVAS\" === e.nodeName ? (r.width = e.width, r.height = e.height, r.getContext(\"2d\").drawImage(e, 0, 0)) : \"TEXTAREA\" !== e.nodeName && \"SELECT\" !== e.nodeName || (r.value = e.value), r.addEventListener(\"load\", function () {\n            r.scrollTop = e.scrollTop, r.scrollLeft = e.scrollLeft;\n          }, !0)), r;\n        }(this.prop.src, this.opt.html2canvas.javascriptEnabled);\n      \"BODY\" === e.tagName && (t.height = Math.max(document.body.scrollHeight, document.body.offsetHeight, document.documentElement.clientHeight, document.documentElement.scrollHeight, document.documentElement.offsetHeight) + \"px\"), this.prop.overlay = m(\"div\", {\n        className: \"html2pdf__overlay\",\n        style: {\n          position: \"fixed\",\n          overflow: \"hidden\",\n          zIndex: 1e3,\n          left: \"-100000px\",\n          right: 0,\n          bottom: 0,\n          top: 0\n        }\n      }), this.prop.container = m(\"div\", {\n        className: \"html2pdf__container\",\n        style: t\n      }), this.prop.container.appendChild(e), this.prop.container.firstChild.appendChild(m(\"div\", {\n        style: {\n          clear: \"both\",\n          border: \"0 none transparent\",\n          margin: 0,\n          padding: 0,\n          height: 0\n        }\n      })), this.prop.container.style.float = \"none\", this.prop.overlay.appendChild(this.prop.container), document.body.appendChild(this.prop.overlay), this.prop.container.firstChild.style.position = \"relative\", this.prop.container.height = Math.max(this.prop.container.firstChild.clientHeight, this.prop.container.firstChild.scrollHeight, this.prop.container.firstChild.offsetHeight) + \"px\";\n    });\n  }, y.prototype.toCanvas = function () {\n    var t = [function () {\n      return document.body.contains(this.prop.container) || this.toContainer();\n    }];\n    return this.thenList(t).then(function () {\n      var t = Object.assign({}, this.opt.html2canvas);\n      if (delete t.onrendered, this.isHtml2CanvasLoaded()) return html2canvas(this.prop.container, t);\n    }).then(function (t) {\n      (this.opt.html2canvas.onrendered || function () {})(t), this.prop.canvas = t, document.body.removeChild(this.prop.overlay);\n    });\n  }, y.prototype.toContext2d = function () {\n    var t = [function () {\n      return document.body.contains(this.prop.container) || this.toContainer();\n    }];\n    return this.thenList(t).then(function () {\n      var t = this.opt.jsPDF,\n        e = Object.assign({\n          async: !0,\n          allowTaint: !0,\n          backgroundColor: \"#ffffff\",\n          imageTimeout: 15e3,\n          logging: !0,\n          proxy: null,\n          removeContainer: !0,\n          foreignObjectRendering: !1,\n          useCORS: !1\n        }, this.opt.html2canvas);\n      if (delete e.onrendered, t.context2d.autoPaging = !0, t.context2d.posX = this.opt.x, t.context2d.posY = this.opt.y, e.windowHeight = e.windowHeight || 0, e.windowHeight = 0 == e.windowHeight ? Math.max(this.prop.container.clientHeight, this.prop.container.scrollHeight, this.prop.container.offsetHeight) : e.windowHeight, this.isHtml2CanvasLoaded()) return html2canvas(this.prop.container, e);\n    }).then(function (t) {\n      (this.opt.html2canvas.onrendered || function () {})(t), this.prop.canvas = t, document.body.removeChild(this.prop.overlay);\n    });\n  }, y.prototype.toImg = function () {\n    return this.thenList([function () {\n      return this.prop.canvas || this.toCanvas();\n    }]).then(function () {\n      var t = this.prop.canvas.toDataURL(\"image/\" + this.opt.image.type, this.opt.image.quality);\n      this.prop.img = document.createElement(\"img\"), this.prop.img.src = t;\n    });\n  }, y.prototype.toPdf = function () {\n    return this.thenList([function () {\n      return this.toContext2d();\n    }]).then(function () {\n      this.prop.pdf = this.prop.pdf || this.opt.jsPDF;\n    });\n  }, y.prototype.output = function (t, e, n) {\n    return \"img\" === (n = n || \"pdf\").toLowerCase() || \"image\" === n.toLowerCase() ? this.outputImg(t, e) : this.outputPdf(t, e);\n  }, y.prototype.outputPdf = function (t, e) {\n    return this.thenList([function () {\n      return this.prop.pdf || this.toPdf();\n    }]).then(function () {\n      return this.prop.pdf.output(t, e);\n    });\n  }, y.prototype.outputImg = function (t, e) {\n    return this.thenList([function () {\n      return this.prop.img || this.toImg();\n    }]).then(function () {\n      switch (t) {\n        case void 0:\n        case \"img\":\n          return this.prop.img;\n        case \"datauristring\":\n        case \"dataurlstring\":\n          return this.prop.img.src;\n        case \"datauri\":\n        case \"dataurl\":\n          return document.location.href = this.prop.img.src;\n        default:\n          throw 'Image output type \"' + t + '\" is not supported.';\n      }\n    });\n  }, y.prototype.isHtml2CanvasLoaded = function () {\n    var t = void 0 !== f.html2canvas;\n    return t || console.error(\"html2canvas not loaded.\"), t;\n  }, y.prototype.save = function (t) {\n    if (this.isHtml2CanvasLoaded()) return this.thenList([function () {\n      return this.prop.pdf || this.toPdf();\n    }]).set(t ? {\n      filename: t\n    } : null).then(function () {\n      this.prop.pdf.save(this.opt.filename);\n    });\n  }, y.prototype.doCallback = function (t) {\n    if (this.isHtml2CanvasLoaded()) return this.thenList([function () {\n      return this.prop.pdf || this.toPdf();\n    }]).then(function () {\n      this.prop.callback(this.prop.pdf);\n    });\n  }, y.prototype.set = function (e) {\n    if (\"object\" !== g(e)) return this;\n    var t = Object.keys(e || {}).map(function (t) {\n      if (t in y.template.prop) return function () {\n        this.prop[t] = e[t];\n      };\n      switch (t) {\n        case \"margin\":\n          return this.setMargin.bind(this, e.margin);\n        case \"jsPDF\":\n          return function () {\n            return this.opt.jsPDF = e.jsPDF, this.setPageSize();\n          };\n        case \"pageSize\":\n          return this.setPageSize.bind(this, e.pageSize);\n        default:\n          return function () {\n            this.opt[t] = e[t];\n          };\n      }\n    }, this);\n    return this.then(function () {\n      return this.thenList(t);\n    });\n  }, y.prototype.get = function (e, n) {\n    return this.then(function () {\n      var t = e in y.template.prop ? this.prop[e] : this.opt[e];\n      return n ? n(t) : t;\n    });\n  }, y.prototype.setMargin = function (t) {\n    return this.then(function () {\n      switch (g(t)) {\n        case \"number\":\n          t = [t, t, t, t];\n        case \"array\":\n          if (2 === t.length && (t = [t[0], t[1], t[0], t[1]]), 4 === t.length) break;\n        default:\n          return this.error(\"Invalid margin array.\");\n      }\n      this.opt.margin = t;\n    }).then(this.setPageSize);\n  }, y.prototype.setPageSize = function (t) {\n    function e(t, e) {\n      return Math.floor(t * e / 72 * 96);\n    }\n    return this.then(function () {\n      (t = t || lt.getPageSize(this.opt.jsPDF)).hasOwnProperty(\"inner\") || (t.inner = {\n        width: t.width - this.opt.margin[1] - this.opt.margin[3],\n        height: t.height - this.opt.margin[0] - this.opt.margin[2]\n      }, t.inner.px = {\n        width: e(t.inner.width, t.k),\n        height: e(t.inner.height, t.k)\n      }, t.inner.ratio = t.inner.height / t.inner.width), this.prop.pageSize = t;\n    });\n  }, y.prototype.setProgress = function (t, e, n, r) {\n    return null != t && (this.progress.val = t), null != e && (this.progress.state = e), null != n && (this.progress.n = n), null != r && (this.progress.stack = r), this.progress.ratio = this.progress.val / this.progress.state, this;\n  }, y.prototype.updateProgress = function (t, e, n, r) {\n    return this.setProgress(t ? this.progress.val + t : null, e || null, n ? this.progress.n + n : null, r ? this.progress.stack.concat(r) : null);\n  }, y.prototype.then = function (t, e) {\n    var n = this;\n    return this.thenCore(t, e, function (e, t) {\n      return n.updateProgress(null, null, 1, [e]), Promise.prototype.then.call(this, function (t) {\n        return n.updateProgress(null, e), t;\n      }).then(e, t).then(function (t) {\n        return n.updateProgress(1), t;\n      });\n    });\n  }, y.prototype.thenCore = function (t, e, n) {\n    n = n || Promise.prototype.then;\n    var r = this;\n    t && (t = t.bind(r)), e && (e = e.bind(r));\n    var i = -1 !== Promise.toString().indexOf(\"[native code]\") && \"Promise\" === Promise.name ? r : y.convert(Object.assign({}, r), Promise.prototype),\n      o = n.call(i, t, e);\n    return y.convert(o, r.__proto__);\n  }, y.prototype.thenExternal = function (t, e) {\n    return Promise.prototype.then.call(this, t, e);\n  }, y.prototype.thenList = function (t) {\n    var e = this;\n    return t.forEach(function (t) {\n      e = e.thenCore(t);\n    }), e;\n  }, y.prototype.catch = function (t) {\n    t && (t = t.bind(this));\n    var e = Promise.prototype.catch.call(this, t);\n    return y.convert(e, this);\n  }, y.prototype.catchExternal = function (t) {\n    return Promise.prototype.catch.call(this, t);\n  }, y.prototype.error = function (t) {\n    return this.then(function () {\n      throw new Error(t);\n    });\n  }, y.prototype.using = y.prototype.set, y.prototype.saveAs = y.prototype.save, y.prototype.export = y.prototype.output, y.prototype.run = y.prototype.then, lt.getPageSize = function (t, e, n) {\n    if (\"object\" === se(t)) {\n      var r = t;\n      t = r.orientation, e = r.unit || e, n = r.format || n;\n    }\n    e = e || \"mm\", n = n || \"a4\", t = (\"\" + (t || \"P\")).toLowerCase();\n    var i = (\"\" + n).toLowerCase(),\n      o = {\n        a0: [2383.94, 3370.39],\n        a1: [1683.78, 2383.94],\n        a2: [1190.55, 1683.78],\n        a3: [841.89, 1190.55],\n        a4: [595.28, 841.89],\n        a5: [419.53, 595.28],\n        a6: [297.64, 419.53],\n        a7: [209.76, 297.64],\n        a8: [147.4, 209.76],\n        a9: [104.88, 147.4],\n        a10: [73.7, 104.88],\n        b0: [2834.65, 4008.19],\n        b1: [2004.09, 2834.65],\n        b2: [1417.32, 2004.09],\n        b3: [1000.63, 1417.32],\n        b4: [708.66, 1000.63],\n        b5: [498.9, 708.66],\n        b6: [354.33, 498.9],\n        b7: [249.45, 354.33],\n        b8: [175.75, 249.45],\n        b9: [124.72, 175.75],\n        b10: [87.87, 124.72],\n        c0: [2599.37, 3676.54],\n        c1: [1836.85, 2599.37],\n        c2: [1298.27, 1836.85],\n        c3: [918.43, 1298.27],\n        c4: [649.13, 918.43],\n        c5: [459.21, 649.13],\n        c6: [323.15, 459.21],\n        c7: [229.61, 323.15],\n        c8: [161.57, 229.61],\n        c9: [113.39, 161.57],\n        c10: [79.37, 113.39],\n        dl: [311.81, 623.62],\n        letter: [612, 792],\n        \"government-letter\": [576, 756],\n        legal: [612, 1008],\n        \"junior-legal\": [576, 360],\n        ledger: [1224, 792],\n        tabloid: [792, 1224],\n        \"credit-card\": [153, 243]\n      };\n    switch (e) {\n      case \"pt\":\n        var a = 1;\n        break;\n      case \"mm\":\n        a = 72 / 25.4;\n        break;\n      case \"cm\":\n        a = 72 / 2.54;\n        break;\n      case \"in\":\n        a = 72;\n        break;\n      case \"px\":\n        a = .75;\n        break;\n      case \"pc\":\n      case \"em\":\n        a = 12;\n        break;\n      case \"ex\":\n        a = 6;\n        break;\n      default:\n        throw \"Invalid unit: \" + e;\n    }\n    if (o.hasOwnProperty(i)) var s = o[i][1] / a,\n      l = o[i][0] / a;else try {\n      s = n[1], l = n[0];\n    } catch (t) {\n      throw new Error(\"Invalid format: \" + n);\n    }\n    if (\"p\" === t || \"portrait\" === t) {\n      if (t = \"p\", s < l) {\n        var h = l;\n        l = s, s = h;\n      }\n    } else {\n      if (\"l\" !== t && \"landscape\" !== t) throw \"Invalid orientation: \" + t;\n      t = \"l\", l < s && (h = l, l = s, s = h);\n    }\n    return {\n      width: l,\n      height: s,\n      unit: e,\n      k: a\n    };\n  }, i.html = function (t, e) {\n    (e = e || {}).callback = e.callback || function () {}, e.html2canvas = e.html2canvas || {}, e.html2canvas.canvas = e.html2canvas.canvas || this.canvas, e.jsPDF = e.jsPDF || this, e.jsPDF;\n    var n = new y(e);\n    return e.worker ? n : n.from(t).doCallback();\n  }, lt.API.addJS = function (t) {\n    return b = t, this.internal.events.subscribe(\"postPutResources\", function (t) {\n      v = this.internal.newObject(), this.internal.out(\"<<\"), this.internal.out(\"/Names [(EmbeddedJS) \" + (v + 1) + \" 0 R]\"), this.internal.out(\">>\"), this.internal.out(\"endobj\"), w = this.internal.newObject(), this.internal.out(\"<<\"), this.internal.out(\"/S /JavaScript\"), this.internal.out(\"/JS (\" + b + \")\"), this.internal.out(\">>\"), this.internal.out(\"endobj\");\n    }), this.internal.events.subscribe(\"putCatalog\", function () {\n      void 0 !== v && void 0 !== w && this.internal.out(\"/Names <</JavaScript \" + v + \" 0 R>>\");\n    }), this;\n  }, (\n  /**\n     * @license\n     * Copyright (c) 2014 Steven Spungin (TwelveTone LLC)  <EMAIL>\n     *\n     * Licensed under the MIT License.\n     * http://opensource.org/licenses/mit-license\n     */\n  x = lt.API).events.push([\"postPutResources\", function () {\n    var t = this,\n      e = /^(\\d+) 0 obj$/;\n    if (0 < this.outline.root.children.length) for (var n = t.outline.render().split(/\\r\\n/), r = 0; r < n.length; r++) {\n      var i = n[r],\n        o = e.exec(i);\n      if (null != o) {\n        var a = o[1];\n        t.internal.newObjectDeferredBegin(a, !1);\n      }\n      t.internal.write(i);\n    }\n    if (this.outline.createNamedDestinations) {\n      var s = this.internal.pages.length,\n        l = [];\n      for (r = 0; r < s; r++) {\n        var h = t.internal.newObject();\n        l.push(h);\n        var u = t.internal.getPageInfo(r + 1);\n        t.internal.write(\"<< /D[\" + u.objId + \" 0 R /XYZ null null null]>> endobj\");\n      }\n      var c = t.internal.newObject();\n      for (t.internal.write(\"<< /Names [ \"), r = 0; r < l.length; r++) t.internal.write(\"(page_\" + (r + 1) + \")\" + l[r] + \" 0 R\");\n      t.internal.write(\" ] >>\", \"endobj\"), t.internal.newObject(), t.internal.write(\"<< /Dests \" + c + \" 0 R\"), t.internal.write(\">>\", \"endobj\");\n    }\n  }]), x.events.push([\"putCatalog\", function () {\n    0 < this.outline.root.children.length && (this.internal.write(\"/Outlines\", this.outline.makeRef(this.outline.root)), this.outline.createNamedDestinations && this.internal.write(\"/Names \" + namesOid + \" 0 R\"));\n  }]), x.events.push([\"initialized\", function () {\n    var a = this;\n    a.outline = {\n      createNamedDestinations: !1,\n      root: {\n        children: []\n      }\n    }, a.outline.add = function (t, e, n) {\n      var r = {\n        title: e,\n        options: n,\n        children: []\n      };\n      return null == t && (t = this.root), t.children.push(r), r;\n    }, a.outline.render = function () {\n      return this.ctx = {}, this.ctx.val = \"\", this.ctx.pdf = a, this.genIds_r(this.root), this.renderRoot(this.root), this.renderItems(this.root), this.ctx.val;\n    }, a.outline.genIds_r = function (t) {\n      t.id = a.internal.newObjectDeferred();\n      for (var e = 0; e < t.children.length; e++) this.genIds_r(t.children[e]);\n    }, a.outline.renderRoot = function (t) {\n      this.objStart(t), this.line(\"/Type /Outlines\"), 0 < t.children.length && (this.line(\"/First \" + this.makeRef(t.children[0])), this.line(\"/Last \" + this.makeRef(t.children[t.children.length - 1]))), this.line(\"/Count \" + this.count_r({\n        count: 0\n      }, t)), this.objEnd();\n    }, a.outline.renderItems = function (t) {\n      this.ctx.pdf.internal.getCoordinateString;\n      for (var e = this.ctx.pdf.internal.getVerticalCoordinateString, n = 0; n < t.children.length; n++) {\n        var r = t.children[n];\n        this.objStart(r), this.line(\"/Title \" + this.makeString(r.title)), this.line(\"/Parent \" + this.makeRef(t)), 0 < n && this.line(\"/Prev \" + this.makeRef(t.children[n - 1])), n < t.children.length - 1 && this.line(\"/Next \" + this.makeRef(t.children[n + 1])), 0 < r.children.length && (this.line(\"/First \" + this.makeRef(r.children[0])), this.line(\"/Last \" + this.makeRef(r.children[r.children.length - 1])));\n        var i = this.count = this.count_r({\n          count: 0\n        }, r);\n        if (0 < i && this.line(\"/Count \" + i), r.options && r.options.pageNumber) {\n          var o = a.internal.getPageInfo(r.options.pageNumber);\n          this.line(\"/Dest [\" + o.objId + \" 0 R /XYZ 0 \" + e(0) + \" 0]\");\n        }\n        this.objEnd();\n      }\n      for (n = 0; n < t.children.length; n++) r = t.children[n], this.renderItems(r);\n    }, a.outline.line = function (t) {\n      this.ctx.val += t + \"\\r\\n\";\n    }, a.outline.makeRef = function (t) {\n      return t.id + \" 0 R\";\n    }, a.outline.makeString = function (t) {\n      return \"(\" + a.internal.pdfEscape(t) + \")\";\n    }, a.outline.objStart = function (t) {\n      this.ctx.val += \"\\r\\n\" + t.id + \" 0 obj\\r\\n<<\\r\\n\";\n    }, a.outline.objEnd = function (t) {\n      this.ctx.val += \">> \\r\\nendobj\\r\\n\";\n    }, a.outline.count_r = function (t, e) {\n      for (var n = 0; n < e.children.length; n++) t.count++, this.count_r(t, e.children[n]);\n      return t.count;\n    };\n  }]),\n  /**\n     * @license\n     * \n     * Copyright (c) 2014 James Robb, https://github.com/jamesbrobb\n     *\n     * \n     * ====================================================================\n     */\n  I = lt.API, C = function () {\n    var t = \"function\" == typeof Deflater;\n    if (!t) throw new Error(\"requires deflate.js for compression\");\n    return t;\n  }, B = function (t, e, n, r) {\n    var i = 5,\n      o = E;\n    switch (r) {\n      case I.image_compression.FAST:\n        i = 3, o = j;\n        break;\n      case I.image_compression.MEDIUM:\n        i = 6, o = M;\n        break;\n      case I.image_compression.SLOW:\n        i = 9, o = O;\n    }\n    t = A(t, e, n, o);\n    var a = new Uint8Array(N(i)),\n      s = L(t),\n      l = new Deflater(i),\n      h = l.append(t),\n      u = l.flush(),\n      c = a.length + h.length + u.length,\n      f = new Uint8Array(c + 4);\n    return f.set(a), f.set(h, a.length), f.set(u, a.length + h.length), f[c++] = s >>> 24 & 255, f[c++] = s >>> 16 & 255, f[c++] = s >>> 8 & 255, f[c++] = 255 & s, I.arrayBufferToBinaryString(f);\n  }, N = function (t, e) {\n    var n = Math.LOG2E * Math.log(32768) - 8 << 4 | 8,\n      r = n << 8;\n    return r |= Math.min(3, (e - 1 & 255) >> 1) << 6, r |= 0, [n, 255 & (r += 31 - r % 31)];\n  }, L = function (t, e) {\n    for (var n, r = 1, i = 0, o = t.length, a = 0; 0 < o;) {\n      for (o -= n = e < o ? e : o; i += r += t[a++], --n;);\n      r %= 65521, i %= 65521;\n    }\n    return (i << 16 | r) >>> 0;\n  }, A = function (t, e, n, r) {\n    for (var i, o, a, s = t.length / e, l = new Uint8Array(t.length + s), h = T(), u = 0; u < s; u++) {\n      if (a = u * e, i = t.subarray(a, a + e), r) l.set(r(i, n, o), a + u);else {\n        for (var c = 0, f = h.length, p = []; c < f; c++) p[c] = h[c](i, n, o);\n        var d = R(p.concat());\n        l.set(p[d], a + u);\n      }\n      o = i;\n    }\n    return l;\n  }, S = function (t, e, n) {\n    var r = Array.apply([], t);\n    return r.unshift(0), r;\n  }, j = function (t, e, n) {\n    var r,\n      i = [],\n      o = 0,\n      a = t.length;\n    for (i[0] = 1; o < a; o++) r = t[o - e] || 0, i[o + 1] = t[o] - r + 256 & 255;\n    return i;\n  }, E = function (t, e, n) {\n    var r,\n      i = [],\n      o = 0,\n      a = t.length;\n    for (i[0] = 2; o < a; o++) r = n && n[o] || 0, i[o + 1] = t[o] - r + 256 & 255;\n    return i;\n  }, M = function (t, e, n) {\n    var r,\n      i,\n      o = [],\n      a = 0,\n      s = t.length;\n    for (o[0] = 3; a < s; a++) r = t[a - e] || 0, i = n && n[a] || 0, o[a + 1] = t[a] + 256 - (r + i >>> 1) & 255;\n    return o;\n  }, O = function (t, e, n) {\n    var r,\n      i,\n      o,\n      a,\n      s = [],\n      l = 0,\n      h = t.length;\n    for (s[0] = 4; l < h; l++) r = t[l - e] || 0, i = n && n[l] || 0, o = n && n[l - e] || 0, a = q(r, i, o), s[l + 1] = t[l] - a + 256 & 255;\n    return s;\n  }, q = function (t, e, n) {\n    var r = t + e - n,\n      i = Math.abs(r - t),\n      o = Math.abs(r - e),\n      a = Math.abs(r - n);\n    return i <= o && i <= a ? t : o <= a ? e : n;\n  }, T = function () {\n    return [S, j, E, M, O];\n  }, R = function (t) {\n    for (var e, n, r, i = 0, o = t.length; i < o;) ((e = D(t[i].slice(1))) < n || !n) && (n = e, r = i), i++;\n    return r;\n  }, D = function (t) {\n    for (var e = 0, n = t.length, r = 0; e < n;) r += Math.abs(t[e++]);\n    return r;\n  }, I.processPNG = function (t, e, n, r, i) {\n    var o,\n      a,\n      s,\n      l,\n      h,\n      u,\n      c = this.color_spaces.DEVICE_RGB,\n      f = this.decode.FLATE_DECODE,\n      p = 8;\n    if (this.isArrayBuffer(t) && (t = new Uint8Array(t)), this.isArrayBufferView(t)) {\n      if (\"function\" != typeof PNG || \"function\" != typeof kt) throw new Error(\"PNG support requires png.js and zlib.js\");\n      if (t = (o = new PNG(t)).imgData, p = o.bits, c = o.colorSpace, l = o.colors, -1 !== [4, 6].indexOf(o.colorType)) {\n        if (8 === o.bits) for (var d, g = (_ = 32 == o.pixelBitlength ? new Uint32Array(o.decodePixels().buffer) : 16 == o.pixelBitlength ? new Uint16Array(o.decodePixels().buffer) : new Uint8Array(o.decodePixels().buffer)).length, m = new Uint8Array(g * o.colors), y = new Uint8Array(g), v = o.pixelBitlength - o.bits, w = 0, b = 0; w < g; w++) {\n          for (x = _[w], d = 0; d < v;) m[b++] = x >>> d & 255, d += o.bits;\n          y[w] = x >>> d & 255;\n        }\n        if (16 === o.bits) {\n          g = (_ = new Uint32Array(o.decodePixels().buffer)).length, m = new Uint8Array(g * (32 / o.pixelBitlength) * o.colors), y = new Uint8Array(g * (32 / o.pixelBitlength));\n          for (var x, N = 1 < o.colors, L = b = w = 0; w < g;) x = _[w++], m[b++] = x >>> 0 & 255, N && (m[b++] = x >>> 16 & 255, x = _[w++], m[b++] = x >>> 0 & 255), y[L++] = x >>> 16 & 255;\n          p = 8;\n        }\n        r !== I.image_compression.NONE && C() ? (t = B(m, o.width * o.colors, o.colors, r), u = B(y, o.width, 1, r)) : (t = m, u = y, f = null);\n      }\n      if (3 === o.colorType && (c = this.color_spaces.INDEXED, h = o.palette, o.transparency.indexed)) {\n        var A = o.transparency.indexed,\n          S = 0;\n        for (w = 0, g = A.length; w < g; ++w) S += A[w];\n        if ((S /= 255) == g - 1 && -1 !== A.indexOf(0)) s = [A.indexOf(0)];else if (S !== g) {\n          var _ = o.decodePixels();\n          for (y = new Uint8Array(_.length), w = 0, g = _.length; w < g; w++) y[w] = A[_[w]];\n          u = B(y, o.width, 1);\n        }\n      }\n      var F = function (t) {\n        var e;\n        switch (t) {\n          case I.image_compression.FAST:\n            e = 11;\n            break;\n          case I.image_compression.MEDIUM:\n            e = 13;\n            break;\n          case I.image_compression.SLOW:\n            e = 14;\n            break;\n          default:\n            e = 12;\n        }\n        return e;\n      }(r);\n      return a = f === this.decode.FLATE_DECODE ? \"/Predictor \" + F + \" /Colors \" + l + \" /BitsPerComponent \" + p + \" /Columns \" + o.width : \"/Colors \" + l + \" /BitsPerComponent \" + p + \" /Columns \" + o.width, (this.isArrayBuffer(t) || this.isArrayBufferView(t)) && (t = this.arrayBufferToBinaryString(t)), (u && this.isArrayBuffer(u) || this.isArrayBufferView(u)) && (u = this.arrayBufferToBinaryString(u)), this.createImageInfo(t, o.width, o.height, c, p, f, e, n, a, s, h, u, F);\n    }\n    throw new Error(\"Unsupported PNG image data, try using JPEG instead.\");\n  }, (\n  /**\n     * @license\n     * Copyright (c) 2017 Aras Abbasi \n     *\n     * Licensed under the MIT License.\n     * http://opensource.org/licenses/mit-license\n     */\n  U = lt.API).processGIF89A = function (t, e, n, r, i) {\n    var o = new At(t),\n      a = o.width,\n      s = o.height,\n      l = [];\n    o.decodeAndBlitFrameRGBA(0, l);\n    var h = {\n        data: l,\n        width: a,\n        height: s\n      },\n      u = new _t(100).encode(h, 100);\n    return U.processJPEG.call(this, u, e, n, r);\n  }, U.processGIF87A = U.processGIF89A, (\n  /**\n     * Copyright (c) 2018 Aras Abbasi \n     *\n     * Licensed under the MIT License.\n     * http://opensource.org/licenses/mit-license\n     */\n  z = lt.API).processBMP = function (t, e, n, r, i) {\n    var o = new Ft(t, !1),\n      a = o.width,\n      s = o.height,\n      l = {\n        data: o.getData(),\n        width: a,\n        height: s\n      },\n      h = new _t(100).encode(l, 100);\n    return z.processJPEG.call(this, h, e, n, r);\n  }, lt.API.setLanguage = function (t) {\n    return void 0 === this.internal.languageSettings && (this.internal.languageSettings = {}, this.internal.languageSettings.isSubscribed = !1), void 0 !== {\n      af: \"Afrikaans\",\n      sq: \"Albanian\",\n      ar: \"Arabic (Standard)\",\n      \"ar-DZ\": \"Arabic (Algeria)\",\n      \"ar-BH\": \"Arabic (Bahrain)\",\n      \"ar-EG\": \"Arabic (Egypt)\",\n      \"ar-IQ\": \"Arabic (Iraq)\",\n      \"ar-JO\": \"Arabic (Jordan)\",\n      \"ar-KW\": \"Arabic (Kuwait)\",\n      \"ar-LB\": \"Arabic (Lebanon)\",\n      \"ar-LY\": \"Arabic (Libya)\",\n      \"ar-MA\": \"Arabic (Morocco)\",\n      \"ar-OM\": \"Arabic (Oman)\",\n      \"ar-QA\": \"Arabic (Qatar)\",\n      \"ar-SA\": \"Arabic (Saudi Arabia)\",\n      \"ar-SY\": \"Arabic (Syria)\",\n      \"ar-TN\": \"Arabic (Tunisia)\",\n      \"ar-AE\": \"Arabic (U.A.E.)\",\n      \"ar-YE\": \"Arabic (Yemen)\",\n      an: \"Aragonese\",\n      hy: \"Armenian\",\n      as: \"Assamese\",\n      ast: \"Asturian\",\n      az: \"Azerbaijani\",\n      eu: \"Basque\",\n      be: \"Belarusian\",\n      bn: \"Bengali\",\n      bs: \"Bosnian\",\n      br: \"Breton\",\n      bg: \"Bulgarian\",\n      my: \"Burmese\",\n      ca: \"Catalan\",\n      ch: \"Chamorro\",\n      ce: \"Chechen\",\n      zh: \"Chinese\",\n      \"zh-HK\": \"Chinese (Hong Kong)\",\n      \"zh-CN\": \"Chinese (PRC)\",\n      \"zh-SG\": \"Chinese (Singapore)\",\n      \"zh-TW\": \"Chinese (Taiwan)\",\n      cv: \"Chuvash\",\n      co: \"Corsican\",\n      cr: \"Cree\",\n      hr: \"Croatian\",\n      cs: \"Czech\",\n      da: \"Danish\",\n      nl: \"Dutch (Standard)\",\n      \"nl-BE\": \"Dutch (Belgian)\",\n      en: \"English\",\n      \"en-AU\": \"English (Australia)\",\n      \"en-BZ\": \"English (Belize)\",\n      \"en-CA\": \"English (Canada)\",\n      \"en-IE\": \"English (Ireland)\",\n      \"en-JM\": \"English (Jamaica)\",\n      \"en-NZ\": \"English (New Zealand)\",\n      \"en-PH\": \"English (Philippines)\",\n      \"en-ZA\": \"English (South Africa)\",\n      \"en-TT\": \"English (Trinidad & Tobago)\",\n      \"en-GB\": \"English (United Kingdom)\",\n      \"en-US\": \"English (United States)\",\n      \"en-ZW\": \"English (Zimbabwe)\",\n      eo: \"Esperanto\",\n      et: \"Estonian\",\n      fo: \"Faeroese\",\n      fj: \"Fijian\",\n      fi: \"Finnish\",\n      fr: \"French (Standard)\",\n      \"fr-BE\": \"French (Belgium)\",\n      \"fr-CA\": \"French (Canada)\",\n      \"fr-FR\": \"French (France)\",\n      \"fr-LU\": \"French (Luxembourg)\",\n      \"fr-MC\": \"French (Monaco)\",\n      \"fr-CH\": \"French (Switzerland)\",\n      fy: \"Frisian\",\n      fur: \"Friulian\",\n      gd: \"Gaelic (Scots)\",\n      \"gd-IE\": \"Gaelic (Irish)\",\n      gl: \"Galacian\",\n      ka: \"Georgian\",\n      de: \"German (Standard)\",\n      \"de-AT\": \"German (Austria)\",\n      \"de-DE\": \"German (Germany)\",\n      \"de-LI\": \"German (Liechtenstein)\",\n      \"de-LU\": \"German (Luxembourg)\",\n      \"de-CH\": \"German (Switzerland)\",\n      el: \"Greek\",\n      gu: \"Gujurati\",\n      ht: \"Haitian\",\n      he: \"Hebrew\",\n      hi: \"Hindi\",\n      hu: \"Hungarian\",\n      is: \"Icelandic\",\n      id: \"Indonesian\",\n      iu: \"Inuktitut\",\n      ga: \"Irish\",\n      it: \"Italian (Standard)\",\n      \"it-CH\": \"Italian (Switzerland)\",\n      ja: \"Japanese\",\n      kn: \"Kannada\",\n      ks: \"Kashmiri\",\n      kk: \"Kazakh\",\n      km: \"Khmer\",\n      ky: \"Kirghiz\",\n      tlh: \"Klingon\",\n      ko: \"Korean\",\n      \"ko-KP\": \"Korean (North Korea)\",\n      \"ko-KR\": \"Korean (South Korea)\",\n      la: \"Latin\",\n      lv: \"Latvian\",\n      lt: \"Lithuanian\",\n      lb: \"Luxembourgish\",\n      mk: \"FYRO Macedonian\",\n      ms: \"Malay\",\n      ml: \"Malayalam\",\n      mt: \"Maltese\",\n      mi: \"Maori\",\n      mr: \"Marathi\",\n      mo: \"Moldavian\",\n      nv: \"Navajo\",\n      ng: \"Ndonga\",\n      ne: \"Nepali\",\n      no: \"Norwegian\",\n      nb: \"Norwegian (Bokmal)\",\n      nn: \"Norwegian (Nynorsk)\",\n      oc: \"Occitan\",\n      or: \"Oriya\",\n      om: \"Oromo\",\n      fa: \"Persian\",\n      \"fa-IR\": \"Persian/Iran\",\n      pl: \"Polish\",\n      pt: \"Portuguese\",\n      \"pt-BR\": \"Portuguese (Brazil)\",\n      pa: \"Punjabi\",\n      \"pa-IN\": \"Punjabi (India)\",\n      \"pa-PK\": \"Punjabi (Pakistan)\",\n      qu: \"Quechua\",\n      rm: \"Rhaeto-Romanic\",\n      ro: \"Romanian\",\n      \"ro-MO\": \"Romanian (Moldavia)\",\n      ru: \"Russian\",\n      \"ru-MO\": \"Russian (Moldavia)\",\n      sz: \"Sami (Lappish)\",\n      sg: \"Sango\",\n      sa: \"Sanskrit\",\n      sc: \"Sardinian\",\n      sd: \"Sindhi\",\n      si: \"Singhalese\",\n      sr: \"Serbian\",\n      sk: \"Slovak\",\n      sl: \"Slovenian\",\n      so: \"Somani\",\n      sb: \"Sorbian\",\n      es: \"Spanish\",\n      \"es-AR\": \"Spanish (Argentina)\",\n      \"es-BO\": \"Spanish (Bolivia)\",\n      \"es-CL\": \"Spanish (Chile)\",\n      \"es-CO\": \"Spanish (Colombia)\",\n      \"es-CR\": \"Spanish (Costa Rica)\",\n      \"es-DO\": \"Spanish (Dominican Republic)\",\n      \"es-EC\": \"Spanish (Ecuador)\",\n      \"es-SV\": \"Spanish (El Salvador)\",\n      \"es-GT\": \"Spanish (Guatemala)\",\n      \"es-HN\": \"Spanish (Honduras)\",\n      \"es-MX\": \"Spanish (Mexico)\",\n      \"es-NI\": \"Spanish (Nicaragua)\",\n      \"es-PA\": \"Spanish (Panama)\",\n      \"es-PY\": \"Spanish (Paraguay)\",\n      \"es-PE\": \"Spanish (Peru)\",\n      \"es-PR\": \"Spanish (Puerto Rico)\",\n      \"es-ES\": \"Spanish (Spain)\",\n      \"es-UY\": \"Spanish (Uruguay)\",\n      \"es-VE\": \"Spanish (Venezuela)\",\n      sx: \"Sutu\",\n      sw: \"Swahili\",\n      sv: \"Swedish\",\n      \"sv-FI\": \"Swedish (Finland)\",\n      \"sv-SV\": \"Swedish (Sweden)\",\n      ta: \"Tamil\",\n      tt: \"Tatar\",\n      te: \"Teluga\",\n      th: \"Thai\",\n      tig: \"Tigre\",\n      ts: \"Tsonga\",\n      tn: \"Tswana\",\n      tr: \"Turkish\",\n      tk: \"Turkmen\",\n      uk: \"Ukrainian\",\n      hsb: \"Upper Sorbian\",\n      ur: \"Urdu\",\n      ve: \"Venda\",\n      vi: \"Vietnamese\",\n      vo: \"Volapuk\",\n      wa: \"Walloon\",\n      cy: \"Welsh\",\n      xh: \"Xhosa\",\n      ji: \"Yiddish\",\n      zu: \"Zulu\"\n    }[t] && (this.internal.languageSettings.languageCode = t, !1 === this.internal.languageSettings.isSubscribed && (this.internal.events.subscribe(\"putCatalog\", function () {\n      this.internal.write(\"/Lang (\" + this.internal.languageSettings.languageCode + \")\");\n    }), this.internal.languageSettings.isSubscribed = !0)), this;\n  },\n  /** @license\n     * MIT license.\n     * Copyright (c) 2012 Willow Systems Corporation, willow-systems.com\n     *               2014 Diego Casorran, https://github.com/diegocr\n     *\n     * \n     * ====================================================================\n     */\n  H = lt.API, W = H.getCharWidthsArray = function (t, e) {\n    var n,\n      r,\n      i,\n      o = (e = e || {}).font || this.internal.getFont(),\n      a = e.fontSize || this.internal.getFontSize(),\n      s = e.charSpace || this.internal.getCharSpace(),\n      l = e.widths ? e.widths : o.metadata.Unicode.widths,\n      h = l.fof ? l.fof : 1,\n      u = e.kerning ? e.kerning : o.metadata.Unicode.kerning,\n      c = u.fof ? u.fof : 1,\n      f = 0,\n      p = l[0] || h,\n      d = [];\n    for (n = 0, r = t.length; n < r; n++) i = t.charCodeAt(n), \"function\" == typeof o.metadata.widthOfString ? d.push((o.metadata.widthOfGlyph(o.metadata.characterToGlyph(i)) + s * (1e3 / a) || 0) / 1e3) : d.push((l[i] || p) / h + (u[i] && u[i][f] || 0) / c), f = i;\n    return d;\n  }, V = H.getArraySum = function (t) {\n    for (var e = t.length, n = 0; e;) n += t[--e];\n    return n;\n  }, G = H.getStringUnitWidth = function (t, e) {\n    var n = (e = e || {}).fontSize || this.internal.getFontSize(),\n      r = e.font || this.internal.getFont(),\n      i = e.charSpace || this.internal.getCharSpace();\n    return \"function\" == typeof r.metadata.widthOfString ? r.metadata.widthOfString(t, n, i) / n : V(W.apply(this, arguments));\n  }, Y = function (t, e, n, r) {\n    for (var i = [], o = 0, a = t.length, s = 0; o !== a && s + e[o] < n;) s += e[o], o++;\n    i.push(t.slice(0, o));\n    var l = o;\n    for (s = 0; o !== a;) s + e[o] > r && (i.push(t.slice(l, o)), s = 0, l = o), s += e[o], o++;\n    return l !== o && i.push(t.slice(l, o)), i;\n  }, J = function (t, e, n) {\n    n || (n = {});\n    var r,\n      i,\n      o,\n      a,\n      s,\n      l,\n      h = [],\n      u = [h],\n      c = n.textIndent || 0,\n      f = 0,\n      p = 0,\n      d = t.split(\" \"),\n      g = W.apply(this, [\" \", n])[0];\n    if (l = -1 === n.lineIndent ? d[0].length + 2 : n.lineIndent || 0) {\n      var m = Array(l).join(\" \"),\n        y = [];\n      d.map(function (t) {\n        1 < (t = t.split(/\\s*\\n/)).length ? y = y.concat(t.map(function (t, e) {\n          return (e && t.length ? \"\\n\" : \"\") + t;\n        })) : y.push(t[0]);\n      }), d = y, l = G.apply(this, [m, n]);\n    }\n    for (o = 0, a = d.length; o < a; o++) {\n      var v = 0;\n      if (r = d[o], l && \"\\n\" == r[0] && (r = r.substr(1), v = 1), i = W.apply(this, [r, n]), e < c + f + (p = V(i)) || v) {\n        if (e < p) {\n          for (s = Y.apply(this, [r, i, e - (c + f), e]), h.push(s.shift()), h = [s.pop()]; s.length;) u.push([s.shift()]);\n          p = V(i.slice(r.length - (h[0] ? h[0].length : 0)));\n        } else h = [r];\n        u.push(h), c = p + l, f = g;\n      } else h.push(r), c += f + p, f = g;\n    }\n    if (l) var w = function (t, e) {\n      return (e ? m : \"\") + t.join(\" \");\n    };else w = function (t) {\n      return t.join(\" \");\n    };\n    return u.map(w);\n  }, H.splitTextToSize = function (t, e, n) {\n    var r,\n      i = (n = n || {}).fontSize || this.internal.getFontSize(),\n      o = function (t) {\n        var e = {\n            0: 1\n          },\n          n = {};\n        if (t.widths && t.kerning) return {\n          widths: t.widths,\n          kerning: t.kerning\n        };\n        var r = this.internal.getFont(t.fontName, t.fontStyle),\n          i = \"Unicode\";\n        return r.metadata[i] ? {\n          widths: r.metadata[i].widths || e,\n          kerning: r.metadata[i].kerning || n\n        } : {\n          font: r.metadata,\n          fontSize: this.internal.getFontSize(),\n          charSpace: this.internal.getCharSpace()\n        };\n      }.call(this, n);\n    r = Array.isArray(t) ? t : t.split(/\\r?\\n/);\n    var a = 1 * this.internal.scaleFactor * e / i;\n    o.textIndent = n.textIndent ? 1 * n.textIndent * this.internal.scaleFactor / i : 0, o.lineIndent = n.lineIndent;\n    var s,\n      l,\n      h = [];\n    for (s = 0, l = r.length; s < l; s++) h = h.concat(J.apply(this, [r[s], a, o]));\n    return h;\n  },\n  /** @license\n     jsPDF standard_fonts_metrics plugin\n     * Copyright (c) 2012 Willow Systems Corporation, willow-systems.com\n     * MIT license.\n     * \n     * ====================================================================\n     */\n  X = lt.API, Z = {\n    codePages: [\"WinAnsiEncoding\"],\n    WinAnsiEncoding: (K = function (t) {\n      for (var e = \"klmnopqrstuvwxyz\", n = {}, r = 0; r < e.length; r++) n[e[r]] = \"0123456789abcdef\"[r];\n      var i,\n        o,\n        a,\n        s,\n        l,\n        h = {},\n        u = 1,\n        c = h,\n        f = [],\n        p = \"\",\n        d = \"\",\n        g = t.length - 1;\n      for (r = 1; r != g;) l = t[r], r += 1, \"'\" == l ? o = o ? (s = o.join(\"\"), i) : [] : o ? o.push(l) : \"{\" == l ? (f.push([c, s]), c = {}, s = i) : \"}\" == l ? ((a = f.pop())[0][a[1]] = c, s = i, c = a[0]) : \"-\" == l ? u = -1 : s === i ? n.hasOwnProperty(l) ? (p += n[l], s = parseInt(p, 16) * u, u = 1, p = \"\") : p += l : n.hasOwnProperty(l) ? (d += n[l], c[s] = parseInt(d, 16) * u, u = 1, s = i, d = \"\") : d += l;\n      return h;\n    })(\"{19m8n201n9q201o9r201s9l201t9m201u8m201w9n201x9o201y8o202k8q202l8r202m9p202q8p20aw8k203k8t203t8v203u9v2cq8s212m9t15m8w15n9w2dw9s16k8u16l9u17s9z17x8y17y9y}\")\n  }, Q = {\n    Unicode: {\n      Courier: Z,\n      \"Courier-Bold\": Z,\n      \"Courier-BoldOblique\": Z,\n      \"Courier-Oblique\": Z,\n      Helvetica: Z,\n      \"Helvetica-Bold\": Z,\n      \"Helvetica-BoldOblique\": Z,\n      \"Helvetica-Oblique\": Z,\n      \"Times-Roman\": Z,\n      \"Times-Bold\": Z,\n      \"Times-BoldItalic\": Z,\n      \"Times-Italic\": Z\n    }\n  }, $ = {\n    Unicode: {\n      \"Courier-Oblique\": K(\"{'widths'{k3w'fof'6o}'kerning'{'fof'-6o}}\"),\n      \"Times-BoldItalic\": K(\"{'widths'{k3o2q4ycx2r201n3m201o6o201s2l201t2l201u2l201w3m201x3m201y3m2k1t2l2r202m2n2n3m2o3m2p5n202q6o2r1w2s2l2t2l2u3m2v3t2w1t2x2l2y1t2z1w3k3m3l3m3m3m3n3m3o3m3p3m3q3m3r3m3s3m203t2l203u2l3v2l3w3t3x3t3y3t3z3m4k5n4l4m4m4m4n4m4o4s4p4m4q4m4r4s4s4y4t2r4u3m4v4m4w3x4x5t4y4s4z4s5k3x5l4s5m4m5n3r5o3x5p4s5q4m5r5t5s4m5t3x5u3x5v2l5w1w5x2l5y3t5z3m6k2l6l3m6m3m6n2w6o3m6p2w6q2l6r3m6s3r6t1w6u1w6v3m6w1w6x4y6y3r6z3m7k3m7l3m7m2r7n2r7o1w7p3r7q2w7r4m7s3m7t2w7u2r7v2n7w1q7x2n7y3t202l3mcl4mal2ram3man3mao3map3mar3mas2lat4uau1uav3maw3way4uaz2lbk2sbl3t'fof'6obo2lbp3tbq3mbr1tbs2lbu1ybv3mbz3mck4m202k3mcm4mcn4mco4mcp4mcq5ycr4mcs4mct4mcu4mcv4mcw2r2m3rcy2rcz2rdl4sdm4sdn4sdo4sdp4sdq4sds4sdt4sdu4sdv4sdw4sdz3mek3mel3mem3men3meo3mep3meq4ser2wes2wet2weu2wev2wew1wex1wey1wez1wfl3rfm3mfn3mfo3mfp3mfq3mfr3tfs3mft3rfu3rfv3rfw3rfz2w203k6o212m6o2dw2l2cq2l3t3m3u2l17s3x19m3m}'kerning'{cl{4qu5kt5qt5rs17ss5ts}201s{201ss}201t{cks4lscmscnscoscpscls2wu2yu201ts}201x{2wu2yu}2k{201ts}2w{4qx5kx5ou5qx5rs17su5tu}2x{17su5tu5ou}2y{4qx5kx5ou5qx5rs17ss5ts}'fof'-6ofn{17sw5tw5ou5qw5rs}7t{cksclscmscnscoscps4ls}3u{17su5tu5os5qs}3v{17su5tu5os5qs}7p{17su5tu}ck{4qu5kt5qt5rs17ss5ts}4l{4qu5kt5qt5rs17ss5ts}cm{4qu5kt5qt5rs17ss5ts}cn{4qu5kt5qt5rs17ss5ts}co{4qu5kt5qt5rs17ss5ts}cp{4qu5kt5qt5rs17ss5ts}6l{4qu5ou5qw5rt17su5tu}5q{ckuclucmucnucoucpu4lu}5r{ckuclucmucnucoucpu4lu}7q{cksclscmscnscoscps4ls}6p{4qu5ou5qw5rt17sw5tw}ek{4qu5ou5qw5rt17su5tu}el{4qu5ou5qw5rt17su5tu}em{4qu5ou5qw5rt17su5tu}en{4qu5ou5qw5rt17su5tu}eo{4qu5ou5qw5rt17su5tu}ep{4qu5ou5qw5rt17su5tu}es{17ss5ts5qs4qu}et{4qu5ou5qw5rt17sw5tw}eu{4qu5ou5qw5rt17ss5ts}ev{17ss5ts5qs4qu}6z{17sw5tw5ou5qw5rs}fm{17sw5tw5ou5qw5rs}7n{201ts}fo{17sw5tw5ou5qw5rs}fp{17sw5tw5ou5qw5rs}fq{17sw5tw5ou5qw5rs}7r{cksclscmscnscoscps4ls}fs{17sw5tw5ou5qw5rs}ft{17su5tu}fu{17su5tu}fv{17su5tu}fw{17su5tu}fz{cksclscmscnscoscps4ls}}}\"),\n      \"Helvetica-Bold\": K(\"{'widths'{k3s2q4scx1w201n3r201o6o201s1w201t1w201u1w201w3m201x3m201y3m2k1w2l2l202m2n2n3r2o3r2p5t202q6o2r1s2s2l2t2l2u2r2v3u2w1w2x2l2y1w2z1w3k3r3l3r3m3r3n3r3o3r3p3r3q3r3r3r3s3r203t2l203u2l3v2l3w3u3x3u3y3u3z3x4k6l4l4s4m4s4n4s4o4s4p4m4q3x4r4y4s4s4t1w4u3r4v4s4w3x4x5n4y4s4z4y5k4m5l4y5m4s5n4m5o3x5p4s5q4m5r5y5s4m5t4m5u3x5v2l5w1w5x2l5y3u5z3r6k2l6l3r6m3x6n3r6o3x6p3r6q2l6r3x6s3x6t1w6u1w6v3r6w1w6x5t6y3x6z3x7k3x7l3x7m2r7n3r7o2l7p3x7q3r7r4y7s3r7t3r7u3m7v2r7w1w7x2r7y3u202l3rcl4sal2lam3ran3rao3rap3rar3ras2lat4tau2pav3raw3uay4taz2lbk2sbl3u'fof'6obo2lbp3xbq3rbr1wbs2lbu2obv3rbz3xck4s202k3rcm4scn4sco4scp4scq6ocr4scs4mct4mcu4mcv4mcw1w2m2zcy1wcz1wdl4sdm4ydn4ydo4ydp4ydq4yds4ydt4sdu4sdv4sdw4sdz3xek3rel3rem3ren3reo3rep3req5ter3res3ret3reu3rev3rew1wex1wey1wez1wfl3xfm3xfn3xfo3xfp3xfq3xfr3ufs3xft3xfu3xfv3xfw3xfz3r203k6o212m6o2dw2l2cq2l3t3r3u2l17s4m19m3r}'kerning'{cl{4qs5ku5ot5qs17sv5tv}201t{2ww4wy2yw}201w{2ks}201x{2ww4wy2yw}2k{201ts201xs}2w{7qs4qu5kw5os5qw5rs17su5tu7tsfzs}2x{5ow5qs}2y{7qs4qu5kw5os5qw5rs17su5tu7tsfzs}'fof'-6o7p{17su5tu5ot}ck{4qs5ku5ot5qs17sv5tv}4l{4qs5ku5ot5qs17sv5tv}cm{4qs5ku5ot5qs17sv5tv}cn{4qs5ku5ot5qs17sv5tv}co{4qs5ku5ot5qs17sv5tv}cp{4qs5ku5ot5qs17sv5tv}6l{17st5tt5os}17s{2kwclvcmvcnvcovcpv4lv4wwckv}5o{2kucltcmtcntcotcpt4lt4wtckt}5q{2ksclscmscnscoscps4ls4wvcks}5r{2ks4ws}5t{2kwclvcmvcnvcovcpv4lv4wwckv}eo{17st5tt5os}fu{17su5tu5ot}6p{17ss5ts}ek{17st5tt5os}el{17st5tt5os}em{17st5tt5os}en{17st5tt5os}6o{201ts}ep{17st5tt5os}es{17ss5ts}et{17ss5ts}eu{17ss5ts}ev{17ss5ts}6z{17su5tu5os5qt}fm{17su5tu5os5qt}fn{17su5tu5os5qt}fo{17su5tu5os5qt}fp{17su5tu5os5qt}fq{17su5tu5os5qt}fs{17su5tu5os5qt}ft{17su5tu5ot}7m{5os}fv{17su5tu5ot}fw{17su5tu5ot}}}\"),\n      Courier: K(\"{'widths'{k3w'fof'6o}'kerning'{'fof'-6o}}\"),\n      \"Courier-BoldOblique\": K(\"{'widths'{k3w'fof'6o}'kerning'{'fof'-6o}}\"),\n      \"Times-Bold\": K(\"{'widths'{k3q2q5ncx2r201n3m201o6o201s2l201t2l201u2l201w3m201x3m201y3m2k1t2l2l202m2n2n3m2o3m2p6o202q6o2r1w2s2l2t2l2u3m2v3t2w1t2x2l2y1t2z1w3k3m3l3m3m3m3n3m3o3m3p3m3q3m3r3m3s3m203t2l203u2l3v2l3w3t3x3t3y3t3z3m4k5x4l4s4m4m4n4s4o4s4p4m4q3x4r4y4s4y4t2r4u3m4v4y4w4m4x5y4y4s4z4y5k3x5l4y5m4s5n3r5o4m5p4s5q4s5r6o5s4s5t4s5u4m5v2l5w1w5x2l5y3u5z3m6k2l6l3m6m3r6n2w6o3r6p2w6q2l6r3m6s3r6t1w6u2l6v3r6w1w6x5n6y3r6z3m7k3r7l3r7m2w7n2r7o2l7p3r7q3m7r4s7s3m7t3m7u2w7v2r7w1q7x2r7y3o202l3mcl4sal2lam3man3mao3map3mar3mas2lat4uau1yav3maw3tay4uaz2lbk2sbl3t'fof'6obo2lbp3rbr1tbs2lbu2lbv3mbz3mck4s202k3mcm4scn4sco4scp4scq6ocr4scs4mct4mcu4mcv4mcw2r2m3rcy2rcz2rdl4sdm4ydn4ydo4ydp4ydq4yds4ydt4sdu4sdv4sdw4sdz3rek3mel3mem3men3meo3mep3meq4ser2wes2wet2weu2wev2wew1wex1wey1wez1wfl3rfm3mfn3mfo3mfp3mfq3mfr3tfs3mft3rfu3rfv3rfw3rfz3m203k6o212m6o2dw2l2cq2l3t3m3u2l17s4s19m3m}'kerning'{cl{4qt5ks5ot5qy5rw17sv5tv}201t{cks4lscmscnscoscpscls4wv}2k{201ts}2w{4qu5ku7mu5os5qx5ru17su5tu}2x{17su5tu5ou5qs}2y{4qv5kv7mu5ot5qz5ru17su5tu}'fof'-6o7t{cksclscmscnscoscps4ls}3u{17su5tu5os5qu}3v{17su5tu5os5qu}fu{17su5tu5ou5qu}7p{17su5tu5ou5qu}ck{4qt5ks5ot5qy5rw17sv5tv}4l{4qt5ks5ot5qy5rw17sv5tv}cm{4qt5ks5ot5qy5rw17sv5tv}cn{4qt5ks5ot5qy5rw17sv5tv}co{4qt5ks5ot5qy5rw17sv5tv}cp{4qt5ks5ot5qy5rw17sv5tv}6l{17st5tt5ou5qu}17s{ckuclucmucnucoucpu4lu4wu}5o{ckuclucmucnucoucpu4lu4wu}5q{ckzclzcmzcnzcozcpz4lz4wu}5r{ckxclxcmxcnxcoxcpx4lx4wu}5t{ckuclucmucnucoucpu4lu4wu}7q{ckuclucmucnucoucpu4lu}6p{17sw5tw5ou5qu}ek{17st5tt5qu}el{17st5tt5ou5qu}em{17st5tt5qu}en{17st5tt5qu}eo{17st5tt5qu}ep{17st5tt5ou5qu}es{17ss5ts5qu}et{17sw5tw5ou5qu}eu{17sw5tw5ou5qu}ev{17ss5ts5qu}6z{17sw5tw5ou5qu5rs}fm{17sw5tw5ou5qu5rs}fn{17sw5tw5ou5qu5rs}fo{17sw5tw5ou5qu5rs}fp{17sw5tw5ou5qu5rs}fq{17sw5tw5ou5qu5rs}7r{cktcltcmtcntcotcpt4lt5os}fs{17sw5tw5ou5qu5rs}ft{17su5tu5ou5qu}7m{5os}fv{17su5tu5ou5qu}fw{17su5tu5ou5qu}fz{cksclscmscnscoscps4ls}}}\"),\n      Symbol: K(\"{'widths'{k3uaw4r19m3m2k1t2l2l202m2y2n3m2p5n202q6o3k3m2s2l2t2l2v3r2w1t3m3m2y1t2z1wbk2sbl3r'fof'6o3n3m3o3m3p3m3q3m3r3m3s3m3t3m3u1w3v1w3w3r3x3r3y3r3z2wbp3t3l3m5v2l5x2l5z3m2q4yfr3r7v3k7w1o7x3k}'kerning'{'fof'-6o}}\"),\n      Helvetica: K(\"{'widths'{k3p2q4mcx1w201n3r201o6o201s1q201t1q201u1q201w2l201x2l201y2l2k1w2l1w202m2n2n3r2o3r2p5t202q6o2r1n2s2l2t2l2u2r2v3u2w1w2x2l2y1w2z1w3k3r3l3r3m3r3n3r3o3r3p3r3q3r3r3r3s3r203t2l203u2l3v1w3w3u3x3u3y3u3z3r4k6p4l4m4m4m4n4s4o4s4p4m4q3x4r4y4s4s4t1w4u3m4v4m4w3r4x5n4y4s4z4y5k4m5l4y5m4s5n4m5o3x5p4s5q4m5r5y5s4m5t4m5u3x5v1w5w1w5x1w5y2z5z3r6k2l6l3r6m3r6n3m6o3r6p3r6q1w6r3r6s3r6t1q6u1q6v3m6w1q6x5n6y3r6z3r7k3r7l3r7m2l7n3m7o1w7p3r7q3m7r4s7s3m7t3m7u3m7v2l7w1u7x2l7y3u202l3rcl4mal2lam3ran3rao3rap3rar3ras2lat4tau2pav3raw3uay4taz2lbk2sbl3u'fof'6obo2lbp3rbr1wbs2lbu2obv3rbz3xck4m202k3rcm4mcn4mco4mcp4mcq6ocr4scs4mct4mcu4mcv4mcw1w2m2ncy1wcz1wdl4sdm4ydn4ydo4ydp4ydq4yds4ydt4sdu4sdv4sdw4sdz3xek3rel3rem3ren3reo3rep3req5ter3mes3ret3reu3rev3rew1wex1wey1wez1wfl3rfm3rfn3rfo3rfp3rfq3rfr3ufs3xft3rfu3rfv3rfw3rfz3m203k6o212m6o2dw2l2cq2l3t3r3u1w17s4m19m3r}'kerning'{5q{4wv}cl{4qs5kw5ow5qs17sv5tv}201t{2wu4w1k2yu}201x{2wu4wy2yu}17s{2ktclucmucnu4otcpu4lu4wycoucku}2w{7qs4qz5k1m17sy5ow5qx5rsfsu5ty7tufzu}2x{17sy5ty5oy5qs}2y{7qs4qz5k1m17sy5ow5qx5rsfsu5ty7tufzu}'fof'-6o7p{17sv5tv5ow}ck{4qs5kw5ow5qs17sv5tv}4l{4qs5kw5ow5qs17sv5tv}cm{4qs5kw5ow5qs17sv5tv}cn{4qs5kw5ow5qs17sv5tv}co{4qs5kw5ow5qs17sv5tv}cp{4qs5kw5ow5qs17sv5tv}6l{17sy5ty5ow}do{17st5tt}4z{17st5tt}7s{fst}dm{17st5tt}dn{17st5tt}5o{ckwclwcmwcnwcowcpw4lw4wv}dp{17st5tt}dq{17st5tt}7t{5ow}ds{17st5tt}5t{2ktclucmucnu4otcpu4lu4wycoucku}fu{17sv5tv5ow}6p{17sy5ty5ow5qs}ek{17sy5ty5ow}el{17sy5ty5ow}em{17sy5ty5ow}en{5ty}eo{17sy5ty5ow}ep{17sy5ty5ow}es{17sy5ty5qs}et{17sy5ty5ow5qs}eu{17sy5ty5ow5qs}ev{17sy5ty5ow5qs}6z{17sy5ty5ow5qs}fm{17sy5ty5ow5qs}fn{17sy5ty5ow5qs}fo{17sy5ty5ow5qs}fp{17sy5ty5qs}fq{17sy5ty5ow5qs}7r{5ow}fs{17sy5ty5ow5qs}ft{17sv5tv5ow}7m{5ow}fv{17sv5tv5ow}fw{17sv5tv5ow}}}\"),\n      \"Helvetica-BoldOblique\": K(\"{'widths'{k3s2q4scx1w201n3r201o6o201s1w201t1w201u1w201w3m201x3m201y3m2k1w2l2l202m2n2n3r2o3r2p5t202q6o2r1s2s2l2t2l2u2r2v3u2w1w2x2l2y1w2z1w3k3r3l3r3m3r3n3r3o3r3p3r3q3r3r3r3s3r203t2l203u2l3v2l3w3u3x3u3y3u3z3x4k6l4l4s4m4s4n4s4o4s4p4m4q3x4r4y4s4s4t1w4u3r4v4s4w3x4x5n4y4s4z4y5k4m5l4y5m4s5n4m5o3x5p4s5q4m5r5y5s4m5t4m5u3x5v2l5w1w5x2l5y3u5z3r6k2l6l3r6m3x6n3r6o3x6p3r6q2l6r3x6s3x6t1w6u1w6v3r6w1w6x5t6y3x6z3x7k3x7l3x7m2r7n3r7o2l7p3x7q3r7r4y7s3r7t3r7u3m7v2r7w1w7x2r7y3u202l3rcl4sal2lam3ran3rao3rap3rar3ras2lat4tau2pav3raw3uay4taz2lbk2sbl3u'fof'6obo2lbp3xbq3rbr1wbs2lbu2obv3rbz3xck4s202k3rcm4scn4sco4scp4scq6ocr4scs4mct4mcu4mcv4mcw1w2m2zcy1wcz1wdl4sdm4ydn4ydo4ydp4ydq4yds4ydt4sdu4sdv4sdw4sdz3xek3rel3rem3ren3reo3rep3req5ter3res3ret3reu3rev3rew1wex1wey1wez1wfl3xfm3xfn3xfo3xfp3xfq3xfr3ufs3xft3xfu3xfv3xfw3xfz3r203k6o212m6o2dw2l2cq2l3t3r3u2l17s4m19m3r}'kerning'{cl{4qs5ku5ot5qs17sv5tv}201t{2ww4wy2yw}201w{2ks}201x{2ww4wy2yw}2k{201ts201xs}2w{7qs4qu5kw5os5qw5rs17su5tu7tsfzs}2x{5ow5qs}2y{7qs4qu5kw5os5qw5rs17su5tu7tsfzs}'fof'-6o7p{17su5tu5ot}ck{4qs5ku5ot5qs17sv5tv}4l{4qs5ku5ot5qs17sv5tv}cm{4qs5ku5ot5qs17sv5tv}cn{4qs5ku5ot5qs17sv5tv}co{4qs5ku5ot5qs17sv5tv}cp{4qs5ku5ot5qs17sv5tv}6l{17st5tt5os}17s{2kwclvcmvcnvcovcpv4lv4wwckv}5o{2kucltcmtcntcotcpt4lt4wtckt}5q{2ksclscmscnscoscps4ls4wvcks}5r{2ks4ws}5t{2kwclvcmvcnvcovcpv4lv4wwckv}eo{17st5tt5os}fu{17su5tu5ot}6p{17ss5ts}ek{17st5tt5os}el{17st5tt5os}em{17st5tt5os}en{17st5tt5os}6o{201ts}ep{17st5tt5os}es{17ss5ts}et{17ss5ts}eu{17ss5ts}ev{17ss5ts}6z{17su5tu5os5qt}fm{17su5tu5os5qt}fn{17su5tu5os5qt}fo{17su5tu5os5qt}fp{17su5tu5os5qt}fq{17su5tu5os5qt}fs{17su5tu5os5qt}ft{17su5tu5ot}7m{5os}fv{17su5tu5ot}fw{17su5tu5ot}}}\"),\n      ZapfDingbats: K(\"{'widths'{k4u2k1w'fof'6o}'kerning'{'fof'-6o}}\"),\n      \"Courier-Bold\": K(\"{'widths'{k3w'fof'6o}'kerning'{'fof'-6o}}\"),\n      \"Times-Italic\": K(\"{'widths'{k3n2q4ycx2l201n3m201o5t201s2l201t2l201u2l201w3r201x3r201y3r2k1t2l2l202m2n2n3m2o3m2p5n202q5t2r1p2s2l2t2l2u3m2v4n2w1t2x2l2y1t2z1w3k3m3l3m3m3m3n3m3o3m3p3m3q3m3r3m3s3m203t2l203u2l3v2l3w4n3x4n3y4n3z3m4k5w4l3x4m3x4n4m4o4s4p3x4q3x4r4s4s4s4t2l4u2w4v4m4w3r4x5n4y4m4z4s5k3x5l4s5m3x5n3m5o3r5p4s5q3x5r5n5s3x5t3r5u3r5v2r5w1w5x2r5y2u5z3m6k2l6l3m6m3m6n2w6o3m6p2w6q1w6r3m6s3m6t1w6u1w6v2w6w1w6x4s6y3m6z3m7k3m7l3m7m2r7n2r7o1w7p3m7q2w7r4m7s2w7t2w7u2r7v2s7w1v7x2s7y3q202l3mcl3xal2ram3man3mao3map3mar3mas2lat4wau1vav3maw4nay4waz2lbk2sbl4n'fof'6obo2lbp3mbq3obr1tbs2lbu1zbv3mbz3mck3x202k3mcm3xcn3xco3xcp3xcq5tcr4mcs3xct3xcu3xcv3xcw2l2m2ucy2lcz2ldl4mdm4sdn4sdo4sdp4sdq4sds4sdt4sdu4sdv4sdw4sdz3mek3mel3mem3men3meo3mep3meq4mer2wes2wet2weu2wev2wew1wex1wey1wez1wfl3mfm3mfn3mfo3mfp3mfq3mfr4nfs3mft3mfu3mfv3mfw3mfz2w203k6o212m6m2dw2l2cq2l3t3m3u2l17s3r19m3m}'kerning'{cl{5kt4qw}201s{201sw}201t{201tw2wy2yy6q-t}201x{2wy2yy}2k{201tw}2w{7qs4qy7rs5ky7mw5os5qx5ru17su5tu}2x{17ss5ts5os}2y{7qs4qy7rs5ky7mw5os5qx5ru17su5tu}'fof'-6o6t{17ss5ts5qs}7t{5os}3v{5qs}7p{17su5tu5qs}ck{5kt4qw}4l{5kt4qw}cm{5kt4qw}cn{5kt4qw}co{5kt4qw}cp{5kt4qw}6l{4qs5ks5ou5qw5ru17su5tu}17s{2ks}5q{ckvclvcmvcnvcovcpv4lv}5r{ckuclucmucnucoucpu4lu}5t{2ks}6p{4qs5ks5ou5qw5ru17su5tu}ek{4qs5ks5ou5qw5ru17su5tu}el{4qs5ks5ou5qw5ru17su5tu}em{4qs5ks5ou5qw5ru17su5tu}en{4qs5ks5ou5qw5ru17su5tu}eo{4qs5ks5ou5qw5ru17su5tu}ep{4qs5ks5ou5qw5ru17su5tu}es{5ks5qs4qs}et{4qs5ks5ou5qw5ru17su5tu}eu{4qs5ks5qw5ru17su5tu}ev{5ks5qs4qs}ex{17ss5ts5qs}6z{4qv5ks5ou5qw5ru17su5tu}fm{4qv5ks5ou5qw5ru17su5tu}fn{4qv5ks5ou5qw5ru17su5tu}fo{4qv5ks5ou5qw5ru17su5tu}fp{4qv5ks5ou5qw5ru17su5tu}fq{4qv5ks5ou5qw5ru17su5tu}7r{5os}fs{4qv5ks5ou5qw5ru17su5tu}ft{17su5tu5qs}fu{17su5tu5qs}fv{17su5tu5qs}fw{17su5tu5qs}}}\"),\n      \"Times-Roman\": K(\"{'widths'{k3n2q4ycx2l201n3m201o6o201s2l201t2l201u2l201w2w201x2w201y2w2k1t2l2l202m2n2n3m2o3m2p5n202q6o2r1m2s2l2t2l2u3m2v3s2w1t2x2l2y1t2z1w3k3m3l3m3m3m3n3m3o3m3p3m3q3m3r3m3s3m203t2l203u2l3v1w3w3s3x3s3y3s3z2w4k5w4l4s4m4m4n4m4o4s4p3x4q3r4r4s4s4s4t2l4u2r4v4s4w3x4x5t4y4s4z4s5k3r5l4s5m4m5n3r5o3x5p4s5q4s5r5y5s4s5t4s5u3x5v2l5w1w5x2l5y2z5z3m6k2l6l2w6m3m6n2w6o3m6p2w6q2l6r3m6s3m6t1w6u1w6v3m6w1w6x4y6y3m6z3m7k3m7l3m7m2l7n2r7o1w7p3m7q3m7r4s7s3m7t3m7u2w7v3k7w1o7x3k7y3q202l3mcl4sal2lam3man3mao3map3mar3mas2lat4wau1vav3maw3say4waz2lbk2sbl3s'fof'6obo2lbp3mbq2xbr1tbs2lbu1zbv3mbz2wck4s202k3mcm4scn4sco4scp4scq5tcr4mcs3xct3xcu3xcv3xcw2l2m2tcy2lcz2ldl4sdm4sdn4sdo4sdp4sdq4sds4sdt4sdu4sdv4sdw4sdz3mek2wel2wem2wen2weo2wep2weq4mer2wes2wet2weu2wev2wew1wex1wey1wez1wfl3mfm3mfn3mfo3mfp3mfq3mfr3sfs3mft3mfu3mfv3mfw3mfz3m203k6o212m6m2dw2l2cq2l3t3m3u1w17s4s19m3m}'kerning'{cl{4qs5ku17sw5ou5qy5rw201ss5tw201ws}201s{201ss}201t{ckw4lwcmwcnwcowcpwclw4wu201ts}2k{201ts}2w{4qs5kw5os5qx5ru17sx5tx}2x{17sw5tw5ou5qu}2y{4qs5kw5os5qx5ru17sx5tx}'fof'-6o7t{ckuclucmucnucoucpu4lu5os5rs}3u{17su5tu5qs}3v{17su5tu5qs}7p{17sw5tw5qs}ck{4qs5ku17sw5ou5qy5rw201ss5tw201ws}4l{4qs5ku17sw5ou5qy5rw201ss5tw201ws}cm{4qs5ku17sw5ou5qy5rw201ss5tw201ws}cn{4qs5ku17sw5ou5qy5rw201ss5tw201ws}co{4qs5ku17sw5ou5qy5rw201ss5tw201ws}cp{4qs5ku17sw5ou5qy5rw201ss5tw201ws}6l{17su5tu5os5qw5rs}17s{2ktclvcmvcnvcovcpv4lv4wuckv}5o{ckwclwcmwcnwcowcpw4lw4wu}5q{ckyclycmycnycoycpy4ly4wu5ms}5r{cktcltcmtcntcotcpt4lt4ws}5t{2ktclvcmvcnvcovcpv4lv4wuckv}7q{cksclscmscnscoscps4ls}6p{17su5tu5qw5rs}ek{5qs5rs}el{17su5tu5os5qw5rs}em{17su5tu5os5qs5rs}en{17su5qs5rs}eo{5qs5rs}ep{17su5tu5os5qw5rs}es{5qs}et{17su5tu5qw5rs}eu{17su5tu5qs5rs}ev{5qs}6z{17sv5tv5os5qx5rs}fm{5os5qt5rs}fn{17sv5tv5os5qx5rs}fo{17sv5tv5os5qx5rs}fp{5os5qt5rs}fq{5os5qt5rs}7r{ckuclucmucnucoucpu4lu5os}fs{17sv5tv5os5qx5rs}ft{17ss5ts5qs}fu{17sw5tw5qs}fv{17sw5tw5qs}fw{17ss5ts5qs}fz{ckuclucmucnucoucpu4lu5os5rs}}}\"),\n      \"Helvetica-Oblique\": K(\"{'widths'{k3p2q4mcx1w201n3r201o6o201s1q201t1q201u1q201w2l201x2l201y2l2k1w2l1w202m2n2n3r2o3r2p5t202q6o2r1n2s2l2t2l2u2r2v3u2w1w2x2l2y1w2z1w3k3r3l3r3m3r3n3r3o3r3p3r3q3r3r3r3s3r203t2l203u2l3v1w3w3u3x3u3y3u3z3r4k6p4l4m4m4m4n4s4o4s4p4m4q3x4r4y4s4s4t1w4u3m4v4m4w3r4x5n4y4s4z4y5k4m5l4y5m4s5n4m5o3x5p4s5q4m5r5y5s4m5t4m5u3x5v1w5w1w5x1w5y2z5z3r6k2l6l3r6m3r6n3m6o3r6p3r6q1w6r3r6s3r6t1q6u1q6v3m6w1q6x5n6y3r6z3r7k3r7l3r7m2l7n3m7o1w7p3r7q3m7r4s7s3m7t3m7u3m7v2l7w1u7x2l7y3u202l3rcl4mal2lam3ran3rao3rap3rar3ras2lat4tau2pav3raw3uay4taz2lbk2sbl3u'fof'6obo2lbp3rbr1wbs2lbu2obv3rbz3xck4m202k3rcm4mcn4mco4mcp4mcq6ocr4scs4mct4mcu4mcv4mcw1w2m2ncy1wcz1wdl4sdm4ydn4ydo4ydp4ydq4yds4ydt4sdu4sdv4sdw4sdz3xek3rel3rem3ren3reo3rep3req5ter3mes3ret3reu3rev3rew1wex1wey1wez1wfl3rfm3rfn3rfo3rfp3rfq3rfr3ufs3xft3rfu3rfv3rfw3rfz3m203k6o212m6o2dw2l2cq2l3t3r3u1w17s4m19m3r}'kerning'{5q{4wv}cl{4qs5kw5ow5qs17sv5tv}201t{2wu4w1k2yu}201x{2wu4wy2yu}17s{2ktclucmucnu4otcpu4lu4wycoucku}2w{7qs4qz5k1m17sy5ow5qx5rsfsu5ty7tufzu}2x{17sy5ty5oy5qs}2y{7qs4qz5k1m17sy5ow5qx5rsfsu5ty7tufzu}'fof'-6o7p{17sv5tv5ow}ck{4qs5kw5ow5qs17sv5tv}4l{4qs5kw5ow5qs17sv5tv}cm{4qs5kw5ow5qs17sv5tv}cn{4qs5kw5ow5qs17sv5tv}co{4qs5kw5ow5qs17sv5tv}cp{4qs5kw5ow5qs17sv5tv}6l{17sy5ty5ow}do{17st5tt}4z{17st5tt}7s{fst}dm{17st5tt}dn{17st5tt}5o{ckwclwcmwcnwcowcpw4lw4wv}dp{17st5tt}dq{17st5tt}7t{5ow}ds{17st5tt}5t{2ktclucmucnu4otcpu4lu4wycoucku}fu{17sv5tv5ow}6p{17sy5ty5ow5qs}ek{17sy5ty5ow}el{17sy5ty5ow}em{17sy5ty5ow}en{5ty}eo{17sy5ty5ow}ep{17sy5ty5ow}es{17sy5ty5qs}et{17sy5ty5ow5qs}eu{17sy5ty5ow5qs}ev{17sy5ty5ow5qs}6z{17sy5ty5ow5qs}fm{17sy5ty5ow5qs}fn{17sy5ty5ow5qs}fo{17sy5ty5ow5qs}fp{17sy5ty5qs}fq{17sy5ty5ow5qs}7r{5ow}fs{17sy5ty5ow5qs}ft{17sv5tv5ow}7m{5ow}fv{17sv5tv5ow}fw{17sv5tv5ow}}}\")\n    }\n  }, X.events.push([\"addFont\", function (t) {\n    var e,\n      n,\n      r,\n      i = t.font,\n      o = \"Unicode\";\n    (e = $[o][i.postScriptName]) && ((n = i.metadata[o] ? i.metadata[o] : i.metadata[o] = {}).widths = e.widths, n.kerning = e.kerning), (r = Q[o][i.postScriptName]) && ((n = i.metadata[o] ? i.metadata[o] : i.metadata[o] = {}).encoding = r).codePages && r.codePages.length && (i.encoding = r.codePages[0]);\n  }]),\n  /**\n     * @license\n     * Licensed under the MIT License.\n     * http://opensource.org/licenses/mit-license\n     */\n  tt = lt, \"undefined\" != typeof self && self || \"undefined\" != typeof global && global || \"undefined\" != typeof window && window || Function(\"return this\")(), tt.API.events.push([\"addFont\", function (t) {\n    var e = t.font,\n      n = t.instance;\n    if (void 0 !== n && n.existsFileInVFS(e.postScriptName)) {\n      var r = n.getFileFromVFS(e.postScriptName);\n      if (\"string\" != typeof r) throw new Error(\"Font is not stored as string-data in vFS, import fonts or remove declaration doc.addFont('\" + e.postScriptName + \"').\");\n      e.metadata = tt.API.TTFFont.open(e.postScriptName, e.fontName, r, e.encoding), e.metadata.Unicode = e.metadata.Unicode || {\n        encoding: {},\n        kerning: {},\n        widths: []\n      }, e.metadata.glyIdsUsed = [0];\n    } else if (!1 === e.isStandardFont) throw new Error(\"Font does not exist in vFS, import fonts or remove declaration doc.addFont('\" + e.postScriptName + \"').\");\n  }]), (\n  /** @license\n     * Copyright (c) 2012 Willow Systems Corporation, willow-systems.com\n     * \n     * \n     * ====================================================================\n     */\n  et = lt.API).addSvg = function (t, e, n, r, i) {\n    if (void 0 === e || void 0 === n) throw new Error(\"addSVG needs values for 'x' and 'y'\");\n    function o(t) {\n      for (var e = parseFloat(t[1]), n = parseFloat(t[2]), r = [], i = 3, o = t.length; i < o;) \"c\" === t[i] ? (r.push([parseFloat(t[i + 1]), parseFloat(t[i + 2]), parseFloat(t[i + 3]), parseFloat(t[i + 4]), parseFloat(t[i + 5]), parseFloat(t[i + 6])]), i += 7) : \"l\" === t[i] ? (r.push([parseFloat(t[i + 1]), parseFloat(t[i + 2])]), i += 3) : i += 1;\n      return [e, n, r];\n    }\n    var a,\n      s,\n      l,\n      h,\n      u,\n      c,\n      f,\n      p,\n      d = (h = document, p = h.createElement(\"iframe\"), u = \".jsPDF_sillysvg_iframe {display:none;position:absolute;}\", (f = (c = h).createElement(\"style\")).type = \"text/css\", f.styleSheet ? f.styleSheet.cssText = u : f.appendChild(c.createTextNode(u)), c.getElementsByTagName(\"head\")[0].appendChild(f), p.name = \"childframe\", p.setAttribute(\"width\", 0), p.setAttribute(\"height\", 0), p.setAttribute(\"frameborder\", \"0\"), p.setAttribute(\"scrolling\", \"no\"), p.setAttribute(\"seamless\", \"seamless\"), p.setAttribute(\"class\", \"jsPDF_sillysvg_iframe\"), h.body.appendChild(p), p),\n      g = (a = t, (l = ((s = d).contentWindow || s.contentDocument).document).write(a), l.close(), l.getElementsByTagName(\"svg\")[0]),\n      m = [1, 1],\n      y = parseFloat(g.getAttribute(\"width\")),\n      v = parseFloat(g.getAttribute(\"height\"));\n    y && v && (r && i ? m = [r / y, i / v] : r ? m = [r / y, r / y] : i && (m = [i / v, i / v]));\n    var w,\n      b,\n      x,\n      N,\n      L = g.childNodes;\n    for (w = 0, b = L.length; w < b; w++) (x = L[w]).tagName && \"PATH\" === x.tagName.toUpperCase() && ((N = o(x.getAttribute(\"d\").split(\" \")))[0] = N[0] * m[0] + e, N[1] = N[1] * m[1] + n, this.lines.call(this, N[2], N[0], N[1], m));\n    return this;\n  }, et.addSVG = et.addSvg, et.addSvgAsImage = function (t, e, n, r, i, o, a, s) {\n    if (isNaN(e) || isNaN(n)) throw console.error(\"jsPDF.addSvgAsImage: Invalid coordinates\", arguments), new Error(\"Invalid coordinates passed to jsPDF.addSvgAsImage\");\n    if (isNaN(r) || isNaN(i)) throw console.error(\"jsPDF.addSvgAsImage: Invalid measurements\", arguments), new Error(\"Invalid measurements (width and/or height) passed to jsPDF.addSvgAsImage\");\n    var l = document.createElement(\"canvas\");\n    l.width = r, l.height = i;\n    var h = l.getContext(\"2d\");\n    return h.fillStyle = \"#fff\", h.fillRect(0, 0, l.width, l.height), canvg(l, t, {\n      ignoreMouse: !0,\n      ignoreAnimation: !0,\n      ignoreDimensions: !0,\n      ignoreClear: !0\n    }), this.addImage(l.toDataURL(\"image/jpeg\", 1), e, n, r, i, a, s), this;\n  }, lt.API.putTotalPages = function (t) {\n    var e,\n      n = 0;\n    n = parseInt(this.internal.getFont().id.substr(1), 10) < 15 ? (e = new RegExp(t, \"g\"), this.internal.getNumberOfPages()) : (e = new RegExp(this.pdfEscape16(t, this.internal.getFont()), \"g\"), this.pdfEscape16(this.internal.getNumberOfPages() + \"\", this.internal.getFont()));\n    for (var r = 1; r <= this.internal.getNumberOfPages(); r++) for (var i = 0; i < this.internal.pages[r].length; i++) this.internal.pages[r][i] = this.internal.pages[r][i].replace(e, n);\n    return this;\n  }, lt.API.viewerPreferences = function (t, e) {\n    var n;\n    t = t || {}, e = e || !1;\n    var r,\n      i,\n      o = {\n        HideToolbar: {\n          defaultValue: !1,\n          value: !1,\n          type: \"boolean\",\n          explicitSet: !1,\n          valueSet: [!0, !1],\n          pdfVersion: 1.3\n        },\n        HideMenubar: {\n          defaultValue: !1,\n          value: !1,\n          type: \"boolean\",\n          explicitSet: !1,\n          valueSet: [!0, !1],\n          pdfVersion: 1.3\n        },\n        HideWindowUI: {\n          defaultValue: !1,\n          value: !1,\n          type: \"boolean\",\n          explicitSet: !1,\n          valueSet: [!0, !1],\n          pdfVersion: 1.3\n        },\n        FitWindow: {\n          defaultValue: !1,\n          value: !1,\n          type: \"boolean\",\n          explicitSet: !1,\n          valueSet: [!0, !1],\n          pdfVersion: 1.3\n        },\n        CenterWindow: {\n          defaultValue: !1,\n          value: !1,\n          type: \"boolean\",\n          explicitSet: !1,\n          valueSet: [!0, !1],\n          pdfVersion: 1.3\n        },\n        DisplayDocTitle: {\n          defaultValue: !1,\n          value: !1,\n          type: \"boolean\",\n          explicitSet: !1,\n          valueSet: [!0, !1],\n          pdfVersion: 1.4\n        },\n        NonFullScreenPageMode: {\n          defaultValue: \"UseNone\",\n          value: \"UseNone\",\n          type: \"name\",\n          explicitSet: !1,\n          valueSet: [\"UseNone\", \"UseOutlines\", \"UseThumbs\", \"UseOC\"],\n          pdfVersion: 1.3\n        },\n        Direction: {\n          defaultValue: \"L2R\",\n          value: \"L2R\",\n          type: \"name\",\n          explicitSet: !1,\n          valueSet: [\"L2R\", \"R2L\"],\n          pdfVersion: 1.3\n        },\n        ViewArea: {\n          defaultValue: \"CropBox\",\n          value: \"CropBox\",\n          type: \"name\",\n          explicitSet: !1,\n          valueSet: [\"MediaBox\", \"CropBox\", \"TrimBox\", \"BleedBox\", \"ArtBox\"],\n          pdfVersion: 1.4\n        },\n        ViewClip: {\n          defaultValue: \"CropBox\",\n          value: \"CropBox\",\n          type: \"name\",\n          explicitSet: !1,\n          valueSet: [\"MediaBox\", \"CropBox\", \"TrimBox\", \"BleedBox\", \"ArtBox\"],\n          pdfVersion: 1.4\n        },\n        PrintArea: {\n          defaultValue: \"CropBox\",\n          value: \"CropBox\",\n          type: \"name\",\n          explicitSet: !1,\n          valueSet: [\"MediaBox\", \"CropBox\", \"TrimBox\", \"BleedBox\", \"ArtBox\"],\n          pdfVersion: 1.4\n        },\n        PrintClip: {\n          defaultValue: \"CropBox\",\n          value: \"CropBox\",\n          type: \"name\",\n          explicitSet: !1,\n          valueSet: [\"MediaBox\", \"CropBox\", \"TrimBox\", \"BleedBox\", \"ArtBox\"],\n          pdfVersion: 1.4\n        },\n        PrintScaling: {\n          defaultValue: \"AppDefault\",\n          value: \"AppDefault\",\n          type: \"name\",\n          explicitSet: !1,\n          valueSet: [\"AppDefault\", \"None\"],\n          pdfVersion: 1.6\n        },\n        Duplex: {\n          defaultValue: \"\",\n          value: \"none\",\n          type: \"name\",\n          explicitSet: !1,\n          valueSet: [\"Simplex\", \"DuplexFlipShortEdge\", \"DuplexFlipLongEdge\", \"none\"],\n          pdfVersion: 1.7\n        },\n        PickTrayByPDFSize: {\n          defaultValue: !1,\n          value: !1,\n          type: \"boolean\",\n          explicitSet: !1,\n          valueSet: [!0, !1],\n          pdfVersion: 1.7\n        },\n        PrintPageRange: {\n          defaultValue: \"\",\n          value: \"\",\n          type: \"array\",\n          explicitSet: !1,\n          valueSet: null,\n          pdfVersion: 1.7\n        },\n        NumCopies: {\n          defaultValue: 1,\n          value: 1,\n          type: \"integer\",\n          explicitSet: !1,\n          valueSet: null,\n          pdfVersion: 1.7\n        }\n      },\n      a = Object.keys(o),\n      s = [],\n      l = 0,\n      h = 0,\n      u = 0,\n      c = !0;\n    function f(t, e) {\n      var n,\n        r = !1;\n      for (n = 0; n < t.length; n += 1) t[n] === e && (r = !0);\n      return r;\n    }\n    if (void 0 === this.internal.viewerpreferences && (this.internal.viewerpreferences = {}, this.internal.viewerpreferences.configuration = JSON.parse(JSON.stringify(o)), this.internal.viewerpreferences.isSubscribed = !1), n = this.internal.viewerpreferences.configuration, \"reset\" === t || !0 === e) {\n      var p = a.length;\n      for (u = 0; u < p; u += 1) n[a[u]].value = n[a[u]].defaultValue, n[a[u]].explicitSet = !1;\n    }\n    if (\"object\" === se(t)) for (r in t) if (i = t[r], f(a, r) && void 0 !== i) {\n      if (\"boolean\" === n[r].type && \"boolean\" == typeof i) n[r].value = i;else if (\"name\" === n[r].type && f(n[r].valueSet, i)) n[r].value = i;else if (\"integer\" === n[r].type && Number.isInteger(i)) n[r].value = i;else if (\"array\" === n[r].type) {\n        for (l = 0; l < i.length; l += 1) if (c = !0, 1 === i[l].length && \"number\" == typeof i[l][0]) s.push(String(i[l] - 1));else if (1 < i[l].length) {\n          for (h = 0; h < i[l].length; h += 1) \"number\" != typeof i[l][h] && (c = !1);\n          !0 === c && s.push([i[l][0] - 1, i[l][1] - 1].join(\" \"));\n        }\n        n[r].value = \"[\" + s.join(\" \") + \"]\";\n      } else n[r].value = n[r].defaultValue;\n      n[r].explicitSet = !0;\n    }\n    return !1 === this.internal.viewerpreferences.isSubscribed && (this.internal.events.subscribe(\"putCatalog\", function () {\n      var t,\n        e = [];\n      for (t in n) !0 === n[t].explicitSet && (\"name\" === n[t].type ? e.push(\"/\" + t + \" /\" + n[t].value) : e.push(\"/\" + t + \" \" + n[t].value));\n      0 !== e.length && this.internal.write(\"/ViewerPreferences\\n<<\\n\" + e.join(\"\\n\") + \"\\n>>\");\n    }), this.internal.viewerpreferences.isSubscribed = !0), this.internal.viewerpreferences.configuration = n, this;\n  },\n  /** ==================================================================== \n     * jsPDF XMP metadata plugin\n     * Copyright (c) 2016 Jussi Utunen, <EMAIL>\n     * \n     * \n     * ====================================================================\n     */\n  nt = lt.API, ot = it = rt = \"\", nt.addMetadata = function (t, e) {\n    return it = e || \"http://jspdf.default.namespaceuri/\", rt = t, this.internal.events.subscribe(\"postPutResources\", function () {\n      if (rt) {\n        var t = '<rdf:RDF xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\"><rdf:Description rdf:about=\"\" xmlns:jspdf=\"' + it + '\"><jspdf:metadata>',\n          e = unescape(encodeURIComponent('<x:xmpmeta xmlns:x=\"adobe:ns:meta/\">')),\n          n = unescape(encodeURIComponent(t)),\n          r = unescape(encodeURIComponent(rt)),\n          i = unescape(encodeURIComponent(\"</jspdf:metadata></rdf:Description></rdf:RDF>\")),\n          o = unescape(encodeURIComponent(\"</x:xmpmeta>\")),\n          a = n.length + r.length + i.length + e.length + o.length;\n        ot = this.internal.newObject(), this.internal.write(\"<< /Type /Metadata /Subtype /XML /Length \" + a + \" >>\"), this.internal.write(\"stream\"), this.internal.write(e + n + r + i + o), this.internal.write(\"endstream\"), this.internal.write(\"endobj\");\n      } else ot = \"\";\n    }), this.internal.events.subscribe(\"putCatalog\", function () {\n      ot && this.internal.write(\"/Metadata \" + ot + \" 0 R\");\n    }), this;\n  }, function (f, t) {\n    var e = f.API;\n    var m = e.pdfEscape16 = function (t, e) {\n        for (var n, r = e.metadata.Unicode.widths, i = [\"\", \"0\", \"00\", \"000\", \"0000\"], o = [\"\"], a = 0, s = t.length; a < s; ++a) {\n          if (n = e.metadata.characterToGlyph(t.charCodeAt(a)), e.metadata.glyIdsUsed.push(n), e.metadata.toUnicode[n] = t.charCodeAt(a), -1 == r.indexOf(n) && (r.push(n), r.push([parseInt(e.metadata.widthOfGlyph(n), 10)])), \"0\" == n) return o.join(\"\");\n          n = n.toString(16), o.push(i[4 - n.length], n);\n        }\n        return o.join(\"\");\n      },\n      p = function (t) {\n        var e, n, r, i, o, a, s;\n        for (o = \"/CIDInit /ProcSet findresource begin\\n12 dict begin\\nbegincmap\\n/CIDSystemInfo <<\\n  /Registry (Adobe)\\n  /Ordering (UCS)\\n  /Supplement 0\\n>> def\\n/CMapName /Adobe-Identity-UCS def\\n/CMapType 2 def\\n1 begincodespacerange\\n<0000><ffff>\\nendcodespacerange\", r = [], a = 0, s = (n = Object.keys(t).sort(function (t, e) {\n          return t - e;\n        })).length; a < s; a++) e = n[a], 100 <= r.length && (o += \"\\n\" + r.length + \" beginbfchar\\n\" + r.join(\"\\n\") + \"\\nendbfchar\", r = []), i = (\"0000\" + t[e].toString(16)).slice(-4), e = (\"0000\" + (+e).toString(16)).slice(-4), r.push(\"<\" + e + \"><\" + i + \">\");\n        return r.length && (o += \"\\n\" + r.length + \" beginbfchar\\n\" + r.join(\"\\n\") + \"\\nendbfchar\\n\"), o += \"endcmap\\nCMapName currentdict /CMap defineresource pop\\nend\\nend\";\n      };\n    e.events.push([\"putFont\", function (t) {\n      !function (t, e, n, r) {\n        if (t.metadata instanceof f.API.TTFFont && \"Identity-H\" === t.encoding) {\n          for (var i = t.metadata.Unicode.widths, o = t.metadata.subset.encode(t.metadata.glyIdsUsed, 1), a = \"\", s = 0; s < o.length; s++) a += String.fromCharCode(o[s]);\n          var l = n();\n          r({\n            data: a,\n            addLength1: !0\n          }), e(\"endobj\");\n          var h = n();\n          r({\n            data: p(t.metadata.toUnicode),\n            addLength1: !0\n          }), e(\"endobj\");\n          var u = n();\n          e(\"<<\"), e(\"/Type /FontDescriptor\"), e(\"/FontName /\" + t.fontName), e(\"/FontFile2 \" + l + \" 0 R\"), e(\"/FontBBox \" + f.API.PDFObject.convert(t.metadata.bbox)), e(\"/Flags \" + t.metadata.flags), e(\"/StemV \" + t.metadata.stemV), e(\"/ItalicAngle \" + t.metadata.italicAngle), e(\"/Ascent \" + t.metadata.ascender), e(\"/Descent \" + t.metadata.decender), e(\"/CapHeight \" + t.metadata.capHeight), e(\">>\"), e(\"endobj\");\n          var c = n();\n          e(\"<<\"), e(\"/Type /Font\"), e(\"/BaseFont /\" + t.fontName), e(\"/FontDescriptor \" + u + \" 0 R\"), e(\"/W \" + f.API.PDFObject.convert(i)), e(\"/CIDToGIDMap /Identity\"), e(\"/DW 1000\"), e(\"/Subtype /CIDFontType2\"), e(\"/CIDSystemInfo\"), e(\"<<\"), e(\"/Supplement 0\"), e(\"/Registry (Adobe)\"), e(\"/Ordering (\" + t.encoding + \")\"), e(\">>\"), e(\">>\"), e(\"endobj\"), t.objectNumber = n(), e(\"<<\"), e(\"/Type /Font\"), e(\"/Subtype /Type0\"), e(\"/ToUnicode \" + h + \" 0 R\"), e(\"/BaseFont /\" + t.fontName), e(\"/Encoding /\" + t.encoding), e(\"/DescendantFonts [\" + c + \" 0 R]\"), e(\">>\"), e(\"endobj\"), t.isAlreadyPutted = !0;\n        }\n      }(t.font, t.out, t.newObject, t.putStream);\n    }]);\n    e.events.push([\"putFont\", function (t) {\n      !function (t, e, n, r) {\n        if (t.metadata instanceof f.API.TTFFont && \"WinAnsiEncoding\" === t.encoding) {\n          t.metadata.Unicode.widths;\n          for (var i = t.metadata.rawData, o = \"\", a = 0; a < i.length; a++) o += String.fromCharCode(i[a]);\n          var s = n();\n          r({\n            data: o,\n            addLength1: !0\n          }), e(\"endobj\");\n          var l = n();\n          r({\n            data: p(t.metadata.toUnicode),\n            addLength1: !0\n          }), e(\"endobj\");\n          var h = n();\n          for (e(\"<<\"), e(\"/Descent \" + t.metadata.decender), e(\"/CapHeight \" + t.metadata.capHeight), e(\"/StemV \" + t.metadata.stemV), e(\"/Type /FontDescriptor\"), e(\"/FontFile2 \" + s + \" 0 R\"), e(\"/Flags 96\"), e(\"/FontBBox \" + f.API.PDFObject.convert(t.metadata.bbox)), e(\"/FontName /\" + t.fontName), e(\"/ItalicAngle \" + t.metadata.italicAngle), e(\"/Ascent \" + t.metadata.ascender), e(\">>\"), e(\"endobj\"), t.objectNumber = n(), a = 0; a < t.metadata.hmtx.widths.length; a++) t.metadata.hmtx.widths[a] = parseInt(t.metadata.hmtx.widths[a] * (1e3 / t.metadata.head.unitsPerEm));\n          e(\"<</Subtype/TrueType/Type/Font/ToUnicode \" + l + \" 0 R/BaseFont/\" + t.fontName + \"/FontDescriptor \" + h + \" 0 R/Encoding/\" + t.encoding + \" /FirstChar 29 /LastChar 255 /Widths \" + f.API.PDFObject.convert(t.metadata.hmtx.widths) + \">>\"), e(\"endobj\"), t.isAlreadyPutted = !0;\n        }\n      }(t.font, t.out, t.newObject, t.putStream);\n    }]);\n    var h = function (t) {\n      var e,\n        n,\n        r = t.text || \"\",\n        i = t.x,\n        o = t.y,\n        a = t.options || {},\n        s = t.mutex || {},\n        l = s.pdfEscape,\n        h = s.activeFontKey,\n        u = s.fonts,\n        c = (s.activeFontSize, \"\"),\n        f = 0,\n        p = \"\",\n        d = u[n = h].encoding;\n      if (\"Identity-H\" !== u[n].encoding) return {\n        text: r,\n        x: i,\n        y: o,\n        options: a,\n        mutex: s\n      };\n      for (p = r, n = h, \"[object Array]\" === Object.prototype.toString.call(r) && (p = r[0]), f = 0; f < p.length; f += 1) u[n].metadata.hasOwnProperty(\"cmap\") && (e = u[n].metadata.cmap.unicode.codeMap[p[f].charCodeAt(0)]), e ? c += p[f] : p[f].charCodeAt(0) < 256 && u[n].metadata.hasOwnProperty(\"Unicode\") ? c += p[f] : c += \"\";\n      var g = \"\";\n      return parseInt(n.slice(1)) < 14 || \"WinAnsiEncoding\" === d ? g = function (t) {\n        for (var e = \"\", n = 0; n < t.length; n++) e += \"\" + t.charCodeAt(n).toString(16);\n        return e;\n      }(l(c, n)) : \"Identity-H\" === d && (g = m(c, u[n])), s.isHex = !0, {\n        text: g,\n        x: i,\n        y: o,\n        options: a,\n        mutex: s\n      };\n    };\n    e.events.push([\"postProcessText\", function (t) {\n      var e = t.text || \"\",\n        n = t.x,\n        r = t.y,\n        i = t.options,\n        o = t.mutex,\n        a = (i.lang, []),\n        s = {\n          text: e,\n          x: n,\n          y: r,\n          options: i,\n          mutex: o\n        };\n      if (\"[object Array]\" === Object.prototype.toString.call(e)) {\n        var l = 0;\n        for (l = 0; l < e.length; l += 1) \"[object Array]\" === Object.prototype.toString.call(e[l]) && 3 === e[l].length ? a.push([h(Object.assign({}, s, {\n          text: e[l][0]\n        })).text, e[l][1], e[l][2]]) : a.push(h(Object.assign({}, s, {\n          text: e[l]\n        })).text);\n        t.text = a;\n      } else t.text = h(Object.assign({}, s, {\n        text: e\n      })).text;\n    }]);\n  }(lt, \"undefined\" != typeof self && self || \"undefined\" != typeof global && global || \"undefined\" != typeof window && window || Function(\"return this\")()), at = lt.API, st = function (t) {\n    return void 0 !== t && (void 0 === t.vFS && (t.vFS = {}), !0);\n  }, at.existsFileInVFS = function (t) {\n    return !!st(this.internal) && void 0 !== this.internal.vFS[t];\n  }, at.addFileToVFS = function (t, e) {\n    return st(this.internal), this.internal.vFS[t] = e, this;\n  }, at.getFileFromVFS = function (t) {\n    return st(this.internal), void 0 !== this.internal.vFS[t] ? this.internal.vFS[t] : null;\n  }, lt.API.addHTML = function (t, d, g, s, m) {\n    if (\"undefined\" == typeof html2canvas && \"undefined\" == typeof rasterizeHTML) throw new Error(\"You need either https://github.com/niklasvh/html2canvas or https://github.com/cburgmer/rasterizeHTML.js\");\n    \"number\" != typeof d && (s = d, m = g), \"function\" == typeof s && (m = s, s = null), \"function\" != typeof m && (m = function () {});\n    var e = this.internal,\n      y = e.scaleFactor,\n      v = e.pageSize.getWidth(),\n      w = e.pageSize.getHeight();\n    if ((s = s || {}).onrendered = function (l) {\n      d = parseInt(d) || 0, g = parseInt(g) || 0;\n      var t = s.dim || {},\n        h = Object.assign({\n          top: 0,\n          right: 0,\n          bottom: 0,\n          left: 0,\n          useFor: \"content\"\n        }, s.margin),\n        e = t.h || Math.min(w, l.height / y),\n        u = t.w || Math.min(v, l.width / y) - d,\n        c = s.format || \"JPEG\",\n        f = s.imageCompression || \"SLOW\";\n      if (l.height > w - h.top - h.bottom && s.pagesplit) {\n        var p = function (t, e, n, r, i) {\n            var o = document.createElement(\"canvas\");\n            o.height = i, o.width = r;\n            var a = o.getContext(\"2d\");\n            return a.mozImageSmoothingEnabled = !1, a.webkitImageSmoothingEnabled = !1, a.msImageSmoothingEnabled = !1, a.imageSmoothingEnabled = !1, a.fillStyle = s.backgroundColor || \"#ffffff\", a.fillRect(0, 0, r, i), a.drawImage(t, e, n, r, i, 0, 0, r, i), o;\n          },\n          n = function () {\n            for (var t, e, n = 0, r = 0, i = {}, o = !1;;) {\n              var a;\n              if (r = 0, i.top = 0 !== n ? h.top : g, i.left = 0 !== n ? h.left : d, o = (v - h.left - h.right) * y < l.width, \"content\" === h.useFor ? 0 === n ? (t = Math.min((v - h.left) * y, l.width), e = Math.min((w - h.top) * y, l.height - n)) : (t = Math.min(v * y, l.width), e = Math.min(w * y, l.height - n), i.top = 0) : (t = Math.min((v - h.left - h.right) * y, l.width), e = Math.min((w - h.bottom - h.top) * y, l.height - n)), o) for (;;) {\n                \"content\" === h.useFor && (0 === r ? t = Math.min((v - h.left) * y, l.width) : (t = Math.min(v * y, l.width - r), i.left = 0));\n                var s = [a = p(l, r, n, t, e), i.left, i.top, a.width / y, a.height / y, c, null, f];\n                if (this.addImage.apply(this, s), (r += t) >= l.width) break;\n                this.addPage();\n              } else s = [a = p(l, 0, n, t, e), i.left, i.top, a.width / y, a.height / y, c, null, f], this.addImage.apply(this, s);\n              if ((n += e) >= l.height) break;\n              this.addPage();\n            }\n            m(u, n, null, s);\n          }.bind(this);\n        if (\"CANVAS\" === l.nodeName) {\n          var r = new Image();\n          r.onload = n, r.src = l.toDataURL(\"image/png\"), l = r;\n        } else n();\n      } else {\n        var i = Math.random().toString(35),\n          o = [l, d, g, u, e, c, i, f];\n        this.addImage.apply(this, o), m(u, e, i, o);\n      }\n    }.bind(this), \"undefined\" != typeof html2canvas && !s.rstz) return html2canvas(t, s);\n    if (\"undefined\" == typeof rasterizeHTML) return null;\n    var n = \"drawDocument\";\n    return \"string\" == typeof t && (n = /^http/.test(t) ? \"drawURL\" : \"drawHTML\"), s.width = s.width || v * y, rasterizeHTML[n](t, void 0, s).then(function (t) {\n      s.onrendered(t.image);\n    }, function (t) {\n      m(null, t);\n    });\n  },\n  /**\n     * jsPDF fromHTML plugin. BETA stage. API subject to change. Needs browser\n     * Copyright (c) 2012 Willow Systems Corporation, willow-systems.com\n     *               2014 Juan Pablo Gaviria, https://github.com/juanpgaviria\n     *               2014 Diego Casorran, https://github.com/diegocr\n     *               2014 Daniel Husar, https://github.com/danielhusar\n     *               2014 Wolfgang Gassler, https://github.com/woolfg\n     *               2014 Steven Spungin, https://github.com/flamenco\n     *\n     * @license\n     * \n     * ====================================================================\n     */\n  function (t) {\n    var P, k, i, a, s, l, h, u, I, w, f, c, p, n, C, B, d, g, m, j;\n    P = function () {\n      return function (t) {\n        return e.prototype = t, new e();\n      };\n      function e() {}\n    }(), w = function (t) {\n      var e, n, r, i, o, a, s;\n      for (n = 0, r = t.length, e = void 0, a = i = !1; !i && n !== r;) (e = t[n] = t[n].trimLeft()) && (i = !0), n++;\n      for (n = r - 1; r && !a && -1 !== n;) (e = t[n] = t[n].trimRight()) && (a = !0), n--;\n      for (o = /\\s+$/g, s = !0, n = 0; n !== r;) \"\\u2028\" != t[n] && (e = t[n].replace(/\\s+/g, \" \"), s && (e = e.trimLeft()), e && (s = o.test(e)), t[n] = e), n++;\n      return t;\n    }, c = function (t) {\n      var e, n, r;\n      for (e = void 0, n = (r = t.split(\",\")).shift(); !e && n;) e = i[n.trim().toLowerCase()], n = r.shift();\n      return e;\n    }, p = function (t) {\n      var e;\n      return -1 < (t = \"auto\" === t ? \"0px\" : t).indexOf(\"em\") && !isNaN(Number(t.replace(\"em\", \"\"))) && (t = 18.719 * Number(t.replace(\"em\", \"\")) + \"px\"), -1 < t.indexOf(\"pt\") && !isNaN(Number(t.replace(\"pt\", \"\"))) && (t = 1.333 * Number(t.replace(\"pt\", \"\")) + \"px\"), void 0, 16, (e = n[t]) ? e : void 0 !== (e = {\n        \"xx-small\": 9,\n        \"x-small\": 11,\n        small: 13,\n        medium: 16,\n        large: 19,\n        \"x-large\": 23,\n        \"xx-large\": 28,\n        auto: 0\n      }[t]) ? n[t] = e / 16 : (e = parseFloat(t)) ? n[t] = e / 16 : (e = t.match(/([\\d\\.]+)(px)/), Array.isArray(e) && 3 === e.length ? n[t] = parseFloat(e[1]) / 16 : n[t] = 1);\n    }, I = function (t) {\n      var e, n, r, i, o;\n      return o = t, i = document.defaultView && document.defaultView.getComputedStyle ? document.defaultView.getComputedStyle(o, null) : o.currentStyle ? o.currentStyle : o.style, n = void 0, (e = {})[\"font-family\"] = c((r = function (t) {\n        return t = t.replace(/-\\D/g, function (t) {\n          return t.charAt(1).toUpperCase();\n        }), i[t];\n      })(\"font-family\")) || \"times\", e[\"font-style\"] = a[r(\"font-style\")] || \"normal\", e[\"text-align\"] = s[r(\"text-align\")] || \"left\", \"bold\" === (n = l[r(\"font-weight\")] || \"normal\") && (\"normal\" === e[\"font-style\"] ? e[\"font-style\"] = n : e[\"font-style\"] = n + e[\"font-style\"]), e[\"font-size\"] = p(r(\"font-size\")) || 1, e[\"line-height\"] = p(r(\"line-height\")) || 1, e.display = \"inline\" === r(\"display\") ? \"inline\" : \"block\", n = \"block\" === e.display, e[\"margin-top\"] = n && p(r(\"margin-top\")) || 0, e[\"margin-bottom\"] = n && p(r(\"margin-bottom\")) || 0, e[\"padding-top\"] = n && p(r(\"padding-top\")) || 0, e[\"padding-bottom\"] = n && p(r(\"padding-bottom\")) || 0, e[\"margin-left\"] = n && p(r(\"margin-left\")) || 0, e[\"margin-right\"] = n && p(r(\"margin-right\")) || 0, e[\"padding-left\"] = n && p(r(\"padding-left\")) || 0, e[\"padding-right\"] = n && p(r(\"padding-right\")) || 0, e[\"page-break-before\"] = r(\"page-break-before\") || \"auto\", e.float = h[r(\"cssFloat\")] || \"none\", e.clear = u[r(\"clear\")] || \"none\", e.color = r(\"color\"), e;\n    }, C = function (t, e, n) {\n      var r, i, o, a, s;\n      if (o = !1, a = i = void 0, r = n[\"#\" + t.id]) if (\"function\" == typeof r) o = r(t, e);else for (i = 0, a = r.length; !o && i !== a;) o = r[i](t, e), i++;\n      if (r = n[t.nodeName], !o && r) if (\"function\" == typeof r) o = r(t, e);else for (i = 0, a = r.length; !o && i !== a;) o = r[i](t, e), i++;\n      for (s = \"string\" == typeof t.className ? t.className.split(\" \") : [], i = 0; i < s.length; i++) if (r = n[\".\" + s[i]], !o && r) if (\"function\" == typeof r) o = r(t, e);else for (i = 0, a = r.length; !o && i !== a;) o = r[i](t, e), i++;\n      return o;\n    }, j = function (t, e) {\n      var n, r, i, o, a, s, l, h, u;\n      for (n = [], r = [], i = 0, u = t.rows[0].cells.length, l = t.clientWidth; i < u;) h = t.rows[0].cells[i], r[i] = {\n        name: h.textContent.toLowerCase().replace(/\\s+/g, \"\"),\n        prompt: h.textContent.replace(/\\r?\\n/g, \"\"),\n        width: h.clientWidth / l * e.pdf.internal.pageSize.getWidth()\n      }, i++;\n      for (i = 1; i < t.rows.length;) {\n        for (s = t.rows[i], a = {}, o = 0; o < s.cells.length;) a[r[o].name] = s.cells[o].textContent.replace(/\\r?\\n/g, \"\"), o++;\n        n.push(a), i++;\n      }\n      return {\n        rows: n,\n        headers: r\n      };\n    };\n    var E = {\n        SCRIPT: 1,\n        STYLE: 1,\n        NOSCRIPT: 1,\n        OBJECT: 1,\n        EMBED: 1,\n        SELECT: 1\n      },\n      M = 1;\n    k = function (t, i, e) {\n      var n, r, o, a, s, l, h, u;\n      for (r = t.childNodes, n = void 0, (s = \"block\" === (o = I(t)).display) && (i.setBlockBoundary(), i.setBlockStyle(o)), a = 0, l = r.length; a < l;) {\n        if (\"object\" === se(n = r[a])) {\n          if (i.executeWatchFunctions(n), 1 === n.nodeType && \"HEADER\" === n.nodeName) {\n            var c = n,\n              f = i.pdf.margins_doc.top;\n            i.pdf.internal.events.subscribe(\"addPage\", function (t) {\n              i.y = f, k(c, i, e), i.pdf.margins_doc.top = i.y + 10, i.y += 10;\n            }, !1);\n          }\n          if (8 === n.nodeType && \"#comment\" === n.nodeName) ~n.textContent.indexOf(\"ADD_PAGE\") && (i.pdf.addPage(), i.y = i.pdf.margins_doc.top);else if (1 !== n.nodeType || E[n.nodeName]) {\n            if (3 === n.nodeType) {\n              var p = n.nodeValue;\n              if (n.nodeValue && \"LI\" === n.parentNode.nodeName) if (\"OL\" === n.parentNode.parentNode.nodeName) p = M++ + \". \" + p;else {\n                var d = o[\"font-size\"],\n                  g = (3 - .75 * d) * i.pdf.internal.scaleFactor,\n                  m = .75 * d * i.pdf.internal.scaleFactor,\n                  y = 1.74 * d / i.pdf.internal.scaleFactor;\n                u = function (t, e) {\n                  this.pdf.circle(t + g, e + m, y, \"FD\");\n                };\n              }\n              16 & n.ownerDocument.body.compareDocumentPosition(n) && i.addText(p, o);\n            } else \"string\" == typeof n && i.addText(n, o);\n          } else {\n            var v;\n            if (\"IMG\" === n.nodeName) {\n              var w = n.getAttribute(\"src\");\n              v = B[i.pdf.sHashCode(w) || w];\n            }\n            if (v) {\n              i.pdf.internal.pageSize.getHeight() - i.pdf.margins_doc.bottom < i.y + n.height && i.y > i.pdf.margins_doc.top && (i.pdf.addPage(), i.y = i.pdf.margins_doc.top, i.executeWatchFunctions(n));\n              var b = I(n),\n                x = i.x,\n                N = 12 / i.pdf.internal.scaleFactor,\n                L = (b[\"margin-left\"] + b[\"padding-left\"]) * N,\n                A = (b[\"margin-right\"] + b[\"padding-right\"]) * N,\n                S = (b[\"margin-top\"] + b[\"padding-top\"]) * N,\n                _ = (b[\"margin-bottom\"] + b[\"padding-bottom\"]) * N;\n              void 0 !== b.float && \"right\" === b.float ? x += i.settings.width - n.width - A : x += L, i.pdf.addImage(v, x, i.y + S, n.width, n.height), v = void 0, \"right\" === b.float || \"left\" === b.float ? (i.watchFunctions.push(function (t, e, n, r) {\n                return i.y >= e ? (i.x += t, i.settings.width += n, !0) : !!(r && 1 === r.nodeType && !E[r.nodeName] && i.x + r.width > i.pdf.margins_doc.left + i.pdf.margins_doc.width) && (i.x += t, i.y = e, i.settings.width += n, !0);\n              }.bind(this, \"left\" === b.float ? -n.width - L - A : 0, i.y + n.height + S + _, n.width)), i.watchFunctions.push(function (t, e, n) {\n                return !(i.y < t && e === i.pdf.internal.getNumberOfPages()) || 1 === n.nodeType && \"both\" === I(n).clear && (i.y = t, !0);\n              }.bind(this, i.y + n.height, i.pdf.internal.getNumberOfPages())), i.settings.width -= n.width + L + A, \"left\" === b.float && (i.x += n.width + L + A)) : i.y += n.height + S + _;\n            } else if (\"TABLE\" === n.nodeName) h = j(n, i), i.y += 10, i.pdf.table(i.x, i.y, h.rows, h.headers, {\n              autoSize: !1,\n              printHeaders: e.printHeaders,\n              margins: i.pdf.margins_doc,\n              css: I(n)\n            }), i.y = i.pdf.lastCellPos.y + i.pdf.lastCellPos.h + 20;else if (\"OL\" === n.nodeName || \"UL\" === n.nodeName) M = 1, C(n, i, e) || k(n, i, e), i.y += 10;else if (\"LI\" === n.nodeName) {\n              var F = i.x;\n              i.x += 20 / i.pdf.internal.scaleFactor, i.y += 3, C(n, i, e) || k(n, i, e), i.x = F;\n            } else \"BR\" === n.nodeName ? (i.y += o[\"font-size\"] * i.pdf.internal.scaleFactor, i.addText(\"\\u2028\", P(o))) : C(n, i, e) || k(n, i, e);\n          }\n        }\n        a++;\n      }\n      if (e.outY = i.y, s) return i.setBlockBoundary(u);\n    }, B = {}, d = function (t, o, e, n) {\n      var a,\n        r = t.getElementsByTagName(\"img\"),\n        i = r.length,\n        s = 0;\n      function l() {\n        o.pdf.internal.events.publish(\"imagesLoaded\"), n(a);\n      }\n      function h(e, n, r) {\n        if (e) {\n          var i = new Image();\n          a = ++s, i.crossOrigin = \"\", i.onerror = i.onload = function () {\n            if (i.complete && (0 === i.src.indexOf(\"data:image/\") && (i.width = n || i.width || 0, i.height = r || i.height || 0), i.width + i.height)) {\n              var t = o.pdf.sHashCode(e) || e;\n              B[t] = B[t] || i;\n            }\n            --s || l();\n          }, i.src = e;\n        }\n      }\n      for (; i--;) h(r[i].getAttribute(\"src\"), r[i].width, r[i].height);\n      return s || l();\n    }, g = function (t, o, a) {\n      var s = t.getElementsByTagName(\"footer\");\n      if (0 < s.length) {\n        s = s[0];\n        var e = o.pdf.internal.write,\n          n = o.y;\n        o.pdf.internal.write = function () {}, k(s, o, a);\n        var l = Math.ceil(o.y - n) + 5;\n        o.y = n, o.pdf.internal.write = e, o.pdf.margins_doc.bottom += l;\n        for (var r = function (t) {\n            var e = void 0 !== t ? t.pageNumber : 1,\n              n = o.y;\n            o.y = o.pdf.internal.pageSize.getHeight() - o.pdf.margins_doc.bottom, o.pdf.margins_doc.bottom -= l;\n            for (var r = s.getElementsByTagName(\"span\"), i = 0; i < r.length; ++i) -1 < (\" \" + r[i].className + \" \").replace(/[\\n\\t]/g, \" \").indexOf(\" pageCounter \") && (r[i].innerHTML = e), -1 < (\" \" + r[i].className + \" \").replace(/[\\n\\t]/g, \" \").indexOf(\" totalPages \") && (r[i].innerHTML = \"###jsPDFVarTotalPages###\");\n            k(s, o, a), o.pdf.margins_doc.bottom += l, o.y = n;\n          }, i = s.getElementsByTagName(\"span\"), h = 0; h < i.length; ++h) -1 < (\" \" + i[h].className + \" \").replace(/[\\n\\t]/g, \" \").indexOf(\" totalPages \") && o.pdf.internal.events.subscribe(\"htmlRenderingFinished\", o.pdf.putTotalPages.bind(o.pdf, \"###jsPDFVarTotalPages###\"), !0);\n        o.pdf.internal.events.subscribe(\"addPage\", r, !1), r(), E.FOOTER = 1;\n      }\n    }, m = function (t, e, n, r, i, o) {\n      if (!e) return !1;\n      var a, s, l, h;\n      \"string\" == typeof e || e.parentNode || (e = \"\" + e.innerHTML), \"string\" == typeof e && (a = e.replace(/<\\/?script[^>]*?>/gi, \"\"), h = \"jsPDFhtmlText\" + Date.now().toString() + (1e3 * Math.random()).toFixed(0), (l = document.createElement(\"div\")).style.cssText = \"position: absolute !important;clip: rect(1px 1px 1px 1px); /* IE6, IE7 */clip: rect(1px, 1px, 1px, 1px);padding:0 !important;border:0 !important;height: 1px !important;width: 1px !important; top:auto;left:-100px;overflow: hidden;\", l.innerHTML = '<iframe style=\"height:1px;width:1px\" name=\"' + h + '\" />', document.body.appendChild(l), (s = window.frames[h]).document.open(), s.document.writeln(a), s.document.close(), e = s.document.body);\n      var u,\n        c = new f(t, n, r, i);\n      return d.call(this, e, c, i.elementHandlers, function (t) {\n        g(e, c, i.elementHandlers), k(e, c, i.elementHandlers), c.pdf.internal.events.publish(\"htmlRenderingFinished\"), u = c.dispose(), \"function\" == typeof o ? o(u) : t && console.error(\"jsPDF Warning: rendering issues? provide a callback to fromHTML!\");\n      }), u || {\n        x: c.x,\n        y: c.y\n      };\n    }, (f = function (t, e, n, r) {\n      return this.pdf = t, this.x = e, this.y = n, this.settings = r, this.watchFunctions = [], this.init(), this;\n    }).prototype.init = function () {\n      return this.paragraph = {\n        text: [],\n        style: []\n      }, this.pdf.internal.write(\"q\");\n    }, f.prototype.dispose = function () {\n      return this.pdf.internal.write(\"Q\"), {\n        x: this.x,\n        y: this.y,\n        ready: !0\n      };\n    }, f.prototype.executeWatchFunctions = function (t) {\n      var e = !1,\n        n = [];\n      if (0 < this.watchFunctions.length) {\n        for (var r = 0; r < this.watchFunctions.length; ++r) !0 === this.watchFunctions[r](t) ? e = !0 : n.push(this.watchFunctions[r]);\n        this.watchFunctions = n;\n      }\n      return e;\n    }, f.prototype.splitFragmentsIntoLines = function (t, e) {\n      var n, r, i, o, a, s, l, h, u, c, f, p, d, g;\n      for (12, c = this.pdf.internal.scaleFactor, o = {}, s = l = h = g = a = i = u = r = void 0, p = [f = []], n = 0, d = this.settings.width; t.length;) if (a = t.shift(), g = e.shift(), a) if ((i = o[(r = g[\"font-family\"]) + (u = g[\"font-style\"])]) || (i = this.pdf.internal.getFont(r, u).metadata.Unicode, o[r + u] = i), h = {\n        widths: i.widths,\n        kerning: i.kerning,\n        fontSize: 12 * g[\"font-size\"],\n        textIndent: n\n      }, l = this.pdf.getStringUnitWidth(a, h) * h.fontSize / c, \"\\u2028\" == a) f = [], p.push(f);else if (d < n + l) {\n        for (s = this.pdf.splitTextToSize(a, d, h), f.push([s.shift(), g]); s.length;) f = [[s.shift(), g]], p.push(f);\n        n = this.pdf.getStringUnitWidth(f[0][0], h) * h.fontSize / c;\n      } else f.push([a, g]), n += l;\n      if (void 0 !== g[\"text-align\"] && (\"center\" === g[\"text-align\"] || \"right\" === g[\"text-align\"] || \"justify\" === g[\"text-align\"])) for (var m = 0; m < p.length; ++m) {\n        var y = this.pdf.getStringUnitWidth(p[m][0][0], h) * h.fontSize / c;\n        0 < m && (p[m][0][1] = P(p[m][0][1]));\n        var v = d - y;\n        if (\"right\" === g[\"text-align\"]) p[m][0][1][\"margin-left\"] = v;else if (\"center\" === g[\"text-align\"]) p[m][0][1][\"margin-left\"] = v / 2;else if (\"justify\" === g[\"text-align\"]) {\n          var w = p[m][0][0].split(\" \").length - 1;\n          p[m][0][1][\"word-spacing\"] = v / w, m === p.length - 1 && (p[m][0][1][\"word-spacing\"] = 0);\n        }\n      }\n      return p;\n    }, f.prototype.RenderTextFragment = function (t, e) {\n      var n, r;\n      r = 0, this.pdf.internal.pageSize.getHeight() - this.pdf.margins_doc.bottom < this.y + this.pdf.internal.getFontSize() && (this.pdf.internal.write(\"ET\", \"Q\"), this.pdf.addPage(), this.y = this.pdf.margins_doc.top, this.pdf.internal.write(\"q\", \"BT\", this.getPdfColor(e.color), this.pdf.internal.getCoordinateString(this.x), this.pdf.internal.getVerticalCoordinateString(this.y), \"Td\"), r = Math.max(r, e[\"line-height\"], e[\"font-size\"]), this.pdf.internal.write(0, (-12 * r).toFixed(2), \"Td\")), n = this.pdf.internal.getFont(e[\"font-family\"], e[\"font-style\"]);\n      var i = this.getPdfColor(e.color);\n      i !== this.lastTextColor && (this.pdf.internal.write(i), this.lastTextColor = i), void 0 !== e[\"word-spacing\"] && 0 < e[\"word-spacing\"] && this.pdf.internal.write(e[\"word-spacing\"].toFixed(2), \"Tw\"), this.pdf.internal.write(\"/\" + n.id, (12 * e[\"font-size\"]).toFixed(2), \"Tf\", \"(\" + this.pdf.internal.pdfEscape(t) + \") Tj\"), void 0 !== e[\"word-spacing\"] && this.pdf.internal.write(0, \"Tw\");\n    }, f.prototype.getPdfColor = function (t) {\n      var e,\n        n,\n        r,\n        i = /rgb\\s*\\(\\s*(\\d+),\\s*(\\d+),\\s*(\\d+\\s*)\\)/.exec(t);\n      if (null != i) e = parseInt(i[1]), n = parseInt(i[2]), r = parseInt(i[3]);else {\n        if (\"string\" == typeof t && \"#\" != t.charAt(0)) {\n          var o = new RGBColor(t);\n          t = o.ok ? o.toHex() : \"#000000\";\n        }\n        e = t.substring(1, 3), e = parseInt(e, 16), n = t.substring(3, 5), n = parseInt(n, 16), r = t.substring(5, 7), r = parseInt(r, 16);\n      }\n      if (\"string\" == typeof e && /^#[0-9A-Fa-f]{6}$/.test(e)) {\n        var a = parseInt(e.substr(1), 16);\n        e = a >> 16 & 255, n = a >> 8 & 255, r = 255 & a;\n      }\n      var s = this.f3;\n      return 0 === e && 0 === n && 0 === r || void 0 === n ? s(e / 255) + \" g\" : [s(e / 255), s(n / 255), s(r / 255), \"rg\"].join(\" \");\n    }, f.prototype.f3 = function (t) {\n      return t.toFixed(3);\n    }, f.prototype.renderParagraph = function (t) {\n      var e, n, r, i, o, a, s, l, h, u, c, f, p;\n      if (r = w(this.paragraph.text), f = this.paragraph.style, e = this.paragraph.blockstyle, this.paragraph.priorblockstyle || {}, this.paragraph = {\n        text: [],\n        style: [],\n        blockstyle: {},\n        priorblockstyle: e\n      }, r.join(\"\").trim()) {\n        s = this.splitFragmentsIntoLines(r, f), l = a = void 0, n = 12 / this.pdf.internal.scaleFactor, this.priorMarginBottom = this.priorMarginBottom || 0, c = (Math.max((e[\"margin-top\"] || 0) - this.priorMarginBottom, 0) + (e[\"padding-top\"] || 0)) * n, u = ((e[\"margin-bottom\"] || 0) + (e[\"padding-bottom\"] || 0)) * n, this.priorMarginBottom = e[\"margin-bottom\"] || 0, \"always\" === e[\"page-break-before\"] && (this.pdf.addPage(), this.y = 0, c = ((e[\"margin-top\"] || 0) + (e[\"padding-top\"] || 0)) * n), h = this.pdf.internal.write, o = i = void 0, this.y += c, h(\"q\", \"BT 0 g\", this.pdf.internal.getCoordinateString(this.x), this.pdf.internal.getVerticalCoordinateString(this.y), \"Td\");\n        for (var d = 0; s.length;) {\n          for (i = l = 0, o = (a = s.shift()).length; i !== o;) a[i][0].trim() && (l = Math.max(l, a[i][1][\"line-height\"], a[i][1][\"font-size\"]), p = 7 * a[i][1][\"font-size\"]), i++;\n          var g = 0,\n            m = 0;\n          for (void 0 !== a[0][1][\"margin-left\"] && 0 < a[0][1][\"margin-left\"] && (g = (m = this.pdf.internal.getCoordinateString(a[0][1][\"margin-left\"])) - d, d = m), h(g + Math.max(e[\"margin-left\"] || 0, 0) * n, (-12 * l).toFixed(2), \"Td\"), i = 0, o = a.length; i !== o;) a[i][0] && this.RenderTextFragment(a[i][0], a[i][1]), i++;\n          if (this.y += l * n, this.executeWatchFunctions(a[0][1]) && 0 < s.length) {\n            var y = [],\n              v = [];\n            s.forEach(function (t) {\n              for (var e = 0, n = t.length; e !== n;) t[e][0] && (y.push(t[e][0] + \" \"), v.push(t[e][1])), ++e;\n            }), s = this.splitFragmentsIntoLines(w(y), v), h(\"ET\", \"Q\"), h(\"q\", \"BT 0 g\", this.pdf.internal.getCoordinateString(this.x), this.pdf.internal.getVerticalCoordinateString(this.y), \"Td\");\n          }\n        }\n        return t && \"function\" == typeof t && t.call(this, this.x - 9, this.y - p / 2), h(\"ET\", \"Q\"), this.y += u;\n      }\n    }, f.prototype.setBlockBoundary = function (t) {\n      return this.renderParagraph(t);\n    }, f.prototype.setBlockStyle = function (t) {\n      return this.paragraph.blockstyle = t;\n    }, f.prototype.addText = function (t, e) {\n      return this.paragraph.text.push(t), this.paragraph.style.push(e);\n    }, i = {\n      helvetica: \"helvetica\",\n      \"sans-serif\": \"helvetica\",\n      \"times new roman\": \"times\",\n      serif: \"times\",\n      times: \"times\",\n      monospace: \"courier\",\n      courier: \"courier\"\n    }, l = {\n      100: \"normal\",\n      200: \"normal\",\n      300: \"normal\",\n      400: \"normal\",\n      500: \"bold\",\n      600: \"bold\",\n      700: \"bold\",\n      800: \"bold\",\n      900: \"bold\",\n      normal: \"normal\",\n      bold: \"bold\",\n      bolder: \"bold\",\n      lighter: \"normal\"\n    }, a = {\n      normal: \"normal\",\n      italic: \"italic\",\n      oblique: \"italic\"\n    }, s = {\n      left: \"left\",\n      right: \"right\",\n      center: \"center\",\n      justify: \"justify\"\n    }, h = {\n      none: \"none\",\n      right: \"right\",\n      left: \"left\"\n    }, u = {\n      none: \"none\",\n      both: \"both\"\n    }, n = {\n      normal: 1\n    }, t.fromHTML = function (t, e, n, r, i, o) {\n      return this.margins_doc = o || {\n        top: 0,\n        bottom: 0\n      }, r || (r = {}), r.elementHandlers || (r.elementHandlers = {}), m(this, t, isNaN(e) ? 4 : e, isNaN(n) ? 4 : n, r, i);\n    };\n  }(lt.API), lt.API, (\"undefined\" != typeof window && window || \"undefined\" != typeof global && global).html2pdf = function (t, a, e) {\n    var n = a.canvas;\n    if (n) {\n      var r, i;\n      if ((n.pdf = a).annotations = {\n        _nameMap: [],\n        createAnnotation: function (t, e) {\n          var n,\n            r = a.context2d._wrapX(e.left),\n            i = a.context2d._wrapY(e.top),\n            o = (a.context2d._page(e.top), t.indexOf(\"#\"));\n          n = 0 <= o ? {\n            name: t.substring(o + 1)\n          } : {\n            url: t\n          }, a.link(r, i, e.right - e.left, e.bottom - e.top, n);\n        },\n        setName: function (t, e) {\n          var n = a.context2d._wrapX(e.left),\n            r = a.context2d._wrapY(e.top),\n            i = a.context2d._page(e.top);\n          this._nameMap[t] = {\n            page: i,\n            x: n,\n            y: r\n          };\n        }\n      }, n.annotations = a.annotations, a.context2d._pageBreakAt = function (t) {\n        this.pageBreaks.push(t);\n      }, a.context2d._gotoPage = function (t) {\n        for (; a.internal.getNumberOfPages() < t;) a.addPage();\n        a.setPage(t);\n      }, \"string\" == typeof t) {\n        t = t.replace(/<script\\b[^<]*(?:(?!<\\/script>)<[^<]*)*<\\/script>/gi, \"\");\n        var o,\n          s,\n          l = document.createElement(\"iframe\");\n        document.body.appendChild(l), null != (o = l.contentDocument) && null != o || (o = l.contentWindow.document), o.open(), o.write(t), o.close(), r = o.body, s = o.body || {}, t = o.documentElement || {}, i = Math.max(s.scrollHeight, s.offsetHeight, t.clientHeight, t.scrollHeight, t.offsetHeight);\n      } else s = (r = t).body || {}, i = Math.max(s.scrollHeight, s.offsetHeight, t.clientHeight, t.scrollHeight, t.offsetHeight);\n      var h = {\n        async: !0,\n        allowTaint: !0,\n        backgroundColor: \"#ffffff\",\n        canvas: n,\n        imageTimeout: 15e3,\n        logging: !0,\n        proxy: null,\n        removeContainer: !0,\n        foreignObjectRendering: !1,\n        useCORS: !1,\n        windowHeight: i = a.internal.pageSize.getHeight(),\n        scrollY: i\n      };\n      a.context2d.pageWrapYEnabled = !0, a.context2d.pageWrapY = a.internal.pageSize.getHeight(), html2canvas(r, h).then(function (t) {\n        e && (l && l.parentElement.removeChild(l), e(a));\n      });\n    } else alert(\"jsPDF canvas plugin not installed\");\n  }, window.tmp = html2pdf, function (f) {\n    var r = f.BlobBuilder || f.WebKitBlobBuilder || f.MSBlobBuilder || f.MozBlobBuilder;\n    f.URL = f.URL || f.webkitURL || function (t, e) {\n      return (e = document.createElement(\"a\")).href = t, e;\n    };\n    var n = f.Blob,\n      p = URL.createObjectURL,\n      d = URL.revokeObjectURL,\n      o = f.Symbol && f.Symbol.toStringTag,\n      t = !1,\n      e = !1,\n      g = !!f.ArrayBuffer,\n      i = r && r.prototype.append && r.prototype.getBlob;\n    try {\n      t = 2 === new Blob([\"ä\"]).size, e = 2 === new Blob([new Uint8Array([1, 2])]).size;\n    } catch (t) {}\n    function a(t) {\n      return t.map(function (t) {\n        if (t.buffer instanceof ArrayBuffer) {\n          var e = t.buffer;\n          if (t.byteLength !== e.byteLength) {\n            var n = new Uint8Array(t.byteLength);\n            n.set(new Uint8Array(e, t.byteOffset, t.byteLength)), e = n.buffer;\n          }\n          return e;\n        }\n        return t;\n      });\n    }\n    function s(t, e) {\n      e = e || {};\n      var n = new r();\n      return a(t).forEach(function (t) {\n        n.append(t);\n      }), e.type ? n.getBlob(e.type) : n.getBlob();\n    }\n    function l(t, e) {\n      return new n(a(t), e || {});\n    }\n    if (f.Blob && (s.prototype = Blob.prototype, l.prototype = Blob.prototype), o) try {\n      File.prototype[o] = \"File\", Blob.prototype[o] = \"Blob\", FileReader.prototype[o] = \"FileReader\";\n    } catch (t) {}\n    function h() {\n      var t = !!f.ActiveXObject || \"-ms-scroll-limit\" in document.documentElement.style && \"-ms-ime-align\" in document.documentElement.style,\n        e = f.XMLHttpRequest && f.XMLHttpRequest.prototype.send;\n      t && e && (XMLHttpRequest.prototype.send = function (t) {\n        t instanceof Blob && this.setRequestHeader(\"Content-Type\", t.type), e.call(this, t);\n      });\n      try {\n        new File([], \"\");\n      } catch (t) {\n        try {\n          var n = new Function('class File extends Blob {constructor(chunks, name, opts) {opts = opts || {};super(chunks, opts || {});this.name = name;this.lastModifiedDate = opts.lastModified ? new Date(opts.lastModified) : new Date;this.lastModified = +this.lastModifiedDate;}};return new File([], \"\"), File')();\n          f.File = n;\n        } catch (t) {\n          n = function (t, e, n) {\n            var r = new Blob(t, n),\n              i = n && void 0 !== n.lastModified ? new Date(n.lastModified) : new Date();\n            return r.name = e, r.lastModifiedDate = i, r.lastModified = +i, r.toString = function () {\n              return \"[object File]\";\n            }, o && (r[o] = \"File\"), r;\n          };\n          f.File = n;\n        }\n      }\n    }\n    t ? (h(), f.Blob = e ? f.Blob : l) : i ? (h(), f.Blob = s) : function () {\n      function a(t) {\n        for (var e = [], n = 0; n < t.length; n++) {\n          var r = t.charCodeAt(n);\n          r < 128 ? e.push(r) : r < 2048 ? e.push(192 | r >> 6, 128 | 63 & r) : r < 55296 || 57344 <= r ? e.push(224 | r >> 12, 128 | r >> 6 & 63, 128 | 63 & r) : (n++, r = 65536 + ((1023 & r) << 10 | 1023 & t.charCodeAt(n)), e.push(240 | r >> 18, 128 | r >> 12 & 63, 128 | r >> 6 & 63, 128 | 63 & r));\n        }\n        return e;\n      }\n      function e(t) {\n        var e, n, r, i, o, a;\n        for (e = \"\", r = t.length, n = 0; n < r;) switch ((i = t[n++]) >> 4) {\n          case 0:\n          case 1:\n          case 2:\n          case 3:\n          case 4:\n          case 5:\n          case 6:\n          case 7:\n            e += String.fromCharCode(i);\n            break;\n          case 12:\n          case 13:\n            o = t[n++], e += String.fromCharCode((31 & i) << 6 | 63 & o);\n            break;\n          case 14:\n            o = t[n++], a = t[n++], e += String.fromCharCode((15 & i) << 12 | (63 & o) << 6 | (63 & a) << 0);\n        }\n        return e;\n      }\n      function s(t) {\n        for (var e = new Array(t.byteLength), n = new Uint8Array(t), r = e.length; r--;) e[r] = n[r];\n        return e;\n      }\n      function n(t) {\n        for (var e = \"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789\", n = [], r = 0; r < t.length; r += 3) {\n          var i = t[r],\n            o = r + 1 < t.length,\n            a = o ? t[r + 1] : 0,\n            s = r + 2 < t.length,\n            l = s ? t[r + 2] : 0,\n            h = i >> 2,\n            u = (3 & i) << 4 | a >> 4,\n            c = (15 & a) << 2 | l >> 6,\n            f = 63 & l;\n          s || (f = 64, o || (c = 64)), n.push(e[h], e[u], e[c], e[f]);\n        }\n        return n.join(\"\");\n      }\n      var t = Object.create || function (t) {\n        function e() {}\n        return e.prototype = t, new e();\n      };\n      if (g) var r = [\"[object Int8Array]\", \"[object Uint8Array]\", \"[object Uint8ClampedArray]\", \"[object Int16Array]\", \"[object Uint16Array]\", \"[object Int32Array]\", \"[object Uint32Array]\", \"[object Float32Array]\", \"[object Float64Array]\"],\n        l = ArrayBuffer.isView || function (t) {\n          return t && -1 < r.indexOf(Object.prototype.toString.call(t));\n        };\n      function h(t, e) {\n        for (var n = 0, r = (t = t || []).length; n < r; n++) {\n          var i = t[n];\n          i instanceof h ? t[n] = i._buffer : \"string\" == typeof i ? t[n] = a(i) : g && (ArrayBuffer.prototype.isPrototypeOf(i) || l(i)) ? t[n] = s(i) : g && (o = i) && DataView.prototype.isPrototypeOf(o) ? t[n] = s(i.buffer) : t[n] = a(String(i));\n        }\n        var o;\n        this._buffer = [].concat.apply([], t), this.size = this._buffer.length, this.type = e && e.type || \"\";\n      }\n      function i(t, e, n) {\n        var r = h.call(this, t, n = n || {}) || this;\n        return r.name = e, r.lastModifiedDate = n.lastModified ? new Date(n.lastModified) : new Date(), r.lastModified = +r.lastModifiedDate, r;\n      }\n      if (h.prototype.slice = function (t, e, n) {\n        return new h([this._buffer.slice(t || 0, e || this._buffer.length)], {\n          type: n\n        });\n      }, h.prototype.toString = function () {\n        return \"[object Blob]\";\n      }, (i.prototype = t(h.prototype)).constructor = i, Object.setPrototypeOf) Object.setPrototypeOf(i, h);else try {\n        i.__proto__ = h;\n      } catch (t) {}\n      function o() {\n        if (!(this instanceof o)) throw new TypeError(\"Failed to construct 'FileReader': Please use the 'new' operator, this DOM object constructor cannot be called as a function.\");\n        var n = document.createDocumentFragment();\n        this.addEventListener = n.addEventListener, this.dispatchEvent = function (t) {\n          var e = this[\"on\" + t.type];\n          \"function\" == typeof e && e(t), n.dispatchEvent(t);\n        }, this.removeEventListener = n.removeEventListener;\n      }\n      function u(t, e, n) {\n        if (!(e instanceof h)) throw new TypeError(\"Failed to execute '\" + n + \"' on 'FileReader': parameter 1 is not of type 'Blob'.\");\n        t.result = \"\", setTimeout(function () {\n          this.readyState = o.LOADING, t.dispatchEvent(new Event(\"load\")), t.dispatchEvent(new Event(\"loadend\"));\n        });\n      }\n      i.prototype.toString = function () {\n        return \"[object File]\";\n      }, o.EMPTY = 0, o.LOADING = 1, o.DONE = 2, o.prototype.error = null, o.prototype.onabort = null, o.prototype.onerror = null, o.prototype.onload = null, o.prototype.onloadend = null, o.prototype.onloadstart = null, o.prototype.onprogress = null, o.prototype.readAsDataURL = function (t) {\n        u(this, t, \"readAsDataURL\"), this.result = \"data:\" + t.type + \";base64,\" + n(t._buffer);\n      }, o.prototype.readAsText = function (t) {\n        u(this, t, \"readAsText\"), this.result = e(t._buffer);\n      }, o.prototype.readAsArrayBuffer = function (t) {\n        u(this, t, \"readAsText\"), this.result = t._buffer.slice();\n      }, o.prototype.abort = function () {}, URL.createObjectURL = function (t) {\n        return t instanceof h ? \"data:\" + t.type + \";base64,\" + n(t._buffer) : p.call(URL, t);\n      }, URL.revokeObjectURL = function (t) {\n        d && d.call(URL, t);\n      };\n      var c = f.XMLHttpRequest && f.XMLHttpRequest.prototype.send;\n      c && (XMLHttpRequest.prototype.send = function (t) {\n        t instanceof h ? (this.setRequestHeader(\"Content-Type\", t.type), c.call(this, e(t._buffer))) : c.call(this, t);\n      }), f.FileReader = o, f.File = i, f.Blob = h;\n    }();\n  }(\"undefined\" != typeof self && self || \"undefined\" != typeof window && window || \"undefined\" != typeof global && global || Function('return typeof this === \"object\" && this.content')() || Function(\"return this\")());\n  var ht,\n    ut,\n    ct,\n    ft,\n    pt,\n    dt,\n    gt,\n    mt,\n    yt,\n    vt,\n    wt,\n    bt,\n    xt,\n    Nt,\n    Lt,\n    le = le || function (s) {\n      if (!(void 0 === s || \"undefined\" != typeof navigator && /MSIE [1-9]\\./.test(navigator.userAgent))) {\n        var t = s.document,\n          l = function () {\n            return s.URL || s.webkitURL || s;\n          },\n          h = t.createElementNS(\"http://www.w3.org/1999/xhtml\", \"a\"),\n          u = \"download\" in h,\n          c = /constructor/i.test(s.HTMLElement) || s.safari,\n          f = /CriOS\\/[\\d]+/.test(navigator.userAgent),\n          p = s.setImmediate || s.setTimeout,\n          d = function (t) {\n            p(function () {\n              throw t;\n            }, 0);\n          },\n          g = function (t) {\n            setTimeout(function () {\n              \"string\" == typeof t ? l().revokeObjectURL(t) : t.remove();\n            }, 4e4);\n          },\n          m = function (t) {\n            return /^\\s*(?:text\\/\\S*|application\\/xml|\\S*\\/\\S*\\+xml)\\s*;.*charset\\s*=\\s*utf-8/i.test(t.type) ? new Blob([String.fromCharCode(65279), t], {\n              type: t.type\n            }) : t;\n          },\n          r = function (t, n, e) {\n            e || (t = m(t));\n            var r,\n              i = this,\n              o = \"application/octet-stream\" === t.type,\n              a = function () {\n                !function (t, e, n) {\n                  for (var r = (e = [].concat(e)).length; r--;) {\n                    var i = t[\"on\" + e[r]];\n                    if (\"function\" == typeof i) try {\n                      i.call(t, n || t);\n                    } catch (t) {\n                      d(t);\n                    }\n                  }\n                }(i, \"writestart progress write writeend\".split(\" \"));\n              };\n            if (i.readyState = i.INIT, u) return r = l().createObjectURL(t), void p(function () {\n              var t, e;\n              h.href = r, h.download = n, t = h, e = new MouseEvent(\"click\"), t.dispatchEvent(e), a(), g(r), i.readyState = i.DONE;\n            }, 0);\n            !function () {\n              if ((f || o && c) && s.FileReader) {\n                var e = new FileReader();\n                return e.onloadend = function () {\n                  var t = f ? e.result : e.result.replace(/^data:[^;]*;/, \"data:attachment/file;\");\n                  s.open(t, \"_blank\") || (s.location.href = t), t = void 0, i.readyState = i.DONE, a();\n                }, e.readAsDataURL(t), i.readyState = i.INIT;\n              }\n              r || (r = l().createObjectURL(t)), o ? s.location.href = r : s.open(r, \"_blank\") || (s.location.href = r);\n              i.readyState = i.DONE, a(), g(r);\n            }();\n          },\n          e = r.prototype;\n        return \"undefined\" != typeof navigator && navigator.msSaveOrOpenBlob ? function (t, e, n) {\n          return e = e || t.name || \"download\", n || (t = m(t)), navigator.msSaveOrOpenBlob(t, e);\n        } : (e.abort = function () {}, e.readyState = e.INIT = 0, e.WRITING = 1, e.DONE = 2, e.error = e.onwritestart = e.onprogress = e.onwrite = e.onabort = e.onerror = e.onwriteend = null, function (t, e, n) {\n          return new r(t, e || t.name || \"download\", n);\n        });\n      }\n    }(\"undefined\" != typeof self && self || \"undefined\" != typeof window && window || void 0);\n  function At(x) {\n    var t = 0;\n    if (71 !== x[t++] || 73 !== x[t++] || 70 !== x[t++] || 56 !== x[t++] || 56 != (x[t++] + 1 & 253) || 97 !== x[t++]) throw \"Invalid GIF 87a/89a header.\";\n    var N = x[t++] | x[t++] << 8,\n      e = x[t++] | x[t++] << 8,\n      n = x[t++],\n      r = n >> 7,\n      i = 1 << (7 & n) + 1;\n    x[t++];\n    x[t++];\n    var o = null;\n    r && (o = t, t += 3 * i);\n    var a = !0,\n      s = [],\n      l = 0,\n      h = null,\n      u = 0,\n      c = null;\n    for (this.width = N, this.height = e; a && t < x.length;) switch (x[t++]) {\n      case 33:\n        switch (x[t++]) {\n          case 255:\n            if (11 !== x[t] || 78 == x[t + 1] && 69 == x[t + 2] && 84 == x[t + 3] && 83 == x[t + 4] && 67 == x[t + 5] && 65 == x[t + 6] && 80 == x[t + 7] && 69 == x[t + 8] && 50 == x[t + 9] && 46 == x[t + 10] && 48 == x[t + 11] && 3 == x[t + 12] && 1 == x[t + 13] && 0 == x[t + 16]) t += 14, c = x[t++] | x[t++] << 8, t++;else for (t += 12;;) {\n              if (0 === (A = x[t++])) break;\n              t += A;\n            }\n            break;\n          case 249:\n            if (4 !== x[t++] || 0 !== x[t + 4]) throw \"Invalid graphics extension block.\";\n            var f = x[t++];\n            l = x[t++] | x[t++] << 8, h = x[t++], 0 == (1 & f) && (h = null), u = f >> 2 & 7, t++;\n            break;\n          case 254:\n            for (;;) {\n              if (0 === (A = x[t++])) break;\n              t += A;\n            }\n            break;\n          default:\n            throw \"Unknown graphic control label: 0x\" + x[t - 1].toString(16);\n        }\n        break;\n      case 44:\n        var p = x[t++] | x[t++] << 8,\n          d = x[t++] | x[t++] << 8,\n          g = x[t++] | x[t++] << 8,\n          m = x[t++] | x[t++] << 8,\n          y = x[t++],\n          v = y >> 6 & 1,\n          w = o,\n          b = !1;\n        if (y >> 7) {\n          b = !0;\n          w = t, t += 3 * (1 << (7 & y) + 1);\n        }\n        var L = t;\n        for (t++;;) {\n          var A;\n          if (0 === (A = x[t++])) break;\n          t += A;\n        }\n        s.push({\n          x: p,\n          y: d,\n          width: g,\n          height: m,\n          has_local_palette: b,\n          palette_offset: w,\n          data_offset: L,\n          data_length: t - L,\n          transparent_index: h,\n          interlaced: !!v,\n          delay: l,\n          disposal: u\n        });\n        break;\n      case 59:\n        a = !1;\n        break;\n      default:\n        throw \"Unknown gif block: 0x\" + x[t - 1].toString(16);\n    }\n    this.numFrames = function () {\n      return s.length;\n    }, this.loopCount = function () {\n      return c;\n    }, this.frameInfo = function (t) {\n      if (t < 0 || t >= s.length) throw \"Frame index out of range.\";\n      return s[t];\n    }, this.decodeAndBlitFrameBGRA = function (t, e) {\n      var n = this.frameInfo(t),\n        r = n.width * n.height,\n        i = new Uint8Array(r);\n      St(x, n.data_offset, i, r);\n      var o = n.palette_offset,\n        a = n.transparent_index;\n      null === a && (a = 256);\n      var s = n.width,\n        l = N - s,\n        h = s,\n        u = 4 * (n.y * N + n.x),\n        c = 4 * ((n.y + n.height) * N + n.x),\n        f = u,\n        p = 4 * l;\n      !0 === n.interlaced && (p += 4 * (s + l) * 7);\n      for (var d = 8, g = 0, m = i.length; g < m; ++g) {\n        var y = i[g];\n        if (0 === h && (h = s, c <= (f += p) && (p = l + 4 * (s + l) * (d - 1), f = u + (s + l) * (d << 1), d >>= 1)), y === a) f += 4;else {\n          var v = x[o + 3 * y],\n            w = x[o + 3 * y + 1],\n            b = x[o + 3 * y + 2];\n          e[f++] = b, e[f++] = w, e[f++] = v, e[f++] = 255;\n        }\n        --h;\n      }\n    }, this.decodeAndBlitFrameRGBA = function (t, e) {\n      var n = this.frameInfo(t),\n        r = n.width * n.height,\n        i = new Uint8Array(r);\n      St(x, n.data_offset, i, r);\n      var o = n.palette_offset,\n        a = n.transparent_index;\n      null === a && (a = 256);\n      var s = n.width,\n        l = N - s,\n        h = s,\n        u = 4 * (n.y * N + n.x),\n        c = 4 * ((n.y + n.height) * N + n.x),\n        f = u,\n        p = 4 * l;\n      !0 === n.interlaced && (p += 4 * (s + l) * 7);\n      for (var d = 8, g = 0, m = i.length; g < m; ++g) {\n        var y = i[g];\n        if (0 === h && (h = s, c <= (f += p) && (p = l + 4 * (s + l) * (d - 1), f = u + (s + l) * (d << 1), d >>= 1)), y === a) f += 4;else {\n          var v = x[o + 3 * y],\n            w = x[o + 3 * y + 1],\n            b = x[o + 3 * y + 2];\n          e[f++] = v, e[f++] = w, e[f++] = b, e[f++] = 255;\n        }\n        --h;\n      }\n    };\n  }\n  function St(t, e, n, r) {\n    for (var i = t[e++], o = 1 << i, a = o + 1, s = a + 1, l = i + 1, h = (1 << l) - 1, u = 0, c = 0, f = 0, p = t[e++], d = new Int32Array(4096), g = null;;) {\n      for (; u < 16 && 0 !== p;) c |= t[e++] << u, u += 8, 1 === p ? p = t[e++] : --p;\n      if (u < l) break;\n      var m = c & h;\n      if (c >>= l, u -= l, m !== o) {\n        if (m === a) break;\n        for (var y = m < s ? m : g, v = 0, w = y; o < w;) w = d[w] >> 8, ++v;\n        var b = w;\n        if (r < f + v + (y !== m ? 1 : 0)) return void console.log(\"Warning, gif stream longer than expected.\");\n        n[f++] = b;\n        var x = f += v;\n        for (y !== m && (n[f++] = b), w = y; v--;) w = d[w], n[--x] = 255 & w, w >>= 8;\n        null !== g && s < 4096 && (d[s++] = g << 8 | b, h + 1 <= s && l < 12 && (++l, h = h << 1 | 1)), g = m;\n      } else s = a + 1, h = (1 << (l = i + 1)) - 1, g = null;\n    }\n    return f !== r && console.log(\"Warning, gif stream shorter than expected.\"), n;\n  }\n  try {\n    exports.GifWriter = function (y, t, e, n) {\n      var v = 0,\n        r = void 0 === (n = void 0 === n ? {} : n).loop ? null : n.loop,\n        w = void 0 === n.palette ? null : n.palette;\n      if (t <= 0 || e <= 0 || 65535 < t || 65535 < e) throw \"Width/Height invalid.\";\n      function b(t) {\n        var e = t.length;\n        if (e < 2 || 256 < e || e & e - 1) throw \"Invalid code/color length, must be power of 2 and 2 .. 256.\";\n        return e;\n      }\n      y[v++] = 71, y[v++] = 73, y[v++] = 70, y[v++] = 56, y[v++] = 57, y[v++] = 97;\n      var i = 0,\n        o = 0;\n      if (null !== w) {\n        for (var a = b(w); a >>= 1;) ++i;\n        if (a = 1 << i, --i, void 0 !== n.background) {\n          if (a <= (o = n.background)) throw \"Background index out of range.\";\n          if (0 === o) throw \"Background index explicitly passed as 0.\";\n        }\n      }\n      if (y[v++] = 255 & t, y[v++] = t >> 8 & 255, y[v++] = 255 & e, y[v++] = e >> 8 & 255, y[v++] = (null !== w ? 128 : 0) | i, y[v++] = o, y[v++] = 0, null !== w) for (var s = 0, l = w.length; s < l; ++s) {\n        var h = w[s];\n        y[v++] = h >> 16 & 255, y[v++] = h >> 8 & 255, y[v++] = 255 & h;\n      }\n      if (null !== r) {\n        if (r < 0 || 65535 < r) throw \"Loop count invalid.\";\n        y[v++] = 33, y[v++] = 255, y[v++] = 11, y[v++] = 78, y[v++] = 69, y[v++] = 84, y[v++] = 83, y[v++] = 67, y[v++] = 65, y[v++] = 80, y[v++] = 69, y[v++] = 50, y[v++] = 46, y[v++] = 48, y[v++] = 3, y[v++] = 1, y[v++] = 255 & r, y[v++] = r >> 8 & 255, y[v++] = 0;\n      }\n      var x = !1;\n      this.addFrame = function (t, e, n, r, i, o) {\n        if (!0 === x && (--v, x = !1), o = void 0 === o ? {} : o, t < 0 || e < 0 || 65535 < t || 65535 < e) throw \"x/y invalid.\";\n        if (n <= 0 || r <= 0 || 65535 < n || 65535 < r) throw \"Width/Height invalid.\";\n        if (i.length < n * r) throw \"Not enough pixels for the frame size.\";\n        var a = !0,\n          s = o.palette;\n        if (null == s && (a = !1, s = w), null == s) throw \"Must supply either a local or global palette.\";\n        for (var l = b(s), h = 0; l >>= 1;) ++h;\n        l = 1 << h;\n        var u = void 0 === o.delay ? 0 : o.delay,\n          c = void 0 === o.disposal ? 0 : o.disposal;\n        if (c < 0 || 3 < c) throw \"Disposal out of range.\";\n        var f = !1,\n          p = 0;\n        if (void 0 !== o.transparent && null !== o.transparent && (f = !0, (p = o.transparent) < 0 || l <= p)) throw \"Transparent color index.\";\n        if ((0 !== c || f || 0 !== u) && (y[v++] = 33, y[v++] = 249, y[v++] = 4, y[v++] = c << 2 | (!0 === f ? 1 : 0), y[v++] = 255 & u, y[v++] = u >> 8 & 255, y[v++] = p, y[v++] = 0), y[v++] = 44, y[v++] = 255 & t, y[v++] = t >> 8 & 255, y[v++] = 255 & e, y[v++] = e >> 8 & 255, y[v++] = 255 & n, y[v++] = n >> 8 & 255, y[v++] = 255 & r, y[v++] = r >> 8 & 255, y[v++] = !0 === a ? 128 | h - 1 : 0, !0 === a) for (var d = 0, g = s.length; d < g; ++d) {\n          var m = s[d];\n          y[v++] = m >> 16 & 255, y[v++] = m >> 8 & 255, y[v++] = 255 & m;\n        }\n        v = function (e, n, t, r) {\n          e[n++] = t;\n          var i = n++,\n            o = 1 << t,\n            a = o - 1,\n            s = o + 1,\n            l = s + 1,\n            h = t + 1,\n            u = 0,\n            c = 0;\n          function f(t) {\n            for (; t <= u;) e[n++] = 255 & c, c >>= 8, u -= 8, n === i + 256 && (e[i] = 255, i = n++);\n          }\n          function p(t) {\n            c |= t << u, u += h, f(8);\n          }\n          var d = r[0] & a,\n            g = {};\n          p(o);\n          for (var m = 1, y = r.length; m < y; ++m) {\n            var v = r[m] & a,\n              w = d << 8 | v,\n              b = g[w];\n            if (void 0 === b) {\n              for (c |= d << u, u += h; 8 <= u;) e[n++] = 255 & c, c >>= 8, u -= 8, n === i + 256 && (e[i] = 255, i = n++);\n              4096 === l ? (p(o), l = s + 1, h = t + 1, g = {}) : (1 << h <= l && ++h, g[w] = l++), d = v;\n            } else d = b;\n          }\n          return p(d), p(s), f(1), i + 1 === n ? e[i] = 0 : (e[i] = n - i - 1, e[n++] = 0), n;\n        }(y, v, h < 2 ? 2 : h, i);\n      }, this.end = function () {\n        return !1 === x && (y[v++] = 59, x = !0), v;\n      };\n    }, exports.GifReader = At;\n  } catch (t) {}\n  /*\n      Copyright (c) 2008, Adobe Systems Incorporated\n      All rights reserved.\n  \n      Redistribution and use in source and binary forms, with or without \n      modification, are permitted provided that the following conditions are\n      met:\n  \n      * Redistributions of source code must retain the above copyright notice, \n        this list of conditions and the following disclaimer.\n      \n      * Redistributions in binary form must reproduce the above copyright\n        notice, this list of conditions and the following disclaimer in the \n        documentation and/or other materials provided with the distribution.\n      \n      * Neither the name of Adobe Systems Incorporated nor the names of its \n        contributors may be used to endorse or promote products derived from \n        this software without specific prior written permission.\n  \n      THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS \"AS\n      IS\" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO,\n      THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR\n      PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR \n      CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL,\n      EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,\n      PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n      PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF\n      LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING\n      NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS\n      SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.\n    */\n  function _t(t) {\n    var N,\n      L,\n      A,\n      S,\n      e,\n      c = Math.floor,\n      _ = new Array(64),\n      F = new Array(64),\n      P = new Array(64),\n      k = new Array(64),\n      y = new Array(65535),\n      v = new Array(65535),\n      Z = new Array(64),\n      w = new Array(64),\n      I = [],\n      C = 0,\n      B = 7,\n      j = new Array(64),\n      E = new Array(64),\n      M = new Array(64),\n      n = new Array(256),\n      O = new Array(2048),\n      b = [0, 1, 5, 6, 14, 15, 27, 28, 2, 4, 7, 13, 16, 26, 29, 42, 3, 8, 12, 17, 25, 30, 41, 43, 9, 11, 18, 24, 31, 40, 44, 53, 10, 19, 23, 32, 39, 45, 52, 54, 20, 22, 33, 38, 46, 51, 55, 60, 21, 34, 37, 47, 50, 56, 59, 61, 35, 36, 48, 49, 57, 58, 62, 63],\n      q = [0, 0, 1, 5, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0],\n      T = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11],\n      R = [0, 0, 2, 1, 3, 3, 2, 4, 3, 5, 5, 4, 4, 0, 0, 1, 125],\n      D = [1, 2, 3, 0, 4, 17, 5, 18, 33, 49, 65, 6, 19, 81, 97, 7, 34, 113, 20, 50, 129, 145, 161, 8, 35, 66, 177, 193, 21, 82, 209, 240, 36, 51, 98, 114, 130, 9, 10, 22, 23, 24, 25, 26, 37, 38, 39, 40, 41, 42, 52, 53, 54, 55, 56, 57, 58, 67, 68, 69, 70, 71, 72, 73, 74, 83, 84, 85, 86, 87, 88, 89, 90, 99, 100, 101, 102, 103, 104, 105, 106, 115, 116, 117, 118, 119, 120, 121, 122, 131, 132, 133, 134, 135, 136, 137, 138, 146, 147, 148, 149, 150, 151, 152, 153, 154, 162, 163, 164, 165, 166, 167, 168, 169, 170, 178, 179, 180, 181, 182, 183, 184, 185, 186, 194, 195, 196, 197, 198, 199, 200, 201, 202, 210, 211, 212, 213, 214, 215, 216, 217, 218, 225, 226, 227, 228, 229, 230, 231, 232, 233, 234, 241, 242, 243, 244, 245, 246, 247, 248, 249, 250],\n      U = [0, 0, 3, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0],\n      z = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11],\n      H = [0, 0, 2, 1, 2, 4, 4, 3, 4, 7, 5, 4, 4, 0, 1, 2, 119],\n      W = [0, 1, 2, 3, 17, 4, 5, 33, 49, 6, 18, 65, 81, 7, 97, 113, 19, 34, 50, 129, 8, 20, 66, 145, 161, 177, 193, 9, 35, 51, 82, 240, 21, 98, 114, 209, 10, 22, 36, 52, 225, 37, 241, 23, 24, 25, 26, 38, 39, 40, 41, 42, 53, 54, 55, 56, 57, 58, 67, 68, 69, 70, 71, 72, 73, 74, 83, 84, 85, 86, 87, 88, 89, 90, 99, 100, 101, 102, 103, 104, 105, 106, 115, 116, 117, 118, 119, 120, 121, 122, 130, 131, 132, 133, 134, 135, 136, 137, 138, 146, 147, 148, 149, 150, 151, 152, 153, 154, 162, 163, 164, 165, 166, 167, 168, 169, 170, 178, 179, 180, 181, 182, 183, 184, 185, 186, 194, 195, 196, 197, 198, 199, 200, 201, 202, 210, 211, 212, 213, 214, 215, 216, 217, 218, 226, 227, 228, 229, 230, 231, 232, 233, 234, 242, 243, 244, 245, 246, 247, 248, 249, 250];\n    function r(t, e) {\n      for (var n = 0, r = 0, i = new Array(), o = 1; o <= 16; o++) {\n        for (var a = 1; a <= t[o]; a++) i[e[r]] = [], i[e[r]][0] = n, i[e[r]][1] = o, r++, n++;\n        n *= 2;\n      }\n      return i;\n    }\n    function V(t) {\n      for (var e = t[0], n = t[1] - 1; 0 <= n;) e & 1 << n && (C |= 1 << B), n--, --B < 0 && (255 == C ? (G(255), G(0)) : G(C), B = 7, C = 0);\n    }\n    function G(t) {\n      I.push(t);\n    }\n    function Y(t) {\n      G(t >> 8 & 255), G(255 & t);\n    }\n    function J(t, e, n, r, i) {\n      for (var o, a = i[0], s = i[240], l = function (t, e) {\n          var n,\n            r,\n            i,\n            o,\n            a,\n            s,\n            l,\n            h,\n            u,\n            c,\n            f = 0;\n          for (u = 0; u < 8; ++u) {\n            n = t[f], r = t[f + 1], i = t[f + 2], o = t[f + 3], a = t[f + 4], s = t[f + 5], l = t[f + 6];\n            var p = n + (h = t[f + 7]),\n              d = n - h,\n              g = r + l,\n              m = r - l,\n              y = i + s,\n              v = i - s,\n              w = o + a,\n              b = o - a,\n              x = p + w,\n              N = p - w,\n              L = g + y,\n              A = g - y;\n            t[f] = x + L, t[f + 4] = x - L;\n            var S = .707106781 * (A + N);\n            t[f + 2] = N + S, t[f + 6] = N - S;\n            var _ = .382683433 * ((x = b + v) - (A = m + d)),\n              F = .5411961 * x + _,\n              P = 1.306562965 * A + _,\n              k = .707106781 * (L = v + m),\n              I = d + k,\n              C = d - k;\n            t[f + 5] = C + F, t[f + 3] = C - F, t[f + 1] = I + P, t[f + 7] = I - P, f += 8;\n          }\n          for (u = f = 0; u < 8; ++u) {\n            n = t[f], r = t[f + 8], i = t[f + 16], o = t[f + 24], a = t[f + 32], s = t[f + 40], l = t[f + 48];\n            var B = n + (h = t[f + 56]),\n              j = n - h,\n              E = r + l,\n              M = r - l,\n              O = i + s,\n              q = i - s,\n              T = o + a,\n              R = o - a,\n              D = B + T,\n              U = B - T,\n              z = E + O,\n              H = E - O;\n            t[f] = D + z, t[f + 32] = D - z;\n            var W = .707106781 * (H + U);\n            t[f + 16] = U + W, t[f + 48] = U - W;\n            var V = .382683433 * ((D = R + q) - (H = M + j)),\n              G = .5411961 * D + V,\n              Y = 1.306562965 * H + V,\n              J = .707106781 * (z = q + M),\n              X = j + J,\n              K = j - J;\n            t[f + 40] = K + G, t[f + 24] = K - G, t[f + 8] = X + Y, t[f + 56] = X - Y, f++;\n          }\n          for (u = 0; u < 64; ++u) c = t[u] * e[u], Z[u] = 0 < c ? c + .5 | 0 : c - .5 | 0;\n          return Z;\n        }(t, e), h = 0; h < 64; ++h) w[b[h]] = l[h];\n      var u = w[0] - n;\n      n = w[0], 0 == u ? V(r[0]) : (V(r[v[o = 32767 + u]]), V(y[o]));\n      for (var c = 63; 0 < c && 0 == w[c]; c--);\n      if (0 == c) return V(a), n;\n      for (var f, p = 1; p <= c;) {\n        for (var d = p; 0 == w[p] && p <= c; ++p);\n        var g = p - d;\n        if (16 <= g) {\n          f = g >> 4;\n          for (var m = 1; m <= f; ++m) V(s);\n          g &= 15;\n        }\n        o = 32767 + w[p], V(i[(g << 4) + v[o]]), V(y[o]), p++;\n      }\n      return 63 != c && V(a), n;\n    }\n    function X(t) {\n      if (t <= 0 && (t = 1), 100 < t && (t = 100), e != t) {\n        (function (t) {\n          for (var e = [16, 11, 10, 16, 24, 40, 51, 61, 12, 12, 14, 19, 26, 58, 60, 55, 14, 13, 16, 24, 40, 57, 69, 56, 14, 17, 22, 29, 51, 87, 80, 62, 18, 22, 37, 56, 68, 109, 103, 77, 24, 35, 55, 64, 81, 104, 113, 92, 49, 64, 78, 87, 103, 121, 120, 101, 72, 92, 95, 98, 112, 100, 103, 99], n = 0; n < 64; n++) {\n            var r = c((e[n] * t + 50) / 100);\n            r < 1 ? r = 1 : 255 < r && (r = 255), _[b[n]] = r;\n          }\n          for (var i = [17, 18, 24, 47, 99, 99, 99, 99, 18, 21, 26, 66, 99, 99, 99, 99, 24, 26, 56, 99, 99, 99, 99, 99, 47, 66, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99], o = 0; o < 64; o++) {\n            var a = c((i[o] * t + 50) / 100);\n            a < 1 ? a = 1 : 255 < a && (a = 255), F[b[o]] = a;\n          }\n          for (var s = [1, 1.387039845, 1.306562965, 1.175875602, 1, .785694958, .5411961, .275899379], l = 0, h = 0; h < 8; h++) for (var u = 0; u < 8; u++) P[l] = 1 / (_[b[l]] * s[h] * s[u] * 8), k[l] = 1 / (F[b[l]] * s[h] * s[u] * 8), l++;\n        })(t < 50 ? Math.floor(5e3 / t) : Math.floor(200 - 2 * t)), e = t;\n      }\n    }\n    this.encode = function (t, e) {\n      var n, r;\n      new Date().getTime();\n      e && X(e), I = new Array(), C = 0, B = 7, Y(65496), Y(65504), Y(16), G(74), G(70), G(73), G(70), G(0), G(1), G(1), G(0), Y(1), Y(1), G(0), G(0), function () {\n        Y(65499), Y(132), G(0);\n        for (var t = 0; t < 64; t++) G(_[t]);\n        G(1);\n        for (var e = 0; e < 64; e++) G(F[e]);\n      }(), n = t.width, r = t.height, Y(65472), Y(17), G(8), Y(r), Y(n), G(3), G(1), G(17), G(0), G(2), G(17), G(1), G(3), G(17), G(1), function () {\n        Y(65476), Y(418), G(0);\n        for (var t = 0; t < 16; t++) G(q[t + 1]);\n        for (var e = 0; e <= 11; e++) G(T[e]);\n        G(16);\n        for (var n = 0; n < 16; n++) G(R[n + 1]);\n        for (var r = 0; r <= 161; r++) G(D[r]);\n        G(1);\n        for (var i = 0; i < 16; i++) G(U[i + 1]);\n        for (var o = 0; o <= 11; o++) G(z[o]);\n        G(17);\n        for (var a = 0; a < 16; a++) G(H[a + 1]);\n        for (var s = 0; s <= 161; s++) G(W[s]);\n      }(), Y(65498), Y(12), G(3), G(1), G(0), G(2), G(17), G(3), G(17), G(0), G(63), G(0);\n      var i = 0,\n        o = 0,\n        a = 0;\n      C = 0, B = 7, this.encode.displayName = \"_encode_\";\n      for (var s, l, h, u, c, f, p, d, g, m = t.data, y = t.width, v = t.height, w = 4 * y, b = 0; b < v;) {\n        for (s = 0; s < w;) {\n          for (f = c = w * b + s, p = -1, g = d = 0; g < 64; g++) f = c + (d = g >> 3) * w + (p = 4 * (7 & g)), v <= b + d && (f -= w * (b + 1 + d - v)), w <= s + p && (f -= s + p - w + 4), l = m[f++], h = m[f++], u = m[f++], j[g] = (O[l] + O[h + 256 >> 0] + O[u + 512 >> 0] >> 16) - 128, E[g] = (O[l + 768 >> 0] + O[h + 1024 >> 0] + O[u + 1280 >> 0] >> 16) - 128, M[g] = (O[l + 1280 >> 0] + O[h + 1536 >> 0] + O[u + 1792 >> 0] >> 16) - 128;\n          i = J(j, P, i, N, A), o = J(E, k, o, L, S), a = J(M, k, a, L, S), s += 32;\n        }\n        b += 8;\n      }\n      if (0 <= B) {\n        var x = [];\n        x[1] = B + 1, x[0] = (1 << B + 1) - 1, V(x);\n      }\n      return Y(65497), new Uint8Array(I);\n    }, function () {\n      new Date().getTime();\n      t || (t = 50), function () {\n        for (var t = String.fromCharCode, e = 0; e < 256; e++) n[e] = t(e);\n      }(), N = r(q, T), L = r(U, z), A = r(R, D), S = r(H, W), function () {\n        for (var t = 1, e = 2, n = 1; n <= 15; n++) {\n          for (var r = t; r < e; r++) v[32767 + r] = n, y[32767 + r] = [], y[32767 + r][1] = n, y[32767 + r][0] = r;\n          for (var i = -(e - 1); i <= -t; i++) v[32767 + i] = n, y[32767 + i] = [], y[32767 + i][1] = n, y[32767 + i][0] = e - 1 + i;\n          t <<= 1, e <<= 1;\n        }\n      }(), function () {\n        for (var t = 0; t < 256; t++) O[t] = 19595 * t, O[t + 256 >> 0] = 38470 * t, O[t + 512 >> 0] = 7471 * t + 32768, O[t + 768 >> 0] = -11059 * t, O[t + 1024 >> 0] = -21709 * t, O[t + 1280 >> 0] = 32768 * t + 8421375, O[t + 1536 >> 0] = -27439 * t, O[t + 1792 >> 0] = -5329 * t;\n      }(), X(t), new Date().getTime();\n    }();\n  }\n  function Ft(t, e) {\n    if (this.pos = 0, this.buffer = t, this.datav = new DataView(t.buffer), this.is_with_alpha = !!e, this.bottom_up = !0, this.flag = String.fromCharCode(this.buffer[0]) + String.fromCharCode(this.buffer[1]), this.pos += 2, -1 === [\"BM\", \"BA\", \"CI\", \"CP\", \"IC\", \"PT\"].indexOf(this.flag)) throw new Error(\"Invalid BMP File\");\n    this.parseHeader(), this.parseBGR();\n  }\n  window.tmp = At, lt.API.adler32cs = (dt = \"function\" == typeof ArrayBuffer && \"function\" == typeof Uint8Array, gt = null, mt = function () {\n    if (!dt) return function () {\n      return !1;\n    };\n    try {\n      var t = {};\n      \"function\" == typeof t.Buffer && (gt = t.Buffer);\n    } catch (t) {}\n    return function (t) {\n      return t instanceof ArrayBuffer || null !== gt && t instanceof gt;\n    };\n  }(), yt = null !== gt ? function (t) {\n    return new gt(t, \"utf8\").toString(\"binary\");\n  } : function (t) {\n    return unescape(encodeURIComponent(t));\n  }, vt = function (t, e) {\n    for (var n = 65535 & t, r = t >>> 16, i = 0, o = e.length; i < o; i++) n = (n + (255 & e.charCodeAt(i))) % 65521, r = (r + n) % 65521;\n    return (r << 16 | n) >>> 0;\n  }, wt = function (t, e) {\n    for (var n = 65535 & t, r = t >>> 16, i = 0, o = e.length; i < o; i++) n = (n + e[i]) % 65521, r = (r + n) % 65521;\n    return (r << 16 | n) >>> 0;\n  }, xt = (bt = {}).Adler32 = (((pt = (ft = function (t) {\n    if (!(this instanceof ft)) throw new TypeError(\"Constructor cannot called be as a function.\");\n    if (!isFinite(t = null == t ? 1 : +t)) throw new Error(\"First arguments needs to be a finite number.\");\n    this.checksum = t >>> 0;\n  }).prototype = {}).constructor = ft).from = ((ht = function (t) {\n    if (!(this instanceof ft)) throw new TypeError(\"Constructor cannot called be as a function.\");\n    if (null == t) throw new Error(\"First argument needs to be a string.\");\n    this.checksum = vt(1, t.toString());\n  }).prototype = pt, ht), ft.fromUtf8 = ((ut = function (t) {\n    if (!(this instanceof ft)) throw new TypeError(\"Constructor cannot called be as a function.\");\n    if (null == t) throw new Error(\"First argument needs to be a string.\");\n    var e = yt(t.toString());\n    this.checksum = vt(1, e);\n  }).prototype = pt, ut), dt && (ft.fromBuffer = ((ct = function (t) {\n    if (!(this instanceof ft)) throw new TypeError(\"Constructor cannot called be as a function.\");\n    if (!mt(t)) throw new Error(\"First argument needs to be ArrayBuffer.\");\n    var e = new Uint8Array(t);\n    return this.checksum = wt(1, e);\n  }).prototype = pt, ct)), pt.update = function (t) {\n    if (null == t) throw new Error(\"First argument needs to be a string.\");\n    return t = t.toString(), this.checksum = vt(this.checksum, t);\n  }, pt.updateUtf8 = function (t) {\n    if (null == t) throw new Error(\"First argument needs to be a string.\");\n    var e = yt(t.toString());\n    return this.checksum = vt(this.checksum, e);\n  }, dt && (pt.updateBuffer = function (t) {\n    if (!mt(t)) throw new Error(\"First argument needs to be ArrayBuffer.\");\n    var e = new Uint8Array(t);\n    return this.checksum = wt(this.checksum, e);\n  }), pt.clone = function () {\n    return new xt(this.checksum);\n  }, ft), bt.from = function (t) {\n    if (null == t) throw new Error(\"First argument needs to be a string.\");\n    return vt(1, t.toString());\n  }, bt.fromUtf8 = function (t) {\n    if (null == t) throw new Error(\"First argument needs to be a string.\");\n    var e = yt(t.toString());\n    return vt(1, e);\n  }, dt && (bt.fromBuffer = function (t) {\n    if (!mt(t)) throw new Error(\"First argument need to be ArrayBuffer.\");\n    var e = new Uint8Array(t);\n    return wt(1, e);\n  }), bt), function (t) {\n    t.__bidiEngine__ = t.prototype.__bidiEngine__ = function (t) {\n      var d,\n        g,\n        c,\n        f,\n        i,\n        o,\n        a,\n        s = e,\n        m = [[0, 3, 0, 1, 0, 0, 0], [0, 3, 0, 1, 2, 2, 0], [0, 3, 0, 17, 2, 0, 1], [0, 3, 5, 5, 4, 1, 0], [0, 3, 21, 21, 4, 0, 1], [0, 3, 5, 5, 4, 2, 0]],\n        y = [[2, 0, 1, 1, 0, 1, 0], [2, 0, 1, 1, 0, 2, 0], [2, 0, 2, 1, 3, 2, 0], [2, 0, 2, 33, 3, 1, 1]],\n        v = {\n          L: 0,\n          R: 1,\n          EN: 2,\n          AN: 3,\n          N: 4,\n          B: 5,\n          S: 6\n        },\n        l = {\n          0: 0,\n          5: 1,\n          6: 2,\n          7: 3,\n          32: 4,\n          251: 5,\n          254: 6,\n          255: 7\n        },\n        h = [\"(\", \")\", \"(\", \"<\", \">\", \"<\", \"[\", \"]\", \"[\", \"{\", \"}\", \"{\", \"«\", \"»\", \"«\", \"‹\", \"›\", \"‹\", \"⁅\", \"⁆\", \"⁅\", \"⁽\", \"⁾\", \"⁽\", \"₍\", \"₎\", \"₍\", \"≤\", \"≥\", \"≤\", \"〈\", \"〉\", \"〈\", \"﹙\", \"﹚\", \"﹙\", \"﹛\", \"﹜\", \"﹛\", \"﹝\", \"﹞\", \"﹝\", \"﹤\", \"﹥\", \"﹤\"],\n        u = new RegExp(/^([1-4|9]|1[0-9]|2[0-9]|3[0168]|4[04589]|5[012]|7[78]|159|16[0-9]|17[0-2]|21[569]|22[03489]|250)$/),\n        w = !1,\n        b = 0;\n      this.__bidiEngine__ = {};\n      var x = function (t) {\n          var e = t.charCodeAt(),\n            n = e >> 8,\n            r = l[n];\n          return void 0 !== r ? s[256 * r + (255 & e)] : 252 === n || 253 === n ? \"AL\" : u.test(n) ? \"L\" : 8 === n ? \"R\" : \"N\";\n        },\n        p = function (t) {\n          for (var e, n = 0; n < t.length; n++) {\n            if (\"L\" === (e = x(t.charAt(n)))) return !1;\n            if (\"R\" === e) return !0;\n          }\n          return !1;\n        },\n        N = function (t, e, n, r) {\n          var i,\n            o,\n            a,\n            s,\n            l = e[r];\n          switch (l) {\n            case \"L\":\n            case \"R\":\n              w = !1;\n              break;\n            case \"N\":\n            case \"AN\":\n              break;\n            case \"EN\":\n              w && (l = \"AN\");\n              break;\n            case \"AL\":\n              w = !0, l = \"R\";\n              break;\n            case \"WS\":\n              l = \"N\";\n              break;\n            case \"CS\":\n              r < 1 || r + 1 >= e.length || \"EN\" !== (i = n[r - 1]) && \"AN\" !== i || \"EN\" !== (o = e[r + 1]) && \"AN\" !== o ? l = \"N\" : w && (o = \"AN\"), l = o === i ? o : \"N\";\n              break;\n            case \"ES\":\n              l = \"EN\" === (i = 0 < r ? n[r - 1] : \"B\") && r + 1 < e.length && \"EN\" === e[r + 1] ? \"EN\" : \"N\";\n              break;\n            case \"ET\":\n              if (0 < r && \"EN\" === n[r - 1]) {\n                l = \"EN\";\n                break;\n              }\n              if (w) {\n                l = \"N\";\n                break;\n              }\n              for (a = r + 1, s = e.length; a < s && \"ET\" === e[a];) a++;\n              l = a < s && \"EN\" === e[a] ? \"EN\" : \"N\";\n              break;\n            case \"NSM\":\n              if (c && !f) {\n                for (s = e.length, a = r + 1; a < s && \"NSM\" === e[a];) a++;\n                if (a < s) {\n                  var h = t[r],\n                    u = 1425 <= h && h <= 2303 || 64286 === h;\n                  if (i = e[a], u && (\"R\" === i || \"AL\" === i)) {\n                    l = \"R\";\n                    break;\n                  }\n                }\n              }\n              l = r < 1 || \"B\" === (i = e[r - 1]) ? \"N\" : n[r - 1];\n              break;\n            case \"B\":\n              d = !(w = !1), l = b;\n              break;\n            case \"S\":\n              g = !0, l = \"N\";\n              break;\n            case \"LRE\":\n            case \"RLE\":\n            case \"LRO\":\n            case \"RLO\":\n            case \"PDF\":\n              w = !1;\n              break;\n            case \"BN\":\n              l = \"N\";\n          }\n          return l;\n        },\n        L = function (t, e, n) {\n          var r = t.split(\"\");\n          return n && A(r, n, {\n            hiLevel: b\n          }), r.reverse(), e && e.reverse(), r.join(\"\");\n        },\n        A = function (t, e, n) {\n          var r,\n            i,\n            o,\n            a,\n            s,\n            l = -1,\n            h = t.length,\n            u = 0,\n            c = [],\n            f = b ? y : m,\n            p = [];\n          for (g = d = w = !1, i = 0; i < h; i++) p[i] = x(t[i]);\n          for (o = 0; o < h; o++) {\n            if (s = u, c[o] = N(t, p, c, o), r = 240 & (u = f[s][v[c[o]]]), u &= 15, e[o] = a = f[u][5], 0 < r) if (16 === r) {\n              for (i = l; i < o; i++) e[i] = 1;\n              l = -1;\n            } else l = -1;\n            if (f[u][6]) -1 === l && (l = o);else if (-1 < l) {\n              for (i = l; i < o; i++) e[i] = a;\n              l = -1;\n            }\n            \"B\" === p[o] && (e[o] = 0), n.hiLevel |= a;\n          }\n          g && function (t, e, n) {\n            for (var r = 0; r < n; r++) if (\"S\" === t[r]) {\n              e[r] = b;\n              for (var i = r - 1; 0 <= i && \"WS\" === t[i]; i--) e[i] = b;\n            }\n          }(p, e, h);\n        },\n        S = function (t, e, n, r, i) {\n          if (!(i.hiLevel < t)) {\n            if (1 === t && 1 === b && !d) return e.reverse(), void (n && n.reverse());\n            for (var o, a, s, l, h = e.length, u = 0; u < h;) {\n              if (r[u] >= t) {\n                for (s = u + 1; s < h && r[s] >= t;) s++;\n                for (l = u, a = s - 1; l < a; l++, a--) o = e[l], e[l] = e[a], e[a] = o, n && (o = n[l], n[l] = n[a], n[a] = o);\n                u = s;\n              }\n              u++;\n            }\n          }\n        },\n        _ = function (t, e, n) {\n          var r = t.split(\"\"),\n            i = {\n              hiLevel: b\n            };\n          return n || (n = []), A(r, n, i), function (t, e, n) {\n            if (0 !== n.hiLevel && a) for (var r, i = 0; i < t.length; i++) 1 === e[i] && 0 <= (r = h.indexOf(t[i])) && (t[i] = h[r + 1]);\n          }(r, n, i), S(2, r, e, n, i), S(1, r, e, n, i), r.join(\"\");\n        };\n      return this.__bidiEngine__.doBidiReorder = function (t, e, n) {\n        if (function (t, e) {\n          if (e) for (var n = 0; n < t.length; n++) e[n] = n;\n          void 0 === f && (f = p(t)), void 0 === o && (o = p(t));\n        }(t, e), c || !i || o) {\n          if (c && i && f ^ o) b = f ? 1 : 0, t = L(t, e, n);else if (!c && i && o) b = f ? 1 : 0, t = _(t, e, n), t = L(t, e);else if (!c || f || i || o) {\n            if (c && !i && f ^ o) t = L(t, e), t = f ? (b = 0, _(t, e, n)) : (b = 1, t = _(t, e, n), L(t, e));else if (c && f && !i && o) b = 1, t = _(t, e, n), t = L(t, e);else if (!c && !i && f ^ o) {\n              var r = a;\n              f ? (b = 1, t = _(t, e, n), b = 0, a = !1, t = _(t, e, n), a = r) : (b = 0, t = _(t, e, n), t = L(t, e), a = !(b = 1), t = _(t, e, n), a = r, t = L(t, e));\n            }\n          } else b = 0, t = _(t, e, n);\n        } else b = f ? 1 : 0, t = _(t, e, n);\n        return t;\n      }, this.__bidiEngine__.setOptions = function (t) {\n        t && (c = t.isInputVisual, i = t.isOutputVisual, f = t.isInputRtl, o = t.isOutputRtl, a = t.isSymmetricSwapping);\n      }, this.__bidiEngine__.setOptions(t), this.__bidiEngine__;\n    };\n    var e = [\"BN\", \"BN\", \"BN\", \"BN\", \"BN\", \"BN\", \"BN\", \"BN\", \"BN\", \"S\", \"B\", \"S\", \"WS\", \"B\", \"BN\", \"BN\", \"BN\", \"BN\", \"BN\", \"BN\", \"BN\", \"BN\", \"BN\", \"BN\", \"BN\", \"BN\", \"BN\", \"BN\", \"B\", \"B\", \"B\", \"S\", \"WS\", \"N\", \"N\", \"ET\", \"ET\", \"ET\", \"N\", \"N\", \"N\", \"N\", \"N\", \"ES\", \"CS\", \"ES\", \"CS\", \"CS\", \"EN\", \"EN\", \"EN\", \"EN\", \"EN\", \"EN\", \"EN\", \"EN\", \"EN\", \"EN\", \"CS\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"N\", \"N\", \"N\", \"N\", \"BN\", \"BN\", \"BN\", \"BN\", \"BN\", \"BN\", \"B\", \"BN\", \"BN\", \"BN\", \"BN\", \"BN\", \"BN\", \"BN\", \"BN\", \"BN\", \"BN\", \"BN\", \"BN\", \"BN\", \"BN\", \"BN\", \"BN\", \"BN\", \"BN\", \"BN\", \"BN\", \"BN\", \"BN\", \"BN\", \"BN\", \"BN\", \"BN\", \"CS\", \"N\", \"ET\", \"ET\", \"ET\", \"ET\", \"N\", \"N\", \"N\", \"N\", \"L\", \"N\", \"N\", \"BN\", \"N\", \"N\", \"ET\", \"ET\", \"EN\", \"EN\", \"N\", \"L\", \"N\", \"N\", \"N\", \"EN\", \"L\", \"N\", \"N\", \"N\", \"N\", \"N\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"N\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"N\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"N\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"N\", \"N\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"N\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"N\", \"L\", \"N\", \"N\", \"N\", \"N\", \"N\", \"ET\", \"N\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"R\", \"NSM\", \"R\", \"NSM\", \"NSM\", \"R\", \"NSM\", \"NSM\", \"R\", \"NSM\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"R\", \"R\", \"R\", \"R\", \"R\", \"R\", \"R\", \"R\", \"R\", \"R\", \"R\", \"R\", \"R\", \"R\", \"R\", \"R\", \"R\", \"R\", \"R\", \"R\", \"R\", \"R\", \"R\", \"R\", \"R\", \"R\", \"R\", \"N\", \"N\", \"N\", \"N\", \"N\", \"R\", \"R\", \"R\", \"R\", \"R\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"AN\", \"AN\", \"AN\", \"AN\", \"AN\", \"AN\", \"N\", \"N\", \"AL\", \"ET\", \"ET\", \"AL\", \"CS\", \"AL\", \"N\", \"N\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"AL\", \"AL\", \"N\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"AN\", \"AN\", \"AN\", \"AN\", \"AN\", \"AN\", \"AN\", \"AN\", \"AN\", \"AN\", \"ET\", \"AN\", \"AN\", \"AL\", \"AL\", \"AL\", \"NSM\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"AN\", \"N\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"AL\", \"AL\", \"NSM\", \"NSM\", \"N\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"AL\", \"AL\", \"EN\", \"EN\", \"EN\", \"EN\", \"EN\", \"EN\", \"EN\", \"EN\", \"EN\", \"EN\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"N\", \"AL\", \"AL\", \"NSM\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"N\", \"N\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"AL\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"R\", \"R\", \"R\", \"R\", \"R\", \"R\", \"R\", \"R\", \"R\", \"R\", \"R\", \"R\", \"R\", \"R\", \"R\", \"R\", \"R\", \"R\", \"R\", \"R\", \"R\", \"R\", \"R\", \"R\", \"R\", \"R\", \"R\", \"R\", \"R\", \"R\", \"R\", \"R\", \"R\", \"R\", \"R\", \"R\", \"R\", \"R\", \"R\", \"R\", \"R\", \"R\", \"R\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"R\", \"R\", \"N\", \"N\", \"N\", \"N\", \"R\", \"N\", \"N\", \"N\", \"N\", \"N\", \"WS\", \"WS\", \"WS\", \"WS\", \"WS\", \"WS\", \"WS\", \"WS\", \"WS\", \"WS\", \"WS\", \"BN\", \"BN\", \"BN\", \"L\", \"R\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"WS\", \"B\", \"LRE\", \"RLE\", \"PDF\", \"LRO\", \"RLO\", \"CS\", \"ET\", \"ET\", \"ET\", \"ET\", \"ET\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"CS\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"WS\", \"BN\", \"BN\", \"BN\", \"BN\", \"BN\", \"N\", \"LRI\", \"RLI\", \"FSI\", \"PDI\", \"BN\", \"BN\", \"BN\", \"BN\", \"BN\", \"BN\", \"EN\", \"L\", \"N\", \"N\", \"EN\", \"EN\", \"EN\", \"EN\", \"EN\", \"EN\", \"ES\", \"ES\", \"N\", \"N\", \"N\", \"L\", \"EN\", \"EN\", \"EN\", \"EN\", \"EN\", \"EN\", \"EN\", \"EN\", \"EN\", \"EN\", \"ES\", \"ES\", \"N\", \"N\", \"N\", \"N\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"N\", \"N\", \"N\", \"ET\", \"ET\", \"ET\", \"ET\", \"ET\", \"ET\", \"ET\", \"ET\", \"ET\", \"ET\", \"ET\", \"ET\", \"ET\", \"ET\", \"ET\", \"ET\", \"ET\", \"ET\", \"ET\", \"ET\", \"ET\", \"ET\", \"ET\", \"ET\", \"ET\", \"ET\", \"ET\", \"ET\", \"ET\", \"ET\", \"ET\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"L\", \"L\", \"L\", \"L\", \"L\", \"N\", \"N\", \"N\", \"N\", \"N\", \"R\", \"NSM\", \"R\", \"R\", \"R\", \"R\", \"R\", \"R\", \"R\", \"R\", \"R\", \"R\", \"ES\", \"R\", \"R\", \"R\", \"R\", \"R\", \"R\", \"R\", \"R\", \"R\", \"R\", \"R\", \"R\", \"R\", \"N\", \"R\", \"R\", \"R\", \"R\", \"R\", \"N\", \"R\", \"N\", \"R\", \"R\", \"N\", \"R\", \"R\", \"N\", \"R\", \"R\", \"R\", \"R\", \"R\", \"R\", \"R\", \"R\", \"R\", \"R\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"NSM\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"CS\", \"N\", \"CS\", \"N\", \"N\", \"CS\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"ET\", \"N\", \"N\", \"ES\", \"ES\", \"N\", \"N\", \"N\", \"N\", \"N\", \"ET\", \"ET\", \"N\", \"N\", \"N\", \"N\", \"N\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"N\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"AL\", \"N\", \"N\", \"BN\", \"N\", \"N\", \"N\", \"ET\", \"ET\", \"ET\", \"N\", \"N\", \"N\", \"N\", \"N\", \"ES\", \"CS\", \"ES\", \"CS\", \"CS\", \"EN\", \"EN\", \"EN\", \"EN\", \"EN\", \"EN\", \"EN\", \"EN\", \"EN\", \"EN\", \"CS\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"N\", \"N\", \"N\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"N\", \"N\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"N\", \"N\", \"L\", \"L\", \"L\", \"L\", \"L\", \"L\", \"N\", \"N\", \"L\", \"L\", \"L\", \"N\", \"N\", \"N\", \"ET\", \"ET\", \"N\", \"N\", \"N\", \"ET\", \"ET\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\", \"N\"],\n      o = new t.__bidiEngine__({\n        isInputVisual: !0\n      });\n    t.API.events.push([\"postProcessText\", function (t) {\n      var e = t.text,\n        n = (t.x, t.y, t.options || {}),\n        r = (t.mutex, n.lang, []);\n      if (\"[object Array]\" === Object.prototype.toString.call(e)) {\n        var i = 0;\n        for (r = [], i = 0; i < e.length; i += 1) \"[object Array]\" === Object.prototype.toString.call(e[i]) ? r.push([o.doBidiReorder(e[i][0]), e[i][1], e[i][2]]) : r.push([o.doBidiReorder(e[i])]);\n        t.text = r;\n      } else t.text = o.doBidiReorder(e);\n    }]);\n  }(lt), window.tmp = _t, Ft.prototype.parseHeader = function () {\n    if (this.fileSize = this.datav.getUint32(this.pos, !0), this.pos += 4, this.reserved = this.datav.getUint32(this.pos, !0), this.pos += 4, this.offset = this.datav.getUint32(this.pos, !0), this.pos += 4, this.headerSize = this.datav.getUint32(this.pos, !0), this.pos += 4, this.width = this.datav.getUint32(this.pos, !0), this.pos += 4, this.height = this.datav.getInt32(this.pos, !0), this.pos += 4, this.planes = this.datav.getUint16(this.pos, !0), this.pos += 2, this.bitPP = this.datav.getUint16(this.pos, !0), this.pos += 2, this.compress = this.datav.getUint32(this.pos, !0), this.pos += 4, this.rawSize = this.datav.getUint32(this.pos, !0), this.pos += 4, this.hr = this.datav.getUint32(this.pos, !0), this.pos += 4, this.vr = this.datav.getUint32(this.pos, !0), this.pos += 4, this.colors = this.datav.getUint32(this.pos, !0), this.pos += 4, this.importantColors = this.datav.getUint32(this.pos, !0), this.pos += 4, 16 === this.bitPP && this.is_with_alpha && (this.bitPP = 15), this.bitPP < 15) {\n      var t = 0 === this.colors ? 1 << this.bitPP : this.colors;\n      this.palette = new Array(t);\n      for (var e = 0; e < t; e++) {\n        var n = this.datav.getUint8(this.pos++, !0),\n          r = this.datav.getUint8(this.pos++, !0),\n          i = this.datav.getUint8(this.pos++, !0),\n          o = this.datav.getUint8(this.pos++, !0);\n        this.palette[e] = {\n          red: i,\n          green: r,\n          blue: n,\n          quad: o\n        };\n      }\n    }\n    this.height < 0 && (this.height *= -1, this.bottom_up = !1);\n  }, Ft.prototype.parseBGR = function () {\n    this.pos = this.offset;\n    try {\n      var t = \"bit\" + this.bitPP,\n        e = this.width * this.height * 4;\n      this.data = new Uint8Array(e), this[t]();\n    } catch (t) {\n      console.log(\"bit decode error:\" + t);\n    }\n  }, Ft.prototype.bit1 = function () {\n    var t = Math.ceil(this.width / 8),\n      e = t % 4,\n      n = 0 <= this.height ? this.height - 1 : -this.height;\n    for (n = this.height - 1; 0 <= n; n--) {\n      for (var r = this.bottom_up ? n : this.height - 1 - n, i = 0; i < t; i++) for (var o = this.datav.getUint8(this.pos++, !0), a = r * this.width * 4 + 8 * i * 4, s = 0; s < 8 && 8 * i + s < this.width; s++) {\n        var l = this.palette[o >> 7 - s & 1];\n        this.data[a + 4 * s] = l.blue, this.data[a + 4 * s + 1] = l.green, this.data[a + 4 * s + 2] = l.red, this.data[a + 4 * s + 3] = 255;\n      }\n      0 != e && (this.pos += 4 - e);\n    }\n  }, Ft.prototype.bit4 = function () {\n    for (var t = Math.ceil(this.width / 2), e = t % 4, n = this.height - 1; 0 <= n; n--) {\n      for (var r = this.bottom_up ? n : this.height - 1 - n, i = 0; i < t; i++) {\n        var o = this.datav.getUint8(this.pos++, !0),\n          a = r * this.width * 4 + 2 * i * 4,\n          s = o >> 4,\n          l = 15 & o,\n          h = this.palette[s];\n        if (this.data[a] = h.blue, this.data[a + 1] = h.green, this.data[a + 2] = h.red, this.data[a + 3] = 255, 2 * i + 1 >= this.width) break;\n        h = this.palette[l], this.data[a + 4] = h.blue, this.data[a + 4 + 1] = h.green, this.data[a + 4 + 2] = h.red, this.data[a + 4 + 3] = 255;\n      }\n      0 != e && (this.pos += 4 - e);\n    }\n  }, Ft.prototype.bit8 = function () {\n    for (var t = this.width % 4, e = this.height - 1; 0 <= e; e--) {\n      for (var n = this.bottom_up ? e : this.height - 1 - e, r = 0; r < this.width; r++) {\n        var i = this.datav.getUint8(this.pos++, !0),\n          o = n * this.width * 4 + 4 * r;\n        if (i < this.palette.length) {\n          var a = this.palette[i];\n          this.data[o] = a.red, this.data[o + 1] = a.green, this.data[o + 2] = a.blue, this.data[o + 3] = 255;\n        } else this.data[o] = 255, this.data[o + 1] = 255, this.data[o + 2] = 255, this.data[o + 3] = 255;\n      }\n      0 != t && (this.pos += 4 - t);\n    }\n  }, Ft.prototype.bit15 = function () {\n    for (var t = this.width % 3, e = parseInt(\"11111\", 2), n = this.height - 1; 0 <= n; n--) {\n      for (var r = this.bottom_up ? n : this.height - 1 - n, i = 0; i < this.width; i++) {\n        var o = this.datav.getUint16(this.pos, !0);\n        this.pos += 2;\n        var a = (o & e) / e * 255 | 0,\n          s = (o >> 5 & e) / e * 255 | 0,\n          l = (o >> 10 & e) / e * 255 | 0,\n          h = o >> 15 ? 255 : 0,\n          u = r * this.width * 4 + 4 * i;\n        this.data[u] = l, this.data[u + 1] = s, this.data[u + 2] = a, this.data[u + 3] = h;\n      }\n      this.pos += t;\n    }\n  }, Ft.prototype.bit16 = function () {\n    for (var t = this.width % 3, e = parseInt(\"11111\", 2), n = parseInt(\"111111\", 2), r = this.height - 1; 0 <= r; r--) {\n      for (var i = this.bottom_up ? r : this.height - 1 - r, o = 0; o < this.width; o++) {\n        var a = this.datav.getUint16(this.pos, !0);\n        this.pos += 2;\n        var s = (a & e) / e * 255 | 0,\n          l = (a >> 5 & n) / n * 255 | 0,\n          h = (a >> 11) / e * 255 | 0,\n          u = i * this.width * 4 + 4 * o;\n        this.data[u] = h, this.data[u + 1] = l, this.data[u + 2] = s, this.data[u + 3] = 255;\n      }\n      this.pos += t;\n    }\n  }, Ft.prototype.bit24 = function () {\n    for (var t = this.height - 1; 0 <= t; t--) {\n      for (var e = this.bottom_up ? t : this.height - 1 - t, n = 0; n < this.width; n++) {\n        var r = this.datav.getUint8(this.pos++, !0),\n          i = this.datav.getUint8(this.pos++, !0),\n          o = this.datav.getUint8(this.pos++, !0),\n          a = e * this.width * 4 + 4 * n;\n        this.data[a] = o, this.data[a + 1] = i, this.data[a + 2] = r, this.data[a + 3] = 255;\n      }\n      this.pos += this.width % 4;\n    }\n  }, Ft.prototype.bit32 = function () {\n    for (var t = this.height - 1; 0 <= t; t--) for (var e = this.bottom_up ? t : this.height - 1 - t, n = 0; n < this.width; n++) {\n      var r = this.datav.getUint8(this.pos++, !0),\n        i = this.datav.getUint8(this.pos++, !0),\n        o = this.datav.getUint8(this.pos++, !0),\n        a = this.datav.getUint8(this.pos++, !0),\n        s = e * this.width * 4 + 4 * n;\n      this.data[s] = o, this.data[s + 1] = i, this.data[s + 2] = r, this.data[s + 3] = a;\n    }\n  }, Ft.prototype.getData = function () {\n    return this.data;\n  }, window.tmp = Ft,\n  /*\n     Copyright (c) 2013 Gildas Lormeau. All rights reserved.\n  \n     Redistribution and use in source and binary forms, with or without\n     modification, are permitted provided that the following conditions are met:\n  \n     1. Redistributions of source code must retain the above copyright notice,\n     this list of conditions and the following disclaimer.\n  \n     2. Redistributions in binary form must reproduce the above copyright \n     notice, this list of conditions and the following disclaimer in \n     the documentation and/or other materials provided with the distribution.\n  \n     3. The names of the authors may not be used to endorse or promote products\n     derived from this software without specific prior written permission.\n  \n     THIS SOFTWARE IS PROVIDED ``AS IS'' AND ANY EXPRESSED OR IMPLIED WARRANTIES,\n     INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND\n     FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL JCRAFT,\n     INC. OR ANY CONTRIBUTORS TO THIS SOFTWARE BE LIABLE FOR ANY DIRECT, INDIRECT,\n     INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT\n     LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA,\n     OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF\n     LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING\n     NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE,\n     EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.\n     */\n  function (t) {\n    var d = 15,\n      g = 573,\n      e = [0, 1, 2, 3, 4, 4, 5, 5, 6, 6, 6, 6, 7, 7, 7, 7, 8, 8, 8, 8, 8, 8, 8, 8, 9, 9, 9, 9, 9, 9, 9, 9, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 12, 12, 12, 12, 12, 12, 12, 12, 12, 12, 12, 12, 12, 12, 12, 12, 12, 12, 12, 12, 12, 12, 12, 12, 12, 12, 12, 12, 12, 12, 12, 12, 13, 13, 13, 13, 13, 13, 13, 13, 13, 13, 13, 13, 13, 13, 13, 13, 13, 13, 13, 13, 13, 13, 13, 13, 13, 13, 13, 13, 13, 13, 13, 13, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 15, 15, 15, 15, 15, 15, 15, 15, 15, 15, 15, 15, 15, 15, 15, 15, 15, 15, 15, 15, 15, 15, 15, 15, 15, 15, 15, 15, 15, 15, 15, 15, 15, 15, 15, 15, 15, 15, 15, 15, 15, 15, 15, 15, 15, 15, 15, 15, 15, 15, 15, 15, 15, 15, 15, 15, 15, 15, 15, 15, 15, 15, 15, 15, 0, 0, 16, 17, 18, 18, 19, 19, 20, 20, 20, 20, 21, 21, 21, 21, 22, 22, 22, 22, 22, 22, 22, 22, 23, 23, 23, 23, 23, 23, 23, 23, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29, 29];\n    function ct() {\n      var p = this;\n      function l(t, e) {\n        for (var n = 0; n |= 1 & t, t >>>= 1, n <<= 1, 0 < --e;);\n        return n >>> 1;\n      }\n      p.build_tree = function (t) {\n        var e,\n          n,\n          r,\n          i = p.dyn_tree,\n          o = p.stat_desc.static_tree,\n          a = p.stat_desc.elems,\n          s = -1;\n        for (t.heap_len = 0, t.heap_max = g, e = 0; e < a; e++) 0 !== i[2 * e] ? (t.heap[++t.heap_len] = s = e, t.depth[e] = 0) : i[2 * e + 1] = 0;\n        for (; t.heap_len < 2;) i[2 * (r = t.heap[++t.heap_len] = s < 2 ? ++s : 0)] = 1, t.depth[r] = 0, t.opt_len--, o && (t.static_len -= o[2 * r + 1]);\n        for (p.max_code = s, e = Math.floor(t.heap_len / 2); 1 <= e; e--) t.pqdownheap(i, e);\n        for (r = a; e = t.heap[1], t.heap[1] = t.heap[t.heap_len--], t.pqdownheap(i, 1), n = t.heap[1], t.heap[--t.heap_max] = e, t.heap[--t.heap_max] = n, i[2 * r] = i[2 * e] + i[2 * n], t.depth[r] = Math.max(t.depth[e], t.depth[n]) + 1, i[2 * e + 1] = i[2 * n + 1] = r, t.heap[1] = r++, t.pqdownheap(i, 1), 2 <= t.heap_len;);\n        t.heap[--t.heap_max] = t.heap[1], function (t) {\n          var e,\n            n,\n            r,\n            i,\n            o,\n            a,\n            s = p.dyn_tree,\n            l = p.stat_desc.static_tree,\n            h = p.stat_desc.extra_bits,\n            u = p.stat_desc.extra_base,\n            c = p.stat_desc.max_length,\n            f = 0;\n          for (i = 0; i <= d; i++) t.bl_count[i] = 0;\n          for (s[2 * t.heap[t.heap_max] + 1] = 0, e = t.heap_max + 1; e < g; e++) c < (i = s[2 * s[2 * (n = t.heap[e]) + 1] + 1] + 1) && (i = c, f++), s[2 * n + 1] = i, n > p.max_code || (t.bl_count[i]++, o = 0, u <= n && (o = h[n - u]), a = s[2 * n], t.opt_len += a * (i + o), l && (t.static_len += a * (l[2 * n + 1] + o)));\n          if (0 !== f) {\n            do {\n              for (i = c - 1; 0 === t.bl_count[i];) i--;\n              t.bl_count[i]--, t.bl_count[i + 1] += 2, t.bl_count[c]--, f -= 2;\n            } while (0 < f);\n            for (i = c; 0 !== i; i--) for (n = t.bl_count[i]; 0 !== n;) (r = t.heap[--e]) > p.max_code || (s[2 * r + 1] != i && (t.opt_len += (i - s[2 * r + 1]) * s[2 * r], s[2 * r + 1] = i), n--);\n          }\n        }(t), function (t, e, n) {\n          var r,\n            i,\n            o,\n            a = [],\n            s = 0;\n          for (r = 1; r <= d; r++) a[r] = s = s + n[r - 1] << 1;\n          for (i = 0; i <= e; i++) 0 !== (o = t[2 * i + 1]) && (t[2 * i] = l(a[o]++, o));\n        }(i, p.max_code, t.bl_count);\n      };\n    }\n    function ft(t, e, n, r, i) {\n      this.static_tree = t, this.extra_bits = e, this.extra_base = n, this.elems = r, this.max_length = i;\n    }\n    ct._length_code = [0, 1, 2, 3, 4, 5, 6, 7, 8, 8, 9, 9, 10, 10, 11, 11, 12, 12, 12, 12, 13, 13, 13, 13, 14, 14, 14, 14, 15, 15, 15, 15, 16, 16, 16, 16, 16, 16, 16, 16, 17, 17, 17, 17, 17, 17, 17, 17, 18, 18, 18, 18, 18, 18, 18, 18, 19, 19, 19, 19, 19, 19, 19, 19, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 28], ct.base_length = [0, 1, 2, 3, 4, 5, 6, 7, 8, 10, 12, 14, 16, 20, 24, 28, 32, 40, 48, 56, 64, 80, 96, 112, 128, 160, 192, 224, 0], ct.base_dist = [0, 1, 2, 3, 4, 6, 8, 12, 16, 24, 32, 48, 64, 96, 128, 192, 256, 384, 512, 768, 1024, 1536, 2048, 3072, 4096, 6144, 8192, 12288, 16384, 24576], ct.d_code = function (t) {\n      return t < 256 ? e[t] : e[256 + (t >>> 7)];\n    }, ct.extra_lbits = [0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 2, 2, 2, 2, 3, 3, 3, 3, 4, 4, 4, 4, 5, 5, 5, 5, 0], ct.extra_dbits = [0, 0, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11, 12, 12, 13, 13], ct.extra_blbits = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 3, 7], ct.bl_order = [16, 17, 18, 0, 8, 7, 9, 6, 10, 5, 11, 4, 12, 3, 13, 2, 14, 1, 15], ft.static_ltree = [12, 8, 140, 8, 76, 8, 204, 8, 44, 8, 172, 8, 108, 8, 236, 8, 28, 8, 156, 8, 92, 8, 220, 8, 60, 8, 188, 8, 124, 8, 252, 8, 2, 8, 130, 8, 66, 8, 194, 8, 34, 8, 162, 8, 98, 8, 226, 8, 18, 8, 146, 8, 82, 8, 210, 8, 50, 8, 178, 8, 114, 8, 242, 8, 10, 8, 138, 8, 74, 8, 202, 8, 42, 8, 170, 8, 106, 8, 234, 8, 26, 8, 154, 8, 90, 8, 218, 8, 58, 8, 186, 8, 122, 8, 250, 8, 6, 8, 134, 8, 70, 8, 198, 8, 38, 8, 166, 8, 102, 8, 230, 8, 22, 8, 150, 8, 86, 8, 214, 8, 54, 8, 182, 8, 118, 8, 246, 8, 14, 8, 142, 8, 78, 8, 206, 8, 46, 8, 174, 8, 110, 8, 238, 8, 30, 8, 158, 8, 94, 8, 222, 8, 62, 8, 190, 8, 126, 8, 254, 8, 1, 8, 129, 8, 65, 8, 193, 8, 33, 8, 161, 8, 97, 8, 225, 8, 17, 8, 145, 8, 81, 8, 209, 8, 49, 8, 177, 8, 113, 8, 241, 8, 9, 8, 137, 8, 73, 8, 201, 8, 41, 8, 169, 8, 105, 8, 233, 8, 25, 8, 153, 8, 89, 8, 217, 8, 57, 8, 185, 8, 121, 8, 249, 8, 5, 8, 133, 8, 69, 8, 197, 8, 37, 8, 165, 8, 101, 8, 229, 8, 21, 8, 149, 8, 85, 8, 213, 8, 53, 8, 181, 8, 117, 8, 245, 8, 13, 8, 141, 8, 77, 8, 205, 8, 45, 8, 173, 8, 109, 8, 237, 8, 29, 8, 157, 8, 93, 8, 221, 8, 61, 8, 189, 8, 125, 8, 253, 8, 19, 9, 275, 9, 147, 9, 403, 9, 83, 9, 339, 9, 211, 9, 467, 9, 51, 9, 307, 9, 179, 9, 435, 9, 115, 9, 371, 9, 243, 9, 499, 9, 11, 9, 267, 9, 139, 9, 395, 9, 75, 9, 331, 9, 203, 9, 459, 9, 43, 9, 299, 9, 171, 9, 427, 9, 107, 9, 363, 9, 235, 9, 491, 9, 27, 9, 283, 9, 155, 9, 411, 9, 91, 9, 347, 9, 219, 9, 475, 9, 59, 9, 315, 9, 187, 9, 443, 9, 123, 9, 379, 9, 251, 9, 507, 9, 7, 9, 263, 9, 135, 9, 391, 9, 71, 9, 327, 9, 199, 9, 455, 9, 39, 9, 295, 9, 167, 9, 423, 9, 103, 9, 359, 9, 231, 9, 487, 9, 23, 9, 279, 9, 151, 9, 407, 9, 87, 9, 343, 9, 215, 9, 471, 9, 55, 9, 311, 9, 183, 9, 439, 9, 119, 9, 375, 9, 247, 9, 503, 9, 15, 9, 271, 9, 143, 9, 399, 9, 79, 9, 335, 9, 207, 9, 463, 9, 47, 9, 303, 9, 175, 9, 431, 9, 111, 9, 367, 9, 239, 9, 495, 9, 31, 9, 287, 9, 159, 9, 415, 9, 95, 9, 351, 9, 223, 9, 479, 9, 63, 9, 319, 9, 191, 9, 447, 9, 127, 9, 383, 9, 255, 9, 511, 9, 0, 7, 64, 7, 32, 7, 96, 7, 16, 7, 80, 7, 48, 7, 112, 7, 8, 7, 72, 7, 40, 7, 104, 7, 24, 7, 88, 7, 56, 7, 120, 7, 4, 7, 68, 7, 36, 7, 100, 7, 20, 7, 84, 7, 52, 7, 116, 7, 3, 8, 131, 8, 67, 8, 195, 8, 35, 8, 163, 8, 99, 8, 227, 8], ft.static_dtree = [0, 5, 16, 5, 8, 5, 24, 5, 4, 5, 20, 5, 12, 5, 28, 5, 2, 5, 18, 5, 10, 5, 26, 5, 6, 5, 22, 5, 14, 5, 30, 5, 1, 5, 17, 5, 9, 5, 25, 5, 5, 5, 21, 5, 13, 5, 29, 5, 3, 5, 19, 5, 11, 5, 27, 5, 7, 5, 23, 5], ft.static_l_desc = new ft(ft.static_ltree, ct.extra_lbits, 257, 286, d), ft.static_d_desc = new ft(ft.static_dtree, ct.extra_dbits, 0, 30, d), ft.static_bl_desc = new ft(null, ct.extra_blbits, 0, 19, 7);\n    function n(t, e, n, r, i) {\n      this.good_length = t, this.max_lazy = e, this.nice_length = n, this.max_chain = r, this.func = i;\n    }\n    var pt = [new n(0, 0, 0, 0, 0), new n(4, 4, 8, 4, 1), new n(4, 5, 16, 8, 1), new n(4, 6, 32, 32, 1), new n(4, 4, 16, 16, 2), new n(8, 16, 32, 32, 2), new n(8, 16, 128, 128, 2), new n(8, 32, 128, 256, 2), new n(32, 128, 258, 1024, 2), new n(32, 258, 258, 4096, 2)],\n      dt = [\"need dictionary\", \"stream end\", \"\", \"\", \"stream error\", \"data error\", \"\", \"buffer error\", \"\", \"\"];\n    function gt(t, e, n, r) {\n      var i = t[2 * e],\n        o = t[2 * n];\n      return i < o || i == o && r[e] <= r[n];\n    }\n    function r() {\n      var l,\n        h,\n        u,\n        c,\n        f,\n        p,\n        d,\n        g,\n        i,\n        m,\n        y,\n        v,\n        w,\n        a,\n        b,\n        x,\n        N,\n        L,\n        A,\n        S,\n        _,\n        F,\n        P,\n        k,\n        I,\n        C,\n        B,\n        j,\n        E,\n        M,\n        s,\n        O,\n        q,\n        T,\n        R,\n        D,\n        U,\n        o,\n        z,\n        H,\n        W,\n        V = this,\n        G = new ct(),\n        Y = new ct(),\n        J = new ct();\n      function X() {\n        var t;\n        for (t = 0; t < 286; t++) s[2 * t] = 0;\n        for (t = 0; t < 30; t++) O[2 * t] = 0;\n        for (t = 0; t < 19; t++) q[2 * t] = 0;\n        s[512] = 1, V.opt_len = V.static_len = 0, D = o = 0;\n      }\n      function K(t, e) {\n        var n,\n          r,\n          i = -1,\n          o = t[1],\n          a = 0,\n          s = 7,\n          l = 4;\n        for (0 === o && (s = 138, l = 3), t[2 * (e + 1) + 1] = 65535, n = 0; n <= e; n++) r = o, o = t[2 * (n + 1) + 1], ++a < s && r == o || (a < l ? q[2 * r] += a : 0 !== r ? (r != i && q[2 * r]++, q[32]++) : a <= 10 ? q[34]++ : q[36]++, i = r, l = (a = 0) === o ? (s = 138, 3) : r == o ? (s = 6, 3) : (s = 7, 4));\n      }\n      function Z(t) {\n        V.pending_buf[V.pending++] = t;\n      }\n      function Q(t) {\n        Z(255 & t), Z(t >>> 8 & 255);\n      }\n      function $(t, e) {\n        var n,\n          r = e;\n        16 - r < W ? (Q(H |= (n = t) << W & 65535), H = n >>> 16 - W, W += r - 16) : (H |= t << W & 65535, W += r);\n      }\n      function tt(t, e) {\n        var n = 2 * t;\n        $(65535 & e[n], 65535 & e[n + 1]);\n      }\n      function et(t, e) {\n        var n,\n          r,\n          i = -1,\n          o = t[1],\n          a = 0,\n          s = 7,\n          l = 4;\n        for (0 === o && (s = 138, l = 3), n = 0; n <= e; n++) if (r = o, o = t[2 * (n + 1) + 1], !(++a < s && r == o)) {\n          if (a < l) for (; tt(r, q), 0 != --a;);else 0 !== r ? (r != i && (tt(r, q), a--), tt(16, q), $(a - 3, 2)) : a <= 10 ? (tt(17, q), $(a - 3, 3)) : (tt(18, q), $(a - 11, 7));\n          i = r, l = (a = 0) === o ? (s = 138, 3) : r == o ? (s = 6, 3) : (s = 7, 4);\n        }\n      }\n      function nt() {\n        16 == W ? (Q(H), W = H = 0) : 8 <= W && (Z(255 & H), H >>>= 8, W -= 8);\n      }\n      function rt(t, e) {\n        var n, r, i;\n        if (V.pending_buf[U + 2 * D] = t >>> 8 & 255, V.pending_buf[U + 2 * D + 1] = 255 & t, V.pending_buf[T + D] = 255 & e, D++, 0 === t ? s[2 * e]++ : (o++, t--, s[2 * (ct._length_code[e] + 256 + 1)]++, O[2 * ct.d_code(t)]++), 0 == (8191 & D) && 2 < B) {\n          for (n = 8 * D, r = _ - N, i = 0; i < 30; i++) n += O[2 * i] * (5 + ct.extra_dbits[i]);\n          if (n >>>= 3, o < Math.floor(D / 2) && n < Math.floor(r / 2)) return !0;\n        }\n        return D == R - 1;\n      }\n      function it(t, e) {\n        var n,\n          r,\n          i,\n          o,\n          a = 0;\n        if (0 !== D) for (; n = V.pending_buf[U + 2 * a] << 8 & 65280 | 255 & V.pending_buf[U + 2 * a + 1], r = 255 & V.pending_buf[T + a], a++, 0 === n ? tt(r, t) : (tt((i = ct._length_code[r]) + 256 + 1, t), 0 !== (o = ct.extra_lbits[i]) && $(r -= ct.base_length[i], o), tt(i = ct.d_code(--n), e), 0 !== (o = ct.extra_dbits[i]) && $(n -= ct.base_dist[i], o)), a < D;);\n        tt(256, t), z = t[513];\n      }\n      function ot() {\n        8 < W ? Q(H) : 0 < W && Z(255 & H), W = H = 0;\n      }\n      function at(t, e, n) {\n        var r, i, o;\n        $(0 + (n ? 1 : 0), 3), r = t, i = e, o = !0, ot(), z = 8, o && (Q(i), Q(~i)), V.pending_buf.set(g.subarray(r, r + i), V.pending), V.pending += i;\n      }\n      function e(t, e, n) {\n        var r,\n          i,\n          o = 0;\n        0 < B ? (G.build_tree(V), Y.build_tree(V), o = function () {\n          var t;\n          for (K(s, G.max_code), K(O, Y.max_code), J.build_tree(V), t = 18; 3 <= t && 0 === q[2 * ct.bl_order[t] + 1]; t--);\n          return V.opt_len += 3 * (t + 1) + 5 + 5 + 4, t;\n        }(), r = V.opt_len + 3 + 7 >>> 3, (i = V.static_len + 3 + 7 >>> 3) <= r && (r = i)) : r = i = e + 5, e + 4 <= r && -1 != t ? at(t, e, n) : i == r ? ($(2 + (n ? 1 : 0), 3), it(ft.static_ltree, ft.static_dtree)) : ($(4 + (n ? 1 : 0), 3), function (t, e, n) {\n          var r;\n          for ($(t - 257, 5), $(e - 1, 5), $(n - 4, 4), r = 0; r < n; r++) $(q[2 * ct.bl_order[r] + 1], 3);\n          et(s, t - 1), et(O, e - 1);\n        }(G.max_code + 1, Y.max_code + 1, o + 1), it(s, O)), X(), n && ot();\n      }\n      function st(t) {\n        e(0 <= N ? N : -1, _ - N, t), N = _, l.flush_pending();\n      }\n      function lt() {\n        var t, e, n, r;\n        do {\n          if (0 === (r = i - P - _) && 0 === _ && 0 === P) r = f;else if (-1 == r) r--;else if (f + f - 262 <= _) {\n            for (g.set(g.subarray(f, f + f), 0), F -= f, _ -= f, N -= f, n = t = w; e = 65535 & y[--n], y[n] = f <= e ? e - f : 0, 0 != --t;);\n            for (n = t = f; e = 65535 & m[--n], m[n] = f <= e ? e - f : 0, 0 != --t;);\n            r += f;\n          }\n          if (0 === l.avail_in) return;\n          t = l.read_buf(g, _ + P, r), 3 <= (P += t) && (v = ((v = 255 & g[_]) << x ^ 255 & g[_ + 1]) & b);\n        } while (P < 262 && 0 !== l.avail_in);\n      }\n      function ht(t) {\n        var e,\n          n,\n          r = I,\n          i = _,\n          o = k,\n          a = f - 262 < _ ? _ - (f - 262) : 0,\n          s = M,\n          l = d,\n          h = _ + 258,\n          u = g[i + o - 1],\n          c = g[i + o];\n        E <= k && (r >>= 2), P < s && (s = P);\n        do {\n          if (g[(e = t) + o] == c && g[e + o - 1] == u && g[e] == g[i] && g[++e] == g[i + 1]) {\n            i += 2, e++;\n            do {} while (g[++i] == g[++e] && g[++i] == g[++e] && g[++i] == g[++e] && g[++i] == g[++e] && g[++i] == g[++e] && g[++i] == g[++e] && g[++i] == g[++e] && g[++i] == g[++e] && i < h);\n            if (n = 258 - (h - i), i = h - 258, o < n) {\n              if (F = t, s <= (o = n)) break;\n              u = g[i + o - 1], c = g[i + o];\n            }\n          }\n        } while ((t = 65535 & m[t & l]) > a && 0 != --r);\n        return o <= P ? o : P;\n      }\n      function ut(t) {\n        return t.total_in = t.total_out = 0, t.msg = null, V.pending = 0, V.pending_out = 0, h = 113, c = 0, G.dyn_tree = s, G.stat_desc = ft.static_l_desc, Y.dyn_tree = O, Y.stat_desc = ft.static_d_desc, J.dyn_tree = q, J.stat_desc = ft.static_bl_desc, W = H = 0, z = 8, X(), function () {\n          var t;\n          for (i = 2 * f, t = y[w - 1] = 0; t < w - 1; t++) y[t] = 0;\n          C = pt[B].max_lazy, E = pt[B].good_length, M = pt[B].nice_length, I = pt[B].max_chain, L = k = 2, v = S = P = N = _ = 0;\n        }(), 0;\n      }\n      V.depth = [], V.bl_count = [], V.heap = [], s = [], O = [], q = [], V.pqdownheap = function (t, e) {\n        for (var n = V.heap, r = n[e], i = e << 1; i <= V.heap_len && (i < V.heap_len && gt(t, n[i + 1], n[i], V.depth) && i++, !gt(t, r, n[i], V.depth));) n[e] = n[i], e = i, i <<= 1;\n        n[e] = r;\n      }, V.deflateInit = function (t, e, n, r, i, o) {\n        return r || (r = 8), i || (i = 8), o || (o = 0), t.msg = null, -1 == e && (e = 6), i < 1 || 9 < i || 8 != r || n < 9 || 15 < n || e < 0 || 9 < e || o < 0 || 2 < o ? -2 : (t.dstate = V, d = (f = 1 << (p = n)) - 1, b = (w = 1 << (a = i + 7)) - 1, x = Math.floor((a + 3 - 1) / 3), g = new Uint8Array(2 * f), m = [], y = [], R = 1 << i + 6, V.pending_buf = new Uint8Array(4 * R), u = 4 * R, U = Math.floor(R / 2), T = 3 * R, B = e, j = o, ut(t));\n      }, V.deflateEnd = function () {\n        return 42 != h && 113 != h && 666 != h ? -2 : (V.pending_buf = null, g = m = y = null, V.dstate = null, 113 == h ? -3 : 0);\n      }, V.deflateParams = function (t, e, n) {\n        var r = 0;\n        return -1 == e && (e = 6), e < 0 || 9 < e || n < 0 || 2 < n ? -2 : (pt[B].func != pt[e].func && 0 !== t.total_in && (r = t.deflate(1)), B != e && (C = pt[B = e].max_lazy, E = pt[B].good_length, M = pt[B].nice_length, I = pt[B].max_chain), j = n, r);\n      }, V.deflateSetDictionary = function (t, e, n) {\n        var r,\n          i = n,\n          o = 0;\n        if (!e || 42 != h) return -2;\n        if (i < 3) return 0;\n        for (f - 262 < i && (o = n - (i = f - 262)), g.set(e.subarray(o, o + i), 0), N = _ = i, v = ((v = 255 & g[0]) << x ^ 255 & g[1]) & b, r = 0; r <= i - 3; r++) v = (v << x ^ 255 & g[r + 2]) & b, m[r & d] = y[v], y[v] = r;\n        return 0;\n      }, V.deflate = function (t, e) {\n        var n, r, i, o, a, s;\n        if (4 < e || e < 0) return -2;\n        if (!t.next_out || !t.next_in && 0 !== t.avail_in || 666 == h && 4 != e) return t.msg = dt[4], -2;\n        if (0 === t.avail_out) return t.msg = dt[7], -5;\n        if (l = t, o = c, c = e, 42 == h && (r = 8 + (p - 8 << 4) << 8, 3 < (i = (B - 1 & 255) >> 1) && (i = 3), r |= i << 6, 0 !== _ && (r |= 32), h = 113, Z((s = r += 31 - r % 31) >> 8 & 255), Z(255 & s)), 0 !== V.pending) {\n          if (l.flush_pending(), 0 === l.avail_out) return c = -1, 0;\n        } else if (0 === l.avail_in && e <= o && 4 != e) return l.msg = dt[7], -5;\n        if (666 == h && 0 !== l.avail_in) return t.msg = dt[7], -5;\n        if (0 !== l.avail_in || 0 !== P || 0 != e && 666 != h) {\n          switch (a = -1, pt[B].func) {\n            case 0:\n              a = function (t) {\n                var e,\n                  n = 65535;\n                for (u - 5 < n && (n = u - 5);;) {\n                  if (P <= 1) {\n                    if (lt(), 0 === P && 0 == t) return 0;\n                    if (0 === P) break;\n                  }\n                  if (_ += P, e = N + n, ((P = 0) === _ || e <= _) && (P = _ - e, _ = e, st(!1), 0 === l.avail_out)) return 0;\n                  if (f - 262 <= _ - N && (st(!1), 0 === l.avail_out)) return 0;\n                }\n                return st(4 == t), 0 === l.avail_out ? 4 == t ? 2 : 0 : 4 == t ? 3 : 1;\n              }(e);\n              break;\n            case 1:\n              a = function (t) {\n                for (var e, n = 0;;) {\n                  if (P < 262) {\n                    if (lt(), P < 262 && 0 == t) return 0;\n                    if (0 === P) break;\n                  }\n                  if (3 <= P && (v = (v << x ^ 255 & g[_ + 2]) & b, n = 65535 & y[v], m[_ & d] = y[v], y[v] = _), 0 !== n && (_ - n & 65535) <= f - 262 && 2 != j && (L = ht(n)), 3 <= L) {\n                    if (e = rt(_ - F, L - 3), P -= L, L <= C && 3 <= P) {\n                      for (L--; v = (v << x ^ 255 & g[++_ + 2]) & b, n = 65535 & y[v], m[_ & d] = y[v], y[v] = _, 0 != --L;);\n                      _++;\n                    } else _ += L, L = 0, v = ((v = 255 & g[_]) << x ^ 255 & g[_ + 1]) & b;\n                  } else e = rt(0, 255 & g[_]), P--, _++;\n                  if (e && (st(!1), 0 === l.avail_out)) return 0;\n                }\n                return st(4 == t), 0 === l.avail_out ? 4 == t ? 2 : 0 : 4 == t ? 3 : 1;\n              }(e);\n              break;\n            case 2:\n              a = function (t) {\n                for (var e, n, r = 0;;) {\n                  if (P < 262) {\n                    if (lt(), P < 262 && 0 == t) return 0;\n                    if (0 === P) break;\n                  }\n                  if (3 <= P && (v = (v << x ^ 255 & g[_ + 2]) & b, r = 65535 & y[v], m[_ & d] = y[v], y[v] = _), k = L, A = F, L = 2, 0 !== r && k < C && (_ - r & 65535) <= f - 262 && (2 != j && (L = ht(r)), L <= 5 && (1 == j || 3 == L && 4096 < _ - F) && (L = 2)), 3 <= k && L <= k) {\n                    for (n = _ + P - 3, e = rt(_ - 1 - A, k - 3), P -= k - 1, k -= 2; ++_ <= n && (v = (v << x ^ 255 & g[_ + 2]) & b, r = 65535 & y[v], m[_ & d] = y[v], y[v] = _), 0 != --k;);\n                    if (S = 0, L = 2, _++, e && (st(!1), 0 === l.avail_out)) return 0;\n                  } else if (0 !== S) {\n                    if ((e = rt(0, 255 & g[_ - 1])) && st(!1), _++, P--, 0 === l.avail_out) return 0;\n                  } else S = 1, _++, P--;\n                }\n                return 0 !== S && (e = rt(0, 255 & g[_ - 1]), S = 0), st(4 == t), 0 === l.avail_out ? 4 == t ? 2 : 0 : 4 == t ? 3 : 1;\n              }(e);\n          }\n          if (2 != a && 3 != a || (h = 666), 0 == a || 2 == a) return 0 === l.avail_out && (c = -1), 0;\n          if (1 == a) {\n            if (1 == e) $(2, 3), tt(256, ft.static_ltree), nt(), 1 + z + 10 - W < 9 && ($(2, 3), tt(256, ft.static_ltree), nt()), z = 7;else if (at(0, 0, !1), 3 == e) for (n = 0; n < w; n++) y[n] = 0;\n            if (l.flush_pending(), 0 === l.avail_out) return c = -1, 0;\n          }\n        }\n        return 4 != e ? 0 : 1;\n      };\n    }\n    function i() {\n      this.next_in_index = 0, this.next_out_index = 0, this.avail_in = 0, this.total_in = 0, this.avail_out = 0, this.total_out = 0;\n    }\n    i.prototype = {\n      deflateInit: function (t, e) {\n        return this.dstate = new r(), e || (e = d), this.dstate.deflateInit(this, t, e);\n      },\n      deflate: function (t) {\n        return this.dstate ? this.dstate.deflate(this, t) : -2;\n      },\n      deflateEnd: function () {\n        if (!this.dstate) return -2;\n        var t = this.dstate.deflateEnd();\n        return this.dstate = null, t;\n      },\n      deflateParams: function (t, e) {\n        return this.dstate ? this.dstate.deflateParams(this, t, e) : -2;\n      },\n      deflateSetDictionary: function (t, e) {\n        return this.dstate ? this.dstate.deflateSetDictionary(this, t, e) : -2;\n      },\n      read_buf: function (t, e, n) {\n        var r = this.avail_in;\n        return n < r && (r = n), 0 === r ? 0 : (this.avail_in -= r, t.set(this.next_in.subarray(this.next_in_index, this.next_in_index + r), e), this.next_in_index += r, this.total_in += r, r);\n      },\n      flush_pending: function () {\n        var t = this,\n          e = t.dstate.pending;\n        e > t.avail_out && (e = t.avail_out), 0 !== e && (t.next_out.set(t.dstate.pending_buf.subarray(t.dstate.pending_out, t.dstate.pending_out + e), t.next_out_index), t.next_out_index += e, t.dstate.pending_out += e, t.total_out += e, t.avail_out -= e, t.dstate.pending -= e, 0 === t.dstate.pending && (t.dstate.pending_out = 0));\n      }\n    };\n    var o = t.zip || t;\n    o.Deflater = o._jzlib_Deflater = function (t) {\n      var s = new i(),\n        l = new Uint8Array(512),\n        e = t ? t.level : -1;\n      void 0 === e && (e = -1), s.deflateInit(e), s.next_out = l, this.append = function (t, e) {\n        var n,\n          r = [],\n          i = 0,\n          o = 0,\n          a = 0;\n        if (t.length) {\n          s.next_in_index = 0, s.next_in = t, s.avail_in = t.length;\n          do {\n            if (s.next_out_index = 0, s.avail_out = 512, 0 != s.deflate(0)) throw new Error(\"deflating: \" + s.msg);\n            s.next_out_index && (512 == s.next_out_index ? r.push(new Uint8Array(l)) : r.push(new Uint8Array(l.subarray(0, s.next_out_index)))), a += s.next_out_index, e && 0 < s.next_in_index && s.next_in_index != i && (e(s.next_in_index), i = s.next_in_index);\n          } while (0 < s.avail_in || 0 === s.avail_out);\n          return n = new Uint8Array(a), r.forEach(function (t) {\n            n.set(t, o), o += t.length;\n          }), n;\n        }\n      }, this.flush = function () {\n        var t,\n          e,\n          n = [],\n          r = 0,\n          i = 0;\n        do {\n          if (s.next_out_index = 0, s.avail_out = 512, 1 != (t = s.deflate(4)) && 0 != t) throw new Error(\"deflating: \" + s.msg);\n          0 < 512 - s.avail_out && n.push(new Uint8Array(l.subarray(0, s.next_out_index))), i += s.next_out_index;\n        } while (0 < s.avail_in || 0 === s.avail_out);\n        return s.deflateEnd(), e = new Uint8Array(i), n.forEach(function (t) {\n          e.set(t, r), r += t.length;\n        }), e;\n      };\n    };\n  }(\"undefined\" != typeof self && self || \"undefined\" != typeof window && window || \"undefined\" != typeof global && global || Function('return typeof this === \"object\" && this.content')() || Function(\"return this\")()), (\"undefined\" != typeof self && self || \"undefined\" != typeof window && window || \"undefined\" != typeof global && global || Function('return typeof this === \"object\" && this.content')() || Function(\"return this\")()).RGBColor = function (t) {\n    var e;\n    t = t || \"\", this.ok = !1, \"#\" == t.charAt(0) && (t = t.substr(1, 6)), t = (t = t.replace(/ /g, \"\")).toLowerCase();\n    var n = {\n      aliceblue: \"f0f8ff\",\n      antiquewhite: \"faebd7\",\n      aqua: \"00ffff\",\n      aquamarine: \"7fffd4\",\n      azure: \"f0ffff\",\n      beige: \"f5f5dc\",\n      bisque: \"ffe4c4\",\n      black: \"000000\",\n      blanchedalmond: \"ffebcd\",\n      blue: \"0000ff\",\n      blueviolet: \"8a2be2\",\n      brown: \"a52a2a\",\n      burlywood: \"deb887\",\n      cadetblue: \"5f9ea0\",\n      chartreuse: \"7fff00\",\n      chocolate: \"d2691e\",\n      coral: \"ff7f50\",\n      cornflowerblue: \"6495ed\",\n      cornsilk: \"fff8dc\",\n      crimson: \"dc143c\",\n      cyan: \"00ffff\",\n      darkblue: \"00008b\",\n      darkcyan: \"008b8b\",\n      darkgoldenrod: \"b8860b\",\n      darkgray: \"a9a9a9\",\n      darkgreen: \"006400\",\n      darkkhaki: \"bdb76b\",\n      darkmagenta: \"8b008b\",\n      darkolivegreen: \"556b2f\",\n      darkorange: \"ff8c00\",\n      darkorchid: \"9932cc\",\n      darkred: \"8b0000\",\n      darksalmon: \"e9967a\",\n      darkseagreen: \"8fbc8f\",\n      darkslateblue: \"483d8b\",\n      darkslategray: \"2f4f4f\",\n      darkturquoise: \"00ced1\",\n      darkviolet: \"9400d3\",\n      deeppink: \"ff1493\",\n      deepskyblue: \"00bfff\",\n      dimgray: \"696969\",\n      dodgerblue: \"1e90ff\",\n      feldspar: \"d19275\",\n      firebrick: \"b22222\",\n      floralwhite: \"fffaf0\",\n      forestgreen: \"228b22\",\n      fuchsia: \"ff00ff\",\n      gainsboro: \"dcdcdc\",\n      ghostwhite: \"f8f8ff\",\n      gold: \"ffd700\",\n      goldenrod: \"daa520\",\n      gray: \"808080\",\n      green: \"008000\",\n      greenyellow: \"adff2f\",\n      honeydew: \"f0fff0\",\n      hotpink: \"ff69b4\",\n      indianred: \"cd5c5c\",\n      indigo: \"4b0082\",\n      ivory: \"fffff0\",\n      khaki: \"f0e68c\",\n      lavender: \"e6e6fa\",\n      lavenderblush: \"fff0f5\",\n      lawngreen: \"7cfc00\",\n      lemonchiffon: \"fffacd\",\n      lightblue: \"add8e6\",\n      lightcoral: \"f08080\",\n      lightcyan: \"e0ffff\",\n      lightgoldenrodyellow: \"fafad2\",\n      lightgrey: \"d3d3d3\",\n      lightgreen: \"90ee90\",\n      lightpink: \"ffb6c1\",\n      lightsalmon: \"ffa07a\",\n      lightseagreen: \"20b2aa\",\n      lightskyblue: \"87cefa\",\n      lightslateblue: \"8470ff\",\n      lightslategray: \"778899\",\n      lightsteelblue: \"b0c4de\",\n      lightyellow: \"ffffe0\",\n      lime: \"00ff00\",\n      limegreen: \"32cd32\",\n      linen: \"faf0e6\",\n      magenta: \"ff00ff\",\n      maroon: \"800000\",\n      mediumaquamarine: \"66cdaa\",\n      mediumblue: \"0000cd\",\n      mediumorchid: \"ba55d3\",\n      mediumpurple: \"9370d8\",\n      mediumseagreen: \"3cb371\",\n      mediumslateblue: \"7b68ee\",\n      mediumspringgreen: \"00fa9a\",\n      mediumturquoise: \"48d1cc\",\n      mediumvioletred: \"c71585\",\n      midnightblue: \"191970\",\n      mintcream: \"f5fffa\",\n      mistyrose: \"ffe4e1\",\n      moccasin: \"ffe4b5\",\n      navajowhite: \"ffdead\",\n      navy: \"000080\",\n      oldlace: \"fdf5e6\",\n      olive: \"808000\",\n      olivedrab: \"6b8e23\",\n      orange: \"ffa500\",\n      orangered: \"ff4500\",\n      orchid: \"da70d6\",\n      palegoldenrod: \"eee8aa\",\n      palegreen: \"98fb98\",\n      paleturquoise: \"afeeee\",\n      palevioletred: \"d87093\",\n      papayawhip: \"ffefd5\",\n      peachpuff: \"ffdab9\",\n      peru: \"cd853f\",\n      pink: \"ffc0cb\",\n      plum: \"dda0dd\",\n      powderblue: \"b0e0e6\",\n      purple: \"800080\",\n      red: \"ff0000\",\n      rosybrown: \"bc8f8f\",\n      royalblue: \"4169e1\",\n      saddlebrown: \"8b4513\",\n      salmon: \"fa8072\",\n      sandybrown: \"f4a460\",\n      seagreen: \"2e8b57\",\n      seashell: \"fff5ee\",\n      sienna: \"a0522d\",\n      silver: \"c0c0c0\",\n      skyblue: \"87ceeb\",\n      slateblue: \"6a5acd\",\n      slategray: \"708090\",\n      snow: \"fffafa\",\n      springgreen: \"00ff7f\",\n      steelblue: \"4682b4\",\n      tan: \"d2b48c\",\n      teal: \"008080\",\n      thistle: \"d8bfd8\",\n      tomato: \"ff6347\",\n      turquoise: \"40e0d0\",\n      violet: \"ee82ee\",\n      violetred: \"d02090\",\n      wheat: \"f5deb3\",\n      white: \"ffffff\",\n      whitesmoke: \"f5f5f5\",\n      yellow: \"ffff00\",\n      yellowgreen: \"9acd32\"\n    };\n    for (var r in n) t == r && (t = n[r]);\n    for (var i = [{\n        re: /^rgb\\((\\d{1,3}),\\s*(\\d{1,3}),\\s*(\\d{1,3})\\)$/,\n        example: [\"rgb(123, 234, 45)\", \"rgb(255,234,245)\"],\n        process: function (t) {\n          return [parseInt(t[1]), parseInt(t[2]), parseInt(t[3])];\n        }\n      }, {\n        re: /^(\\w{2})(\\w{2})(\\w{2})$/,\n        example: [\"#00ff00\", \"336699\"],\n        process: function (t) {\n          return [parseInt(t[1], 16), parseInt(t[2], 16), parseInt(t[3], 16)];\n        }\n      }, {\n        re: /^(\\w{1})(\\w{1})(\\w{1})$/,\n        example: [\"#fb0\", \"f0f\"],\n        process: function (t) {\n          return [parseInt(t[1] + t[1], 16), parseInt(t[2] + t[2], 16), parseInt(t[3] + t[3], 16)];\n        }\n      }], o = 0; o < i.length; o++) {\n      var a = i[o].re,\n        s = i[o].process,\n        l = a.exec(t);\n      l && (e = s(l), this.r = e[0], this.g = e[1], this.b = e[2], this.ok = !0);\n    }\n    this.r = this.r < 0 || isNaN(this.r) ? 0 : 255 < this.r ? 255 : this.r, this.g = this.g < 0 || isNaN(this.g) ? 0 : 255 < this.g ? 255 : this.g, this.b = this.b < 0 || isNaN(this.b) ? 0 : 255 < this.b ? 255 : this.b, this.toRGB = function () {\n      return \"rgb(\" + this.r + \", \" + this.g + \", \" + this.b + \")\";\n    }, this.toHex = function () {\n      var t = this.r.toString(16),\n        e = this.g.toString(16),\n        n = this.b.toString(16);\n      return 1 == t.length && (t = \"0\" + t), 1 == e.length && (e = \"0\" + e), 1 == n.length && (n = \"0\" + n), \"#\" + t + e + n;\n    };\n  }, function (t) {\n    var n = \"+\".charCodeAt(0),\n      r = \"/\".charCodeAt(0),\n      i = \"0\".charCodeAt(0),\n      o = \"a\".charCodeAt(0),\n      a = \"A\".charCodeAt(0),\n      s = \"-\".charCodeAt(0),\n      l = \"_\".charCodeAt(0),\n      u = function (t) {\n        var e = t.charCodeAt(0);\n        return e === n || e === s ? 62 : e === r || e === l ? 63 : e < i ? -1 : e < i + 10 ? e - i + 26 + 26 : e < a + 26 ? e - a : e < o + 26 ? e - o + 26 : void 0;\n      };\n    t.API.TTFFont = function () {\n      function i(t, e, n) {\n        var r;\n        if (this.rawData = t, r = this.contents = new J(t), this.contents.pos = 4, \"ttcf\" === r.readString(4)) {\n          if (!e) throw new Error(\"Must specify a font name for TTC files.\");\n          throw new Error(\"Font \" + e + \" not found in TTC file.\");\n        }\n        r.pos = 0, this.parse(), this.subset = new P(this), this.registerTTF();\n      }\n      return i.open = function (t, e, n, r) {\n        if (\"string\" != typeof n) throw new Error(\"Invalid argument supplied in TTFFont.open\");\n        return new i(function (t) {\n          var e, n, r, i, o, a;\n          if (0 < t.length % 4) throw new Error(\"Invalid string. Length must be a multiple of 4\");\n          var s = t.length;\n          o = \"=\" === t.charAt(s - 2) ? 2 : \"=\" === t.charAt(s - 1) ? 1 : 0, a = new Uint8Array(3 * t.length / 4 - o), r = 0 < o ? t.length - 4 : t.length;\n          var l = 0;\n          function h(t) {\n            a[l++] = t;\n          }\n          for (n = e = 0; e < r; e += 4, n += 3) h((16711680 & (i = u(t.charAt(e)) << 18 | u(t.charAt(e + 1)) << 12 | u(t.charAt(e + 2)) << 6 | u(t.charAt(e + 3)))) >> 16), h((65280 & i) >> 8), h(255 & i);\n          return 2 === o ? h(255 & (i = u(t.charAt(e)) << 2 | u(t.charAt(e + 1)) >> 4)) : 1 === o && (h((i = u(t.charAt(e)) << 10 | u(t.charAt(e + 1)) << 4 | u(t.charAt(e + 2)) >> 2) >> 8 & 255), h(255 & i)), a;\n        }(n), e, r);\n      }, i.prototype.parse = function () {\n        return this.directory = new e(this.contents), this.head = new p(this), this.name = new b(this), this.cmap = new y(this), this.toUnicode = new Map(), this.hhea = new g(this), this.maxp = new x(this), this.hmtx = new N(this), this.post = new v(this), this.os2 = new m(this), this.loca = new F(this), this.glyf = new A(this), this.ascender = this.os2.exists && this.os2.ascender || this.hhea.ascender, this.decender = this.os2.exists && this.os2.decender || this.hhea.decender, this.lineGap = this.os2.exists && this.os2.lineGap || this.hhea.lineGap, this.bbox = [this.head.xMin, this.head.yMin, this.head.xMax, this.head.yMax];\n      }, i.prototype.registerTTF = function () {\n        var i, t, e, n, r;\n        if (this.scaleFactor = 1e3 / this.head.unitsPerEm, this.bbox = function () {\n          var t, e, n, r;\n          for (r = [], t = 0, e = (n = this.bbox).length; t < e; t++) i = n[t], r.push(Math.round(i * this.scaleFactor));\n          return r;\n        }.call(this), this.stemV = 0, this.post.exists ? (e = 255 & (n = this.post.italic_angle), !0 & (t = n >> 16) && (t = -(1 + (65535 ^ t))), this.italicAngle = +(t + \".\" + e)) : this.italicAngle = 0, this.ascender = Math.round(this.ascender * this.scaleFactor), this.decender = Math.round(this.decender * this.scaleFactor), this.lineGap = Math.round(this.lineGap * this.scaleFactor), this.capHeight = this.os2.exists && this.os2.capHeight || this.ascender, this.xHeight = this.os2.exists && this.os2.xHeight || 0, this.familyClass = (this.os2.exists && this.os2.familyClass || 0) >> 8, this.isSerif = 1 === (r = this.familyClass) || 2 === r || 3 === r || 4 === r || 5 === r || 7 === r, this.isScript = 10 === this.familyClass, this.flags = 0, this.post.isFixedPitch && (this.flags |= 1), this.isSerif && (this.flags |= 2), this.isScript && (this.flags |= 8), 0 !== this.italicAngle && (this.flags |= 64), this.flags |= 32, !this.cmap.unicode) throw new Error(\"No unicode cmap for font\");\n      }, i.prototype.characterToGlyph = function (t) {\n        var e;\n        return (null != (e = this.cmap.unicode) ? e.codeMap[t] : void 0) || 0;\n      }, i.prototype.widthOfGlyph = function (t) {\n        var e;\n        return e = 1e3 / this.head.unitsPerEm, this.hmtx.forGlyph(t).advance * e;\n      }, i.prototype.widthOfString = function (t, e, n) {\n        var r, i, o, a, s;\n        for (i = a = o = 0, s = (t = \"\" + t).length; 0 <= s ? a < s : s < a; i = 0 <= s ? ++a : --a) r = t.charCodeAt(i), o += this.widthOfGlyph(this.characterToGlyph(r)) + n * (1e3 / e) || 0;\n        return o * (e / 1e3);\n      }, i.prototype.lineHeight = function (t, e) {\n        var n;\n        return null == e && (e = !1), n = e ? this.lineGap : 0, (this.ascender + n - this.decender) / 1e3 * t;\n      }, i;\n    }();\n    var h,\n      J = function () {\n        function t(t) {\n          this.data = null != t ? t : [], this.pos = 0, this.length = this.data.length;\n        }\n        return t.prototype.readByte = function () {\n          return this.data[this.pos++];\n        }, t.prototype.writeByte = function (t) {\n          return this.data[this.pos++] = t;\n        }, t.prototype.readUInt32 = function () {\n          return 16777216 * this.readByte() + (this.readByte() << 16) + (this.readByte() << 8) + this.readByte();\n        }, t.prototype.writeUInt32 = function (t) {\n          return this.writeByte(t >>> 24 & 255), this.writeByte(t >> 16 & 255), this.writeByte(t >> 8 & 255), this.writeByte(255 & t);\n        }, t.prototype.readInt32 = function () {\n          var t;\n          return 2147483648 <= (t = this.readUInt32()) ? t - 4294967296 : t;\n        }, t.prototype.writeInt32 = function (t) {\n          return t < 0 && (t += 4294967296), this.writeUInt32(t);\n        }, t.prototype.readUInt16 = function () {\n          return this.readByte() << 8 | this.readByte();\n        }, t.prototype.writeUInt16 = function (t) {\n          return this.writeByte(t >> 8 & 255), this.writeByte(255 & t);\n        }, t.prototype.readInt16 = function () {\n          var t;\n          return 32768 <= (t = this.readUInt16()) ? t - 65536 : t;\n        }, t.prototype.writeInt16 = function (t) {\n          return t < 0 && (t += 65536), this.writeUInt16(t);\n        }, t.prototype.readString = function (t) {\n          var e, n, r;\n          for (n = [], e = r = 0; 0 <= t ? r < t : t < r; e = 0 <= t ? ++r : --r) n[e] = String.fromCharCode(this.readByte());\n          return n.join(\"\");\n        }, t.prototype.writeString = function (t) {\n          var e, n, r, i;\n          for (i = [], e = n = 0, r = t.length; 0 <= r ? n < r : r < n; e = 0 <= r ? ++n : --n) i.push(this.writeByte(t.charCodeAt(e)));\n          return i;\n        }, t.prototype.readShort = function () {\n          return this.readInt16();\n        }, t.prototype.writeShort = function (t) {\n          return this.writeInt16(t);\n        }, t.prototype.readLongLong = function () {\n          var t, e, n, r, i, o, a, s;\n          return t = this.readByte(), e = this.readByte(), n = this.readByte(), r = this.readByte(), i = this.readByte(), o = this.readByte(), a = this.readByte(), s = this.readByte(), 128 & t ? -1 * (72057594037927940 * (255 ^ t) + 281474976710656 * (255 ^ e) + 1099511627776 * (255 ^ n) + 4294967296 * (255 ^ r) + 16777216 * (255 ^ i) + 65536 * (255 ^ o) + 256 * (255 ^ a) + (255 ^ s) + 1) : 72057594037927940 * t + 281474976710656 * e + 1099511627776 * n + 4294967296 * r + 16777216 * i + 65536 * o + 256 * a + s;\n        }, t.prototype.writeLongLong = function (t) {\n          var e, n;\n          return e = Math.floor(t / 4294967296), n = 4294967295 & t, this.writeByte(e >> 24 & 255), this.writeByte(e >> 16 & 255), this.writeByte(e >> 8 & 255), this.writeByte(255 & e), this.writeByte(n >> 24 & 255), this.writeByte(n >> 16 & 255), this.writeByte(n >> 8 & 255), this.writeByte(255 & n);\n        }, t.prototype.readInt = function () {\n          return this.readInt32();\n        }, t.prototype.writeInt = function (t) {\n          return this.writeInt32(t);\n        }, t.prototype.read = function (t) {\n          var e, n;\n          for (e = [], n = 0; 0 <= t ? n < t : t < n; 0 <= t ? ++n : --n) e.push(this.readByte());\n          return e;\n        }, t.prototype.write = function (t) {\n          var e, n, r, i;\n          for (i = [], n = 0, r = t.length; n < r; n++) e = t[n], i.push(this.writeByte(e));\n          return i;\n        }, t;\n      }(),\n      e = function () {\n        var d;\n        function t(t) {\n          var e, n, r;\n          for (this.scalarType = t.readInt(), this.tableCount = t.readShort(), this.searchRange = t.readShort(), this.entrySelector = t.readShort(), this.rangeShift = t.readShort(), this.tables = {}, n = 0, r = this.tableCount; 0 <= r ? n < r : r < n; 0 <= r ? ++n : --n) e = {\n            tag: t.readString(4),\n            checksum: t.readInt(),\n            offset: t.readInt(),\n            length: t.readInt()\n          }, this.tables[e.tag] = e;\n        }\n        return t.prototype.encode = function (t) {\n          var e, n, r, i, o, a, s, l, h, u, c, f, p;\n          for (p in c = Object.keys(t).length, a = Math.log(2), h = 16 * Math.floor(Math.log(c) / a), i = Math.floor(h / a), l = 16 * c - h, (n = new J()).writeInt(this.scalarType), n.writeShort(c), n.writeShort(h), n.writeShort(i), n.writeShort(l), r = 16 * c, s = n.pos + r, o = null, f = [], t) for (u = t[p], n.writeString(p), n.writeInt(d(u)), n.writeInt(s), n.writeInt(u.length), f = f.concat(u), \"head\" === p && (o = s), s += u.length; s % 4;) f.push(0), s++;\n          return n.write(f), e = 2981146554 - d(n.data), n.pos = o + 8, n.writeUInt32(e), n.data;\n        }, d = function (t) {\n          var e, n, r, i;\n          for (t = L.call(t); t.length % 4;) t.push(0);\n          for (n = new J(t), r = e = 0, i = t.length; r < i; r += 4) e += n.readUInt32();\n          return 4294967295 & e;\n        }, t;\n      }(),\n      c = {}.hasOwnProperty,\n      f = function (t, e) {\n        for (var n in e) c.call(e, n) && (t[n] = e[n]);\n        function r() {\n          this.constructor = t;\n        }\n        return r.prototype = e.prototype, t.prototype = new r(), t.__super__ = e.prototype, t;\n      };\n    h = function () {\n      function t(t) {\n        var e;\n        this.file = t, e = this.file.directory.tables[this.tag], this.exists = !!e, e && (this.offset = e.offset, this.length = e.length, this.parse(this.file.contents));\n      }\n      return t.prototype.parse = function () {}, t.prototype.encode = function () {}, t.prototype.raw = function () {\n        return this.exists ? (this.file.contents.pos = this.offset, this.file.contents.read(this.length)) : null;\n      }, t;\n    }();\n    var p = function (t) {\n        function e() {\n          return e.__super__.constructor.apply(this, arguments);\n        }\n        return f(e, h), e.prototype.tag = \"head\", e.prototype.parse = function (t) {\n          return t.pos = this.offset, this.version = t.readInt(), this.revision = t.readInt(), this.checkSumAdjustment = t.readInt(), this.magicNumber = t.readInt(), this.flags = t.readShort(), this.unitsPerEm = t.readShort(), this.created = t.readLongLong(), this.modified = t.readLongLong(), this.xMin = t.readShort(), this.yMin = t.readShort(), this.xMax = t.readShort(), this.yMax = t.readShort(), this.macStyle = t.readShort(), this.lowestRecPPEM = t.readShort(), this.fontDirectionHint = t.readShort(), this.indexToLocFormat = t.readShort(), this.glyphDataFormat = t.readShort();\n        }, e.prototype.encode = function (t) {\n          var e;\n          return (e = new J()).writeInt(this.version), e.writeInt(this.revision), e.writeInt(this.checkSumAdjustment), e.writeInt(this.magicNumber), e.writeShort(this.flags), e.writeShort(this.unitsPerEm), e.writeLongLong(this.created), e.writeLongLong(this.modified), e.writeShort(this.xMin), e.writeShort(this.yMin), e.writeShort(this.xMax), e.writeShort(this.yMax), e.writeShort(this.macStyle), e.writeShort(this.lowestRecPPEM), e.writeShort(this.fontDirectionHint), e.writeShort(t), e.writeShort(this.glyphDataFormat), e.data;\n        }, e;\n      }(),\n      d = function () {\n        function t(n, t) {\n          var e, r, i, o, a, s, l, h, u, c, f, p, d, g, m, y, v, w;\n          switch (this.platformID = n.readUInt16(), this.encodingID = n.readShort(), this.offset = t + n.readInt(), u = n.pos, n.pos = this.offset, this.format = n.readUInt16(), this.length = n.readUInt16(), this.language = n.readUInt16(), this.isUnicode = 3 === this.platformID && 1 === this.encodingID && 4 === this.format || 0 === this.platformID && 4 === this.format, this.codeMap = {}, this.format) {\n            case 0:\n              for (s = m = 0; m < 256; s = ++m) this.codeMap[s] = n.readByte();\n              break;\n            case 4:\n              for (f = n.readUInt16(), c = f / 2, n.pos += 6, i = function () {\n                var t, e;\n                for (e = [], s = t = 0; 0 <= c ? t < c : c < t; s = 0 <= c ? ++t : --t) e.push(n.readUInt16());\n                return e;\n              }(), n.pos += 2, d = function () {\n                var t, e;\n                for (e = [], s = t = 0; 0 <= c ? t < c : c < t; s = 0 <= c ? ++t : --t) e.push(n.readUInt16());\n                return e;\n              }(), l = function () {\n                var t, e;\n                for (e = [], s = t = 0; 0 <= c ? t < c : c < t; s = 0 <= c ? ++t : --t) e.push(n.readUInt16());\n                return e;\n              }(), h = function () {\n                var t, e;\n                for (e = [], s = t = 0; 0 <= c ? t < c : c < t; s = 0 <= c ? ++t : --t) e.push(n.readUInt16());\n                return e;\n              }(), r = (this.length - n.pos + this.offset) / 2, a = function () {\n                var t, e;\n                for (e = [], s = t = 0; 0 <= r ? t < r : r < t; s = 0 <= r ? ++t : --t) e.push(n.readUInt16());\n                return e;\n              }(), s = y = 0, w = i.length; y < w; s = ++y) for (g = i[s], e = v = p = d[s]; p <= g ? v <= g : g <= v; e = p <= g ? ++v : --v) 0 === h[s] ? o = e + l[s] : 0 !== (o = a[h[s] / 2 + (e - p) - (c - s)] || 0) && (o += l[s]), this.codeMap[e] = 65535 & o;\n          }\n          n.pos = u;\n        }\n        return t.encode = function (t, e) {\n          var n, r, i, o, a, s, l, h, u, c, f, p, d, g, m, y, v, w, b, x, N, L, A, S, _, F, P, k, I, C, B, j, E, M, O, q, T, R, D, U, z, H, W, V, G, Y;\n          switch (k = new J(), o = Object.keys(t).sort(function (t, e) {\n            return t - e;\n          }), e) {\n            case \"macroman\":\n              for (d = 0, g = function () {\n                var t, e;\n                for (e = [], p = t = 0; t < 256; p = ++t) e.push(0);\n                return e;\n              }(), y = {\n                0: 0\n              }, i = {}, I = 0, E = o.length; I < E; I++) null == y[W = t[r = o[I]]] && (y[W] = ++d), i[r] = {\n                old: t[r],\n                new: y[t[r]]\n              }, g[r] = y[t[r]];\n              return k.writeUInt16(1), k.writeUInt16(0), k.writeUInt32(12), k.writeUInt16(0), k.writeUInt16(262), k.writeUInt16(0), k.write(g), {\n                charMap: i,\n                subtable: k.data,\n                maxGlyphID: d + 1\n              };\n            case \"unicode\":\n              for (F = [], u = [], y = {}, n = {}, m = l = null, C = v = 0, M = o.length; C < M; C++) null == y[b = t[r = o[C]]] && (y[b] = ++v), n[r] = {\n                old: b,\n                new: y[b]\n              }, a = y[b] - r, null != m && a === l || (m && u.push(m), F.push(r), l = a), m = r;\n              for (m && u.push(m), u.push(65535), F.push(65535), S = 2 * (A = F.length), L = 2 * Math.pow(Math.log(A) / Math.LN2, 2), c = Math.log(L / 2) / Math.LN2, N = 2 * A - L, s = [], x = [], f = [], p = B = 0, O = F.length; B < O; p = ++B) {\n                if (_ = F[p], h = u[p], 65535 === _) {\n                  s.push(0), x.push(0);\n                  break;\n                }\n                if (32768 <= _ - (P = n[_].new)) for (s.push(0), x.push(2 * (f.length + A - p)), r = j = _; _ <= h ? j <= h : h <= j; r = _ <= h ? ++j : --j) f.push(n[r].new);else s.push(P - _), x.push(0);\n              }\n              for (k.writeUInt16(3), k.writeUInt16(1), k.writeUInt32(12), k.writeUInt16(4), k.writeUInt16(16 + 8 * A + 2 * f.length), k.writeUInt16(0), k.writeUInt16(S), k.writeUInt16(L), k.writeUInt16(c), k.writeUInt16(N), z = 0, q = u.length; z < q; z++) r = u[z], k.writeUInt16(r);\n              for (k.writeUInt16(0), H = 0, T = F.length; H < T; H++) r = F[H], k.writeUInt16(r);\n              for (V = 0, R = s.length; V < R; V++) a = s[V], k.writeUInt16(a);\n              for (G = 0, D = x.length; G < D; G++) w = x[G], k.writeUInt16(w);\n              for (Y = 0, U = f.length; Y < U; Y++) d = f[Y], k.writeUInt16(d);\n              return {\n                charMap: n,\n                subtable: k.data,\n                maxGlyphID: v + 1\n              };\n          }\n        }, t;\n      }(),\n      y = function (t) {\n        function e() {\n          return e.__super__.constructor.apply(this, arguments);\n        }\n        return f(e, h), e.prototype.tag = \"cmap\", e.prototype.parse = function (t) {\n          var e, n, r;\n          for (t.pos = this.offset, this.version = t.readUInt16(), n = t.readUInt16(), this.tables = [], this.unicode = null, r = 0; 0 <= n ? r < n : n < r; 0 <= n ? ++r : --r) e = new d(t, this.offset), this.tables.push(e), e.isUnicode && null == this.unicode && (this.unicode = e);\n          return !0;\n        }, e.encode = function (t, e) {\n          var n, r;\n          return null == e && (e = \"macroman\"), n = d.encode(t, e), (r = new J()).writeUInt16(0), r.writeUInt16(1), n.table = r.data.concat(n.subtable), n;\n        }, e;\n      }(),\n      g = function (t) {\n        function e() {\n          return e.__super__.constructor.apply(this, arguments);\n        }\n        return f(e, h), e.prototype.tag = \"hhea\", e.prototype.parse = function (t) {\n          return t.pos = this.offset, this.version = t.readInt(), this.ascender = t.readShort(), this.decender = t.readShort(), this.lineGap = t.readShort(), this.advanceWidthMax = t.readShort(), this.minLeftSideBearing = t.readShort(), this.minRightSideBearing = t.readShort(), this.xMaxExtent = t.readShort(), this.caretSlopeRise = t.readShort(), this.caretSlopeRun = t.readShort(), this.caretOffset = t.readShort(), t.pos += 8, this.metricDataFormat = t.readShort(), this.numberOfMetrics = t.readUInt16();\n        }, e;\n      }(),\n      m = function (t) {\n        function e() {\n          return e.__super__.constructor.apply(this, arguments);\n        }\n        return f(e, h), e.prototype.tag = \"OS/2\", e.prototype.parse = function (n) {\n          if (n.pos = this.offset, this.version = n.readUInt16(), this.averageCharWidth = n.readShort(), this.weightClass = n.readUInt16(), this.widthClass = n.readUInt16(), this.type = n.readShort(), this.ySubscriptXSize = n.readShort(), this.ySubscriptYSize = n.readShort(), this.ySubscriptXOffset = n.readShort(), this.ySubscriptYOffset = n.readShort(), this.ySuperscriptXSize = n.readShort(), this.ySuperscriptYSize = n.readShort(), this.ySuperscriptXOffset = n.readShort(), this.ySuperscriptYOffset = n.readShort(), this.yStrikeoutSize = n.readShort(), this.yStrikeoutPosition = n.readShort(), this.familyClass = n.readShort(), this.panose = function () {\n            var t, e;\n            for (e = [], t = 0; t < 10; ++t) e.push(n.readByte());\n            return e;\n          }(), this.charRange = function () {\n            var t, e;\n            for (e = [], t = 0; t < 4; ++t) e.push(n.readInt());\n            return e;\n          }(), this.vendorID = n.readString(4), this.selection = n.readShort(), this.firstCharIndex = n.readShort(), this.lastCharIndex = n.readShort(), 0 < this.version && (this.ascent = n.readShort(), this.descent = n.readShort(), this.lineGap = n.readShort(), this.winAscent = n.readShort(), this.winDescent = n.readShort(), this.codePageRange = function () {\n            var t, e;\n            for (e = [], t = 0; t < 2; ++t) e.push(n.readInt());\n            return e;\n          }(), 1 < this.version)) return this.xHeight = n.readShort(), this.capHeight = n.readShort(), this.defaultChar = n.readShort(), this.breakChar = n.readShort(), this.maxContext = n.readShort();\n        }, e;\n      }(),\n      v = function (t) {\n        function e() {\n          return e.__super__.constructor.apply(this, arguments);\n        }\n        return f(e, h), e.prototype.tag = \"post\", e.prototype.parse = function (r) {\n          var t, e, n, i;\n          switch (r.pos = this.offset, this.format = r.readInt(), this.italicAngle = r.readInt(), this.underlinePosition = r.readShort(), this.underlineThickness = r.readShort(), this.isFixedPitch = r.readInt(), this.minMemType42 = r.readInt(), this.maxMemType42 = r.readInt(), this.minMemType1 = r.readInt(), this.maxMemType1 = r.readInt(), this.format) {\n            case 65536:\n              break;\n            case 131072:\n              for (e = r.readUInt16(), this.glyphNameIndex = [], n = 0; 0 <= e ? n < e : e < n; 0 <= e ? ++n : --n) this.glyphNameIndex.push(r.readUInt16());\n              for (this.names = [], i = []; r.pos < this.offset + this.length;) t = r.readByte(), i.push(this.names.push(r.readString(t)));\n              return i;\n            case 151552:\n              return e = r.readUInt16(), this.offsets = r.read(e);\n            case 196608:\n              break;\n            case 262144:\n              return this.map = function () {\n                var t, e, n;\n                for (n = [], t = 0, e = this.file.maxp.numGlyphs; 0 <= e ? t < e : e < t; 0 <= e ? ++t : --t) n.push(r.readUInt32());\n                return n;\n              }.call(this);\n          }\n        }, e;\n      }(),\n      w = function (t, e) {\n        this.raw = t, this.length = t.length, this.platformID = e.platformID, this.encodingID = e.encodingID, this.languageID = e.languageID;\n      },\n      b = function (t) {\n        function e() {\n          return e.__super__.constructor.apply(this, arguments);\n        }\n        return f(e, h), e.prototype.tag = \"name\", e.prototype.parse = function (t) {\n          var e, n, r, i, o, a, s, l, h, u, c, f;\n          for (t.pos = this.offset, t.readShort(), e = t.readShort(), a = t.readShort(), n = [], i = h = 0; 0 <= e ? h < e : e < h; i = 0 <= e ? ++h : --h) n.push({\n            platformID: t.readShort(),\n            encodingID: t.readShort(),\n            languageID: t.readShort(),\n            nameID: t.readShort(),\n            length: t.readShort(),\n            offset: this.offset + a + t.readShort()\n          });\n          for (s = {}, i = u = 0, c = n.length; u < c; i = ++u) r = n[i], t.pos = r.offset, l = t.readString(r.length), o = new w(l, r), null == s[f = r.nameID] && (s[f] = []), s[r.nameID].push(o);\n          this.strings = s, this.copyright = s[0], this.fontFamily = s[1], this.fontSubfamily = s[2], this.uniqueSubfamily = s[3], this.fontName = s[4], this.version = s[5];\n          try {\n            this.postscriptName = s[6][0].raw.replace(/[\\x00-\\x19\\x80-\\xff]/g, \"\");\n          } catch (t) {\n            this.postscriptName = s[4][0].raw.replace(/[\\x00-\\x19\\x80-\\xff]/g, \"\");\n          }\n          return this.trademark = s[7], this.manufacturer = s[8], this.designer = s[9], this.description = s[10], this.vendorUrl = s[11], this.designerUrl = s[12], this.license = s[13], this.licenseUrl = s[14], this.preferredFamily = s[15], this.preferredSubfamily = s[17], this.compatibleFull = s[18], this.sampleText = s[19];\n        }, e;\n      }(),\n      x = function (t) {\n        function e() {\n          return e.__super__.constructor.apply(this, arguments);\n        }\n        return f(e, h), e.prototype.tag = \"maxp\", e.prototype.parse = function (t) {\n          return t.pos = this.offset, this.version = t.readInt(), this.numGlyphs = t.readUInt16(), this.maxPoints = t.readUInt16(), this.maxContours = t.readUInt16(), this.maxCompositePoints = t.readUInt16(), this.maxComponentContours = t.readUInt16(), this.maxZones = t.readUInt16(), this.maxTwilightPoints = t.readUInt16(), this.maxStorage = t.readUInt16(), this.maxFunctionDefs = t.readUInt16(), this.maxInstructionDefs = t.readUInt16(), this.maxStackElements = t.readUInt16(), this.maxSizeOfInstructions = t.readUInt16(), this.maxComponentElements = t.readUInt16(), this.maxComponentDepth = t.readUInt16();\n        }, e;\n      }(),\n      N = function (t) {\n        function e() {\n          return e.__super__.constructor.apply(this, arguments);\n        }\n        return f(e, h), e.prototype.tag = \"hmtx\", e.prototype.parse = function (n) {\n          var t, r, i, e, o, a, s;\n          for (n.pos = this.offset, this.metrics = [], e = 0, a = this.file.hhea.numberOfMetrics; 0 <= a ? e < a : a < e; 0 <= a ? ++e : --e) this.metrics.push({\n            advance: n.readUInt16(),\n            lsb: n.readInt16()\n          });\n          for (r = this.file.maxp.numGlyphs - this.file.hhea.numberOfMetrics, this.leftSideBearings = function () {\n            var t, e;\n            for (e = [], t = 0; 0 <= r ? t < r : r < t; 0 <= r ? ++t : --t) e.push(n.readInt16());\n            return e;\n          }(), this.widths = function () {\n            var t, e, n, r;\n            for (r = [], t = 0, e = (n = this.metrics).length; t < e; t++) i = n[t], r.push(i.advance);\n            return r;\n          }.call(this), t = this.widths[this.widths.length - 1], s = [], o = 0; 0 <= r ? o < r : r < o; 0 <= r ? ++o : --o) s.push(this.widths.push(t));\n          return s;\n        }, e.prototype.forGlyph = function (t) {\n          return t in this.metrics ? this.metrics[t] : {\n            advance: this.metrics[this.metrics.length - 1].advance,\n            lsb: this.leftSideBearings[t - this.metrics.length]\n          };\n        }, e;\n      }(),\n      L = [].slice,\n      A = function (t) {\n        function e() {\n          return e.__super__.constructor.apply(this, arguments);\n        }\n        return f(e, h), e.prototype.tag = \"glyf\", e.prototype.parse = function (t) {\n          return this.cache = {};\n        }, e.prototype.glyphFor = function (t) {\n          var e, n, r, i, o, a, s, l, h, u;\n          return (t = t) in this.cache ? this.cache[t] : (i = this.file.loca, e = this.file.contents, n = i.indexOf(t), 0 === (r = i.lengthOf(t)) ? this.cache[t] = null : (e.pos = this.offset + n, o = (a = new J(e.read(r))).readShort(), l = a.readShort(), u = a.readShort(), s = a.readShort(), h = a.readShort(), this.cache[t] = -1 === o ? new _(a, l, u, s, h) : new S(a, o, l, u, s, h), this.cache[t]));\n        }, e.prototype.encode = function (t, e, n) {\n          var r, i, o, a, s;\n          for (o = [], i = [], a = 0, s = e.length; a < s; a++) r = t[e[a]], i.push(o.length), r && (o = o.concat(r.encode(n)));\n          return i.push(o.length), {\n            table: o,\n            offsets: i\n          };\n        }, e;\n      }(),\n      S = function () {\n        function t(t, e, n, r, i, o) {\n          this.raw = t, this.numberOfContours = e, this.xMin = n, this.yMin = r, this.xMax = i, this.yMax = o, this.compound = !1;\n        }\n        return t.prototype.encode = function () {\n          return this.raw.data;\n        }, t;\n      }(),\n      _ = function () {\n        function t(t, e, n, r, i) {\n          var o, a;\n          for (this.raw = t, this.xMin = e, this.yMin = n, this.xMax = r, this.yMax = i, this.compound = !0, this.glyphIDs = [], this.glyphOffsets = [], o = this.raw; a = o.readShort(), this.glyphOffsets.push(o.pos), this.glyphIDs.push(o.readShort()), 32 & a;) o.pos += 1 & a ? 4 : 2, 128 & a ? o.pos += 8 : 64 & a ? o.pos += 4 : 8 & a && (o.pos += 2);\n        }\n        return 1, 8, 32, 64, 128, t.prototype.encode = function (t) {\n          var e, n, r, i, o;\n          for (n = new J(L.call(this.raw.data)), e = r = 0, i = (o = this.glyphIDs).length; r < i; e = ++r) o[e], n.pos = this.glyphOffsets[e];\n          return n.data;\n        }, t;\n      }(),\n      F = function (t) {\n        function e() {\n          return e.__super__.constructor.apply(this, arguments);\n        }\n        return f(e, h), e.prototype.tag = \"loca\", e.prototype.parse = function (r) {\n          var t;\n          return r.pos = this.offset, t = this.file.head.indexToLocFormat, this.offsets = 0 === t ? function () {\n            var t, e, n;\n            for (n = [], t = 0, e = this.length; t < e; t += 2) n.push(2 * r.readUInt16());\n            return n;\n          }.call(this) : function () {\n            var t, e, n;\n            for (n = [], t = 0, e = this.length; t < e; t += 4) n.push(r.readUInt32());\n            return n;\n          }.call(this);\n        }, e.prototype.indexOf = function (t) {\n          return this.offsets[t];\n        }, e.prototype.lengthOf = function (t) {\n          return this.offsets[t + 1] - this.offsets[t];\n        }, e.prototype.encode = function (t, e) {\n          for (var n = new Uint32Array(this.offsets.length), r = 0, i = 0, o = 0; o < n.length; ++o) if (n[o] = r, i < e.length && e[i] == o) {\n            ++i, n[o] = r;\n            var a = this.offsets[o],\n              s = this.offsets[o + 1] - a;\n            0 < s && (r += s);\n          }\n          for (var l = new Array(4 * n.length), h = 0; h < n.length; ++h) l[4 * h + 3] = 255 & n[h], l[4 * h + 2] = (65280 & n[h]) >> 8, l[4 * h + 1] = (16711680 & n[h]) >> 16, l[4 * h] = (4278190080 & n[h]) >> 24;\n          return l;\n        }, e;\n      }(),\n      P = function () {\n        function t(t) {\n          this.font = t, this.subset = {}, this.unicodes = {}, this.next = 33;\n        }\n        return t.prototype.generateCmap = function () {\n          var t, e, n, r, i;\n          for (e in r = this.font.cmap.tables[0].codeMap, t = {}, i = this.subset) n = i[e], t[e] = r[n];\n          return t;\n        }, t.prototype.glyphsFor = function (t) {\n          var e, n, r, i, o, a, s;\n          for (r = {}, o = 0, a = t.length; o < a; o++) r[i = t[o]] = this.font.glyf.glyphFor(i);\n          for (i in e = [], r) (null != (n = r[i]) ? n.compound : void 0) && e.push.apply(e, n.glyphIDs);\n          if (0 < e.length) for (i in s = this.glyphsFor(e)) n = s[i], r[i] = n;\n          return r;\n        }, t.prototype.encode = function (t, e) {\n          var n, r, i, o, a, s, l, h, u, c, f, p, d, g, m;\n          for (r in n = y.encode(this.generateCmap(), \"unicode\"), o = this.glyphsFor(t), f = {\n            0: 0\n          }, m = n.charMap) f[(s = m[r]).old] = s.new;\n          for (p in c = n.maxGlyphID, o) p in f || (f[p] = c++);\n          return h = function (t) {\n            var e, n;\n            for (e in n = {}, t) n[t[e]] = e;\n            return n;\n          }(f), u = Object.keys(h).sort(function (t, e) {\n            return t - e;\n          }), d = function () {\n            var t, e, n;\n            for (n = [], t = 0, e = u.length; t < e; t++) a = u[t], n.push(h[a]);\n            return n;\n          }(), i = this.font.glyf.encode(o, d, f), l = this.font.loca.encode(i.offsets, d), g = {\n            cmap: this.font.cmap.raw(),\n            glyf: i.table,\n            loca: l,\n            hmtx: this.font.hmtx.raw(),\n            hhea: this.font.hhea.raw(),\n            maxp: this.font.maxp.raw(),\n            post: this.font.post.raw(),\n            name: this.font.name.raw(),\n            head: this.font.head.encode(e)\n          }, this.font.os2.exists && (g[\"OS/2\"] = this.font.os2.raw()), this.font.directory.encode(g);\n        }, t;\n      }();\n    t.API.PDFObject = function () {\n      var o;\n      function a() {}\n      return o = function (t, e) {\n        return (Array(e + 1).join(\"0\") + t).slice(-e);\n      }, a.convert = function (r) {\n        var i, t, e, n;\n        if (Array.isArray(r)) return \"[\" + function () {\n          var t, e, n;\n          for (n = [], t = 0, e = r.length; t < e; t++) i = r[t], n.push(a.convert(i));\n          return n;\n        }().join(\" \") + \"]\";\n        if (\"string\" == typeof r) return \"/\" + r;\n        if (null != r ? r.isString : void 0) return \"(\" + r + \")\";\n        if (r instanceof Date) return \"(D:\" + o(r.getUTCFullYear(), 4) + o(r.getUTCMonth(), 2) + o(r.getUTCDate(), 2) + o(r.getUTCHours(), 2) + o(r.getUTCMinutes(), 2) + o(r.getUTCSeconds(), 2) + \"Z)\";\n        if (\"[object Object]\" !== {}.toString.call(r)) return \"\" + r;\n        for (t in e = [\"<<\"], r) n = r[t], e.push(\"/\" + t + \" \" + a.convert(n));\n        return e.push(\">>\"), e.join(\"\\n\");\n      }, a;\n    }();\n  }(lt),\n  /*\n    # PNG.js\n    # Copyright (c) 2011 Devon Govett\n    # MIT LICENSE\n    # \n    # \n    */\n  Nt = \"undefined\" != typeof self && self || \"undefined\" != typeof window && window || \"undefined\" != typeof global && global || Function('return typeof this === \"object\" && this.content')() || Function(\"return this\")(), Lt = function () {\n    var h, n, r;\n    function i(t) {\n      var e, n, r, i, o, a, s, l, h, u, c, f, p, d;\n      for (this.data = t, this.pos = 8, this.palette = [], this.imgData = [], this.transparency = {}, this.animation = null, this.text = {}, a = null;;) {\n        switch (e = this.readUInt32(), h = function () {\n          var t, e;\n          for (e = [], t = 0; t < 4; ++t) e.push(String.fromCharCode(this.data[this.pos++]));\n          return e;\n        }.call(this).join(\"\")) {\n          case \"IHDR\":\n            this.width = this.readUInt32(), this.height = this.readUInt32(), this.bits = this.data[this.pos++], this.colorType = this.data[this.pos++], this.compressionMethod = this.data[this.pos++], this.filterMethod = this.data[this.pos++], this.interlaceMethod = this.data[this.pos++];\n            break;\n          case \"acTL\":\n            this.animation = {\n              numFrames: this.readUInt32(),\n              numPlays: this.readUInt32() || 1 / 0,\n              frames: []\n            };\n            break;\n          case \"PLTE\":\n            this.palette = this.read(e);\n            break;\n          case \"fcTL\":\n            a && this.animation.frames.push(a), this.pos += 4, a = {\n              width: this.readUInt32(),\n              height: this.readUInt32(),\n              xOffset: this.readUInt32(),\n              yOffset: this.readUInt32()\n            }, o = this.readUInt16(), i = this.readUInt16() || 100, a.delay = 1e3 * o / i, a.disposeOp = this.data[this.pos++], a.blendOp = this.data[this.pos++], a.data = [];\n            break;\n          case \"IDAT\":\n          case \"fdAT\":\n            for (\"fdAT\" === h && (this.pos += 4, e -= 4), t = (null != a ? a.data : void 0) || this.imgData, f = 0; 0 <= e ? f < e : e < f; 0 <= e ? ++f : --f) t.push(this.data[this.pos++]);\n            break;\n          case \"tRNS\":\n            switch (this.transparency = {}, this.colorType) {\n              case 3:\n                if (r = this.palette.length / 3, this.transparency.indexed = this.read(e), this.transparency.indexed.length > r) throw new Error(\"More transparent colors than palette size\");\n                if (0 < (u = r - this.transparency.indexed.length)) for (p = 0; 0 <= u ? p < u : u < p; 0 <= u ? ++p : --p) this.transparency.indexed.push(255);\n                break;\n              case 0:\n                this.transparency.grayscale = this.read(e)[0];\n                break;\n              case 2:\n                this.transparency.rgb = this.read(e);\n            }\n            break;\n          case \"tEXt\":\n            s = (c = this.read(e)).indexOf(0), l = String.fromCharCode.apply(String, c.slice(0, s)), this.text[l] = String.fromCharCode.apply(String, c.slice(s + 1));\n            break;\n          case \"IEND\":\n            return a && this.animation.frames.push(a), this.colors = function () {\n              switch (this.colorType) {\n                case 0:\n                case 3:\n                case 4:\n                  return 1;\n                case 2:\n                case 6:\n                  return 3;\n              }\n            }.call(this), this.hasAlphaChannel = 4 === (d = this.colorType) || 6 === d, n = this.colors + (this.hasAlphaChannel ? 1 : 0), this.pixelBitlength = this.bits * n, this.colorSpace = function () {\n              switch (this.colors) {\n                case 1:\n                  return \"DeviceGray\";\n                case 3:\n                  return \"DeviceRGB\";\n              }\n            }.call(this), void (this.imgData = new Uint8Array(this.imgData));\n          default:\n            this.pos += e;\n        }\n        if (this.pos += 4, this.pos > this.data.length) throw new Error(\"Incomplete or corrupt PNG file\");\n      }\n    }\n    i.load = function (t, e, n) {\n      var r;\n      return \"function\" == typeof e && (n = e), (r = new XMLHttpRequest()).open(\"GET\", t, !0), r.responseType = \"arraybuffer\", r.onload = function () {\n        var t;\n        return t = new i(new Uint8Array(r.response || r.mozResponseArrayBuffer)), \"function\" == typeof (null != e ? e.getContext : void 0) && t.render(e), \"function\" == typeof n ? n(t) : void 0;\n      }, r.send(null);\n    }, i.prototype.read = function (t) {\n      var e, n;\n      for (n = [], e = 0; 0 <= t ? e < t : t < e; 0 <= t ? ++e : --e) n.push(this.data[this.pos++]);\n      return n;\n    }, i.prototype.readUInt32 = function () {\n      return this.data[this.pos++] << 24 | this.data[this.pos++] << 16 | this.data[this.pos++] << 8 | this.data[this.pos++];\n    }, i.prototype.readUInt16 = function () {\n      return this.data[this.pos++] << 8 | this.data[this.pos++];\n    }, i.prototype.decodePixels = function (C) {\n      var B = this.pixelBitlength / 8,\n        j = new Uint8Array(this.width * this.height * B),\n        E = 0,\n        M = this;\n      if (null == C && (C = this.imgData), 0 === C.length) return new Uint8Array(0);\n      function t(t, e, n, r) {\n        var i,\n          o,\n          a,\n          s,\n          l,\n          h,\n          u,\n          c,\n          f,\n          p,\n          d,\n          g,\n          m,\n          y,\n          v,\n          w,\n          b,\n          x,\n          N,\n          L,\n          A,\n          S = Math.ceil((M.width - t) / n),\n          _ = Math.ceil((M.height - e) / r),\n          F = M.width == S && M.height == _;\n        for (y = B * S, g = F ? j : new Uint8Array(y * _), h = C.length, o = m = 0; m < _ && E < h;) {\n          switch (C[E++]) {\n            case 0:\n              for (s = b = 0; b < y; s = b += 1) g[o++] = C[E++];\n              break;\n            case 1:\n              for (s = x = 0; x < y; s = x += 1) i = C[E++], l = s < B ? 0 : g[o - B], g[o++] = (i + l) % 256;\n              break;\n            case 2:\n              for (s = N = 0; N < y; s = N += 1) i = C[E++], a = (s - s % B) / B, v = m && g[(m - 1) * y + a * B + s % B], g[o++] = (v + i) % 256;\n              break;\n            case 3:\n              for (s = L = 0; L < y; s = L += 1) i = C[E++], a = (s - s % B) / B, l = s < B ? 0 : g[o - B], v = m && g[(m - 1) * y + a * B + s % B], g[o++] = (i + Math.floor((l + v) / 2)) % 256;\n              break;\n            case 4:\n              for (s = A = 0; A < y; s = A += 1) i = C[E++], a = (s - s % B) / B, l = s < B ? 0 : g[o - B], 0 === m ? v = w = 0 : (v = g[(m - 1) * y + a * B + s % B], w = a && g[(m - 1) * y + (a - 1) * B + s % B]), u = l + v - w, c = Math.abs(u - l), p = Math.abs(u - v), d = Math.abs(u - w), f = c <= p && c <= d ? l : p <= d ? v : w, g[o++] = (i + f) % 256;\n              break;\n            default:\n              throw new Error(\"Invalid filter algorithm: \" + C[E - 1]);\n          }\n          if (!F) {\n            var P = ((e + m * r) * M.width + t) * B,\n              k = m * y;\n            for (s = 0; s < S; s += 1) {\n              for (var I = 0; I < B; I += 1) j[P++] = g[k++];\n              P += (n - 1) * B;\n            }\n          }\n          m++;\n        }\n      }\n      return C = (C = new kt(C)).getBytes(), 1 == M.interlaceMethod ? (t(0, 0, 8, 8), t(4, 0, 8, 8), t(0, 4, 4, 8), t(2, 0, 4, 4), t(0, 2, 2, 4), t(1, 0, 2, 2), t(0, 1, 1, 2)) : t(0, 0, 1, 1), j;\n    }, i.prototype.decodePalette = function () {\n      var t, e, n, r, i, o, a, s, l;\n      for (n = this.palette, o = this.transparency.indexed || [], i = new Uint8Array((o.length || 0) + n.length), r = 0, n.length, e = a = t = 0, s = n.length; a < s; e = a += 3) i[r++] = n[e], i[r++] = n[e + 1], i[r++] = n[e + 2], i[r++] = null != (l = o[t++]) ? l : 255;\n      return i;\n    }, i.prototype.copyToImageData = function (t, e) {\n      var n, r, i, o, a, s, l, h, u, c, f;\n      if (r = this.colors, u = null, n = this.hasAlphaChannel, this.palette.length && (u = null != (f = this._decodedPalette) ? f : this._decodedPalette = this.decodePalette(), r = 4, n = !0), h = (i = t.data || t).length, a = u || e, o = s = 0, 1 === r) for (; o < h;) l = u ? 4 * e[o / 4] : s, c = a[l++], i[o++] = c, i[o++] = c, i[o++] = c, i[o++] = n ? a[l++] : 255, s = l;else for (; o < h;) l = u ? 4 * e[o / 4] : s, i[o++] = a[l++], i[o++] = a[l++], i[o++] = a[l++], i[o++] = n ? a[l++] : 255, s = l;\n    }, i.prototype.decode = function () {\n      var t;\n      return t = new Uint8Array(this.width * this.height * 4), this.copyToImageData(t, this.decodePixels()), t;\n    };\n    try {\n      n = Nt.document.createElement(\"canvas\"), r = n.getContext(\"2d\");\n    } catch (t) {\n      return -1;\n    }\n    return h = function (t) {\n      var e;\n      return r.width = t.width, r.height = t.height, r.clearRect(0, 0, t.width, t.height), r.putImageData(t, 0, 0), (e = new Image()).src = n.toDataURL(), e;\n    }, i.prototype.decodeFrames = function (t) {\n      var e, n, r, i, o, a, s, l;\n      if (this.animation) {\n        for (l = [], n = o = 0, a = (s = this.animation.frames).length; o < a; n = ++o) e = s[n], r = t.createImageData(e.width, e.height), i = this.decodePixels(new Uint8Array(e.data)), this.copyToImageData(r, i), e.imageData = r, l.push(e.image = h(r));\n        return l;\n      }\n    }, i.prototype.renderFrame = function (t, e) {\n      var n, r, i;\n      return n = (r = this.animation.frames)[e], i = r[e - 1], 0 === e && t.clearRect(0, 0, this.width, this.height), 1 === (null != i ? i.disposeOp : void 0) ? t.clearRect(i.xOffset, i.yOffset, i.width, i.height) : 2 === (null != i ? i.disposeOp : void 0) && t.putImageData(i.imageData, i.xOffset, i.yOffset), 0 === n.blendOp && t.clearRect(n.xOffset, n.yOffset, n.width, n.height), t.drawImage(n.image, n.xOffset, n.yOffset);\n    }, i.prototype.animate = function (n) {\n      var r,\n        i,\n        o,\n        a,\n        s,\n        t,\n        l = this;\n      return i = 0, t = this.animation, a = t.numFrames, o = t.frames, s = t.numPlays, (r = function () {\n        var t, e;\n        if (t = i++ % a, e = o[t], l.renderFrame(n, t), 1 < a && i / a < s) return l.animation._timeout = setTimeout(r, e.delay);\n      })();\n    }, i.prototype.stopAnimation = function () {\n      var t;\n      return clearTimeout(null != (t = this.animation) ? t._timeout : void 0);\n    }, i.prototype.render = function (t) {\n      var e, n;\n      return t._png && t._png.stopAnimation(), t._png = this, t.width = this.width, t.height = this.height, e = t.getContext(\"2d\"), this.animation ? (this.decodeFrames(e), this.animate(e)) : (n = e.createImageData(this.width, this.height), this.copyToImageData(n, this.decodePixels()), e.putImageData(n, 0, 0));\n    }, i;\n  }(), Nt.PNG = Lt;\n  /*\n     * Extracted from pdf.js\n     * https://github.com/andreasgal/pdf.js\n     *\n     * Copyright (c) 2011 Mozilla Foundation\n     *\n     * Contributors: Andreas Gal <<EMAIL>>\n     *               Chris G Jones <<EMAIL>>\n     *               Shaon Barman <<EMAIL>>\n     *               Vivien Nicolas <<EMAIL>>\n     *               Justin D'Arcangelo <<EMAIL>>\n     *               Yury Delendik\n     *\n     * \n     */\n  var Pt = function () {\n      function t() {\n        this.pos = 0, this.bufferLength = 0, this.eof = !1, this.buffer = null;\n      }\n      return t.prototype = {\n        ensureBuffer: function (t) {\n          var e = this.buffer,\n            n = e ? e.byteLength : 0;\n          if (t < n) return e;\n          for (var r = 512; r < t;) r <<= 1;\n          for (var i = new Uint8Array(r), o = 0; o < n; ++o) i[o] = e[o];\n          return this.buffer = i;\n        },\n        getByte: function () {\n          for (var t = this.pos; this.bufferLength <= t;) {\n            if (this.eof) return null;\n            this.readBlock();\n          }\n          return this.buffer[this.pos++];\n        },\n        getBytes: function (t) {\n          var e = this.pos;\n          if (t) {\n            this.ensureBuffer(e + t);\n            for (var n = e + t; !this.eof && this.bufferLength < n;) this.readBlock();\n            var r = this.bufferLength;\n            r < n && (n = r);\n          } else {\n            for (; !this.eof;) this.readBlock();\n            n = this.bufferLength;\n          }\n          return this.pos = n, this.buffer.subarray(e, n);\n        },\n        lookChar: function () {\n          for (var t = this.pos; this.bufferLength <= t;) {\n            if (this.eof) return null;\n            this.readBlock();\n          }\n          return String.fromCharCode(this.buffer[this.pos]);\n        },\n        getChar: function () {\n          for (var t = this.pos; this.bufferLength <= t;) {\n            if (this.eof) return null;\n            this.readBlock();\n          }\n          return String.fromCharCode(this.buffer[this.pos++]);\n        },\n        makeSubStream: function (t, e, n) {\n          for (var r = t + e; this.bufferLength <= r && !this.eof;) this.readBlock();\n          return new Stream(this.buffer, t, e, n);\n        },\n        skip: function (t) {\n          t || (t = 1), this.pos += t;\n        },\n        reset: function () {\n          this.pos = 0;\n        }\n      }, t;\n    }(),\n    kt = function () {\n      if (\"undefined\" != typeof Uint32Array) {\n        var k = new Uint32Array([16, 17, 18, 0, 8, 7, 9, 6, 10, 5, 11, 4, 12, 3, 13, 2, 14, 1, 15]),\n          I = new Uint32Array([3, 4, 5, 6, 7, 8, 9, 10, 65547, 65549, 65551, 65553, 131091, 131095, 131099, 131103, 196643, 196651, 196659, 196667, 262211, 262227, 262243, 262259, 327811, 327843, 327875, 327907, 258, 258, 258]),\n          C = new Uint32Array([1, 2, 3, 4, 65541, 65543, 131081, 131085, 196625, 196633, 262177, 262193, 327745, 327777, 393345, 393409, 459009, 459137, 524801, 525057, 590849, 591361, 657409, 658433, 724993, 727041, 794625, 798721, 868353, 876545]),\n          B = [new Uint32Array([459008, 524368, 524304, 524568, 459024, 524400, 524336, 590016, 459016, 524384, 524320, 589984, 524288, 524416, 524352, 590048, 459012, 524376, 524312, 589968, 459028, 524408, 524344, 590032, 459020, 524392, 524328, 59e4, 524296, 524424, 524360, 590064, 459010, 524372, 524308, 524572, 459026, 524404, 524340, 590024, 459018, 524388, 524324, 589992, 524292, 524420, 524356, 590056, 459014, 524380, 524316, 589976, 459030, 524412, 524348, 590040, 459022, 524396, 524332, 590008, 524300, 524428, 524364, 590072, 459009, 524370, 524306, 524570, 459025, 524402, 524338, 590020, 459017, 524386, 524322, 589988, 524290, 524418, 524354, 590052, 459013, 524378, 524314, 589972, 459029, 524410, 524346, 590036, 459021, 524394, 524330, 590004, 524298, 524426, 524362, 590068, 459011, 524374, 524310, 524574, 459027, 524406, 524342, 590028, 459019, 524390, 524326, 589996, 524294, 524422, 524358, 590060, 459015, 524382, 524318, 589980, 459031, 524414, 524350, 590044, 459023, 524398, 524334, 590012, 524302, 524430, 524366, 590076, 459008, 524369, 524305, 524569, 459024, 524401, 524337, 590018, 459016, 524385, 524321, 589986, 524289, 524417, 524353, 590050, 459012, 524377, 524313, 589970, 459028, 524409, 524345, 590034, 459020, 524393, 524329, 590002, 524297, 524425, 524361, 590066, 459010, 524373, 524309, 524573, 459026, 524405, 524341, 590026, 459018, 524389, 524325, 589994, 524293, 524421, 524357, 590058, 459014, 524381, 524317, 589978, 459030, 524413, 524349, 590042, 459022, 524397, 524333, 590010, 524301, 524429, 524365, 590074, 459009, 524371, 524307, 524571, 459025, 524403, 524339, 590022, 459017, 524387, 524323, 589990, 524291, 524419, 524355, 590054, 459013, 524379, 524315, 589974, 459029, 524411, 524347, 590038, 459021, 524395, 524331, 590006, 524299, 524427, 524363, 590070, 459011, 524375, 524311, 524575, 459027, 524407, 524343, 590030, 459019, 524391, 524327, 589998, 524295, 524423, 524359, 590062, 459015, 524383, 524319, 589982, 459031, 524415, 524351, 590046, 459023, 524399, 524335, 590014, 524303, 524431, 524367, 590078, 459008, 524368, 524304, 524568, 459024, 524400, 524336, 590017, 459016, 524384, 524320, 589985, 524288, 524416, 524352, 590049, 459012, 524376, 524312, 589969, 459028, 524408, 524344, 590033, 459020, 524392, 524328, 590001, 524296, 524424, 524360, 590065, 459010, 524372, 524308, 524572, 459026, 524404, 524340, 590025, 459018, 524388, 524324, 589993, 524292, 524420, 524356, 590057, 459014, 524380, 524316, 589977, 459030, 524412, 524348, 590041, 459022, 524396, 524332, 590009, 524300, 524428, 524364, 590073, 459009, 524370, 524306, 524570, 459025, 524402, 524338, 590021, 459017, 524386, 524322, 589989, 524290, 524418, 524354, 590053, 459013, 524378, 524314, 589973, 459029, 524410, 524346, 590037, 459021, 524394, 524330, 590005, 524298, 524426, 524362, 590069, 459011, 524374, 524310, 524574, 459027, 524406, 524342, 590029, 459019, 524390, 524326, 589997, 524294, 524422, 524358, 590061, 459015, 524382, 524318, 589981, 459031, 524414, 524350, 590045, 459023, 524398, 524334, 590013, 524302, 524430, 524366, 590077, 459008, 524369, 524305, 524569, 459024, 524401, 524337, 590019, 459016, 524385, 524321, 589987, 524289, 524417, 524353, 590051, 459012, 524377, 524313, 589971, 459028, 524409, 524345, 590035, 459020, 524393, 524329, 590003, 524297, 524425, 524361, 590067, 459010, 524373, 524309, 524573, 459026, 524405, 524341, 590027, 459018, 524389, 524325, 589995, 524293, 524421, 524357, 590059, 459014, 524381, 524317, 589979, 459030, 524413, 524349, 590043, 459022, 524397, 524333, 590011, 524301, 524429, 524365, 590075, 459009, 524371, 524307, 524571, 459025, 524403, 524339, 590023, 459017, 524387, 524323, 589991, 524291, 524419, 524355, 590055, 459013, 524379, 524315, 589975, 459029, 524411, 524347, 590039, 459021, 524395, 524331, 590007, 524299, 524427, 524363, 590071, 459011, 524375, 524311, 524575, 459027, 524407, 524343, 590031, 459019, 524391, 524327, 589999, 524295, 524423, 524359, 590063, 459015, 524383, 524319, 589983, 459031, 524415, 524351, 590047, 459023, 524399, 524335, 590015, 524303, 524431, 524367, 590079]), 9],\n          j = [new Uint32Array([327680, 327696, 327688, 327704, 327684, 327700, 327692, 327708, 327682, 327698, 327690, 327706, 327686, 327702, 327694, 0, 327681, 327697, 327689, 327705, 327685, 327701, 327693, 327709, 327683, 327699, 327691, 327707, 327687, 327703, 327695, 0]), 5];\n        return (t.prototype = Object.create(Pt.prototype)).getBits = function (t) {\n          for (var e, n = this.codeSize, r = this.codeBuf, i = this.bytes, o = this.bytesPos; n < t;) void 0 === (e = i[o++]) && E(\"Bad encoding in flate stream\"), r |= e << n, n += 8;\n          return e = r & (1 << t) - 1, this.codeBuf = r >> t, this.codeSize = n -= t, this.bytesPos = o, e;\n        }, t.prototype.getCode = function (t) {\n          for (var e = t[0], n = t[1], r = this.codeSize, i = this.codeBuf, o = this.bytes, a = this.bytesPos; r < n;) {\n            var s;\n            void 0 === (s = o[a++]) && E(\"Bad encoding in flate stream\"), i |= s << r, r += 8;\n          }\n          var l = e[i & (1 << n) - 1],\n            h = l >> 16,\n            u = 65535 & l;\n          return (0 == r || r < h || 0 == h) && E(\"Bad encoding in flate stream\"), this.codeBuf = i >> h, this.codeSize = r - h, this.bytesPos = a, u;\n        }, t.prototype.generateHuffmanTable = function (t) {\n          for (var e = t.length, n = 0, r = 0; r < e; ++r) t[r] > n && (n = t[r]);\n          for (var i = 1 << n, o = new Uint32Array(i), a = 1, s = 0, l = 2; a <= n; ++a, s <<= 1, l <<= 1) for (var h = 0; h < e; ++h) if (t[h] == a) {\n            var u = 0,\n              c = s;\n            for (r = 0; r < a; ++r) u = u << 1 | 1 & c, c >>= 1;\n            for (r = u; r < i; r += l) o[r] = a << 16 | h;\n            ++s;\n          }\n          return [o, n];\n        }, t.prototype.readBlock = function () {\n          function t(t, e, n, r, i) {\n            for (var o = t.getBits(n) + r; 0 < o--;) e[l++] = i;\n          }\n          var e = this.getBits(3);\n          if (1 & e && (this.eof = !0), 0 != (e >>= 1)) {\n            var n, r;\n            if (1 == e) n = B, r = j;else if (2 == e) {\n              for (var i = this.getBits(5) + 257, o = this.getBits(5) + 1, a = this.getBits(4) + 4, s = Array(k.length), l = 0; l < a;) s[k[l++]] = this.getBits(3);\n              for (var h = this.generateHuffmanTable(s), u = 0, c = (l = 0, i + o), f = new Array(c); l < c;) {\n                var p = this.getCode(h);\n                16 == p ? t(this, f, 2, 3, u) : 17 == p ? t(this, f, 3, 3, u = 0) : 18 == p ? t(this, f, 7, 11, u = 0) : f[l++] = u = p;\n              }\n              n = this.generateHuffmanTable(f.slice(0, i)), r = this.generateHuffmanTable(f.slice(i, c));\n            } else E(\"Unknown block type in flate stream\");\n            for (var d = (_ = this.buffer) ? _.length : 0, g = this.bufferLength;;) {\n              var m = this.getCode(n);\n              if (m < 256) d <= g + 1 && (d = (_ = this.ensureBuffer(g + 1)).length), _[g++] = m;else {\n                if (256 == m) return void (this.bufferLength = g);\n                var y = (m = I[m -= 257]) >> 16;\n                0 < y && (y = this.getBits(y));\n                u = (65535 & m) + y;\n                m = this.getCode(r), 0 < (y = (m = C[m]) >> 16) && (y = this.getBits(y));\n                var v = (65535 & m) + y;\n                d <= g + u && (d = (_ = this.ensureBuffer(g + u)).length);\n                for (var w = 0; w < u; ++w, ++g) _[g] = _[g - v];\n              }\n            }\n          } else {\n            var b,\n              x = this.bytes,\n              N = this.bytesPos;\n            void 0 === (b = x[N++]) && E(\"Bad block header in flate stream\");\n            var L = b;\n            void 0 === (b = x[N++]) && E(\"Bad block header in flate stream\"), L |= b << 8, void 0 === (b = x[N++]) && E(\"Bad block header in flate stream\");\n            var A = b;\n            void 0 === (b = x[N++]) && E(\"Bad block header in flate stream\"), (A |= b << 8) != (65535 & ~L) && E(\"Bad uncompressed block length in flate stream\"), this.codeBuf = 0, this.codeSize = 0;\n            var S = this.bufferLength,\n              _ = this.ensureBuffer(S + L),\n              F = S + L;\n            this.bufferLength = F;\n            for (var P = S; P < F; ++P) {\n              if (void 0 === (b = x[N++])) {\n                this.eof = !0;\n                break;\n              }\n              _[P] = b;\n            }\n            this.bytesPos = N;\n          }\n        }, t;\n      }\n      function E(t) {\n        throw new Error(t);\n      }\n      function t(t) {\n        var e = 0,\n          n = t[e++],\n          r = t[e++];\n        -1 != n && -1 != r || E(\"Invalid header in flate stream\"), 8 != (15 & n) && E(\"Unknown compression method in flate stream\"), ((n << 8) + r) % 31 != 0 && E(\"Bad FCHECK in flate stream\"), 32 & r && E(\"FDICT bit set in flate stream\"), this.bytes = t, this.bytesPos = 2, this.codeSize = 0, this.codeBuf = 0, Pt.call(this);\n      }\n    }();\n  window.tmp = kt;\n});\ntry {\n  module.exports = jsPDF;\n} catch (t) {}", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}