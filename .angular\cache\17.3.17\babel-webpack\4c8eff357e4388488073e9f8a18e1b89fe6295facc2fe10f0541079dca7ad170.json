{"ast": null, "code": "import { getBsVer } from 'ngx-bootstrap/utils';\nimport * as i0 from '@angular/core';\nimport { PLATFORM_ID, Injectable, Inject, ElementRef } from '@angular/core';\nimport { isPlatformBrowser } from '@angular/common';\nimport { Subject, fromEvent, animationFrameScheduler, of, merge } from 'rxjs';\nvar MapPlacementInToRL = /*#__PURE__*/function (MapPlacementInToRL) {\n  MapPlacementInToRL[\"top\"] = \"top\";\n  MapPlacementInToRL[\"bottom\"] = \"bottom\";\n  MapPlacementInToRL[\"left\"] = \"left\";\n  MapPlacementInToRL[\"right\"] = \"right\";\n  MapPlacementInToRL[\"auto\"] = \"auto\";\n  MapPlacementInToRL[\"end\"] = \"right\";\n  MapPlacementInToRL[\"start\"] = \"left\";\n  MapPlacementInToRL[\"top left\"] = \"top left\";\n  MapPlacementInToRL[\"top right\"] = \"top right\";\n  MapPlacementInToRL[\"right top\"] = \"right top\";\n  MapPlacementInToRL[\"right bottom\"] = \"right bottom\";\n  MapPlacementInToRL[\"bottom right\"] = \"bottom right\";\n  MapPlacementInToRL[\"bottom left\"] = \"bottom left\";\n  MapPlacementInToRL[\"left bottom\"] = \"left bottom\";\n  MapPlacementInToRL[\"left top\"] = \"left top\";\n  MapPlacementInToRL[\"top start\"] = \"top left\";\n  MapPlacementInToRL[\"top end\"] = \"top right\";\n  MapPlacementInToRL[\"end top\"] = \"right top\";\n  MapPlacementInToRL[\"end bottom\"] = \"right bottom\";\n  MapPlacementInToRL[\"bottom end\"] = \"bottom right\";\n  MapPlacementInToRL[\"bottom start\"] = \"bottom left\";\n  MapPlacementInToRL[\"start bottom\"] = \"start bottom\";\n  MapPlacementInToRL[\"start top\"] = \"left top\";\n  return MapPlacementInToRL;\n}(MapPlacementInToRL || {});\nvar PlacementForBs5 = /*#__PURE__*/function (PlacementForBs5) {\n  PlacementForBs5[\"top\"] = \"top\";\n  PlacementForBs5[\"bottom\"] = \"bottom\";\n  PlacementForBs5[\"left\"] = \"start\";\n  PlacementForBs5[\"right\"] = \"end\";\n  PlacementForBs5[\"auto\"] = \"auto\";\n  PlacementForBs5[\"end\"] = \"end\";\n  PlacementForBs5[\"start\"] = \"start\";\n  PlacementForBs5[\"top left\"] = \"top start\";\n  PlacementForBs5[\"top right\"] = \"top end\";\n  PlacementForBs5[\"right top\"] = \"end top\";\n  PlacementForBs5[\"right bottom\"] = \"end bottom\";\n  PlacementForBs5[\"bottom right\"] = \"bottom end\";\n  PlacementForBs5[\"bottom left\"] = \"bottom start\";\n  PlacementForBs5[\"left bottom\"] = \"start bottom\";\n  PlacementForBs5[\"left top\"] = \"start top\";\n  PlacementForBs5[\"top start\"] = \"top start\";\n  PlacementForBs5[\"top end\"] = \"top end\";\n  PlacementForBs5[\"end top\"] = \"end top\";\n  PlacementForBs5[\"end bottom\"] = \"end bottom\";\n  PlacementForBs5[\"bottom end\"] = \"bottom end\";\n  PlacementForBs5[\"bottom start\"] = \"bottom start\";\n  PlacementForBs5[\"start bottom\"] = \"start bottom\";\n  PlacementForBs5[\"start top\"] = \"start top\";\n  return PlacementForBs5;\n}(PlacementForBs5 || {});\nfunction getStyleComputedProperty(element, property) {\n  if (element.nodeType !== 1) {\n    return [];\n  }\n  // NOTE: 1 DOM access here\n  const window = element.ownerDocument.defaultView;\n  const css = window?.getComputedStyle(element, null);\n  // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n  // @ts-ignore\n  return property ? css && css[property] : css;\n}\n\n/**\n * Returns the offset parent of the given element\n */\nfunction getOffsetParent(element) {\n  if (!element) {\n    return document.documentElement;\n  }\n  const noOffsetParent = null;\n  // NOTE: 1 DOM access here\n  let offsetParent = element?.offsetParent;\n  // Skip hidden elements which don't have an offsetParent\n  let sibling = void 0;\n  while (offsetParent === noOffsetParent && element.nextElementSibling && sibling !== element.nextElementSibling) {\n    // todo: valorkin fix\n    sibling = element.nextElementSibling;\n    offsetParent = sibling.offsetParent;\n  }\n  const nodeName = offsetParent && offsetParent.nodeName;\n  if (!nodeName || nodeName === 'BODY' || nodeName === 'HTML') {\n    return sibling ? sibling.ownerDocument.documentElement : document.documentElement;\n  }\n  // .offsetParent will return the closest TH, TD or TABLE in case\n  if (offsetParent && ['TH', 'TD', 'TABLE'].indexOf(offsetParent.nodeName) !== -1 && getStyleComputedProperty(offsetParent, 'position') === 'static') {\n    return getOffsetParent(offsetParent);\n  }\n  return offsetParent;\n}\n\n// todo: valorkin fix\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nfunction isOffsetContainer(element) {\n  const {\n    nodeName\n  } = element;\n  if (nodeName === 'BODY') {\n    return false;\n  }\n  return nodeName === 'HTML' || getOffsetParent(element.firstElementChild) === element;\n}\n\n/**\n * Finds the root node (document, shadowDOM root) of the given element\n */\nfunction getRoot(node) {\n  if (node.parentNode !== null) {\n    return getRoot(node.parentNode);\n  }\n  return node;\n}\n\n/**\n * Finds the offset parent common to the two provided nodes\n */\nfunction findCommonOffsetParent(element1, element2) {\n  // This check is needed to avoid errors in case one of the elements isn't defined for any reason\n  if (!element1 || !element1.nodeType || !element2 || !element2.nodeType) {\n    return document.documentElement;\n  }\n  // Here we make sure to give as \"start\" the element that comes first in the DOM\n  const order = element1.compareDocumentPosition(element2) & Node.DOCUMENT_POSITION_FOLLOWING;\n  const start = order ? element1 : element2;\n  const end = order ? element2 : element1;\n  // Get common ancestor container\n  const range = document.createRange();\n  range.setStart(start, 0);\n  range.setEnd(end, 0);\n  // todo: valorkin fix\n  const commonAncestorContainer = range.commonAncestorContainer;\n  // Both nodes are inside #document\n  if (element1 !== commonAncestorContainer && element2 !== commonAncestorContainer || start.contains(end)) {\n    if (isOffsetContainer(commonAncestorContainer)) {\n      return commonAncestorContainer;\n    }\n    return getOffsetParent(commonAncestorContainer);\n  }\n  // one of the nodes is inside shadowDOM, find which one\n  const element1root = getRoot(element1);\n  if (element1root.host) {\n    return findCommonOffsetParent(element1root.host, element2);\n  } else {\n    return findCommonOffsetParent(element1, getRoot(element2).host);\n  }\n}\n\n/**\n * Finds the first parent of an element that has a transformed property defined\n */\nfunction getFixedPositionOffsetParent(element) {\n  // This check is needed to avoid errors in case one of the elements isn't defined for any reason\n  if (!element || !element.parentElement) {\n    return document.documentElement;\n  }\n  let el = element.parentElement;\n  while (el?.parentElement && getStyleComputedProperty(el, 'transform') === 'none') {\n    el = el.parentElement;\n  }\n  return el || document.documentElement;\n}\n\n/**\n * Helper to detect borders of a given element\n */\nfunction getBordersSize(styles, axis) {\n  const sideA = axis === 'x' ? 'Left' : 'Top';\n  const sideB = sideA === 'Left' ? 'Right' : 'Bottom';\n  return parseFloat(styles[`border${sideA}Width`]) + parseFloat(styles[`border${sideB}Width`]);\n}\nfunction getSize(axis, body, html) {\n  const _body = body;\n  const _html = html;\n  return Math.max(_body[`offset${axis}`], _body[`scroll${axis}`], _html[`client${axis}`], _html[`offset${axis}`], _html[`scroll${axis}`], 0);\n}\nfunction getWindowSizes(document) {\n  const body = document.body;\n  const html = document.documentElement;\n  return {\n    height: getSize('Height', body, html),\n    width: getSize('Width', body, html)\n  };\n}\nfunction getClientRect(offsets) {\n  return {\n    ...offsets,\n    right: (offsets.left || 0) + offsets.width,\n    bottom: (offsets.top || 0) + offsets.height\n  };\n}\n\n/**\n * Tells if a given input is a number\n */\nfunction isNumeric(n) {\n  return n !== '' && !isNaN(parseFloat(n)) && isFinite(Number(n));\n}\nfunction isNumber(value) {\n  return typeof value === 'number' || Object.prototype.toString.call(value) === '[object Number]';\n}\n\n/**\n * Get bounding client rect of given element\n */\nfunction getBoundingClientRect(element) {\n  const rect = element.getBoundingClientRect();\n  // IE10 10 FIX: Please, don't ask, the element isn't\n  // considered in DOM in some circumstances...\n  // This isn't reproducible in IE10 compatibility mode of IE11\n  // try {\n  //   if (isIE(10)) {\n  //     const scrollTop = getScroll(element, 'top');\n  //     const scrollLeft = getScroll(element, 'left');\n  //     if (rect && isNumber(rect.top) && isNumber(rect.left) && isNumber(rect.bottom) && isNumber(rect.right)) {\n  //       rect.top += scrollTop;\n  //       rect.left += scrollLeft;\n  //       rect.bottom += scrollTop;\n  //       rect.right += scrollLeft;\n  //     }\n  //   }\n  // } catch (e) {\n  //   return rect;\n  // }\n  if (!(rect && isNumber(rect.top) && isNumber(rect.left) && isNumber(rect.bottom) && isNumber(rect.right))) {\n    return rect;\n  }\n  const result = {\n    left: rect.left,\n    top: rect.top,\n    width: rect.right - rect.left,\n    height: rect.bottom - rect.top\n  };\n  // subtract scrollbar size from sizes\n  const sizes = element.nodeName === 'HTML' ? getWindowSizes(element.ownerDocument) : undefined;\n  const width = sizes?.width || element.clientWidth || isNumber(rect.right) && isNumber(result.left) && rect.right - result.left || 0;\n  const height = sizes?.height || element.clientHeight || isNumber(rect.bottom) && isNumber(result.top) && rect.bottom - result.top || 0;\n  let horizScrollbar = element.offsetWidth - width;\n  let vertScrollbar = element.offsetHeight - height;\n  // if an hypothetical scrollbar is detected, we must be sure it's not a `border`\n  // we make this check conditional for performance reasons\n  if (horizScrollbar || vertScrollbar) {\n    const styles = getStyleComputedProperty(element);\n    horizScrollbar -= getBordersSize(styles, 'x');\n    vertScrollbar -= getBordersSize(styles, 'y');\n    result.width -= horizScrollbar;\n    result.height -= vertScrollbar;\n  }\n  return getClientRect(result);\n}\nfunction getOffsetRectRelativeToArbitraryNode(children, parent, fixedPosition = false) {\n  const isHTML = parent.nodeName === 'HTML';\n  const childrenRect = getBoundingClientRect(children);\n  const parentRect = getBoundingClientRect(parent);\n  const styles = getStyleComputedProperty(parent);\n  const borderTopWidth = parseFloat(styles.borderTopWidth);\n  const borderLeftWidth = parseFloat(styles.borderLeftWidth);\n  // In cases where the parent is fixed, we must ignore negative scroll in offset calc\n  if (fixedPosition && isHTML) {\n    parentRect.top = Math.max(parentRect.top ?? 0, 0);\n    parentRect.left = Math.max(parentRect.left ?? 0, 0);\n  }\n  const offsets = getClientRect({\n    top: (childrenRect.top ?? 0) - (parentRect.top ?? 0) - borderTopWidth,\n    left: (childrenRect.left ?? 0) - (parentRect.left ?? 0) - borderLeftWidth,\n    width: childrenRect.width,\n    height: childrenRect.height\n  });\n  offsets.marginTop = 0;\n  offsets.marginLeft = 0;\n  // Subtract margins of documentElement in case it's being used as parent\n  // we do this only on HTML because it's the only element that behaves\n  // differently when margins are applied to it. The margins are included in\n  // the box of the documentElement, in the other cases not.\n  if (isHTML) {\n    const marginTop = parseFloat(styles.marginTop);\n    const marginLeft = parseFloat(styles.marginLeft);\n    if (isNumber(offsets.top)) {\n      offsets.top -= borderTopWidth - marginTop;\n    }\n    if (isNumber(offsets.bottom)) {\n      offsets.bottom -= borderTopWidth - marginTop;\n    }\n    if (isNumber(offsets.left)) {\n      offsets.left -= borderLeftWidth - marginLeft;\n    }\n    if (isNumber(offsets.right)) {\n      offsets.right -= borderLeftWidth - marginLeft;\n    }\n    // Attach marginTop and marginLeft because in some circumstances we may need them\n    offsets.marginTop = marginTop;\n    offsets.marginLeft = marginLeft;\n  }\n  return offsets;\n}\n\n/**\n * Returns the parentNode or the host of the element\n */\n// todo: valorkin fix\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nfunction getParentNode(element) {\n  if (element.nodeName === 'HTML') {\n    return element;\n  }\n  return element.parentNode || element.host;\n}\n\n/**\n * Returns the scrolling parent of the given element\n */\n// todo: valorkin fix\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nfunction getScrollParent(element) {\n  // Return body, `getScroll` will take care to get the correct `scrollTop` from it\n  if (!element) {\n    return document.body;\n  }\n  switch (element.nodeName) {\n    case 'HTML':\n    case 'BODY':\n      return element.ownerDocument.body;\n    case '#document':\n      return element.body;\n    default:\n  }\n  // Firefox want us to check `-x` and `-y` variations as well\n  const {\n    overflow,\n    overflowX,\n    overflowY\n  } = getStyleComputedProperty(element);\n  if (/(auto|scroll|overlay)/.test(String(overflow) + String(overflowY) + String(overflowX))) {\n    return element;\n  }\n  return getScrollParent(getParentNode(element));\n}\n\n/**\n * Gets the scroll value of the given element in the given side (top and left)\n */\nfunction getScroll(element, side = 'top') {\n  const upperSide = side === 'top' ? 'scrollTop' : 'scrollLeft';\n  const nodeName = element.nodeName;\n  if (nodeName === 'BODY' || nodeName === 'HTML') {\n    const html = element.ownerDocument.documentElement;\n    const scrollingElement = element.ownerDocument.scrollingElement || html;\n    return scrollingElement[upperSide];\n  }\n  return element[upperSide];\n}\nfunction getViewportOffsetRectRelativeToArtbitraryNode(element, excludeScroll = false) {\n  const html = element.ownerDocument.documentElement;\n  const relativeOffset = getOffsetRectRelativeToArbitraryNode(element, html);\n  const width = Math.max(html.clientWidth, window.innerWidth || 0);\n  const height = Math.max(html.clientHeight, window.innerHeight || 0);\n  const scrollTop = !excludeScroll ? getScroll(html) : 0;\n  const scrollLeft = !excludeScroll ? getScroll(html, 'left') : 0;\n  const offset = {\n    top: scrollTop - Number(relativeOffset?.top) + Number(relativeOffset?.marginTop),\n    left: scrollLeft - Number(relativeOffset?.left) + Number(relativeOffset?.marginLeft),\n    width,\n    height\n  };\n  return getClientRect(offset);\n}\n\n/**\n * Check if the given element is fixed or is inside a fixed parent\n */\nfunction isFixed(element) {\n  const nodeName = element.nodeName;\n  if (nodeName === 'BODY' || nodeName === 'HTML') {\n    return false;\n  }\n  if (getStyleComputedProperty(element, 'position') === 'fixed') {\n    return true;\n  }\n  return isFixed(getParentNode(element));\n}\nfunction getBoundaries(target, host, padding = 0, boundariesElement, fixedPosition = false) {\n  // NOTE: 1 DOM access here\n  let boundaries = {\n    top: 0,\n    left: 0\n  };\n  const offsetParent = fixedPosition ? getFixedPositionOffsetParent(target) : findCommonOffsetParent(target, host);\n  // Handle viewport case\n  if (boundariesElement === 'viewport') {\n    boundaries = getViewportOffsetRectRelativeToArtbitraryNode(offsetParent, fixedPosition);\n  } else {\n    // Handle other cases based on DOM element used as boundaries\n    let boundariesNode;\n    if (boundariesElement === 'scrollParent') {\n      boundariesNode = getScrollParent(getParentNode(host));\n      if (boundariesNode.nodeName === 'BODY') {\n        boundariesNode = target.ownerDocument.documentElement;\n      }\n    } else if (boundariesElement === 'window') {\n      boundariesNode = target.ownerDocument.documentElement;\n    } else {\n      boundariesNode = boundariesElement;\n    }\n    const offsets = getOffsetRectRelativeToArbitraryNode(boundariesNode, offsetParent, fixedPosition);\n    // In case of HTML, we need a different computation\n    if (offsets && boundariesNode.nodeName === 'HTML' && !isFixed(offsetParent)) {\n      const {\n        height,\n        width\n      } = getWindowSizes(target.ownerDocument);\n      if (isNumber(boundaries.top) && isNumber(offsets.top) && isNumber(offsets.marginTop)) {\n        boundaries.top += offsets.top - offsets.marginTop;\n      }\n      if (isNumber(boundaries.top)) {\n        boundaries.bottom = Number(height) + Number(offsets.top);\n      }\n      if (isNumber(boundaries.left) && isNumber(offsets.left) && isNumber(offsets.marginLeft)) {\n        boundaries.left += offsets.left - offsets.marginLeft;\n      }\n      if (isNumber(boundaries.top)) {\n        boundaries.right = Number(width) + Number(offsets.left);\n      }\n    } else if (offsets) {\n      // for all the other DOM elements, this one is good\n      boundaries = offsets;\n    }\n  }\n  // Add paddings\n  if (isNumber(boundaries.left)) {\n    boundaries.left += padding;\n  }\n  if (isNumber(boundaries.top)) {\n    boundaries.top += padding;\n  }\n  if (isNumber(boundaries.right)) {\n    boundaries.right -= padding;\n  }\n  if (isNumber(boundaries.bottom)) {\n    boundaries.bottom -= padding;\n  }\n  return boundaries;\n}\n\n/**\n * Utility used to transform the `auto` placement to the placement with more\n * available space.\n */\nfunction getArea({\n  width,\n  height\n}) {\n  return width * height;\n}\nfunction computeAutoPlacement(placement, refRect, target, host, allowedPositions = ['top', 'bottom', 'right', 'left'], boundariesElement = 'viewport', padding = 0) {\n  if (placement.indexOf('auto') === -1) {\n    return placement;\n  }\n  const boundaries = getBoundaries(target, host, padding, boundariesElement);\n  const rects = {\n    top: {\n      width: boundaries?.width ?? 0,\n      height: (refRect?.top ?? 0) - (boundaries?.top ?? 0)\n    },\n    right: {\n      width: (boundaries?.right ?? 0) - (refRect?.right ?? 0),\n      height: boundaries?.height ?? 0\n    },\n    bottom: {\n      width: boundaries?.width ?? 0,\n      height: (boundaries?.bottom ?? 0) - (refRect?.bottom ?? 0)\n    },\n    left: {\n      width: (refRect.left ?? 0) - (boundaries?.left ?? 0),\n      height: boundaries?.height ?? 0\n    }\n  };\n  const sortedAreas = Object.keys(rects).map(key => ({\n    position: key,\n    ...rects[key],\n    area: getArea(rects[key])\n  })).sort((a, b) => b.area - a.area);\n  let filteredAreas = sortedAreas.filter(({\n    width,\n    height\n  }) => {\n    return width >= target.clientWidth && height >= target.clientHeight;\n  });\n  filteredAreas = filteredAreas.filter(({\n    position\n  }) => {\n    return allowedPositions.some(allowedPosition => {\n      return allowedPosition === position;\n    });\n  });\n  const computedPlacement = filteredAreas.length > 0 ? filteredAreas[0].position : sortedAreas[0].position;\n  const variation = placement.split(' ')[1];\n  // for tooltip on auto position\n  target.className = target.className.replace(/bs-tooltip-auto/g, `bs-tooltip-${getBsVer().isBs5 ? PlacementForBs5[computedPlacement] : computedPlacement}`);\n  return computedPlacement + (variation ? `-${variation}` : '');\n}\nfunction getOffsets(data) {\n  return {\n    width: data.offsets.target.width,\n    height: data.offsets.target.height,\n    left: Math.floor(data.offsets.target.left ?? 0),\n    top: Math.round(data.offsets.target.top ?? 0),\n    bottom: Math.round(data.offsets.target.bottom ?? 0),\n    right: Math.floor(data.offsets.target.right ?? 0)\n  };\n}\n\n/**\n * Get the opposite placement of the given one\n */\nfunction getOppositePlacement(placement) {\n  const hash = {\n    left: 'right',\n    right: 'left',\n    bottom: 'top',\n    top: 'bottom'\n  };\n  return placement.replace(/left|right|bottom|top/g, matched => hash[matched]);\n}\n\n/**\n * Get the opposite placement variation of the given one\n */\nfunction getOppositeVariation(variation) {\n  if (variation === 'right') {\n    return 'left';\n  } else if (variation === 'left') {\n    return 'right';\n  }\n  return variation;\n}\nconst parse = (value, def = 0) => value ? parseFloat(value) : def;\nfunction getOuterSizes(element) {\n  const window = element.ownerDocument.defaultView;\n  const styles = window?.getComputedStyle(element);\n  const x = parse(styles?.marginTop) + parse(styles?.marginBottom);\n  const y = parse(styles?.marginLeft) + parse(styles?.marginRight);\n  return {\n    width: Number(element.offsetWidth) + y,\n    height: Number(element.offsetHeight) + x\n  };\n}\n\n/**\n * Get offsets to the reference element\n */\nfunction getReferenceOffsets(target, host, fixedPosition) {\n  const commonOffsetParent = fixedPosition ? getFixedPositionOffsetParent(target) : findCommonOffsetParent(target, host);\n  return getOffsetRectRelativeToArbitraryNode(host, commonOffsetParent, fixedPosition);\n}\n\n/**\n * Get offsets to the target\n */\nfunction getTargetOffsets(target, hostOffsets, position) {\n  const placement = position.split(' ')[0];\n  // Get target node sizes\n  const targetRect = getOuterSizes(target);\n  // Add position, width and height to our offsets object\n  const targetOffsets = {\n    width: targetRect.width,\n    height: targetRect.height\n  };\n  // depending by the target placement we have to compute its offsets slightly differently\n  const isHoriz = ['right', 'left'].indexOf(placement) !== -1;\n  const mainSide = isHoriz ? 'top' : 'left';\n  const secondarySide = isHoriz ? 'left' : 'top';\n  const measurement = isHoriz ? 'height' : 'width';\n  const secondaryMeasurement = !isHoriz ? 'height' : 'width';\n  targetOffsets[mainSide] = (hostOffsets[mainSide] ?? 0) + hostOffsets[measurement] / 2 - targetRect[measurement] / 2;\n  targetOffsets[secondarySide] = placement === secondarySide ? (hostOffsets[secondarySide] ?? 0) - targetRect[secondaryMeasurement] : hostOffsets[getOppositePlacement(secondarySide)] ?? 0;\n  return targetOffsets;\n}\nfunction isModifierEnabled(options, modifierName) {\n  return !!options.modifiers[modifierName]?.enabled;\n}\nconst availablePositions = {\n  top: ['top', 'top start', 'top end'],\n  bottom: ['bottom', 'bottom start', 'bottom end'],\n  start: ['start', 'start top', 'start bottom'],\n  end: ['end', 'end top', 'end bottom']\n};\nfunction checkPopoverMargin(placement, checkPosition) {\n  if (!getBsVer().isBs5) {\n    return false;\n  }\n  return availablePositions[checkPosition].includes(placement);\n}\nfunction checkMargins(placement) {\n  if (!getBsVer().isBs5) {\n    return '';\n  }\n  if (checkPopoverMargin(placement, 'end')) {\n    return 'ms-2';\n  }\n  if (checkPopoverMargin(placement, 'start')) {\n    return 'me-2';\n  }\n  if (checkPopoverMargin(placement, 'top')) {\n    return 'mb-2';\n  }\n  if (checkPopoverMargin(placement, 'bottom')) {\n    return 'mt-2';\n  }\n  return '';\n}\nfunction updateContainerClass(data, renderer) {\n  const target = data.instance.target;\n  let containerClass = target.className;\n  const dataPlacement = getBsVer().isBs5 ? PlacementForBs5[data.placement] : data.placement;\n  if (data.placementAuto) {\n    containerClass = containerClass.replace(/bs-popover-auto/g, `bs-popover-${dataPlacement}`);\n    containerClass = containerClass.replace(/ms-2|me-2|mb-2|mt-2/g, '');\n    containerClass = containerClass.replace(/bs-tooltip-auto/g, `bs-tooltip-${dataPlacement}`);\n    containerClass = containerClass.replace(/\\sauto/g, ` ${dataPlacement}`);\n    if (containerClass.indexOf('popover') !== -1) {\n      containerClass = containerClass + ' ' + checkMargins(dataPlacement);\n    }\n    if (containerClass.indexOf('popover') !== -1 && containerClass.indexOf('popover-auto') === -1) {\n      containerClass += ' popover-auto';\n    }\n    if (containerClass.indexOf('tooltip') !== -1 && containerClass.indexOf('tooltip-auto') === -1) {\n      containerClass += ' tooltip-auto';\n    }\n  }\n  containerClass = containerClass.replace(/left|right|top|bottom|end|start/g, `${dataPlacement.split(' ')[0]}`);\n  if (renderer) {\n    renderer.setAttribute(target, 'class', containerClass);\n    return;\n  }\n  target.className = containerClass;\n}\nfunction setStyles(element, styles, renderer) {\n  if (!element || !styles) {\n    return;\n  }\n  Object.keys(styles).forEach(prop => {\n    let unit = '';\n    // add unit if the value is numeric and is one of the following\n    if (['width', 'height', 'top', 'right', 'bottom', 'left'].indexOf(prop) !== -1 && isNumeric(styles[prop])) {\n      unit = 'px';\n    }\n    if (renderer) {\n      renderer.setStyle(element, prop, `${String(styles[prop])}${unit}`);\n      return;\n    }\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    element.style[prop] = String(styles[prop]) + unit;\n  });\n}\nfunction arrow(data) {\n  let targetOffsets = data.offsets.target;\n  // if arrowElement is a string, suppose it's a CSS selector\n  const arrowElement = data.instance.target.querySelector('.arrow');\n  // if arrowElement is not found, don't run the modifier\n  if (!arrowElement) {\n    return data;\n  }\n  const isVertical = ['left', 'right'].indexOf(data.placement.split(' ')[0]) !== -1;\n  const len = isVertical ? 'height' : 'width';\n  const sideCapitalized = isVertical ? 'Top' : 'Left';\n  const side = sideCapitalized.toLowerCase();\n  const altSide = isVertical ? 'left' : 'top';\n  const opSide = isVertical ? 'bottom' : 'right';\n  const arrowElementSize = getOuterSizes(arrowElement)[len];\n  const placementVariation = data.placement.split(' ')[1];\n  // top/left side\n  if ((data.offsets.host[opSide] ?? 0) - arrowElementSize < (targetOffsets[side] ?? 0)) {\n    targetOffsets[side] -= (targetOffsets[side] ?? 0) - ((data.offsets.host[opSide] ?? 0) - arrowElementSize);\n  }\n  // bottom/right side\n  if (Number(data.offsets.host[side]) + Number(arrowElementSize) > (targetOffsets[opSide] ?? 0)) {\n    targetOffsets[side] += Number(data.offsets.host[side]) + Number(arrowElementSize) - Number(targetOffsets[opSide]);\n  }\n  targetOffsets = getClientRect(targetOffsets);\n  // Compute the sideValue using the updated target offsets\n  // take target margin in account because we don't have this info available\n  const css = getStyleComputedProperty(data.instance.target);\n  const targetMarginSide = parseFloat(css[`margin${sideCapitalized}`]) || 0;\n  const targetBorderSide = parseFloat(css[`border${sideCapitalized}Width`]) || 0;\n  // compute center of the target\n  let center;\n  if (!placementVariation) {\n    center = Number(data.offsets.host[side]) + Number(data.offsets.host[len] / 2 - arrowElementSize / 2);\n  } else {\n    const targetBorderRadius = parseFloat(css[\"borderRadius\"]) || 0;\n    const targetSideArrowOffset = Number(targetMarginSide + targetBorderSide + targetBorderRadius);\n    center = side === placementVariation ? Number(data.offsets.host[side]) + targetSideArrowOffset : Number(data.offsets.host[side]) + Number(data.offsets.host[len] - targetSideArrowOffset);\n  }\n  let sideValue = center - (targetOffsets[side] ?? 0) - targetMarginSide - targetBorderSide;\n  // prevent arrowElement from being placed not contiguously to its target\n  sideValue = Math.max(Math.min(targetOffsets[len] - (arrowElementSize + 5), sideValue), 0);\n  data.offsets.arrow = {\n    [side]: Math.round(sideValue),\n    [altSide]: '' // make sure to unset any eventual altSide value from the DOM node\n  };\n  data.instance.arrow = arrowElement;\n  return data;\n}\nfunction flip(data) {\n  data.offsets.target = getClientRect(data.offsets.target);\n  if (!isModifierEnabled(data.options, 'flip')) {\n    data.offsets.target = {\n      ...data.offsets.target,\n      ...getTargetOffsets(data.instance.target, data.offsets.host, data.placement)\n    };\n    return data;\n  }\n  const boundaries = getBoundaries(data.instance.target, data.instance.host, 0,\n  // padding\n  'viewport', false // positionFixed\n  );\n  let placement = data.placement.split(' ')[0];\n  let variation = data.placement.split(' ')[1] || '';\n  const offsetsHost = data.offsets.host;\n  const target = data.instance.target;\n  const host = data.instance.host;\n  const adaptivePosition = computeAutoPlacement('auto', offsetsHost, target, host, data.options.allowedPositions);\n  const flipOrder = [placement, adaptivePosition];\n  flipOrder.forEach((step, index) => {\n    if (placement !== step || flipOrder.length === index + 1) {\n      return;\n    }\n    placement = data.placement.split(' ')[0];\n    // using floor because the host offsets may contain decimals we are not going to consider here\n    const overlapsRef = placement === 'left' && Math.floor(data.offsets.target.right ?? 0) > Math.floor(data.offsets.host.left ?? 0) || placement === 'right' && Math.floor(data.offsets.target.left ?? 0) < Math.floor(data.offsets.host.right ?? 0) || placement === 'top' && Math.floor(data.offsets.target.bottom ?? 0) > Math.floor(data.offsets.host.top ?? 0) || placement === 'bottom' && Math.floor(data.offsets.target.top ?? 0) < Math.floor(data.offsets.host.bottom ?? 0);\n    const overflowsLeft = Math.floor(data.offsets.target.left ?? 0) < Math.floor(boundaries.left ?? 0);\n    const overflowsRight = Math.floor(data.offsets.target.right ?? 0) > Math.floor(boundaries.right ?? 0);\n    const overflowsTop = Math.floor(data.offsets.target.top ?? 0) < Math.floor(boundaries.top ?? 0);\n    const overflowsBottom = Math.floor(data.offsets.target.bottom ?? 0) > Math.floor(boundaries.bottom ?? 0);\n    const overflowsBoundaries = placement === 'left' && overflowsLeft || placement === 'right' && overflowsRight || placement === 'top' && overflowsTop || placement === 'bottom' && overflowsBottom;\n    // flip the variation if required\n    const isVertical = ['top', 'bottom'].indexOf(placement) !== -1;\n    const flippedVariation = isVertical && variation === 'left' && overflowsLeft || isVertical && variation === 'right' && overflowsRight || !isVertical && variation === 'left' && overflowsTop || !isVertical && variation === 'right' && overflowsBottom;\n    if (overlapsRef || overflowsBoundaries || flippedVariation) {\n      if (overlapsRef || overflowsBoundaries) {\n        placement = flipOrder[index + 1];\n      }\n      if (flippedVariation) {\n        variation = getOppositeVariation(variation);\n      }\n      data.placement = placement + (variation ? ` ${variation}` : '');\n      data.offsets.target = {\n        ...data.offsets.target,\n        ...getTargetOffsets(data.instance.target, data.offsets.host, data.placement)\n      };\n    }\n  });\n  return data;\n}\nfunction initData(targetElement, hostElement, position, options) {\n  if (!targetElement || !hostElement) {\n    return;\n  }\n  const hostElPosition = getReferenceOffsets(targetElement, hostElement);\n  if (!position.match(/^(auto)*\\s*(left|right|top|bottom|start|end)*$/) && !position.match(/^(left|right|top|bottom|start|end)*(?: (left|right|top|bottom|start|end))*$/)) {\n    position = 'auto';\n  }\n  const placementAuto = !!position.match(/auto/g);\n  // support old placements 'auto left|right|top|bottom'\n  let placement = position.match(/auto\\s(left|right|top|bottom|start|end)/) ? position.split(' ')[1] || 'auto' : position;\n  // Normalize placements that have identical main placement and variation (\"right right\" => \"right\").\n  const matches = placement.match(/^(left|right|top|bottom|start|end)* ?(?!\\1)(left|right|top|bottom|start|end)?/);\n  if (matches) {\n    placement = matches[1] + (matches[2] ? ` ${matches[2]}` : '');\n  }\n  // \"left right\", \"top bottom\" etc. placements also considered incorrect.\n  if (['left right', 'right left', 'top bottom', 'bottom top'].indexOf(placement) !== -1) {\n    placement = 'auto';\n  }\n  placement = computeAutoPlacement(placement, hostElPosition, targetElement, hostElement, options ? options.allowedPositions : undefined);\n  const targetOffset = getTargetOffsets(targetElement, hostElPosition, placement);\n  return {\n    options: options || {\n      modifiers: {}\n    },\n    instance: {\n      target: targetElement,\n      host: hostElement,\n      arrow: void 0\n    },\n    offsets: {\n      target: targetOffset,\n      host: hostElPosition,\n      arrow: void 0\n    },\n    positionFixed: false,\n    placement,\n    placementAuto\n  };\n}\nfunction preventOverflow(data) {\n  if (!isModifierEnabled(data.options, 'preventOverflow')) {\n    return data;\n  }\n  // NOTE: DOM access here\n  // resets the target Offsets's position so that the document size can be calculated excluding\n  // the size of the targetOffsets element itself\n  const transformProp = 'transform';\n  const targetStyles = data.instance.target.style; // assignment to help minification\n  const {\n    top,\n    left,\n    [transformProp]: transform\n  } = targetStyles;\n  targetStyles.top = '';\n  targetStyles.left = '';\n  targetStyles[transformProp] = '';\n  const boundaries = getBoundaries(data.instance.target, data.instance.host, 0,\n  // padding\n  data.options.modifiers.preventOverflow?.boundariesElement || 'scrollParent', false // positionFixed\n  );\n  // NOTE: DOM access here\n  // restores the original style properties after the offsets have been computed\n  targetStyles.top = top;\n  targetStyles.left = left;\n  targetStyles[transformProp] = transform;\n  const order = ['left', 'right', 'top', 'bottom'];\n  const check = {\n    primary(placement) {\n      let value = data.offsets.target[placement];\n      // options.escapeWithReference\n      if ((data.offsets.target[placement] ?? 0) < (boundaries[placement] ?? 0)) {\n        value = Math.max(data.offsets.target[placement] ?? 0, boundaries[placement] ?? 0);\n      }\n      return {\n        [placement]: value\n      };\n    },\n    secondary(placement) {\n      const isPlacementHorizontal = placement === 'right';\n      const mainSide = isPlacementHorizontal ? 'left' : 'top';\n      const measurement = isPlacementHorizontal ? 'width' : 'height';\n      let value = data.offsets.target[mainSide];\n      // escapeWithReference\n      if ((data.offsets.target[placement] ?? 0) > (boundaries[placement] ?? 0)) {\n        value = Math.min(data.offsets.target[mainSide] ?? 0, (boundaries[placement] ?? 0) - data.offsets.target[measurement]);\n      }\n      return {\n        [mainSide]: value\n      };\n    }\n  };\n  order.forEach(placement => {\n    const side = ['left', 'top', 'start'].indexOf(placement) !== -1 ? check['primary'] : check['secondary'];\n    data.offsets.target = {\n      ...data.offsets.target,\n      ...side(placement)\n    };\n  });\n  return data;\n}\nfunction shift(data) {\n  const placement = data.placement;\n  const basePlacement = placement.split(' ')[0];\n  const shiftVariation = placement.split(' ')[1];\n  if (shiftVariation) {\n    const {\n      host,\n      target\n    } = data.offsets;\n    const isVertical = ['bottom', 'top'].indexOf(basePlacement) !== -1;\n    const side = isVertical ? 'left' : 'top';\n    const measurement = isVertical ? 'width' : 'height';\n    const shiftOffsets = {\n      start: {\n        [side]: host[side]\n      },\n      end: {\n        [side]: (host[side] ?? 0) + host[measurement] - target[measurement]\n      }\n    };\n    data.offsets.target = {\n      ...target,\n      ...{\n        [side]: side === shiftVariation ? shiftOffsets.start[side] : shiftOffsets.end[side]\n      }\n    };\n  }\n  return data;\n}\nclass Positioning {\n  position(hostElement, targetElement /*, round = true*/) {\n    return this.offset(hostElement, targetElement /*, false*/);\n  }\n  offset(hostElement, targetElement /*, round = true*/) {\n    return getReferenceOffsets(targetElement, hostElement);\n  }\n  positionElements(hostElement, targetElement, position, appendToBody, options) {\n    const chainOfModifiers = [flip, shift, preventOverflow, arrow];\n    const _position = MapPlacementInToRL[position];\n    const data = initData(targetElement, hostElement, _position, options);\n    if (!data) {\n      return;\n    }\n    return chainOfModifiers.reduce((modifiedData, modifier) => modifier(modifiedData), data);\n  }\n}\nconst positionService = new Positioning();\nfunction positionElements(hostElement, targetElement, placement, appendToBody, options, renderer) {\n  const data = positionService.positionElements(hostElement, targetElement, placement, appendToBody, options);\n  if (!data) {\n    return;\n  }\n  const offsets = getOffsets(data);\n  setStyles(targetElement, {\n    'will-change': 'transform',\n    top: '0px',\n    left: '0px',\n    transform: `translate3d(${offsets.left}px, ${offsets.top}px, 0px)`\n  }, renderer);\n  if (data.instance.arrow) {\n    setStyles(data.instance.arrow, data.offsets.arrow, renderer);\n  }\n  updateContainerClass(data, renderer);\n}\nlet PositioningService = /*#__PURE__*/(() => {\n  class PositioningService {\n    constructor(ngZone, rendererFactory, platformId) {\n      this.update$$ = new Subject();\n      this.positionElements = new Map();\n      this.isDisabled = false;\n      if (isPlatformBrowser(platformId)) {\n        ngZone.runOutsideAngular(() => {\n          this.triggerEvent$ = merge(fromEvent(window, 'scroll', {\n            passive: true\n          }), fromEvent(window, 'resize', {\n            passive: true\n          }), of(0, animationFrameScheduler), this.update$$);\n          this.triggerEvent$.subscribe(() => {\n            if (this.isDisabled) {\n              return;\n            }\n            this.positionElements\n            // eslint-disable-next-line @typescript-eslint/no-explicit-any\n            .forEach(positionElement => {\n              positionElements(_getHtmlElement(positionElement.target), _getHtmlElement(positionElement.element), positionElement.attachment, positionElement.appendToBody, this.options, rendererFactory.createRenderer(null, null));\n            });\n          });\n        });\n      }\n    }\n    position(options) {\n      this.addPositionElement(options);\n    }\n    get event$() {\n      return this.triggerEvent$;\n    }\n    disable() {\n      this.isDisabled = true;\n    }\n    enable() {\n      this.isDisabled = false;\n    }\n    addPositionElement(options) {\n      this.positionElements.set(_getHtmlElement(options.element), options);\n    }\n    calcPosition() {\n      this.update$$.next(null);\n    }\n    deletePositionElement(elRef) {\n      this.positionElements.delete(_getHtmlElement(elRef));\n    }\n    setOptions(options) {\n      this.options = options;\n    }\n    static {\n      this.ɵfac = function PositioningService_Factory(t) {\n        return new (t || PositioningService)(i0.ɵɵinject(i0.NgZone), i0.ɵɵinject(i0.RendererFactory2), i0.ɵɵinject(PLATFORM_ID));\n      };\n    }\n    static {\n      this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n        token: PositioningService,\n        factory: PositioningService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return PositioningService;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nfunction _getHtmlElement(element) {\n  // it means that we got a selector\n  if (typeof element === 'string') {\n    return document.querySelector(element);\n  }\n  if (element instanceof ElementRef) {\n    return element.nativeElement;\n  }\n  return element ?? null;\n}\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { PlacementForBs5, Positioning, PositioningService, checkMargins, positionElements };\n//# sourceMappingURL=ngx-bootstrap-positioning.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}