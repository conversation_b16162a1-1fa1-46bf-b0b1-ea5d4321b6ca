{"ast": null, "code": "import { asyncScheduler } from '../scheduler/async';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function timeInterval(scheduler = asyncScheduler) {\n  return operate((source, subscriber) => {\n    let last = scheduler.now();\n    source.subscribe(createOperatorSubscriber(subscriber, value => {\n      const now = scheduler.now();\n      const interval = now - last;\n      last = now;\n      subscriber.next(new TimeInterval(value, interval));\n    }));\n  });\n}\nexport class TimeInterval {\n  constructor(value, interval) {\n    this.value = value;\n    this.interval = interval;\n  }\n}\n//# sourceMappingURL=timeInterval.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}