{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.scheduleIterable = void 0;\nvar Observable_1 = require(\"../Observable\");\nvar iterator_1 = require(\"../symbol/iterator\");\nvar isFunction_1 = require(\"../util/isFunction\");\nvar executeSchedule_1 = require(\"../util/executeSchedule\");\nfunction scheduleIterable(input, scheduler) {\n  return new Observable_1.Observable(function (subscriber) {\n    var iterator;\n    executeSchedule_1.executeSchedule(subscriber, scheduler, function () {\n      iterator = input[iterator_1.iterator]();\n      executeSchedule_1.executeSchedule(subscriber, scheduler, function () {\n        var _a;\n        var value;\n        var done;\n        try {\n          _a = iterator.next(), value = _a.value, done = _a.done;\n        } catch (err) {\n          subscriber.error(err);\n          return;\n        }\n        if (done) {\n          subscriber.complete();\n        } else {\n          subscriber.next(value);\n        }\n      }, 0, true);\n    });\n    return function () {\n      return isFunction_1.isFunction(iterator === null || iterator === void 0 ? void 0 : iterator.return) && iterator.return();\n    };\n  });\n}\nexports.scheduleIterable = scheduleIterable;\n//# sourceMappingURL=scheduleIterable.js.map", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}