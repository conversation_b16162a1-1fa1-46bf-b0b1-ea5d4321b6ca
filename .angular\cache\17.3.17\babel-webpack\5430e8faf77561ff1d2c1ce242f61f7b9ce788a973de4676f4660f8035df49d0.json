{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.single = void 0;\nvar EmptyError_1 = require(\"../util/EmptyError\");\nvar SequenceError_1 = require(\"../util/SequenceError\");\nvar NotFoundError_1 = require(\"../util/NotFoundError\");\nvar lift_1 = require(\"../util/lift\");\nvar OperatorSubscriber_1 = require(\"./OperatorSubscriber\");\nfunction single(predicate) {\n  return lift_1.operate(function (source, subscriber) {\n    var hasValue = false;\n    var singleValue;\n    var seenValue = false;\n    var index = 0;\n    source.subscribe(OperatorSubscriber_1.createOperatorSubscriber(subscriber, function (value) {\n      seenValue = true;\n      if (!predicate || predicate(value, index++, source)) {\n        hasValue && subscriber.error(new SequenceError_1.SequenceError('Too many matching values'));\n        hasValue = true;\n        singleValue = value;\n      }\n    }, function () {\n      if (hasValue) {\n        subscriber.next(singleValue);\n        subscriber.complete();\n      } else {\n        subscriber.error(seenValue ? new NotFoundError_1.NotFoundError('No matching values') : new EmptyError_1.EmptyError());\n      }\n    }));\n  });\n}\nexports.single = single;\n//# sourceMappingURL=single.js.map", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}