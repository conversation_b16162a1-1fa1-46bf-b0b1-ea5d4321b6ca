{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.takeWhile = void 0;\nvar lift_1 = require(\"../util/lift\");\nvar OperatorSubscriber_1 = require(\"./OperatorSubscriber\");\nfunction takeWhile(predicate, inclusive) {\n  if (inclusive === void 0) {\n    inclusive = false;\n  }\n  return lift_1.operate(function (source, subscriber) {\n    var index = 0;\n    source.subscribe(OperatorSubscriber_1.createOperatorSubscriber(subscriber, function (value) {\n      var result = predicate(value, index++);\n      (result || inclusive) && subscriber.next(value);\n      !result && subscriber.complete();\n    }));\n  });\n}\nexports.takeWhile = takeWhile;\n//# sourceMappingURL=takeWhile.js.map", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}