{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.argsOrArgArray = void 0;\nvar isArray = Array.isArray;\nfunction argsOrArgArray(args) {\n  return args.length === 1 && isArray(args[0]) ? args[0] : args;\n}\nexports.argsOrArgArray = argsOrArgArray;\n//# sourceMappingURL=argsOrArgArray.js.map", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}