{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.elementAt = void 0;\nvar ArgumentOutOfRangeError_1 = require(\"../util/ArgumentOutOfRangeError\");\nvar filter_1 = require(\"./filter\");\nvar throwIfEmpty_1 = require(\"./throwIfEmpty\");\nvar defaultIfEmpty_1 = require(\"./defaultIfEmpty\");\nvar take_1 = require(\"./take\");\nfunction elementAt(index, defaultValue) {\n  if (index < 0) {\n    throw new ArgumentOutOfRangeError_1.ArgumentOutOfRangeError();\n  }\n  var hasDefaultValue = arguments.length >= 2;\n  return function (source) {\n    return source.pipe(filter_1.filter(function (v, i) {\n      return i === index;\n    }), take_1.take(1), hasDefaultValue ? defaultIfEmpty_1.defaultIfEmpty(defaultValue) : throwIfEmpty_1.throwIfEmpty(function () {\n      return new ArgumentOutOfRangeError_1.ArgumentOutOfRangeError();\n    }));\n  };\n}\nexports.elementAt = elementAt;\n//# sourceMappingURL=elementAt.js.map", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}