{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.isObservable = void 0;\nvar Observable_1 = require(\"../Observable\");\nvar isFunction_1 = require(\"./isFunction\");\nfunction isObservable(obj) {\n  return !!obj && (obj instanceof Observable_1.Observable || isFunction_1.isFunction(obj.lift) && isFunction_1.isFunction(obj.subscribe));\n}\nexports.isObservable = isObservable;\n//# sourceMappingURL=isObservable.js.map", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}