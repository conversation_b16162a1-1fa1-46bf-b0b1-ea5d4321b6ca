{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport let WebapiService = /*#__PURE__*/(() => {\n  class WebapiService {\n    constructor(http) {\n      this.http = http;\n      this.CKHost = JSON.parse(sessionStorage.getItem('CKhost'));\n      //urlApi = 'http://*************/node/apinano/api/';\n      this.urlApi = 'http://*************/node/apinano/api/';\n      this.urlimg = 'http://*************/assets/imageBill/';\n      this.urlimgDefault = 'http://*************/assets/img/default-image.png';\n      this.urlimgbookbank = 'http://*************/assets/bookbank/';\n      this.urlApiuploadimg = 'http://*************/node/apinanoloadimg/api/';\n      this.urluserlist = '/security/userlist';\n      this.urlApifind_user = 'http://*************/security/find_user/';\n      this.urlApipostuser = '/node/apinano/api/create_user';\n      this.urlApideluser = '/node/apinano/api/delete_user';\n      this.urlApiUserlist = '/node/apinano/api/find_user';\n      this.urlApiUpdateUserlist = '/node/apinano/api/update_user';\n      this.urlApiUpdataGroup = '/node/apinano/api/update_usergroup';\n      this.urlusergroup = '/node/apinano/api/usergroup';\n      this.urlApipostgroup = '/node/apinano/api/create_usergroup';\n      this.urlApiDelGroup = '/node/apinano/api/delete_usergroup';\n      this.alt = false;\n      this.premissiondata = [];\n    }\n    getDummy2() {\n      return this.urlApi;\n    }\n    getUrlUpdataGroup() {\n      return this.urlApiUpdataGroup;\n    }\n    getUrlGroup() {\n      return this.urlApipostgroup;\n    }\n    getUrlDelGroup() {\n      return this.urlApiDelGroup;\n    }\n    getuserlogin(value) {\n      this.userlogin = value;\n    }\n    setuserlogin() {\n      return this.userlogin;\n    }\n    getmenulogin(value) {\n      this.menulogin = value;\n    }\n    setmenulogin() {\n      return this.menulogin;\n    }\n    getalert(value) {\n      this.alt = value;\n    }\n    setalert() {\n      return this.alt;\n    }\n    getUrluser() {\n      return this.urlApipostuser;\n    }\n    getUrlUserlist() {\n      return this.urlApiUserlist;\n    }\n    getUrlUpdateUserlist() {\n      return this.urlApiUpdateUserlist;\n    }\n    getUrldeluser() {\n      return this.urlApideluser;\n    }\n    setpromisstiondata(value) {\n      this.premissiondata = value;\n    }\n    getpermisstiondata() {\n      return this.premissiondata;\n    }\n    feedData() {\n      const url = this.urlApi + this.urluserlist;\n      return this.http.get(url).toPromise().catch();\n    }\n    DataGroup() {\n      const url = this.urlApi + this.urlusergroup;\n      return this.http.get(url).toPromise().catch();\n    }\n    geturlservice() {\n      // return this.urlApi='http://localhost/node/apinano/api/';\n      return this.urlApi = 'http://*************/node/apinano/api/';\n      // return this.urlApi='http://*************3:9090/node/apinano/api/';\n    }\n    geturlloadimgservice() {\n      return this.urlApiuploadimg;\n    }\n    geturlserviceIMG() {\n      return this.urlimg;\n    }\n    geturlimgDefault() {\n      return this.urlimgDefault;\n    }\n    geturlserviceIMGbookbank() {\n      return this.urlimgbookbank;\n    }\n    downloadfiletxt(valuename) {\n      var body = {\n        filename: valuename\n      };\n      return this.http.get(this.urlApi + 'downloadfile');\n    }\n    static {\n      this.ɵfac = function WebapiService_Factory(t) {\n        return new (t || WebapiService)(i0.ɵɵinject(i1.HttpClient));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: WebapiService,\n        factory: WebapiService.ɵfac\n      });\n    }\n  }\n  return WebapiService;\n})();\nvar Ajax;\n(function (Ajax) {\n  class Options {\n    constructor(url, method, data) {\n      this.url = url;\n      this.method = method || \"get\";\n      this.data = data || {};\n    }\n  }\n  Ajax.Options = Options;\n  class Service {\n    constructor() {\n      this.request = (options, successCallback, errorCallback) => {\n        var that = this;\n        $.ajax({\n          url: options.url,\n          type: options.method,\n          data: options.data,\n          cache: false,\n          success: function (d) {\n            successCallback(d);\n          },\n          error: function (d) {\n            if (errorCallback) {\n              errorCallback(d);\n              return;\n            }\n            var errorTitle = \"Error in (\" + options.url + \")\";\n            var fullError = JSON.stringify(d);\n            console.log(errorTitle);\n            console.log(fullError);\n            that.showJqueryDialog(fullError, errorTitle);\n          }\n        });\n      };\n      this.get = (url, successCallback, errorCallback) => {\n        this.request(new Options(url), successCallback, errorCallback);\n      };\n      this.getWithDataInput = (url, data, successCallback, errorCallback) => {\n        this.request(new Options(url, \"get\", data), successCallback, errorCallback);\n      };\n      this.post = (url, successCallback, errorCallback) => {\n        this.request(new Options(url, \"post\"), successCallback, errorCallback);\n      };\n      this.postWithData = (url, data, successCallback, errorCallback) => {\n        this.request(new Options(url, \"post\", data), successCallback, errorCallback);\n      };\n      this.showJqueryDialog = (message, title, height) => {\n        alert(title + \"\\n\" + message);\n        title = title || \"Info\";\n        height = height || 120;\n        message = message.replace(\"\\r\", \"\").replace(\"\\n\", \"<br/>\");\n      };\n    }\n  }\n  Ajax.Service = Service;\n})(Ajax || (Ajax = {}));", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}