{"ast": null, "code": "import _asyncToGenerator from \"D:/ISR/front_soweb/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { HttpHeaders } from '@angular/common/http';\nimport { listLocales } from 'ngx-bootstrap/chronos';\nimport { debounceTime, map } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"../webapi.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/forms\";\nimport * as i5 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i6 from \"ngx-bootstrap/datepicker\";\nconst _c0 = a0 => ({\n  \"display\": a0,\n  \"opacity\": 1\n});\nconst _c1 = a0 => ({\n  \"color\": a0\n});\nfunction SorecordComponent_button_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 80);\n    i0.ɵɵlistener(\"click\", function SorecordComponent_button_13_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.deleteebeforupdate());\n    });\n    i0.ɵɵtext(1, \"Draft\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"disabled\", ctx_r2.dissave == false);\n  }\n}\nfunction SorecordComponent_button_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 81);\n    i0.ɵɵlistener(\"click\", function SorecordComponent_button_14_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.savesaleoderbywherehouse());\n    });\n    i0.ɵɵtext(1, \"Save\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"disabled\", ctx_r2.dissave == false);\n  }\n}\nfunction SorecordComponent_button_17_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 82);\n    i0.ɵɵlistener(\"click\", function SorecordComponent_button_17_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.ReloadIdSo());\n    });\n    i0.ɵɵtext(1, \"New ID\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SorecordComponent_ng_template_24_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 83);\n    i0.ɵɵlistener(\"click\", function SorecordComponent_ng_template_24_Template_div_click_0_listener() {\n      const r_r7 = i0.ɵɵrestoreView(_r6).result;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.getcustomersalefunction(r_r7.name));\n    });\n    i0.ɵɵelementStart(1, \"label\", 84);\n    i0.ɵɵlistener(\"mousedown\", function SorecordComponent_ng_template_24_Template_label_mousedown_1_listener() {\n      const r_r7 = i0.ɵɵrestoreView(_r6).result;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.getcustomersalefunction(r_r7.name));\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const r_r7 = ctx.result;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate3(\"\", r_r7.name, \" (\", r_r7.accountnum, \") (\", r_r7.regnum, \")\");\n  }\n}\nfunction SorecordComponent_option_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 85);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r8 = ctx.$implicit;\n    i0.ɵɵpropertyInterpolate(\"value\", item_r8.locationno);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\"(\", item_r8.name, \") \", item_r8.address, \"\");\n  }\n}\nfunction SorecordComponent_option_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 85);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r9 = ctx.$implicit;\n    i0.ɵɵpropertyInterpolate(\"value\", item_r9.locationno);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\"(\", item_r9.name, \") \", item_r9.address, \"\");\n  }\n}\nfunction SorecordComponent_option_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 85);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r10 = ctx.$implicit;\n    i0.ɵɵpropertyInterpolate(\"value\", item_r10.vattype);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r10.vattype);\n  }\n}\nfunction SorecordComponent_option_39_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 85);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r11 = ctx.$implicit;\n    i0.ɵɵpropertyInterpolate(\"value\", item_r11.deliver);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r11.deliver);\n  }\n}\nfunction SorecordComponent_option_45_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 86);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r12 = ctx.$implicit;\n    i0.ɵɵproperty(\"ngValue\", item_r12.paymtermid);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r12.paymtermid);\n  }\n}\nfunction SorecordComponent_label_57_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"label\", 87);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"Item In Stock: \", ctx_r2.Instock, \"\");\n  }\n}\nfunction SorecordComponent_ng_template_62_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 88);\n    i0.ɵɵlistener(\"mousedown\", function SorecordComponent_ng_template_62_Template_div_mousedown_0_listener() {\n      const r_r14 = i0.ɵɵrestoreView(_r13).result;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.Searchitemclick(r_r14.itemid));\n    })(\"click\", function SorecordComponent_ng_template_62_Template_div_click_0_listener() {\n      const r_r14 = i0.ɵɵrestoreView(_r13).result;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.Searchitemclick(r_r14.itemid));\n    });\n    i0.ɵɵelementStart(1, \"label\", 89);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const r_r14 = ctx.result;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"(\", r_r14.itemid, \") \", r_r14.name, \" \");\n  }\n}\nfunction SorecordComponent_option_69_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 85);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r15 = ctx.$implicit;\n    i0.ɵɵpropertyInterpolate(\"value\", item_r15.unit + item_r15.unitid);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r15.unitid);\n  }\n}\nfunction SorecordComponent_div_70_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 27)(1, \"input\", 90);\n    i0.ɵɵpipe(2, \"myCurrency\");\n    i0.ɵɵlistener(\"focusout\", function SorecordComponent_div_70_Template_input_focusout_1_listener($event) {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.editpriceperunit($event.target.value));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵpropertyInterpolate(\"value\", i0.ɵɵpipeBind1(2, 2, ctx_r2.priceperunit));\n    i0.ɵɵproperty(\"disabled\", ctx_r2.ennableeditprice);\n  }\n}\nfunction SorecordComponent_div_71_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 27)(1, \"input\", 91);\n    i0.ɵɵlistener(\"focusout\", function SorecordComponent_div_71_Template_input_focusout_1_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.editdiscount());\n    });\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SorecordComponent_div_71_Template_input_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.dis1, $event) || (ctx_r2.dis1 = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r2.ennablecustomer);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.dis1);\n  }\n}\nfunction SorecordComponent_div_72_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 27)(1, \"input\", 92);\n    i0.ɵɵlistener(\"focusout\", function SorecordComponent_div_72_Template_input_focusout_1_listener() {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.editdiscount());\n    });\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SorecordComponent_div_72_Template_input_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.dis2, $event) || (ctx_r2.dis2 = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r2.ennablecustomer);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.dis2);\n  }\n}\nfunction SorecordComponent_div_73_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r19 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 27)(1, \"input\", 93);\n    i0.ɵɵlistener(\"focusout\", function SorecordComponent_div_73_Template_input_focusout_1_listener() {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.editdiscount());\n    });\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SorecordComponent_div_73_Template_input_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.dis3, $event) || (ctx_r2.dis3 = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r2.ennablecustomer);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.dis3);\n  }\n}\nfunction SorecordComponent_div_74_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 27);\n    i0.ɵɵelement(1, \"input\", 94);\n    i0.ɵɵpipe(2, \"myCurrency\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵpropertyInterpolate(\"value\", i0.ɵɵpipeBind1(2, 1, ctx_r2.alldiscount));\n  }\n}\nfunction SorecordComponent_div_75_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 27);\n    i0.ɵɵelement(1, \"input\", 95);\n    i0.ɵɵpipe(2, \"myCurrency\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵpropertyInterpolate(\"value\", i0.ɵɵpipeBind1(2, 1, ctx_r2.finalprice));\n  }\n}\nfunction SorecordComponent_div_79_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r20 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 27)(1, \"div\", 96)(2, \"input\", 97);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SorecordComponent_div_79_Template_input_ngModelChange_2_listener($event) {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.freeitem, $event) || (ctx_r2.freeitem = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"click\", function SorecordComponent_div_79_Template_input_click_2_listener($event) {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.checkfreeitemfunction($event.target.checked));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"label\", 98);\n    i0.ɵɵtext(4, \"\\u0E02\\u0E2D\\u0E07\\u0E41\\u0E16\\u0E21\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.freeitem);\n  }\n}\nfunction SorecordComponent_th_97_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 50);\n    i0.ɵɵtext(1, \"In Stock\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SorecordComponent_th_98_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 50);\n    i0.ɵɵtext(1, \"\\u0E23\\u0E32\\u0E04\\u0E32/\\u0E2B\\u0E19\\u0E48\\u0E27\\u0E22\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SorecordComponent_th_99_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 50);\n    i0.ɵɵtext(1, \"\\u0E23\\u0E32\\u0E04\\u0E32\\u0E2A\\u0E34\\u0E19\\u0E04\\u0E49\\u0E32\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SorecordComponent_th_100_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 50);\n    i0.ɵɵtext(1, \"%\\u0E25\\u0E14#1\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SorecordComponent_th_101_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 50);\n    i0.ɵɵtext(1, \"%\\u0E25\\u0E14#2\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SorecordComponent_th_102_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 50);\n    i0.ɵɵtext(1, \"%\\u0E25\\u0E14#3\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SorecordComponent_th_103_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 50);\n    i0.ɵɵtext(1, \"\\u0E23\\u0E27\\u0E21\\u0E2A\\u0E48\\u0E27\\u0E19\\u0E25\\u0E14\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SorecordComponent_th_104_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 50);\n    i0.ɵɵtext(1, \"\\u0E21\\u0E39\\u0E25\\u0E04\\u0E48\\u0E32\\u0E2A\\u0E38\\u0E17\\u0E18\\u0E34\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SorecordComponent_tr_107_td_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 100);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"number\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r22 = i0.ɵɵnextContext().$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(2, 1, ctx_r2.Realtimeinstock(item_r22.iditem), \"1.0-2\"));\n  }\n}\nfunction SorecordComponent_tr_107_td_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 100);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"number\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r22 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(2, 1, item_r22.priceperunit, \"1.2-2\"));\n  }\n}\nfunction SorecordComponent_tr_107_td_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 100);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"number\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r22 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(2, 1, item_r22.priceproduct, \"1.2-2\"));\n  }\n}\nfunction SorecordComponent_tr_107_td_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 107);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"number\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r22 = i0.ɵɵnextContext().$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(5, _c1, ctx_r2.getColordis1(item_r22.disst)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(2, 2, item_r22.discount1, \"1.2-2\"));\n  }\n}\nfunction SorecordComponent_tr_107_td_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 107);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"number\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r22 = i0.ɵɵnextContext().$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(5, _c1, ctx_r2.getColordis2(item_r22.disst)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(2, 2, item_r22.discount2, \"1.2-2\"));\n  }\n}\nfunction SorecordComponent_tr_107_td_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 107);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"number\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r22 = i0.ɵɵnextContext().$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(5, _c1, ctx_r2.getColordis3(item_r22.disst)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(2, 2, item_r22.discount3, \"1.2-2\"));\n  }\n}\nfunction SorecordComponent_tr_107_td_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 100);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"number\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r22 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(2, 1, item_r22.sumdiscount, \"1.2-2\"));\n  }\n}\nfunction SorecordComponent_tr_107_td_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 100);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"number\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r22 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(2, 1, item_r22.sumallprice, \"1.2-2\"));\n  }\n}\nfunction SorecordComponent_tr_107_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r21 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 99);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\", 99);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\", 100);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\", 101);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"td\", 100);\n    i0.ɵɵtext(12);\n    i0.ɵɵpipe(13, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"td\", 100);\n    i0.ɵɵtext(15);\n    i0.ɵɵpipe(16, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(17, SorecordComponent_tr_107_td_17_Template, 3, 4, \"td\", 102)(18, SorecordComponent_tr_107_td_18_Template, 3, 4, \"td\", 102)(19, SorecordComponent_tr_107_td_19_Template, 3, 4, \"td\", 102)(20, SorecordComponent_tr_107_td_20_Template, 3, 7, \"td\", 103)(21, SorecordComponent_tr_107_td_21_Template, 3, 7, \"td\", 104)(22, SorecordComponent_tr_107_td_22_Template, 3, 7, \"td\", 104)(23, SorecordComponent_tr_107_td_23_Template, 3, 4, \"td\", 102)(24, SorecordComponent_tr_107_td_24_Template, 3, 4, \"td\", 102);\n    i0.ɵɵelementStart(25, \"td\", 99)(26, \"label\", 105);\n    i0.ɵɵlistener(\"click\", function SorecordComponent_tr_107_Template_label_click_26_listener() {\n      const i_r23 = i0.ɵɵrestoreView(_r21).index;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.foreditlistlineoderclick(i_r23, true));\n    });\n    i0.ɵɵtext(27, \"\\u0E41\\u0E01\\u0E49\\u0E44\\u0E02\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(28, \"td\", 99)(29, \"label\", 106);\n    i0.ɵɵlistener(\"click\", function SorecordComponent_tr_107_Template_label_click_29_listener() {\n      const ctx_r23 = i0.ɵɵrestoreView(_r21);\n      const item_r22 = ctx_r23.$implicit;\n      const i_r23 = ctx_r23.index;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.deletelistlineoderclick(i_r23, item_r22.idline));\n    });\n    i0.ɵɵtext(30, \"\\u0E25\\u0E1A\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const item_r22 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r22.linenum);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r22.iditem);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r22.nameproduct);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r22.numberpcs);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r22.unitid);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(13, 15, item_r22.packingitem, \"1.2-2\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(16, 18, item_r22.totleweight, \"1.2-2\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.ennablecustomer == false);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.ennablecustomer == false);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.ennablecustomer == false);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.ennablecustomer == false);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.ennablecustomer == false);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.ennablecustomer == false);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.ennablecustomer == false);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.ennablecustomer == false);\n  }\n}\nfunction SorecordComponent_td_109_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"td\", 58);\n  }\n}\nfunction SorecordComponent_td_110_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"td\", 58);\n  }\n}\nfunction SorecordComponent_td_111_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"td\", 58);\n  }\n}\nfunction SorecordComponent_td_112_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"td\", 58);\n  }\n}\nfunction SorecordComponent_td_113_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"td\", 58);\n  }\n}\nfunction SorecordComponent_td_114_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"td\", 58);\n  }\n}\nfunction SorecordComponent_td_115_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"td\", 58);\n  }\n}\nfunction SorecordComponent_td_116_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"td\", 58);\n  }\n}\nfunction SorecordComponent_td_117_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"td\", 58);\n  }\n}\nfunction SorecordComponent_td_118_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"td\", 58);\n  }\n}\nfunction SorecordComponent_td_119_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"td\", 58);\n  }\n}\nfunction SorecordComponent_td_120_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"td\", 58);\n  }\n}\nfunction SorecordComponent_td_121_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 59);\n    i0.ɵɵtext(1, \"\\u0E21\\u0E39\\u0E25\\u0E04\\u0E48\\u0E32\\u0E23\\u0E27\\u0E21\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SorecordComponent_td_122_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 60);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"number\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(2, 1, ctx_r2.sumalldiscount, \"1.2-2\"));\n  }\n}\nfunction SorecordComponent_td_123_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 60);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"number\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(2, 1, ctx_r2.sumallpricedis, \"1.2-2\"));\n  }\n}\nfunction SorecordComponent_td_124_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"td\", 61);\n  }\n}\nfunction SorecordComponent_td_125_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"td\", 61);\n  }\n}\nfunction SorecordComponent_td_131_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"td\", 58);\n  }\n}\nfunction SorecordComponent_td_132_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"td\", 58);\n  }\n}\nfunction SorecordComponent_td_133_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"td\", 58);\n  }\n}\nfunction SorecordComponent_td_134_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"td\", 58);\n  }\n}\nfunction SorecordComponent_td_135_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"td\", 58);\n  }\n}\nfunction SorecordComponent_td_136_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"td\", 58);\n  }\n}\nfunction SorecordComponent_td_137_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"td\", 58);\n  }\n}\nfunction SorecordComponent_td_138_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"td\", 58);\n  }\n}\n;\n;\n;\nconst httpOptions = {\n  headers: new HttpHeaders({\n    'Accept': 'application/json, text/plain, */*',\n    'Content-Type': 'application/json',\n    'Origin': '*'\n  })\n};\nexport let SorecordComponent = /*#__PURE__*/(() => {\n  class SorecordComponent {\n    getColordis1(disst) {\n      var st = disst;\n      var fi = st.substring(0, 1);\n      switch (fi) {\n        case '1':\n          return 'red';\n      }\n    }\n    getColordis2(disst) {\n      var st = disst;\n      var fi = st.substring(1, 2);\n      switch (fi) {\n        case '1':\n          return 'red';\n      }\n    }\n    getColordis3(disst) {\n      var st = disst;\n      var fi = st.substring(2, 3);\n      switch (fi) {\n        case '1':\n          return 'red';\n      }\n    }\n    constructor(http, service, route, router, fb, calendar, localeService) {\n      this.http = http;\n      this.service = service;\n      this.route = route;\n      this.router = router;\n      this.fb = fb;\n      this.calendar = calendar;\n      this.localeService = localeService;\n      this.mdlSampleIsOpen = false;\n      this.alt = '';\n      this.checkreload = true;\n      this.olddis1 = 0.00;\n      this.olddis2 = 0.00;\n      this.olddis3 = 0.00;\n      this.filetype = '-';\n      this.filename = '-';\n      this.chackdalateoder = true;\n      this.upperdis = '';\n      this.idforshow = '';\n      this.ennableeditprice = true;\n      this.openbtnclose = false;\n      this.getpromotionedit = false;\n      this.checkpacking = 0;\n      this.getcheckpcs = 'P';\n      this.getwh = [];\n      this.checkwh = [];\n      this.warehouselist = [];\n      this.wh = '';\n      this.getallidsaleoder = [];\n      this.getrunnumber = '';\n      this.checkadditem = false;\n      this.filterargs = {\n        title: 'hello'\n      };\n      this.items = [{\n        title: 'hello world'\n      }, {\n        title: 'hello kitty'\n      }, {\n        title: 'foo bar'\n      }];\n      this.selectedpaymentmap = '';\n      this.getpackingprice = 0.00;\n      this.getunitprice = 0.00;\n      this.showitem = \"รหัสสินค้า\";\n      this.headerlist = [];\n      this.headerlistsave = [];\n      this.freeitem = false;\n      this.promotionlist = [];\n      this.accoutnreration = '';\n      this.pricegroup = '';\n      this.lineoderlist = [];\n      this.lineoderlistsave = [];\n      this.today = new Date();\n      this.x = 1;\n      this.numlist = 0;\n      this.locationno = '';\n      this.notesoinput = '';\n      this.remarksoinput = '';\n      this.checkbuttonsave = false;\n      this.sumalldiscount = 0;\n      this.sumallpricedis = 0;\n      this.sumallweight = 0;\n      this.selectunitid = '';\n      this.packingid = '';\n      this.numpackingitem = 0;\n      this.numline = 1;\n      this.weightproduct = 0;\n      this.idsaleoder = '';\n      this.testpara = '';\n      this.model = [];\n      this.numberproductsearch = null;\n      this.searching = false;\n      this.searchFailed = false;\n      this.salecustomer = '';\n      this.priceperunit = '0';\n      this.selectpayment = '';\n      this.amount = '0';\n      this.totalweight = '0.0';\n      this.discount = '0';\n      this.iditem = '';\n      this.price = 0;\n      this.allpcs = 0;\n      this.allsumprice = 0.0;\n      this.dis1 = 0.00;\n      this.dis2 = 0.00;\n      this.dis3 = 0.00;\n      this.alldiscount = 0.0;\n      this.finalprice = 0.0;\n      this.unitlist = [];\n      this.deliveryonclick = '';\n      this.deliverytype = [{\n        'deliver': 'รถบริษัท'\n      }, {\n        'deliver': 'รับเอง'\n      }, {\n        'deliver': 'ขนส่ง'\n      }, {\n        'deliver': 'ประเภทการขนส่ง'\n      }];\n      this.paymentlist = [];\n      this.vatlist = [{\n        'vattype': 'VAT ไม่ยื่น'\n      }, {\n        'vattype': 'VAT'\n      }];\n      this.showvat = true;\n      this.idsoedit = '';\n      this.company = 'ป้อนชื่อ หรือ รหัสลูกค้า';\n      this.shownovat = true;\n      this.usseredit = '';\n      this.locationnodelie = '';\n      this.fromdate = '';\n      this.todate = '';\n      this.locale = 'th';\n      this.locales = listLocales();\n      this.ennablecustomer = false;\n      this.accountnum = '';\n      this.permisstiondata = [];\n      this.exportbtn = true;\n      this.actionbtn = false;\n      this.idhead = 0;\n      this.time = '';\n      this.dissave = false;\n      this.clickbtnclose = true;\n      this.waitsave = true;\n      this.setpackde = 0;\n      this.selectedunit = false;\n      this.Instock = '';\n      this.StockAllList = [];\n      this.oldidso = '';\n      this.CheckSoidbtn = false;\n      this.CheckSoidWord = \"\";\n      this.search = text$ =>\n      //Autocomplete ลูกค้า\n      text$.pipe(debounceTime(200), map(term => term === '' ? [] : this.customers.filter(v => v.name.toLowerCase().indexOf(term.toLowerCase()) > -1 || v.accountnum.toLowerCase().indexOf(term.toLowerCase()) > -1).slice(0, 150)));\n      this.formatter = x => x.name + ' (' + x.accountnum + ')' + '(' + x.regnum + ')';\n      this.searchpr = text$ =>\n      //Autocomplete รหัสสินค้า\n      text$.pipe(debounceTime(200), map(term => term === '' ? [] : this.productauto.filter(v => v.itemid.toLowerCase().indexOf(term.toLowerCase()) > -1 || v.name.toLowerCase().indexOf(term.toLowerCase()) > -1).slice(0, 50)));\n      this.formatterpr = x => x.itemid;\n      //ดึงข้อมูล URL สำหรับ API\n      localStorage.removeItem('DataSOderreview');\n      this.oldidso = localStorage.getItem('SoidOld');\n      this.bsValue = new Date();\n      this.url = service.geturlservice();\n      this.fromDate = calendar.getPrev(calendar.getToday(), 'd', 30);\n      this.toDate = calendar.getToday();\n      this.fromdate = '';\n      this.todate = '';\n      this.Datatodate = new Date(this.toDate.day, this.toDate.month - 1, this.toDate.year);\n      this.Datafromdate = new Date(this.fromDate.year, this.fromDate.month - 1, this.fromDate.day);\n      this.LoadAllstock();\n      this.getdate();\n      this.model = null;\n      this.time = this.bsValue.getHours() + ':' + this.bsValue.getMinutes() + ':' + this.bsValue.getSeconds();\n      //alert(this.time);\n      //this.exportbtn=!this.permisstiondata[3].flag_print;\n      //this.actionbtn=!this.permisstiondata[3].flag_action;\n      //ดึงข้อฒุลการ เข้าสู่ระบบ\n      //[(ngModel)]=\"alldiscount\"\n      this.Namelogin = JSON.parse(sessionStorage.getItem('login'));\n      this.usseredit = this.Namelogin[0].salegroup;\n      this.loadpermisstion(this.Namelogin[0].id_group_user);\n      this.setpackde = this.Namelogin[0].pack;\n      //alert(this.setpackde);\n      //alert(JSON.stringify(this.Namelogin[0].accountnum));\n      this.idsoedit = this.route.snapshot.queryParams['idso'];\n      if (this.idsoedit == undefined) {\n        localStorage.removeItem('DataSOderreview');\n        localStorage.removeItem('DataSOderlist');\n      } else {\n        this.waitsave = false;\n        this.idsaleoder = this.idsoedit;\n        this.idforshow = this.idsaleoder;\n        this.oldidso = this.idsaleoder;\n        this.openbtnclose = true;\n        this.selecttoeditheader(this.idsoedit);\n        //this.setloadcustomeredit=setInterval(()=>,300);\n      }\n      var getdate = '';\n      var day = this.today.getDate();\n      var month = this.today.getMonth() + 1;\n      var year = this.today.getFullYear();\n      if (day.toString().length > 1) {\n        getdate = day.toString();\n      } else {\n        getdate = '0' + day.toString();\n      }\n      if (month.toString().length > 1) {\n        this.dateshipping = year.toString() + '-' + month.toString() + '-' + getdate;\n      } else {\n        this.dateshipping = year.toString() + '-0' + month.toString() + '-' + getdate;\n      }\n      this.getcostomerauto();\n      this.loadrunnumberid(this.dateshipping);\n      if (this.idsoedit == undefined) {\n        // if(this.idforshow==this.oldidso){\n        //   alert('เลขที่ SO ใช้งานซ้ำ กรุณากด New ID เพื่อสร้างเลข SO ใหม่');\n        // }\n        // this.checkidsaleorderbeforinput(this.idforshow);\n      }\n    }\n    getdate() {\n      this.fromdate = `${this.Datafromdate.getFullYear()}-${this.Datafromdate.getMonth() + 1}-${this.Datafromdate.getDate()}`;\n      this.todate = `${this.Datatodate.getFullYear()}-${this.Datatodate.getMonth() + 1}-${this.Datatodate.getDate()}`;\n    }\n    ngOnInit() {\n      if (this.Namelogin[0].accountnum != undefined) {\n        this.getcustomersalefunction(this.Namelogin[0].accountnum);\n        this.ennablecustomer = true;\n        this.setpackde = 1;\n        this.getproductid(this.Namelogin[0].accountnum);\n      } else {\n        this.getproductid(this.Namelogin[0].salegroup);\n      }\n      // alert(this.idsaleoder);\n    }\n    checkidsaleorderbeforinput(idso) {\n      this.http.get('http://119.59.112.47/SoAPI/api/values/CheckIdso/' + idso).subscribe(res => {\n        if (res) {\n          //  alert('เลขที่ SO ใช้งานซ้ำ กรุณากด New ID เพื่อสร้างเลข SO ใหม่');\n          //  this.CheckSoidbtn=true;\n          //  this.CheckSoidWord=\"text-danger\";\n          this.updaterunnumber(this.getallidsaleoder);\n          return true;\n        } else {\n          this.CheckSoidbtn = false;\n          this.CheckSoidWord = \"text-info\";\n          return false;\n        }\n      });\n    }\n    loadpermisstion(value) {\n      this.http.post(this.url + 'find_permission', {\n        id_user_group: value\n      }).subscribe(res => {\n        if (res.length > 0) {\n          this.exportbtn = !res[3].flag_print;\n          this.actionbtn = !res[3].flag_action;\n        }\n      }, error => {\n        console.log(error);\n      });\n    }\n    // CheckSoidbtn(){\n    //   if(this.idsoedit==undefined){\n    //    //var ch= this.checkidsaleorderbeforinput(this.idforshow)\n    //     if(ch==false){\n    //       return true;\n    //     }else{\n    //       //alert('เลขที่ SO ใช้งานซ้ำ กรุณากด New ID เพื่อสร้างเลข SO ใหม่');\n    //       return false;\n    //     }\n    //   }else{\n    //     return false;\n    //   }\n    // }\n    ReloadIdSo() {\n      this.updaterunnumber(this.getallidsaleoder);\n    }\n    //ดึงข้อมูลการรัน ID Sale Oder จาก Database\n    loadrunnumberid(date) {\n      //alert(date);\n      var text = \"SW\";\n      var zero = '0';\n      var num = 0;\n      var saleid = this.Namelogin[0].salegroup;\n      if (this.Namelogin[0].accountnum != undefined) {\n        saleid = this.Namelogin[0].accountnum;\n        text = \"S\";\n      }\n      var month = date.substring(5, 7);\n      var year = date.substring(0, 4);\n      this.http.get(this.url + 'checknumbersaleoder/' + text + '/' + saleid + '/' + year.toString() + '/' + month.toString() + '/' + 0).subscribe(res => {\n        num = res[0].num + 1;\n        for (var i = 0; i < 3 - JSON.stringify(res[0].num).length; i++) {\n          zero = zero + '0';\n        }\n        if (num == 10) {\n          zero = zero.substring(1);\n        }\n        this.getrunnumber = zero + num;\n        this.idsaleoder = this.makeidsaleOder(this.getrunnumber, num, date);\n        this.checkidsaleorderbeforinput(this.idsaleoder);\n      });\n    }\n    //ดึงรหัสลูกค้ามาใช้ใน Autocomplete\n    getcostomerauto() {\n      var idsale = this.Namelogin[0].salegroup;\n      if (this.Namelogin[0].salegroup === 'admin') {\n        idsale = '%20';\n      } else {\n        idsale = this.Namelogin[0].salegroup;\n      }\n      this.http.get(this.url + 'customerauto/' + idsale).subscribe(res => {\n        this.customers = res;\n      });\n    }\n    //ดึงรหัสสินค้ามาใช้ใน Autocomplete\n    getproductid(accountnum) {\n      if (accountnum == '') {\n        accountnum = '%20';\n      }\n      this.http.get(this.url + 'productauto/' + accountnum).subscribe(res => {\n        if (res.length > 0) {\n          this.productauto = res;\n        }\n      });\n    }\n    //ตั้งสินค้าให้เป็นของแุถม\n    checkfreeitemfunction(value) {\n      if (value == true) {\n        this.selectedunit = true;\n        this.checkadditem = value;\n        this.reprice = parseFloat(this.priceperunit);\n        this.redis1 = this.dis1;\n        this.redis2 = this.dis2;\n        this.redis3 = this.dis3;\n        this.refinalprice = this.finalprice;\n        this.reallpcs = this.allpcs;\n        this.regetunitprice = this.getunitprice;\n        this.realldiscount = this.alldiscount;\n        this.priceperunit = '0';\n        this.dis1 = 0;\n        this.dis2 = 0;\n        this.dis3 = 0;\n        this.finalprice = 0;\n        this.getunitprice = 0;\n        this.alldiscount = 0;\n      } else {\n        this.selectedunit = false;\n        this.checkadditem = value;\n        this.priceperunit = this.reprice.toString();\n        this.dis1 = this.redis1;\n        this.dis2 = this.redis2;\n        this.dis3 = this.redis3;\n        this.finalprice = this.refinalprice;\n        this.allpcs = this.reallpcs;\n        this.getunitprice = this.regetunitprice;\n        this.alldiscount = this.realldiscount;\n      }\n    }\n    //ดึงข้อมูล Sale Heaader จาก Database มาแก้ไข\n    selecttoeditheader(valueid) {\n      this.http.get(this.url + 'find_saleheader/' + valueid).subscribe(res => {\n        if (res.length > 0) {\n          this.remarksoinput = res[0].remark;\n          var daya = new Date();\n          //clearInterval(this.setloadcustomeredit);\n          var getdate = res[0].dateid.toString();\n          this.dateshipping = getdate;\n          var day = getdate.substring(0, 2);\n          var month = getdate.substring(3, 5);\n          var year = getdate.substring(6, 10);\n          //this.bsValue.setDate(day);\n          if (day == daya.getDate() && month == daya.getMonth() + 1) {} else {\n            this.bsValue = new Date(year, month - 1, day);\n          }\n          // alert(this.bsValue);\n          this.selectedsetsaleheader(res);\n          //this.lineoderlist=null;\n          this.company = res[0].SalesName;\n          this.getcustomersalefunction(this.company);\n          //this.setloadaddress=setInterval(()=> ,1000);\n          //this.getcustomersalefunction(this.company);\n          //this.getcustomersalefunction(this.company);\n          this.mappaymenttype(res[0].paymenttype);\n          this.usseredit = res[0].SalesId;\n          this.deliveryonclick = res[0].DlvMode;\n          /*this.Searchcustomeraddress(res[0].InvoiceAccount);*/\n          if (res[0].DlvMode === 'ขนส่ง') {\n            this.deliveryonclick = 'ขนส่ง';\n            this.deliverytype = [{\n              'deliver': 'รถบริษัท'\n            }, {\n              'deliver': 'รับเอง'\n            }, {\n              'deliver': 'ขนส่ง'\n            }];\n          } else if (res[0].DlvMode === 'รับเอง') {\n            this.deliveryonclick = 'รับเอง';\n            this.deliverytype = [{\n              'deliver': 'รถบริษัท'\n            }, {\n              'deliver': 'ขนส่ง'\n            }, {\n              'deliver': 'รับเอง'\n            }];\n          } else if (res[0].DlvMode === 'รถบริษัท') {\n            this.deliveryonclick = 'รถบริษัท';\n            this.deliverytype = [{\n              'deliver': 'รับเอง'\n            }, {\n              'deliver': 'ขนส่ง'\n            }, {\n              'deliver': 'รถบริษัท'\n            }];\n          }\n          if (res[0].vattype === 'VAT') {\n            this.vatselect = res[0].vattype;\n            this.vatlist = [{\n              'vattype': 'VAT ไม่ยื่น'\n            }, {\n              'vattype': 'VAT'\n            }];\n          } else if (res[0].vattype === 'NO VAT') {\n            this.vatselect = res[0].vattype;\n            this.vatlist = [{\n              'vattype': 'VAT'\n            }, {\n              'vattype': 'VAT ไม่ยื่น'\n            }];\n          } else if (res[0].vattype === 'VAT ไม่ยื่น') {\n            this.vatselect = res[0].vattype;\n            this.vatlist = [{\n              'vattype': 'VAT'\n            }, {\n              'vattype': 'VAT ไม่ยื่น'\n            }];\n          }\n        }\n      }, error => {});\n      this.openModal(false, 'กำลังโหลดข้อมูล', false);\n    }\n    //สร้าง SaleOder ID ใหม่จากวันที่ ที่เลือก\n    clickdateshipping(value) {\n      this.loadrunnumberid(value);\n    }\n    setsaleheader(id, wh) {\n      this.headerlist = [];\n      this.headerlist.push({\n        idhead: 0,\n        id: id,\n        Custaccount: this.customerno,\n        vattype: this.vatselect,\n        paymenttype: this.selectpayment,\n        deliverytype: this.deliveryonclick,\n        locationno: this.locationno,\n        amount: this.sumallpricedis,\n        discount: this.sumalldiscount,\n        totalweigth: this.sumallweight,\n        note: this.notesoinput,\n        remark: this.remarksoinput,\n        dateshipping: this.dateshipping,\n        wh: wh,\n        locationde: this.locationnodelie,\n        saleid: this.Namelogin[0].salegroup,\n        filetype: '-',\n        filename: '-'\n      });\n    }\n    //เก็บข้อมูล Sale Header ที่ดึงมาจาก Database เก็บใส่ Array\n    selectedsetsaleheader(value) {\n      // this.vatselect=value[0].vattype;\n      // alert(this.vatselect);\n      this.filetype = value[0].filetype;\n      this.filename = value[0].filename;\n      this.idhead = value[0].idhead;\n      this.locationno = value[0].InvAddress;\n      this.locationnodelie = value[0].DeliveryAddress;\n      this.notesoinput = value[0].CustomerRef;\n      this.remarksoinput = value[0].remark;\n      for (var i = 0; i < value.length; i++) {\n        this.headerlist.push({\n          idhead: value[i].idhead,\n          id: value[i].id,\n          Custaccount: value[i].InvoiceAccount,\n          vattype: value[i].TaxGroup,\n          paymenttype: value[i].Payment,\n          deliverytype: value[i].DlvMode,\n          locationno: value[i].InvAddress,\n          amount: value[i].amount,\n          discount: value[i].discount,\n          totalweigth: value[i].totalweigh,\n          note: value[i].notesoinput,\n          remark: value[i].remark,\n          dateshipping: value[i].ReceiptDateRequested,\n          wh: value[i].InventLocationId,\n          locationde: value[i].DeliveryAddress,\n          saleid: this.Namelogin[0].salegroup,\n          filetype: value[i].filetype,\n          filename: value[i].filename\n        });\n      }\n      if (this.idsoedit != '' || this.idsoedit != undefined) {\n        // this.lineoderlist=[];\n        this.http.get(this.url + 'find_saleline/' + this.idsoedit).subscribe(res => {\n          if (res.length > 0) {\n            this.lineoderlist = [];\n            var d1, d2, d3;\n            // alert(JSON.stringify(res));\n            for (var i = 0; i < res.length; i++) {\n              if (res[i].disc1 == null) {\n                d1 = 0;\n              } else {\n                d1 = res[i].disc1;\n              }\n              if (res[i].disc2 == null) {\n                d2 = 0;\n              } else {\n                d2 = res[i].disc2;\n              }\n              if (res[i].disc3 == null) {\n                d3 = 0;\n              } else {\n                d3 = res[i].disc3;\n              }\n              this.lineoderlist.push({\n                idline: res[i].idline,\n                id: res[i].id,\n                linenum: res[i].linenum,\n                custaccount: res[i].CustAccount,\n                iditem: res[i].ItemId,\n                vattype: res[i].TaxItemGroup,\n                nameproduct: res[i].Name,\n                numberpcs: res[i].SalesQty,\n                unitid: res[i].SalesUnit,\n                packingitem: res[i].packqty,\n                totleweight: res[i].totalweight,\n                priceperunit: res[i].PriceUnit,\n                priceproduct: res[i].totaldisc + res[i].LineAmount,\n                discount1: res[i].IVZ_Percent1_CT,\n                discount2: res[i].IVZ_Percent2_CT,\n                discount3: res[i].IVZ_Percent3_CT,\n                sumdiscount: parseFloat(d1) + parseFloat(d2) + parseFloat(d3),\n                sumallprice: res[i].LineAmount,\n                wh: res[i].InventLocationId,\n                checkpcs: res[i].checkpcs,\n                disst: res[i].disstate,\n                eidtable: res[i].eidtable,\n                saleid: res[i].SalesGroup,\n                dateshipping: this.dateshipping + ' ' + this.time\n              });\n              ;\n              this.checkwh.push({\n                wh: res[i].InventLocationId,\n                eid: res[i].eidtable\n              });\n            }\n            this.idsaleoder = res[0].id;\n            if (this.lineoderlist.length > 0) {\n              this.sumalldiscount = 0;\n              this.sumallpricedis = 0;\n              this.sumallweight = 0;\n              for (var i = 0; i < this.lineoderlist.length; i++) {\n                this.sumalldiscount = this.sumalldiscount + parseFloat(this.lineoderlist[i].sumdiscount.toFixed(2));\n                this.sumallpricedis = this.sumallpricedis + parseFloat(this.lineoderlist[i].sumallprice.toFixed(2));\n                this.sumallweight = this.sumallweight + parseFloat(this.lineoderlist[i].totleweight.toFixed(2));\n              }\n            }\n          }\n        }, errar => {\n          status = errar.status;\n          alert('เกิดปัญหาในการ Process ข้อมูล กรุณาลองใหม่อีกครั้ง');\n          location.reload();\n          //this.openModal(true,'เกิดปัญหาในการ Process ข้อมูล กรุณาลองใหม่อีกครั้ง',false);\n        });\n      }\n    }\n    //ดึงข้อมูล Sale Line จาก Database มาแก้ไข\n    selectsaleoderline() {\n      this.setdefultUnit();\n      if (this.idsoedit == '' || this.idsoedit == undefined) {\n        this.setsaleheader(this.idsaleoder, this.inventlocationid);\n        this.getproductid(this.customerno);\n      } else {\n        this.getproductid(this.customerno);\n      }\n      if (this.lineoderlist.length < 1) {\n        if (this.deliveryonclick != '') {\n          if (this.idsoedit != '' || this.idsoedit != undefined) {\n            this.http.get(this.url + 'find_saleline/' + this.idsoedit).subscribe(res => {\n              var ch = 0;\n              if (res.length > 0) {\n                this.lineoderlist = []; // แม็ก 19/12/2018\n                // alert(JSON.stringify(res));\n                var d1, d2, d3;\n                for (var i = 0; i < res.length; i++) {\n                  if (res[i].disc1 == null) {\n                    d1 = 0;\n                  } else {\n                    d1 = res[i].disc1;\n                  }\n                  if (res[i].disc2 == null) {\n                    d2 = 0;\n                  } else {\n                    d2 = res[i].disc2;\n                  }\n                  if (res[i].disc3 == null) {\n                    d3 = 0;\n                  } else {\n                    d3 = res[i].disc3;\n                  }\n                  if (this.lineoderlist.length > 0) {\n                    for (var x = 0; x < this.lineoderlist.length; x++) {\n                      if (this.lineoderlist[x].iditem == res[i].ItemId && this.lineoderlist[x].numberpcs == res[i].SalesQty && this.lineoderlist[x].priceperunit == res[i].PriceUnit) {\n                        ch = 1;\n                      }\n                    }\n                    if (ch == 0) {\n                      this.lineoderlist.push({\n                        idline: res[i].idline,\n                        id: res[i].id,\n                        linenum: res[i].linenum,\n                        custaccount: res[i].CustAccount,\n                        iditem: res[i].ItemId,\n                        nameproduct: res[i].Name,\n                        vattype: res[i].TaxItemGroup,\n                        numberpcs: res[i].SalesQty,\n                        unitid: res[i].SalesUnit,\n                        packingitem: res[i].packqty,\n                        totleweight: res[i].totalweight,\n                        priceperunit: res[i].PriceUnit,\n                        priceproduct: res[i].CostPrice,\n                        discount1: res[i].IVZ_Percent1_CT,\n                        discount2: res[i].IVZ_Percent2_CT,\n                        discount3: res[i].IVZ_Percent3_CT,\n                        sumdiscount: parseFloat(d1) + parseFloat(d2) + parseFloat(d3),\n                        sumallprice: res[i].LineAmount,\n                        wh: res[i].InventLocationId,\n                        checkpcs: res[i].checkpcs,\n                        disst: res[i].disstate,\n                        eidtable: res[i].eidtable,\n                        saleid: res[i].SalesGroup,\n                        dateshipping: this.dateshipping + ' ' + this.time\n                      });\n                    } else {}\n                  } else {\n                    this.lineoderlist.push({\n                      idline: res[i].idline,\n                      id: res[i].id,\n                      linenum: res[i].linenum,\n                      custaccount: res[i].CustAccount,\n                      iditem: res[i].ItemId,\n                      vattype: res[i].TaxItemGroup,\n                      nameproduct: res[i].Name,\n                      numberpcs: res[i].SalesQty,\n                      unitid: res[i].SalesUnit,\n                      packingitem: res[i].packqty,\n                      totleweight: res[i].totalweight,\n                      priceperunit: res[i].PriceUnit,\n                      priceproduct: res[i].CostPrice,\n                      discount1: res[i].IVZ_Percent1_CT,\n                      discount2: res[i].IVZ_Percent2_CT,\n                      discount3: res[i].IVZ_Percent3_CT,\n                      sumdiscount: parseFloat(d1) + parseFloat(d2) + parseFloat(d3),\n                      sumallprice: res[i].LineAmount,\n                      wh: res[i].InventLocationId,\n                      checkpcs: res[i].checkpcs,\n                      disst: res[i].disstate,\n                      eidtable: res[i].eidtable,\n                      saleid: res[i].SalesGroup,\n                      dateshipping: this.dateshipping + ' ' + this.time\n                    });\n                    ;\n                  }\n                  this.checkwh.push({\n                    wh: res[i].InventLocationId,\n                    eid: res[i].eidtable\n                  });\n                }\n                this.idsaleoder = res[0].id;\n                if (this.lineoderlist.length > 0) {\n                  this.sumalldiscount = 0;\n                  this.sumallpricedis = 0;\n                  this.sumallweight = 0;\n                  for (var i = 0; i < this.lineoderlist.length; i++) {\n                    this.sumalldiscount = this.sumalldiscount + parseFloat(this.lineoderlist[i].sumdiscount.toFixed(2));\n                    this.sumallpricedis = this.sumallpricedis + parseFloat(this.lineoderlist[i].sumallprice.toFixed(2));\n                    this.sumallweight = this.sumallweight + parseFloat(this.lineoderlist[i].totleweight.toFixed(2));\n                  }\n                }\n              }\n            }, errar => {\n              alert('เกิดปัญหาในการ Process ข้อมูล กรุณาลองใหม่อีกครั้ง');\n              location.reload();\n              //this.openModal(true,'เกิดปัญหาในการ Process ข้อมูล กรุณาลองใหม่อีกครั้ง',false);\n            });\n          }\n        } else {\n          alert('กรุณาเลือก การขนส่ง');\n          //this.openModal(true,'กรุณาเลือก การขนส่ง',false);\n        }\n      }\n    }\n    //เก็บข้อมูล วันที่\n    clickdate(data) {\n      var month = this.bsValue.getMonth() + 1;\n      if (month < 10) {\n        this.dateshipping = this.bsValue.getFullYear() + '-' + '0' + month + '-' + this.bsValue.getUTCDate();\n      } else {\n        this.dateshipping = this.bsValue.getFullYear() + '-' + month + '-' + this.bsValue.getUTCDate();\n      }\n    }\n    //เก็บข้อมูล ประเภทการ ขนส่งสินค้า\n    selectdelivery(value) {\n      this.deliveryonclick = value;\n    }\n    //เก็บข้อมูล ประเภทการชำระเงิน\n    selectpaymentfn(value) {\n      this.selectpayment = value;\n    }\n    //ตั้งค่าข้อมูล ประเภท ขนส่งสินค้า\n    mappaymenttype(paymenttype) {\n      this.selectpayment = paymenttype;\n      /*if(paymenttype==='เงินสด') {\n        this.dis3=3.00;\n        this.selectpayment=paymenttype\n        this.paymentlist=[{'paymenttype':'เงินสด-เครดิต'},{'paymenttype':'เครดิต'},{'paymenttype':'เงินสด'}];\n      } else if (paymenttype==='เครดิต') {\n        this.selectpayment=paymenttype\n        this.paymentlist=[{'paymenttype':'เงินสด-เครดิต'},{'paymenttype':'เงินสด'},{'paymenttype':'เครดิต'}];\n      }\n      */\n      this.http.get(this.url + 'paymentlist').subscribe(res => {\n        this.paymentlist = [];\n        if (res.length > 0) {\n          for (var i = 0; i < res.length; i++) {\n            if (res[i].paymtermid == paymenttype) {\n              this.paymentlist.push({\n                paymtermid: res[i].paymtermid,\n                paymenttype: res[i].paymenttype\n              });\n            } else {\n              this.paymentlist.unshift({\n                paymtermid: res[i].paymtermid,\n                paymenttype: res[i].paymenttype\n              });\n            }\n          }\n        }\n        this.getwarehouse();\n      });\n    }\n    //ตั้งค่าข้อมูล ประเภทภาษี\n    maptaxgrouptype(vattype) {\n      if (this.idsoedit == undefined) {\n        if (vattype === 'VAT') {\n          this.vatselect = vattype;\n          this.vatlist = [{\n            'vattype': 'VAT ไม่ยื่น'\n          }, {\n            'vattype': 'VAT'\n          }];\n        } else if (vattype === 'NO VAT') {\n          this.vatselect = vattype;\n          this.vatlist = [{\n            'vattype': 'VAT'\n          }, {\n            'vattype': 'VAT ไม่ยื่น'\n          }];\n        } else if (vattype === 'VAT ไม่ยื่น') {\n          this.vatselect = vattype;\n          this.vatlist = [{\n            'vattype': 'VAT'\n          }, {\n            'vattype': 'VAT ไม่ยื่น'\n          }];\n        }\n      }\n    }\n    //ค้าหาที่อยู่ จากชื่อ หรือ รหัสลูกค้า\n    Searchcustomeraddress(valuecustomer) {\n      this.daddress = [];\n      this.inaddress = [];\n      this.http.get(this.url + 'customer_list' + '/%20/%20/' + valuecustomer + '/%20/%20').subscribe(res => {\n        if (res.length > 0) {\n          this.customerno = res[0].accountnum;\n          this.getaddressdary(res[0].accountnum);\n          this.getaddressinv(res[0].accountnum);\n          this.locationno = res[0].locationno;\n          this.locationnodelie = res[0].locationno;\n        }\n      }, error => {\n        alert('เกิดปัญหาในการ Process ข้อมูล กรุณาลองใหม่อีกครั้ง');\n        //this.openModal(true,'เกิดปัญหาในการ Process ข้อมูล กรุณาลองใหม่อีกครั้ง',false);\n      });\n    }\n    //เก็บข้อมูล เลขที่ ที่อยู่\n    setlocationno(value) {\n      this.locationno = value;\n    }\n    //เก็บข้อมูล ภาษี\n    selectvat(value) {\n      if (value === 'VAT ไม่ยื่น') {\n        this.notesoinput = 'บิลเขียว';\n      } else {\n        this.notesoinput = '';\n      }\n      this.vatselect = value;\n      //alert(value);\n    }\n    //อัพเดทข้อมูล การรัน เลขที่ SaleOder\n    updaterunnumber(value) {\n      var getdate = '';\n      var day = this.today.getDate();\n      var month = this.today.getMonth() + 1;\n      var year = this.today.getFullYear();\n      var date = '';\n      if (day.toString().length > 1) {\n        getdate = day.toString();\n      } else {\n        getdate = '0' + day.toString();\n      }\n      if (month.toString().length > 1) {\n        date = year.toString() + '-' + month.toString() + '-' + getdate;\n      } else {\n        date = year.toString() + '-0' + month.toString() + '-' + getdate;\n      }\n      var urlpost = `${this.url}${'updaterunnumber'}/${value[0].text}/${value[0].saleid}/${value[0].year}/${value[0].month}/${value[0].runnumber}`;\n      this.http.post(urlpost, '').subscribe(res => {\n        if (res == true) {\n          //this.getallidsaleoder=[];\n          //location.reload();\n          this.loadrunnumberid(date);\n          this.actionbtn = false;\n          //this.openModal(true,'บันทึกข้อมูลเสร็จสิ้น',true);\n          this.company = '';\n          // this.idsoedit=undefined;\n        } else {\n          alert('Update So Id Fail !!!!');\n        }\n      });\n    }\n    /*/so/createso/:id/:Custaccount/:vattype/:paymenttype/:deliverytype/:locationno/:amount/:discount/:totalweight/:note/:remark'\n       */\n    //ลบข้อมูล SaleOder เมื่อมีการ อัพเดทข้อมูล\n    deleteebeforupdate() {\n      //alert(this.vatselect);\n      //return;\n      //alert(JSON.stringify(this.lineoderlist));\n      //console.log(JSON.stringify(this.lineoderlist));\n      //return;\n      if (this.idsoedit != undefined) {\n        var urlpost = `${this.url}${'delete_sale_line'}/${this.idsoedit}/${this.Namelogin[0].salegroup}`;\n        this.http.post(urlpost, '').subscribe(res => {\n          this.savesaleoderlist();\n        }, error => {\n          alert('พบข้อผิดพลาด กรุณาลองใหม่อีกครั้ง');\n          return;\n        });\n      } else {\n        this.savesaleoderlist();\n      }\n    }\n    clrscrsaleheader() {\n      this.model = null;\n      this.idsaleoder = '';\n      this.customerno = '';\n      this.vatselect = '';\n      this.selectpayment = '';\n      this.deliveryonclick = '';\n      this.locationno = '';\n      this.sumallpricedis = 0;\n      this.sumalldiscount = 0;\n      this.sumallweight = 0;\n      this.inaddress = [];\n      this.daddress = [];\n      this.notesoinput = '';\n      this.remarksoinput = '';\n      //alert('บันทึกข้อมูลเสร็จสิ้น');\n    }\n    // บันทึกข้อมูล Sale Oder Header\n    savesaleoderlist() {\n      this.dissave = false;\n      var noteina = '';\n      var noteinb = '';\n      var remarka = '';\n      var remarkb = '';\n      this.actionbtn = true;\n      if (this.deliveryonclick == '') {\n        alert('กรุณาเลือก การขนส่ง');\n        this.actionbtn = false;\n        return;\n        //this.openModal(true,'กรุณาเลือก การขนส่ง',false);\n      } else {\n        if (this.remarksoinput == '') {\n          this.remarksoinput = 'N';\n          remarkb = 'N';\n        } else {\n          remarka = this.remarksoinput.replace('/', '-');\n          remarkb = remarka.replace('%', 'เปอร์เซ็น');\n        }\n        if (this.notesoinput == '') {\n          this.notesoinput = 'N';\n          noteinb = 'N';\n        } else {\n          this.notesoinput.replace('/', '-');\n          noteina = this.notesoinput.replace('/', '-');\n          noteinb = noteina.replace('%', 'เปอร์เซ็น');\n        }\n        if (this.wh == '') {\n          this.wh = '';\n        }\n        if (this.lineoderlist.length == 0) {\n          alert('กรุณาป้อน รายการสินค้า');\n          this.actionbtn = false;\n          return;\n        }\n        if (this.Namelogin[0].salegroup == 'admin') {} else {\n          this.usseredit = this.Namelogin[0].salegroup;\n        }\n        //alert(this.locationnodelie+'/'+this.idsoedit);\n        //this.idforshow=this.idsaleoder;\n        //alert(this.idsaleoder+'/'+this.customerno+'/'+this.vatselect+'/'+this.selectpayment+'/'+this.deliveryonclick+'/'+this.locationnodelie+'/'+this.sumallpricedis+'/'+this.sumalldiscount+'/'+this.sumallweight+'/'+this.notesoinput+'/'+this.remarksoinput+'/'+this.dateshipping+'/'+this.Namelogin[0].salegroup+'/'+this.wh+'/'+this.locationno);\n        //return;\n        this.dateshipping = this.bsValue.getFullYear() + '-' + (parseInt(this.bsValue.getMonth().toString()) + 1) + '-' + this.bsValue.getDate();\n        //alert('INV'+this.locationno+'DIV'+this.locationnodelie);\n        var body = {\n          Data: [{\n            id: this.idsaleoder,\n            Custaccount: this.customerno,\n            vattype: this.vatselect,\n            paymenttype: this.selectpayment,\n            deliverytype: this.deliveryonclick,\n            locationno: this.locationno,\n            amount: this.sumallpricedis,\n            discount: this.sumalldiscount,\n            totalweight: this.sumallweight,\n            note: noteinb,\n            remark: remarkb,\n            dateshipping: this.dateshipping + ' ' + this.time,\n            saleid: this.usseredit,\n            wh: this.wh,\n            locationde: this.locationnodelie,\n            idhead: this.idhead,\n            filetype: this.filetype,\n            filename: this.filename\n          }]\n        };\n        localStorage.setItem('SoidOld', this.idsaleoder);\n        //alert(this.locationno+'/'+this.locationnodelie);\n        var urlpost = `${this.url}${'createsonew'}`;\n        const headerOptions = new HttpHeaders({\n          'Content-Type': 'application/json'\n        });\n        //var urlpost='https://localhost:5001/api/values/CreateSoHeader';\n        if (this.daddress != null) {\n          this.http.post(urlpost, body).subscribe(res => {\n            if (res == true) {\n              if (this.idsoedit == undefined) {\n                this.updaterunnumber(this.getallidsaleoder);\n              }\n              this.savesalelinefn();\n              this.sumalldiscount = 0;\n              this.sumallpricedis = 0;\n              this.sumallweight = 0;\n              this.remarksoinput = '';\n              this.notesoinput = '';\n            } else {}\n          }, err => {\n            alert('ไม่สามารถบันทึกข้อมูลได้กรุณาลองใหม่อีก ครั้ง');\n            this.actionbtn = false;\n            return;\n          });\n        } else {\n          alert('กรุณาเลือกลูกค้า');\n          this.actionbtn = false;\n          return;\n          //this.openModal(true,'กรุณาเลือกลูกค้า',false);\n        }\n      }\n    }\n    /*\n    /so/savesaleline/:id/:linenumber/:custaccount/:itemid/:saleqty/:packqty/:vattype/:price/:percent1/:percent2/:percent3/:lineweight' */\n    //บันทึกข้อมูล SaleLine แบบยังไม่แยก คลังสินค้า\n    savesalelinefn() {\n      console.log(JSON.stringify(line));\n      this.dateshipping = this.bsValue.getFullYear() + '-' + (parseInt(this.bsValue.getMonth().toString()) + 1) + '-' + this.bsValue.getDate();\n      var line = [];\n      if (this.lineoderlist.length > 0) {\n        for (var i = 0; i < this.lineoderlist.length; i++) {\n          line.push({\n            id: this.lineoderlist[i].id,\n            linenum: this.lineoderlist[i].linenum,\n            custaccount: this.customerno,\n            iditem: this.lineoderlist[i].iditem,\n            numberpcs: this.lineoderlist[i].numberpcs,\n            packingitem: this.lineoderlist[i].packingitem,\n            vattype: this.vatselect,\n            priceperunit: this.lineoderlist[i].priceperunit,\n            discount1: this.lineoderlist[i].discount1,\n            discount2: this.lineoderlist[i].discount2,\n            discount3: this.lineoderlist[i].discount3,\n            totleweight: this.lineoderlist[i].totleweight,\n            dateshipping: this.dateshipping + ' ' + this.time,\n            saleid: this.lineoderlist[i].saleid,\n            checkpcs: this.lineoderlist[i].checkpcs,\n            disst: this.lineoderlist[i].disst,\n            eidtable: this.lineoderlist[i].eidtable,\n            idline: this.lineoderlist[i].idline\n          });\n        }\n      } else {\n        return;\n      }\n      var i = 0;\n      var urlpost = `${this.url}${'savesalelinenew'}`;\n      this.http.post(urlpost, {\n        Data: line\n      }).subscribe(res => {\n        if (res == true) {\n          this.dissave = false;\n          this.clrscrlinelist();\n          this.company = '';\n          this.clrscrsaleheader();\n          this.headerlist = [];\n          if (this.Namelogin[0].accountnum != undefined) {\n            this.getcustomersalefunction(this.Namelogin[0].accountnum);\n          }\n          if (this.idsoedit == undefined) {\n            alert('บันทึกข้อมูลเสร็จสิ้น');\n            this.getallidsaleoder = [];\n            location.reload();\n            this.lineoderlist = [];\n            this.dissave = false;\n          } else {\n            this.closeeditlistoder();\n            alert('บันทึกข้อมูลเสร็จสิ้น');\n            this.dissave = false;\n            this.actionbtn = false;\n            this.clickbtnclose = false;\n            this.lineoderlist = [];\n          }\n        }\n        /*if(this.lineoderlist.length==0) {\n        \n        \n        \n          //this.router.navigate(['/soreview']);\n        } else if(this.idsoedit!=undefined && this.lineoderlist.length==0){\n        \n          this.clrscrlinelist();\n          this.company='';\n          this.clrscrsaleheader();\n          if(this.Namelogin[0].accountnum != undefined){\n            this.getcustomersalefunction(this.Namelogin[0].accountnum);\n            }\n          alert('บันทึกข้อมูลเสร็จสิ้น');\n          this.clickbtnclose=false;\n        this.actionbtn=false;\n          //this.openModal(true,'บันทึกข้อมูลเสร็จสิ้น',true);\n          this.headerlist=[];\n          //this.router.navigate(['/soreview']);\n        }*/\n        this.sumalldiscount = 0;\n        this.sumallpricedis = 0;\n        this.sumallweight = 0;\n        this.x++;\n      });\n      /*for (var i=0; i<this.lineoderlist.length; i++) {\n      \n        var urlpost= `${ this.url }${ '/so/savesaleline' }/${ this.lineoderlist[i].id}/${ i+1 }/${ this.inaddress[0].accountnum }/${ this.lineoderlist[i].iditem}/${ this.lineoderlist[i].numberpcs}/${ this.lineoderlist[i].packingitem}/${ this.vatselect }/${ this.lineoderlist[i].priceperunit}/${ this.lineoderlist[i].discount1 }/${ this.lineoderlist[i].discount2 }/${ this.lineoderlist[i].discount3 }/${ this.lineoderlist[i].totleweight}`;\n       alert(urlpost);\n        this.http.post(urlpost,'').subscribe(res =>{\n        if(res !=  true) {\n          alert('SaveLineFail!!!!');\n        }\n        });\n      \n      }*/\n    }\n    //ดึงข้อมูลราคาสินค้าจากรหัสสินค้า\n    Searchproductlist(productid) {\n      var body = {\n        inventlocationid: '',\n        itemgroupid: '',\n        itemid: productid,\n        catname: '',\n        name: ''\n      };\n      this.http.post(this.url + 'product_list', body).subscribe(res => {\n        if (res.length > 0) {\n          this.eidtable = res[0].eidtable;\n          if (res[0].eidtable == 0) {\n            this.ennableeditprice = true;\n          } else {\n            this.ennableeditprice = false;\n          }\n          this.productlist = res;\n          this.checkpromotionfunction(productid);\n          for (var i = 0; i < this.promotionlist.length; i++) {\n            if (this.promotionlist[i].amount > 0) {\n              this.priceperunit = this.promotionlist[i].amount.toString();\n            }\n          }\n          this.iditem = res[0].itemid;\n          this.numproduct = parseInt(JSON.stringify(res[0].taxpackagingqty));\n          this.productname = res[0].name;\n          this.weightproduct = res[0].netweight;\n        }\n      });\n    }\n    setdefultUnit() {\n      if (this.setpackde == 1) {\n        this.getcheckpcs = 'U';\n      } else {\n        this.getcheckpcs = 'P';\n      }\n    }\n    //(focusin)=\"Searchitem()\"\n    //ค้นหาสินค้าจากรหัสสินค้า\n    Searchitem() {\n      var _this = this;\n      return _asyncToGenerator(function* () {\n        _this.numberproductsearch = '';\n        var forword = '';\n        var low = '';\n        _this.priceperunit = '0';\n        _this.freeitem = false;\n        _this.selectedunit = false;\n        //alert(this.getcheckpcs);\n        //alert(this.productidsearch.itemid);\n        if (_this.productidsearch.itemid != undefined && _this.productidsearch.itemid != '') {\n          //alert(1);\n          forword = _this.productidsearch.itemid;\n          low = _this.productidsearch.itemid;\n        } else if (_this.productidsearch.itemid == undefined) {\n          forword = _this.showitem;\n          low = _this.showitem;\n        } else {}\n        //alert(forword+'/'+this.showitem);\n        /*if(this.showitem===\"รหัสสินค้า\"){\n        \n        } else {\n          forword=this.showitem;\n          low=this.showitem;\n        }*/\n        /*this.iditem=idproduct;*/\n        //alert(forword);\n        var body = {\n          itemid: forword,\n          itemidor: low\n        };\n        yield _this.http.post(_this.url + 'uom', body).subscribe(res => {\n          _this.unitlist = [];\n          if (res.length > 0) {\n            _this.checkadditem = false;\n            _this.inventlocationid = res[0].inventlocationid;\n            _this.checkunitprice = res[1].unit;\n            for (var i = 0; i < res.length; i++) {\n              if (res[i].unit == _this.getcheckpcs) {\n                _this.unitlist.push({\n                  inventlocationid: res[i].inventlocationid,\n                  unitid: res[i].unitid,\n                  unit: res[i].unit,\n                  itemid: res[i].itemid\n                });\n              } else {\n                _this.unitlist.unshift({\n                  inventlocationid: res[i].inventlocationid,\n                  unitid: res[i].unitid,\n                  unit: res[i].unit,\n                  itemid: res[i].itemid\n                });\n              }\n            }\n            _this.selectunitid = res[0].unitid;\n            _this.packingid = res[1].unitid;\n            _this.Searchproductlist(res[0].itemid);\n            _this.viewsstockitem(res[0].itemid);\n          } else {\n            /*this.openModal(true,'ไม่พบสินค้า',false);*/\n          }\n        });\n      })();\n    }\n    LoadAllstock() {\n      var _this2 = this;\n      return _asyncToGenerator(function* () {\n        yield _this2.http.get('http://119.59.112.47/SoAPI/api/values/GetStockCountGetAllGroup').subscribe(res => {\n          _this2.StockAllList = res;\n        });\n      })();\n    }\n    Realtimeinstock(itemid) {\n      var stock = [];\n      if (this.StockAllList.length > 0) {\n        stock = [this.StockAllList.find(x => x.itemid == itemid)];\n        return stock[0].avastock;\n      }\n    }\n    viewsstockitem(itemid) {\n      this.Instock = this.Realtimeinstock(itemid) == '0' ? '0' : this.Realtimeinstock(itemid);\n      // this.http.get<any[]>('http://119.59.112.47/SoAPI/api/values/getstockcountget/'+itemid).subscribe(res=>{\n      //   res[0].avastock;\n      // //alert(res);\n      // });\n    }\n    Searchitemclick(value) {\n      //alert(value);\n      var body = {\n        itemid: value,\n        itemidor: ''\n      };\n      this.http.post(this.url + 'uom', body).subscribe(res => {\n        this.unitlist = [];\n        if (res.length > 0) {\n          this.checkadditem = false;\n          clearInterval(this.loaduom);\n          this.inventlocationid = res[0].inventlocationid;\n          this.checkunitprice = res[1].unit;\n          for (var i = 0; i < res.length; i++) {\n            if (res[i].unit == this.getcheckpcs) {\n              this.unitlist.push({\n                inventlocationid: res[i].inventlocationid,\n                unitid: res[i].unitid,\n                unit: res[i].unit,\n                itemid: res[i].itemid\n              });\n            } else {\n              this.unitlist.unshift({\n                inventlocationid: res[i].inventlocationid,\n                unitid: res[i].unitid,\n                unit: res[i].unit,\n                itemid: res[i].itemid\n              });\n            }\n          }\n          this.selectunitid = res[0].unitid;\n          this.packingid = res[1].unitid;\n          this.Searchproductlist(res[0].itemid);\n          this.viewsstockitem(res[0].itemid);\n        } else {\n          /*this.openModal(true,'ไม่พบสินค้า',false);*/\n        }\n      });\n      /*this.iditem=idproduct;*/\n    }\n    Searchcustomersalekey(event) {\n      this.getcustomersalefunction(event.target.value);\n    }\n    //ค้นหาลูกค้าจากชื่อหรือ รหัสลูกค้า พร้อมเรียก Function ดึงที่อยู่\n    getcustomersalefunction(value) {\n      this.daddress = [];\n      this.inaddress = [];\n      this.dissave = true;\n      this.http.get(this.url + 'customer_list/%20/%20/' + value + '/%20/%20').subscribe(res => {\n        //clearInterval(this.setloadaddress);\n        //alert(value);\n        if (res.length > 0) {\n          if (this.idsoedit == undefined) {\n            if (res[0].DLVMODE == '01-ขนส่ง' || res[0].DLVMODE == '03-ขนส่ง') {\n              this.deliveryonclick = 'ขนส่ง';\n              this.deliverytype = [{\n                'deliver': 'รถบริษัท'\n              }, {\n                'deliver': 'รับเอง'\n              }, {\n                'deliver': 'ขนส่ง'\n              }];\n            } else if (res[0].DLVMODE == '02รถบริษัท') {\n              this.deliveryonclick = 'รถบริษัท';\n              this.deliverytype = [{\n                'deliver': 'รับเอง'\n              }, {\n                'deliver': 'ขนส่ง'\n              }, {\n                'deliver': 'รถบริษัท'\n              }];\n            } else if (res[0].DLVMODE == '03-รับเอง') {\n              this.deliveryonclick = 'รับเอง';\n              this.deliverytype = [{\n                'deliver': 'รถบริษัท'\n              }, {\n                'deliver': 'ขนส่ง'\n              }, {\n                'deliver': 'รับเอง'\n              }];\n            } else {\n              this.deliveryonclick = '';\n              this.deliverytype = [{\n                'deliver': 'รถบริษัท'\n              }, {\n                'deliver': 'รับเอง'\n              }, {\n                'deliver': 'ขนส่ง'\n              }, {\n                'deliver': 'ประเภทการขนส่ง'\n              }];\n            }\n            this.remarksoinput = res[0].MEMO;\n            this.locationno = res[0].locationno;\n            this.locationnodelie = res[0].locationno;\n          }\n          //alert(res[0].DLVMODE);\n          clearInterval(this.setloadaddress);\n          this.customerno = res[0].accountnum;\n          this.getaddressdary(res[0].accountnum);\n          this.getaddressinv(res[0].accountnum);\n          if (this.Namelogin[0].accountnum != undefined) {\n            this.company = res[0].name;\n          }\n          this.accoutnreration = res[0].linedisc;\n          this.pricegroup = res[0].pricegroup;\n          this.accountnum = res[0].accountnum;\n          //this.locationno=this.inaddress[0].locationno;\n          this.maptaxgrouptype(res[0].vattype);\n          if (this.idsoedit == undefined) {\n            this.mappaymenttype(res[0].paymtermid);\n          } else {}\n          //this.selectpayment=res[0].paymtermid;\n          //alert(this.selectpayment);\n          this.selectedpaymentmap = res[0].paymtermid;\n          //alert(this.selectpayment);\n          this.openModal(false, 'กำลังโหลดข้อมูล กรุณารอสักครู่....', false);\n        }\n      }, error => {\n        this.openModal(true, 'กำลังโหลดข้อมูล กรุณารอสักครู่....', false);\n        status = error.status;\n      });\n      this.waitsave = true;\n    }\n    Searchcustomersalekeychen(event) {\n      this.getcustomersalefunction(event.target.value);\n    }\n    //ดึงที่อยู่ วางบิล\n    getaddressinv(accountnum) {\n      this.inaddress = [];\n      var body = {\n        accountnum: accountnum\n      };\n      this.http.post(this.url + 'get_invaddress', body).subscribe(res => {\n        if (res.length > 0) {\n          this.inaddress = [];\n          for (var i = 0; i < res.length; i++) {\n            if (this.idsoedit == undefined) {\n              if (res[i].DPRIMARY == 1) {\n                this.locationno = res[i].locationno;\n                this.inaddress.push({\n                  name: res[i].description,\n                  accountnum: res[i].accountnum,\n                  address: res[i].address,\n                  locationno: res[i].locationno\n                });\n              } else {\n                this.inaddress.unshift({\n                  name: res[i].description,\n                  accountnum: res[i].accountnum,\n                  address: res[i].address,\n                  locationno: res[i].locationno\n                });\n              }\n            } else {\n              if (res[i].locationno == this.locationno) {\n                this.locationno = res[i].locationno;\n                this.inaddress.push({\n                  name: res[i].description,\n                  accountnum: res[i].accountnum,\n                  address: res[i].address,\n                  locationno: res[i].locationno\n                });\n              } else {\n                this.inaddress.unshift({\n                  name: res[i].description,\n                  accountnum: res[i].accountnum,\n                  address: res[i].address,\n                  locationno: res[i].locationno\n                });\n              }\n            }\n          }\n          this.locationno = this.inaddress[this.inaddress.length - 1].locationno;\n        }\n      });\n      //this.locationno=this.inaddress[this.inaddress.length-1].locationno;\n    }\n    //ดึงที่อยู่ขนส่งสินค้า\n    getaddressdary(accountnum) {\n      this.daddress = [];\n      var chedlv = 0;\n      var body = {\n        accountnum: accountnum\n      };\n      this.http.post(this.url + 'get_dlvaddress', body).subscribe(res => {\n        this.daddress = [];\n        if (res.length > 0) {\n          for (var x = 0; x < res.length; x++) {\n            if (res[x].DPRIMARY == 2) {\n              chedlv++;\n            }\n          }\n          //alert(chedlv+'/ddsds');\n          for (var i = 0; i < res.length; i++) {\n            if (this.idsoedit == undefined) {\n              if (chedlv == 1) {\n                //alert('1'+chedlv);\n                if (res[i].DPRIMARY == 2) {\n                  this.locationnodelie = res[i].locationno;\n                  this.daddress.push({\n                    name: res[i].description,\n                    accountnum: res[i].accountnum,\n                    address: res[i].address,\n                    locationno: res[i].locationno\n                  });\n                } else {\n                  this.daddress.unshift({\n                    name: res[i].description,\n                    accountnum: res[i].accountnum,\n                    address: res[i].address,\n                    locationno: res[i].locationno\n                  });\n                }\n              } else {\n                //alert('2'+chedlv);\n                if (res[i].DPRIMARY == 1) {\n                  this.locationnodelie = res[i].locationno;\n                  this.daddress.push({\n                    name: res[i].description,\n                    accountnum: res[i].accountnum,\n                    address: res[i].address,\n                    locationno: res[i].locationno\n                  });\n                } else {\n                  this.daddress.unshift({\n                    name: res[i].description,\n                    accountnum: res[i].accountnum,\n                    address: res[i].address,\n                    locationno: res[i].locationno\n                  });\n                }\n              }\n            } else {\n              if (res[i].locationno == this.locationnodelie) {\n                this.locationnodelie = res[i].locationno;\n                this.daddress.push({\n                  name: res[i].description,\n                  accountnum: res[i].accountnum,\n                  address: res[i].address,\n                  locationno: res[i].locationno\n                });\n              } else {\n                this.daddress.unshift({\n                  name: res[i].description,\n                  accountnum: res[i].accountnum,\n                  address: res[i].address,\n                  locationno: res[i].locationno\n                });\n              }\n            }\n          }\n          this.locationnodelie = this.daddress[this.daddress.length - 1].locationno;\n        }\n      });\n      //alert(this.locationnodelie);\n      //this.locationnodelie=this.daddress[this.daddress.length-1].locationno;\n    }\n    selectlocationde(value) {\n      this.locationnodelie = value;\n      //alert(this.locationnodelie);\n    }\n    //คำนวนโปรโมชั่น\n    checkpromotionfunction(value) {\n      var stbefor = this.accoutnreration.replace('+', '');\n      var st = stbefor.replace('%', '');\n      //alert(st+'/'+st.length);\n      this.promotionlist = [];\n      if (this.accoutnreration == '') {\n        this.accoutnreration = '';\n      }\n      if (this.pricegroup == '') {\n        this.pricegroup = '';\n      }\n      if (st == '' || st == '20') {\n        st = '';\n      }\n      var body = {\n        itemid: value,\n        accountrelation: st,\n        pricegroup: this.pricegroup,\n        accountnum: this.accountnum\n      };\n      this.http.post(this.url + 'map_promotion', body).subscribe(res => {\n        if (res.length > 0) {\n          this.upperdis = res[0].friendlyname;\n          for (var i = 0; i < res.length; i++) {\n            this.promotionlist.push({\n              itemrelation: res[i].itemrelation,\n              accountrelation: res[i].accountrelation,\n              accountcode: res[i].accountcode,\n              quantityamountfrom: res[i].quantityamountfrom,\n              quantityamountto: res[i].quantityamountto,\n              amount: res[i].amount,\n              percent1: res[i].percent1,\n              percent2: res[i].percent2,\n              relation: res[i].relation,\n              percent3: res[i].percent3\n            });\n          }\n        }\n        this.http.post(this.url + 'map_promotion_price', body).subscribe(res => {\n          if (res.length > 0) {\n            //alert(JSON.stringify(res));\n            for (var x = 0; x < res.length; x++) {\n              this.promotionlist.push({\n                itemrelation: res[x].itemrelation,\n                accountrelation: res[x].accountrelation,\n                accountcode: res[x].accountcode,\n                quantityamountfrom: res[x].quantityamountfrom,\n                quantityamountto: res[x].quantityamountto,\n                amount: res[x].amount,\n                percent1: res[x].percent1,\n                percent2: res[x].percent2,\n                relation: res[x].relation,\n                percent3: res[x].percent3\n              });\n            }\n          }\n        });\n      });\n    }\n    //คำนวน ราคาสินค้าจาก จำนวน\n    checknumproduct(event) {\n      var packitem = event;\n      var pcsitem = packitem * this.numproduct;\n      //alert(pcsitem);\n      if (this.getcheckpcs === 'P') {\n        this.allpcs = pcsitem;\n        this.numpackingitem = event;\n        this.checkpacking = 0;\n      } else {\n        this.allpcs = packitem;\n        this.numpackingitem = packitem / this.numproduct;\n        this.checkpacking = packitem % this.numproduct;\n        pcsitem = packitem;\n      }\n      //alert(pcsitem);\n      //alert(this.getcheckpcs);\n      //this.checkpacking=packitem%this.numproduct;\n      var disc1 = 0.00,\n        disc2 = 0.00,\n        disc3 = 0.00,\n        pricec = 0.00,\n        pricecpa = 0.00;\n      var priceperdis1 = 0,\n        priceperdis2 = 0,\n        priceperdis3 = 0,\n        sumdis = 0.00,\n        allprice = 0.00,\n        sumpricebedis1 = 0,\n        sumpricebedis2 = 0,\n        sumpricebedis3 = 0,\n        finalpri,\n        sumdisdd = 0;\n      if (this.iditem == '') {\n        alert('กรุณาป้อนรหัสสินค้า');\n        return;\n        //this.openModal(true,'กรุณาป้อนรหัสสินค้า',false);\n      } else {\n        if (this.promotionlist.length > 0) {\n          //alert(JSON.stringify(this.promotionlist));\n          for (var i = 0; i < this.promotionlist.length; i++) {\n            if (this.promotionlist[i].amount > 0 && this.promotionlist[i].relation == '4' && pcsitem >= this.promotionlist[i].quantityamountfrom && pcsitem < this.promotionlist[i].quantityamountto) {\n              this.checkadditem = true;\n              //alert(1);\n              pricec = this.promotionlist[i].amount;\n            } else if (this.promotionlist[i].amount > 0 && this.promotionlist[i].relation == '4' && pcsitem >= this.promotionlist[i].quantityamountfrom && pcsitem > this.promotionlist[i].quantityamountto) {\n              this.checkadditem = true;\n              //alert(2);\n              pricec = this.promotionlist[i].amount;\n            } else if (this.promotionlist[i].amount > 0 && this.promotionlist[i].relation == '4' && pcsitem >= this.promotionlist[i].quantityamountfrom && pcsitem < this.promotionlist[i].quantityamountto) {\n              this.checkadditem = true;\n              //alert(3);\n              //pricec =this.promotionlist[i].amount;\n            } else if (this.promotionlist[i].amount > 0 && this.promotionlist[i].relation == '4' && pcsitem >= this.promotionlist[i].quantityamountfrom && pcsitem > this.promotionlist[i].quantityamountto) {\n              this.checkadditem = true;\n              // alert(4);\n              //pricec =this.promotionlist[i].amount;\n            } else if (this.promotionlist[i].amount > 0 && this.promotionlist[i].relation == '4' && pcsitem >= this.promotionlist[i].quantityamountfrom && pcsitem < this.promotionlist[i].quantityamountto) {\n              this.checkadditem = true;\n              //alert(5);\n              //pricec =this.promotionlist[i].amount;\n            } else if (this.promotionlist[i].amount > 0 && this.promotionlist[i].relation == '4' && pcsitem >= this.promotionlist[i].quantityamountfrom && pcsitem > this.promotionlist[i].quantityamountto) {\n              this.checkadditem = true;\n              //alert(6);\n              pricec = this.promotionlist[i].amount;\n            } else if (this.promotionlist[i].amount > 0 && this.promotionlist[i].relation == '4' && this.promotionlist[i].quantityamountfrom == 0 && this.promotionlist[i].quantityamountto == 0) {\n              this.checkadditem = true;\n              //alert(7);\n              pricec = this.promotionlist[i].amount;\n            }\n            if (this.accoutnreration === '%20') {\n              this.accoutnreration = '';\n            }\n            if (pcsitem >= this.promotionlist[i].quantityamountfrom && pcsitem < this.promotionlist[i].quantityamountto && this.promotionlist[i].relation == '5') {\n              disc1 = this.promotionlist[i].percent1;\n              disc2 = this.promotionlist[i].percent2;\n              disc3 = this.promotionlist[i].percent3;\n              this.olddis1 = this.promotionlist[i].percent1;\n              this.olddis2 = this.promotionlist[i].percent2;\n              this.olddis3 = this.promotionlist[i].percent3;\n              //alert(1);\n              //alert(disc3);\n            } else if (pcsitem >= this.promotionlist[i].quantityamountfrom && this.promotionlist[i].quantityamountto == 0 && this.promotionlist[i].relation == '5') {\n              //alert(2);\n              disc1 = this.promotionlist[i].percent1;\n              disc2 = this.promotionlist[i].percent2;\n              disc3 = this.promotionlist[i].percent3;\n              //alert(disc1+'/'+pcsitem);\n              this.olddis1 = this.promotionlist[i].percent1;\n              this.olddis2 = this.promotionlist[i].percent2;\n              this.olddis3 = this.promotionlist[i].percent3;\n            }\n            //  else if( pcsitem >= this.promotionlist[i].quantityamountfrom && pcsitem > this.promotionlist[i].quantityamountto  && this.promotionlist[i].relation=='5') {\n            //  disc1=this.promotionlist[i].percent1;\n            //   disc2=this.promotionlist[i].percent2;\n            //   disc3=this.promotionlist[i].percent3;\n            //   this.olddis1=this.promotionlist[i].percent1;\n            //   this.olddis2=this.promotionlist[i].percent2;\n            //   this.olddis3=this.promotionlist[i].percent3;\n            //  alert(3);\n            // }\n          }\n        } else {\n          pricec = parseFloat(this.priceperunit);\n        }\n        this.dis1 = disc1;\n        this.dis2 = disc2;\n        //alert(disc1);\n        if (this.dis3 > 0) {\n          this.dis3 = 0;\n        }\n        if (this.dis3 == 0) {\n          this.dis3 = disc3;\n        }\n        //alert(this.dis3+'/'+disc3+'/'+this.selectpayment);\n        if ((this.selectpayment === 'S60' || this.selectpayment === 'N01' || this.selectpayment === 'N07' || this.selectpayment === 'TT' || this.selectpayment === 'COD') && disc3 == 0) {\n          disc3 = 3.00;\n          this.dis3 = 3.00;\n        }\n        //alert(this.dis3);\n        if (this.checkunitprice == 'U') {\n          this.priceperunit = pricec.toString();\n        } else {\n          this.priceperunit = pricec.toString();\n          this.getpackingprice = pricec * this.numproduct;\n          //alert('BBSS'+parseFloat(pricec.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2})).toFixed(2));\n        }\n        this.getunitprice = pricec;\n        allprice = pcsitem * pricec;\n        if (disc1 > 0 && disc3 < 1 || disc1 > 0 && disc2 > 0 && disc3 > 0) {\n          priceperdis1 = allprice * disc1 / 100;\n          sumpricebedis1 = parseFloat(allprice.toFixed(2)) - parseFloat(priceperdis1.toFixed(2));\n          finalpri = parseFloat(sumpricebedis1.toFixed(2));\n          if (disc2 > 0) {\n            priceperdis2 = sumpricebedis1 * disc2 / 100;\n            sumpricebedis2 = sumpricebedis1 - priceperdis2;\n            finalpri = parseFloat(sumpricebedis2.toFixed(2));\n            if (disc3 > 0) {\n              priceperdis3 = sumpricebedis2 * disc3 / 100;\n              sumpricebedis3 = sumpricebedis2 - priceperdis3;\n              finalpri = parseFloat(sumpricebedis3.toFixed(2));\n            }\n          }\n          sumdis = priceperdis1 + priceperdis2 + priceperdis3;\n          sumdisdd = sumdis;\n          this.alldiscount = sumdisdd;\n          this.finalprice = finalpri;\n        } else if (disc1 > 0 && disc3 > 0) {\n          priceperdis1 = allprice * disc1 / 100;\n          sumpricebedis1 = allprice - priceperdis1;\n          finalpri = parseFloat(sumpricebedis1.toFixed(2));\n          if (disc3 > 0) {\n            priceperdis3 = sumpricebedis1 * disc3 / 100;\n            sumpricebedis3 = sumpricebedis1 - priceperdis3;\n            finalpri = sumpricebedis3;\n          }\n          sumdis = priceperdis1 + priceperdis2 + priceperdis3;\n          sumdisdd = sumdis;\n          this.alldiscount = sumdisdd;\n          this.finalprice = finalpri;\n        } else if (disc1 < 1 && disc2 < 1 && disc3 > 0) {\n          priceperdis1 = allprice * disc3 / 100;\n          sumpricebedis1 = allprice - priceperdis1;\n          finalpri = parseFloat(sumpricebedis1.toFixed(2));\n          sumdis = priceperdis1 + priceperdis2 + priceperdis3;\n          sumdisdd = sumdis;\n          this.alldiscount = sumdisdd;\n          this.finalprice = finalpri;\n        } else {\n          this.finalprice = parseFloat(allprice.toFixed(2));\n        }\n      }\n      //alert(this.finalprice);\n    }\n    editpriceperunit(value) {\n      this.priceperunit = value.toString();\n      this.getunitprice = value;\n      this.editdiscount();\n    }\n    //แก้ไขส่วนลด\n    editdiscount() {\n      if (this.dis1 == null || this.dis1.toString() == '') {\n        this.dis1 = 0;\n      }\n      if (this.dis2 == null || this.dis2.toString() == '') {\n        this.dis2 = 0;\n      }\n      if (this.dis3 == null || this.dis3.toString() == '') {\n        this.dis3 = 0;\n      }\n      var pcsitem = this.allpcs;\n      var disc1, disc2, disc3, pricec;\n      var priceperdis1 = 0,\n        priceperdis2 = 0,\n        priceperdis3 = 0,\n        sumdis = 0,\n        allprice = 0,\n        sumpricebedis1 = 0,\n        sumpricebedis2 = 0,\n        sumpricebedis3 = 0,\n        finalpri,\n        sumdisdd = 0.00;\n      disc1 = this.dis1;\n      disc2 = this.dis2;\n      disc3 = this.dis3;\n      pricec = this.getunitprice;\n      allprice = pcsitem * pricec;\n      if (disc1 > 0 && disc3 < 1 || disc1 > 0 && disc2 > 0 && disc3 > 0) {\n        priceperdis1 = allprice * disc1 / 100;\n        sumpricebedis1 = allprice - priceperdis1;\n        finalpri = sumpricebedis1;\n        if (disc2 > 0) {\n          priceperdis2 = sumpricebedis1 * disc2 / 100;\n          sumpricebedis2 = sumpricebedis1 - priceperdis2;\n          finalpri = sumpricebedis2;\n          if (disc3 > 0) {\n            priceperdis3 = sumpricebedis2 * disc3 / 100;\n            sumpricebedis3 = sumpricebedis2 - priceperdis3;\n            finalpri = sumpricebedis3;\n          }\n        }\n        sumdis = priceperdis1 + priceperdis2 + priceperdis3;\n        sumdisdd = parseFloat(sumdis.toFixed(2));\n        this.alldiscount = sumdisdd;\n        this.finalprice = finalpri;\n      } else if (disc1 > 0 && disc3 > 0) {\n        priceperdis1 = allprice * disc1 / 100;\n        sumpricebedis1 = allprice - priceperdis1;\n        finalpri = sumpricebedis1;\n        if (disc3 > 0) {\n          priceperdis3 = sumpricebedis1 * disc3 / 100;\n          sumpricebedis3 = sumpricebedis1 - priceperdis3;\n          finalpri = sumpricebedis3;\n        }\n        sumdis = priceperdis1 + priceperdis2 + priceperdis3;\n        sumdisdd = sumdis;\n        this.alldiscount = sumdisdd;\n        this.finalprice = finalpri;\n      } else if (disc1 < 1 && disc2 < 1 && disc3 > 0) {\n        priceperdis1 = allprice * disc3 / 100;\n        sumpricebedis1 = allprice - priceperdis1;\n        finalpri = sumpricebedis1;\n        sumdis = priceperdis1 + priceperdis2 + priceperdis3;\n        sumdisdd = sumdis;\n        this.alldiscount = sumdisdd;\n        this.finalprice = finalpri;\n      } else {\n        this.finalprice = allprice;\n      }\n      if (disc1 == 0 && disc2 == 0 && disc3 == 0) {\n        this.alldiscount = 0.00;\n      }\n    }\n    clrscrlinelist() {\n      this.iditem = '';\n      this.productname = '';\n      this.allpcs = 0;\n      this.selectunitid = '';\n      this.numpackingitem = 0;\n      this.priceperunit = '0';\n      this.dis1 = 0;\n      this.dis2 = 0;\n      this.dis3 = 0;\n      this.alldiscount = 0;\n      this.finalprice = 0;\n      this.productidsearch = null;\n      this.numberproductsearch = null;\n      this.unitlist = [];\n      this.showitem = 'รหัสสินค้า';\n      this.realldiscount = 0;\n      this.reallpcs = 0;\n      this.regetunitprice = 0;\n      this.reprice = 0;\n      this.redis1 = 0;\n      this.redis2 = 0;\n      this.redis3 = 0;\n      this.refinalprice = 0;\n      this.checkbuttonsave = false;\n      this.checkadditem = false;\n    }\n    //สร้าง SaleOder ID\n    makeidsaleOder(value, numvalue, date) {\n      //เปลี่ยนเลขที่ SalOder\n      var p = \"SW\";\n      var text = \"SW-\";\n      var zero = '0';\n      var saleid = this.Namelogin[0].salegroup;\n      if (this.Namelogin[0].accountnum != undefined) {\n        saleid = this.Namelogin[0].accountnum;\n        text = \"S-\";\n        p = \"S\";\n      }\n      var month = date.substring(5, 7);\n      var ye = date.substring(0, 4);\n      var year = ye + 543;\n      var monst = month;\n      var numst = this.getrunnumber;\n      /*if(month<10){\n      monst='0'+month.toString();\n      }*/\n      /*for(var i=0;i<this.getrunnumber.length;i++){\n      zero =zero+'0';\n      }*/\n      this.getallidsaleoder.push({\n        text: p,\n        saleid: saleid,\n        year: ye,\n        month: month,\n        runnumber: numvalue\n      });\n      if (this.idsoedit == undefined) {\n        this.idforshow = text + year.toString().substring(2, 4) + monst + saleid + '-' + value;\n      }\n      return text + year.toString().substring(2, 4) + monst + saleid + '-' + value;\n    }\n    /*:id/:linenumber/:custaccount/:itemid/:saleqty/:packqty/:price/:percent1/:percent2/:percent3/:lineweight''*/\n    //เพิ่มสินค้าลง SaleLine\n    addsaleline() {\n      //alert('IDF='+this.inventlocationid);\n      //alert(this.iditem);\n      if (this.lineoderlist.length > 0) {\n        var chitem = 0;\n        for (var i = 0; i < this.lineoderlist.length; i++) {\n          if (this.lineoderlist[i].iditem == this.iditem) {\n            chitem = 1;\n          }\n        }\n        if (chitem == 1 && this.checkbuttonsave == false) {\n          if (confirm('สินค้าชินนี้มีอยู่แล้ว ต้องการเพิ่มสินค้า ใช่ หรือ ไม่')) {} else {\n            return;\n          }\n        }\n      }\n      var disst = '000';\n      if (this.getpromotionedit == true) {\n        this.checkadditem = true;\n        if (this.promotionlist.length > 0) {\n          for (var i = 0; i < this.promotionlist.length; i++) {\n            if (this.accoutnreration === '%20') {\n              this.accoutnreration = '';\n            }\n            if (this.accoutnreration == this.promotionlist[i].accountrelation && this.allpcs >= this.promotionlist[i].quantityamountto && this.allpcs < this.promotionlist[i].quantityamountto && this.promotionlist[i].relation == '5') {\n              this.olddis1 = this.promotionlist[i].percent1;\n              this.olddis2 = this.promotionlist[i].percent2;\n              this.olddis3 = this.promotionlist[i].percent3;\n            } else if (this.accoutnreration == this.promotionlist[i].accountrelation && this.allpcs >= this.promotionlist[i].quantityamountfrom && this.allpcs < this.promotionlist[i].quantityamountto && this.promotionlist[i].relation == '5') {\n              this.olddis1 = this.promotionlist[i].percent1;\n              this.olddis2 = this.promotionlist[i].percent2;\n              this.olddis3 = this.promotionlist[i].percent3;\n            } else if (this.accoutnreration == this.promotionlist[i].accountrelation && this.allpcs >= this.promotionlist[i].quantityamountfrom && this.allpcs > this.promotionlist[i].quantityamountto && this.promotionlist[i].relation == '5') {\n              this.olddis1 = this.promotionlist[i].percent1;\n              this.olddis2 = this.promotionlist[i].percent2;\n              this.olddis3 = this.promotionlist[i].percent3;\n            }\n          }\n        }\n      }\n      if (this.upperdis != '') {\n        if (this.dis1 > parseInt(this.upperdis)) {\n          disst = '100';\n        } else {\n          disst = '000';\n        }\n      }\n      if (this.dis1 == null || this.dis1.toString() == '') {\n        this.dis1 = 0;\n      }\n      if (this.dis2 == null || this.dis2.toString() == '') {\n        this.dis2 = 0;\n      }\n      if (this.dis3 == null || this.dis3.toString() == '') {\n        this.dis3 = 0;\n      }\n      /* if(this.selectpayment==='N01' || this.selectpayment==='N07'|| this.selectpayment==='TT'){\n      if(disst==='111' && this.dis3==3){\n       disst='110';\n      } else if(disst==='101'&& this.dis3==3){\n       disst='100';\n      } else if(disst==='011'&& this.dis3==3) {\n       disst='010';\n      } else if(disst==='001'&& this.dis3==3) {\n       disst='000';\n      }\n       }*/\n      if (this.checkpacking > 0) {\n        alert('สินค้าไม่เต็ม Pack');\n        //this.openModal(true,'สินค้าไม่เต็ม Pack ต้องการ เพิ่มรายการใช่หรือไม่',false);\n        //return;\n      }\n      clearInterval(this.loaduom);\n      //alert(this.priceperunit+'/'+this.checkadditem);\n      var packpcs;\n      this.freeitem = false;\n      //alert(this.checkadditem+'/'+this.priceperunit+'/'+this.numberproductsearch);\n      if (this.iditem == '') {\n        alert('กรุณาป้อนรหัสสินค้า');\n        return;\n        //this.openModal(true,'กรุณาป้อนรหัสสินค้า',false);\n      } else if (parseFloat(this.priceperunit) < 1 && this.checkadditem == false || parseFloat(this.priceperunit) < 1 && this.checkadditem != true || this.priceperunit == '0' && this.checkadditem == false || this.numberproductsearch == '' || this.numberproductsearch == 0) {\n        //alert(this.checkadditem+'/'+parseFloat(this.priceperunit)+'/'+this.checkadditem);\n        alert('สินค้าราคาเป็น 0 ขายไม่ได้');\n        return;\n        //this.openModal(true,'สินค้าราคาเป็น 0 ขายไม่ได้',false);\n      } else if (this.checkbuttonsave == true) {\n        //alert(this.selectunitid);\n        //alert(this.iditem+'/ in='+this.index);\n        this.lineoderlist[this.index].id = this.idsaleoder;\n        this.lineoderlist[this.index].iditem = this.iditem;\n        this.lineoderlist[this.index].nameproduct = this.productname;\n        this.lineoderlist[this.index].numberpcs = this.allpcs;\n        this.lineoderlist[this.index].unitid = this.selectunitid;\n        this.lineoderlist[this.index].packingitem = this.numpackingitem;\n        this.lineoderlist[this.index].priceperunit = this.getunitprice;\n        this.lineoderlist[this.index].priceproduct = this.allpcs * this.getunitprice;\n        this.lineoderlist[this.index].totleweight = this.numpackingitem * this.weightproduct;\n        this.lineoderlist[this.index].discount1 = this.dis1;\n        this.lineoderlist[this.index].discount2 = this.dis2;\n        this.lineoderlist[this.index].discount3 = this.dis3;\n        this.lineoderlist[this.index].sumdiscount = this.alldiscount;\n        this.lineoderlist[this.index].sumallprice = this.finalprice;\n        this.lineoderlist[this.index].wh = this.inventlocationid;\n        //alert(JSON.stringify(this.productidsearch.itemid));\n        if (this.productidsearch.itemid != '' && this.productidsearch.itemid != undefined) {\n          this.lineoderlist[this.index].iditem = this.productidsearch.itemid;\n        }\n        //alert(this.productidsearch.itemid+'/'+this.iditem);\n        this.lineoderlist[this.index].checkpcs = this.getcheckpcs;\n        this.lineoderlist[this.index].disst = disst;\n        //console.log(JSON.stringify(this.lineoderlist[this.index].iditem));\n        //this.lineoderlist[this.index].packingitem = this.numberproductsearch;\n        this.iditem = '';\n        this.productname = '';\n        this.allpcs = 0;\n        this.selectunitid = '';\n        this.numpackingitem = 0;\n        this.priceperunit = '0';\n        this.dis1 = 0;\n        this.dis2 = 0;\n        this.dis3 = 0;\n        this.alldiscount = 0;\n        this.finalprice = 0;\n        this.productidsearch = null;\n        this.numberproductsearch = null;\n        this.unitlist = [];\n        this.showitem = 'รหัสสินค้า';\n        this.realldiscount = 0;\n        this.reallpcs = 0;\n        this.regetunitprice = 0;\n        this.reprice = 0;\n        this.redis1 = 0;\n        this.redis2 = 0;\n        this.redis3 = 0;\n        this.refinalprice = 0;\n        this.checkbuttonsave = false;\n        this.checkadditem = false;\n        this.getcheckpcs = 'P';\n        this.checkpacking = 0;\n        this.getpromotionedit = false;\n        disst = '000';\n        this.olddis1 = 0;\n        this.olddis2 = 0;\n        this.olddis3 = 0;\n        this.productidsearch = null;\n      } else {\n        /*this.headerlist[0].wh=this.inventlocationid;*/\n        this.lineoderlist.push({\n          idline: 0,\n          id: this.idsaleoder,\n          linenum: this.lineoderlist.length,\n          custaccount: this.accountnum,\n          iditem: this.iditem,\n          vattype: this.vatselect,\n          nameproduct: this.productname,\n          numberpcs: this.allpcs,\n          unitid: this.selectunitid,\n          packingitem: this.numpackingitem,\n          totleweight: this.weightproduct * this.numpackingitem,\n          priceperunit: this.getunitprice,\n          priceproduct: this.allpcs * this.getunitprice,\n          discount1: this.dis1,\n          discount2: this.dis2,\n          discount3: this.dis3,\n          sumdiscount: this.alldiscount,\n          sumallprice: this.finalprice,\n          wh: this.inventlocationid,\n          checkpcs: this.getcheckpcs,\n          disst: disst,\n          eidtable: this.eidtable,\n          saleid: this.Namelogin[0].salegroup,\n          dateshipping: this.dateshipping + ' ' + this.time\n        });\n        this.checkwh.push({\n          wh: this.inventlocationid,\n          eid: this.eidtable\n        });\n        this.iditem = '';\n        this.productname = '';\n        this.allpcs = 0;\n        this.selectunitid = '';\n        this.numpackingitem = 0;\n        this.priceperunit = '0';\n        this.dis1 = 0;\n        this.dis2 = 0;\n        this.dis3 = 0;\n        this.alldiscount = 0;\n        this.finalprice = 0;\n        this.productidsearch = null;\n        this.numberproductsearch = null;\n        this.inventlocationid = '';\n        this.unitlist = [];\n        this.showitem = 'รหัสสินค้า';\n        this.realldiscount = 0;\n        this.reallpcs = 0;\n        this.regetunitprice = 0;\n        this.reprice = 0;\n        this.redis1 = 0;\n        this.redis2 = 0;\n        this.redis3 = 0;\n        this.refinalprice = 0;\n        this.checkadditem = false;\n        this.getcheckpcs = 'P';\n        this.checkpacking = 0;\n        this.getpromotionedit = false;\n        disst = '000';\n        this.olddis1 = 0;\n        this.olddis2 = 0;\n        this.olddis3 = 0;\n      }\n      this.deleteunitlist();\n      this.numline++;\n      /*var urlpost=`${this.url+'/so/savesaleline/'}${this.idsaleoder}`\n      this.http.post(urlpost,'')*/\n      if (this.lineoderlist.length > 0) {\n        this.sumalldiscount = 0;\n        this.sumallpricedis = 0;\n        this.sumallweight = 0;\n        for (var i = 0; i < this.lineoderlist.length; i++) {\n          this.sumalldiscount = this.sumalldiscount + parseFloat(this.lineoderlist[i].sumdiscount.toFixed(2));\n          this.sumallpricedis = this.sumallpricedis + parseFloat(this.lineoderlist[i].sumallprice.toFixed(2));\n          this.sumallweight = this.sumallweight + parseFloat(this.lineoderlist[i].totleweight.toFixed(2));\n        }\n      }\n      //al\n      //alert(this.getcheckpcs);\n      //alert(JSON.stringify(this.lineoderlist));\n      this.runninnglinenumsaleline(this.lineoderlist);\n      this.setdefultUnit();\n      this.selectedunit = false;\n      this.Instock = '';\n      //alert(JSON.stringify(this.lineoderlist));\n    }\n    runninnglinenumsaleline(order) {\n      if (order.length > 0) {\n        for (var i = 0; i < order.length; i++) {\n          order[i].linenum = i + 1;\n        }\n        this.lineoderlist = order;\n      } else {\n        return;\n      }\n      //alert(JSON.stringify(this.lineoderlist));\n    }\n    deleteunitlist() {\n      if (this.unitlist.length > 0) {\n        for (var i = 0; i < this.unitlist.length; i++) {\n          this.unitlist.pop();\n        }\n      }\n    }\n    //ลบ Sale Line ออกจาก Array\n    deletelistlineoderclick(value, id) {\n      if (id > 0) {\n        var urlpost = `${this.url}${'delete_sale_linebyidline'}/${id}/${this.Namelogin[0].salegroup}`;\n        this.http.post(urlpost, '').subscribe(res => {\n          // this.setinheader= setInterval(this.savesaleoderlistsave(),);\n          this.lineoderlist.splice(value, 1);\n          this.lineoderlistsave.splice(value, 1);\n          alert('ลบ Sale Line เสร็จสิ้น');\n          if (this.lineoderlist.length > 0 || this.lineoderlist.length < 1) {\n            this.sumalldiscount = 0;\n            this.sumallpricedis = 0;\n            this.sumallweight = 0;\n            for (var i = 0; i < this.lineoderlist.length; i++) {\n              this.sumalldiscount = this.sumalldiscount + parseFloat(this.lineoderlist[i].sumdiscount.toFixed(2));\n              this.sumallpricedis = this.sumallpricedis + parseFloat(this.lineoderlist[i].sumallprice.toFixed(2));\n              this.sumallweight = this.sumallweight + parseFloat(this.lineoderlist[i].totleweight.toFixed(2));\n            }\n          }\n        });\n      } else {\n        this.lineoderlist.splice(value, 1);\n        this.lineoderlistsave.splice(value, 1);\n        if (this.lineoderlist.length > 0 || this.lineoderlist.length < 1) {\n          this.sumalldiscount = 0;\n          this.sumallpricedis = 0;\n          this.sumallweight = 0;\n          for (var i = 0; i < this.lineoderlist.length; i++) {\n            this.sumalldiscount = this.sumalldiscount + parseFloat(this.lineoderlist[i].sumdiscount.toFixed(2));\n            this.sumallpricedis = this.sumallpricedis + parseFloat(this.lineoderlist[i].sumallprice.toFixed(2));\n            this.sumallweight = this.sumallweight + parseFloat(this.lineoderlist[i].totleweight.toFixed(2));\n          }\n        }\n      }\n      if (this.lineoderlist.length > 0 || this.lineoderlist.length < 1) {\n        this.sumalldiscount = 0;\n        this.sumallpricedis = 0;\n        this.sumallweight = 0;\n        for (var i = 0; i < this.lineoderlist.length; i++) {\n          this.sumalldiscount = this.sumalldiscount + parseFloat(this.lineoderlist[i].sumdiscount.toFixed(2));\n          this.sumallpricedis = this.sumallpricedis + parseFloat(this.lineoderlist[i].sumallprice.toFixed(2));\n          this.sumallweight = this.sumallweight + parseFloat(this.lineoderlist[i].totleweight.toFixed(2));\n        }\n      }\n      this.runninnglinenumsaleline(this.lineoderlist);\n    }\n    //สลับการแสดงผล ราคาสินค้า Packing / Unit\n    getunitid(value) {\n      var st = value.target.value;\n      var ch = st.substring(0, 1);\n      this.checkunitprice = ch;\n      if (ch == 'P') {\n        this.getcheckpcs = 'P';\n        //this.priceperunit=this.getpackingprice;\n      } else {\n        this.getcheckpcs = 'U';\n        this.selectunitid = st.substring(1);\n        //this.priceperunit=this.getunitprice;\n      }\n      this.checknumproduct(this.numberproductsearch);\n      this.editdiscount();\n    }\n    foreditlistlineoderclick(value, i) {\n      this.getpromotionedit = i;\n      this.checkadditem = true;\n      //this.Searchitemclick(this.iditem)\n      this.loaduom = setInterval(() => this.Searchitemclick(this.iditem), 350); //เดิม150\n      this.editlistlineoderclick(value);\n    }\n    //คลิกแก้ไข Sale Oderline\n    editlistlineoderclick(value) {\n      var numpcs = this.lineoderlist[value].numberpcs;\n      this.Searchproductlist(this.lineoderlist[value].iditem);\n      this.clrscrlinelist();\n      this.checkbuttonsave = true;\n      this.index = value;\n      this.idsaleoder = this.lineoderlist[value].id;\n      this.iditem = this.lineoderlist[value].iditem;\n      this.showitem = this.lineoderlist[value].iditem;\n      this.productname = this.lineoderlist[value].nameproduct;\n      this.allpcs = this.lineoderlist[value].numberpcs;\n      this.selectunitid = this.lineoderlist[value].unitid;\n      if (this.lineoderlist[value].checkpcs === 'P') {\n        this.numproduct = this.lineoderlist[value].numberpcs / this.lineoderlist[value].packingitem;\n        this.getcheckpcs = 'P';\n        this.checkunitprice = 'P';\n        var pricepack = this.lineoderlist[value].priceperunit;\n        this.numberproductsearch = this.lineoderlist[value].packingitem;\n        var sdas = pricepack;\n        this.priceperunit = sdas.toString();\n        //this.checknumproduct(this.lineoderlist[value].packingitem);\n        this.getunitprice = pricepack;\n      } else {\n        this.lineoderlist[value].priceperunit;\n        this.getcheckpcs = 'U';\n        this.checkunitprice = 'U';\n        this.numberproductsearch = this.lineoderlist[value].numberpcs;\n        this.priceperunit = this.lineoderlist[value].priceperunit.toString();\n        // this.checknumproduct(this.lineoderlist[value].numberpcs);\n        this.getunitprice = this.lineoderlist[value].priceperunit;\n      }\n      this.numpackingitem = this.lineoderlist[value].packingitem;\n      this.dis1 = this.lineoderlist[value].discount1;\n      this.dis2 = this.lineoderlist[value].discount2;\n      this.dis3 = this.lineoderlist[value].discount3;\n      this.alldiscount = this.lineoderlist[value].sumdiscount;\n      this.finalprice = this.lineoderlist[value].sumallprice;\n      this.productidsearch = this.lineoderlist[value].iditem;\n      this.numproduct = this.lineoderlist[value].numberpcs / this.lineoderlist[value].packingitem;\n    }\n    //โหลดข้อมูลคลังสินค้าจาก Database\n    getwarehouse() {\n      this.http.get(this.url + 'getwarehouse').subscribe(res => {\n        this.getwh = res;\n      }, error => {\n        this.openModal(true, 'เกิดปัญหาในการ Process ข้อมูล กรุณาลองใหม่อีกครั้ง', false);\n      });\n    }\n    //สร้าง Sale Oder แยกคัลงสินค้าและ บันทึกข้อมูล\n    savesaleoderbywherehouse() {\n      if (this.idsoedit == undefined) {\n        localStorage.setItem('SoidOld', this.idsaleoder);\n      }\n      this.headerlistsave = [];\n      this.dateshipping = this.bsValue.getFullYear() + '-' + (parseInt(this.bsValue.getMonth().toString()) + 1) + '-' + this.bsValue.getDate();\n      this.actionbtn = true;\n      if (this.deliveryonclick == '') {\n        alert('กรุณาเลือก การขนส่ง');\n        this.actionbtn = false;\n        return;\n      } else if (this.lineoderlist.length < 1) {\n        alert('กรุณาป้อน รายการสินค้า');\n        this.actionbtn = false;\n        return;\n      } else {\n        var w = 1;\n        this.warehouselist = [];\n        if (this.getwh.length > 0) {\n          for (var i = 0; i < this.getwh.length; i++) {\n            this.warehouselist.push({\n              wh: this.getwh[i].inventlocationid,\n              num: i + 1,\n              sumalldiscount: 0,\n              sumallpricedis: 0,\n              sumallweight: 0\n            });\n          }\n          if (this.checkwh.length > 0) {\n            for (var i = 0; i < this.checkwh.length; i++) {\n              if (this.checkwh[i].wh == 'WF1' && this.checkwh[i].eid == 0) {\n                w = 0;\n              }\n              //   if(this.checkwh[i].wh=='WF1' ){\n              //     w=0;\n              //         }\n            }\n          }\n          /* alert(JSON.stringify(this.lineoderlist));\n           alert(JSON.stringify(this.headerlist));*/\n          var weight;\n          var discount;\n          var allprice;\n          for (var x = 0; x < this.warehouselist.length; x++) {\n            for (var i = 0; i < this.lineoderlist.length; i++) {\n              weight = this.lineoderlist[i].totleweight;\n              discount = this.lineoderlist[i].sumdiscount;\n              allprice = this.lineoderlist[i].sumallprice;\n              if (this.lineoderlist[i].wh == this.warehouselist[x].wh && this.lineoderlist[i].eidtable == 0) {\n                // alert(1);\n                this.warehouselist[x].sumalldiscount = parseFloat(this.warehouselist[x].sumalldiscount.toFixed(2)) + parseFloat(this.lineoderlist[i].sumdiscount.toFixed(2));\n                this.warehouselist[x].sumallpricedis = parseFloat(this.warehouselist[x].sumallpricedis.toFixed(2)) + parseFloat(this.lineoderlist[i].sumallprice.toFixed(2));\n                this.warehouselist[x].sumallweight = parseFloat(this.warehouselist[x].sumallweight.toFixed(2)) + parseFloat(this.lineoderlist[i].totleweight.toFixed(2));\n              } else if (this.lineoderlist[i].wh == this.warehouselist[x].wh && this.lineoderlist[i].eidtable == 1) {\n                //alert(2);\n                this.warehouselist[x].sumalldiscount = parseFloat(this.warehouselist[x].sumalldiscount.toFixed(2)) + parseFloat(this.lineoderlist[i].sumdiscount.toFixed(2));\n                this.warehouselist[x].sumallpricedis = parseFloat(this.warehouselist[x].sumallpricedis.toFixed(2)) + parseFloat(this.lineoderlist[i].sumallprice.toFixed(2));\n                this.warehouselist[x].sumallweight = parseFloat(this.warehouselist[x].sumallweight.toFixed(2)) + parseFloat(this.lineoderlist[i].totleweight.toFixed(2));\n              } else {\n                // if(this.lineoderlist[i].eidtable==1){\n                //   alert(3);\n                //   this.warehouselist[w].sumalldiscount=parseFloat(this.warehouselist[w].sumalldiscount.toFixed(2))+parseFloat(this.lineoderlist[i].sumdiscount.toFixed(2));\n                //   this.warehouselist[w].sumallpricedis=parseFloat(this.warehouselist[w].sumallpricedis.toFixed(2))+parseFloat(this.lineoderlist[i].sumallprice.toFixed(2));\n                //   this.warehouselist[w].sumallweight=parseFloat(this.warehouselist[w].sumallweight.toFixed(2))+parseFloat(this.lineoderlist[i].totleweight.toFixed(2));\n                // }\n              }\n            }\n          }\n          //alert(JSON.stringify(this.warehouselist));\n          //return;\n          if (this.warehouselist.length > 1) {\n            for (var i = 0; i < this.warehouselist.length; i++) {\n              if (this.warehouselist[i].sumalldiscount == 0 && this.warehouselist[i].sumallpricedis == 0 && this.warehouselist[i].sumallweight == 0) {\n                //alert('1'+this.warehouselist[i].sumalldiscount+'/'+this.warehouselist[i].sumallpricedis+'/'+this.warehouselist[i].sumallweight);\n              } else {\n                //alert(2);\n                this.headerlistsave.push({\n                  idhead: this.headerlist[0].idhead,\n                  id: this.headerlist[0].id + '-' + this.warehouselist[i].num,\n                  Custaccount: this.headerlist[0].Custaccount,\n                  vattype: this.vatselect,\n                  paymenttype: this.headerlist[0].paymenttype,\n                  deliverytype: this.headerlist[0].deliverytype,\n                  locationno: this.locationno,\n                  amount: this.warehouselist[i].sumallpricedis,\n                  discount: this.warehouselist[i].sumalldiscount,\n                  totalweigth: this.warehouselist[i].sumallweight,\n                  note: this.notesoinput,\n                  remark: this.remarksoinput,\n                  dateshipping: this.dateshipping + ' ' + this.time,\n                  wh: this.warehouselist[i].wh,\n                  locationde: this.locationnodelie,\n                  saleid: this.Namelogin[0].salegroup,\n                  filetype: this.headerlist[0].filetype,\n                  filename: this.headerlist[0].filename\n                });\n              }\n            }\n            for (var x = 0; x < this.headerlistsave.length; x++) {\n              var id = this.headerlistsave[x].id;\n              if (this.headerlistsave.length == 1) {\n                this.chackdalateoder = false;\n                if (id.length == 16) {\n                  this.headerlistsave[x].id = id.substring(0, 14);\n                } else if (id.length == 17) {\n                  this.headerlistsave[x].id = id.substring(0, 15);\n                } else if (id.length == 15) {\n                  this.headerlistsave[x].id = id.substring(0, 13);\n                } else if (id.length == 14) {\n                  this.headerlistsave[x].id = id.substring(0, 12);\n                } else if (id.length == 19) {\n                  this.headerlistsave[x].id = id.substring(0, 17);\n                } else if (id.length == 18) {\n                  this.headerlistsave[x].id = id.substring(0, 16);\n                } else if (id.length == 23) {\n                  this.headerlistsave[x].id = id.substring(0, 21);\n                } else if (id.length == 24) {\n                  this.headerlistsave[x].id = id.substring(0, 22);\n                } else if (id.length == 25) {\n                  this.headerlistsave[x].id = id.substring(0, 23);\n                }\n              } else {\n                this.chackdalateoder = true;\n              }\n              //return;\n              for (var i = 0; i < this.lineoderlist.length; i++) {\n                var ch = 0;\n                if (this.lineoderlist[i].wh == this.headerlistsave[x].wh && this.lineoderlist[i].eidtable == 0) {\n                  /*var wh='WF4';\n                  if(w==0){\n                    wh='WF1'\n                  } */\n                  //alert('WH= '+this.lineoderlist[i].wh+'ITEM='+this.lineoderlist[i].iditem);\n                  this.lineoderlistsave.push({\n                    idline: this.lineoderlist[i].idline,\n                    id: this.headerlistsave[x].id,\n                    linenum: this.lineoderlist[i].linenum,\n                    custaccount: this.lineoderlist[i].custaccount,\n                    iditem: this.lineoderlist[i].iditem,\n                    vattype: this.vatselect,\n                    nameproduct: this.lineoderlist[i].nameproduct,\n                    numberpcs: this.lineoderlist[i].numberpcs,\n                    unitid: this.lineoderlist[i].unitid,\n                    packingitem: this.lineoderlist[i].packingitem,\n                    totleweight: this.lineoderlist[i].totleweight,\n                    priceperunit: this.lineoderlist[i].priceperunit,\n                    priceproduct: this.lineoderlist[i].priceproduct,\n                    discount1: this.lineoderlist[i].discount1,\n                    discount2: this.lineoderlist[i].discount2,\n                    discount3: this.lineoderlist[i].discount3,\n                    sumdiscount: this.lineoderlist[i].sumdiscount,\n                    sumallprice: this.lineoderlist[i].sumallprice,\n                    wh: this.lineoderlist[i].wh,\n                    checkpcs: this.lineoderlist[i].checkpcs,\n                    disst: this.lineoderlist[i].disst,\n                    eidtable: this.lineoderlist[i].eidtable,\n                    saleid: this.Namelogin[0].salegroup,\n                    dateshipping: this.dateshipping + ' ' + this.time\n                  });\n                } else {\n                  //alert('x='+x);\n                  if (this.lineoderlist[i].wh == this.headerlistsave[x].wh && this.lineoderlist[i].eidtable == 1) {\n                    //alert(2);\n                    this.lineoderlistsave.push({\n                      idline: this.lineoderlist[i].idline,\n                      id: this.headerlistsave[x].id,\n                      linenum: this.lineoderlist[i].linenum,\n                      custaccount: this.lineoderlist[i].custaccount,\n                      iditem: this.lineoderlist[i].iditem,\n                      vattype: this.vatselect,\n                      nameproduct: this.lineoderlist[i].nameproduct,\n                      numberpcs: this.lineoderlist[i].numberpcs,\n                      unitid: this.lineoderlist[i].unitid,\n                      packingitem: this.lineoderlist[i].packingitem,\n                      totleweight: this.lineoderlist[i].totleweight,\n                      priceperunit: this.lineoderlist[i].priceperunit,\n                      priceproduct: this.lineoderlist[i].priceproduct,\n                      discount1: this.lineoderlist[i].discount1,\n                      discount2: this.lineoderlist[i].discount2,\n                      discount3: this.lineoderlist[i].discount3,\n                      sumdiscount: this.lineoderlist[i].sumdiscount,\n                      sumallprice: this.lineoderlist[i].sumallprice,\n                      wh: this.lineoderlist[i].wh,\n                      checkpcs: this.lineoderlist[i].checkpcs,\n                      disst: this.lineoderlist[i].disst,\n                      eidtable: this.lineoderlist[i].eidtable,\n                      saleid: this.Namelogin[0].salegroup,\n                      dateshipping: this.dateshipping + ' ' + this.time\n                    });\n                  } else if (this.lineoderlist[i].wh != this.headerlistsave[x].wh && this.lineoderlist[i].eidtable == 1 && x == 0) {\n                    // alert(2.1);\n                    // this.lineoderlistsave.push({\n                    //   idline:this.lineoderlist[i].idline,\n                    //   id:this.headerlistsave[x].id,\n                    //   linenum:this.lineoderlist[i].linenum,\n                    //   custaccount:this.lineoderlist[i].custaccount,\n                    //   iditem: this.lineoderlist[i].iditem,\n                    //   vattype:this.vatselect,\n                    //   nameproduct:this.lineoderlist[i].nameproduct,\n                    //   numberpcs:this.lineoderlist[i].numberpcs,\n                    //   unitid: this.lineoderlist[i].unitid,\n                    //   packingitem:this.lineoderlist[i].packingitem,\n                    //   totleweight:this.lineoderlist[i].totleweight,\n                    //   priceperunit:this.lineoderlist[i].priceperunit,\n                    //   priceproduct:this.lineoderlist[i].priceproduct,\n                    //   discount1:this.lineoderlist[i].discount1,\n                    //   discount2:this.lineoderlist[i].discount2,\n                    //   discount3:this.lineoderlist[i].discount3,\n                    //   sumdiscount:this.lineoderlist[i].sumdiscount,\n                    //   sumallprice:this.lineoderlist[i].sumallprice,\n                    //   wh:this.lineoderlist[i].wh,\n                    //   checkpcs:this.lineoderlist[i].checkpcs,\n                    //   disst:this.lineoderlist[i].disst,\n                    //   eidtable: this.lineoderlist[i].eidtable,\n                    //   saleid:this.Namelogin[0].salegroup,\n                    //   dateshipping:this.dateshipping+ ' '+this.time\n                    //         })\n                  }\n                }\n              }\n            }\n            //this.actionbtn=false;\n            //return;\n            //alert(JSON.stringify(this.checkwh));\n            //alert(JSON.stringify(this.lineoderlistsave));\n            //alert(JSON.stringify(this.headerlistsave));\n            //this.this.idsaleoder();\n            //return;\n            //alert('asdsdsdd')\n            // alert('SSS'+this.headerlistsave.length);\n            this.savesaleoderlistsave();\n            //this.setinheader=setInterval(() => this.savesaleoderlistsave(), 700);\n            this.checkwh = [];\n          } else {}\n        } else {\n          //alert('เกิดข้อผิดพลาดกรุณาลองใหม่อีกครั้ง');\n          //this.openModal(true,'เกิดข้อผิดพลาดกรุณาลองใหม่อีกครั้ง',false);\n        }\n        //console.log(JSON.stringify(this.headerlistsave));\n      }\n    }\n    //บันทึกขอมูล Sale Line แบบ แยก คลังสินค้า\n    savesalelinebywarehouselist(valuewh) {\n      //alert(valuewh);\n      this.dateshipping = this.bsValue.getFullYear() + '-' + (parseInt(this.bsValue.getMonth().toString()) + 1) + '-' + this.bsValue.getDate();\n      var i = 0;\n      var line = [];\n      if (this.lineoderlistsave.length > 0) {\n        for (var i = 0; i < this.lineoderlistsave.length; i++) {\n          line.push({\n            id: this.lineoderlistsave[i].id,\n            linenum: this.lineoderlistsave[i].linenum,\n            custaccount: this.customerno,\n            iditem: this.lineoderlistsave[i].iditem,\n            numberpcs: this.lineoderlistsave[i].numberpcs,\n            packingitem: this.lineoderlistsave[i].packingitem,\n            vattype: this.vatselect,\n            priceperunit: this.lineoderlistsave[i].priceperunit,\n            discount1: this.lineoderlistsave[i].discount1,\n            discount2: this.lineoderlistsave[i].discount2,\n            discount3: this.lineoderlistsave[i].discount3,\n            totleweight: this.lineoderlistsave[i].totleweight,\n            dateshipping: this.dateshipping + ' ' + this.time,\n            saleid: this.lineoderlistsave[i].saleid,\n            checkpcs: this.lineoderlistsave[i].checkpcs,\n            disst: this.lineoderlistsave[i].disst,\n            eidtable: this.lineoderlistsave[i].eidtable,\n            idline: this.lineoderlistsave[i].idline\n          });\n        }\n      } else {\n        return;\n      }\n      console.log(line.length + '///' + JSON.stringify(line));\n      var urlpost = `${this.url}${'savesalelinenew'}`;\n      this.http.post(urlpost, {\n        Data: line\n      }).subscribe(res => {\n        if (res != true) {} else {\n          if (this.idsoedit == undefined) {\n            this.actionbtn = false;\n            this.lineoderlist = [];\n            this.dissave = false;\n            alert('บันทึกข้อมูลเสร็จสิ้น');\n            location.reload();\n          } else {\n            this.dissave = false;\n            this.actionbtn = false;\n            this.clickbtnclose = false;\n            this.lineoderlist = [];\n            this.headerlist = [];\n            this.company = '';\n            this.clrscrsaleheader();\n            //this.router.navigate(['/soreview']);\n            if (valuewh >= 2) {\n              this.deleteebeforupdatesave(this.idsoedit);\n              //alert('บันทึกข้อมูลเสร็จสิ้น');\n              //this.closeeditlistoder();\n            } else {\n              alert('บันทึกข้อมูลเสร็จสิ้น');\n              this.router.navigate(['/soreview']);\n            }\n          }\n          /*if(this.lineoderlistsave.length==0) {\n          \n          \n          \n            //this.openModal(true,'บันทึกข้อมูลเสร็จสิ้น',true);\n          \n           // this.deleteebeforupdatesave(this.idsoedit);\n          \n          \n            //alert('บันทึกข้อมูลเสร็จสิ้น');\n          } else if(this.idsoedit!=undefined && this.lineoderlist.length==0){\n            clearInterval(this.setin);\n            this.clrscrsaleheader();\n            this.clrscrlinelist();\n          \n            if(this.Namelogin[0].accountnum != undefined){\n              this.getcustomersalefunction(this.Namelogin[0].accountnum);\n              }\n          \n          \n            //this.openModal(true,'บันทึกข้อมูลเสร็จสิ้น',true);\n          \n            if(this.idsoedit==undefined){\n          \n              this.actionbtn=false;\n            } else {\n              alert('บันทึกข้อมูลเสร็จสิ้น');\n              this.actionbtn=false;\n              this.clickbtnclose=false;\n            }\n            //this.deleteebeforupdatesave(this.idsaleoder);\n            this.company='';\n            this.headerlist=[];\n            this.headerlistsave=[];\n          }*/\n          this.sumalldiscount = 0;\n          this.sumallpricedis = 0;\n          this.sumallweight = 0;\n        }\n      }, error => {\n        alert('ไม่สามารถบันทึกข้อมูลได้ กรุณาลองใหม่อีกครั้ง');\n        this.actionbtn = false;\n        return;\n      });\n    }\n    deletelistlineodersave(value) {\n      this.lineoderlistsave.splice(value, 1);\n      this.lineoderlist.splice(value, 1);\n      if (this.lineoderlist.length > 0 || this.lineoderlist.length < 1) {\n        this.sumalldiscount = 0;\n        this.sumallpricedis = 0;\n        this.sumallweight = 0;\n        for (var i = 0; i < this.lineoderlist.length; i++) {\n          this.sumalldiscount = this.sumalldiscount + parseFloat(this.lineoderlist[i].sumdiscount.toFixed(2));\n          this.sumallpricedis = this.sumallpricedis + parseFloat(this.lineoderlist[i].sumallprice.toFixed(2));\n          this.sumallweight = this.sumallweight + parseFloat(this.lineoderlist[i].totleweight.toFixed(2));\n        }\n      }\n    }\n    //บันทึกข้อมูล Sale header แบบ แยกคลังสินค้า\n    savesaleoderlistsave() {\n      this.dissave = false;\n      var head = [];\n      // alert('Length= '+this.headerlistsave.length);\n      // return;\n      //alert('INV'+this.locationno+'DIV'+this.locationnodelie);\n      if (this.headerlistsave.length > 0) {\n        for (var i = 0; i < this.headerlistsave.length; i++) {\n          if (this.headerlistsave[i].id.toString().length < 5) {\n            alert('เกิดข้อผิดพลาดกรุณาลองใหม่อีกครั้ง');\n            return;\n          }\n          head.push({\n            id: this.headerlistsave[i].id,\n            Custaccount: this.customerno,\n            vattype: this.vatselect,\n            paymenttype: this.selectpayment,\n            deliverytype: this.deliveryonclick,\n            locationno: this.locationno,\n            amount: this.headerlistsave[i].amount,\n            discount: this.headerlistsave[i].discount,\n            totalweight: this.headerlistsave[i].totalweigth,\n            note: this.notesoinput,\n            remark: this.remarksoinput,\n            dateshipping: this.headerlistsave[i].dateshipping,\n            saleid: this.headerlistsave[i].saleid,\n            wh: this.headerlistsave[i].wh,\n            locationde: this.locationnodelie,\n            idhead: this.headerlistsave[i].idhead,\n            filetype: this.headerlistsave[i].filetype,\n            filename: this.headerlistsave[i].filename\n          });\n        }\n      } else {\n        return;\n      }\n      // alert(JSON.stringify(head));\n      // return;\n      // alert(JSON.stringify(this.headerlistsave));\n      var noteina = '';\n      var noteinb = '';\n      var remarka = '';\n      var remarkb = '';\n      this.actionbtn = true;\n      if (this.deliveryonclick == '') {\n        alert('กรุณาเลือก การขนส่ง');\n        clearInterval(this.setinheader);\n        this.actionbtn = false;\n        return;\n        //this.openModal(true,'กรุณาเลือก การขนส่ง',false);\n      } else {\n        if (this.remarksoinput == '') {\n          this.remarksoinput = 'N';\n          remarkb = 'N';\n        } else {\n          remarka = this.remarksoinput.replace('/', '-');\n          remarkb = remarka.replace('%', 'เปอร์เซ็น');\n        }\n        if (this.notesoinput == '') {\n          this.notesoinput = 'N';\n          noteinb = 'N';\n        } else {\n          this.notesoinput.replace('/', '-');\n          noteina = this.notesoinput.replace('/', '-');\n          noteinb = noteina.replace('%', 'เปอร์เซ็น');\n        }\n        if (this.wh == '') {\n          this.wh = '%20';\n        }\n        if (this.headerlistsave[0].note == '') {\n          this.headerlistsave[0].note = 'N';\n        } else {\n          if (this.headerlistsave[0].note != undefined) {\n            var note = this.headerlistsave[0].note;\n            var notea = note.replace('/', '-');\n            var noteb = notea.replace('%', 'เปอร์เซ็น');\n          }\n          this.headerlistsave[0].note = noteb;\n        }\n        if (this.headerlistsave[0].remark == '') {\n          this.headerlistsave[0].remark = 'N';\n        } else {\n          if (this.headerlistsave[0].remark != undefined) {\n            var remak = this.headerlistsave[0].remark;\n            var rema = remak.replace('/', '-');\n            var remb = rema.replace('%', 'เปอร์เซ็น');\n            this.headerlistsave[0].remark = remb;\n          }\n        }\n        //this.idforshow=this.headerlistsave[0].idsaleoder;\n        if (this.lineoderlistsave.length == 0) {\n          alert('กรุณาป้อน รายการสินค้า');\n          this.actionbtn = false;\n          return;\n        }\n        if (this.Namelogin[0].salegroup == 'admin') {} else {\n          this.usseredit = this.Namelogin[0].salegroup;\n        }\n        //console.log(head.length+'///'+JSON.stringify(head));\n        //alert(this.locationno+'/'+this.locationnodelie);\n        var urlpost = `${this.url}${'createsonew'}`;\n        if (this.daddress != null) {\n          this.http.post(urlpost, {\n            Data: head\n          }).subscribe(res => {\n            if (res == true) {\n              if (this.idsoedit == undefined) {\n                this.updaterunnumber(this.getallidsaleoder);\n              }\n              this.x = 1;\n              this.savesalelinebywarehouselist(head.length);\n              this.sumalldiscount = 0;\n              this.sumallpricedis = 0;\n              this.sumallweight = 0;\n              this.remarksoinput = '';\n              this.notesoinput = '';\n            }\n          }, err => {\n            alert('ไม่สามารถบันทึกข้อมูลได้กรุณาลองใหม่อีก ครั้ง');\n            this.actionbtn = false;\n            return;\n          });\n        } else {\n          alert('กรุณาเลือกลูกค้า');\n          clearInterval(this.setinheader);\n          this.actionbtn = false;\n          return;\n          //this.openModal(true,'กรุณาเลือกลูกค้า',false);\n        }\n      }\n    }\n    //ลบข้อมูล SaleOder หลังจาก บันทึกข้อมูล Sale Oder แบบแยกคลังสินคาแล้ว\n    deleteebeforupdatesave(value) {\n      if (this.idsoedit != undefined) {\n        var urlpost = `${this.url}${'delete_sale_line'}/${value}/${this.usseredit}`;\n        this.http.post(urlpost, '').subscribe(res => {\n          alert('บันทึกข้อมูลเสร็จสิ้น');\n          this.router.navigate(['/soreview']);\n          //this.closeeditlistoder();\n        });\n      } else {}\n    }\n    closeeditlistoder() {\n      this.router.navigate(['/solist1']);\n    }\n    convertnumber(number) {\n      var parts = number.toFixed(2).split(\".\");\n      var num = parts[0].replace(/(\\d)(?=(\\d{3})+(?!\\d))/g, \"$1,\") + (parts[1] ? \".\" + parts[1] : \"\");\n      return num;\n    }\n    checklength(valelength, numlength) {\n      if (valelength.length >= numlength) {\n        alert('จำนวนข้อมูล ต้องไม่เกิน ' + numlength + ' ตัวอักษร');\n      }\n    }\n    //สั่งพิมใบ สั่งซื้อ\n    print() {\n      let popupWin;\n      popupWin = window.open('', '_blank');\n      popupWin.document.open();\n      popupWin.document.write(`\n      <html>\n      <head>\n        <title>ใบแปะหน้ากล่อง</title>\n        <meta http-equiv=\"Content-Type\" content=\"text/html; charset=UTF-8\" />\n        <meta http-equiv=\"Content-Type\" content=\"text/html; charset=iso-8859-1\">\n  <meta http-equiv=\"Content-Language\" content=\"en-us\">\n  <meta http-equiv=\"Content-Script-Type\" content=\"text/javascript\">\n  <meta name=\"GENERATOR\" content=\"TrackInternet._Default Class\">\n\n        <script src=\"https://code.jquery.com/jquery-3.3.1.js\" ></script>\n  <script src=\"https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.14.3/umd/popper.min.js\" ></script>\n  <script src=\"https://stackpath.bootstrapcdn.com/bootstrap/4.1.3/js/bootstrap.min.js\"></script>\n        <style>\n  .textcen{\n  text-align: center;\n  }\n  .he{\n    border-width:5px;\n  border-style:solid;\n  border-color: black;\n  }\n        </style>\n      </head>\n  <body onload=\"window.print();window.close()\">\n  <div class=\"container-fluid \">\n\n              <div class=\"col-md-12\">\n              <div class=\" he \">\n              <h1 class=\"textcen\" style=\" font-size: 50px\">*** กรุณาอย่าวางของหนักทับ ***</h1>\n                 </div>\n                 <div class=\"textcen p-1\">\n                     <span class=\"textcen\" ><h2 style=\" font-size: 45px\"></h2></span>\n                 </div>\n                 <div class=\" textcen p-1\">\n                     <div class=\"textcen\" ><h2 style=\" font-size: 40px\"></h2></div>\n                 </div>\n                 <div class=\"textcen p-1\">\n                     <div class=\"textcen\" ><h2 style=\" font-size: 40px\">\n                      </h2></div>\n                 </div>\n                 <div class=\" he\">\n                     <span class=\"textcen\"  ><h1 style=\" font-size: 50px\">*** กรุณาอย่าวางของหนักทับ ***</h1> </span>\n                 </div>\n\n              </div>\n\n\n      </div>\n  </body>\n    </html>`);\n      popupWin.document.close();\n    }\n    //เปิดแจ้งเตือน\n    openModal(open, text, load) {\n      this.mdlSampleIsOpen = open;\n      this.alt = text;\n      this.checkreload = load;\n    }\n    //ปิดแจ้งเตือน\n    closemodel(cl) {\n      this.mdlSampleIsOpen = false;\n      if (this.checkreload == true) {\n        this.router.navigate(['sorecord']);\n        this.notesoinput = '';\n        this.remarksoinput = '';\n        this.inaddress = [];\n        this.daddress = [];\n        this.model = [];\n      }\n    }\n    applyLocale(pop) {\n      this.localeService.use(this.locale);\n      pop.hide();\n      pop.show();\n    }\n    CheckValueRemark(value) {\n      if (value == '') {\n        // alert(this.remarksoinput);\n        //AdwCleanerthis.remarksoinput='BY'\n        //alert(this.remarksoinput);\n      }\n    }\n    static {\n      this.ɵfac = function SorecordComponent_Factory(t) {\n        return new (t || SorecordComponent)(i0.ɵɵdirectiveInject(i1.HttpClient), i0.ɵɵdirectiveInject(i2.WebapiService), i0.ɵɵdirectiveInject(i3.ActivatedRoute), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i4.FormBuilder), i0.ɵɵdirectiveInject(i5.NgbCalendar), i0.ɵɵdirectiveInject(i6.BsLocaleService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: SorecordComponent,\n        selectors: [[\"app-sorecord\"]],\n        decls: 175,\n        vars: 87,\n        consts: [[\"rt\", \"\"], [\"rtpr\", \"\"], [1, \"container-fluid\", 2, \"padding-right\", \"5px\", \"padding-left\", \"5px\"], [2, \"padding-top\", \"60px\"], [1, \"p-sm-1\", \"col-xs-12\", \"col-12\", \"col-md-12\", \"bg-secondary\", \"text-white\", \"text-center\"], [\"id\", \"accordion\"], [1, \"card\"], [\"id\", \"headingOne\", 1, \"card-header\", 2, \"padding-top\", \"0px\", \"padding-bottom\", \"0px\"], [1, \"form-row\"], [1, \"mb-0\"], [\"data-toggle\", \"collapse\", \"data-target\", \"#collapseOne\", \"aria-expanded\", \"true\", \"aria-controls\", \"collapseOne\", 1, \"btn\", \"btn-link\"], [1, \"col\", 2, \"padding-top\", \"4px\"], [\"class\", \"btn btn-warning btn-sm font-weight-light\", \"type\", \"submit\", \"style\", \"width:45px;height:26px;padding:0px\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [\"class\", \"btn btn-primary btn-sm font-weight-light\", \"type\", \"button\", \"style\", \"width:45px;height:26px;padding:0px\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [2, \"margin-left\", \"2%\"], [\"style\", \"margin-left: 2%\", \"type\", \"button\", \"class\", \"btn btn-sm btn-danger\", 3, \"click\", 4, \"ngIf\"], [\"id\", \"collapseOne\", \"aria-labelledby\", \"headingOne\", \"data-parent\", \"#accordion\", 1, \"collapse\", \"show\"], [1, \"card-body\"], [1, \"container-fluid\"], [\"novalidate\", \"\", 1, \"needs-validation\"], [1, \"col-md-4\", \"mb-3\"], [\"tooltip\", \"\\u0E1B\\u0E49\\u0E2D\\u0E19\\u0E0A\\u0E37\\u0E48\\u0E2D \\u0E2B\\u0E23\\u0E37\\u0E2D \\u0E23\\u0E2B\\u0E31\\u0E2A\\u0E25\\u0E39\\u0E01\\u0E04\\u0E49\\u0E32\", \"triggers\", \"focus\", \"id\", \"typeahead-template\", \"type\", \"text\", \"name\", \"model\", 1, \"form-control\", \"form-control-sm\", 3, \"ngModelChange\", \"disabled\", \"placeholder\", \"ngModel\", \"ngbTypeahead\", \"resultTemplate\", \"inputFormatter\"], [\"tooltip\", \"\\u0E40\\u0E25\\u0E37\\u0E2D\\u0E01\\u0E17\\u0E35\\u0E48\\u0E2D\\u0E22\\u0E39\\u0E48 \\u0E27\\u0E32\\u0E07\\u0E1A\\u0E34\\u0E25\", 1, \"custom-select\", \"custom-select-sm\", 3, \"change\"], [\"selected\", \"\", 3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"tooltip\", \"\\u0E40\\u0E25\\u0E37\\u0E2D\\u0E01\\u0E17\\u0E35\\u0E48\\u0E2D\\u0E22\\u0E39\\u0E48 \\u0E02\\u0E19\\u0E2A\\u0E48\\u0E07\", 1, \"custom-select\", \"custom-select-sm\", 3, \"change\"], [1, \"col-md-1\", \"mb-3\"], [\"tooltip\", \"\\u0E40\\u0E25\\u0E37\\u0E2D\\u0E01\\u0E20\\u0E32\\u0E29\\u0E35\", 1, \"custom-select\", \"custom-select-sm\", 3, \"change\", \"disabled\"], [1, \"col-md-1\", \"mb-1\"], [\"tooltip\", \"\\u0E40\\u0E25\\u0E37\\u0E2D\\u0E01\\u0E1B\\u0E23\\u0E30\\u0E40\\u0E20\\u0E17\\u0E01\\u0E32\\u0E23\\u0E02\\u0E19\\u0E2A\\u0E48\\u0E07\", 1, \"custom-select\", \"custom-select-sm\", 3, \"change\"], [1, \"col-md-2\", \"mb-2\", \"form-group\"], [\"type\", \"text\", \"tooltip\", \"Ship Date\", \"placeholder\", \"DD/MM/YYYY\", \"bsDatepicker\", \"\", 1, \"form-control\", \"form-control-sm\", 3, \"bsValueChange\", \"bsValue\", \"value\"], [\"tooltip\", \"\\u0E40\\u0E25\\u0E37\\u0E2D\\u0E01\\u0E01\\u0E32\\u0E23\\u0E0A\\u0E33\\u0E23\\u0E30\\u0E40\\u0E07\\u0E34\\u0E19\", 1, \"custom-select\", \"custom-select-sm\", 3, \"change\", \"disabled\"], [\"selected\", \"\", 3, \"ngValue\", 4, \"ngFor\", \"ngForOf\"], [1, \"col-md-3\", \"mb-2\"], [\"maxlength\", \"60\", \"tooltip\", \"Note \\u0E20\\u0E32\\u0E22\\u0E43\\u0E19\", \"tooltip-show-trigger\", \"focusin\", \"tooltip-hide-trigger\", \"focusout\", \"name\", \"notesoinput\", \"type\", \"text\", \"id\", \"notesoinput\", \"placeholder\", \"Note \\u0E20\\u0E32\\u0E22\\u0E43\\u0E19\", 1, \"form-control\", \"form-control-sm\", 3, \"ngModelChange\", \"input\", \"ngModel\"], [\"tooltip\", \"\\u0E2B\\u0E21\\u0E32\\u0E22\\u0E40\\u0E2B\\u0E15\\u0E38\", \"maxlength\", \"255\", \"name\", \"remarksoinput\", \"type\", \"text\", \"id\", \"remarksoinput\", \"placeholder\", \"\\u0E2B\\u0E21\\u0E32\\u0E22\\u0E40\\u0E2B\\u0E15\\u0E38\", 1, \"form-control\", \"form-control-sm\", 3, \"ngModelChange\", \"focusout\", \"input\", \"ngModel\"], [\"id\", \"headingTwo\", 1, \"card-header\", 2, \"padding-top\", \"0px\", \"padding-bottom\", \"0px\"], [1, \"mb-0\", 2, \"vertical-align\", \"central\"], [\"data-toggle\", \"collapse\", \"data-target\", \"#collapseTwo\", \"aria-expanded\", \"false\", \"aria-controls\", \"collapseTwo\", 1, \"btn\", \"btn-link\", \"collapsed\", 3, \"click\"], [1, \"col\", 2, \"padding-top\", \"7px\"], [\"class\", \"text-success\", 4, \"ngIf\"], [\"id\", \"collapseTwo\", \"aria-labelledby\", \"headingTwo\", \"data-parent\", \"#accordion\", 1, \"collapse\"], [1, \"col-md-2\", \"mb-2\"], [\"id\", \"typeahead-templatep\", \"tooltip\", \"\\u0E1B\\u0E49\\u0E2D\\u0E19 \\u0E23\\u0E2B\\u0E31\\u0E2A\\u0E2A\\u0E34\\u0E19\\u0E04\\u0E49\\u0E32\", \"triggers\", \"focus\", \"type\", \"text\", \"name\", \"productidsearch\", 1, \"form-control\", \"form-control-sm\", 3, \"focusout\", \"ngModelChange\", \"placeholder\", \"ngModel\", \"ngbTypeahead\", \"resultTemplate\", \"inputFormatter\"], [\"type\", \"number\", \"tooltip\", \"\\u0E1B\\u0E49\\u0E2D\\u0E19 \\u0E08\\u0E33\\u0E19\\u0E27\\u0E19\\u0E2A\\u0E34\\u0E19\\u0E04\\u0E49\\u0E32\", \"triggers\", \"focus\", \"id\", \"validationTooltip01\", \"placeholder\", \"\\u0E08\\u0E33\\u0E19\\u0E27\\u0E19\", 1, \"form-control\", \"form-control-sm\", 3, \"ngModelChange\", \"focusin\", \"focusout\", \"ngModel\"], [\"tooltip\", \"\\u0E40\\u0E25\\u0E37\\u0E2D\\u0E01\\u0E2B\\u0E19\\u0E48\\u0E27\\u0E22\\u0E19\\u0E31\\u0E1A\", 1, \"custom-select\", \"custom-select-sm\", 3, \"change\", \"disabled\"], [\"class\", \"col-md-1 mb-1\", 4, \"ngIf\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", \"btn-sm\", \"font-weight-light\", 3, \"click\", \"disabled\"], [1, \"table\", \"table-hover\", \"table-bordered\", \"table-sm\"], [1, \"text-sm-center\", \"bg-light\"], [1, \"font-weight-normal\"], [\"class\", \"font-weight-normal\", 4, \"ngIf\"], [\"width\", \"50px\"], [4, \"ngFor\", \"ngForOf\"], [\"class\", \"border-0 bg-light\", 4, \"ngIf\"], [\"class\", \"text-center bg-light font-weight-normal\", 4, \"ngIf\"], [\"class\", \"text-right bg-light font-weight-normal\", 4, \"ngIf\"], [\"class\", \"bg-light\", 4, \"ngIf\"], [1, \"border-0\", \"bg-light\"], [1, \"text-center\", \"bg-light\", \"font-weight-normal\"], [1, \"text-right\", \"bg-light\", \"font-weight-normal\"], [1, \"bg-light\"], [\"id\", \"mdlSample\", \"role\", \"dialog\", 1, \"modal\", \"fade\", 3, \"ngStyle\"], [1, \"modal-dialog\", \"modal-md\"], [1, \"modal-content\"], [1, \"modal-header\", \"colhaederal\"], [1, \"modal-title\"], [1, \"modal-body\"], [\"align\", \"right\", 1, \"modal-footer\"], [\"type\", \"button\", \"id\", \"btnClose\", 1, \"btn\", \"btn-danger\", 3, \"click\"], [1, \"fa\", \"fa-times\", \"fa-fw\"], [\"id\", \"testmessage\", \"tabindex\", \"-1\", \"role\", \"dialog\", \"aria-labelledby\", \"exampleModalLabel\", \"aria-hidden\", \"true\", 1, \"modal\", \"fade\"], [\"role\", \"document\", 1, \"modal-dialog\"], [1, \"modal-header\"], [\"id\", \"exampleModalLabel\", 1, \"modal-title\"], [\"type\", \"button\", \"data-dismiss\", \"modal\", \"aria-label\", \"Close\", 1, \"close\"], [\"aria-hidden\", \"true\"], [1, \"modal-footer\"], [\"type\", \"button\", \"data-dismiss\", \"modal\", 1, \"btn\", \"btn-secondary\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\"], [\"type\", \"submit\", 1, \"btn\", \"btn-warning\", \"btn-sm\", \"font-weight-light\", 2, \"width\", \"45px\", \"height\", \"26px\", \"padding\", \"0px\", 3, \"click\", \"disabled\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", \"btn-sm\", \"font-weight-light\", 2, \"width\", \"45px\", \"height\", \"26px\", \"padding\", \"0px\", 3, \"click\", \"disabled\"], [\"type\", \"button\", 1, \"btn\", \"btn-sm\", \"btn-danger\", 2, \"margin-left\", \"2%\", 3, \"click\"], [3, \"click\"], [3, \"mousedown\"], [\"selected\", \"\", 3, \"value\"], [\"selected\", \"\", 3, \"ngValue\"], [1, \"text-success\"], [3, \"mousedown\", \"click\"], [\"for\", \"typeahead-templatep\"], [\"type\", \"text\", \"id\", \"validationTooltip01\", \"tooltip\", \"\\u0E23\\u0E32\\u0E04\\u0E32\\u0E15\\u0E31\\u0E49\\u0E07\", \"placeholder\", \"\\u0E23\\u0E32\\u0E04\\u0E32/\\u0E2B\\u0E19\\u0E48\\u0E27\\u0E22\", \"required\", \"\", 1, \"form-control\", \"form-control-sm\", \"text-right\", 3, \"focusout\", \"disabled\", \"value\"], [\"type\", \"number\", \"tooltip\", \"\\u0E1B\\u0E49\\u0E2D\\u0E19 %\\u0E2A\\u0E48\\u0E27\\u0E19\\u0E25\\u0E14#1\", \"triggers\", \"focus\", \"id\", \"validationTooltip01\", \"placeholder\", \"%\\u0E2A\\u0E48\\u0E27\\u0E19\\u0E25\\u0E14#1\", \"required\", \"\", 1, \"form-control\", \"form-control-sm\", 3, \"focusout\", \"ngModelChange\", \"disabled\", \"ngModel\"], [\"type\", \"text\", \"tooltip\", \"\\u0E1B\\u0E49\\u0E2D\\u0E19 %\\u0E2A\\u0E48\\u0E27\\u0E19\\u0E25\\u0E14#2\", \"triggers\", \"focus\", \"id\", \"validationTooltip01\", \"placeholder\", \"%\\u0E2A\\u0E48\\u0E27\\u0E19\\u0E25\\u0E14#2\", \"required\", \"\", 1, \"form-control\", \"form-control-sm\", 3, \"focusout\", \"ngModelChange\", \"disabled\", \"ngModel\"], [\"type\", \"text\", \"tooltip\", \"\\u0E1B\\u0E49\\u0E2D\\u0E19 %\\u0E2A\\u0E48\\u0E27\\u0E19\\u0E25\\u0E14#3\", \"triggers\", \"focus\", \"id\", \"validationTooltip01\", \"placeholder\", \"%\\u0E2A\\u0E48\\u0E27\\u0E19\\u0E25\\u0E14#3\", \"required\", \"\", 1, \"form-control\", \"form-control-sm\", 3, \"focusout\", \"ngModelChange\", \"disabled\", \"ngModel\"], [\"type\", \"text\", \"tooltip\", \"\\u0E23\\u0E27\\u0E21\\u0E2A\\u0E48\\u0E27\\u0E19\\u0E25\\u0E14\", \"placeholder\", \"\\u0E23\\u0E27\\u0E21\\u0E2A\\u0E48\\u0E27\\u0E19\\u0E25\\u0E14\", \"readonly\", \"\", 1, \"form-control\", \"currency\", \"form-control-sm\", \"text-right\", 3, \"value\"], [\"type\", \"text\", \"tooltip\", \"\\u0E21\\u0E39\\u0E25\\u0E04\\u0E48\\u0E32\\u0E2A\\u0E34\\u0E19\\u0E04\\u0E49\\u0E32\", \"placeholder\", \"\\u0E21\\u0E39\\u0E25\\u0E04\\u0E48\\u0E32\\u0E2A\\u0E34\\u0E19\\u0E04\\u0E49\\u0E32\", \"readonly\", \"\", 1, \"form-control\", \"currency\", \"form-control-sm\", \"text-right\", 3, \"value\"], [1, \"form-group\", \"form-check\"], [\"type\", \"checkbox\", \"id\", \"exampleCheck1\", 1, \"form-control\", 3, \"ngModelChange\", \"click\", \"ngModel\"], [\"for\", \"exampleCheck1\", 1, \"form-check-label\"], [1, \"text-center\"], [1, \"text-right\", \"font-weight-normal\"], [1, \"text-center\", \"font-weight-normal\"], [\"class\", \"text-right font-weight-normal\", 4, \"ngIf\"], [\"class\", \"text-right  font-weight-normal\", 3, \"ngStyle\", 4, \"ngIf\"], [\"class\", \"text-right font-weight-normal\", 3, \"ngStyle\", 4, \"ngIf\"], [1, \"text-info\", \"btn-link\", \"btn-sm\", 3, \"click\"], [1, \"text-danger\", \"btn-link\", \"btn-sm\", 3, \"click\"], [1, \"text-right\", \"font-weight-normal\", 3, \"ngStyle\"]],\n        template: function SorecordComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            const _r1 = i0.ɵɵgetCurrentView();\n            i0.ɵɵelement(0, \"app-topmenu\");\n            i0.ɵɵelementStart(1, \"div\", 2)(2, \"section\", 3)(3, \"h5\", 4);\n            i0.ɵɵtext(4, \"Sale Order Record\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(5, \"div\", 5)(6, \"div\", 6)(7, \"div\", 7)(8, \"div\", 8)(9, \"h5\", 9)(10, \"button\", 10);\n            i0.ɵɵtext(11, \" Sale Order Header \");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(12, \"div\", 11);\n            i0.ɵɵtemplate(13, SorecordComponent_button_13_Template, 2, 1, \"button\", 12)(14, SorecordComponent_button_14_Template, 2, 1, \"button\", 13);\n            i0.ɵɵelementStart(15, \"label\", 14);\n            i0.ɵɵtext(16);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(17, SorecordComponent_button_17_Template, 2, 0, \"button\", 15);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(18, \"div\", 16)(19, \"div\", 17)(20, \"div\", 18)(21, \"form\", 19)(22, \"div\", 8)(23, \"div\", 20);\n            i0.ɵɵtemplate(24, SorecordComponent_ng_template_24_Template, 3, 3, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n            i0.ɵɵelementStart(26, \"input\", 21);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function SorecordComponent_Template_input_ngModelChange_26_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.model, $event) || (ctx.model = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(27, \"div\", 20)(28, \"select\", 22);\n            i0.ɵɵlistener(\"change\", function SorecordComponent_Template_select_change_28_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.setlocationno($event.target.value));\n            });\n            i0.ɵɵtemplate(29, SorecordComponent_option_29_Template, 2, 3, \"option\", 23);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(30, \"div\", 20)(31, \"select\", 24);\n            i0.ɵɵlistener(\"change\", function SorecordComponent_Template_select_change_31_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.selectlocationde($event.target.value));\n            });\n            i0.ɵɵtemplate(32, SorecordComponent_option_32_Template, 2, 3, \"option\", 23);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(33, \"div\", 8)(34, \"div\", 25)(35, \"select\", 26);\n            i0.ɵɵlistener(\"change\", function SorecordComponent_Template_select_change_35_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.selectvat($event.target.value));\n            });\n            i0.ɵɵtemplate(36, SorecordComponent_option_36_Template, 2, 2, \"option\", 23);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(37, \"div\", 27)(38, \"select\", 28);\n            i0.ɵɵlistener(\"change\", function SorecordComponent_Template_select_change_38_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.selectdelivery($event.target.value));\n            });\n            i0.ɵɵtemplate(39, SorecordComponent_option_39_Template, 2, 2, \"option\", 23);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(40, \"div\", 29)(41, \"input\", 30);\n            i0.ɵɵpipe(42, \"date\");\n            i0.ɵɵtwoWayListener(\"bsValueChange\", function SorecordComponent_Template_input_bsValueChange_41_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.bsValue, $event) || (ctx.bsValue = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵlistener(\"bsValueChange\", function SorecordComponent_Template_input_bsValueChange_41_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.clickdate($event));\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(43, \"div\", 27)(44, \"select\", 31);\n            i0.ɵɵlistener(\"change\", function SorecordComponent_Template_select_change_44_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.selectpaymentfn($event.target.value));\n            });\n            i0.ɵɵtemplate(45, SorecordComponent_option_45_Template, 2, 2, \"option\", 32);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(46, \"div\", 33)(47, \"input\", 34);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function SorecordComponent_Template_input_ngModelChange_47_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.notesoinput, $event) || (ctx.notesoinput = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵlistener(\"input\", function SorecordComponent_Template_input_input_47_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.checklength($event.target.value, 60));\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(48, \"div\", 20)(49, \"input\", 35);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function SorecordComponent_Template_input_ngModelChange_49_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.remarksoinput, $event) || (ctx.remarksoinput = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵlistener(\"focusout\", function SorecordComponent_Template_input_focusout_49_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.CheckValueRemark($event.target.value));\n            })(\"input\", function SorecordComponent_Template_input_input_49_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.checklength($event.target.value, 255));\n            });\n            i0.ɵɵelementEnd()()()()()()()();\n            i0.ɵɵelementStart(50, \"div\", 6)(51, \"div\", 36)(52, \"div\", 8)(53, \"h5\", 37)(54, \"button\", 38);\n            i0.ɵɵlistener(\"click\", function SorecordComponent_Template_button_click_54_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.selectsaleoderline());\n            });\n            i0.ɵɵtext(55, \" Product Line Item \");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(56, \"div\", 39);\n            i0.ɵɵtemplate(57, SorecordComponent_label_57_Template, 2, 1, \"label\", 40);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(58, \"div\", 41)(59, \"div\", 17)(60, \"div\", 8)(61, \"div\", 42);\n            i0.ɵɵtemplate(62, SorecordComponent_ng_template_62_Template, 3, 2, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n            i0.ɵɵelementStart(64, \"input\", 43);\n            i0.ɵɵlistener(\"focusout\", function SorecordComponent_Template_input_focusout_64_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.Searchitem());\n            });\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function SorecordComponent_Template_input_ngModelChange_64_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.productidsearch, $event) || (ctx.productidsearch = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(65, \"div\", 27)(66, \"input\", 44);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function SorecordComponent_Template_input_ngModelChange_66_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.numberproductsearch, $event) || (ctx.numberproductsearch = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵlistener(\"focusin\", function SorecordComponent_Template_input_focusin_66_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.Searchitem());\n            })(\"focusout\", function SorecordComponent_Template_input_focusout_66_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.checknumproduct($event.target.value));\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(67, \"div\", 27)(68, \"select\", 45);\n            i0.ɵɵlistener(\"change\", function SorecordComponent_Template_select_change_68_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.getunitid($event));\n            });\n            i0.ɵɵtemplate(69, SorecordComponent_option_69_Template, 2, 2, \"option\", 23);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵtemplate(70, SorecordComponent_div_70_Template, 3, 4, \"div\", 46)(71, SorecordComponent_div_71_Template, 2, 2, \"div\", 46)(72, SorecordComponent_div_72_Template, 2, 2, \"div\", 46)(73, SorecordComponent_div_73_Template, 2, 2, \"div\", 46)(74, SorecordComponent_div_74_Template, 3, 3, \"div\", 46)(75, SorecordComponent_div_75_Template, 3, 3, \"div\", 46);\n            i0.ɵɵelementStart(76, \"div\", 27)(77, \"button\", 47);\n            i0.ɵɵlistener(\"click\", function SorecordComponent_Template_button_click_77_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.addsaleline());\n            });\n            i0.ɵɵtext(78, \"\\u0E40\\u0E1E\\u0E34\\u0E48\\u0E21\\u0E23\\u0E32\\u0E22\\u0E01\\u0E32\\u0E23\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵtemplate(79, SorecordComponent_div_79_Template, 5, 1, \"div\", 46);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(80, \"table\", 48)(81, \"thead\")(82, \"tr\", 49)(83, \"th\", 50);\n            i0.ɵɵtext(84, \"Item\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(85, \"th\", 50);\n            i0.ɵɵtext(86, \"\\u0E23\\u0E2B\\u0E31\\u0E2A\\u0E2A\\u0E34\\u0E19\\u0E04\\u0E49\\u0E32\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(87, \"th\", 50);\n            i0.ɵɵtext(88, \"\\u0E0A\\u0E37\\u0E48\\u0E2D\\u0E2A\\u0E34\\u0E19\\u0E04\\u0E49\\u0E32\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(89, \"th\", 50);\n            i0.ɵɵtext(90, \"\\u0E08\\u0E33\\u0E19\\u0E27\\u0E19\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(91, \"th\", 50);\n            i0.ɵɵtext(92, \"\\u0E2B\\u0E19\\u0E48\\u0E27\\u0E22\\u0E19\\u0E31\\u0E1A\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(93, \"th\", 50);\n            i0.ɵɵtext(94, \"PACK\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(95, \"th\", 50);\n            i0.ɵɵtext(96, \"\\u0E19\\u0E49\\u0E33\\u0E2B\\u0E19\\u0E31\\u0E01(KG.)\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(97, SorecordComponent_th_97_Template, 2, 0, \"th\", 51)(98, SorecordComponent_th_98_Template, 2, 0, \"th\", 51)(99, SorecordComponent_th_99_Template, 2, 0, \"th\", 51)(100, SorecordComponent_th_100_Template, 2, 0, \"th\", 51)(101, SorecordComponent_th_101_Template, 2, 0, \"th\", 51)(102, SorecordComponent_th_102_Template, 2, 0, \"th\", 51)(103, SorecordComponent_th_103_Template, 2, 0, \"th\", 51)(104, SorecordComponent_th_104_Template, 2, 0, \"th\", 51);\n            i0.ɵɵelement(105, \"th\", 52)(106, \"th\", 52);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵtemplate(107, SorecordComponent_tr_107_Template, 31, 21, \"tr\", 53);\n            i0.ɵɵelementStart(108, \"tr\");\n            i0.ɵɵtemplate(109, SorecordComponent_td_109_Template, 1, 0, \"td\", 54)(110, SorecordComponent_td_110_Template, 1, 0, \"td\", 54)(111, SorecordComponent_td_111_Template, 1, 0, \"td\", 54)(112, SorecordComponent_td_112_Template, 1, 0, \"td\", 54)(113, SorecordComponent_td_113_Template, 1, 0, \"td\", 54)(114, SorecordComponent_td_114_Template, 1, 0, \"td\", 54)(115, SorecordComponent_td_115_Template, 1, 0, \"td\", 54)(116, SorecordComponent_td_116_Template, 1, 0, \"td\", 54)(117, SorecordComponent_td_117_Template, 1, 0, \"td\", 54)(118, SorecordComponent_td_118_Template, 1, 0, \"td\", 54)(119, SorecordComponent_td_119_Template, 1, 0, \"td\", 54)(120, SorecordComponent_td_120_Template, 1, 0, \"td\", 54)(121, SorecordComponent_td_121_Template, 2, 0, \"td\", 55)(122, SorecordComponent_td_122_Template, 3, 4, \"td\", 56)(123, SorecordComponent_td_123_Template, 3, 4, \"td\", 56)(124, SorecordComponent_td_124_Template, 1, 0, \"td\", 57)(125, SorecordComponent_td_125_Template, 1, 0, \"td\", 57);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(126, \"tr\");\n            i0.ɵɵelement(127, \"td\", 58)(128, \"td\", 58)(129, \"td\", 58)(130, \"td\", 58);\n            i0.ɵɵtemplate(131, SorecordComponent_td_131_Template, 1, 0, \"td\", 54)(132, SorecordComponent_td_132_Template, 1, 0, \"td\", 54)(133, SorecordComponent_td_133_Template, 1, 0, \"td\", 54)(134, SorecordComponent_td_134_Template, 1, 0, \"td\", 54)(135, SorecordComponent_td_135_Template, 1, 0, \"td\", 54)(136, SorecordComponent_td_136_Template, 1, 0, \"td\", 54)(137, SorecordComponent_td_137_Template, 1, 0, \"td\", 54)(138, SorecordComponent_td_138_Template, 1, 0, \"td\", 54);\n            i0.ɵɵelementStart(139, \"td\", 59);\n            i0.ɵɵtext(140, \"\\u0E19\\u0E49\\u0E33\\u0E2B\\u0E19\\u0E31\\u0E01\\u0E23\\u0E27\\u0E21(KG.)\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(141, \"td\", 60);\n            i0.ɵɵtext(142);\n            i0.ɵɵpipe(143, \"number\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(144, \"td\", 58)(145, \"td\", 61)(146, \"td\", 61);\n            i0.ɵɵelementEnd()()()()()();\n            i0.ɵɵelementStart(147, \"div\", 62)(148, \"div\", 63)(149, \"div\", 64)(150, \"div\", 65)(151, \"h4\", 66);\n            i0.ɵɵtext(152, \"Report\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(153, \"div\", 67);\n            i0.ɵɵtext(154);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(155, \"div\", 68)(156, \"button\", 69);\n            i0.ɵɵlistener(\"click\", function SorecordComponent_Template_button_click_156_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.closemodel(false));\n            });\n            i0.ɵɵelement(157, \"i\", 70);\n            i0.ɵɵtext(158, \" \\u0E1B\\u0E34\\u0E14\");\n            i0.ɵɵelementEnd()()()()();\n            i0.ɵɵelementStart(159, \"div\", 71)(160, \"div\", 72)(161, \"div\", 64)(162, \"div\", 73)(163, \"h5\", 74);\n            i0.ɵɵtext(164, \"Modal title\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(165, \"button\", 75)(166, \"span\", 76);\n            i0.ɵɵtext(167, \"\\u00D7\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(168, \"div\", 67);\n            i0.ɵɵtext(169, \" ... \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(170, \"div\", 77)(171, \"button\", 78);\n            i0.ɵɵtext(172, \"Close\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(173, \"button\", 79);\n            i0.ɵɵtext(174, \"Save changes\");\n            i0.ɵɵelementEnd()()()()()()();\n          }\n          if (rf & 2) {\n            const rt_r25 = i0.ɵɵreference(25);\n            const rtpr_r26 = i0.ɵɵreference(63);\n            i0.ɵɵadvance(13);\n            i0.ɵɵproperty(\"ngIf\", ctx.waitsave);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.waitsave);\n            i0.ɵɵadvance();\n            i0.ɵɵclassMapInterpolate1(\" \", ctx.CheckSoidWord, \"\");\n            i0.ɵɵadvance();\n            i0.ɵɵtextInterpolate1(\" \\u0E40\\u0E25\\u0E02\\u0E17\\u0E35\\u0E48 SO WEB: \", ctx.idforshow, \" \");\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.CheckSoidbtn);\n            i0.ɵɵadvance(9);\n            i0.ɵɵpropertyInterpolate(\"placeholder\", ctx.company);\n            i0.ɵɵproperty(\"disabled\", ctx.ennablecustomer);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.model);\n            i0.ɵɵproperty(\"ngbTypeahead\", ctx.search)(\"resultTemplate\", rt_r25)(\"inputFormatter\", ctx.formatter);\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"ngForOf\", ctx.inaddress);\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"ngForOf\", ctx.daddress);\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"disabled\", ctx.ennablecustomer);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngForOf\", ctx.vatlist);\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"ngForOf\", ctx.deliverytype);\n            i0.ɵɵadvance(2);\n            i0.ɵɵpropertyInterpolate1(\"value\", \"\", i0.ɵɵpipeBind2(42, 79, ctx.bsValue, \"dd/MM/yyyy\"), \" \");\n            i0.ɵɵtwoWayProperty(\"bsValue\", ctx.bsValue);\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"disabled\", ctx.ennablecustomer);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngForOf\", ctx.paymentlist);\n            i0.ɵɵadvance(2);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.notesoinput);\n            i0.ɵɵadvance(2);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.remarksoinput);\n            i0.ɵɵadvance(8);\n            i0.ɵɵproperty(\"ngIf\", ctx.Instock != \"\" && ctx.ennablecustomer == false);\n            i0.ɵɵadvance(7);\n            i0.ɵɵpropertyInterpolate(\"placeholder\", ctx.showitem);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.productidsearch);\n            i0.ɵɵproperty(\"ngbTypeahead\", ctx.searchpr)(\"resultTemplate\", rtpr_r26)(\"inputFormatter\", ctx.formatterpr);\n            i0.ɵɵadvance(2);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.numberproductsearch);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"disabled\", ctx.selectedunit);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngForOf\", ctx.unitlist);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.ennablecustomer == false);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.ennablecustomer == false);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.ennablecustomer == false);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.ennablecustomer == false);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.ennablecustomer == false);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.ennablecustomer == false);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"disabled\", ctx.actionbtn);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngIf\", ctx.ennablecustomer == false);\n            i0.ɵɵadvance(18);\n            i0.ɵɵproperty(\"ngIf\", ctx.ennablecustomer == false);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.ennablecustomer == false);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.ennablecustomer == false);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.ennablecustomer == false);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.ennablecustomer == false);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.ennablecustomer == false);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.ennablecustomer == false);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.ennablecustomer == false);\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"ngForOf\", ctx.lineoderlist);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngIf\", ctx.ennablecustomer == false);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.ennablecustomer == false);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.ennablecustomer == false);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.ennablecustomer == false);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.ennablecustomer == false);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.ennablecustomer == false);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.ennablecustomer == false);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.ennablecustomer == false);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.ennablecustomer == false);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.ennablecustomer == false);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.ennablecustomer == false);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.ennablecustomer == false);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.ennablecustomer == false);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.ennablecustomer == false);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.ennablecustomer == false);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.ennablecustomer == false);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.ennablecustomer == false);\n            i0.ɵɵadvance(6);\n            i0.ɵɵproperty(\"ngIf\", ctx.ennablecustomer == false);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.ennablecustomer == false);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.ennablecustomer == false);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.ennablecustomer == false);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.ennablecustomer == false);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.ennablecustomer == false);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.ennablecustomer == false);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.ennablecustomer == false);\n            i0.ɵɵadvance(4);\n            i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(143, 82, ctx.sumallweight, \"1.2-2\"));\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(85, _c0, ctx.mdlSampleIsOpen ? \"block\" : \"none\"));\n            i0.ɵɵadvance(7);\n            i0.ɵɵtextInterpolate1(\"\", ctx.alt, \" \");\n          }\n        },\n        styles: [\".example-form[_ngcontent-%COMP%]{min-width:150px;max-width:500px;width:100%}.example-full-width[_ngcontent-%COMP%]{width:100%;height:50%}.container[_ngcontent-%COMP%]{width:300px;font:17px Calibri}\"]\n      });\n    }\n  }\n  return SorecordComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}