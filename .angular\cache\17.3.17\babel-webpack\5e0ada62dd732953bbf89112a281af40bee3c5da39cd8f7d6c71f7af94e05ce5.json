{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { PLATFORM_ID, Injectable, Inject } from '@angular/core';\nimport { isPlatformBrowser, DOCUMENT } from '@angular/common';\n\n// This service is based on the `ng2-cookies` package which sadly is not a service and does\n// not use `DOCUMENT` injection and therefore doesn't work well with AoT production builds.\n// Package: https://github.com/BCJTI/ng2-cookies\nlet CookieService = /*#__PURE__*/(() => {\n  class CookieService {\n    constructor(document,\n    // Get the `PLATFORM_ID` so we can check if we're in a browser.\n    platformId) {\n      this.document = document;\n      this.platformId = platformId;\n      this.documentIsAccessible = isPlatformBrowser(this.platformId);\n    }\n    /**\n     * Get cookie Regular Expression\n     *\n     * @param name Cookie name\n     * @returns property RegExp\n     *\n     * @author: Stepan <PERSON>vorov\n     * @since: 1.0.0\n     */\n    static getCookieRegExp(name) {\n      const escapedName = name.replace(/([\\[\\]{}()|=;+?,.*^$])/gi, '\\\\$1');\n      return new RegExp('(?:^' + escapedName + '|;\\\\s*' + escapedName + ')=(.*?)(?:;|$)', 'g');\n    }\n    /**\n     * Gets the unencoded version of an encoded component of a Uniform Resource Identifier (URI).\n     *\n     * @param encodedURIComponent A value representing an encoded URI component.\n     *\n     * @returns The unencoded version of an encoded component of a Uniform Resource Identifier (URI).\n     *\n     * @author: Stepan Suvorov\n     * @since: 1.0.0\n     */\n    static safeDecodeURIComponent(encodedURIComponent) {\n      try {\n        return decodeURIComponent(encodedURIComponent);\n      } catch {\n        // probably it is not uri encoded. return as is\n        return encodedURIComponent;\n      }\n    }\n    /**\n     * Return `true` if {@link Document} is accessible, otherwise return `false`\n     *\n     * @param name Cookie name\n     * @returns boolean - whether cookie with specified name exists\n     *\n     * @author: Stepan Suvorov\n     * @since: 1.0.0\n     */\n    check(name) {\n      if (!this.documentIsAccessible) {\n        return false;\n      }\n      name = encodeURIComponent(name);\n      const regExp = CookieService.getCookieRegExp(name);\n      return regExp.test(this.document.cookie);\n    }\n    /**\n     * Get cookies by name\n     *\n     * @param name Cookie name\n     * @returns property value\n     *\n     * @author: Stepan Suvorov\n     * @since: 1.0.0\n     */\n    get(name) {\n      if (this.documentIsAccessible && this.check(name)) {\n        name = encodeURIComponent(name);\n        const regExp = CookieService.getCookieRegExp(name);\n        const result = regExp.exec(this.document.cookie);\n        return result[1] ? CookieService.safeDecodeURIComponent(result[1]) : '';\n      } else {\n        return '';\n      }\n    }\n    /**\n     * Get all cookies in JSON format\n     *\n     * @returns all the cookies in json\n     *\n     * @author: Stepan Suvorov\n     * @since: 1.0.0\n     */\n    getAll() {\n      if (!this.documentIsAccessible) {\n        return {};\n      }\n      const cookies = {};\n      const document = this.document;\n      if (document.cookie && document.cookie !== '') {\n        document.cookie.split(';').forEach(currentCookie => {\n          const [cookieName, cookieValue] = currentCookie.split('=');\n          cookies[CookieService.safeDecodeURIComponent(cookieName.replace(/^ /, ''))] = CookieService.safeDecodeURIComponent(cookieValue);\n        });\n      }\n      return cookies;\n    }\n    set(name, value, expiresOrOptions, path, domain, secure, sameSite, partitioned) {\n      if (!this.documentIsAccessible) {\n        return;\n      }\n      if (typeof expiresOrOptions === 'number' || expiresOrOptions instanceof Date || path || domain || secure || sameSite) {\n        const optionsBody = {\n          expires: expiresOrOptions,\n          path,\n          domain,\n          secure,\n          sameSite: sameSite ? sameSite : 'Lax',\n          partitioned\n        };\n        this.set(name, value, optionsBody);\n        return;\n      }\n      let cookieString = encodeURIComponent(name) + '=' + encodeURIComponent(value) + ';';\n      const options = expiresOrOptions ? expiresOrOptions : {};\n      if (options.expires) {\n        if (typeof options.expires === 'number') {\n          const dateExpires = new Date(new Date().getTime() + options.expires * 1000 * 60 * 60 * 24);\n          cookieString += 'expires=' + dateExpires.toUTCString() + ';';\n        } else {\n          cookieString += 'expires=' + options.expires.toUTCString() + ';';\n        }\n      }\n      if (options.path) {\n        cookieString += 'path=' + options.path + ';';\n      }\n      if (options.domain) {\n        cookieString += 'domain=' + options.domain + ';';\n      }\n      if (options.secure === false && options.sameSite === 'None') {\n        options.secure = true;\n        console.warn(`[ngx-cookie-service] Cookie ${name} was forced with secure flag because sameSite=None.` + `More details : https://github.com/stevermeister/ngx-cookie-service/issues/86#issuecomment-597720130`);\n      }\n      if (options.secure) {\n        cookieString += 'secure;';\n      }\n      if (!options.sameSite) {\n        options.sameSite = 'Lax';\n      }\n      cookieString += 'sameSite=' + options.sameSite + ';';\n      if (options.partitioned) {\n        cookieString += 'Partitioned;';\n      }\n      this.document.cookie = cookieString;\n    }\n    /**\n     * Delete cookie by name\n     *\n     * @param name   Cookie name\n     * @param path   Cookie path\n     * @param domain Cookie domain\n     * @param secure Cookie secure flag\n     * @param sameSite Cookie sameSite flag - https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Set-Cookie/SameSite\n     *\n     * @author: Stepan Suvorov\n     * @since: 1.0.0\n     */\n    delete(name, path, domain, secure, sameSite = 'Lax') {\n      if (!this.documentIsAccessible) {\n        return;\n      }\n      const expiresDate = new Date('Thu, 01 Jan 1970 00:00:01 GMT');\n      this.set(name, '', {\n        expires: expiresDate,\n        path,\n        domain,\n        secure,\n        sameSite\n      });\n    }\n    /**\n     * Delete all cookies\n     *\n     * @param path   Cookie path\n     * @param domain Cookie domain\n     * @param secure Is the Cookie secure\n     * @param sameSite Is the cookie same site\n     *\n     * @author: Stepan Suvorov\n     * @since: 1.0.0\n     */\n    deleteAll(path, domain, secure, sameSite = 'Lax') {\n      if (!this.documentIsAccessible) {\n        return;\n      }\n      const cookies = this.getAll();\n      for (const cookieName in cookies) {\n        if (cookies.hasOwnProperty(cookieName)) {\n          this.delete(cookieName, path, domain, secure, sameSite);\n        }\n      }\n    }\n    static {\n      this.ɵfac = function CookieService_Factory(t) {\n        return new (t || CookieService)(i0.ɵɵinject(DOCUMENT), i0.ɵɵinject(PLATFORM_ID));\n      };\n    }\n    static {\n      this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n        token: CookieService,\n        factory: CookieService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return CookieService;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/*\n * Public API Surface of ngx-cookie-service\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { CookieService };\n//# sourceMappingURL=ngx-cookie-service.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}