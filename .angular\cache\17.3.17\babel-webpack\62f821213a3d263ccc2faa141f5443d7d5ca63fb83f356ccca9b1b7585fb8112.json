{"ast": null, "code": "var dataTable_directive = require('./lib/src/DataTable');\nvar defaultSorter_directive = require('./lib/src/DefaultSorter');\nvar paginator_component = require('./lib/src/Paginator');\nvar bootstrapPaginator_component = require('./lib/src/BootstrapPaginator');\nvar dataTable_module = require('./lib/src/DataTableModule');\nexports.DataTable = dataTable_directive.DataTable;\nexports.DataEvent = dataTable_directive.DataEvent;\nexports.PageEvent = dataTable_directive.PageEvent;\nexports.SortEvent = dataTable_directive.SortEvent;\nexports.DefaultSorter = defaultSorter_directive.DefaultSorter;\nexports.Paginator = paginator_component.Paginator;\nexports.BootstrapPaginator = bootstrapPaginator_component.BootstrapPaginator;\nexports.DataTableModule = dataTable_module.DataTableModule;", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}