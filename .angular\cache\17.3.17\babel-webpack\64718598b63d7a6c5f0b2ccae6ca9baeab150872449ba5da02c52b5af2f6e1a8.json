{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.scheduled = void 0;\nvar scheduleObservable_1 = require(\"./scheduleObservable\");\nvar schedulePromise_1 = require(\"./schedulePromise\");\nvar scheduleArray_1 = require(\"./scheduleArray\");\nvar scheduleIterable_1 = require(\"./scheduleIterable\");\nvar scheduleAsyncIterable_1 = require(\"./scheduleAsyncIterable\");\nvar isInteropObservable_1 = require(\"../util/isInteropObservable\");\nvar isPromise_1 = require(\"../util/isPromise\");\nvar isArrayLike_1 = require(\"../util/isArrayLike\");\nvar isIterable_1 = require(\"../util/isIterable\");\nvar isAsyncIterable_1 = require(\"../util/isAsyncIterable\");\nvar throwUnobservableError_1 = require(\"../util/throwUnobservableError\");\nvar isReadableStreamLike_1 = require(\"../util/isReadableStreamLike\");\nvar scheduleReadableStreamLike_1 = require(\"./scheduleReadableStreamLike\");\nfunction scheduled(input, scheduler) {\n  if (input != null) {\n    if (isInteropObservable_1.isInteropObservable(input)) {\n      return scheduleObservable_1.scheduleObservable(input, scheduler);\n    }\n    if (isArrayLike_1.isArrayLike(input)) {\n      return scheduleArray_1.scheduleArray(input, scheduler);\n    }\n    if (isPromise_1.isPromise(input)) {\n      return schedulePromise_1.schedulePromise(input, scheduler);\n    }\n    if (isAsyncIterable_1.isAsyncIterable(input)) {\n      return scheduleAsyncIterable_1.scheduleAsyncIterable(input, scheduler);\n    }\n    if (isIterable_1.isIterable(input)) {\n      return scheduleIterable_1.scheduleIterable(input, scheduler);\n    }\n    if (isReadableStreamLike_1.isReadableStreamLike(input)) {\n      return scheduleReadableStreamLike_1.scheduleReadableStreamLike(input, scheduler);\n    }\n  }\n  throw throwUnobservableError_1.createInvalidObservableTypeError(input);\n}\nexports.scheduled = scheduled;\n//# sourceMappingURL=scheduled.js.map", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}