{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nvar core_1 = require(\"@angular/core\");\nvar DataTable_1 = require(\"./DataTable\");\nvar Paginator = function () {\n  function Paginator(injectMfTable) {\n    var _this = this;\n    this.injectMfTable = injectMfTable;\n    this.dataLength = 0;\n    this.onPageChangeSubscriber = function (event) {\n      _this.activePage = event.activePage;\n      _this.rowsOnPage = event.rowsOnPage;\n      _this.dataLength = event.dataLength;\n      _this.lastPage = Math.ceil(_this.dataLength / _this.rowsOnPage);\n    };\n  }\n  Paginator.prototype.ngOnChanges = function (changes) {\n    this.mfTable = this.inputMfTable || this.injectMfTable;\n    this.onPageChangeSubscriber(this.mfTable.getPage());\n    this.mfTable.onPageChange.subscribe(this.onPageChangeSubscriber);\n  };\n  Paginator.prototype.setPage = function (pageNumber) {\n    this.mfTable.setPage(pageNumber, this.rowsOnPage);\n  };\n  Paginator.prototype.setRowsOnPage = function (rowsOnPage) {\n    this.mfTable.setPage(this.activePage, rowsOnPage);\n  };\n  Paginator.decorators = [{\n    type: core_1.Component,\n    args: [{\n      selector: \"mfPaginator\",\n      template: \"<ng-content></ng-content>\"\n    }]\n  }];\n  Paginator.ctorParameters = function () {\n    return [{\n      type: DataTable_1.DataTable,\n      decorators: [{\n        type: core_1.Optional\n      }]\n    }];\n  };\n  Paginator.propDecorators = {\n    \"inputMfTable\": [{\n      type: core_1.Input,\n      args: [\"mfTable\"]\n    }]\n  };\n  return Paginator;\n}();\nexports.Paginator = Paginator;\n//# sourceMappingURL=Paginator.js.map", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}