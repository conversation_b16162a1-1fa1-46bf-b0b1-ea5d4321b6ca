{"ast": null, "code": "var __decorate = this && this.__decorate || function (decorators, target, key, desc) {\n  var c = arguments.length,\n    r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc,\n    d;\n  if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n  return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nvar __metadata = this && this.__metadata || function (k, v) {\n  if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(k, v);\n};\nimport { Component, NgModule } from '@angular/core';\nimport { HttpClientModule } from '@angular/common/http';\n// tslint:disable-next-line:import-spacing\nimport { Chart } from 'node_modules/chart.js/dist/chart.js';\nlet TrendproductComponent = class TrendproductComponent {\n  constructor() {}\n  ngOnInit() {\n    this.trendchart = new Chart('myChart', {\n      type: 'bar',\n      data: {\n        labels: ['Red', 'Blue', 'Yellow', 'Green', 'Purple', 'Orange'],\n        datasets: [{\n          label: '# of Votes',\n          data: [12, 19, 3, 5, 2, 3],\n          backgroundColor: ['rgba(255, 99, 132, 0.2)', 'rgba(54, 162, 235, 0.2)', 'rgba(255, 206, 86, 0.2)', 'rgba(75, 192, 192, 0.2)', 'rgba(153, 102, 255, 0.2)', 'rgba(255, 159, 64, 0.2)'],\n          borderColor: ['rgba(255,99,132,1)', 'rgba(54, 162, 235, 1)', 'rgba(255, 206, 86, 1)', 'rgba(75, 192, 192, 1)', 'rgba(153, 102, 255, 1)', 'rgba(255, 159, 64, 1)'],\n          borderWidth: 1\n        }]\n      },\n      options: {\n        scales: {\n          yAxes: [{\n            ticks: {\n              beginAtZero: true\n            }\n          }]\n        }\n      }\n    });\n  }\n};\nTrendproductComponent = __decorate([Component({\n  selector: 'app-trendproduct',\n  templateUrl: './trendproduct.component.html',\n  styleUrls: ['./trendproduct.component.css']\n}), NgModule({\n  imports: [HttpClientModule // Add this\n  ],\n  providers: [] // Add this\n}), __metadata(\"design:paramtypes\", [])], TrendproductComponent);\nexport { TrendproductComponent };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}