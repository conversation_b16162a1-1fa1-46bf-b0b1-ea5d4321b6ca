{"ast": null, "code": "/* global define, KeyboardEvent, module */\n\n(function () {\n  var keyboardeventKeyPolyfill = {\n    polyfill: polyfill,\n    keys: {\n      3: 'Cancel',\n      6: 'Help',\n      8: 'Backspace',\n      9: 'Tab',\n      12: 'Clear',\n      13: 'Enter',\n      16: 'Shift',\n      17: 'Control',\n      18: 'Alt',\n      19: 'Pause',\n      20: 'CapsLock',\n      27: 'Escape',\n      28: 'Convert',\n      29: 'NonConvert',\n      30: 'Accept',\n      31: 'ModeChange',\n      32: ' ',\n      33: 'PageUp',\n      34: 'PageDown',\n      35: 'End',\n      36: 'Home',\n      37: 'ArrowLeft',\n      38: 'ArrowUp',\n      39: 'ArrowRight',\n      40: 'ArrowDown',\n      41: 'Select',\n      42: 'Print',\n      43: 'Execute',\n      44: 'PrintScreen',\n      45: 'Insert',\n      46: 'Delete',\n      48: ['0', ')'],\n      49: ['1', '!'],\n      50: ['2', '@'],\n      51: ['3', '#'],\n      52: ['4', '$'],\n      53: ['5', '%'],\n      54: ['6', '^'],\n      55: ['7', '&'],\n      56: ['8', '*'],\n      57: ['9', '('],\n      91: 'OS',\n      93: 'ContextMenu',\n      144: 'NumLock',\n      145: 'ScrollLock',\n      181: 'VolumeMute',\n      182: 'VolumeDown',\n      183: 'VolumeUp',\n      186: [';', ':'],\n      187: ['=', '+'],\n      188: [',', '<'],\n      189: ['-', '_'],\n      190: ['.', '>'],\n      191: ['/', '?'],\n      192: ['`', '~'],\n      219: ['[', '{'],\n      220: ['\\\\', '|'],\n      221: [']', '}'],\n      222: [\"'\", '\"'],\n      224: 'Meta',\n      225: 'AltGraph',\n      246: 'Attn',\n      247: 'CrSel',\n      248: 'ExSel',\n      249: 'EraseEof',\n      250: 'Play',\n      251: 'ZoomOut'\n    }\n  };\n\n  // Function keys (F1-24).\n  var i;\n  for (i = 1; i < 25; i++) {\n    keyboardeventKeyPolyfill.keys[111 + i] = 'F' + i;\n  }\n\n  // Printable ASCII characters.\n  var letter = '';\n  for (i = 65; i < 91; i++) {\n    letter = String.fromCharCode(i);\n    keyboardeventKeyPolyfill.keys[i] = [letter.toLowerCase(), letter.toUpperCase()];\n  }\n  function polyfill() {\n    if (!('KeyboardEvent' in window) || 'key' in KeyboardEvent.prototype) {\n      return false;\n    }\n\n    // Polyfill `key` on `KeyboardEvent`.\n    var proto = {\n      get: function (x) {\n        var key = keyboardeventKeyPolyfill.keys[this.which || this.keyCode];\n        if (Array.isArray(key)) {\n          key = key[+this.shiftKey];\n        }\n        return key;\n      }\n    };\n    Object.defineProperty(KeyboardEvent.prototype, 'key', proto);\n    return proto;\n  }\n  if (typeof define === 'function' && define.amd) {\n    define('keyboardevent-key-polyfill', keyboardeventKeyPolyfill);\n  } else if (typeof exports !== 'undefined' && typeof module !== 'undefined') {\n    module.exports = keyboardeventKeyPolyfill;\n  } else if (window) {\n    window.keyboardeventKeyPolyfill = keyboardeventKeyPolyfill;\n  }\n})();", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}