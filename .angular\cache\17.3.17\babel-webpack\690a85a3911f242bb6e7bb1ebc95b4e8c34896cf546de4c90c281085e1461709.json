{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.skip = void 0;\nvar filter_1 = require(\"./filter\");\nfunction skip(count) {\n  return filter_1.filter(function (_, index) {\n    return count <= index;\n  });\n}\nexports.skip = skip;\n//# sourceMappingURL=skip.js.map", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}