{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.isInteropObservable = void 0;\nvar observable_1 = require(\"../symbol/observable\");\nvar isFunction_1 = require(\"./isFunction\");\nfunction isInteropObservable(input) {\n  return isFunction_1.isFunction(input[observable_1.observable]);\n}\nexports.isInteropObservable = isInteropObservable;\n//# sourceMappingURL=isInteropObservable.js.map", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}