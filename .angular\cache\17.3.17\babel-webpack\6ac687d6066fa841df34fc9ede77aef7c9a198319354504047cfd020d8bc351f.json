{"ast": null, "code": "/*!\n * jQuery JavaScript Library v3.7.1\n * https://jquery.com/\n *\n * Copyright OpenJS Foundation and other contributors\n * Released under the MIT license\n * https://jquery.org/license\n *\n * Date: 2023-08-28T13:37Z\n */\n(function (global, factory) {\n  \"use strict\";\n\n  if (typeof module === \"object\" && typeof module.exports === \"object\") {\n    // For CommonJS and CommonJS-like environments where a proper `window`\n    // is present, execute the factory and get jQuery.\n    // For environments that do not have a `window` with a `document`\n    // (such as Node.js), expose a factory as module.exports.\n    // This accentuates the need for the creation of a real `window`.\n    // e.g. var jQuery = require(\"jquery\")(window);\n    // See ticket trac-14549 for more info.\n    module.exports = global.document ? factory(global, true) : function (w) {\n      if (!w.document) {\n        throw new Error(\"jQuery requires a window with a document\");\n      }\n      return factory(w);\n    };\n  } else {\n    factory(global);\n  }\n\n  // Pass this if window is not defined yet\n})(typeof window !== \"undefined\" ? window : this, function (window, noGlobal) {\n  // Edge <= 12 - 13+, Firefox <=18 - 45+, IE 10 - 11, Safari 5.1 - 9+, iOS 6 - 9.1\n  // throw exceptions when non-strict code (e.g., ASP.NET 4.5) accesses strict mode\n  // arguments.callee.caller (trac-13335). But as of jQuery 3.0 (2016), strict mode should be common\n  // enough that all such attempts are guarded in a try block.\n  \"use strict\";\n\n  var arr = [];\n  var getProto = Object.getPrototypeOf;\n  var slice = arr.slice;\n  var flat = arr.flat ? function (array) {\n    return arr.flat.call(array);\n  } : function (array) {\n    return arr.concat.apply([], array);\n  };\n  var push = arr.push;\n  var indexOf = arr.indexOf;\n  var class2type = {};\n  var toString = class2type.toString;\n  var hasOwn = class2type.hasOwnProperty;\n  var fnToString = hasOwn.toString;\n  var ObjectFunctionString = fnToString.call(Object);\n  var support = {};\n  var isFunction = function isFunction(obj) {\n    // Support: Chrome <=57, Firefox <=52\n    // In some browsers, typeof returns \"function\" for HTML <object> elements\n    // (i.e., `typeof document.createElement( \"object\" ) === \"function\"`).\n    // We don't want to classify *any* DOM node as a function.\n    // Support: QtWeb <=3.8.5, WebKit <=534.34, wkhtmltopdf tool <=0.12.5\n    // Plus for old WebKit, typeof returns \"function\" for HTML collections\n    // (e.g., `typeof document.getElementsByTagName(\"div\") === \"function\"`). (gh-4756)\n    return typeof obj === \"function\" && typeof obj.nodeType !== \"number\" && typeof obj.item !== \"function\";\n  };\n  var isWindow = function isWindow(obj) {\n    return obj != null && obj === obj.window;\n  };\n  var document = window.document;\n  var preservedScriptAttributes = {\n    type: true,\n    src: true,\n    nonce: true,\n    noModule: true\n  };\n  function DOMEval(code, node, doc) {\n    doc = doc || document;\n    var i,\n      val,\n      script = doc.createElement(\"script\");\n    script.text = code;\n    if (node) {\n      for (i in preservedScriptAttributes) {\n        // Support: Firefox 64+, Edge 18+\n        // Some browsers don't support the \"nonce\" property on scripts.\n        // On the other hand, just using `getAttribute` is not enough as\n        // the `nonce` attribute is reset to an empty string whenever it\n        // becomes browsing-context connected.\n        // See https://github.com/whatwg/html/issues/2369\n        // See https://html.spec.whatwg.org/#nonce-attributes\n        // The `node.getAttribute` check was added for the sake of\n        // `jQuery.globalEval` so that it can fake a nonce-containing node\n        // via an object.\n        val = node[i] || node.getAttribute && node.getAttribute(i);\n        if (val) {\n          script.setAttribute(i, val);\n        }\n      }\n    }\n    doc.head.appendChild(script).parentNode.removeChild(script);\n  }\n  function toType(obj) {\n    if (obj == null) {\n      return obj + \"\";\n    }\n\n    // Support: Android <=2.3 only (functionish RegExp)\n    return typeof obj === \"object\" || typeof obj === \"function\" ? class2type[toString.call(obj)] || \"object\" : typeof obj;\n  }\n  /* global Symbol */\n  // Defining this global in .eslintrc.json would create a danger of using the global\n  // unguarded in another place, it seems safer to define global only for this module\n\n  var version = \"3.7.1\",\n    rhtmlSuffix = /HTML$/i,\n    // Define a local copy of jQuery\n    jQuery = function (selector, context) {\n      // The jQuery object is actually just the init constructor 'enhanced'\n      // Need init if jQuery is called (just allow error to be thrown if not included)\n      return new jQuery.fn.init(selector, context);\n    };\n  jQuery.fn = jQuery.prototype = {\n    // The current version of jQuery being used\n    jquery: version,\n    constructor: jQuery,\n    // The default length of a jQuery object is 0\n    length: 0,\n    toArray: function () {\n      return slice.call(this);\n    },\n    // Get the Nth element in the matched element set OR\n    // Get the whole matched element set as a clean array\n    get: function (num) {\n      // Return all the elements in a clean array\n      if (num == null) {\n        return slice.call(this);\n      }\n\n      // Return just the one element from the set\n      return num < 0 ? this[num + this.length] : this[num];\n    },\n    // Take an array of elements and push it onto the stack\n    // (returning the new matched element set)\n    pushStack: function (elems) {\n      // Build a new jQuery matched element set\n      var ret = jQuery.merge(this.constructor(), elems);\n\n      // Add the old object onto the stack (as a reference)\n      ret.prevObject = this;\n\n      // Return the newly-formed element set\n      return ret;\n    },\n    // Execute a callback for every element in the matched set.\n    each: function (callback) {\n      return jQuery.each(this, callback);\n    },\n    map: function (callback) {\n      return this.pushStack(jQuery.map(this, function (elem, i) {\n        return callback.call(elem, i, elem);\n      }));\n    },\n    slice: function () {\n      return this.pushStack(slice.apply(this, arguments));\n    },\n    first: function () {\n      return this.eq(0);\n    },\n    last: function () {\n      return this.eq(-1);\n    },\n    even: function () {\n      return this.pushStack(jQuery.grep(this, function (_elem, i) {\n        return (i + 1) % 2;\n      }));\n    },\n    odd: function () {\n      return this.pushStack(jQuery.grep(this, function (_elem, i) {\n        return i % 2;\n      }));\n    },\n    eq: function (i) {\n      var len = this.length,\n        j = +i + (i < 0 ? len : 0);\n      return this.pushStack(j >= 0 && j < len ? [this[j]] : []);\n    },\n    end: function () {\n      return this.prevObject || this.constructor();\n    },\n    // For internal use only.\n    // Behaves like an Array's method, not like a jQuery method.\n    push: push,\n    sort: arr.sort,\n    splice: arr.splice\n  };\n  jQuery.extend = jQuery.fn.extend = function () {\n    var options,\n      name,\n      src,\n      copy,\n      copyIsArray,\n      clone,\n      target = arguments[0] || {},\n      i = 1,\n      length = arguments.length,\n      deep = false;\n\n    // Handle a deep copy situation\n    if (typeof target === \"boolean\") {\n      deep = target;\n\n      // Skip the boolean and the target\n      target = arguments[i] || {};\n      i++;\n    }\n\n    // Handle case when target is a string or something (possible in deep copy)\n    if (typeof target !== \"object\" && !isFunction(target)) {\n      target = {};\n    }\n\n    // Extend jQuery itself if only one argument is passed\n    if (i === length) {\n      target = this;\n      i--;\n    }\n    for (; i < length; i++) {\n      // Only deal with non-null/undefined values\n      if ((options = arguments[i]) != null) {\n        // Extend the base object\n        for (name in options) {\n          copy = options[name];\n\n          // Prevent Object.prototype pollution\n          // Prevent never-ending loop\n          if (name === \"__proto__\" || target === copy) {\n            continue;\n          }\n\n          // Recurse if we're merging plain objects or arrays\n          if (deep && copy && (jQuery.isPlainObject(copy) || (copyIsArray = Array.isArray(copy)))) {\n            src = target[name];\n\n            // Ensure proper type for the source value\n            if (copyIsArray && !Array.isArray(src)) {\n              clone = [];\n            } else if (!copyIsArray && !jQuery.isPlainObject(src)) {\n              clone = {};\n            } else {\n              clone = src;\n            }\n            copyIsArray = false;\n\n            // Never move original objects, clone them\n            target[name] = jQuery.extend(deep, clone, copy);\n\n            // Don't bring in undefined values\n          } else if (copy !== undefined) {\n            target[name] = copy;\n          }\n        }\n      }\n    }\n\n    // Return the modified object\n    return target;\n  };\n  jQuery.extend({\n    // Unique for each copy of jQuery on the page\n    expando: \"jQuery\" + (version + Math.random()).replace(/\\D/g, \"\"),\n    // Assume jQuery is ready without the ready module\n    isReady: true,\n    error: function (msg) {\n      throw new Error(msg);\n    },\n    noop: function () {},\n    isPlainObject: function (obj) {\n      var proto, Ctor;\n\n      // Detect obvious negatives\n      // Use toString instead of jQuery.type to catch host objects\n      if (!obj || toString.call(obj) !== \"[object Object]\") {\n        return false;\n      }\n      proto = getProto(obj);\n\n      // Objects with no prototype (e.g., `Object.create( null )`) are plain\n      if (!proto) {\n        return true;\n      }\n\n      // Objects with prototype are plain iff they were constructed by a global Object function\n      Ctor = hasOwn.call(proto, \"constructor\") && proto.constructor;\n      return typeof Ctor === \"function\" && fnToString.call(Ctor) === ObjectFunctionString;\n    },\n    isEmptyObject: function (obj) {\n      var name;\n      for (name in obj) {\n        return false;\n      }\n      return true;\n    },\n    // Evaluates a script in a provided context; falls back to the global one\n    // if not specified.\n    globalEval: function (code, options, doc) {\n      DOMEval(code, {\n        nonce: options && options.nonce\n      }, doc);\n    },\n    each: function (obj, callback) {\n      var length,\n        i = 0;\n      if (isArrayLike(obj)) {\n        length = obj.length;\n        for (; i < length; i++) {\n          if (callback.call(obj[i], i, obj[i]) === false) {\n            break;\n          }\n        }\n      } else {\n        for (i in obj) {\n          if (callback.call(obj[i], i, obj[i]) === false) {\n            break;\n          }\n        }\n      }\n      return obj;\n    },\n    // Retrieve the text value of an array of DOM nodes\n    text: function (elem) {\n      var node,\n        ret = \"\",\n        i = 0,\n        nodeType = elem.nodeType;\n      if (!nodeType) {\n        // If no nodeType, this is expected to be an array\n        while (node = elem[i++]) {\n          // Do not traverse comment nodes\n          ret += jQuery.text(node);\n        }\n      }\n      if (nodeType === 1 || nodeType === 11) {\n        return elem.textContent;\n      }\n      if (nodeType === 9) {\n        return elem.documentElement.textContent;\n      }\n      if (nodeType === 3 || nodeType === 4) {\n        return elem.nodeValue;\n      }\n\n      // Do not include comment or processing instruction nodes\n\n      return ret;\n    },\n    // results is for internal usage only\n    makeArray: function (arr, results) {\n      var ret = results || [];\n      if (arr != null) {\n        if (isArrayLike(Object(arr))) {\n          jQuery.merge(ret, typeof arr === \"string\" ? [arr] : arr);\n        } else {\n          push.call(ret, arr);\n        }\n      }\n      return ret;\n    },\n    inArray: function (elem, arr, i) {\n      return arr == null ? -1 : indexOf.call(arr, elem, i);\n    },\n    isXMLDoc: function (elem) {\n      var namespace = elem && elem.namespaceURI,\n        docElem = elem && (elem.ownerDocument || elem).documentElement;\n\n      // Assume HTML when documentElement doesn't yet exist, such as inside\n      // document fragments.\n      return !rhtmlSuffix.test(namespace || docElem && docElem.nodeName || \"HTML\");\n    },\n    // Support: Android <=4.0 only, PhantomJS 1 only\n    // push.apply(_, arraylike) throws on ancient WebKit\n    merge: function (first, second) {\n      var len = +second.length,\n        j = 0,\n        i = first.length;\n      for (; j < len; j++) {\n        first[i++] = second[j];\n      }\n      first.length = i;\n      return first;\n    },\n    grep: function (elems, callback, invert) {\n      var callbackInverse,\n        matches = [],\n        i = 0,\n        length = elems.length,\n        callbackExpect = !invert;\n\n      // Go through the array, only saving the items\n      // that pass the validator function\n      for (; i < length; i++) {\n        callbackInverse = !callback(elems[i], i);\n        if (callbackInverse !== callbackExpect) {\n          matches.push(elems[i]);\n        }\n      }\n      return matches;\n    },\n    // arg is for internal usage only\n    map: function (elems, callback, arg) {\n      var length,\n        value,\n        i = 0,\n        ret = [];\n\n      // Go through the array, translating each of the items to their new values\n      if (isArrayLike(elems)) {\n        length = elems.length;\n        for (; i < length; i++) {\n          value = callback(elems[i], i, arg);\n          if (value != null) {\n            ret.push(value);\n          }\n        }\n\n        // Go through every key on the object,\n      } else {\n        for (i in elems) {\n          value = callback(elems[i], i, arg);\n          if (value != null) {\n            ret.push(value);\n          }\n        }\n      }\n\n      // Flatten any nested arrays\n      return flat(ret);\n    },\n    // A global GUID counter for objects\n    guid: 1,\n    // jQuery.support is not used in Core but other projects attach their\n    // properties to it so it needs to exist.\n    support: support\n  });\n  if (typeof Symbol === \"function\") {\n    jQuery.fn[Symbol.iterator] = arr[Symbol.iterator];\n  }\n\n  // Populate the class2type map\n  jQuery.each(\"Boolean Number String Function Array Date RegExp Object Error Symbol\".split(\" \"), function (_i, name) {\n    class2type[\"[object \" + name + \"]\"] = name.toLowerCase();\n  });\n  function isArrayLike(obj) {\n    // Support: real iOS 8.2 only (not reproducible in simulator)\n    // `in` check used to prevent JIT error (gh-2145)\n    // hasOwn isn't used here due to false negatives\n    // regarding Nodelist length in IE\n    var length = !!obj && \"length\" in obj && obj.length,\n      type = toType(obj);\n    if (isFunction(obj) || isWindow(obj)) {\n      return false;\n    }\n    return type === \"array\" || length === 0 || typeof length === \"number\" && length > 0 && length - 1 in obj;\n  }\n  function nodeName(elem, name) {\n    return elem.nodeName && elem.nodeName.toLowerCase() === name.toLowerCase();\n  }\n  var pop = arr.pop;\n  var sort = arr.sort;\n  var splice = arr.splice;\n  var whitespace = \"[\\\\x20\\\\t\\\\r\\\\n\\\\f]\";\n  var rtrimCSS = new RegExp(\"^\" + whitespace + \"+|((?:^|[^\\\\\\\\])(?:\\\\\\\\.)*)\" + whitespace + \"+$\", \"g\");\n\n  // Note: an element does not contain itself\n  jQuery.contains = function (a, b) {\n    var bup = b && b.parentNode;\n    return a === bup || !!(bup && bup.nodeType === 1 && (\n    // Support: IE 9 - 11+\n    // IE doesn't have `contains` on SVG.\n    a.contains ? a.contains(bup) : a.compareDocumentPosition && a.compareDocumentPosition(bup) & 16));\n  };\n\n  // CSS string/identifier serialization\n  // https://drafts.csswg.org/cssom/#common-serializing-idioms\n  var rcssescape = /([\\0-\\x1f\\x7f]|^-?\\d)|^-$|[^\\x80-\\uFFFF\\w-]/g;\n  function fcssescape(ch, asCodePoint) {\n    if (asCodePoint) {\n      // U+0000 NULL becomes U+FFFD REPLACEMENT CHARACTER\n      if (ch === \"\\0\") {\n        return \"\\uFFFD\";\n      }\n\n      // Control characters and (dependent upon position) numbers get escaped as code points\n      return ch.slice(0, -1) + \"\\\\\" + ch.charCodeAt(ch.length - 1).toString(16) + \" \";\n    }\n\n    // Other potentially-special ASCII characters get backslash-escaped\n    return \"\\\\\" + ch;\n  }\n  jQuery.escapeSelector = function (sel) {\n    return (sel + \"\").replace(rcssescape, fcssescape);\n  };\n  var preferredDoc = document,\n    pushNative = push;\n  (function () {\n    var i,\n      Expr,\n      outermostContext,\n      sortInput,\n      hasDuplicate,\n      push = pushNative,\n      // Local document vars\n      document,\n      documentElement,\n      documentIsHTML,\n      rbuggyQSA,\n      matches,\n      // Instance-specific data\n      expando = jQuery.expando,\n      dirruns = 0,\n      done = 0,\n      classCache = createCache(),\n      tokenCache = createCache(),\n      compilerCache = createCache(),\n      nonnativeSelectorCache = createCache(),\n      sortOrder = function (a, b) {\n        if (a === b) {\n          hasDuplicate = true;\n        }\n        return 0;\n      },\n      booleans = \"checked|selected|async|autofocus|autoplay|controls|defer|disabled|hidden|ismap|\" + \"loop|multiple|open|readonly|required|scoped\",\n      // Regular expressions\n\n      // https://www.w3.org/TR/css-syntax-3/#ident-token-diagram\n      identifier = \"(?:\\\\\\\\[\\\\da-fA-F]{1,6}\" + whitespace + \"?|\\\\\\\\[^\\\\r\\\\n\\\\f]|[\\\\w-]|[^\\0-\\\\x7f])+\",\n      // Attribute selectors: https://www.w3.org/TR/selectors/#attribute-selectors\n      attributes = \"\\\\[\" + whitespace + \"*(\" + identifier + \")(?:\" + whitespace +\n      // Operator (capture 2)\n      \"*([*^$|!~]?=)\" + whitespace +\n      // \"Attribute values must be CSS identifiers [capture 5] or strings [capture 3 or capture 4]\"\n      \"*(?:'((?:\\\\\\\\.|[^\\\\\\\\'])*)'|\\\"((?:\\\\\\\\.|[^\\\\\\\\\\\"])*)\\\"|(\" + identifier + \"))|)\" + whitespace + \"*\\\\]\",\n      pseudos = \":(\" + identifier + \")(?:\\\\((\" +\n      // To reduce the number of selectors needing tokenize in the preFilter, prefer arguments:\n      // 1. quoted (capture 3; capture 4 or capture 5)\n      \"('((?:\\\\\\\\.|[^\\\\\\\\'])*)'|\\\"((?:\\\\\\\\.|[^\\\\\\\\\\\"])*)\\\")|\" +\n      // 2. simple (capture 6)\n      \"((?:\\\\\\\\.|[^\\\\\\\\()[\\\\]]|\" + attributes + \")*)|\" +\n      // 3. anything else (capture 2)\n      \".*\" + \")\\\\)|)\",\n      // Leading and non-escaped trailing whitespace, capturing some non-whitespace characters preceding the latter\n      rwhitespace = new RegExp(whitespace + \"+\", \"g\"),\n      rcomma = new RegExp(\"^\" + whitespace + \"*,\" + whitespace + \"*\"),\n      rleadingCombinator = new RegExp(\"^\" + whitespace + \"*([>+~]|\" + whitespace + \")\" + whitespace + \"*\"),\n      rdescend = new RegExp(whitespace + \"|>\"),\n      rpseudo = new RegExp(pseudos),\n      ridentifier = new RegExp(\"^\" + identifier + \"$\"),\n      matchExpr = {\n        ID: new RegExp(\"^#(\" + identifier + \")\"),\n        CLASS: new RegExp(\"^\\\\.(\" + identifier + \")\"),\n        TAG: new RegExp(\"^(\" + identifier + \"|[*])\"),\n        ATTR: new RegExp(\"^\" + attributes),\n        PSEUDO: new RegExp(\"^\" + pseudos),\n        CHILD: new RegExp(\"^:(only|first|last|nth|nth-last)-(child|of-type)(?:\\\\(\" + whitespace + \"*(even|odd|(([+-]|)(\\\\d*)n|)\" + whitespace + \"*(?:([+-]|)\" + whitespace + \"*(\\\\d+)|))\" + whitespace + \"*\\\\)|)\", \"i\"),\n        bool: new RegExp(\"^(?:\" + booleans + \")$\", \"i\"),\n        // For use in libraries implementing .is()\n        // We use this for POS matching in `select`\n        needsContext: new RegExp(\"^\" + whitespace + \"*[>+~]|:(even|odd|eq|gt|lt|nth|first|last)(?:\\\\(\" + whitespace + \"*((?:-\\\\d)?\\\\d*)\" + whitespace + \"*\\\\)|)(?=[^-]|$)\", \"i\")\n      },\n      rinputs = /^(?:input|select|textarea|button)$/i,\n      rheader = /^h\\d$/i,\n      // Easily-parseable/retrievable ID or TAG or CLASS selectors\n      rquickExpr = /^(?:#([\\w-]+)|(\\w+)|\\.([\\w-]+))$/,\n      rsibling = /[+~]/,\n      // CSS escapes\n      // https://www.w3.org/TR/CSS21/syndata.html#escaped-characters\n      runescape = new RegExp(\"\\\\\\\\[\\\\da-fA-F]{1,6}\" + whitespace + \"?|\\\\\\\\([^\\\\r\\\\n\\\\f])\", \"g\"),\n      funescape = function (escape, nonHex) {\n        var high = \"0x\" + escape.slice(1) - 0x10000;\n        if (nonHex) {\n          // Strip the backslash prefix from a non-hex escape sequence\n          return nonHex;\n        }\n\n        // Replace a hexadecimal escape sequence with the encoded Unicode code point\n        // Support: IE <=11+\n        // For values outside the Basic Multilingual Plane (BMP), manually construct a\n        // surrogate pair\n        return high < 0 ? String.fromCharCode(high + 0x10000) : String.fromCharCode(high >> 10 | 0xD800, high & 0x3FF | 0xDC00);\n      },\n      // Used for iframes; see `setDocument`.\n      // Support: IE 9 - 11+, Edge 12 - 18+\n      // Removing the function wrapper causes a \"Permission Denied\"\n      // error in IE/Edge.\n      unloadHandler = function () {\n        setDocument();\n      },\n      inDisabledFieldset = addCombinator(function (elem) {\n        return elem.disabled === true && nodeName(elem, \"fieldset\");\n      }, {\n        dir: \"parentNode\",\n        next: \"legend\"\n      });\n\n    // Support: IE <=9 only\n    // Accessing document.activeElement can throw unexpectedly\n    // https://bugs.jquery.com/ticket/13393\n    function safeActiveElement() {\n      try {\n        return document.activeElement;\n      } catch (err) {}\n    }\n\n    // Optimize for push.apply( _, NodeList )\n    try {\n      push.apply(arr = slice.call(preferredDoc.childNodes), preferredDoc.childNodes);\n\n      // Support: Android <=4.0\n      // Detect silently failing push.apply\n      // eslint-disable-next-line no-unused-expressions\n      arr[preferredDoc.childNodes.length].nodeType;\n    } catch (e) {\n      push = {\n        apply: function (target, els) {\n          pushNative.apply(target, slice.call(els));\n        },\n        call: function (target) {\n          pushNative.apply(target, slice.call(arguments, 1));\n        }\n      };\n    }\n    function find(selector, context, results, seed) {\n      var m,\n        i,\n        elem,\n        nid,\n        match,\n        groups,\n        newSelector,\n        newContext = context && context.ownerDocument,\n        // nodeType defaults to 9, since context defaults to document\n        nodeType = context ? context.nodeType : 9;\n      results = results || [];\n\n      // Return early from calls with invalid selector or context\n      if (typeof selector !== \"string\" || !selector || nodeType !== 1 && nodeType !== 9 && nodeType !== 11) {\n        return results;\n      }\n\n      // Try to shortcut find operations (as opposed to filters) in HTML documents\n      if (!seed) {\n        setDocument(context);\n        context = context || document;\n        if (documentIsHTML) {\n          // If the selector is sufficiently simple, try using a \"get*By*\" DOM method\n          // (excepting DocumentFragment context, where the methods don't exist)\n          if (nodeType !== 11 && (match = rquickExpr.exec(selector))) {\n            // ID selector\n            if (m = match[1]) {\n              // Document context\n              if (nodeType === 9) {\n                if (elem = context.getElementById(m)) {\n                  // Support: IE 9 only\n                  // getElementById can match elements by name instead of ID\n                  if (elem.id === m) {\n                    push.call(results, elem);\n                    return results;\n                  }\n                } else {\n                  return results;\n                }\n\n                // Element context\n              } else {\n                // Support: IE 9 only\n                // getElementById can match elements by name instead of ID\n                if (newContext && (elem = newContext.getElementById(m)) && find.contains(context, elem) && elem.id === m) {\n                  push.call(results, elem);\n                  return results;\n                }\n              }\n\n              // Type selector\n            } else if (match[2]) {\n              push.apply(results, context.getElementsByTagName(selector));\n              return results;\n\n              // Class selector\n            } else if ((m = match[3]) && context.getElementsByClassName) {\n              push.apply(results, context.getElementsByClassName(m));\n              return results;\n            }\n          }\n\n          // Take advantage of querySelectorAll\n          if (!nonnativeSelectorCache[selector + \" \"] && (!rbuggyQSA || !rbuggyQSA.test(selector))) {\n            newSelector = selector;\n            newContext = context;\n\n            // qSA considers elements outside a scoping root when evaluating child or\n            // descendant combinators, which is not what we want.\n            // In such cases, we work around the behavior by prefixing every selector in the\n            // list with an ID selector referencing the scope context.\n            // The technique has to be used as well when a leading combinator is used\n            // as such selectors are not recognized by querySelectorAll.\n            // Thanks to Andrew Dupont for this technique.\n            if (nodeType === 1 && (rdescend.test(selector) || rleadingCombinator.test(selector))) {\n              // Expand context for sibling selectors\n              newContext = rsibling.test(selector) && testContext(context.parentNode) || context;\n\n              // We can use :scope instead of the ID hack if the browser\n              // supports it & if we're not changing the context.\n              // Support: IE 11+, Edge 17 - 18+\n              // IE/Edge sometimes throw a \"Permission denied\" error when\n              // strict-comparing two documents; shallow comparisons work.\n              // eslint-disable-next-line eqeqeq\n              if (newContext != context || !support.scope) {\n                // Capture the context ID, setting it first if necessary\n                if (nid = context.getAttribute(\"id\")) {\n                  nid = jQuery.escapeSelector(nid);\n                } else {\n                  context.setAttribute(\"id\", nid = expando);\n                }\n              }\n\n              // Prefix every selector in the list\n              groups = tokenize(selector);\n              i = groups.length;\n              while (i--) {\n                groups[i] = (nid ? \"#\" + nid : \":scope\") + \" \" + toSelector(groups[i]);\n              }\n              newSelector = groups.join(\",\");\n            }\n            try {\n              push.apply(results, newContext.querySelectorAll(newSelector));\n              return results;\n            } catch (qsaError) {\n              nonnativeSelectorCache(selector, true);\n            } finally {\n              if (nid === expando) {\n                context.removeAttribute(\"id\");\n              }\n            }\n          }\n        }\n      }\n\n      // All others\n      return select(selector.replace(rtrimCSS, \"$1\"), context, results, seed);\n    }\n\n    /**\n     * Create key-value caches of limited size\n     * @returns {function(string, object)} Returns the Object data after storing it on itself with\n     *\tproperty name the (space-suffixed) string and (if the cache is larger than Expr.cacheLength)\n     *\tdeleting the oldest entry\n     */\n    function createCache() {\n      var keys = [];\n      function cache(key, value) {\n        // Use (key + \" \") to avoid collision with native prototype properties\n        // (see https://github.com/jquery/sizzle/issues/157)\n        if (keys.push(key + \" \") > Expr.cacheLength) {\n          // Only keep the most recent entries\n          delete cache[keys.shift()];\n        }\n        return cache[key + \" \"] = value;\n      }\n      return cache;\n    }\n\n    /**\n     * Mark a function for special use by jQuery selector module\n     * @param {Function} fn The function to mark\n     */\n    function markFunction(fn) {\n      fn[expando] = true;\n      return fn;\n    }\n\n    /**\n     * Support testing using an element\n     * @param {Function} fn Passed the created element and returns a boolean result\n     */\n    function assert(fn) {\n      var el = document.createElement(\"fieldset\");\n      try {\n        return !!fn(el);\n      } catch (e) {\n        return false;\n      } finally {\n        // Remove from its parent by default\n        if (el.parentNode) {\n          el.parentNode.removeChild(el);\n        }\n\n        // release memory in IE\n        el = null;\n      }\n    }\n\n    /**\n     * Returns a function to use in pseudos for input types\n     * @param {String} type\n     */\n    function createInputPseudo(type) {\n      return function (elem) {\n        return nodeName(elem, \"input\") && elem.type === type;\n      };\n    }\n\n    /**\n     * Returns a function to use in pseudos for buttons\n     * @param {String} type\n     */\n    function createButtonPseudo(type) {\n      return function (elem) {\n        return (nodeName(elem, \"input\") || nodeName(elem, \"button\")) && elem.type === type;\n      };\n    }\n\n    /**\n     * Returns a function to use in pseudos for :enabled/:disabled\n     * @param {Boolean} disabled true for :disabled; false for :enabled\n     */\n    function createDisabledPseudo(disabled) {\n      // Known :disabled false positives: fieldset[disabled] > legend:nth-of-type(n+2) :can-disable\n      return function (elem) {\n        // Only certain elements can match :enabled or :disabled\n        // https://html.spec.whatwg.org/multipage/scripting.html#selector-enabled\n        // https://html.spec.whatwg.org/multipage/scripting.html#selector-disabled\n        if (\"form\" in elem) {\n          // Check for inherited disabledness on relevant non-disabled elements:\n          // * listed form-associated elements in a disabled fieldset\n          //   https://html.spec.whatwg.org/multipage/forms.html#category-listed\n          //   https://html.spec.whatwg.org/multipage/forms.html#concept-fe-disabled\n          // * option elements in a disabled optgroup\n          //   https://html.spec.whatwg.org/multipage/forms.html#concept-option-disabled\n          // All such elements have a \"form\" property.\n          if (elem.parentNode && elem.disabled === false) {\n            // Option elements defer to a parent optgroup if present\n            if (\"label\" in elem) {\n              if (\"label\" in elem.parentNode) {\n                return elem.parentNode.disabled === disabled;\n              } else {\n                return elem.disabled === disabled;\n              }\n            }\n\n            // Support: IE 6 - 11+\n            // Use the isDisabled shortcut property to check for disabled fieldset ancestors\n            return elem.isDisabled === disabled ||\n            // Where there is no isDisabled, check manually\n            elem.isDisabled !== !disabled && inDisabledFieldset(elem) === disabled;\n          }\n          return elem.disabled === disabled;\n\n          // Try to winnow out elements that can't be disabled before trusting the disabled property.\n          // Some victims get caught in our net (label, legend, menu, track), but it shouldn't\n          // even exist on them, let alone have a boolean value.\n        } else if (\"label\" in elem) {\n          return elem.disabled === disabled;\n        }\n\n        // Remaining elements are neither :enabled nor :disabled\n        return false;\n      };\n    }\n\n    /**\n     * Returns a function to use in pseudos for positionals\n     * @param {Function} fn\n     */\n    function createPositionalPseudo(fn) {\n      return markFunction(function (argument) {\n        argument = +argument;\n        return markFunction(function (seed, matches) {\n          var j,\n            matchIndexes = fn([], seed.length, argument),\n            i = matchIndexes.length;\n\n          // Match elements found at the specified indexes\n          while (i--) {\n            if (seed[j = matchIndexes[i]]) {\n              seed[j] = !(matches[j] = seed[j]);\n            }\n          }\n        });\n      });\n    }\n\n    /**\n     * Checks a node for validity as a jQuery selector context\n     * @param {Element|Object=} context\n     * @returns {Element|Object|Boolean} The input node if acceptable, otherwise a falsy value\n     */\n    function testContext(context) {\n      return context && typeof context.getElementsByTagName !== \"undefined\" && context;\n    }\n\n    /**\n     * Sets document-related variables once based on the current document\n     * @param {Element|Object} [node] An element or document object to use to set the document\n     * @returns {Object} Returns the current document\n     */\n    function setDocument(node) {\n      var subWindow,\n        doc = node ? node.ownerDocument || node : preferredDoc;\n\n      // Return early if doc is invalid or already selected\n      // Support: IE 11+, Edge 17 - 18+\n      // IE/Edge sometimes throw a \"Permission denied\" error when strict-comparing\n      // two documents; shallow comparisons work.\n      // eslint-disable-next-line eqeqeq\n      if (doc == document || doc.nodeType !== 9 || !doc.documentElement) {\n        return document;\n      }\n\n      // Update global variables\n      document = doc;\n      documentElement = document.documentElement;\n      documentIsHTML = !jQuery.isXMLDoc(document);\n\n      // Support: iOS 7 only, IE 9 - 11+\n      // Older browsers didn't support unprefixed `matches`.\n      matches = documentElement.matches || documentElement.webkitMatchesSelector || documentElement.msMatchesSelector;\n\n      // Support: IE 9 - 11+, Edge 12 - 18+\n      // Accessing iframe documents after unload throws \"permission denied\" errors\n      // (see trac-13936).\n      // Limit the fix to IE & Edge Legacy; despite Edge 15+ implementing `matches`,\n      // all IE 9+ and Edge Legacy versions implement `msMatchesSelector` as well.\n      if (documentElement.msMatchesSelector &&\n      // Support: IE 11+, Edge 17 - 18+\n      // IE/Edge sometimes throw a \"Permission denied\" error when strict-comparing\n      // two documents; shallow comparisons work.\n      // eslint-disable-next-line eqeqeq\n      preferredDoc != document && (subWindow = document.defaultView) && subWindow.top !== subWindow) {\n        // Support: IE 9 - 11+, Edge 12 - 18+\n        subWindow.addEventListener(\"unload\", unloadHandler);\n      }\n\n      // Support: IE <10\n      // Check if getElementById returns elements by name\n      // The broken getElementById methods don't pick up programmatically-set names,\n      // so use a roundabout getElementsByName test\n      support.getById = assert(function (el) {\n        documentElement.appendChild(el).id = jQuery.expando;\n        return !document.getElementsByName || !document.getElementsByName(jQuery.expando).length;\n      });\n\n      // Support: IE 9 only\n      // Check to see if it's possible to do matchesSelector\n      // on a disconnected node.\n      support.disconnectedMatch = assert(function (el) {\n        return matches.call(el, \"*\");\n      });\n\n      // Support: IE 9 - 11+, Edge 12 - 18+\n      // IE/Edge don't support the :scope pseudo-class.\n      support.scope = assert(function () {\n        return document.querySelectorAll(\":scope\");\n      });\n\n      // Support: Chrome 105 - 111 only, Safari 15.4 - 16.3 only\n      // Make sure the `:has()` argument is parsed unforgivingly.\n      // We include `*` in the test to detect buggy implementations that are\n      // _selectively_ forgiving (specifically when the list includes at least\n      // one valid selector).\n      // Note that we treat complete lack of support for `:has()` as if it were\n      // spec-compliant support, which is fine because use of `:has()` in such\n      // environments will fail in the qSA path and fall back to jQuery traversal\n      // anyway.\n      support.cssHas = assert(function () {\n        try {\n          document.querySelector(\":has(*,:jqfake)\");\n          return false;\n        } catch (e) {\n          return true;\n        }\n      });\n\n      // ID filter and find\n      if (support.getById) {\n        Expr.filter.ID = function (id) {\n          var attrId = id.replace(runescape, funescape);\n          return function (elem) {\n            return elem.getAttribute(\"id\") === attrId;\n          };\n        };\n        Expr.find.ID = function (id, context) {\n          if (typeof context.getElementById !== \"undefined\" && documentIsHTML) {\n            var elem = context.getElementById(id);\n            return elem ? [elem] : [];\n          }\n        };\n      } else {\n        Expr.filter.ID = function (id) {\n          var attrId = id.replace(runescape, funescape);\n          return function (elem) {\n            var node = typeof elem.getAttributeNode !== \"undefined\" && elem.getAttributeNode(\"id\");\n            return node && node.value === attrId;\n          };\n        };\n\n        // Support: IE 6 - 7 only\n        // getElementById is not reliable as a find shortcut\n        Expr.find.ID = function (id, context) {\n          if (typeof context.getElementById !== \"undefined\" && documentIsHTML) {\n            var node,\n              i,\n              elems,\n              elem = context.getElementById(id);\n            if (elem) {\n              // Verify the id attribute\n              node = elem.getAttributeNode(\"id\");\n              if (node && node.value === id) {\n                return [elem];\n              }\n\n              // Fall back on getElementsByName\n              elems = context.getElementsByName(id);\n              i = 0;\n              while (elem = elems[i++]) {\n                node = elem.getAttributeNode(\"id\");\n                if (node && node.value === id) {\n                  return [elem];\n                }\n              }\n            }\n            return [];\n          }\n        };\n      }\n\n      // Tag\n      Expr.find.TAG = function (tag, context) {\n        if (typeof context.getElementsByTagName !== \"undefined\") {\n          return context.getElementsByTagName(tag);\n\n          // DocumentFragment nodes don't have gEBTN\n        } else {\n          return context.querySelectorAll(tag);\n        }\n      };\n\n      // Class\n      Expr.find.CLASS = function (className, context) {\n        if (typeof context.getElementsByClassName !== \"undefined\" && documentIsHTML) {\n          return context.getElementsByClassName(className);\n        }\n      };\n\n      /* QSA/matchesSelector\n      ---------------------------------------------------------------------- */\n\n      // QSA and matchesSelector support\n\n      rbuggyQSA = [];\n\n      // Build QSA regex\n      // Regex strategy adopted from Diego Perini\n      assert(function (el) {\n        var input;\n        documentElement.appendChild(el).innerHTML = \"<a id='\" + expando + \"' href='' disabled='disabled'></a>\" + \"<select id='\" + expando + \"-\\r\\\\' disabled='disabled'>\" + \"<option selected=''></option></select>\";\n\n        // Support: iOS <=7 - 8 only\n        // Boolean attributes and \"value\" are not treated correctly in some XML documents\n        if (!el.querySelectorAll(\"[selected]\").length) {\n          rbuggyQSA.push(\"\\\\[\" + whitespace + \"*(?:value|\" + booleans + \")\");\n        }\n\n        // Support: iOS <=7 - 8 only\n        if (!el.querySelectorAll(\"[id~=\" + expando + \"-]\").length) {\n          rbuggyQSA.push(\"~=\");\n        }\n\n        // Support: iOS 8 only\n        // https://bugs.webkit.org/show_bug.cgi?id=136851\n        // In-page `selector#id sibling-combinator selector` fails\n        if (!el.querySelectorAll(\"a#\" + expando + \"+*\").length) {\n          rbuggyQSA.push(\".#.+[+~]\");\n        }\n\n        // Support: Chrome <=105+, Firefox <=104+, Safari <=15.4+\n        // In some of the document kinds, these selectors wouldn't work natively.\n        // This is probably OK but for backwards compatibility we want to maintain\n        // handling them through jQuery traversal in jQuery 3.x.\n        if (!el.querySelectorAll(\":checked\").length) {\n          rbuggyQSA.push(\":checked\");\n        }\n\n        // Support: Windows 8 Native Apps\n        // The type and name attributes are restricted during .innerHTML assignment\n        input = document.createElement(\"input\");\n        input.setAttribute(\"type\", \"hidden\");\n        el.appendChild(input).setAttribute(\"name\", \"D\");\n\n        // Support: IE 9 - 11+\n        // IE's :disabled selector does not pick up the children of disabled fieldsets\n        // Support: Chrome <=105+, Firefox <=104+, Safari <=15.4+\n        // In some of the document kinds, these selectors wouldn't work natively.\n        // This is probably OK but for backwards compatibility we want to maintain\n        // handling them through jQuery traversal in jQuery 3.x.\n        documentElement.appendChild(el).disabled = true;\n        if (el.querySelectorAll(\":disabled\").length !== 2) {\n          rbuggyQSA.push(\":enabled\", \":disabled\");\n        }\n\n        // Support: IE 11+, Edge 15 - 18+\n        // IE 11/Edge don't find elements on a `[name='']` query in some cases.\n        // Adding a temporary attribute to the document before the selection works\n        // around the issue.\n        // Interestingly, IE 10 & older don't seem to have the issue.\n        input = document.createElement(\"input\");\n        input.setAttribute(\"name\", \"\");\n        el.appendChild(input);\n        if (!el.querySelectorAll(\"[name='']\").length) {\n          rbuggyQSA.push(\"\\\\[\" + whitespace + \"*name\" + whitespace + \"*=\" + whitespace + \"*(?:''|\\\"\\\")\");\n        }\n      });\n      if (!support.cssHas) {\n        // Support: Chrome 105 - 110+, Safari 15.4 - 16.3+\n        // Our regular `try-catch` mechanism fails to detect natively-unsupported\n        // pseudo-classes inside `:has()` (such as `:has(:contains(\"Foo\"))`)\n        // in browsers that parse the `:has()` argument as a forgiving selector list.\n        // https://drafts.csswg.org/selectors/#relational now requires the argument\n        // to be parsed unforgivingly, but browsers have not yet fully adjusted.\n        rbuggyQSA.push(\":has\");\n      }\n      rbuggyQSA = rbuggyQSA.length && new RegExp(rbuggyQSA.join(\"|\"));\n\n      /* Sorting\n      ---------------------------------------------------------------------- */\n\n      // Document order sorting\n      sortOrder = function (a, b) {\n        // Flag for duplicate removal\n        if (a === b) {\n          hasDuplicate = true;\n          return 0;\n        }\n\n        // Sort on method existence if only one input has compareDocumentPosition\n        var compare = !a.compareDocumentPosition - !b.compareDocumentPosition;\n        if (compare) {\n          return compare;\n        }\n\n        // Calculate position if both inputs belong to the same document\n        // Support: IE 11+, Edge 17 - 18+\n        // IE/Edge sometimes throw a \"Permission denied\" error when strict-comparing\n        // two documents; shallow comparisons work.\n        // eslint-disable-next-line eqeqeq\n        compare = (a.ownerDocument || a) == (b.ownerDocument || b) ? a.compareDocumentPosition(b) :\n        // Otherwise we know they are disconnected\n        1;\n\n        // Disconnected nodes\n        if (compare & 1 || !support.sortDetached && b.compareDocumentPosition(a) === compare) {\n          // Choose the first element that is related to our preferred document\n          // Support: IE 11+, Edge 17 - 18+\n          // IE/Edge sometimes throw a \"Permission denied\" error when strict-comparing\n          // two documents; shallow comparisons work.\n          // eslint-disable-next-line eqeqeq\n          if (a === document || a.ownerDocument == preferredDoc && find.contains(preferredDoc, a)) {\n            return -1;\n          }\n\n          // Support: IE 11+, Edge 17 - 18+\n          // IE/Edge sometimes throw a \"Permission denied\" error when strict-comparing\n          // two documents; shallow comparisons work.\n          // eslint-disable-next-line eqeqeq\n          if (b === document || b.ownerDocument == preferredDoc && find.contains(preferredDoc, b)) {\n            return 1;\n          }\n\n          // Maintain original order\n          return sortInput ? indexOf.call(sortInput, a) - indexOf.call(sortInput, b) : 0;\n        }\n        return compare & 4 ? -1 : 1;\n      };\n      return document;\n    }\n    find.matches = function (expr, elements) {\n      return find(expr, null, null, elements);\n    };\n    find.matchesSelector = function (elem, expr) {\n      setDocument(elem);\n      if (documentIsHTML && !nonnativeSelectorCache[expr + \" \"] && (!rbuggyQSA || !rbuggyQSA.test(expr))) {\n        try {\n          var ret = matches.call(elem, expr);\n\n          // IE 9's matchesSelector returns false on disconnected nodes\n          if (ret || support.disconnectedMatch ||\n          // As well, disconnected nodes are said to be in a document\n          // fragment in IE 9\n          elem.document && elem.document.nodeType !== 11) {\n            return ret;\n          }\n        } catch (e) {\n          nonnativeSelectorCache(expr, true);\n        }\n      }\n      return find(expr, document, null, [elem]).length > 0;\n    };\n    find.contains = function (context, elem) {\n      // Set document vars if needed\n      // Support: IE 11+, Edge 17 - 18+\n      // IE/Edge sometimes throw a \"Permission denied\" error when strict-comparing\n      // two documents; shallow comparisons work.\n      // eslint-disable-next-line eqeqeq\n      if ((context.ownerDocument || context) != document) {\n        setDocument(context);\n      }\n      return jQuery.contains(context, elem);\n    };\n    find.attr = function (elem, name) {\n      // Set document vars if needed\n      // Support: IE 11+, Edge 17 - 18+\n      // IE/Edge sometimes throw a \"Permission denied\" error when strict-comparing\n      // two documents; shallow comparisons work.\n      // eslint-disable-next-line eqeqeq\n      if ((elem.ownerDocument || elem) != document) {\n        setDocument(elem);\n      }\n      var fn = Expr.attrHandle[name.toLowerCase()],\n        // Don't get fooled by Object.prototype properties (see trac-13807)\n        val = fn && hasOwn.call(Expr.attrHandle, name.toLowerCase()) ? fn(elem, name, !documentIsHTML) : undefined;\n      if (val !== undefined) {\n        return val;\n      }\n      return elem.getAttribute(name);\n    };\n    find.error = function (msg) {\n      throw new Error(\"Syntax error, unrecognized expression: \" + msg);\n    };\n\n    /**\n     * Document sorting and removing duplicates\n     * @param {ArrayLike} results\n     */\n    jQuery.uniqueSort = function (results) {\n      var elem,\n        duplicates = [],\n        j = 0,\n        i = 0;\n\n      // Unless we *know* we can detect duplicates, assume their presence\n      //\n      // Support: Android <=4.0+\n      // Testing for detecting duplicates is unpredictable so instead assume we can't\n      // depend on duplicate detection in all browsers without a stable sort.\n      hasDuplicate = !support.sortStable;\n      sortInput = !support.sortStable && slice.call(results, 0);\n      sort.call(results, sortOrder);\n      if (hasDuplicate) {\n        while (elem = results[i++]) {\n          if (elem === results[i]) {\n            j = duplicates.push(i);\n          }\n        }\n        while (j--) {\n          splice.call(results, duplicates[j], 1);\n        }\n      }\n\n      // Clear input after sorting to release objects\n      // See https://github.com/jquery/sizzle/pull/225\n      sortInput = null;\n      return results;\n    };\n    jQuery.fn.uniqueSort = function () {\n      return this.pushStack(jQuery.uniqueSort(slice.apply(this)));\n    };\n    Expr = jQuery.expr = {\n      // Can be adjusted by the user\n      cacheLength: 50,\n      createPseudo: markFunction,\n      match: matchExpr,\n      attrHandle: {},\n      find: {},\n      relative: {\n        \">\": {\n          dir: \"parentNode\",\n          first: true\n        },\n        \" \": {\n          dir: \"parentNode\"\n        },\n        \"+\": {\n          dir: \"previousSibling\",\n          first: true\n        },\n        \"~\": {\n          dir: \"previousSibling\"\n        }\n      },\n      preFilter: {\n        ATTR: function (match) {\n          match[1] = match[1].replace(runescape, funescape);\n\n          // Move the given value to match[3] whether quoted or unquoted\n          match[3] = (match[3] || match[4] || match[5] || \"\").replace(runescape, funescape);\n          if (match[2] === \"~=\") {\n            match[3] = \" \" + match[3] + \" \";\n          }\n          return match.slice(0, 4);\n        },\n        CHILD: function (match) {\n          /* matches from matchExpr[\"CHILD\"]\n          \t1 type (only|nth|...)\n          \t2 what (child|of-type)\n          \t3 argument (even|odd|\\d*|\\d*n([+-]\\d+)?|...)\n          \t4 xn-component of xn+y argument ([+-]?\\d*n|)\n          \t5 sign of xn-component\n          \t6 x of xn-component\n          \t7 sign of y-component\n          \t8 y of y-component\n          */\n          match[1] = match[1].toLowerCase();\n          if (match[1].slice(0, 3) === \"nth\") {\n            // nth-* requires argument\n            if (!match[3]) {\n              find.error(match[0]);\n            }\n\n            // numeric x and y parameters for Expr.filter.CHILD\n            // remember that false/true cast respectively to 0/1\n            match[4] = +(match[4] ? match[5] + (match[6] || 1) : 2 * (match[3] === \"even\" || match[3] === \"odd\"));\n            match[5] = +(match[7] + match[8] || match[3] === \"odd\");\n\n            // other types prohibit arguments\n          } else if (match[3]) {\n            find.error(match[0]);\n          }\n          return match;\n        },\n        PSEUDO: function (match) {\n          var excess,\n            unquoted = !match[6] && match[2];\n          if (matchExpr.CHILD.test(match[0])) {\n            return null;\n          }\n\n          // Accept quoted arguments as-is\n          if (match[3]) {\n            match[2] = match[4] || match[5] || \"\";\n\n            // Strip excess characters from unquoted arguments\n          } else if (unquoted && rpseudo.test(unquoted) && (\n          // Get excess from tokenize (recursively)\n          excess = tokenize(unquoted, true)) && (\n          // advance to the next closing parenthesis\n          excess = unquoted.indexOf(\")\", unquoted.length - excess) - unquoted.length)) {\n            // excess is a negative index\n            match[0] = match[0].slice(0, excess);\n            match[2] = unquoted.slice(0, excess);\n          }\n\n          // Return only captures needed by the pseudo filter method (type and argument)\n          return match.slice(0, 3);\n        }\n      },\n      filter: {\n        TAG: function (nodeNameSelector) {\n          var expectedNodeName = nodeNameSelector.replace(runescape, funescape).toLowerCase();\n          return nodeNameSelector === \"*\" ? function () {\n            return true;\n          } : function (elem) {\n            return nodeName(elem, expectedNodeName);\n          };\n        },\n        CLASS: function (className) {\n          var pattern = classCache[className + \" \"];\n          return pattern || (pattern = new RegExp(\"(^|\" + whitespace + \")\" + className + \"(\" + whitespace + \"|$)\")) && classCache(className, function (elem) {\n            return pattern.test(typeof elem.className === \"string\" && elem.className || typeof elem.getAttribute !== \"undefined\" && elem.getAttribute(\"class\") || \"\");\n          });\n        },\n        ATTR: function (name, operator, check) {\n          return function (elem) {\n            var result = find.attr(elem, name);\n            if (result == null) {\n              return operator === \"!=\";\n            }\n            if (!operator) {\n              return true;\n            }\n            result += \"\";\n            if (operator === \"=\") {\n              return result === check;\n            }\n            if (operator === \"!=\") {\n              return result !== check;\n            }\n            if (operator === \"^=\") {\n              return check && result.indexOf(check) === 0;\n            }\n            if (operator === \"*=\") {\n              return check && result.indexOf(check) > -1;\n            }\n            if (operator === \"$=\") {\n              return check && result.slice(-check.length) === check;\n            }\n            if (operator === \"~=\") {\n              return (\" \" + result.replace(rwhitespace, \" \") + \" \").indexOf(check) > -1;\n            }\n            if (operator === \"|=\") {\n              return result === check || result.slice(0, check.length + 1) === check + \"-\";\n            }\n            return false;\n          };\n        },\n        CHILD: function (type, what, _argument, first, last) {\n          var simple = type.slice(0, 3) !== \"nth\",\n            forward = type.slice(-4) !== \"last\",\n            ofType = what === \"of-type\";\n          return first === 1 && last === 0 ?\n          // Shortcut for :nth-*(n)\n          function (elem) {\n            return !!elem.parentNode;\n          } : function (elem, _context, xml) {\n            var cache,\n              outerCache,\n              node,\n              nodeIndex,\n              start,\n              dir = simple !== forward ? \"nextSibling\" : \"previousSibling\",\n              parent = elem.parentNode,\n              name = ofType && elem.nodeName.toLowerCase(),\n              useCache = !xml && !ofType,\n              diff = false;\n            if (parent) {\n              // :(first|last|only)-(child|of-type)\n              if (simple) {\n                while (dir) {\n                  node = elem;\n                  while (node = node[dir]) {\n                    if (ofType ? nodeName(node, name) : node.nodeType === 1) {\n                      return false;\n                    }\n                  }\n\n                  // Reverse direction for :only-* (if we haven't yet done so)\n                  start = dir = type === \"only\" && !start && \"nextSibling\";\n                }\n                return true;\n              }\n              start = [forward ? parent.firstChild : parent.lastChild];\n\n              // non-xml :nth-child(...) stores cache data on `parent`\n              if (forward && useCache) {\n                // Seek `elem` from a previously-cached index\n                outerCache = parent[expando] || (parent[expando] = {});\n                cache = outerCache[type] || [];\n                nodeIndex = cache[0] === dirruns && cache[1];\n                diff = nodeIndex && cache[2];\n                node = nodeIndex && parent.childNodes[nodeIndex];\n                while (node = ++nodeIndex && node && node[dir] || (\n                // Fallback to seeking `elem` from the start\n                diff = nodeIndex = 0) || start.pop()) {\n                  // When found, cache indexes on `parent` and break\n                  if (node.nodeType === 1 && ++diff && node === elem) {\n                    outerCache[type] = [dirruns, nodeIndex, diff];\n                    break;\n                  }\n                }\n              } else {\n                // Use previously-cached element index if available\n                if (useCache) {\n                  outerCache = elem[expando] || (elem[expando] = {});\n                  cache = outerCache[type] || [];\n                  nodeIndex = cache[0] === dirruns && cache[1];\n                  diff = nodeIndex;\n                }\n\n                // xml :nth-child(...)\n                // or :nth-last-child(...) or :nth(-last)?-of-type(...)\n                if (diff === false) {\n                  // Use the same loop as above to seek `elem` from the start\n                  while (node = ++nodeIndex && node && node[dir] || (diff = nodeIndex = 0) || start.pop()) {\n                    if ((ofType ? nodeName(node, name) : node.nodeType === 1) && ++diff) {\n                      // Cache the index of each encountered element\n                      if (useCache) {\n                        outerCache = node[expando] || (node[expando] = {});\n                        outerCache[type] = [dirruns, diff];\n                      }\n                      if (node === elem) {\n                        break;\n                      }\n                    }\n                  }\n                }\n              }\n\n              // Incorporate the offset, then check against cycle size\n              diff -= last;\n              return diff === first || diff % first === 0 && diff / first >= 0;\n            }\n          };\n        },\n        PSEUDO: function (pseudo, argument) {\n          // pseudo-class names are case-insensitive\n          // https://www.w3.org/TR/selectors/#pseudo-classes\n          // Prioritize by case sensitivity in case custom pseudos are added with uppercase letters\n          // Remember that setFilters inherits from pseudos\n          var args,\n            fn = Expr.pseudos[pseudo] || Expr.setFilters[pseudo.toLowerCase()] || find.error(\"unsupported pseudo: \" + pseudo);\n\n          // The user may use createPseudo to indicate that\n          // arguments are needed to create the filter function\n          // just as jQuery does\n          if (fn[expando]) {\n            return fn(argument);\n          }\n\n          // But maintain support for old signatures\n          if (fn.length > 1) {\n            args = [pseudo, pseudo, \"\", argument];\n            return Expr.setFilters.hasOwnProperty(pseudo.toLowerCase()) ? markFunction(function (seed, matches) {\n              var idx,\n                matched = fn(seed, argument),\n                i = matched.length;\n              while (i--) {\n                idx = indexOf.call(seed, matched[i]);\n                seed[idx] = !(matches[idx] = matched[i]);\n              }\n            }) : function (elem) {\n              return fn(elem, 0, args);\n            };\n          }\n          return fn;\n        }\n      },\n      pseudos: {\n        // Potentially complex pseudos\n        not: markFunction(function (selector) {\n          // Trim the selector passed to compile\n          // to avoid treating leading and trailing\n          // spaces as combinators\n          var input = [],\n            results = [],\n            matcher = compile(selector.replace(rtrimCSS, \"$1\"));\n          return matcher[expando] ? markFunction(function (seed, matches, _context, xml) {\n            var elem,\n              unmatched = matcher(seed, null, xml, []),\n              i = seed.length;\n\n            // Match elements unmatched by `matcher`\n            while (i--) {\n              if (elem = unmatched[i]) {\n                seed[i] = !(matches[i] = elem);\n              }\n            }\n          }) : function (elem, _context, xml) {\n            input[0] = elem;\n            matcher(input, null, xml, results);\n\n            // Don't keep the element\n            // (see https://github.com/jquery/sizzle/issues/299)\n            input[0] = null;\n            return !results.pop();\n          };\n        }),\n        has: markFunction(function (selector) {\n          return function (elem) {\n            return find(selector, elem).length > 0;\n          };\n        }),\n        contains: markFunction(function (text) {\n          text = text.replace(runescape, funescape);\n          return function (elem) {\n            return (elem.textContent || jQuery.text(elem)).indexOf(text) > -1;\n          };\n        }),\n        // \"Whether an element is represented by a :lang() selector\n        // is based solely on the element's language value\n        // being equal to the identifier C,\n        // or beginning with the identifier C immediately followed by \"-\".\n        // The matching of C against the element's language value is performed case-insensitively.\n        // The identifier C does not have to be a valid language name.\"\n        // https://www.w3.org/TR/selectors/#lang-pseudo\n        lang: markFunction(function (lang) {\n          // lang value must be a valid identifier\n          if (!ridentifier.test(lang || \"\")) {\n            find.error(\"unsupported lang: \" + lang);\n          }\n          lang = lang.replace(runescape, funescape).toLowerCase();\n          return function (elem) {\n            var elemLang;\n            do {\n              if (elemLang = documentIsHTML ? elem.lang : elem.getAttribute(\"xml:lang\") || elem.getAttribute(\"lang\")) {\n                elemLang = elemLang.toLowerCase();\n                return elemLang === lang || elemLang.indexOf(lang + \"-\") === 0;\n              }\n            } while ((elem = elem.parentNode) && elem.nodeType === 1);\n            return false;\n          };\n        }),\n        // Miscellaneous\n        target: function (elem) {\n          var hash = window.location && window.location.hash;\n          return hash && hash.slice(1) === elem.id;\n        },\n        root: function (elem) {\n          return elem === documentElement;\n        },\n        focus: function (elem) {\n          return elem === safeActiveElement() && document.hasFocus() && !!(elem.type || elem.href || ~elem.tabIndex);\n        },\n        // Boolean properties\n        enabled: createDisabledPseudo(false),\n        disabled: createDisabledPseudo(true),\n        checked: function (elem) {\n          // In CSS3, :checked should return both checked and selected elements\n          // https://www.w3.org/TR/2011/REC-css3-selectors-20110929/#checked\n          return nodeName(elem, \"input\") && !!elem.checked || nodeName(elem, \"option\") && !!elem.selected;\n        },\n        selected: function (elem) {\n          // Support: IE <=11+\n          // Accessing the selectedIndex property\n          // forces the browser to treat the default option as\n          // selected when in an optgroup.\n          if (elem.parentNode) {\n            // eslint-disable-next-line no-unused-expressions\n            elem.parentNode.selectedIndex;\n          }\n          return elem.selected === true;\n        },\n        // Contents\n        empty: function (elem) {\n          // https://www.w3.org/TR/selectors/#empty-pseudo\n          // :empty is negated by element (1) or content nodes (text: 3; cdata: 4; entity ref: 5),\n          //   but not by others (comment: 8; processing instruction: 7; etc.)\n          // nodeType < 6 works because attributes (2) do not appear as children\n          for (elem = elem.firstChild; elem; elem = elem.nextSibling) {\n            if (elem.nodeType < 6) {\n              return false;\n            }\n          }\n          return true;\n        },\n        parent: function (elem) {\n          return !Expr.pseudos.empty(elem);\n        },\n        // Element/input types\n        header: function (elem) {\n          return rheader.test(elem.nodeName);\n        },\n        input: function (elem) {\n          return rinputs.test(elem.nodeName);\n        },\n        button: function (elem) {\n          return nodeName(elem, \"input\") && elem.type === \"button\" || nodeName(elem, \"button\");\n        },\n        text: function (elem) {\n          var attr;\n          return nodeName(elem, \"input\") && elem.type === \"text\" && (\n          // Support: IE <10 only\n          // New HTML5 attribute values (e.g., \"search\") appear\n          // with elem.type === \"text\"\n          (attr = elem.getAttribute(\"type\")) == null || attr.toLowerCase() === \"text\");\n        },\n        // Position-in-collection\n        first: createPositionalPseudo(function () {\n          return [0];\n        }),\n        last: createPositionalPseudo(function (_matchIndexes, length) {\n          return [length - 1];\n        }),\n        eq: createPositionalPseudo(function (_matchIndexes, length, argument) {\n          return [argument < 0 ? argument + length : argument];\n        }),\n        even: createPositionalPseudo(function (matchIndexes, length) {\n          var i = 0;\n          for (; i < length; i += 2) {\n            matchIndexes.push(i);\n          }\n          return matchIndexes;\n        }),\n        odd: createPositionalPseudo(function (matchIndexes, length) {\n          var i = 1;\n          for (; i < length; i += 2) {\n            matchIndexes.push(i);\n          }\n          return matchIndexes;\n        }),\n        lt: createPositionalPseudo(function (matchIndexes, length, argument) {\n          var i;\n          if (argument < 0) {\n            i = argument + length;\n          } else if (argument > length) {\n            i = length;\n          } else {\n            i = argument;\n          }\n          for (; --i >= 0;) {\n            matchIndexes.push(i);\n          }\n          return matchIndexes;\n        }),\n        gt: createPositionalPseudo(function (matchIndexes, length, argument) {\n          var i = argument < 0 ? argument + length : argument;\n          for (; ++i < length;) {\n            matchIndexes.push(i);\n          }\n          return matchIndexes;\n        })\n      }\n    };\n    Expr.pseudos.nth = Expr.pseudos.eq;\n\n    // Add button/input type pseudos\n    for (i in {\n      radio: true,\n      checkbox: true,\n      file: true,\n      password: true,\n      image: true\n    }) {\n      Expr.pseudos[i] = createInputPseudo(i);\n    }\n    for (i in {\n      submit: true,\n      reset: true\n    }) {\n      Expr.pseudos[i] = createButtonPseudo(i);\n    }\n\n    // Easy API for creating new setFilters\n    function setFilters() {}\n    setFilters.prototype = Expr.filters = Expr.pseudos;\n    Expr.setFilters = new setFilters();\n    function tokenize(selector, parseOnly) {\n      var matched,\n        match,\n        tokens,\n        type,\n        soFar,\n        groups,\n        preFilters,\n        cached = tokenCache[selector + \" \"];\n      if (cached) {\n        return parseOnly ? 0 : cached.slice(0);\n      }\n      soFar = selector;\n      groups = [];\n      preFilters = Expr.preFilter;\n      while (soFar) {\n        // Comma and first run\n        if (!matched || (match = rcomma.exec(soFar))) {\n          if (match) {\n            // Don't consume trailing commas as valid\n            soFar = soFar.slice(match[0].length) || soFar;\n          }\n          groups.push(tokens = []);\n        }\n        matched = false;\n\n        // Combinators\n        if (match = rleadingCombinator.exec(soFar)) {\n          matched = match.shift();\n          tokens.push({\n            value: matched,\n            // Cast descendant combinators to space\n            type: match[0].replace(rtrimCSS, \" \")\n          });\n          soFar = soFar.slice(matched.length);\n        }\n\n        // Filters\n        for (type in Expr.filter) {\n          if ((match = matchExpr[type].exec(soFar)) && (!preFilters[type] || (match = preFilters[type](match)))) {\n            matched = match.shift();\n            tokens.push({\n              value: matched,\n              type: type,\n              matches: match\n            });\n            soFar = soFar.slice(matched.length);\n          }\n        }\n        if (!matched) {\n          break;\n        }\n      }\n\n      // Return the length of the invalid excess\n      // if we're just parsing\n      // Otherwise, throw an error or return tokens\n      if (parseOnly) {\n        return soFar.length;\n      }\n      return soFar ? find.error(selector) :\n      // Cache the tokens\n      tokenCache(selector, groups).slice(0);\n    }\n    function toSelector(tokens) {\n      var i = 0,\n        len = tokens.length,\n        selector = \"\";\n      for (; i < len; i++) {\n        selector += tokens[i].value;\n      }\n      return selector;\n    }\n    function addCombinator(matcher, combinator, base) {\n      var dir = combinator.dir,\n        skip = combinator.next,\n        key = skip || dir,\n        checkNonElements = base && key === \"parentNode\",\n        doneName = done++;\n      return combinator.first ?\n      // Check against closest ancestor/preceding element\n      function (elem, context, xml) {\n        while (elem = elem[dir]) {\n          if (elem.nodeType === 1 || checkNonElements) {\n            return matcher(elem, context, xml);\n          }\n        }\n        return false;\n      } :\n      // Check against all ancestor/preceding elements\n      function (elem, context, xml) {\n        var oldCache,\n          outerCache,\n          newCache = [dirruns, doneName];\n\n        // We can't set arbitrary data on XML nodes, so they don't benefit from combinator caching\n        if (xml) {\n          while (elem = elem[dir]) {\n            if (elem.nodeType === 1 || checkNonElements) {\n              if (matcher(elem, context, xml)) {\n                return true;\n              }\n            }\n          }\n        } else {\n          while (elem = elem[dir]) {\n            if (elem.nodeType === 1 || checkNonElements) {\n              outerCache = elem[expando] || (elem[expando] = {});\n              if (skip && nodeName(elem, skip)) {\n                elem = elem[dir] || elem;\n              } else if ((oldCache = outerCache[key]) && oldCache[0] === dirruns && oldCache[1] === doneName) {\n                // Assign to newCache so results back-propagate to previous elements\n                return newCache[2] = oldCache[2];\n              } else {\n                // Reuse newcache so results back-propagate to previous elements\n                outerCache[key] = newCache;\n\n                // A match means we're done; a fail means we have to keep checking\n                if (newCache[2] = matcher(elem, context, xml)) {\n                  return true;\n                }\n              }\n            }\n          }\n        }\n        return false;\n      };\n    }\n    function elementMatcher(matchers) {\n      return matchers.length > 1 ? function (elem, context, xml) {\n        var i = matchers.length;\n        while (i--) {\n          if (!matchers[i](elem, context, xml)) {\n            return false;\n          }\n        }\n        return true;\n      } : matchers[0];\n    }\n    function multipleContexts(selector, contexts, results) {\n      var i = 0,\n        len = contexts.length;\n      for (; i < len; i++) {\n        find(selector, contexts[i], results);\n      }\n      return results;\n    }\n    function condense(unmatched, map, filter, context, xml) {\n      var elem,\n        newUnmatched = [],\n        i = 0,\n        len = unmatched.length,\n        mapped = map != null;\n      for (; i < len; i++) {\n        if (elem = unmatched[i]) {\n          if (!filter || filter(elem, context, xml)) {\n            newUnmatched.push(elem);\n            if (mapped) {\n              map.push(i);\n            }\n          }\n        }\n      }\n      return newUnmatched;\n    }\n    function setMatcher(preFilter, selector, matcher, postFilter, postFinder, postSelector) {\n      if (postFilter && !postFilter[expando]) {\n        postFilter = setMatcher(postFilter);\n      }\n      if (postFinder && !postFinder[expando]) {\n        postFinder = setMatcher(postFinder, postSelector);\n      }\n      return markFunction(function (seed, results, context, xml) {\n        var temp,\n          i,\n          elem,\n          matcherOut,\n          preMap = [],\n          postMap = [],\n          preexisting = results.length,\n          // Get initial elements from seed or context\n          elems = seed || multipleContexts(selector || \"*\", context.nodeType ? [context] : context, []),\n          // Prefilter to get matcher input, preserving a map for seed-results synchronization\n          matcherIn = preFilter && (seed || !selector) ? condense(elems, preMap, preFilter, context, xml) : elems;\n        if (matcher) {\n          // If we have a postFinder, or filtered seed, or non-seed postFilter\n          // or preexisting results,\n          matcherOut = postFinder || (seed ? preFilter : preexisting || postFilter) ?\n          // ...intermediate processing is necessary\n          [] :\n          // ...otherwise use results directly\n          results;\n\n          // Find primary matches\n          matcher(matcherIn, matcherOut, context, xml);\n        } else {\n          matcherOut = matcherIn;\n        }\n\n        // Apply postFilter\n        if (postFilter) {\n          temp = condense(matcherOut, postMap);\n          postFilter(temp, [], context, xml);\n\n          // Un-match failing elements by moving them back to matcherIn\n          i = temp.length;\n          while (i--) {\n            if (elem = temp[i]) {\n              matcherOut[postMap[i]] = !(matcherIn[postMap[i]] = elem);\n            }\n          }\n        }\n        if (seed) {\n          if (postFinder || preFilter) {\n            if (postFinder) {\n              // Get the final matcherOut by condensing this intermediate into postFinder contexts\n              temp = [];\n              i = matcherOut.length;\n              while (i--) {\n                if (elem = matcherOut[i]) {\n                  // Restore matcherIn since elem is not yet a final match\n                  temp.push(matcherIn[i] = elem);\n                }\n              }\n              postFinder(null, matcherOut = [], temp, xml);\n            }\n\n            // Move matched elements from seed to results to keep them synchronized\n            i = matcherOut.length;\n            while (i--) {\n              if ((elem = matcherOut[i]) && (temp = postFinder ? indexOf.call(seed, elem) : preMap[i]) > -1) {\n                seed[temp] = !(results[temp] = elem);\n              }\n            }\n          }\n\n          // Add elements to results, through postFinder if defined\n        } else {\n          matcherOut = condense(matcherOut === results ? matcherOut.splice(preexisting, matcherOut.length) : matcherOut);\n          if (postFinder) {\n            postFinder(null, results, matcherOut, xml);\n          } else {\n            push.apply(results, matcherOut);\n          }\n        }\n      });\n    }\n    function matcherFromTokens(tokens) {\n      var checkContext,\n        matcher,\n        j,\n        len = tokens.length,\n        leadingRelative = Expr.relative[tokens[0].type],\n        implicitRelative = leadingRelative || Expr.relative[\" \"],\n        i = leadingRelative ? 1 : 0,\n        // The foundational matcher ensures that elements are reachable from top-level context(s)\n        matchContext = addCombinator(function (elem) {\n          return elem === checkContext;\n        }, implicitRelative, true),\n        matchAnyContext = addCombinator(function (elem) {\n          return indexOf.call(checkContext, elem) > -1;\n        }, implicitRelative, true),\n        matchers = [function (elem, context, xml) {\n          // Support: IE 11+, Edge 17 - 18+\n          // IE/Edge sometimes throw a \"Permission denied\" error when strict-comparing\n          // two documents; shallow comparisons work.\n          // eslint-disable-next-line eqeqeq\n          var ret = !leadingRelative && (xml || context != outermostContext) || ((checkContext = context).nodeType ? matchContext(elem, context, xml) : matchAnyContext(elem, context, xml));\n\n          // Avoid hanging onto element\n          // (see https://github.com/jquery/sizzle/issues/299)\n          checkContext = null;\n          return ret;\n        }];\n      for (; i < len; i++) {\n        if (matcher = Expr.relative[tokens[i].type]) {\n          matchers = [addCombinator(elementMatcher(matchers), matcher)];\n        } else {\n          matcher = Expr.filter[tokens[i].type].apply(null, tokens[i].matches);\n\n          // Return special upon seeing a positional matcher\n          if (matcher[expando]) {\n            // Find the next relative operator (if any) for proper handling\n            j = ++i;\n            for (; j < len; j++) {\n              if (Expr.relative[tokens[j].type]) {\n                break;\n              }\n            }\n            return setMatcher(i > 1 && elementMatcher(matchers), i > 1 && toSelector(\n            // If the preceding token was a descendant combinator, insert an implicit any-element `*`\n            tokens.slice(0, i - 1).concat({\n              value: tokens[i - 2].type === \" \" ? \"*\" : \"\"\n            })).replace(rtrimCSS, \"$1\"), matcher, i < j && matcherFromTokens(tokens.slice(i, j)), j < len && matcherFromTokens(tokens = tokens.slice(j)), j < len && toSelector(tokens));\n          }\n          matchers.push(matcher);\n        }\n      }\n      return elementMatcher(matchers);\n    }\n    function matcherFromGroupMatchers(elementMatchers, setMatchers) {\n      var bySet = setMatchers.length > 0,\n        byElement = elementMatchers.length > 0,\n        superMatcher = function (seed, context, xml, results, outermost) {\n          var elem,\n            j,\n            matcher,\n            matchedCount = 0,\n            i = \"0\",\n            unmatched = seed && [],\n            setMatched = [],\n            contextBackup = outermostContext,\n            // We must always have either seed elements or outermost context\n            elems = seed || byElement && Expr.find.TAG(\"*\", outermost),\n            // Use integer dirruns iff this is the outermost matcher\n            dirrunsUnique = dirruns += contextBackup == null ? 1 : Math.random() || 0.1,\n            len = elems.length;\n          if (outermost) {\n            // Support: IE 11+, Edge 17 - 18+\n            // IE/Edge sometimes throw a \"Permission denied\" error when strict-comparing\n            // two documents; shallow comparisons work.\n            // eslint-disable-next-line eqeqeq\n            outermostContext = context == document || context || outermost;\n          }\n\n          // Add elements passing elementMatchers directly to results\n          // Support: iOS <=7 - 9 only\n          // Tolerate NodeList properties (IE: \"length\"; Safari: <number>) matching\n          // elements by id. (see trac-14142)\n          for (; i !== len && (elem = elems[i]) != null; i++) {\n            if (byElement && elem) {\n              j = 0;\n\n              // Support: IE 11+, Edge 17 - 18+\n              // IE/Edge sometimes throw a \"Permission denied\" error when strict-comparing\n              // two documents; shallow comparisons work.\n              // eslint-disable-next-line eqeqeq\n              if (!context && elem.ownerDocument != document) {\n                setDocument(elem);\n                xml = !documentIsHTML;\n              }\n              while (matcher = elementMatchers[j++]) {\n                if (matcher(elem, context || document, xml)) {\n                  push.call(results, elem);\n                  break;\n                }\n              }\n              if (outermost) {\n                dirruns = dirrunsUnique;\n              }\n            }\n\n            // Track unmatched elements for set filters\n            if (bySet) {\n              // They will have gone through all possible matchers\n              if (elem = !matcher && elem) {\n                matchedCount--;\n              }\n\n              // Lengthen the array for every element, matched or not\n              if (seed) {\n                unmatched.push(elem);\n              }\n            }\n          }\n\n          // `i` is now the count of elements visited above, and adding it to `matchedCount`\n          // makes the latter nonnegative.\n          matchedCount += i;\n\n          // Apply set filters to unmatched elements\n          // NOTE: This can be skipped if there are no unmatched elements (i.e., `matchedCount`\n          // equals `i`), unless we didn't visit _any_ elements in the above loop because we have\n          // no element matchers and no seed.\n          // Incrementing an initially-string \"0\" `i` allows `i` to remain a string only in that\n          // case, which will result in a \"00\" `matchedCount` that differs from `i` but is also\n          // numerically zero.\n          if (bySet && i !== matchedCount) {\n            j = 0;\n            while (matcher = setMatchers[j++]) {\n              matcher(unmatched, setMatched, context, xml);\n            }\n            if (seed) {\n              // Reintegrate element matches to eliminate the need for sorting\n              if (matchedCount > 0) {\n                while (i--) {\n                  if (!(unmatched[i] || setMatched[i])) {\n                    setMatched[i] = pop.call(results);\n                  }\n                }\n              }\n\n              // Discard index placeholder values to get only actual matches\n              setMatched = condense(setMatched);\n            }\n\n            // Add matches to results\n            push.apply(results, setMatched);\n\n            // Seedless set matches succeeding multiple successful matchers stipulate sorting\n            if (outermost && !seed && setMatched.length > 0 && matchedCount + setMatchers.length > 1) {\n              jQuery.uniqueSort(results);\n            }\n          }\n\n          // Override manipulation of globals by nested matchers\n          if (outermost) {\n            dirruns = dirrunsUnique;\n            outermostContext = contextBackup;\n          }\n          return unmatched;\n        };\n      return bySet ? markFunction(superMatcher) : superMatcher;\n    }\n    function compile(selector, match /* Internal Use Only */) {\n      var i,\n        setMatchers = [],\n        elementMatchers = [],\n        cached = compilerCache[selector + \" \"];\n      if (!cached) {\n        // Generate a function of recursive functions that can be used to check each element\n        if (!match) {\n          match = tokenize(selector);\n        }\n        i = match.length;\n        while (i--) {\n          cached = matcherFromTokens(match[i]);\n          if (cached[expando]) {\n            setMatchers.push(cached);\n          } else {\n            elementMatchers.push(cached);\n          }\n        }\n\n        // Cache the compiled function\n        cached = compilerCache(selector, matcherFromGroupMatchers(elementMatchers, setMatchers));\n\n        // Save selector and tokenization\n        cached.selector = selector;\n      }\n      return cached;\n    }\n\n    /**\n     * A low-level selection function that works with jQuery's compiled\n     *  selector functions\n     * @param {String|Function} selector A selector or a pre-compiled\n     *  selector function built with jQuery selector compile\n     * @param {Element} context\n     * @param {Array} [results]\n     * @param {Array} [seed] A set of elements to match against\n     */\n    function select(selector, context, results, seed) {\n      var i,\n        tokens,\n        token,\n        type,\n        find,\n        compiled = typeof selector === \"function\" && selector,\n        match = !seed && tokenize(selector = compiled.selector || selector);\n      results = results || [];\n\n      // Try to minimize operations if there is only one selector in the list and no seed\n      // (the latter of which guarantees us context)\n      if (match.length === 1) {\n        // Reduce context if the leading compound selector is an ID\n        tokens = match[0] = match[0].slice(0);\n        if (tokens.length > 2 && (token = tokens[0]).type === \"ID\" && context.nodeType === 9 && documentIsHTML && Expr.relative[tokens[1].type]) {\n          context = (Expr.find.ID(token.matches[0].replace(runescape, funescape), context) || [])[0];\n          if (!context) {\n            return results;\n\n            // Precompiled matchers will still verify ancestry, so step up a level\n          } else if (compiled) {\n            context = context.parentNode;\n          }\n          selector = selector.slice(tokens.shift().value.length);\n        }\n\n        // Fetch a seed set for right-to-left matching\n        i = matchExpr.needsContext.test(selector) ? 0 : tokens.length;\n        while (i--) {\n          token = tokens[i];\n\n          // Abort if we hit a combinator\n          if (Expr.relative[type = token.type]) {\n            break;\n          }\n          if (find = Expr.find[type]) {\n            // Search, expanding context for leading sibling combinators\n            if (seed = find(token.matches[0].replace(runescape, funescape), rsibling.test(tokens[0].type) && testContext(context.parentNode) || context)) {\n              // If seed is empty or no tokens remain, we can return early\n              tokens.splice(i, 1);\n              selector = seed.length && toSelector(tokens);\n              if (!selector) {\n                push.apply(results, seed);\n                return results;\n              }\n              break;\n            }\n          }\n        }\n      }\n\n      // Compile and execute a filtering function if one is not provided\n      // Provide `match` to avoid retokenization if we modified the selector above\n      (compiled || compile(selector, match))(seed, context, !documentIsHTML, results, !context || rsibling.test(selector) && testContext(context.parentNode) || context);\n      return results;\n    }\n\n    // One-time assignments\n\n    // Support: Android <=4.0 - 4.1+\n    // Sort stability\n    support.sortStable = expando.split(\"\").sort(sortOrder).join(\"\") === expando;\n\n    // Initialize against the default document\n    setDocument();\n\n    // Support: Android <=4.0 - 4.1+\n    // Detached nodes confoundingly follow *each other*\n    support.sortDetached = assert(function (el) {\n      // Should return 1, but returns 4 (following)\n      return el.compareDocumentPosition(document.createElement(\"fieldset\")) & 1;\n    });\n    jQuery.find = find;\n\n    // Deprecated\n    jQuery.expr[\":\"] = jQuery.expr.pseudos;\n    jQuery.unique = jQuery.uniqueSort;\n\n    // These have always been private, but they used to be documented as part of\n    // Sizzle so let's maintain them for now for backwards compatibility purposes.\n    find.compile = compile;\n    find.select = select;\n    find.setDocument = setDocument;\n    find.tokenize = tokenize;\n    find.escape = jQuery.escapeSelector;\n    find.getText = jQuery.text;\n    find.isXML = jQuery.isXMLDoc;\n    find.selectors = jQuery.expr;\n    find.support = jQuery.support;\n    find.uniqueSort = jQuery.uniqueSort;\n\n    /* eslint-enable */\n  })();\n  var dir = function (elem, dir, until) {\n    var matched = [],\n      truncate = until !== undefined;\n    while ((elem = elem[dir]) && elem.nodeType !== 9) {\n      if (elem.nodeType === 1) {\n        if (truncate && jQuery(elem).is(until)) {\n          break;\n        }\n        matched.push(elem);\n      }\n    }\n    return matched;\n  };\n  var siblings = function (n, elem) {\n    var matched = [];\n    for (; n; n = n.nextSibling) {\n      if (n.nodeType === 1 && n !== elem) {\n        matched.push(n);\n      }\n    }\n    return matched;\n  };\n  var rneedsContext = jQuery.expr.match.needsContext;\n  var rsingleTag = /^<([a-z][^\\/\\0>:\\x20\\t\\r\\n\\f]*)[\\x20\\t\\r\\n\\f]*\\/?>(?:<\\/\\1>|)$/i;\n\n  // Implement the identical functionality for filter and not\n  function winnow(elements, qualifier, not) {\n    if (isFunction(qualifier)) {\n      return jQuery.grep(elements, function (elem, i) {\n        return !!qualifier.call(elem, i, elem) !== not;\n      });\n    }\n\n    // Single element\n    if (qualifier.nodeType) {\n      return jQuery.grep(elements, function (elem) {\n        return elem === qualifier !== not;\n      });\n    }\n\n    // Arraylike of elements (jQuery, arguments, Array)\n    if (typeof qualifier !== \"string\") {\n      return jQuery.grep(elements, function (elem) {\n        return indexOf.call(qualifier, elem) > -1 !== not;\n      });\n    }\n\n    // Filtered directly for both simple and complex selectors\n    return jQuery.filter(qualifier, elements, not);\n  }\n  jQuery.filter = function (expr, elems, not) {\n    var elem = elems[0];\n    if (not) {\n      expr = \":not(\" + expr + \")\";\n    }\n    if (elems.length === 1 && elem.nodeType === 1) {\n      return jQuery.find.matchesSelector(elem, expr) ? [elem] : [];\n    }\n    return jQuery.find.matches(expr, jQuery.grep(elems, function (elem) {\n      return elem.nodeType === 1;\n    }));\n  };\n  jQuery.fn.extend({\n    find: function (selector) {\n      var i,\n        ret,\n        len = this.length,\n        self = this;\n      if (typeof selector !== \"string\") {\n        return this.pushStack(jQuery(selector).filter(function () {\n          for (i = 0; i < len; i++) {\n            if (jQuery.contains(self[i], this)) {\n              return true;\n            }\n          }\n        }));\n      }\n      ret = this.pushStack([]);\n      for (i = 0; i < len; i++) {\n        jQuery.find(selector, self[i], ret);\n      }\n      return len > 1 ? jQuery.uniqueSort(ret) : ret;\n    },\n    filter: function (selector) {\n      return this.pushStack(winnow(this, selector || [], false));\n    },\n    not: function (selector) {\n      return this.pushStack(winnow(this, selector || [], true));\n    },\n    is: function (selector) {\n      return !!winnow(this,\n      // If this is a positional/relative selector, check membership in the returned set\n      // so $(\"p:first\").is(\"p:last\") won't return true for a doc with two \"p\".\n      typeof selector === \"string\" && rneedsContext.test(selector) ? jQuery(selector) : selector || [], false).length;\n    }\n  });\n\n  // Initialize a jQuery object\n\n  // A central reference to the root jQuery(document)\n  var rootjQuery,\n    // A simple way to check for HTML strings\n    // Prioritize #id over <tag> to avoid XSS via location.hash (trac-9521)\n    // Strict HTML recognition (trac-11290: must start with <)\n    // Shortcut simple #id case for speed\n    rquickExpr = /^(?:\\s*(<[\\w\\W]+>)[^>]*|#([\\w-]+))$/,\n    init = jQuery.fn.init = function (selector, context, root) {\n      var match, elem;\n\n      // HANDLE: $(\"\"), $(null), $(undefined), $(false)\n      if (!selector) {\n        return this;\n      }\n\n      // Method init() accepts an alternate rootjQuery\n      // so migrate can support jQuery.sub (gh-2101)\n      root = root || rootjQuery;\n\n      // Handle HTML strings\n      if (typeof selector === \"string\") {\n        if (selector[0] === \"<\" && selector[selector.length - 1] === \">\" && selector.length >= 3) {\n          // Assume that strings that start and end with <> are HTML and skip the regex check\n          match = [null, selector, null];\n        } else {\n          match = rquickExpr.exec(selector);\n        }\n\n        // Match html or make sure no context is specified for #id\n        if (match && (match[1] || !context)) {\n          // HANDLE: $(html) -> $(array)\n          if (match[1]) {\n            context = context instanceof jQuery ? context[0] : context;\n\n            // Option to run scripts is true for back-compat\n            // Intentionally let the error be thrown if parseHTML is not present\n            jQuery.merge(this, jQuery.parseHTML(match[1], context && context.nodeType ? context.ownerDocument || context : document, true));\n\n            // HANDLE: $(html, props)\n            if (rsingleTag.test(match[1]) && jQuery.isPlainObject(context)) {\n              for (match in context) {\n                // Properties of context are called as methods if possible\n                if (isFunction(this[match])) {\n                  this[match](context[match]);\n\n                  // ...and otherwise set as attributes\n                } else {\n                  this.attr(match, context[match]);\n                }\n              }\n            }\n            return this;\n\n            // HANDLE: $(#id)\n          } else {\n            elem = document.getElementById(match[2]);\n            if (elem) {\n              // Inject the element directly into the jQuery object\n              this[0] = elem;\n              this.length = 1;\n            }\n            return this;\n          }\n\n          // HANDLE: $(expr, $(...))\n        } else if (!context || context.jquery) {\n          return (context || root).find(selector);\n\n          // HANDLE: $(expr, context)\n          // (which is just equivalent to: $(context).find(expr)\n        } else {\n          return this.constructor(context).find(selector);\n        }\n\n        // HANDLE: $(DOMElement)\n      } else if (selector.nodeType) {\n        this[0] = selector;\n        this.length = 1;\n        return this;\n\n        // HANDLE: $(function)\n        // Shortcut for document ready\n      } else if (isFunction(selector)) {\n        return root.ready !== undefined ? root.ready(selector) :\n        // Execute immediately if ready is not present\n        selector(jQuery);\n      }\n      return jQuery.makeArray(selector, this);\n    };\n\n  // Give the init function the jQuery prototype for later instantiation\n  init.prototype = jQuery.fn;\n\n  // Initialize central reference\n  rootjQuery = jQuery(document);\n  var rparentsprev = /^(?:parents|prev(?:Until|All))/,\n    // Methods guaranteed to produce a unique set when starting from a unique set\n    guaranteedUnique = {\n      children: true,\n      contents: true,\n      next: true,\n      prev: true\n    };\n  jQuery.fn.extend({\n    has: function (target) {\n      var targets = jQuery(target, this),\n        l = targets.length;\n      return this.filter(function () {\n        var i = 0;\n        for (; i < l; i++) {\n          if (jQuery.contains(this, targets[i])) {\n            return true;\n          }\n        }\n      });\n    },\n    closest: function (selectors, context) {\n      var cur,\n        i = 0,\n        l = this.length,\n        matched = [],\n        targets = typeof selectors !== \"string\" && jQuery(selectors);\n\n      // Positional selectors never match, since there's no _selection_ context\n      if (!rneedsContext.test(selectors)) {\n        for (; i < l; i++) {\n          for (cur = this[i]; cur && cur !== context; cur = cur.parentNode) {\n            // Always skip document fragments\n            if (cur.nodeType < 11 && (targets ? targets.index(cur) > -1 :\n            // Don't pass non-elements to jQuery#find\n            cur.nodeType === 1 && jQuery.find.matchesSelector(cur, selectors))) {\n              matched.push(cur);\n              break;\n            }\n          }\n        }\n      }\n      return this.pushStack(matched.length > 1 ? jQuery.uniqueSort(matched) : matched);\n    },\n    // Determine the position of an element within the set\n    index: function (elem) {\n      // No argument, return index in parent\n      if (!elem) {\n        return this[0] && this[0].parentNode ? this.first().prevAll().length : -1;\n      }\n\n      // Index in selector\n      if (typeof elem === \"string\") {\n        return indexOf.call(jQuery(elem), this[0]);\n      }\n\n      // Locate the position of the desired element\n      return indexOf.call(this,\n      // If it receives a jQuery object, the first element is used\n      elem.jquery ? elem[0] : elem);\n    },\n    add: function (selector, context) {\n      return this.pushStack(jQuery.uniqueSort(jQuery.merge(this.get(), jQuery(selector, context))));\n    },\n    addBack: function (selector) {\n      return this.add(selector == null ? this.prevObject : this.prevObject.filter(selector));\n    }\n  });\n  function sibling(cur, dir) {\n    while ((cur = cur[dir]) && cur.nodeType !== 1) {}\n    return cur;\n  }\n  jQuery.each({\n    parent: function (elem) {\n      var parent = elem.parentNode;\n      return parent && parent.nodeType !== 11 ? parent : null;\n    },\n    parents: function (elem) {\n      return dir(elem, \"parentNode\");\n    },\n    parentsUntil: function (elem, _i, until) {\n      return dir(elem, \"parentNode\", until);\n    },\n    next: function (elem) {\n      return sibling(elem, \"nextSibling\");\n    },\n    prev: function (elem) {\n      return sibling(elem, \"previousSibling\");\n    },\n    nextAll: function (elem) {\n      return dir(elem, \"nextSibling\");\n    },\n    prevAll: function (elem) {\n      return dir(elem, \"previousSibling\");\n    },\n    nextUntil: function (elem, _i, until) {\n      return dir(elem, \"nextSibling\", until);\n    },\n    prevUntil: function (elem, _i, until) {\n      return dir(elem, \"previousSibling\", until);\n    },\n    siblings: function (elem) {\n      return siblings((elem.parentNode || {}).firstChild, elem);\n    },\n    children: function (elem) {\n      return siblings(elem.firstChild);\n    },\n    contents: function (elem) {\n      if (elem.contentDocument != null &&\n      // Support: IE 11+\n      // <object> elements with no `data` attribute has an object\n      // `contentDocument` with a `null` prototype.\n      getProto(elem.contentDocument)) {\n        return elem.contentDocument;\n      }\n\n      // Support: IE 9 - 11 only, iOS 7 only, Android Browser <=4.3 only\n      // Treat the template element as a regular one in browsers that\n      // don't support it.\n      if (nodeName(elem, \"template\")) {\n        elem = elem.content || elem;\n      }\n      return jQuery.merge([], elem.childNodes);\n    }\n  }, function (name, fn) {\n    jQuery.fn[name] = function (until, selector) {\n      var matched = jQuery.map(this, fn, until);\n      if (name.slice(-5) !== \"Until\") {\n        selector = until;\n      }\n      if (selector && typeof selector === \"string\") {\n        matched = jQuery.filter(selector, matched);\n      }\n      if (this.length > 1) {\n        // Remove duplicates\n        if (!guaranteedUnique[name]) {\n          jQuery.uniqueSort(matched);\n        }\n\n        // Reverse order for parents* and prev-derivatives\n        if (rparentsprev.test(name)) {\n          matched.reverse();\n        }\n      }\n      return this.pushStack(matched);\n    };\n  });\n  var rnothtmlwhite = /[^\\x20\\t\\r\\n\\f]+/g;\n\n  // Convert String-formatted options into Object-formatted ones\n  function createOptions(options) {\n    var object = {};\n    jQuery.each(options.match(rnothtmlwhite) || [], function (_, flag) {\n      object[flag] = true;\n    });\n    return object;\n  }\n\n  /*\n   * Create a callback list using the following parameters:\n   *\n   *\toptions: an optional list of space-separated options that will change how\n   *\t\t\tthe callback list behaves or a more traditional option object\n   *\n   * By default a callback list will act like an event callback list and can be\n   * \"fired\" multiple times.\n   *\n   * Possible options:\n   *\n   *\tonce:\t\t\twill ensure the callback list can only be fired once (like a Deferred)\n   *\n   *\tmemory:\t\t\twill keep track of previous values and will call any callback added\n   *\t\t\t\t\tafter the list has been fired right away with the latest \"memorized\"\n   *\t\t\t\t\tvalues (like a Deferred)\n   *\n   *\tunique:\t\t\twill ensure a callback can only be added once (no duplicate in the list)\n   *\n   *\tstopOnFalse:\tinterrupt callings when a callback returns false\n   *\n   */\n  jQuery.Callbacks = function (options) {\n    // Convert options from String-formatted to Object-formatted if needed\n    // (we check in cache first)\n    options = typeof options === \"string\" ? createOptions(options) : jQuery.extend({}, options);\n    var\n      // Flag to know if list is currently firing\n      firing,\n      // Last fire value for non-forgettable lists\n      memory,\n      // Flag to know if list was already fired\n      fired,\n      // Flag to prevent firing\n      locked,\n      // Actual callback list\n      list = [],\n      // Queue of execution data for repeatable lists\n      queue = [],\n      // Index of currently firing callback (modified by add/remove as needed)\n      firingIndex = -1,\n      // Fire callbacks\n      fire = function () {\n        // Enforce single-firing\n        locked = locked || options.once;\n\n        // Execute callbacks for all pending executions,\n        // respecting firingIndex overrides and runtime changes\n        fired = firing = true;\n        for (; queue.length; firingIndex = -1) {\n          memory = queue.shift();\n          while (++firingIndex < list.length) {\n            // Run callback and check for early termination\n            if (list[firingIndex].apply(memory[0], memory[1]) === false && options.stopOnFalse) {\n              // Jump to end and forget the data so .add doesn't re-fire\n              firingIndex = list.length;\n              memory = false;\n            }\n          }\n        }\n\n        // Forget the data if we're done with it\n        if (!options.memory) {\n          memory = false;\n        }\n        firing = false;\n\n        // Clean up if we're done firing for good\n        if (locked) {\n          // Keep an empty list if we have data for future add calls\n          if (memory) {\n            list = [];\n\n            // Otherwise, this object is spent\n          } else {\n            list = \"\";\n          }\n        }\n      },\n      // Actual Callbacks object\n      self = {\n        // Add a callback or a collection of callbacks to the list\n        add: function () {\n          if (list) {\n            // If we have memory from a past run, we should fire after adding\n            if (memory && !firing) {\n              firingIndex = list.length - 1;\n              queue.push(memory);\n            }\n            (function add(args) {\n              jQuery.each(args, function (_, arg) {\n                if (isFunction(arg)) {\n                  if (!options.unique || !self.has(arg)) {\n                    list.push(arg);\n                  }\n                } else if (arg && arg.length && toType(arg) !== \"string\") {\n                  // Inspect recursively\n                  add(arg);\n                }\n              });\n            })(arguments);\n            if (memory && !firing) {\n              fire();\n            }\n          }\n          return this;\n        },\n        // Remove a callback from the list\n        remove: function () {\n          jQuery.each(arguments, function (_, arg) {\n            var index;\n            while ((index = jQuery.inArray(arg, list, index)) > -1) {\n              list.splice(index, 1);\n\n              // Handle firing indexes\n              if (index <= firingIndex) {\n                firingIndex--;\n              }\n            }\n          });\n          return this;\n        },\n        // Check if a given callback is in the list.\n        // If no argument is given, return whether or not list has callbacks attached.\n        has: function (fn) {\n          return fn ? jQuery.inArray(fn, list) > -1 : list.length > 0;\n        },\n        // Remove all callbacks from the list\n        empty: function () {\n          if (list) {\n            list = [];\n          }\n          return this;\n        },\n        // Disable .fire and .add\n        // Abort any current/pending executions\n        // Clear all callbacks and values\n        disable: function () {\n          locked = queue = [];\n          list = memory = \"\";\n          return this;\n        },\n        disabled: function () {\n          return !list;\n        },\n        // Disable .fire\n        // Also disable .add unless we have memory (since it would have no effect)\n        // Abort any pending executions\n        lock: function () {\n          locked = queue = [];\n          if (!memory && !firing) {\n            list = memory = \"\";\n          }\n          return this;\n        },\n        locked: function () {\n          return !!locked;\n        },\n        // Call all callbacks with the given context and arguments\n        fireWith: function (context, args) {\n          if (!locked) {\n            args = args || [];\n            args = [context, args.slice ? args.slice() : args];\n            queue.push(args);\n            if (!firing) {\n              fire();\n            }\n          }\n          return this;\n        },\n        // Call all the callbacks with the given arguments\n        fire: function () {\n          self.fireWith(this, arguments);\n          return this;\n        },\n        // To know if the callbacks have already been called at least once\n        fired: function () {\n          return !!fired;\n        }\n      };\n    return self;\n  };\n  function Identity(v) {\n    return v;\n  }\n  function Thrower(ex) {\n    throw ex;\n  }\n  function adoptValue(value, resolve, reject, noValue) {\n    var method;\n    try {\n      // Check for promise aspect first to privilege synchronous behavior\n      if (value && isFunction(method = value.promise)) {\n        method.call(value).done(resolve).fail(reject);\n\n        // Other thenables\n      } else if (value && isFunction(method = value.then)) {\n        method.call(value, resolve, reject);\n\n        // Other non-thenables\n      } else {\n        // Control `resolve` arguments by letting Array#slice cast boolean `noValue` to integer:\n        // * false: [ value ].slice( 0 ) => resolve( value )\n        // * true: [ value ].slice( 1 ) => resolve()\n        resolve.apply(undefined, [value].slice(noValue));\n      }\n\n      // For Promises/A+, convert exceptions into rejections\n      // Since jQuery.when doesn't unwrap thenables, we can skip the extra checks appearing in\n      // Deferred#then to conditionally suppress rejection.\n    } catch (value) {\n      // Support: Android 4.0 only\n      // Strict mode functions invoked without .call/.apply get global-object context\n      reject.apply(undefined, [value]);\n    }\n  }\n  jQuery.extend({\n    Deferred: function (func) {\n      var tuples = [\n        // action, add listener, callbacks,\n        // ... .then handlers, argument index, [final state]\n        [\"notify\", \"progress\", jQuery.Callbacks(\"memory\"), jQuery.Callbacks(\"memory\"), 2], [\"resolve\", \"done\", jQuery.Callbacks(\"once memory\"), jQuery.Callbacks(\"once memory\"), 0, \"resolved\"], [\"reject\", \"fail\", jQuery.Callbacks(\"once memory\"), jQuery.Callbacks(\"once memory\"), 1, \"rejected\"]],\n        state = \"pending\",\n        promise = {\n          state: function () {\n            return state;\n          },\n          always: function () {\n            deferred.done(arguments).fail(arguments);\n            return this;\n          },\n          \"catch\": function (fn) {\n            return promise.then(null, fn);\n          },\n          // Keep pipe for back-compat\n          pipe: function /* fnDone, fnFail, fnProgress */\n          () {\n            var fns = arguments;\n            return jQuery.Deferred(function (newDefer) {\n              jQuery.each(tuples, function (_i, tuple) {\n                // Map tuples (progress, done, fail) to arguments (done, fail, progress)\n                var fn = isFunction(fns[tuple[4]]) && fns[tuple[4]];\n\n                // deferred.progress(function() { bind to newDefer or newDefer.notify })\n                // deferred.done(function() { bind to newDefer or newDefer.resolve })\n                // deferred.fail(function() { bind to newDefer or newDefer.reject })\n                deferred[tuple[1]](function () {\n                  var returned = fn && fn.apply(this, arguments);\n                  if (returned && isFunction(returned.promise)) {\n                    returned.promise().progress(newDefer.notify).done(newDefer.resolve).fail(newDefer.reject);\n                  } else {\n                    newDefer[tuple[0] + \"With\"](this, fn ? [returned] : arguments);\n                  }\n                });\n              });\n              fns = null;\n            }).promise();\n          },\n          then: function (onFulfilled, onRejected, onProgress) {\n            var maxDepth = 0;\n            function resolve(depth, deferred, handler, special) {\n              return function () {\n                var that = this,\n                  args = arguments,\n                  mightThrow = function () {\n                    var returned, then;\n\n                    // Support: Promises/A+ section *******.3\n                    // https://promisesaplus.com/#point-59\n                    // Ignore double-resolution attempts\n                    if (depth < maxDepth) {\n                      return;\n                    }\n                    returned = handler.apply(that, args);\n\n                    // Support: Promises/A+ section 2.3.1\n                    // https://promisesaplus.com/#point-48\n                    if (returned === deferred.promise()) {\n                      throw new TypeError(\"Thenable self-resolution\");\n                    }\n\n                    // Support: Promises/A+ sections 2.3.3.1, 3.5\n                    // https://promisesaplus.com/#point-54\n                    // https://promisesaplus.com/#point-75\n                    // Retrieve `then` only once\n                    then = returned && (\n                    // Support: Promises/A+ section 2.3.4\n                    // https://promisesaplus.com/#point-64\n                    // Only check objects and functions for thenability\n                    typeof returned === \"object\" || typeof returned === \"function\") && returned.then;\n\n                    // Handle a returned thenable\n                    if (isFunction(then)) {\n                      // Special processors (notify) just wait for resolution\n                      if (special) {\n                        then.call(returned, resolve(maxDepth, deferred, Identity, special), resolve(maxDepth, deferred, Thrower, special));\n\n                        // Normal processors (resolve) also hook into progress\n                      } else {\n                        // ...and disregard older resolution values\n                        maxDepth++;\n                        then.call(returned, resolve(maxDepth, deferred, Identity, special), resolve(maxDepth, deferred, Thrower, special), resolve(maxDepth, deferred, Identity, deferred.notifyWith));\n                      }\n\n                      // Handle all other returned values\n                    } else {\n                      // Only substitute handlers pass on context\n                      // and multiple values (non-spec behavior)\n                      if (handler !== Identity) {\n                        that = undefined;\n                        args = [returned];\n                      }\n\n                      // Process the value(s)\n                      // Default process is resolve\n                      (special || deferred.resolveWith)(that, args);\n                    }\n                  },\n                  // Only normal processors (resolve) catch and reject exceptions\n                  process = special ? mightThrow : function () {\n                    try {\n                      mightThrow();\n                    } catch (e) {\n                      if (jQuery.Deferred.exceptionHook) {\n                        jQuery.Deferred.exceptionHook(e, process.error);\n                      }\n\n                      // Support: Promises/A+ section *******.4.1\n                      // https://promisesaplus.com/#point-61\n                      // Ignore post-resolution exceptions\n                      if (depth + 1 >= maxDepth) {\n                        // Only substitute handlers pass on context\n                        // and multiple values (non-spec behavior)\n                        if (handler !== Thrower) {\n                          that = undefined;\n                          args = [e];\n                        }\n                        deferred.rejectWith(that, args);\n                      }\n                    }\n                  };\n\n                // Support: Promises/A+ section *******.1\n                // https://promisesaplus.com/#point-57\n                // Re-resolve promises immediately to dodge false rejection from\n                // subsequent errors\n                if (depth) {\n                  process();\n                } else {\n                  // Call an optional hook to record the error, in case of exception\n                  // since it's otherwise lost when execution goes async\n                  if (jQuery.Deferred.getErrorHook) {\n                    process.error = jQuery.Deferred.getErrorHook();\n\n                    // The deprecated alias of the above. While the name suggests\n                    // returning the stack, not an error instance, jQuery just passes\n                    // it directly to `console.warn` so both will work; an instance\n                    // just better cooperates with source maps.\n                  } else if (jQuery.Deferred.getStackHook) {\n                    process.error = jQuery.Deferred.getStackHook();\n                  }\n                  window.setTimeout(process);\n                }\n              };\n            }\n            return jQuery.Deferred(function (newDefer) {\n              // progress_handlers.add( ... )\n              tuples[0][3].add(resolve(0, newDefer, isFunction(onProgress) ? onProgress : Identity, newDefer.notifyWith));\n\n              // fulfilled_handlers.add( ... )\n              tuples[1][3].add(resolve(0, newDefer, isFunction(onFulfilled) ? onFulfilled : Identity));\n\n              // rejected_handlers.add( ... )\n              tuples[2][3].add(resolve(0, newDefer, isFunction(onRejected) ? onRejected : Thrower));\n            }).promise();\n          },\n          // Get a promise for this deferred\n          // If obj is provided, the promise aspect is added to the object\n          promise: function (obj) {\n            return obj != null ? jQuery.extend(obj, promise) : promise;\n          }\n        },\n        deferred = {};\n\n      // Add list-specific methods\n      jQuery.each(tuples, function (i, tuple) {\n        var list = tuple[2],\n          stateString = tuple[5];\n\n        // promise.progress = list.add\n        // promise.done = list.add\n        // promise.fail = list.add\n        promise[tuple[1]] = list.add;\n\n        // Handle state\n        if (stateString) {\n          list.add(function () {\n            // state = \"resolved\" (i.e., fulfilled)\n            // state = \"rejected\"\n            state = stateString;\n          },\n          // rejected_callbacks.disable\n          // fulfilled_callbacks.disable\n          tuples[3 - i][2].disable,\n          // rejected_handlers.disable\n          // fulfilled_handlers.disable\n          tuples[3 - i][3].disable,\n          // progress_callbacks.lock\n          tuples[0][2].lock,\n          // progress_handlers.lock\n          tuples[0][3].lock);\n        }\n\n        // progress_handlers.fire\n        // fulfilled_handlers.fire\n        // rejected_handlers.fire\n        list.add(tuple[3].fire);\n\n        // deferred.notify = function() { deferred.notifyWith(...) }\n        // deferred.resolve = function() { deferred.resolveWith(...) }\n        // deferred.reject = function() { deferred.rejectWith(...) }\n        deferred[tuple[0]] = function () {\n          deferred[tuple[0] + \"With\"](this === deferred ? undefined : this, arguments);\n          return this;\n        };\n\n        // deferred.notifyWith = list.fireWith\n        // deferred.resolveWith = list.fireWith\n        // deferred.rejectWith = list.fireWith\n        deferred[tuple[0] + \"With\"] = list.fireWith;\n      });\n\n      // Make the deferred a promise\n      promise.promise(deferred);\n\n      // Call given func if any\n      if (func) {\n        func.call(deferred, deferred);\n      }\n\n      // All done!\n      return deferred;\n    },\n    // Deferred helper\n    when: function (singleValue) {\n      var\n        // count of uncompleted subordinates\n        remaining = arguments.length,\n        // count of unprocessed arguments\n        i = remaining,\n        // subordinate fulfillment data\n        resolveContexts = Array(i),\n        resolveValues = slice.call(arguments),\n        // the primary Deferred\n        primary = jQuery.Deferred(),\n        // subordinate callback factory\n        updateFunc = function (i) {\n          return function (value) {\n            resolveContexts[i] = this;\n            resolveValues[i] = arguments.length > 1 ? slice.call(arguments) : value;\n            if (! --remaining) {\n              primary.resolveWith(resolveContexts, resolveValues);\n            }\n          };\n        };\n\n      // Single- and empty arguments are adopted like Promise.resolve\n      if (remaining <= 1) {\n        adoptValue(singleValue, primary.done(updateFunc(i)).resolve, primary.reject, !remaining);\n\n        // Use .then() to unwrap secondary thenables (cf. gh-3000)\n        if (primary.state() === \"pending\" || isFunction(resolveValues[i] && resolveValues[i].then)) {\n          return primary.then();\n        }\n      }\n\n      // Multiple arguments are aggregated like Promise.all array elements\n      while (i--) {\n        adoptValue(resolveValues[i], updateFunc(i), primary.reject);\n      }\n      return primary.promise();\n    }\n  });\n\n  // These usually indicate a programmer mistake during development,\n  // warn about them ASAP rather than swallowing them by default.\n  var rerrorNames = /^(Eval|Internal|Range|Reference|Syntax|Type|URI)Error$/;\n\n  // If `jQuery.Deferred.getErrorHook` is defined, `asyncError` is an error\n  // captured before the async barrier to get the original error cause\n  // which may otherwise be hidden.\n  jQuery.Deferred.exceptionHook = function (error, asyncError) {\n    // Support: IE 8 - 9 only\n    // Console exists when dev tools are open, which can happen at any time\n    if (window.console && window.console.warn && error && rerrorNames.test(error.name)) {\n      window.console.warn(\"jQuery.Deferred exception: \" + error.message, error.stack, asyncError);\n    }\n  };\n  jQuery.readyException = function (error) {\n    window.setTimeout(function () {\n      throw error;\n    });\n  };\n\n  // The deferred used on DOM ready\n  var readyList = jQuery.Deferred();\n  jQuery.fn.ready = function (fn) {\n    readyList.then(fn)\n\n    // Wrap jQuery.readyException in a function so that the lookup\n    // happens at the time of error handling instead of callback\n    // registration.\n    .catch(function (error) {\n      jQuery.readyException(error);\n    });\n    return this;\n  };\n  jQuery.extend({\n    // Is the DOM ready to be used? Set to true once it occurs.\n    isReady: false,\n    // A counter to track how many items to wait for before\n    // the ready event fires. See trac-6781\n    readyWait: 1,\n    // Handle when the DOM is ready\n    ready: function (wait) {\n      // Abort if there are pending holds or we're already ready\n      if (wait === true ? --jQuery.readyWait : jQuery.isReady) {\n        return;\n      }\n\n      // Remember that the DOM is ready\n      jQuery.isReady = true;\n\n      // If a normal DOM Ready event fired, decrement, and wait if need be\n      if (wait !== true && --jQuery.readyWait > 0) {\n        return;\n      }\n\n      // If there are functions bound, to execute\n      readyList.resolveWith(document, [jQuery]);\n    }\n  });\n  jQuery.ready.then = readyList.then;\n\n  // The ready event handler and self cleanup method\n  function completed() {\n    document.removeEventListener(\"DOMContentLoaded\", completed);\n    window.removeEventListener(\"load\", completed);\n    jQuery.ready();\n  }\n\n  // Catch cases where $(document).ready() is called\n  // after the browser event has already occurred.\n  // Support: IE <=9 - 10 only\n  // Older IE sometimes signals \"interactive\" too soon\n  if (document.readyState === \"complete\" || document.readyState !== \"loading\" && !document.documentElement.doScroll) {\n    // Handle it asynchronously to allow scripts the opportunity to delay ready\n    window.setTimeout(jQuery.ready);\n  } else {\n    // Use the handy event callback\n    document.addEventListener(\"DOMContentLoaded\", completed);\n\n    // A fallback to window.onload, that will always work\n    window.addEventListener(\"load\", completed);\n  }\n\n  // Multifunctional method to get and set values of a collection\n  // The value/s can optionally be executed if it's a function\n  var access = function (elems, fn, key, value, chainable, emptyGet, raw) {\n    var i = 0,\n      len = elems.length,\n      bulk = key == null;\n\n    // Sets many values\n    if (toType(key) === \"object\") {\n      chainable = true;\n      for (i in key) {\n        access(elems, fn, i, key[i], true, emptyGet, raw);\n      }\n\n      // Sets one value\n    } else if (value !== undefined) {\n      chainable = true;\n      if (!isFunction(value)) {\n        raw = true;\n      }\n      if (bulk) {\n        // Bulk operations run against the entire set\n        if (raw) {\n          fn.call(elems, value);\n          fn = null;\n\n          // ...except when executing function values\n        } else {\n          bulk = fn;\n          fn = function (elem, _key, value) {\n            return bulk.call(jQuery(elem), value);\n          };\n        }\n      }\n      if (fn) {\n        for (; i < len; i++) {\n          fn(elems[i], key, raw ? value : value.call(elems[i], i, fn(elems[i], key)));\n        }\n      }\n    }\n    if (chainable) {\n      return elems;\n    }\n\n    // Gets\n    if (bulk) {\n      return fn.call(elems);\n    }\n    return len ? fn(elems[0], key) : emptyGet;\n  };\n\n  // Matches dashed string for camelizing\n  var rmsPrefix = /^-ms-/,\n    rdashAlpha = /-([a-z])/g;\n\n  // Used by camelCase as callback to replace()\n  function fcamelCase(_all, letter) {\n    return letter.toUpperCase();\n  }\n\n  // Convert dashed to camelCase; used by the css and data modules\n  // Support: IE <=9 - 11, Edge 12 - 15\n  // Microsoft forgot to hump their vendor prefix (trac-9572)\n  function camelCase(string) {\n    return string.replace(rmsPrefix, \"ms-\").replace(rdashAlpha, fcamelCase);\n  }\n  var acceptData = function (owner) {\n    // Accepts only:\n    //  - Node\n    //    - Node.ELEMENT_NODE\n    //    - Node.DOCUMENT_NODE\n    //  - Object\n    //    - Any\n    return owner.nodeType === 1 || owner.nodeType === 9 || !+owner.nodeType;\n  };\n  function Data() {\n    this.expando = jQuery.expando + Data.uid++;\n  }\n  Data.uid = 1;\n  Data.prototype = {\n    cache: function (owner) {\n      // Check if the owner object already has a cache\n      var value = owner[this.expando];\n\n      // If not, create one\n      if (!value) {\n        value = {};\n\n        // We can accept data for non-element nodes in modern browsers,\n        // but we should not, see trac-8335.\n        // Always return an empty object.\n        if (acceptData(owner)) {\n          // If it is a node unlikely to be stringify-ed or looped over\n          // use plain assignment\n          if (owner.nodeType) {\n            owner[this.expando] = value;\n\n            // Otherwise secure it in a non-enumerable property\n            // configurable must be true to allow the property to be\n            // deleted when data is removed\n          } else {\n            Object.defineProperty(owner, this.expando, {\n              value: value,\n              configurable: true\n            });\n          }\n        }\n      }\n      return value;\n    },\n    set: function (owner, data, value) {\n      var prop,\n        cache = this.cache(owner);\n\n      // Handle: [ owner, key, value ] args\n      // Always use camelCase key (gh-2257)\n      if (typeof data === \"string\") {\n        cache[camelCase(data)] = value;\n\n        // Handle: [ owner, { properties } ] args\n      } else {\n        // Copy the properties one-by-one to the cache object\n        for (prop in data) {\n          cache[camelCase(prop)] = data[prop];\n        }\n      }\n      return cache;\n    },\n    get: function (owner, key) {\n      return key === undefined ? this.cache(owner) :\n      // Always use camelCase key (gh-2257)\n      owner[this.expando] && owner[this.expando][camelCase(key)];\n    },\n    access: function (owner, key, value) {\n      // In cases where either:\n      //\n      //   1. No key was specified\n      //   2. A string key was specified, but no value provided\n      //\n      // Take the \"read\" path and allow the get method to determine\n      // which value to return, respectively either:\n      //\n      //   1. The entire cache object\n      //   2. The data stored at the key\n      //\n      if (key === undefined || key && typeof key === \"string\" && value === undefined) {\n        return this.get(owner, key);\n      }\n\n      // When the key is not a string, or both a key and value\n      // are specified, set or extend (existing objects) with either:\n      //\n      //   1. An object of properties\n      //   2. A key and value\n      //\n      this.set(owner, key, value);\n\n      // Since the \"set\" path can have two possible entry points\n      // return the expected data based on which path was taken[*]\n      return value !== undefined ? value : key;\n    },\n    remove: function (owner, key) {\n      var i,\n        cache = owner[this.expando];\n      if (cache === undefined) {\n        return;\n      }\n      if (key !== undefined) {\n        // Support array or space separated string of keys\n        if (Array.isArray(key)) {\n          // If key is an array of keys...\n          // We always set camelCase keys, so remove that.\n          key = key.map(camelCase);\n        } else {\n          key = camelCase(key);\n\n          // If a key with the spaces exists, use it.\n          // Otherwise, create an array by matching non-whitespace\n          key = key in cache ? [key] : key.match(rnothtmlwhite) || [];\n        }\n        i = key.length;\n        while (i--) {\n          delete cache[key[i]];\n        }\n      }\n\n      // Remove the expando if there's no more data\n      if (key === undefined || jQuery.isEmptyObject(cache)) {\n        // Support: Chrome <=35 - 45\n        // Webkit & Blink performance suffers when deleting properties\n        // from DOM nodes, so set to undefined instead\n        // https://bugs.chromium.org/p/chromium/issues/detail?id=378607 (bug restricted)\n        if (owner.nodeType) {\n          owner[this.expando] = undefined;\n        } else {\n          delete owner[this.expando];\n        }\n      }\n    },\n    hasData: function (owner) {\n      var cache = owner[this.expando];\n      return cache !== undefined && !jQuery.isEmptyObject(cache);\n    }\n  };\n  var dataPriv = new Data();\n  var dataUser = new Data();\n\n  //\tImplementation Summary\n  //\n  //\t1. Enforce API surface and semantic compatibility with 1.9.x branch\n  //\t2. Improve the module's maintainability by reducing the storage\n  //\t\tpaths to a single mechanism.\n  //\t3. Use the same single mechanism to support \"private\" and \"user\" data.\n  //\t4. _Never_ expose \"private\" data to user code (TODO: Drop _data, _removeData)\n  //\t5. Avoid exposing implementation details on user objects (eg. expando properties)\n  //\t6. Provide a clear path for implementation upgrade to WeakMap in 2014\n\n  var rbrace = /^(?:\\{[\\w\\W]*\\}|\\[[\\w\\W]*\\])$/,\n    rmultiDash = /[A-Z]/g;\n  function getData(data) {\n    if (data === \"true\") {\n      return true;\n    }\n    if (data === \"false\") {\n      return false;\n    }\n    if (data === \"null\") {\n      return null;\n    }\n\n    // Only convert to a number if it doesn't change the string\n    if (data === +data + \"\") {\n      return +data;\n    }\n    if (rbrace.test(data)) {\n      return JSON.parse(data);\n    }\n    return data;\n  }\n  function dataAttr(elem, key, data) {\n    var name;\n\n    // If nothing was found internally, try to fetch any\n    // data from the HTML5 data-* attribute\n    if (data === undefined && elem.nodeType === 1) {\n      name = \"data-\" + key.replace(rmultiDash, \"-$&\").toLowerCase();\n      data = elem.getAttribute(name);\n      if (typeof data === \"string\") {\n        try {\n          data = getData(data);\n        } catch (e) {}\n\n        // Make sure we set the data so it isn't changed later\n        dataUser.set(elem, key, data);\n      } else {\n        data = undefined;\n      }\n    }\n    return data;\n  }\n  jQuery.extend({\n    hasData: function (elem) {\n      return dataUser.hasData(elem) || dataPriv.hasData(elem);\n    },\n    data: function (elem, name, data) {\n      return dataUser.access(elem, name, data);\n    },\n    removeData: function (elem, name) {\n      dataUser.remove(elem, name);\n    },\n    // TODO: Now that all calls to _data and _removeData have been replaced\n    // with direct calls to dataPriv methods, these can be deprecated.\n    _data: function (elem, name, data) {\n      return dataPriv.access(elem, name, data);\n    },\n    _removeData: function (elem, name) {\n      dataPriv.remove(elem, name);\n    }\n  });\n  jQuery.fn.extend({\n    data: function (key, value) {\n      var i,\n        name,\n        data,\n        elem = this[0],\n        attrs = elem && elem.attributes;\n\n      // Gets all values\n      if (key === undefined) {\n        if (this.length) {\n          data = dataUser.get(elem);\n          if (elem.nodeType === 1 && !dataPriv.get(elem, \"hasDataAttrs\")) {\n            i = attrs.length;\n            while (i--) {\n              // Support: IE 11 only\n              // The attrs elements can be null (trac-14894)\n              if (attrs[i]) {\n                name = attrs[i].name;\n                if (name.indexOf(\"data-\") === 0) {\n                  name = camelCase(name.slice(5));\n                  dataAttr(elem, name, data[name]);\n                }\n              }\n            }\n            dataPriv.set(elem, \"hasDataAttrs\", true);\n          }\n        }\n        return data;\n      }\n\n      // Sets multiple values\n      if (typeof key === \"object\") {\n        return this.each(function () {\n          dataUser.set(this, key);\n        });\n      }\n      return access(this, function (value) {\n        var data;\n\n        // The calling jQuery object (element matches) is not empty\n        // (and therefore has an element appears at this[ 0 ]) and the\n        // `value` parameter was not undefined. An empty jQuery object\n        // will result in `undefined` for elem = this[ 0 ] which will\n        // throw an exception if an attempt to read a data cache is made.\n        if (elem && value === undefined) {\n          // Attempt to get data from the cache\n          // The key will always be camelCased in Data\n          data = dataUser.get(elem, key);\n          if (data !== undefined) {\n            return data;\n          }\n\n          // Attempt to \"discover\" the data in\n          // HTML5 custom data-* attrs\n          data = dataAttr(elem, key);\n          if (data !== undefined) {\n            return data;\n          }\n\n          // We tried really hard, but the data doesn't exist.\n          return;\n        }\n\n        // Set the data...\n        this.each(function () {\n          // We always store the camelCased key\n          dataUser.set(this, key, value);\n        });\n      }, null, value, arguments.length > 1, null, true);\n    },\n    removeData: function (key) {\n      return this.each(function () {\n        dataUser.remove(this, key);\n      });\n    }\n  });\n  jQuery.extend({\n    queue: function (elem, type, data) {\n      var queue;\n      if (elem) {\n        type = (type || \"fx\") + \"queue\";\n        queue = dataPriv.get(elem, type);\n\n        // Speed up dequeue by getting out quickly if this is just a lookup\n        if (data) {\n          if (!queue || Array.isArray(data)) {\n            queue = dataPriv.access(elem, type, jQuery.makeArray(data));\n          } else {\n            queue.push(data);\n          }\n        }\n        return queue || [];\n      }\n    },\n    dequeue: function (elem, type) {\n      type = type || \"fx\";\n      var queue = jQuery.queue(elem, type),\n        startLength = queue.length,\n        fn = queue.shift(),\n        hooks = jQuery._queueHooks(elem, type),\n        next = function () {\n          jQuery.dequeue(elem, type);\n        };\n\n      // If the fx queue is dequeued, always remove the progress sentinel\n      if (fn === \"inprogress\") {\n        fn = queue.shift();\n        startLength--;\n      }\n      if (fn) {\n        // Add a progress sentinel to prevent the fx queue from being\n        // automatically dequeued\n        if (type === \"fx\") {\n          queue.unshift(\"inprogress\");\n        }\n\n        // Clear up the last queue stop function\n        delete hooks.stop;\n        fn.call(elem, next, hooks);\n      }\n      if (!startLength && hooks) {\n        hooks.empty.fire();\n      }\n    },\n    // Not public - generate a queueHooks object, or return the current one\n    _queueHooks: function (elem, type) {\n      var key = type + \"queueHooks\";\n      return dataPriv.get(elem, key) || dataPriv.access(elem, key, {\n        empty: jQuery.Callbacks(\"once memory\").add(function () {\n          dataPriv.remove(elem, [type + \"queue\", key]);\n        })\n      });\n    }\n  });\n  jQuery.fn.extend({\n    queue: function (type, data) {\n      var setter = 2;\n      if (typeof type !== \"string\") {\n        data = type;\n        type = \"fx\";\n        setter--;\n      }\n      if (arguments.length < setter) {\n        return jQuery.queue(this[0], type);\n      }\n      return data === undefined ? this : this.each(function () {\n        var queue = jQuery.queue(this, type, data);\n\n        // Ensure a hooks for this queue\n        jQuery._queueHooks(this, type);\n        if (type === \"fx\" && queue[0] !== \"inprogress\") {\n          jQuery.dequeue(this, type);\n        }\n      });\n    },\n    dequeue: function (type) {\n      return this.each(function () {\n        jQuery.dequeue(this, type);\n      });\n    },\n    clearQueue: function (type) {\n      return this.queue(type || \"fx\", []);\n    },\n    // Get a promise resolved when queues of a certain type\n    // are emptied (fx is the type by default)\n    promise: function (type, obj) {\n      var tmp,\n        count = 1,\n        defer = jQuery.Deferred(),\n        elements = this,\n        i = this.length,\n        resolve = function () {\n          if (! --count) {\n            defer.resolveWith(elements, [elements]);\n          }\n        };\n      if (typeof type !== \"string\") {\n        obj = type;\n        type = undefined;\n      }\n      type = type || \"fx\";\n      while (i--) {\n        tmp = dataPriv.get(elements[i], type + \"queueHooks\");\n        if (tmp && tmp.empty) {\n          count++;\n          tmp.empty.add(resolve);\n        }\n      }\n      resolve();\n      return defer.promise(obj);\n    }\n  });\n  var pnum = /[+-]?(?:\\d*\\.|)\\d+(?:[eE][+-]?\\d+|)/.source;\n  var rcssNum = new RegExp(\"^(?:([+-])=|)(\" + pnum + \")([a-z%]*)$\", \"i\");\n  var cssExpand = [\"Top\", \"Right\", \"Bottom\", \"Left\"];\n  var documentElement = document.documentElement;\n  var isAttached = function (elem) {\n      return jQuery.contains(elem.ownerDocument, elem);\n    },\n    composed = {\n      composed: true\n    };\n\n  // Support: IE 9 - 11+, Edge 12 - 18+, iOS 10.0 - 10.2 only\n  // Check attachment across shadow DOM boundaries when possible (gh-3504)\n  // Support: iOS 10.0-10.2 only\n  // Early iOS 10 versions support `attachShadow` but not `getRootNode`,\n  // leading to errors. We need to check for `getRootNode`.\n  if (documentElement.getRootNode) {\n    isAttached = function (elem) {\n      return jQuery.contains(elem.ownerDocument, elem) || elem.getRootNode(composed) === elem.ownerDocument;\n    };\n  }\n  var isHiddenWithinTree = function (elem, el) {\n    // isHiddenWithinTree might be called from jQuery#filter function;\n    // in that case, element will be second argument\n    elem = el || elem;\n\n    // Inline style trumps all\n    return elem.style.display === \"none\" || elem.style.display === \"\" &&\n    // Otherwise, check computed style\n    // Support: Firefox <=43 - 45\n    // Disconnected elements can have computed display: none, so first confirm that elem is\n    // in the document.\n    isAttached(elem) && jQuery.css(elem, \"display\") === \"none\";\n  };\n  function adjustCSS(elem, prop, valueParts, tween) {\n    var adjusted,\n      scale,\n      maxIterations = 20,\n      currentValue = tween ? function () {\n        return tween.cur();\n      } : function () {\n        return jQuery.css(elem, prop, \"\");\n      },\n      initial = currentValue(),\n      unit = valueParts && valueParts[3] || (jQuery.cssNumber[prop] ? \"\" : \"px\"),\n      // Starting value computation is required for potential unit mismatches\n      initialInUnit = elem.nodeType && (jQuery.cssNumber[prop] || unit !== \"px\" && +initial) && rcssNum.exec(jQuery.css(elem, prop));\n    if (initialInUnit && initialInUnit[3] !== unit) {\n      // Support: Firefox <=54\n      // Halve the iteration target value to prevent interference from CSS upper bounds (gh-2144)\n      initial = initial / 2;\n\n      // Trust units reported by jQuery.css\n      unit = unit || initialInUnit[3];\n\n      // Iteratively approximate from a nonzero starting point\n      initialInUnit = +initial || 1;\n      while (maxIterations--) {\n        // Evaluate and update our best guess (doubling guesses that zero out).\n        // Finish if the scale equals or crosses 1 (making the old*new product non-positive).\n        jQuery.style(elem, prop, initialInUnit + unit);\n        if ((1 - scale) * (1 - (scale = currentValue() / initial || 0.5)) <= 0) {\n          maxIterations = 0;\n        }\n        initialInUnit = initialInUnit / scale;\n      }\n      initialInUnit = initialInUnit * 2;\n      jQuery.style(elem, prop, initialInUnit + unit);\n\n      // Make sure we update the tween properties later on\n      valueParts = valueParts || [];\n    }\n    if (valueParts) {\n      initialInUnit = +initialInUnit || +initial || 0;\n\n      // Apply relative offset (+=/-=) if specified\n      adjusted = valueParts[1] ? initialInUnit + (valueParts[1] + 1) * valueParts[2] : +valueParts[2];\n      if (tween) {\n        tween.unit = unit;\n        tween.start = initialInUnit;\n        tween.end = adjusted;\n      }\n    }\n    return adjusted;\n  }\n  var defaultDisplayMap = {};\n  function getDefaultDisplay(elem) {\n    var temp,\n      doc = elem.ownerDocument,\n      nodeName = elem.nodeName,\n      display = defaultDisplayMap[nodeName];\n    if (display) {\n      return display;\n    }\n    temp = doc.body.appendChild(doc.createElement(nodeName));\n    display = jQuery.css(temp, \"display\");\n    temp.parentNode.removeChild(temp);\n    if (display === \"none\") {\n      display = \"block\";\n    }\n    defaultDisplayMap[nodeName] = display;\n    return display;\n  }\n  function showHide(elements, show) {\n    var display,\n      elem,\n      values = [],\n      index = 0,\n      length = elements.length;\n\n    // Determine new display value for elements that need to change\n    for (; index < length; index++) {\n      elem = elements[index];\n      if (!elem.style) {\n        continue;\n      }\n      display = elem.style.display;\n      if (show) {\n        // Since we force visibility upon cascade-hidden elements, an immediate (and slow)\n        // check is required in this first loop unless we have a nonempty display value (either\n        // inline or about-to-be-restored)\n        if (display === \"none\") {\n          values[index] = dataPriv.get(elem, \"display\") || null;\n          if (!values[index]) {\n            elem.style.display = \"\";\n          }\n        }\n        if (elem.style.display === \"\" && isHiddenWithinTree(elem)) {\n          values[index] = getDefaultDisplay(elem);\n        }\n      } else {\n        if (display !== \"none\") {\n          values[index] = \"none\";\n\n          // Remember what we're overwriting\n          dataPriv.set(elem, \"display\", display);\n        }\n      }\n    }\n\n    // Set the display of the elements in a second loop to avoid constant reflow\n    for (index = 0; index < length; index++) {\n      if (values[index] != null) {\n        elements[index].style.display = values[index];\n      }\n    }\n    return elements;\n  }\n  jQuery.fn.extend({\n    show: function () {\n      return showHide(this, true);\n    },\n    hide: function () {\n      return showHide(this);\n    },\n    toggle: function (state) {\n      if (typeof state === \"boolean\") {\n        return state ? this.show() : this.hide();\n      }\n      return this.each(function () {\n        if (isHiddenWithinTree(this)) {\n          jQuery(this).show();\n        } else {\n          jQuery(this).hide();\n        }\n      });\n    }\n  });\n  var rcheckableType = /^(?:checkbox|radio)$/i;\n  var rtagName = /<([a-z][^\\/\\0>\\x20\\t\\r\\n\\f]*)/i;\n  var rscriptType = /^$|^module$|\\/(?:java|ecma)script/i;\n  (function () {\n    var fragment = document.createDocumentFragment(),\n      div = fragment.appendChild(document.createElement(\"div\")),\n      input = document.createElement(\"input\");\n\n    // Support: Android 4.0 - 4.3 only\n    // Check state lost if the name is set (trac-11217)\n    // Support: Windows Web Apps (WWA)\n    // `name` and `type` must use .setAttribute for WWA (trac-14901)\n    input.setAttribute(\"type\", \"radio\");\n    input.setAttribute(\"checked\", \"checked\");\n    input.setAttribute(\"name\", \"t\");\n    div.appendChild(input);\n\n    // Support: Android <=4.1 only\n    // Older WebKit doesn't clone checked state correctly in fragments\n    support.checkClone = div.cloneNode(true).cloneNode(true).lastChild.checked;\n\n    // Support: IE <=11 only\n    // Make sure textarea (and checkbox) defaultValue is properly cloned\n    div.innerHTML = \"<textarea>x</textarea>\";\n    support.noCloneChecked = !!div.cloneNode(true).lastChild.defaultValue;\n\n    // Support: IE <=9 only\n    // IE <=9 replaces <option> tags with their contents when inserted outside of\n    // the select element.\n    div.innerHTML = \"<option></option>\";\n    support.option = !!div.lastChild;\n  })();\n\n  // We have to close these tags to support XHTML (trac-13200)\n  var wrapMap = {\n    // XHTML parsers do not magically insert elements in the\n    // same way that tag soup parsers do. So we cannot shorten\n    // this by omitting <tbody> or other required elements.\n    thead: [1, \"<table>\", \"</table>\"],\n    col: [2, \"<table><colgroup>\", \"</colgroup></table>\"],\n    tr: [2, \"<table><tbody>\", \"</tbody></table>\"],\n    td: [3, \"<table><tbody><tr>\", \"</tr></tbody></table>\"],\n    _default: [0, \"\", \"\"]\n  };\n  wrapMap.tbody = wrapMap.tfoot = wrapMap.colgroup = wrapMap.caption = wrapMap.thead;\n  wrapMap.th = wrapMap.td;\n\n  // Support: IE <=9 only\n  if (!support.option) {\n    wrapMap.optgroup = wrapMap.option = [1, \"<select multiple='multiple'>\", \"</select>\"];\n  }\n  function getAll(context, tag) {\n    // Support: IE <=9 - 11 only\n    // Use typeof to avoid zero-argument method invocation on host objects (trac-15151)\n    var ret;\n    if (typeof context.getElementsByTagName !== \"undefined\") {\n      ret = context.getElementsByTagName(tag || \"*\");\n    } else if (typeof context.querySelectorAll !== \"undefined\") {\n      ret = context.querySelectorAll(tag || \"*\");\n    } else {\n      ret = [];\n    }\n    if (tag === undefined || tag && nodeName(context, tag)) {\n      return jQuery.merge([context], ret);\n    }\n    return ret;\n  }\n\n  // Mark scripts as having already been evaluated\n  function setGlobalEval(elems, refElements) {\n    var i = 0,\n      l = elems.length;\n    for (; i < l; i++) {\n      dataPriv.set(elems[i], \"globalEval\", !refElements || dataPriv.get(refElements[i], \"globalEval\"));\n    }\n  }\n  var rhtml = /<|&#?\\w+;/;\n  function buildFragment(elems, context, scripts, selection, ignored) {\n    var elem,\n      tmp,\n      tag,\n      wrap,\n      attached,\n      j,\n      fragment = context.createDocumentFragment(),\n      nodes = [],\n      i = 0,\n      l = elems.length;\n    for (; i < l; i++) {\n      elem = elems[i];\n      if (elem || elem === 0) {\n        // Add nodes directly\n        if (toType(elem) === \"object\") {\n          // Support: Android <=4.0 only, PhantomJS 1 only\n          // push.apply(_, arraylike) throws on ancient WebKit\n          jQuery.merge(nodes, elem.nodeType ? [elem] : elem);\n\n          // Convert non-html into a text node\n        } else if (!rhtml.test(elem)) {\n          nodes.push(context.createTextNode(elem));\n\n          // Convert html into DOM nodes\n        } else {\n          tmp = tmp || fragment.appendChild(context.createElement(\"div\"));\n\n          // Deserialize a standard representation\n          tag = (rtagName.exec(elem) || [\"\", \"\"])[1].toLowerCase();\n          wrap = wrapMap[tag] || wrapMap._default;\n          tmp.innerHTML = wrap[1] + jQuery.htmlPrefilter(elem) + wrap[2];\n\n          // Descend through wrappers to the right content\n          j = wrap[0];\n          while (j--) {\n            tmp = tmp.lastChild;\n          }\n\n          // Support: Android <=4.0 only, PhantomJS 1 only\n          // push.apply(_, arraylike) throws on ancient WebKit\n          jQuery.merge(nodes, tmp.childNodes);\n\n          // Remember the top-level container\n          tmp = fragment.firstChild;\n\n          // Ensure the created nodes are orphaned (trac-12392)\n          tmp.textContent = \"\";\n        }\n      }\n    }\n\n    // Remove wrapper from fragment\n    fragment.textContent = \"\";\n    i = 0;\n    while (elem = nodes[i++]) {\n      // Skip elements already in the context collection (trac-4087)\n      if (selection && jQuery.inArray(elem, selection) > -1) {\n        if (ignored) {\n          ignored.push(elem);\n        }\n        continue;\n      }\n      attached = isAttached(elem);\n\n      // Append to fragment\n      tmp = getAll(fragment.appendChild(elem), \"script\");\n\n      // Preserve script evaluation history\n      if (attached) {\n        setGlobalEval(tmp);\n      }\n\n      // Capture executables\n      if (scripts) {\n        j = 0;\n        while (elem = tmp[j++]) {\n          if (rscriptType.test(elem.type || \"\")) {\n            scripts.push(elem);\n          }\n        }\n      }\n    }\n    return fragment;\n  }\n  var rtypenamespace = /^([^.]*)(?:\\.(.+)|)/;\n  function returnTrue() {\n    return true;\n  }\n  function returnFalse() {\n    return false;\n  }\n  function on(elem, types, selector, data, fn, one) {\n    var origFn, type;\n\n    // Types can be a map of types/handlers\n    if (typeof types === \"object\") {\n      // ( types-Object, selector, data )\n      if (typeof selector !== \"string\") {\n        // ( types-Object, data )\n        data = data || selector;\n        selector = undefined;\n      }\n      for (type in types) {\n        on(elem, type, selector, data, types[type], one);\n      }\n      return elem;\n    }\n    if (data == null && fn == null) {\n      // ( types, fn )\n      fn = selector;\n      data = selector = undefined;\n    } else if (fn == null) {\n      if (typeof selector === \"string\") {\n        // ( types, selector, fn )\n        fn = data;\n        data = undefined;\n      } else {\n        // ( types, data, fn )\n        fn = data;\n        data = selector;\n        selector = undefined;\n      }\n    }\n    if (fn === false) {\n      fn = returnFalse;\n    } else if (!fn) {\n      return elem;\n    }\n    if (one === 1) {\n      origFn = fn;\n      fn = function (event) {\n        // Can use an empty set, since event contains the info\n        jQuery().off(event);\n        return origFn.apply(this, arguments);\n      };\n\n      // Use same guid so caller can remove using origFn\n      fn.guid = origFn.guid || (origFn.guid = jQuery.guid++);\n    }\n    return elem.each(function () {\n      jQuery.event.add(this, types, fn, data, selector);\n    });\n  }\n\n  /*\n   * Helper functions for managing events -- not part of the public interface.\n   * Props to Dean Edwards' addEvent library for many of the ideas.\n   */\n  jQuery.event = {\n    global: {},\n    add: function (elem, types, handler, data, selector) {\n      var handleObjIn,\n        eventHandle,\n        tmp,\n        events,\n        t,\n        handleObj,\n        special,\n        handlers,\n        type,\n        namespaces,\n        origType,\n        elemData = dataPriv.get(elem);\n\n      // Only attach events to objects that accept data\n      if (!acceptData(elem)) {\n        return;\n      }\n\n      // Caller can pass in an object of custom data in lieu of the handler\n      if (handler.handler) {\n        handleObjIn = handler;\n        handler = handleObjIn.handler;\n        selector = handleObjIn.selector;\n      }\n\n      // Ensure that invalid selectors throw exceptions at attach time\n      // Evaluate against documentElement in case elem is a non-element node (e.g., document)\n      if (selector) {\n        jQuery.find.matchesSelector(documentElement, selector);\n      }\n\n      // Make sure that the handler has a unique ID, used to find/remove it later\n      if (!handler.guid) {\n        handler.guid = jQuery.guid++;\n      }\n\n      // Init the element's event structure and main handler, if this is the first\n      if (!(events = elemData.events)) {\n        events = elemData.events = Object.create(null);\n      }\n      if (!(eventHandle = elemData.handle)) {\n        eventHandle = elemData.handle = function (e) {\n          // Discard the second event of a jQuery.event.trigger() and\n          // when an event is called after a page has unloaded\n          return typeof jQuery !== \"undefined\" && jQuery.event.triggered !== e.type ? jQuery.event.dispatch.apply(elem, arguments) : undefined;\n        };\n      }\n\n      // Handle multiple events separated by a space\n      types = (types || \"\").match(rnothtmlwhite) || [\"\"];\n      t = types.length;\n      while (t--) {\n        tmp = rtypenamespace.exec(types[t]) || [];\n        type = origType = tmp[1];\n        namespaces = (tmp[2] || \"\").split(\".\").sort();\n\n        // There *must* be a type, no attaching namespace-only handlers\n        if (!type) {\n          continue;\n        }\n\n        // If event changes its type, use the special event handlers for the changed type\n        special = jQuery.event.special[type] || {};\n\n        // If selector defined, determine special event api type, otherwise given type\n        type = (selector ? special.delegateType : special.bindType) || type;\n\n        // Update special based on newly reset type\n        special = jQuery.event.special[type] || {};\n\n        // handleObj is passed to all event handlers\n        handleObj = jQuery.extend({\n          type: type,\n          origType: origType,\n          data: data,\n          handler: handler,\n          guid: handler.guid,\n          selector: selector,\n          needsContext: selector && jQuery.expr.match.needsContext.test(selector),\n          namespace: namespaces.join(\".\")\n        }, handleObjIn);\n\n        // Init the event handler queue if we're the first\n        if (!(handlers = events[type])) {\n          handlers = events[type] = [];\n          handlers.delegateCount = 0;\n\n          // Only use addEventListener if the special events handler returns false\n          if (!special.setup || special.setup.call(elem, data, namespaces, eventHandle) === false) {\n            if (elem.addEventListener) {\n              elem.addEventListener(type, eventHandle);\n            }\n          }\n        }\n        if (special.add) {\n          special.add.call(elem, handleObj);\n          if (!handleObj.handler.guid) {\n            handleObj.handler.guid = handler.guid;\n          }\n        }\n\n        // Add to the element's handler list, delegates in front\n        if (selector) {\n          handlers.splice(handlers.delegateCount++, 0, handleObj);\n        } else {\n          handlers.push(handleObj);\n        }\n\n        // Keep track of which events have ever been used, for event optimization\n        jQuery.event.global[type] = true;\n      }\n    },\n    // Detach an event or set of events from an element\n    remove: function (elem, types, handler, selector, mappedTypes) {\n      var j,\n        origCount,\n        tmp,\n        events,\n        t,\n        handleObj,\n        special,\n        handlers,\n        type,\n        namespaces,\n        origType,\n        elemData = dataPriv.hasData(elem) && dataPriv.get(elem);\n      if (!elemData || !(events = elemData.events)) {\n        return;\n      }\n\n      // Once for each type.namespace in types; type may be omitted\n      types = (types || \"\").match(rnothtmlwhite) || [\"\"];\n      t = types.length;\n      while (t--) {\n        tmp = rtypenamespace.exec(types[t]) || [];\n        type = origType = tmp[1];\n        namespaces = (tmp[2] || \"\").split(\".\").sort();\n\n        // Unbind all events (on this namespace, if provided) for the element\n        if (!type) {\n          for (type in events) {\n            jQuery.event.remove(elem, type + types[t], handler, selector, true);\n          }\n          continue;\n        }\n        special = jQuery.event.special[type] || {};\n        type = (selector ? special.delegateType : special.bindType) || type;\n        handlers = events[type] || [];\n        tmp = tmp[2] && new RegExp(\"(^|\\\\.)\" + namespaces.join(\"\\\\.(?:.*\\\\.|)\") + \"(\\\\.|$)\");\n\n        // Remove matching events\n        origCount = j = handlers.length;\n        while (j--) {\n          handleObj = handlers[j];\n          if ((mappedTypes || origType === handleObj.origType) && (!handler || handler.guid === handleObj.guid) && (!tmp || tmp.test(handleObj.namespace)) && (!selector || selector === handleObj.selector || selector === \"**\" && handleObj.selector)) {\n            handlers.splice(j, 1);\n            if (handleObj.selector) {\n              handlers.delegateCount--;\n            }\n            if (special.remove) {\n              special.remove.call(elem, handleObj);\n            }\n          }\n        }\n\n        // Remove generic event handler if we removed something and no more handlers exist\n        // (avoids potential for endless recursion during removal of special event handlers)\n        if (origCount && !handlers.length) {\n          if (!special.teardown || special.teardown.call(elem, namespaces, elemData.handle) === false) {\n            jQuery.removeEvent(elem, type, elemData.handle);\n          }\n          delete events[type];\n        }\n      }\n\n      // Remove data and the expando if it's no longer used\n      if (jQuery.isEmptyObject(events)) {\n        dataPriv.remove(elem, \"handle events\");\n      }\n    },\n    dispatch: function (nativeEvent) {\n      var i,\n        j,\n        ret,\n        matched,\n        handleObj,\n        handlerQueue,\n        args = new Array(arguments.length),\n        // Make a writable jQuery.Event from the native event object\n        event = jQuery.event.fix(nativeEvent),\n        handlers = (dataPriv.get(this, \"events\") || Object.create(null))[event.type] || [],\n        special = jQuery.event.special[event.type] || {};\n\n      // Use the fix-ed jQuery.Event rather than the (read-only) native event\n      args[0] = event;\n      for (i = 1; i < arguments.length; i++) {\n        args[i] = arguments[i];\n      }\n      event.delegateTarget = this;\n\n      // Call the preDispatch hook for the mapped type, and let it bail if desired\n      if (special.preDispatch && special.preDispatch.call(this, event) === false) {\n        return;\n      }\n\n      // Determine handlers\n      handlerQueue = jQuery.event.handlers.call(this, event, handlers);\n\n      // Run delegates first; they may want to stop propagation beneath us\n      i = 0;\n      while ((matched = handlerQueue[i++]) && !event.isPropagationStopped()) {\n        event.currentTarget = matched.elem;\n        j = 0;\n        while ((handleObj = matched.handlers[j++]) && !event.isImmediatePropagationStopped()) {\n          // If the event is namespaced, then each handler is only invoked if it is\n          // specially universal or its namespaces are a superset of the event's.\n          if (!event.rnamespace || handleObj.namespace === false || event.rnamespace.test(handleObj.namespace)) {\n            event.handleObj = handleObj;\n            event.data = handleObj.data;\n            ret = ((jQuery.event.special[handleObj.origType] || {}).handle || handleObj.handler).apply(matched.elem, args);\n            if (ret !== undefined) {\n              if ((event.result = ret) === false) {\n                event.preventDefault();\n                event.stopPropagation();\n              }\n            }\n          }\n        }\n      }\n\n      // Call the postDispatch hook for the mapped type\n      if (special.postDispatch) {\n        special.postDispatch.call(this, event);\n      }\n      return event.result;\n    },\n    handlers: function (event, handlers) {\n      var i,\n        handleObj,\n        sel,\n        matchedHandlers,\n        matchedSelectors,\n        handlerQueue = [],\n        delegateCount = handlers.delegateCount,\n        cur = event.target;\n\n      // Find delegate handlers\n      if (delegateCount &&\n      // Support: IE <=9\n      // Black-hole SVG <use> instance trees (trac-13180)\n      cur.nodeType &&\n      // Support: Firefox <=42\n      // Suppress spec-violating clicks indicating a non-primary pointer button (trac-3861)\n      // https://www.w3.org/TR/DOM-Level-3-Events/#event-type-click\n      // Support: IE 11 only\n      // ...but not arrow key \"clicks\" of radio inputs, which can have `button` -1 (gh-2343)\n      !(event.type === \"click\" && event.button >= 1)) {\n        for (; cur !== this; cur = cur.parentNode || this) {\n          // Don't check non-elements (trac-13208)\n          // Don't process clicks on disabled elements (trac-6911, trac-8165, trac-11382, trac-11764)\n          if (cur.nodeType === 1 && !(event.type === \"click\" && cur.disabled === true)) {\n            matchedHandlers = [];\n            matchedSelectors = {};\n            for (i = 0; i < delegateCount; i++) {\n              handleObj = handlers[i];\n\n              // Don't conflict with Object.prototype properties (trac-13203)\n              sel = handleObj.selector + \" \";\n              if (matchedSelectors[sel] === undefined) {\n                matchedSelectors[sel] = handleObj.needsContext ? jQuery(sel, this).index(cur) > -1 : jQuery.find(sel, this, null, [cur]).length;\n              }\n              if (matchedSelectors[sel]) {\n                matchedHandlers.push(handleObj);\n              }\n            }\n            if (matchedHandlers.length) {\n              handlerQueue.push({\n                elem: cur,\n                handlers: matchedHandlers\n              });\n            }\n          }\n        }\n      }\n\n      // Add the remaining (directly-bound) handlers\n      cur = this;\n      if (delegateCount < handlers.length) {\n        handlerQueue.push({\n          elem: cur,\n          handlers: handlers.slice(delegateCount)\n        });\n      }\n      return handlerQueue;\n    },\n    addProp: function (name, hook) {\n      Object.defineProperty(jQuery.Event.prototype, name, {\n        enumerable: true,\n        configurable: true,\n        get: isFunction(hook) ? function () {\n          if (this.originalEvent) {\n            return hook(this.originalEvent);\n          }\n        } : function () {\n          if (this.originalEvent) {\n            return this.originalEvent[name];\n          }\n        },\n        set: function (value) {\n          Object.defineProperty(this, name, {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: value\n          });\n        }\n      });\n    },\n    fix: function (originalEvent) {\n      return originalEvent[jQuery.expando] ? originalEvent : new jQuery.Event(originalEvent);\n    },\n    special: {\n      load: {\n        // Prevent triggered image.load events from bubbling to window.load\n        noBubble: true\n      },\n      click: {\n        // Utilize native event to ensure correct state for checkable inputs\n        setup: function (data) {\n          // For mutual compressibility with _default, replace `this` access with a local var.\n          // `|| data` is dead code meant only to preserve the variable through minification.\n          var el = this || data;\n\n          // Claim the first handler\n          if (rcheckableType.test(el.type) && el.click && nodeName(el, \"input\")) {\n            // dataPriv.set( el, \"click\", ... )\n            leverageNative(el, \"click\", true);\n          }\n\n          // Return false to allow normal processing in the caller\n          return false;\n        },\n        trigger: function (data) {\n          // For mutual compressibility with _default, replace `this` access with a local var.\n          // `|| data` is dead code meant only to preserve the variable through minification.\n          var el = this || data;\n\n          // Force setup before triggering a click\n          if (rcheckableType.test(el.type) && el.click && nodeName(el, \"input\")) {\n            leverageNative(el, \"click\");\n          }\n\n          // Return non-false to allow normal event-path propagation\n          return true;\n        },\n        // For cross-browser consistency, suppress native .click() on links\n        // Also prevent it if we're currently inside a leveraged native-event stack\n        _default: function (event) {\n          var target = event.target;\n          return rcheckableType.test(target.type) && target.click && nodeName(target, \"input\") && dataPriv.get(target, \"click\") || nodeName(target, \"a\");\n        }\n      },\n      beforeunload: {\n        postDispatch: function (event) {\n          // Support: Firefox 20+\n          // Firefox doesn't alert if the returnValue field is not set.\n          if (event.result !== undefined && event.originalEvent) {\n            event.originalEvent.returnValue = event.result;\n          }\n        }\n      }\n    }\n  };\n\n  // Ensure the presence of an event listener that handles manually-triggered\n  // synthetic events by interrupting progress until reinvoked in response to\n  // *native* events that it fires directly, ensuring that state changes have\n  // already occurred before other listeners are invoked.\n  function leverageNative(el, type, isSetup) {\n    // Missing `isSetup` indicates a trigger call, which must force setup through jQuery.event.add\n    if (!isSetup) {\n      if (dataPriv.get(el, type) === undefined) {\n        jQuery.event.add(el, type, returnTrue);\n      }\n      return;\n    }\n\n    // Register the controller as a special universal handler for all event namespaces\n    dataPriv.set(el, type, false);\n    jQuery.event.add(el, type, {\n      namespace: false,\n      handler: function (event) {\n        var result,\n          saved = dataPriv.get(this, type);\n        if (event.isTrigger & 1 && this[type]) {\n          // Interrupt processing of the outer synthetic .trigger()ed event\n          if (!saved) {\n            // Store arguments for use when handling the inner native event\n            // There will always be at least one argument (an event object), so this array\n            // will not be confused with a leftover capture object.\n            saved = slice.call(arguments);\n            dataPriv.set(this, type, saved);\n\n            // Trigger the native event and capture its result\n            this[type]();\n            result = dataPriv.get(this, type);\n            dataPriv.set(this, type, false);\n            if (saved !== result) {\n              // Cancel the outer synthetic event\n              event.stopImmediatePropagation();\n              event.preventDefault();\n              return result;\n            }\n\n            // If this is an inner synthetic event for an event with a bubbling surrogate\n            // (focus or blur), assume that the surrogate already propagated from triggering\n            // the native event and prevent that from happening again here.\n            // This technically gets the ordering wrong w.r.t. to `.trigger()` (in which the\n            // bubbling surrogate propagates *after* the non-bubbling base), but that seems\n            // less bad than duplication.\n          } else if ((jQuery.event.special[type] || {}).delegateType) {\n            event.stopPropagation();\n          }\n\n          // If this is a native event triggered above, everything is now in order\n          // Fire an inner synthetic event with the original arguments\n        } else if (saved) {\n          // ...and capture the result\n          dataPriv.set(this, type, jQuery.event.trigger(saved[0], saved.slice(1), this));\n\n          // Abort handling of the native event by all jQuery handlers while allowing\n          // native handlers on the same element to run. On target, this is achieved\n          // by stopping immediate propagation just on the jQuery event. However,\n          // the native event is re-wrapped by a jQuery one on each level of the\n          // propagation so the only way to stop it for jQuery is to stop it for\n          // everyone via native `stopPropagation()`. This is not a problem for\n          // focus/blur which don't bubble, but it does also stop click on checkboxes\n          // and radios. We accept this limitation.\n          event.stopPropagation();\n          event.isImmediatePropagationStopped = returnTrue;\n        }\n      }\n    });\n  }\n  jQuery.removeEvent = function (elem, type, handle) {\n    // This \"if\" is needed for plain objects\n    if (elem.removeEventListener) {\n      elem.removeEventListener(type, handle);\n    }\n  };\n  jQuery.Event = function (src, props) {\n    // Allow instantiation without the 'new' keyword\n    if (!(this instanceof jQuery.Event)) {\n      return new jQuery.Event(src, props);\n    }\n\n    // Event object\n    if (src && src.type) {\n      this.originalEvent = src;\n      this.type = src.type;\n\n      // Events bubbling up the document may have been marked as prevented\n      // by a handler lower down the tree; reflect the correct value.\n      this.isDefaultPrevented = src.defaultPrevented || src.defaultPrevented === undefined &&\n      // Support: Android <=2.3 only\n      src.returnValue === false ? returnTrue : returnFalse;\n\n      // Create target properties\n      // Support: Safari <=6 - 7 only\n      // Target should not be a text node (trac-504, trac-13143)\n      this.target = src.target && src.target.nodeType === 3 ? src.target.parentNode : src.target;\n      this.currentTarget = src.currentTarget;\n      this.relatedTarget = src.relatedTarget;\n\n      // Event type\n    } else {\n      this.type = src;\n    }\n\n    // Put explicitly provided properties onto the event object\n    if (props) {\n      jQuery.extend(this, props);\n    }\n\n    // Create a timestamp if incoming event doesn't have one\n    this.timeStamp = src && src.timeStamp || Date.now();\n\n    // Mark it as fixed\n    this[jQuery.expando] = true;\n  };\n\n  // jQuery.Event is based on DOM3 Events as specified by the ECMAScript Language Binding\n  // https://www.w3.org/TR/2003/WD-DOM-Level-3-Events-20030331/ecma-script-binding.html\n  jQuery.Event.prototype = {\n    constructor: jQuery.Event,\n    isDefaultPrevented: returnFalse,\n    isPropagationStopped: returnFalse,\n    isImmediatePropagationStopped: returnFalse,\n    isSimulated: false,\n    preventDefault: function () {\n      var e = this.originalEvent;\n      this.isDefaultPrevented = returnTrue;\n      if (e && !this.isSimulated) {\n        e.preventDefault();\n      }\n    },\n    stopPropagation: function () {\n      var e = this.originalEvent;\n      this.isPropagationStopped = returnTrue;\n      if (e && !this.isSimulated) {\n        e.stopPropagation();\n      }\n    },\n    stopImmediatePropagation: function () {\n      var e = this.originalEvent;\n      this.isImmediatePropagationStopped = returnTrue;\n      if (e && !this.isSimulated) {\n        e.stopImmediatePropagation();\n      }\n      this.stopPropagation();\n    }\n  };\n\n  // Includes all common event props including KeyEvent and MouseEvent specific props\n  jQuery.each({\n    altKey: true,\n    bubbles: true,\n    cancelable: true,\n    changedTouches: true,\n    ctrlKey: true,\n    detail: true,\n    eventPhase: true,\n    metaKey: true,\n    pageX: true,\n    pageY: true,\n    shiftKey: true,\n    view: true,\n    \"char\": true,\n    code: true,\n    charCode: true,\n    key: true,\n    keyCode: true,\n    button: true,\n    buttons: true,\n    clientX: true,\n    clientY: true,\n    offsetX: true,\n    offsetY: true,\n    pointerId: true,\n    pointerType: true,\n    screenX: true,\n    screenY: true,\n    targetTouches: true,\n    toElement: true,\n    touches: true,\n    which: true\n  }, jQuery.event.addProp);\n  jQuery.each({\n    focus: \"focusin\",\n    blur: \"focusout\"\n  }, function (type, delegateType) {\n    function focusMappedHandler(nativeEvent) {\n      if (document.documentMode) {\n        // Support: IE 11+\n        // Attach a single focusin/focusout handler on the document while someone wants\n        // focus/blur. This is because the former are synchronous in IE while the latter\n        // are async. In other browsers, all those handlers are invoked synchronously.\n\n        // `handle` from private data would already wrap the event, but we need\n        // to change the `type` here.\n        var handle = dataPriv.get(this, \"handle\"),\n          event = jQuery.event.fix(nativeEvent);\n        event.type = nativeEvent.type === \"focusin\" ? \"focus\" : \"blur\";\n        event.isSimulated = true;\n\n        // First, handle focusin/focusout\n        handle(nativeEvent);\n\n        // ...then, handle focus/blur\n        //\n        // focus/blur don't bubble while focusin/focusout do; simulate the former by only\n        // invoking the handler at the lower level.\n        if (event.target === event.currentTarget) {\n          // The setup part calls `leverageNative`, which, in turn, calls\n          // `jQuery.event.add`, so event handle will already have been set\n          // by this point.\n          handle(event);\n        }\n      } else {\n        // For non-IE browsers, attach a single capturing handler on the document\n        // while someone wants focusin/focusout.\n        jQuery.event.simulate(delegateType, nativeEvent.target, jQuery.event.fix(nativeEvent));\n      }\n    }\n    jQuery.event.special[type] = {\n      // Utilize native event if possible so blur/focus sequence is correct\n      setup: function () {\n        var attaches;\n\n        // Claim the first handler\n        // dataPriv.set( this, \"focus\", ... )\n        // dataPriv.set( this, \"blur\", ... )\n        leverageNative(this, type, true);\n        if (document.documentMode) {\n          // Support: IE 9 - 11+\n          // We use the same native handler for focusin & focus (and focusout & blur)\n          // so we need to coordinate setup & teardown parts between those events.\n          // Use `delegateType` as the key as `type` is already used by `leverageNative`.\n          attaches = dataPriv.get(this, delegateType);\n          if (!attaches) {\n            this.addEventListener(delegateType, focusMappedHandler);\n          }\n          dataPriv.set(this, delegateType, (attaches || 0) + 1);\n        } else {\n          // Return false to allow normal processing in the caller\n          return false;\n        }\n      },\n      trigger: function () {\n        // Force setup before trigger\n        leverageNative(this, type);\n\n        // Return non-false to allow normal event-path propagation\n        return true;\n      },\n      teardown: function () {\n        var attaches;\n        if (document.documentMode) {\n          attaches = dataPriv.get(this, delegateType) - 1;\n          if (!attaches) {\n            this.removeEventListener(delegateType, focusMappedHandler);\n            dataPriv.remove(this, delegateType);\n          } else {\n            dataPriv.set(this, delegateType, attaches);\n          }\n        } else {\n          // Return false to indicate standard teardown should be applied\n          return false;\n        }\n      },\n      // Suppress native focus or blur if we're currently inside\n      // a leveraged native-event stack\n      _default: function (event) {\n        return dataPriv.get(event.target, type);\n      },\n      delegateType: delegateType\n    };\n\n    // Support: Firefox <=44\n    // Firefox doesn't have focus(in | out) events\n    // Related ticket - https://bugzilla.mozilla.org/show_bug.cgi?id=687787\n    //\n    // Support: Chrome <=48 - 49, Safari <=9.0 - 9.1\n    // focus(in | out) events fire after focus & blur events,\n    // which is spec violation - http://www.w3.org/TR/DOM-Level-3-Events/#events-focusevent-event-order\n    // Related ticket - https://bugs.chromium.org/p/chromium/issues/detail?id=449857\n    //\n    // Support: IE 9 - 11+\n    // To preserve relative focusin/focus & focusout/blur event order guaranteed on the 3.x branch,\n    // attach a single handler for both events in IE.\n    jQuery.event.special[delegateType] = {\n      setup: function () {\n        // Handle: regular nodes (via `this.ownerDocument`), window\n        // (via `this.document`) & document (via `this`).\n        var doc = this.ownerDocument || this.document || this,\n          dataHolder = document.documentMode ? this : doc,\n          attaches = dataPriv.get(dataHolder, delegateType);\n\n        // Support: IE 9 - 11+\n        // We use the same native handler for focusin & focus (and focusout & blur)\n        // so we need to coordinate setup & teardown parts between those events.\n        // Use `delegateType` as the key as `type` is already used by `leverageNative`.\n        if (!attaches) {\n          if (document.documentMode) {\n            this.addEventListener(delegateType, focusMappedHandler);\n          } else {\n            doc.addEventListener(type, focusMappedHandler, true);\n          }\n        }\n        dataPriv.set(dataHolder, delegateType, (attaches || 0) + 1);\n      },\n      teardown: function () {\n        var doc = this.ownerDocument || this.document || this,\n          dataHolder = document.documentMode ? this : doc,\n          attaches = dataPriv.get(dataHolder, delegateType) - 1;\n        if (!attaches) {\n          if (document.documentMode) {\n            this.removeEventListener(delegateType, focusMappedHandler);\n          } else {\n            doc.removeEventListener(type, focusMappedHandler, true);\n          }\n          dataPriv.remove(dataHolder, delegateType);\n        } else {\n          dataPriv.set(dataHolder, delegateType, attaches);\n        }\n      }\n    };\n  });\n\n  // Create mouseenter/leave events using mouseover/out and event-time checks\n  // so that event delegation works in jQuery.\n  // Do the same for pointerenter/pointerleave and pointerover/pointerout\n  //\n  // Support: Safari 7 only\n  // Safari sends mouseenter too often; see:\n  // https://bugs.chromium.org/p/chromium/issues/detail?id=470258\n  // for the description of the bug (it existed in older Chrome versions as well).\n  jQuery.each({\n    mouseenter: \"mouseover\",\n    mouseleave: \"mouseout\",\n    pointerenter: \"pointerover\",\n    pointerleave: \"pointerout\"\n  }, function (orig, fix) {\n    jQuery.event.special[orig] = {\n      delegateType: fix,\n      bindType: fix,\n      handle: function (event) {\n        var ret,\n          target = this,\n          related = event.relatedTarget,\n          handleObj = event.handleObj;\n\n        // For mouseenter/leave call the handler if related is outside the target.\n        // NB: No relatedTarget if the mouse left/entered the browser window\n        if (!related || related !== target && !jQuery.contains(target, related)) {\n          event.type = handleObj.origType;\n          ret = handleObj.handler.apply(this, arguments);\n          event.type = fix;\n        }\n        return ret;\n      }\n    };\n  });\n  jQuery.fn.extend({\n    on: function (types, selector, data, fn) {\n      return on(this, types, selector, data, fn);\n    },\n    one: function (types, selector, data, fn) {\n      return on(this, types, selector, data, fn, 1);\n    },\n    off: function (types, selector, fn) {\n      var handleObj, type;\n      if (types && types.preventDefault && types.handleObj) {\n        // ( event )  dispatched jQuery.Event\n        handleObj = types.handleObj;\n        jQuery(types.delegateTarget).off(handleObj.namespace ? handleObj.origType + \".\" + handleObj.namespace : handleObj.origType, handleObj.selector, handleObj.handler);\n        return this;\n      }\n      if (typeof types === \"object\") {\n        // ( types-object [, selector] )\n        for (type in types) {\n          this.off(type, selector, types[type]);\n        }\n        return this;\n      }\n      if (selector === false || typeof selector === \"function\") {\n        // ( types [, fn] )\n        fn = selector;\n        selector = undefined;\n      }\n      if (fn === false) {\n        fn = returnFalse;\n      }\n      return this.each(function () {\n        jQuery.event.remove(this, types, fn, selector);\n      });\n    }\n  });\n  var\n    // Support: IE <=10 - 11, Edge 12 - 13 only\n    // In IE/Edge using regex groups here causes severe slowdowns.\n    // See https://connect.microsoft.com/IE/feedback/details/1736512/\n    rnoInnerhtml = /<script|<style|<link/i,\n    // checked=\"checked\" or checked\n    rchecked = /checked\\s*(?:[^=]|=\\s*.checked.)/i,\n    rcleanScript = /^\\s*<!\\[CDATA\\[|\\]\\]>\\s*$/g;\n\n  // Prefer a tbody over its parent table for containing new rows\n  function manipulationTarget(elem, content) {\n    if (nodeName(elem, \"table\") && nodeName(content.nodeType !== 11 ? content : content.firstChild, \"tr\")) {\n      return jQuery(elem).children(\"tbody\")[0] || elem;\n    }\n    return elem;\n  }\n\n  // Replace/restore the type attribute of script elements for safe DOM manipulation\n  function disableScript(elem) {\n    elem.type = (elem.getAttribute(\"type\") !== null) + \"/\" + elem.type;\n    return elem;\n  }\n  function restoreScript(elem) {\n    if ((elem.type || \"\").slice(0, 5) === \"true/\") {\n      elem.type = elem.type.slice(5);\n    } else {\n      elem.removeAttribute(\"type\");\n    }\n    return elem;\n  }\n  function cloneCopyEvent(src, dest) {\n    var i, l, type, pdataOld, udataOld, udataCur, events;\n    if (dest.nodeType !== 1) {\n      return;\n    }\n\n    // 1. Copy private data: events, handlers, etc.\n    if (dataPriv.hasData(src)) {\n      pdataOld = dataPriv.get(src);\n      events = pdataOld.events;\n      if (events) {\n        dataPriv.remove(dest, \"handle events\");\n        for (type in events) {\n          for (i = 0, l = events[type].length; i < l; i++) {\n            jQuery.event.add(dest, type, events[type][i]);\n          }\n        }\n      }\n    }\n\n    // 2. Copy user data\n    if (dataUser.hasData(src)) {\n      udataOld = dataUser.access(src);\n      udataCur = jQuery.extend({}, udataOld);\n      dataUser.set(dest, udataCur);\n    }\n  }\n\n  // Fix IE bugs, see support tests\n  function fixInput(src, dest) {\n    var nodeName = dest.nodeName.toLowerCase();\n\n    // Fails to persist the checked state of a cloned checkbox or radio button.\n    if (nodeName === \"input\" && rcheckableType.test(src.type)) {\n      dest.checked = src.checked;\n\n      // Fails to return the selected option to the default selected state when cloning options\n    } else if (nodeName === \"input\" || nodeName === \"textarea\") {\n      dest.defaultValue = src.defaultValue;\n    }\n  }\n  function domManip(collection, args, callback, ignored) {\n    // Flatten any nested arrays\n    args = flat(args);\n    var fragment,\n      first,\n      scripts,\n      hasScripts,\n      node,\n      doc,\n      i = 0,\n      l = collection.length,\n      iNoClone = l - 1,\n      value = args[0],\n      valueIsFunction = isFunction(value);\n\n    // We can't cloneNode fragments that contain checked, in WebKit\n    if (valueIsFunction || l > 1 && typeof value === \"string\" && !support.checkClone && rchecked.test(value)) {\n      return collection.each(function (index) {\n        var self = collection.eq(index);\n        if (valueIsFunction) {\n          args[0] = value.call(this, index, self.html());\n        }\n        domManip(self, args, callback, ignored);\n      });\n    }\n    if (l) {\n      fragment = buildFragment(args, collection[0].ownerDocument, false, collection, ignored);\n      first = fragment.firstChild;\n      if (fragment.childNodes.length === 1) {\n        fragment = first;\n      }\n\n      // Require either new content or an interest in ignored elements to invoke the callback\n      if (first || ignored) {\n        scripts = jQuery.map(getAll(fragment, \"script\"), disableScript);\n        hasScripts = scripts.length;\n\n        // Use the original fragment for the last item\n        // instead of the first because it can end up\n        // being emptied incorrectly in certain situations (trac-8070).\n        for (; i < l; i++) {\n          node = fragment;\n          if (i !== iNoClone) {\n            node = jQuery.clone(node, true, true);\n\n            // Keep references to cloned scripts for later restoration\n            if (hasScripts) {\n              // Support: Android <=4.0 only, PhantomJS 1 only\n              // push.apply(_, arraylike) throws on ancient WebKit\n              jQuery.merge(scripts, getAll(node, \"script\"));\n            }\n          }\n          callback.call(collection[i], node, i);\n        }\n        if (hasScripts) {\n          doc = scripts[scripts.length - 1].ownerDocument;\n\n          // Re-enable scripts\n          jQuery.map(scripts, restoreScript);\n\n          // Evaluate executable scripts on first document insertion\n          for (i = 0; i < hasScripts; i++) {\n            node = scripts[i];\n            if (rscriptType.test(node.type || \"\") && !dataPriv.access(node, \"globalEval\") && jQuery.contains(doc, node)) {\n              if (node.src && (node.type || \"\").toLowerCase() !== \"module\") {\n                // Optional AJAX dependency, but won't run scripts if not present\n                if (jQuery._evalUrl && !node.noModule) {\n                  jQuery._evalUrl(node.src, {\n                    nonce: node.nonce || node.getAttribute(\"nonce\")\n                  }, doc);\n                }\n              } else {\n                // Unwrap a CDATA section containing script contents. This shouldn't be\n                // needed as in XML documents they're already not visible when\n                // inspecting element contents and in HTML documents they have no\n                // meaning but we're preserving that logic for backwards compatibility.\n                // This will be removed completely in 4.0. See gh-4904.\n                DOMEval(node.textContent.replace(rcleanScript, \"\"), node, doc);\n              }\n            }\n          }\n        }\n      }\n    }\n    return collection;\n  }\n  function remove(elem, selector, keepData) {\n    var node,\n      nodes = selector ? jQuery.filter(selector, elem) : elem,\n      i = 0;\n    for (; (node = nodes[i]) != null; i++) {\n      if (!keepData && node.nodeType === 1) {\n        jQuery.cleanData(getAll(node));\n      }\n      if (node.parentNode) {\n        if (keepData && isAttached(node)) {\n          setGlobalEval(getAll(node, \"script\"));\n        }\n        node.parentNode.removeChild(node);\n      }\n    }\n    return elem;\n  }\n  jQuery.extend({\n    htmlPrefilter: function (html) {\n      return html;\n    },\n    clone: function (elem, dataAndEvents, deepDataAndEvents) {\n      var i,\n        l,\n        srcElements,\n        destElements,\n        clone = elem.cloneNode(true),\n        inPage = isAttached(elem);\n\n      // Fix IE cloning issues\n      if (!support.noCloneChecked && (elem.nodeType === 1 || elem.nodeType === 11) && !jQuery.isXMLDoc(elem)) {\n        // We eschew jQuery#find here for performance reasons:\n        // https://jsperf.com/getall-vs-sizzle/2\n        destElements = getAll(clone);\n        srcElements = getAll(elem);\n        for (i = 0, l = srcElements.length; i < l; i++) {\n          fixInput(srcElements[i], destElements[i]);\n        }\n      }\n\n      // Copy the events from the original to the clone\n      if (dataAndEvents) {\n        if (deepDataAndEvents) {\n          srcElements = srcElements || getAll(elem);\n          destElements = destElements || getAll(clone);\n          for (i = 0, l = srcElements.length; i < l; i++) {\n            cloneCopyEvent(srcElements[i], destElements[i]);\n          }\n        } else {\n          cloneCopyEvent(elem, clone);\n        }\n      }\n\n      // Preserve script evaluation history\n      destElements = getAll(clone, \"script\");\n      if (destElements.length > 0) {\n        setGlobalEval(destElements, !inPage && getAll(elem, \"script\"));\n      }\n\n      // Return the cloned set\n      return clone;\n    },\n    cleanData: function (elems) {\n      var data,\n        elem,\n        type,\n        special = jQuery.event.special,\n        i = 0;\n      for (; (elem = elems[i]) !== undefined; i++) {\n        if (acceptData(elem)) {\n          if (data = elem[dataPriv.expando]) {\n            if (data.events) {\n              for (type in data.events) {\n                if (special[type]) {\n                  jQuery.event.remove(elem, type);\n\n                  // This is a shortcut to avoid jQuery.event.remove's overhead\n                } else {\n                  jQuery.removeEvent(elem, type, data.handle);\n                }\n              }\n            }\n\n            // Support: Chrome <=35 - 45+\n            // Assign undefined instead of using delete, see Data#remove\n            elem[dataPriv.expando] = undefined;\n          }\n          if (elem[dataUser.expando]) {\n            // Support: Chrome <=35 - 45+\n            // Assign undefined instead of using delete, see Data#remove\n            elem[dataUser.expando] = undefined;\n          }\n        }\n      }\n    }\n  });\n  jQuery.fn.extend({\n    detach: function (selector) {\n      return remove(this, selector, true);\n    },\n    remove: function (selector) {\n      return remove(this, selector);\n    },\n    text: function (value) {\n      return access(this, function (value) {\n        return value === undefined ? jQuery.text(this) : this.empty().each(function () {\n          if (this.nodeType === 1 || this.nodeType === 11 || this.nodeType === 9) {\n            this.textContent = value;\n          }\n        });\n      }, null, value, arguments.length);\n    },\n    append: function () {\n      return domManip(this, arguments, function (elem) {\n        if (this.nodeType === 1 || this.nodeType === 11 || this.nodeType === 9) {\n          var target = manipulationTarget(this, elem);\n          target.appendChild(elem);\n        }\n      });\n    },\n    prepend: function () {\n      return domManip(this, arguments, function (elem) {\n        if (this.nodeType === 1 || this.nodeType === 11 || this.nodeType === 9) {\n          var target = manipulationTarget(this, elem);\n          target.insertBefore(elem, target.firstChild);\n        }\n      });\n    },\n    before: function () {\n      return domManip(this, arguments, function (elem) {\n        if (this.parentNode) {\n          this.parentNode.insertBefore(elem, this);\n        }\n      });\n    },\n    after: function () {\n      return domManip(this, arguments, function (elem) {\n        if (this.parentNode) {\n          this.parentNode.insertBefore(elem, this.nextSibling);\n        }\n      });\n    },\n    empty: function () {\n      var elem,\n        i = 0;\n      for (; (elem = this[i]) != null; i++) {\n        if (elem.nodeType === 1) {\n          // Prevent memory leaks\n          jQuery.cleanData(getAll(elem, false));\n\n          // Remove any remaining nodes\n          elem.textContent = \"\";\n        }\n      }\n      return this;\n    },\n    clone: function (dataAndEvents, deepDataAndEvents) {\n      dataAndEvents = dataAndEvents == null ? false : dataAndEvents;\n      deepDataAndEvents = deepDataAndEvents == null ? dataAndEvents : deepDataAndEvents;\n      return this.map(function () {\n        return jQuery.clone(this, dataAndEvents, deepDataAndEvents);\n      });\n    },\n    html: function (value) {\n      return access(this, function (value) {\n        var elem = this[0] || {},\n          i = 0,\n          l = this.length;\n        if (value === undefined && elem.nodeType === 1) {\n          return elem.innerHTML;\n        }\n\n        // See if we can take a shortcut and just use innerHTML\n        if (typeof value === \"string\" && !rnoInnerhtml.test(value) && !wrapMap[(rtagName.exec(value) || [\"\", \"\"])[1].toLowerCase()]) {\n          value = jQuery.htmlPrefilter(value);\n          try {\n            for (; i < l; i++) {\n              elem = this[i] || {};\n\n              // Remove element nodes and prevent memory leaks\n              if (elem.nodeType === 1) {\n                jQuery.cleanData(getAll(elem, false));\n                elem.innerHTML = value;\n              }\n            }\n            elem = 0;\n\n            // If using innerHTML throws an exception, use the fallback method\n          } catch (e) {}\n        }\n        if (elem) {\n          this.empty().append(value);\n        }\n      }, null, value, arguments.length);\n    },\n    replaceWith: function () {\n      var ignored = [];\n\n      // Make the changes, replacing each non-ignored context element with the new content\n      return domManip(this, arguments, function (elem) {\n        var parent = this.parentNode;\n        if (jQuery.inArray(this, ignored) < 0) {\n          jQuery.cleanData(getAll(this));\n          if (parent) {\n            parent.replaceChild(elem, this);\n          }\n        }\n\n        // Force callback invocation\n      }, ignored);\n    }\n  });\n  jQuery.each({\n    appendTo: \"append\",\n    prependTo: \"prepend\",\n    insertBefore: \"before\",\n    insertAfter: \"after\",\n    replaceAll: \"replaceWith\"\n  }, function (name, original) {\n    jQuery.fn[name] = function (selector) {\n      var elems,\n        ret = [],\n        insert = jQuery(selector),\n        last = insert.length - 1,\n        i = 0;\n      for (; i <= last; i++) {\n        elems = i === last ? this : this.clone(true);\n        jQuery(insert[i])[original](elems);\n\n        // Support: Android <=4.0 only, PhantomJS 1 only\n        // .get() because push.apply(_, arraylike) throws on ancient WebKit\n        push.apply(ret, elems.get());\n      }\n      return this.pushStack(ret);\n    };\n  });\n  var rnumnonpx = new RegExp(\"^(\" + pnum + \")(?!px)[a-z%]+$\", \"i\");\n  var rcustomProp = /^--/;\n  var getStyles = function (elem) {\n    // Support: IE <=11 only, Firefox <=30 (trac-15098, trac-14150)\n    // IE throws on elements created in popups\n    // FF meanwhile throws on frame elements through \"defaultView.getComputedStyle\"\n    var view = elem.ownerDocument.defaultView;\n    if (!view || !view.opener) {\n      view = window;\n    }\n    return view.getComputedStyle(elem);\n  };\n  var swap = function (elem, options, callback) {\n    var ret,\n      name,\n      old = {};\n\n    // Remember the old values, and insert the new ones\n    for (name in options) {\n      old[name] = elem.style[name];\n      elem.style[name] = options[name];\n    }\n    ret = callback.call(elem);\n\n    // Revert the old values\n    for (name in options) {\n      elem.style[name] = old[name];\n    }\n    return ret;\n  };\n  var rboxStyle = new RegExp(cssExpand.join(\"|\"), \"i\");\n  (function () {\n    // Executing both pixelPosition & boxSizingReliable tests require only one layout\n    // so they're executed at the same time to save the second computation.\n    function computeStyleTests() {\n      // This is a singleton, we need to execute it only once\n      if (!div) {\n        return;\n      }\n      container.style.cssText = \"position:absolute;left:-11111px;width:60px;\" + \"margin-top:1px;padding:0;border:0\";\n      div.style.cssText = \"position:relative;display:block;box-sizing:border-box;overflow:scroll;\" + \"margin:auto;border:1px;padding:1px;\" + \"width:60%;top:1%\";\n      documentElement.appendChild(container).appendChild(div);\n      var divStyle = window.getComputedStyle(div);\n      pixelPositionVal = divStyle.top !== \"1%\";\n\n      // Support: Android 4.0 - 4.3 only, Firefox <=3 - 44\n      reliableMarginLeftVal = roundPixelMeasures(divStyle.marginLeft) === 12;\n\n      // Support: Android 4.0 - 4.3 only, Safari <=9.1 - 10.1, iOS <=7.0 - 9.3\n      // Some styles come back with percentage values, even though they shouldn't\n      div.style.right = \"60%\";\n      pixelBoxStylesVal = roundPixelMeasures(divStyle.right) === 36;\n\n      // Support: IE 9 - 11 only\n      // Detect misreporting of content dimensions for box-sizing:border-box elements\n      boxSizingReliableVal = roundPixelMeasures(divStyle.width) === 36;\n\n      // Support: IE 9 only\n      // Detect overflow:scroll screwiness (gh-3699)\n      // Support: Chrome <=64\n      // Don't get tricked when zoom affects offsetWidth (gh-4029)\n      div.style.position = \"absolute\";\n      scrollboxSizeVal = roundPixelMeasures(div.offsetWidth / 3) === 12;\n      documentElement.removeChild(container);\n\n      // Nullify the div so it wouldn't be stored in the memory and\n      // it will also be a sign that checks already performed\n      div = null;\n    }\n    function roundPixelMeasures(measure) {\n      return Math.round(parseFloat(measure));\n    }\n    var pixelPositionVal,\n      boxSizingReliableVal,\n      scrollboxSizeVal,\n      pixelBoxStylesVal,\n      reliableTrDimensionsVal,\n      reliableMarginLeftVal,\n      container = document.createElement(\"div\"),\n      div = document.createElement(\"div\");\n\n    // Finish early in limited (non-browser) environments\n    if (!div.style) {\n      return;\n    }\n\n    // Support: IE <=9 - 11 only\n    // Style of cloned element affects source element cloned (trac-8908)\n    div.style.backgroundClip = \"content-box\";\n    div.cloneNode(true).style.backgroundClip = \"\";\n    support.clearCloneStyle = div.style.backgroundClip === \"content-box\";\n    jQuery.extend(support, {\n      boxSizingReliable: function () {\n        computeStyleTests();\n        return boxSizingReliableVal;\n      },\n      pixelBoxStyles: function () {\n        computeStyleTests();\n        return pixelBoxStylesVal;\n      },\n      pixelPosition: function () {\n        computeStyleTests();\n        return pixelPositionVal;\n      },\n      reliableMarginLeft: function () {\n        computeStyleTests();\n        return reliableMarginLeftVal;\n      },\n      scrollboxSize: function () {\n        computeStyleTests();\n        return scrollboxSizeVal;\n      },\n      // Support: IE 9 - 11+, Edge 15 - 18+\n      // IE/Edge misreport `getComputedStyle` of table rows with width/height\n      // set in CSS while `offset*` properties report correct values.\n      // Behavior in IE 9 is more subtle than in newer versions & it passes\n      // some versions of this test; make sure not to make it pass there!\n      //\n      // Support: Firefox 70+\n      // Only Firefox includes border widths\n      // in computed dimensions. (gh-4529)\n      reliableTrDimensions: function () {\n        var table, tr, trChild, trStyle;\n        if (reliableTrDimensionsVal == null) {\n          table = document.createElement(\"table\");\n          tr = document.createElement(\"tr\");\n          trChild = document.createElement(\"div\");\n          table.style.cssText = \"position:absolute;left:-11111px;border-collapse:separate\";\n          tr.style.cssText = \"box-sizing:content-box;border:1px solid\";\n\n          // Support: Chrome 86+\n          // Height set through cssText does not get applied.\n          // Computed height then comes back as 0.\n          tr.style.height = \"1px\";\n          trChild.style.height = \"9px\";\n\n          // Support: Android 8 Chrome 86+\n          // In our bodyBackground.html iframe,\n          // display for all div elements is set to \"inline\",\n          // which causes a problem only in Android 8 Chrome 86.\n          // Ensuring the div is `display: block`\n          // gets around this issue.\n          trChild.style.display = \"block\";\n          documentElement.appendChild(table).appendChild(tr).appendChild(trChild);\n          trStyle = window.getComputedStyle(tr);\n          reliableTrDimensionsVal = parseInt(trStyle.height, 10) + parseInt(trStyle.borderTopWidth, 10) + parseInt(trStyle.borderBottomWidth, 10) === tr.offsetHeight;\n          documentElement.removeChild(table);\n        }\n        return reliableTrDimensionsVal;\n      }\n    });\n  })();\n  function curCSS(elem, name, computed) {\n    var width,\n      minWidth,\n      maxWidth,\n      ret,\n      isCustomProp = rcustomProp.test(name),\n      // Support: Firefox 51+\n      // Retrieving style before computed somehow\n      // fixes an issue with getting wrong values\n      // on detached elements\n      style = elem.style;\n    computed = computed || getStyles(elem);\n\n    // getPropertyValue is needed for:\n    //   .css('filter') (IE 9 only, trac-12537)\n    //   .css('--customProperty) (gh-3144)\n    if (computed) {\n      // Support: IE <=9 - 11+\n      // IE only supports `\"float\"` in `getPropertyValue`; in computed styles\n      // it's only available as `\"cssFloat\"`. We no longer modify properties\n      // sent to `.css()` apart from camelCasing, so we need to check both.\n      // Normally, this would create difference in behavior: if\n      // `getPropertyValue` returns an empty string, the value returned\n      // by `.css()` would be `undefined`. This is usually the case for\n      // disconnected elements. However, in IE even disconnected elements\n      // with no styles return `\"none\"` for `getPropertyValue( \"float\" )`\n      ret = computed.getPropertyValue(name) || computed[name];\n      if (isCustomProp && ret) {\n        // Support: Firefox 105+, Chrome <=105+\n        // Spec requires trimming whitespace for custom properties (gh-4926).\n        // Firefox only trims leading whitespace. Chrome just collapses\n        // both leading & trailing whitespace to a single space.\n        //\n        // Fall back to `undefined` if empty string returned.\n        // This collapses a missing definition with property defined\n        // and set to an empty string but there's no standard API\n        // allowing us to differentiate them without a performance penalty\n        // and returning `undefined` aligns with older jQuery.\n        //\n        // rtrimCSS treats U+000D CARRIAGE RETURN and U+000C FORM FEED\n        // as whitespace while CSS does not, but this is not a problem\n        // because CSS preprocessing replaces them with U+000A LINE FEED\n        // (which *is* CSS whitespace)\n        // https://www.w3.org/TR/css-syntax-3/#input-preprocessing\n        ret = ret.replace(rtrimCSS, \"$1\") || undefined;\n      }\n      if (ret === \"\" && !isAttached(elem)) {\n        ret = jQuery.style(elem, name);\n      }\n\n      // A tribute to the \"awesome hack by Dean Edwards\"\n      // Android Browser returns percentage for some values,\n      // but width seems to be reliably pixels.\n      // This is against the CSSOM draft spec:\n      // https://drafts.csswg.org/cssom/#resolved-values\n      if (!support.pixelBoxStyles() && rnumnonpx.test(ret) && rboxStyle.test(name)) {\n        // Remember the original values\n        width = style.width;\n        minWidth = style.minWidth;\n        maxWidth = style.maxWidth;\n\n        // Put in the new values to get a computed value out\n        style.minWidth = style.maxWidth = style.width = ret;\n        ret = computed.width;\n\n        // Revert the changed values\n        style.width = width;\n        style.minWidth = minWidth;\n        style.maxWidth = maxWidth;\n      }\n    }\n    return ret !== undefined ?\n    // Support: IE <=9 - 11 only\n    // IE returns zIndex value as an integer.\n    ret + \"\" : ret;\n  }\n  function addGetHookIf(conditionFn, hookFn) {\n    // Define the hook, we'll check on the first run if it's really needed.\n    return {\n      get: function () {\n        if (conditionFn()) {\n          // Hook not needed (or it's not possible to use it due\n          // to missing dependency), remove it.\n          delete this.get;\n          return;\n        }\n\n        // Hook needed; redefine it so that the support test is not executed again.\n        return (this.get = hookFn).apply(this, arguments);\n      }\n    };\n  }\n  var cssPrefixes = [\"Webkit\", \"Moz\", \"ms\"],\n    emptyStyle = document.createElement(\"div\").style,\n    vendorProps = {};\n\n  // Return a vendor-prefixed property or undefined\n  function vendorPropName(name) {\n    // Check for vendor prefixed names\n    var capName = name[0].toUpperCase() + name.slice(1),\n      i = cssPrefixes.length;\n    while (i--) {\n      name = cssPrefixes[i] + capName;\n      if (name in emptyStyle) {\n        return name;\n      }\n    }\n  }\n\n  // Return a potentially-mapped jQuery.cssProps or vendor prefixed property\n  function finalPropName(name) {\n    var final = jQuery.cssProps[name] || vendorProps[name];\n    if (final) {\n      return final;\n    }\n    if (name in emptyStyle) {\n      return name;\n    }\n    return vendorProps[name] = vendorPropName(name) || name;\n  }\n  var\n    // Swappable if display is none or starts with table\n    // except \"table\", \"table-cell\", or \"table-caption\"\n    // See here for display values: https://developer.mozilla.org/en-US/docs/CSS/display\n    rdisplayswap = /^(none|table(?!-c[ea]).+)/,\n    cssShow = {\n      position: \"absolute\",\n      visibility: \"hidden\",\n      display: \"block\"\n    },\n    cssNormalTransform = {\n      letterSpacing: \"0\",\n      fontWeight: \"400\"\n    };\n  function setPositiveNumber(_elem, value, subtract) {\n    // Any relative (+/-) values have already been\n    // normalized at this point\n    var matches = rcssNum.exec(value);\n    return matches ?\n    // Guard against undefined \"subtract\", e.g., when used as in cssHooks\n    Math.max(0, matches[2] - (subtract || 0)) + (matches[3] || \"px\") : value;\n  }\n  function boxModelAdjustment(elem, dimension, box, isBorderBox, styles, computedVal) {\n    var i = dimension === \"width\" ? 1 : 0,\n      extra = 0,\n      delta = 0,\n      marginDelta = 0;\n\n    // Adjustment may not be necessary\n    if (box === (isBorderBox ? \"border\" : \"content\")) {\n      return 0;\n    }\n    for (; i < 4; i += 2) {\n      // Both box models exclude margin\n      // Count margin delta separately to only add it after scroll gutter adjustment.\n      // This is needed to make negative margins work with `outerHeight( true )` (gh-3982).\n      if (box === \"margin\") {\n        marginDelta += jQuery.css(elem, box + cssExpand[i], true, styles);\n      }\n\n      // If we get here with a content-box, we're seeking \"padding\" or \"border\" or \"margin\"\n      if (!isBorderBox) {\n        // Add padding\n        delta += jQuery.css(elem, \"padding\" + cssExpand[i], true, styles);\n\n        // For \"border\" or \"margin\", add border\n        if (box !== \"padding\") {\n          delta += jQuery.css(elem, \"border\" + cssExpand[i] + \"Width\", true, styles);\n\n          // But still keep track of it otherwise\n        } else {\n          extra += jQuery.css(elem, \"border\" + cssExpand[i] + \"Width\", true, styles);\n        }\n\n        // If we get here with a border-box (content + padding + border), we're seeking \"content\" or\n        // \"padding\" or \"margin\"\n      } else {\n        // For \"content\", subtract padding\n        if (box === \"content\") {\n          delta -= jQuery.css(elem, \"padding\" + cssExpand[i], true, styles);\n        }\n\n        // For \"content\" or \"padding\", subtract border\n        if (box !== \"margin\") {\n          delta -= jQuery.css(elem, \"border\" + cssExpand[i] + \"Width\", true, styles);\n        }\n      }\n    }\n\n    // Account for positive content-box scroll gutter when requested by providing computedVal\n    if (!isBorderBox && computedVal >= 0) {\n      // offsetWidth/offsetHeight is a rounded sum of content, padding, scroll gutter, and border\n      // Assuming integer scroll gutter, subtract the rest and round down\n      delta += Math.max(0, Math.ceil(elem[\"offset\" + dimension[0].toUpperCase() + dimension.slice(1)] - computedVal - delta - extra - 0.5\n\n      // If offsetWidth/offsetHeight is unknown, then we can't determine content-box scroll gutter\n      // Use an explicit zero to avoid NaN (gh-3964)\n      )) || 0;\n    }\n    return delta + marginDelta;\n  }\n  function getWidthOrHeight(elem, dimension, extra) {\n    // Start with computed style\n    var styles = getStyles(elem),\n      // To avoid forcing a reflow, only fetch boxSizing if we need it (gh-4322).\n      // Fake content-box until we know it's needed to know the true value.\n      boxSizingNeeded = !support.boxSizingReliable() || extra,\n      isBorderBox = boxSizingNeeded && jQuery.css(elem, \"boxSizing\", false, styles) === \"border-box\",\n      valueIsBorderBox = isBorderBox,\n      val = curCSS(elem, dimension, styles),\n      offsetProp = \"offset\" + dimension[0].toUpperCase() + dimension.slice(1);\n\n    // Support: Firefox <=54\n    // Return a confounding non-pixel value or feign ignorance, as appropriate.\n    if (rnumnonpx.test(val)) {\n      if (!extra) {\n        return val;\n      }\n      val = \"auto\";\n    }\n\n    // Support: IE 9 - 11 only\n    // Use offsetWidth/offsetHeight for when box sizing is unreliable.\n    // In those cases, the computed value can be trusted to be border-box.\n    if ((!support.boxSizingReliable() && isBorderBox ||\n    // Support: IE 10 - 11+, Edge 15 - 18+\n    // IE/Edge misreport `getComputedStyle` of table rows with width/height\n    // set in CSS while `offset*` properties report correct values.\n    // Interestingly, in some cases IE 9 doesn't suffer from this issue.\n    !support.reliableTrDimensions() && nodeName(elem, \"tr\") ||\n    // Fall back to offsetWidth/offsetHeight when value is \"auto\"\n    // This happens for inline elements with no explicit setting (gh-3571)\n    val === \"auto\" ||\n    // Support: Android <=4.1 - 4.3 only\n    // Also use offsetWidth/offsetHeight for misreported inline dimensions (gh-3602)\n    !parseFloat(val) && jQuery.css(elem, \"display\", false, styles) === \"inline\") &&\n    // Make sure the element is visible & connected\n    elem.getClientRects().length) {\n      isBorderBox = jQuery.css(elem, \"boxSizing\", false, styles) === \"border-box\";\n\n      // Where available, offsetWidth/offsetHeight approximate border box dimensions.\n      // Where not available (e.g., SVG), assume unreliable box-sizing and interpret the\n      // retrieved value as a content box dimension.\n      valueIsBorderBox = offsetProp in elem;\n      if (valueIsBorderBox) {\n        val = elem[offsetProp];\n      }\n    }\n\n    // Normalize \"\" and auto\n    val = parseFloat(val) || 0;\n\n    // Adjust for the element's box model\n    return val + boxModelAdjustment(elem, dimension, extra || (isBorderBox ? \"border\" : \"content\"), valueIsBorderBox, styles,\n    // Provide the current computed size to request scroll gutter calculation (gh-3589)\n    val) + \"px\";\n  }\n  jQuery.extend({\n    // Add in style property hooks for overriding the default\n    // behavior of getting and setting a style property\n    cssHooks: {\n      opacity: {\n        get: function (elem, computed) {\n          if (computed) {\n            // We should always get a number back from opacity\n            var ret = curCSS(elem, \"opacity\");\n            return ret === \"\" ? \"1\" : ret;\n          }\n        }\n      }\n    },\n    // Don't automatically add \"px\" to these possibly-unitless properties\n    cssNumber: {\n      animationIterationCount: true,\n      aspectRatio: true,\n      borderImageSlice: true,\n      columnCount: true,\n      flexGrow: true,\n      flexShrink: true,\n      fontWeight: true,\n      gridArea: true,\n      gridColumn: true,\n      gridColumnEnd: true,\n      gridColumnStart: true,\n      gridRow: true,\n      gridRowEnd: true,\n      gridRowStart: true,\n      lineHeight: true,\n      opacity: true,\n      order: true,\n      orphans: true,\n      scale: true,\n      widows: true,\n      zIndex: true,\n      zoom: true,\n      // SVG-related\n      fillOpacity: true,\n      floodOpacity: true,\n      stopOpacity: true,\n      strokeMiterlimit: true,\n      strokeOpacity: true\n    },\n    // Add in properties whose names you wish to fix before\n    // setting or getting the value\n    cssProps: {},\n    // Get and set the style property on a DOM Node\n    style: function (elem, name, value, extra) {\n      // Don't set styles on text and comment nodes\n      if (!elem || elem.nodeType === 3 || elem.nodeType === 8 || !elem.style) {\n        return;\n      }\n\n      // Make sure that we're working with the right name\n      var ret,\n        type,\n        hooks,\n        origName = camelCase(name),\n        isCustomProp = rcustomProp.test(name),\n        style = elem.style;\n\n      // Make sure that we're working with the right name. We don't\n      // want to query the value if it is a CSS custom property\n      // since they are user-defined.\n      if (!isCustomProp) {\n        name = finalPropName(origName);\n      }\n\n      // Gets hook for the prefixed version, then unprefixed version\n      hooks = jQuery.cssHooks[name] || jQuery.cssHooks[origName];\n\n      // Check if we're setting a value\n      if (value !== undefined) {\n        type = typeof value;\n\n        // Convert \"+=\" or \"-=\" to relative numbers (trac-7345)\n        if (type === \"string\" && (ret = rcssNum.exec(value)) && ret[1]) {\n          value = adjustCSS(elem, name, ret);\n\n          // Fixes bug trac-9237\n          type = \"number\";\n        }\n\n        // Make sure that null and NaN values aren't set (trac-7116)\n        if (value == null || value !== value) {\n          return;\n        }\n\n        // If a number was passed in, add the unit (except for certain CSS properties)\n        // The isCustomProp check can be removed in jQuery 4.0 when we only auto-append\n        // \"px\" to a few hardcoded values.\n        if (type === \"number\" && !isCustomProp) {\n          value += ret && ret[3] || (jQuery.cssNumber[origName] ? \"\" : \"px\");\n        }\n\n        // background-* props affect original clone's values\n        if (!support.clearCloneStyle && value === \"\" && name.indexOf(\"background\") === 0) {\n          style[name] = \"inherit\";\n        }\n\n        // If a hook was provided, use that value, otherwise just set the specified value\n        if (!hooks || !(\"set\" in hooks) || (value = hooks.set(elem, value, extra)) !== undefined) {\n          if (isCustomProp) {\n            style.setProperty(name, value);\n          } else {\n            style[name] = value;\n          }\n        }\n      } else {\n        // If a hook was provided get the non-computed value from there\n        if (hooks && \"get\" in hooks && (ret = hooks.get(elem, false, extra)) !== undefined) {\n          return ret;\n        }\n\n        // Otherwise just get the value from the style object\n        return style[name];\n      }\n    },\n    css: function (elem, name, extra, styles) {\n      var val,\n        num,\n        hooks,\n        origName = camelCase(name),\n        isCustomProp = rcustomProp.test(name);\n\n      // Make sure that we're working with the right name. We don't\n      // want to modify the value if it is a CSS custom property\n      // since they are user-defined.\n      if (!isCustomProp) {\n        name = finalPropName(origName);\n      }\n\n      // Try prefixed name followed by the unprefixed name\n      hooks = jQuery.cssHooks[name] || jQuery.cssHooks[origName];\n\n      // If a hook was provided get the computed value from there\n      if (hooks && \"get\" in hooks) {\n        val = hooks.get(elem, true, extra);\n      }\n\n      // Otherwise, if a way to get the computed value exists, use that\n      if (val === undefined) {\n        val = curCSS(elem, name, styles);\n      }\n\n      // Convert \"normal\" to computed value\n      if (val === \"normal\" && name in cssNormalTransform) {\n        val = cssNormalTransform[name];\n      }\n\n      // Make numeric if forced or a qualifier was provided and val looks numeric\n      if (extra === \"\" || extra) {\n        num = parseFloat(val);\n        return extra === true || isFinite(num) ? num || 0 : val;\n      }\n      return val;\n    }\n  });\n  jQuery.each([\"height\", \"width\"], function (_i, dimension) {\n    jQuery.cssHooks[dimension] = {\n      get: function (elem, computed, extra) {\n        if (computed) {\n          // Certain elements can have dimension info if we invisibly show them\n          // but it must have a current display style that would benefit\n          return rdisplayswap.test(jQuery.css(elem, \"display\")) && (\n          // Support: Safari 8+\n          // Table columns in Safari have non-zero offsetWidth & zero\n          // getBoundingClientRect().width unless display is changed.\n          // Support: IE <=11 only\n          // Running getBoundingClientRect on a disconnected node\n          // in IE throws an error.\n          !elem.getClientRects().length || !elem.getBoundingClientRect().width) ? swap(elem, cssShow, function () {\n            return getWidthOrHeight(elem, dimension, extra);\n          }) : getWidthOrHeight(elem, dimension, extra);\n        }\n      },\n      set: function (elem, value, extra) {\n        var matches,\n          styles = getStyles(elem),\n          // Only read styles.position if the test has a chance to fail\n          // to avoid forcing a reflow.\n          scrollboxSizeBuggy = !support.scrollboxSize() && styles.position === \"absolute\",\n          // To avoid forcing a reflow, only fetch boxSizing if we need it (gh-3991)\n          boxSizingNeeded = scrollboxSizeBuggy || extra,\n          isBorderBox = boxSizingNeeded && jQuery.css(elem, \"boxSizing\", false, styles) === \"border-box\",\n          subtract = extra ? boxModelAdjustment(elem, dimension, extra, isBorderBox, styles) : 0;\n\n        // Account for unreliable border-box dimensions by comparing offset* to computed and\n        // faking a content-box to get border and padding (gh-3699)\n        if (isBorderBox && scrollboxSizeBuggy) {\n          subtract -= Math.ceil(elem[\"offset\" + dimension[0].toUpperCase() + dimension.slice(1)] - parseFloat(styles[dimension]) - boxModelAdjustment(elem, dimension, \"border\", false, styles) - 0.5);\n        }\n\n        // Convert to pixels if value adjustment is needed\n        if (subtract && (matches = rcssNum.exec(value)) && (matches[3] || \"px\") !== \"px\") {\n          elem.style[dimension] = value;\n          value = jQuery.css(elem, dimension);\n        }\n        return setPositiveNumber(elem, value, subtract);\n      }\n    };\n  });\n  jQuery.cssHooks.marginLeft = addGetHookIf(support.reliableMarginLeft, function (elem, computed) {\n    if (computed) {\n      return (parseFloat(curCSS(elem, \"marginLeft\")) || elem.getBoundingClientRect().left - swap(elem, {\n        marginLeft: 0\n      }, function () {\n        return elem.getBoundingClientRect().left;\n      })) + \"px\";\n    }\n  });\n\n  // These hooks are used by animate to expand properties\n  jQuery.each({\n    margin: \"\",\n    padding: \"\",\n    border: \"Width\"\n  }, function (prefix, suffix) {\n    jQuery.cssHooks[prefix + suffix] = {\n      expand: function (value) {\n        var i = 0,\n          expanded = {},\n          // Assumes a single number if not a string\n          parts = typeof value === \"string\" ? value.split(\" \") : [value];\n        for (; i < 4; i++) {\n          expanded[prefix + cssExpand[i] + suffix] = parts[i] || parts[i - 2] || parts[0];\n        }\n        return expanded;\n      }\n    };\n    if (prefix !== \"margin\") {\n      jQuery.cssHooks[prefix + suffix].set = setPositiveNumber;\n    }\n  });\n  jQuery.fn.extend({\n    css: function (name, value) {\n      return access(this, function (elem, name, value) {\n        var styles,\n          len,\n          map = {},\n          i = 0;\n        if (Array.isArray(name)) {\n          styles = getStyles(elem);\n          len = name.length;\n          for (; i < len; i++) {\n            map[name[i]] = jQuery.css(elem, name[i], false, styles);\n          }\n          return map;\n        }\n        return value !== undefined ? jQuery.style(elem, name, value) : jQuery.css(elem, name);\n      }, name, value, arguments.length > 1);\n    }\n  });\n  function Tween(elem, options, prop, end, easing) {\n    return new Tween.prototype.init(elem, options, prop, end, easing);\n  }\n  jQuery.Tween = Tween;\n  Tween.prototype = {\n    constructor: Tween,\n    init: function (elem, options, prop, end, easing, unit) {\n      this.elem = elem;\n      this.prop = prop;\n      this.easing = easing || jQuery.easing._default;\n      this.options = options;\n      this.start = this.now = this.cur();\n      this.end = end;\n      this.unit = unit || (jQuery.cssNumber[prop] ? \"\" : \"px\");\n    },\n    cur: function () {\n      var hooks = Tween.propHooks[this.prop];\n      return hooks && hooks.get ? hooks.get(this) : Tween.propHooks._default.get(this);\n    },\n    run: function (percent) {\n      var eased,\n        hooks = Tween.propHooks[this.prop];\n      if (this.options.duration) {\n        this.pos = eased = jQuery.easing[this.easing](percent, this.options.duration * percent, 0, 1, this.options.duration);\n      } else {\n        this.pos = eased = percent;\n      }\n      this.now = (this.end - this.start) * eased + this.start;\n      if (this.options.step) {\n        this.options.step.call(this.elem, this.now, this);\n      }\n      if (hooks && hooks.set) {\n        hooks.set(this);\n      } else {\n        Tween.propHooks._default.set(this);\n      }\n      return this;\n    }\n  };\n  Tween.prototype.init.prototype = Tween.prototype;\n  Tween.propHooks = {\n    _default: {\n      get: function (tween) {\n        var result;\n\n        // Use a property on the element directly when it is not a DOM element,\n        // or when there is no matching style property that exists.\n        if (tween.elem.nodeType !== 1 || tween.elem[tween.prop] != null && tween.elem.style[tween.prop] == null) {\n          return tween.elem[tween.prop];\n        }\n\n        // Passing an empty string as a 3rd parameter to .css will automatically\n        // attempt a parseFloat and fallback to a string if the parse fails.\n        // Simple values such as \"10px\" are parsed to Float;\n        // complex values such as \"rotate(1rad)\" are returned as-is.\n        result = jQuery.css(tween.elem, tween.prop, \"\");\n\n        // Empty strings, null, undefined and \"auto\" are converted to 0.\n        return !result || result === \"auto\" ? 0 : result;\n      },\n      set: function (tween) {\n        // Use step hook for back compat.\n        // Use cssHook if its there.\n        // Use .style if available and use plain properties where available.\n        if (jQuery.fx.step[tween.prop]) {\n          jQuery.fx.step[tween.prop](tween);\n        } else if (tween.elem.nodeType === 1 && (jQuery.cssHooks[tween.prop] || tween.elem.style[finalPropName(tween.prop)] != null)) {\n          jQuery.style(tween.elem, tween.prop, tween.now + tween.unit);\n        } else {\n          tween.elem[tween.prop] = tween.now;\n        }\n      }\n    }\n  };\n\n  // Support: IE <=9 only\n  // Panic based approach to setting things on disconnected nodes\n  Tween.propHooks.scrollTop = Tween.propHooks.scrollLeft = {\n    set: function (tween) {\n      if (tween.elem.nodeType && tween.elem.parentNode) {\n        tween.elem[tween.prop] = tween.now;\n      }\n    }\n  };\n  jQuery.easing = {\n    linear: function (p) {\n      return p;\n    },\n    swing: function (p) {\n      return 0.5 - Math.cos(p * Math.PI) / 2;\n    },\n    _default: \"swing\"\n  };\n  jQuery.fx = Tween.prototype.init;\n\n  // Back compat <1.8 extension point\n  jQuery.fx.step = {};\n  var fxNow,\n    inProgress,\n    rfxtypes = /^(?:toggle|show|hide)$/,\n    rrun = /queueHooks$/;\n  function schedule() {\n    if (inProgress) {\n      if (document.hidden === false && window.requestAnimationFrame) {\n        window.requestAnimationFrame(schedule);\n      } else {\n        window.setTimeout(schedule, jQuery.fx.interval);\n      }\n      jQuery.fx.tick();\n    }\n  }\n\n  // Animations created synchronously will run synchronously\n  function createFxNow() {\n    window.setTimeout(function () {\n      fxNow = undefined;\n    });\n    return fxNow = Date.now();\n  }\n\n  // Generate parameters to create a standard animation\n  function genFx(type, includeWidth) {\n    var which,\n      i = 0,\n      attrs = {\n        height: type\n      };\n\n    // If we include width, step value is 1 to do all cssExpand values,\n    // otherwise step value is 2 to skip over Left and Right\n    includeWidth = includeWidth ? 1 : 0;\n    for (; i < 4; i += 2 - includeWidth) {\n      which = cssExpand[i];\n      attrs[\"margin\" + which] = attrs[\"padding\" + which] = type;\n    }\n    if (includeWidth) {\n      attrs.opacity = attrs.width = type;\n    }\n    return attrs;\n  }\n  function createTween(value, prop, animation) {\n    var tween,\n      collection = (Animation.tweeners[prop] || []).concat(Animation.tweeners[\"*\"]),\n      index = 0,\n      length = collection.length;\n    for (; index < length; index++) {\n      if (tween = collection[index].call(animation, prop, value)) {\n        // We're done with this property\n        return tween;\n      }\n    }\n  }\n  function defaultPrefilter(elem, props, opts) {\n    var prop,\n      value,\n      toggle,\n      hooks,\n      oldfire,\n      propTween,\n      restoreDisplay,\n      display,\n      isBox = \"width\" in props || \"height\" in props,\n      anim = this,\n      orig = {},\n      style = elem.style,\n      hidden = elem.nodeType && isHiddenWithinTree(elem),\n      dataShow = dataPriv.get(elem, \"fxshow\");\n\n    // Queue-skipping animations hijack the fx hooks\n    if (!opts.queue) {\n      hooks = jQuery._queueHooks(elem, \"fx\");\n      if (hooks.unqueued == null) {\n        hooks.unqueued = 0;\n        oldfire = hooks.empty.fire;\n        hooks.empty.fire = function () {\n          if (!hooks.unqueued) {\n            oldfire();\n          }\n        };\n      }\n      hooks.unqueued++;\n      anim.always(function () {\n        // Ensure the complete handler is called before this completes\n        anim.always(function () {\n          hooks.unqueued--;\n          if (!jQuery.queue(elem, \"fx\").length) {\n            hooks.empty.fire();\n          }\n        });\n      });\n    }\n\n    // Detect show/hide animations\n    for (prop in props) {\n      value = props[prop];\n      if (rfxtypes.test(value)) {\n        delete props[prop];\n        toggle = toggle || value === \"toggle\";\n        if (value === (hidden ? \"hide\" : \"show\")) {\n          // Pretend to be hidden if this is a \"show\" and\n          // there is still data from a stopped show/hide\n          if (value === \"show\" && dataShow && dataShow[prop] !== undefined) {\n            hidden = true;\n\n            // Ignore all other no-op show/hide data\n          } else {\n            continue;\n          }\n        }\n        orig[prop] = dataShow && dataShow[prop] || jQuery.style(elem, prop);\n      }\n    }\n\n    // Bail out if this is a no-op like .hide().hide()\n    propTween = !jQuery.isEmptyObject(props);\n    if (!propTween && jQuery.isEmptyObject(orig)) {\n      return;\n    }\n\n    // Restrict \"overflow\" and \"display\" styles during box animations\n    if (isBox && elem.nodeType === 1) {\n      // Support: IE <=9 - 11, Edge 12 - 15\n      // Record all 3 overflow attributes because IE does not infer the shorthand\n      // from identically-valued overflowX and overflowY and Edge just mirrors\n      // the overflowX value there.\n      opts.overflow = [style.overflow, style.overflowX, style.overflowY];\n\n      // Identify a display type, preferring old show/hide data over the CSS cascade\n      restoreDisplay = dataShow && dataShow.display;\n      if (restoreDisplay == null) {\n        restoreDisplay = dataPriv.get(elem, \"display\");\n      }\n      display = jQuery.css(elem, \"display\");\n      if (display === \"none\") {\n        if (restoreDisplay) {\n          display = restoreDisplay;\n        } else {\n          // Get nonempty value(s) by temporarily forcing visibility\n          showHide([elem], true);\n          restoreDisplay = elem.style.display || restoreDisplay;\n          display = jQuery.css(elem, \"display\");\n          showHide([elem]);\n        }\n      }\n\n      // Animate inline elements as inline-block\n      if (display === \"inline\" || display === \"inline-block\" && restoreDisplay != null) {\n        if (jQuery.css(elem, \"float\") === \"none\") {\n          // Restore the original display value at the end of pure show/hide animations\n          if (!propTween) {\n            anim.done(function () {\n              style.display = restoreDisplay;\n            });\n            if (restoreDisplay == null) {\n              display = style.display;\n              restoreDisplay = display === \"none\" ? \"\" : display;\n            }\n          }\n          style.display = \"inline-block\";\n        }\n      }\n    }\n    if (opts.overflow) {\n      style.overflow = \"hidden\";\n      anim.always(function () {\n        style.overflow = opts.overflow[0];\n        style.overflowX = opts.overflow[1];\n        style.overflowY = opts.overflow[2];\n      });\n    }\n\n    // Implement show/hide animations\n    propTween = false;\n    for (prop in orig) {\n      // General show/hide setup for this element animation\n      if (!propTween) {\n        if (dataShow) {\n          if (\"hidden\" in dataShow) {\n            hidden = dataShow.hidden;\n          }\n        } else {\n          dataShow = dataPriv.access(elem, \"fxshow\", {\n            display: restoreDisplay\n          });\n        }\n\n        // Store hidden/visible for toggle so `.stop().toggle()` \"reverses\"\n        if (toggle) {\n          dataShow.hidden = !hidden;\n        }\n\n        // Show elements before animating them\n        if (hidden) {\n          showHide([elem], true);\n        }\n\n        /* eslint-disable no-loop-func */\n\n        anim.done(function () {\n          /* eslint-enable no-loop-func */\n\n          // The final step of a \"hide\" animation is actually hiding the element\n          if (!hidden) {\n            showHide([elem]);\n          }\n          dataPriv.remove(elem, \"fxshow\");\n          for (prop in orig) {\n            jQuery.style(elem, prop, orig[prop]);\n          }\n        });\n      }\n\n      // Per-property setup\n      propTween = createTween(hidden ? dataShow[prop] : 0, prop, anim);\n      if (!(prop in dataShow)) {\n        dataShow[prop] = propTween.start;\n        if (hidden) {\n          propTween.end = propTween.start;\n          propTween.start = 0;\n        }\n      }\n    }\n  }\n  function propFilter(props, specialEasing) {\n    var index, name, easing, value, hooks;\n\n    // camelCase, specialEasing and expand cssHook pass\n    for (index in props) {\n      name = camelCase(index);\n      easing = specialEasing[name];\n      value = props[index];\n      if (Array.isArray(value)) {\n        easing = value[1];\n        value = props[index] = value[0];\n      }\n      if (index !== name) {\n        props[name] = value;\n        delete props[index];\n      }\n      hooks = jQuery.cssHooks[name];\n      if (hooks && \"expand\" in hooks) {\n        value = hooks.expand(value);\n        delete props[name];\n\n        // Not quite $.extend, this won't overwrite existing keys.\n        // Reusing 'index' because we have the correct \"name\"\n        for (index in value) {\n          if (!(index in props)) {\n            props[index] = value[index];\n            specialEasing[index] = easing;\n          }\n        }\n      } else {\n        specialEasing[name] = easing;\n      }\n    }\n  }\n  function Animation(elem, properties, options) {\n    var result,\n      stopped,\n      index = 0,\n      length = Animation.prefilters.length,\n      deferred = jQuery.Deferred().always(function () {\n        // Don't match elem in the :animated selector\n        delete tick.elem;\n      }),\n      tick = function () {\n        if (stopped) {\n          return false;\n        }\n        var currentTime = fxNow || createFxNow(),\n          remaining = Math.max(0, animation.startTime + animation.duration - currentTime),\n          // Support: Android 2.3 only\n          // Archaic crash bug won't allow us to use `1 - ( 0.5 || 0 )` (trac-12497)\n          temp = remaining / animation.duration || 0,\n          percent = 1 - temp,\n          index = 0,\n          length = animation.tweens.length;\n        for (; index < length; index++) {\n          animation.tweens[index].run(percent);\n        }\n        deferred.notifyWith(elem, [animation, percent, remaining]);\n\n        // If there's more to do, yield\n        if (percent < 1 && length) {\n          return remaining;\n        }\n\n        // If this was an empty animation, synthesize a final progress notification\n        if (!length) {\n          deferred.notifyWith(elem, [animation, 1, 0]);\n        }\n\n        // Resolve the animation and report its conclusion\n        deferred.resolveWith(elem, [animation]);\n        return false;\n      },\n      animation = deferred.promise({\n        elem: elem,\n        props: jQuery.extend({}, properties),\n        opts: jQuery.extend(true, {\n          specialEasing: {},\n          easing: jQuery.easing._default\n        }, options),\n        originalProperties: properties,\n        originalOptions: options,\n        startTime: fxNow || createFxNow(),\n        duration: options.duration,\n        tweens: [],\n        createTween: function (prop, end) {\n          var tween = jQuery.Tween(elem, animation.opts, prop, end, animation.opts.specialEasing[prop] || animation.opts.easing);\n          animation.tweens.push(tween);\n          return tween;\n        },\n        stop: function (gotoEnd) {\n          var index = 0,\n            // If we are going to the end, we want to run all the tweens\n            // otherwise we skip this part\n            length = gotoEnd ? animation.tweens.length : 0;\n          if (stopped) {\n            return this;\n          }\n          stopped = true;\n          for (; index < length; index++) {\n            animation.tweens[index].run(1);\n          }\n\n          // Resolve when we played the last frame; otherwise, reject\n          if (gotoEnd) {\n            deferred.notifyWith(elem, [animation, 1, 0]);\n            deferred.resolveWith(elem, [animation, gotoEnd]);\n          } else {\n            deferred.rejectWith(elem, [animation, gotoEnd]);\n          }\n          return this;\n        }\n      }),\n      props = animation.props;\n    propFilter(props, animation.opts.specialEasing);\n    for (; index < length; index++) {\n      result = Animation.prefilters[index].call(animation, elem, props, animation.opts);\n      if (result) {\n        if (isFunction(result.stop)) {\n          jQuery._queueHooks(animation.elem, animation.opts.queue).stop = result.stop.bind(result);\n        }\n        return result;\n      }\n    }\n    jQuery.map(props, createTween, animation);\n    if (isFunction(animation.opts.start)) {\n      animation.opts.start.call(elem, animation);\n    }\n\n    // Attach callbacks from options\n    animation.progress(animation.opts.progress).done(animation.opts.done, animation.opts.complete).fail(animation.opts.fail).always(animation.opts.always);\n    jQuery.fx.timer(jQuery.extend(tick, {\n      elem: elem,\n      anim: animation,\n      queue: animation.opts.queue\n    }));\n    return animation;\n  }\n  jQuery.Animation = jQuery.extend(Animation, {\n    tweeners: {\n      \"*\": [function (prop, value) {\n        var tween = this.createTween(prop, value);\n        adjustCSS(tween.elem, prop, rcssNum.exec(value), tween);\n        return tween;\n      }]\n    },\n    tweener: function (props, callback) {\n      if (isFunction(props)) {\n        callback = props;\n        props = [\"*\"];\n      } else {\n        props = props.match(rnothtmlwhite);\n      }\n      var prop,\n        index = 0,\n        length = props.length;\n      for (; index < length; index++) {\n        prop = props[index];\n        Animation.tweeners[prop] = Animation.tweeners[prop] || [];\n        Animation.tweeners[prop].unshift(callback);\n      }\n    },\n    prefilters: [defaultPrefilter],\n    prefilter: function (callback, prepend) {\n      if (prepend) {\n        Animation.prefilters.unshift(callback);\n      } else {\n        Animation.prefilters.push(callback);\n      }\n    }\n  });\n  jQuery.speed = function (speed, easing, fn) {\n    var opt = speed && typeof speed === \"object\" ? jQuery.extend({}, speed) : {\n      complete: fn || !fn && easing || isFunction(speed) && speed,\n      duration: speed,\n      easing: fn && easing || easing && !isFunction(easing) && easing\n    };\n\n    // Go to the end state if fx are off\n    if (jQuery.fx.off) {\n      opt.duration = 0;\n    } else {\n      if (typeof opt.duration !== \"number\") {\n        if (opt.duration in jQuery.fx.speeds) {\n          opt.duration = jQuery.fx.speeds[opt.duration];\n        } else {\n          opt.duration = jQuery.fx.speeds._default;\n        }\n      }\n    }\n\n    // Normalize opt.queue - true/undefined/null -> \"fx\"\n    if (opt.queue == null || opt.queue === true) {\n      opt.queue = \"fx\";\n    }\n\n    // Queueing\n    opt.old = opt.complete;\n    opt.complete = function () {\n      if (isFunction(opt.old)) {\n        opt.old.call(this);\n      }\n      if (opt.queue) {\n        jQuery.dequeue(this, opt.queue);\n      }\n    };\n    return opt;\n  };\n  jQuery.fn.extend({\n    fadeTo: function (speed, to, easing, callback) {\n      // Show any hidden elements after setting opacity to 0\n      return this.filter(isHiddenWithinTree).css(\"opacity\", 0).show()\n\n      // Animate to the value specified\n      .end().animate({\n        opacity: to\n      }, speed, easing, callback);\n    },\n    animate: function (prop, speed, easing, callback) {\n      var empty = jQuery.isEmptyObject(prop),\n        optall = jQuery.speed(speed, easing, callback),\n        doAnimation = function () {\n          // Operate on a copy of prop so per-property easing won't be lost\n          var anim = Animation(this, jQuery.extend({}, prop), optall);\n\n          // Empty animations, or finishing resolves immediately\n          if (empty || dataPriv.get(this, \"finish\")) {\n            anim.stop(true);\n          }\n        };\n      doAnimation.finish = doAnimation;\n      return empty || optall.queue === false ? this.each(doAnimation) : this.queue(optall.queue, doAnimation);\n    },\n    stop: function (type, clearQueue, gotoEnd) {\n      var stopQueue = function (hooks) {\n        var stop = hooks.stop;\n        delete hooks.stop;\n        stop(gotoEnd);\n      };\n      if (typeof type !== \"string\") {\n        gotoEnd = clearQueue;\n        clearQueue = type;\n        type = undefined;\n      }\n      if (clearQueue) {\n        this.queue(type || \"fx\", []);\n      }\n      return this.each(function () {\n        var dequeue = true,\n          index = type != null && type + \"queueHooks\",\n          timers = jQuery.timers,\n          data = dataPriv.get(this);\n        if (index) {\n          if (data[index] && data[index].stop) {\n            stopQueue(data[index]);\n          }\n        } else {\n          for (index in data) {\n            if (data[index] && data[index].stop && rrun.test(index)) {\n              stopQueue(data[index]);\n            }\n          }\n        }\n        for (index = timers.length; index--;) {\n          if (timers[index].elem === this && (type == null || timers[index].queue === type)) {\n            timers[index].anim.stop(gotoEnd);\n            dequeue = false;\n            timers.splice(index, 1);\n          }\n        }\n\n        // Start the next in the queue if the last step wasn't forced.\n        // Timers currently will call their complete callbacks, which\n        // will dequeue but only if they were gotoEnd.\n        if (dequeue || !gotoEnd) {\n          jQuery.dequeue(this, type);\n        }\n      });\n    },\n    finish: function (type) {\n      if (type !== false) {\n        type = type || \"fx\";\n      }\n      return this.each(function () {\n        var index,\n          data = dataPriv.get(this),\n          queue = data[type + \"queue\"],\n          hooks = data[type + \"queueHooks\"],\n          timers = jQuery.timers,\n          length = queue ? queue.length : 0;\n\n        // Enable finishing flag on private data\n        data.finish = true;\n\n        // Empty the queue first\n        jQuery.queue(this, type, []);\n        if (hooks && hooks.stop) {\n          hooks.stop.call(this, true);\n        }\n\n        // Look for any active animations, and finish them\n        for (index = timers.length; index--;) {\n          if (timers[index].elem === this && timers[index].queue === type) {\n            timers[index].anim.stop(true);\n            timers.splice(index, 1);\n          }\n        }\n\n        // Look for any animations in the old queue and finish them\n        for (index = 0; index < length; index++) {\n          if (queue[index] && queue[index].finish) {\n            queue[index].finish.call(this);\n          }\n        }\n\n        // Turn off finishing flag\n        delete data.finish;\n      });\n    }\n  });\n  jQuery.each([\"toggle\", \"show\", \"hide\"], function (_i, name) {\n    var cssFn = jQuery.fn[name];\n    jQuery.fn[name] = function (speed, easing, callback) {\n      return speed == null || typeof speed === \"boolean\" ? cssFn.apply(this, arguments) : this.animate(genFx(name, true), speed, easing, callback);\n    };\n  });\n\n  // Generate shortcuts for custom animations\n  jQuery.each({\n    slideDown: genFx(\"show\"),\n    slideUp: genFx(\"hide\"),\n    slideToggle: genFx(\"toggle\"),\n    fadeIn: {\n      opacity: \"show\"\n    },\n    fadeOut: {\n      opacity: \"hide\"\n    },\n    fadeToggle: {\n      opacity: \"toggle\"\n    }\n  }, function (name, props) {\n    jQuery.fn[name] = function (speed, easing, callback) {\n      return this.animate(props, speed, easing, callback);\n    };\n  });\n  jQuery.timers = [];\n  jQuery.fx.tick = function () {\n    var timer,\n      i = 0,\n      timers = jQuery.timers;\n    fxNow = Date.now();\n    for (; i < timers.length; i++) {\n      timer = timers[i];\n\n      // Run the timer and safely remove it when done (allowing for external removal)\n      if (!timer() && timers[i] === timer) {\n        timers.splice(i--, 1);\n      }\n    }\n    if (!timers.length) {\n      jQuery.fx.stop();\n    }\n    fxNow = undefined;\n  };\n  jQuery.fx.timer = function (timer) {\n    jQuery.timers.push(timer);\n    jQuery.fx.start();\n  };\n  jQuery.fx.interval = 13;\n  jQuery.fx.start = function () {\n    if (inProgress) {\n      return;\n    }\n    inProgress = true;\n    schedule();\n  };\n  jQuery.fx.stop = function () {\n    inProgress = null;\n  };\n  jQuery.fx.speeds = {\n    slow: 600,\n    fast: 200,\n    // Default speed\n    _default: 400\n  };\n\n  // Based off of the plugin by Clint Helfers, with permission.\n  jQuery.fn.delay = function (time, type) {\n    time = jQuery.fx ? jQuery.fx.speeds[time] || time : time;\n    type = type || \"fx\";\n    return this.queue(type, function (next, hooks) {\n      var timeout = window.setTimeout(next, time);\n      hooks.stop = function () {\n        window.clearTimeout(timeout);\n      };\n    });\n  };\n  (function () {\n    var input = document.createElement(\"input\"),\n      select = document.createElement(\"select\"),\n      opt = select.appendChild(document.createElement(\"option\"));\n    input.type = \"checkbox\";\n\n    // Support: Android <=4.3 only\n    // Default value for a checkbox should be \"on\"\n    support.checkOn = input.value !== \"\";\n\n    // Support: IE <=11 only\n    // Must access selectedIndex to make default options select\n    support.optSelected = opt.selected;\n\n    // Support: IE <=11 only\n    // An input loses its value after becoming a radio\n    input = document.createElement(\"input\");\n    input.value = \"t\";\n    input.type = \"radio\";\n    support.radioValue = input.value === \"t\";\n  })();\n  var boolHook,\n    attrHandle = jQuery.expr.attrHandle;\n  jQuery.fn.extend({\n    attr: function (name, value) {\n      return access(this, jQuery.attr, name, value, arguments.length > 1);\n    },\n    removeAttr: function (name) {\n      return this.each(function () {\n        jQuery.removeAttr(this, name);\n      });\n    }\n  });\n  jQuery.extend({\n    attr: function (elem, name, value) {\n      var ret,\n        hooks,\n        nType = elem.nodeType;\n\n      // Don't get/set attributes on text, comment and attribute nodes\n      if (nType === 3 || nType === 8 || nType === 2) {\n        return;\n      }\n\n      // Fallback to prop when attributes are not supported\n      if (typeof elem.getAttribute === \"undefined\") {\n        return jQuery.prop(elem, name, value);\n      }\n\n      // Attribute hooks are determined by the lowercase version\n      // Grab necessary hook if one is defined\n      if (nType !== 1 || !jQuery.isXMLDoc(elem)) {\n        hooks = jQuery.attrHooks[name.toLowerCase()] || (jQuery.expr.match.bool.test(name) ? boolHook : undefined);\n      }\n      if (value !== undefined) {\n        if (value === null) {\n          jQuery.removeAttr(elem, name);\n          return;\n        }\n        if (hooks && \"set\" in hooks && (ret = hooks.set(elem, value, name)) !== undefined) {\n          return ret;\n        }\n        elem.setAttribute(name, value + \"\");\n        return value;\n      }\n      if (hooks && \"get\" in hooks && (ret = hooks.get(elem, name)) !== null) {\n        return ret;\n      }\n      ret = jQuery.find.attr(elem, name);\n\n      // Non-existent attributes return null, we normalize to undefined\n      return ret == null ? undefined : ret;\n    },\n    attrHooks: {\n      type: {\n        set: function (elem, value) {\n          if (!support.radioValue && value === \"radio\" && nodeName(elem, \"input\")) {\n            var val = elem.value;\n            elem.setAttribute(\"type\", value);\n            if (val) {\n              elem.value = val;\n            }\n            return value;\n          }\n        }\n      }\n    },\n    removeAttr: function (elem, value) {\n      var name,\n        i = 0,\n        // Attribute names can contain non-HTML whitespace characters\n        // https://html.spec.whatwg.org/multipage/syntax.html#attributes-2\n        attrNames = value && value.match(rnothtmlwhite);\n      if (attrNames && elem.nodeType === 1) {\n        while (name = attrNames[i++]) {\n          elem.removeAttribute(name);\n        }\n      }\n    }\n  });\n\n  // Hooks for boolean attributes\n  boolHook = {\n    set: function (elem, value, name) {\n      if (value === false) {\n        // Remove boolean attributes when set to false\n        jQuery.removeAttr(elem, name);\n      } else {\n        elem.setAttribute(name, name);\n      }\n      return name;\n    }\n  };\n  jQuery.each(jQuery.expr.match.bool.source.match(/\\w+/g), function (_i, name) {\n    var getter = attrHandle[name] || jQuery.find.attr;\n    attrHandle[name] = function (elem, name, isXML) {\n      var ret,\n        handle,\n        lowercaseName = name.toLowerCase();\n      if (!isXML) {\n        // Avoid an infinite loop by temporarily removing this function from the getter\n        handle = attrHandle[lowercaseName];\n        attrHandle[lowercaseName] = ret;\n        ret = getter(elem, name, isXML) != null ? lowercaseName : null;\n        attrHandle[lowercaseName] = handle;\n      }\n      return ret;\n    };\n  });\n  var rfocusable = /^(?:input|select|textarea|button)$/i,\n    rclickable = /^(?:a|area)$/i;\n  jQuery.fn.extend({\n    prop: function (name, value) {\n      return access(this, jQuery.prop, name, value, arguments.length > 1);\n    },\n    removeProp: function (name) {\n      return this.each(function () {\n        delete this[jQuery.propFix[name] || name];\n      });\n    }\n  });\n  jQuery.extend({\n    prop: function (elem, name, value) {\n      var ret,\n        hooks,\n        nType = elem.nodeType;\n\n      // Don't get/set properties on text, comment and attribute nodes\n      if (nType === 3 || nType === 8 || nType === 2) {\n        return;\n      }\n      if (nType !== 1 || !jQuery.isXMLDoc(elem)) {\n        // Fix name and attach hooks\n        name = jQuery.propFix[name] || name;\n        hooks = jQuery.propHooks[name];\n      }\n      if (value !== undefined) {\n        if (hooks && \"set\" in hooks && (ret = hooks.set(elem, value, name)) !== undefined) {\n          return ret;\n        }\n        return elem[name] = value;\n      }\n      if (hooks && \"get\" in hooks && (ret = hooks.get(elem, name)) !== null) {\n        return ret;\n      }\n      return elem[name];\n    },\n    propHooks: {\n      tabIndex: {\n        get: function (elem) {\n          // Support: IE <=9 - 11 only\n          // elem.tabIndex doesn't always return the\n          // correct value when it hasn't been explicitly set\n          // Use proper attribute retrieval (trac-12072)\n          var tabindex = jQuery.find.attr(elem, \"tabindex\");\n          if (tabindex) {\n            return parseInt(tabindex, 10);\n          }\n          if (rfocusable.test(elem.nodeName) || rclickable.test(elem.nodeName) && elem.href) {\n            return 0;\n          }\n          return -1;\n        }\n      }\n    },\n    propFix: {\n      \"for\": \"htmlFor\",\n      \"class\": \"className\"\n    }\n  });\n\n  // Support: IE <=11 only\n  // Accessing the selectedIndex property\n  // forces the browser to respect setting selected\n  // on the option\n  // The getter ensures a default option is selected\n  // when in an optgroup\n  // eslint rule \"no-unused-expressions\" is disabled for this code\n  // since it considers such accessions noop\n  if (!support.optSelected) {\n    jQuery.propHooks.selected = {\n      get: function (elem) {\n        /* eslint no-unused-expressions: \"off\" */\n\n        var parent = elem.parentNode;\n        if (parent && parent.parentNode) {\n          parent.parentNode.selectedIndex;\n        }\n        return null;\n      },\n      set: function (elem) {\n        /* eslint no-unused-expressions: \"off\" */\n\n        var parent = elem.parentNode;\n        if (parent) {\n          parent.selectedIndex;\n          if (parent.parentNode) {\n            parent.parentNode.selectedIndex;\n          }\n        }\n      }\n    };\n  }\n  jQuery.each([\"tabIndex\", \"readOnly\", \"maxLength\", \"cellSpacing\", \"cellPadding\", \"rowSpan\", \"colSpan\", \"useMap\", \"frameBorder\", \"contentEditable\"], function () {\n    jQuery.propFix[this.toLowerCase()] = this;\n  });\n\n  // Strip and collapse whitespace according to HTML spec\n  // https://infra.spec.whatwg.org/#strip-and-collapse-ascii-whitespace\n  function stripAndCollapse(value) {\n    var tokens = value.match(rnothtmlwhite) || [];\n    return tokens.join(\" \");\n  }\n  function getClass(elem) {\n    return elem.getAttribute && elem.getAttribute(\"class\") || \"\";\n  }\n  function classesToArray(value) {\n    if (Array.isArray(value)) {\n      return value;\n    }\n    if (typeof value === \"string\") {\n      return value.match(rnothtmlwhite) || [];\n    }\n    return [];\n  }\n  jQuery.fn.extend({\n    addClass: function (value) {\n      var classNames, cur, curValue, className, i, finalValue;\n      if (isFunction(value)) {\n        return this.each(function (j) {\n          jQuery(this).addClass(value.call(this, j, getClass(this)));\n        });\n      }\n      classNames = classesToArray(value);\n      if (classNames.length) {\n        return this.each(function () {\n          curValue = getClass(this);\n          cur = this.nodeType === 1 && \" \" + stripAndCollapse(curValue) + \" \";\n          if (cur) {\n            for (i = 0; i < classNames.length; i++) {\n              className = classNames[i];\n              if (cur.indexOf(\" \" + className + \" \") < 0) {\n                cur += className + \" \";\n              }\n            }\n\n            // Only assign if different to avoid unneeded rendering.\n            finalValue = stripAndCollapse(cur);\n            if (curValue !== finalValue) {\n              this.setAttribute(\"class\", finalValue);\n            }\n          }\n        });\n      }\n      return this;\n    },\n    removeClass: function (value) {\n      var classNames, cur, curValue, className, i, finalValue;\n      if (isFunction(value)) {\n        return this.each(function (j) {\n          jQuery(this).removeClass(value.call(this, j, getClass(this)));\n        });\n      }\n      if (!arguments.length) {\n        return this.attr(\"class\", \"\");\n      }\n      classNames = classesToArray(value);\n      if (classNames.length) {\n        return this.each(function () {\n          curValue = getClass(this);\n\n          // This expression is here for better compressibility (see addClass)\n          cur = this.nodeType === 1 && \" \" + stripAndCollapse(curValue) + \" \";\n          if (cur) {\n            for (i = 0; i < classNames.length; i++) {\n              className = classNames[i];\n\n              // Remove *all* instances\n              while (cur.indexOf(\" \" + className + \" \") > -1) {\n                cur = cur.replace(\" \" + className + \" \", \" \");\n              }\n            }\n\n            // Only assign if different to avoid unneeded rendering.\n            finalValue = stripAndCollapse(cur);\n            if (curValue !== finalValue) {\n              this.setAttribute(\"class\", finalValue);\n            }\n          }\n        });\n      }\n      return this;\n    },\n    toggleClass: function (value, stateVal) {\n      var classNames,\n        className,\n        i,\n        self,\n        type = typeof value,\n        isValidValue = type === \"string\" || Array.isArray(value);\n      if (isFunction(value)) {\n        return this.each(function (i) {\n          jQuery(this).toggleClass(value.call(this, i, getClass(this), stateVal), stateVal);\n        });\n      }\n      if (typeof stateVal === \"boolean\" && isValidValue) {\n        return stateVal ? this.addClass(value) : this.removeClass(value);\n      }\n      classNames = classesToArray(value);\n      return this.each(function () {\n        if (isValidValue) {\n          // Toggle individual class names\n          self = jQuery(this);\n          for (i = 0; i < classNames.length; i++) {\n            className = classNames[i];\n\n            // Check each className given, space separated list\n            if (self.hasClass(className)) {\n              self.removeClass(className);\n            } else {\n              self.addClass(className);\n            }\n          }\n\n          // Toggle whole class name\n        } else if (value === undefined || type === \"boolean\") {\n          className = getClass(this);\n          if (className) {\n            // Store className if set\n            dataPriv.set(this, \"__className__\", className);\n          }\n\n          // If the element has a class name or if we're passed `false`,\n          // then remove the whole classname (if there was one, the above saved it).\n          // Otherwise bring back whatever was previously saved (if anything),\n          // falling back to the empty string if nothing was stored.\n          if (this.setAttribute) {\n            this.setAttribute(\"class\", className || value === false ? \"\" : dataPriv.get(this, \"__className__\") || \"\");\n          }\n        }\n      });\n    },\n    hasClass: function (selector) {\n      var className,\n        elem,\n        i = 0;\n      className = \" \" + selector + \" \";\n      while (elem = this[i++]) {\n        if (elem.nodeType === 1 && (\" \" + stripAndCollapse(getClass(elem)) + \" \").indexOf(className) > -1) {\n          return true;\n        }\n      }\n      return false;\n    }\n  });\n  var rreturn = /\\r/g;\n  jQuery.fn.extend({\n    val: function (value) {\n      var hooks,\n        ret,\n        valueIsFunction,\n        elem = this[0];\n      if (!arguments.length) {\n        if (elem) {\n          hooks = jQuery.valHooks[elem.type] || jQuery.valHooks[elem.nodeName.toLowerCase()];\n          if (hooks && \"get\" in hooks && (ret = hooks.get(elem, \"value\")) !== undefined) {\n            return ret;\n          }\n          ret = elem.value;\n\n          // Handle most common string cases\n          if (typeof ret === \"string\") {\n            return ret.replace(rreturn, \"\");\n          }\n\n          // Handle cases where value is null/undef or number\n          return ret == null ? \"\" : ret;\n        }\n        return;\n      }\n      valueIsFunction = isFunction(value);\n      return this.each(function (i) {\n        var val;\n        if (this.nodeType !== 1) {\n          return;\n        }\n        if (valueIsFunction) {\n          val = value.call(this, i, jQuery(this).val());\n        } else {\n          val = value;\n        }\n\n        // Treat null/undefined as \"\"; convert numbers to string\n        if (val == null) {\n          val = \"\";\n        } else if (typeof val === \"number\") {\n          val += \"\";\n        } else if (Array.isArray(val)) {\n          val = jQuery.map(val, function (value) {\n            return value == null ? \"\" : value + \"\";\n          });\n        }\n        hooks = jQuery.valHooks[this.type] || jQuery.valHooks[this.nodeName.toLowerCase()];\n\n        // If set returns undefined, fall back to normal setting\n        if (!hooks || !(\"set\" in hooks) || hooks.set(this, val, \"value\") === undefined) {\n          this.value = val;\n        }\n      });\n    }\n  });\n  jQuery.extend({\n    valHooks: {\n      option: {\n        get: function (elem) {\n          var val = jQuery.find.attr(elem, \"value\");\n          return val != null ? val :\n          // Support: IE <=10 - 11 only\n          // option.text throws exceptions (trac-14686, trac-14858)\n          // Strip and collapse whitespace\n          // https://html.spec.whatwg.org/#strip-and-collapse-whitespace\n          stripAndCollapse(jQuery.text(elem));\n        }\n      },\n      select: {\n        get: function (elem) {\n          var value,\n            option,\n            i,\n            options = elem.options,\n            index = elem.selectedIndex,\n            one = elem.type === \"select-one\",\n            values = one ? null : [],\n            max = one ? index + 1 : options.length;\n          if (index < 0) {\n            i = max;\n          } else {\n            i = one ? index : 0;\n          }\n\n          // Loop through all the selected options\n          for (; i < max; i++) {\n            option = options[i];\n\n            // Support: IE <=9 only\n            // IE8-9 doesn't update selected after form reset (trac-2551)\n            if ((option.selected || i === index) &&\n            // Don't return options that are disabled or in a disabled optgroup\n            !option.disabled && (!option.parentNode.disabled || !nodeName(option.parentNode, \"optgroup\"))) {\n              // Get the specific value for the option\n              value = jQuery(option).val();\n\n              // We don't need an array for one selects\n              if (one) {\n                return value;\n              }\n\n              // Multi-Selects return an array\n              values.push(value);\n            }\n          }\n          return values;\n        },\n        set: function (elem, value) {\n          var optionSet,\n            option,\n            options = elem.options,\n            values = jQuery.makeArray(value),\n            i = options.length;\n          while (i--) {\n            option = options[i];\n\n            /* eslint-disable no-cond-assign */\n\n            if (option.selected = jQuery.inArray(jQuery.valHooks.option.get(option), values) > -1) {\n              optionSet = true;\n            }\n\n            /* eslint-enable no-cond-assign */\n          }\n\n          // Force browsers to behave consistently when non-matching value is set\n          if (!optionSet) {\n            elem.selectedIndex = -1;\n          }\n          return values;\n        }\n      }\n    }\n  });\n\n  // Radios and checkboxes getter/setter\n  jQuery.each([\"radio\", \"checkbox\"], function () {\n    jQuery.valHooks[this] = {\n      set: function (elem, value) {\n        if (Array.isArray(value)) {\n          return elem.checked = jQuery.inArray(jQuery(elem).val(), value) > -1;\n        }\n      }\n    };\n    if (!support.checkOn) {\n      jQuery.valHooks[this].get = function (elem) {\n        return elem.getAttribute(\"value\") === null ? \"on\" : elem.value;\n      };\n    }\n  });\n\n  // Return jQuery for attributes-only inclusion\n  var location = window.location;\n  var nonce = {\n    guid: Date.now()\n  };\n  var rquery = /\\?/;\n\n  // Cross-browser xml parsing\n  jQuery.parseXML = function (data) {\n    var xml, parserErrorElem;\n    if (!data || typeof data !== \"string\") {\n      return null;\n    }\n\n    // Support: IE 9 - 11 only\n    // IE throws on parseFromString with invalid input.\n    try {\n      xml = new window.DOMParser().parseFromString(data, \"text/xml\");\n    } catch (e) {}\n    parserErrorElem = xml && xml.getElementsByTagName(\"parsererror\")[0];\n    if (!xml || parserErrorElem) {\n      jQuery.error(\"Invalid XML: \" + (parserErrorElem ? jQuery.map(parserErrorElem.childNodes, function (el) {\n        return el.textContent;\n      }).join(\"\\n\") : data));\n    }\n    return xml;\n  };\n  var rfocusMorph = /^(?:focusinfocus|focusoutblur)$/,\n    stopPropagationCallback = function (e) {\n      e.stopPropagation();\n    };\n  jQuery.extend(jQuery.event, {\n    trigger: function (event, data, elem, onlyHandlers) {\n      var i,\n        cur,\n        tmp,\n        bubbleType,\n        ontype,\n        handle,\n        special,\n        lastElement,\n        eventPath = [elem || document],\n        type = hasOwn.call(event, \"type\") ? event.type : event,\n        namespaces = hasOwn.call(event, \"namespace\") ? event.namespace.split(\".\") : [];\n      cur = lastElement = tmp = elem = elem || document;\n\n      // Don't do events on text and comment nodes\n      if (elem.nodeType === 3 || elem.nodeType === 8) {\n        return;\n      }\n\n      // focus/blur morphs to focusin/out; ensure we're not firing them right now\n      if (rfocusMorph.test(type + jQuery.event.triggered)) {\n        return;\n      }\n      if (type.indexOf(\".\") > -1) {\n        // Namespaced trigger; create a regexp to match event type in handle()\n        namespaces = type.split(\".\");\n        type = namespaces.shift();\n        namespaces.sort();\n      }\n      ontype = type.indexOf(\":\") < 0 && \"on\" + type;\n\n      // Caller can pass in a jQuery.Event object, Object, or just an event type string\n      event = event[jQuery.expando] ? event : new jQuery.Event(type, typeof event === \"object\" && event);\n\n      // Trigger bitmask: & 1 for native handlers; & 2 for jQuery (always true)\n      event.isTrigger = onlyHandlers ? 2 : 3;\n      event.namespace = namespaces.join(\".\");\n      event.rnamespace = event.namespace ? new RegExp(\"(^|\\\\.)\" + namespaces.join(\"\\\\.(?:.*\\\\.|)\") + \"(\\\\.|$)\") : null;\n\n      // Clean up the event in case it is being reused\n      event.result = undefined;\n      if (!event.target) {\n        event.target = elem;\n      }\n\n      // Clone any incoming data and prepend the event, creating the handler arg list\n      data = data == null ? [event] : jQuery.makeArray(data, [event]);\n\n      // Allow special events to draw outside the lines\n      special = jQuery.event.special[type] || {};\n      if (!onlyHandlers && special.trigger && special.trigger.apply(elem, data) === false) {\n        return;\n      }\n\n      // Determine event propagation path in advance, per W3C events spec (trac-9951)\n      // Bubble up to document, then to window; watch for a global ownerDocument var (trac-9724)\n      if (!onlyHandlers && !special.noBubble && !isWindow(elem)) {\n        bubbleType = special.delegateType || type;\n        if (!rfocusMorph.test(bubbleType + type)) {\n          cur = cur.parentNode;\n        }\n        for (; cur; cur = cur.parentNode) {\n          eventPath.push(cur);\n          tmp = cur;\n        }\n\n        // Only add window if we got to document (e.g., not plain obj or detached DOM)\n        if (tmp === (elem.ownerDocument || document)) {\n          eventPath.push(tmp.defaultView || tmp.parentWindow || window);\n        }\n      }\n\n      // Fire handlers on the event path\n      i = 0;\n      while ((cur = eventPath[i++]) && !event.isPropagationStopped()) {\n        lastElement = cur;\n        event.type = i > 1 ? bubbleType : special.bindType || type;\n\n        // jQuery handler\n        handle = (dataPriv.get(cur, \"events\") || Object.create(null))[event.type] && dataPriv.get(cur, \"handle\");\n        if (handle) {\n          handle.apply(cur, data);\n        }\n\n        // Native handler\n        handle = ontype && cur[ontype];\n        if (handle && handle.apply && acceptData(cur)) {\n          event.result = handle.apply(cur, data);\n          if (event.result === false) {\n            event.preventDefault();\n          }\n        }\n      }\n      event.type = type;\n\n      // If nobody prevented the default action, do it now\n      if (!onlyHandlers && !event.isDefaultPrevented()) {\n        if ((!special._default || special._default.apply(eventPath.pop(), data) === false) && acceptData(elem)) {\n          // Call a native DOM method on the target with the same name as the event.\n          // Don't do default actions on window, that's where global variables be (trac-6170)\n          if (ontype && isFunction(elem[type]) && !isWindow(elem)) {\n            // Don't re-trigger an onFOO event when we call its FOO() method\n            tmp = elem[ontype];\n            if (tmp) {\n              elem[ontype] = null;\n            }\n\n            // Prevent re-triggering of the same event, since we already bubbled it above\n            jQuery.event.triggered = type;\n            if (event.isPropagationStopped()) {\n              lastElement.addEventListener(type, stopPropagationCallback);\n            }\n            elem[type]();\n            if (event.isPropagationStopped()) {\n              lastElement.removeEventListener(type, stopPropagationCallback);\n            }\n            jQuery.event.triggered = undefined;\n            if (tmp) {\n              elem[ontype] = tmp;\n            }\n          }\n        }\n      }\n      return event.result;\n    },\n    // Piggyback on a donor event to simulate a different one\n    // Used only for `focus(in | out)` events\n    simulate: function (type, elem, event) {\n      var e = jQuery.extend(new jQuery.Event(), event, {\n        type: type,\n        isSimulated: true\n      });\n      jQuery.event.trigger(e, null, elem);\n    }\n  });\n  jQuery.fn.extend({\n    trigger: function (type, data) {\n      return this.each(function () {\n        jQuery.event.trigger(type, data, this);\n      });\n    },\n    triggerHandler: function (type, data) {\n      var elem = this[0];\n      if (elem) {\n        return jQuery.event.trigger(type, data, elem, true);\n      }\n    }\n  });\n  var rbracket = /\\[\\]$/,\n    rCRLF = /\\r?\\n/g,\n    rsubmitterTypes = /^(?:submit|button|image|reset|file)$/i,\n    rsubmittable = /^(?:input|select|textarea|keygen)/i;\n  function buildParams(prefix, obj, traditional, add) {\n    var name;\n    if (Array.isArray(obj)) {\n      // Serialize array item.\n      jQuery.each(obj, function (i, v) {\n        if (traditional || rbracket.test(prefix)) {\n          // Treat each array item as a scalar.\n          add(prefix, v);\n        } else {\n          // Item is non-scalar (array or object), encode its numeric index.\n          buildParams(prefix + \"[\" + (typeof v === \"object\" && v != null ? i : \"\") + \"]\", v, traditional, add);\n        }\n      });\n    } else if (!traditional && toType(obj) === \"object\") {\n      // Serialize object item.\n      for (name in obj) {\n        buildParams(prefix + \"[\" + name + \"]\", obj[name], traditional, add);\n      }\n    } else {\n      // Serialize scalar item.\n      add(prefix, obj);\n    }\n  }\n\n  // Serialize an array of form elements or a set of\n  // key/values into a query string\n  jQuery.param = function (a, traditional) {\n    var prefix,\n      s = [],\n      add = function (key, valueOrFunction) {\n        // If value is a function, invoke it and use its return value\n        var value = isFunction(valueOrFunction) ? valueOrFunction() : valueOrFunction;\n        s[s.length] = encodeURIComponent(key) + \"=\" + encodeURIComponent(value == null ? \"\" : value);\n      };\n    if (a == null) {\n      return \"\";\n    }\n\n    // If an array was passed in, assume that it is an array of form elements.\n    if (Array.isArray(a) || a.jquery && !jQuery.isPlainObject(a)) {\n      // Serialize the form elements\n      jQuery.each(a, function () {\n        add(this.name, this.value);\n      });\n    } else {\n      // If traditional, encode the \"old\" way (the way 1.3.2 or older\n      // did it), otherwise encode params recursively.\n      for (prefix in a) {\n        buildParams(prefix, a[prefix], traditional, add);\n      }\n    }\n\n    // Return the resulting serialization\n    return s.join(\"&\");\n  };\n  jQuery.fn.extend({\n    serialize: function () {\n      return jQuery.param(this.serializeArray());\n    },\n    serializeArray: function () {\n      return this.map(function () {\n        // Can add propHook for \"elements\" to filter or add form elements\n        var elements = jQuery.prop(this, \"elements\");\n        return elements ? jQuery.makeArray(elements) : this;\n      }).filter(function () {\n        var type = this.type;\n\n        // Use .is( \":disabled\" ) so that fieldset[disabled] works\n        return this.name && !jQuery(this).is(\":disabled\") && rsubmittable.test(this.nodeName) && !rsubmitterTypes.test(type) && (this.checked || !rcheckableType.test(type));\n      }).map(function (_i, elem) {\n        var val = jQuery(this).val();\n        if (val == null) {\n          return null;\n        }\n        if (Array.isArray(val)) {\n          return jQuery.map(val, function (val) {\n            return {\n              name: elem.name,\n              value: val.replace(rCRLF, \"\\r\\n\")\n            };\n          });\n        }\n        return {\n          name: elem.name,\n          value: val.replace(rCRLF, \"\\r\\n\")\n        };\n      }).get();\n    }\n  });\n  var r20 = /%20/g,\n    rhash = /#.*$/,\n    rantiCache = /([?&])_=[^&]*/,\n    rheaders = /^(.*?):[ \\t]*([^\\r\\n]*)$/mg,\n    // trac-7653, trac-8125, trac-8152: local protocol detection\n    rlocalProtocol = /^(?:about|app|app-storage|.+-extension|file|res|widget):$/,\n    rnoContent = /^(?:GET|HEAD)$/,\n    rprotocol = /^\\/\\//,\n    /* Prefilters\n     * 1) They are useful to introduce custom dataTypes (see ajax/jsonp.js for an example)\n     * 2) These are called:\n     *    - BEFORE asking for a transport\n     *    - AFTER param serialization (s.data is a string if s.processData is true)\n     * 3) key is the dataType\n     * 4) the catchall symbol \"*\" can be used\n     * 5) execution will start with transport dataType and THEN continue down to \"*\" if needed\n     */\n    prefilters = {},\n    /* Transports bindings\n     * 1) key is the dataType\n     * 2) the catchall symbol \"*\" can be used\n     * 3) selection will start with transport dataType and THEN go to \"*\" if needed\n     */\n    transports = {},\n    // Avoid comment-prolog char sequence (trac-10098); must appease lint and evade compression\n    allTypes = \"*/\".concat(\"*\"),\n    // Anchor tag for parsing the document origin\n    originAnchor = document.createElement(\"a\");\n  originAnchor.href = location.href;\n\n  // Base \"constructor\" for jQuery.ajaxPrefilter and jQuery.ajaxTransport\n  function addToPrefiltersOrTransports(structure) {\n    // dataTypeExpression is optional and defaults to \"*\"\n    return function (dataTypeExpression, func) {\n      if (typeof dataTypeExpression !== \"string\") {\n        func = dataTypeExpression;\n        dataTypeExpression = \"*\";\n      }\n      var dataType,\n        i = 0,\n        dataTypes = dataTypeExpression.toLowerCase().match(rnothtmlwhite) || [];\n      if (isFunction(func)) {\n        // For each dataType in the dataTypeExpression\n        while (dataType = dataTypes[i++]) {\n          // Prepend if requested\n          if (dataType[0] === \"+\") {\n            dataType = dataType.slice(1) || \"*\";\n            (structure[dataType] = structure[dataType] || []).unshift(func);\n\n            // Otherwise append\n          } else {\n            (structure[dataType] = structure[dataType] || []).push(func);\n          }\n        }\n      }\n    };\n  }\n\n  // Base inspection function for prefilters and transports\n  function inspectPrefiltersOrTransports(structure, options, originalOptions, jqXHR) {\n    var inspected = {},\n      seekingTransport = structure === transports;\n    function inspect(dataType) {\n      var selected;\n      inspected[dataType] = true;\n      jQuery.each(structure[dataType] || [], function (_, prefilterOrFactory) {\n        var dataTypeOrTransport = prefilterOrFactory(options, originalOptions, jqXHR);\n        if (typeof dataTypeOrTransport === \"string\" && !seekingTransport && !inspected[dataTypeOrTransport]) {\n          options.dataTypes.unshift(dataTypeOrTransport);\n          inspect(dataTypeOrTransport);\n          return false;\n        } else if (seekingTransport) {\n          return !(selected = dataTypeOrTransport);\n        }\n      });\n      return selected;\n    }\n    return inspect(options.dataTypes[0]) || !inspected[\"*\"] && inspect(\"*\");\n  }\n\n  // A special extend for ajax options\n  // that takes \"flat\" options (not to be deep extended)\n  // Fixes trac-9887\n  function ajaxExtend(target, src) {\n    var key,\n      deep,\n      flatOptions = jQuery.ajaxSettings.flatOptions || {};\n    for (key in src) {\n      if (src[key] !== undefined) {\n        (flatOptions[key] ? target : deep || (deep = {}))[key] = src[key];\n      }\n    }\n    if (deep) {\n      jQuery.extend(true, target, deep);\n    }\n    return target;\n  }\n\n  /* Handles responses to an ajax request:\n   * - finds the right dataType (mediates between content-type and expected dataType)\n   * - returns the corresponding response\n   */\n  function ajaxHandleResponses(s, jqXHR, responses) {\n    var ct,\n      type,\n      finalDataType,\n      firstDataType,\n      contents = s.contents,\n      dataTypes = s.dataTypes;\n\n    // Remove auto dataType and get content-type in the process\n    while (dataTypes[0] === \"*\") {\n      dataTypes.shift();\n      if (ct === undefined) {\n        ct = s.mimeType || jqXHR.getResponseHeader(\"Content-Type\");\n      }\n    }\n\n    // Check if we're dealing with a known content-type\n    if (ct) {\n      for (type in contents) {\n        if (contents[type] && contents[type].test(ct)) {\n          dataTypes.unshift(type);\n          break;\n        }\n      }\n    }\n\n    // Check to see if we have a response for the expected dataType\n    if (dataTypes[0] in responses) {\n      finalDataType = dataTypes[0];\n    } else {\n      // Try convertible dataTypes\n      for (type in responses) {\n        if (!dataTypes[0] || s.converters[type + \" \" + dataTypes[0]]) {\n          finalDataType = type;\n          break;\n        }\n        if (!firstDataType) {\n          firstDataType = type;\n        }\n      }\n\n      // Or just use first one\n      finalDataType = finalDataType || firstDataType;\n    }\n\n    // If we found a dataType\n    // We add the dataType to the list if needed\n    // and return the corresponding response\n    if (finalDataType) {\n      if (finalDataType !== dataTypes[0]) {\n        dataTypes.unshift(finalDataType);\n      }\n      return responses[finalDataType];\n    }\n  }\n\n  /* Chain conversions given the request and the original response\n   * Also sets the responseXXX fields on the jqXHR instance\n   */\n  function ajaxConvert(s, response, jqXHR, isSuccess) {\n    var conv2,\n      current,\n      conv,\n      tmp,\n      prev,\n      converters = {},\n      // Work with a copy of dataTypes in case we need to modify it for conversion\n      dataTypes = s.dataTypes.slice();\n\n    // Create converters map with lowercased keys\n    if (dataTypes[1]) {\n      for (conv in s.converters) {\n        converters[conv.toLowerCase()] = s.converters[conv];\n      }\n    }\n    current = dataTypes.shift();\n\n    // Convert to each sequential dataType\n    while (current) {\n      if (s.responseFields[current]) {\n        jqXHR[s.responseFields[current]] = response;\n      }\n\n      // Apply the dataFilter if provided\n      if (!prev && isSuccess && s.dataFilter) {\n        response = s.dataFilter(response, s.dataType);\n      }\n      prev = current;\n      current = dataTypes.shift();\n      if (current) {\n        // There's only work to do if current dataType is non-auto\n        if (current === \"*\") {\n          current = prev;\n\n          // Convert response if prev dataType is non-auto and differs from current\n        } else if (prev !== \"*\" && prev !== current) {\n          // Seek a direct converter\n          conv = converters[prev + \" \" + current] || converters[\"* \" + current];\n\n          // If none found, seek a pair\n          if (!conv) {\n            for (conv2 in converters) {\n              // If conv2 outputs current\n              tmp = conv2.split(\" \");\n              if (tmp[1] === current) {\n                // If prev can be converted to accepted input\n                conv = converters[prev + \" \" + tmp[0]] || converters[\"* \" + tmp[0]];\n                if (conv) {\n                  // Condense equivalence converters\n                  if (conv === true) {\n                    conv = converters[conv2];\n\n                    // Otherwise, insert the intermediate dataType\n                  } else if (converters[conv2] !== true) {\n                    current = tmp[0];\n                    dataTypes.unshift(tmp[1]);\n                  }\n                  break;\n                }\n              }\n            }\n          }\n\n          // Apply converter (if not an equivalence)\n          if (conv !== true) {\n            // Unless errors are allowed to bubble, catch and return them\n            if (conv && s.throws) {\n              response = conv(response);\n            } else {\n              try {\n                response = conv(response);\n              } catch (e) {\n                return {\n                  state: \"parsererror\",\n                  error: conv ? e : \"No conversion from \" + prev + \" to \" + current\n                };\n              }\n            }\n          }\n        }\n      }\n    }\n    return {\n      state: \"success\",\n      data: response\n    };\n  }\n  jQuery.extend({\n    // Counter for holding the number of active queries\n    active: 0,\n    // Last-Modified header cache for next request\n    lastModified: {},\n    etag: {},\n    ajaxSettings: {\n      url: location.href,\n      type: \"GET\",\n      isLocal: rlocalProtocol.test(location.protocol),\n      global: true,\n      processData: true,\n      async: true,\n      contentType: \"application/x-www-form-urlencoded; charset=UTF-8\",\n      /*\n      timeout: 0,\n      data: null,\n      dataType: null,\n      username: null,\n      password: null,\n      cache: null,\n      throws: false,\n      traditional: false,\n      headers: {},\n      */\n\n      accepts: {\n        \"*\": allTypes,\n        text: \"text/plain\",\n        html: \"text/html\",\n        xml: \"application/xml, text/xml\",\n        json: \"application/json, text/javascript\"\n      },\n      contents: {\n        xml: /\\bxml\\b/,\n        html: /\\bhtml/,\n        json: /\\bjson\\b/\n      },\n      responseFields: {\n        xml: \"responseXML\",\n        text: \"responseText\",\n        json: \"responseJSON\"\n      },\n      // Data converters\n      // Keys separate source (or catchall \"*\") and destination types with a single space\n      converters: {\n        // Convert anything to text\n        \"* text\": String,\n        // Text to html (true = no transformation)\n        \"text html\": true,\n        // Evaluate text as a json expression\n        \"text json\": JSON.parse,\n        // Parse text as xml\n        \"text xml\": jQuery.parseXML\n      },\n      // For options that shouldn't be deep extended:\n      // you can add your own custom options here if\n      // and when you create one that shouldn't be\n      // deep extended (see ajaxExtend)\n      flatOptions: {\n        url: true,\n        context: true\n      }\n    },\n    // Creates a full fledged settings object into target\n    // with both ajaxSettings and settings fields.\n    // If target is omitted, writes into ajaxSettings.\n    ajaxSetup: function (target, settings) {\n      return settings ?\n      // Building a settings object\n      ajaxExtend(ajaxExtend(target, jQuery.ajaxSettings), settings) :\n      // Extending ajaxSettings\n      ajaxExtend(jQuery.ajaxSettings, target);\n    },\n    ajaxPrefilter: addToPrefiltersOrTransports(prefilters),\n    ajaxTransport: addToPrefiltersOrTransports(transports),\n    // Main method\n    ajax: function (url, options) {\n      // If url is an object, simulate pre-1.5 signature\n      if (typeof url === \"object\") {\n        options = url;\n        url = undefined;\n      }\n\n      // Force options to be an object\n      options = options || {};\n      var transport,\n        // URL without anti-cache param\n        cacheURL,\n        // Response headers\n        responseHeadersString,\n        responseHeaders,\n        // timeout handle\n        timeoutTimer,\n        // Url cleanup var\n        urlAnchor,\n        // Request state (becomes false upon send and true upon completion)\n        completed,\n        // To know if global events are to be dispatched\n        fireGlobals,\n        // Loop variable\n        i,\n        // uncached part of the url\n        uncached,\n        // Create the final options object\n        s = jQuery.ajaxSetup({}, options),\n        // Callbacks context\n        callbackContext = s.context || s,\n        // Context for global events is callbackContext if it is a DOM node or jQuery collection\n        globalEventContext = s.context && (callbackContext.nodeType || callbackContext.jquery) ? jQuery(callbackContext) : jQuery.event,\n        // Deferreds\n        deferred = jQuery.Deferred(),\n        completeDeferred = jQuery.Callbacks(\"once memory\"),\n        // Status-dependent callbacks\n        statusCode = s.statusCode || {},\n        // Headers (they are sent all at once)\n        requestHeaders = {},\n        requestHeadersNames = {},\n        // Default abort message\n        strAbort = \"canceled\",\n        // Fake xhr\n        jqXHR = {\n          readyState: 0,\n          // Builds headers hashtable if needed\n          getResponseHeader: function (key) {\n            var match;\n            if (completed) {\n              if (!responseHeaders) {\n                responseHeaders = {};\n                while (match = rheaders.exec(responseHeadersString)) {\n                  responseHeaders[match[1].toLowerCase() + \" \"] = (responseHeaders[match[1].toLowerCase() + \" \"] || []).concat(match[2]);\n                }\n              }\n              match = responseHeaders[key.toLowerCase() + \" \"];\n            }\n            return match == null ? null : match.join(\", \");\n          },\n          // Raw string\n          getAllResponseHeaders: function () {\n            return completed ? responseHeadersString : null;\n          },\n          // Caches the header\n          setRequestHeader: function (name, value) {\n            if (completed == null) {\n              name = requestHeadersNames[name.toLowerCase()] = requestHeadersNames[name.toLowerCase()] || name;\n              requestHeaders[name] = value;\n            }\n            return this;\n          },\n          // Overrides response content-type header\n          overrideMimeType: function (type) {\n            if (completed == null) {\n              s.mimeType = type;\n            }\n            return this;\n          },\n          // Status-dependent callbacks\n          statusCode: function (map) {\n            var code;\n            if (map) {\n              if (completed) {\n                // Execute the appropriate callbacks\n                jqXHR.always(map[jqXHR.status]);\n              } else {\n                // Lazy-add the new callbacks in a way that preserves old ones\n                for (code in map) {\n                  statusCode[code] = [statusCode[code], map[code]];\n                }\n              }\n            }\n            return this;\n          },\n          // Cancel the request\n          abort: function (statusText) {\n            var finalText = statusText || strAbort;\n            if (transport) {\n              transport.abort(finalText);\n            }\n            done(0, finalText);\n            return this;\n          }\n        };\n\n      // Attach deferreds\n      deferred.promise(jqXHR);\n\n      // Add protocol if not provided (prefilters might expect it)\n      // Handle falsy url in the settings object (trac-10093: consistency with old signature)\n      // We also use the url parameter if available\n      s.url = ((url || s.url || location.href) + \"\").replace(rprotocol, location.protocol + \"//\");\n\n      // Alias method option to type as per ticket trac-12004\n      s.type = options.method || options.type || s.method || s.type;\n\n      // Extract dataTypes list\n      s.dataTypes = (s.dataType || \"*\").toLowerCase().match(rnothtmlwhite) || [\"\"];\n\n      // A cross-domain request is in order when the origin doesn't match the current origin.\n      if (s.crossDomain == null) {\n        urlAnchor = document.createElement(\"a\");\n\n        // Support: IE <=8 - 11, Edge 12 - 15\n        // IE throws exception on accessing the href property if url is malformed,\n        // e.g. http://example.com:80x/\n        try {\n          urlAnchor.href = s.url;\n\n          // Support: IE <=8 - 11 only\n          // Anchor's host property isn't correctly set when s.url is relative\n          urlAnchor.href = urlAnchor.href;\n          s.crossDomain = originAnchor.protocol + \"//\" + originAnchor.host !== urlAnchor.protocol + \"//\" + urlAnchor.host;\n        } catch (e) {\n          // If there is an error parsing the URL, assume it is crossDomain,\n          // it can be rejected by the transport if it is invalid\n          s.crossDomain = true;\n        }\n      }\n\n      // Convert data if not already a string\n      if (s.data && s.processData && typeof s.data !== \"string\") {\n        s.data = jQuery.param(s.data, s.traditional);\n      }\n\n      // Apply prefilters\n      inspectPrefiltersOrTransports(prefilters, s, options, jqXHR);\n\n      // If request was aborted inside a prefilter, stop there\n      if (completed) {\n        return jqXHR;\n      }\n\n      // We can fire global events as of now if asked to\n      // Don't fire events if jQuery.event is undefined in an AMD-usage scenario (trac-15118)\n      fireGlobals = jQuery.event && s.global;\n\n      // Watch for a new set of requests\n      if (fireGlobals && jQuery.active++ === 0) {\n        jQuery.event.trigger(\"ajaxStart\");\n      }\n\n      // Uppercase the type\n      s.type = s.type.toUpperCase();\n\n      // Determine if request has content\n      s.hasContent = !rnoContent.test(s.type);\n\n      // Save the URL in case we're toying with the If-Modified-Since\n      // and/or If-None-Match header later on\n      // Remove hash to simplify url manipulation\n      cacheURL = s.url.replace(rhash, \"\");\n\n      // More options handling for requests with no content\n      if (!s.hasContent) {\n        // Remember the hash so we can put it back\n        uncached = s.url.slice(cacheURL.length);\n\n        // If data is available and should be processed, append data to url\n        if (s.data && (s.processData || typeof s.data === \"string\")) {\n          cacheURL += (rquery.test(cacheURL) ? \"&\" : \"?\") + s.data;\n\n          // trac-9682: remove data so that it's not used in an eventual retry\n          delete s.data;\n        }\n\n        // Add or update anti-cache param if needed\n        if (s.cache === false) {\n          cacheURL = cacheURL.replace(rantiCache, \"$1\");\n          uncached = (rquery.test(cacheURL) ? \"&\" : \"?\") + \"_=\" + nonce.guid++ + uncached;\n        }\n\n        // Put hash and anti-cache on the URL that will be requested (gh-1732)\n        s.url = cacheURL + uncached;\n\n        // Change '%20' to '+' if this is encoded form body content (gh-2658)\n      } else if (s.data && s.processData && (s.contentType || \"\").indexOf(\"application/x-www-form-urlencoded\") === 0) {\n        s.data = s.data.replace(r20, \"+\");\n      }\n\n      // Set the If-Modified-Since and/or If-None-Match header, if in ifModified mode.\n      if (s.ifModified) {\n        if (jQuery.lastModified[cacheURL]) {\n          jqXHR.setRequestHeader(\"If-Modified-Since\", jQuery.lastModified[cacheURL]);\n        }\n        if (jQuery.etag[cacheURL]) {\n          jqXHR.setRequestHeader(\"If-None-Match\", jQuery.etag[cacheURL]);\n        }\n      }\n\n      // Set the correct header, if data is being sent\n      if (s.data && s.hasContent && s.contentType !== false || options.contentType) {\n        jqXHR.setRequestHeader(\"Content-Type\", s.contentType);\n      }\n\n      // Set the Accepts header for the server, depending on the dataType\n      jqXHR.setRequestHeader(\"Accept\", s.dataTypes[0] && s.accepts[s.dataTypes[0]] ? s.accepts[s.dataTypes[0]] + (s.dataTypes[0] !== \"*\" ? \", \" + allTypes + \"; q=0.01\" : \"\") : s.accepts[\"*\"]);\n\n      // Check for headers option\n      for (i in s.headers) {\n        jqXHR.setRequestHeader(i, s.headers[i]);\n      }\n\n      // Allow custom headers/mimetypes and early abort\n      if (s.beforeSend && (s.beforeSend.call(callbackContext, jqXHR, s) === false || completed)) {\n        // Abort if not done already and return\n        return jqXHR.abort();\n      }\n\n      // Aborting is no longer a cancellation\n      strAbort = \"abort\";\n\n      // Install callbacks on deferreds\n      completeDeferred.add(s.complete);\n      jqXHR.done(s.success);\n      jqXHR.fail(s.error);\n\n      // Get transport\n      transport = inspectPrefiltersOrTransports(transports, s, options, jqXHR);\n\n      // If no transport, we auto-abort\n      if (!transport) {\n        done(-1, \"No Transport\");\n      } else {\n        jqXHR.readyState = 1;\n\n        // Send global event\n        if (fireGlobals) {\n          globalEventContext.trigger(\"ajaxSend\", [jqXHR, s]);\n        }\n\n        // If request was aborted inside ajaxSend, stop there\n        if (completed) {\n          return jqXHR;\n        }\n\n        // Timeout\n        if (s.async && s.timeout > 0) {\n          timeoutTimer = window.setTimeout(function () {\n            jqXHR.abort(\"timeout\");\n          }, s.timeout);\n        }\n        try {\n          completed = false;\n          transport.send(requestHeaders, done);\n        } catch (e) {\n          // Rethrow post-completion exceptions\n          if (completed) {\n            throw e;\n          }\n\n          // Propagate others as results\n          done(-1, e);\n        }\n      }\n\n      // Callback for when everything is done\n      function done(status, nativeStatusText, responses, headers) {\n        var isSuccess,\n          success,\n          error,\n          response,\n          modified,\n          statusText = nativeStatusText;\n\n        // Ignore repeat invocations\n        if (completed) {\n          return;\n        }\n        completed = true;\n\n        // Clear timeout if it exists\n        if (timeoutTimer) {\n          window.clearTimeout(timeoutTimer);\n        }\n\n        // Dereference transport for early garbage collection\n        // (no matter how long the jqXHR object will be used)\n        transport = undefined;\n\n        // Cache response headers\n        responseHeadersString = headers || \"\";\n\n        // Set readyState\n        jqXHR.readyState = status > 0 ? 4 : 0;\n\n        // Determine if successful\n        isSuccess = status >= 200 && status < 300 || status === 304;\n\n        // Get response data\n        if (responses) {\n          response = ajaxHandleResponses(s, jqXHR, responses);\n        }\n\n        // Use a noop converter for missing script but not if jsonp\n        if (!isSuccess && jQuery.inArray(\"script\", s.dataTypes) > -1 && jQuery.inArray(\"json\", s.dataTypes) < 0) {\n          s.converters[\"text script\"] = function () {};\n        }\n\n        // Convert no matter what (that way responseXXX fields are always set)\n        response = ajaxConvert(s, response, jqXHR, isSuccess);\n\n        // If successful, handle type chaining\n        if (isSuccess) {\n          // Set the If-Modified-Since and/or If-None-Match header, if in ifModified mode.\n          if (s.ifModified) {\n            modified = jqXHR.getResponseHeader(\"Last-Modified\");\n            if (modified) {\n              jQuery.lastModified[cacheURL] = modified;\n            }\n            modified = jqXHR.getResponseHeader(\"etag\");\n            if (modified) {\n              jQuery.etag[cacheURL] = modified;\n            }\n          }\n\n          // if no content\n          if (status === 204 || s.type === \"HEAD\") {\n            statusText = \"nocontent\";\n\n            // if not modified\n          } else if (status === 304) {\n            statusText = \"notmodified\";\n\n            // If we have data, let's convert it\n          } else {\n            statusText = response.state;\n            success = response.data;\n            error = response.error;\n            isSuccess = !error;\n          }\n        } else {\n          // Extract error from statusText and normalize for non-aborts\n          error = statusText;\n          if (status || !statusText) {\n            statusText = \"error\";\n            if (status < 0) {\n              status = 0;\n            }\n          }\n        }\n\n        // Set data for the fake xhr object\n        jqXHR.status = status;\n        jqXHR.statusText = (nativeStatusText || statusText) + \"\";\n\n        // Success/Error\n        if (isSuccess) {\n          deferred.resolveWith(callbackContext, [success, statusText, jqXHR]);\n        } else {\n          deferred.rejectWith(callbackContext, [jqXHR, statusText, error]);\n        }\n\n        // Status-dependent callbacks\n        jqXHR.statusCode(statusCode);\n        statusCode = undefined;\n        if (fireGlobals) {\n          globalEventContext.trigger(isSuccess ? \"ajaxSuccess\" : \"ajaxError\", [jqXHR, s, isSuccess ? success : error]);\n        }\n\n        // Complete\n        completeDeferred.fireWith(callbackContext, [jqXHR, statusText]);\n        if (fireGlobals) {\n          globalEventContext.trigger(\"ajaxComplete\", [jqXHR, s]);\n\n          // Handle the global AJAX counter\n          if (! --jQuery.active) {\n            jQuery.event.trigger(\"ajaxStop\");\n          }\n        }\n      }\n      return jqXHR;\n    },\n    getJSON: function (url, data, callback) {\n      return jQuery.get(url, data, callback, \"json\");\n    },\n    getScript: function (url, callback) {\n      return jQuery.get(url, undefined, callback, \"script\");\n    }\n  });\n  jQuery.each([\"get\", \"post\"], function (_i, method) {\n    jQuery[method] = function (url, data, callback, type) {\n      // Shift arguments if data argument was omitted\n      if (isFunction(data)) {\n        type = type || callback;\n        callback = data;\n        data = undefined;\n      }\n\n      // The url can be an options object (which then must have .url)\n      return jQuery.ajax(jQuery.extend({\n        url: url,\n        type: method,\n        dataType: type,\n        data: data,\n        success: callback\n      }, jQuery.isPlainObject(url) && url));\n    };\n  });\n  jQuery.ajaxPrefilter(function (s) {\n    var i;\n    for (i in s.headers) {\n      if (i.toLowerCase() === \"content-type\") {\n        s.contentType = s.headers[i] || \"\";\n      }\n    }\n  });\n  jQuery._evalUrl = function (url, options, doc) {\n    return jQuery.ajax({\n      url: url,\n      // Make this explicit, since user can override this through ajaxSetup (trac-11264)\n      type: \"GET\",\n      dataType: \"script\",\n      cache: true,\n      async: false,\n      global: false,\n      // Only evaluate the response if it is successful (gh-4126)\n      // dataFilter is not invoked for failure responses, so using it instead\n      // of the default converter is kludgy but it works.\n      converters: {\n        \"text script\": function () {}\n      },\n      dataFilter: function (response) {\n        jQuery.globalEval(response, options, doc);\n      }\n    });\n  };\n  jQuery.fn.extend({\n    wrapAll: function (html) {\n      var wrap;\n      if (this[0]) {\n        if (isFunction(html)) {\n          html = html.call(this[0]);\n        }\n\n        // The elements to wrap the target around\n        wrap = jQuery(html, this[0].ownerDocument).eq(0).clone(true);\n        if (this[0].parentNode) {\n          wrap.insertBefore(this[0]);\n        }\n        wrap.map(function () {\n          var elem = this;\n          while (elem.firstElementChild) {\n            elem = elem.firstElementChild;\n          }\n          return elem;\n        }).append(this);\n      }\n      return this;\n    },\n    wrapInner: function (html) {\n      if (isFunction(html)) {\n        return this.each(function (i) {\n          jQuery(this).wrapInner(html.call(this, i));\n        });\n      }\n      return this.each(function () {\n        var self = jQuery(this),\n          contents = self.contents();\n        if (contents.length) {\n          contents.wrapAll(html);\n        } else {\n          self.append(html);\n        }\n      });\n    },\n    wrap: function (html) {\n      var htmlIsFunction = isFunction(html);\n      return this.each(function (i) {\n        jQuery(this).wrapAll(htmlIsFunction ? html.call(this, i) : html);\n      });\n    },\n    unwrap: function (selector) {\n      this.parent(selector).not(\"body\").each(function () {\n        jQuery(this).replaceWith(this.childNodes);\n      });\n      return this;\n    }\n  });\n  jQuery.expr.pseudos.hidden = function (elem) {\n    return !jQuery.expr.pseudos.visible(elem);\n  };\n  jQuery.expr.pseudos.visible = function (elem) {\n    return !!(elem.offsetWidth || elem.offsetHeight || elem.getClientRects().length);\n  };\n  jQuery.ajaxSettings.xhr = function () {\n    try {\n      return new window.XMLHttpRequest();\n    } catch (e) {}\n  };\n  var xhrSuccessStatus = {\n      // File protocol always yields status code 0, assume 200\n      0: 200,\n      // Support: IE <=9 only\n      // trac-1450: sometimes IE returns 1223 when it should be 204\n      1223: 204\n    },\n    xhrSupported = jQuery.ajaxSettings.xhr();\n  support.cors = !!xhrSupported && \"withCredentials\" in xhrSupported;\n  support.ajax = xhrSupported = !!xhrSupported;\n  jQuery.ajaxTransport(function (options) {\n    var callback, errorCallback;\n\n    // Cross domain only allowed if supported through XMLHttpRequest\n    if (support.cors || xhrSupported && !options.crossDomain) {\n      return {\n        send: function (headers, complete) {\n          var i,\n            xhr = options.xhr();\n          xhr.open(options.type, options.url, options.async, options.username, options.password);\n\n          // Apply custom fields if provided\n          if (options.xhrFields) {\n            for (i in options.xhrFields) {\n              xhr[i] = options.xhrFields[i];\n            }\n          }\n\n          // Override mime type if needed\n          if (options.mimeType && xhr.overrideMimeType) {\n            xhr.overrideMimeType(options.mimeType);\n          }\n\n          // X-Requested-With header\n          // For cross-domain requests, seeing as conditions for a preflight are\n          // akin to a jigsaw puzzle, we simply never set it to be sure.\n          // (it can always be set on a per-request basis or even using ajaxSetup)\n          // For same-domain requests, won't change header if already provided.\n          if (!options.crossDomain && !headers[\"X-Requested-With\"]) {\n            headers[\"X-Requested-With\"] = \"XMLHttpRequest\";\n          }\n\n          // Set headers\n          for (i in headers) {\n            xhr.setRequestHeader(i, headers[i]);\n          }\n\n          // Callback\n          callback = function (type) {\n            return function () {\n              if (callback) {\n                callback = errorCallback = xhr.onload = xhr.onerror = xhr.onabort = xhr.ontimeout = xhr.onreadystatechange = null;\n                if (type === \"abort\") {\n                  xhr.abort();\n                } else if (type === \"error\") {\n                  // Support: IE <=9 only\n                  // On a manual native abort, IE9 throws\n                  // errors on any property access that is not readyState\n                  if (typeof xhr.status !== \"number\") {\n                    complete(0, \"error\");\n                  } else {\n                    complete(\n                    // File: protocol always yields status 0; see trac-8605, trac-14207\n                    xhr.status, xhr.statusText);\n                  }\n                } else {\n                  complete(xhrSuccessStatus[xhr.status] || xhr.status, xhr.statusText,\n                  // Support: IE <=9 only\n                  // IE9 has no XHR2 but throws on binary (trac-11426)\n                  // For XHR2 non-text, let the caller handle it (gh-2498)\n                  (xhr.responseType || \"text\") !== \"text\" || typeof xhr.responseText !== \"string\" ? {\n                    binary: xhr.response\n                  } : {\n                    text: xhr.responseText\n                  }, xhr.getAllResponseHeaders());\n                }\n              }\n            };\n          };\n\n          // Listen to events\n          xhr.onload = callback();\n          errorCallback = xhr.onerror = xhr.ontimeout = callback(\"error\");\n\n          // Support: IE 9 only\n          // Use onreadystatechange to replace onabort\n          // to handle uncaught aborts\n          if (xhr.onabort !== undefined) {\n            xhr.onabort = errorCallback;\n          } else {\n            xhr.onreadystatechange = function () {\n              // Check readyState before timeout as it changes\n              if (xhr.readyState === 4) {\n                // Allow onerror to be called first,\n                // but that will not handle a native abort\n                // Also, save errorCallback to a variable\n                // as xhr.onerror cannot be accessed\n                window.setTimeout(function () {\n                  if (callback) {\n                    errorCallback();\n                  }\n                });\n              }\n            };\n          }\n\n          // Create the abort callback\n          callback = callback(\"abort\");\n          try {\n            // Do send the request (this may raise an exception)\n            xhr.send(options.hasContent && options.data || null);\n          } catch (e) {\n            // trac-14683: Only rethrow if this hasn't been notified as an error yet\n            if (callback) {\n              throw e;\n            }\n          }\n        },\n        abort: function () {\n          if (callback) {\n            callback();\n          }\n        }\n      };\n    }\n  });\n\n  // Prevent auto-execution of scripts when no explicit dataType was provided (See gh-2432)\n  jQuery.ajaxPrefilter(function (s) {\n    if (s.crossDomain) {\n      s.contents.script = false;\n    }\n  });\n\n  // Install script dataType\n  jQuery.ajaxSetup({\n    accepts: {\n      script: \"text/javascript, application/javascript, \" + \"application/ecmascript, application/x-ecmascript\"\n    },\n    contents: {\n      script: /\\b(?:java|ecma)script\\b/\n    },\n    converters: {\n      \"text script\": function (text) {\n        jQuery.globalEval(text);\n        return text;\n      }\n    }\n  });\n\n  // Handle cache's special case and crossDomain\n  jQuery.ajaxPrefilter(\"script\", function (s) {\n    if (s.cache === undefined) {\n      s.cache = false;\n    }\n    if (s.crossDomain) {\n      s.type = \"GET\";\n    }\n  });\n\n  // Bind script tag hack transport\n  jQuery.ajaxTransport(\"script\", function (s) {\n    // This transport only deals with cross domain or forced-by-attrs requests\n    if (s.crossDomain || s.scriptAttrs) {\n      var script, callback;\n      return {\n        send: function (_, complete) {\n          script = jQuery(\"<script>\").attr(s.scriptAttrs || {}).prop({\n            charset: s.scriptCharset,\n            src: s.url\n          }).on(\"load error\", callback = function (evt) {\n            script.remove();\n            callback = null;\n            if (evt) {\n              complete(evt.type === \"error\" ? 404 : 200, evt.type);\n            }\n          });\n\n          // Use native DOM manipulation to avoid our domManip AJAX trickery\n          document.head.appendChild(script[0]);\n        },\n        abort: function () {\n          if (callback) {\n            callback();\n          }\n        }\n      };\n    }\n  });\n  var oldCallbacks = [],\n    rjsonp = /(=)\\?(?=&|$)|\\?\\?/;\n\n  // Default jsonp settings\n  jQuery.ajaxSetup({\n    jsonp: \"callback\",\n    jsonpCallback: function () {\n      var callback = oldCallbacks.pop() || jQuery.expando + \"_\" + nonce.guid++;\n      this[callback] = true;\n      return callback;\n    }\n  });\n\n  // Detect, normalize options and install callbacks for jsonp requests\n  jQuery.ajaxPrefilter(\"json jsonp\", function (s, originalSettings, jqXHR) {\n    var callbackName,\n      overwritten,\n      responseContainer,\n      jsonProp = s.jsonp !== false && (rjsonp.test(s.url) ? \"url\" : typeof s.data === \"string\" && (s.contentType || \"\").indexOf(\"application/x-www-form-urlencoded\") === 0 && rjsonp.test(s.data) && \"data\");\n\n    // Handle iff the expected data type is \"jsonp\" or we have a parameter to set\n    if (jsonProp || s.dataTypes[0] === \"jsonp\") {\n      // Get callback name, remembering preexisting value associated with it\n      callbackName = s.jsonpCallback = isFunction(s.jsonpCallback) ? s.jsonpCallback() : s.jsonpCallback;\n\n      // Insert callback into url or form data\n      if (jsonProp) {\n        s[jsonProp] = s[jsonProp].replace(rjsonp, \"$1\" + callbackName);\n      } else if (s.jsonp !== false) {\n        s.url += (rquery.test(s.url) ? \"&\" : \"?\") + s.jsonp + \"=\" + callbackName;\n      }\n\n      // Use data converter to retrieve json after script execution\n      s.converters[\"script json\"] = function () {\n        if (!responseContainer) {\n          jQuery.error(callbackName + \" was not called\");\n        }\n        return responseContainer[0];\n      };\n\n      // Force json dataType\n      s.dataTypes[0] = \"json\";\n\n      // Install callback\n      overwritten = window[callbackName];\n      window[callbackName] = function () {\n        responseContainer = arguments;\n      };\n\n      // Clean-up function (fires after converters)\n      jqXHR.always(function () {\n        // If previous value didn't exist - remove it\n        if (overwritten === undefined) {\n          jQuery(window).removeProp(callbackName);\n\n          // Otherwise restore preexisting value\n        } else {\n          window[callbackName] = overwritten;\n        }\n\n        // Save back as free\n        if (s[callbackName]) {\n          // Make sure that re-using the options doesn't screw things around\n          s.jsonpCallback = originalSettings.jsonpCallback;\n\n          // Save the callback name for future use\n          oldCallbacks.push(callbackName);\n        }\n\n        // Call if it was a function and we have a response\n        if (responseContainer && isFunction(overwritten)) {\n          overwritten(responseContainer[0]);\n        }\n        responseContainer = overwritten = undefined;\n      });\n\n      // Delegate to script\n      return \"script\";\n    }\n  });\n\n  // Support: Safari 8 only\n  // In Safari 8 documents created via document.implementation.createHTMLDocument\n  // collapse sibling forms: the second one becomes a child of the first one.\n  // Because of that, this security measure has to be disabled in Safari 8.\n  // https://bugs.webkit.org/show_bug.cgi?id=137337\n  support.createHTMLDocument = function () {\n    var body = document.implementation.createHTMLDocument(\"\").body;\n    body.innerHTML = \"<form></form><form></form>\";\n    return body.childNodes.length === 2;\n  }();\n\n  // Argument \"data\" should be string of html\n  // context (optional): If specified, the fragment will be created in this context,\n  // defaults to document\n  // keepScripts (optional): If true, will include scripts passed in the html string\n  jQuery.parseHTML = function (data, context, keepScripts) {\n    if (typeof data !== \"string\") {\n      return [];\n    }\n    if (typeof context === \"boolean\") {\n      keepScripts = context;\n      context = false;\n    }\n    var base, parsed, scripts;\n    if (!context) {\n      // Stop scripts or inline event handlers from being executed immediately\n      // by using document.implementation\n      if (support.createHTMLDocument) {\n        context = document.implementation.createHTMLDocument(\"\");\n\n        // Set the base href for the created document\n        // so any parsed elements with URLs\n        // are based on the document's URL (gh-2965)\n        base = context.createElement(\"base\");\n        base.href = document.location.href;\n        context.head.appendChild(base);\n      } else {\n        context = document;\n      }\n    }\n    parsed = rsingleTag.exec(data);\n    scripts = !keepScripts && [];\n\n    // Single tag\n    if (parsed) {\n      return [context.createElement(parsed[1])];\n    }\n    parsed = buildFragment([data], context, scripts);\n    if (scripts && scripts.length) {\n      jQuery(scripts).remove();\n    }\n    return jQuery.merge([], parsed.childNodes);\n  };\n\n  /**\n   * Load a url into a page\n   */\n  jQuery.fn.load = function (url, params, callback) {\n    var selector,\n      type,\n      response,\n      self = this,\n      off = url.indexOf(\" \");\n    if (off > -1) {\n      selector = stripAndCollapse(url.slice(off));\n      url = url.slice(0, off);\n    }\n\n    // If it's a function\n    if (isFunction(params)) {\n      // We assume that it's the callback\n      callback = params;\n      params = undefined;\n\n      // Otherwise, build a param string\n    } else if (params && typeof params === \"object\") {\n      type = \"POST\";\n    }\n\n    // If we have elements to modify, make the request\n    if (self.length > 0) {\n      jQuery.ajax({\n        url: url,\n        // If \"type\" variable is undefined, then \"GET\" method will be used.\n        // Make value of this field explicit since\n        // user can override it through ajaxSetup method\n        type: type || \"GET\",\n        dataType: \"html\",\n        data: params\n      }).done(function (responseText) {\n        // Save response for use in complete callback\n        response = arguments;\n        self.html(selector ?\n        // If a selector was specified, locate the right elements in a dummy div\n        // Exclude scripts to avoid IE 'Permission Denied' errors\n        jQuery(\"<div>\").append(jQuery.parseHTML(responseText)).find(selector) :\n        // Otherwise use the full result\n        responseText);\n\n        // If the request succeeds, this function gets \"data\", \"status\", \"jqXHR\"\n        // but they are ignored because response was set above.\n        // If it fails, this function gets \"jqXHR\", \"status\", \"error\"\n      }).always(callback && function (jqXHR, status) {\n        self.each(function () {\n          callback.apply(this, response || [jqXHR.responseText, status, jqXHR]);\n        });\n      });\n    }\n    return this;\n  };\n  jQuery.expr.pseudos.animated = function (elem) {\n    return jQuery.grep(jQuery.timers, function (fn) {\n      return elem === fn.elem;\n    }).length;\n  };\n  jQuery.offset = {\n    setOffset: function (elem, options, i) {\n      var curPosition,\n        curLeft,\n        curCSSTop,\n        curTop,\n        curOffset,\n        curCSSLeft,\n        calculatePosition,\n        position = jQuery.css(elem, \"position\"),\n        curElem = jQuery(elem),\n        props = {};\n\n      // Set position first, in-case top/left are set even on static elem\n      if (position === \"static\") {\n        elem.style.position = \"relative\";\n      }\n      curOffset = curElem.offset();\n      curCSSTop = jQuery.css(elem, \"top\");\n      curCSSLeft = jQuery.css(elem, \"left\");\n      calculatePosition = (position === \"absolute\" || position === \"fixed\") && (curCSSTop + curCSSLeft).indexOf(\"auto\") > -1;\n\n      // Need to be able to calculate position if either\n      // top or left is auto and position is either absolute or fixed\n      if (calculatePosition) {\n        curPosition = curElem.position();\n        curTop = curPosition.top;\n        curLeft = curPosition.left;\n      } else {\n        curTop = parseFloat(curCSSTop) || 0;\n        curLeft = parseFloat(curCSSLeft) || 0;\n      }\n      if (isFunction(options)) {\n        // Use jQuery.extend here to allow modification of coordinates argument (gh-1848)\n        options = options.call(elem, i, jQuery.extend({}, curOffset));\n      }\n      if (options.top != null) {\n        props.top = options.top - curOffset.top + curTop;\n      }\n      if (options.left != null) {\n        props.left = options.left - curOffset.left + curLeft;\n      }\n      if (\"using\" in options) {\n        options.using.call(elem, props);\n      } else {\n        curElem.css(props);\n      }\n    }\n  };\n  jQuery.fn.extend({\n    // offset() relates an element's border box to the document origin\n    offset: function (options) {\n      // Preserve chaining for setter\n      if (arguments.length) {\n        return options === undefined ? this : this.each(function (i) {\n          jQuery.offset.setOffset(this, options, i);\n        });\n      }\n      var rect,\n        win,\n        elem = this[0];\n      if (!elem) {\n        return;\n      }\n\n      // Return zeros for disconnected and hidden (display: none) elements (gh-2310)\n      // Support: IE <=11 only\n      // Running getBoundingClientRect on a\n      // disconnected node in IE throws an error\n      if (!elem.getClientRects().length) {\n        return {\n          top: 0,\n          left: 0\n        };\n      }\n\n      // Get document-relative position by adding viewport scroll to viewport-relative gBCR\n      rect = elem.getBoundingClientRect();\n      win = elem.ownerDocument.defaultView;\n      return {\n        top: rect.top + win.pageYOffset,\n        left: rect.left + win.pageXOffset\n      };\n    },\n    // position() relates an element's margin box to its offset parent's padding box\n    // This corresponds to the behavior of CSS absolute positioning\n    position: function () {\n      if (!this[0]) {\n        return;\n      }\n      var offsetParent,\n        offset,\n        doc,\n        elem = this[0],\n        parentOffset = {\n          top: 0,\n          left: 0\n        };\n\n      // position:fixed elements are offset from the viewport, which itself always has zero offset\n      if (jQuery.css(elem, \"position\") === \"fixed\") {\n        // Assume position:fixed implies availability of getBoundingClientRect\n        offset = elem.getBoundingClientRect();\n      } else {\n        offset = this.offset();\n\n        // Account for the *real* offset parent, which can be the document or its root element\n        // when a statically positioned element is identified\n        doc = elem.ownerDocument;\n        offsetParent = elem.offsetParent || doc.documentElement;\n        while (offsetParent && (offsetParent === doc.body || offsetParent === doc.documentElement) && jQuery.css(offsetParent, \"position\") === \"static\") {\n          offsetParent = offsetParent.parentNode;\n        }\n        if (offsetParent && offsetParent !== elem && offsetParent.nodeType === 1) {\n          // Incorporate borders into its offset, since they are outside its content origin\n          parentOffset = jQuery(offsetParent).offset();\n          parentOffset.top += jQuery.css(offsetParent, \"borderTopWidth\", true);\n          parentOffset.left += jQuery.css(offsetParent, \"borderLeftWidth\", true);\n        }\n      }\n\n      // Subtract parent offsets and element margins\n      return {\n        top: offset.top - parentOffset.top - jQuery.css(elem, \"marginTop\", true),\n        left: offset.left - parentOffset.left - jQuery.css(elem, \"marginLeft\", true)\n      };\n    },\n    // This method will return documentElement in the following cases:\n    // 1) For the element inside the iframe without offsetParent, this method will return\n    //    documentElement of the parent window\n    // 2) For the hidden or detached element\n    // 3) For body or html element, i.e. in case of the html node - it will return itself\n    //\n    // but those exceptions were never presented as a real life use-cases\n    // and might be considered as more preferable results.\n    //\n    // This logic, however, is not guaranteed and can change at any point in the future\n    offsetParent: function () {\n      return this.map(function () {\n        var offsetParent = this.offsetParent;\n        while (offsetParent && jQuery.css(offsetParent, \"position\") === \"static\") {\n          offsetParent = offsetParent.offsetParent;\n        }\n        return offsetParent || documentElement;\n      });\n    }\n  });\n\n  // Create scrollLeft and scrollTop methods\n  jQuery.each({\n    scrollLeft: \"pageXOffset\",\n    scrollTop: \"pageYOffset\"\n  }, function (method, prop) {\n    var top = \"pageYOffset\" === prop;\n    jQuery.fn[method] = function (val) {\n      return access(this, function (elem, method, val) {\n        // Coalesce documents and windows\n        var win;\n        if (isWindow(elem)) {\n          win = elem;\n        } else if (elem.nodeType === 9) {\n          win = elem.defaultView;\n        }\n        if (val === undefined) {\n          return win ? win[prop] : elem[method];\n        }\n        if (win) {\n          win.scrollTo(!top ? val : win.pageXOffset, top ? val : win.pageYOffset);\n        } else {\n          elem[method] = val;\n        }\n      }, method, val, arguments.length);\n    };\n  });\n\n  // Support: Safari <=7 - 9.1, Chrome <=37 - 49\n  // Add the top/left cssHooks using jQuery.fn.position\n  // Webkit bug: https://bugs.webkit.org/show_bug.cgi?id=29084\n  // Blink bug: https://bugs.chromium.org/p/chromium/issues/detail?id=589347\n  // getComputedStyle returns percent when specified for top/left/bottom/right;\n  // rather than make the css module depend on the offset module, just check for it here\n  jQuery.each([\"top\", \"left\"], function (_i, prop) {\n    jQuery.cssHooks[prop] = addGetHookIf(support.pixelPosition, function (elem, computed) {\n      if (computed) {\n        computed = curCSS(elem, prop);\n\n        // If curCSS returns percentage, fallback to offset\n        return rnumnonpx.test(computed) ? jQuery(elem).position()[prop] + \"px\" : computed;\n      }\n    });\n  });\n\n  // Create innerHeight, innerWidth, height, width, outerHeight and outerWidth methods\n  jQuery.each({\n    Height: \"height\",\n    Width: \"width\"\n  }, function (name, type) {\n    jQuery.each({\n      padding: \"inner\" + name,\n      content: type,\n      \"\": \"outer\" + name\n    }, function (defaultExtra, funcName) {\n      // Margin is only for outerHeight, outerWidth\n      jQuery.fn[funcName] = function (margin, value) {\n        var chainable = arguments.length && (defaultExtra || typeof margin !== \"boolean\"),\n          extra = defaultExtra || (margin === true || value === true ? \"margin\" : \"border\");\n        return access(this, function (elem, type, value) {\n          var doc;\n          if (isWindow(elem)) {\n            // $( window ).outerWidth/Height return w/h including scrollbars (gh-1729)\n            return funcName.indexOf(\"outer\") === 0 ? elem[\"inner\" + name] : elem.document.documentElement[\"client\" + name];\n          }\n\n          // Get document width or height\n          if (elem.nodeType === 9) {\n            doc = elem.documentElement;\n\n            // Either scroll[Width/Height] or offset[Width/Height] or client[Width/Height],\n            // whichever is greatest\n            return Math.max(elem.body[\"scroll\" + name], doc[\"scroll\" + name], elem.body[\"offset\" + name], doc[\"offset\" + name], doc[\"client\" + name]);\n          }\n          return value === undefined ?\n          // Get width or height on the element, requesting but not forcing parseFloat\n          jQuery.css(elem, type, extra) :\n          // Set width or height on the element\n          jQuery.style(elem, type, value, extra);\n        }, type, chainable ? margin : undefined, chainable);\n      };\n    });\n  });\n  jQuery.each([\"ajaxStart\", \"ajaxStop\", \"ajaxComplete\", \"ajaxError\", \"ajaxSuccess\", \"ajaxSend\"], function (_i, type) {\n    jQuery.fn[type] = function (fn) {\n      return this.on(type, fn);\n    };\n  });\n  jQuery.fn.extend({\n    bind: function (types, data, fn) {\n      return this.on(types, null, data, fn);\n    },\n    unbind: function (types, fn) {\n      return this.off(types, null, fn);\n    },\n    delegate: function (selector, types, data, fn) {\n      return this.on(types, selector, data, fn);\n    },\n    undelegate: function (selector, types, fn) {\n      // ( namespace ) or ( selector, types [, fn] )\n      return arguments.length === 1 ? this.off(selector, \"**\") : this.off(types, selector || \"**\", fn);\n    },\n    hover: function (fnOver, fnOut) {\n      return this.on(\"mouseenter\", fnOver).on(\"mouseleave\", fnOut || fnOver);\n    }\n  });\n  jQuery.each((\"blur focus focusin focusout resize scroll click dblclick \" + \"mousedown mouseup mousemove mouseover mouseout mouseenter mouseleave \" + \"change select submit keydown keypress keyup contextmenu\").split(\" \"), function (_i, name) {\n    // Handle event binding\n    jQuery.fn[name] = function (data, fn) {\n      return arguments.length > 0 ? this.on(name, null, data, fn) : this.trigger(name);\n    };\n  });\n\n  // Support: Android <=4.0 only\n  // Make sure we trim BOM and NBSP\n  // Require that the \"whitespace run\" starts from a non-whitespace\n  // to avoid O(N^2) behavior when the engine would try matching \"\\s+$\" at each space position.\n  var rtrim = /^[\\s\\uFEFF\\xA0]+|([^\\s\\uFEFF\\xA0])[\\s\\uFEFF\\xA0]+$/g;\n\n  // Bind a function to a context, optionally partially applying any\n  // arguments.\n  // jQuery.proxy is deprecated to promote standards (specifically Function#bind)\n  // However, it is not slated for removal any time soon\n  jQuery.proxy = function (fn, context) {\n    var tmp, args, proxy;\n    if (typeof context === \"string\") {\n      tmp = fn[context];\n      context = fn;\n      fn = tmp;\n    }\n\n    // Quick check to determine if target is callable, in the spec\n    // this throws a TypeError, but we will just return undefined.\n    if (!isFunction(fn)) {\n      return undefined;\n    }\n\n    // Simulated bind\n    args = slice.call(arguments, 2);\n    proxy = function () {\n      return fn.apply(context || this, args.concat(slice.call(arguments)));\n    };\n\n    // Set the guid of unique handler to the same of original handler, so it can be removed\n    proxy.guid = fn.guid = fn.guid || jQuery.guid++;\n    return proxy;\n  };\n  jQuery.holdReady = function (hold) {\n    if (hold) {\n      jQuery.readyWait++;\n    } else {\n      jQuery.ready(true);\n    }\n  };\n  jQuery.isArray = Array.isArray;\n  jQuery.parseJSON = JSON.parse;\n  jQuery.nodeName = nodeName;\n  jQuery.isFunction = isFunction;\n  jQuery.isWindow = isWindow;\n  jQuery.camelCase = camelCase;\n  jQuery.type = toType;\n  jQuery.now = Date.now;\n  jQuery.isNumeric = function (obj) {\n    // As of jQuery 3.0, isNumeric is limited to\n    // strings and numbers (primitives or objects)\n    // that can be coerced to finite numbers (gh-2662)\n    var type = jQuery.type(obj);\n    return (type === \"number\" || type === \"string\") &&\n    // parseFloat NaNs numeric-cast false positives (\"\")\n    // ...but misinterprets leading-number strings, particularly hex literals (\"0x...\")\n    // subtraction forces infinities to NaN\n    !isNaN(obj - parseFloat(obj));\n  };\n  jQuery.trim = function (text) {\n    return text == null ? \"\" : (text + \"\").replace(rtrim, \"$1\");\n  };\n\n  // Register as a named AMD module, since jQuery can be concatenated with other\n  // files that may use define, but not via a proper concatenation script that\n  // understands anonymous AMD modules. A named AMD is safest and most robust\n  // way to register. Lowercase jquery is used because AMD module names are\n  // derived from file names, and jQuery is normally delivered in a lowercase\n  // file name. Do this after creating the global so that if an AMD module wants\n  // to call noConflict to hide this version of jQuery, it will work.\n\n  // Note that for maximum portability, libraries that are not jQuery should\n  // declare themselves as anonymous modules, and avoid setting a global if an\n  // AMD loader is present. jQuery is a special case. For more information, see\n  // https://github.com/jrburke/requirejs/wiki/Updating-existing-libraries#wiki-anon\n\n  if (typeof define === \"function\" && define.amd) {\n    define(\"jquery\", [], function () {\n      return jQuery;\n    });\n  }\n  var\n    // Map over jQuery in case of overwrite\n    _jQuery = window.jQuery,\n    // Map over the $ in case of overwrite\n    _$ = window.$;\n  jQuery.noConflict = function (deep) {\n    if (window.$ === jQuery) {\n      window.$ = _$;\n    }\n    if (deep && window.jQuery === jQuery) {\n      window.jQuery = _jQuery;\n    }\n    return jQuery;\n  };\n\n  // Expose jQuery and $ identifiers, even in AMD\n  // (trac-7102#comment:10, https://github.com/jquery/jquery/pull/557)\n  // and CommonJS for browser emulators (trac-13566)\n  if (typeof noGlobal === \"undefined\") {\n    window.jQuery = window.$ = jQuery;\n  }\n  return jQuery;\n});", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}