{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.retryWhen = void 0;\nvar innerFrom_1 = require(\"../observable/innerFrom\");\nvar Subject_1 = require(\"../Subject\");\nvar lift_1 = require(\"../util/lift\");\nvar OperatorSubscriber_1 = require(\"./OperatorSubscriber\");\nfunction retryWhen(notifier) {\n  return lift_1.operate(function (source, subscriber) {\n    var innerSub;\n    var syncResub = false;\n    var errors$;\n    var subscribeForRetryWhen = function () {\n      innerSub = source.subscribe(OperatorSubscriber_1.createOperatorSubscriber(subscriber, undefined, undefined, function (err) {\n        if (!errors$) {\n          errors$ = new Subject_1.Subject();\n          innerFrom_1.innerFrom(notifier(errors$)).subscribe(OperatorSubscriber_1.createOperatorSubscriber(subscriber, function () {\n            return innerSub ? subscribeForRetryWhen() : syncResub = true;\n          }));\n        }\n        if (errors$) {\n          errors$.next(err);\n        }\n      }));\n      if (syncResub) {\n        innerSub.unsubscribe();\n        innerSub = null;\n        syncResub = false;\n        subscribeForRetryWhen();\n      }\n    };\n    subscribeForRetryWhen();\n  });\n}\nexports.retryWhen = retryWhen;\n//# sourceMappingURL=retryWhen.js.map", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}