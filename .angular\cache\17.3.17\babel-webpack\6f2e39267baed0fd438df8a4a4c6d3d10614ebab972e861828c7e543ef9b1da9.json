{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.of = void 0;\nvar args_1 = require(\"../util/args\");\nvar from_1 = require(\"./from\");\nfunction of() {\n  var args = [];\n  for (var _i = 0; _i < arguments.length; _i++) {\n    args[_i] = arguments[_i];\n  }\n  var scheduler = args_1.popScheduler(args);\n  return from_1.from(args, scheduler);\n}\nexports.of = of;\n//# sourceMappingURL=of.js.map", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}