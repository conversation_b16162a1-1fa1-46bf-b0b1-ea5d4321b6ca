{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Injectable, NgModule } from '@angular/core';\nimport * as exifr from 'exifr';\nimport { Subject } from 'rxjs';\nimport picaLib from 'pica';\nlet ImgExifService = /*#__PURE__*/(() => {\n  class ImgExifService {\n    getOrientedImage(image) {\n      return new Promise(resolve => {\n        let img;\n        exifr.orientation(image).catch(err => undefined).then(orientation => {\n          if (orientation != 1) {\n            let canvas = document.createElement(\"canvas\"),\n              ctx = canvas.getContext(\"2d\"),\n              cw = image.width,\n              ch = image.height,\n              cx = 0,\n              cy = 0,\n              deg = 0;\n            switch (orientation) {\n              case 3:\n              case 4:\n                cx = -image.width;\n                cy = -image.height;\n                deg = 180;\n                break;\n              case 5:\n              case 6:\n                cw = image.height;\n                ch = image.width;\n                cy = -image.height;\n                deg = 90;\n                break;\n              case 7:\n              case 8:\n                cw = image.height;\n                ch = image.width;\n                cx = -image.width;\n                deg = 270;\n                break;\n              default:\n                break;\n            }\n            canvas.width = cw;\n            canvas.height = ch;\n            if (orientation && [2, 4, 5, 7].indexOf(orientation) > -1) {\n              //flip image\n              ctx.translate(cw, 0);\n              ctx.scale(-1, 1);\n            }\n            ctx.rotate(deg * Math.PI / 180);\n            ctx.drawImage(image, cx, cy);\n            img = document.createElement(\"img\");\n            img.width = cw;\n            img.height = ch;\n            img.addEventListener('load', function () {\n              resolve(img);\n            });\n            img.src = canvas.toDataURL(\"image/png\");\n          } else {\n            resolve(image);\n          }\n        });\n      });\n    }\n  }\n  ImgExifService.ɵfac = function ImgExifService_Factory(t) {\n    return new (t || ImgExifService)();\n  };\n  ImgExifService.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: ImgExifService,\n    factory: ImgExifService.ɵfac,\n    providedIn: 'root'\n  });\n  return ImgExifService;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nconst MAX_STEPS = 15;\nlet ImgMaxSizeService = /*#__PURE__*/(() => {\n  class ImgMaxSizeService {\n    constructor(imageExifService) {\n      this.imageExifService = imageExifService;\n      this.timeAtStart = 0;\n    }\n    compressImage(file, maxSizeInMB, ignoreAlpha = false, logExecutionTime = false) {\n      let compressedFileSubject = new Subject();\n      this.timeAtStart = new Date().getTime();\n      this.initialFile = file;\n      if (file.type !== \"image/jpeg\" && file.type !== \"image/png\") {\n        //END OF COMPRESSION\n        setTimeout(() => {\n          compressedFileSubject.error({\n            compressedFile: file,\n            reason: \"File provided is neither of type jpg nor of type png.\",\n            error: \"INVALID_EXTENSION\"\n          });\n        }, 0);\n        return compressedFileSubject.asObservable();\n      }\n      let oldFileSize = file.size / 1024 / 1024;\n      if (oldFileSize < maxSizeInMB) {\n        // END OF COMPRESSION\n        // FILE SIZE ALREADY BELOW MAX_SIZE -> no compression needed\n        setTimeout(() => {\n          compressedFileSubject.next(file);\n        }, 0);\n        return compressedFileSubject.asObservable();\n      }\n      let cvs = document.createElement('canvas');\n      let ctx = cvs.getContext('2d');\n      let img = new Image();\n      let self = this;\n      img.onload = () => {\n        this.imageExifService.getOrientedImage(img).then(orientedImg => {\n          window.URL.revokeObjectURL(img.src);\n          cvs.width = orientedImg.width;\n          cvs.height = orientedImg.height;\n          ctx?.drawImage(orientedImg, 0, 0);\n          let imageData = ctx?.getImageData(0, 0, orientedImg.width, orientedImg.height);\n          if (file.type === \"image/png\" && this.isImgUsingAlpha(imageData) && !ignoreAlpha) {\n            //png image with alpha\n            compressedFileSubject.error({\n              compressedFile: file,\n              reason: \"File provided is a png image which uses the alpha channel. No compression possible.\",\n              error: \"PNG_WITH_ALPHA\"\n            });\n          }\n          ctx = cvs.getContext('2d', {\n            'alpha': false\n          });\n          ctx?.drawImage(orientedImg, 0, 0);\n          self.getCompressedFile(cvs, 50, maxSizeInMB, 1).then(compressedFile => {\n            compressedFileSubject.next(compressedFile);\n            self.logExecutionTime(logExecutionTime);\n          }).catch(error => {\n            compressedFileSubject.error(error);\n            self.logExecutionTime(logExecutionTime);\n          });\n        });\n      };\n      img.src = window.URL.createObjectURL(file);\n      return compressedFileSubject.asObservable();\n    }\n    getCompressedFile(cvs, quality, maxSizeInMB, currentStep) {\n      let result = new Promise((resolve, reject) => {\n        cvs.toBlob(blob => {\n          if (!blob) {\n            return reject({\n              compressedFile: null,\n              reason: \"Blob error\",\n              error: \"BAD_BLOB\"\n            });\n          }\n          if (currentStep + 1 > MAX_STEPS) {\n            //COMPRESSION END\n            //maximal steps reached\n            reject({\n              compressedFile: this.getResultFile(blob),\n              reason: \"Could not find the correct compression quality in \" + MAX_STEPS + \" steps.\",\n              error: \"MAX_STEPS_EXCEEDED\"\n            });\n          } else {\n            let newQuality = this.getCalculatedQuality(blob, quality, maxSizeInMB, currentStep);\n            this.checkCompressionStatus(cvs, blob, quality, maxSizeInMB, currentStep, newQuality).then(result => {\n              resolve(result);\n            }).catch(result => {\n              reject(result);\n            });\n          }\n        }, \"image/jpeg\", quality / 100);\n      });\n      return result;\n    }\n    getResultFile(blob) {\n      if (!this.initialFile) {\n        return null;\n      }\n      return this.generateResultFile(blob, this.initialFile.name, this.initialFile.type, new Date().getTime());\n    }\n    generateResultFile(blob, name, type, lastModified) {\n      let resultFile = new Blob([blob], {\n        type: type\n      });\n      return this.blobToFile(resultFile, name, lastModified);\n    }\n    blobToFile(blob, name, lastModified) {\n      let file = blob;\n      file.name = name;\n      file.lastModified = lastModified;\n      //Cast to a File() type\n      return file;\n    }\n    getCalculatedQuality(blob, quality, maxSizeInMB, currentStep) {\n      if (!this.initialFile) {\n        return 0;\n      }\n      //CALCULATE NEW QUALITY\n      let currentSize = blob.size / 1024 / 1024;\n      let ratioMaxSizeToCurrentSize = maxSizeInMB / currentSize;\n      if (ratioMaxSizeToCurrentSize > 5) {\n        //max ratio to avoid extreme quality values\n        ratioMaxSizeToCurrentSize = 5;\n      }\n      let ratioMaxSizeToInitialSize = currentSize / (this.initialFile.size / 1024 / 1024);\n      if (ratioMaxSizeToInitialSize < 0.05) {\n        //min ratio to avoid extreme quality values\n        ratioMaxSizeToInitialSize = 0.05;\n      }\n      let newQuality = 0;\n      let multiplicator = Math.abs(ratioMaxSizeToInitialSize - 1) * 10 / (currentStep * 1.7) / ratioMaxSizeToCurrentSize;\n      if (multiplicator < 1) {\n        multiplicator = 1;\n      }\n      if (ratioMaxSizeToCurrentSize >= 1) {\n        newQuality = quality + (ratioMaxSizeToCurrentSize - 1) * 10 * multiplicator;\n      } else {\n        newQuality = quality - (1 - ratioMaxSizeToCurrentSize) * 10 * multiplicator;\n      }\n      if (newQuality > 100) {\n        //max quality = 100, so let's set the new quality to the value in between the old quality and 100 in case of > 100\n        newQuality = quality + (100 - quality) / 2;\n      }\n      if (newQuality < 0) {\n        //min quality = 0, so let's set the new quality to the value in between the old quality and 0 in case of < 0\n        newQuality = quality - quality / 2;\n      }\n      return newQuality;\n    }\n    checkCompressionStatus(cvs, blob, quality, maxSizeInMB, currentStep, newQuality) {\n      let result = new Promise((resolve, reject) => {\n        if (quality === 100 && newQuality >= 100) {\n          //COMPRESSION END\n          //Seems like quality 100 is max but file still too small, case that shouldn't exist as the compression shouldn't even have started in the first place\n          reject({\n            compressedFile: this.initialFile,\n            reason: \"Unfortunately there was an error while compressing the file.\",\n            error: \"FILE_BIGGER_THAN_INITIAL_FILE\"\n          });\n        } else if (quality < 1 && newQuality < quality) {\n          //COMPRESSION END\n          //File size still too big but can't compress further than quality=0\n          reject({\n            compressedFile: this.getResultFile(blob),\n            reason: \"Could not compress image enough to fit the maximal file size limit.\",\n            error: \"UNABLE_TO_COMPRESS_ENOUGH\"\n          });\n        } else if (newQuality > quality && Math.round(quality) == Math.round(newQuality)) {\n          //COMPRESSION END\n          //next steps quality would be the same quality but newQuality is slightly bigger than old one, means we most likely found the nearest quality to compress to maximal size\n          resolve(this.getResultFile(blob));\n        } else if (currentStep > 5 && newQuality > quality && newQuality < quality + 2) {\n          //COMPRESSION END\n          //for some rare occasions the algorithm might be stuck around e.g. 98.5 and 97.4 because of the maxQuality of 100, the current quality is the nearest possible quality in that case\n          resolve(this.getResultFile(blob));\n        } else if (newQuality > quality && Number.isInteger(quality) && Math.floor(newQuality) == quality) {\n          //COMPRESSION END\n          /*\r\n              in the previous step if ((quality > newQuality) && (Math.round(quality) == Math.round(newQuality))) applied, so\r\n              newQuality = Math.round(newQuality) - 1; this was done to reduce the quality at least a full integer down to not waste a step\r\n              with the same compression rate quality as before. Now, the newQuality is still only in between the old quality (e.g. 93)\r\n              and the newQuality (e.g. 94) which most likely means that the value for the newQuality (the bigger one) would make the filesize\r\n              too big so we should just stick with the current, lower quality and return that file.\r\n          */\n          resolve(this.getResultFile(blob));\n        } else {\n          //CONTINUE COMPRESSION\n          if (quality > newQuality && Math.round(quality) == Math.round(newQuality)) {\n            //quality can only be an integer -> make sure difference between old quality and new one is at least a whole integer number\n            // - it would be nonsense to compress again with the same quality\n            newQuality = Math.round(newQuality) - 1;\n          }\n          //recursively call function again\n          resolve(this.getCompressedFile(cvs, newQuality, maxSizeInMB, currentStep + 1));\n        }\n      });\n      return result;\n    }\n    isImgUsingAlpha(imageData) {\n      for (var i = 0; i < imageData.data.length; i += 4) {\n        if (imageData.data[i + 3] !== 255) {\n          return true;\n        }\n      }\n      return false;\n    }\n    logExecutionTime(logExecutionTime) {\n      if (logExecutionTime) {\n        console.info(\"Execution time: \", new Date().getTime() - this.timeAtStart + \"ms\");\n      }\n    }\n  }\n  ImgMaxSizeService.ɵfac = function ImgMaxSizeService_Factory(t) {\n    return new (t || ImgMaxSizeService)(i0.ɵɵinject(ImgExifService));\n  };\n  ImgMaxSizeService.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: ImgMaxSizeService,\n    factory: ImgMaxSizeService.ɵfac,\n    providedIn: 'root'\n  });\n  return ImgMaxSizeService;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nconst pica = picaLib();\nconst globalWindow = window;\nlet Ng2PicaService = /*#__PURE__*/(() => {\n  class Ng2PicaService {\n    constructor(imageExifService) {\n      this.imageExifService = imageExifService;\n    }\n    resize(files, width, height, keepAspectRatio = false) {\n      let resizedFile = new Subject();\n      for (let i = 0; i < files.length; i++) {\n        this.resizeFile(files[i], width, height, keepAspectRatio).then(returnedFile => {\n          resizedFile.next(returnedFile);\n        }).catch(error => {\n          resizedFile.error(error);\n        });\n      }\n      return resizedFile.asObservable();\n    }\n    resizeCanvas(from, to, options) {\n      let result = new Promise((resolve, reject) => {\n        let curPica = pica;\n        if (!curPica || !curPica.resize) {\n          curPica = new globalWindow.pica();\n        }\n        curPica.resize(from, to, options).then(response => {\n          resolve(response);\n        }, error => {\n          reject(error);\n        });\n      });\n      return result;\n    }\n    resizeBuffer(options) {\n      let result = new Promise((resolve, reject) => {\n        let curPica = pica;\n        if (!curPica || !curPica.resizeBuffer) {\n          curPica = new globalWindow.pica();\n        }\n        curPica.resizeBuffer(options).then(response => {\n          resolve(response);\n        }, error => {\n          reject(error);\n        });\n      });\n      return result;\n    }\n    resizeFile(file, width, height, keepAspectRatio = false) {\n      let result = new Promise((resolve, reject) => {\n        let fromCanvas = document.createElement('canvas');\n        let ctx = fromCanvas.getContext('2d');\n        let img = new Image();\n        img.onload = () => {\n          this.imageExifService.getOrientedImage(img).then(orientedImg => {\n            globalWindow.URL.revokeObjectURL(img.src);\n            fromCanvas.width = orientedImg.width;\n            fromCanvas.height = orientedImg.height;\n            ctx?.drawImage(orientedImg, 0, 0);\n            let imageData = ctx?.getImageData(0, 0, orientedImg.width, orientedImg.height);\n            if (keepAspectRatio && imageData) {\n              let ratio = Math.min(width / imageData.width, height / imageData.height);\n              width = Math.round(imageData.width * ratio);\n              height = Math.round(imageData.height * ratio);\n            }\n            let useAlpha = true;\n            if (file.type === \"image/jpeg\" || file.type === \"image/png\" && !this.isImgUsingAlpha(imageData)) {\n              //image without alpha\n              useAlpha = false;\n              ctx = fromCanvas.getContext('2d', {\n                'alpha': false\n              });\n              ctx?.drawImage(orientedImg, 0, 0);\n            }\n            let toCanvas = document.createElement('canvas');\n            toCanvas.width = width;\n            toCanvas.height = height;\n            this.resizeCanvas(fromCanvas, toCanvas, {\n              'alpha': useAlpha\n            }).then(resizedCanvas => {\n              resizedCanvas.toBlob(blob => {\n                if (!blob) {\n                  return reject('error blob');\n                }\n                let newFile = this.generateResultFile(blob, file.name, file.type, new Date().getTime());\n                resolve(newFile);\n              }, file.type);\n            }).catch(error => {\n              reject(error);\n            });\n          });\n        };\n        img.src = globalWindow.URL.createObjectURL(file);\n      });\n      return result;\n    }\n    isImgUsingAlpha(imageData) {\n      for (var i = 0; i < imageData.data.length; i += 4) {\n        if (imageData.data[i + 3] !== 255) {\n          return true;\n        }\n      }\n      return false;\n    }\n    generateResultFile(blob, name, type, lastModified) {\n      let resultFile = new Blob([blob], {\n        type: type\n      });\n      return this.blobToFile(resultFile, name, lastModified);\n    }\n    blobToFile(blob, name, lastModified) {\n      let file = blob;\n      file.name = name;\n      file.lastModified = lastModified;\n      //Cast to a File() type\n      return file;\n    }\n  }\n  Ng2PicaService.ɵfac = function Ng2PicaService_Factory(t) {\n    return new (t || Ng2PicaService)(i0.ɵɵinject(ImgExifService));\n  };\n  Ng2PicaService.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: Ng2PicaService,\n    factory: Ng2PicaService.ɵfac,\n    providedIn: 'root'\n  });\n  return Ng2PicaService;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet ImgMaxPXSizeService = /*#__PURE__*/(() => {\n  class ImgMaxPXSizeService {\n    constructor(ng2PicaService, imageExifService) {\n      this.ng2PicaService = ng2PicaService;\n      this.imageExifService = imageExifService;\n      this.timeAtStart = 0;\n    }\n    resizeImage(file, maxWidth, maxHeight, logExecutionTime = false) {\n      let resizedFileSubject = new Subject();\n      this.timeAtStart = new Date().getTime();\n      if (file.type !== \"image/jpeg\" && file.type !== \"image/png\") {\n        //END OF RESIZE\n        setTimeout(() => {\n          resizedFileSubject.error({\n            resizedFile: file,\n            reason: \"The provided File is neither of type jpg nor of type png.\",\n            error: \"INVALID_EXTENSION\"\n          });\n        }, 0);\n        return resizedFileSubject.asObservable();\n      }\n      let img = new Image();\n      let self = this;\n      img.onload = () => {\n        this.imageExifService.getOrientedImage(img).then(orientedImg => {\n          window.URL.revokeObjectURL(img.src);\n          let currentWidth = orientedImg.width;\n          let currentHeight = orientedImg.height;\n          let newWidth = currentWidth;\n          let newHeight = currentHeight;\n          if (newWidth > maxWidth) {\n            newWidth = maxWidth;\n            //resize height proportionally\n            let ratio = maxWidth / currentWidth; //is gonna be <1\n            newHeight = newHeight * ratio;\n          }\n          currentHeight = newHeight;\n          if (newHeight > maxHeight) {\n            newHeight = maxHeight;\n            //resize width proportionally\n            let ratio = maxHeight / currentHeight; //is gonna be <1\n            newWidth = newWidth * ratio;\n          }\n          if (newHeight === orientedImg.height && newWidth === orientedImg.width) {\n            //no resizing necessary\n            resizedFileSubject.next(file);\n            self.logExecutionTime(logExecutionTime);\n          } else {\n            self.ng2PicaService.resize([file], newWidth, newHeight).subscribe(result => {\n              //all good, result is a file\n              resizedFileSubject.next(result);\n              self.logExecutionTime(logExecutionTime);\n            }, error => {\n              //something went wrong\n              resizedFileSubject.error({\n                resizedFile: file,\n                reason: error,\n                error: \"PICA_ERROR\"\n              });\n              self.logExecutionTime(logExecutionTime);\n            });\n          }\n        });\n      };\n      img.src = window.URL.createObjectURL(file);\n      return resizedFileSubject.asObservable();\n    }\n    logExecutionTime(logExecutionTime) {\n      if (logExecutionTime) {\n        console.info(\"Execution time: \", new Date().getTime() - this.timeAtStart + \"ms\");\n      }\n    }\n  }\n  ImgMaxPXSizeService.ɵfac = function ImgMaxPXSizeService_Factory(t) {\n    return new (t || ImgMaxPXSizeService)(i0.ɵɵinject(Ng2PicaService), i0.ɵɵinject(ImgExifService));\n  };\n  ImgMaxPXSizeService.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: ImgMaxPXSizeService,\n    factory: ImgMaxPXSizeService.ɵfac,\n    providedIn: 'root'\n  });\n  return ImgMaxPXSizeService;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet Ng2ImgMaxService = /*#__PURE__*/(() => {\n  class Ng2ImgMaxService {\n    constructor(imgMaxSizeService, imgMaxPXSizeService, imageExifService) {\n      this.imgMaxSizeService = imgMaxSizeService;\n      this.imgMaxPXSizeService = imgMaxPXSizeService;\n      this.imageExifService = imageExifService;\n    }\n    compress(files, maxSizeInMB, ignoreAlpha = false, logExecutionTime = false) {\n      let compressedFileSubject = new Subject();\n      files.forEach(file => {\n        this.compressImage(file, maxSizeInMB, ignoreAlpha, logExecutionTime).subscribe(value => {\n          compressedFileSubject.next(value);\n        }, error => {\n          compressedFileSubject.error(error);\n        });\n      });\n      return compressedFileSubject.asObservable();\n    }\n    resize(files, maxWidth, maxHeight, logExecutionTime = false) {\n      let resizedFileSubject = new Subject();\n      files.forEach(file => {\n        this.resizeImage(file, maxWidth, maxHeight, logExecutionTime).subscribe(value => {\n          resizedFileSubject.next(value);\n        }, error => {\n          resizedFileSubject.error(error);\n        });\n      });\n      return resizedFileSubject.asObservable();\n    }\n    compressImage(file, maxSizeInMB, ignoreAlpha = false, logExecutionTime = false) {\n      return this.imgMaxSizeService.compressImage(file, maxSizeInMB, ignoreAlpha, logExecutionTime);\n    }\n    resizeImage(file, maxWidth, maxHeight, logExecutionTime = false) {\n      return this.imgMaxPXSizeService.resizeImage(file, maxWidth, maxHeight, logExecutionTime);\n    }\n    getEXIFOrientedImage(image) {\n      return this.imageExifService.getOrientedImage(image);\n    }\n  }\n  Ng2ImgMaxService.ɵfac = function Ng2ImgMaxService_Factory(t) {\n    return new (t || Ng2ImgMaxService)(i0.ɵɵinject(ImgMaxSizeService), i0.ɵɵinject(ImgMaxPXSizeService), i0.ɵɵinject(ImgExifService));\n  };\n  Ng2ImgMaxService.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: Ng2ImgMaxService,\n    factory: Ng2ImgMaxService.ɵfac,\n    providedIn: 'root'\n  });\n  return Ng2ImgMaxService;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet Ng2ImgMaxModule = /*#__PURE__*/(() => {\n  class Ng2ImgMaxModule {}\n  Ng2ImgMaxModule.ɵfac = function Ng2ImgMaxModule_Factory(t) {\n    return new (t || Ng2ImgMaxModule)();\n  };\n  Ng2ImgMaxModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: Ng2ImgMaxModule\n  });\n  Ng2ImgMaxModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    providers: [Ng2PicaService, ImgMaxPXSizeService, ImgMaxSizeService, ImgExifService, Ng2ImgMaxService, Ng2PicaService]\n  });\n  return Ng2ImgMaxModule;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\r\n * Generated bundle index. Do not edit.\r\n */\n\nexport { ImgExifService, ImgMaxPXSizeService, ImgMaxSizeService, Ng2ImgMaxModule, Ng2ImgMaxService, Ng2PicaService };\n//# sourceMappingURL=ng2-img-max.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}