{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nvar SelectItem = function () {\n  function SelectItem(source) {\n    var _this = this;\n    if (typeof source === 'string') {\n      this.id = this.text = source;\n    }\n    if (typeof source === 'object') {\n      this.id = source.id || source.text;\n      this.text = source.text;\n      if (source.children && source.text) {\n        this.children = source.children.map(function (c) {\n          var r = new SelectItem(c);\n          r.parent = _this;\n          return r;\n        });\n        this.text = source.text;\n      }\n    }\n  }\n  SelectItem.prototype.fillChildrenHash = function (optionsMap, startIndex) {\n    var i = startIndex;\n    this.children.map(function (child) {\n      optionsMap.set(child.id, i++);\n    });\n    return i;\n  };\n  SelectItem.prototype.hasChildren = function () {\n    return this.children && this.children.length > 0;\n  };\n  SelectItem.prototype.getSimilar = function () {\n    var r = new SelectItem(false);\n    r.id = this.id;\n    r.text = this.text;\n    r.parent = this.parent;\n    return r;\n  };\n  return SelectItem;\n}();\nexports.SelectItem = SelectItem;", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}