{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Injectable, Component, ChangeDetectionStrategy, EventEmitter, Directive, Input, Output, NgModule } from '@angular/core';\nimport { getBsVer, warnOnce, parseTriggers, OnChange } from 'ngx-bootstrap/utils';\nimport * as i3 from 'ngx-bootstrap/positioning';\nimport { PlacementForBs5, PositioningService } from 'ngx-bootstrap/positioning';\nimport { __metadata, __decorate } from 'tslib';\nimport * as i1 from 'ngx-bootstrap/component-loader';\nimport { ComponentLoaderFactory } from 'ngx-bootstrap/component-loader';\nimport { timer } from 'rxjs';\nimport { CommonModule } from '@angular/common';\n\n/** Default values provider for tooltip */\nconst _c0 = [\"*\"];\nlet TooltipConfig = /*#__PURE__*/(() => {\n  class TooltipConfig {\n    constructor() {\n      /** sets disable adaptive position */\n      this.adaptivePosition = true;\n      /** tooltip placement, supported positions: 'top', 'bottom', 'left', 'right' */\n      this.placement = 'top';\n      /** array of event names which triggers tooltip opening */\n      this.triggers = 'hover focus';\n      /** delay before showing the tooltip */\n      this.delay = 0;\n    }\n    static {\n      this.ɵfac = function TooltipConfig_Factory(t) {\n        return new (t || TooltipConfig)();\n      };\n    }\n    static {\n      this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n        token: TooltipConfig,\n        factory: TooltipConfig.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return TooltipConfig;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet TooltipContainerComponent = /*#__PURE__*/(() => {\n  class TooltipContainerComponent {\n    get _bsVersions() {\n      return getBsVer();\n    }\n    constructor(config) {\n      Object.assign(this, config);\n    }\n    ngAfterViewInit() {\n      this.classMap = {\n        in: false,\n        fade: false\n      };\n      if (this.placement) {\n        if (this._bsVersions.isBs5) {\n          this.placement = PlacementForBs5[this.placement];\n        }\n        this.classMap[this.placement] = true;\n      }\n      this.classMap[`tooltip-${this.placement}`] = true;\n      this.classMap[\"in\"] = true;\n      if (this.animation) {\n        this.classMap[\"fade\"] = true;\n      }\n      if (this.containerClass) {\n        this.classMap[this.containerClass] = true;\n      }\n    }\n    static {\n      this.ɵfac = function TooltipContainerComponent_Factory(t) {\n        return new (t || TooltipContainerComponent)(i0.ɵɵdirectiveInject(TooltipConfig));\n      };\n    }\n    static {\n      this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n        type: TooltipContainerComponent,\n        selectors: [[\"bs-tooltip-container\"]],\n        hostAttrs: [\"role\", \"tooltip\"],\n        hostVars: 3,\n        hostBindings: function TooltipContainerComponent_HostBindings(rf, ctx) {\n          if (rf & 2) {\n            i0.ɵɵattribute(\"id\", ctx.id);\n            i0.ɵɵclassMap(\"show tooltip in tooltip-\" + ctx.placement + \" \" + \"bs-tooltip-\" + ctx.placement + \" \" + ctx.placement + \" \" + ctx.containerClass);\n          }\n        },\n        ngContentSelectors: _c0,\n        decls: 3,\n        vars: 0,\n        consts: [[1, \"tooltip-arrow\", \"arrow\"], [1, \"tooltip-inner\"]],\n        template: function TooltipContainerComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵprojectionDef();\n            i0.ɵɵelement(0, \"div\", 0);\n            i0.ɵɵelementStart(1, \"div\", 1);\n            i0.ɵɵprojection(2);\n            i0.ɵɵelementEnd();\n          }\n        },\n        styles: [\".tooltip[_nghost-%COMP%]{display:block;pointer-events:none;position:absolute}.tooltip[_nghost-%COMP%]   .tooltip-arrow[_ngcontent-%COMP%]{position:absolute}\"],\n        changeDetection: 0\n      });\n    }\n  }\n  return TooltipContainerComponent;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet id = 0;\nlet TooltipDirective = /*#__PURE__*/(() => {\n  class TooltipDirective {\n    /**\n     * Returns whether or not the tooltip is currently being shown\n     */\n    get isOpen() {\n      return this._tooltip.isShown;\n    }\n    set isOpen(value) {\n      if (value) {\n        this.show();\n      } else {\n        this.hide();\n      }\n    }\n    /** @deprecated - please use `tooltip` instead */\n    set htmlContent(value) {\n      warnOnce('tooltipHtml was deprecated, please use `tooltip` instead');\n      this.tooltip = value;\n    }\n    /** @deprecated - please use `placement` instead */\n    // eslint-disable-next-line @angular-eslint/no-input-rename\n    set _placement(value) {\n      warnOnce('tooltipPlacement was deprecated, please use `placement` instead');\n      this.placement = value;\n    }\n    /** @deprecated - please use `isOpen` instead */\n    // eslint-disable-next-line @angular-eslint/no-input-rename\n    set _isOpen(value) {\n      warnOnce('tooltipIsOpen was deprecated, please use `isOpen` instead');\n      this.isOpen = value;\n    }\n    get _isOpen() {\n      warnOnce('tooltipIsOpen was deprecated, please use `isOpen` instead');\n      return this.isOpen;\n    }\n    /** @deprecated - please use `isDisabled` instead */\n    // eslint-disable-next-line @angular-eslint/no-input-rename\n    set _enable(value) {\n      warnOnce('tooltipEnable was deprecated, please use `isDisabled` instead');\n      this.isDisabled = !value;\n    }\n    get _enable() {\n      warnOnce('tooltipEnable was deprecated, please use `isDisabled` instead');\n      return this.isDisabled;\n    }\n    /** @deprecated - please use `container=\"body\"` instead */\n    // eslint-disable-next-line @angular-eslint/no-input-rename\n    set _appendToBody(value) {\n      warnOnce('tooltipAppendToBody was deprecated, please use `container=\"body\"` instead');\n      this.container = value ? 'body' : this.container;\n    }\n    get _appendToBody() {\n      warnOnce('tooltipAppendToBody was deprecated, please use `container=\"body\"` instead');\n      return this.container === 'body';\n    }\n    /** @deprecated - will replaced with customClass */\n    // eslint-disable-next-line @angular-eslint/no-input-rename\n    set _popupClass(value) {\n      warnOnce('tooltipClass deprecated');\n    }\n    /** @deprecated - removed */\n    // eslint-disable-next-line @angular-eslint/no-input-rename\n    set _tooltipContext(value) {\n      warnOnce('tooltipContext deprecated');\n    }\n    /** @deprecated */\n    // eslint-disable-next-line @angular-eslint/no-input-rename\n    set _tooltipPopupDelay(value) {\n      warnOnce('tooltipPopupDelay is deprecated, use `delay` instead');\n      this.delay = value;\n    }\n    /** @deprecated -  please use `triggers` instead */\n    get _tooltipTrigger() {\n      warnOnce('tooltipTrigger was deprecated, please use `triggers` instead');\n      return this.triggers;\n    }\n    set _tooltipTrigger(value) {\n      warnOnce('tooltipTrigger was deprecated, please use `triggers` instead');\n      this.triggers = (value || '').toString();\n    }\n    constructor(_viewContainerRef, cis, config, _elementRef, _renderer, _positionService) {\n      this._elementRef = _elementRef;\n      this._renderer = _renderer;\n      this._positionService = _positionService;\n      this.tooltipId = id++;\n      /** sets disable adaptive position */\n      this.adaptivePosition = true;\n      /** Fired when tooltip content changes */\n      this.tooltipChange = new EventEmitter();\n      /**\n       * Placement of a tooltip. Accepts: \"top\", \"bottom\", \"left\", \"right\"\n       */\n      this.placement = 'top';\n      /**\n       * Specifies events that should trigger. Supports a space separated list of\n       * event names.\n       */\n      this.triggers = 'hover focus';\n      /**\n       * Css class for tooltip container\n       */\n      this.containerClass = '';\n      /**\n       * Allows to disable tooltip\n       */\n      this.isDisabled = false;\n      /**\n       * Delay before showing the tooltip\n       */\n      this.delay = 0;\n      /** @deprecated - removed, will be added to configuration */\n      this.tooltipAnimation = true;\n      /** @deprecated */\n      this.tooltipFadeDuration = 150;\n      /** @deprecated */\n      this.tooltipStateChanged = new EventEmitter();\n      this._tooltip = cis.createLoader(this._elementRef, _viewContainerRef, this._renderer).provide({\n        provide: TooltipConfig,\n        useValue: config\n      });\n      Object.assign(this, config);\n      this.onShown = this._tooltip.onShown;\n      this.onHidden = this._tooltip.onHidden;\n    }\n    ngOnInit() {\n      this._tooltip.listen({\n        triggers: this.triggers,\n        show: () => this.show()\n      });\n      this.tooltipChange.subscribe(value => {\n        if (!value) {\n          this._tooltip.hide();\n        }\n      });\n      this.onShown.subscribe(() => {\n        this.setAriaDescribedBy();\n      });\n      this.onHidden.subscribe(() => {\n        this.setAriaDescribedBy();\n      });\n    }\n    setAriaDescribedBy() {\n      this._ariaDescribedby = this.isOpen ? `tooltip-${this.tooltipId}` : void 0;\n      if (this._ariaDescribedby) {\n        this._renderer.setAttribute(this._elementRef.nativeElement, 'aria-describedby', this._ariaDescribedby);\n      } else {\n        this._renderer.removeAttribute(this._elementRef.nativeElement, 'aria-describedby');\n      }\n    }\n    /**\n     * Toggles an element’s tooltip. This is considered a “manual” triggering of\n     * the tooltip.\n     */\n    toggle() {\n      if (this.isOpen) {\n        return this.hide();\n      }\n      this.show();\n    }\n    /**\n     * Opens an element’s tooltip. This is considered a “manual” triggering of\n     * the tooltip.\n     */\n    show() {\n      this._positionService.setOptions({\n        modifiers: {\n          flip: {\n            enabled: this.adaptivePosition\n          },\n          preventOverflow: {\n            enabled: this.adaptivePosition,\n            boundariesElement: this.boundariesElement || 'scrollParent'\n          }\n        }\n      });\n      if (this.isOpen || this.isDisabled || this._delayTimeoutId || !this.tooltip) {\n        return;\n      }\n      const showTooltip = () => {\n        if (this._delayTimeoutId) {\n          this._delayTimeoutId = undefined;\n        }\n        this._tooltip.attach(TooltipContainerComponent).to(this.container).position({\n          attachment: this.placement\n        }).show({\n          content: this.tooltip,\n          placement: this.placement,\n          containerClass: this.containerClass,\n          id: `tooltip-${this.tooltipId}`\n        });\n      };\n      const cancelDelayedTooltipShowing = () => {\n        if (this._tooltipCancelShowFn) {\n          this._tooltipCancelShowFn();\n        }\n      };\n      if (this.delay) {\n        if (this._delaySubscription) {\n          this._delaySubscription.unsubscribe();\n        }\n        this._delaySubscription = timer(this.delay).subscribe(() => {\n          showTooltip();\n          cancelDelayedTooltipShowing();\n        });\n        if (this.triggers) {\n          parseTriggers(this.triggers).forEach(trigger => {\n            if (!trigger.close) {\n              return;\n            }\n            this._tooltipCancelShowFn = this._renderer.listen(this._elementRef.nativeElement, trigger.close, () => {\n              this._delaySubscription?.unsubscribe();\n              cancelDelayedTooltipShowing();\n            });\n          });\n        }\n      } else {\n        showTooltip();\n      }\n    }\n    /**\n     * Closes an element’s tooltip. This is considered a “manual” triggering of\n     * the tooltip.\n     */\n    hide() {\n      if (this._delayTimeoutId) {\n        clearTimeout(this._delayTimeoutId);\n        this._delayTimeoutId = undefined;\n      }\n      if (!this._tooltip.isShown) {\n        return;\n      }\n      if (this._tooltip.instance?.classMap) {\n        this._tooltip.instance.classMap['in'] = false;\n      }\n      setTimeout(() => {\n        this._tooltip.hide();\n      }, this.tooltipFadeDuration);\n    }\n    ngOnDestroy() {\n      this._tooltip.dispose();\n      this.tooltipChange.unsubscribe();\n      if (this._delaySubscription) {\n        this._delaySubscription.unsubscribe();\n      }\n      this.onShown.unsubscribe();\n      this.onHidden.unsubscribe();\n    }\n    static {\n      this.ɵfac = function TooltipDirective_Factory(t) {\n        return new (t || TooltipDirective)(i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(i1.ComponentLoaderFactory), i0.ɵɵdirectiveInject(TooltipConfig), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i3.PositioningService));\n      };\n    }\n    static {\n      this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n        type: TooltipDirective,\n        selectors: [[\"\", \"tooltip\", \"\"], [\"\", \"tooltipHtml\", \"\"]],\n        inputs: {\n          adaptivePosition: \"adaptivePosition\",\n          tooltip: \"tooltip\",\n          placement: \"placement\",\n          triggers: \"triggers\",\n          container: \"container\",\n          containerClass: \"containerClass\",\n          boundariesElement: \"boundariesElement\",\n          isOpen: \"isOpen\",\n          isDisabled: \"isDisabled\",\n          delay: \"delay\",\n          htmlContent: [i0.ɵɵInputFlags.None, \"tooltipHtml\", \"htmlContent\"],\n          _placement: [i0.ɵɵInputFlags.None, \"tooltipPlacement\", \"_placement\"],\n          _isOpen: [i0.ɵɵInputFlags.None, \"tooltipIsOpen\", \"_isOpen\"],\n          _enable: [i0.ɵɵInputFlags.None, \"tooltipEnable\", \"_enable\"],\n          _appendToBody: [i0.ɵɵInputFlags.None, \"tooltipAppendToBody\", \"_appendToBody\"],\n          tooltipAnimation: \"tooltipAnimation\",\n          _popupClass: [i0.ɵɵInputFlags.None, \"tooltipClass\", \"_popupClass\"],\n          _tooltipContext: [i0.ɵɵInputFlags.None, \"tooltipContext\", \"_tooltipContext\"],\n          _tooltipPopupDelay: [i0.ɵɵInputFlags.None, \"tooltipPopupDelay\", \"_tooltipPopupDelay\"],\n          tooltipFadeDuration: \"tooltipFadeDuration\",\n          _tooltipTrigger: [i0.ɵɵInputFlags.None, \"tooltipTrigger\", \"_tooltipTrigger\"]\n        },\n        outputs: {\n          tooltipChange: \"tooltipChange\",\n          onShown: \"onShown\",\n          onHidden: \"onHidden\",\n          tooltipStateChanged: \"tooltipStateChanged\"\n        },\n        exportAs: [\"bs-tooltip\"]\n      });\n    }\n  }\n  __decorate([OnChange(), __metadata(\"design:type\", Object)], TooltipDirective.prototype, \"tooltip\", void 0);\n  return TooltipDirective;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet TooltipModule = /*#__PURE__*/(() => {\n  class TooltipModule {\n    static forRoot() {\n      return {\n        ngModule: TooltipModule,\n        providers: [ComponentLoaderFactory, PositioningService]\n      };\n    }\n    static {\n      this.ɵfac = function TooltipModule_Factory(t) {\n        return new (t || TooltipModule)();\n      };\n    }\n    static {\n      this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n        type: TooltipModule\n      });\n    }\n    static {\n      this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n        imports: [CommonModule]\n      });\n    }\n  }\n  return TooltipModule;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TooltipConfig, TooltipContainerComponent, TooltipDirective, TooltipModule };\n//# sourceMappingURL=ngx-bootstrap-tooltip.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}