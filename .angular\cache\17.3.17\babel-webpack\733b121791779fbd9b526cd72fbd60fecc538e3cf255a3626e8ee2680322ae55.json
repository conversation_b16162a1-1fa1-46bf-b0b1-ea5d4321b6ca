{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Northern Kurdish [ku-kmr]\n//! authors : <PERSON><PERSON><PERSON> : https://github.com/mergehez\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  function processRelativeTime(num, withoutSuffix, key, isFuture) {\n    var format = {\n      s: ['çend sanîye', 'çend sanîyeyan'],\n      ss: [num + ' sanîye', num + ' sanîyeyan'],\n      m: ['deqîqeyek', 'deqîqeyekê'],\n      mm: [num + ' deqîqe', num + ' deqîqeyan'],\n      h: ['saetek', 'saetekê'],\n      hh: [num + ' saet', num + ' saetan'],\n      d: ['rojek', 'rojekê'],\n      dd: [num + ' roj', num + ' rojan'],\n      w: ['hefteyek', 'hefteyekê'],\n      ww: [num + ' hefte', num + ' hefteyan'],\n      M: ['mehek', 'mehekê'],\n      MM: [num + ' meh', num + ' mehan'],\n      y: ['salek', 'salekê'],\n      yy: [num + ' sal', num + ' salan']\n    };\n    return withoutSuffix ? format[key][0] : format[key][1];\n  }\n  // function obliqueNumSuffix(num) {\n  //     if(num.includes(':'))\n  //         num = parseInt(num.split(':')[0]);\n  //     else\n  //         num = parseInt(num);\n  //     return num == 0 || num % 10 == 1 ? 'ê'\n  //                         : (num > 10 && num % 10 == 0 ? 'î' : 'an');\n  // }\n  function ezafeNumSuffix(num) {\n    num = '' + num;\n    var l = num.substring(num.length - 1),\n      ll = num.length > 1 ? num.substring(num.length - 2) : '';\n    if (!(ll == 12 || ll == 13) && (l == '2' || l == '3' || ll == '50' || l == '70' || l == '80')) return 'yê';\n    return 'ê';\n  }\n  var kuKmr = moment.defineLocale('ku-kmr', {\n    // According to the spelling rules defined by the work group of Weqfa Mezopotamyayê (Mesopotamia Foundation)\n    // this should be: 'Kanûna Paşîn_Sibat_Adar_Nîsan_Gulan_Hezîran_Tîrmeh_Tebax_Îlon_Çirîya Pêşîn_Çirîya Paşîn_Kanûna Pêşîn'\n    // But the names below are more well known and handy\n    months: 'Rêbendan_Sibat_Adar_Nîsan_Gulan_Hezîran_Tîrmeh_Tebax_Îlon_Cotmeh_Mijdar_Berfanbar'.split('_'),\n    monthsShort: 'Rêb_Sib_Ada_Nîs_Gul_Hez_Tîr_Teb_Îlo_Cot_Mij_Ber'.split('_'),\n    monthsParseExact: true,\n    weekdays: 'Yekşem_Duşem_Sêşem_Çarşem_Pêncşem_În_Şemî'.split('_'),\n    weekdaysShort: 'Yek_Du_Sê_Çar_Pên_În_Şem'.split('_'),\n    weekdaysMin: 'Ye_Du_Sê_Ça_Pê_În_Şe'.split('_'),\n    meridiem: function (hours, minutes, isLower) {\n      if (hours < 12) {\n        return isLower ? 'bn' : 'BN';\n      } else {\n        return isLower ? 'pn' : 'PN';\n      }\n    },\n    meridiemParse: /bn|BN|pn|PN/,\n    longDateFormat: {\n      LT: 'HH:mm',\n      LTS: 'HH:mm:ss',\n      L: 'DD.MM.YYYY',\n      LL: 'Do MMMM[a] YYYY[an]',\n      LLL: 'Do MMMM[a] YYYY[an] HH:mm',\n      LLLL: 'dddd, Do MMMM[a] YYYY[an] HH:mm',\n      ll: 'Do MMM[.] YYYY[an]',\n      lll: 'Do MMM[.] YYYY[an] HH:mm',\n      llll: 'ddd[.], Do MMM[.] YYYY[an] HH:mm'\n    },\n    calendar: {\n      sameDay: '[Îro di saet] LT [de]',\n      nextDay: '[Sibê di saet] LT [de]',\n      nextWeek: 'dddd [di saet] LT [de]',\n      lastDay: '[Duh di saet] LT [de]',\n      lastWeek: 'dddd[a borî di saet] LT [de]',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: 'di %s de',\n      past: 'berî %s',\n      s: processRelativeTime,\n      ss: processRelativeTime,\n      m: processRelativeTime,\n      mm: processRelativeTime,\n      h: processRelativeTime,\n      hh: processRelativeTime,\n      d: processRelativeTime,\n      dd: processRelativeTime,\n      w: processRelativeTime,\n      ww: processRelativeTime,\n      M: processRelativeTime,\n      MM: processRelativeTime,\n      y: processRelativeTime,\n      yy: processRelativeTime\n    },\n    dayOfMonthOrdinalParse: /\\d{1,2}(?:yê|ê|\\.)/,\n    ordinal: function (num, period) {\n      var p = period.toLowerCase();\n      if (p.includes('w') || p.includes('m')) return num + '.';\n      return num + ezafeNumSuffix(num);\n    },\n    week: {\n      dow: 1,\n      // Monday is the first day of the week.\n      doy: 4 // The week that contains Jan 4th is the first week of the year.\n    }\n  });\n  return kuKmr;\n});", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}