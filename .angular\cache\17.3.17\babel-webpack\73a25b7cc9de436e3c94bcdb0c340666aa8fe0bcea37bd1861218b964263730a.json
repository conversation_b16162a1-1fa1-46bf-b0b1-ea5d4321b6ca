{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Injectable, EventEmitter, Component, ChangeDetectionStrategy, Input, Output, ViewChild, HostBinding, Directive, forwardRef, Host, HostListener, NgModule } from '@angular/core';\nimport { filter, map, take, takeUntil, distinctUntilChanged } from 'rxjs/operators';\nimport { isFirstDayOfWeek, getDay, shiftDate, endOf, isBefore, startOf, isAfter, isArray, isSame, getFirstDayOfMonth, formatDate, getLocale, isSameMonth, isSameDay, isDisabledDay, isSameYear, isDateValid, setFullDate, getMonth, getFullYear, isDate, parseDate, utcAsLocal } from 'ngx-bootstrap/chronos';\nimport * as i5 from 'ngx-bootstrap/positioning';\nimport { PositioningService } from 'ngx-bootstrap/positioning';\nimport * as i7 from 'ngx-bootstrap/timepicker';\nimport { TimepickerActions, TimepickerModule } from 'ngx-bootstrap/timepicker';\nimport { style, state, animate, transition, trigger } from '@angular/animations';\nimport { Subscription, BehaviorSubject, combineLatest, Subject } from 'rxjs';\nimport { MiniStore, MiniState } from 'ngx-bootstrap/mini-ngrx';\nimport * as i6 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport * as i3 from 'ngx-bootstrap/tooltip';\nimport { TooltipModule } from 'ngx-bootstrap/tooltip';\nimport * as i2 from 'ngx-bootstrap/component-loader';\nimport { ComponentLoaderFactory } from 'ngx-bootstrap/component-loader';\nimport { NG_VALUE_ACCESSOR, NG_VALIDATORS } from '@angular/forms';\n\n/**\n * For date range picker there are `BsDaterangepickerConfig` which inherits all properties,\n * except `displayMonths`, for range picker it default to `2`\n */\nfunction BsCustomDatesViewComponent_button_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 2);\n    i0.ɵɵlistener(\"click\", function BsCustomDatesViewComponent_button_1_Template_button_click_0_listener() {\n      const range_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.selectFromRanges(range_r2));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const range_r2 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"selected\", range_r2.value === ctx_r2.selectedRange);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", range_r2.label, \" \");\n  }\n}\nconst _c0 = [[[\"bs-datepicker-navigation-view\"]], \"*\"];\nconst _c1 = [\"bs-datepicker-navigation-view\", \"*\"];\nfunction BsCalendarLayoutComponent_bs_current_date_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"bs-current-date\", 4);\n  }\n}\nfunction BsCalendarLayoutComponent_bs_timepicker_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"bs-timepicker\");\n  }\n}\nconst _c2 = [\"bsDatepickerDayDecorator\", \"\"];\nfunction BsDatepickerNavigationViewComponent_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1, \" \\u200B \");\n    i0.ɵɵelementStart(2, \"button\", 2);\n    i0.ɵɵlistener(\"click\", function BsDatepickerNavigationViewComponent_ng_container_3_Template_button_click_2_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.view(\"month\"));\n    });\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.isDisabled);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.calendar.monthTitle);\n  }\n}\nfunction BsDaysCalendarViewComponent_th_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"th\");\n  }\n}\nfunction BsDaysCalendarViewComponent_th_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 5);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const i_r1 = ctx.index;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"\", ctx_r1.calendar.weekdays[i_r1], \" \");\n  }\n}\nfunction BsDaysCalendarViewComponent_tr_8_td_1_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"span\", 11);\n    i0.ɵɵlistener(\"click\", function BsDaysCalendarViewComponent_tr_8_td_1_span_1_Template_span_click_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const week_r4 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.selectWeek(week_r4));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const i_r5 = i0.ɵɵnextContext(2).index;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.calendar.weekNumbers[i_r5]);\n  }\n}\nfunction BsDaysCalendarViewComponent_tr_8_td_1_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"span\", 12);\n    i0.ɵɵlistener(\"click\", function BsDaysCalendarViewComponent_tr_8_td_1_span_2_Template_span_click_0_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const week_r4 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.selectWeek(week_r4));\n    })(\"mouseenter\", function BsDaysCalendarViewComponent_tr_8_td_1_span_2_Template_span_mouseenter_0_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const week_r4 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.weekHoverHandler(week_r4, true));\n    })(\"mouseleave\", function BsDaysCalendarViewComponent_tr_8_td_1_span_2_Template_span_mouseleave_0_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const week_r4 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.weekHoverHandler(week_r4, false));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const i_r5 = i0.ɵɵnextContext(2).index;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.calendar.weekNumbers[i_r5]);\n  }\n}\nfunction BsDaysCalendarViewComponent_tr_8_td_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 8);\n    i0.ɵɵtemplate(1, BsDaysCalendarViewComponent_tr_8_td_1_span_1_Template, 2, 1, \"span\", 9)(2, BsDaysCalendarViewComponent_tr_8_td_1_span_2_Template, 2, 1, \"span\", 10);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"active-week\", ctx_r1.isWeekHovered);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isiOS);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.isiOS);\n  }\n}\nfunction BsDaysCalendarViewComponent_tr_8_td_2_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"span\", 17);\n    i0.ɵɵlistener(\"click\", function BsDaysCalendarViewComponent_tr_8_td_2_span_1_Template_span_click_0_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const day_r8 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.selectDay(day_r8));\n    })(\"mouseenter\", function BsDaysCalendarViewComponent_tr_8_td_2_span_1_Template_span_mouseenter_0_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const day_r8 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.hoverDay(day_r8, true));\n    })(\"mouseleave\", function BsDaysCalendarViewComponent_tr_8_td_2_span_1_Template_span_mouseleave_0_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const day_r8 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.hoverDay(day_r8, false));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const day_r8 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵpropertyInterpolate(\"tooltip\", day_r8.tooltipText);\n    i0.ɵɵproperty(\"day\", day_r8);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"\", day_r8.label, \" 3\");\n  }\n}\nfunction BsDaysCalendarViewComponent_tr_8_td_2_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"span\", 18);\n    i0.ɵɵlistener(\"click\", function BsDaysCalendarViewComponent_tr_8_td_2_span_2_Template_span_click_0_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const day_r8 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.selectDay(day_r8));\n    })(\"mouseenter\", function BsDaysCalendarViewComponent_tr_8_td_2_span_2_Template_span_mouseenter_0_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const day_r8 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.hoverDay(day_r8, true));\n    })(\"mouseleave\", function BsDaysCalendarViewComponent_tr_8_td_2_span_2_Template_span_mouseleave_0_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const day_r8 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.hoverDay(day_r8, false));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const day_r8 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"day\", day_r8);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"\", day_r8.label, \" 2\");\n  }\n}\nfunction BsDaysCalendarViewComponent_tr_8_td_2_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"span\", 19);\n    i0.ɵɵlistener(\"click\", function BsDaysCalendarViewComponent_tr_8_td_2_span_3_Template_span_click_0_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const day_r8 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.selectDay(day_r8));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const day_r8 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"day\", day_r8);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"\", day_r8.label, \" 1\");\n  }\n}\nfunction BsDaysCalendarViewComponent_tr_8_td_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 13);\n    i0.ɵɵtemplate(1, BsDaysCalendarViewComponent_tr_8_td_2_span_1_Template, 2, 3, \"span\", 14)(2, BsDaysCalendarViewComponent_tr_8_td_2_span_2_Template, 2, 2, \"span\", 15)(3, BsDaysCalendarViewComponent_tr_8_td_2_span_3_Template, 2, 2, \"span\", 16);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.isiOS && ctx_r1.isShowTooltip);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.isiOS && !ctx_r1.isShowTooltip);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isiOS);\n  }\n}\nfunction BsDaysCalendarViewComponent_tr_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\");\n    i0.ɵɵtemplate(1, BsDaysCalendarViewComponent_tr_8_td_1_Template, 3, 4, \"td\", 6)(2, BsDaysCalendarViewComponent_tr_8_td_2_Template, 4, 3, \"td\", 7);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const week_r4 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.options && ctx_r1.options.showWeekNumbers);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", week_r4.days);\n  }\n}\nfunction BsMonthCalendarViewComponent_tr_4_td_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"td\", 4);\n    i0.ɵɵlistener(\"click\", function BsMonthCalendarViewComponent_tr_4_td_1_Template_td_click_0_listener() {\n      const month_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.viewMonth(month_r2));\n    })(\"mouseenter\", function BsMonthCalendarViewComponent_tr_4_td_1_Template_td_mouseenter_0_listener() {\n      const month_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.hoverMonth(month_r2, true));\n    })(\"mouseleave\", function BsMonthCalendarViewComponent_tr_4_td_1_Template_td_mouseleave_0_listener() {\n      const month_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.hoverMonth(month_r2, false));\n    });\n    i0.ɵɵelementStart(1, \"span\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const month_r2 = ctx.$implicit;\n    i0.ɵɵclassProp(\"disabled\", month_r2.isDisabled)(\"is-highlighted\", month_r2.isHovered);\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"selected\", month_r2.isSelected);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(month_r2.label);\n  }\n}\nfunction BsMonthCalendarViewComponent_tr_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\");\n    i0.ɵɵtemplate(1, BsMonthCalendarViewComponent_tr_4_td_1_Template, 3, 7, \"td\", 3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const row_r4 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", row_r4);\n  }\n}\nfunction BsYearsCalendarViewComponent_tr_4_td_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"td\", 4);\n    i0.ɵɵlistener(\"click\", function BsYearsCalendarViewComponent_tr_4_td_1_Template_td_click_0_listener() {\n      const year_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.viewYear(year_r2));\n    })(\"mouseenter\", function BsYearsCalendarViewComponent_tr_4_td_1_Template_td_mouseenter_0_listener() {\n      const year_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.hoverYear(year_r2, true));\n    })(\"mouseleave\", function BsYearsCalendarViewComponent_tr_4_td_1_Template_td_mouseleave_0_listener() {\n      const year_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.hoverYear(year_r2, false));\n    });\n    i0.ɵɵelementStart(1, \"span\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const year_r2 = ctx.$implicit;\n    i0.ɵɵclassProp(\"disabled\", year_r2.isDisabled)(\"is-highlighted\", year_r2.isHovered);\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"selected\", year_r2.isSelected);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(year_r2.label);\n  }\n}\nfunction BsYearsCalendarViewComponent_tr_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\");\n    i0.ɵɵtemplate(1, BsYearsCalendarViewComponent_tr_4_td_1_Template, 3, 7, \"td\", 3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const row_r4 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", row_r4);\n  }\n}\nconst _c3 = [\"startTP\"];\nfunction BsDatepickerContainerComponent_div_0_ng_container_4_bs_days_calendar_view_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"bs-days-calendar-view\", 13);\n    i0.ɵɵpipe(1, \"async\");\n    i0.ɵɵlistener(\"onNavigate\", function BsDatepickerContainerComponent_div_0_ng_container_4_bs_days_calendar_view_2_Template_bs_days_calendar_view_onNavigate_0_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.navigateTo($event));\n    })(\"onViewMode\", function BsDatepickerContainerComponent_div_0_ng_container_4_bs_days_calendar_view_2_Template_bs_days_calendar_view_onViewMode_0_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.setViewMode($event));\n    })(\"onHover\", function BsDatepickerContainerComponent_div_0_ng_container_4_bs_days_calendar_view_2_Template_bs_days_calendar_view_onHover_0_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.dayHoverHandler($event));\n    })(\"onHoverWeek\", function BsDatepickerContainerComponent_div_0_ng_container_4_bs_days_calendar_view_2_Template_bs_days_calendar_view_onHoverWeek_0_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.weekHoverHandler($event));\n    })(\"onSelect\", function BsDatepickerContainerComponent_div_0_ng_container_4_bs_days_calendar_view_2_Template_bs_days_calendar_view_onSelect_0_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.daySelectHandler($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const calendar_r4 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵclassProp(\"bs-datepicker-multiple\", ctx_r1.multipleCalendars);\n    i0.ɵɵproperty(\"calendar\", calendar_r4)(\"isDisabled\", ctx_r1.isDatePickerDisabled)(\"options\", i0.ɵɵpipeBind1(1, 5, ctx_r1.options$));\n  }\n}\nfunction BsDatepickerContainerComponent_div_0_ng_container_4_div_4_timepicker_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"timepicker\", 15, 1);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.isDatePickerDisabled);\n  }\n}\nfunction BsDatepickerContainerComponent_div_0_ng_container_4_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 14);\n    i0.ɵɵelement(1, \"timepicker\", 15, 0);\n    i0.ɵɵtemplate(3, BsDatepickerContainerComponent_div_0_ng_container_4_div_4_timepicker_3_Template, 2, 1, \"timepicker\", 16);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.isDatePickerDisabled);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isRangePicker);\n  }\n}\nfunction BsDatepickerContainerComponent_div_0_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 10);\n    i0.ɵɵtemplate(2, BsDatepickerContainerComponent_div_0_ng_container_4_bs_days_calendar_view_2_Template, 2, 7, \"bs-days-calendar-view\", 11);\n    i0.ɵɵpipe(3, \"async\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, BsDatepickerContainerComponent_div_0_ng_container_4_div_4_Template, 4, 2, \"div\", 12);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind1(3, 2, ctx_r1.daysCalendar$));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.withTimepicker);\n  }\n}\nfunction BsDatepickerContainerComponent_div_0_div_5_bs_month_calendar_view_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"bs-month-calendar-view\", 18);\n    i0.ɵɵlistener(\"onNavigate\", function BsDatepickerContainerComponent_div_0_div_5_bs_month_calendar_view_1_Template_bs_month_calendar_view_onNavigate_0_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.navigateTo($event));\n    })(\"onViewMode\", function BsDatepickerContainerComponent_div_0_div_5_bs_month_calendar_view_1_Template_bs_month_calendar_view_onViewMode_0_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.setViewMode($event));\n    })(\"onHover\", function BsDatepickerContainerComponent_div_0_div_5_bs_month_calendar_view_1_Template_bs_month_calendar_view_onHover_0_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.monthHoverHandler($event));\n    })(\"onSelect\", function BsDatepickerContainerComponent_div_0_div_5_bs_month_calendar_view_1_Template_bs_month_calendar_view_onSelect_0_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.monthSelectHandler($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const calendar_r6 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵclassProp(\"bs-datepicker-multiple\", ctx_r1.multipleCalendars);\n    i0.ɵɵproperty(\"calendar\", calendar_r6);\n  }\n}\nfunction BsDatepickerContainerComponent_div_0_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10);\n    i0.ɵɵtemplate(1, BsDatepickerContainerComponent_div_0_div_5_bs_month_calendar_view_1_Template, 1, 3, \"bs-month-calendar-view\", 17);\n    i0.ɵɵpipe(2, \"async\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind1(2, 1, ctx_r1.monthsCalendar));\n  }\n}\nfunction BsDatepickerContainerComponent_div_0_div_6_bs_years_calendar_view_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"bs-years-calendar-view\", 18);\n    i0.ɵɵlistener(\"onNavigate\", function BsDatepickerContainerComponent_div_0_div_6_bs_years_calendar_view_1_Template_bs_years_calendar_view_onNavigate_0_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.navigateTo($event));\n    })(\"onViewMode\", function BsDatepickerContainerComponent_div_0_div_6_bs_years_calendar_view_1_Template_bs_years_calendar_view_onViewMode_0_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.setViewMode($event));\n    })(\"onHover\", function BsDatepickerContainerComponent_div_0_div_6_bs_years_calendar_view_1_Template_bs_years_calendar_view_onHover_0_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.yearHoverHandler($event));\n    })(\"onSelect\", function BsDatepickerContainerComponent_div_0_div_6_bs_years_calendar_view_1_Template_bs_years_calendar_view_onSelect_0_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.yearSelectHandler($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const calendar_r8 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵclassProp(\"bs-datepicker-multiple\", ctx_r1.multipleCalendars);\n    i0.ɵɵproperty(\"calendar\", calendar_r8);\n  }\n}\nfunction BsDatepickerContainerComponent_div_0_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10);\n    i0.ɵɵtemplate(1, BsDatepickerContainerComponent_div_0_div_6_bs_years_calendar_view_1_Template, 1, 3, \"bs-years-calendar-view\", 17);\n    i0.ɵɵpipe(2, \"async\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind1(2, 1, ctx_r1.yearsCalendar));\n  }\n}\nfunction BsDatepickerContainerComponent_div_0_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 19)(1, \"button\", 20);\n    i0.ɵɵtext(2, \"Apply\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 21);\n    i0.ɵɵtext(4, \"Cancel\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction BsDatepickerContainerComponent_div_0_div_8_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 24)(1, \"button\", 25);\n    i0.ɵɵlistener(\"click\", function BsDatepickerContainerComponent_div_0_div_8_div_1_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.setToday());\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵclassProp(\"today-left\", ctx_r1.todayPos === \"left\")(\"today-right\", ctx_r1.todayPos === \"right\")(\"today-center\", ctx_r1.todayPos === \"center\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.todayBtnLbl);\n  }\n}\nfunction BsDatepickerContainerComponent_div_0_div_8_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 26)(1, \"button\", 25);\n    i0.ɵɵlistener(\"click\", function BsDatepickerContainerComponent_div_0_div_8_div_2_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.clearDate());\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵclassProp(\"clear-left\", ctx_r1.clearPos === \"left\")(\"clear-right\", ctx_r1.clearPos === \"right\")(\"clear-center\", ctx_r1.clearPos === \"center\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.clearBtnLbl);\n  }\n}\nfunction BsDatepickerContainerComponent_div_0_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 19);\n    i0.ɵɵtemplate(1, BsDatepickerContainerComponent_div_0_div_8_div_1_Template, 3, 7, \"div\", 22)(2, BsDatepickerContainerComponent_div_0_div_8_div_2_Template, 3, 7, \"div\", 23);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.showTodayBtn);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.showClearBtn);\n  }\n}\nfunction BsDatepickerContainerComponent_div_0_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 27)(1, \"bs-custom-date-view\", 28);\n    i0.ɵɵlistener(\"onSelect\", function BsDatepickerContainerComponent_div_0_div_9_Template_bs_custom_date_view_onSelect_1_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.setRangeOnCalendar($event));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"selectedRange\", ctx_r1.chosenRange)(\"ranges\", ctx_r1.customRanges)(\"customRangeLabel\", ctx_r1.customRangeBtnLbl);\n  }\n}\nfunction BsDatepickerContainerComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 3)(1, \"div\", 4);\n    i0.ɵɵlistener(\"@datepickerAnimation.done\", function BsDatepickerContainerComponent_div_0_Template_div_animation_datepickerAnimation_done_1_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.positionServiceEnable());\n    });\n    i0.ɵɵelementStart(2, \"div\", 5);\n    i0.ɵɵpipe(3, \"async\");\n    i0.ɵɵtemplate(4, BsDatepickerContainerComponent_div_0_ng_container_4_Template, 5, 4, \"ng-container\", 6)(5, BsDatepickerContainerComponent_div_0_div_5_Template, 3, 3, \"div\", 7)(6, BsDatepickerContainerComponent_div_0_div_6_Template, 3, 3, \"div\", 7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(7, BsDatepickerContainerComponent_div_0_div_7_Template, 5, 0, \"div\", 8)(8, BsDatepickerContainerComponent_div_0_div_8_Template, 3, 2, \"div\", 8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(9, BsDatepickerContainerComponent_div_0_div_9_Template, 2, 3, \"div\", 9);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.containerClass);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"@datepickerAnimation\", ctx_r1.animationState);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitch\", i0.ɵɵpipeBind1(3, 9, ctx_r1.viewMode));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngSwitchCase\", \"day\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"month\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"year\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", false);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.showTodayBtn || ctx_r1.showClearBtn);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.customRanges && ctx_r1.customRanges.length > 0);\n  }\n}\nfunction BsDatepickerInlineContainerComponent_div_0_ng_container_4_bs_days_calendar_view_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"bs-days-calendar-view\", 13);\n    i0.ɵɵpipe(1, \"async\");\n    i0.ɵɵlistener(\"onNavigate\", function BsDatepickerInlineContainerComponent_div_0_ng_container_4_bs_days_calendar_view_2_Template_bs_days_calendar_view_onNavigate_0_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.navigateTo($event));\n    })(\"onViewMode\", function BsDatepickerInlineContainerComponent_div_0_ng_container_4_bs_days_calendar_view_2_Template_bs_days_calendar_view_onViewMode_0_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.setViewMode($event));\n    })(\"onHover\", function BsDatepickerInlineContainerComponent_div_0_ng_container_4_bs_days_calendar_view_2_Template_bs_days_calendar_view_onHover_0_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.dayHoverHandler($event));\n    })(\"onHoverWeek\", function BsDatepickerInlineContainerComponent_div_0_ng_container_4_bs_days_calendar_view_2_Template_bs_days_calendar_view_onHoverWeek_0_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.weekHoverHandler($event));\n    })(\"onSelect\", function BsDatepickerInlineContainerComponent_div_0_ng_container_4_bs_days_calendar_view_2_Template_bs_days_calendar_view_onSelect_0_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.daySelectHandler($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const calendar_r4 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵclassProp(\"bs-datepicker-multiple\", ctx_r1.multipleCalendars);\n    i0.ɵɵproperty(\"calendar\", calendar_r4)(\"isDisabled\", ctx_r1.isDatePickerDisabled)(\"options\", i0.ɵɵpipeBind1(1, 5, ctx_r1.options$));\n  }\n}\nfunction BsDatepickerInlineContainerComponent_div_0_ng_container_4_div_4_timepicker_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"timepicker\", 15, 1);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.isDatePickerDisabled);\n  }\n}\nfunction BsDatepickerInlineContainerComponent_div_0_ng_container_4_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 14);\n    i0.ɵɵelement(1, \"timepicker\", 15, 0);\n    i0.ɵɵtemplate(3, BsDatepickerInlineContainerComponent_div_0_ng_container_4_div_4_timepicker_3_Template, 2, 1, \"timepicker\", 16);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.isDatePickerDisabled);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isRangePicker);\n  }\n}\nfunction BsDatepickerInlineContainerComponent_div_0_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 10);\n    i0.ɵɵtemplate(2, BsDatepickerInlineContainerComponent_div_0_ng_container_4_bs_days_calendar_view_2_Template, 2, 7, \"bs-days-calendar-view\", 11);\n    i0.ɵɵpipe(3, \"async\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, BsDatepickerInlineContainerComponent_div_0_ng_container_4_div_4_Template, 4, 2, \"div\", 12);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind1(3, 2, ctx_r1.daysCalendar$));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.withTimepicker);\n  }\n}\nfunction BsDatepickerInlineContainerComponent_div_0_div_5_bs_month_calendar_view_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"bs-month-calendar-view\", 18);\n    i0.ɵɵlistener(\"onNavigate\", function BsDatepickerInlineContainerComponent_div_0_div_5_bs_month_calendar_view_1_Template_bs_month_calendar_view_onNavigate_0_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.navigateTo($event));\n    })(\"onViewMode\", function BsDatepickerInlineContainerComponent_div_0_div_5_bs_month_calendar_view_1_Template_bs_month_calendar_view_onViewMode_0_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.setViewMode($event));\n    })(\"onHover\", function BsDatepickerInlineContainerComponent_div_0_div_5_bs_month_calendar_view_1_Template_bs_month_calendar_view_onHover_0_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.monthHoverHandler($event));\n    })(\"onSelect\", function BsDatepickerInlineContainerComponent_div_0_div_5_bs_month_calendar_view_1_Template_bs_month_calendar_view_onSelect_0_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.monthSelectHandler($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const calendar_r6 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵclassProp(\"bs-datepicker-multiple\", ctx_r1.multipleCalendars);\n    i0.ɵɵproperty(\"calendar\", calendar_r6);\n  }\n}\nfunction BsDatepickerInlineContainerComponent_div_0_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10);\n    i0.ɵɵtemplate(1, BsDatepickerInlineContainerComponent_div_0_div_5_bs_month_calendar_view_1_Template, 1, 3, \"bs-month-calendar-view\", 17);\n    i0.ɵɵpipe(2, \"async\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind1(2, 1, ctx_r1.monthsCalendar));\n  }\n}\nfunction BsDatepickerInlineContainerComponent_div_0_div_6_bs_years_calendar_view_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"bs-years-calendar-view\", 18);\n    i0.ɵɵlistener(\"onNavigate\", function BsDatepickerInlineContainerComponent_div_0_div_6_bs_years_calendar_view_1_Template_bs_years_calendar_view_onNavigate_0_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.navigateTo($event));\n    })(\"onViewMode\", function BsDatepickerInlineContainerComponent_div_0_div_6_bs_years_calendar_view_1_Template_bs_years_calendar_view_onViewMode_0_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.setViewMode($event));\n    })(\"onHover\", function BsDatepickerInlineContainerComponent_div_0_div_6_bs_years_calendar_view_1_Template_bs_years_calendar_view_onHover_0_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.yearHoverHandler($event));\n    })(\"onSelect\", function BsDatepickerInlineContainerComponent_div_0_div_6_bs_years_calendar_view_1_Template_bs_years_calendar_view_onSelect_0_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.yearSelectHandler($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const calendar_r8 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵclassProp(\"bs-datepicker-multiple\", ctx_r1.multipleCalendars);\n    i0.ɵɵproperty(\"calendar\", calendar_r8);\n  }\n}\nfunction BsDatepickerInlineContainerComponent_div_0_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10);\n    i0.ɵɵtemplate(1, BsDatepickerInlineContainerComponent_div_0_div_6_bs_years_calendar_view_1_Template, 1, 3, \"bs-years-calendar-view\", 17);\n    i0.ɵɵpipe(2, \"async\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind1(2, 1, ctx_r1.yearsCalendar));\n  }\n}\nfunction BsDatepickerInlineContainerComponent_div_0_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 19)(1, \"button\", 20);\n    i0.ɵɵtext(2, \"Apply\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 21);\n    i0.ɵɵtext(4, \"Cancel\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction BsDatepickerInlineContainerComponent_div_0_div_8_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 24)(1, \"button\", 25);\n    i0.ɵɵlistener(\"click\", function BsDatepickerInlineContainerComponent_div_0_div_8_div_1_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.setToday());\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵclassProp(\"today-left\", ctx_r1.todayPos === \"left\")(\"today-right\", ctx_r1.todayPos === \"right\")(\"today-center\", ctx_r1.todayPos === \"center\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.todayBtnLbl);\n  }\n}\nfunction BsDatepickerInlineContainerComponent_div_0_div_8_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 26)(1, \"button\", 25);\n    i0.ɵɵlistener(\"click\", function BsDatepickerInlineContainerComponent_div_0_div_8_div_2_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.clearDate());\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵclassProp(\"clear-left\", ctx_r1.clearPos === \"left\")(\"clear-right\", ctx_r1.clearPos === \"right\")(\"clear-center\", ctx_r1.clearPos === \"center\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.clearBtnLbl);\n  }\n}\nfunction BsDatepickerInlineContainerComponent_div_0_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 19);\n    i0.ɵɵtemplate(1, BsDatepickerInlineContainerComponent_div_0_div_8_div_1_Template, 3, 7, \"div\", 22)(2, BsDatepickerInlineContainerComponent_div_0_div_8_div_2_Template, 3, 7, \"div\", 23);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.showTodayBtn);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.showClearBtn);\n  }\n}\nfunction BsDatepickerInlineContainerComponent_div_0_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 27)(1, \"bs-custom-date-view\", 28);\n    i0.ɵɵlistener(\"onSelect\", function BsDatepickerInlineContainerComponent_div_0_div_9_Template_bs_custom_date_view_onSelect_1_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.setRangeOnCalendar($event));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"selectedRange\", ctx_r1.chosenRange)(\"ranges\", ctx_r1.customRanges)(\"customRangeLabel\", ctx_r1.customRangeBtnLbl);\n  }\n}\nfunction BsDatepickerInlineContainerComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 3)(1, \"div\", 4);\n    i0.ɵɵlistener(\"@datepickerAnimation.done\", function BsDatepickerInlineContainerComponent_div_0_Template_div_animation_datepickerAnimation_done_1_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.positionServiceEnable());\n    });\n    i0.ɵɵelementStart(2, \"div\", 5);\n    i0.ɵɵpipe(3, \"async\");\n    i0.ɵɵtemplate(4, BsDatepickerInlineContainerComponent_div_0_ng_container_4_Template, 5, 4, \"ng-container\", 6)(5, BsDatepickerInlineContainerComponent_div_0_div_5_Template, 3, 3, \"div\", 7)(6, BsDatepickerInlineContainerComponent_div_0_div_6_Template, 3, 3, \"div\", 7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(7, BsDatepickerInlineContainerComponent_div_0_div_7_Template, 5, 0, \"div\", 8)(8, BsDatepickerInlineContainerComponent_div_0_div_8_Template, 3, 2, \"div\", 8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(9, BsDatepickerInlineContainerComponent_div_0_div_9_Template, 2, 3, \"div\", 9);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.containerClass);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"@datepickerAnimation\", ctx_r1.animationState);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitch\", i0.ɵɵpipeBind1(3, 9, ctx_r1.viewMode));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngSwitchCase\", \"day\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"month\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"year\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", false);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.showTodayBtn || ctx_r1.showClearBtn);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.customRanges && ctx_r1.customRanges.length > 0);\n  }\n}\nconst _c4 = [\"endTP\"];\nfunction BsDaterangepickerContainerComponent_div_0_ng_container_4_bs_days_calendar_view_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"bs-days-calendar-view\", 13);\n    i0.ɵɵpipe(1, \"async\");\n    i0.ɵɵlistener(\"onNavigate\", function BsDaterangepickerContainerComponent_div_0_ng_container_4_bs_days_calendar_view_2_Template_bs_days_calendar_view_onNavigate_0_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.navigateTo($event));\n    })(\"onViewMode\", function BsDaterangepickerContainerComponent_div_0_ng_container_4_bs_days_calendar_view_2_Template_bs_days_calendar_view_onViewMode_0_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.setViewMode($event));\n    })(\"onHover\", function BsDaterangepickerContainerComponent_div_0_ng_container_4_bs_days_calendar_view_2_Template_bs_days_calendar_view_onHover_0_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.dayHoverHandler($event));\n    })(\"onHoverWeek\", function BsDaterangepickerContainerComponent_div_0_ng_container_4_bs_days_calendar_view_2_Template_bs_days_calendar_view_onHoverWeek_0_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.weekHoverHandler($event));\n    })(\"onSelect\", function BsDaterangepickerContainerComponent_div_0_ng_container_4_bs_days_calendar_view_2_Template_bs_days_calendar_view_onSelect_0_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.daySelectHandler($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const calendar_r4 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵclassProp(\"bs-datepicker-multiple\", ctx_r1.multipleCalendars);\n    i0.ɵɵproperty(\"calendar\", calendar_r4)(\"isDisabled\", ctx_r1.isDatePickerDisabled)(\"options\", i0.ɵɵpipeBind1(1, 5, ctx_r1.options$));\n  }\n}\nfunction BsDaterangepickerContainerComponent_div_0_ng_container_4_div_4_timepicker_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"timepicker\", 15, 1);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.isDatePickerDisabled);\n  }\n}\nfunction BsDaterangepickerContainerComponent_div_0_ng_container_4_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 14);\n    i0.ɵɵelement(1, \"timepicker\", 15, 0);\n    i0.ɵɵtemplate(3, BsDaterangepickerContainerComponent_div_0_ng_container_4_div_4_timepicker_3_Template, 2, 1, \"timepicker\", 16);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.isDatePickerDisabled);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isRangePicker);\n  }\n}\nfunction BsDaterangepickerContainerComponent_div_0_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 10);\n    i0.ɵɵtemplate(2, BsDaterangepickerContainerComponent_div_0_ng_container_4_bs_days_calendar_view_2_Template, 2, 7, \"bs-days-calendar-view\", 11);\n    i0.ɵɵpipe(3, \"async\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, BsDaterangepickerContainerComponent_div_0_ng_container_4_div_4_Template, 4, 2, \"div\", 12);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind1(3, 2, ctx_r1.daysCalendar$));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.withTimepicker);\n  }\n}\nfunction BsDaterangepickerContainerComponent_div_0_div_5_bs_month_calendar_view_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"bs-month-calendar-view\", 18);\n    i0.ɵɵlistener(\"onNavigate\", function BsDaterangepickerContainerComponent_div_0_div_5_bs_month_calendar_view_1_Template_bs_month_calendar_view_onNavigate_0_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.navigateTo($event));\n    })(\"onViewMode\", function BsDaterangepickerContainerComponent_div_0_div_5_bs_month_calendar_view_1_Template_bs_month_calendar_view_onViewMode_0_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.setViewMode($event));\n    })(\"onHover\", function BsDaterangepickerContainerComponent_div_0_div_5_bs_month_calendar_view_1_Template_bs_month_calendar_view_onHover_0_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.monthHoverHandler($event));\n    })(\"onSelect\", function BsDaterangepickerContainerComponent_div_0_div_5_bs_month_calendar_view_1_Template_bs_month_calendar_view_onSelect_0_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.monthSelectHandler($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const calendar_r6 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵclassProp(\"bs-datepicker-multiple\", ctx_r1.multipleCalendars);\n    i0.ɵɵproperty(\"calendar\", calendar_r6);\n  }\n}\nfunction BsDaterangepickerContainerComponent_div_0_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10);\n    i0.ɵɵtemplate(1, BsDaterangepickerContainerComponent_div_0_div_5_bs_month_calendar_view_1_Template, 1, 3, \"bs-month-calendar-view\", 17);\n    i0.ɵɵpipe(2, \"async\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind1(2, 1, ctx_r1.monthsCalendar));\n  }\n}\nfunction BsDaterangepickerContainerComponent_div_0_div_6_bs_years_calendar_view_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"bs-years-calendar-view\", 18);\n    i0.ɵɵlistener(\"onNavigate\", function BsDaterangepickerContainerComponent_div_0_div_6_bs_years_calendar_view_1_Template_bs_years_calendar_view_onNavigate_0_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.navigateTo($event));\n    })(\"onViewMode\", function BsDaterangepickerContainerComponent_div_0_div_6_bs_years_calendar_view_1_Template_bs_years_calendar_view_onViewMode_0_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.setViewMode($event));\n    })(\"onHover\", function BsDaterangepickerContainerComponent_div_0_div_6_bs_years_calendar_view_1_Template_bs_years_calendar_view_onHover_0_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.yearHoverHandler($event));\n    })(\"onSelect\", function BsDaterangepickerContainerComponent_div_0_div_6_bs_years_calendar_view_1_Template_bs_years_calendar_view_onSelect_0_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.yearSelectHandler($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const calendar_r8 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵclassProp(\"bs-datepicker-multiple\", ctx_r1.multipleCalendars);\n    i0.ɵɵproperty(\"calendar\", calendar_r8);\n  }\n}\nfunction BsDaterangepickerContainerComponent_div_0_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10);\n    i0.ɵɵtemplate(1, BsDaterangepickerContainerComponent_div_0_div_6_bs_years_calendar_view_1_Template, 1, 3, \"bs-years-calendar-view\", 17);\n    i0.ɵɵpipe(2, \"async\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind1(2, 1, ctx_r1.yearsCalendar));\n  }\n}\nfunction BsDaterangepickerContainerComponent_div_0_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 19)(1, \"button\", 20);\n    i0.ɵɵtext(2, \"Apply\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 21);\n    i0.ɵɵtext(4, \"Cancel\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction BsDaterangepickerContainerComponent_div_0_div_8_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 24)(1, \"button\", 25);\n    i0.ɵɵlistener(\"click\", function BsDaterangepickerContainerComponent_div_0_div_8_div_1_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.setToday());\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵclassProp(\"today-left\", ctx_r1.todayPos === \"left\")(\"today-right\", ctx_r1.todayPos === \"right\")(\"today-center\", ctx_r1.todayPos === \"center\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.todayBtnLbl);\n  }\n}\nfunction BsDaterangepickerContainerComponent_div_0_div_8_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 26)(1, \"button\", 25);\n    i0.ɵɵlistener(\"click\", function BsDaterangepickerContainerComponent_div_0_div_8_div_2_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.clearDate());\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵclassProp(\"clear-left\", ctx_r1.clearPos === \"left\")(\"clear-right\", ctx_r1.clearPos === \"right\")(\"clear-center\", ctx_r1.clearPos === \"center\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.clearBtnLbl);\n  }\n}\nfunction BsDaterangepickerContainerComponent_div_0_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 19);\n    i0.ɵɵtemplate(1, BsDaterangepickerContainerComponent_div_0_div_8_div_1_Template, 3, 7, \"div\", 22)(2, BsDaterangepickerContainerComponent_div_0_div_8_div_2_Template, 3, 7, \"div\", 23);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.showTodayBtn);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.showClearBtn);\n  }\n}\nfunction BsDaterangepickerContainerComponent_div_0_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 27)(1, \"bs-custom-date-view\", 28);\n    i0.ɵɵlistener(\"onSelect\", function BsDaterangepickerContainerComponent_div_0_div_9_Template_bs_custom_date_view_onSelect_1_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.setRangeOnCalendar($event));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"selectedRange\", ctx_r1.chosenRange)(\"ranges\", ctx_r1.customRanges)(\"customRangeLabel\", ctx_r1.customRangeBtnLbl);\n  }\n}\nfunction BsDaterangepickerContainerComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 3)(1, \"div\", 4);\n    i0.ɵɵlistener(\"@datepickerAnimation.done\", function BsDaterangepickerContainerComponent_div_0_Template_div_animation_datepickerAnimation_done_1_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.positionServiceEnable());\n    });\n    i0.ɵɵelementStart(2, \"div\", 5);\n    i0.ɵɵpipe(3, \"async\");\n    i0.ɵɵtemplate(4, BsDaterangepickerContainerComponent_div_0_ng_container_4_Template, 5, 4, \"ng-container\", 6)(5, BsDaterangepickerContainerComponent_div_0_div_5_Template, 3, 3, \"div\", 7)(6, BsDaterangepickerContainerComponent_div_0_div_6_Template, 3, 3, \"div\", 7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(7, BsDaterangepickerContainerComponent_div_0_div_7_Template, 5, 0, \"div\", 8)(8, BsDaterangepickerContainerComponent_div_0_div_8_Template, 3, 2, \"div\", 8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(9, BsDaterangepickerContainerComponent_div_0_div_9_Template, 2, 3, \"div\", 9);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.containerClass);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"@datepickerAnimation\", ctx_r1.animationState);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitch\", i0.ɵɵpipeBind1(3, 9, ctx_r1.viewMode));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngSwitchCase\", \"day\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"month\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"year\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", false);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.showTodayBtn || ctx_r1.showClearBtn);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.customRanges && ctx_r1.customRanges.length > 0);\n  }\n}\nfunction BsDaterangepickerInlineContainerComponent_div_0_ng_container_4_bs_days_calendar_view_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"bs-days-calendar-view\", 13);\n    i0.ɵɵpipe(1, \"async\");\n    i0.ɵɵlistener(\"onNavigate\", function BsDaterangepickerInlineContainerComponent_div_0_ng_container_4_bs_days_calendar_view_2_Template_bs_days_calendar_view_onNavigate_0_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.navigateTo($event));\n    })(\"onViewMode\", function BsDaterangepickerInlineContainerComponent_div_0_ng_container_4_bs_days_calendar_view_2_Template_bs_days_calendar_view_onViewMode_0_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.setViewMode($event));\n    })(\"onHover\", function BsDaterangepickerInlineContainerComponent_div_0_ng_container_4_bs_days_calendar_view_2_Template_bs_days_calendar_view_onHover_0_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.dayHoverHandler($event));\n    })(\"onHoverWeek\", function BsDaterangepickerInlineContainerComponent_div_0_ng_container_4_bs_days_calendar_view_2_Template_bs_days_calendar_view_onHoverWeek_0_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.weekHoverHandler($event));\n    })(\"onSelect\", function BsDaterangepickerInlineContainerComponent_div_0_ng_container_4_bs_days_calendar_view_2_Template_bs_days_calendar_view_onSelect_0_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.daySelectHandler($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const calendar_r4 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵclassProp(\"bs-datepicker-multiple\", ctx_r1.multipleCalendars);\n    i0.ɵɵproperty(\"calendar\", calendar_r4)(\"isDisabled\", ctx_r1.isDatePickerDisabled)(\"options\", i0.ɵɵpipeBind1(1, 5, ctx_r1.options$));\n  }\n}\nfunction BsDaterangepickerInlineContainerComponent_div_0_ng_container_4_div_4_timepicker_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"timepicker\", 15, 1);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.isDatePickerDisabled);\n  }\n}\nfunction BsDaterangepickerInlineContainerComponent_div_0_ng_container_4_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 14);\n    i0.ɵɵelement(1, \"timepicker\", 15, 0);\n    i0.ɵɵtemplate(3, BsDaterangepickerInlineContainerComponent_div_0_ng_container_4_div_4_timepicker_3_Template, 2, 1, \"timepicker\", 16);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.isDatePickerDisabled);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isRangePicker);\n  }\n}\nfunction BsDaterangepickerInlineContainerComponent_div_0_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 10);\n    i0.ɵɵtemplate(2, BsDaterangepickerInlineContainerComponent_div_0_ng_container_4_bs_days_calendar_view_2_Template, 2, 7, \"bs-days-calendar-view\", 11);\n    i0.ɵɵpipe(3, \"async\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, BsDaterangepickerInlineContainerComponent_div_0_ng_container_4_div_4_Template, 4, 2, \"div\", 12);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind1(3, 2, ctx_r1.daysCalendar$));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.withTimepicker);\n  }\n}\nfunction BsDaterangepickerInlineContainerComponent_div_0_div_5_bs_month_calendar_view_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"bs-month-calendar-view\", 18);\n    i0.ɵɵlistener(\"onNavigate\", function BsDaterangepickerInlineContainerComponent_div_0_div_5_bs_month_calendar_view_1_Template_bs_month_calendar_view_onNavigate_0_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.navigateTo($event));\n    })(\"onViewMode\", function BsDaterangepickerInlineContainerComponent_div_0_div_5_bs_month_calendar_view_1_Template_bs_month_calendar_view_onViewMode_0_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.setViewMode($event));\n    })(\"onHover\", function BsDaterangepickerInlineContainerComponent_div_0_div_5_bs_month_calendar_view_1_Template_bs_month_calendar_view_onHover_0_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.monthHoverHandler($event));\n    })(\"onSelect\", function BsDaterangepickerInlineContainerComponent_div_0_div_5_bs_month_calendar_view_1_Template_bs_month_calendar_view_onSelect_0_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.monthSelectHandler($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const calendar_r6 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵclassProp(\"bs-datepicker-multiple\", ctx_r1.multipleCalendars);\n    i0.ɵɵproperty(\"calendar\", calendar_r6);\n  }\n}\nfunction BsDaterangepickerInlineContainerComponent_div_0_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10);\n    i0.ɵɵtemplate(1, BsDaterangepickerInlineContainerComponent_div_0_div_5_bs_month_calendar_view_1_Template, 1, 3, \"bs-month-calendar-view\", 17);\n    i0.ɵɵpipe(2, \"async\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind1(2, 1, ctx_r1.monthsCalendar));\n  }\n}\nfunction BsDaterangepickerInlineContainerComponent_div_0_div_6_bs_years_calendar_view_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"bs-years-calendar-view\", 18);\n    i0.ɵɵlistener(\"onNavigate\", function BsDaterangepickerInlineContainerComponent_div_0_div_6_bs_years_calendar_view_1_Template_bs_years_calendar_view_onNavigate_0_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.navigateTo($event));\n    })(\"onViewMode\", function BsDaterangepickerInlineContainerComponent_div_0_div_6_bs_years_calendar_view_1_Template_bs_years_calendar_view_onViewMode_0_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.setViewMode($event));\n    })(\"onHover\", function BsDaterangepickerInlineContainerComponent_div_0_div_6_bs_years_calendar_view_1_Template_bs_years_calendar_view_onHover_0_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.yearHoverHandler($event));\n    })(\"onSelect\", function BsDaterangepickerInlineContainerComponent_div_0_div_6_bs_years_calendar_view_1_Template_bs_years_calendar_view_onSelect_0_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.yearSelectHandler($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const calendar_r8 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵclassProp(\"bs-datepicker-multiple\", ctx_r1.multipleCalendars);\n    i0.ɵɵproperty(\"calendar\", calendar_r8);\n  }\n}\nfunction BsDaterangepickerInlineContainerComponent_div_0_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10);\n    i0.ɵɵtemplate(1, BsDaterangepickerInlineContainerComponent_div_0_div_6_bs_years_calendar_view_1_Template, 1, 3, \"bs-years-calendar-view\", 17);\n    i0.ɵɵpipe(2, \"async\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind1(2, 1, ctx_r1.yearsCalendar));\n  }\n}\nfunction BsDaterangepickerInlineContainerComponent_div_0_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 19)(1, \"button\", 20);\n    i0.ɵɵtext(2, \"Apply\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 21);\n    i0.ɵɵtext(4, \"Cancel\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction BsDaterangepickerInlineContainerComponent_div_0_div_8_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 24)(1, \"button\", 25);\n    i0.ɵɵlistener(\"click\", function BsDaterangepickerInlineContainerComponent_div_0_div_8_div_1_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.setToday());\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵclassProp(\"today-left\", ctx_r1.todayPos === \"left\")(\"today-right\", ctx_r1.todayPos === \"right\")(\"today-center\", ctx_r1.todayPos === \"center\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.todayBtnLbl);\n  }\n}\nfunction BsDaterangepickerInlineContainerComponent_div_0_div_8_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 26)(1, \"button\", 25);\n    i0.ɵɵlistener(\"click\", function BsDaterangepickerInlineContainerComponent_div_0_div_8_div_2_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.clearDate());\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵclassProp(\"clear-left\", ctx_r1.clearPos === \"left\")(\"clear-right\", ctx_r1.clearPos === \"right\")(\"clear-center\", ctx_r1.clearPos === \"center\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.clearBtnLbl);\n  }\n}\nfunction BsDaterangepickerInlineContainerComponent_div_0_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 19);\n    i0.ɵɵtemplate(1, BsDaterangepickerInlineContainerComponent_div_0_div_8_div_1_Template, 3, 7, \"div\", 22)(2, BsDaterangepickerInlineContainerComponent_div_0_div_8_div_2_Template, 3, 7, \"div\", 23);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.showTodayBtn);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.showClearBtn);\n  }\n}\nfunction BsDaterangepickerInlineContainerComponent_div_0_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 27)(1, \"bs-custom-date-view\", 28);\n    i0.ɵɵlistener(\"onSelect\", function BsDaterangepickerInlineContainerComponent_div_0_div_9_Template_bs_custom_date_view_onSelect_1_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.setRangeOnCalendar($event));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"selectedRange\", ctx_r1.chosenRange)(\"ranges\", ctx_r1.customRanges)(\"customRangeLabel\", ctx_r1.customRangeBtnLbl);\n  }\n}\nfunction BsDaterangepickerInlineContainerComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 3)(1, \"div\", 4);\n    i0.ɵɵlistener(\"@datepickerAnimation.done\", function BsDaterangepickerInlineContainerComponent_div_0_Template_div_animation_datepickerAnimation_done_1_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.positionServiceEnable());\n    });\n    i0.ɵɵelementStart(2, \"div\", 5);\n    i0.ɵɵpipe(3, \"async\");\n    i0.ɵɵtemplate(4, BsDaterangepickerInlineContainerComponent_div_0_ng_container_4_Template, 5, 4, \"ng-container\", 6)(5, BsDaterangepickerInlineContainerComponent_div_0_div_5_Template, 3, 3, \"div\", 7)(6, BsDaterangepickerInlineContainerComponent_div_0_div_6_Template, 3, 3, \"div\", 7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(7, BsDaterangepickerInlineContainerComponent_div_0_div_7_Template, 5, 0, \"div\", 8)(8, BsDaterangepickerInlineContainerComponent_div_0_div_8_Template, 3, 2, \"div\", 8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(9, BsDaterangepickerInlineContainerComponent_div_0_div_9_Template, 2, 3, \"div\", 9);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.containerClass);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"@datepickerAnimation\", ctx_r1.animationState);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitch\", i0.ɵɵpipeBind1(3, 9, ctx_r1.viewMode));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngSwitchCase\", \"day\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"month\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"year\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", false);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.showTodayBtn || ctx_r1.showClearBtn);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.customRanges && ctx_r1.customRanges.length > 0);\n  }\n}\nlet BsDatepickerConfig = /*#__PURE__*/(() => {\n  class BsDatepickerConfig {\n    constructor() {\n      /** sets use adaptive position */\n      this.adaptivePosition = false;\n      /** sets use UTC date time format */\n      this.useUtc = false;\n      /** turn on/off animation */\n      this.isAnimated = false;\n      /**\n       * The view that the datepicker should start in\n       */\n      this.startView = 'day';\n      /**\n       * If true, returns focus to the datepicker / daterangepicker input after date selection\n       */\n      this.returnFocusToInput = false;\n      /** CSS class which will be applied to datepicker container,\n       * usually used to set color theme\n       */\n      this.containerClass = 'theme-green';\n      // DatepickerRenderOptions\n      this.displayMonths = 1;\n      /**\n       * Allows to hide week numbers in datepicker\n       */\n      this.showWeekNumbers = true;\n      this.dateInputFormat = 'L';\n      // range picker\n      this.rangeSeparator = ' - ';\n      /**\n       * Date format for date range input field\n       */\n      this.rangeInputFormat = 'L';\n      // DatepickerFormatOptions\n      this.monthTitle = 'MMMM';\n      this.yearTitle = 'YYYY';\n      this.dayLabel = 'D';\n      this.monthLabel = 'MMMM';\n      this.yearLabel = 'YYYY';\n      this.weekNumbers = 'w';\n      /**\n       * Shows 'today' button\n       */\n      this.showTodayButton = false;\n      /**\n       * Shows clear button\n       */\n      this.showClearButton = false;\n      /**\n       * Positioning of 'today' button\n       */\n      this.todayPosition = 'center';\n      /**\n       * Positioning of 'clear' button\n       */\n      this.clearPosition = 'right';\n      /**\n       * Label for 'today' button\n       */\n      this.todayButtonLabel = 'Today';\n      /**\n       * Label for 'clear' button\n       */\n      this.clearButtonLabel = 'Clear';\n      /**\n       * Label for 'custom range' button\n       */\n      this.customRangeButtonLabel = 'Custom Range';\n      /**\n       * Shows timepicker under datepicker\n       */\n      this.withTimepicker = false;\n      /**\n       * Set allowed positions of container.\n       */\n      this.allowedPositions = ['top', 'bottom'];\n      /**\n       * Set rule for datepicker closing. If value is true datepicker closes only if date is changed, if user changes only time datepicker doesn't close. It is available only if property withTimepicker is set true\n       * */\n      this.keepDatepickerOpened = false;\n      /**\n       * Allows keep invalid dates in range. Can be used with minDate, maxDate\n       * */\n      this.keepDatesOutOfRules = false;\n    }\n    static {\n      this.ɵfac = function BsDatepickerConfig_Factory(t) {\n        return new (t || BsDatepickerConfig)();\n      };\n    }\n    static {\n      this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n        token: BsDatepickerConfig,\n        factory: BsDatepickerConfig.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return BsDatepickerConfig;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nconst DATEPICKER_ANIMATION_TIMING = '220ms cubic-bezier(0, 0, 0.2, 1)';\nconst datepickerAnimation = trigger('datepickerAnimation', [state('animated-down', style({\n  height: '*',\n  overflow: 'hidden'\n})), transition('* => animated-down', [style({\n  height: 0,\n  overflow: 'hidden'\n}), animate(DATEPICKER_ANIMATION_TIMING)]), state('animated-up', style({\n  height: '*',\n  overflow: 'hidden'\n})), transition('* => animated-up', [style({\n  height: '*',\n  overflow: 'hidden'\n}), animate(DATEPICKER_ANIMATION_TIMING)]), transition('* => unanimated', animate('0s'))]);\nclass BsDatepickerAbstractComponent {\n  constructor() {\n    this.containerClass = '';\n    this.customRanges = [];\n    this.chosenRange = [];\n    this._daysCalendarSub = new Subscription();\n    this.selectedTimeSub = new Subscription();\n  }\n  set minDate(value) {\n    this._effects?.setMinDate(value);\n  }\n  set maxDate(value) {\n    this._effects?.setMaxDate(value);\n  }\n  set daysDisabled(value) {\n    this._effects?.setDaysDisabled(value);\n  }\n  set datesDisabled(value) {\n    this._effects?.setDatesDisabled(value);\n  }\n  set datesEnabled(value) {\n    this._effects?.setDatesEnabled(value);\n  }\n  set isDisabled(value) {\n    this._effects?.setDisabled(value);\n  }\n  set dateCustomClasses(value) {\n    this._effects?.setDateCustomClasses(value);\n  }\n  set dateTooltipTexts(value) {\n    this._effects?.setDateTooltipTexts(value);\n  }\n  set daysCalendar$(value) {\n    this._daysCalendar$ = value;\n    this._daysCalendarSub.unsubscribe();\n    this._daysCalendarSub.add(this._daysCalendar$.subscribe(value => {\n      this.multipleCalendars = !!value && value.length > 1;\n    }));\n  }\n  get daysCalendar$() {\n    return this._daysCalendar$;\n  }\n  // todo: valorkin fix\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars,@typescript-eslint/no-empty-function\n  setViewMode(event) {}\n  // eslint-disable-next-line\n  navigateTo(event) {}\n  // eslint-disable-next-line\n  dayHoverHandler(event) {}\n  // eslint-disable-next-line\n  weekHoverHandler(event) {}\n  // eslint-disable-next-line\n  monthHoverHandler(event) {}\n  // eslint-disable-next-line\n  yearHoverHandler(event) {}\n  // eslint-disable-next-line\n  timeSelectHandler(date, index) {}\n  // eslint-disable-next-line\n  daySelectHandler(day) {}\n  // eslint-disable-next-line\n  monthSelectHandler(event) {}\n  // eslint-disable-next-line\n  yearSelectHandler(event) {}\n  // eslint-disable-next-line\n  setRangeOnCalendar(dates) {}\n  // eslint-disable-next-line\n  setToday() {}\n  // eslint-disable-next-line\n  clearDate() {}\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  _stopPropagation(event) {\n    event.stopPropagation();\n  }\n}\nlet BsDatepickerActions = /*#__PURE__*/(() => {\n  class BsDatepickerActions {\n    static {\n      this.CALCULATE = '[datepicker] calculate dates matrix';\n    }\n    static {\n      this.FORMAT = '[datepicker] format datepicker values';\n    }\n    static {\n      this.FLAG = '[datepicker] set flags';\n    }\n    static {\n      this.SELECT = '[datepicker] select date';\n    }\n    static {\n      this.NAVIGATE_OFFSET = '[datepicker] shift view date';\n    }\n    static {\n      this.NAVIGATE_TO = '[datepicker] change view date';\n    }\n    static {\n      this.SET_OPTIONS = '[datepicker] update render options';\n    }\n    static {\n      this.HOVER = '[datepicker] hover date';\n    }\n    static {\n      this.CHANGE_VIEWMODE = '[datepicker] switch view mode';\n    }\n    static {\n      this.SET_MIN_DATE = '[datepicker] set min date';\n    }\n    static {\n      this.SET_MAX_DATE = '[datepicker] set max date';\n    }\n    static {\n      this.SET_DAYSDISABLED = '[datepicker] set days disabled';\n    }\n    static {\n      this.SET_DATESDISABLED = '[datepicker] set dates disabled';\n    }\n    static {\n      this.SET_DATESENABLED = '[datepicker] set dates enabled';\n    }\n    static {\n      this.SET_IS_DISABLED = '[datepicker] set is disabled';\n    }\n    static {\n      this.SET_DATE_CUSTOM_CLASSES = '[datepicker] set date custom classes';\n    }\n    static {\n      this.SET_DATE_TOOLTIP_TEXTS = '[datepicker] set date tooltip texts';\n    }\n    static {\n      this.SET_LOCALE = '[datepicker] set datepicker locale';\n    }\n    static {\n      this.SELECT_TIME = '[datepicker] select time';\n    }\n    static {\n      this.SELECT_RANGE = '[daterangepicker] select dates range';\n    }\n    calculate() {\n      return {\n        type: BsDatepickerActions.CALCULATE\n      };\n    }\n    format() {\n      return {\n        type: BsDatepickerActions.FORMAT\n      };\n    }\n    flag() {\n      return {\n        type: BsDatepickerActions.FLAG\n      };\n    }\n    select(date) {\n      return {\n        type: BsDatepickerActions.SELECT,\n        payload: date\n      };\n    }\n    selectTime(date, index) {\n      return {\n        type: BsDatepickerActions.SELECT_TIME,\n        payload: {\n          date,\n          index\n        }\n      };\n    }\n    changeViewMode(event) {\n      return {\n        type: BsDatepickerActions.CHANGE_VIEWMODE,\n        payload: event\n      };\n    }\n    navigateTo(event) {\n      return {\n        type: BsDatepickerActions.NAVIGATE_TO,\n        payload: event\n      };\n    }\n    navigateStep(step) {\n      return {\n        type: BsDatepickerActions.NAVIGATE_OFFSET,\n        payload: step\n      };\n    }\n    setOptions(options) {\n      return {\n        type: BsDatepickerActions.SET_OPTIONS,\n        payload: options\n      };\n    }\n    // date range picker\n    selectRange(value) {\n      return {\n        type: BsDatepickerActions.SELECT_RANGE,\n        payload: value\n      };\n    }\n    hoverDay(event) {\n      return {\n        type: BsDatepickerActions.HOVER,\n        payload: event.isHovered ? event.cell.date : null\n      };\n    }\n    minDate(date) {\n      return {\n        type: BsDatepickerActions.SET_MIN_DATE,\n        payload: date\n      };\n    }\n    maxDate(date) {\n      return {\n        type: BsDatepickerActions.SET_MAX_DATE,\n        payload: date\n      };\n    }\n    daysDisabled(days) {\n      return {\n        type: BsDatepickerActions.SET_DAYSDISABLED,\n        payload: days\n      };\n    }\n    datesDisabled(dates) {\n      return {\n        type: BsDatepickerActions.SET_DATESDISABLED,\n        payload: dates\n      };\n    }\n    datesEnabled(dates) {\n      return {\n        type: BsDatepickerActions.SET_DATESENABLED,\n        payload: dates\n      };\n    }\n    isDisabled(value) {\n      return {\n        type: BsDatepickerActions.SET_IS_DISABLED,\n        payload: value\n      };\n    }\n    setDateCustomClasses(value) {\n      return {\n        type: BsDatepickerActions.SET_DATE_CUSTOM_CLASSES,\n        payload: value\n      };\n    }\n    setDateTooltipTexts(value) {\n      return {\n        type: BsDatepickerActions.SET_DATE_TOOLTIP_TEXTS,\n        payload: value\n      };\n    }\n    setLocale(locale) {\n      return {\n        type: BsDatepickerActions.SET_LOCALE,\n        payload: locale\n      };\n    }\n    static {\n      this.ɵfac = function BsDatepickerActions_Factory(t) {\n        return new (t || BsDatepickerActions)();\n      };\n    }\n    static {\n      this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n        token: BsDatepickerActions,\n        factory: BsDatepickerActions.ɵfac,\n        providedIn: 'platform'\n      });\n    }\n  }\n  return BsDatepickerActions;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet BsLocaleService = /*#__PURE__*/(() => {\n  class BsLocaleService {\n    constructor() {\n      this._defaultLocale = 'en';\n      this._locale = new BehaviorSubject(this._defaultLocale);\n      this._localeChange = this._locale.asObservable();\n    }\n    get locale() {\n      return this._locale;\n    }\n    get localeChange() {\n      return this._localeChange;\n    }\n    get currentLocale() {\n      return this._locale.getValue();\n    }\n    use(locale) {\n      if (locale === this.currentLocale) {\n        return;\n      }\n      this._locale.next(locale);\n    }\n    static {\n      this.ɵfac = function BsLocaleService_Factory(t) {\n        return new (t || BsLocaleService)();\n      };\n    }\n    static {\n      this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n        token: BsLocaleService,\n        factory: BsLocaleService.ɵfac,\n        providedIn: 'platform'\n      });\n    }\n  }\n  return BsLocaleService;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet BsDatepickerEffects = /*#__PURE__*/(() => {\n  class BsDatepickerEffects {\n    constructor(_actions, _localeService) {\n      this._actions = _actions;\n      this._localeService = _localeService;\n      this._subs = [];\n    }\n    init(_bsDatepickerStore) {\n      this._store = _bsDatepickerStore;\n      return this;\n    }\n    /** setters */\n    setValue(value) {\n      this._store?.dispatch(this._actions.select(value));\n    }\n    setRangeValue(value) {\n      this._store?.dispatch(this._actions.selectRange(value));\n    }\n    setMinDate(value) {\n      this._store?.dispatch(this._actions.minDate(value));\n      return this;\n    }\n    setMaxDate(value) {\n      this._store?.dispatch(this._actions.maxDate(value));\n      return this;\n    }\n    setDaysDisabled(value) {\n      this._store?.dispatch(this._actions.daysDisabled(value));\n      return this;\n    }\n    setDatesDisabled(value) {\n      this._store?.dispatch(this._actions.datesDisabled(value));\n      return this;\n    }\n    setDatesEnabled(value) {\n      this._store?.dispatch(this._actions.datesEnabled(value));\n      return this;\n    }\n    setDisabled(value) {\n      this._store?.dispatch(this._actions.isDisabled(value));\n      return this;\n    }\n    setDateCustomClasses(value) {\n      this._store?.dispatch(this._actions.setDateCustomClasses(value));\n      return this;\n    }\n    setDateTooltipTexts(value) {\n      this._store?.dispatch(this._actions.setDateTooltipTexts(value));\n      return this;\n    }\n    /* Set rendering options */\n    setOptions(_config) {\n      const _options = Object.assign({\n        locale: this._localeService.currentLocale\n      }, _config);\n      this._store?.dispatch(this._actions.setOptions(_options));\n      return this;\n    }\n    /** view to mode bindings */\n    setBindings(container) {\n      if (!this._store) {\n        return this;\n      }\n      container.selectedTime = this._store.select(state => state.selectedTime).pipe(filter(times => !!times));\n      container.daysCalendar$ = this._store.select(state => state.flaggedMonths).pipe(filter(months => !!months));\n      // month calendar\n      container.monthsCalendar = this._store.select(state => state.flaggedMonthsCalendar).pipe(filter(months => !!months));\n      // year calendar\n      container.yearsCalendar = this._store.select(state => state.yearsCalendarFlagged).pipe(filter(years => !!years));\n      container.viewMode = this._store.select(state => state.view?.mode);\n      container.options$ = combineLatest([this._store.select(state => state.showWeekNumbers), this._store.select(state => state.displayMonths)]).pipe(map(latest => ({\n        showWeekNumbers: latest[0],\n        displayMonths: latest[1]\n      })));\n      return this;\n    }\n    /** event handlers */\n    setEventHandlers(container) {\n      container.setViewMode = event => {\n        this._store?.dispatch(this._actions.changeViewMode(event));\n      };\n      container.navigateTo = event => {\n        this._store?.dispatch(this._actions.navigateStep(event.step));\n      };\n      container.dayHoverHandler = event => {\n        const _cell = event.cell;\n        if (_cell.isOtherMonth || _cell.isDisabled) {\n          return;\n        }\n        this._store?.dispatch(this._actions.hoverDay(event));\n        _cell.isHovered = event.isHovered;\n      };\n      container.monthHoverHandler = event => {\n        event.cell.isHovered = event.isHovered;\n      };\n      container.yearHoverHandler = event => {\n        event.cell.isHovered = event.isHovered;\n      };\n      return this;\n    }\n    registerDatepickerSideEffects() {\n      if (!this._store) {\n        return this;\n      }\n      this._subs.push(this._store.select(state => state.view).subscribe(() => {\n        this._store?.dispatch(this._actions.calculate());\n      }));\n      // format calendar values on month model change\n      this._subs.push(this._store.select(state => state.monthsModel).pipe(filter(monthModel => !!monthModel)).subscribe(() => this._store?.dispatch(this._actions.format())));\n      // flag day values\n      this._subs.push(this._store.select(state => state.formattedMonths).pipe(filter(month => !!month)).subscribe(() => this._store?.dispatch(this._actions.flag())));\n      // flag day values\n      this._subs.push(this._store.select(state => state.selectedDate).pipe(filter(selectedDate => !!selectedDate)).subscribe(() => this._store?.dispatch(this._actions.flag())));\n      // flag for date range picker\n      this._subs.push(this._store.select(state => state.selectedRange).pipe(filter(selectedRange => !!selectedRange)).subscribe(() => this._store?.dispatch(this._actions.flag())));\n      // monthsCalendar\n      this._subs.push(this._store.select(state => state.monthsCalendar).subscribe(() => this._store?.dispatch(this._actions.flag())));\n      // years calendar\n      this._subs.push(this._store.select(state => state.yearsCalendarModel).pipe(filter(state => !!state)).subscribe(() => this._store?.dispatch(this._actions.flag())));\n      // on hover\n      this._subs.push(this._store.select(state => state.hoveredDate).pipe(filter(hoveredDate => !!hoveredDate)).subscribe(() => this._store?.dispatch(this._actions.flag())));\n      // date custom classes\n      this._subs.push(this._store.select(state => state.dateCustomClasses).pipe(filter(dateCustomClasses => !!dateCustomClasses)).subscribe(() => this._store?.dispatch(this._actions.flag())));\n      // date tooltip texts\n      this._subs.push(this._store.select(state => state.dateTooltipTexts).pipe(filter(dateTooltipTexts => !!dateTooltipTexts)).subscribe(() => this._store?.dispatch(this._actions.flag())));\n      // on locale change\n      this._subs.push(this._localeService.localeChange.subscribe(locale => this._store?.dispatch(this._actions.setLocale(locale))));\n      return this;\n    }\n    destroy() {\n      for (const sub of this._subs) {\n        sub.unsubscribe();\n      }\n    }\n    static {\n      this.ɵfac = function BsDatepickerEffects_Factory(t) {\n        return new (t || BsDatepickerEffects)(i0.ɵɵinject(BsDatepickerActions), i0.ɵɵinject(BsLocaleService));\n      };\n    }\n    static {\n      this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n        token: BsDatepickerEffects,\n        factory: BsDatepickerEffects.ɵfac,\n        providedIn: 'platform'\n      });\n    }\n  }\n  return BsDatepickerEffects;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nconst defaultMonthOptions = {\n  width: 7,\n  height: 6\n};\nconst dayInMilliseconds = 24 * 60 * 60 * 1000;\nclass BsDatepickerState {\n  constructor() {\n    // DatepickerRenderOptions\n    this.showWeekNumbers = true;\n    this.displayMonths = 1;\n  }\n}\nconst _initialView = {\n  date: new Date(),\n  mode: 'day'\n};\nconst initialDatepickerState = Object.assign(new BsDatepickerConfig(), {\n  locale: 'en',\n  view: _initialView,\n  selectedRange: [],\n  selectedTime: [],\n  monthViewOptions: defaultMonthOptions\n});\nfunction getStartingDayOfCalendar(date, options) {\n  if (isFirstDayOfWeek(date, options.firstDayOfWeek)) {\n    return date;\n  }\n  const weekDay = getDay(date);\n  const offset = calculateDateOffset(weekDay, options.firstDayOfWeek);\n  return shiftDate(date, {\n    day: -offset\n  });\n}\nfunction calculateDateOffset(weekday, startingDayOffset) {\n  const _startingDayOffset = Number(startingDayOffset);\n  if (isNaN(_startingDayOffset)) {\n    return 0;\n  }\n  if (_startingDayOffset === 0) {\n    return weekday;\n  }\n  const offset = weekday - _startingDayOffset % 7;\n  return offset < 0 ? offset + 7 : offset;\n}\nfunction isMonthDisabled(date, min, max) {\n  const minBound = min && isBefore(endOf(date, 'month'), min, 'day');\n  const maxBound = max && isAfter(startOf(date, 'month'), max, 'day');\n  return minBound || maxBound || false;\n}\nfunction isYearDisabled(date, min, max) {\n  const minBound = min && isBefore(endOf(date, 'year'), min, 'day');\n  const maxBound = max && isAfter(startOf(date, 'year'), max, 'day');\n  return minBound || maxBound || false;\n}\nfunction isDisabledDate(date, datesDisabled, unit) {\n  if (!datesDisabled || !isArray(datesDisabled) || !datesDisabled.length) {\n    return false;\n  }\n  if (unit && unit === 'year' && !datesDisabled[0].getDate()) {\n    return datesDisabled.some(dateDisabled => isSame(date, dateDisabled, 'year'));\n  }\n  return datesDisabled.some(dateDisabled => isSame(date, dateDisabled, 'date'));\n}\nfunction isEnabledDate(date, datesEnabled, unit) {\n  if (!datesEnabled || !isArray(datesEnabled) || !datesEnabled.length) {\n    return false;\n  }\n  return !datesEnabled.some(enabledDate => isSame(date, enabledDate, unit || 'date'));\n}\nfunction getYearsCalendarInitialDate(state, calendarIndex = 0) {\n  const model = state && state.yearsCalendarModel && state.yearsCalendarModel[calendarIndex];\n  return model?.years[0] && model.years[0][0] && model.years[0][0].date;\n}\nfunction checkRangesWithMaxDate(ranges, maxDate) {\n  if (!ranges) return ranges;\n  if (!maxDate) return ranges;\n  if (!ranges.length && !ranges[0].value) return ranges;\n  ranges.forEach(item => {\n    if (!item || !item.value) return ranges;\n    if (item.value instanceof Date) return ranges;\n    if (!(item.value instanceof Array && item.value.length)) return ranges;\n    item.value = compareDateWithMaxDateHelper(item.value, maxDate);\n    return ranges;\n  });\n  return ranges;\n}\nfunction checkBsValue(date, maxDate) {\n  if (!date) return date;\n  if (!maxDate) return date;\n  if (date instanceof Array && !date.length) return date;\n  if (date instanceof Date) return date;\n  return compareDateWithMaxDateHelper(date, maxDate);\n}\nfunction compareDateWithMaxDateHelper(date, maxDate) {\n  if (date instanceof Array) {\n    const editedValues = date.map(item => {\n      if (!item) return item;\n      if (isAfter(item, maxDate, 'date')) item = maxDate;\n      return item;\n    });\n    return editedValues;\n  }\n  return date;\n}\nfunction setCurrentTimeOnDateSelect(value) {\n  if (!value) return value;\n  return setCurrentTimeHelper(value);\n}\nfunction setDateRangesCurrentTimeOnDateSelect(value) {\n  if (!value?.length) return value;\n  value.map(date => {\n    if (!date) {\n      return date;\n    }\n    return setCurrentTimeHelper(date);\n  });\n  return value;\n}\nfunction setCurrentTimeHelper(date) {\n  const now = new Date();\n  date.setMilliseconds(now.getMilliseconds());\n  date.setSeconds(now.getSeconds());\n  date.setMinutes(now.getMinutes());\n  date.setHours(now.getHours());\n  return date;\n}\nfunction createMatrix(options, fn) {\n  let prevValue = options.initialDate;\n  const matrix = new Array(options.height);\n  for (let i = 0; i < options.height; i++) {\n    matrix[i] = new Array(options.width);\n    for (let j = 0; j < options.width; j++) {\n      matrix[i][j] = fn(prevValue);\n      prevValue = shiftDate(prevValue, options.shift);\n    }\n  }\n  return matrix;\n}\n\n// user and model input should handle parsing and validating input values\nfunction calcDaysCalendar(startingDate, options) {\n  const firstDay = getFirstDayOfMonth(startingDate);\n  const initialDate = getStartingDayOfCalendar(firstDay, options);\n  // todo test\n  const matrixOptions = {\n    width: options.width || 0,\n    height: options.height || 0,\n    initialDate,\n    shift: {\n      day: 1\n    }\n  };\n  const daysMatrix = createMatrix(matrixOptions, date => date);\n  return {\n    daysMatrix,\n    month: firstDay\n  };\n}\nfunction formatDaysCalendar(daysCalendar, formatOptions, monthIndex) {\n  return {\n    month: daysCalendar.month,\n    monthTitle: formatDate(daysCalendar.month, formatOptions.monthTitle, formatOptions.locale),\n    yearTitle: formatDate(daysCalendar.month, formatOptions.yearTitle, formatOptions.locale),\n    weekNumbers: getWeekNumbers(daysCalendar.daysMatrix, formatOptions.weekNumbers, formatOptions.locale),\n    weekdays: getShiftedWeekdays(formatOptions.locale),\n    weeks: daysCalendar.daysMatrix.map((week, weekIndex) => ({\n      days: week.map((date, dayIndex) => ({\n        date,\n        label: formatDate(date, formatOptions.dayLabel, formatOptions.locale),\n        monthIndex,\n        weekIndex,\n        dayIndex\n      }))\n    })),\n    hideLeftArrow: false,\n    hideRightArrow: false,\n    disableLeftArrow: false,\n    disableRightArrow: false\n  };\n}\nfunction getWeekNumbers(daysMatrix, format, locale) {\n  return daysMatrix.map(days => days[0] ? formatDate(days[0], format, locale) : '');\n}\nfunction getShiftedWeekdays(locale) {\n  const _locale = getLocale(locale);\n  const weekdays = _locale.weekdaysShort();\n  const firstDayOfWeek = _locale.firstDayOfWeek();\n  return [...weekdays.slice(firstDayOfWeek), ...weekdays.slice(0, firstDayOfWeek)];\n}\nfunction flagDaysCalendar(formattedMonth, options) {\n  formattedMonth.weeks.forEach(week => {\n    week.days.forEach((day, dayIndex) => {\n      // datepicker\n      const isOtherMonth = !isSameMonth(day.date, formattedMonth.month);\n      const isHovered = !isOtherMonth && isSameDay(day.date, options.hoveredDate);\n      // date range picker\n      const isSelectionStart = !isOtherMonth && options.selectedRange && isSameDay(day.date, options.selectedRange[0]);\n      const isSelectionEnd = !isOtherMonth && options.selectedRange && isSameDay(day.date, options.selectedRange[1]);\n      const isSelected = !isOtherMonth && isSameDay(day.date, options.selectedDate) || isSelectionStart || isSelectionEnd;\n      const isInRange = !isOtherMonth && options.selectedRange && isDateInRange(day.date, options.selectedRange, options.hoveredDate);\n      const isDisabled = options.isDisabled || isBefore(day.date, options.minDate, 'day') || isAfter(day.date, options.maxDate, 'day') || isDisabledDay(day.date, options.daysDisabled) || isDisabledDate(day.date, options.datesDisabled) || isEnabledDate(day.date, options.datesEnabled);\n      const currentDate = new Date();\n      const isToday = !isOtherMonth && isSameDay(day.date, currentDate);\n      const customClasses = options.dateCustomClasses && options.dateCustomClasses.map(dcc => isSameDay(day.date, dcc.date) ? dcc.classes : []).reduce((previousValue, currentValue) => previousValue.concat(currentValue), []).join(' ') || '';\n      const tooltipText = options.dateTooltipTexts && options.dateTooltipTexts.map(tt => isSameDay(day.date, tt.date) ? tt.tooltipText : '').reduce((previousValue, currentValue) => {\n        previousValue.push(currentValue);\n        return previousValue;\n      }, []).join(' ') || '';\n      // decide update or not\n      const newDay = Object.assign({}, day, {\n        isOtherMonth,\n        isHovered,\n        isSelected,\n        isSelectionStart,\n        isSelectionEnd,\n        isInRange,\n        isDisabled,\n        isToday,\n        customClasses,\n        tooltipText\n      });\n      if (day.isOtherMonth !== newDay.isOtherMonth || day.isHovered !== newDay.isHovered || day.isSelected !== newDay.isSelected || day.isSelectionStart !== newDay.isSelectionStart || day.isSelectionEnd !== newDay.isSelectionEnd || day.isDisabled !== newDay.isDisabled || day.isInRange !== newDay.isInRange || day.customClasses !== newDay.customClasses || day.tooltipText !== newDay.tooltipText) {\n        week.days[dayIndex] = newDay;\n      }\n    });\n  });\n  // todo: add check for linked calendars\n  formattedMonth.hideLeftArrow = options.isDisabled || !!options.monthIndex && options.monthIndex > 0 && options.monthIndex !== options.displayMonths;\n  formattedMonth.hideRightArrow = options.isDisabled || (!!options.monthIndex || options.monthIndex === 0) && !!options.displayMonths && options.monthIndex < options.displayMonths && options.monthIndex + 1 !== options.displayMonths;\n  formattedMonth.disableLeftArrow = isMonthDisabled(shiftDate(formattedMonth.month, {\n    month: -1\n  }), options.minDate, options.maxDate);\n  formattedMonth.disableRightArrow = isMonthDisabled(shiftDate(formattedMonth.month, {\n    month: 1\n  }), options.minDate, options.maxDate);\n  return formattedMonth;\n}\nfunction isDateInRange(date, selectedRange, hoveredDate) {\n  if (!date || !selectedRange || !selectedRange[0]) {\n    return false;\n  }\n  if (selectedRange[1]) {\n    return date > selectedRange[0] && date <= selectedRange[1];\n  }\n  if (hoveredDate) {\n    return date > selectedRange[0] && date <= hoveredDate;\n  }\n  return false;\n}\nfunction canSwitchMode(mode, minMode) {\n  return minMode ? mode >= minMode : true;\n}\nconst height$1 = 4;\nconst width$1 = 3;\nconst shift$1 = {\n  month: 1\n};\nfunction formatMonthsCalendar(viewDate, formatOptions) {\n  const initialDate = startOf(viewDate, 'year');\n  const matrixOptions = {\n    width: width$1,\n    height: height$1,\n    initialDate,\n    shift: shift$1\n  };\n  const monthMatrix = createMatrix(matrixOptions, date => ({\n    date,\n    label: formatDate(date, formatOptions.monthLabel, formatOptions.locale)\n  }));\n  return {\n    months: monthMatrix,\n    monthTitle: '',\n    yearTitle: formatDate(viewDate, formatOptions.yearTitle, formatOptions.locale),\n    hideRightArrow: false,\n    hideLeftArrow: false,\n    disableRightArrow: false,\n    disableLeftArrow: false\n  };\n}\nfunction flagMonthsCalendar(monthCalendar, options) {\n  monthCalendar.months.forEach((months, rowIndex) => {\n    months.forEach((month, monthIndex) => {\n      let isSelected;\n      const isHovered = isSameMonth(month.date, options.hoveredMonth);\n      const isDisabled = options.isDisabled || isDisabledDate(month.date, options.datesDisabled) || isEnabledDate(month.date, options.datesEnabled, 'month') || isMonthDisabled(month.date, options.minDate, options.maxDate);\n      if (!options.selectedDate && options.selectedRange) {\n        isSelected = isSameMonth(month.date, options.selectedRange[0]);\n        if (!isSelected) {\n          isSelected = isSameMonth(month.date, options.selectedRange[1]);\n        }\n      } else {\n        isSelected = isSameMonth(month.date, options.selectedDate);\n      }\n      const newMonth = Object.assign(/*{},*/month, {\n        isHovered,\n        isDisabled,\n        isSelected\n      });\n      if (month.isHovered !== newMonth.isHovered || month.isDisabled !== newMonth.isDisabled || month.isSelected !== newMonth.isSelected) {\n        monthCalendar.months[rowIndex][monthIndex] = newMonth;\n      }\n    });\n  });\n  // todo: add check for linked calendars\n  monthCalendar.hideLeftArrow = !!options.monthIndex && options.monthIndex > 0 && options.monthIndex !== options.displayMonths;\n  monthCalendar.hideRightArrow = (!!options.monthIndex || options.monthIndex === 0) && (!!options.displayMonths || options.displayMonths === 0) && options.monthIndex < options.displayMonths && options.monthIndex + 1 !== options.displayMonths;\n  monthCalendar.disableLeftArrow = isYearDisabled(shiftDate(monthCalendar.months[0][0].date, {\n    year: -1\n  }), options.minDate, options.maxDate);\n  monthCalendar.disableRightArrow = isYearDisabled(shiftDate(monthCalendar.months[0][0].date, {\n    year: 1\n  }), options.minDate, options.maxDate);\n  return monthCalendar;\n}\nconst height = 4;\nconst width = 4;\nconst yearsPerCalendar = height * width;\nconst initialYearShift = (Math.floor(yearsPerCalendar / 2) - 1) * -1;\nconst shift = {\n  year: 1\n};\nfunction formatYearsCalendar(viewDate, formatOptions, previousInitialDate) {\n  const initialDate = calculateInitialDate(viewDate, previousInitialDate);\n  const matrixOptions = {\n    width,\n    height,\n    initialDate,\n    shift\n  };\n  const yearsMatrix = createMatrix(matrixOptions, date => ({\n    date,\n    label: formatDate(date, formatOptions.yearLabel, formatOptions.locale)\n  }));\n  const yearTitle = formatYearRangeTitle(yearsMatrix, formatOptions);\n  return {\n    years: yearsMatrix,\n    monthTitle: '',\n    yearTitle,\n    hideLeftArrow: false,\n    hideRightArrow: false,\n    disableLeftArrow: false,\n    disableRightArrow: false\n  };\n}\nfunction calculateInitialDate(viewDate, previousInitialDate) {\n  if (previousInitialDate && viewDate.getFullYear() >= previousInitialDate.getFullYear() && viewDate.getFullYear() < previousInitialDate.getFullYear() + yearsPerCalendar) {\n    return previousInitialDate;\n  }\n  return shiftDate(viewDate, {\n    year: initialYearShift\n  });\n}\nfunction formatYearRangeTitle(yearsMatrix, formatOptions) {\n  const from = formatDate(yearsMatrix[0][0].date, formatOptions.yearTitle, formatOptions.locale);\n  const to = formatDate(yearsMatrix[height - 1][width - 1].date, formatOptions.yearTitle, formatOptions.locale);\n  return `${from} - ${to}`;\n}\nfunction flagYearsCalendar(yearsCalendar, options) {\n  yearsCalendar.years.forEach((years, rowIndex) => {\n    years.forEach((year, yearIndex) => {\n      let isSelected;\n      const isHovered = isSameYear(year.date, options.hoveredYear);\n      const isDisabled = options.isDisabled || isDisabledDate(year.date, options.datesDisabled, 'year') || isEnabledDate(year.date, options.datesEnabled, 'year') || isYearDisabled(year.date, options.minDate, options.maxDate);\n      if (!options.selectedDate && options.selectedRange) {\n        isSelected = isSameYear(year.date, options.selectedRange[0]);\n        if (!isSelected) {\n          isSelected = isSameYear(year.date, options.selectedRange[1]);\n        }\n      } else {\n        isSelected = isSameYear(year.date, options.selectedDate);\n      }\n      const newMonth = Object.assign(/*{},*/year, {\n        isHovered,\n        isDisabled,\n        isSelected\n      });\n      if (year.isHovered !== newMonth.isHovered || year.isDisabled !== newMonth.isDisabled || year.isSelected !== newMonth.isSelected) {\n        yearsCalendar.years[rowIndex][yearIndex] = newMonth;\n      }\n    });\n  });\n  // todo: add check for linked calendars\n  yearsCalendar.hideLeftArrow = !!options.yearIndex && options.yearIndex > 0 && options.yearIndex !== options.displayMonths;\n  yearsCalendar.hideRightArrow = !!options.yearIndex && !!options.displayMonths && options.yearIndex < options.displayMonths && options.yearIndex + 1 !== options.displayMonths;\n  yearsCalendar.disableLeftArrow = isYearDisabled(shiftDate(yearsCalendar.years[0][0].date, {\n    year: -1\n  }), options.minDate, options.maxDate);\n  const i = yearsCalendar.years.length - 1;\n  const j = yearsCalendar.years[i].length - 1;\n  yearsCalendar.disableRightArrow = isYearDisabled(shiftDate(yearsCalendar.years[i][j].date, {\n    year: 1\n  }), options.minDate, options.maxDate);\n  return yearsCalendar;\n}\nfunction copyTime(sourceDate, time) {\n  if (!sourceDate || !isNaN(sourceDate.getTime())) {\n    return;\n  }\n  sourceDate.setHours(time.getHours());\n  sourceDate.setMinutes(time.getMinutes());\n  sourceDate.setSeconds(time.getSeconds());\n  sourceDate.setMilliseconds(time.getMilliseconds());\n}\nfunction bsDatepickerReducer(state = initialDatepickerState, action) {\n  switch (action.type) {\n    case BsDatepickerActions.CALCULATE:\n      {\n        return calculateReducer(state);\n      }\n    case BsDatepickerActions.FORMAT:\n      {\n        return formatReducer(state);\n      }\n    case BsDatepickerActions.FLAG:\n      {\n        return flagReducer(state);\n      }\n    case BsDatepickerActions.NAVIGATE_OFFSET:\n      {\n        return navigateOffsetReducer(state, action);\n      }\n    case BsDatepickerActions.NAVIGATE_TO:\n      {\n        const payload = action.payload;\n        if (!state.view || !payload.unit) {\n          return state;\n        }\n        const date = setFullDate(state.view.date, payload.unit);\n        let newState;\n        let mode;\n        if (canSwitchMode(payload.viewMode, state.minMode)) {\n          mode = payload.viewMode;\n          newState = {\n            view: {\n              date,\n              mode\n            }\n          };\n        } else {\n          mode = state.view.mode;\n          newState = {\n            selectedDate: date,\n            view: {\n              date,\n              mode\n            }\n          };\n        }\n        return Object.assign({}, state, newState);\n      }\n    case BsDatepickerActions.CHANGE_VIEWMODE:\n      {\n        if (!canSwitchMode(action.payload, state.minMode) || !state.view) {\n          return state;\n        }\n        const date = state.view.date;\n        const mode = action.payload;\n        const newState = {\n          view: {\n            date,\n            mode\n          }\n        };\n        return Object.assign({}, state, newState);\n      }\n    case BsDatepickerActions.HOVER:\n      {\n        return Object.assign({}, state, {\n          hoveredDate: action.payload\n        });\n      }\n    case BsDatepickerActions.SELECT:\n      {\n        if (!state.view) {\n          return state;\n        }\n        const newState = {\n          selectedDate: action.payload,\n          view: state.view\n        };\n        if (Array.isArray(state.selectedTime)) {\n          const _time = state.selectedTime[0];\n          if (newState.selectedDate && _time) {\n            copyTime(newState.selectedDate, _time);\n          }\n        }\n        const mode = state.view.mode;\n        const _date = action.payload || state.view.date;\n        const date = getViewDate(_date, state.minDate, state.maxDate);\n        newState.view = {\n          mode,\n          date\n        };\n        return Object.assign({}, state, newState);\n      }\n    case BsDatepickerActions.SELECT_TIME:\n      {\n        const {\n          date,\n          index\n        } = action.payload;\n        const selectedTime = state.selectedTime ? [...state.selectedTime] : [];\n        selectedTime[index] = date;\n        return Object.assign({}, state, {\n          selectedTime\n        });\n      }\n    case BsDatepickerActions.SET_OPTIONS:\n      {\n        if (!state.view) {\n          return state;\n        }\n        const newState = action.payload;\n        // preserve view mode\n        const mode = newState.minMode ? newState.minMode : state.view.mode;\n        const _viewDate = isDateValid(newState.value) && newState.value || isArray(newState.value) && isDateValid(newState.value[0]) && newState.value[0] || state.view.date;\n        const date = getViewDate(_viewDate, newState.minDate, newState.maxDate);\n        newState.view = {\n          mode,\n          date\n        };\n        // update selected value\n        if (newState.value) {\n          // if new value is array we work with date range\n          if (isArray(newState.value)) {\n            newState.selectedRange = newState.value;\n            newState.selectedTime = newState.value.map(i => i);\n          }\n          // if new value is a date -> datepicker\n          if (newState.value instanceof Date) {\n            newState.selectedDate = newState.value;\n            newState.selectedTime = [newState.value];\n          }\n          // provided value is not supported :)\n          // need to report it somehow\n        }\n        return Object.assign({}, state, newState);\n      }\n    // date range picker\n    case BsDatepickerActions.SELECT_RANGE:\n      {\n        if (!state.view) {\n          return state;\n        }\n        const newState = {\n          selectedRange: action.payload,\n          view: state.view\n        };\n        newState.selectedRange?.forEach((dte, index) => {\n          if (Array.isArray(state.selectedTime)) {\n            const _time = state.selectedTime[index];\n            if (_time) {\n              copyTime(dte, _time);\n            }\n          }\n        });\n        const mode = state.view.mode;\n        const _date = action.payload && action.payload[0] || state.view.date;\n        const date = getViewDate(_date, state.minDate, state.maxDate);\n        newState.view = {\n          mode,\n          date\n        };\n        return Object.assign({}, state, newState);\n      }\n    case BsDatepickerActions.SET_MIN_DATE:\n      {\n        return Object.assign({}, state, {\n          minDate: action.payload\n        });\n      }\n    case BsDatepickerActions.SET_MAX_DATE:\n      {\n        return Object.assign({}, state, {\n          maxDate: action.payload\n        });\n      }\n    case BsDatepickerActions.SET_IS_DISABLED:\n      {\n        return Object.assign({}, state, {\n          isDisabled: action.payload\n        });\n      }\n    case BsDatepickerActions.SET_DATE_CUSTOM_CLASSES:\n      {\n        return Object.assign({}, state, {\n          dateCustomClasses: action.payload\n        });\n      }\n    case BsDatepickerActions.SET_DATE_TOOLTIP_TEXTS:\n      {\n        return Object.assign({}, state, {\n          dateTooltipTexts: action.payload\n        });\n      }\n    default:\n      return state;\n  }\n}\nfunction calculateReducer(state) {\n  if (!state.view) {\n    return state;\n  }\n  // how many calendars\n  let displayMonths;\n  if (state.displayOneMonthRange && isDisplayOneMonth(state.view.date, state.minDate, state.maxDate)) {\n    displayMonths = 1;\n  } else {\n    displayMonths = state.displayMonths || 1;\n  }\n  // use selected date on initial rendering if set\n  let viewDate = state.view.date;\n  if (state.view.mode === 'day' && state.monthViewOptions) {\n    if (state.showPreviousMonth && state.selectedRange && state.selectedRange.length === 0) {\n      viewDate = shiftDate(viewDate, {\n        month: -1\n      });\n    }\n    state.monthViewOptions.firstDayOfWeek = getLocale(state.locale).firstDayOfWeek();\n    let monthsModel = new Array(displayMonths);\n    for (let monthIndex = 0; monthIndex < displayMonths; monthIndex++) {\n      // todo: for unlinked calendars it will be harder\n      monthsModel[monthIndex] = calcDaysCalendar(viewDate, state.monthViewOptions);\n      viewDate = shiftDate(viewDate, {\n        month: 1\n      });\n    }\n    // Check if parameter enabled and check if it's not months navigation event\n    if (state.preventChangeToNextMonth && state.flaggedMonths && state.hoveredDate) {\n      const viewMonth = calcDaysCalendar(state.view.date, state.monthViewOptions);\n      // Check if viewed right month same as in flaggedMonths state, then override months model with flaggedMonths\n      if (state.flaggedMonths.length && state.flaggedMonths[1].month.getMonth() === viewMonth.month.getMonth()) {\n        monthsModel = state.flaggedMonths.map(item => {\n          if (state.monthViewOptions) {\n            return calcDaysCalendar(item.month, state.monthViewOptions);\n          }\n          return null;\n        }).filter(item => item !== null);\n      }\n    }\n    return Object.assign({}, state, {\n      monthsModel\n    });\n  }\n  if (state.view.mode === 'month') {\n    const monthsCalendar = new Array(displayMonths);\n    for (let calendarIndex = 0; calendarIndex < displayMonths; calendarIndex++) {\n      // todo: for unlinked calendars it will be harder\n      monthsCalendar[calendarIndex] = formatMonthsCalendar(viewDate, getFormatOptions(state));\n      viewDate = shiftDate(viewDate, {\n        year: 1\n      });\n    }\n    return Object.assign({}, state, {\n      monthsCalendar\n    });\n  }\n  if (state.view.mode === 'year') {\n    const yearsCalendarModel = new Array(displayMonths);\n    for (let calendarIndex = 0; calendarIndex < displayMonths; calendarIndex++) {\n      // todo: for unlinked calendars it will be harder\n      yearsCalendarModel[calendarIndex] = formatYearsCalendar(viewDate, getFormatOptions(state), state.minMode === 'year' ? getYearsCalendarInitialDate(state, calendarIndex) : undefined);\n      viewDate = shiftDate(viewDate, {\n        year: yearsPerCalendar\n      });\n    }\n    return Object.assign({}, state, {\n      yearsCalendarModel\n    });\n  }\n  return state;\n}\nfunction formatReducer(state) {\n  if (!state.view) {\n    return state;\n  }\n  if (state.view.mode === 'day' && state.monthsModel) {\n    const formattedMonths = state.monthsModel.map((month, monthIndex) => formatDaysCalendar(month, getFormatOptions(state), monthIndex));\n    return Object.assign({}, state, {\n      formattedMonths\n    });\n  }\n  // how many calendars\n  const displayMonths = state.displayMonths || 1;\n  // check initial rendering\n  // use selected date on initial rendering if set\n  let viewDate = state.view.date;\n  if (state.view.mode === 'month') {\n    const monthsCalendar = new Array(displayMonths);\n    for (let calendarIndex = 0; calendarIndex < displayMonths; calendarIndex++) {\n      // todo: for unlinked calendars it will be harder\n      monthsCalendar[calendarIndex] = formatMonthsCalendar(viewDate, getFormatOptions(state));\n      viewDate = shiftDate(viewDate, {\n        year: 1\n      });\n    }\n    return Object.assign({}, state, {\n      monthsCalendar\n    });\n  }\n  if (state.view.mode === 'year') {\n    const yearsCalendarModel = new Array(displayMonths);\n    for (let calendarIndex = 0; calendarIndex < displayMonths; calendarIndex++) {\n      // todo: for unlinked calendars it will be harder\n      yearsCalendarModel[calendarIndex] = formatYearsCalendar(viewDate, getFormatOptions(state));\n      viewDate = shiftDate(viewDate, {\n        year: 16\n      });\n    }\n    return Object.assign({}, state, {\n      yearsCalendarModel\n    });\n  }\n  return state;\n}\nfunction flagReducer(state) {\n  if (!state.view) {\n    return state;\n  }\n  const displayMonths = isDisplayOneMonth(state.view.date, state.minDate, state.maxDate) ? 1 : state.displayMonths;\n  if (state.formattedMonths && state.view.mode === 'day') {\n    const flaggedMonths = state.formattedMonths.map((formattedMonth, monthIndex) => flagDaysCalendar(formattedMonth, {\n      isDisabled: state.isDisabled,\n      minDate: state.minDate,\n      maxDate: state.maxDate,\n      daysDisabled: state.daysDisabled,\n      datesDisabled: state.datesDisabled,\n      datesEnabled: state.datesEnabled,\n      hoveredDate: state.hoveredDate,\n      selectedDate: state.selectedDate,\n      selectedRange: state.selectedRange,\n      displayMonths,\n      dateCustomClasses: state.dateCustomClasses,\n      dateTooltipTexts: state.dateTooltipTexts,\n      monthIndex\n    }));\n    return Object.assign({}, state, {\n      flaggedMonths\n    });\n  }\n  if (state.view.mode === 'month' && state.monthsCalendar) {\n    const flaggedMonthsCalendar = state.monthsCalendar.map((formattedMonth, monthIndex) => flagMonthsCalendar(formattedMonth, {\n      isDisabled: state.isDisabled,\n      minDate: state.minDate,\n      maxDate: state.maxDate,\n      hoveredMonth: state.hoveredMonth,\n      selectedDate: state.selectedDate,\n      datesDisabled: state.datesDisabled,\n      datesEnabled: state.datesEnabled,\n      selectedRange: state.selectedRange,\n      displayMonths,\n      monthIndex\n    }));\n    return Object.assign({}, state, {\n      flaggedMonthsCalendar\n    });\n  }\n  if (state.view.mode === 'year' && state.yearsCalendarModel) {\n    const yearsCalendarFlagged = state.yearsCalendarModel.map((formattedMonth, yearIndex) => flagYearsCalendar(formattedMonth, {\n      isDisabled: state.isDisabled,\n      minDate: state.minDate,\n      maxDate: state.maxDate,\n      hoveredYear: state.hoveredYear,\n      selectedDate: state.selectedDate,\n      datesDisabled: state.datesDisabled,\n      datesEnabled: state.datesEnabled,\n      selectedRange: state.selectedRange,\n      displayMonths,\n      yearIndex\n    }));\n    return Object.assign({}, state, {\n      yearsCalendarFlagged\n    });\n  }\n  return state;\n}\nfunction navigateOffsetReducer(state, action) {\n  if (!state.view) {\n    return state;\n  }\n  const date = shiftViewDate(state, action);\n  if (!date) {\n    return state;\n  }\n  const newState = {\n    view: {\n      mode: state.view.mode,\n      date\n    }\n  };\n  return Object.assign({}, state, newState);\n}\nfunction shiftViewDate(state, action) {\n  if (!state.view) {\n    return undefined;\n  }\n  if (state.view.mode === 'year' && state.minMode === 'year') {\n    const initialDate = getYearsCalendarInitialDate(state, 0);\n    if (initialDate) {\n      const middleDate = shiftDate(initialDate, {\n        year: -initialYearShift\n      });\n      return shiftDate(middleDate, action.payload);\n    }\n  }\n  return shiftDate(startOf(state.view.date, 'month'), action.payload);\n}\nfunction getFormatOptions(state) {\n  return {\n    locale: state.locale,\n    monthTitle: state.monthTitle,\n    yearTitle: state.yearTitle,\n    dayLabel: state.dayLabel,\n    monthLabel: state.monthLabel,\n    yearLabel: state.yearLabel,\n    weekNumbers: state.weekNumbers\n  };\n}\n/**\n * if view date is provided (bsValue|ngModel) it should be shown\n * if view date is not provider:\n * if minDate>currentDate (default view value), show minDate\n * if maxDate<currentDate(default view value) show maxDate\n */\nfunction getViewDate(viewDate, minDate, maxDate) {\n  const _date = Array.isArray(viewDate) ? viewDate[0] : viewDate;\n  if (minDate && isAfter(minDate, _date, 'day')) {\n    return minDate;\n  }\n  if (maxDate && isBefore(maxDate, _date, 'day')) {\n    return maxDate;\n  }\n  return _date;\n}\nfunction isDisplayOneMonth(viewDate, minDate, maxDate) {\n  if (maxDate && isSame(maxDate, viewDate, 'day')) {\n    return true;\n  }\n  return minDate && maxDate && minDate.getMonth() === maxDate.getMonth();\n}\nlet BsDatepickerStore = /*#__PURE__*/(() => {\n  class BsDatepickerStore extends MiniStore {\n    constructor() {\n      const _dispatcher = new BehaviorSubject({\n        type: '[datepicker] dispatcher init'\n      });\n      const state = new MiniState(initialDatepickerState, _dispatcher, bsDatepickerReducer);\n      super(_dispatcher, bsDatepickerReducer, state);\n    }\n    static {\n      this.ɵfac = function BsDatepickerStore_Factory(t) {\n        return new (t || BsDatepickerStore)();\n      };\n    }\n    static {\n      this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n        token: BsDatepickerStore,\n        factory: BsDatepickerStore.ɵfac,\n        providedIn: 'platform'\n      });\n    }\n  }\n  return BsDatepickerStore;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet BsCustomDatesViewComponent = /*#__PURE__*/(() => {\n  class BsCustomDatesViewComponent {\n    constructor() {\n      this.onSelect = new EventEmitter();\n    }\n    selectFromRanges(range) {\n      this.onSelect.emit(range);\n    }\n    static {\n      this.ɵfac = function BsCustomDatesViewComponent_Factory(t) {\n        return new (t || BsCustomDatesViewComponent)();\n      };\n    }\n    static {\n      this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n        type: BsCustomDatesViewComponent,\n        selectors: [[\"bs-custom-date-view\"]],\n        inputs: {\n          ranges: \"ranges\",\n          selectedRange: \"selectedRange\",\n          customRangeLabel: \"customRangeLabel\"\n        },\n        outputs: {\n          onSelect: \"onSelect\"\n        },\n        decls: 2,\n        vars: 1,\n        consts: [[1, \"bs-datepicker-predefined-btns\"], [\"type\", \"button\", \"class\", \"btn\", 3, \"selected\", \"click\", 4, \"ngFor\", \"ngForOf\"], [\"type\", \"button\", 1, \"btn\", 3, \"click\"]],\n        template: function BsCustomDatesViewComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0);\n            i0.ɵɵtemplate(1, BsCustomDatesViewComponent_button_1_Template, 2, 3, \"button\", 1);\n            i0.ɵɵelementEnd();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngForOf\", ctx.ranges);\n          }\n        },\n        dependencies: [i6.NgForOf],\n        encapsulation: 2,\n        changeDetection: 0\n      });\n    }\n  }\n  return BsCustomDatesViewComponent;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/** *************** */\n// events\n/** *************** */\nvar BsNavigationDirection = /*#__PURE__*/function (BsNavigationDirection) {\n  BsNavigationDirection[BsNavigationDirection[\"UP\"] = 0] = \"UP\";\n  BsNavigationDirection[BsNavigationDirection[\"DOWN\"] = 1] = \"DOWN\";\n  return BsNavigationDirection;\n}(BsNavigationDirection || {});\nlet BsCurrentDateViewComponent = /*#__PURE__*/(() => {\n  class BsCurrentDateViewComponent {\n    static {\n      this.ɵfac = function BsCurrentDateViewComponent_Factory(t) {\n        return new (t || BsCurrentDateViewComponent)();\n      };\n    }\n    static {\n      this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n        type: BsCurrentDateViewComponent,\n        selectors: [[\"bs-current-date\"]],\n        inputs: {\n          title: \"title\"\n        },\n        decls: 3,\n        vars: 1,\n        consts: [[1, \"current-timedate\"]],\n        template: function BsCurrentDateViewComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"span\");\n            i0.ɵɵtext(2);\n            i0.ɵɵelementEnd()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(2);\n            i0.ɵɵtextInterpolate(ctx.title);\n          }\n        },\n        encapsulation: 2\n      });\n    }\n  }\n  return BsCurrentDateViewComponent;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet BsTimepickerViewComponent = /*#__PURE__*/(() => {\n  class BsTimepickerViewComponent {\n    constructor() {\n      this.ampm = 'ok';\n      this.hours = 0;\n      this.minutes = 0;\n    }\n    static {\n      this.ɵfac = function BsTimepickerViewComponent_Factory(t) {\n        return new (t || BsTimepickerViewComponent)();\n      };\n    }\n    static {\n      this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n        type: BsTimepickerViewComponent,\n        selectors: [[\"bs-timepicker\"]],\n        decls: 16,\n        vars: 3,\n        consts: [[1, \"bs-timepicker-container\"], [1, \"bs-timepicker-controls\"], [\"type\", \"button\", 1, \"bs-decrease\"], [\"type\", \"text\", \"placeholder\", \"00\", 3, \"value\"], [\"type\", \"button\", 1, \"bs-increase\"], [\"type\", \"button\", 1, \"switch-time-format\"], [\"src\", \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAsAAAAKCAYAAABi8KSDAAABSElEQVQYV3XQPUvDUBQG4HNuagtVqc6KgouCv6GIuIntYBLB9hcIQpLStCAIV7DYmpTcRWcXqZio3Vwc/UCc/QEqfgyKGbr0I7nS1EiHeqYzPO/h5SD0jaxUZjmSLCB+OFb+UFINFwASAEAdpu9gaGXVyAHHFQBkHpKHc6a9dzECvADyY9sqlAMsK9W0jzxDXqeytr3mhQckxSji27TJJ5/rPmIpwJJq3HrtduriYOurv1a4i1p5HnhkG9OFymi0ReoO05cGwb+ayv4dysVygjeFmsP05f8wpZQ8fsdvfmuY9zjWSNqUtgYFVnOVReILYoBFzdQI5/GGFzNHhGbeZnopDGU29sZbscgldmC99w35VOATTycIMMcBXIfpSVGzZhA6C8hh00conln6VQ9TGgV32OEAKQC4DrBq7CJwd0ggR7Vq/rPrfgB+C3sGypY5DAAAAABJRU5ErkJggg==\", \"alt\", \"\"]],\n        template: function BsTimepickerViewComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"button\", 2);\n            i0.ɵɵtext(3, \"-\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(4, \"input\", 3);\n            i0.ɵɵelementStart(5, \"button\", 4);\n            i0.ɵɵtext(6, \"+\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(7, \"div\", 1)(8, \"button\", 2);\n            i0.ɵɵtext(9, \"-\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(10, \"input\", 3);\n            i0.ɵɵelementStart(11, \"button\", 4);\n            i0.ɵɵtext(12, \"+\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(13, \"button\", 5);\n            i0.ɵɵtext(14);\n            i0.ɵɵelement(15, \"img\", 6);\n            i0.ɵɵelementEnd()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"value\", ctx.hours);\n            i0.ɵɵadvance(6);\n            i0.ɵɵproperty(\"value\", ctx.minutes);\n            i0.ɵɵadvance(4);\n            i0.ɵɵtextInterpolate1(\"\", ctx.ampm, \" \");\n          }\n        },\n        encapsulation: 2\n      });\n    }\n  }\n  return BsTimepickerViewComponent;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet BsCalendarLayoutComponent = /*#__PURE__*/(() => {\n  class BsCalendarLayoutComponent {\n    static {\n      this.ɵfac = function BsCalendarLayoutComponent_Factory(t) {\n        return new (t || BsCalendarLayoutComponent)();\n      };\n    }\n    static {\n      this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n        type: BsCalendarLayoutComponent,\n        selectors: [[\"bs-calendar-layout\"]],\n        ngContentSelectors: _c1,\n        decls: 6,\n        vars: 2,\n        consts: [[\"title\", \"hey there\", 4, \"ngIf\"], [1, \"bs-datepicker-head\"], [1, \"bs-datepicker-body\"], [4, \"ngIf\"], [\"title\", \"hey there\"]],\n        template: function BsCalendarLayoutComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵprojectionDef(_c0);\n            i0.ɵɵtemplate(0, BsCalendarLayoutComponent_bs_current_date_0_Template, 1, 0, \"bs-current-date\", 0);\n            i0.ɵɵelementStart(1, \"div\", 1);\n            i0.ɵɵprojection(2);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(3, \"div\", 2);\n            i0.ɵɵprojection(4, 1);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(5, BsCalendarLayoutComponent_bs_timepicker_5_Template, 1, 0, \"bs-timepicker\", 3);\n          }\n          if (rf & 2) {\n            i0.ɵɵproperty(\"ngIf\", false);\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"ngIf\", false);\n          }\n        },\n        dependencies: [i6.NgIf, BsCurrentDateViewComponent, BsTimepickerViewComponent],\n        encapsulation: 2\n      });\n    }\n  }\n  return BsCalendarLayoutComponent;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet BsDatepickerDayDecoratorComponent = /*#__PURE__*/(() => {\n  class BsDatepickerDayDecoratorComponent {\n    constructor(_config, _elRef, _renderer) {\n      this._config = _config;\n      this._elRef = _elRef;\n      this._renderer = _renderer;\n      this.day = {\n        date: new Date(),\n        label: ''\n      };\n    }\n    ngOnInit() {\n      if (this.day?.isToday && this._config && this._config.customTodayClass) {\n        this._renderer.addClass(this._elRef.nativeElement, this._config.customTodayClass);\n      }\n      if (typeof this.day?.customClasses === 'string') {\n        this.day?.customClasses.split(' ').filter(className => className).forEach(className => {\n          this._renderer.addClass(this._elRef.nativeElement, className);\n        });\n      }\n    }\n    static {\n      this.ɵfac = function BsDatepickerDayDecoratorComponent_Factory(t) {\n        return new (t || BsDatepickerDayDecoratorComponent)(i0.ɵɵdirectiveInject(BsDatepickerConfig), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.Renderer2));\n      };\n    }\n    static {\n      this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n        type: BsDatepickerDayDecoratorComponent,\n        selectors: [[\"\", \"bsDatepickerDayDecorator\", \"\"]],\n        hostVars: 16,\n        hostBindings: function BsDatepickerDayDecoratorComponent_HostBindings(rf, ctx) {\n          if (rf & 2) {\n            i0.ɵɵclassProp(\"disabled\", ctx.day.isDisabled)(\"is-highlighted\", ctx.day.isHovered)(\"is-other-month\", ctx.day.isOtherMonth)(\"is-active-other-month\", ctx.day.isOtherMonthHovered)(\"in-range\", ctx.day.isInRange)(\"select-start\", ctx.day.isSelectionStart)(\"select-end\", ctx.day.isSelectionEnd)(\"selected\", ctx.day.isSelected);\n          }\n        },\n        inputs: {\n          day: \"day\"\n        },\n        attrs: _c2,\n        decls: 1,\n        vars: 1,\n        template: function BsDatepickerDayDecoratorComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵtext(0);\n          }\n          if (rf & 2) {\n            i0.ɵɵtextInterpolate(ctx.day && ctx.day.label || \"\");\n          }\n        },\n        encapsulation: 2,\n        changeDetection: 0\n      });\n    }\n  }\n  return BsDatepickerDayDecoratorComponent;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet BsDatepickerNavigationViewComponent = /*#__PURE__*/(() => {\n  class BsDatepickerNavigationViewComponent {\n    constructor() {\n      this.isDisabled = false;\n      this.onNavigate = new EventEmitter();\n      this.onViewMode = new EventEmitter();\n    }\n    navTo(down) {\n      this.onNavigate.emit(down ? BsNavigationDirection.DOWN : BsNavigationDirection.UP);\n    }\n    view(viewMode) {\n      if (this.isDisabled) {\n        return;\n      }\n      this.onViewMode.emit(viewMode);\n    }\n    static {\n      this.ɵfac = function BsDatepickerNavigationViewComponent_Factory(t) {\n        return new (t || BsDatepickerNavigationViewComponent)();\n      };\n    }\n    static {\n      this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n        type: BsDatepickerNavigationViewComponent,\n        selectors: [[\"bs-datepicker-navigation-view\"]],\n        inputs: {\n          calendar: \"calendar\",\n          isDisabled: \"isDisabled\"\n        },\n        outputs: {\n          onNavigate: \"onNavigate\",\n          onViewMode: \"onViewMode\"\n        },\n        decls: 12,\n        vars: 9,\n        consts: [[\"type\", \"button\", 1, \"previous\", 3, \"click\", \"disabled\"], [4, \"ngIf\"], [\"type\", \"button\", 1, \"current\", 3, \"click\", \"disabled\"], [\"type\", \"button\", 1, \"next\", 3, \"click\", \"disabled\"]],\n        template: function BsDatepickerNavigationViewComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"button\", 0);\n            i0.ɵɵlistener(\"click\", function BsDatepickerNavigationViewComponent_Template_button_click_0_listener() {\n              return ctx.navTo(true);\n            });\n            i0.ɵɵelementStart(1, \"span\");\n            i0.ɵɵtext(2, \"\\u2039\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵtemplate(3, BsDatepickerNavigationViewComponent_ng_container_3_Template, 5, 2, \"ng-container\", 1);\n            i0.ɵɵtext(4, \" \\u200B \");\n            i0.ɵɵelementStart(5, \"button\", 2);\n            i0.ɵɵlistener(\"click\", function BsDatepickerNavigationViewComponent_Template_button_click_5_listener() {\n              return ctx.view(\"year\");\n            });\n            i0.ɵɵelementStart(6, \"span\");\n            i0.ɵɵtext(7);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵtext(8, \" \\u200B \");\n            i0.ɵɵelementStart(9, \"button\", 3);\n            i0.ɵɵlistener(\"click\", function BsDatepickerNavigationViewComponent_Template_button_click_9_listener() {\n              return ctx.navTo(false);\n            });\n            i0.ɵɵelementStart(10, \"span\");\n            i0.ɵɵtext(11, \"\\u203A\");\n            i0.ɵɵelementEnd()();\n          }\n          if (rf & 2) {\n            i0.ɵɵstyleProp(\"visibility\", ctx.calendar.hideLeftArrow ? \"hidden\" : \"visible\");\n            i0.ɵɵproperty(\"disabled\", ctx.calendar.disableLeftArrow);\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"ngIf\", ctx.calendar && ctx.calendar.monthTitle);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"disabled\", ctx.isDisabled);\n            i0.ɵɵadvance(2);\n            i0.ɵɵtextInterpolate(ctx.calendar.yearTitle);\n            i0.ɵɵadvance(2);\n            i0.ɵɵstyleProp(\"visibility\", ctx.calendar.hideRightArrow ? \"hidden\" : \"visible\");\n            i0.ɵɵproperty(\"disabled\", ctx.calendar.disableRightArrow);\n          }\n        },\n        dependencies: [i6.NgIf],\n        encapsulation: 2,\n        changeDetection: 0\n      });\n    }\n  }\n  return BsDatepickerNavigationViewComponent;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet BsDaysCalendarViewComponent = /*#__PURE__*/(() => {\n  class BsDaysCalendarViewComponent {\n    constructor(_config) {\n      this._config = _config;\n      this.onNavigate = new EventEmitter();\n      this.onViewMode = new EventEmitter();\n      this.onSelect = new EventEmitter();\n      this.onHover = new EventEmitter();\n      this.onHoverWeek = new EventEmitter();\n      this.isiOS = /iPad|iPhone|iPod/.test(navigator.platform) || navigator.platform === 'MacIntel' && navigator.maxTouchPoints > 1;\n      if (this._config.dateTooltipTexts && this._config.dateTooltipTexts.length > 0) {\n        this.isShowTooltip = true;\n      }\n    }\n    navigateTo(event) {\n      const step = BsNavigationDirection.DOWN === event ? -1 : 1;\n      this.onNavigate.emit({\n        step: {\n          month: step\n        }\n      });\n    }\n    changeViewMode(event) {\n      this.onViewMode.emit(event);\n    }\n    selectDay(event) {\n      this.onSelect.emit(event);\n    }\n    selectWeek(week) {\n      if (!this._config.selectWeek && !this._config.selectWeekDateRange) {\n        return;\n      }\n      if (week.days.length === 0) {\n        return;\n      }\n      if (this._config.selectWeek && week.days[0] && !week.days[0].isDisabled && this._config.selectFromOtherMonth) {\n        this.onSelect.emit(week.days[0]);\n        return;\n      }\n      const selectedDay = week.days.find(day => {\n        return this._config.selectFromOtherMonth ? !day.isDisabled : !day.isOtherMonth && !day.isDisabled;\n      });\n      this.onSelect.emit(selectedDay);\n      if (this._config.selectWeekDateRange) {\n        const days = week.days.slice(0);\n        const lastDayOfRange = days.reverse().find(day => {\n          return this._config.selectFromOtherMonth ? !day.isDisabled : !day.isOtherMonth && !day.isDisabled;\n        });\n        this.onSelect.emit(lastDayOfRange);\n      }\n    }\n    weekHoverHandler(cell, isHovered) {\n      if (!this._config.selectWeek && !this._config.selectWeekDateRange) {\n        return;\n      }\n      const hasActiveDays = cell.days.find(day => {\n        return this._config.selectFromOtherMonth ? !day.isDisabled : !day.isOtherMonth && !day.isDisabled;\n      });\n      if (hasActiveDays) {\n        cell.isHovered = isHovered;\n        this.isWeekHovered = isHovered;\n        this.onHoverWeek.emit(cell);\n      }\n    }\n    hoverDay(cell, isHovered) {\n      if (this._config.selectFromOtherMonth && cell.isOtherMonth) {\n        cell.isOtherMonthHovered = isHovered;\n      }\n      if (this._config.dateTooltipTexts) {\n        cell.tooltipText = '';\n        this._config.dateTooltipTexts.forEach(dateData => {\n          if (isSameDay(dateData.date, cell.date)) {\n            cell.tooltipText = dateData.tooltipText;\n            return;\n          }\n        });\n      }\n      this.onHover.emit({\n        cell,\n        isHovered\n      });\n    }\n    static {\n      this.ɵfac = function BsDaysCalendarViewComponent_Factory(t) {\n        return new (t || BsDaysCalendarViewComponent)(i0.ɵɵdirectiveInject(BsDatepickerConfig));\n      };\n    }\n    static {\n      this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n        type: BsDaysCalendarViewComponent,\n        selectors: [[\"bs-days-calendar-view\"]],\n        inputs: {\n          calendar: \"calendar\",\n          options: \"options\",\n          isDisabled: \"isDisabled\"\n        },\n        outputs: {\n          onNavigate: \"onNavigate\",\n          onViewMode: \"onViewMode\",\n          onSelect: \"onSelect\",\n          onHover: \"onHover\",\n          onHoverWeek: \"onHoverWeek\"\n        },\n        decls: 9,\n        vars: 5,\n        consts: [[3, \"onNavigate\", \"onViewMode\", \"calendar\", \"isDisabled\"], [\"role\", \"grid\", 1, \"days\", \"weeks\"], [4, \"ngIf\"], [\"aria-label\", \"weekday\", 4, \"ngFor\", \"ngForOf\"], [4, \"ngFor\", \"ngForOf\"], [\"aria-label\", \"weekday\"], [\"class\", \"week\", 3, \"active-week\", 4, \"ngIf\"], [\"role\", \"gridcell\", 4, \"ngFor\", \"ngForOf\"], [1, \"week\"], [3, \"click\", 4, \"ngIf\"], [3, \"click\", \"mouseenter\", \"mouseleave\", 4, \"ngIf\"], [3, \"click\"], [3, \"click\", \"mouseenter\", \"mouseleave\"], [\"role\", \"gridcell\"], [\"bsDatepickerDayDecorator\", \"\", 3, \"day\", \"tooltip\", \"click\", \"mouseenter\", \"mouseleave\", 4, \"ngIf\"], [\"bsDatepickerDayDecorator\", \"\", 3, \"day\", \"click\", \"mouseenter\", \"mouseleave\", 4, \"ngIf\"], [\"bsDatepickerDayDecorator\", \"\", 3, \"day\", \"click\", 4, \"ngIf\"], [\"bsDatepickerDayDecorator\", \"\", 3, \"click\", \"mouseenter\", \"mouseleave\", \"day\", \"tooltip\"], [\"bsDatepickerDayDecorator\", \"\", 3, \"click\", \"mouseenter\", \"mouseleave\", \"day\"], [\"bsDatepickerDayDecorator\", \"\", 3, \"click\", \"day\"]],\n        template: function BsDaysCalendarViewComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"bs-calendar-layout\")(1, \"bs-datepicker-navigation-view\", 0);\n            i0.ɵɵlistener(\"onNavigate\", function BsDaysCalendarViewComponent_Template_bs_datepicker_navigation_view_onNavigate_1_listener($event) {\n              return ctx.navigateTo($event);\n            })(\"onViewMode\", function BsDaysCalendarViewComponent_Template_bs_datepicker_navigation_view_onViewMode_1_listener($event) {\n              return ctx.changeViewMode($event);\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(2, \"table\", 1)(3, \"thead\")(4, \"tr\");\n            i0.ɵɵtemplate(5, BsDaysCalendarViewComponent_th_5_Template, 1, 0, \"th\", 2)(6, BsDaysCalendarViewComponent_th_6_Template, 2, 1, \"th\", 3);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(7, \"tbody\");\n            i0.ɵɵtemplate(8, BsDaysCalendarViewComponent_tr_8_Template, 3, 2, \"tr\", 4);\n            i0.ɵɵelementEnd()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"calendar\", ctx.calendar)(\"isDisabled\", !!ctx.isDisabled);\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"ngIf\", ctx.options && ctx.options.showWeekNumbers);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngForOf\", ctx.calendar.weekdays);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngForOf\", ctx.calendar.weeks);\n          }\n        },\n        dependencies: [i6.NgForOf, i6.NgIf, i3.TooltipDirective, BsCalendarLayoutComponent, BsDatepickerDayDecoratorComponent, BsDatepickerNavigationViewComponent],\n        encapsulation: 2\n      });\n    }\n  }\n  return BsDaysCalendarViewComponent;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet BsMonthCalendarViewComponent = /*#__PURE__*/(() => {\n  class BsMonthCalendarViewComponent {\n    constructor() {\n      this.onNavigate = new EventEmitter();\n      this.onViewMode = new EventEmitter();\n      this.onSelect = new EventEmitter();\n      this.onHover = new EventEmitter();\n    }\n    navigateTo(event) {\n      const step = BsNavigationDirection.DOWN === event ? -1 : 1;\n      this.onNavigate.emit({\n        step: {\n          year: step\n        }\n      });\n    }\n    viewMonth(month) {\n      this.onSelect.emit(month);\n    }\n    hoverMonth(cell, isHovered) {\n      this.onHover.emit({\n        cell,\n        isHovered\n      });\n    }\n    changeViewMode(event) {\n      this.onViewMode.emit(event);\n    }\n    static {\n      this.ɵfac = function BsMonthCalendarViewComponent_Factory(t) {\n        return new (t || BsMonthCalendarViewComponent)();\n      };\n    }\n    static {\n      this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n        type: BsMonthCalendarViewComponent,\n        selectors: [[\"bs-month-calendar-view\"]],\n        inputs: {\n          calendar: \"calendar\"\n        },\n        outputs: {\n          onNavigate: \"onNavigate\",\n          onViewMode: \"onViewMode\",\n          onSelect: \"onSelect\",\n          onHover: \"onHover\"\n        },\n        decls: 5,\n        vars: 2,\n        consts: [[3, \"onNavigate\", \"onViewMode\", \"calendar\"], [\"role\", \"grid\", 1, \"months\"], [4, \"ngFor\", \"ngForOf\"], [\"role\", \"gridcell\", 3, \"disabled\", \"is-highlighted\", \"click\", \"mouseenter\", \"mouseleave\", 4, \"ngFor\", \"ngForOf\"], [\"role\", \"gridcell\", 3, \"click\", \"mouseenter\", \"mouseleave\"]],\n        template: function BsMonthCalendarViewComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"bs-calendar-layout\")(1, \"bs-datepicker-navigation-view\", 0);\n            i0.ɵɵlistener(\"onNavigate\", function BsMonthCalendarViewComponent_Template_bs_datepicker_navigation_view_onNavigate_1_listener($event) {\n              return ctx.navigateTo($event);\n            })(\"onViewMode\", function BsMonthCalendarViewComponent_Template_bs_datepicker_navigation_view_onViewMode_1_listener($event) {\n              return ctx.changeViewMode($event);\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(2, \"table\", 1)(3, \"tbody\");\n            i0.ɵɵtemplate(4, BsMonthCalendarViewComponent_tr_4_Template, 2, 1, \"tr\", 2);\n            i0.ɵɵelementEnd()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"calendar\", ctx.calendar);\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"ngForOf\", ctx.calendar == null ? null : ctx.calendar.months);\n          }\n        },\n        dependencies: [i6.NgForOf, BsCalendarLayoutComponent, BsDatepickerNavigationViewComponent],\n        encapsulation: 2\n      });\n    }\n  }\n  return BsMonthCalendarViewComponent;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet BsYearsCalendarViewComponent = /*#__PURE__*/(() => {\n  class BsYearsCalendarViewComponent {\n    constructor() {\n      this.onNavigate = new EventEmitter();\n      this.onViewMode = new EventEmitter();\n      this.onSelect = new EventEmitter();\n      this.onHover = new EventEmitter();\n    }\n    navigateTo(event) {\n      const step = BsNavigationDirection.DOWN === event ? -1 : 1;\n      this.onNavigate.emit({\n        step: {\n          year: step * yearsPerCalendar\n        }\n      });\n    }\n    viewYear(year) {\n      this.onSelect.emit(year);\n    }\n    hoverYear(cell, isHovered) {\n      this.onHover.emit({\n        cell,\n        isHovered\n      });\n    }\n    changeViewMode(event) {\n      this.onViewMode.emit(event);\n    }\n    static {\n      this.ɵfac = function BsYearsCalendarViewComponent_Factory(t) {\n        return new (t || BsYearsCalendarViewComponent)();\n      };\n    }\n    static {\n      this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n        type: BsYearsCalendarViewComponent,\n        selectors: [[\"bs-years-calendar-view\"]],\n        inputs: {\n          calendar: \"calendar\"\n        },\n        outputs: {\n          onNavigate: \"onNavigate\",\n          onViewMode: \"onViewMode\",\n          onSelect: \"onSelect\",\n          onHover: \"onHover\"\n        },\n        decls: 5,\n        vars: 2,\n        consts: [[3, \"onNavigate\", \"onViewMode\", \"calendar\"], [\"role\", \"grid\", 1, \"years\"], [4, \"ngFor\", \"ngForOf\"], [\"role\", \"gridcell\", 3, \"disabled\", \"is-highlighted\", \"click\", \"mouseenter\", \"mouseleave\", 4, \"ngFor\", \"ngForOf\"], [\"role\", \"gridcell\", 3, \"click\", \"mouseenter\", \"mouseleave\"]],\n        template: function BsYearsCalendarViewComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"bs-calendar-layout\")(1, \"bs-datepicker-navigation-view\", 0);\n            i0.ɵɵlistener(\"onNavigate\", function BsYearsCalendarViewComponent_Template_bs_datepicker_navigation_view_onNavigate_1_listener($event) {\n              return ctx.navigateTo($event);\n            })(\"onViewMode\", function BsYearsCalendarViewComponent_Template_bs_datepicker_navigation_view_onViewMode_1_listener($event) {\n              return ctx.changeViewMode($event);\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(2, \"table\", 1)(3, \"tbody\");\n            i0.ɵɵtemplate(4, BsYearsCalendarViewComponent_tr_4_Template, 2, 1, \"tr\", 2);\n            i0.ɵɵelementEnd()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"calendar\", ctx.calendar);\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"ngForOf\", ctx.calendar == null ? null : ctx.calendar.years);\n          }\n        },\n        dependencies: [i6.NgForOf, BsCalendarLayoutComponent, BsDatepickerNavigationViewComponent],\n        encapsulation: 2\n      });\n    }\n  }\n  return BsYearsCalendarViewComponent;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet BsDatepickerContainerComponent = /*#__PURE__*/(() => {\n  class BsDatepickerContainerComponent extends BsDatepickerAbstractComponent {\n    set value(value) {\n      this._effects?.setValue(value);\n    }\n    get isDatePickerDisabled() {\n      return !!this._config.isDisabled;\n    }\n    get isDatepickerDisabled() {\n      return this.isDatePickerDisabled ? '' : null;\n    }\n    get isDatepickerReadonly() {\n      return this.isDatePickerDisabled ? '' : null;\n    }\n    constructor(_renderer, _config, _store, _element, _actions, _effects, _positionService) {\n      super();\n      this._config = _config;\n      this._store = _store;\n      this._element = _element;\n      this._actions = _actions;\n      this._positionService = _positionService;\n      this.valueChange = new EventEmitter();\n      this.animationState = 'void';\n      this.isRangePicker = false;\n      this._subs = [];\n      this._effects = _effects;\n      _renderer.setStyle(_element.nativeElement, 'display', 'block');\n      _renderer.setStyle(_element.nativeElement, 'position', 'absolute');\n    }\n    ngOnInit() {\n      this._positionService.setOptions({\n        modifiers: {\n          flip: {\n            enabled: this._config.adaptivePosition\n          },\n          preventOverflow: {\n            enabled: this._config.adaptivePosition\n          }\n        },\n        allowedPositions: this._config.allowedPositions\n      });\n      this._positionService.event$?.pipe(take(1)).subscribe(() => {\n        this._positionService.disable();\n        if (this._config.isAnimated) {\n          this.animationState = this.isTopPosition ? 'animated-up' : 'animated-down';\n          return;\n        }\n        this.animationState = 'unanimated';\n      });\n      this.isOtherMonthsActive = this._config.selectFromOtherMonth;\n      this.containerClass = this._config.containerClass;\n      this.showTodayBtn = this._config.showTodayButton;\n      this.todayBtnLbl = this._config.todayButtonLabel;\n      this.todayPos = this._config.todayPosition;\n      this.showClearBtn = this._config.showClearButton;\n      this.clearBtnLbl = this._config.clearButtonLabel;\n      this.clearPos = this._config.clearPosition;\n      this.customRangeBtnLbl = this._config.customRangeButtonLabel;\n      this.withTimepicker = this._config.withTimepicker;\n      this._effects?.init(this._store)\n      // intial state options\n      .setOptions(this._config)\n      // data binding view --> model\n      .setBindings(this)\n      // set event handlers\n      .setEventHandlers(this).registerDatepickerSideEffects();\n      let currentDate;\n      // todo: move it somewhere else\n      // on selected date change\n      this._subs.push(this._store.select(state => state.selectedDate).subscribe(date => {\n        currentDate = date;\n        this.valueChange.emit(date);\n      }));\n      this._subs.push(this._store.select(state => state.selectedTime).subscribe(time => {\n        if (!time || !time[0] || !(time[0] instanceof Date) || time[0] === currentDate) {\n          return;\n        }\n        this.valueChange.emit(time[0]);\n      }));\n      this._store.dispatch(this._actions.changeViewMode(this._config.startView));\n    }\n    ngAfterViewInit() {\n      this.selectedTimeSub.add(this.selectedTime?.subscribe(val => {\n        if (Array.isArray(val) && val.length >= 1) {\n          this.startTimepicker?.writeValue(val[0]);\n        }\n      }));\n      this.startTimepicker?.registerOnChange(val => {\n        this.timeSelectHandler(val, 0);\n      });\n    }\n    get isTopPosition() {\n      return this._element.nativeElement.classList.contains('top');\n    }\n    positionServiceEnable() {\n      this._positionService.enable();\n    }\n    timeSelectHandler(date, index) {\n      this._store.dispatch(this._actions.selectTime(date, index));\n    }\n    daySelectHandler(day) {\n      if (!day) {\n        return;\n      }\n      const isDisabled = this.isOtherMonthsActive ? day.isDisabled : day.isOtherMonth || day.isDisabled;\n      if (isDisabled) {\n        return;\n      }\n      this._store.dispatch(this._actions.select(day.date));\n    }\n    monthSelectHandler(day) {\n      if (!day || day.isDisabled) {\n        return;\n      }\n      this._store.dispatch(this._actions.navigateTo({\n        unit: {\n          month: getMonth(day.date),\n          year: getFullYear(day.date)\n        },\n        viewMode: 'day'\n      }));\n    }\n    yearSelectHandler(day) {\n      if (!day || day.isDisabled) {\n        return;\n      }\n      this._store.dispatch(this._actions.navigateTo({\n        unit: {\n          year: getFullYear(day.date)\n        },\n        viewMode: 'month'\n      }));\n    }\n    setToday() {\n      this._store.dispatch(this._actions.select(new Date()));\n    }\n    clearDate() {\n      this._store.dispatch(this._actions.select(undefined));\n    }\n    ngOnDestroy() {\n      for (const sub of this._subs) {\n        sub.unsubscribe();\n      }\n      this.selectedTimeSub.unsubscribe();\n      this._effects?.destroy();\n    }\n    static {\n      this.ɵfac = function BsDatepickerContainerComponent_Factory(t) {\n        return new (t || BsDatepickerContainerComponent)(i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(BsDatepickerConfig), i0.ɵɵdirectiveInject(BsDatepickerStore), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(BsDatepickerActions), i0.ɵɵdirectiveInject(BsDatepickerEffects), i0.ɵɵdirectiveInject(i5.PositioningService));\n      };\n    }\n    static {\n      this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n        type: BsDatepickerContainerComponent,\n        selectors: [[\"bs-datepicker-container\"]],\n        viewQuery: function BsDatepickerContainerComponent_Query(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵviewQuery(_c3, 5);\n          }\n          if (rf & 2) {\n            let _t;\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.startTimepicker = _t.first);\n          }\n        },\n        hostAttrs: [\"role\", \"dialog\", \"aria-label\", \"calendar\", 1, \"bottom\"],\n        hostVars: 2,\n        hostBindings: function BsDatepickerContainerComponent_HostBindings(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵlistener(\"click\", function BsDatepickerContainerComponent_click_HostBindingHandler($event) {\n              return ctx._stopPropagation($event);\n            });\n          }\n          if (rf & 2) {\n            i0.ɵɵattribute(\"disabled\", ctx.isDatepickerDisabled)(\"readonly\", ctx.isDatepickerReadonly);\n          }\n        },\n        features: [i0.ɵɵProvidersFeature([BsDatepickerStore, BsDatepickerEffects]), i0.ɵɵInheritDefinitionFeature],\n        decls: 2,\n        vars: 3,\n        consts: [[\"startTP\", \"\"], [\"endTP\", \"\"], [\"class\", \"bs-datepicker\", 3, \"ngClass\", 4, \"ngIf\"], [1, \"bs-datepicker\", 3, \"ngClass\"], [1, \"bs-datepicker-container\"], [\"role\", \"application\", 1, \"bs-calendar-container\", 3, \"ngSwitch\"], [4, \"ngSwitchCase\"], [\"class\", \"bs-media-container\", 4, \"ngSwitchCase\"], [\"class\", \"bs-datepicker-buttons\", 4, \"ngIf\"], [\"class\", \"bs-datepicker-custom-range\", 4, \"ngIf\"], [1, \"bs-media-container\"], [3, \"bs-datepicker-multiple\", \"calendar\", \"isDisabled\", \"options\", \"onNavigate\", \"onViewMode\", \"onHover\", \"onHoverWeek\", \"onSelect\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"bs-timepicker-in-datepicker-container\", 4, \"ngIf\"], [3, \"onNavigate\", \"onViewMode\", \"onHover\", \"onHoverWeek\", \"onSelect\", \"calendar\", \"isDisabled\", \"options\"], [1, \"bs-timepicker-in-datepicker-container\"], [3, \"disabled\"], [3, \"disabled\", 4, \"ngIf\"], [3, \"bs-datepicker-multiple\", \"calendar\", \"onNavigate\", \"onViewMode\", \"onHover\", \"onSelect\", 4, \"ngFor\", \"ngForOf\"], [3, \"onNavigate\", \"onViewMode\", \"onHover\", \"onSelect\", \"calendar\"], [1, \"bs-datepicker-buttons\"], [\"type\", \"button\", 1, \"btn\", \"btn-success\"], [\"type\", \"button\", 1, \"btn\", \"btn-default\"], [\"class\", \"btn-today-wrapper\", 3, \"today-left\", \"today-right\", \"today-center\", 4, \"ngIf\"], [\"class\", \"btn-clear-wrapper\", 3, \"clear-left\", \"clear-right\", \"clear-center\", 4, \"ngIf\"], [1, \"btn-today-wrapper\"], [1, \"btn\", \"btn-success\", 3, \"click\"], [1, \"btn-clear-wrapper\"], [1, \"bs-datepicker-custom-range\"], [3, \"onSelect\", \"selectedRange\", \"ranges\", \"customRangeLabel\"]],\n        template: function BsDatepickerContainerComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵtemplate(0, BsDatepickerContainerComponent_div_0_Template, 10, 11, \"div\", 2);\n            i0.ɵɵpipe(1, \"async\");\n          }\n          if (rf & 2) {\n            i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind1(1, 1, ctx.viewMode));\n          }\n        },\n        dependencies: [i6.NgClass, i6.NgForOf, i6.NgIf, i6.NgSwitch, i6.NgSwitchCase, i7.TimepickerComponent, BsCustomDatesViewComponent, BsDaysCalendarViewComponent, BsMonthCalendarViewComponent, BsYearsCalendarViewComponent, i6.AsyncPipe],\n        encapsulation: 2,\n        data: {\n          animation: [datepickerAnimation]\n        }\n      });\n    }\n  }\n  return BsDatepickerContainerComponent;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet previousDate$1;\nlet BsDatepickerDirective = /*#__PURE__*/(() => {\n  class BsDatepickerDirective {\n    get readonlyValue() {\n      return this.isDisabled ? '' : null;\n    }\n    constructor(_config, _elementRef, _renderer, _viewContainerRef, cis) {\n      this._config = _config;\n      this._elementRef = _elementRef;\n      this._renderer = _renderer;\n      /**\n       * Placement of a datepicker. Accepts: \"top\", \"bottom\", \"left\", \"right\"\n       */\n      this.placement = 'bottom';\n      /**\n       * Specifies events that should trigger. Supports a space separated list of\n       * event names.\n       */\n      this.triggers = 'click';\n      /**\n       * Close datepicker on outside click\n       */\n      this.outsideClick = true;\n      /**\n       * A selector specifying the element the datepicker should be appended to.\n       */\n      this.container = 'body';\n      this.outsideEsc = true;\n      this.isDestroy$ = new Subject();\n      /**\n       * Indicates whether datepicker's content is enabled or not\n       */\n      this.isDisabled = false;\n      /**\n       * Emits when datepicker value has been changed\n       */\n      this.bsValueChange = new EventEmitter();\n      this._subs = [];\n      this._dateInputFormat$ = new Subject();\n      // todo: assign only subset of fields\n      Object.assign(this, this._config);\n      this._datepicker = cis.createLoader(_elementRef, _viewContainerRef, _renderer);\n      this.onShown = this._datepicker.onShown;\n      this.onHidden = this._datepicker.onHidden;\n      this.isOpen$ = new BehaviorSubject(this.isOpen);\n    }\n    /**\n     * Returns whether or not the datepicker is currently being shown\n     */\n    get isOpen() {\n      return this._datepicker.isShown;\n    }\n    set isOpen(value) {\n      this.isOpen$.next(value);\n    }\n    /**\n     * Initial value of datepicker\n     */\n    set bsValue(value) {\n      if (this._bsValue && value && this._bsValue.getTime() === value.getTime()) {\n        return;\n      }\n      if (!this._bsValue && value && !this._config.withTimepicker) {\n        const now = new Date();\n        copyTime(value, now);\n      }\n      if (value && this.bsConfig?.initCurrentTime) {\n        value = setCurrentTimeOnDateSelect(value);\n      }\n      this.initPreviousValue();\n      this._bsValue = value;\n      this.bsValueChange.emit(value);\n    }\n    get dateInputFormat$() {\n      return this._dateInputFormat$;\n    }\n    ngOnInit() {\n      this._datepicker.listen({\n        outsideClick: this.outsideClick,\n        outsideEsc: this.outsideEsc,\n        triggers: this.triggers,\n        show: () => this.show()\n      });\n      this.setConfig();\n      this.initPreviousValue();\n    }\n    initPreviousValue() {\n      previousDate$1 = this._bsValue;\n    }\n    ngOnChanges(changes) {\n      if (changes[\"bsConfig\"]) {\n        if (changes[\"bsConfig\"].currentValue?.initCurrentTime && changes[\"bsConfig\"].currentValue?.initCurrentTime !== changes[\"bsConfig\"].previousValue?.initCurrentTime && this._bsValue) {\n          this.initPreviousValue();\n          this._bsValue = setCurrentTimeOnDateSelect(this._bsValue);\n          this.bsValueChange.emit(this._bsValue);\n        }\n        this.setConfig();\n        this._dateInputFormat$.next(this.bsConfig && this.bsConfig.dateInputFormat);\n      }\n      if (!this._datepickerRef || !this._datepickerRef.instance) {\n        return;\n      }\n      if (changes[\"minDate\"]) {\n        this._datepickerRef.instance.minDate = this.minDate;\n      }\n      if (changes[\"maxDate\"]) {\n        this._datepickerRef.instance.maxDate = this.maxDate;\n      }\n      if (changes[\"daysDisabled\"]) {\n        this._datepickerRef.instance.daysDisabled = this.daysDisabled;\n      }\n      if (changes[\"datesDisabled\"]) {\n        this._datepickerRef.instance.datesDisabled = this.datesDisabled;\n      }\n      if (changes[\"datesEnabled\"]) {\n        this._datepickerRef.instance.datesEnabled = this.datesEnabled;\n      }\n      if (changes[\"isDisabled\"]) {\n        this._datepickerRef.instance.isDisabled = this.isDisabled;\n      }\n      if (changes[\"dateCustomClasses\"]) {\n        this._datepickerRef.instance.dateCustomClasses = this.dateCustomClasses;\n      }\n      if (changes[\"dateTooltipTexts\"]) {\n        this._datepickerRef.instance.dateTooltipTexts = this.dateTooltipTexts;\n      }\n    }\n    initSubscribes() {\n      // if date changes from external source (model -> view)\n      this._subs.push(this.bsValueChange.subscribe(value => {\n        if (this._datepickerRef) {\n          this._datepickerRef.instance.value = value;\n        }\n      }));\n      // if date changes from picker (view -> model)\n      if (this._datepickerRef) {\n        this._subs.push(this._datepickerRef.instance.valueChange.subscribe(value => {\n          this.initPreviousValue();\n          this.bsValue = value;\n          if (this.keepDatepickerModalOpened()) {\n            return;\n          }\n          this.hide();\n        }));\n      }\n    }\n    keepDatepickerModalOpened() {\n      if (!previousDate$1 || !this.bsConfig?.keepDatepickerOpened || !this._config.withTimepicker) {\n        return false;\n      }\n      return this.isDateSame();\n    }\n    isDateSame() {\n      return previousDate$1 instanceof Date && this._bsValue?.getDate() === previousDate$1?.getDate() && this._bsValue?.getMonth() === previousDate$1?.getMonth() && this._bsValue?.getFullYear() === previousDate$1?.getFullYear();\n    }\n    ngAfterViewInit() {\n      this.isOpen$.pipe(filter(isOpen => isOpen !== this.isOpen), takeUntil(this.isDestroy$)).subscribe(() => this.toggle());\n    }\n    /**\n     * Opens an element’s datepicker. This is considered a “manual” triggering of\n     * the datepicker.\n     */\n    show() {\n      if (this._datepicker.isShown) {\n        return;\n      }\n      this.setConfig();\n      this._datepickerRef = this._datepicker.provide({\n        provide: BsDatepickerConfig,\n        useValue: this._config\n      }).attach(BsDatepickerContainerComponent).to(this.container).position({\n        attachment: this.placement\n      }).show({\n        placement: this.placement\n      });\n      this.initSubscribes();\n    }\n    /**\n     * Closes an element’s datepicker. This is considered a “manual” triggering of\n     * the datepicker.\n     */\n    hide() {\n      if (this.isOpen) {\n        this._datepicker.hide();\n      }\n      for (const sub of this._subs) {\n        sub.unsubscribe();\n      }\n      if (this._config.returnFocusToInput) {\n        this._renderer.selectRootElement(this._elementRef.nativeElement).focus();\n      }\n    }\n    /**\n     * Toggles an element’s datepicker. This is considered a “manual” triggering\n     * of the datepicker.\n     */\n    toggle() {\n      if (this.isOpen) {\n        return this.hide();\n      }\n      this.show();\n    }\n    /**\n     * Set config for datepicker\n     */\n    setConfig() {\n      this._config = Object.assign({}, this._config, this.bsConfig, {\n        value: this._config.keepDatesOutOfRules ? this._bsValue : checkBsValue(this._bsValue, this.maxDate || this.bsConfig && this.bsConfig.maxDate),\n        isDisabled: this.isDisabled,\n        minDate: this.minDate || this.bsConfig && this.bsConfig.minDate,\n        maxDate: this.maxDate || this.bsConfig && this.bsConfig.maxDate,\n        daysDisabled: this.daysDisabled || this.bsConfig && this.bsConfig.daysDisabled,\n        dateCustomClasses: this.dateCustomClasses || this.bsConfig && this.bsConfig.dateCustomClasses,\n        dateTooltipTexts: this.dateTooltipTexts || this.bsConfig && this.bsConfig.dateTooltipTexts,\n        datesDisabled: this.datesDisabled || this.bsConfig && this.bsConfig.datesDisabled,\n        datesEnabled: this.datesEnabled || this.bsConfig && this.bsConfig.datesEnabled,\n        minMode: this.minMode || this.bsConfig && this.bsConfig.minMode,\n        initCurrentTime: this.bsConfig?.initCurrentTime,\n        keepDatepickerOpened: this.bsConfig?.keepDatepickerOpened,\n        keepDatesOutOfRules: this.bsConfig?.keepDatesOutOfRules\n      });\n    }\n    unsubscribeSubscriptions() {\n      if (this._subs?.length) {\n        this._subs.map(sub => sub.unsubscribe());\n        this._subs.length = 0;\n      }\n    }\n    ngOnDestroy() {\n      this._datepicker.dispose();\n      this.isOpen$.next(false);\n      if (this.isDestroy$) {\n        this.isDestroy$.next(null);\n        this.isDestroy$.complete();\n      }\n      this.unsubscribeSubscriptions();\n    }\n    static {\n      this.ɵfac = function BsDatepickerDirective_Factory(t) {\n        return new (t || BsDatepickerDirective)(i0.ɵɵdirectiveInject(BsDatepickerConfig), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(i2.ComponentLoaderFactory));\n      };\n    }\n    static {\n      this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n        type: BsDatepickerDirective,\n        selectors: [[\"\", \"bsDatepicker\", \"\"]],\n        hostVars: 1,\n        hostBindings: function BsDatepickerDirective_HostBindings(rf, ctx) {\n          if (rf & 2) {\n            i0.ɵɵattribute(\"readonly\", ctx.readonlyValue);\n          }\n        },\n        inputs: {\n          placement: \"placement\",\n          triggers: \"triggers\",\n          outsideClick: \"outsideClick\",\n          container: \"container\",\n          outsideEsc: \"outsideEsc\",\n          isDisabled: \"isDisabled\",\n          minDate: \"minDate\",\n          maxDate: \"maxDate\",\n          minMode: \"minMode\",\n          daysDisabled: \"daysDisabled\",\n          datesDisabled: \"datesDisabled\",\n          datesEnabled: \"datesEnabled\",\n          dateCustomClasses: \"dateCustomClasses\",\n          dateTooltipTexts: \"dateTooltipTexts\",\n          isOpen: \"isOpen\",\n          bsValue: \"bsValue\",\n          bsConfig: \"bsConfig\"\n        },\n        outputs: {\n          onShown: \"onShown\",\n          onHidden: \"onHidden\",\n          bsValueChange: \"bsValueChange\"\n        },\n        exportAs: [\"bsDatepicker\"],\n        features: [i0.ɵɵNgOnChangesFeature]\n      });\n    }\n  }\n  return BsDatepickerDirective;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet BsDatepickerInlineConfig = /*#__PURE__*/(() => {\n  class BsDatepickerInlineConfig extends BsDatepickerConfig {\n    static {\n      this.ɵfac = /* @__PURE__ */(() => {\n        let ɵBsDatepickerInlineConfig_BaseFactory;\n        return function BsDatepickerInlineConfig_Factory(t) {\n          return (ɵBsDatepickerInlineConfig_BaseFactory || (ɵBsDatepickerInlineConfig_BaseFactory = i0.ɵɵgetInheritedFactory(BsDatepickerInlineConfig)))(t || BsDatepickerInlineConfig);\n        };\n      })();\n    }\n    static {\n      this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n        token: BsDatepickerInlineConfig,\n        factory: BsDatepickerInlineConfig.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return BsDatepickerInlineConfig;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet BsDatepickerInlineContainerComponent = /*#__PURE__*/(() => {\n  class BsDatepickerInlineContainerComponent extends BsDatepickerContainerComponent {\n    get disabledValue() {\n      return this.isDatePickerDisabled ? '' : null;\n    }\n    get readonlyValue() {\n      return this.isDatePickerDisabled ? '' : null;\n    }\n    constructor(_renderer, _config, _store, _element, _actions, _effects, _positioningService) {\n      super(_renderer, _config, _store, _element, _actions, _effects, _positioningService);\n      _renderer.setStyle(_element.nativeElement, 'display', 'inline-block');\n      _renderer.setStyle(_element.nativeElement, 'position', 'static');\n    }\n    static {\n      this.ɵfac = function BsDatepickerInlineContainerComponent_Factory(t) {\n        return new (t || BsDatepickerInlineContainerComponent)(i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(BsDatepickerConfig), i0.ɵɵdirectiveInject(BsDatepickerStore), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(BsDatepickerActions), i0.ɵɵdirectiveInject(BsDatepickerEffects), i0.ɵɵdirectiveInject(i5.PositioningService));\n      };\n    }\n    static {\n      this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n        type: BsDatepickerInlineContainerComponent,\n        selectors: [[\"bs-datepicker-inline-container\"]],\n        hostVars: 2,\n        hostBindings: function BsDatepickerInlineContainerComponent_HostBindings(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵlistener(\"click\", function BsDatepickerInlineContainerComponent_click_HostBindingHandler($event) {\n              return ctx._stopPropagation($event);\n            });\n          }\n          if (rf & 2) {\n            i0.ɵɵattribute(\"disabled\", ctx.disabledValue)(\"readonly\", ctx.readonlyValue);\n          }\n        },\n        features: [i0.ɵɵProvidersFeature([BsDatepickerStore, BsDatepickerEffects]), i0.ɵɵInheritDefinitionFeature],\n        decls: 2,\n        vars: 3,\n        consts: [[\"startTP\", \"\"], [\"endTP\", \"\"], [\"class\", \"bs-datepicker\", 3, \"ngClass\", 4, \"ngIf\"], [1, \"bs-datepicker\", 3, \"ngClass\"], [1, \"bs-datepicker-container\"], [\"role\", \"application\", 1, \"bs-calendar-container\", 3, \"ngSwitch\"], [4, \"ngSwitchCase\"], [\"class\", \"bs-media-container\", 4, \"ngSwitchCase\"], [\"class\", \"bs-datepicker-buttons\", 4, \"ngIf\"], [\"class\", \"bs-datepicker-custom-range\", 4, \"ngIf\"], [1, \"bs-media-container\"], [3, \"bs-datepicker-multiple\", \"calendar\", \"isDisabled\", \"options\", \"onNavigate\", \"onViewMode\", \"onHover\", \"onHoverWeek\", \"onSelect\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"bs-timepicker-in-datepicker-container\", 4, \"ngIf\"], [3, \"onNavigate\", \"onViewMode\", \"onHover\", \"onHoverWeek\", \"onSelect\", \"calendar\", \"isDisabled\", \"options\"], [1, \"bs-timepicker-in-datepicker-container\"], [3, \"disabled\"], [3, \"disabled\", 4, \"ngIf\"], [3, \"bs-datepicker-multiple\", \"calendar\", \"onNavigate\", \"onViewMode\", \"onHover\", \"onSelect\", 4, \"ngFor\", \"ngForOf\"], [3, \"onNavigate\", \"onViewMode\", \"onHover\", \"onSelect\", \"calendar\"], [1, \"bs-datepicker-buttons\"], [\"type\", \"button\", 1, \"btn\", \"btn-success\"], [\"type\", \"button\", 1, \"btn\", \"btn-default\"], [\"class\", \"btn-today-wrapper\", 3, \"today-left\", \"today-right\", \"today-center\", 4, \"ngIf\"], [\"class\", \"btn-clear-wrapper\", 3, \"clear-left\", \"clear-right\", \"clear-center\", 4, \"ngIf\"], [1, \"btn-today-wrapper\"], [1, \"btn\", \"btn-success\", 3, \"click\"], [1, \"btn-clear-wrapper\"], [1, \"bs-datepicker-custom-range\"], [3, \"onSelect\", \"selectedRange\", \"ranges\", \"customRangeLabel\"]],\n        template: function BsDatepickerInlineContainerComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵtemplate(0, BsDatepickerInlineContainerComponent_div_0_Template, 10, 11, \"div\", 2);\n            i0.ɵɵpipe(1, \"async\");\n          }\n          if (rf & 2) {\n            i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind1(1, 1, ctx.viewMode));\n          }\n        },\n        dependencies: [i6.NgClass, i6.NgForOf, i6.NgIf, i6.NgSwitch, i6.NgSwitchCase, i7.TimepickerComponent, BsCustomDatesViewComponent, BsDaysCalendarViewComponent, BsMonthCalendarViewComponent, BsYearsCalendarViewComponent, i6.AsyncPipe],\n        encapsulation: 2,\n        data: {\n          animation: [datepickerAnimation]\n        }\n      });\n    }\n  }\n  return BsDatepickerInlineContainerComponent;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet BsDatepickerInlineDirective = /*#__PURE__*/(() => {\n  class BsDatepickerInlineDirective {\n    constructor(_config, _elementRef, _renderer, _viewContainerRef, cis) {\n      this._config = _config;\n      this._elementRef = _elementRef;\n      /**\n       * Indicates whether datepicker is enabled or not\n       */\n      this.isDisabled = false;\n      /**\n       * Emits when datepicker value has been changed\n       */\n      this.bsValueChange = new EventEmitter();\n      this._subs = [];\n      // todo: assign only subset of fields\n      Object.assign(this, this._config);\n      this._datepicker = cis.createLoader(_elementRef, _viewContainerRef, _renderer);\n    }\n    /**\n     * Initial value of datepicker\n     */\n    set bsValue(value) {\n      if (this._bsValue === value) {\n        return;\n      }\n      if (!this._bsValue && value && !this._config.withTimepicker) {\n        const now = new Date();\n        copyTime(value, now);\n      }\n      if (value && this.bsConfig?.initCurrentTime) {\n        value = setCurrentTimeOnDateSelect(value);\n      }\n      this._bsValue = value;\n      this.bsValueChange.emit(value);\n    }\n    ngOnInit() {\n      this.setConfig();\n      this.initSubscribes();\n    }\n    initSubscribes() {\n      this.unsubscribeSubscriptions();\n      this._subs.push(this.bsValueChange.subscribe(value => {\n        if (this._datepickerRef) {\n          this._datepickerRef.instance.value = value;\n        }\n      }));\n      if (this._datepickerRef) {\n        this._subs.push(this._datepickerRef.instance.valueChange.subscribe(value => {\n          this.bsValue = value;\n        }));\n      }\n    }\n    unsubscribeSubscriptions() {\n      if (this._subs?.length) {\n        this._subs.map(sub => sub.unsubscribe());\n        this._subs.length = 0;\n      }\n    }\n    ngOnChanges(changes) {\n      if (changes[\"bsConfig\"]) {\n        if (changes[\"bsConfig\"].currentValue?.initCurrentTime && changes[\"bsConfig\"].currentValue?.initCurrentTime !== changes[\"bsConfig\"].previousValue?.initCurrentTime && this._bsValue) {\n          this._bsValue = setCurrentTimeOnDateSelect(this._bsValue);\n          this.bsValueChange.emit(this._bsValue);\n        }\n      }\n      if (!this._datepickerRef || !this._datepickerRef.instance) {\n        return;\n      }\n      if (changes[\"minDate\"]) {\n        this._datepickerRef.instance.minDate = this.minDate;\n      }\n      if (changes[\"maxDate\"]) {\n        this._datepickerRef.instance.maxDate = this.maxDate;\n      }\n      if (changes[\"datesDisabled\"]) {\n        this._datepickerRef.instance.datesDisabled = this.datesDisabled;\n      }\n      if (changes[\"datesEnabled\"]) {\n        this._datepickerRef.instance.datesEnabled = this.datesEnabled;\n        this._datepickerRef.instance.value = this._bsValue;\n      }\n      if (changes[\"isDisabled\"]) {\n        this._datepickerRef.instance.isDisabled = this.isDisabled;\n      }\n      if (changes[\"dateCustomClasses\"]) {\n        this._datepickerRef.instance.dateCustomClasses = this.dateCustomClasses;\n      }\n      if (changes[\"dateTooltipTexts\"]) {\n        this._datepickerRef.instance.dateTooltipTexts = this.dateTooltipTexts;\n      }\n      this.setConfig();\n    }\n    /**\n     * Set config for datepicker\n     */\n    setConfig() {\n      if (this._datepicker) {\n        this._datepicker.hide();\n      }\n      this._config = Object.assign({}, this._config, this.bsConfig, {\n        value: checkBsValue(this._bsValue, this.maxDate || this.bsConfig && this.bsConfig.maxDate),\n        isDisabled: this.isDisabled,\n        minDate: this.minDate || this.bsConfig && this.bsConfig.minDate,\n        maxDate: this.maxDate || this.bsConfig && this.bsConfig.maxDate,\n        dateCustomClasses: this.dateCustomClasses || this.bsConfig && this.bsConfig.dateCustomClasses,\n        dateTooltipTexts: this.dateTooltipTexts || this.bsConfig && this.bsConfig.dateTooltipTexts,\n        datesDisabled: this.datesDisabled || this.bsConfig && this.bsConfig.datesDisabled,\n        datesEnabled: this.datesEnabled || this.bsConfig && this.bsConfig.datesEnabled,\n        initCurrentTime: this.bsConfig?.initCurrentTime\n      });\n      this._datepickerRef = this._datepicker.provide({\n        provide: BsDatepickerConfig,\n        useValue: this._config\n      }).attach(BsDatepickerInlineContainerComponent).to(this._elementRef).show();\n      this.initSubscribes();\n    }\n    ngOnDestroy() {\n      this._datepicker.dispose();\n      this.unsubscribeSubscriptions();\n    }\n    static {\n      this.ɵfac = function BsDatepickerInlineDirective_Factory(t) {\n        return new (t || BsDatepickerInlineDirective)(i0.ɵɵdirectiveInject(BsDatepickerInlineConfig), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(i2.ComponentLoaderFactory));\n      };\n    }\n    static {\n      this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n        type: BsDatepickerInlineDirective,\n        selectors: [[\"bs-datepicker-inline\"]],\n        inputs: {\n          bsConfig: \"bsConfig\",\n          isDisabled: \"isDisabled\",\n          minDate: \"minDate\",\n          maxDate: \"maxDate\",\n          dateCustomClasses: \"dateCustomClasses\",\n          dateTooltipTexts: \"dateTooltipTexts\",\n          datesEnabled: \"datesEnabled\",\n          datesDisabled: \"datesDisabled\",\n          bsValue: \"bsValue\"\n        },\n        outputs: {\n          bsValueChange: \"bsValueChange\"\n        },\n        exportAs: [\"bsDatepickerInline\"],\n        features: [i0.ɵɵNgOnChangesFeature]\n      });\n    }\n  }\n  return BsDatepickerInlineDirective;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet BsDaterangepickerInlineConfig = /*#__PURE__*/(() => {\n  class BsDaterangepickerInlineConfig extends BsDatepickerConfig {\n    constructor() {\n      super(...arguments);\n      // DatepickerRenderOptions\n      this.displayMonths = 2;\n      /** turn on/off animation */\n      this.isAnimated = false;\n    }\n    static {\n      this.ɵfac = /* @__PURE__ */(() => {\n        let ɵBsDaterangepickerInlineConfig_BaseFactory;\n        return function BsDaterangepickerInlineConfig_Factory(t) {\n          return (ɵBsDaterangepickerInlineConfig_BaseFactory || (ɵBsDaterangepickerInlineConfig_BaseFactory = i0.ɵɵgetInheritedFactory(BsDaterangepickerInlineConfig)))(t || BsDaterangepickerInlineConfig);\n        };\n      })();\n    }\n    static {\n      this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n        token: BsDaterangepickerInlineConfig,\n        factory: BsDaterangepickerInlineConfig.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return BsDaterangepickerInlineConfig;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet BsDaterangepickerContainerComponent = /*#__PURE__*/(() => {\n  class BsDaterangepickerContainerComponent extends BsDatepickerAbstractComponent {\n    set value(value) {\n      this._effects?.setRangeValue(value);\n    }\n    get isDatePickerDisabled() {\n      return !!this._config.isDisabled;\n    }\n    get isDatepickerDisabled() {\n      return this.isDatePickerDisabled ? '' : null;\n    }\n    get isDatepickerReadonly() {\n      return this.isDatePickerDisabled ? '' : null;\n    }\n    constructor(_renderer, _config, _store, _element, _actions, _effects, _positionService) {\n      super();\n      this._config = _config;\n      this._store = _store;\n      this._element = _element;\n      this._actions = _actions;\n      this._positionService = _positionService;\n      this.valueChange = new EventEmitter();\n      this.animationState = 'void';\n      this._rangeStack = [];\n      this.chosenRange = [];\n      this._subs = [];\n      this.isRangePicker = true;\n      this._effects = _effects;\n      this.customRanges = this._config.ranges || [];\n      this.customRangeBtnLbl = this._config.customRangeButtonLabel;\n      _renderer.setStyle(_element.nativeElement, 'display', 'block');\n      _renderer.setStyle(_element.nativeElement, 'position', 'absolute');\n    }\n    ngOnInit() {\n      this._positionService.setOptions({\n        modifiers: {\n          flip: {\n            enabled: this._config.adaptivePosition\n          },\n          preventOverflow: {\n            enabled: this._config.adaptivePosition\n          }\n        },\n        allowedPositions: this._config.allowedPositions\n      });\n      this._positionService.event$?.pipe(take(1)).subscribe(() => {\n        this._positionService.disable();\n        if (this._config.isAnimated) {\n          this.animationState = this.isTopPosition ? 'animated-up' : 'animated-down';\n          return;\n        }\n        this.animationState = 'unanimated';\n      });\n      this.containerClass = this._config.containerClass;\n      this.isOtherMonthsActive = this._config.selectFromOtherMonth;\n      this.withTimepicker = this._config.withTimepicker;\n      this._effects?.init(this._store)\n      // intial state options\n      // todo: fix this, split configs\n      .setOptions(this._config)\n      // data binding view --> model\n      .setBindings(this)\n      // set event handlers\n      .setEventHandlers(this).registerDatepickerSideEffects();\n      let currentDate;\n      // todo: move it somewhere else\n      // on selected date change\n      this._subs.push(this._store.select(state => state.selectedRange).subscribe(dateRange => {\n        currentDate = dateRange;\n        this.valueChange.emit(dateRange);\n        this.chosenRange = dateRange || [];\n      }));\n      this._subs.push(this._store.select(state => state.selectedTime).subscribe(time => {\n        if (!time || !time[0] || !time[1] || !(time[0] instanceof Date) || !(time[1] instanceof Date) || currentDate && time[0] === currentDate[0] && time[1] === currentDate[1]) {\n          return;\n        }\n        this.valueChange.emit(time);\n        this.chosenRange = time || [];\n      }));\n    }\n    ngAfterViewInit() {\n      this.selectedTimeSub.add(this.selectedTime?.subscribe(val => {\n        if (Array.isArray(val) && val.length >= 2) {\n          this.startTimepicker?.writeValue(val[0]);\n          this.endTimepicker?.writeValue(val[1]);\n        }\n      }));\n      this.startTimepicker?.registerOnChange(val => {\n        this.timeSelectHandler(val, 0);\n      });\n      this.endTimepicker?.registerOnChange(val => {\n        this.timeSelectHandler(val, 1);\n      });\n    }\n    get isTopPosition() {\n      return this._element.nativeElement.classList.contains('top');\n    }\n    positionServiceEnable() {\n      this._positionService.enable();\n    }\n    timeSelectHandler(date, index) {\n      this._store.dispatch(this._actions.selectTime(date, index));\n    }\n    daySelectHandler(day) {\n      if (!day) {\n        return;\n      }\n      const isDisabled = this.isOtherMonthsActive ? day.isDisabled : day.isOtherMonth || day.isDisabled;\n      if (isDisabled) {\n        return;\n      }\n      this.rangesProcessing(day);\n    }\n    monthSelectHandler(day) {\n      if (!day || day.isDisabled) {\n        return;\n      }\n      day.isSelected = true;\n      if (this._config.minMode !== 'month') {\n        if (day.isDisabled) {\n          return;\n        }\n        this._store.dispatch(this._actions.navigateTo({\n          unit: {\n            month: getMonth(day.date),\n            year: getFullYear(day.date)\n          },\n          viewMode: 'day'\n        }));\n        return;\n      }\n      this.rangesProcessing(day);\n    }\n    yearSelectHandler(day) {\n      if (!day || day.isDisabled) {\n        return;\n      }\n      day.isSelected = true;\n      if (this._config.minMode !== 'year') {\n        if (day.isDisabled) {\n          return;\n        }\n        this._store.dispatch(this._actions.navigateTo({\n          unit: {\n            year: getFullYear(day.date)\n          },\n          viewMode: 'month'\n        }));\n        return;\n      }\n      this.rangesProcessing(day);\n    }\n    rangesProcessing(day) {\n      // if only one date is already selected\n      // and user clicks on previous date\n      // start selection from new date\n      // but if new date is after initial one\n      // than finish selection\n      if (this._rangeStack.length === 1) {\n        this._rangeStack = day.date >= this._rangeStack[0] ? [this._rangeStack[0], day.date] : [day.date];\n      }\n      if (this._config.maxDateRange) {\n        this.setMaxDateRangeOnCalendar(day.date);\n      }\n      if (this._rangeStack.length === 0) {\n        this._rangeStack = [day.date];\n        if (this._config.maxDateRange) {\n          this.setMaxDateRangeOnCalendar(day.date);\n        }\n      }\n      this._store.dispatch(this._actions.selectRange(this._rangeStack));\n      if (this._rangeStack.length === 2) {\n        this._rangeStack = [];\n      }\n    }\n    ngOnDestroy() {\n      for (const sub of this._subs) {\n        sub.unsubscribe();\n      }\n      this.selectedTimeSub.unsubscribe();\n      this._effects?.destroy();\n    }\n    setRangeOnCalendar(dates) {\n      if (dates) {\n        this._rangeStack = dates.value instanceof Date ? [dates.value] : dates.value;\n      }\n      this._store.dispatch(this._actions.selectRange(this._rangeStack));\n    }\n    setMaxDateRangeOnCalendar(currentSelection) {\n      let maxDateRange = new Date(currentSelection);\n      if (this._config.maxDate) {\n        const maxDateValueInMilliseconds = this._config.maxDate.getTime();\n        const maxDateRangeInMilliseconds = currentSelection.getTime() + (this._config.maxDateRange || 0) * dayInMilliseconds;\n        maxDateRange = maxDateRangeInMilliseconds > maxDateValueInMilliseconds ? new Date(this._config.maxDate) : new Date(maxDateRangeInMilliseconds);\n      } else {\n        maxDateRange.setDate(currentSelection.getDate() + (this._config.maxDateRange || 0));\n      }\n      this._effects?.setMaxDate(maxDateRange);\n    }\n    static {\n      this.ɵfac = function BsDaterangepickerContainerComponent_Factory(t) {\n        return new (t || BsDaterangepickerContainerComponent)(i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(BsDatepickerConfig), i0.ɵɵdirectiveInject(BsDatepickerStore), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(BsDatepickerActions), i0.ɵɵdirectiveInject(BsDatepickerEffects), i0.ɵɵdirectiveInject(i5.PositioningService));\n      };\n    }\n    static {\n      this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n        type: BsDaterangepickerContainerComponent,\n        selectors: [[\"bs-daterangepicker-container\"]],\n        viewQuery: function BsDaterangepickerContainerComponent_Query(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵviewQuery(_c3, 5);\n            i0.ɵɵviewQuery(_c4, 5);\n          }\n          if (rf & 2) {\n            let _t;\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.startTimepicker = _t.first);\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.endTimepicker = _t.first);\n          }\n        },\n        hostAttrs: [\"role\", \"dialog\", \"aria-label\", \"calendar\", 1, \"bottom\"],\n        hostVars: 2,\n        hostBindings: function BsDaterangepickerContainerComponent_HostBindings(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵlistener(\"click\", function BsDaterangepickerContainerComponent_click_HostBindingHandler($event) {\n              return ctx._stopPropagation($event);\n            });\n          }\n          if (rf & 2) {\n            i0.ɵɵattribute(\"disabled\", ctx.isDatepickerDisabled)(\"readonly\", ctx.isDatepickerReadonly);\n          }\n        },\n        features: [i0.ɵɵProvidersFeature([BsDatepickerStore, BsDatepickerEffects]), i0.ɵɵInheritDefinitionFeature],\n        decls: 2,\n        vars: 3,\n        consts: [[\"startTP\", \"\"], [\"endTP\", \"\"], [\"class\", \"bs-datepicker\", 3, \"ngClass\", 4, \"ngIf\"], [1, \"bs-datepicker\", 3, \"ngClass\"], [1, \"bs-datepicker-container\"], [\"role\", \"application\", 1, \"bs-calendar-container\", 3, \"ngSwitch\"], [4, \"ngSwitchCase\"], [\"class\", \"bs-media-container\", 4, \"ngSwitchCase\"], [\"class\", \"bs-datepicker-buttons\", 4, \"ngIf\"], [\"class\", \"bs-datepicker-custom-range\", 4, \"ngIf\"], [1, \"bs-media-container\"], [3, \"bs-datepicker-multiple\", \"calendar\", \"isDisabled\", \"options\", \"onNavigate\", \"onViewMode\", \"onHover\", \"onHoverWeek\", \"onSelect\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"bs-timepicker-in-datepicker-container\", 4, \"ngIf\"], [3, \"onNavigate\", \"onViewMode\", \"onHover\", \"onHoverWeek\", \"onSelect\", \"calendar\", \"isDisabled\", \"options\"], [1, \"bs-timepicker-in-datepicker-container\"], [3, \"disabled\"], [3, \"disabled\", 4, \"ngIf\"], [3, \"bs-datepicker-multiple\", \"calendar\", \"onNavigate\", \"onViewMode\", \"onHover\", \"onSelect\", 4, \"ngFor\", \"ngForOf\"], [3, \"onNavigate\", \"onViewMode\", \"onHover\", \"onSelect\", \"calendar\"], [1, \"bs-datepicker-buttons\"], [\"type\", \"button\", 1, \"btn\", \"btn-success\"], [\"type\", \"button\", 1, \"btn\", \"btn-default\"], [\"class\", \"btn-today-wrapper\", 3, \"today-left\", \"today-right\", \"today-center\", 4, \"ngIf\"], [\"class\", \"btn-clear-wrapper\", 3, \"clear-left\", \"clear-right\", \"clear-center\", 4, \"ngIf\"], [1, \"btn-today-wrapper\"], [1, \"btn\", \"btn-success\", 3, \"click\"], [1, \"btn-clear-wrapper\"], [1, \"bs-datepicker-custom-range\"], [3, \"onSelect\", \"selectedRange\", \"ranges\", \"customRangeLabel\"]],\n        template: function BsDaterangepickerContainerComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵtemplate(0, BsDaterangepickerContainerComponent_div_0_Template, 10, 11, \"div\", 2);\n            i0.ɵɵpipe(1, \"async\");\n          }\n          if (rf & 2) {\n            i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind1(1, 1, ctx.viewMode));\n          }\n        },\n        dependencies: [i6.NgClass, i6.NgForOf, i6.NgIf, i6.NgSwitch, i6.NgSwitchCase, i7.TimepickerComponent, BsCustomDatesViewComponent, BsDaysCalendarViewComponent, BsMonthCalendarViewComponent, BsYearsCalendarViewComponent, i6.AsyncPipe],\n        encapsulation: 2,\n        data: {\n          animation: [datepickerAnimation]\n        }\n      });\n    }\n  }\n  return BsDaterangepickerContainerComponent;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet BsDaterangepickerInlineContainerComponent = /*#__PURE__*/(() => {\n  class BsDaterangepickerInlineContainerComponent extends BsDaterangepickerContainerComponent {\n    get disabledValue() {\n      return this.isDatePickerDisabled ? '' : null;\n    }\n    get readonlyValue() {\n      return this.isDatePickerDisabled ? '' : null;\n    }\n    constructor(_renderer, _config, _store, _element, _actions, _effects, _positioningService) {\n      super(_renderer, _config, _store, _element, _actions, _effects, _positioningService);\n      _renderer.setStyle(_element.nativeElement, 'display', 'inline-block');\n      _renderer.setStyle(_element.nativeElement, 'position', 'static');\n    }\n    static {\n      this.ɵfac = function BsDaterangepickerInlineContainerComponent_Factory(t) {\n        return new (t || BsDaterangepickerInlineContainerComponent)(i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(BsDatepickerConfig), i0.ɵɵdirectiveInject(BsDatepickerStore), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(BsDatepickerActions), i0.ɵɵdirectiveInject(BsDatepickerEffects), i0.ɵɵdirectiveInject(i5.PositioningService));\n      };\n    }\n    static {\n      this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n        type: BsDaterangepickerInlineContainerComponent,\n        selectors: [[\"bs-daterangepicker-inline-container\"]],\n        hostVars: 2,\n        hostBindings: function BsDaterangepickerInlineContainerComponent_HostBindings(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵlistener(\"click\", function BsDaterangepickerInlineContainerComponent_click_HostBindingHandler($event) {\n              return ctx._stopPropagation($event);\n            });\n          }\n          if (rf & 2) {\n            i0.ɵɵattribute(\"disabled\", ctx.disabledValue)(\"readonly\", ctx.readonlyValue);\n          }\n        },\n        features: [i0.ɵɵProvidersFeature([BsDatepickerStore, BsDatepickerEffects]), i0.ɵɵInheritDefinitionFeature],\n        decls: 2,\n        vars: 3,\n        consts: [[\"startTP\", \"\"], [\"endTP\", \"\"], [\"class\", \"bs-datepicker\", 3, \"ngClass\", 4, \"ngIf\"], [1, \"bs-datepicker\", 3, \"ngClass\"], [1, \"bs-datepicker-container\"], [\"role\", \"application\", 1, \"bs-calendar-container\", 3, \"ngSwitch\"], [4, \"ngSwitchCase\"], [\"class\", \"bs-media-container\", 4, \"ngSwitchCase\"], [\"class\", \"bs-datepicker-buttons\", 4, \"ngIf\"], [\"class\", \"bs-datepicker-custom-range\", 4, \"ngIf\"], [1, \"bs-media-container\"], [3, \"bs-datepicker-multiple\", \"calendar\", \"isDisabled\", \"options\", \"onNavigate\", \"onViewMode\", \"onHover\", \"onHoverWeek\", \"onSelect\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"bs-timepicker-in-datepicker-container\", 4, \"ngIf\"], [3, \"onNavigate\", \"onViewMode\", \"onHover\", \"onHoverWeek\", \"onSelect\", \"calendar\", \"isDisabled\", \"options\"], [1, \"bs-timepicker-in-datepicker-container\"], [3, \"disabled\"], [3, \"disabled\", 4, \"ngIf\"], [3, \"bs-datepicker-multiple\", \"calendar\", \"onNavigate\", \"onViewMode\", \"onHover\", \"onSelect\", 4, \"ngFor\", \"ngForOf\"], [3, \"onNavigate\", \"onViewMode\", \"onHover\", \"onSelect\", \"calendar\"], [1, \"bs-datepicker-buttons\"], [\"type\", \"button\", 1, \"btn\", \"btn-success\"], [\"type\", \"button\", 1, \"btn\", \"btn-default\"], [\"class\", \"btn-today-wrapper\", 3, \"today-left\", \"today-right\", \"today-center\", 4, \"ngIf\"], [\"class\", \"btn-clear-wrapper\", 3, \"clear-left\", \"clear-right\", \"clear-center\", 4, \"ngIf\"], [1, \"btn-today-wrapper\"], [1, \"btn\", \"btn-success\", 3, \"click\"], [1, \"btn-clear-wrapper\"], [1, \"bs-datepicker-custom-range\"], [3, \"onSelect\", \"selectedRange\", \"ranges\", \"customRangeLabel\"]],\n        template: function BsDaterangepickerInlineContainerComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵtemplate(0, BsDaterangepickerInlineContainerComponent_div_0_Template, 10, 11, \"div\", 2);\n            i0.ɵɵpipe(1, \"async\");\n          }\n          if (rf & 2) {\n            i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind1(1, 1, ctx.viewMode));\n          }\n        },\n        dependencies: [i6.NgClass, i6.NgForOf, i6.NgIf, i6.NgSwitch, i6.NgSwitchCase, i7.TimepickerComponent, BsCustomDatesViewComponent, BsDaysCalendarViewComponent, BsMonthCalendarViewComponent, BsYearsCalendarViewComponent, i6.AsyncPipe],\n        encapsulation: 2,\n        data: {\n          animation: [datepickerAnimation]\n        }\n      });\n    }\n  }\n  return BsDaterangepickerInlineContainerComponent;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet BsDaterangepickerInlineDirective = /*#__PURE__*/(() => {\n  class BsDaterangepickerInlineDirective {\n    /**\n     * Initial value of datepicker\n     */\n    set bsValue(value) {\n      if (this._bsValue === value) {\n        return;\n      }\n      if (value && this.bsConfig?.initCurrentTime) {\n        value = setDateRangesCurrentTimeOnDateSelect(value);\n      }\n      this._bsValue = value;\n      this.bsValueChange.emit(value);\n    }\n    constructor(_config, _elementRef, _renderer, _viewContainerRef, cis) {\n      this._config = _config;\n      this._elementRef = _elementRef;\n      /**\n       * Indicates whether datepicker is enabled or not\n       */\n      this.isDisabled = false;\n      /**\n       * Emits when daterangepicker value has been changed\n       */\n      this.bsValueChange = new EventEmitter();\n      this._subs = [];\n      // todo: assign only subset of fields\n      Object.assign(this, this._config);\n      this._datepicker = cis.createLoader(_elementRef, _viewContainerRef, _renderer);\n    }\n    ngOnInit() {\n      this.setConfig();\n      this.initSubscribes();\n    }\n    ngOnChanges(changes) {\n      if (changes[\"bsConfig\"]) {\n        if (changes[\"bsConfig\"].currentValue.initCurrentTime && changes[\"bsConfig\"].currentValue.initCurrentTime !== changes[\"bsConfig\"].previousValue.initCurrentTime && this._bsValue) {\n          this._bsValue = setDateRangesCurrentTimeOnDateSelect(this._bsValue);\n          this.bsValueChange.emit(this._bsValue);\n        }\n      }\n      if (!this._datepickerRef || !this._datepickerRef.instance) {\n        return;\n      }\n      if (changes[\"minDate\"]) {\n        this._datepickerRef.instance.minDate = this.minDate;\n      }\n      if (changes[\"maxDate\"]) {\n        this._datepickerRef.instance.maxDate = this.maxDate;\n      }\n      if (changes[\"datesEnabled\"]) {\n        this._datepickerRef.instance.datesEnabled = this.datesEnabled;\n        this._datepickerRef.instance.value = this._bsValue;\n      }\n      if (changes[\"datesDisabled\"]) {\n        this._datepickerRef.instance.datesDisabled = this.datesDisabled;\n      }\n      if (changes[\"daysDisabled\"]) {\n        this._datepickerRef.instance.daysDisabled = this.daysDisabled;\n      }\n      if (changes[\"isDisabled\"]) {\n        this._datepickerRef.instance.isDisabled = this.isDisabled;\n      }\n      if (changes[\"dateCustomClasses\"]) {\n        this._datepickerRef.instance.dateCustomClasses = this.dateCustomClasses;\n      }\n      this.setConfig();\n    }\n    /**\n     * Set config for datepicker\n     */\n    setConfig() {\n      if (this._datepicker) {\n        this._datepicker.hide();\n      }\n      this._config = Object.assign({}, this._config, this.bsConfig, {\n        value: checkBsValue(this._bsValue, this.maxDate || this.bsConfig && this.bsConfig.maxDate),\n        isDisabled: this.isDisabled,\n        minDate: this.minDate || this.bsConfig && this.bsConfig.minDate,\n        maxDate: this.maxDate || this.bsConfig && this.bsConfig.maxDate,\n        daysDisabled: this.daysDisabled || this.bsConfig && this.bsConfig.daysDisabled,\n        dateCustomClasses: this.dateCustomClasses || this.bsConfig && this.bsConfig.dateCustomClasses,\n        datesDisabled: this.datesDisabled || this.bsConfig && this.bsConfig.datesDisabled,\n        datesEnabled: this.datesEnabled || this.bsConfig && this.bsConfig.datesEnabled,\n        ranges: checkRangesWithMaxDate(this.bsConfig && this.bsConfig.ranges, this.maxDate || this.bsConfig && this.bsConfig.maxDate),\n        maxDateRange: this.bsConfig && this.bsConfig.maxDateRange,\n        initCurrentTime: this.bsConfig?.initCurrentTime\n      });\n      this._datepickerRef = this._datepicker.provide({\n        provide: BsDatepickerConfig,\n        useValue: this._config\n      }).attach(BsDaterangepickerInlineContainerComponent).to(this._elementRef).show();\n      this.initSubscribes();\n    }\n    initSubscribes() {\n      this.unsubscribeSubscriptions();\n      // if date changes from external source (model -> view)\n      this._subs.push(this.bsValueChange.subscribe(value => {\n        if (this._datepickerRef) {\n          this._datepickerRef.instance.value = value;\n        }\n      }));\n      // if date changes from picker (view -> model)\n      if (this._datepickerRef) {\n        this._subs.push(this._datepickerRef.instance.valueChange.pipe(filter(range => range && range[0] && !!range[1])).subscribe(value => {\n          this.bsValue = value;\n        }));\n      }\n    }\n    unsubscribeSubscriptions() {\n      if (this._subs?.length) {\n        this._subs.map(sub => sub.unsubscribe());\n        this._subs.length = 0;\n      }\n    }\n    ngOnDestroy() {\n      this._datepicker.dispose();\n      this.unsubscribeSubscriptions();\n    }\n    static {\n      this.ɵfac = function BsDaterangepickerInlineDirective_Factory(t) {\n        return new (t || BsDaterangepickerInlineDirective)(i0.ɵɵdirectiveInject(BsDaterangepickerInlineConfig), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(i2.ComponentLoaderFactory));\n      };\n    }\n    static {\n      this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n        type: BsDaterangepickerInlineDirective,\n        selectors: [[\"bs-daterangepicker-inline\"]],\n        inputs: {\n          bsValue: \"bsValue\",\n          bsConfig: \"bsConfig\",\n          isDisabled: \"isDisabled\",\n          minDate: \"minDate\",\n          maxDate: \"maxDate\",\n          dateCustomClasses: \"dateCustomClasses\",\n          daysDisabled: \"daysDisabled\",\n          datesDisabled: \"datesDisabled\",\n          datesEnabled: \"datesEnabled\"\n        },\n        outputs: {\n          bsValueChange: \"bsValueChange\"\n        },\n        exportAs: [\"bsDaterangepickerInline\"],\n        features: [i0.ɵɵNgOnChangesFeature]\n      });\n    }\n  }\n  return BsDaterangepickerInlineDirective;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nconst BS_DATEPICKER_VALUE_ACCESSOR = {\n  provide: NG_VALUE_ACCESSOR,\n  useExisting: forwardRef(() => BsDatepickerInputDirective),\n  multi: true\n};\nconst BS_DATEPICKER_VALIDATOR = {\n  provide: NG_VALIDATORS,\n  useExisting: forwardRef(() => BsDatepickerInputDirective),\n  multi: true\n};\nlet BsDatepickerInputDirective = /*#__PURE__*/(() => {\n  class BsDatepickerInputDirective {\n    constructor(_picker, _localeService, _renderer, _elRef, changeDetection) {\n      this._picker = _picker;\n      this._localeService = _localeService;\n      this._renderer = _renderer;\n      this._elRef = _elRef;\n      this.changeDetection = changeDetection;\n      this._onChange = Function.prototype;\n      this._onTouched = Function.prototype;\n      this._validatorChange = Function.prototype;\n      this._subs = new Subscription();\n    }\n    onChange(event) {\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      this.writeValue(event.target.value);\n      this._onChange(this._value);\n      if (this._picker._config.returnFocusToInput) {\n        this._renderer.selectRootElement(this._elRef.nativeElement).focus();\n      }\n      this._onTouched();\n    }\n    onBlur() {\n      this._onTouched();\n    }\n    hide() {\n      this._picker.hide();\n      this._renderer.selectRootElement(this._elRef.nativeElement).blur();\n      if (this._picker._config.returnFocusToInput) {\n        this._renderer.selectRootElement(this._elRef.nativeElement).focus();\n      }\n    }\n    ngOnInit() {\n      const setBsValue = value => {\n        this._setInputValue(value);\n        if (this._value !== value) {\n          this._value = value;\n          this._onChange(value);\n          this._onTouched();\n        }\n        this.changeDetection.markForCheck();\n      };\n      // if value set via [bsValue] it will not get into value change\n      if (this._picker._bsValue) {\n        setBsValue(this._picker._bsValue);\n      }\n      // update input value on datepicker value update\n      this._subs.add(this._picker.bsValueChange.subscribe(setBsValue));\n      // update input value on locale change\n      this._subs.add(this._localeService.localeChange.subscribe(() => {\n        this._setInputValue(this._value);\n      }));\n      this._subs.add(this._picker.dateInputFormat$.pipe(distinctUntilChanged()).subscribe(() => {\n        this._setInputValue(this._value);\n      }));\n    }\n    ngOnDestroy() {\n      this._subs.unsubscribe();\n    }\n    _setInputValue(value) {\n      const initialDate = !value ? '' : formatDate(value, this._picker._config.dateInputFormat, this._localeService.currentLocale);\n      this._renderer.setProperty(this._elRef.nativeElement, 'value', initialDate);\n    }\n    validate(c) {\n      const _value = c.value;\n      if (_value === null || _value === undefined || _value === '') {\n        return null;\n      }\n      if (isDate(_value)) {\n        const _isDateValid = isDateValid(_value);\n        if (!_isDateValid) {\n          return {\n            bsDate: {\n              invalid: _value\n            }\n          };\n        }\n        if (this._picker && this._picker.minDate && isBefore(_value, this._picker.minDate, 'date')) {\n          this.writeValue(this._picker.minDate);\n          return {\n            bsDate: {\n              minDate: this._picker.minDate\n            }\n          };\n        }\n        if (this._picker && this._picker.maxDate && isAfter(_value, this._picker.maxDate, 'date')) {\n          this.writeValue(this._picker.maxDate);\n          return {\n            bsDate: {\n              maxDate: this._picker.maxDate\n            }\n          };\n        }\n      }\n      return null;\n    }\n    registerOnValidatorChange(fn) {\n      this._validatorChange = fn;\n    }\n    writeValue(value) {\n      if (!value) {\n        this._value = void 0;\n      } else {\n        const _localeKey = this._localeService.currentLocale;\n        const _locale = getLocale(_localeKey);\n        if (!_locale) {\n          throw new Error(`Locale \"${_localeKey}\" is not defined, please add it with \"defineLocale(...)\"`);\n        }\n        this._value = parseDate(value, this._picker._config.dateInputFormat, this._localeService.currentLocale);\n        if (this._picker._config.useUtc) {\n          const utcValue = utcAsLocal(this._value);\n          this._value = utcValue === null ? void 0 : utcValue;\n        }\n      }\n      this._picker.bsValue = this._value;\n    }\n    setDisabledState(isDisabled) {\n      this._picker.isDisabled = isDisabled;\n      if (isDisabled) {\n        this._renderer.setAttribute(this._elRef.nativeElement, 'disabled', 'disabled');\n        return;\n      }\n      this._renderer.removeAttribute(this._elRef.nativeElement, 'disabled');\n    }\n    registerOnChange(fn) {\n      this._onChange = fn;\n    }\n    registerOnTouched(fn) {\n      this._onTouched = fn;\n    }\n    static {\n      this.ɵfac = function BsDatepickerInputDirective_Factory(t) {\n        return new (t || BsDatepickerInputDirective)(i0.ɵɵdirectiveInject(BsDatepickerDirective, 1), i0.ɵɵdirectiveInject(BsLocaleService), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n      };\n    }\n    static {\n      this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n        type: BsDatepickerInputDirective,\n        selectors: [[\"input\", \"bsDatepicker\", \"\"]],\n        hostBindings: function BsDatepickerInputDirective_HostBindings(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵlistener(\"change\", function BsDatepickerInputDirective_change_HostBindingHandler($event) {\n              return ctx.onChange($event);\n            })(\"blur\", function BsDatepickerInputDirective_blur_HostBindingHandler() {\n              return ctx.onBlur();\n            })(\"keyup.esc\", function BsDatepickerInputDirective_keyup_esc_HostBindingHandler() {\n              return ctx.hide();\n            })(\"keydown.enter\", function BsDatepickerInputDirective_keydown_enter_HostBindingHandler() {\n              return ctx.hide();\n            });\n          }\n        },\n        features: [i0.ɵɵProvidersFeature([BS_DATEPICKER_VALUE_ACCESSOR, BS_DATEPICKER_VALIDATOR])]\n      });\n    }\n  }\n  return BsDatepickerInputDirective;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet BsDaterangepickerConfig = /*#__PURE__*/(() => {\n  class BsDaterangepickerConfig extends BsDatepickerConfig {\n    constructor() {\n      super(...arguments);\n      // DatepickerRenderOptions\n      this.displayMonths = 2;\n    }\n    static {\n      this.ɵfac = /* @__PURE__ */(() => {\n        let ɵBsDaterangepickerConfig_BaseFactory;\n        return function BsDaterangepickerConfig_Factory(t) {\n          return (ɵBsDaterangepickerConfig_BaseFactory || (ɵBsDaterangepickerConfig_BaseFactory = i0.ɵɵgetInheritedFactory(BsDaterangepickerConfig)))(t || BsDaterangepickerConfig);\n        };\n      })();\n    }\n    static {\n      this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n        token: BsDaterangepickerConfig,\n        factory: BsDaterangepickerConfig.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return BsDaterangepickerConfig;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet previousDate;\nlet BsDaterangepickerDirective = /*#__PURE__*/(() => {\n  class BsDaterangepickerDirective {\n    /**\n     * Returns whether or not the daterangepicker is currently being shown\n     */\n    get isOpen() {\n      return this._datepicker.isShown;\n    }\n    set isOpen(value) {\n      this.isOpen$.next(value);\n    }\n    /**\n     * Initial value of daterangepicker\n     */\n    set bsValue(value) {\n      if (this._bsValue === value) {\n        return;\n      }\n      if (value && this.bsConfig?.initCurrentTime) {\n        value = setDateRangesCurrentTimeOnDateSelect(value);\n      }\n      this.initPreviousValue();\n      this._bsValue = value;\n      this.bsValueChange.emit(value);\n    }\n    get isDatepickerReadonly() {\n      return this.isDisabled ? '' : null;\n    }\n    get rangeInputFormat$() {\n      return this._rangeInputFormat$;\n    }\n    constructor(_config, _elementRef, _renderer, _viewContainerRef, cis) {\n      this._config = _config;\n      this._elementRef = _elementRef;\n      this._renderer = _renderer;\n      /**\n       * Placement of a daterangepicker. Accepts: \"top\", \"bottom\", \"left\", \"right\"\n       */\n      this.placement = 'bottom';\n      /**\n       * Specifies events that should trigger. Supports a space separated list of\n       * event names.\n       */\n      this.triggers = 'click';\n      /**\n       * Close daterangepicker on outside click\n       */\n      this.outsideClick = true;\n      /**\n       * A selector specifying the element the daterangepicker should be appended to.\n       */\n      this.container = 'body';\n      this.outsideEsc = true;\n      this.isDestroy$ = new Subject();\n      /**\n       * Indicates whether daterangepicker's content is enabled or not\n       */\n      this.isDisabled = false;\n      /**\n       * Emits when daterangepicker value has been changed\n       */\n      this.bsValueChange = new EventEmitter();\n      this._subs = [];\n      this._rangeInputFormat$ = new Subject();\n      this._datepicker = cis.createLoader(_elementRef, _viewContainerRef, _renderer);\n      Object.assign(this, _config);\n      this.onShown = this._datepicker.onShown;\n      this.onHidden = this._datepicker.onHidden;\n      this.isOpen$ = new BehaviorSubject(this.isOpen);\n    }\n    ngOnInit() {\n      this.isDestroy$ = new Subject();\n      this._datepicker.listen({\n        outsideClick: this.outsideClick,\n        outsideEsc: this.outsideEsc,\n        triggers: this.triggers,\n        show: () => this.show()\n      });\n      this.initPreviousValue();\n      this.setConfig();\n    }\n    ngOnChanges(changes) {\n      if (changes[\"bsConfig\"]) {\n        if (changes[\"bsConfig\"].currentValue?.initCurrentTime && changes[\"bsConfig\"].currentValue?.initCurrentTime !== changes[\"bsConfig\"].previousValue?.initCurrentTime && this._bsValue) {\n          this.initPreviousValue();\n          this._bsValue = setDateRangesCurrentTimeOnDateSelect(this._bsValue);\n          this.bsValueChange.emit(this._bsValue);\n        }\n        this.setConfig();\n        this._rangeInputFormat$.next(changes[\"bsConfig\"].currentValue && changes[\"bsConfig\"].currentValue.rangeInputFormat);\n      }\n      if (!this._datepickerRef || !this._datepickerRef.instance) {\n        return;\n      }\n      if (changes[\"minDate\"]) {\n        this._datepickerRef.instance.minDate = this.minDate;\n      }\n      if (changes[\"maxDate\"]) {\n        this._datepickerRef.instance.maxDate = this.maxDate;\n      }\n      if (changes[\"datesDisabled\"]) {\n        this._datepickerRef.instance.datesDisabled = this.datesDisabled;\n      }\n      if (changes[\"datesEnabled\"]) {\n        this._datepickerRef.instance.datesEnabled = this.datesEnabled;\n      }\n      if (changes[\"daysDisabled\"]) {\n        this._datepickerRef.instance.daysDisabled = this.daysDisabled;\n      }\n      if (changes[\"isDisabled\"]) {\n        this._datepickerRef.instance.isDisabled = this.isDisabled;\n      }\n      if (changes[\"dateCustomClasses\"]) {\n        this._datepickerRef.instance.dateCustomClasses = this.dateCustomClasses;\n      }\n    }\n    ngAfterViewInit() {\n      this.isOpen$.pipe(filter(isOpen => isOpen !== this.isOpen), takeUntil(this.isDestroy$)).subscribe(() => this.toggle());\n    }\n    /**\n     * Opens an element’s datepicker. This is considered a “manual” triggering of\n     * the datepicker.\n     */\n    show() {\n      if (this._datepicker.isShown) {\n        return;\n      }\n      this.setConfig();\n      this._datepickerRef = this._datepicker.provide({\n        provide: BsDatepickerConfig,\n        useValue: this._config\n      }).attach(BsDaterangepickerContainerComponent).to(this.container).position({\n        attachment: this.placement\n      }).show({\n        placement: this.placement\n      });\n      this.initSubscribes();\n    }\n    initSubscribes() {\n      // if date changes from external source (model -> view)\n      this._subs.push(this.bsValueChange.subscribe(value => {\n        if (this._datepickerRef) {\n          this._datepickerRef.instance.value = value;\n        }\n      }));\n      // if date changes from picker (view -> model)\n      if (this._datepickerRef) {\n        this._subs.push(this._datepickerRef.instance.valueChange.pipe(filter(range => range && range[0] && !!range[1])).subscribe(value => {\n          this.initPreviousValue();\n          this.bsValue = value;\n          if (this.keepDatepickerModalOpened()) {\n            return;\n          }\n          this.hide();\n        }));\n      }\n    }\n    initPreviousValue() {\n      previousDate = this._bsValue;\n    }\n    keepDatepickerModalOpened() {\n      if (!previousDate || !this.bsConfig?.keepDatepickerOpened || !this._config.withTimepicker) {\n        return false;\n      }\n      return this.isDateSame();\n    }\n    isDateSame() {\n      return this._bsValue?.[0]?.getDate() === previousDate?.[0]?.getDate() && this._bsValue?.[0]?.getMonth() === previousDate?.[0]?.getMonth() && this._bsValue?.[0]?.getFullYear() === previousDate?.[0]?.getFullYear() && this._bsValue?.[1]?.getDate() === previousDate?.[1]?.getDate() && this._bsValue?.[1]?.getMonth() === previousDate?.[1]?.getMonth() && this._bsValue?.[1]?.getFullYear() === previousDate?.[1]?.getFullYear();\n    }\n    /**\n     * Set config for daterangepicker\n     */\n    setConfig() {\n      this._config = Object.assign({}, this._config, this.bsConfig, {\n        value: this.bsConfig?.keepDatesOutOfRules ? this._bsValue : checkBsValue(this._bsValue, this.maxDate || this.bsConfig && this.bsConfig.maxDate),\n        isDisabled: this.isDisabled,\n        minDate: this.minDate || this.bsConfig && this.bsConfig.minDate,\n        maxDate: this.maxDate || this.bsConfig && this.bsConfig.maxDate,\n        daysDisabled: this.daysDisabled || this.bsConfig && this.bsConfig.daysDisabled,\n        dateCustomClasses: this.dateCustomClasses || this.bsConfig && this.bsConfig.dateCustomClasses,\n        datesDisabled: this.datesDisabled || this.bsConfig && this.bsConfig.datesDisabled,\n        datesEnabled: this.datesEnabled || this.bsConfig && this.bsConfig.datesEnabled,\n        ranges: checkRangesWithMaxDate(this.bsConfig && this.bsConfig.ranges, this.maxDate || this.bsConfig && this.bsConfig.maxDate),\n        maxDateRange: this.bsConfig && this.bsConfig.maxDateRange,\n        initCurrentTime: this.bsConfig?.initCurrentTime,\n        keepDatepickerOpened: this.bsConfig?.keepDatepickerOpened,\n        keepDatesOutOfRules: this.bsConfig?.keepDatesOutOfRules\n      });\n    }\n    /**\n     * Closes an element’s datepicker. This is considered a “manual” triggering of\n     * the datepicker.\n     */\n    hide() {\n      if (this.isOpen) {\n        this._datepicker.hide();\n      }\n      for (const sub of this._subs) {\n        sub.unsubscribe();\n      }\n      if (this._config.returnFocusToInput) {\n        this._renderer.selectRootElement(this._elementRef.nativeElement).focus();\n      }\n    }\n    /**\n     * Toggles an element’s datepicker. This is considered a “manual” triggering\n     * of the datepicker.\n     */\n    toggle() {\n      if (this.isOpen) {\n        return this.hide();\n      }\n      this.show();\n    }\n    unsubscribeSubscriptions() {\n      if (this._subs?.length) {\n        this._subs.map(sub => sub.unsubscribe());\n        this._subs.length = 0;\n      }\n    }\n    ngOnDestroy() {\n      this._datepicker.dispose();\n      this.isOpen$.next(false);\n      if (this.isDestroy$) {\n        this.isDestroy$.next(null);\n        this.isDestroy$.complete();\n      }\n      this.unsubscribeSubscriptions();\n    }\n    static {\n      this.ɵfac = function BsDaterangepickerDirective_Factory(t) {\n        return new (t || BsDaterangepickerDirective)(i0.ɵɵdirectiveInject(BsDaterangepickerConfig), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(i2.ComponentLoaderFactory));\n      };\n    }\n    static {\n      this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n        type: BsDaterangepickerDirective,\n        selectors: [[\"\", \"bsDaterangepicker\", \"\"]],\n        hostVars: 1,\n        hostBindings: function BsDaterangepickerDirective_HostBindings(rf, ctx) {\n          if (rf & 2) {\n            i0.ɵɵattribute(\"readonly\", ctx.isDatepickerReadonly);\n          }\n        },\n        inputs: {\n          placement: \"placement\",\n          triggers: \"triggers\",\n          outsideClick: \"outsideClick\",\n          container: \"container\",\n          outsideEsc: \"outsideEsc\",\n          isOpen: \"isOpen\",\n          bsValue: \"bsValue\",\n          bsConfig: \"bsConfig\",\n          isDisabled: \"isDisabled\",\n          minDate: \"minDate\",\n          maxDate: \"maxDate\",\n          dateCustomClasses: \"dateCustomClasses\",\n          daysDisabled: \"daysDisabled\",\n          datesDisabled: \"datesDisabled\",\n          datesEnabled: \"datesEnabled\"\n        },\n        outputs: {\n          onShown: \"onShown\",\n          onHidden: \"onHidden\",\n          bsValueChange: \"bsValueChange\"\n        },\n        exportAs: [\"bsDaterangepicker\"],\n        features: [i0.ɵɵNgOnChangesFeature]\n      });\n    }\n  }\n  return BsDaterangepickerDirective;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nconst BS_DATERANGEPICKER_VALUE_ACCESSOR = {\n  provide: NG_VALUE_ACCESSOR,\n  useExisting: forwardRef(() => BsDaterangepickerInputDirective),\n  multi: true\n};\nconst BS_DATERANGEPICKER_VALIDATOR = {\n  provide: NG_VALIDATORS,\n  useExisting: forwardRef(() => BsDaterangepickerInputDirective),\n  multi: true\n};\nlet BsDaterangepickerInputDirective = /*#__PURE__*/(() => {\n  class BsDaterangepickerInputDirective {\n    constructor(_picker, _localeService, _renderer, _elRef, changeDetection) {\n      this._picker = _picker;\n      this._localeService = _localeService;\n      this._renderer = _renderer;\n      this._elRef = _elRef;\n      this.changeDetection = changeDetection;\n      this._onChange = Function.prototype;\n      this._onTouched = Function.prototype;\n      this._validatorChange = Function.prototype;\n      this._subs = new Subscription();\n    }\n    ngOnInit() {\n      const setBsValue = value => {\n        this._setInputValue(value);\n        if (this._value !== value) {\n          this._value = value;\n          this._onChange(value);\n          this._onTouched();\n        }\n        this.changeDetection.markForCheck();\n      };\n      // if value set via [bsValue] it will not get into value change\n      if (this._picker._bsValue) {\n        setBsValue(this._picker._bsValue);\n      }\n      // update input value on datepicker value update\n      this._subs.add(this._picker.bsValueChange.subscribe(value => {\n        this._setInputValue(value);\n        if (this._value !== value) {\n          this._value = value;\n          this._onChange(value);\n          this._onTouched();\n        }\n        this.changeDetection.markForCheck();\n      }));\n      // update input value on locale change\n      this._subs.add(this._localeService.localeChange.subscribe(() => {\n        this._setInputValue(this._value);\n      }));\n      this._subs.add(\n      // update input value on format change\n      this._picker.rangeInputFormat$.pipe(distinctUntilChanged()).subscribe(() => {\n        this._setInputValue(this._value);\n      }));\n    }\n    ngOnDestroy() {\n      this._subs.unsubscribe();\n    }\n    onKeydownEvent(event) {\n      if (event.keyCode === 13 || event.code === 'Enter') {\n        this.hide();\n      }\n    }\n    _setInputValue(date) {\n      let range = '';\n      if (date) {\n        const start = !date[0] ? '' : formatDate(date[0], this._picker._config.rangeInputFormat, this._localeService.currentLocale);\n        const end = !date[1] ? '' : formatDate(date[1], this._picker._config.rangeInputFormat, this._localeService.currentLocale);\n        range = start && end ? start + this._picker._config.rangeSeparator + end : '';\n      }\n      this._renderer.setProperty(this._elRef.nativeElement, 'value', range);\n    }\n    onChange(event) {\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      this.writeValue(event.target.value);\n      this._onChange(this._value);\n      if (this._picker._config.returnFocusToInput) {\n        this._renderer.selectRootElement(this._elRef.nativeElement).focus();\n      }\n      this._onTouched();\n    }\n    validate(c) {\n      let _value = c.value;\n      const errors = [];\n      if (_value === null || _value === undefined || !isArray(_value)) {\n        return null;\n      }\n      _value = _value.slice().sort((a, b) => a.getTime() - b.getTime());\n      const _isFirstDateValid = isDateValid(_value[0]);\n      const _isSecondDateValid = isDateValid(_value[1]);\n      if (!_isFirstDateValid) {\n        return {\n          bsDate: {\n            invalid: _value[0]\n          }\n        };\n      }\n      if (!_isSecondDateValid) {\n        return {\n          bsDate: {\n            invalid: _value[1]\n          }\n        };\n      }\n      if (this._picker && this._picker.minDate && isBefore(_value[0], this._picker.minDate, 'date')) {\n        _value[0] = this._picker.minDate;\n        errors.push({\n          bsDate: {\n            minDate: this._picker.minDate\n          }\n        });\n      }\n      if (this._picker && this._picker.maxDate && isAfter(_value[1], this._picker.maxDate, 'date')) {\n        _value[1] = this._picker.maxDate;\n        errors.push({\n          bsDate: {\n            maxDate: this._picker.maxDate\n          }\n        });\n      }\n      if (errors.length > 0) {\n        this.writeValue(_value);\n        return errors;\n      }\n      return null;\n    }\n    registerOnValidatorChange(fn) {\n      this._validatorChange = fn;\n    }\n    writeValue(value) {\n      if (!value) {\n        this._value = void 0;\n      } else {\n        const _localeKey = this._localeService.currentLocale;\n        const _locale = getLocale(_localeKey);\n        if (!_locale) {\n          throw new Error(`Locale \"${_localeKey}\" is not defined, please add it with \"defineLocale(...)\"`);\n        }\n        let _input = [];\n        if (typeof value === 'string') {\n          const trimmedSeparator = this._picker._config.rangeSeparator.trim();\n          if (value.replace(/[^-]/g, '').length > 1) {\n            _input = value.split(this._picker._config.rangeSeparator);\n          } else {\n            _input = value.split(trimmedSeparator.length > 0 ? trimmedSeparator : this._picker._config.rangeSeparator).map(_val => _val.trim());\n          }\n        }\n        if (Array.isArray(value)) {\n          _input = value;\n        }\n        this._value = _input.map(_val => {\n          if (this._picker._config.useUtc) {\n            return utcAsLocal(parseDate(_val, this._picker._config.rangeInputFormat, this._localeService.currentLocale));\n          }\n          return parseDate(_val, this._picker._config.rangeInputFormat, this._localeService.currentLocale);\n        }).map(date => isNaN(date.valueOf()) ? void 0 : date);\n      }\n      this._picker.bsValue = this._value;\n    }\n    setDisabledState(isDisabled) {\n      this._picker.isDisabled = isDisabled;\n      if (isDisabled) {\n        this._renderer.setAttribute(this._elRef.nativeElement, 'disabled', 'disabled');\n        return;\n      }\n      this._renderer.removeAttribute(this._elRef.nativeElement, 'disabled');\n    }\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    registerOnChange(fn) {\n      this._onChange = fn;\n    }\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    registerOnTouched(fn) {\n      this._onTouched = fn;\n    }\n    onBlur() {\n      this._onTouched();\n    }\n    hide() {\n      this._picker.hide();\n      this._renderer.selectRootElement(this._elRef.nativeElement).blur();\n      if (this._picker._config.returnFocusToInput) {\n        this._renderer.selectRootElement(this._elRef.nativeElement).focus();\n      }\n    }\n    static {\n      this.ɵfac = function BsDaterangepickerInputDirective_Factory(t) {\n        return new (t || BsDaterangepickerInputDirective)(i0.ɵɵdirectiveInject(BsDaterangepickerDirective, 1), i0.ɵɵdirectiveInject(BsLocaleService), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n      };\n    }\n    static {\n      this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n        type: BsDaterangepickerInputDirective,\n        selectors: [[\"input\", \"bsDaterangepicker\", \"\"]],\n        hostBindings: function BsDaterangepickerInputDirective_HostBindings(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵlistener(\"change\", function BsDaterangepickerInputDirective_change_HostBindingHandler($event) {\n              return ctx.onChange($event);\n            })(\"keyup.esc\", function BsDaterangepickerInputDirective_keyup_esc_HostBindingHandler() {\n              return ctx.hide();\n            })(\"keydown\", function BsDaterangepickerInputDirective_keydown_HostBindingHandler($event) {\n              return ctx.onKeydownEvent($event);\n            })(\"blur\", function BsDaterangepickerInputDirective_blur_HostBindingHandler() {\n              return ctx.onBlur();\n            });\n          }\n        },\n        features: [i0.ɵɵProvidersFeature([BS_DATERANGEPICKER_VALUE_ACCESSOR, BS_DATERANGEPICKER_VALIDATOR])]\n      });\n    }\n  }\n  return BsDaterangepickerInputDirective;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet BsDatepickerModule = /*#__PURE__*/(() => {\n  class BsDatepickerModule {\n    static forRoot() {\n      return {\n        ngModule: BsDatepickerModule,\n        providers: [ComponentLoaderFactory, PositioningService, BsDatepickerStore, BsDatepickerActions, BsDatepickerEffects, BsLocaleService, TimepickerActions]\n      };\n    }\n    static {\n      this.ɵfac = function BsDatepickerModule_Factory(t) {\n        return new (t || BsDatepickerModule)();\n      };\n    }\n    static {\n      this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n        type: BsDatepickerModule\n      });\n    }\n    static {\n      this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n        imports: [CommonModule, TooltipModule, TimepickerModule]\n      });\n    }\n  }\n  return BsDatepickerModule;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { BsDatepickerConfig, BsDatepickerContainerComponent, BsDatepickerDirective, BsDatepickerInlineConfig, BsDatepickerInlineContainerComponent, BsDatepickerInlineDirective, BsDatepickerInputDirective, BsDatepickerModule, BsDaterangepickerConfig, BsDaterangepickerContainerComponent, BsDaterangepickerDirective, BsDaterangepickerInlineConfig, BsDaterangepickerInlineContainerComponent, BsDaterangepickerInlineDirective, BsDaterangepickerInputDirective, BsLocaleService };\n//# sourceMappingURL=ngx-bootstrap-datepicker.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}