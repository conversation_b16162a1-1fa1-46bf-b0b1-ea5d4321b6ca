{"ast": null, "code": "import { WebapiService } from './../webapi.service';\nimport { HttpClient } from '@angular/common/http';\nimport { debounceTime, map } from 'rxjs/operators';\nimport { AppComponent } from '../app.component';\nimport { Router } from '../../../node_modules/@angular/router';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"./../webapi.service\";\nimport * as i3 from \"../app.component\";\nimport * as i4 from \"../../../node_modules/@angular/router\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"../../../node_modules/@angular/forms/index\";\nimport * as i7 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i8 from \"../topmenu/topmenu.component\";\nconst _c0 = a0 => ({\n  \"display\": a0,\n  \"opacity\": 1\n});\nfunction MasterpackageComponent_ng_template_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"label\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const r_r2 = ctx.result;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(r_r2.itemid);\n  }\n}\nfunction MasterpackageComponent_tr_35_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"th\", 43);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"th\", 43);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\", 44);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\", 45);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\", 45);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"td\", 46)(12, \"button\", 47);\n    i0.ɵɵlistener(\"click\", function MasterpackageComponent_tr_35_Template_button_click_12_listener() {\n      const item_r4 = i0.ɵɵrestoreView(_r3).$implicit;\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.editmasterpackagefn(item_r4.id, item_r4.itemid, item_r4.qty, item_r4.pack));\n    });\n    i0.ɵɵtext(13, \" Update \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const item_r4 = ctx.$implicit;\n    const i_r6 = ctx.index;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i_r6 + 1);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r4.itemid);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r4.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r4.qty);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r4.pack);\n  }\n}\nfunction MasterpackageComponent_ng_template_48_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"label\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const r_r7 = ctx.result;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(r_r7.itemid);\n  }\n}\nexport let MasterpackageComponent = /*#__PURE__*/(() => {\n  class MasterpackageComponent {\n    constructor(http, service, app, router) {\n      this.http = http;\n      this.service = service;\n      this.app = app;\n      this.router = router;\n      this.mdlSampleIsOpen = false;\n      this.alt = '';\n      this.checkreload = true;\n      this.urlApi = 'http://119.59.112.478:1433';\n      this.itemidedit = '';\n      this.editnumpacking = '';\n      this.editnumpcs = '';\n      this.url = '';\n      this.product = '';\n      this.numpacking = '';\n      this.numpcs = '';\n      this.masterpackgetlist = [];\n      this.permisstiondata = [];\n      this.exportbtn = true;\n      this.searchbtn = true;\n      this.masterpackgetlistse = [];\n      this.search = text$ => text$.pipe(debounceTime(200), map(term => term === '' ? [] : this.productlist.filter(v => v.itemid.toLowerCase().indexOf(term.toLowerCase()) > -1).slice(0, 10)));\n      this.formatter = x => x.itemid;\n      localStorage.removeItem('DataSOderreview');\n      localStorage.removeItem('DataSOderlist');\n      this.url = service.geturlservice();\n      this.permisstiondata = JSON.parse(sessionStorage.getItem('menu'));\n      if (this.permisstiondata == null) {\n        this.router.navigate(['login']);\n      } else {\n        this.exportbtn = !this.permisstiondata[10].flag_print;\n        this.searchbtn = !this.permisstiondata[10].flag_action;\n      }\n    }\n    ngOnInit() {\n      this.http.get(this.url + 'productauto/admin').subscribe(res => {\n        if (res.length > 0) {\n          this.productlist = res;\n          this.getmasterpack();\n        }\n      });\n    }\n    addmasterpackage() {\n      if (this.packget == undefined) {\n        this.openModal(true, 'กรุณาป้อนรหัสสินค้า', false);\n      } else if (this.numpacking == '') {\n        this.openModal(true, 'กรุณาป้อนจำนวนกล่อง/มัด', false);\n      } else if (this.numpcs == '') {\n        this.openModal(true, 'กรุณาป้อนจำนวนแผ่น', false);\n      } else {\n        var item;\n        var st = '';\n        if (this.packget.itemid == undefined) {\n          item = this.packget;\n        } else {\n          item = this.packget.itemid;\n        }\n        st = item.toString();\n        this.http.get(this.url + 'checkitemidtran/' + st.toUpperCase() + '/P').subscribe(res => {\n          if (res[0].num >= 1) {\n            this.openModal(true, 'รหัสสินค้า' + st.toUpperCase() + 'มีในระบบแล้ว', false);\n          } else {\n            var urlpost = `${this.url}${'add_packgetmaster'}/${st.toUpperCase()}/${this.numpacking}/${this.numpcs}`;\n            this.http.post(urlpost, '').subscribe(res => {\n              if (res == true) {\n                this.openModal(true, 'บันทึกข้อมูลเสร็จสิ้น', true);\n                this.packget = [];\n                this.numpacking = '';\n                this.numpcs = '';\n              }\n            });\n          }\n        });\n      }\n    }\n    getmasterpack() {\n      this.http.get(this.url + 'get_masterpack').subscribe(res => {\n        if (res.length > 0) {\n          this.masterpackgetlist = res;\n          this.masterpackgetlistse = this.masterpackgetlist;\n        }\n      });\n    }\n    searchmaster(value) {\n      if (value.length > 0) {\n        this.masterpackgetlistse = this.masterpackgetlist.filter(v => v.itemid.toLowerCase().indexOf(value.toLowerCase()) > -1).slice(0, 50);\n      } else {\n        this.masterpackgetlistse = this.masterpackgetlist;\n      }\n    }\n    editmasterpackagefn(id, itemid, qty, pack) {\n      this.idpack = id;\n      this.itemidedit = itemid;\n      this.editnumpacking = qty;\n      this.editnumpcs = pack;\n    }\n    updatepackage() {\n      var idcheck;\n      if (this.packget == undefined) {\n        idcheck = this.itemidedit;\n      } else {\n        idcheck = this.packget.itemid;\n      }\n      var urlpost = `${this.url}${'update_packmaster'}/${this.idpack}/${idcheck}/${this.editnumpacking}/${this.editnumpcs}`;\n      this.http.post(urlpost, '').subscribe(res => {\n        if (res == true) {\n          this.openModal(true, 'บันทึกข้อมูลเสร็จสิ้น', true);\n          this.idpack = 0;\n          this.itemidedit = '';\n          this.editnumpacking = '';\n          this.editnumpcs = '';\n        }\n      });\n    }\n    deletepackage() {\n      var idcheck;\n      if (this.packget == undefined) {\n        idcheck = this.itemidedit;\n      } else {\n        idcheck = this.packget.itemid;\n      }\n      var urlpost = `${this.url}${'deletepackproduct'}/${idcheck}/${'M'}`;\n      this.http.post(urlpost, '').subscribe(res => {\n        if (res == true) {\n          this.getmasterpack();\n          this.openModal(true, 'ลบข้อมูลเสร็จสิ้น', true);\n        }\n      }, error => {\n        this.openModal(true, 'เกิดข้อผิดพลาดกรุณาลองใหม่อีกครั้ง', true);\n      });\n    }\n    openModal(open, text, load) {\n      this.mdlSampleIsOpen = open;\n      this.alt = text;\n      this.checkreload = load;\n    }\n    closemodel(cl) {\n      this.mdlSampleIsOpen = false;\n      if (this.checkreload == true) {\n        this.router.navigate(['masterpackage']);\n        this.getmasterpack();\n      }\n    }\n    static {\n      this.ɵfac = function MasterpackageComponent_Factory(t) {\n        return new (t || MasterpackageComponent)(i0.ɵɵdirectiveInject(i1.HttpClient), i0.ɵɵdirectiveInject(i2.WebapiService), i0.ɵɵdirectiveInject(i3.AppComponent), i0.ɵɵdirectiveInject(i4.Router));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: MasterpackageComponent,\n        selectors: [[\"app-masterpackage\"]],\n        decls: 76,\n        vars: 21,\n        consts: [[\"rt\", \"\"], [2, \"padding-top\", \"60px\"], [1, \"container-fluid\"], [1, \"p-sm-1\", \"bg-secondary\", \"text-white\", \"text-center\"], [\"novalidate\", \"\", 1, \"needs-validation\"], [1, \"form-row\"], [1, \"col-md-5\", \"mb-2\"], [\"id\", \"typeahead-template\", \"placeholder\", \"\\u0E23\\u0E2B\\u0E31\\u0E2A\\u0E2A\\u0E34\\u0E19\\u0E04\\u0E49\\u0E32\", \"type\", \"text\", \"name\", \"packget\", 1, \"form-control\", \"form-control-sm\", 3, \"ngModelChange\", \"ngModel\", \"ngbTypeahead\", \"resultTemplate\", \"inputFormatter\"], [1, \"col-md-2\", \"mb-2\"], [\"id\", \"numpacking\", \"type\", \"text\", \"name\", \"numpacking\", \"placeholder\", \"\\u0E23\\u0E30\\u0E1A\\u0E38\\u0E08\\u0E33\\u0E19\\u0E27\\u0E19\\u0E01\\u0E25\\u0E48\\u0E2D\\u0E07/\\u0E21\\u0E31\\u0E14\", 1, \"form-control\", \"form-control-sm\", 3, \"ngModelChange\", \"ngModel\"], [\"id\", \"numpcs\", \"type\", \"text\", \"name\", \"numpcs\", \"placeholder\", \"\\u0E23\\u0E30\\u0E1A\\u0E38\\u0E08\\u0E33\\u0E19\\u0E27\\u0E19\\u0E41\\u0E1C\\u0E48\\u0E19\", 1, \"form-control\", \"form-control-sm\", 3, \"ngModelChange\", \"ngModel\"], [1, \"col-md-1\", \"mb-2\", \"col-12\", \"text-center\", \"text-sm-center\", \"text-md-center\", \"text-lg-left\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", \"btn-sm\", \"font-weight-light\", 3, \"click\", \"disabled\"], [1, \"col-md-2\", \"mb-2\", \"float-right\"], [\"placeholder\", \"\\u0E04\\u0E49\\u0E19\\u0E2B\\u0E32\\u0E23\\u0E2B\\u0E31\\u0E2A\\u0E2A\\u0E34\\u0E19\\u0E04\\u0E49\\u0E32\", 1, \"form-control\", \"form-control-sm\", 3, \"keyup\"], [1, \"table\", \"table-hover\", \"table-bordered\", \"table-sm\"], [1, \"text-sm-center\", \"bg-light\"], [\"scope\", \"col\", 1, \"font-weight-normal\", \"test-center\"], [4, \"ngFor\", \"ngForOf\"], [\"id\", \"editmasterpackage\", \"tabindex\", \"-1\", \"role\", \"dialog\", \"aria-labelledby\", \"exampleModalLabel\", \"aria-hidden\", \"true\", 1, \"modal\", \"fade\"], [\"role\", \"document\", 1, \"modal-dialog\"], [1, \"modal-content\"], [1, \"modal-header\"], [\"id\", \"exampleModalLabel\", 1, \"modal-title\"], [\"type\", \"button\", \"data-dismiss\", \"modal\", \"aria-label\", \"Close\", 1, \"close\"], [\"aria-hidden\", \"true\"], [1, \"modal-body\"], [1, \"input-group\"], [1, \"col-md-12\", \"mb-2\"], [\"id\", \"typeahead-template\", \"type\", \"text\", \"name\", \"packget\", 1, \"form-control\", \"form-control-sm\", 3, \"ngModelChange\", \"placeholder\", \"ngModel\", \"ngbTypeahead\", \"resultTemplate\", \"inputFormatter\"], [\"id\", \"editnumpacking\", \"type\", \"text\", \"name\", \"editnumpacking\", \"placeholder\", \"\\u0E23\\u0E30\\u0E1A\\u0E38\\u0E08\\u0E33\\u0E19\\u0E27\\u0E19\\u0E01\\u0E25\\u0E48\\u0E2D\\u0E07/\\u0E21\\u0E31\\u0E14\", 1, \"form-control\", \"form-control-sm\", 3, \"ngModelChange\", \"ngModel\"], [\"id\", \"editnumpcs\", \"type\", \"text\", \"name\", \"editnumpcs\", \"placeholder\", \"\\u0E23\\u0E30\\u0E1A\\u0E38\\u0E08\\u0E33\\u0E19\\u0E27\\u0E19\\u0E41\\u0E1C\\u0E48\\u0E19\", 1, \"form-control\", \"form-control-sm\", 3, \"ngModelChange\", \"ngModel\"], [1, \"modal-footer\"], [\"type\", \"button\", \"data-dismiss\", \"modal\", 1, \"btn\", \"btn-secondary\"], [\"type\", \"button\", \"data-dismiss\", \"modal\", 1, \"btn\", \"btn-primary\", 3, \"click\", \"disabled\"], [\"type\", \"button\", \"data-dismiss\", \"modal\", 1, \"btn\", \"btn-danger\", 3, \"click\", \"disabled\"], [\"id\", \"mdlSample\", \"role\", \"dialog\", 1, \"modal\", \"fade\", 3, \"ngStyle\"], [1, \"modal-dialog\", \"modal-md\"], [1, \"modal-header\", \"colhaederal\"], [1, \"modal-title\"], [\"align\", \"right\", 1, \"modal-footer\"], [\"type\", \"button\", \"id\", \"btnClose\", 1, \"btn\", \"btn-danger\", 3, \"click\"], [1, \"fa\", \"fa-times\", \"fa-fw\"], [1, \"text-sm-center\", \"font-weight-normal\", \"test-center\"], [1, \"text-sm-left\", \"font-weight-normal\"], [1, \"text-sm-center\", \"font-weight-normal\"], [1, \"text-center\"], [\"data-toggle\", \"modal\", \"data-target\", \"#editmasterpackage\", \"aria-expanded\", \"true\", \"aria-controls\", \"collapseOne\", 1, \"btn\", \"btn-link\", \"font-weight-light\", 2, \"padding\", \"0pt\", 3, \"click\"]],\n        template: function MasterpackageComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            const _r1 = i0.ɵɵgetCurrentView();\n            i0.ɵɵelement(0, \"app-topmenu\");\n            i0.ɵɵelementStart(1, \"section\", 1)(2, \"div\", 2)(3, \"h5\", 3);\n            i0.ɵɵtext(4, \"Master Package\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(5, \"form\", 4)(6, \"div\", 5)(7, \"div\", 6);\n            i0.ɵɵtemplate(8, MasterpackageComponent_ng_template_8_Template, 2, 1, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n            i0.ɵɵelementStart(10, \"input\", 7);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function MasterpackageComponent_Template_input_ngModelChange_10_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.packget, $event) || (ctx.packget = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(11, \"div\", 8)(12, \"input\", 9);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function MasterpackageComponent_Template_input_ngModelChange_12_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.numpacking, $event) || (ctx.numpacking = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(13, \"div\", 8)(14, \"input\", 10);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function MasterpackageComponent_Template_input_ngModelChange_14_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.numpcs, $event) || (ctx.numpcs = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(15, \"div\", 11)(16, \"button\", 12);\n            i0.ɵɵlistener(\"click\", function MasterpackageComponent_Template_button_click_16_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.addmasterpackage());\n            });\n            i0.ɵɵtext(17, \"\\u0E40\\u0E1E\\u0E34\\u0E48\\u0E21\\u0E23\\u0E32\\u0E22\\u0E01\\u0E32\\u0E23\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(18, \"div\", 13)(19, \"input\", 14);\n            i0.ɵɵlistener(\"keyup\", function MasterpackageComponent_Template_input_keyup_19_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.searchmaster($event.target.value));\n            });\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(20, \"table\", 15)(21, \"thead\")(22, \"tr\", 16)(23, \"th\", 17);\n            i0.ɵɵtext(24, \"Item\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(25, \"th\", 17);\n            i0.ɵɵtext(26, \"\\u0E23\\u0E2B\\u0E31\\u0E2A\\u0E2A\\u0E34\\u0E19\\u0E04\\u0E49\\u0E32\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(27, \"th\", 17);\n            i0.ɵɵtext(28, \"\\u0E2A\\u0E34\\u0E19\\u0E04\\u0E49\\u0E32\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(29, \"th\", 17);\n            i0.ɵɵtext(30, \"\\u0E08\\u0E33\\u0E19\\u0E27\\u0E19\\u0E01\\u0E25\\u0E48\\u0E2D\\u0E07/\\u0E21\\u0E31\\u0E14\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(31, \"th\", 17);\n            i0.ɵɵtext(32, \"\\u0E08\\u0E33\\u0E19\\u0E27\\u0E19\\u0E41\\u0E1C\\u0E48\\u0E19\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(33, \"th\", 17);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(34, \"tbody\");\n            i0.ɵɵtemplate(35, MasterpackageComponent_tr_35_Template, 14, 5, \"tr\", 18);\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(36, \"div\", 19)(37, \"div\", 20)(38, \"div\", 21)(39, \"div\", 22)(40, \"h5\", 23);\n            i0.ɵɵtext(41, \"Edit MasterPackage\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(42, \"button\", 24)(43, \"span\", 25);\n            i0.ɵɵtext(44, \"\\u00D7\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(45, \"div\", 26)(46, \"div\", 27)(47, \"div\", 28);\n            i0.ɵɵtemplate(48, MasterpackageComponent_ng_template_48_Template, 2, 1, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n            i0.ɵɵelementStart(50, \"input\", 29);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function MasterpackageComponent_Template_input_ngModelChange_50_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.packget, $event) || (ctx.packget = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(51, \"div\", 27)(52, \"div\", 28)(53, \"input\", 30);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function MasterpackageComponent_Template_input_ngModelChange_53_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.editnumpacking, $event) || (ctx.editnumpacking = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(54, \"div\", 27)(55, \"div\", 28)(56, \"input\", 31);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function MasterpackageComponent_Template_input_ngModelChange_56_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.editnumpcs, $event) || (ctx.editnumpcs = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(57, \"div\", 32)(58, \"button\", 33);\n            i0.ɵɵtext(59, \"Close\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(60, \"button\", 34);\n            i0.ɵɵlistener(\"click\", function MasterpackageComponent_Template_button_click_60_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.updatepackage());\n            });\n            i0.ɵɵtext(61, \"Update\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(62, \"button\", 35);\n            i0.ɵɵlistener(\"click\", function MasterpackageComponent_Template_button_click_62_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.deletepackage());\n            });\n            i0.ɵɵtext(63, \"Delete\");\n            i0.ɵɵelementEnd()()()()();\n            i0.ɵɵelementStart(64, \"div\", 36)(65, \"div\", 37)(66, \"div\", 21)(67, \"div\", 38)(68, \"h4\", 39);\n            i0.ɵɵtext(69, \"Report\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(70, \"div\", 26);\n            i0.ɵɵtext(71);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(72, \"div\", 40)(73, \"button\", 41);\n            i0.ɵɵlistener(\"click\", function MasterpackageComponent_Template_button_click_73_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.closemodel(false));\n            });\n            i0.ɵɵelement(74, \"i\", 42);\n            i0.ɵɵtext(75, \" \\u0E1B\\u0E34\\u0E14\");\n            i0.ɵɵelementEnd()()()()();\n          }\n          if (rf & 2) {\n            const rt_r8 = i0.ɵɵreference(9);\n            i0.ɵɵadvance(10);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.packget);\n            i0.ɵɵproperty(\"ngbTypeahead\", ctx.search)(\"resultTemplate\", rt_r8)(\"inputFormatter\", ctx.formatter);\n            i0.ɵɵadvance(2);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.numpacking);\n            i0.ɵɵadvance(2);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.numpcs);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"disabled\", ctx.searchbtn);\n            i0.ɵɵadvance(19);\n            i0.ɵɵproperty(\"ngForOf\", ctx.masterpackgetlistse);\n            i0.ɵɵadvance(15);\n            i0.ɵɵpropertyInterpolate(\"placeholder\", ctx.itemidedit);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.packget);\n            i0.ɵɵproperty(\"ngbTypeahead\", ctx.search)(\"resultTemplate\", rt_r8)(\"inputFormatter\", ctx.formatter);\n            i0.ɵɵadvance(3);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.editnumpacking);\n            i0.ɵɵadvance(3);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.editnumpcs);\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"disabled\", ctx.searchbtn);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"disabled\", ctx.searchbtn);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(19, _c0, ctx.mdlSampleIsOpen ? \"block\" : \"none\"));\n            i0.ɵɵadvance(7);\n            i0.ɵɵtextInterpolate(ctx.alt);\n          }\n        },\n        dependencies: [i5.NgForOf, i5.NgStyle, i6.ɵNgNoValidate, i6.DefaultValueAccessor, i6.NgControlStatus, i6.NgControlStatusGroup, i6.NgModel, i6.NgForm, i7.NgbTypeahead, i8.TopmenuComponent]\n      });\n    }\n  }\n  return MasterpackageComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}