{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.throwIfEmpty = void 0;\nvar EmptyError_1 = require(\"../util/EmptyError\");\nvar lift_1 = require(\"../util/lift\");\nvar OperatorSubscriber_1 = require(\"./OperatorSubscriber\");\nfunction throwIfEmpty(errorFactory) {\n  if (errorFactory === void 0) {\n    errorFactory = defaultErrorFactory;\n  }\n  return lift_1.operate(function (source, subscriber) {\n    var hasValue = false;\n    source.subscribe(OperatorSubscriber_1.createOperatorSubscriber(subscriber, function (value) {\n      hasValue = true;\n      subscriber.next(value);\n    }, function () {\n      return hasValue ? subscriber.complete() : subscriber.error(errorFactory());\n    }));\n  });\n}\nexports.throwIfEmpty = throwIfEmpty;\nfunction defaultErrorFactory() {\n  return new EmptyError_1.EmptyError();\n}\n//# sourceMappingURL=throwIfEmpty.js.map", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}