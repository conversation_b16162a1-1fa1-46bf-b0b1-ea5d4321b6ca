{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Turkish [tr]\n//! authors : <PERSON><PERSON><PERSON> : https://github.com/erhangundogan,\n//!           <PERSON><PERSON><PERSON>: https://github.com/BYK\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  var suffixes = {\n    1: \"'inci\",\n    5: \"'inci\",\n    8: \"'inci\",\n    70: \"'inci\",\n    80: \"'inci\",\n    2: \"'nci\",\n    7: \"'nci\",\n    20: \"'nci\",\n    50: \"'nci\",\n    3: \"'üncü\",\n    4: \"'üncü\",\n    100: \"'üncü\",\n    6: \"'ncı\",\n    9: \"'uncu\",\n    10: \"'uncu\",\n    30: \"'uncu\",\n    60: \"'ıncı\",\n    90: \"'ıncı\"\n  };\n  var tr = moment.defineLocale('tr', {\n    months: 'Ocak_Şubat_Mart_Nisan_Mayıs_Haziran_Temmuz_Ağustos_Eylül_Ekim_Kasım_Aralık'.split('_'),\n    monthsShort: 'Oca_Şub_Mar_Nis_May_Haz_Tem_Ağu_Eyl_Eki_Kas_Ara'.split('_'),\n    weekdays: 'Pazar_Pazartesi_Salı_Çarşamba_Perşembe_Cuma_Cumartesi'.split('_'),\n    weekdaysShort: 'Paz_Pzt_Sal_Çar_Per_Cum_Cmt'.split('_'),\n    weekdaysMin: 'Pz_Pt_Sa_Ça_Pe_Cu_Ct'.split('_'),\n    meridiem: function (hours, minutes, isLower) {\n      if (hours < 12) {\n        return isLower ? 'öö' : 'ÖÖ';\n      } else {\n        return isLower ? 'ös' : 'ÖS';\n      }\n    },\n    meridiemParse: /öö|ÖÖ|ös|ÖS/,\n    isPM: function (input) {\n      return input === 'ös' || input === 'ÖS';\n    },\n    longDateFormat: {\n      LT: 'HH:mm',\n      LTS: 'HH:mm:ss',\n      L: 'DD.MM.YYYY',\n      LL: 'D MMMM YYYY',\n      LLL: 'D MMMM YYYY HH:mm',\n      LLLL: 'dddd, D MMMM YYYY HH:mm'\n    },\n    calendar: {\n      sameDay: '[bugün saat] LT',\n      nextDay: '[yarın saat] LT',\n      nextWeek: '[gelecek] dddd [saat] LT',\n      lastDay: '[dün] LT',\n      lastWeek: '[geçen] dddd [saat] LT',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: '%s sonra',\n      past: '%s önce',\n      s: 'birkaç saniye',\n      ss: '%d saniye',\n      m: 'bir dakika',\n      mm: '%d dakika',\n      h: 'bir saat',\n      hh: '%d saat',\n      d: 'bir gün',\n      dd: '%d gün',\n      w: 'bir hafta',\n      ww: '%d hafta',\n      M: 'bir ay',\n      MM: '%d ay',\n      y: 'bir yıl',\n      yy: '%d yıl'\n    },\n    ordinal: function (number, period) {\n      switch (period) {\n        case 'd':\n        case 'D':\n        case 'Do':\n        case 'DD':\n          return number;\n        default:\n          if (number === 0) {\n            // special case for zero\n            return number + \"'ıncı\";\n          }\n          var a = number % 10,\n            b = number % 100 - a,\n            c = number >= 100 ? 100 : null;\n          return number + (suffixes[a] || suffixes[b] || suffixes[c]);\n      }\n    },\n    week: {\n      dow: 1,\n      // Monday is the first day of the week.\n      doy: 7 // The week that contains Jan 7th is the first week of the year.\n    }\n  });\n  return tr;\n});", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}