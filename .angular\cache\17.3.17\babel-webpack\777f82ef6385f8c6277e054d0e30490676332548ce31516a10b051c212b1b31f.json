{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.fromSubscribable = void 0;\nvar Observable_1 = require(\"../Observable\");\nfunction fromSubscribable(subscribable) {\n  return new Observable_1.Observable(function (subscriber) {\n    return subscribable.subscribe(subscriber);\n  });\n}\nexports.fromSubscribable = fromSubscribable;\n//# sourceMappingURL=fromSubscribable.js.map", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}