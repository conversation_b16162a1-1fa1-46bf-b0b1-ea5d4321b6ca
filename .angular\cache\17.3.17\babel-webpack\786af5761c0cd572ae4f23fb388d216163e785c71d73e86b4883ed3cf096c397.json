{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.empty = exports.EMPTY = void 0;\nvar Observable_1 = require(\"../Observable\");\nexports.EMPTY = new Observable_1.Observable(function (subscriber) {\n  return subscriber.complete();\n});\nfunction empty(scheduler) {\n  return scheduler ? emptyScheduled(scheduler) : exports.EMPTY;\n}\nexports.empty = empty;\nfunction emptyScheduled(scheduler) {\n  return new Observable_1.Observable(function (subscriber) {\n    return scheduler.schedule(function () {\n      return subscriber.complete();\n    });\n  });\n}\n//# sourceMappingURL=empty.js.map", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}