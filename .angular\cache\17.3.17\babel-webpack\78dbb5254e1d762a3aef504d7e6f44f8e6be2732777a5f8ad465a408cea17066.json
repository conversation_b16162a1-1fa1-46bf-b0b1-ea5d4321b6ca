{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.combineLatestInit = exports.combineLatest = void 0;\nvar Observable_1 = require(\"../Observable\");\nvar argsArgArrayOrObject_1 = require(\"../util/argsArgArrayOrObject\");\nvar from_1 = require(\"./from\");\nvar identity_1 = require(\"../util/identity\");\nvar mapOneOrManyArgs_1 = require(\"../util/mapOneOrManyArgs\");\nvar args_1 = require(\"../util/args\");\nvar createObject_1 = require(\"../util/createObject\");\nvar OperatorSubscriber_1 = require(\"../operators/OperatorSubscriber\");\nvar executeSchedule_1 = require(\"../util/executeSchedule\");\nfunction combineLatest() {\n  var args = [];\n  for (var _i = 0; _i < arguments.length; _i++) {\n    args[_i] = arguments[_i];\n  }\n  var scheduler = args_1.popScheduler(args);\n  var resultSelector = args_1.popResultSelector(args);\n  var _a = argsArgArrayOrObject_1.argsArgArrayOrObject(args),\n    observables = _a.args,\n    keys = _a.keys;\n  if (observables.length === 0) {\n    return from_1.from([], scheduler);\n  }\n  var result = new Observable_1.Observable(combineLatestInit(observables, scheduler, keys ? function (values) {\n    return createObject_1.createObject(keys, values);\n  } : identity_1.identity));\n  return resultSelector ? result.pipe(mapOneOrManyArgs_1.mapOneOrManyArgs(resultSelector)) : result;\n}\nexports.combineLatest = combineLatest;\nfunction combineLatestInit(observables, scheduler, valueTransform) {\n  if (valueTransform === void 0) {\n    valueTransform = identity_1.identity;\n  }\n  return function (subscriber) {\n    maybeSchedule(scheduler, function () {\n      var length = observables.length;\n      var values = new Array(length);\n      var active = length;\n      var remainingFirstValues = length;\n      var _loop_1 = function (i) {\n        maybeSchedule(scheduler, function () {\n          var source = from_1.from(observables[i], scheduler);\n          var hasFirstValue = false;\n          source.subscribe(OperatorSubscriber_1.createOperatorSubscriber(subscriber, function (value) {\n            values[i] = value;\n            if (!hasFirstValue) {\n              hasFirstValue = true;\n              remainingFirstValues--;\n            }\n            if (!remainingFirstValues) {\n              subscriber.next(valueTransform(values.slice()));\n            }\n          }, function () {\n            if (! --active) {\n              subscriber.complete();\n            }\n          }));\n        }, subscriber);\n      };\n      for (var i = 0; i < length; i++) {\n        _loop_1(i);\n      }\n    }, subscriber);\n  };\n}\nexports.combineLatestInit = combineLatestInit;\nfunction maybeSchedule(scheduler, execute, subscription) {\n  if (scheduler) {\n    executeSchedule_1.executeSchedule(subscription, scheduler, execute);\n  } else {\n    execute();\n  }\n}\n//# sourceMappingURL=combineLatest.js.map", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}