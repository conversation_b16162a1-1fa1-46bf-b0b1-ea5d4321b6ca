{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.materialize = void 0;\nvar Notification_1 = require(\"../Notification\");\nvar lift_1 = require(\"../util/lift\");\nvar OperatorSubscriber_1 = require(\"./OperatorSubscriber\");\nfunction materialize() {\n  return lift_1.operate(function (source, subscriber) {\n    source.subscribe(OperatorSubscriber_1.createOperatorSubscriber(subscriber, function (value) {\n      subscriber.next(Notification_1.Notification.createNext(value));\n    }, function () {\n      subscriber.next(Notification_1.Notification.createComplete());\n      subscriber.complete();\n    }, function (err) {\n      subscriber.next(Notification_1.Notification.createError(err));\n      subscriber.complete();\n    }));\n  });\n}\nexports.materialize = materialize;\n//# sourceMappingURL=materialize.js.map", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}