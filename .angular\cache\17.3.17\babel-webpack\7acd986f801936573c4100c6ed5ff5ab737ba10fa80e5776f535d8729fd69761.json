{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.popNumber = exports.popScheduler = exports.popResultSelector = void 0;\nvar isFunction_1 = require(\"./isFunction\");\nvar isScheduler_1 = require(\"./isScheduler\");\nfunction last(arr) {\n  return arr[arr.length - 1];\n}\nfunction popResultSelector(args) {\n  return isFunction_1.isFunction(last(args)) ? args.pop() : undefined;\n}\nexports.popResultSelector = popResultSelector;\nfunction popScheduler(args) {\n  return isScheduler_1.isScheduler(last(args)) ? args.pop() : undefined;\n}\nexports.popScheduler = popScheduler;\nfunction popNumber(args, defaultValue) {\n  return typeof last(args) === 'number' ? args.pop() : defaultValue;\n}\nexports.popNumber = popNumber;\n//# sourceMappingURL=args.js.map", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}