{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.timestamp = void 0;\nvar dateTimestampProvider_1 = require(\"../scheduler/dateTimestampProvider\");\nvar map_1 = require(\"./map\");\nfunction timestamp(timestampProvider) {\n  if (timestampProvider === void 0) {\n    timestampProvider = dateTimestampProvider_1.dateTimestampProvider;\n  }\n  return map_1.map(function (value) {\n    return {\n      value: value,\n      timestamp: timestampProvider.now()\n    };\n  });\n}\nexports.timestamp = timestamp;\n//# sourceMappingURL=timestamp.js.map", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}