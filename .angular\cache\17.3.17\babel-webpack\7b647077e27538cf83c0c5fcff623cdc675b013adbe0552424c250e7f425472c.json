{"ast": null, "code": "import _asyncToGenerator from \"D:/ISR/front_soweb/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nvar e = \"undefined\" != typeof self ? self : global;\nconst t = \"undefined\" != typeof navigator,\n  i = t && \"undefined\" == typeof HTMLImageElement,\n  n = !(\"undefined\" == typeof global || \"undefined\" == typeof process || !process.versions || !process.versions.node),\n  s = e.<PERSON>,\n  r = e.BigInt,\n  a = !!s,\n  o = e => e;\nfunction l(e, t = o) {\n  if (n) try {\n    return \"function\" == typeof require ? Promise.resolve(t(require(e))) : import(/* webpackIgnore: true */e).then(t);\n  } catch (t) {\n    console.warn(`Couldn't load ${e}`);\n  }\n}\nlet h = e.fetch;\nconst u = e => h = e;\nif (!e.fetch) {\n  const e = l(\"http\", e => e),\n    t = l(\"https\", e => e),\n    i = (n, {\n      headers: s\n    } = {}) => new Promise(/*#__PURE__*/function () {\n      var _ref = _asyncToGenerator(function* (r, a) {\n        let {\n          port: o,\n          hostname: l,\n          pathname: h,\n          protocol: u,\n          search: c\n        } = new URL(n);\n        const f = {\n          method: \"GET\",\n          hostname: l,\n          path: encodeURI(h) + c,\n          headers: s\n        };\n        \"\" !== o && (f.port = Number(o));\n        const d = (\"https:\" === u ? yield t : yield e).request(f, e => {\n          if (301 === e.statusCode || 302 === e.statusCode) {\n            let t = new URL(e.headers.location, n).toString();\n            return i(t, {\n              headers: s\n            }).then(r).catch(a);\n          }\n          r({\n            status: e.statusCode,\n            arrayBuffer: () => new Promise(t => {\n              let i = [];\n              e.on(\"data\", e => i.push(e)), e.on(\"end\", () => t(Buffer.concat(i)));\n            })\n          });\n        });\n        d.on(\"error\", a), d.end();\n      });\n      return function (_x, _x2) {\n        return _ref.apply(this, arguments);\n      };\n    }());\n  u(i);\n}\nfunction c(e, t, i) {\n  return t in e ? Object.defineProperty(e, t, {\n    value: i,\n    enumerable: !0,\n    configurable: !0,\n    writable: !0\n  }) : e[t] = i, e;\n}\nconst f = e => p(e) ? void 0 : e,\n  d = e => void 0 !== e;\nfunction p(e) {\n  return void 0 === e || (e instanceof Map ? 0 === e.size : 0 === Object.values(e).filter(d).length);\n}\nfunction g(e) {\n  let t = new Error(e);\n  throw delete t.stack, t;\n}\nfunction m(e) {\n  return \"\" === (e = function (e) {\n    for (; e.endsWith(\"\\0\");) e = e.slice(0, -1);\n    return e;\n  }(e).trim()) ? void 0 : e;\n}\nfunction S(e) {\n  let t = function (e) {\n    let t = 0;\n    return e.ifd0.enabled && (t += 1024), e.exif.enabled && (t += 2048), e.makerNote && (t += 2048), e.userComment && (t += 1024), e.gps.enabled && (t += 512), e.interop.enabled && (t += 100), e.ifd1.enabled && (t += 1024), t + 2048;\n  }(e);\n  return e.jfif.enabled && (t += 50), e.xmp.enabled && (t += 2e4), e.iptc.enabled && (t += 14e3), e.icc.enabled && (t += 6e3), t;\n}\nconst C = e => String.fromCharCode.apply(null, e),\n  y = \"undefined\" != typeof TextDecoder ? new TextDecoder(\"utf-8\") : void 0;\nfunction b(e) {\n  return y ? y.decode(e) : a ? Buffer.from(e).toString(\"utf8\") : decodeURIComponent(escape(C(e)));\n}\nclass I {\n  static from(e, t) {\n    return e instanceof this && e.le === t ? e : new I(e, void 0, void 0, t);\n  }\n  constructor(e, t = 0, i, n) {\n    if (\"boolean\" == typeof n && (this.le = n), Array.isArray(e) && (e = new Uint8Array(e)), 0 === e) this.byteOffset = 0, this.byteLength = 0;else if (e instanceof ArrayBuffer) {\n      void 0 === i && (i = e.byteLength - t);\n      let n = new DataView(e, t, i);\n      this._swapDataView(n);\n    } else if (e instanceof Uint8Array || e instanceof DataView || e instanceof I) {\n      void 0 === i && (i = e.byteLength - t), (t += e.byteOffset) + i > e.byteOffset + e.byteLength && g(\"Creating view outside of available memory in ArrayBuffer\");\n      let n = new DataView(e.buffer, t, i);\n      this._swapDataView(n);\n    } else if (\"number\" == typeof e) {\n      let t = new DataView(new ArrayBuffer(e));\n      this._swapDataView(t);\n    } else g(\"Invalid input argument for BufferView: \" + e);\n  }\n  _swapArrayBuffer(e) {\n    this._swapDataView(new DataView(e));\n  }\n  _swapBuffer(e) {\n    this._swapDataView(new DataView(e.buffer, e.byteOffset, e.byteLength));\n  }\n  _swapDataView(e) {\n    this.dataView = e, this.buffer = e.buffer, this.byteOffset = e.byteOffset, this.byteLength = e.byteLength;\n  }\n  _lengthToEnd(e) {\n    return this.byteLength - e;\n  }\n  set(e, t, i = I) {\n    return e instanceof DataView || e instanceof I ? e = new Uint8Array(e.buffer, e.byteOffset, e.byteLength) : e instanceof ArrayBuffer && (e = new Uint8Array(e)), e instanceof Uint8Array || g(\"BufferView.set(): Invalid data argument.\"), this.toUint8().set(e, t), new i(this, t, e.byteLength);\n  }\n  subarray(e, t) {\n    return t = t || this._lengthToEnd(e), new I(this, e, t);\n  }\n  toUint8() {\n    return new Uint8Array(this.buffer, this.byteOffset, this.byteLength);\n  }\n  getUint8Array(e, t) {\n    return new Uint8Array(this.buffer, this.byteOffset + e, t);\n  }\n  getString(e = 0, t = this.byteLength) {\n    return b(this.getUint8Array(e, t));\n  }\n  getLatin1String(e = 0, t = this.byteLength) {\n    let i = this.getUint8Array(e, t);\n    return C(i);\n  }\n  getUnicodeString(e = 0, t = this.byteLength) {\n    const i = [];\n    for (let n = 0; n < t && e + n < this.byteLength; n += 2) i.push(this.getUint16(e + n));\n    return C(i);\n  }\n  getInt8(e) {\n    return this.dataView.getInt8(e);\n  }\n  getUint8(e) {\n    return this.dataView.getUint8(e);\n  }\n  getInt16(e, t = this.le) {\n    return this.dataView.getInt16(e, t);\n  }\n  getInt32(e, t = this.le) {\n    return this.dataView.getInt32(e, t);\n  }\n  getUint16(e, t = this.le) {\n    return this.dataView.getUint16(e, t);\n  }\n  getUint32(e, t = this.le) {\n    return this.dataView.getUint32(e, t);\n  }\n  getFloat32(e, t = this.le) {\n    return this.dataView.getFloat32(e, t);\n  }\n  getFloat64(e, t = this.le) {\n    return this.dataView.getFloat64(e, t);\n  }\n  getFloat(e, t = this.le) {\n    return this.dataView.getFloat32(e, t);\n  }\n  getDouble(e, t = this.le) {\n    return this.dataView.getFloat64(e, t);\n  }\n  getUintBytes(e, t, i) {\n    switch (t) {\n      case 1:\n        return this.getUint8(e, i);\n      case 2:\n        return this.getUint16(e, i);\n      case 4:\n        return this.getUint32(e, i);\n      case 8:\n        return this.getUint64 && this.getUint64(e, i);\n    }\n  }\n  getUint(e, t, i) {\n    switch (t) {\n      case 8:\n        return this.getUint8(e, i);\n      case 16:\n        return this.getUint16(e, i);\n      case 32:\n        return this.getUint32(e, i);\n      case 64:\n        return this.getUint64 && this.getUint64(e, i);\n    }\n  }\n  toString(e) {\n    return this.dataView.toString(e, this.constructor.name);\n  }\n  ensureChunk() {}\n}\nfunction P(e, t) {\n  g(`${e} '${t}' was not loaded, try using full build of exifr.`);\n}\nclass k extends Map {\n  constructor(e) {\n    super(), this.kind = e;\n  }\n  get(e, t) {\n    return this.has(e) || P(this.kind, e), t && (e in t || function (e, t) {\n      g(`Unknown ${e} '${t}'.`);\n    }(this.kind, e), t[e].enabled || P(this.kind, e)), super.get(e);\n  }\n  keyList() {\n    return Array.from(this.keys());\n  }\n}\nvar w = new k(\"file parser\"),\n  T = new k(\"segment parser\"),\n  A = new k(\"file reader\");\nfunction D(e, n) {\n  return \"string\" == typeof e ? O(e, n) : t && !i && e instanceof HTMLImageElement ? O(e.src, n) : e instanceof Uint8Array || e instanceof ArrayBuffer || e instanceof DataView ? new I(e) : t && e instanceof Blob ? x(e, n, \"blob\", R) : void g(\"Invalid input argument\");\n}\nfunction O(e, i) {\n  return (s = e).startsWith(\"data:\") || s.length > 1e4 ? v(e, i, \"base64\") : n && e.includes(\"://\") ? x(e, i, \"url\", M) : n ? v(e, i, \"fs\") : t ? x(e, i, \"url\", M) : void g(\"Invalid input argument\");\n  var s;\n}\nfunction x(_x3, _x4, _x5, _x6) {\n  return _x7.apply(this, arguments);\n}\nfunction _x7() {\n  _x7 = _asyncToGenerator(function* (e, t, i, n) {\n    return A.has(i) ? v(e, t, i) : n ? function () {\n      var _ref4 = _asyncToGenerator(function* (e, t) {\n        let i = yield t(e);\n        return new I(i);\n      });\n      return function (_x24, _x25) {\n        return _ref4.apply(this, arguments);\n      };\n    }()(e, n) : void g(`Parser ${i} is not loaded`);\n  });\n  return _x7.apply(this, arguments);\n}\nfunction v(_x8, _x9, _x0) {\n  return _v.apply(this, arguments);\n}\nfunction _v() {\n  _v = _asyncToGenerator(function* (e, t, i) {\n    let n = new (A.get(i))(e, t);\n    return yield n.read(), n;\n  });\n  return _v.apply(this, arguments);\n}\nconst M = e => h(e).then(e => e.arrayBuffer()),\n  R = e => new Promise((t, i) => {\n    let n = new FileReader();\n    n.onloadend = () => t(n.result || new ArrayBuffer()), n.onerror = i, n.readAsArrayBuffer(e);\n  });\nclass L extends Map {\n  get tagKeys() {\n    return this.allKeys || (this.allKeys = Array.from(this.keys())), this.allKeys;\n  }\n  get tagValues() {\n    return this.allValues || (this.allValues = Array.from(this.values())), this.allValues;\n  }\n}\nfunction U(e, t, i) {\n  let n = new L();\n  for (let [e, t] of i) n.set(e, t);\n  if (Array.isArray(t)) for (let i of t) e.set(i, n);else e.set(t, n);\n  return n;\n}\nfunction F(e, t, i) {\n  let n,\n    s = e.get(t);\n  for (n of i) s.set(n[0], n[1]);\n}\nconst E = new Map(),\n  B = new Map(),\n  N = new Map(),\n  G = [\"chunked\", \"firstChunkSize\", \"firstChunkSizeNode\", \"firstChunkSizeBrowser\", \"chunkSize\", \"chunkLimit\"],\n  V = [\"jfif\", \"xmp\", \"icc\", \"iptc\", \"ihdr\"],\n  z = [\"tiff\", ...V],\n  H = [\"ifd0\", \"ifd1\", \"exif\", \"gps\", \"interop\"],\n  j = [...z, ...H],\n  W = [\"makerNote\", \"userComment\"],\n  K = [\"translateKeys\", \"translateValues\", \"reviveValues\", \"multiSegment\"],\n  X = [...K, \"sanitize\", \"mergeOutput\", \"silentErrors\"];\nclass _ {\n  get translate() {\n    return this.translateKeys || this.translateValues || this.reviveValues;\n  }\n}\nclass Y extends _ {\n  get needed() {\n    return this.enabled || this.deps.size > 0;\n  }\n  constructor(e, t, i, n) {\n    if (super(), c(this, \"enabled\", !1), c(this, \"skip\", new Set()), c(this, \"pick\", new Set()), c(this, \"deps\", new Set()), c(this, \"translateKeys\", !1), c(this, \"translateValues\", !1), c(this, \"reviveValues\", !1), this.key = e, this.enabled = t, this.parse = this.enabled, this.applyInheritables(n), this.canBeFiltered = H.includes(e), this.canBeFiltered && (this.dict = E.get(e)), void 0 !== i) if (Array.isArray(i)) this.parse = this.enabled = !0, this.canBeFiltered && i.length > 0 && this.translateTagSet(i, this.pick);else if (\"object\" == typeof i) {\n      if (this.enabled = !0, this.parse = !1 !== i.parse, this.canBeFiltered) {\n        let {\n          pick: e,\n          skip: t\n        } = i;\n        e && e.length > 0 && this.translateTagSet(e, this.pick), t && t.length > 0 && this.translateTagSet(t, this.skip);\n      }\n      this.applyInheritables(i);\n    } else !0 === i || !1 === i ? this.parse = this.enabled = i : g(`Invalid options argument: ${i}`);\n  }\n  applyInheritables(e) {\n    let t, i;\n    for (t of K) i = e[t], void 0 !== i && (this[t] = i);\n  }\n  translateTagSet(e, t) {\n    if (this.dict) {\n      let i,\n        n,\n        {\n          tagKeys: s,\n          tagValues: r\n        } = this.dict;\n      for (i of e) \"string\" == typeof i ? (n = r.indexOf(i), -1 === n && (n = s.indexOf(Number(i))), -1 !== n && t.add(Number(s[n]))) : t.add(i);\n    } else for (let i of e) t.add(i);\n  }\n  finalizeFilters() {\n    !this.enabled && this.deps.size > 0 ? (this.enabled = !0, ee(this.pick, this.deps)) : this.enabled && this.pick.size > 0 && ee(this.pick, this.deps);\n  }\n}\nvar $ = {\n    jfif: !1,\n    tiff: !0,\n    xmp: !1,\n    icc: !1,\n    iptc: !1,\n    ifd0: !0,\n    ifd1: !1,\n    exif: !0,\n    gps: !0,\n    interop: !1,\n    ihdr: void 0,\n    makerNote: !1,\n    userComment: !1,\n    multiSegment: !1,\n    skip: [],\n    pick: [],\n    translateKeys: !0,\n    translateValues: !0,\n    reviveValues: !0,\n    sanitize: !0,\n    mergeOutput: !0,\n    silentErrors: !0,\n    chunked: !0,\n    firstChunkSize: void 0,\n    firstChunkSizeNode: 512,\n    firstChunkSizeBrowser: 65536,\n    chunkSize: 65536,\n    chunkLimit: 5\n  },\n  J = new Map();\nclass q extends _ {\n  static useCached(e) {\n    let t = J.get(e);\n    return void 0 !== t || (t = new this(e), J.set(e, t)), t;\n  }\n  constructor(e) {\n    super(), !0 === e ? this.setupFromTrue() : void 0 === e ? this.setupFromUndefined() : Array.isArray(e) ? this.setupFromArray(e) : \"object\" == typeof e ? this.setupFromObject(e) : g(`Invalid options argument ${e}`), void 0 === this.firstChunkSize && (this.firstChunkSize = t ? this.firstChunkSizeBrowser : this.firstChunkSizeNode), this.mergeOutput && (this.ifd1.enabled = !1), this.filterNestedSegmentTags(), this.traverseTiffDependencyTree(), this.checkLoadedPlugins();\n  }\n  setupFromUndefined() {\n    let e;\n    for (e of G) this[e] = $[e];\n    for (e of X) this[e] = $[e];\n    for (e of W) this[e] = $[e];\n    for (e of j) this[e] = new Y(e, $[e], void 0, this);\n  }\n  setupFromTrue() {\n    let e;\n    for (e of G) this[e] = $[e];\n    for (e of X) this[e] = $[e];\n    for (e of W) this[e] = !0;\n    for (e of j) this[e] = new Y(e, !0, void 0, this);\n  }\n  setupFromArray(e) {\n    let t;\n    for (t of G) this[t] = $[t];\n    for (t of X) this[t] = $[t];\n    for (t of W) this[t] = $[t];\n    for (t of j) this[t] = new Y(t, !1, void 0, this);\n    this.setupGlobalFilters(e, void 0, H);\n  }\n  setupFromObject(e) {\n    let t;\n    for (t of (H.ifd0 = H.ifd0 || H.image, H.ifd1 = H.ifd1 || H.thumbnail, Object.assign(this, e), G)) this[t] = Z(e[t], $[t]);\n    for (t of X) this[t] = Z(e[t], $[t]);\n    for (t of W) this[t] = Z(e[t], $[t]);\n    for (t of z) this[t] = new Y(t, $[t], e[t], this);\n    for (t of H) this[t] = new Y(t, $[t], e[t], this.tiff);\n    this.setupGlobalFilters(e.pick, e.skip, H, j), !0 === e.tiff ? this.batchEnableWithBool(H, !0) : !1 === e.tiff ? this.batchEnableWithUserValue(H, e) : Array.isArray(e.tiff) ? this.setupGlobalFilters(e.tiff, void 0, H) : \"object\" == typeof e.tiff && this.setupGlobalFilters(e.tiff.pick, e.tiff.skip, H);\n  }\n  batchEnableWithBool(e, t) {\n    for (let i of e) this[i].enabled = t;\n  }\n  batchEnableWithUserValue(e, t) {\n    for (let i of e) {\n      let e = t[i];\n      this[i].enabled = !1 !== e && void 0 !== e;\n    }\n  }\n  setupGlobalFilters(e, t, i, n = i) {\n    if (e && e.length) {\n      for (let e of n) this[e].enabled = !1;\n      let t = Q(e, i);\n      for (let [e, i] of t) ee(this[e].pick, i), this[e].enabled = !0;\n    } else if (t && t.length) {\n      let e = Q(t, i);\n      for (let [t, i] of e) ee(this[t].skip, i);\n    }\n  }\n  filterNestedSegmentTags() {\n    let {\n      ifd0: e,\n      exif: t,\n      xmp: i,\n      iptc: n,\n      icc: s\n    } = this;\n    this.makerNote ? t.deps.add(37500) : t.skip.add(37500), this.userComment ? t.deps.add(37510) : t.skip.add(37510), i.enabled || e.skip.add(700), n.enabled || e.skip.add(33723), s.enabled || e.skip.add(34675);\n  }\n  traverseTiffDependencyTree() {\n    let {\n      ifd0: e,\n      exif: t,\n      gps: i,\n      interop: n\n    } = this;\n    n.needed && (t.deps.add(40965), e.deps.add(40965)), t.needed && e.deps.add(34665), i.needed && e.deps.add(34853), this.tiff.enabled = H.some(e => !0 === this[e].enabled) || this.makerNote || this.userComment;\n    for (let e of H) this[e].finalizeFilters();\n  }\n  get onlyTiff() {\n    return !V.map(e => this[e].enabled).some(e => !0 === e) && this.tiff.enabled;\n  }\n  checkLoadedPlugins() {\n    for (let e of z) this[e].enabled && !T.has(e) && P(\"segment parser\", e);\n  }\n}\nfunction Q(e, t) {\n  let i,\n    n,\n    s,\n    r,\n    a = [];\n  for (s of t) {\n    for (r of (i = E.get(s), n = [], i)) (e.includes(r[0]) || e.includes(r[1])) && n.push(r[0]);\n    n.length && a.push([s, n]);\n  }\n  return a;\n}\nfunction Z(e, t) {\n  return void 0 !== e ? e : void 0 !== t ? t : void 0;\n}\nfunction ee(e, t) {\n  for (let i of t) e.add(i);\n}\nc(q, \"default\", $);\nclass te {\n  constructor(e) {\n    c(this, \"parsers\", {}), c(this, \"output\", {}), c(this, \"errors\", []), c(this, \"pushToErrors\", e => this.errors.push(e)), this.options = q.useCached(e);\n  }\n  read(e) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      _this.file = yield D(e, _this.options);\n    })();\n  }\n  setup() {\n    if (this.fileParser) return;\n    let {\n        file: e\n      } = this,\n      t = e.getUint16(0);\n    for (let [i, n] of w) if (n.canHandle(e, t)) return this.fileParser = new n(this.options, this.file, this.parsers), e[i] = !0;\n    this.file.close && this.file.close(), g(\"Unknown file format\");\n  }\n  parse() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      let {\n        output: e,\n        errors: t\n      } = _this2;\n      return _this2.setup(), _this2.options.silentErrors ? (yield _this2.executeParsers().catch(_this2.pushToErrors), t.push(..._this2.fileParser.errors)) : yield _this2.executeParsers(), _this2.file.close && _this2.file.close(), _this2.options.silentErrors && t.length > 0 && (e.errors = t), f(e);\n    })();\n  }\n  executeParsers() {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      let {\n        output: e\n      } = _this3;\n      yield _this3.fileParser.parse();\n      let t = Object.values(_this3.parsers).map(/*#__PURE__*/function () {\n        var _ref2 = _asyncToGenerator(function* (t) {\n          let i = yield t.parse();\n          t.assignToOutput(e, i);\n        });\n        return function (_x1) {\n          return _ref2.apply(this, arguments);\n        };\n      }());\n      _this3.options.silentErrors && (t = t.map(e => e.catch(_this3.pushToErrors))), yield Promise.all(t);\n    })();\n  }\n  extractThumbnail() {\n    var _this4 = this;\n    return _asyncToGenerator(function* () {\n      _this4.setup();\n      let {\n          options: e,\n          file: t\n        } = _this4,\n        i = T.get(\"tiff\", e);\n      var n;\n      if (t.tiff ? n = {\n        start: 0,\n        type: \"tiff\"\n      } : t.jpeg && (n = yield _this4.fileParser.getOrFindSegment(\"tiff\")), void 0 === n) return;\n      let s = yield _this4.fileParser.ensureSegmentChunk(n),\n        r = _this4.parsers.tiff = new i(s, e, t),\n        a = yield r.extractThumbnail();\n      return t.close && t.close(), a;\n    })();\n  }\n}\nfunction ie(_x10, _x11) {\n  return _ie.apply(this, arguments);\n}\nfunction _ie() {\n  _ie = _asyncToGenerator(function* (e, t) {\n    let i = new te(t);\n    return yield i.read(e), i.parse();\n  });\n  return _ie.apply(this, arguments);\n}\nvar ne = Object.freeze({\n  __proto__: null,\n  parse: ie,\n  Exifr: te,\n  fileParsers: w,\n  segmentParsers: T,\n  fileReaders: A,\n  tagKeys: E,\n  tagValues: B,\n  tagRevivers: N,\n  createDictionary: U,\n  extendDictionary: F,\n  fetchUrlAsArrayBuffer: M,\n  readBlobAsArrayBuffer: R,\n  chunkedProps: G,\n  otherSegments: V,\n  segments: z,\n  tiffBlocks: H,\n  segmentsAndBlocks: j,\n  tiffExtractables: W,\n  inheritables: K,\n  allFormatters: X,\n  Options: q\n});\nclass se {\n  constructor(e, t, i) {\n    var _this5 = this;\n    c(this, \"errors\", []), c(this, \"ensureSegmentChunk\", /*#__PURE__*/function () {\n      var _ref3 = _asyncToGenerator(function* (e) {\n        let t = e.start,\n          i = e.size || 65536;\n        if (_this5.file.chunked) {\n          if (_this5.file.available(t, i)) e.chunk = _this5.file.subarray(t, i);else try {\n            e.chunk = yield _this5.file.readChunk(t, i);\n          } catch (t) {\n            g(`Couldn't read segment: ${JSON.stringify(e)}. ${t.message}`);\n          }\n        } else _this5.file.byteLength > t + i ? e.chunk = _this5.file.subarray(t, i) : void 0 === e.size ? e.chunk = _this5.file.subarray(t) : g(\"Segment unreachable: \" + JSON.stringify(e));\n        return e.chunk;\n      });\n      return function (_x12) {\n        return _ref3.apply(this, arguments);\n      };\n    }()), this.extendOptions && this.extendOptions(e), this.options = e, this.file = t, this.parsers = i;\n  }\n  injectSegment(e, t) {\n    this.options[e].enabled && this.createParser(e, t);\n  }\n  createParser(e, t) {\n    let i = new (T.get(e))(t, this.options, this.file);\n    return this.parsers[e] = i;\n  }\n  createParsers(e) {\n    for (let t of e) {\n      let {\n          type: e,\n          chunk: i\n        } = t,\n        n = this.options[e];\n      if (n && n.enabled) {\n        let t = this.parsers[e];\n        t && t.append || t || this.createParser(e, i);\n      }\n    }\n  }\n  readSegments(e) {\n    var _this6 = this;\n    return _asyncToGenerator(function* () {\n      let t = e.map(_this6.ensureSegmentChunk);\n      yield Promise.all(t);\n    })();\n  }\n}\nclass re {\n  static findPosition(e, t) {\n    let i = e.getUint16(t + 2) + 2,\n      n = \"function\" == typeof this.headerLength ? this.headerLength(e, t, i) : this.headerLength,\n      s = t + n,\n      r = i - n;\n    return {\n      offset: t,\n      length: i,\n      headerLength: n,\n      start: s,\n      size: r,\n      end: s + r\n    };\n  }\n  static parse(e, t = {}) {\n    return new this(e, new q({\n      [this.type]: t\n    }), e).parse();\n  }\n  normalizeInput(e) {\n    return e instanceof I ? e : new I(e);\n  }\n  constructor(e, t = {}, i) {\n    c(this, \"errors\", []), c(this, \"raw\", new Map()), c(this, \"handleError\", e => {\n      if (!this.options.silentErrors) throw e;\n      this.errors.push(e.message);\n    }), this.chunk = this.normalizeInput(e), this.file = i, this.type = this.constructor.type, this.globalOptions = this.options = t, this.localOptions = t[this.type], this.canTranslate = this.localOptions && this.localOptions.translate;\n  }\n  translate() {\n    this.canTranslate && (this.translated = this.translateBlock(this.raw, this.type));\n  }\n  get output() {\n    return this.translated ? this.translated : this.raw ? Object.fromEntries(this.raw) : void 0;\n  }\n  translateBlock(e, t) {\n    let i = N.get(t),\n      n = B.get(t),\n      s = E.get(t),\n      r = this.options[t],\n      a = r.reviveValues && !!i,\n      o = r.translateValues && !!n,\n      l = r.translateKeys && !!s,\n      h = {};\n    for (let [t, r] of e) a && i.has(t) ? r = i.get(t)(r) : o && n.has(t) && (r = this.translateValue(r, n.get(t))), l && s.has(t) && (t = s.get(t) || t), h[t] = r;\n    return h;\n  }\n  translateValue(e, t) {\n    return t[e] || t.DEFAULT || e;\n  }\n  assignToOutput(e, t) {\n    this.assignObjectToOutput(e, this.constructor.type, t);\n  }\n  assignObjectToOutput(e, t, i) {\n    if (this.globalOptions.mergeOutput) return Object.assign(e, i);\n    e[t] ? Object.assign(e[t], i) : e[t] = i;\n  }\n}\nc(re, \"headerLength\", 4), c(re, \"type\", void 0), c(re, \"multiSegment\", !1), c(re, \"canHandle\", () => !1);\nfunction ae(e) {\n  return 192 === e || 194 === e || 196 === e || 219 === e || 221 === e || 218 === e || 254 === e;\n}\nfunction oe(e) {\n  return e >= 224 && e <= 239;\n}\nfunction le(e, t, i) {\n  for (let [n, s] of T) if (s.canHandle(e, t, i)) return n;\n}\nclass he extends se {\n  constructor(...e) {\n    super(...e), c(this, \"appSegments\", []), c(this, \"jpegSegments\", []), c(this, \"unknownSegments\", []);\n  }\n  static canHandle(e, t) {\n    return 65496 === t;\n  }\n  parse() {\n    var _this7 = this;\n    return _asyncToGenerator(function* () {\n      yield _this7.findAppSegments(), yield _this7.readSegments(_this7.appSegments), _this7.mergeMultiSegments(), _this7.createParsers(_this7.mergedAppSegments || _this7.appSegments);\n    })();\n  }\n  setupSegmentFinderArgs(e) {\n    !0 === e ? (this.findAll = !0, this.wanted = new Set(T.keyList())) : (e = void 0 === e ? T.keyList().filter(e => this.options[e].enabled) : e.filter(e => this.options[e].enabled && T.has(e)), this.findAll = !1, this.remaining = new Set(e), this.wanted = new Set(e)), this.unfinishedMultiSegment = !1;\n  }\n  findAppSegments() {\n    var _this8 = this;\n    return _asyncToGenerator(function* (e = 0, t) {\n      _this8.setupSegmentFinderArgs(t);\n      let {\n        file: i,\n        findAll: n,\n        wanted: s,\n        remaining: r\n      } = _this8;\n      if (!n && _this8.file.chunked && (n = Array.from(s).some(e => {\n        let t = T.get(e),\n          i = _this8.options[e];\n        return t.multiSegment && i.multiSegment;\n      }), n && (yield _this8.file.readWhole())), e = _this8.findAppSegmentsInRange(e, i.byteLength), !_this8.options.onlyTiff && i.chunked) {\n        let t = !1;\n        for (; r.size > 0 && !t && (i.canReadNextChunk || _this8.unfinishedMultiSegment);) {\n          let {\n              nextChunkOffset: n\n            } = i,\n            s = _this8.appSegments.some(e => !_this8.file.available(e.offset || e.start, e.length || e.size));\n          if (t = e > n && !s ? !(yield i.readNextChunk(e)) : !(yield i.readNextChunk(n)), void 0 === (e = _this8.findAppSegmentsInRange(e, i.byteLength))) return;\n        }\n      }\n    }).apply(this, arguments);\n  }\n  findAppSegmentsInRange(e, t) {\n    t -= 2;\n    let i,\n      n,\n      s,\n      r,\n      a,\n      o,\n      {\n        file: l,\n        findAll: h,\n        wanted: u,\n        remaining: c,\n        options: f\n      } = this;\n    for (; e < t; e++) if (255 === l.getUint8(e)) if (i = l.getUint8(e + 1), oe(i)) {\n      if (n = l.getUint16(e + 2), s = le(l, e, n), s && u.has(s) && (r = T.get(s), a = r.findPosition(l, e), o = f[s], a.type = s, this.appSegments.push(a), !h && (r.multiSegment && o.multiSegment ? (this.unfinishedMultiSegment = a.chunkNumber < a.chunkCount, this.unfinishedMultiSegment || c.delete(s)) : c.delete(s), 0 === c.size))) break;\n      f.recordUnknownSegments && (a = re.findPosition(l, e), a.marker = i, this.unknownSegments.push(a)), e += n + 1;\n    } else if (ae(i)) {\n      if (n = l.getUint16(e + 2), 218 === i && !1 !== f.stopAfterSos) return;\n      f.recordJpegSegments && this.jpegSegments.push({\n        offset: e,\n        length: n,\n        marker: i\n      }), e += n + 1;\n    }\n    return e;\n  }\n  mergeMultiSegments() {\n    if (!this.appSegments.some(e => e.multiSegment)) return;\n    let e = function (e, t) {\n      let i,\n        n,\n        s,\n        r = new Map();\n      for (let a = 0; a < e.length; a++) i = e[a], n = i[t], r.has(n) ? s = r.get(n) : r.set(n, s = []), s.push(i);\n      return Array.from(r);\n    }(this.appSegments, \"type\");\n    this.mergedAppSegments = e.map(([e, t]) => {\n      let i = T.get(e, this.options);\n      if (i.handleMultiSegments) {\n        return {\n          type: e,\n          chunk: i.handleMultiSegments(t)\n        };\n      }\n      return t[0];\n    });\n  }\n  getSegment(e) {\n    return this.appSegments.find(t => t.type === e);\n  }\n  getOrFindSegment(e) {\n    var _this9 = this;\n    return _asyncToGenerator(function* () {\n      let t = _this9.getSegment(e);\n      return void 0 === t && (yield _this9.findAppSegments(0, [e]), t = _this9.getSegment(e)), t;\n    })();\n  }\n}\nc(he, \"type\", \"jpeg\"), w.set(\"jpeg\", he);\nconst ue = [void 0, 1, 1, 2, 4, 8, 1, 1, 2, 4, 8, 4, 8, 4];\nclass ce extends re {\n  parseHeader() {\n    var e = this.chunk.getUint16();\n    18761 === e ? this.le = !0 : 19789 === e && (this.le = !1), this.chunk.le = this.le, this.headerParsed = !0;\n  }\n  parseTags(e, t, i = new Map()) {\n    let {\n      pick: n,\n      skip: s\n    } = this.options[t];\n    n = new Set(n);\n    let r = n.size > 0,\n      a = 0 === s.size,\n      o = this.chunk.getUint16(e);\n    e += 2;\n    for (let l = 0; l < o; l++) {\n      let o = this.chunk.getUint16(e);\n      if (r) {\n        if (n.has(o) && (i.set(o, this.parseTag(e, o, t)), n.delete(o), 0 === n.size)) break;\n      } else !a && s.has(o) || i.set(o, this.parseTag(e, o, t));\n      e += 12;\n    }\n    return i;\n  }\n  parseTag(e, t, i) {\n    let {\n        chunk: n\n      } = this,\n      s = n.getUint16(e + 2),\n      r = n.getUint32(e + 4),\n      a = ue[s];\n    if (a * r <= 4 ? e += 8 : e = n.getUint32(e + 8), (s < 1 || s > 13) && g(`Invalid TIFF value type. block: ${i.toUpperCase()}, tag: ${t.toString(16)}, type: ${s}, offset ${e}`), e > n.byteLength && g(`Invalid TIFF value offset. block: ${i.toUpperCase()}, tag: ${t.toString(16)}, type: ${s}, offset ${e} is outside of chunk size ${n.byteLength}`), 1 === s) return n.getUint8Array(e, r);\n    if (2 === s) return m(n.getString(e, r));\n    if (7 === s) return n.getUint8Array(e, r);\n    if (1 === r) return this.parseTagValue(s, e);\n    {\n      let t = new (function (e) {\n          switch (e) {\n            case 1:\n              return Uint8Array;\n            case 3:\n              return Uint16Array;\n            case 4:\n              return Uint32Array;\n            case 5:\n              return Array;\n            case 6:\n              return Int8Array;\n            case 8:\n              return Int16Array;\n            case 9:\n              return Int32Array;\n            case 10:\n              return Array;\n            case 11:\n              return Float32Array;\n            case 12:\n              return Float64Array;\n            default:\n              return Array;\n          }\n        }(s))(r),\n        i = a;\n      for (let n = 0; n < r; n++) t[n] = this.parseTagValue(s, e), e += i;\n      return t;\n    }\n  }\n  parseTagValue(e, t) {\n    let {\n      chunk: i\n    } = this;\n    switch (e) {\n      case 1:\n        return i.getUint8(t);\n      case 3:\n        return i.getUint16(t);\n      case 4:\n        return i.getUint32(t);\n      case 5:\n        return i.getUint32(t) / i.getUint32(t + 4);\n      case 6:\n        return i.getInt8(t);\n      case 8:\n        return i.getInt16(t);\n      case 9:\n        return i.getInt32(t);\n      case 10:\n        return i.getInt32(t) / i.getInt32(t + 4);\n      case 11:\n        return i.getFloat(t);\n      case 12:\n        return i.getDouble(t);\n      case 13:\n        return i.getUint32(t);\n      default:\n        g(`Invalid tiff type ${e}`);\n    }\n  }\n}\nclass fe extends ce {\n  static canHandle(e, t) {\n    return 225 === e.getUint8(t + 1) && 1165519206 === e.getUint32(t + 4) && 0 === e.getUint16(t + 8);\n  }\n  parse() {\n    var _this0 = this;\n    return _asyncToGenerator(function* () {\n      _this0.parseHeader();\n      let {\n        options: e\n      } = _this0;\n      return e.ifd0.enabled && (yield _this0.parseIfd0Block()), e.exif.enabled && (yield _this0.safeParse(\"parseExifBlock\")), e.gps.enabled && (yield _this0.safeParse(\"parseGpsBlock\")), e.interop.enabled && (yield _this0.safeParse(\"parseInteropBlock\")), e.ifd1.enabled && (yield _this0.safeParse(\"parseThumbnailBlock\")), _this0.createOutput();\n    })();\n  }\n  safeParse(e) {\n    let t = this[e]();\n    return void 0 !== t.catch && (t = t.catch(this.handleError)), t;\n  }\n  findIfd0Offset() {\n    void 0 === this.ifd0Offset && (this.ifd0Offset = this.chunk.getUint32(4));\n  }\n  findIfd1Offset() {\n    if (void 0 === this.ifd1Offset) {\n      this.findIfd0Offset();\n      let e = this.chunk.getUint16(this.ifd0Offset),\n        t = this.ifd0Offset + 2 + 12 * e;\n      this.ifd1Offset = this.chunk.getUint32(t);\n    }\n  }\n  parseBlock(e, t) {\n    let i = new Map();\n    return this[t] = i, this.parseTags(e, t, i), i;\n  }\n  parseIfd0Block() {\n    var _this1 = this;\n    return _asyncToGenerator(function* () {\n      if (_this1.ifd0) return;\n      let {\n        file: e\n      } = _this1;\n      _this1.findIfd0Offset(), _this1.ifd0Offset < 8 && g(\"Malformed EXIF data\"), !e.chunked && _this1.ifd0Offset > e.byteLength && g(`IFD0 offset points to outside of file.\\nthis.ifd0Offset: ${_this1.ifd0Offset}, file.byteLength: ${e.byteLength}`), e.tiff && (yield e.ensureChunk(_this1.ifd0Offset, S(_this1.options)));\n      let t = _this1.parseBlock(_this1.ifd0Offset, \"ifd0\");\n      return 0 !== t.size ? (_this1.exifOffset = t.get(34665), _this1.interopOffset = t.get(40965), _this1.gpsOffset = t.get(34853), _this1.xmp = t.get(700), _this1.iptc = t.get(33723), _this1.icc = t.get(34675), _this1.options.sanitize && (t.delete(34665), t.delete(40965), t.delete(34853), t.delete(700), t.delete(33723), t.delete(34675)), t) : void 0;\n    })();\n  }\n  parseExifBlock() {\n    var _this10 = this;\n    return _asyncToGenerator(function* () {\n      if (_this10.exif) return;\n      if (_this10.ifd0 || (yield _this10.parseIfd0Block()), void 0 === _this10.exifOffset) return;\n      _this10.file.tiff && (yield _this10.file.ensureChunk(_this10.exifOffset, S(_this10.options)));\n      let e = _this10.parseBlock(_this10.exifOffset, \"exif\");\n      return _this10.interopOffset || (_this10.interopOffset = e.get(40965)), _this10.makerNote = e.get(37500), _this10.userComment = e.get(37510), _this10.options.sanitize && (e.delete(40965), e.delete(37500), e.delete(37510)), _this10.unpack(e, 41728), _this10.unpack(e, 41729), e;\n    })();\n  }\n  unpack(e, t) {\n    let i = e.get(t);\n    i && 1 === i.length && e.set(t, i[0]);\n  }\n  parseGpsBlock() {\n    var _this11 = this;\n    return _asyncToGenerator(function* () {\n      if (_this11.gps) return;\n      if (_this11.ifd0 || (yield _this11.parseIfd0Block()), void 0 === _this11.gpsOffset) return;\n      let e = _this11.parseBlock(_this11.gpsOffset, \"gps\");\n      return e && e.has(2) && e.has(4) && (e.set(\"latitude\", de(...e.get(2), e.get(1))), e.set(\"longitude\", de(...e.get(4), e.get(3)))), e;\n    })();\n  }\n  parseInteropBlock() {\n    var _this12 = this;\n    return _asyncToGenerator(function* () {\n      if (!_this12.interop && (_this12.ifd0 || (yield _this12.parseIfd0Block()), void 0 !== _this12.interopOffset || _this12.exif || (yield _this12.parseExifBlock()), void 0 !== _this12.interopOffset)) return _this12.parseBlock(_this12.interopOffset, \"interop\");\n    })();\n  }\n  parseThumbnailBlock() {\n    var _this13 = this;\n    return _asyncToGenerator(function* (e = !1) {\n      if (!_this13.ifd1 && !_this13.ifd1Parsed && (!_this13.options.mergeOutput || e)) return _this13.findIfd1Offset(), _this13.ifd1Offset > 0 && (_this13.parseBlock(_this13.ifd1Offset, \"ifd1\"), _this13.ifd1Parsed = !0), _this13.ifd1;\n    }).apply(this, arguments);\n  }\n  extractThumbnail() {\n    var _this14 = this;\n    return _asyncToGenerator(function* () {\n      if (_this14.headerParsed || _this14.parseHeader(), _this14.ifd1Parsed || (yield _this14.parseThumbnailBlock(!0)), void 0 === _this14.ifd1) return;\n      let e = _this14.ifd1.get(513),\n        t = _this14.ifd1.get(514);\n      return _this14.chunk.getUint8Array(e, t);\n    })();\n  }\n  get image() {\n    return this.ifd0;\n  }\n  get thumbnail() {\n    return this.ifd1;\n  }\n  createOutput() {\n    let e,\n      t,\n      i,\n      n = {};\n    for (t of H) if (e = this[t], !p(e)) if (i = this.canTranslate ? this.translateBlock(e, t) : Object.fromEntries(e), this.options.mergeOutput) {\n      if (\"ifd1\" === t) continue;\n      Object.assign(n, i);\n    } else n[t] = i;\n    return this.makerNote && (n.makerNote = this.makerNote), this.userComment && (n.userComment = this.userComment), n;\n  }\n  assignToOutput(e, t) {\n    if (this.globalOptions.mergeOutput) Object.assign(e, t);else for (let [i, n] of Object.entries(t)) this.assignObjectToOutput(e, i, n);\n  }\n}\nfunction de(e, t, i, n) {\n  var s = e + t / 60 + i / 3600;\n  return \"S\" !== n && \"W\" !== n || (s *= -1), s;\n}\nc(fe, \"type\", \"tiff\"), c(fe, \"headerLength\", 10), T.set(\"tiff\", fe);\nvar pe = Object.freeze({\n  __proto__: null,\n  default: ne,\n  Exifr: te,\n  fileParsers: w,\n  segmentParsers: T,\n  fileReaders: A,\n  tagKeys: E,\n  tagValues: B,\n  tagRevivers: N,\n  createDictionary: U,\n  extendDictionary: F,\n  fetchUrlAsArrayBuffer: M,\n  readBlobAsArrayBuffer: R,\n  chunkedProps: G,\n  otherSegments: V,\n  segments: z,\n  tiffBlocks: H,\n  segmentsAndBlocks: j,\n  tiffExtractables: W,\n  inheritables: K,\n  allFormatters: X,\n  Options: q,\n  parse: ie\n});\nconst ge = {\n    ifd0: !1,\n    ifd1: !1,\n    exif: !1,\n    gps: !1,\n    interop: !1,\n    sanitize: !1,\n    reviveValues: !0,\n    translateKeys: !1,\n    translateValues: !1,\n    mergeOutput: !1\n  },\n  me = Object.assign({}, ge, {\n    firstChunkSize: 4e4,\n    gps: [1, 2, 3, 4]\n  });\nfunction Se(_x13) {\n  return _Se.apply(this, arguments);\n}\nfunction _Se() {\n  _Se = _asyncToGenerator(function* (e) {\n    let t = new te(me);\n    yield t.read(e);\n    let i = yield t.parse();\n    if (i && i.gps) {\n      let {\n        latitude: e,\n        longitude: t\n      } = i.gps;\n      return {\n        latitude: e,\n        longitude: t\n      };\n    }\n  });\n  return _Se.apply(this, arguments);\n}\nconst Ce = Object.assign({}, ge, {\n  tiff: !1,\n  ifd1: !0,\n  mergeOutput: !1\n});\nfunction ye(_x14) {\n  return _ye.apply(this, arguments);\n}\nfunction _ye() {\n  _ye = _asyncToGenerator(function* (e) {\n    let t = new te(Ce);\n    yield t.read(e);\n    let i = yield t.extractThumbnail();\n    return i && a ? s.from(i) : i;\n  });\n  return _ye.apply(this, arguments);\n}\nfunction be(_x15) {\n  return _be.apply(this, arguments);\n}\nfunction _be() {\n  _be = _asyncToGenerator(function* (e) {\n    let t = yield this.thumbnail(e);\n    if (void 0 !== t) {\n      let e = new Blob([t]);\n      return URL.createObjectURL(e);\n    }\n  });\n  return _be.apply(this, arguments);\n}\nconst Ie = Object.assign({}, ge, {\n  firstChunkSize: 4e4,\n  ifd0: [274]\n});\nfunction Pe(_x16) {\n  return _Pe.apply(this, arguments);\n}\nfunction _Pe() {\n  _Pe = _asyncToGenerator(function* (e) {\n    let t = new te(Ie);\n    yield t.read(e);\n    let i = yield t.parse();\n    if (i && i.ifd0) return i.ifd0[274];\n  });\n  return _Pe.apply(this, arguments);\n}\nconst ke = Object.freeze({\n  1: {\n    dimensionSwapped: !1,\n    scaleX: 1,\n    scaleY: 1,\n    deg: 0,\n    rad: 0\n  },\n  2: {\n    dimensionSwapped: !1,\n    scaleX: -1,\n    scaleY: 1,\n    deg: 0,\n    rad: 0\n  },\n  3: {\n    dimensionSwapped: !1,\n    scaleX: 1,\n    scaleY: 1,\n    deg: 180,\n    rad: 180 * Math.PI / 180\n  },\n  4: {\n    dimensionSwapped: !1,\n    scaleX: -1,\n    scaleY: 1,\n    deg: 180,\n    rad: 180 * Math.PI / 180\n  },\n  5: {\n    dimensionSwapped: !0,\n    scaleX: 1,\n    scaleY: -1,\n    deg: 90,\n    rad: 90 * Math.PI / 180\n  },\n  6: {\n    dimensionSwapped: !0,\n    scaleX: 1,\n    scaleY: 1,\n    deg: 90,\n    rad: 90 * Math.PI / 180\n  },\n  7: {\n    dimensionSwapped: !0,\n    scaleX: 1,\n    scaleY: -1,\n    deg: 270,\n    rad: 270 * Math.PI / 180\n  },\n  8: {\n    dimensionSwapped: !0,\n    scaleX: 1,\n    scaleY: 1,\n    deg: 270,\n    rad: 270 * Math.PI / 180\n  }\n});\nlet we = !0,\n  Te = !0;\nif (\"object\" == typeof navigator) {\n  let e = navigator.userAgent;\n  if (e.includes(\"iPad\") || e.includes(\"iPhone\")) {\n    let t = e.match(/OS (\\d+)_(\\d+)/);\n    if (t) {\n      let [, e, i] = t,\n        n = Number(e) + .1 * Number(i);\n      we = n < 13.4, Te = !1;\n    }\n  } else if (e.includes(\"OS X 10\")) {\n    let [, t] = e.match(/OS X 10[_.](\\d+)/);\n    we = Te = Number(t) < 15;\n  }\n  if (e.includes(\"Chrome/\")) {\n    let [, t] = e.match(/Chrome\\/(\\d+)/);\n    we = Te = Number(t) < 81;\n  } else if (e.includes(\"Firefox/\")) {\n    let [, t] = e.match(/Firefox\\/(\\d+)/);\n    we = Te = Number(t) < 77;\n  }\n}\nfunction Ae(_x17) {\n  return _Ae.apply(this, arguments);\n}\nfunction _Ae() {\n  _Ae = _asyncToGenerator(function* (e) {\n    let t = yield Pe(e);\n    return Object.assign({\n      canvas: we,\n      css: Te\n    }, ke[t]);\n  });\n  return _Ae.apply(this, arguments);\n}\nclass De extends I {\n  constructor(...e) {\n    super(...e), c(this, \"ranges\", new Oe()), 0 !== this.byteLength && this.ranges.add(0, this.byteLength);\n  }\n  _tryExtend(e, t, i) {\n    if (0 === e && 0 === this.byteLength && i) {\n      let e = new DataView(i.buffer || i, i.byteOffset, i.byteLength);\n      this._swapDataView(e);\n    } else {\n      let i = e + t;\n      if (i > this.byteLength) {\n        let {\n          dataView: e\n        } = this._extend(i);\n        this._swapDataView(e);\n      }\n    }\n  }\n  _extend(e) {\n    let t;\n    t = a ? s.allocUnsafe(e) : new Uint8Array(e);\n    let i = new DataView(t.buffer, t.byteOffset, t.byteLength);\n    return t.set(new Uint8Array(this.buffer, this.byteOffset, this.byteLength), 0), {\n      uintView: t,\n      dataView: i\n    };\n  }\n  subarray(e, t, i = !1) {\n    return t = t || this._lengthToEnd(e), i && this._tryExtend(e, t), this.ranges.add(e, t), super.subarray(e, t);\n  }\n  set(e, t, i = !1) {\n    i && this._tryExtend(t, e.byteLength, e);\n    let n = super.set(e, t);\n    return this.ranges.add(t, n.byteLength), n;\n  }\n  ensureChunk(e, t) {\n    var _this15 = this;\n    return _asyncToGenerator(function* () {\n      _this15.chunked && (_this15.ranges.available(e, t) || (yield _this15.readChunk(e, t)));\n    })();\n  }\n  available(e, t) {\n    return this.ranges.available(e, t);\n  }\n}\nclass Oe {\n  constructor() {\n    c(this, \"list\", []);\n  }\n  get length() {\n    return this.list.length;\n  }\n  add(e, t, i = 0) {\n    let n = e + t,\n      s = this.list.filter(t => xe(e, t.offset, n) || xe(e, t.end, n));\n    if (s.length > 0) {\n      e = Math.min(e, ...s.map(e => e.offset)), n = Math.max(n, ...s.map(e => e.end)), t = n - e;\n      let i = s.shift();\n      i.offset = e, i.length = t, i.end = n, this.list = this.list.filter(e => !s.includes(e));\n    } else this.list.push({\n      offset: e,\n      length: t,\n      end: n\n    });\n  }\n  available(e, t) {\n    let i = e + t;\n    return this.list.some(t => t.offset <= e && i <= t.end);\n  }\n}\nfunction xe(e, t, i) {\n  return e <= t && t <= i;\n}\nclass ve extends De {\n  constructor(e, t) {\n    super(0), c(this, \"chunksRead\", 0), this.input = e, this.options = t;\n  }\n  readWhole() {\n    var _this16 = this;\n    return _asyncToGenerator(function* () {\n      _this16.chunked = !1, yield _this16.readChunk(_this16.nextChunkOffset);\n    })();\n  }\n  readChunked() {\n    var _this17 = this;\n    return _asyncToGenerator(function* () {\n      _this17.chunked = !0, yield _this17.readChunk(0, _this17.options.firstChunkSize);\n    })();\n  }\n  readNextChunk() {\n    var _this18 = this;\n    return _asyncToGenerator(function* (e = _this18.nextChunkOffset) {\n      if (_this18.fullyRead) return _this18.chunksRead++, !1;\n      let t = _this18.options.chunkSize,\n        i = yield _this18.readChunk(e, t);\n      return !!i && i.byteLength === t;\n    }).apply(this, arguments);\n  }\n  readChunk(e, t) {\n    var _this19 = this;\n    return _asyncToGenerator(function* () {\n      if (_this19.chunksRead++, 0 !== (t = _this19.safeWrapAddress(e, t))) return _this19._readChunk(e, t);\n    })();\n  }\n  safeWrapAddress(e, t) {\n    return void 0 !== this.size && e + t > this.size ? Math.max(0, this.size - e) : t;\n  }\n  get nextChunkOffset() {\n    if (0 !== this.ranges.list.length) return this.ranges.list[0].length;\n  }\n  get canReadNextChunk() {\n    return this.chunksRead < this.options.chunkLimit;\n  }\n  get fullyRead() {\n    return void 0 !== this.size && this.nextChunkOffset === this.size;\n  }\n  read() {\n    return this.options.chunked ? this.readChunked() : this.readWhole();\n  }\n  close() {}\n}\nA.set(\"blob\", class extends ve {\n  readWhole() {\n    var _this20 = this;\n    return _asyncToGenerator(function* () {\n      _this20.chunked = !1;\n      let e = yield R(_this20.input);\n      _this20._swapArrayBuffer(e);\n    })();\n  }\n  readChunked() {\n    return this.chunked = !0, this.size = this.input.size, super.readChunked();\n  }\n  _readChunk(e, t) {\n    var _this21 = this;\n    return _asyncToGenerator(function* () {\n      let i = t ? e + t : void 0,\n        n = _this21.input.slice(e, i),\n        s = yield R(n);\n      return _this21.set(s, e, !0);\n    })();\n  }\n});\nvar Me = Object.freeze({\n  __proto__: null,\n  default: pe,\n  Exifr: te,\n  fileParsers: w,\n  segmentParsers: T,\n  fileReaders: A,\n  tagKeys: E,\n  tagValues: B,\n  tagRevivers: N,\n  createDictionary: U,\n  extendDictionary: F,\n  fetchUrlAsArrayBuffer: M,\n  readBlobAsArrayBuffer: R,\n  chunkedProps: G,\n  otherSegments: V,\n  segments: z,\n  tiffBlocks: H,\n  segmentsAndBlocks: j,\n  tiffExtractables: W,\n  inheritables: K,\n  allFormatters: X,\n  Options: q,\n  parse: ie,\n  gpsOnlyOptions: me,\n  gps: Se,\n  thumbnailOnlyOptions: Ce,\n  thumbnail: ye,\n  thumbnailUrl: be,\n  orientationOnlyOptions: Ie,\n  orientation: Pe,\n  rotations: ke,\n  get rotateCanvas() {\n    return we;\n  },\n  get rotateCss() {\n    return Te;\n  },\n  rotation: Ae\n});\nA.set(\"url\", class extends ve {\n  readWhole() {\n    var _this22 = this;\n    return _asyncToGenerator(function* () {\n      _this22.chunked = !1;\n      let e = yield M(_this22.input);\n      e instanceof ArrayBuffer ? _this22._swapArrayBuffer(e) : e instanceof Uint8Array && _this22._swapBuffer(e);\n    })();\n  }\n  _readChunk(e, t) {\n    var _this23 = this;\n    return _asyncToGenerator(function* () {\n      let i = t ? e + t - 1 : void 0,\n        n = _this23.options.httpHeaders || {};\n      (e || i) && (n.range = `bytes=${[e, i].join(\"-\")}`);\n      let s = yield h(_this23.input, {\n          headers: n\n        }),\n        r = yield s.arrayBuffer(),\n        a = r.byteLength;\n      if (416 !== s.status) return a !== t && (_this23.size = e + a), _this23.set(r, e, !0);\n    })();\n  }\n});\nI.prototype.getUint64 = function (e) {\n  let t = this.getUint32(e),\n    i = this.getUint32(e + 4);\n  return t < 1048575 ? t << 32 | i : void 0 !== typeof r ? (console.warn(\"Using BigInt because of type 64uint but JS can only handle 53b numbers.\"), r(t) << r(32) | r(i)) : void g(\"Trying to read 64b value but JS can only handle 53b numbers.\");\n};\nclass Re extends se {\n  parseBoxes(e = 0) {\n    let t = [];\n    for (; e < this.file.byteLength - 4;) {\n      let i = this.parseBoxHead(e);\n      if (t.push(i), 0 === i.length) break;\n      e += i.length;\n    }\n    return t;\n  }\n  parseSubBoxes(e) {\n    e.boxes = this.parseBoxes(e.start);\n  }\n  findBox(e, t) {\n    return void 0 === e.boxes && this.parseSubBoxes(e), e.boxes.find(e => e.kind === t);\n  }\n  parseBoxHead(e) {\n    let t = this.file.getUint32(e),\n      i = this.file.getString(e + 4, 4),\n      n = e + 8;\n    return 1 === t && (t = this.file.getUint64(e + 8), n += 8), {\n      offset: e,\n      length: t,\n      kind: i,\n      start: n\n    };\n  }\n  parseBoxFullHead(e) {\n    if (void 0 !== e.version) return;\n    let t = this.file.getUint32(e.start);\n    e.version = t >> 24, e.start += 4;\n  }\n}\nclass Le extends Re {\n  static canHandle(e, t) {\n    if (0 !== t) return !1;\n    let i = e.getUint16(2);\n    if (i > 50) return !1;\n    let n = 16,\n      s = [];\n    for (; n < i;) s.push(e.getString(n, 4)), n += 4;\n    return s.includes(this.type);\n  }\n  parse() {\n    var _this24 = this;\n    return _asyncToGenerator(function* () {\n      let e = _this24.file.getUint32(0),\n        t = _this24.parseBoxHead(e);\n      for (; \"meta\" !== t.kind;) e += t.length, yield _this24.file.ensureChunk(e, 16), t = _this24.parseBoxHead(e);\n      yield _this24.file.ensureChunk(t.offset, t.length), _this24.parseBoxFullHead(t), _this24.parseSubBoxes(t), _this24.options.icc.enabled && (yield _this24.findIcc(t)), _this24.options.tiff.enabled && (yield _this24.findExif(t));\n    })();\n  }\n  registerSegment(e, t, i) {\n    var _this25 = this;\n    return _asyncToGenerator(function* () {\n      yield _this25.file.ensureChunk(t, i);\n      let n = _this25.file.subarray(t, i);\n      _this25.createParser(e, n);\n    })();\n  }\n  findIcc(e) {\n    var _this26 = this;\n    return _asyncToGenerator(function* () {\n      let t = _this26.findBox(e, \"iprp\");\n      if (void 0 === t) return;\n      let i = _this26.findBox(t, \"ipco\");\n      if (void 0 === i) return;\n      let n = _this26.findBox(i, \"colr\");\n      void 0 !== n && (yield _this26.registerSegment(\"icc\", n.offset + 12, n.length));\n    })();\n  }\n  findExif(e) {\n    var _this27 = this;\n    return _asyncToGenerator(function* () {\n      let t = _this27.findBox(e, \"iinf\");\n      if (void 0 === t) return;\n      let i = _this27.findBox(e, \"iloc\");\n      if (void 0 === i) return;\n      let n = _this27.findExifLocIdInIinf(t),\n        s = _this27.findExtentInIloc(i, n);\n      if (void 0 === s) return;\n      let [r, a] = s;\n      yield _this27.file.ensureChunk(r, a);\n      let o = 4 + _this27.file.getUint32(r);\n      r += o, a -= o, yield _this27.registerSegment(\"tiff\", r, a);\n    })();\n  }\n  findExifLocIdInIinf(e) {\n    this.parseBoxFullHead(e);\n    let t,\n      i,\n      n,\n      s,\n      r = e.start,\n      a = this.file.getUint16(r);\n    for (r += 2; a--;) {\n      if (t = this.parseBoxHead(r), this.parseBoxFullHead(t), i = t.start, t.version >= 2 && (n = 3 === t.version ? 4 : 2, s = this.file.getString(i + n + 2, 4), \"Exif\" === s)) return this.file.getUintBytes(i, n);\n      r += t.length;\n    }\n  }\n  get8bits(e) {\n    let t = this.file.getUint8(e);\n    return [t >> 4, 15 & t];\n  }\n  findExtentInIloc(e, t) {\n    this.parseBoxFullHead(e);\n    let i = e.start,\n      [n, s] = this.get8bits(i++),\n      [r, a] = this.get8bits(i++),\n      o = 2 === e.version ? 4 : 2,\n      l = 1 === e.version || 2 === e.version ? 2 : 0,\n      h = a + n + s,\n      u = 2 === e.version ? 4 : 2,\n      c = this.file.getUintBytes(i, u);\n    for (i += u; c--;) {\n      let e = this.file.getUintBytes(i, o);\n      i += o + l + 2 + r;\n      let u = this.file.getUint16(i);\n      if (i += 2, e === t) return u > 1 && console.warn(\"ILOC box has more than one extent but we're only processing one\\nPlease create an issue at https://github.com/MikeKovarik/exifr with this file\"), [this.file.getUintBytes(i + a, n), this.file.getUintBytes(i + a + n, s)];\n      i += u * h;\n    }\n  }\n}\nclass Ue extends Le {}\nc(Ue, \"type\", \"heic\");\nclass Fe extends Le {}\nc(Fe, \"type\", \"avif\"), w.set(\"heic\", Ue), w.set(\"avif\", Fe), U(E, [\"ifd0\", \"ifd1\"], [[256, \"ImageWidth\"], [257, \"ImageHeight\"], [258, \"BitsPerSample\"], [259, \"Compression\"], [262, \"PhotometricInterpretation\"], [270, \"ImageDescription\"], [271, \"Make\"], [272, \"Model\"], [273, \"StripOffsets\"], [274, \"Orientation\"], [277, \"SamplesPerPixel\"], [278, \"RowsPerStrip\"], [279, \"StripByteCounts\"], [282, \"XResolution\"], [283, \"YResolution\"], [284, \"PlanarConfiguration\"], [296, \"ResolutionUnit\"], [301, \"TransferFunction\"], [305, \"Software\"], [306, \"ModifyDate\"], [315, \"Artist\"], [316, \"HostComputer\"], [317, \"Predictor\"], [318, \"WhitePoint\"], [319, \"PrimaryChromaticities\"], [513, \"ThumbnailOffset\"], [514, \"ThumbnailLength\"], [529, \"YCbCrCoefficients\"], [530, \"YCbCrSubSampling\"], [531, \"YCbCrPositioning\"], [532, \"ReferenceBlackWhite\"], [700, \"ApplicationNotes\"], [33432, \"Copyright\"], [33723, \"IPTC\"], [34665, \"ExifIFD\"], [34675, \"ICC\"], [34853, \"GpsIFD\"], [330, \"SubIFD\"], [40965, \"InteropIFD\"], [40091, \"XPTitle\"], [40092, \"XPComment\"], [40093, \"XPAuthor\"], [40094, \"XPKeywords\"], [40095, \"XPSubject\"]]), U(E, \"exif\", [[33434, \"ExposureTime\"], [33437, \"FNumber\"], [34850, \"ExposureProgram\"], [34852, \"SpectralSensitivity\"], [34855, \"ISO\"], [34858, \"TimeZoneOffset\"], [34859, \"SelfTimerMode\"], [34864, \"SensitivityType\"], [34865, \"StandardOutputSensitivity\"], [34866, \"RecommendedExposureIndex\"], [34867, \"ISOSpeed\"], [34868, \"ISOSpeedLatitudeyyy\"], [34869, \"ISOSpeedLatitudezzz\"], [36864, \"ExifVersion\"], [36867, \"DateTimeOriginal\"], [36868, \"CreateDate\"], [36873, \"GooglePlusUploadCode\"], [36880, \"OffsetTime\"], [36881, \"OffsetTimeOriginal\"], [36882, \"OffsetTimeDigitized\"], [37121, \"ComponentsConfiguration\"], [37122, \"CompressedBitsPerPixel\"], [37377, \"ShutterSpeedValue\"], [37378, \"ApertureValue\"], [37379, \"BrightnessValue\"], [37380, \"ExposureCompensation\"], [37381, \"MaxApertureValue\"], [37382, \"SubjectDistance\"], [37383, \"MeteringMode\"], [37384, \"LightSource\"], [37385, \"Flash\"], [37386, \"FocalLength\"], [37393, \"ImageNumber\"], [37394, \"SecurityClassification\"], [37395, \"ImageHistory\"], [37396, \"SubjectArea\"], [37500, \"MakerNote\"], [37510, \"UserComment\"], [37520, \"SubSecTime\"], [37521, \"SubSecTimeOriginal\"], [37522, \"SubSecTimeDigitized\"], [37888, \"AmbientTemperature\"], [37889, \"Humidity\"], [37890, \"Pressure\"], [37891, \"WaterDepth\"], [37892, \"Acceleration\"], [37893, \"CameraElevationAngle\"], [40960, \"FlashpixVersion\"], [40961, \"ColorSpace\"], [40962, \"ExifImageWidth\"], [40963, \"ExifImageHeight\"], [40964, \"RelatedSoundFile\"], [41483, \"FlashEnergy\"], [41486, \"FocalPlaneXResolution\"], [41487, \"FocalPlaneYResolution\"], [41488, \"FocalPlaneResolutionUnit\"], [41492, \"SubjectLocation\"], [41493, \"ExposureIndex\"], [41495, \"SensingMethod\"], [41728, \"FileSource\"], [41729, \"SceneType\"], [41730, \"CFAPattern\"], [41985, \"CustomRendered\"], [41986, \"ExposureMode\"], [41987, \"WhiteBalance\"], [41988, \"DigitalZoomRatio\"], [41989, \"FocalLengthIn35mmFormat\"], [41990, \"SceneCaptureType\"], [41991, \"GainControl\"], [41992, \"Contrast\"], [41993, \"Saturation\"], [41994, \"Sharpness\"], [41996, \"SubjectDistanceRange\"], [42016, \"ImageUniqueID\"], [42032, \"OwnerName\"], [42033, \"SerialNumber\"], [42034, \"LensInfo\"], [42035, \"LensMake\"], [42036, \"LensModel\"], [42037, \"LensSerialNumber\"], [42080, \"CompositeImage\"], [42081, \"CompositeImageCount\"], [42082, \"CompositeImageExposureTimes\"], [42240, \"Gamma\"], [59932, \"Padding\"], [59933, \"OffsetSchema\"], [65e3, \"OwnerName\"], [65001, \"SerialNumber\"], [65002, \"Lens\"], [65100, \"RawFile\"], [65101, \"Converter\"], [65102, \"WhiteBalance\"], [65105, \"Exposure\"], [65106, \"Shadows\"], [65107, \"Brightness\"], [65108, \"Contrast\"], [65109, \"Saturation\"], [65110, \"Sharpness\"], [65111, \"Smoothness\"], [65112, \"MoireFilter\"], [40965, \"InteropIFD\"]]), U(E, \"gps\", [[0, \"GPSVersionID\"], [1, \"GPSLatitudeRef\"], [2, \"GPSLatitude\"], [3, \"GPSLongitudeRef\"], [4, \"GPSLongitude\"], [5, \"GPSAltitudeRef\"], [6, \"GPSAltitude\"], [7, \"GPSTimeStamp\"], [8, \"GPSSatellites\"], [9, \"GPSStatus\"], [10, \"GPSMeasureMode\"], [11, \"GPSDOP\"], [12, \"GPSSpeedRef\"], [13, \"GPSSpeed\"], [14, \"GPSTrackRef\"], [15, \"GPSTrack\"], [16, \"GPSImgDirectionRef\"], [17, \"GPSImgDirection\"], [18, \"GPSMapDatum\"], [19, \"GPSDestLatitudeRef\"], [20, \"GPSDestLatitude\"], [21, \"GPSDestLongitudeRef\"], [22, \"GPSDestLongitude\"], [23, \"GPSDestBearingRef\"], [24, \"GPSDestBearing\"], [25, \"GPSDestDistanceRef\"], [26, \"GPSDestDistance\"], [27, \"GPSProcessingMethod\"], [28, \"GPSAreaInformation\"], [29, \"GPSDateStamp\"], [30, \"GPSDifferential\"], [31, \"GPSHPositioningError\"]]), U(B, [\"ifd0\", \"ifd1\"], [[274, {\n  1: \"Horizontal (normal)\",\n  2: \"Mirror horizontal\",\n  3: \"Rotate 180\",\n  4: \"Mirror vertical\",\n  5: \"Mirror horizontal and rotate 270 CW\",\n  6: \"Rotate 90 CW\",\n  7: \"Mirror horizontal and rotate 90 CW\",\n  8: \"Rotate 270 CW\"\n}], [296, {\n  1: \"None\",\n  2: \"inches\",\n  3: \"cm\"\n}]]);\nlet Ee = U(B, \"exif\", [[34850, {\n  0: \"Not defined\",\n  1: \"Manual\",\n  2: \"Normal program\",\n  3: \"Aperture priority\",\n  4: \"Shutter priority\",\n  5: \"Creative program\",\n  6: \"Action program\",\n  7: \"Portrait mode\",\n  8: \"Landscape mode\"\n}], [37121, {\n  0: \"-\",\n  1: \"Y\",\n  2: \"Cb\",\n  3: \"Cr\",\n  4: \"R\",\n  5: \"G\",\n  6: \"B\"\n}], [37383, {\n  0: \"Unknown\",\n  1: \"Average\",\n  2: \"CenterWeightedAverage\",\n  3: \"Spot\",\n  4: \"MultiSpot\",\n  5: \"Pattern\",\n  6: \"Partial\",\n  255: \"Other\"\n}], [37384, {\n  0: \"Unknown\",\n  1: \"Daylight\",\n  2: \"Fluorescent\",\n  3: \"Tungsten (incandescent light)\",\n  4: \"Flash\",\n  9: \"Fine weather\",\n  10: \"Cloudy weather\",\n  11: \"Shade\",\n  12: \"Daylight fluorescent (D 5700 - 7100K)\",\n  13: \"Day white fluorescent (N 4600 - 5400K)\",\n  14: \"Cool white fluorescent (W 3900 - 4500K)\",\n  15: \"White fluorescent (WW 3200 - 3700K)\",\n  17: \"Standard light A\",\n  18: \"Standard light B\",\n  19: \"Standard light C\",\n  20: \"D55\",\n  21: \"D65\",\n  22: \"D75\",\n  23: \"D50\",\n  24: \"ISO studio tungsten\",\n  255: \"Other\"\n}], [37385, {\n  0: \"Flash did not fire\",\n  1: \"Flash fired\",\n  5: \"Strobe return light not detected\",\n  7: \"Strobe return light detected\",\n  9: \"Flash fired, compulsory flash mode\",\n  13: \"Flash fired, compulsory flash mode, return light not detected\",\n  15: \"Flash fired, compulsory flash mode, return light detected\",\n  16: \"Flash did not fire, compulsory flash mode\",\n  24: \"Flash did not fire, auto mode\",\n  25: \"Flash fired, auto mode\",\n  29: \"Flash fired, auto mode, return light not detected\",\n  31: \"Flash fired, auto mode, return light detected\",\n  32: \"No flash function\",\n  65: \"Flash fired, red-eye reduction mode\",\n  69: \"Flash fired, red-eye reduction mode, return light not detected\",\n  71: \"Flash fired, red-eye reduction mode, return light detected\",\n  73: \"Flash fired, compulsory flash mode, red-eye reduction mode\",\n  77: \"Flash fired, compulsory flash mode, red-eye reduction mode, return light not detected\",\n  79: \"Flash fired, compulsory flash mode, red-eye reduction mode, return light detected\",\n  89: \"Flash fired, auto mode, red-eye reduction mode\",\n  93: \"Flash fired, auto mode, return light not detected, red-eye reduction mode\",\n  95: \"Flash fired, auto mode, return light detected, red-eye reduction mode\"\n}], [41495, {\n  1: \"Not defined\",\n  2: \"One-chip color area sensor\",\n  3: \"Two-chip color area sensor\",\n  4: \"Three-chip color area sensor\",\n  5: \"Color sequential area sensor\",\n  7: \"Trilinear sensor\",\n  8: \"Color sequential linear sensor\"\n}], [41728, {\n  1: \"Film Scanner\",\n  2: \"Reflection Print Scanner\",\n  3: \"Digital Camera\"\n}], [41729, {\n  1: \"Directly photographed\"\n}], [41985, {\n  0: \"Normal\",\n  1: \"Custom\",\n  2: \"HDR (no original saved)\",\n  3: \"HDR (original saved)\",\n  4: \"Original (for HDR)\",\n  6: \"Panorama\",\n  7: \"Portrait HDR\",\n  8: \"Portrait\"\n}], [41986, {\n  0: \"Auto\",\n  1: \"Manual\",\n  2: \"Auto bracket\"\n}], [41987, {\n  0: \"Auto\",\n  1: \"Manual\"\n}], [41990, {\n  0: \"Standard\",\n  1: \"Landscape\",\n  2: \"Portrait\",\n  3: \"Night\",\n  4: \"Other\"\n}], [41991, {\n  0: \"None\",\n  1: \"Low gain up\",\n  2: \"High gain up\",\n  3: \"Low gain down\",\n  4: \"High gain down\"\n}], [41996, {\n  0: \"Unknown\",\n  1: \"Macro\",\n  2: \"Close\",\n  3: \"Distant\"\n}], [42080, {\n  0: \"Unknown\",\n  1: \"Not a Composite Image\",\n  2: \"General Composite Image\",\n  3: \"Composite Image Captured While Shooting\"\n}]]);\nconst Be = {\n  1: \"No absolute unit of measurement\",\n  2: \"Inch\",\n  3: \"Centimeter\"\n};\nEe.set(37392, Be), Ee.set(41488, Be);\nconst Ne = {\n  0: \"Normal\",\n  1: \"Low\",\n  2: \"High\"\n};\nfunction Ge(e) {\n  return \"object\" == typeof e && void 0 !== e.length ? e[0] : e;\n}\nfunction Ve(e) {\n  let t = Array.from(e).slice(1);\n  return t[1] > 15 && (t = t.map(e => String.fromCharCode(e))), \"0\" !== t[2] && 0 !== t[2] || t.pop(), t.join(\".\");\n}\nfunction ze(e) {\n  if (\"string\" == typeof e) {\n    var [t, i, n, s, r, a] = e.trim().split(/[-: ]/g).map(Number),\n      o = new Date(t, i - 1, n);\n    return Number.isNaN(s) || Number.isNaN(r) || Number.isNaN(a) || (o.setHours(s), o.setMinutes(r), o.setSeconds(a)), Number.isNaN(+o) ? e : o;\n  }\n}\nfunction He(e) {\n  if (\"string\" == typeof e) return e;\n  let t = [];\n  if (0 === e[1] && 0 === e[e.length - 1]) for (let i = 0; i < e.length; i += 2) t.push(je(e[i + 1], e[i]));else for (let i = 0; i < e.length; i += 2) t.push(je(e[i], e[i + 1]));\n  return m(String.fromCodePoint(...t));\n}\nfunction je(e, t) {\n  return e << 8 | t;\n}\nEe.set(41992, Ne), Ee.set(41993, Ne), Ee.set(41994, Ne), U(N, [\"ifd0\", \"ifd1\"], [[50827, function (e) {\n  return \"string\" != typeof e ? b(e) : e;\n}], [306, ze], [40091, He], [40092, He], [40093, He], [40094, He], [40095, He]]), U(N, \"exif\", [[40960, Ve], [36864, Ve], [36867, ze], [36868, ze], [40962, Ge], [40963, Ge]]), U(N, \"gps\", [[0, e => Array.from(e).join(\".\")], [7, e => Array.from(e).join(\":\")]]);\nclass We extends re {\n  static canHandle(e, t) {\n    return 225 === e.getUint8(t + 1) && 1752462448 === e.getUint32(t + 4) && \"http://ns.adobe.com/\" === e.getString(t + 4, \"http://ns.adobe.com/\".length);\n  }\n  static headerLength(e, t) {\n    return \"http://ns.adobe.com/xmp/extension/\" === e.getString(t + 4, \"http://ns.adobe.com/xmp/extension/\".length) ? 79 : 4 + \"http://ns.adobe.com/xap/1.0/\".length + 1;\n  }\n  static findPosition(e, t) {\n    let i = super.findPosition(e, t);\n    return i.multiSegment = i.extended = 79 === i.headerLength, i.multiSegment ? (i.chunkCount = e.getUint8(t + 72), i.chunkNumber = e.getUint8(t + 76), 0 !== e.getUint8(t + 77) && i.chunkNumber++) : (i.chunkCount = 1 / 0, i.chunkNumber = -1), i;\n  }\n  static handleMultiSegments(e) {\n    return e.map(e => e.chunk.getString()).join(\"\");\n  }\n  normalizeInput(e) {\n    return \"string\" == typeof e ? e : I.from(e).getString();\n  }\n  parse(e = this.chunk) {\n    if (!this.localOptions.parse) return e;\n    e = function (e) {\n      let t = {},\n        i = {};\n      for (let e of Ze) t[e] = [], i[e] = 0;\n      return e.replace(et, (e, n, s) => {\n        if (\"<\" === n) {\n          let n = ++i[s];\n          return t[s].push(n), `${e}#${n}`;\n        }\n        return `${e}#${t[s].pop()}`;\n      });\n    }(e);\n    let t = Xe.findAll(e, \"rdf\", \"Description\");\n    0 === t.length && t.push(new Xe(\"rdf\", \"Description\", void 0, e));\n    let i,\n      n = {};\n    for (let e of t) for (let t of e.properties) i = Je(t.ns, n), _e(t, i);\n    return function (e) {\n      let t;\n      for (let i in e) t = e[i] = f(e[i]), void 0 === t && delete e[i];\n      return f(e);\n    }(n);\n  }\n  assignToOutput(e, t) {\n    if (this.localOptions.parse) for (let [i, n] of Object.entries(t)) switch (i) {\n      case \"tiff\":\n        this.assignObjectToOutput(e, \"ifd0\", n);\n        break;\n      case \"exif\":\n        this.assignObjectToOutput(e, \"exif\", n);\n        break;\n      case \"xmlns\":\n        break;\n      default:\n        this.assignObjectToOutput(e, i, n);\n    } else e.xmp = t;\n  }\n}\nc(We, \"type\", \"xmp\"), c(We, \"multiSegment\", !0), T.set(\"xmp\", We);\nclass Ke {\n  static findAll(e) {\n    return qe(e, /([a-zA-Z0-9-]+):([a-zA-Z0-9-]+)=(\"[^\"]*\"|'[^']*')/gm).map(Ke.unpackMatch);\n  }\n  static unpackMatch(e) {\n    let t = e[1],\n      i = e[2],\n      n = e[3].slice(1, -1);\n    return n = Qe(n), new Ke(t, i, n);\n  }\n  constructor(e, t, i) {\n    this.ns = e, this.name = t, this.value = i;\n  }\n  serialize() {\n    return this.value;\n  }\n}\nclass Xe {\n  static findAll(e, t, i) {\n    if (void 0 !== t || void 0 !== i) {\n      t = t || \"[\\\\w\\\\d-]+\", i = i || \"[\\\\w\\\\d-]+\";\n      var n = new RegExp(`<(${t}):(${i})(#\\\\d+)?((\\\\s+?[\\\\w\\\\d-:]+=(\"[^\"]*\"|'[^']*'))*\\\\s*)(\\\\/>|>([\\\\s\\\\S]*?)<\\\\/\\\\1:\\\\2\\\\3>)`, \"gm\");\n    } else n = /<([\\w\\d-]+):([\\w\\d-]+)(#\\d+)?((\\s+?[\\w\\d-:]+=(\"[^\"]*\"|'[^']*'))*\\s*)(\\/>|>([\\s\\S]*?)<\\/\\1:\\2\\3>)/gm;\n    return qe(e, n).map(Xe.unpackMatch);\n  }\n  static unpackMatch(e) {\n    let t = e[1],\n      i = e[2],\n      n = e[4],\n      s = e[8];\n    return new Xe(t, i, n, s);\n  }\n  constructor(e, t, i, n) {\n    this.ns = e, this.name = t, this.attrString = i, this.innerXml = n, this.attrs = Ke.findAll(i), this.children = Xe.findAll(n), this.value = 0 === this.children.length ? Qe(n) : void 0, this.properties = [...this.attrs, ...this.children];\n  }\n  get isPrimitive() {\n    return void 0 !== this.value && 0 === this.attrs.length && 0 === this.children.length;\n  }\n  get isListContainer() {\n    return 1 === this.children.length && this.children[0].isList;\n  }\n  get isList() {\n    let {\n      ns: e,\n      name: t\n    } = this;\n    return \"rdf\" === e && (\"Seq\" === t || \"Bag\" === t || \"Alt\" === t);\n  }\n  get isListItem() {\n    return \"rdf\" === this.ns && \"li\" === this.name;\n  }\n  serialize() {\n    if (0 === this.properties.length && void 0 === this.value) return;\n    if (this.isPrimitive) return this.value;\n    if (this.isListContainer) return this.children[0].serialize();\n    if (this.isList) return $e(this.children.map(Ye));\n    if (this.isListItem && 1 === this.children.length && 0 === this.attrs.length) return this.children[0].serialize();\n    let e = {};\n    for (let t of this.properties) _e(t, e);\n    return void 0 !== this.value && (e.value = this.value), f(e);\n  }\n}\nfunction _e(e, t) {\n  let i = e.serialize();\n  void 0 !== i && (t[e.name] = i);\n}\nvar Ye = e => e.serialize(),\n  $e = e => 1 === e.length ? e[0] : e,\n  Je = (e, t) => t[e] ? t[e] : t[e] = {};\nfunction qe(e, t) {\n  let i,\n    n = [];\n  if (!e) return n;\n  for (; null !== (i = t.exec(e));) n.push(i);\n  return n;\n}\nfunction Qe(e) {\n  if (function (e) {\n    return null == e || \"null\" === e || \"undefined\" === e || \"\" === e || \"\" === e.trim();\n  }(e)) return;\n  let t = Number(e);\n  if (!Number.isNaN(t)) return t;\n  let i = e.toLowerCase();\n  return \"true\" === i || \"false\" !== i && e.trim();\n}\nconst Ze = [\"rdf:li\", \"rdf:Seq\", \"rdf:Bag\", \"rdf:Alt\", \"rdf:Description\"],\n  et = new RegExp(`(<|\\\\/)(${Ze.join(\"|\")})`, \"g\");\nvar tt = Object.freeze({\n  __proto__: null,\n  default: Me,\n  Exifr: te,\n  fileParsers: w,\n  segmentParsers: T,\n  fileReaders: A,\n  tagKeys: E,\n  tagValues: B,\n  tagRevivers: N,\n  createDictionary: U,\n  extendDictionary: F,\n  fetchUrlAsArrayBuffer: M,\n  readBlobAsArrayBuffer: R,\n  chunkedProps: G,\n  otherSegments: V,\n  segments: z,\n  tiffBlocks: H,\n  segmentsAndBlocks: j,\n  tiffExtractables: W,\n  inheritables: K,\n  allFormatters: X,\n  Options: q,\n  parse: ie,\n  gpsOnlyOptions: me,\n  gps: Se,\n  thumbnailOnlyOptions: Ce,\n  thumbnail: ye,\n  thumbnailUrl: be,\n  orientationOnlyOptions: Ie,\n  orientation: Pe,\n  rotations: ke,\n  get rotateCanvas() {\n    return we;\n  },\n  get rotateCss() {\n    return Te;\n  },\n  rotation: Ae\n});\nconst it = [\"xmp\", \"icc\", \"iptc\", \"tiff\"],\n  nt = () => {};\nfunction st(_x18, _x19, _x20) {\n  return _st.apply(this, arguments);\n}\nfunction _st() {\n  _st = _asyncToGenerator(function* (e, t, i) {\n    let n = new q(t);\n    n.chunked = !1, void 0 === i && \"string\" == typeof e && (i = function (e) {\n      let t = e.toLowerCase().split(\".\").pop();\n      if (function (e) {\n        return \"exif\" === e || \"tiff\" === e || \"tif\" === e;\n      }(t)) return \"tiff\";\n      if (it.includes(t)) return t;\n    }(e));\n    let s = yield D(e, n);\n    if (i) {\n      if (it.includes(i)) return rt(i, s, n);\n      g(\"Invalid segment type\");\n    } else {\n      if (function (e) {\n        let t = e.getString(0, 50).trim();\n        return t.includes(\"<?xpacket\") || t.includes(\"<x:\");\n      }(s)) return rt(\"xmp\", s, n);\n      for (let [e] of T) {\n        if (!it.includes(e)) continue;\n        let t = yield rt(e, s, n).catch(nt);\n        if (t) return t;\n      }\n      g(\"Unknown file format\");\n    }\n  });\n  return _st.apply(this, arguments);\n}\nfunction rt(_x21, _x22, _x23) {\n  return _rt.apply(this, arguments);\n}\nfunction _rt() {\n  _rt = _asyncToGenerator(function* (e, t, i) {\n    let n = i[e];\n    return n.enabled = !0, n.parse = !0, T.get(e).parse(t, n);\n  });\n  return _rt.apply(this, arguments);\n}\nlet at = l(\"fs\", e => e.promises);\nA.set(\"fs\", class extends ve {\n  readWhole() {\n    var _this28 = this;\n    return _asyncToGenerator(function* () {\n      _this28.chunked = !1, _this28.fs = yield at;\n      let e = yield _this28.fs.readFile(_this28.input);\n      _this28._swapBuffer(e);\n    })();\n  }\n  readChunked() {\n    var _this29 = this;\n    return _asyncToGenerator(function* () {\n      _this29.chunked = !0, _this29.fs = yield at, yield _this29.open(), yield _this29.readChunk(0, _this29.options.firstChunkSize);\n    })();\n  }\n  open() {\n    var _this30 = this;\n    return _asyncToGenerator(function* () {\n      void 0 === _this30.fh && (_this30.fh = yield _this30.fs.open(_this30.input, \"r\"), _this30.size = (yield _this30.fh.stat(_this30.input)).size);\n    })();\n  }\n  _readChunk(e, t) {\n    var _this31 = this;\n    return _asyncToGenerator(function* () {\n      void 0 === _this31.fh && (yield _this31.open()), e + t > _this31.size && (t = _this31.size - e);\n      var i = _this31.subarray(e, t, !0);\n      return yield _this31.fh.read(i.dataView, 0, t, e), i;\n    })();\n  }\n  close() {\n    var _this32 = this;\n    return _asyncToGenerator(function* () {\n      if (_this32.fh) {\n        let e = _this32.fh;\n        _this32.fh = void 0, yield e.close();\n      }\n    })();\n  }\n});\nA.set(\"base64\", class extends ve {\n  constructor(...e) {\n    super(...e), this.input = this.input.replace(/^data:([^;]+);base64,/gim, \"\"), this.size = this.input.length / 4 * 3, this.input.endsWith(\"==\") ? this.size -= 2 : this.input.endsWith(\"=\") && (this.size -= 1);\n  }\n  _readChunk(e, t) {\n    var _this33 = this;\n    return _asyncToGenerator(function* () {\n      let i,\n        n,\n        r = _this33.input;\n      void 0 === e ? (e = 0, i = 0, n = 0) : (i = 4 * Math.floor(e / 3), n = e - i / 4 * 3), void 0 === t && (t = _this33.size);\n      let o = e + t,\n        l = i + 4 * Math.ceil(o / 3);\n      r = r.slice(i, l);\n      let h = Math.min(t, _this33.size - e);\n      if (a) {\n        let t = s.from(r, \"base64\").slice(n, n + h);\n        return _this33.set(t, e, !0);\n      }\n      {\n        let t = _this33.subarray(e, h, !0),\n          i = atob(r),\n          s = t.toUint8();\n        for (let e = 0; e < h; e++) s[e] = i.charCodeAt(n + e);\n        return t;\n      }\n    })();\n  }\n});\nclass ot extends se {\n  static canHandle(e, t) {\n    return 18761 === t || 19789 === t;\n  }\n  extendOptions(e) {\n    let {\n      ifd0: t,\n      xmp: i,\n      iptc: n,\n      icc: s\n    } = e;\n    i.enabled && t.deps.add(700), n.enabled && t.deps.add(33723), s.enabled && t.deps.add(34675), t.finalizeFilters();\n  }\n  parse() {\n    var _this34 = this;\n    return _asyncToGenerator(function* () {\n      let {\n        tiff: e,\n        xmp: t,\n        iptc: i,\n        icc: n\n      } = _this34.options;\n      if (e.enabled || t.enabled || i.enabled || n.enabled) {\n        let e = Math.max(S(_this34.options), _this34.options.chunkSize);\n        yield _this34.file.ensureChunk(0, e), _this34.createParser(\"tiff\", _this34.file), _this34.parsers.tiff.parseHeader(), yield _this34.parsers.tiff.parseIfd0Block(), _this34.adaptTiffPropAsSegment(\"xmp\"), _this34.adaptTiffPropAsSegment(\"iptc\"), _this34.adaptTiffPropAsSegment(\"icc\");\n      }\n    })();\n  }\n  adaptTiffPropAsSegment(e) {\n    if (this.parsers.tiff[e]) {\n      let t = this.parsers.tiff[e];\n      this.injectSegment(e, t);\n    }\n  }\n}\nc(ot, \"type\", \"tiff\"), w.set(\"tiff\", ot);\nlet lt = l(\"zlib\");\nconst ht = [\"ihdr\", \"iccp\", \"text\", \"itxt\", \"exif\"];\nclass ut extends se {\n  constructor(...e) {\n    super(...e), c(this, \"catchError\", e => this.errors.push(e)), c(this, \"metaChunks\", []), c(this, \"unknownChunks\", []);\n  }\n  static canHandle(e, t) {\n    return 35152 === t && 2303741511 === e.getUint32(0) && 218765834 === e.getUint32(4);\n  }\n  parse() {\n    var _this35 = this;\n    return _asyncToGenerator(function* () {\n      let {\n        file: e\n      } = _this35;\n      yield _this35.findPngChunksInRange(\"PNG\\r\\n\u001a\\n\".length, e.byteLength), yield _this35.readSegments(_this35.metaChunks), _this35.findIhdr(), _this35.parseTextChunks(), yield _this35.findExif().catch(_this35.catchError), yield _this35.findXmp().catch(_this35.catchError), yield _this35.findIcc().catch(_this35.catchError);\n    })();\n  }\n  findPngChunksInRange(e, t) {\n    var _this36 = this;\n    return _asyncToGenerator(function* () {\n      let {\n        file: i\n      } = _this36;\n      for (; e < t;) {\n        let t = i.getUint32(e),\n          n = i.getUint32(e + 4),\n          s = i.getString(e + 4, 4).toLowerCase(),\n          r = t + 4 + 4 + 4,\n          a = {\n            type: s,\n            offset: e,\n            length: r,\n            start: e + 4 + 4,\n            size: t,\n            marker: n\n          };\n        ht.includes(s) ? _this36.metaChunks.push(a) : _this36.unknownChunks.push(a), e += r;\n      }\n    })();\n  }\n  parseTextChunks() {\n    let e = this.metaChunks.filter(e => \"text\" === e.type);\n    for (let t of e) {\n      let [e, i] = this.file.getString(t.start, t.size).split(\"\\0\");\n      this.injectKeyValToIhdr(e, i);\n    }\n  }\n  injectKeyValToIhdr(e, t) {\n    let i = this.parsers.ihdr;\n    i && i.raw.set(e, t);\n  }\n  findIhdr() {\n    let e = this.metaChunks.find(e => \"ihdr\" === e.type);\n    e && !1 !== this.options.ihdr.enabled && this.createParser(\"ihdr\", e.chunk);\n  }\n  findExif() {\n    var _this37 = this;\n    return _asyncToGenerator(function* () {\n      let e = _this37.metaChunks.find(e => \"exif\" === e.type);\n      e && _this37.injectSegment(\"tiff\", e.chunk);\n    })();\n  }\n  findXmp() {\n    var _this38 = this;\n    return _asyncToGenerator(function* () {\n      let e = _this38.metaChunks.filter(e => \"itxt\" === e.type);\n      for (let t of e) {\n        \"XML:com.adobe.xmp\" === t.chunk.getString(0, \"XML:com.adobe.xmp\".length) && _this38.injectSegment(\"xmp\", t.chunk);\n      }\n    })();\n  }\n  findIcc() {\n    var _this39 = this;\n    return _asyncToGenerator(function* () {\n      let e = _this39.metaChunks.find(e => \"iccp\" === e.type);\n      if (!e) return;\n      let {\n          chunk: t\n        } = e,\n        i = t.getUint8Array(0, 81),\n        s = 0;\n      for (; s < 80 && 0 !== i[s];) s++;\n      let r = s + 2,\n        a = t.getString(0, s);\n      if (_this39.injectKeyValToIhdr(\"ProfileName\", a), n) {\n        let e = yield lt,\n          i = t.getUint8Array(r);\n        i = e.inflateSync(i), _this39.injectSegment(\"icc\", i);\n      }\n    })();\n  }\n}\nc(ut, \"type\", \"png\"), w.set(\"png\", ut), U(E, \"interop\", [[1, \"InteropIndex\"], [2, \"InteropVersion\"], [4096, \"RelatedImageFileFormat\"], [4097, \"RelatedImageWidth\"], [4098, \"RelatedImageHeight\"]]), F(E, \"ifd0\", [[11, \"ProcessingSoftware\"], [254, \"SubfileType\"], [255, \"OldSubfileType\"], [263, \"Thresholding\"], [264, \"CellWidth\"], [265, \"CellLength\"], [266, \"FillOrder\"], [269, \"DocumentName\"], [280, \"MinSampleValue\"], [281, \"MaxSampleValue\"], [285, \"PageName\"], [286, \"XPosition\"], [287, \"YPosition\"], [290, \"GrayResponseUnit\"], [297, \"PageNumber\"], [321, \"HalftoneHints\"], [322, \"TileWidth\"], [323, \"TileLength\"], [332, \"InkSet\"], [337, \"TargetPrinter\"], [18246, \"Rating\"], [18249, \"RatingPercent\"], [33550, \"PixelScale\"], [34264, \"ModelTransform\"], [34377, \"PhotoshopSettings\"], [50706, \"DNGVersion\"], [50707, \"DNGBackwardVersion\"], [50708, \"UniqueCameraModel\"], [50709, \"LocalizedCameraModel\"], [50736, \"DNGLensInfo\"], [50739, \"ShadowScale\"], [50740, \"DNGPrivateData\"], [33920, \"IntergraphMatrix\"], [33922, \"ModelTiePoint\"], [34118, \"SEMInfo\"], [34735, \"GeoTiffDirectory\"], [34736, \"GeoTiffDoubleParams\"], [34737, \"GeoTiffAsciiParams\"], [50341, \"PrintIM\"], [50721, \"ColorMatrix1\"], [50722, \"ColorMatrix2\"], [50723, \"CameraCalibration1\"], [50724, \"CameraCalibration2\"], [50725, \"ReductionMatrix1\"], [50726, \"ReductionMatrix2\"], [50727, \"AnalogBalance\"], [50728, \"AsShotNeutral\"], [50729, \"AsShotWhiteXY\"], [50730, \"BaselineExposure\"], [50731, \"BaselineNoise\"], [50732, \"BaselineSharpness\"], [50734, \"LinearResponseLimit\"], [50735, \"CameraSerialNumber\"], [50741, \"MakerNoteSafety\"], [50778, \"CalibrationIlluminant1\"], [50779, \"CalibrationIlluminant2\"], [50781, \"RawDataUniqueID\"], [50827, \"OriginalRawFileName\"], [50828, \"OriginalRawFileData\"], [50831, \"AsShotICCProfile\"], [50832, \"AsShotPreProfileMatrix\"], [50833, \"CurrentICCProfile\"], [50834, \"CurrentPreProfileMatrix\"], [50879, \"ColorimetricReference\"], [50885, \"SRawType\"], [50898, \"PanasonicTitle\"], [50899, \"PanasonicTitle2\"], [50931, \"CameraCalibrationSig\"], [50932, \"ProfileCalibrationSig\"], [50933, \"ProfileIFD\"], [50934, \"AsShotProfileName\"], [50936, \"ProfileName\"], [50937, \"ProfileHueSatMapDims\"], [50938, \"ProfileHueSatMapData1\"], [50939, \"ProfileHueSatMapData2\"], [50940, \"ProfileToneCurve\"], [50941, \"ProfileEmbedPolicy\"], [50942, \"ProfileCopyright\"], [50964, \"ForwardMatrix1\"], [50965, \"ForwardMatrix2\"], [50966, \"PreviewApplicationName\"], [50967, \"PreviewApplicationVersion\"], [50968, \"PreviewSettingsName\"], [50969, \"PreviewSettingsDigest\"], [50970, \"PreviewColorSpace\"], [50971, \"PreviewDateTime\"], [50972, \"RawImageDigest\"], [50973, \"OriginalRawFileDigest\"], [50981, \"ProfileLookTableDims\"], [50982, \"ProfileLookTableData\"], [51043, \"TimeCodes\"], [51044, \"FrameRate\"], [51058, \"TStop\"], [51081, \"ReelName\"], [51089, \"OriginalDefaultFinalSize\"], [51090, \"OriginalBestQualitySize\"], [51091, \"OriginalDefaultCropSize\"], [51105, \"CameraLabel\"], [51107, \"ProfileHueSatMapEncoding\"], [51108, \"ProfileLookTableEncoding\"], [51109, \"BaselineExposureOffset\"], [51110, \"DefaultBlackRender\"], [51111, \"NewRawImageDigest\"], [51112, \"RawToPreviewGain\"]]);\nlet ct = [[273, \"StripOffsets\"], [279, \"StripByteCounts\"], [288, \"FreeOffsets\"], [289, \"FreeByteCounts\"], [291, \"GrayResponseCurve\"], [292, \"T4Options\"], [293, \"T6Options\"], [300, \"ColorResponseUnit\"], [320, \"ColorMap\"], [324, \"TileOffsets\"], [325, \"TileByteCounts\"], [326, \"BadFaxLines\"], [327, \"CleanFaxData\"], [328, \"ConsecutiveBadFaxLines\"], [330, \"SubIFD\"], [333, \"InkNames\"], [334, \"NumberofInks\"], [336, \"DotRange\"], [338, \"ExtraSamples\"], [339, \"SampleFormat\"], [340, \"SMinSampleValue\"], [341, \"SMaxSampleValue\"], [342, \"TransferRange\"], [343, \"ClipPath\"], [344, \"XClipPathUnits\"], [345, \"YClipPathUnits\"], [346, \"Indexed\"], [347, \"JPEGTables\"], [351, \"OPIProxy\"], [400, \"GlobalParametersIFD\"], [401, \"ProfileType\"], [402, \"FaxProfile\"], [403, \"CodingMethods\"], [404, \"VersionYear\"], [405, \"ModeNumber\"], [433, \"Decode\"], [434, \"DefaultImageColor\"], [435, \"T82Options\"], [437, \"JPEGTables\"], [512, \"JPEGProc\"], [515, \"JPEGRestartInterval\"], [517, \"JPEGLosslessPredictors\"], [518, \"JPEGPointTransforms\"], [519, \"JPEGQTables\"], [520, \"JPEGDCTables\"], [521, \"JPEGACTables\"], [559, \"StripRowCounts\"], [999, \"USPTOMiscellaneous\"], [18247, \"XP_DIP_XML\"], [18248, \"StitchInfo\"], [28672, \"SonyRawFileType\"], [28688, \"SonyToneCurve\"], [28721, \"VignettingCorrection\"], [28722, \"VignettingCorrParams\"], [28724, \"ChromaticAberrationCorrection\"], [28725, \"ChromaticAberrationCorrParams\"], [28726, \"DistortionCorrection\"], [28727, \"DistortionCorrParams\"], [29895, \"SonyCropTopLeft\"], [29896, \"SonyCropSize\"], [32781, \"ImageID\"], [32931, \"WangTag1\"], [32932, \"WangAnnotation\"], [32933, \"WangTag3\"], [32934, \"WangTag4\"], [32953, \"ImageReferencePoints\"], [32954, \"RegionXformTackPoint\"], [32955, \"WarpQuadrilateral\"], [32956, \"AffineTransformMat\"], [32995, \"Matteing\"], [32996, \"DataType\"], [32997, \"ImageDepth\"], [32998, \"TileDepth\"], [33300, \"ImageFullWidth\"], [33301, \"ImageFullHeight\"], [33302, \"TextureFormat\"], [33303, \"WrapModes\"], [33304, \"FovCot\"], [33305, \"MatrixWorldToScreen\"], [33306, \"MatrixWorldToCamera\"], [33405, \"Model2\"], [33421, \"CFARepeatPatternDim\"], [33422, \"CFAPattern2\"], [33423, \"BatteryLevel\"], [33424, \"KodakIFD\"], [33445, \"MDFileTag\"], [33446, \"MDScalePixel\"], [33447, \"MDColorTable\"], [33448, \"MDLabName\"], [33449, \"MDSampleInfo\"], [33450, \"MDPrepDate\"], [33451, \"MDPrepTime\"], [33452, \"MDFileUnits\"], [33589, \"AdventScale\"], [33590, \"AdventRevision\"], [33628, \"UIC1Tag\"], [33629, \"UIC2Tag\"], [33630, \"UIC3Tag\"], [33631, \"UIC4Tag\"], [33918, \"IntergraphPacketData\"], [33919, \"IntergraphFlagRegisters\"], [33921, \"INGRReserved\"], [34016, \"Site\"], [34017, \"ColorSequence\"], [34018, \"IT8Header\"], [34019, \"RasterPadding\"], [34020, \"BitsPerRunLength\"], [34021, \"BitsPerExtendedRunLength\"], [34022, \"ColorTable\"], [34023, \"ImageColorIndicator\"], [34024, \"BackgroundColorIndicator\"], [34025, \"ImageColorValue\"], [34026, \"BackgroundColorValue\"], [34027, \"PixelIntensityRange\"], [34028, \"TransparencyIndicator\"], [34029, \"ColorCharacterization\"], [34030, \"HCUsage\"], [34031, \"TrapIndicator\"], [34032, \"CMYKEquivalent\"], [34152, \"AFCP_IPTC\"], [34232, \"PixelMagicJBIGOptions\"], [34263, \"JPLCartoIFD\"], [34306, \"WB_GRGBLevels\"], [34310, \"LeafData\"], [34687, \"TIFF_FXExtensions\"], [34688, \"MultiProfiles\"], [34689, \"SharedData\"], [34690, \"T88Options\"], [34732, \"ImageLayer\"], [34750, \"JBIGOptions\"], [34856, \"Opto-ElectricConvFactor\"], [34857, \"Interlace\"], [34908, \"FaxRecvParams\"], [34909, \"FaxSubAddress\"], [34910, \"FaxRecvTime\"], [34929, \"FedexEDR\"], [34954, \"LeafSubIFD\"], [37387, \"FlashEnergy\"], [37388, \"SpatialFrequencyResponse\"], [37389, \"Noise\"], [37390, \"FocalPlaneXResolution\"], [37391, \"FocalPlaneYResolution\"], [37392, \"FocalPlaneResolutionUnit\"], [37397, \"ExposureIndex\"], [37398, \"TIFF-EPStandardID\"], [37399, \"SensingMethod\"], [37434, \"CIP3DataFile\"], [37435, \"CIP3Sheet\"], [37436, \"CIP3Side\"], [37439, \"StoNits\"], [37679, \"MSDocumentText\"], [37680, \"MSPropertySetStorage\"], [37681, \"MSDocumentTextPosition\"], [37724, \"ImageSourceData\"], [40965, \"InteropIFD\"], [40976, \"SamsungRawPointersOffset\"], [40977, \"SamsungRawPointersLength\"], [41217, \"SamsungRawByteOrder\"], [41218, \"SamsungRawUnknown\"], [41484, \"SpatialFrequencyResponse\"], [41485, \"Noise\"], [41489, \"ImageNumber\"], [41490, \"SecurityClassification\"], [41491, \"ImageHistory\"], [41494, \"TIFF-EPStandardID\"], [41995, \"DeviceSettingDescription\"], [42112, \"GDALMetadata\"], [42113, \"GDALNoData\"], [44992, \"ExpandSoftware\"], [44993, \"ExpandLens\"], [44994, \"ExpandFilm\"], [44995, \"ExpandFilterLens\"], [44996, \"ExpandScanner\"], [44997, \"ExpandFlashLamp\"], [46275, \"HasselbladRawImage\"], [48129, \"PixelFormat\"], [48130, \"Transformation\"], [48131, \"Uncompressed\"], [48132, \"ImageType\"], [48256, \"ImageWidth\"], [48257, \"ImageHeight\"], [48258, \"WidthResolution\"], [48259, \"HeightResolution\"], [48320, \"ImageOffset\"], [48321, \"ImageByteCount\"], [48322, \"AlphaOffset\"], [48323, \"AlphaByteCount\"], [48324, \"ImageDataDiscard\"], [48325, \"AlphaDataDiscard\"], [50215, \"OceScanjobDesc\"], [50216, \"OceApplicationSelector\"], [50217, \"OceIDNumber\"], [50218, \"OceImageLogic\"], [50255, \"Annotations\"], [50459, \"HasselbladExif\"], [50547, \"OriginalFileName\"], [50560, \"USPTOOriginalContentType\"], [50656, \"CR2CFAPattern\"], [50710, \"CFAPlaneColor\"], [50711, \"CFALayout\"], [50712, \"LinearizationTable\"], [50713, \"BlackLevelRepeatDim\"], [50714, \"BlackLevel\"], [50715, \"BlackLevelDeltaH\"], [50716, \"BlackLevelDeltaV\"], [50717, \"WhiteLevel\"], [50718, \"DefaultScale\"], [50719, \"DefaultCropOrigin\"], [50720, \"DefaultCropSize\"], [50733, \"BayerGreenSplit\"], [50737, \"ChromaBlurRadius\"], [50738, \"AntiAliasStrength\"], [50752, \"RawImageSegmentation\"], [50780, \"BestQualityScale\"], [50784, \"AliasLayerMetadata\"], [50829, \"ActiveArea\"], [50830, \"MaskedAreas\"], [50935, \"NoiseReductionApplied\"], [50974, \"SubTileBlockSize\"], [50975, \"RowInterleaveFactor\"], [51008, \"OpcodeList1\"], [51009, \"OpcodeList2\"], [51022, \"OpcodeList3\"], [51041, \"NoiseProfile\"], [51114, \"CacheVersion\"], [51125, \"DefaultUserCrop\"], [51157, \"NikonNEFInfo\"], [65024, \"KdcIFD\"]];\nF(E, \"ifd0\", ct), F(E, \"exif\", ct), U(B, \"gps\", [[23, {\n  M: \"Magnetic North\",\n  T: \"True North\"\n}], [25, {\n  K: \"Kilometers\",\n  M: \"Miles\",\n  N: \"Nautical Miles\"\n}]]);\nclass ft extends re {\n  static canHandle(e, t) {\n    return 224 === e.getUint8(t + 1) && 1246120262 === e.getUint32(t + 4) && 0 === e.getUint8(t + 8);\n  }\n  parse() {\n    return this.parseTags(), this.translate(), this.output;\n  }\n  parseTags() {\n    this.raw = new Map([[0, this.chunk.getUint16(0)], [2, this.chunk.getUint8(2)], [3, this.chunk.getUint16(3)], [5, this.chunk.getUint16(5)], [7, this.chunk.getUint8(7)], [8, this.chunk.getUint8(8)]]);\n  }\n}\nc(ft, \"type\", \"jfif\"), c(ft, \"headerLength\", 9), T.set(\"jfif\", ft), U(E, \"jfif\", [[0, \"JFIFVersion\"], [2, \"ResolutionUnit\"], [3, \"XResolution\"], [5, \"YResolution\"], [7, \"ThumbnailWidth\"], [8, \"ThumbnailHeight\"]]);\nclass dt extends re {\n  parse() {\n    return this.parseTags(), this.translate(), this.output;\n  }\n  parseTags() {\n    this.raw = new Map([[0, this.chunk.getUint32(0)], [4, this.chunk.getUint32(4)], [8, this.chunk.getUint8(8)], [9, this.chunk.getUint8(9)], [10, this.chunk.getUint8(10)], [11, this.chunk.getUint8(11)], [12, this.chunk.getUint8(12)], ...Array.from(this.raw)]);\n  }\n}\nc(dt, \"type\", \"ihdr\"), T.set(\"ihdr\", dt), U(E, \"ihdr\", [[0, \"ImageWidth\"], [4, \"ImageHeight\"], [8, \"BitDepth\"], [9, \"ColorType\"], [10, \"Compression\"], [11, \"Filter\"], [12, \"Interlace\"]]), U(B, \"ihdr\", [[9, {\n  0: \"Grayscale\",\n  2: \"RGB\",\n  3: \"Palette\",\n  4: \"Grayscale with Alpha\",\n  6: \"RGB with Alpha\",\n  DEFAULT: \"Unknown\"\n}], [10, {\n  0: \"Deflate/Inflate\",\n  DEFAULT: \"Unknown\"\n}], [11, {\n  0: \"Adaptive\",\n  DEFAULT: \"Unknown\"\n}], [12, {\n  0: \"Noninterlaced\",\n  1: \"Adam7 Interlace\",\n  DEFAULT: \"Unknown\"\n}]]);\nclass pt extends re {\n  static canHandle(e, t) {\n    return 226 === e.getUint8(t + 1) && 1229144927 === e.getUint32(t + 4);\n  }\n  static findPosition(e, t) {\n    let i = super.findPosition(e, t);\n    return i.chunkNumber = e.getUint8(t + 16), i.chunkCount = e.getUint8(t + 17), i.multiSegment = i.chunkCount > 1, i;\n  }\n  static handleMultiSegments(e) {\n    return function (e) {\n      let t = function (e) {\n        let t = e[0].constructor,\n          i = 0;\n        for (let t of e) i += t.length;\n        let n = new t(i),\n          s = 0;\n        for (let t of e) n.set(t, s), s += t.length;\n        return n;\n      }(e.map(e => e.chunk.toUint8()));\n      return new I(t);\n    }(e);\n  }\n  parse() {\n    return this.raw = new Map(), this.parseHeader(), this.parseTags(), this.translate(), this.output;\n  }\n  parseHeader() {\n    let {\n      raw: e\n    } = this;\n    this.chunk.byteLength < 84 && g(\"ICC header is too short\");\n    for (let [t, i] of Object.entries(gt)) {\n      t = parseInt(t, 10);\n      let n = i(this.chunk, t);\n      \"\\0\\0\\0\\0\" !== n && e.set(t, n);\n    }\n  }\n  parseTags() {\n    let e,\n      t,\n      i,\n      n,\n      s,\n      {\n        raw: r\n      } = this,\n      a = this.chunk.getUint32(128),\n      o = 132,\n      l = this.chunk.byteLength;\n    for (; a--;) {\n      if (e = this.chunk.getString(o, 4), t = this.chunk.getUint32(o + 4), i = this.chunk.getUint32(o + 8), n = this.chunk.getString(t, 4), t + i > l) return void console.warn(\"reached the end of the first ICC chunk. Enable options.tiff.multiSegment to read all ICC segments.\");\n      s = this.parseTag(n, t, i), void 0 !== s && \"\\0\\0\\0\\0\" !== s && r.set(e, s), o += 12;\n    }\n  }\n  parseTag(e, t, i) {\n    switch (e) {\n      case \"desc\":\n        return this.parseDesc(t);\n      case \"mluc\":\n        return this.parseMluc(t);\n      case \"text\":\n        return this.parseText(t, i);\n      case \"sig \":\n        return this.parseSig(t);\n    }\n    if (!(t + i > this.chunk.byteLength)) return this.chunk.getUint8Array(t, i);\n  }\n  parseDesc(e) {\n    let t = this.chunk.getUint32(e + 8) - 1;\n    return m(this.chunk.getString(e + 12, t));\n  }\n  parseText(e, t) {\n    return m(this.chunk.getString(e + 8, t - 8));\n  }\n  parseSig(e) {\n    return m(this.chunk.getString(e + 8, 4));\n  }\n  parseMluc(e) {\n    let {\n        chunk: t\n      } = this,\n      i = t.getUint32(e + 8),\n      n = t.getUint32(e + 12),\n      s = e + 16,\n      r = [];\n    for (let a = 0; a < i; a++) {\n      let i = t.getString(s + 0, 2),\n        a = t.getString(s + 2, 2),\n        o = t.getUint32(s + 4),\n        l = t.getUint32(s + 8) + e,\n        h = m(t.getUnicodeString(l, o));\n      r.push({\n        lang: i,\n        country: a,\n        text: h\n      }), s += n;\n    }\n    return 1 === i ? r[0].text : r;\n  }\n  translateValue(e, t) {\n    return \"string\" == typeof e ? t[e] || t[e.toLowerCase()] || e : t[e] || e;\n  }\n}\nc(pt, \"type\", \"icc\"), c(pt, \"multiSegment\", !0), c(pt, \"headerLength\", 18);\nconst gt = {\n  4: mt,\n  8: function (e, t) {\n    return [e.getUint8(t), e.getUint8(t + 1) >> 4, e.getUint8(t + 1) % 16].map(e => e.toString(10)).join(\".\");\n  },\n  12: mt,\n  16: mt,\n  20: mt,\n  24: function (e, t) {\n    const i = e.getUint16(t),\n      n = e.getUint16(t + 2) - 1,\n      s = e.getUint16(t + 4),\n      r = e.getUint16(t + 6),\n      a = e.getUint16(t + 8),\n      o = e.getUint16(t + 10);\n    return new Date(Date.UTC(i, n, s, r, a, o));\n  },\n  36: mt,\n  40: mt,\n  48: mt,\n  52: mt,\n  64: (e, t) => e.getUint32(t),\n  80: mt\n};\nfunction mt(e, t) {\n  return m(e.getString(t, 4));\n}\nT.set(\"icc\", pt), U(E, \"icc\", [[4, \"ProfileCMMType\"], [8, \"ProfileVersion\"], [12, \"ProfileClass\"], [16, \"ColorSpaceData\"], [20, \"ProfileConnectionSpace\"], [24, \"ProfileDateTime\"], [36, \"ProfileFileSignature\"], [40, \"PrimaryPlatform\"], [44, \"CMMFlags\"], [48, \"DeviceManufacturer\"], [52, \"DeviceModel\"], [56, \"DeviceAttributes\"], [64, \"RenderingIntent\"], [68, \"ConnectionSpaceIlluminant\"], [80, \"ProfileCreator\"], [84, \"ProfileID\"], [\"Header\", \"ProfileHeader\"], [\"MS00\", \"WCSProfiles\"], [\"bTRC\", \"BlueTRC\"], [\"bXYZ\", \"BlueMatrixColumn\"], [\"bfd\", \"UCRBG\"], [\"bkpt\", \"MediaBlackPoint\"], [\"calt\", \"CalibrationDateTime\"], [\"chad\", \"ChromaticAdaptation\"], [\"chrm\", \"Chromaticity\"], [\"ciis\", \"ColorimetricIntentImageState\"], [\"clot\", \"ColorantTableOut\"], [\"clro\", \"ColorantOrder\"], [\"clrt\", \"ColorantTable\"], [\"cprt\", \"ProfileCopyright\"], [\"crdi\", \"CRDInfo\"], [\"desc\", \"ProfileDescription\"], [\"devs\", \"DeviceSettings\"], [\"dmdd\", \"DeviceModelDesc\"], [\"dmnd\", \"DeviceMfgDesc\"], [\"dscm\", \"ProfileDescriptionML\"], [\"fpce\", \"FocalPlaneColorimetryEstimates\"], [\"gTRC\", \"GreenTRC\"], [\"gXYZ\", \"GreenMatrixColumn\"], [\"gamt\", \"Gamut\"], [\"kTRC\", \"GrayTRC\"], [\"lumi\", \"Luminance\"], [\"meas\", \"Measurement\"], [\"meta\", \"Metadata\"], [\"mmod\", \"MakeAndModel\"], [\"ncl2\", \"NamedColor2\"], [\"ncol\", \"NamedColor\"], [\"ndin\", \"NativeDisplayInfo\"], [\"pre0\", \"Preview0\"], [\"pre1\", \"Preview1\"], [\"pre2\", \"Preview2\"], [\"ps2i\", \"PS2RenderingIntent\"], [\"ps2s\", \"PostScript2CSA\"], [\"psd0\", \"PostScript2CRD0\"], [\"psd1\", \"PostScript2CRD1\"], [\"psd2\", \"PostScript2CRD2\"], [\"psd3\", \"PostScript2CRD3\"], [\"pseq\", \"ProfileSequenceDesc\"], [\"psid\", \"ProfileSequenceIdentifier\"], [\"psvm\", \"PS2CRDVMSize\"], [\"rTRC\", \"RedTRC\"], [\"rXYZ\", \"RedMatrixColumn\"], [\"resp\", \"OutputResponse\"], [\"rhoc\", \"ReflectionHardcopyOrigColorimetry\"], [\"rig0\", \"PerceptualRenderingIntentGamut\"], [\"rig2\", \"SaturationRenderingIntentGamut\"], [\"rpoc\", \"ReflectionPrintOutputColorimetry\"], [\"sape\", \"SceneAppearanceEstimates\"], [\"scoe\", \"SceneColorimetryEstimates\"], [\"scrd\", \"ScreeningDesc\"], [\"scrn\", \"Screening\"], [\"targ\", \"CharTarget\"], [\"tech\", \"Technology\"], [\"vcgt\", \"VideoCardGamma\"], [\"view\", \"ViewingConditions\"], [\"vued\", \"ViewingCondDesc\"], [\"wtpt\", \"MediaWhitePoint\"]]);\nconst St = {\n    \"4d2p\": \"Erdt Systems\",\n    AAMA: \"Aamazing Technologies\",\n    ACER: \"Acer\",\n    ACLT: \"Acolyte Color Research\",\n    ACTI: \"Actix Sytems\",\n    ADAR: \"Adara Technology\",\n    ADBE: \"Adobe\",\n    ADI: \"ADI Systems\",\n    AGFA: \"Agfa Graphics\",\n    ALMD: \"Alps Electric\",\n    ALPS: \"Alps Electric\",\n    ALWN: \"Alwan Color Expertise\",\n    AMTI: \"Amiable Technologies\",\n    AOC: \"AOC International\",\n    APAG: \"Apago\",\n    APPL: \"Apple Computer\",\n    AST: \"AST\",\n    \"AT&T\": \"AT&T\",\n    BAEL: \"BARBIERI electronic\",\n    BRCO: \"Barco NV\",\n    BRKP: \"Breakpoint\",\n    BROT: \"Brother\",\n    BULL: \"Bull\",\n    BUS: \"Bus Computer Systems\",\n    \"C-IT\": \"C-Itoh\",\n    CAMR: \"Intel\",\n    CANO: \"Canon\",\n    CARR: \"Carroll Touch\",\n    CASI: \"Casio\",\n    CBUS: \"Colorbus PL\",\n    CEL: \"Crossfield\",\n    CELx: \"Crossfield\",\n    CGS: \"CGS Publishing Technologies International\",\n    CHM: \"Rochester Robotics\",\n    CIGL: \"Colour Imaging Group, London\",\n    CITI: \"Citizen\",\n    CL00: \"Candela\",\n    CLIQ: \"Color IQ\",\n    CMCO: \"Chromaco\",\n    CMiX: \"CHROMiX\",\n    COLO: \"Colorgraphic Communications\",\n    COMP: \"Compaq\",\n    COMp: \"Compeq/Focus Technology\",\n    CONR: \"Conrac Display Products\",\n    CORD: \"Cordata Technologies\",\n    CPQ: \"Compaq\",\n    CPRO: \"ColorPro\",\n    CRN: \"Cornerstone\",\n    CTX: \"CTX International\",\n    CVIS: \"ColorVision\",\n    CWC: \"Fujitsu Laboratories\",\n    DARI: \"Darius Technology\",\n    DATA: \"Dataproducts\",\n    DCP: \"Dry Creek Photo\",\n    DCRC: \"Digital Contents Resource Center, Chung-Ang University\",\n    DELL: \"Dell Computer\",\n    DIC: \"Dainippon Ink and Chemicals\",\n    DICO: \"Diconix\",\n    DIGI: \"Digital\",\n    \"DL&C\": \"Digital Light & Color\",\n    DPLG: \"Doppelganger\",\n    DS: \"Dainippon Screen\",\n    DSOL: \"DOOSOL\",\n    DUPN: \"DuPont\",\n    EPSO: \"Epson\",\n    ESKO: \"Esko-Graphics\",\n    ETRI: \"Electronics and Telecommunications Research Institute\",\n    EVER: \"Everex Systems\",\n    EXAC: \"ExactCODE\",\n    Eizo: \"Eizo\",\n    FALC: \"Falco Data Products\",\n    FF: \"Fuji Photo Film\",\n    FFEI: \"FujiFilm Electronic Imaging\",\n    FNRD: \"Fnord Software\",\n    FORA: \"Fora\",\n    FORE: \"Forefront Technology\",\n    FP: \"Fujitsu\",\n    FPA: \"WayTech Development\",\n    FUJI: \"Fujitsu\",\n    FX: \"Fuji Xerox\",\n    GCC: \"GCC Technologies\",\n    GGSL: \"Global Graphics Software\",\n    GMB: \"Gretagmacbeth\",\n    GMG: \"GMG\",\n    GOLD: \"GoldStar Technology\",\n    GOOG: \"Google\",\n    GPRT: \"Giantprint\",\n    GTMB: \"Gretagmacbeth\",\n    GVC: \"WayTech Development\",\n    GW2K: \"Sony\",\n    HCI: \"HCI\",\n    HDM: \"Heidelberger Druckmaschinen\",\n    HERM: \"Hermes\",\n    HITA: \"Hitachi America\",\n    HP: \"Hewlett-Packard\",\n    HTC: \"Hitachi\",\n    HiTi: \"HiTi Digital\",\n    IBM: \"IBM\",\n    IDNT: \"Scitex\",\n    IEC: \"Hewlett-Packard\",\n    IIYA: \"Iiyama North America\",\n    IKEG: \"Ikegami Electronics\",\n    IMAG: \"Image Systems\",\n    IMI: \"Ingram Micro\",\n    INTC: \"Intel\",\n    INTL: \"N/A (INTL)\",\n    INTR: \"Intra Electronics\",\n    IOCO: \"Iocomm International Technology\",\n    IPS: \"InfoPrint Solutions Company\",\n    IRIS: \"Scitex\",\n    ISL: \"Ichikawa Soft Laboratory\",\n    ITNL: \"N/A (ITNL)\",\n    IVM: \"IVM\",\n    IWAT: \"Iwatsu Electric\",\n    Idnt: \"Scitex\",\n    Inca: \"Inca Digital Printers\",\n    Iris: \"Scitex\",\n    JPEG: \"Joint Photographic Experts Group\",\n    JSFT: \"Jetsoft Development\",\n    JVC: \"JVC Information Products\",\n    KART: \"Scitex\",\n    KFC: \"KFC Computek Components\",\n    KLH: \"KLH Computers\",\n    KMHD: \"Konica Minolta\",\n    KNCA: \"Konica\",\n    KODA: \"Kodak\",\n    KYOC: \"Kyocera\",\n    Kart: \"Scitex\",\n    LCAG: \"Leica\",\n    LCCD: \"Leeds Colour\",\n    LDAK: \"Left Dakota\",\n    LEAD: \"Leading Technology\",\n    LEXM: \"Lexmark International\",\n    LINK: \"Link Computer\",\n    LINO: \"Linotronic\",\n    LITE: \"Lite-On\",\n    Leaf: \"Leaf\",\n    Lino: \"Linotronic\",\n    MAGC: \"Mag Computronic\",\n    MAGI: \"MAG Innovision\",\n    MANN: \"Mannesmann\",\n    MICN: \"Micron Technology\",\n    MICR: \"Microtek\",\n    MICV: \"Microvitec\",\n    MINO: \"Minolta\",\n    MITS: \"Mitsubishi Electronics America\",\n    MITs: \"Mitsuba\",\n    MNLT: \"Minolta\",\n    MODG: \"Modgraph\",\n    MONI: \"Monitronix\",\n    MONS: \"Monaco Systems\",\n    MORS: \"Morse Technology\",\n    MOTI: \"Motive Systems\",\n    MSFT: \"Microsoft\",\n    MUTO: \"MUTOH INDUSTRIES\",\n    Mits: \"Mitsubishi Electric\",\n    NANA: \"NANAO\",\n    NEC: \"NEC\",\n    NEXP: \"NexPress Solutions\",\n    NISS: \"Nissei Sangyo America\",\n    NKON: \"Nikon\",\n    NONE: \"none\",\n    OCE: \"Oce Technologies\",\n    OCEC: \"OceColor\",\n    OKI: \"Oki\",\n    OKID: \"Okidata\",\n    OKIP: \"Okidata\",\n    OLIV: \"Olivetti\",\n    OLYM: \"Olympus\",\n    ONYX: \"Onyx Graphics\",\n    OPTI: \"Optiquest\",\n    PACK: \"Packard Bell\",\n    PANA: \"Matsushita Electric Industrial\",\n    PANT: \"Pantone\",\n    PBN: \"Packard Bell\",\n    PFU: \"PFU\",\n    PHIL: \"Philips Consumer Electronics\",\n    PNTX: \"HOYA\",\n    POne: \"Phase One A/S\",\n    PREM: \"Premier Computer Innovations\",\n    PRIN: \"Princeton Graphic Systems\",\n    PRIP: \"Princeton Publishing Labs\",\n    QLUX: \"Hong Kong\",\n    QMS: \"QMS\",\n    QPCD: \"QPcard AB\",\n    QUAD: \"QuadLaser\",\n    QUME: \"Qume\",\n    RADI: \"Radius\",\n    RDDx: \"Integrated Color Solutions\",\n    RDG: \"Roland DG\",\n    REDM: \"REDMS Group\",\n    RELI: \"Relisys\",\n    RGMS: \"Rolf Gierling Multitools\",\n    RICO: \"Ricoh\",\n    RNLD: \"Edmund Ronald\",\n    ROYA: \"Royal\",\n    RPC: \"Ricoh Printing Systems\",\n    RTL: \"Royal Information Electronics\",\n    SAMP: \"Sampo\",\n    SAMS: \"Samsung\",\n    SANT: \"Jaime Santana Pomares\",\n    SCIT: \"Scitex\",\n    SCRN: \"Dainippon Screen\",\n    SDP: \"Scitex\",\n    SEC: \"Samsung\",\n    SEIK: \"Seiko Instruments\",\n    SEIk: \"Seikosha\",\n    SGUY: \"ScanGuy.com\",\n    SHAR: \"Sharp Laboratories\",\n    SICC: \"International Color Consortium\",\n    SONY: \"Sony\",\n    SPCL: \"SpectraCal\",\n    STAR: \"Star\",\n    STC: \"Sampo Technology\",\n    Scit: \"Scitex\",\n    Sdp: \"Scitex\",\n    Sony: \"Sony\",\n    TALO: \"Talon Technology\",\n    TAND: \"Tandy\",\n    TATU: \"Tatung\",\n    TAXA: \"TAXAN America\",\n    TDS: \"Tokyo Denshi Sekei\",\n    TECO: \"TECO Information Systems\",\n    TEGR: \"Tegra\",\n    TEKT: \"Tektronix\",\n    TI: \"Texas Instruments\",\n    TMKR: \"TypeMaker\",\n    TOSB: \"Toshiba\",\n    TOSH: \"Toshiba\",\n    TOTK: \"TOTOKU ELECTRIC\",\n    TRIU: \"Triumph\",\n    TSBT: \"Toshiba\",\n    TTX: \"TTX Computer Products\",\n    TVM: \"TVM Professional Monitor\",\n    TW: \"TW Casper\",\n    ULSX: \"Ulead Systems\",\n    UNIS: \"Unisys\",\n    UTZF: \"Utz Fehlau & Sohn\",\n    VARI: \"Varityper\",\n    VIEW: \"Viewsonic\",\n    VISL: \"Visual communication\",\n    VIVO: \"Vivo Mobile Communication\",\n    WANG: \"Wang\",\n    WLBR: \"Wilbur Imaging\",\n    WTG2: \"Ware To Go\",\n    WYSE: \"WYSE Technology\",\n    XERX: \"Xerox\",\n    XRIT: \"X-Rite\",\n    ZRAN: \"Zoran\",\n    Zebr: \"Zebra Technologies\",\n    appl: \"Apple Computer\",\n    bICC: \"basICColor\",\n    berg: \"bergdesign\",\n    ceyd: \"Integrated Color Solutions\",\n    clsp: \"MacDermid ColorSpan\",\n    ds: \"Dainippon Screen\",\n    dupn: \"DuPont\",\n    ffei: \"FujiFilm Electronic Imaging\",\n    flux: \"FluxData\",\n    iris: \"Scitex\",\n    kart: \"Scitex\",\n    lcms: \"Little CMS\",\n    lino: \"Linotronic\",\n    none: \"none\",\n    ob4d: \"Erdt Systems\",\n    obic: \"Medigraph\",\n    quby: \"Qubyx Sarl\",\n    scit: \"Scitex\",\n    scrn: \"Dainippon Screen\",\n    sdp: \"Scitex\",\n    siwi: \"SIWI GRAFIKA\",\n    yxym: \"YxyMaster\"\n  },\n  Ct = {\n    scnr: \"Scanner\",\n    mntr: \"Monitor\",\n    prtr: \"Printer\",\n    link: \"Device Link\",\n    abst: \"Abstract\",\n    spac: \"Color Space Conversion Profile\",\n    nmcl: \"Named Color\",\n    cenc: \"ColorEncodingSpace profile\",\n    mid: \"MultiplexIdentification profile\",\n    mlnk: \"MultiplexLink profile\",\n    mvis: \"MultiplexVisualization profile\",\n    nkpf: \"Nikon Input Device Profile (NON-STANDARD!)\"\n  };\nU(B, \"icc\", [[4, St], [12, Ct], [40, Object.assign({}, St, Ct)], [48, St], [80, St], [64, {\n  0: \"Perceptual\",\n  1: \"Relative Colorimetric\",\n  2: \"Saturation\",\n  3: \"Absolute Colorimetric\"\n}], [\"tech\", {\n  amd: \"Active Matrix Display\",\n  crt: \"Cathode Ray Tube Display\",\n  kpcd: \"Photo CD\",\n  pmd: \"Passive Matrix Display\",\n  dcam: \"Digital Camera\",\n  dcpj: \"Digital Cinema Projector\",\n  dmpc: \"Digital Motion Picture Camera\",\n  dsub: \"Dye Sublimation Printer\",\n  epho: \"Electrophotographic Printer\",\n  esta: \"Electrostatic Printer\",\n  flex: \"Flexography\",\n  fprn: \"Film Writer\",\n  fscn: \"Film Scanner\",\n  grav: \"Gravure\",\n  ijet: \"Ink Jet Printer\",\n  imgs: \"Photo Image Setter\",\n  mpfr: \"Motion Picture Film Recorder\",\n  mpfs: \"Motion Picture Film Scanner\",\n  offs: \"Offset Lithography\",\n  pjtv: \"Projection Television\",\n  rpho: \"Photographic Paper Printer\",\n  rscn: \"Reflective Scanner\",\n  silk: \"Silkscreen\",\n  twax: \"Thermal Wax Printer\",\n  vidc: \"Video Camera\",\n  vidm: \"Video Monitor\"\n}]]);\nclass yt extends re {\n  static canHandle(e, t, i) {\n    return 237 === e.getUint8(t + 1) && \"Photoshop\" === e.getString(t + 4, 9) && void 0 !== this.containsIptc8bim(e, t, i);\n  }\n  static headerLength(e, t, i) {\n    let n,\n      s = this.containsIptc8bim(e, t, i);\n    if (void 0 !== s) return n = e.getUint8(t + s + 7), n % 2 != 0 && (n += 1), 0 === n && (n = 4), s + 8 + n;\n  }\n  static containsIptc8bim(e, t, i) {\n    for (let n = 0; n < i; n++) if (this.isIptcSegmentHead(e, t + n)) return n;\n  }\n  static isIptcSegmentHead(e, t) {\n    return 56 === e.getUint8(t) && 943868237 === e.getUint32(t) && 1028 === e.getUint16(t + 4);\n  }\n  parse() {\n    let {\n        raw: e\n      } = this,\n      t = this.chunk.byteLength - 1,\n      i = !1;\n    for (let n = 0; n < t; n++) if (28 === this.chunk.getUint8(n) && 2 === this.chunk.getUint8(n + 1)) {\n      i = !0;\n      let t = this.chunk.getUint16(n + 3),\n        s = this.chunk.getUint8(n + 2),\n        r = this.chunk.getLatin1String(n + 5, t);\n      e.set(s, this.pluralizeValue(e.get(s), r)), n += 4 + t;\n    } else if (i) break;\n    return this.translate(), this.output;\n  }\n  pluralizeValue(e, t) {\n    return void 0 !== e ? e instanceof Array ? (e.push(t), e) : [e, t] : t;\n  }\n}\nc(yt, \"type\", \"iptc\"), c(yt, \"translateValues\", !1), c(yt, \"reviveValues\", !1), T.set(\"iptc\", yt), U(E, \"iptc\", [[0, \"ApplicationRecordVersion\"], [3, \"ObjectTypeReference\"], [4, \"ObjectAttributeReference\"], [5, \"ObjectName\"], [7, \"EditStatus\"], [8, \"EditorialUpdate\"], [10, \"Urgency\"], [12, \"SubjectReference\"], [15, \"Category\"], [20, \"SupplementalCategories\"], [22, \"FixtureIdentifier\"], [25, \"Keywords\"], [26, \"ContentLocationCode\"], [27, \"ContentLocationName\"], [30, \"ReleaseDate\"], [35, \"ReleaseTime\"], [37, \"ExpirationDate\"], [38, \"ExpirationTime\"], [40, \"SpecialInstructions\"], [42, \"ActionAdvised\"], [45, \"ReferenceService\"], [47, \"ReferenceDate\"], [50, \"ReferenceNumber\"], [55, \"DateCreated\"], [60, \"TimeCreated\"], [62, \"DigitalCreationDate\"], [63, \"DigitalCreationTime\"], [65, \"OriginatingProgram\"], [70, \"ProgramVersion\"], [75, \"ObjectCycle\"], [80, \"Byline\"], [85, \"BylineTitle\"], [90, \"City\"], [92, \"Sublocation\"], [95, \"State\"], [100, \"CountryCode\"], [101, \"Country\"], [103, \"OriginalTransmissionReference\"], [105, \"Headline\"], [110, \"Credit\"], [115, \"Source\"], [116, \"CopyrightNotice\"], [118, \"Contact\"], [120, \"Caption\"], [121, \"LocalCaption\"], [122, \"Writer\"], [125, \"RasterizedCaption\"], [130, \"ImageType\"], [131, \"ImageOrientation\"], [135, \"LanguageIdentifier\"], [150, \"AudioType\"], [151, \"AudioSamplingRate\"], [152, \"AudioSamplingResolution\"], [153, \"AudioDuration\"], [154, \"AudioOutcue\"], [184, \"JobID\"], [185, \"MasterDocumentID\"], [186, \"ShortDocumentID\"], [187, \"UniqueDocumentID\"], [188, \"OwnerID\"], [200, \"ObjectPreviewFileFormat\"], [201, \"ObjectPreviewFileVersion\"], [202, \"ObjectPreviewData\"], [221, \"Prefs\"], [225, \"ClassifyState\"], [228, \"SimilarityIndex\"], [230, \"DocumentNotes\"], [231, \"DocumentHistory\"], [232, \"ExifCameraInfo\"], [255, \"CatalogSets\"]]), U(B, \"iptc\", [[10, {\n  0: \"0 (reserved)\",\n  1: \"1 (most urgent)\",\n  2: \"2\",\n  3: \"3\",\n  4: \"4\",\n  5: \"5 (normal urgency)\",\n  6: \"6\",\n  7: \"7\",\n  8: \"8 (least urgent)\",\n  9: \"9 (user-defined priority)\"\n}], [75, {\n  a: \"Morning\",\n  b: \"Both Morning and Evening\",\n  p: \"Evening\"\n}], [131, {\n  L: \"Landscape\",\n  P: \"Portrait\",\n  S: \"Square\"\n}]]);\nexport default tt;\nexport { te as Exifr, q as Options, X as allFormatters, G as chunkedProps, U as createDictionary, F as extendDictionary, M as fetchUrlAsArrayBuffer, w as fileParsers, A as fileReaders, Se as gps, me as gpsOnlyOptions, K as inheritables, Pe as orientation, Ie as orientationOnlyOptions, V as otherSegments, ie as parse, R as readBlobAsArrayBuffer, we as rotateCanvas, Te as rotateCss, Ae as rotation, ke as rotations, T as segmentParsers, z as segments, j as segmentsAndBlocks, st as sidecar, E as tagKeys, N as tagRevivers, B as tagValues, ye as thumbnail, Ce as thumbnailOnlyOptions, be as thumbnailUrl, H as tiffBlocks, W as tiffExtractables };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}