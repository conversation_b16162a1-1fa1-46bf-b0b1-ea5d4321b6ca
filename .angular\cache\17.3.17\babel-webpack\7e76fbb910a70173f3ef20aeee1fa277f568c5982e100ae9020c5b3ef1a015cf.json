{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.throwError = void 0;\nvar Observable_1 = require(\"../Observable\");\nvar isFunction_1 = require(\"../util/isFunction\");\nfunction throwError(errorOrErrorFactory, scheduler) {\n  var errorFactory = isFunction_1.isFunction(errorOrErrorFactory) ? errorOrErrorFactory : function () {\n    return errorOrErrorFactory;\n  };\n  var init = function (subscriber) {\n    return subscriber.error(errorFactory());\n  };\n  return new Observable_1.Observable(scheduler ? function (subscriber) {\n    return scheduler.schedule(init, 0, subscriber);\n  } : init);\n}\nexports.throwError = throwError;\n//# sourceMappingURL=throwError.js.map", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}