{"ast": null, "code": "import _asyncToGenerator from \"D:/ISR/front_soweb/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { HttpClient } from '@angular/common/http';\nimport { NgbCalendar } from '@ng-bootstrap/ng-bootstrap';\nimport { listLocales } from 'ngx-bootstrap/chronos';\nimport { FormBuilder } from '@angular/forms';\nimport { ActivatedRoute, Router } from '@angular/router';\nimport { debounceTime, map } from 'rxjs/operators';\nimport { WebapiService } from '../webapi.service';\n// Removed deprecated imports\nimport { BsLocaleService } from 'ngx-bootstrap/datepicker';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"../webapi.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/forms\";\nimport * as i5 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i6 from \"ngx-bootstrap/datepicker\";\nimport * as i7 from \"ngx-bootstrap/tooltip\";\nimport * as i8 from \"@angular/common\";\nimport * as i9 from \"../../../node_modules/@angular/forms/index\";\nimport * as i10 from \"../topmenu/topmenu.component\";\nimport * as i11 from \"../reversepipe.pipe\";\nconst _c0 = a0 => ({\n  \"display\": a0,\n  \"opacity\": 1\n});\nfunction EditsaloderreviewComponent_button_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 78);\n    i0.ɵɵlistener(\"click\", function EditsaloderreviewComponent_button_13_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.deleteebeforupdate());\n    });\n    i0.ɵɵtext(1, \"Save\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction EditsaloderreviewComponent_ng_template_22_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 79);\n    i0.ɵɵlistener(\"click\", function EditsaloderreviewComponent_ng_template_22_Template_div_click_0_listener() {\n      const r_r5 = i0.ɵɵrestoreView(_r4).result;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.getcustomersalefunction(r_r5.name));\n    });\n    i0.ɵɵelementStart(1, \"label\", 80);\n    i0.ɵɵlistener(\"mousedown\", function EditsaloderreviewComponent_ng_template_22_Template_label_mousedown_1_listener() {\n      const r_r5 = i0.ɵɵrestoreView(_r4).result;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.getcustomersalefunction(r_r5.name));\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const r_r5 = ctx.result;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate3(\"\", r_r5.name, \" (\", r_r5.accountnum, \")(\", r_r5.regnum, \")\");\n  }\n}\nfunction EditsaloderreviewComponent_option_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 81);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r6 = ctx.$implicit;\n    i0.ɵɵpropertyInterpolate(\"value\", item_r6.locationno);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\"(\", item_r6.name, \") \", item_r6.address, \"\");\n  }\n}\nfunction EditsaloderreviewComponent_option_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 81);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r7 = ctx.$implicit;\n    i0.ɵɵpropertyInterpolate(\"value\", item_r7.locationno);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\"(\", item_r7.name, \") \", item_r7.address, \"\");\n  }\n}\nfunction EditsaloderreviewComponent_option_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 81);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r8 = ctx.$implicit;\n    i0.ɵɵpropertyInterpolate(\"value\", item_r8.vattype);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r8.vattype);\n  }\n}\nfunction EditsaloderreviewComponent_option_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 81);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r9 = ctx.$implicit;\n    i0.ɵɵpropertyInterpolate(\"value\", item_r9.deliver);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r9.deliver);\n  }\n}\nfunction EditsaloderreviewComponent_option_43_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 81);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r10 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", item_r10.paymtermid);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r10.paymtermid);\n  }\n}\nfunction EditsaloderreviewComponent_label_55_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"label\", 82);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"Item In Stock: \", ctx_r2.Instock, \"\");\n  }\n}\nfunction EditsaloderreviewComponent_ng_template_60_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 83);\n    i0.ɵɵlistener(\"click\", function EditsaloderreviewComponent_ng_template_60_Template_div_click_0_listener() {\n      const r_r12 = i0.ɵɵrestoreView(_r11).result;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.Searchitemclick(r_r12.itemid));\n    })(\"mousedown\", function EditsaloderreviewComponent_ng_template_60_Template_div_mousedown_0_listener() {\n      const r_r12 = i0.ɵɵrestoreView(_r11).result;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.Searchitemclick(r_r12.itemid));\n    });\n    i0.ɵɵelementStart(1, \"label\", 84);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const r_r12 = ctx.result;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"(\", r_r12.itemid, \") \", r_r12.name, \"\");\n  }\n}\nfunction EditsaloderreviewComponent_option_67_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 81);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r13 = ctx.$implicit;\n    i0.ɵɵpropertyInterpolate(\"value\", item_r13.unit + item_r13.unitid);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r13.unitid);\n  }\n}\nfunction EditsaloderreviewComponent_div_86_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 25)(1, \"div\", 85)(2, \"input\", 86);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function EditsaloderreviewComponent_div_86_Template_input_ngModelChange_2_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.freeitem, $event) || (ctx_r2.freeitem = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"click\", function EditsaloderreviewComponent_div_86_Template_input_click_2_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.checkfreeitemfunction($event.target.checked));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"label\", 87);\n    i0.ɵɵtext(4, \"\\u0E02\\u0E2D\\u0E07\\u0E41\\u0E16\\u0E21\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.freeitem);\n  }\n}\nfunction EditsaloderreviewComponent_tr_122_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 88);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\", 88);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\", 89);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\", 90);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"td\", 89);\n    i0.ɵɵtext(12);\n    i0.ɵɵpipe(13, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"td\", 89);\n    i0.ɵɵtext(15);\n    i0.ɵɵpipe(16, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"td\", 89);\n    i0.ɵɵtext(18);\n    i0.ɵɵpipe(19, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"td\", 89);\n    i0.ɵɵtext(21);\n    i0.ɵɵpipe(22, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"td\", 89);\n    i0.ɵɵtext(24);\n    i0.ɵɵpipe(25, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"td\", 89);\n    i0.ɵɵtext(27);\n    i0.ɵɵpipe(28, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"td\", 89);\n    i0.ɵɵtext(30);\n    i0.ɵɵpipe(31, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"td\", 89);\n    i0.ɵɵtext(33);\n    i0.ɵɵpipe(34, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(35, \"td\", 89);\n    i0.ɵɵtext(36);\n    i0.ɵɵpipe(37, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(38, \"td\", 89);\n    i0.ɵɵtext(39);\n    i0.ɵɵpipe(40, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(41, \"td\", 88)(42, \"label\", 91);\n    i0.ɵɵlistener(\"click\", function EditsaloderreviewComponent_tr_122_Template_label_click_42_listener() {\n      const i_r16 = i0.ɵɵrestoreView(_r15).index;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.foreditlistlineoderclick(i_r16, true));\n    });\n    i0.ɵɵtext(43, \"\\u0E41\\u0E01\\u0E49\\u0E44\\u0E02\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(44, \"td\", 88)(45, \"label\", 92);\n    i0.ɵɵlistener(\"click\", function EditsaloderreviewComponent_tr_122_Template_label_click_45_listener() {\n      const ctx_r16 = i0.ɵɵrestoreView(_r15);\n      const item_r18 = ctx_r16.$implicit;\n      const i_r16 = ctx_r16.index;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.deletelistlineoderclick(i_r16, item_r18.idline));\n    });\n    i0.ɵɵtext(46, \"\\u0E25\\u0E1A\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const item_r18 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r18.linenum);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r18.iditem);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r18.nameproduct);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r18.numberpcs);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r18.unitid);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(13, 15, item_r18.packingitem, \"1.2-2\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(16, 18, item_r18.totleweight, \"1.2-2\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(19, 21, ctx_r2.Realtimeinstock(item_r18.iditem), \"1.0-2\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(22, 24, item_r18.priceperunit, \"1.2-2\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(25, 27, item_r18.priceproduct, \"1.2-2\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(28, 30, item_r18.discount1, \"1.2-2\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(31, 33, item_r18.discount2, \"1.2-2\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(34, 36, item_r18.discount3, \"1.2-2\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(37, 39, item_r18.sumdiscount, \"1.2-2\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(40, 42, item_r18.sumallprice, \"1.2-2\"));\n  }\n}\n;\n;\n;\nexport let EditsaloderreviewComponent = /*#__PURE__*/(() => {\n  class EditsaloderreviewComponent {\n    constructor(http, service, route, router, fb, calendar, localeService) {\n      this.http = http;\n      this.service = service;\n      this.route = route;\n      this.router = router;\n      this.fb = fb;\n      this.calendar = calendar;\n      this.localeService = localeService;\n      this.mdlSampleIsOpen = false;\n      this.alt = '';\n      this.checkreload = true;\n      this.disproductline = true;\n      this.filetype = '';\n      this.filename = '';\n      this.usseredit = '';\n      this.accountnum = '';\n      this.olddis1 = 0.00;\n      this.olddis2 = 0.00;\n      this.olddis3 = 0.00;\n      this.upperdis = '';\n      this.ennableeditprice = true;\n      this.openbtnclose = false;\n      this.getpromotionedit = false;\n      this.checkpacking = 0;\n      this.getcheckpcs = 'P';\n      this.getwh = [];\n      this.warehouselist = [];\n      this.wh = '';\n      this.getallidsaleoder = [];\n      this.getrunnumber = '';\n      this.checkadditem = false;\n      this.filterargs = {\n        title: 'hello'\n      };\n      this.items = [{\n        title: 'hello world'\n      }, {\n        title: 'hello kitty'\n      }, {\n        title: 'foo bar'\n      }];\n      this.selectedpaymentmap = '';\n      this.getpackingprice = 0.00;\n      this.getunitprice = 0.00;\n      this.showitem = \"รหัสสินค้า\";\n      this.headerlist = [];\n      this.headerlistsave = [];\n      this.freeitem = false;\n      this.promotionlist = [];\n      this.accoutnreration = '';\n      this.pricegroup = '';\n      this.lineoderlist = [];\n      this.lineoderlistsave = [];\n      this.today = new Date();\n      this.x = 1;\n      this.numlist = 0;\n      this.locationno = '';\n      this.notesoinput = '';\n      this.remarksoinput = '';\n      this.checkbuttonsave = false;\n      this.sumalldiscount = 0;\n      this.sumallpricedis = 0;\n      this.sumallweight = 0;\n      this.selectunitid = '';\n      this.packingid = '';\n      this.numpackingitem = 0;\n      this.numline = 1;\n      this.weightproduct = 0;\n      this.idsaleoder = '';\n      this.testpara = '';\n      this.model = [];\n      this.numberproductsearch = null;\n      this.searching = false;\n      this.searchFailed = false;\n      this.salecustomer = '';\n      this.priceperunit = '0';\n      this.amount = '0';\n      this.totalweight = '0.0';\n      this.discount = '0';\n      this.iditem = '';\n      this.price = 0;\n      this.allpcs = 0;\n      this.allsumprice = 0.0;\n      this.dis1 = 0.00;\n      this.dis2 = 0.00;\n      this.dis3 = 0.00;\n      this.alldiscount = 0.0;\n      this.finalprice = 0.0;\n      this.unitlist = [];\n      this.deliveryonclick = '';\n      this.deliverytype = [{\n        'deliver': 'รถบริษัท'\n      }, {\n        'deliver': 'รับเอง'\n      }, {\n        'deliver': 'ขนส่ง'\n      }, {\n        'deliver': 'ประเภทการขนส่ง'\n      }];\n      this.paymentlist = [];\n      this.vatlist = [{\n        'vattype': 'VAT ไม่ยื่น'\n      }, {\n        'vattype': 'VAT'\n      }];\n      this.showvat = true;\n      this.idsoedit = '';\n      this.company = 'ป้อนชื่อ หรือ รหัสลูกค้า';\n      this.shownovat = true;\n      this.fromdate = '';\n      this.todate = '';\n      this.locale = 'th';\n      this.locales = listLocales();\n      this.ennablecustomer = false;\n      this.locationnodelie = '';\n      this.idforshow = '';\n      this.idhead = 0;\n      this.time = '';\n      this.dissave = false;\n      this.setpackde = 0;\n      this.Instock = '';\n      this.StockAllList = [];\n      this.search = text$ =>\n      //Autocomplete ลูกค้า\n      text$.pipe(debounceTime(200), map(term => term === '' ? [] : this.customers.filter(v => v.name.toLowerCase().indexOf(term.toLowerCase()) > -1 || v.accountnum.toLowerCase().indexOf(term.toLowerCase()) > -1).slice(0, 50)));\n      this.formatter = x => x.name + ' (' + x.accountnum + ')' + '(' + x.regnum + ')';\n      this.searchpr = text$ =>\n      //Autocomplete รหัสสินค้า\n      text$.pipe(debounceTime(200), map(term => term === '' ? [] : this.productauto.filter(v => v.itemid.toLowerCase().indexOf(term.toLowerCase()) > -1 || v.name.toLowerCase().indexOf(term.toLowerCase()) > -1).slice(0, 50)));\n      this.formatterpr = x => x.itemid;\n      localStorage.removeItem('DataSOderlist');\n      this.bsValue = new Date();\n      this.url = service.geturlservice();\n      this.fromDate = calendar.getPrev(calendar.getToday(), 'd', 30);\n      this.toDate = calendar.getToday();\n      this.fromdate = '';\n      this.todate = '';\n      this.Datatodate = new Date(this.toDate.day, this.toDate.month - 1, this.toDate.year);\n      this.Datafromdate = new Date(this.fromDate.year, this.fromDate.month - 1, this.fromDate.day);\n      this.getdate();\n      this.LoadAllstock();\n      this.model = null;\n      this.time = this.bsValue.getHours() + ':' + this.bsValue.getMinutes() + ':' + this.bsValue.getSeconds();\n      //ดึงข้อฒุลการ เข้าสู่ระบบ\n      //[(ngModel)]=\"alldiscount\"\n      this.Namelogin = JSON.parse(sessionStorage.getItem('login'));\n      this.usseredit = this.Namelogin[0].salegroup;\n      this.setpackde = this.Namelogin[0].pack;\n      if (this.Namelogin == null) {\n        this.router.navigate(['login']);\n      }\n      this.idsoedit = this.route.snapshot.queryParams.idso;\n      if (this.idsoedit == undefined) {\n        localStorage.removeItem('DataSOderreview');\n        localStorage.removeItem('DataSOderlist');\n      } else {\n        this.idsaleoder = this.idsoedit;\n        this.idforshow = this.idsoedit;\n        this.setloadcustomeredit = setInterval(() => this.selecttoeditheader(this.idsoedit), 800); //300\n      }\n      var getdate = '';\n      var day = this.today.getDate();\n      var month = this.today.getMonth() + 1;\n      var year = this.today.getFullYear();\n      if (day.toString().length > 1) {\n        getdate = day.toString();\n      } else {\n        getdate = '0' + day.toString();\n      }\n      if (month.toString().length > 1) {\n        this.dateshipping = year.toString() + '-' + month.toString() + '-' + getdate;\n      } else {\n        this.dateshipping = year.toString() + '-0' + month.toString() + '-' + getdate;\n      }\n      this.loadrunnumberid(this.dateshipping);\n    }\n    getdate() {\n      this.fromdate = `${this.Datafromdate.getFullYear()}-${this.Datafromdate.getMonth() + 1}-${this.Datafromdate.getDate()}`;\n      this.todate = `${this.Datatodate.getFullYear()}-${this.Datatodate.getMonth() + 1}-${this.Datatodate.getDate()}`;\n    }\n    ngOnInit() {\n      if (this.Namelogin[0].accountnum != undefined) {\n        this.getcustomersalefunction(this.Namelogin[0].accountnum);\n        this.ennablecustomer = true;\n        this.getproductid(this.Namelogin[0].salegroup);\n      }\n    }\n    //ดึงข้อมูลการรัน ID Sale Oder จาก Database\n    loadrunnumberid(date) {\n      var text = \"SW\";\n      var zero = '0';\n      var num = 0;\n      var saleid = this.Namelogin[0].salegroup;\n      var month = date.substring(5, 7);\n      var year = date.substring(0, 4);\n      this.http.get(this.url + 'checknumbersaleoder/' + text + '/' + saleid + '/' + year.toString() + '/' + month.toString() + '/' + 0).subscribe(res => {\n        this.getcostomerauto();\n        num = res[0].num + 1;\n        for (var i = 0; i < 3 - JSON.stringify(res[0].num).length; i++) {\n          zero = zero + '0';\n        }\n        this.getrunnumber = zero + num;\n        this.idsaleoder = this.makeidsaleOder(this.getrunnumber, num, date);\n      });\n    }\n    //ดึงรหัสลูกค้ามาใช้ใน Autocomplete\n    getcostomerauto() {\n      var idsale = this.Namelogin[0].salegroup;\n      if (this.Namelogin[0].salegroup === 'admin') {\n        idsale = '%20';\n      } else {\n        idsale = this.Namelogin[0].salegroup;\n      }\n      this.http.get(this.url + 'customerauto/' + idsale).subscribe(res => {\n        this.customers = res;\n      });\n    }\n    //ดึงรหัสสินค้ามาใช้ใน Autocomplete\n    getproductid(accountnum) {\n      this.http.get(this.url + 'productauto/' + accountnum).subscribe(res => {\n        if (res.length > 0) {\n          this.productauto = res;\n        }\n      });\n    }\n    //ตั้งสินค้าให้เป็นของแุถม\n    checkfreeitemfunction(value) {\n      if (value == true) {\n        this.checkadditem = value;\n        this.reprice = parseFloat(this.priceperunit);\n        this.redis1 = this.dis1;\n        this.redis2 = this.dis2;\n        this.redis3 = this.dis3;\n        this.refinalprice = this.finalprice;\n        this.reallpcs = this.allpcs;\n        this.regetunitprice = this.getunitprice;\n        this.realldiscount = this.alldiscount;\n        this.priceperunit = '0';\n        this.dis1 = 0;\n        this.dis2 = 0;\n        this.dis3 = 0;\n        this.finalprice = 0;\n        this.getunitprice = 0;\n        this.alldiscount = 0;\n      } else {\n        this.checkadditem = value;\n        this.priceperunit = this.reprice.toLocaleString(undefined, {\n          minimumFractionDigits: 2,\n          maximumFractionDigits: 2\n        });\n        this.dis1 = this.redis1;\n        this.dis2 = this.redis2;\n        this.dis3 = this.redis3;\n        this.finalprice = this.refinalprice;\n        this.allpcs = this.reallpcs;\n        this.getunitprice = this.regetunitprice;\n        this.alldiscount = this.realldiscount;\n      }\n    }\n    //ดึงข้อมูล Sale Heaader จาก Database มาแก้ไข\n    selecttoeditheader(valueid) {\n      this.http.get(this.url + 'find_saleheader/' + valueid).subscribe(res => {\n        if (res.length > 0) {\n          var daya = new Date();\n          clearInterval(this.setloadcustomeredit);\n          var getdate = res[0].dateid.toString();\n          this.dateshipping = getdate;\n          var day = getdate.substring(0, 2);\n          var month = getdate.substring(3, 5);\n          var year = getdate.substring(6, 10);\n          //this.bsValue.setDate(day);\n          if (day == daya.getDate() && month == daya.getMonth() + 1) {} else {\n            this.bsValue = new Date(year, month - 1, day);\n          }\n          this.remarksoinput = res[0].remark;\n          this.selectedsetsaleheader(res);\n          this.setloadaddress = setInterval(() => this.getcustomersalefunction(this.company), 1000);\n          this.mappaymenttype(res[0].paymenttype);\n          this.usseredit = res[0].SalesId;\n          this.company = res[0].SalesName;\n          this.deliveryonclick = res[0].DlvMode;\n          /*this.Searchcustomeraddress(res[0].InvoiceAccount);*/\n          if (res[0].DlvMode === 'ขนส่ง') {\n            this.deliverytype = [{\n              'deliver': 'รถบริษัท'\n            }, {\n              'deliver': 'รับเอง'\n            }, {\n              'deliver': 'ขนส่ง'\n            }];\n          } else if (res[0].DlvMode === 'รับเอง') {\n            this.deliverytype = [{\n              'deliver': 'รถบริษัท'\n            }, {\n              'deliver': 'ขนส่ง'\n            }, {\n              'deliver': 'รับเอง'\n            }];\n          } else if (res[0].DlvMode === 'รถบริษัท') {\n            this.deliverytype = [{\n              'deliver': 'รับเอง'\n            }, {\n              'deliver': 'ขนส่ง'\n            }, {\n              'deliver': 'รถบริษัท'\n            }];\n          }\n          if (res[0].vattype === 'VAT') {\n            this.vatselect = res[0].vattype;\n            this.vatlist = [{\n              'vattype': 'VAT ไม่ยื่น'\n            }, {\n              'vattype': 'VAT'\n            }];\n          } else if (res[0].vattype === 'NO VAT') {\n            this.vatselect = res[0].vattype;\n            this.vatlist = [{\n              'vattype': 'VAT'\n            }, {\n              'vattype': 'VAT ไม่ยื่น'\n            }];\n          } else if (res[0].vattype === 'VAT ไม่ยื่น') {\n            this.vatselect = res[0].vattype;\n            this.vatlist = [{\n              'vattype': 'VAT'\n            }, {\n              'vattype': 'VAT ไม่ยื่น'\n            }];\n          }\n        }\n      }, error => {});\n      this.openModal(false, 'กำลังโหลดข้อมูล', false);\n    }\n    //สร้าง SaleOder ID ใหม่จากวันที่ ที่เลือก\n    clickdateshipping(value) {\n      this.loadrunnumberid(value);\n    }\n    setsaleheader(id, wh) {\n      this.headerlist = [];\n      this.headerlist.push({\n        idhead: 0,\n        id: id,\n        Custaccount: this.customerno,\n        vattype: this.vatselect,\n        paymenttype: this.selectpayment,\n        deliverytype: this.deliveryonclick,\n        locationno: this.locationno,\n        amount: this.sumallpricedis,\n        discount: this.sumalldiscount,\n        totalweigth: this.sumallweight,\n        note: this.notesoinput,\n        remark: this.remarksoinput,\n        dateshipping: this.dateshipping,\n        wh: wh,\n        locationde: this.locationnodelie,\n        saleid: this.Namelogin[0].salegroup,\n        filetype: '-',\n        filename: '-'\n      });\n    }\n    //เก็บข้อมูล Sale Header ที่ดึงมาจาก Database เก็บใส่ Array\n    selectedsetsaleheader(value) {\n      this.filetype = value[0].filetype;\n      this.filename = value[0].filename;\n      this.idhead = value[0].idhead;\n      this.locationno = value[0].InvAddress;\n      this.locationnodelie = value[0].DeliveryAddress;\n      this.notesoinput = value[0].CustomerRef;\n      this.remarksoinput = value[0].remark;\n      for (var i = 0; i < value.length; i++) {\n        this.headerlist.push({\n          idhead: value[i].idhead,\n          id: value[i].id,\n          Custaccount: value[i].InvoiceAccount,\n          vattype: value[i].TaxGroup,\n          paymenttype: value[i].Payment,\n          deliverytype: value[i].DlvMode,\n          locationno: value[i].InvAddress,\n          amount: value[i].amount,\n          discount: value[i].discount,\n          totalweigth: value[i].totalweigh,\n          note: value[i].notesoinput,\n          remark: value[i].remark,\n          dateshipping: value[i].ReceiptDateRequested,\n          wh: value[i].InventLocationId,\n          locationde: value[i].DeliveryAddress,\n          saleid: this.Namelogin[0].salegroup,\n          filetype: value[i].filetype,\n          filename: value[i].filename\n        });\n      }\n      if (this.idsoedit != '' || this.idsoedit != undefined) {\n        this.lineoderlist = [];\n        this.http.get(this.url + 'find_saleline/' + this.idsoedit).subscribe(res => {\n          if (res.length > 0) {\n            var d1, d2, d3;\n            // alert(JSON.stringify(res));\n            for (var i = 0; i < res.length; i++) {\n              if (res[i].disc1 == null) {\n                d1 = 0;\n              } else {\n                d1 = res[i].disc1;\n              }\n              if (res[i].disc2 == null) {\n                d2 = 0;\n              } else {\n                d2 = res[i].disc2;\n              }\n              if (res[i].disc3 == null) {\n                d3 = 0;\n              } else {\n                d3 = res[i].disc3;\n              }\n              this.lineoderlist.push({\n                idline: res[i].idline,\n                id: res[i].id,\n                linenum: res[i].linenum,\n                custaccount: res[i].CustAccount,\n                iditem: res[i].ItemId,\n                vattype: res[i].TaxItemGroup,\n                nameproduct: res[i].Name,\n                numberpcs: res[i].SalesQty,\n                unitid: res[i].SalesUnit,\n                packingitem: res[i].packqty,\n                totleweight: res[i].totalweight,\n                priceperunit: res[i].PriceUnit,\n                priceproduct: res[i].totaldisc + res[i].LineAmount,\n                discount1: res[i].IVZ_Percent1_CT,\n                discount2: res[i].IVZ_Percent2_CT,\n                discount3: res[i].IVZ_Percent3_CT,\n                sumdiscount: parseFloat(d1) + parseFloat(d2) + parseFloat(d3),\n                sumallprice: res[i].LineAmount,\n                wh: res[i].InventLocationId,\n                checkpcs: res[i].checkpcs,\n                disst: res[i].disstate,\n                eidtable: res[i].eidtable,\n                saleid: res[i].SalesGroup,\n                dateshipping: this.dateshipping + ' ' + this.time\n              });\n              ;\n            }\n            this.idsaleoder = res[0].id;\n            if (this.lineoderlist.length > 0) {\n              this.sumalldiscount = 0;\n              this.sumallpricedis = 0;\n              this.sumallweight = 0;\n              for (var i = 0; i < this.lineoderlist.length; i++) {\n                this.sumalldiscount = this.sumalldiscount + parseFloat(this.lineoderlist[i].sumdiscount.toFixed(2));\n                this.sumallpricedis = this.sumallpricedis + parseFloat(this.lineoderlist[i].sumallprice.toFixed(2));\n                this.sumallweight = this.sumallweight + parseFloat(this.lineoderlist[i].totleweight.toFixed(2));\n              }\n            }\n          }\n        }, errar => {\n          status = errar.status;\n          alert('เกิดปัญหาในการ Process ข้อมูล กรุณาลองใหม่อีกครั้ง');\n          location.reload();\n          //this.openModal(true,'เกิดปัญหาในการ Process ข้อมูล กรุณาลองใหม่อีกครั้ง',false);\n        });\n      }\n    }\n    //ดึงข้อมูล Sale Line จาก Database มาแก้ไข\n    selectsaleoderline() {\n      this.setdefultUnit();\n      if (this.idsoedit == '' || this.idsoedit == undefined) {\n        this.setsaleheader(this.idsaleoder, this.inventlocationid);\n      }\n      this.getproductid(this.customerno);\n      var status = 200;\n      do {\n        if (this.lineoderlist.length < 1) {\n          if (this.deliveryonclick != '') {\n            if (this.idsoedit != '' || this.idsoedit != undefined) {\n              this.lineoderlist = [];\n              this.http.get(this.url + 'find_saleline/' + this.idsoedit).subscribe(res => {\n                var ch = 0;\n                if (res.length > 0) {\n                  // alert(JSON.stringify(res));\n                  for (var i = 0; i < res.length; i++) {\n                    if (this.lineoderlist.length > 0) {\n                      for (var x = 0; x < this.lineoderlist.length; x++) {\n                        if (this.lineoderlist[x].iditem == res[i].ItemId && this.lineoderlist[x].numberpcs == res[i].SalesQty && this.lineoderlist[x].priceperunit == res[i].PriceUnit) {\n                          ch = 1;\n                        }\n                      }\n                      if (ch == 0) {\n                        this.lineoderlist.push({\n                          idline: res[i].idline,\n                          id: res[i].id,\n                          linenum: res[i].linenum,\n                          custaccount: res[i].CustAccount,\n                          iditem: res[i].ItemId,\n                          nameproduct: res[i].Name,\n                          vattype: res[i].TaxItemGroup,\n                          numberpcs: res[i].SalesQty,\n                          unitid: res[i].SalesUnit,\n                          packingitem: res[i].packqty,\n                          totleweight: res[i].totalweight,\n                          priceperunit: res[i].PriceUnit,\n                          priceproduct: res[i].totaldisc + res[i].LineAmount,\n                          discount1: res[i].IVZ_Percent1_CT,\n                          discount2: res[i].IVZ_Percent2_CT,\n                          discount3: res[i].IVZ_Percent3_CT,\n                          sumdiscount: parseFloat(res[i].disc1) + parseFloat(res[i].disc2) + parseFloat(res[i].disc3),\n                          sumallprice: res[i].LineAmount,\n                          wh: res[i].InventLocationId,\n                          checkpcs: res[i].checkpcs,\n                          disst: res[i].disstate,\n                          eidtable: res[i].eidtable,\n                          saleid: res[i].SalesGroup,\n                          dateshipping: this.dateshipping + ' ' + this.time\n                        });\n                      } else {}\n                    } else {\n                      this.lineoderlist.push({\n                        idline: res[i].idline,\n                        id: res[i].id,\n                        linenum: res[i].linenum,\n                        custaccount: res[i].CustAccount,\n                        iditem: res[i].ItemId,\n                        vattype: res[i].TaxItemGroup,\n                        nameproduct: res[i].Name,\n                        numberpcs: res[i].SalesQty,\n                        unitid: res[i].SalesUnit,\n                        packingitem: res[i].packqty,\n                        totleweight: res[i].totalweight,\n                        priceperunit: res[i].PriceUnit,\n                        priceproduct: res[i].totaldisc + res[i].LineAmount,\n                        discount1: res[i].IVZ_Percent1_CT,\n                        discount2: res[i].IVZ_Percent2_CT,\n                        discount3: res[i].IVZ_Percent3_CT,\n                        sumdiscount: parseFloat(res[i].disc1) + parseFloat(res[i].disc2) + parseFloat(res[i].disc3),\n                        sumallprice: res[i].LineAmount,\n                        wh: res[i].InventLocationId,\n                        checkpcs: res[i].checkpcs,\n                        disst: res[i].disstate,\n                        eidtable: res[i].eidtable,\n                        saleid: res[i].SalesGroup,\n                        dateshipping: this.dateshipping + ' ' + this.time\n                      });\n                      ;\n                    }\n                  }\n                  this.idsaleoder = res[0].id;\n                  if (this.lineoderlist.length > 0) {\n                    this.sumalldiscount = 0;\n                    this.sumallpricedis = 0;\n                    this.sumallweight = 0;\n                    for (var i = 0; i < this.lineoderlist.length; i++) {\n                      this.sumalldiscount = this.sumalldiscount + parseFloat(this.lineoderlist[i].sumdiscount.toFixed(2));\n                      this.sumallpricedis = this.sumallpricedis + parseFloat(this.lineoderlist[i].sumallprice.toFixed(2));\n                      this.sumallweight = this.sumallweight + parseFloat(this.lineoderlist[i].totleweight.toFixed(2));\n                    }\n                  }\n                }\n              }, errar => {\n                status = errar.status;\n                alert('เกิดปัญหาในการ Process ข้อมูล กรุณาลองใหม่อีกครั้ง');\n                location.reload();\n                //this.openModal(true,'เกิดปัญหาในการ Process ข้อมูล กรุณาลองใหม่อีกครั้ง',false);\n              });\n            }\n          } else {\n            alert('กรุณาเลือก การขนส่ง');\n            //this.openModal(true,'กรุณาเลือก การขนส่ง',false);\n          }\n        }\n      } while (status == 400);\n    }\n    //เก็บข้อมูล วันที่\n    clickdate(data) {\n      var month = this.bsValue.getMonth() + 1;\n      if (month < 10) {\n        this.dateshipping = this.bsValue.getFullYear() + '-' + '0' + month + '-' + this.bsValue.getUTCDate();\n      } else {\n        this.dateshipping = this.bsValue.getFullYear() + '-' + month + '-' + this.bsValue.getUTCDate();\n      }\n    }\n    //เก็บข้อมูล ประเภทการ ขนส่งสินค้า\n    selectdelivery(value) {\n      this.deliveryonclick = value;\n    }\n    //เก็บข้อมูล ประเภทการชำระเงิน\n    selectpaymentfn(value) {\n      this.selectpayment = value;\n    }\n    //ตั้งค่าข้อมูล ประเภท ขนส่งสินค้า\n    mappaymenttype(paymenttype) {\n      this.selectpayment = paymenttype;\n      /*if(paymenttype==='เงินสด') {\n        this.dis3=3.00;\n        this.selectpayment=paymenttype\n        this.paymentlist=[{'paymenttype':'เงินสด-เครดิต'},{'paymenttype':'เครดิต'},{'paymenttype':'เงินสด'}];\n      } else if (paymenttype==='เครดิต') {\n        this.selectpayment=paymenttype\n        this.paymentlist=[{'paymenttype':'เงินสด-เครดิต'},{'paymenttype':'เงินสด'},{'paymenttype':'เครดิต'}];\n      }\n      */\n      this.http.get(this.url + 'paymentlist').subscribe(res => {\n        this.paymentlist = [];\n        if (res.length > 0) {\n          for (var i = 0; i < res.length; i++) {\n            if (res[i].paymtermid == paymenttype) {\n              this.paymentlist.push({\n                paymtermid: res[i].paymtermid,\n                paymenttype: res[i].paymenttype\n              });\n            } else {\n              this.paymentlist.unshift({\n                paymtermid: res[i].paymtermid,\n                paymenttype: res[i].paymenttype\n              });\n            }\n          }\n        }\n        this.getwarehouse();\n      });\n    }\n    selectlocationde(value) {\n      this.locationnodelie = value;\n    }\n    //ตั้งค่าข้อมูล ประเภทภาษี\n    maptaxgrouptype(vattype) {\n      if (this.idsoedit == undefined) {\n        if (vattype === 'VAT') {\n          this.vatselect = vattype;\n          this.vatlist = [{\n            'vattype': 'VAT ไม่ยื่น'\n          }, {\n            'vattype': 'VAT'\n          }];\n        } else if (vattype === 'NO VAT') {\n          this.vatselect = vattype;\n          this.vatlist = [{\n            'vattype': 'VAT'\n          }, {\n            'vattype': 'VAT ไม่ยื่น'\n          }];\n        } else if (vattype === 'VAT ไม่ยื่น') {\n          this.vatselect = vattype;\n          this.vatlist = [{\n            'vattype': 'VAT'\n          }, {\n            'vattype': 'VAT ไม่ยื่น'\n          }];\n        }\n      }\n    }\n    //ค้าหาที่อยู่ จากชื่อ หรือ รหัสลูกค้า\n    Searchcustomeraddress(valuecustomer) {\n      //alert('Clik');\n      this.daddress = [];\n      this.inaddress = [];\n      this.http.get(this.url + 'customer_list' + '/%20/%20/' + valuecustomer + '/%20/%20').subscribe(res => {\n        if (res.length > 0) {\n          this.customerno = res[0].accountnum;\n          this.getaddressdary(res[0].accountnum);\n          this.getaddressinv(res[0].accountnum);\n          //this.locationno=this.inaddress[0].locationno;\n          this.locationno = res[0].locationno;\n          this.locationnodelie = res[0].locationno;\n        }\n      }, error => {\n        alert('เกิดปัญหาในการ Process ข้อมูล กรุณาลองใหม่อีกครั้ง');\n        //this.openModal(true,'เกิดปัญหาในการ Process ข้อมูล กรุณาลองใหม่อีกครั้ง',false);\n      });\n    }\n    //เก็บข้อมูล เลขที่ ที่อยู่\n    setlocationno(value) {\n      this.locationno = value;\n    }\n    //เก็บข้อมูล ภาษี\n    selectvat(value) {\n      this.vatselect = value;\n      //alert(this.vatselect);\n      if (value === 'VAT ไม่ยื่น') {\n        this.notesoinput = 'บิลเขียว';\n      } else {\n        this.notesoinput = '';\n      }\n    }\n    //อัพเดทข้อมูล การรัน เลขที่ SaleOder\n    updaterunnumber(value) {\n      var urlpost = `${this.url}${'updaterunnumber'}/${value[0].text}/${value[0].saleid}/${value[0].year}/${value[0].month}/${value[0].runnumber}`;\n      this.http.post(urlpost, '').subscribe(res => {\n        if (res == true) {\n          this.getallidsaleoder = [];\n          this.loadrunnumberid(this.dateshipping);\n          alert('บันทึกข้อมูลเสร็จสิ้น');\n          //this.openModal(true,'บันทึกข้อมูลเสร็จสิ้น',true);\n          this.company = '';\n          this.idsoedit = undefined;\n        }\n      });\n    }\n    /*/so/createso/:id/:Custaccount/:vattype/:paymenttype/:deliverytype/:locationno/:amount/:discount/:totalweight/:note/:remark'\n     */\n    //ลบข้อมูล SaleOder เมื่อมีการ อัพเดทข้อมูล\n    deleteebeforupdate() {\n      if (this.idsoedit != undefined) {\n        var urlpost = `${this.url}${'delete_sale_line'}/${this.idsoedit}/${this.Namelogin[0].salegroup}`;\n        this.http.post(urlpost, '').subscribe(res => {\n          this.dissave = false;\n          this.savesaleoderlist();\n        });\n      }\n    }\n    clrscrsaleheader() {\n      this.model = null;\n      this.idsaleoder = '';\n      this.customerno = '';\n      this.vatselect = '';\n      this.selectpayment = '';\n      this.deliveryonclick = '';\n      this.locationno = '';\n      this.sumallpricedis = 0;\n      this.sumalldiscount = 0;\n      this.sumallweight = 0;\n      this.inaddress = [];\n      this.daddress = [];\n      this.notesoinput = '';\n      this.remarksoinput = '';\n      //alert('บันทึกข้อมูลเสร็จสิ้น');\n    }\n    // บันทึกข้อมูล Sale Oder Header\n    savesaleoderlist() {\n      var noteina = '';\n      var noteinb = '';\n      var remarka = '';\n      var remarkb = '';\n      if (this.deliveryonclick == '') {\n        alert('กรุณาเลือก การขนส่ง');\n        //this.openModal(true,'กรุณาเลือก การขนส่ง',false);\n      } else {\n        if (this.remarksoinput == '') {\n          this.remarksoinput = 'N';\n          remarkb = 'N';\n        } else {\n          remarka = this.remarksoinput.replace('/', '-');\n          remarkb = remarka.replace('%', 'เปอร์เซ็น');\n        }\n        if (this.notesoinput == '') {\n          this.notesoinput = 'N';\n          noteinb = 'N';\n        } else {\n          this.notesoinput.replace('/', '-');\n          noteina = this.notesoinput.replace('/', '-');\n          noteinb = noteina.replace('%', 'เปอร์เซ็น');\n        }\n        if (this.wh == '') {\n          this.wh = '';\n        }\n        if (this.headerlist.length > 0) {\n          this.wh = this.headerlist[0].wh;\n        }\n        if (this.Namelogin[0].salegroup == 'admin') {} else {\n          this.usseredit = this.Namelogin[0].salegroup;\n        }\n        this.dateshipping = this.bsValue.getFullYear() + '-' + (parseInt(this.bsValue.getMonth().toString()) + 1) + '-' + this.bsValue.getDate();\n        var body = {\n          Data: [{\n            id: this.idsaleoder,\n            Custaccount: this.customerno,\n            vattype: this.vatselect,\n            paymenttype: this.selectpayment,\n            deliverytype: this.deliveryonclick,\n            locationno: this.locationno,\n            amount: this.sumallpricedis,\n            discount: this.sumalldiscount,\n            totalweight: this.sumallweight,\n            note: noteinb,\n            remark: remarkb,\n            dateshipping: this.dateshipping + ' ' + this.time,\n            saleid: this.usseredit,\n            wh: this.wh,\n            locationde: this.locationnodelie,\n            idhead: this.idhead,\n            filetype: this.filetype,\n            filename: this.filename\n          }]\n        };\n        //alert(this.locationno+'/'+this.locationnodelie);\n        var urlpost = `${this.url}${'createsonew'}`;\n        if (this.daddress != null) {\n          this.http.post(urlpost, body).subscribe(res => {\n            if (res == true) {\n              this.savesalelinefn();\n            } else {}\n          }, err => {\n            alert('ไม่สามารถบันทึกข้อมูลได้กรุณาลองใหม่อีก ครั้ง');\n            return;\n          });\n        } else {\n          alert('กรุณาเลือกลูกค้า');\n          //this.openModal(true,'กรุณาเลือกลูกค้า',false);\n        }\n        this.sumalldiscount = 0;\n        this.sumallpricedis = 0;\n        this.sumallweight = 0;\n        this.remarksoinput = '';\n        this.notesoinput = '';\n      }\n    }\n    /*\n    /so/savesaleline/:id/:linenumber/:custaccount/:itemid/:saleqty/:packqty/:vattype/:price/:percent1/:percent2/:percent3/:lineweight' */\n    //บันทึกข้อมูล SaleLine แบบยังไม่แยก คลังสินค้า\n    savesalelinefn() {\n      this.dateshipping = this.bsValue.getFullYear() + '-' + (parseInt(this.bsValue.getMonth().toString()) + 1) + '-' + this.bsValue.getDate();\n      var i = 0;\n      var line = [];\n      if (this.lineoderlist.length > 0) {\n        for (var i = 0; i < this.lineoderlist.length; i++) {\n          line.push({\n            id: this.lineoderlist[i].id,\n            linenum: this.lineoderlist[i].linenum,\n            custaccount: this.customerno,\n            iditem: this.lineoderlist[i].iditem,\n            numberpcs: this.lineoderlist[i].numberpcs,\n            packingitem: this.lineoderlist[i].packingitem,\n            vattype: this.vatselect,\n            priceperunit: this.lineoderlist[i].priceperunit,\n            discount1: this.lineoderlist[i].discount1,\n            discount2: this.lineoderlist[i].discount2,\n            discount3: this.lineoderlist[i].discount3,\n            totleweight: this.lineoderlist[i].totleweight,\n            dateshipping: this.dateshipping + ' ' + this.time,\n            saleid: this.lineoderlist[i].saleid,\n            checkpcs: this.lineoderlist[i].checkpcs,\n            disst: this.lineoderlist[i].disst,\n            eidtable: this.lineoderlist[i].eidtable,\n            idline: this.lineoderlist[i].idline\n          });\n        }\n      } else {\n        return;\n      }\n      var urlpost = `${this.url}${'savesalelinenew'}`;\n      this.http.post(urlpost, {\n        Data: line\n      }).subscribe(res => {\n        this.clrscrlinelist();\n        this.company = '';\n        this.clrscrsaleheader();\n        this.headerlist = [];\n        if (this.Namelogin[0].accountnum != undefined) {\n          this.getcustomersalefunction(this.Namelogin[0].accountnum);\n        }\n        if (this.idsoedit == undefined) {} else {\n          alert('บันทึกข้อมูลเสร็จสิ้น');\n          this.closeeditlistoder();\n        }\n        this.dissave = false;\n        //this.router.navigate(['/soreview']);\n        this.clrscrlinelist();\n        this.company = '';\n        this.clrscrsaleheader();\n        if (this.Namelogin[0].accountnum != undefined) {\n          this.getcustomersalefunction(this.Namelogin[0].accountnum);\n        }\n        //this.openModal(true,'บันทึกข้อมูลเสร็จสิ้น',true);\n        this.headerlist = [];\n        this.lineoderlist = [];\n        //this.router.navigate(['/soreview']);\n        this.sumalldiscount = 0;\n        this.sumallpricedis = 0;\n        this.sumallweight = 0;\n        this.x++;\n      });\n      /*for (var i=0; i<this.lineoderlist.length; i++) {\n               var urlpost= `${ this.url }${ '/so/savesaleline' }/${ this.lineoderlist[i].id}/${ i+1 }/${ this.inaddress[0].accountnum }/${ this.lineoderlist[i].iditem}/${ this.lineoderlist[i].numberpcs}/${ this.lineoderlist[i].packingitem}/${ this.vatselect }/${ this.lineoderlist[i].priceperunit}/${ this.lineoderlist[i].discount1 }/${ this.lineoderlist[i].discount2 }/${ this.lineoderlist[i].discount3 }/${ this.lineoderlist[i].totleweight}`;\n       alert(urlpost);\n        this.http.post(urlpost,'').subscribe(res =>{\n        if(res !=  true) {\n          alert('SaveLineFail!!!!');\n        }\n        });\n             }*/\n    }\n    //ดึงข้อมูลราคาสินค้าจากรหัสสินค้า\n    Searchproductlist(productid) {\n      var body = {\n        inventlocationid: '',\n        itemgroupid: '',\n        itemid: productid,\n        catname: '',\n        name: ''\n      };\n      this.http.post(this.url + 'product_list', body).subscribe(res => {\n        if (res.length > 0) {\n          this.eidtable = res[0].eidtable;\n          if (res[0].eidtable == 0) {\n            this.ennableeditprice = true;\n          } else {\n            this.ennableeditprice = false;\n          }\n          this.productlist = res;\n          this.checkpromotionfunction(productid);\n          for (var i = 0; i < this.promotionlist.length; i++) {\n            if (this.promotionlist[i].amount > 0) {\n              this.priceperunit = this.promotionlist[i].amount.toString();\n            }\n          }\n          this.iditem = res[0].itemid;\n          this.numproduct = parseInt(JSON.stringify(res[0].taxpackagingqty));\n          this.productname = res[0].name;\n          this.weightproduct = res[0].netweight;\n        }\n      });\n    }\n    setdefultUnit() {\n      if (this.setpackde == 1) {\n        this.getcheckpcs = 'U';\n      } else {\n        this.getcheckpcs = 'P';\n      }\n    }\n    LoadAllstock() {\n      var _this = this;\n      return _asyncToGenerator(function* () {\n        yield _this.http.get('http://119.59.112.47/SoAPI/api/values/GetStockCountGetAllGroup').subscribe(res => {\n          _this.StockAllList = res;\n        });\n      })();\n    }\n    Realtimeinstock(itemid) {\n      var stock = [];\n      if (this.StockAllList.length > 0) {\n        stock = [this.StockAllList.find(x => x.itemid == itemid)];\n        //alert(JSON.stringify(stock));\n        return stock[0].avastock;\n      }\n    }\n    viewsstockitem(itemid) {\n      this.Instock = this.Realtimeinstock(itemid) == '0' ? '0' : this.Realtimeinstock(itemid);\n      // this.http.get<any[]>('http://119.59.112.47/SoAPI/api/values/getstockcountget/'+itemid).subscribe(res=>{\n      //   res[0].avastock;\n      // //alert(res);\n      // });\n    }\n    //(focusin)=\"Searchitem()\"\n    //ค้นหาสินค้าจากรหัสสินค้า\n    Searchitem() {\n      this.numberproductsearch = '';\n      this.priceperunit = '0';\n      var forword = '';\n      var low = '';\n      this.freeitem = false;\n      // if(this.setpackde==1){\n      //   this.getcheckpcs='U';\n      // }else{\n      //   this.getcheckpcs='P';\n      // }\n      //alert(this.productidsearch.itemid);\n      if (this.productidsearch.itemid != undefined && this.productidsearch.itemid != '') {\n        //alert(1);\n        forword = this.productidsearch.itemid;\n        low = this.productidsearch.itemid;\n      } else if (this.productidsearch.itemid == undefined) {\n        forword = this.showitem;\n        low = this.showitem;\n      } else {}\n      //alert(forword+'/'+this.showitem);\n      /*if(this.showitem===\"รหัสสินค้า\"){\n             } else {\n        forword=this.showitem;\n        low=this.showitem;\n      }*/\n      /*this.iditem=idproduct;*/\n      //alert(forword);\n      var body = {\n        itemid: forword,\n        itemidor: low\n      };\n      this.http.post(this.url + 'uom', body).subscribe(res => {\n        this.unitlist = [];\n        if (res.length > 0) {\n          this.checkadditem = false;\n          this.inventlocationid = res[0].inventlocationid;\n          this.checkunitprice = res[1].unit;\n          for (var i = 0; i < res.length; i++) {\n            if (res[i].unit == this.getcheckpcs) {\n              this.unitlist.push({\n                inventlocationid: res[i].inventlocationid,\n                unitid: res[i].unitid,\n                unit: res[i].unit,\n                itemid: res[i].itemid\n              });\n            } else {\n              this.unitlist.unshift({\n                inventlocationid: res[i].inventlocationid,\n                unitid: res[i].unitid,\n                unit: res[i].unit,\n                itemid: res[i].itemid\n              });\n            }\n          }\n          this.selectunitid = res[0].unitid;\n          this.packingid = res[1].unitid;\n          this.Searchproductlist(res[0].itemid);\n          this.viewsstockitem(res[0].itemid);\n        } else {\n          /*this.openModal(true,'ไม่พบสินค้า',false);*/\n        }\n      });\n    }\n    Searchitemclick(value) {\n      var body = {\n        itemid: value,\n        itemidor: ''\n      };\n      this.http.post(this.url + 'uom', body).subscribe(res => {\n        this.unitlist = [];\n        if (res.length > 0) {\n          clearInterval(this.loaduom);\n          this.inventlocationid = res[0].inventlocationid;\n          this.checkunitprice = res[1].unit;\n          this.checkadditem = false;\n          for (var i = 0; i < res.length; i++) {\n            if (res[i].unit == this.getcheckpcs) {\n              this.unitlist.push({\n                inventlocationid: res[i].inventlocationid,\n                unitid: res[i].unitid,\n                unit: res[i].unit,\n                itemid: res[i].itemid\n              });\n            } else {\n              this.unitlist.unshift({\n                inventlocationid: res[i].inventlocationid,\n                unitid: res[i].unitid,\n                unit: res[i].unit,\n                itemid: res[i].itemid\n              });\n            }\n          }\n          this.selectunitid = res[0].unitid;\n          this.packingid = res[1].unitid;\n          this.Searchproductlist(res[0].itemid);\n          this.viewsstockitem(res[0].itemid);\n        } else {\n          /*this.openModal(true,'ไม่พบสินค้า',false);*/\n        }\n      });\n      /*this.iditem=idproduct;*/\n    }\n    Searchcustomersalekey(event) {\n      this.getcustomersalefunction(event.target.value);\n    }\n    //ค้นหาลูกค้าจากชื่อหรือ รหัสลูกค้า พร้อมเรียก Function ดึงที่อยู่\n    getcustomersalefunction(value) {\n      //alert('sdasd');\n      this.daddress = [];\n      this.inaddress = [];\n      this.http.get(this.url + 'customer_list/%20/%20/' + value + '/%20/%20').subscribe(res => {\n        clearInterval(this.setloadaddress);\n        if (res.length > 0) {\n          this.dissave = true;\n          if (this.idsoedit == undefined) {\n            if (res[0].DLVMODE == '01-ขนส่ง' || res[0].DLVMODE == '03-ขนส่ง') {\n              this.deliveryonclick = 'ขนส่ง';\n              this.deliverytype = [{\n                'deliver': 'รถบริษัท'\n              }, {\n                'deliver': 'รับเอง'\n              }, {\n                'deliver': 'ขนส่ง'\n              }];\n            } else if (res[0].DLVMODE == '02รถบริษัท') {\n              this.deliveryonclick = 'รถบริษัท';\n              this.deliverytype = [{\n                'deliver': 'รับเอง'\n              }, {\n                'deliver': 'ขนส่ง'\n              }, {\n                'deliver': 'รถบริษัท'\n              }];\n            } else if (res[0].DLVMODE == '03-รับเอง') {\n              this.deliveryonclick = 'รับเอง';\n              this.deliverytype = [{\n                'deliver': 'รถบริษัท'\n              }, {\n                'deliver': 'ขนส่ง'\n              }, {\n                'deliver': 'รับเอง'\n              }];\n            } else {\n              this.deliveryonclick = '';\n              this.deliverytype = [{\n                'deliver': 'รถบริษัท'\n              }, {\n                'deliver': 'รับเอง'\n              }, {\n                'deliver': 'ขนส่ง'\n              }, {\n                'deliver': 'ประเภทการขนส่ง'\n              }];\n            }\n            this.remarksoinput = res[0].MEMO;\n            this.locationno = res[0].locationno;\n            this.locationnodelie = res[0].locationno;\n          }\n          clearInterval(this.setloadaddress);\n          this.customerno = res[0].accountnum;\n          this.getaddressdary(res[0].accountnum);\n          this.getaddressinv(res[0].accountnum);\n          if (this.Namelogin[0].accountnum != undefined) {\n            this.company = res[0].name;\n          }\n          this.accountnum = res[0].accountnum;\n          this.accoutnreration = res[0].linedisc;\n          this.pricegroup = res[0].pricegroup;\n          // this.locationno=this.inaddress[0].locationno;\n          this.maptaxgrouptype(res[0].vattype);\n          //this.mappaymenttype(res[0].paymtermid);\n          this.selectedpaymentmap = res[0].paymtermid;\n          this.openModal(false, 'กำลังโหลดข้อมูล กรุณารอสักครู่....', false);\n        }\n        this.disproductline = false;\n      }, error => {\n        this.openModal(true, 'กำลังโหลดข้อมูล กรุณารอสักครู่....', false);\n        status = error.status;\n      });\n      this.dissave = true;\n    }\n    Searchcustomersalekeychen(event) {\n      this.getcustomersalefunction(event.target.value);\n    }\n    //ดึงที่อยู่ วางบิล\n    getaddressinv(accountnum) {\n      this.inaddress = [];\n      var body = {\n        accountnum: accountnum\n      };\n      this.http.post(this.url + 'get_invaddress', body).subscribe(res => {\n        this.inaddress = [];\n        if (res.length > 0) {\n          this.inaddress = [];\n          for (var i = 0; i < res.length; i++) {\n            if (this.idsoedit == undefined) {\n              if (res[i].DPRIMARY == 1) {\n                this.locationno = res[i].locationno;\n                this.inaddress.push({\n                  name: res[i].description,\n                  accountnum: res[i].accountnum,\n                  address: res[i].address,\n                  locationno: res[i].locationno\n                });\n              } else {\n                this.inaddress.unshift({\n                  name: res[i].description,\n                  accountnum: res[i].accountnum,\n                  address: res[i].address,\n                  locationno: res[i].locationno\n                });\n              }\n            } else {\n              if (res[i].locationno == this.locationno) {\n                this.locationno = res[i].locationno;\n                this.inaddress.push({\n                  name: res[i].description,\n                  accountnum: res[i].accountnum,\n                  address: res[i].address,\n                  locationno: res[i].locationno\n                });\n              } else {\n                this.inaddress.unshift({\n                  name: res[i].description,\n                  accountnum: res[i].accountnum,\n                  address: res[i].address,\n                  locationno: res[i].locationno\n                });\n              }\n            }\n          }\n          this.locationno = this.inaddress[this.inaddress.length - 1].locationno;\n        }\n      });\n      //this.locationno=this.inaddress[this.inaddress.length-1].locationno;\n    }\n    //ดึงที่อยู่ขนส่งสินค้า\n    getaddressdary(accountnum) {\n      this.daddress = [];\n      var body = {\n        accountnum: accountnum\n      };\n      this.http.post(this.url + 'get_dlvaddress', body).subscribe(res => {\n        this.daddress = [];\n        var chedlv = 0;\n        if (res.length > 0) {\n          for (var x = 0; x < res.length; x++) {\n            if (res[x].DPRIMARY == 2) {\n              chedlv = 1;\n            } else {\n              chedlv = 0;\n            }\n          }\n          for (var i = 0; i < res.length; i++) {\n            if (this.idsoedit == undefined) {\n              if (chedlv == 1) {\n                if (res[i].DPRIMARY == 2) {\n                  this.locationnodelie = res[i].locationno;\n                  this.daddress.push({\n                    name: res[i].description,\n                    accountnum: res[i].accountnum,\n                    address: res[i].address,\n                    locationno: res[i].locationno\n                  });\n                } else {\n                  this.daddress.unshift({\n                    name: res[i].description,\n                    accountnum: res[i].accountnum,\n                    address: res[i].address,\n                    locationno: res[i].locationno\n                  });\n                }\n              } else {\n                if (res[i].DPRIMARY == 1) {\n                  this.locationnodelie = res[i].locationno;\n                  this.daddress.push({\n                    name: res[i].description,\n                    accountnum: res[i].accountnum,\n                    address: res[i].address,\n                    locationno: res[i].locationno\n                  });\n                } else {\n                  this.daddress.unshift({\n                    name: res[i].description,\n                    accountnum: res[i].accountnum,\n                    address: res[i].address,\n                    locationno: res[i].locationno\n                  });\n                }\n              }\n            } else {\n              if (res[i].locationno == this.locationnodelie) {\n                this.locationnodelie = res[i].locationno;\n                this.daddress.push({\n                  name: res[i].description,\n                  accountnum: res[i].accountnum,\n                  address: res[i].address,\n                  locationno: res[i].locationno\n                });\n              } else {\n                this.daddress.unshift({\n                  name: res[i].description,\n                  accountnum: res[i].accountnum,\n                  address: res[i].address,\n                  locationno: res[i].locationno\n                });\n              }\n            }\n          }\n          this.locationnodelie = this.daddress[this.daddress.length - 1].locationno;\n        }\n      });\n      //alert(this.locationnodelie);\n      //this.locationnodelie=this.daddress[this.daddress.length-1].locationno;\n    }\n    //คำนวนโปรโมชั่น\n    checkpromotionfunction(value) {\n      var stbefor = this.accoutnreration.replace('+', '');\n      var st = stbefor.replace('%', '');\n      this.promotionlist = [];\n      //   if(this.accoutnreration==''){\n      // this.accoutnreration='%20';\n      //   }\n      // if(this.pricegroup==''){\n      //   this.pricegroup='%20';\n      // }\n      //     if(st==''){\n      // st='%20';\n      //     }\n      var body = {\n        itemid: value,\n        accountrelation: st,\n        pricegroup: this.pricegroup,\n        accountnum: this.accountnum\n      };\n      this.http.post(this.url + 'map_promotion', body).subscribe(res => {\n        if (res.length > 0) {\n          this.upperdis = res[0].friendlyname;\n          for (var i = 0; i < res.length; i++) {\n            this.promotionlist.push({\n              itemrelation: res[i].itemrelation,\n              accountrelation: res[i].accountrelation,\n              accountcode: res[i].accountcode,\n              quantityamountfrom: res[i].quantityamountfrom,\n              quantityamountto: res[i].quantityamountto,\n              amount: res[i].amount,\n              percent1: res[i].percent1,\n              percent2: res[i].percent2,\n              relation: res[i].relation,\n              percent3: res[i].percent3\n            });\n          }\n        }\n        this.http.post(this.url + 'map_promotion_price', body).subscribe(res => {\n          //alert(JSON.stringify(res));\n          if (res.length > 0) {\n            for (var x = 0; x < res.length; x++) {\n              this.promotionlist.push({\n                itemrelation: res[x].itemrelation,\n                accountrelation: res[x].accountrelation,\n                accountcode: res[x].accountcode,\n                quantityamountfrom: res[x].quantityamountfrom,\n                quantityamountto: res[x].quantityamountto,\n                amount: res[x].amount,\n                percent1: res[x].percent1,\n                percent2: res[x].percent2,\n                relation: res[x].relation,\n                percent3: res[x].percent3\n              });\n            }\n          }\n        });\n      });\n    }\n    //คำนวน ราคาสินค้าจาก จำนวน\n    checknumproduct(event) {\n      var packitem = event;\n      var pcsitem = packitem * this.numproduct;\n      this.alldiscount = 0.0;\n      if (this.getcheckpcs === 'P') {\n        this.allpcs = pcsitem;\n        this.numpackingitem = event;\n        this.checkpacking = 0;\n      } else {\n        this.allpcs = packitem;\n        this.numpackingitem = packitem / this.numproduct;\n        this.checkpacking = packitem % this.numproduct;\n        pcsitem = packitem;\n      }\n      //this.checkpacking=packitem%this.numproduct;\n      var disc1 = 0.00,\n        disc2 = 0.00,\n        disc3 = 0.00,\n        pricec = 0.00,\n        pricecpa = 0.00;\n      var priceperdis1 = 0,\n        priceperdis2 = 0,\n        priceperdis3 = 0,\n        sumdis = 0.00,\n        allprice = 0.00,\n        sumpricebedis1 = 0,\n        sumpricebedis2 = 0,\n        sumpricebedis3 = 0,\n        finalpri,\n        sumdisdd = 0;\n      if (this.iditem == '') {\n        alert('กรุณาป้อนรหัสสินค้า');\n        return;\n        //this.openModal(true,'กรุณาป้อนรหัสสินค้า',false);\n      } else {\n        if (this.promotionlist.length > 0) {\n          for (var i = 0; i < this.promotionlist.length; i++) {\n            if (this.promotionlist[i].amount > 0 && this.promotionlist[i].relation == '4') {\n              this.checkadditem = true;\n              pricec = this.promotionlist[i].amount;\n            } else if (this.accoutnreration === '%20') {\n              this.accoutnreration = '';\n            }\n            if (pcsitem >= this.promotionlist[i].quantityamountfrom && pcsitem < this.promotionlist[i].quantityamountto && this.promotionlist[i].relation == '5') {\n              disc1 = this.promotionlist[i].percent1;\n              disc2 = this.promotionlist[i].percent2;\n              disc3 = this.promotionlist[i].percent3;\n              this.olddis1 = this.promotionlist[i].percent1;\n              this.olddis2 = this.promotionlist[i].percent2;\n              this.olddis3 = this.promotionlist[i].percent3;\n              //alert(1);\n              //alert(disc3);\n            } else if (pcsitem >= this.promotionlist[i].quantityamountfrom && this.promotionlist[i].quantityamountto == 0 && this.promotionlist[i].relation == '5') {\n              //alert(2);\n              disc1 = this.promotionlist[i].percent1;\n              disc2 = this.promotionlist[i].percent2;\n              disc3 = this.promotionlist[i].percent3;\n              //alert(disc1+'/'+pcsitem);\n              this.olddis1 = this.promotionlist[i].percent1;\n              this.olddis2 = this.promotionlist[i].percent2;\n              this.olddis3 = this.promotionlist[i].percent3;\n            }\n            // else if(this.accoutnreration == this.promotionlist[i].accountrelation && pcsitem >= this.promotionlist[i].quantityamountfrom && pcsitem > this.promotionlist[i].quantityamountto  && this.promotionlist[i].relation=='5') {\n            //  disc1=this.promotionlist[i].percent1;\n            //   disc2=this.promotionlist[i].percent2;\n            //   disc3=this.promotionlist[i].percent3;\n            //   this.olddis1=this.promotionlist[i].percent1;\n            //   this.olddis2=this.promotionlist[i].percent2;\n            //   this.olddis3=this.promotionlist[i].percent3;\n            // }\n          }\n        } else {\n          pricec = parseFloat(this.priceperunit);\n        }\n        this.dis1 = disc1;\n        this.dis2 = disc2;\n        if (this.dis3 > 0) {\n          this.dis3 = 0;\n        }\n        if (this.dis3 == 0) {\n          this.dis3 = disc3;\n        }\n        if ((this.selectpayment === 'S60' || this.selectpayment === 'N01' || this.selectpayment === 'N07' || this.selectpayment === 'TT' || this.selectpayment === 'COD') && disc3 == 0) {\n          disc3 = 3.00;\n          this.dis3 = 3.00;\n        }\n        if (this.checkunitprice == 'U') {\n          this.priceperunit = pricec.toString();\n        } else {\n          this.priceperunit = pricec.toString();\n          this.getpackingprice = pricec * this.numproduct;\n          //alert('BBSS'+parseFloat(pricec.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2})).toFixed(2));\n        }\n        this.getunitprice = pricec;\n        allprice = pcsitem * pricec;\n        if (disc1 > 0 && disc3 < 1 || disc1 > 0 && disc2 > 0 && disc3 > 0) {\n          priceperdis1 = allprice * disc1 / 100;\n          sumpricebedis1 = allprice - priceperdis1;\n          finalpri = parseFloat(sumpricebedis1.toFixed(2));\n          if (disc2 > 0) {\n            priceperdis2 = sumpricebedis1 * disc2 / 100;\n            sumpricebedis2 = sumpricebedis1 - priceperdis2;\n            finalpri = parseFloat(sumpricebedis2.toFixed(2));\n            if (disc3 > 0) {\n              priceperdis3 = sumpricebedis2 * disc3 / 100;\n              sumpricebedis3 = sumpricebedis2 - priceperdis3;\n              finalpri = parseFloat(sumpricebedis3.toFixed(2));\n            }\n          }\n          sumdis = priceperdis1 + priceperdis2 + priceperdis3;\n          sumdisdd = sumdis;\n          this.alldiscount = sumdisdd;\n          this.finalprice = finalpri;\n        } else if (disc1 > 0 && disc3 > 0) {\n          priceperdis1 = allprice * disc1 / 100;\n          sumpricebedis1 = allprice - priceperdis1;\n          finalpri = parseFloat(sumpricebedis1.toFixed(2));\n          if (disc3 > 0) {\n            priceperdis3 = sumpricebedis1 * disc3 / 100;\n            sumpricebedis3 = sumpricebedis1 - priceperdis3;\n            finalpri = parseFloat(sumpricebedis3.toFixed(2));\n          }\n          sumdis = priceperdis1 + priceperdis2 + priceperdis3;\n          sumdisdd = sumdis;\n          this.alldiscount = sumdisdd;\n          this.finalprice = finalpri;\n        } else if (disc1 < 1 && disc2 < 1 && disc3 > 0) {\n          priceperdis1 = allprice * disc3 / 100;\n          sumpricebedis1 = allprice - priceperdis1;\n          finalpri = parseFloat(sumpricebedis1.toFixed(2));\n          sumdis = priceperdis1 + priceperdis2 + priceperdis3;\n          sumdisdd = sumdis;\n          this.alldiscount = sumdisdd;\n          this.finalprice = finalpri;\n        } else {\n          this.finalprice = allprice;\n        }\n      }\n      //alert(this.finalprice);\n      //alert(this.finalprice);\n    }\n    editpriceperunit(value) {\n      this.priceperunit = value;\n      this.getunitprice = value;\n      this.editdiscount();\n    }\n    //แก้ไขส่วนลด\n    editdiscount() {\n      var pcsitem = this.allpcs;\n      var disc1, disc2, disc3, pricec;\n      var priceperdis1 = 0,\n        priceperdis2 = 0,\n        priceperdis3 = 0,\n        sumdis = 0,\n        allprice = 0,\n        sumpricebedis1 = 0,\n        sumpricebedis2 = 0,\n        sumpricebedis3 = 0,\n        finalpri,\n        sumdisdd = 0.00;\n      disc1 = this.dis1;\n      disc2 = this.dis2;\n      disc3 = this.dis3;\n      pricec = this.getunitprice;\n      allprice = pcsitem * pricec;\n      if (disc1 > 0 && disc3 < 1 || disc1 > 0 && disc2 > 0 && disc3 > 0) {\n        priceperdis1 = allprice * disc1 / 100;\n        sumpricebedis1 = allprice - priceperdis1;\n        finalpri = sumpricebedis1;\n        if (disc2 > 0) {\n          priceperdis2 = sumpricebedis1 * disc2 / 100;\n          sumpricebedis2 = sumpricebedis1 - priceperdis2;\n          finalpri = sumpricebedis2;\n          if (disc3 > 0) {\n            priceperdis3 = sumpricebedis2 * disc3 / 100;\n            sumpricebedis3 = sumpricebedis2 - priceperdis3;\n            finalpri = sumpricebedis3;\n          }\n        }\n        sumdis = priceperdis1 + priceperdis2 + priceperdis3;\n        sumdisdd = sumdis;\n        this.alldiscount = sumdisdd;\n        this.finalprice = finalpri;\n      } else if (disc1 > 0 && disc3 > 0) {\n        priceperdis1 = allprice * disc1 / 100;\n        sumpricebedis1 = allprice - priceperdis1;\n        finalpri = sumpricebedis1;\n        if (disc3 > 0) {\n          priceperdis3 = sumpricebedis1 * disc3 / 100;\n          sumpricebedis3 = sumpricebedis1 - priceperdis3;\n          finalpri = sumpricebedis3;\n        }\n        sumdis = priceperdis1 + priceperdis2 + priceperdis3;\n        sumdisdd = sumdis;\n        this.alldiscount = sumdisdd;\n        this.finalprice = finalpri;\n      } else if (disc1 < 1 && disc2 < 1 && disc3 > 0) {\n        priceperdis1 = allprice * disc3 / 100;\n        sumpricebedis1 = allprice - priceperdis1;\n        finalpri = sumpricebedis1;\n        sumdis = priceperdis1 + priceperdis2 + priceperdis3;\n        sumdisdd = sumdis;\n        this.alldiscount = sumdisdd;\n        this.finalprice = finalpri;\n      } else {\n        this.finalprice = allprice;\n      }\n      if (disc1 == 0 && disc2 == 0 && disc3 == 0) {\n        this.alldiscount = 0.00;\n      }\n    }\n    clrscrlinelist() {\n      this.iditem = '';\n      this.productname = '';\n      this.allpcs = 0;\n      this.selectunitid = '';\n      this.numpackingitem = 0;\n      this.priceperunit = '0';\n      this.dis1 = 0;\n      this.dis2 = 0;\n      this.dis3 = 0;\n      this.alldiscount = 0;\n      this.finalprice = 0;\n      this.productidsearch = null;\n      this.numberproductsearch = null;\n      this.unitlist = [];\n      this.showitem = 'รหัสสินค้า';\n      this.realldiscount = 0;\n      this.reallpcs = 0;\n      this.regetunitprice = 0;\n      this.reprice = 0;\n      this.redis1 = 0;\n      this.redis2 = 0;\n      this.redis3 = 0;\n      this.refinalprice = 0;\n      this.checkbuttonsave = false;\n      this.checkadditem = false;\n    }\n    //สร้าง SaleOder ID\n    makeidsaleOder(value, numvalue, date) {\n      var text = \"SW-\";\n      var zero = '0';\n      var saleid = this.Namelogin[0].salegroup;\n      var month = date.substring(5, 7);\n      var ye = date.substring(0, 4);\n      var year = ye + 543;\n      var monst = month;\n      var numst = this.getrunnumber;\n      /*if(month<10){\n      monst='0'+month.toString();\n      }*/\n      /*for(var i=0;i<this.getrunnumber.length;i++){\n      zero =zero+'0';\n      }*/\n      this.getallidsaleoder.push({\n        text: 'SW',\n        saleid: saleid,\n        year: ye,\n        month: month,\n        runnumber: numvalue\n      });\n      if (this.idsoedit == undefined) {\n        this.idforshow = text + year.toString().substring(2, 4) + monst + saleid + '-' + value;\n      }\n      return text + year.toString().substring(2, 4) + monst + saleid + '-' + value;\n    }\n    /*:id/:linenumber/:custaccount/:itemid/:saleqty/:packqty/:price/:percent1/:percent2/:percent3/:lineweight''*/\n    //เพิ่มสินค้าลง SaleLine\n    addsaleline() {\n      if (this.lineoderlist.length > 0) {\n        var chitem = 0;\n        for (var i = 0; i < this.lineoderlist.length; i++) {\n          if (this.lineoderlist[i].iditem == this.iditem) {\n            chitem = 1;\n          }\n        }\n        if (chitem == 1 && this.checkbuttonsave == false) {\n          if (confirm('สินค้าชินนี้มีอยู่แล้ว ต้องการเพิ่มสินค้า ใช่ หรือ ไม่')) {} else {\n            return;\n          }\n        }\n      }\n      var disst = '000';\n      if (this.getpromotionedit == true) {\n        this.checkadditem = true;\n        if (this.promotionlist.length > 0) {\n          for (var i = 0; i < this.promotionlist.length; i++) {\n            if (this.accoutnreration === '%20') {\n              this.accoutnreration = '';\n            }\n            if (this.accoutnreration == this.promotionlist[i].accountrelation && this.allpcs >= this.promotionlist[i].quantityamountto && this.allpcs < this.promotionlist[i].quantityamountto && this.promotionlist[i].relation == '5') {\n              this.olddis1 = this.promotionlist[i].percent1;\n              this.olddis2 = this.promotionlist[i].percent2;\n              this.olddis3 = this.promotionlist[i].percent3;\n            } else if (this.accoutnreration == this.promotionlist[i].accountrelation && this.allpcs >= this.promotionlist[i].quantityamountfrom && this.allpcs < this.promotionlist[i].quantityamountto && this.promotionlist[i].relation == '5') {\n              this.olddis1 = this.promotionlist[i].percent1;\n              this.olddis2 = this.promotionlist[i].percent2;\n              this.olddis3 = this.promotionlist[i].percent3;\n            } else if (this.accoutnreration == this.promotionlist[i].accountrelation && this.allpcs >= this.promotionlist[i].quantityamountfrom && this.allpcs > this.promotionlist[i].quantityamountto && this.promotionlist[i].relation == '5') {\n              this.olddis1 = this.promotionlist[i].percent1;\n              this.olddis2 = this.promotionlist[i].percent2;\n              this.olddis3 = this.promotionlist[i].percent3;\n            }\n          }\n        }\n      }\n      if (this.upperdis != '') {\n        if (this.dis1 > parseInt(this.upperdis)) {\n          disst = '100';\n        } else {\n          disst = '000';\n        }\n      }\n      /*if(this.olddis1 != this.dis1 && this.olddis2 == this.dis2 && this.olddis3 == this.dis3){\n        disst='100';\n      } else if(this.olddis1 != this.dis1 && this.olddis2 != this.dis2 && this.olddis3 != this.dis3){\n        disst='111';\n      } else if(this.olddis1 != this.dis1 && this.olddis2 == this.dis2 && this.olddis3 != this.dis3){\n        disst='101';\n      } else if(this.olddis1 != this.dis1 && this.olddis2 != this.dis2 && this.olddis3 == this.dis3){\n        disst='110';\n      } else if(this.olddis1 == this.dis1 && this.olddis2 == this.dis2 && this.olddis3 != this.dis3){\n        disst='001';\n      } else if(this.olddis1 == this.dis1 && this.olddis2 != this.dis2 && this.olddis3 != this.dis3){\n        disst='011';\n      }\n      if(this.selectpayment==='N01' || this.selectpayment==='N07'|| this.selectpayment==='TT'){\n      if(disst==='111' && this.dis3==3){\n      disst='110';\n      } else if(disst==='101'&& this.dis3==3){\n      disst='100';\n      } else if(disst==='011'&& this.dis3==3) {\n      disst='010';\n      } else if(disst==='001'&& this.dis3==3) {\n      disst='000';\n      }\n      }*/\n      if (this.checkpacking > 0) {\n        alert('สินค้าไม่เต็ม Pack');\n        //this.openModal(true,'สินค้าไม่เต็ม Pack ต้องการ เพิ่มรายการใช่หรือไม่',false);\n        //return;\n      }\n      clearInterval(this.loaduom);\n      var packpcs;\n      // alert(this.priceperunit+'/'+this.checkadditem+'/'+this.numberproductsearch);\n      this.freeitem = false;\n      if (this.iditem == '') {\n        alert('กรุณาป้อนรหัสสินค้า');\n        return;\n        //this.openModal(true,'กรุณาป้อนรหัสสินค้า',false);\n      } else if (this.checkadditem == false || parseFloat(this.priceperunit) < 1 && this.checkadditem != true || this.priceperunit == '0' && this.checkadditem != true || this.numberproductsearch == '' || this.numberproductsearch == 0) {\n        alert('สินค้าราคาเป็น 0 ขายไม่ได้');\n        return;\n        //this.openModal(true,'สินค้าราคาเป็น 0 ขายไม่ได้',false);\n      } else if (this.checkbuttonsave == true) {\n        this.lineoderlist[this.index].id = this.idsaleoder;\n        this.lineoderlist[this.index].iditem = this.iditem;\n        this.lineoderlist[this.index].nameproduct = this.productname;\n        this.lineoderlist[this.index].numberpcs = this.allpcs;\n        this.lineoderlist[this.index].unitid = this.selectunitid;\n        this.lineoderlist[this.index].packingitem = this.numpackingitem;\n        this.lineoderlist[this.index].priceperunit = this.getunitprice;\n        this.lineoderlist[this.index].priceproduct = this.allpcs * this.getunitprice;\n        this.lineoderlist[this.index].totleweight = this.numpackingitem * this.weightproduct;\n        this.lineoderlist[this.index].discount1 = this.dis1;\n        this.lineoderlist[this.index].discount2 = this.dis2;\n        this.lineoderlist[this.index].discount3 = this.dis3;\n        this.lineoderlist[this.index].sumdiscount = this.alldiscount;\n        this.lineoderlist[this.index].sumallprice = this.finalprice;\n        //this.lineoderlist[this.index].iditem = this.productidsearch.itemid;\n        this.lineoderlist[this.index].checkpcs = this.getcheckpcs;\n        this.lineoderlist[this.index].disst = disst;\n        //this.lineoderlist[this.index].packingitem = this.numberproductsearch;\n        this.iditem = '';\n        this.productname = '';\n        this.allpcs = 0;\n        this.selectunitid = '';\n        this.numpackingitem = 0;\n        this.priceperunit = '0';\n        this.dis1 = 0;\n        this.dis2 = 0;\n        this.dis3 = 0;\n        this.alldiscount = 0;\n        this.finalprice = 0;\n        this.productidsearch = null;\n        this.numberproductsearch = null;\n        this.unitlist = [];\n        this.showitem = 'รหัสสินค้า';\n        this.realldiscount = 0;\n        this.reallpcs = 0;\n        this.regetunitprice = 0;\n        this.reprice = 0;\n        this.redis1 = 0;\n        this.redis2 = 0;\n        this.redis3 = 0;\n        this.refinalprice = 0;\n        this.checkbuttonsave = false;\n        this.checkadditem = false;\n        this.getcheckpcs = 'P';\n        this.checkpacking = 0;\n        this.getpromotionedit = false;\n        disst = '000';\n        this.olddis1 = 0;\n        this.olddis2 = 0;\n        this.olddis3 = 0;\n        // alert(JSON.stringify(this.lineoderlist[0].iditem));\n      } else {\n        /*this.headerlist[0].wh=this.inventlocationid;*/\n        this.lineoderlist.push({\n          idline: 0,\n          id: this.idsaleoder,\n          linenum: this.lineoderlist.length,\n          custaccount: this.accountnum,\n          iditem: this.iditem,\n          vattype: this.vatselect,\n          nameproduct: this.productname,\n          numberpcs: this.allpcs,\n          unitid: this.selectunitid,\n          packingitem: this.numpackingitem,\n          totleweight: this.weightproduct * this.numpackingitem,\n          priceperunit: this.getunitprice,\n          priceproduct: this.allpcs * this.getunitprice,\n          discount1: this.dis1,\n          discount2: this.dis2,\n          discount3: this.dis3,\n          sumdiscount: this.alldiscount,\n          sumallprice: this.finalprice,\n          wh: this.inventlocationid,\n          checkpcs: this.getcheckpcs,\n          disst: disst,\n          eidtable: this.eidtable,\n          saleid: this.Namelogin[0].salegroup,\n          dateshipping: this.dateshipping + ' ' + this.time\n        });\n        this.iditem = '';\n        this.productname = '';\n        this.allpcs = 0;\n        this.selectunitid = '';\n        this.numpackingitem = 0;\n        this.priceperunit = '0';\n        this.dis1 = 0;\n        this.dis2 = 0;\n        this.dis3 = 0;\n        this.alldiscount = 0;\n        this.finalprice = 0;\n        this.productidsearch = null;\n        this.numberproductsearch = null;\n        this.inventlocationid = '';\n        this.unitlist = [];\n        this.showitem = 'รหัสสินค้า';\n        this.realldiscount = 0;\n        this.reallpcs = 0;\n        this.regetunitprice = 0;\n        this.reprice = 0;\n        this.redis1 = 0;\n        this.redis2 = 0;\n        this.redis3 = 0;\n        this.refinalprice = 0;\n        this.checkadditem = false;\n        this.getcheckpcs = 'P';\n        this.checkpacking = 0;\n        this.getpromotionedit = false;\n        disst = '000';\n        this.olddis1 = 0;\n        this.olddis2 = 0;\n        this.olddis3 = 0;\n      }\n      this.deleteunitlist();\n      this.numline++;\n      /*var urlpost=`${this.url+'/so/savesaleline/'}${this.idsaleoder}`\n      this.http.post(urlpost,'')*/\n      if (this.lineoderlist.length > 0) {\n        this.sumalldiscount = 0;\n        this.sumallpricedis = 0;\n        this.sumallweight = 0;\n        for (var i = 0; i < this.lineoderlist.length; i++) {\n          this.sumalldiscount = this.sumalldiscount + parseFloat(this.lineoderlist[i].sumdiscount.toFixed(2));\n          this.sumallpricedis = this.sumallpricedis + parseFloat(this.lineoderlist[i].sumallprice.toFixed(2));\n          this.sumallweight = this.sumallweight + parseFloat(this.lineoderlist[i].totleweight.toFixed(2));\n        }\n      }\n      //alert(this.getcheckpcs);\n      this.runninnglinenumsaleline(this.lineoderlist);\n      this.setdefultUnit();\n    }\n    runninnglinenumsaleline(order) {\n      if (order.length > 0) {\n        for (var i = 0; i < order.length; i++) {\n          order[i].linenum = i + 1;\n        }\n        this.lineoderlist = order;\n      } else {\n        return;\n      }\n    }\n    deleteunitlist() {\n      if (this.unitlist.length > 0) {\n        for (var i = 0; i < this.unitlist.length; i++) {\n          this.unitlist.pop();\n        }\n      }\n    }\n    //ลบ Sale Line ออกจาก Array\n    deletelistlineoderclick(value, id) {\n      if (id > 0) {\n        var urlpost = `${this.url}${'delete_sale_linebyidline'}/${id}/${this.Namelogin[0].salegroup}`;\n        this.http.post(urlpost, '').subscribe(res => {\n          // this.setinheader= setInterval(this.savesaleoderlistsave(),);\n          this.lineoderlist.splice(value, 1);\n          this.lineoderlistsave.splice(value, 1);\n          alert('ลบ Sale Line เสร็จสิ้น');\n          if (this.lineoderlist.length > 0 || this.lineoderlist.length < 1) {\n            this.sumalldiscount = 0;\n            this.sumallpricedis = 0;\n            this.sumallweight = 0;\n            for (var i = 0; i < this.lineoderlist.length; i++) {\n              this.sumalldiscount = this.sumalldiscount + parseFloat(this.lineoderlist[i].sumdiscount.toFixed(2));\n              this.sumallpricedis = this.sumallpricedis + parseFloat(this.lineoderlist[i].sumallprice.toFixed(2));\n              this.sumallweight = this.sumallweight + parseFloat(this.lineoderlist[i].totleweight.toFixed(2));\n            }\n          }\n        });\n      } else {\n        this.lineoderlist.splice(value, 1);\n        this.lineoderlistsave.splice(value, 1);\n        if (this.lineoderlist.length > 0 || this.lineoderlist.length < 1) {\n          this.sumalldiscount = 0;\n          this.sumallpricedis = 0;\n          this.sumallweight = 0;\n          for (var i = 0; i < this.lineoderlist.length; i++) {\n            this.sumalldiscount = this.sumalldiscount + parseFloat(this.lineoderlist[i].sumdiscount.toFixed(2));\n            this.sumallpricedis = this.sumallpricedis + parseFloat(this.lineoderlist[i].sumallprice.toFixed(2));\n            this.sumallweight = this.sumallweight + parseFloat(this.lineoderlist[i].totleweight.toFixed(2));\n          }\n        }\n      }\n      if (this.lineoderlist.length > 0 || this.lineoderlist.length < 1) {\n        this.sumalldiscount = 0;\n        this.sumallpricedis = 0;\n        this.sumallweight = 0;\n        for (var i = 0; i < this.lineoderlist.length; i++) {\n          this.sumalldiscount = this.sumalldiscount + parseFloat(this.lineoderlist[i].sumdiscount.toFixed(2));\n          this.sumallpricedis = this.sumallpricedis + parseFloat(this.lineoderlist[i].sumallprice.toFixed(2));\n          this.sumallweight = this.sumallweight + parseFloat(this.lineoderlist[i].totleweight.toFixed(2));\n        }\n      }\n      this.runninnglinenumsaleline(this.lineoderlist);\n    }\n    //สลับการแสดงผล ราคาสินค้า Packing / Unit\n    getunitid(value) {\n      var st = value.target.value;\n      var ch = st.substring(0, 1);\n      this.checkunitprice = ch;\n      if (ch == 'P') {\n        this.getcheckpcs = 'P';\n        //this.priceperunit=this.getpackingprice;\n      } else {\n        this.getcheckpcs = 'U';\n        this.selectunitid = st.substring(1);\n        //this.priceperunit=this.getunitprice;\n      }\n      this.checknumproduct(this.numberproductsearch);\n      this.editdiscount();\n    }\n    foreditlistlineoderclick(value, i) {\n      this.getpromotionedit = i;\n      this.checkadditem = true;\n      this.loaduom = setInterval(() => this.Searchitemclick(this.iditem), 500); //150\n      this.editlistlineoderclick(value);\n    }\n    //คลิกแก้ไข Sale Oderline\n    editlistlineoderclick(value) {\n      var numpcs = this.lineoderlist[value].numberpcs;\n      this.Searchproductlist(this.lineoderlist[value].iditem);\n      this.clrscrlinelist();\n      this.checkbuttonsave = true;\n      this.index = value;\n      this.idsaleoder = this.lineoderlist[value].id;\n      this.iditem = this.lineoderlist[value].iditem;\n      this.showitem = this.lineoderlist[value].iditem;\n      this.productname = this.lineoderlist[value].nameproduct;\n      this.allpcs = this.lineoderlist[value].numberpcs;\n      this.selectunitid = this.lineoderlist[value].unitid;\n      if (this.lineoderlist[value].checkpcs === 'P') {\n        this.numproduct = this.lineoderlist[value].numberpcs / this.lineoderlist[value].packingitem;\n        this.getcheckpcs = 'P';\n        this.checkunitprice = 'P';\n        var pricepack = this.lineoderlist[value].priceperunit;\n        this.numberproductsearch = this.lineoderlist[value].packingitem;\n        var sdas = pricepack;\n        this.priceperunit = sdas.toString();\n        //this.checknumproduct(this.lineoderlist[value].packingitem);\n        this.getunitprice = pricepack;\n      } else {\n        this.lineoderlist[value].priceperunit;\n        this.getcheckpcs = 'U';\n        this.checkunitprice = 'U';\n        this.numberproductsearch = this.lineoderlist[value].numberpcs;\n        this.priceperunit = this.lineoderlist[value].priceperunit.toString();\n        // this.checknumproduct(this.lineoderlist[value].numberpcs);\n        this.getunitprice = this.lineoderlist[value].priceperunit;\n      }\n      this.numpackingitem = this.lineoderlist[value].packingitem;\n      this.dis1 = this.lineoderlist[value].discount1;\n      this.dis2 = this.lineoderlist[value].discount2;\n      this.dis3 = this.lineoderlist[value].discount3;\n      this.alldiscount = this.lineoderlist[value].sumdiscount;\n      this.finalprice = this.lineoderlist[value].sumallprice;\n      this.productidsearch = this.lineoderlist[value].iditem;\n      this.numproduct = this.lineoderlist[value].numberpcs / this.lineoderlist[value].packingitem;\n    }\n    //โหลดข้อมูลคลังสินค้าจาก Database\n    getwarehouse() {\n      this.http.get(this.url + 'getwarehouse').subscribe(res => {\n        this.getwh = res;\n      }, error => {\n        this.openModal(true, 'เกิดปัญหาในการ Process ข้อมูล กรุณาลองใหม่อีกครั้ง', false);\n      });\n    }\n    checklength(valelength, numlength) {\n      if (valelength.length > numlength) {\n        alert('จำนวนข้อมูล ต้องไม่เกิน ' + numlength + ' ตัวอักษร');\n      }\n    }\n    //สร้าง Sale Oder แยกคัลงสินค้าและ บันทึกข้อมูล\n    /* savesaleoderbywherehouse(){\n           this.warehouselist=[];\n     if(this.getwh.length>0){\n         for(var i=0;i<this.getwh.length;i++){\n     this.warehouselist.push({\n       wh:this.getwh[i].inventlocationid,\n       num:i+1,\n       sumalldiscount:0,\n       sumallpricedis:0,\n       sumallweight:0\n     });\n     }*/\n    /* alert(JSON.stringify(this.lineoderlist));\n     alert(JSON.stringify(this.headerlist));*/\n    /* var weight;\n     var discount;\n     var allprice;\n    for(var x=0;x<this.warehouselist.length;x++){\n      for(var i=0;i<this.lineoderlist.length;i++){\n       weight=this.lineoderlist[i].totleweight;\n       discount=this.lineoderlist[i].sumdiscount;\n       allprice=this.lineoderlist[i].sumallprice;\n       if(this.lineoderlist[i].wh==this.warehouselist[x].wh && this.lineoderlist[i].eidtable==0){\n    this.warehouselist[x].sumalldiscount=parseFloat(this.warehouselist[x].sumalldiscount.toString())+parseFloat(this.lineoderlist[i].sumdiscount.toString());\n    this.warehouselist[x].sumallpricedis=parseFloat(this.warehouselist[x].sumallpricedis.toString())+parseFloat(this.lineoderlist[i].sumallprice.toString());\n    this.warehouselist[x].sumallweight=parseFloat(this.warehouselist[x].sumallweight.toString())+parseFloat(this.lineoderlist[i].totleweight.toString());\n       } else if(this.lineoderlist[i].eidtable==1){\n         this.warehouselist[0].sumalldiscount=parseFloat(this.warehouselist[0].sumalldiscount.toString())+parseFloat(this.lineoderlist[i].sumdiscount.toString());\n         this.warehouselist[0].sumallpricedis=parseFloat(this.warehouselist[0].sumallpricedis.toString())+parseFloat(this.lineoderlist[i].sumallprice.toString());\n         this.warehouselist[0].sumallweight=parseFloat(this.warehouselist[0].sumallweight.toString())+parseFloat(this.lineoderlist[i].totleweight.toString());\n       }\n      }\n    }\n     if(this.warehouselist.length>1){\n    for (var i=0;i<this.warehouselist.length;i++){\n    if(this.warehouselist[i].sumalldiscount == 0 && this.warehouselist[i].sumallpricedis == 0 && this.warehouselist[i].sumallweight == 0){\n    } else {\n     this.headerlistsave.push({\n       idsaleoder: this.headerlist[0].idsaleoder+'-'+this.warehouselist[i].num,\n       customerno:this.headerlist[0].customerno ,\n       vatselect:this.headerlist[0].vatselect,\n       selectpayment:this.headerlist[0].selectpayment,\n       deliveryonclick:this.headerlist[0].deliveryonclick,\n       locationno:this.headerlist[0].locationno,\n       sumallpricedis:this.warehouselist[i].sumallpricedis,\n       sumalldiscount:this.warehouselist[i].sumalldiscount,\n       sumallweight:this.warehouselist[i].sumallweight,\n       notesoinput:this.headerlist[0].notesoinput,\n       remarksoinput:this.headerlist[0].remarksoinput,\n       dateshipping:this.headerlist[0].dateshipping,\n       wh:this.warehouselist[i].wh\n     });\n    }\n    }\n    for(var x=0;x<this.headerlistsave.length;x++){\n     var id=this.headerlistsave[x].idsaleoder;\n     if(this.headerlistsave.length<=1){\n       if(id.length==16){\n         this.headerlistsave[x].idsaleoder=id.substring(0,16);\n       } else if(id.length==17){\n         this.headerlistsave[x].idsaleoder=id.substring(0,17);\n       } else if(id.length==15){\n         this.headerlistsave[x].idsaleoder=id.substring(0,15);\n       } else if(id.length==14){\n         this.headerlistsave[x].idsaleoder=id.substring(0,14);\n       }\n      }\n    for(var i=0;i<this.lineoderlist.length;i++){\n      var ch=0;\n     if(this.lineoderlist[i].wh==this.headerlistsave[x].wh){\n          this.lineoderlistsave.push({\n           id:this.headerlistsave[x].idsaleoder,\n           line:this.lineoderlist[i].line,\n           iditem: this.lineoderlist[i].iditem,\n           nameproduct:this.lineoderlist[i].nameproduct,\n           numberpcs:this.lineoderlist[i].numberpcs,\n           unitid: this.lineoderlist[i].unitid,\n           packingitem:this.lineoderlist[i].packingitem,\n           totleweight:this.lineoderlist[i].totleweight,\n           priceperunit:this.lineoderlist[i].priceperunit,\n           priceproduct:this.lineoderlist[i].priceproduct,\n           discount1:this.lineoderlist[i].discount1,\n           discount2:this.lineoderlist[i].discount2,\n           discount3:this.lineoderlist[i].discount3,\n           sumdiscount:this.lineoderlist[i].sumdiscount,\n           sumallprice:this.lineoderlist[i].sumallprice,\n           wh:this.lineoderlist[i].wh,\n           checkpcs:this.lineoderlist[i].checkpcs,\n           disst:this.lineoderlist[i].disst,\n           eidtable: this.lineoderlist[i].eidtable\n                 })\n       }\n    }\n    }\n    //alert(JSON.stringify(this.lineoderlistsave));\n    //alert(JSON.stringify(this.headerlistsave));\n    //this.this.idsaleoder();\n    this.deleteebeforupdatesave(this.idsoedit);\n    this.setinheader=setInterval(() => this.savesaleoderlistsave(), 300);\n    }else {\n    }\n    } else {\n    alert('เกิดข้อผิดพลาดกรุณาลองใหม่อีกครั้ง');\n    //this.openModal(true,'เกิดข้อผิดพลาดกรุณาลองใหม่อีกครั้ง',false);\n    }\n    console.log(JSON.stringify(this.headerlistsave));\n    } */\n    //บันทึกขอมูล Sale Line แบบ แยก คลังสินค้า\n    /*savesalelinebywarehouselist() {\n      var i=0;\n           var urlpost= `${ this.url }${ 'savesaleline' }/${ this.lineoderlistsave[0].id}/${ this.x }/${ this.inaddress[0].accountnum }/${ this.lineoderlistsave[0].iditem}/${ this.lineoderlistsave[0].numberpcs}/${ this.lineoderlistsave[0].packingitem}/${ this.vatselect }/${ this.lineoderlistsave[0].priceperunit}/${ this.lineoderlistsave[0].discount1 }/${ this.lineoderlistsave[0].discount2 }/${ this.lineoderlistsave[0].discount3 }/${ this.lineoderlistsave[0].totleweight}/${ this.dateshipping }/${ this.Namelogin[0].salegroup }/${this.lineoderlistsave[0].checkpcs}/${this.lineoderlistsave[0].disst}/${this.lineoderlistsave[0].eidtable}`;\n        this.http.post(urlpost,'').subscribe(res =>{\n      if(res !=  true) {\n        clearInterval(this.setin);\n        this.openModal(true,'Save Fail'+ JSON.stringify(res),false);\n      }  else {\n       this.deletelistlineodersave(0);\n    if(this.lineoderlistsave.length==0) {\n      clearInterval(this.setin);\n         this.headerlist=[];\n      this.company='';\n      this.clrscrsaleheader();\n         //this.openModal(true,'บันทึกข้อมูลเสร็จสิ้น',true);\n      this.deleteebeforupdatesave(this.idsaleoder);\n    } else if(this.idsoedit!=undefined && this.lineoderlist.length==0){\n      clearInterval(this.setin);\n      this.clrscrsaleheader();\n      this.clrscrlinelist();\n         if(this.Namelogin[0].accountnum != undefined){\n        this.getcustomersalefunction(this.Namelogin[0].accountnum);\n        }\n      alert('บันทึกข้อมูลเสร็จสิ้น');\n      //this.openModal(true,'บันทึกข้อมูลเสร็จสิ้น',true);\n      this.deleteebeforupdatesave(this.idsaleoder);\n      this.company='';\n      this.headerlist=[];\n      this.headerlistsave=[];\n      this.router.navigate(['/soreview']);\n    }\n    this.sumalldiscount=0;\n    this.sumallpricedis=0;\n    this.sumallweight=0;\n    this.x++;\n      }\n      })\n     \n       }*/\n    deletelistlineodersave(value) {\n      this.lineoderlistsave.splice(value, 1);\n      this.lineoderlist.splice(value, 1);\n      if (this.lineoderlist.length > 0 || this.lineoderlist.length < 1) {\n        this.sumalldiscount = 0;\n        this.sumallpricedis = 0;\n        this.sumallweight = 0;\n        for (var i = 0; i < this.lineoderlist.length; i++) {\n          this.sumalldiscount = this.sumalldiscount + parseFloat(this.lineoderlist[i].sumdiscount.toFixed(2));\n          this.sumallpricedis = this.sumallpricedis + parseFloat(this.lineoderlist[i].sumallprice.toFixed(2));\n          this.sumallweight = this.sumallweight + parseFloat(this.lineoderlist[i].totleweight.toFixed(2));\n        }\n      }\n    }\n    //บันทึกข้อมูล Sale header แบบ แยกคลังสินค้า\n    /*  savesaleoderlistsave() {\n           if(this.deliveryonclick=='') {\n          alert('กรุณาเลือก การขนส่ง');\n          //this.openModal(true,'กรุณาเลือก การขนส่ง',false);\n        } else{\n          if(this.remarksoinput=='') {\n            this.remarksoinput='N';\n          }\n          if(this.notesoinput=='') {\n            this.notesoinput='N';\n          }\n         if(this.wh==''){\n           this.wh='%20';\n         }\n         if(this.headerlistsave[0].notesoinput==''){\n           this.headerlistsave[0].notesoinput=='N';\n         }\n         if(this.headerlistsave[0].remarksoinput==''){\n           this.headerlistsave[0].remarksoinput='N';\n         }\n       var urlpost=`${ this.url }${ 'createso' }/${ this.headerlistsave[0].idsaleoder }/${ this.headerlistsave[0].customerno }/${ this.headerlistsave[0].vatselect }/${ this.headerlistsave[0].selectpayment }/${ this.headerlistsave[0].deliveryonclick }/${ this.locationno }/${ this.headerlistsave[0].sumallpricedis }/${ this.headerlistsave[0].sumalldiscount }/${ this.headerlistsave[0].sumallweight }/${ this.notesoinput }/${ this.remarksoinput }/${ this.headerlistsave[0].dateshipping }/${ this.Namelogin[0].salegroup }/${ this.headerlistsave[0].wh }`;\n              if(this.daddress != null) {\n                 this.http.post(urlpost,'').subscribe(res => {\n                   if(res==true) {\n                      this.headerlistsave.splice(0,1);\n                    if(this.headerlistsave.length==0){\n                      clearInterval(this.setinheader);\n                    this.setin=setInterval(() => this.savesalelinebywarehouselist(), 300);\n                    this.sumalldiscount=0;\n                    this.sumallpricedis=0;\n                    this.sumallweight=0;\n                   this.remarksoinput='';\n                   this.notesoinput='';\n                       }\n                   } else {\n                     alert(JSON.stringify(res));\n                   }\n                 })\n               } else {\n                 alert('กรุณาเลือกลูกค้า');\n                 //this.openModal(true,'กรุณาเลือกลูกค้า',false);\n               }\n           }\n     \n      }\n      */\n    //ลบข้อมูล SaleOder หลังจาก บันทึกข้อมูล Sale Oder แบบแยกคลังสินคาแล้ว\n    deleteebeforupdatesave(value) {\n      if (this.idsoedit != undefined) {\n        var urlpost = `${this.url}${'delete_sale_line'}/${value}`;\n        this.http.post(urlpost, '').subscribe(res => {\n          // this.setinheader= setInterval(this.savesaleoderlistsave(),);\n        });\n      } else {}\n    }\n    closeeditlistoder() {\n      this.router.navigate(['/soreview']);\n    }\n    CheckValueRemark(value) {\n      if (value == '') {\n        this.remarksoinput = 'BY';\n      }\n    }\n    convertnumber(number) {\n      var parts = number.toFixed(2).split(\".\");\n      var num = parts[0].replace(/(\\d)(?=(\\d{3})+(?!\\d))/g, \"$1,\") + (parts[1] ? \".\" + parts[1] : \"\");\n      return num;\n    }\n    //สั่งพิมใบ สั่งซื้อ\n    print() {\n      let popupWin;\n      popupWin = window.open('', '_blank');\n      popupWin.document.open();\n      popupWin.document.write(`\n        <html>\n        <head>\n          <title>ใบแปะหน้ากล่อง</title>\n          <meta http-equiv=\"Content-Type\" content=\"text/html; charset=UTF-8\" />\n          <meta http-equiv=\"Content-Type\" content=\"text/html; charset=iso-8859-1\">\n    <meta http-equiv=\"Content-Language\" content=\"en-us\">\n    <meta http-equiv=\"Content-Script-Type\" content=\"text/javascript\">\n    <meta name=\"GENERATOR\" content=\"TrackInternet._Default Class\">\n\n          <script src=\"https://code.jquery.com/jquery-3.3.1.js\" ></script>\n    <script src=\"https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.14.3/umd/popper.min.js\" ></script>\n    <script src=\"https://stackpath.bootstrapcdn.com/bootstrap/4.1.3/js/bootstrap.min.js\"></script>\n          <style>\n    .textcen{\n    text-align: center;\n    }\n    .he{\n      border-width:5px;\n    border-style:solid;\n    border-color: black;\n    }\n          </style>\n        </head>\n    <body onload=\"window.print();window.close()\">\n    <div class=\"container-fluid \">\n\n                <div class=\"col-md-12\">\n                <div class=\" he \">\n                <h1 class=\"textcen\" style=\" font-size: 50px\">*** กรุณาอย่าวางของหนักทับ ***</h1>\n                   </div>\n                   <div class=\"textcen p-1\">\n                       <span class=\"textcen\" ><h2 style=\" font-size: 45px\"></h2></span>\n                   </div>\n                   <div class=\" textcen p-1\">\n                       <div class=\"textcen\" ><h2 style=\" font-size: 40px\"></h2></div>\n                   </div>\n                   <div class=\"textcen p-1\">\n                       <div class=\"textcen\" ><h2 style=\" font-size: 40px\">\n                        </h2></div>\n                   </div>\n                   <div class=\" he\">\n                       <span class=\"textcen\"  ><h1 style=\" font-size: 50px\">*** กรุณาอย่าวางของหนักทับ ***</h1> </span>\n                   </div>\n\n                </div>\n\n\n        </div>\n    </body>\n      </html>`);\n      popupWin.document.close();\n    }\n    //เปิดแจ้งเตือน\n    openModal(open, text, load) {\n      this.mdlSampleIsOpen = open;\n      this.alt = text;\n      this.checkreload = load;\n    }\n    //ปิดแจ้งเตือน\n    closemodel(cl) {\n      this.mdlSampleIsOpen = false;\n      if (this.checkreload == true) {\n        this.router.navigate(['sorecord']);\n        this.notesoinput = '';\n        this.remarksoinput = '';\n        this.inaddress = [];\n        this.daddress = [];\n        this.model = [];\n      }\n    }\n    applyLocale(pop) {\n      this.localeService.use(this.locale);\n      pop.hide();\n      pop.show();\n    }\n    static {\n      this.ɵfac = function EditsaloderreviewComponent_Factory(t) {\n        return new (t || EditsaloderreviewComponent)(i0.ɵɵdirectiveInject(i1.HttpClient), i0.ɵɵdirectiveInject(i2.WebapiService), i0.ɵɵdirectiveInject(i3.ActivatedRoute), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i4.FormBuilder), i0.ɵɵdirectiveInject(i5.NgbCalendar), i0.ɵɵdirectiveInject(i6.BsLocaleService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: EditsaloderreviewComponent,\n        selectors: [[\"app-editsaloderreview\"]],\n        decls: 195,\n        vars: 61,\n        consts: [[\"rt\", \"\"], [\"rtpr\", \"\"], [1, \"container-fluid\", 2, \"padding-right\", \"5px\", \"padding-left\", \"5px\"], [2, \"padding-top\", \"60px\"], [1, \"p-sm-1\", \"col-xs-12\", \"col-12\", \"col-md-12\", \"bg-secondary\", \"text-white\", \"text-center\"], [\"id\", \"accordion\"], [1, \"card\"], [\"id\", \"headingOne\", 1, \"card-header\", 2, \"padding-top\", \"0px\", \"padding-bottom\", \"0px\"], [1, \"form-row\"], [1, \"mb-0\"], [\"data-toggle\", \"collapse\", \"data-target\", \"#collapseOne\", \"aria-expanded\", \"true\", \"aria-controls\", \"collapseOne\", 1, \"btn\", \"btn-link\"], [1, \"col\", 2, \"padding-top\", \"4px\"], [\"class\", \"btn btn-primary btn-sm font-weight-light\", \"type\", \"button\", \"style\", \"width:45px;height:26px;padding:0px\", 3, \"click\", 4, \"ngIf\"], [1, \"text-info\", 2, \"margin-left\", \"2%\"], [\"id\", \"collapseOne\", \"aria-labelledby\", \"headingOne\", \"data-parent\", \"#accordion\", 1, \"collapse\", \"show\"], [1, \"card-body\"], [1, \"container-fluid\"], [\"novalidate\", \"\", 1, \"needs-validation\"], [1, \"col-md-4\", \"mb-3\"], [\"tooltip\", \"\\u0E1B\\u0E49\\u0E2D\\u0E19\\u0E0A\\u0E37\\u0E48\\u0E2D \\u0E2B\\u0E23\\u0E37\\u0E2D \\u0E23\\u0E2B\\u0E31\\u0E2A\\u0E25\\u0E39\\u0E01\\u0E04\\u0E49\\u0E32\", \"triggers\", \"focus\", \"id\", \"typeahead-template\", \"type\", \"text\", \"name\", \"model\", 1, \"form-control\", \"form-control-sm\", 3, \"ngModelChange\", \"disabled\", \"placeholder\", \"ngModel\", \"ngbTypeahead\", \"resultTemplate\", \"inputFormatter\"], [\"tooltip\", \"\\u0E40\\u0E25\\u0E37\\u0E2D\\u0E01\\u0E17\\u0E35\\u0E48\\u0E2D\\u0E22\\u0E39\\u0E48 \\u0E27\\u0E32\\u0E07\\u0E1A\\u0E34\\u0E25\", 1, \"custom-select\", \"custom-select-sm\", 3, \"change\"], [\"selected\", \"\", 3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"tooltip\", \"\\u0E40\\u0E25\\u0E37\\u0E2D\\u0E01\\u0E17\\u0E35\\u0E48\\u0E2D\\u0E22\\u0E39\\u0E48 \\u0E02\\u0E19\\u0E2A\\u0E48\\u0E07\", 1, \"custom-select\", \"custom-select-sm\", 3, \"change\"], [1, \"col-md-1\", \"mb-3\"], [\"tooltip\", \"\\u0E40\\u0E25\\u0E37\\u0E2D\\u0E01\\u0E20\\u0E32\\u0E29\\u0E35\", 1, \"custom-select\", \"custom-select-sm\", 3, \"change\"], [1, \"col-md-1\", \"mb-1\"], [\"tooltip\", \"\\u0E40\\u0E25\\u0E37\\u0E2D\\u0E01\\u0E1B\\u0E23\\u0E30\\u0E40\\u0E20\\u0E17\\u0E01\\u0E32\\u0E23\\u0E02\\u0E19\\u0E2A\\u0E48\\u0E07\", 1, \"custom-select\", \"custom-select-sm\", 3, \"change\"], [1, \"col-md-2\", \"mb-2\", \"form-group\"], [\"type\", \"text\", \"tooltip\", \"Ship Date\", \"placeholder\", \"DD/MM/YYYY\", \"bsDatepicker\", \"\", 1, \"form-control\", \"form-control-sm\", 3, \"bsValueChange\", \"bsValue\", \"value\"], [\"tooltip\", \"\\u0E40\\u0E25\\u0E37\\u0E2D\\u0E01\\u0E01\\u0E32\\u0E23\\u0E0A\\u0E33\\u0E23\\u0E30\\u0E40\\u0E07\\u0E34\\u0E19\", 1, \"custom-select\", \"custom-select-sm\", 3, \"change\"], [1, \"col-md-3\", \"mb-2\"], [\"maxlength\", \"60\", \"tooltip\", \"Note \\u0E20\\u0E32\\u0E22\\u0E43\\u0E19\", \"tooltip-show-trigger\", \"focusin\", \"tooltip-hide-trigger\", \"focusout\", \"name\", \"notesoinput\", \"type\", \"text\", \"id\", \"notesoinput\", \"placeholder\", \"Note \\u0E20\\u0E32\\u0E22\\u0E43\\u0E19\", 1, \"form-control\", \"form-control-sm\", 3, \"ngModelChange\", \"input\", \"ngModel\"], [\"tooltip\", \"\\u0E2B\\u0E21\\u0E32\\u0E22\\u0E40\\u0E2B\\u0E15\\u0E38\", \"maxlength\", \"255\", \"name\", \"remarksoinput\", \"type\", \"text\", \"id\", \"remarksoinput\", \"placeholder\", \"\\u0E2B\\u0E21\\u0E32\\u0E22\\u0E40\\u0E2B\\u0E15\\u0E38\", 1, \"form-control\", \"form-control-sm\", 3, \"ngModelChange\", \"focusout\", \"input\", \"ngModel\"], [\"id\", \"headingTwo\", 1, \"card-header\", 2, \"padding-top\", \"0px\", \"padding-bottom\", \"0px\"], [1, \"mb-0\", 2, \"vertical-align\", \"central\"], [\"data-toggle\", \"collapse\", \"data-target\", \"#collapseTwo\", \"aria-expanded\", \"false\", \"aria-controls\", \"collapseTwo\", 1, \"btn\", \"btn-link\", \"collapsed\", 3, \"click\", \"disabled\"], [1, \"col\", 2, \"padding-top\", \"7px\"], [\"class\", \"text-success\", 4, \"ngIf\"], [\"id\", \"collapseTwo\", \"aria-labelledby\", \"headingTwo\", \"data-parent\", \"#accordion\", 1, \"collapse\"], [1, \"col-md-2\", \"mb-2\"], [\"id\", \"typeahead-templatep\", \"tooltip\", \"\\u0E1B\\u0E49\\u0E2D\\u0E19 \\u0E23\\u0E2B\\u0E31\\u0E2A\\u0E2A\\u0E34\\u0E19\\u0E04\\u0E49\\u0E32\", \"triggers\", \"focus\", \"type\", \"text\", \"name\", \"productidsearch\", 1, \"form-control\", \"form-control-sm\", 3, \"focusout\", \"ngModelChange\", \"placeholder\", \"ngModel\", \"ngbTypeahead\", \"resultTemplate\", \"inputFormatter\"], [\"type\", \"number\", \"tooltip\", \"\\u0E1B\\u0E49\\u0E2D\\u0E19 \\u0E08\\u0E33\\u0E19\\u0E27\\u0E19\\u0E2A\\u0E34\\u0E19\\u0E04\\u0E49\\u0E32\", \"triggers\", \"focus\", \"id\", \"validationTooltip01\", \"placeholder\", \"\\u0E08\\u0E33\\u0E19\\u0E27\\u0E19\", 1, \"form-control\", \"form-control-sm\", 3, \"ngModelChange\", \"focusout\", \"ngModel\"], [\"tooltip\", \"\\u0E40\\u0E25\\u0E37\\u0E2D\\u0E01\\u0E2B\\u0E19\\u0E48\\u0E27\\u0E22\\u0E19\\u0E31\\u0E1A\", 1, \"custom-select\", \"custom-select-sm\", 3, \"change\"], [\"type\", \"text\", \"id\", \"validationTooltip01\", \"tooltip\", \"\\u0E23\\u0E32\\u0E04\\u0E32\\u0E15\\u0E31\\u0E49\\u0E07\", \"placeholder\", \"\\u0E23\\u0E32\\u0E04\\u0E32/\\u0E2B\\u0E19\\u0E48\\u0E27\\u0E22\", \"required\", \"\", 1, \"form-control\", \"form-control-sm\", 3, \"focusout\", \"disabled\", \"value\"], [\"type\", \"number\", \"tooltip\", \"\\u0E1B\\u0E49\\u0E2D\\u0E19 %\\u0E2A\\u0E48\\u0E27\\u0E19\\u0E25\\u0E14#1\", \"triggers\", \"focus\", \"id\", \"validationTooltip01\", \"placeholder\", \"%\\u0E2A\\u0E48\\u0E27\\u0E19\\u0E25\\u0E14#1\", \"required\", \"\", 1, \"form-control\", \"form-control-sm\", 3, \"focusout\", \"ngModelChange\", \"ngModel\"], [\"type\", \"text\", \"tooltip\", \"\\u0E1B\\u0E49\\u0E2D\\u0E19 %\\u0E2A\\u0E48\\u0E27\\u0E19\\u0E25\\u0E14#2\", \"triggers\", \"focus\", \"id\", \"validationTooltip01\", \"placeholder\", \"%\\u0E2A\\u0E48\\u0E27\\u0E19\\u0E25\\u0E14#2\", \"required\", \"\", 1, \"form-control\", \"form-control-sm\", 3, \"focusout\", \"ngModelChange\", \"ngModel\"], [\"type\", \"text\", \"tooltip\", \"\\u0E1B\\u0E49\\u0E2D\\u0E19 %\\u0E2A\\u0E48\\u0E27\\u0E19\\u0E25\\u0E14#3\", \"triggers\", \"focus\", \"id\", \"validationTooltip01\", \"placeholder\", \"%\\u0E2A\\u0E48\\u0E27\\u0E19\\u0E25\\u0E14#3\", \"required\", \"\", 1, \"form-control\", \"form-control-sm\", 3, \"focusout\", \"ngModelChange\", \"ngModel\"], [\"type\", \"text\", \"tooltip\", \"\\u0E23\\u0E27\\u0E21\\u0E2A\\u0E48\\u0E27\\u0E19\\u0E25\\u0E14\", \"placeholder\", \"\\u0E23\\u0E27\\u0E21\\u0E2A\\u0E48\\u0E27\\u0E19\\u0E25\\u0E14\", \"readonly\", \"\", 1, \"form-control\", \"currency\", \"form-control-sm\", \"text-right\", 3, \"value\"], [\"type\", \"text\", \"tooltip\", \"\\u0E21\\u0E39\\u0E25\\u0E04\\u0E48\\u0E32\\u0E2A\\u0E34\\u0E19\\u0E04\\u0E49\\u0E32\", \"placeholder\", \"\\u0E21\\u0E39\\u0E25\\u0E04\\u0E48\\u0E32\\u0E2A\\u0E34\\u0E19\\u0E04\\u0E49\\u0E32\", \"readonly\", \"\", 1, \"form-control\", \"currency\", \"form-control-sm\", \"text-right\", 3, \"value\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", \"btn-sm\", \"font-weight-light\", 3, \"click\"], [\"class\", \"col-md-1 mb-1\", 4, \"ngIf\"], [1, \"table\", \"table-hover\", \"table-bordered\", \"table-sm\"], [1, \"text-sm-center\", \"bg-light\"], [1, \"font-weight-normal\"], [\"width\", \"50px\"], [4, \"ngFor\", \"ngForOf\"], [1, \"border-0\", \"bg-light\"], [1, \"text-center\", \"bg-light\", \"font-weight-normal\"], [1, \"text-right\", \"bg-light\", \"font-weight-normal\"], [1, \"bg-light\"], [\"id\", \"mdlSample\", \"role\", \"dialog\", 1, \"modal\", \"fade\", 3, \"ngStyle\"], [1, \"modal-dialog\", \"modal-md\"], [1, \"modal-content\"], [1, \"modal-header\", \"colhaederal\"], [1, \"modal-title\"], [1, \"modal-body\"], [\"align\", \"right\", 1, \"modal-footer\"], [\"type\", \"button\", \"id\", \"btnClose\", 1, \"btn\", \"btn-danger\", 3, \"click\"], [1, \"fa\", \"fa-times\", \"fa-fw\"], [\"id\", \"testmessage\", \"tabindex\", \"-1\", \"role\", \"dialog\", \"aria-labelledby\", \"exampleModalLabel\", \"aria-hidden\", \"true\", 1, \"modal\", \"fade\"], [\"role\", \"document\", 1, \"modal-dialog\"], [1, \"modal-header\"], [\"id\", \"exampleModalLabel\", 1, \"modal-title\"], [\"type\", \"button\", \"data-dismiss\", \"modal\", \"aria-label\", \"Close\", 1, \"close\"], [\"aria-hidden\", \"true\"], [1, \"modal-footer\"], [\"type\", \"button\", \"data-dismiss\", \"modal\", 1, \"btn\", \"btn-secondary\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", \"btn-sm\", \"font-weight-light\", 2, \"width\", \"45px\", \"height\", \"26px\", \"padding\", \"0px\", 3, \"click\"], [3, \"click\"], [3, \"mousedown\"], [\"selected\", \"\", 3, \"value\"], [1, \"text-success\"], [3, \"click\", \"mousedown\"], [\"for\", \"typeahead-templatep\"], [1, \"form-group\", \"form-check\"], [\"type\", \"checkbox\", \"id\", \"exampleCheck1\", 1, \"form-control\", 3, \"ngModelChange\", \"click\", \"ngModel\"], [\"for\", \"exampleCheck1\", 1, \"form-check-label\"], [1, \"text-center\"], [1, \"text-right\", \"font-weight-normal\"], [1, \"text-center\", \"font-weight-normal\"], [1, \"text-info\", \"btn-link\", \"btn-sm\", 3, \"click\"], [1, \"text-danger\", \"btn-link\", \"btn-sm\", 3, \"click\"]],\n        template: function EditsaloderreviewComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            const _r1 = i0.ɵɵgetCurrentView();\n            i0.ɵɵelement(0, \"app-topmenu\");\n            i0.ɵɵelementStart(1, \"div\", 2)(2, \"section\", 3)(3, \"h5\", 4);\n            i0.ɵɵtext(4, \"Sale Order Record\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(5, \"div\", 5)(6, \"div\", 6)(7, \"div\", 7)(8, \"div\", 8)(9, \"h5\", 9)(10, \"button\", 10);\n            i0.ɵɵtext(11, \" Sale Order Header \");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(12, \"div\", 11);\n            i0.ɵɵtemplate(13, EditsaloderreviewComponent_button_13_Template, 2, 0, \"button\", 12);\n            i0.ɵɵelementStart(14, \"label\", 13);\n            i0.ɵɵtext(15);\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(16, \"div\", 14)(17, \"div\", 15)(18, \"div\", 16)(19, \"form\", 17)(20, \"div\", 8)(21, \"div\", 18);\n            i0.ɵɵtemplate(22, EditsaloderreviewComponent_ng_template_22_Template, 3, 3, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n            i0.ɵɵelementStart(24, \"input\", 19);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function EditsaloderreviewComponent_Template_input_ngModelChange_24_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.model, $event) || (ctx.model = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(25, \"div\", 18)(26, \"select\", 20);\n            i0.ɵɵlistener(\"change\", function EditsaloderreviewComponent_Template_select_change_26_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.setlocationno($event.target.value));\n            });\n            i0.ɵɵtemplate(27, EditsaloderreviewComponent_option_27_Template, 2, 3, \"option\", 21);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(28, \"div\", 18)(29, \"select\", 22);\n            i0.ɵɵlistener(\"change\", function EditsaloderreviewComponent_Template_select_change_29_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.selectlocationde($event.target.value));\n            });\n            i0.ɵɵtemplate(30, EditsaloderreviewComponent_option_30_Template, 2, 3, \"option\", 21);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(31, \"div\", 8)(32, \"div\", 23)(33, \"select\", 24);\n            i0.ɵɵlistener(\"change\", function EditsaloderreviewComponent_Template_select_change_33_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.selectvat($event.target.value));\n            });\n            i0.ɵɵtemplate(34, EditsaloderreviewComponent_option_34_Template, 2, 2, \"option\", 21);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(35, \"div\", 25)(36, \"select\", 26);\n            i0.ɵɵlistener(\"change\", function EditsaloderreviewComponent_Template_select_change_36_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.selectdelivery($event.target.value));\n            });\n            i0.ɵɵtemplate(37, EditsaloderreviewComponent_option_37_Template, 2, 2, \"option\", 21);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(38, \"div\", 27)(39, \"input\", 28);\n            i0.ɵɵpipe(40, \"date\");\n            i0.ɵɵtwoWayListener(\"bsValueChange\", function EditsaloderreviewComponent_Template_input_bsValueChange_39_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.bsValue, $event) || (ctx.bsValue = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵlistener(\"bsValueChange\", function EditsaloderreviewComponent_Template_input_bsValueChange_39_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.clickdate($event));\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(41, \"div\", 25)(42, \"select\", 29);\n            i0.ɵɵlistener(\"change\", function EditsaloderreviewComponent_Template_select_change_42_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.selectpaymentfn($event.target.value));\n            });\n            i0.ɵɵtemplate(43, EditsaloderreviewComponent_option_43_Template, 2, 2, \"option\", 21);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(44, \"div\", 30)(45, \"input\", 31);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function EditsaloderreviewComponent_Template_input_ngModelChange_45_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.notesoinput, $event) || (ctx.notesoinput = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵlistener(\"input\", function EditsaloderreviewComponent_Template_input_input_45_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.checklength($event.target.value, 60));\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(46, \"div\", 18)(47, \"input\", 32);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function EditsaloderreviewComponent_Template_input_ngModelChange_47_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.remarksoinput, $event) || (ctx.remarksoinput = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵlistener(\"focusout\", function EditsaloderreviewComponent_Template_input_focusout_47_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.CheckValueRemark($event.target.value));\n            })(\"input\", function EditsaloderreviewComponent_Template_input_input_47_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.checklength($event.target.value, 255));\n            });\n            i0.ɵɵelementEnd()()()()()()()();\n            i0.ɵɵelementStart(48, \"div\", 6)(49, \"div\", 33)(50, \"div\", 8)(51, \"h5\", 34)(52, \"button\", 35);\n            i0.ɵɵlistener(\"click\", function EditsaloderreviewComponent_Template_button_click_52_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.selectsaleoderline());\n            });\n            i0.ɵɵtext(53, \" Product Line Item \");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(54, \"div\", 36);\n            i0.ɵɵtemplate(55, EditsaloderreviewComponent_label_55_Template, 2, 1, \"label\", 37);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(56, \"div\", 38)(57, \"div\", 15)(58, \"div\", 8)(59, \"div\", 39);\n            i0.ɵɵtemplate(60, EditsaloderreviewComponent_ng_template_60_Template, 3, 2, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n            i0.ɵɵelementStart(62, \"input\", 40);\n            i0.ɵɵlistener(\"focusout\", function EditsaloderreviewComponent_Template_input_focusout_62_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.Searchitem());\n            });\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function EditsaloderreviewComponent_Template_input_ngModelChange_62_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.productidsearch, $event) || (ctx.productidsearch = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(63, \"div\", 25)(64, \"input\", 41);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function EditsaloderreviewComponent_Template_input_ngModelChange_64_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.numberproductsearch, $event) || (ctx.numberproductsearch = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵlistener(\"focusout\", function EditsaloderreviewComponent_Template_input_focusout_64_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.checknumproduct($event.target.value));\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(65, \"div\", 25)(66, \"select\", 42);\n            i0.ɵɵlistener(\"change\", function EditsaloderreviewComponent_Template_select_change_66_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.getunitid($event));\n            });\n            i0.ɵɵtemplate(67, EditsaloderreviewComponent_option_67_Template, 2, 2, \"option\", 21);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(68, \"div\", 25)(69, \"input\", 43);\n            i0.ɵɵpipe(70, \"myCurrency\");\n            i0.ɵɵlistener(\"focusout\", function EditsaloderreviewComponent_Template_input_focusout_69_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.editpriceperunit($event.target.value));\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(71, \"div\", 25)(72, \"input\", 44);\n            i0.ɵɵlistener(\"focusout\", function EditsaloderreviewComponent_Template_input_focusout_72_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.editdiscount());\n            });\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function EditsaloderreviewComponent_Template_input_ngModelChange_72_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.dis1, $event) || (ctx.dis1 = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(73, \"div\", 25)(74, \"input\", 45);\n            i0.ɵɵlistener(\"focusout\", function EditsaloderreviewComponent_Template_input_focusout_74_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.editdiscount());\n            });\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function EditsaloderreviewComponent_Template_input_ngModelChange_74_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.dis2, $event) || (ctx.dis2 = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(75, \"div\", 25)(76, \"input\", 46);\n            i0.ɵɵlistener(\"focusout\", function EditsaloderreviewComponent_Template_input_focusout_76_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.editdiscount());\n            });\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function EditsaloderreviewComponent_Template_input_ngModelChange_76_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.dis3, $event) || (ctx.dis3 = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(77, \"div\", 25);\n            i0.ɵɵelement(78, \"input\", 47);\n            i0.ɵɵpipe(79, \"myCurrency\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(80, \"div\", 25);\n            i0.ɵɵelement(81, \"input\", 48);\n            i0.ɵɵpipe(82, \"myCurrency\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(83, \"div\", 25)(84, \"button\", 49);\n            i0.ɵɵlistener(\"click\", function EditsaloderreviewComponent_Template_button_click_84_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.addsaleline());\n            });\n            i0.ɵɵtext(85, \"\\u0E40\\u0E1E\\u0E34\\u0E48\\u0E21\\u0E23\\u0E32\\u0E22\\u0E01\\u0E32\\u0E23\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵtemplate(86, EditsaloderreviewComponent_div_86_Template, 5, 1, \"div\", 50);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(87, \"table\", 51)(88, \"thead\")(89, \"tr\", 52)(90, \"th\", 53);\n            i0.ɵɵtext(91, \"Item\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(92, \"th\", 53);\n            i0.ɵɵtext(93, \"\\u0E23\\u0E2B\\u0E31\\u0E2A\\u0E2A\\u0E34\\u0E19\\u0E04\\u0E49\\u0E32\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(94, \"th\", 53);\n            i0.ɵɵtext(95, \"\\u0E0A\\u0E37\\u0E48\\u0E2D\\u0E2A\\u0E34\\u0E19\\u0E04\\u0E49\\u0E32\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(96, \"th\", 53);\n            i0.ɵɵtext(97, \"\\u0E08\\u0E33\\u0E19\\u0E27\\u0E19\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(98, \"th\", 53);\n            i0.ɵɵtext(99, \"\\u0E2B\\u0E19\\u0E48\\u0E27\\u0E22\\u0E19\\u0E31\\u0E1A\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(100, \"th\", 53);\n            i0.ɵɵtext(101, \"PACK\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(102, \"th\", 53);\n            i0.ɵɵtext(103, \"\\u0E19\\u0E49\\u0E33\\u0E2B\\u0E19\\u0E31\\u0E01(KG.)\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(104, \"th\", 53);\n            i0.ɵɵtext(105, \"In Stock\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(106, \"th\", 53);\n            i0.ɵɵtext(107, \"\\u0E23\\u0E32\\u0E04\\u0E32/\\u0E2B\\u0E19\\u0E48\\u0E27\\u0E22\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(108, \"th\", 53);\n            i0.ɵɵtext(109, \"\\u0E23\\u0E32\\u0E04\\u0E32\\u0E2A\\u0E34\\u0E19\\u0E04\\u0E49\\u0E32\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(110, \"th\", 53);\n            i0.ɵɵtext(111, \"%\\u0E25\\u0E14#1\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(112, \"th\", 53);\n            i0.ɵɵtext(113, \"%\\u0E25\\u0E14#2\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(114, \"th\", 53);\n            i0.ɵɵtext(115, \"%\\u0E25\\u0E14#3\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(116, \"th\", 53);\n            i0.ɵɵtext(117, \"\\u0E23\\u0E27\\u0E21\\u0E2A\\u0E48\\u0E27\\u0E19\\u0E25\\u0E14\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(118, \"th\", 53);\n            i0.ɵɵtext(119, \"\\u0E21\\u0E39\\u0E25\\u0E04\\u0E48\\u0E32\\u0E2A\\u0E38\\u0E17\\u0E18\\u0E34\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(120, \"th\", 54)(121, \"th\", 54);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵtemplate(122, EditsaloderreviewComponent_tr_122_Template, 47, 45, \"tr\", 55);\n            i0.ɵɵelementStart(123, \"tr\");\n            i0.ɵɵelement(124, \"td\", 56)(125, \"td\", 56)(126, \"td\", 56)(127, \"td\", 56)(128, \"td\", 56)(129, \"td\", 56)(130, \"td\", 56)(131, \"td\", 56)(132, \"td\", 56)(133, \"td\", 56)(134, \"td\", 56)(135, \"td\", 56);\n            i0.ɵɵelementStart(136, \"td\", 57);\n            i0.ɵɵtext(137, \"\\u0E21\\u0E39\\u0E25\\u0E04\\u0E48\\u0E32\\u0E23\\u0E27\\u0E21\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(138, \"td\", 58);\n            i0.ɵɵtext(139);\n            i0.ɵɵpipe(140, \"number\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(141, \"td\", 58);\n            i0.ɵɵtext(142);\n            i0.ɵɵpipe(143, \"number\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(144, \"td\", 59)(145, \"td\", 59);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(146, \"tr\");\n            i0.ɵɵelement(147, \"td\", 56)(148, \"td\", 56)(149, \"td\", 56)(150, \"td\", 56)(151, \"td\", 56)(152, \"td\", 56)(153, \"td\", 56)(154, \"td\", 56)(155, \"td\", 56)(156, \"td\", 56)(157, \"td\", 56)(158, \"td\", 56);\n            i0.ɵɵelementStart(159, \"td\", 57);\n            i0.ɵɵtext(160, \"\\u0E19\\u0E49\\u0E33\\u0E2B\\u0E19\\u0E31\\u0E01\\u0E23\\u0E27\\u0E21(KG.)\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(161, \"td\", 58);\n            i0.ɵɵtext(162);\n            i0.ɵɵpipe(163, \"number\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(164, \"td\", 56)(165, \"td\", 59)(166, \"td\", 59);\n            i0.ɵɵelementEnd()()()()()();\n            i0.ɵɵelementStart(167, \"div\", 60)(168, \"div\", 61)(169, \"div\", 62)(170, \"div\", 63)(171, \"h4\", 64);\n            i0.ɵɵtext(172, \"Report\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(173, \"div\", 65);\n            i0.ɵɵtext(174);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(175, \"div\", 66)(176, \"button\", 67);\n            i0.ɵɵlistener(\"click\", function EditsaloderreviewComponent_Template_button_click_176_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.closemodel(false));\n            });\n            i0.ɵɵelement(177, \"i\", 68);\n            i0.ɵɵtext(178, \" \\u0E1B\\u0E34\\u0E14\");\n            i0.ɵɵelementEnd()()()()();\n            i0.ɵɵelementStart(179, \"div\", 69)(180, \"div\", 70)(181, \"div\", 62)(182, \"div\", 71)(183, \"h5\", 72);\n            i0.ɵɵtext(184, \"Modal title\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(185, \"button\", 73)(186, \"span\", 74);\n            i0.ɵɵtext(187, \"\\u00D7\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(188, \"div\", 65);\n            i0.ɵɵtext(189, \" ... \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(190, \"div\", 75)(191, \"button\", 76);\n            i0.ɵɵtext(192, \"Close\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(193, \"button\", 77);\n            i0.ɵɵtext(194, \"Save changes\");\n            i0.ɵɵelementEnd()()()()()()();\n          }\n          if (rf & 2) {\n            const rt_r19 = i0.ɵɵreference(23);\n            const rtpr_r20 = i0.ɵɵreference(61);\n            i0.ɵɵadvance(13);\n            i0.ɵɵproperty(\"ngIf\", ctx.dissave);\n            i0.ɵɵadvance(2);\n            i0.ɵɵtextInterpolate1(\" \\u0E40\\u0E25\\u0E02\\u0E17\\u0E35\\u0E48 SO WEB: \", ctx.idforshow, \"\");\n            i0.ɵɵadvance(9);\n            i0.ɵɵpropertyInterpolate(\"placeholder\", ctx.company);\n            i0.ɵɵproperty(\"disabled\", ctx.ennablecustomer);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.model);\n            i0.ɵɵproperty(\"ngbTypeahead\", ctx.search)(\"resultTemplate\", rt_r19)(\"inputFormatter\", ctx.formatter);\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"ngForOf\", ctx.inaddress);\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"ngForOf\", ctx.daddress);\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"ngForOf\", ctx.vatlist);\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"ngForOf\", ctx.deliverytype);\n            i0.ɵɵadvance(2);\n            i0.ɵɵpropertyInterpolate1(\"value\", \"\", i0.ɵɵpipeBind2(40, 41, ctx.bsValue, \"dd/MM/yyyy\"), \" \");\n            i0.ɵɵtwoWayProperty(\"bsValue\", ctx.bsValue);\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"ngForOf\", ctx.paymentlist);\n            i0.ɵɵadvance(2);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.notesoinput);\n            i0.ɵɵadvance(2);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.remarksoinput);\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"disabled\", ctx.disproductline);\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"ngIf\", ctx.Instock != \"\");\n            i0.ɵɵadvance(7);\n            i0.ɵɵpropertyInterpolate(\"placeholder\", ctx.showitem);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.productidsearch);\n            i0.ɵɵproperty(\"ngbTypeahead\", ctx.searchpr)(\"resultTemplate\", rtpr_r20)(\"inputFormatter\", ctx.formatterpr);\n            i0.ɵɵadvance(2);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.numberproductsearch);\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"ngForOf\", ctx.unitlist);\n            i0.ɵɵadvance(2);\n            i0.ɵɵpropertyInterpolate(\"value\", i0.ɵɵpipeBind1(70, 44, ctx.priceperunit));\n            i0.ɵɵproperty(\"disabled\", ctx.ennableeditprice);\n            i0.ɵɵadvance(3);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.dis1);\n            i0.ɵɵadvance(2);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.dis2);\n            i0.ɵɵadvance(2);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.dis3);\n            i0.ɵɵadvance(2);\n            i0.ɵɵpropertyInterpolate(\"value\", i0.ɵɵpipeBind1(79, 46, ctx.alldiscount));\n            i0.ɵɵadvance(3);\n            i0.ɵɵpropertyInterpolate(\"value\", i0.ɵɵpipeBind1(82, 48, ctx.finalprice));\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"ngIf\", ctx.ennablecustomer == false);\n            i0.ɵɵadvance(36);\n            i0.ɵɵproperty(\"ngForOf\", ctx.lineoderlist);\n            i0.ɵɵadvance(17);\n            i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(140, 50, ctx.sumalldiscount, \"1.2-2\"));\n            i0.ɵɵadvance(3);\n            i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(143, 53, ctx.sumallpricedis, \"1.2-2\"));\n            i0.ɵɵadvance(20);\n            i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(163, 56, ctx.sumallweight, \"1.2-2\"));\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(59, _c0, ctx.mdlSampleIsOpen ? \"block\" : \"none\"));\n            i0.ɵɵadvance(7);\n            i0.ɵɵtextInterpolate1(\"\", ctx.alt, \" \");\n          }\n        },\n        dependencies: [i7.TooltipDirective, i8.NgForOf, i8.NgIf, i8.NgStyle, i9.ɵNgNoValidate, i9.NgSelectOption, i9.ɵNgSelectMultipleOption, i9.DefaultValueAccessor, i9.NumberValueAccessor, i9.CheckboxControlValueAccessor, i9.NgControlStatus, i9.NgControlStatusGroup, i9.RequiredValidator, i9.MaxLengthValidator, i9.NgModel, i9.NgForm, i5.NgbTypeahead, i6.BsDatepickerDirective, i6.BsDatepickerInputDirective, i10.TopmenuComponent, i8.DecimalPipe, i8.DatePipe, i11.ReversepipePipe]\n      });\n    }\n  }\n  return EditsaloderreviewComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}