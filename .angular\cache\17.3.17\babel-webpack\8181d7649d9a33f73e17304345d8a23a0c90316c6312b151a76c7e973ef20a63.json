{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.ObjectUnsubscribedError = void 0;\nvar createErrorClass_1 = require(\"./createErrorClass\");\nexports.ObjectUnsubscribedError = createErrorClass_1.createErrorClass(function (_super) {\n  return function ObjectUnsubscribedErrorImpl() {\n    _super(this);\n    this.name = 'ObjectUnsubscribedError';\n    this.message = 'object unsubscribed';\n  };\n});\n//# sourceMappingURL=ObjectUnsubscribedError.js.map", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}