{"ast": null, "code": "\"use strict\";\n\nvar __values = this && this.__values || function (o) {\n  var s = typeof Symbol === \"function\" && Symbol.iterator,\n    m = s && o[s],\n    i = 0;\n  if (m) return m.call(o);\n  if (o && typeof o.length === \"number\") return {\n    next: function () {\n      if (o && i >= o.length) o = void 0;\n      return {\n        value: o && o[i++],\n        done: !o\n      };\n    }\n  };\n  throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.bufferToggle = void 0;\nvar Subscription_1 = require(\"../Subscription\");\nvar lift_1 = require(\"../util/lift\");\nvar innerFrom_1 = require(\"../observable/innerFrom\");\nvar OperatorSubscriber_1 = require(\"./OperatorSubscriber\");\nvar noop_1 = require(\"../util/noop\");\nvar arrRemove_1 = require(\"../util/arrRemove\");\nfunction bufferToggle(openings, closingSelector) {\n  return lift_1.operate(function (source, subscriber) {\n    var buffers = [];\n    innerFrom_1.innerFrom(openings).subscribe(OperatorSubscriber_1.createOperatorSubscriber(subscriber, function (openValue) {\n      var buffer = [];\n      buffers.push(buffer);\n      var closingSubscription = new Subscription_1.Subscription();\n      var emitBuffer = function () {\n        arrRemove_1.arrRemove(buffers, buffer);\n        subscriber.next(buffer);\n        closingSubscription.unsubscribe();\n      };\n      closingSubscription.add(innerFrom_1.innerFrom(closingSelector(openValue)).subscribe(OperatorSubscriber_1.createOperatorSubscriber(subscriber, emitBuffer, noop_1.noop)));\n    }, noop_1.noop));\n    source.subscribe(OperatorSubscriber_1.createOperatorSubscriber(subscriber, function (value) {\n      var e_1, _a;\n      try {\n        for (var buffers_1 = __values(buffers), buffers_1_1 = buffers_1.next(); !buffers_1_1.done; buffers_1_1 = buffers_1.next()) {\n          var buffer = buffers_1_1.value;\n          buffer.push(value);\n        }\n      } catch (e_1_1) {\n        e_1 = {\n          error: e_1_1\n        };\n      } finally {\n        try {\n          if (buffers_1_1 && !buffers_1_1.done && (_a = buffers_1.return)) _a.call(buffers_1);\n        } finally {\n          if (e_1) throw e_1.error;\n        }\n      }\n    }, function () {\n      while (buffers.length > 0) {\n        subscriber.next(buffers.shift());\n      }\n      subscriber.complete();\n    }));\n  });\n}\nexports.bufferToggle = bufferToggle;\n//# sourceMappingURL=bufferToggle.js.map", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}