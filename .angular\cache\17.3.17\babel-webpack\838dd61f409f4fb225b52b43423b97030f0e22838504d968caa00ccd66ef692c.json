{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.groupBy = void 0;\nvar Observable_1 = require(\"../Observable\");\nvar innerFrom_1 = require(\"../observable/innerFrom\");\nvar Subject_1 = require(\"../Subject\");\nvar lift_1 = require(\"../util/lift\");\nvar OperatorSubscriber_1 = require(\"./OperatorSubscriber\");\nfunction groupBy(keySelector, elementOrOptions, duration, connector) {\n  return lift_1.operate(function (source, subscriber) {\n    var element;\n    if (!elementOrOptions || typeof elementOrOptions === 'function') {\n      element = elementOrOptions;\n    } else {\n      duration = elementOrOptions.duration, element = elementOrOptions.element, connector = elementOrOptions.connector;\n    }\n    var groups = new Map();\n    var notify = function (cb) {\n      groups.forEach(cb);\n      cb(subscriber);\n    };\n    var handleError = function (err) {\n      return notify(function (consumer) {\n        return consumer.error(err);\n      });\n    };\n    var activeGroups = 0;\n    var teardownAttempted = false;\n    var groupBySourceSubscriber = new OperatorSubscriber_1.OperatorSubscriber(subscriber, function (value) {\n      try {\n        var key_1 = keySelector(value);\n        var group_1 = groups.get(key_1);\n        if (!group_1) {\n          groups.set(key_1, group_1 = connector ? connector() : new Subject_1.Subject());\n          var grouped = createGroupedObservable(key_1, group_1);\n          subscriber.next(grouped);\n          if (duration) {\n            var durationSubscriber_1 = OperatorSubscriber_1.createOperatorSubscriber(group_1, function () {\n              group_1.complete();\n              durationSubscriber_1 === null || durationSubscriber_1 === void 0 ? void 0 : durationSubscriber_1.unsubscribe();\n            }, undefined, undefined, function () {\n              return groups.delete(key_1);\n            });\n            groupBySourceSubscriber.add(innerFrom_1.innerFrom(duration(grouped)).subscribe(durationSubscriber_1));\n          }\n        }\n        group_1.next(element ? element(value) : value);\n      } catch (err) {\n        handleError(err);\n      }\n    }, function () {\n      return notify(function (consumer) {\n        return consumer.complete();\n      });\n    }, handleError, function () {\n      return groups.clear();\n    }, function () {\n      teardownAttempted = true;\n      return activeGroups === 0;\n    });\n    source.subscribe(groupBySourceSubscriber);\n    function createGroupedObservable(key, groupSubject) {\n      var result = new Observable_1.Observable(function (groupSubscriber) {\n        activeGroups++;\n        var innerSub = groupSubject.subscribe(groupSubscriber);\n        return function () {\n          innerSub.unsubscribe();\n          --activeGroups === 0 && teardownAttempted && groupBySourceSubscriber.unsubscribe();\n        };\n      });\n      result.key = key;\n      return result;\n    }\n  });\n}\nexports.groupBy = groupBy;\n//# sourceMappingURL=groupBy.js.map", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}