{"ast": null, "code": "\"use strict\";\n\nvar __read = this && this.__read || function (o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o),\n    r,\n    ar = [],\n    e;\n  try {\n    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n  } catch (error) {\n    e = {\n      error: error\n    };\n  } finally {\n    try {\n      if (r && !r.done && (m = i[\"return\"])) m.call(i);\n    } finally {\n      if (e) throw e.error;\n    }\n  }\n  return ar;\n};\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.fromEvent = void 0;\nvar innerFrom_1 = require(\"../observable/innerFrom\");\nvar Observable_1 = require(\"../Observable\");\nvar mergeMap_1 = require(\"../operators/mergeMap\");\nvar isArrayLike_1 = require(\"../util/isArrayLike\");\nvar isFunction_1 = require(\"../util/isFunction\");\nvar mapOneOrManyArgs_1 = require(\"../util/mapOneOrManyArgs\");\nvar nodeEventEmitterMethods = ['addListener', 'removeListener'];\nvar eventTargetMethods = ['addEventListener', 'removeEventListener'];\nvar jqueryMethods = ['on', 'off'];\nfunction fromEvent(target, eventName, options, resultSelector) {\n  if (isFunction_1.isFunction(options)) {\n    resultSelector = options;\n    options = undefined;\n  }\n  if (resultSelector) {\n    return fromEvent(target, eventName, options).pipe(mapOneOrManyArgs_1.mapOneOrManyArgs(resultSelector));\n  }\n  var _a = __read(isEventTarget(target) ? eventTargetMethods.map(function (methodName) {\n      return function (handler) {\n        return target[methodName](eventName, handler, options);\n      };\n    }) : isNodeStyleEventEmitter(target) ? nodeEventEmitterMethods.map(toCommonHandlerRegistry(target, eventName)) : isJQueryStyleEventEmitter(target) ? jqueryMethods.map(toCommonHandlerRegistry(target, eventName)) : [], 2),\n    add = _a[0],\n    remove = _a[1];\n  if (!add) {\n    if (isArrayLike_1.isArrayLike(target)) {\n      return mergeMap_1.mergeMap(function (subTarget) {\n        return fromEvent(subTarget, eventName, options);\n      })(innerFrom_1.innerFrom(target));\n    }\n  }\n  if (!add) {\n    throw new TypeError('Invalid event target');\n  }\n  return new Observable_1.Observable(function (subscriber) {\n    var handler = function () {\n      var args = [];\n      for (var _i = 0; _i < arguments.length; _i++) {\n        args[_i] = arguments[_i];\n      }\n      return subscriber.next(1 < args.length ? args : args[0]);\n    };\n    add(handler);\n    return function () {\n      return remove(handler);\n    };\n  });\n}\nexports.fromEvent = fromEvent;\nfunction toCommonHandlerRegistry(target, eventName) {\n  return function (methodName) {\n    return function (handler) {\n      return target[methodName](eventName, handler);\n    };\n  };\n}\nfunction isNodeStyleEventEmitter(target) {\n  return isFunction_1.isFunction(target.addListener) && isFunction_1.isFunction(target.removeListener);\n}\nfunction isJQueryStyleEventEmitter(target) {\n  return isFunction_1.isFunction(target.on) && isFunction_1.isFunction(target.off);\n}\nfunction isEventTarget(target) {\n  return isFunction_1.isFunction(target.addEventListener) && isFunction_1.isFunction(target.removeEventListener);\n}\n//# sourceMappingURL=fromEvent.js.map", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}