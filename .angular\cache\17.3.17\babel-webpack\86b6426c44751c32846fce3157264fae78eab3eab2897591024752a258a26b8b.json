{"ast": null, "code": "import { HttpClient, HttpEventType } from '@angular/common/http';\nimport { TemplateRef } from '@angular/core';\nimport { Router } from '@angular/router';\nimport { NgbCalendar } from '@ng-bootstrap/ng-bootstrap';\nimport { Angular5Csv } from 'angular5-csv/dist/Angular5-csv';\nimport { WebapiService } from '../webapi.service';\nimport { BsModalService } from 'ngx-bootstrap/modal';\nimport { TabsetComponent } from 'ngx-bootstrap/tabs';\nimport { debounceTime, map } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"ngx-bootstrap/modal\";\nimport * as i2 from \"@angular/common/http\";\nimport * as i3 from \"../webapi.service\";\nimport * as i4 from \"@angular/router\";\nimport * as i5 from \"@ng-bootstrap/ng-bootstrap\";\nconst _c0 = [\"staticTabs\"];\nconst _c1 = () => ({\n  dateInputFormat: \"DD/MM/YYYY\"\n});\nconst _c2 = a0 => ({\n  \"display\": a0,\n  \"opacity\": 1\n});\nconst _c3 = a0 => ({\n  \"color\": a0\n});\nconst _c4 = a0 => ({\n  \"display\": a0\n});\nfunction SoreviewComponent_div_6_option_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 44);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r4 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(4, _c3, ctx_r2.getColorsalegroup(item_r4.disstate)))(\"value\", item_r4.groupid);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\" \", item_r4.groupid, \" (\", item_r4.name, \") \");\n  }\n}\nfunction SoreviewComponent_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 11)(1, \"select\", 40);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SoreviewComponent_div_6_Template_select_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.salegroup, $event) || (ctx_r2.salegroup = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementStart(2, \"option\", 41);\n    i0.ɵɵtext(3, \"--\\u0E40\\u0E25\\u0E37\\u0E2D\\u0E01\\u0E23\\u0E2B\\u0E31\\u0E2A\\u0E1E\\u0E19\\u0E31\\u0E01\\u0E07\\u0E32\\u0E19\\u0E02\\u0E32\\u0E22--\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"option\", 42);\n    i0.ɵɵtext(5, \"\\u0E40\\u0E25\\u0E37\\u0E2D\\u0E01\\u0E23\\u0E32\\u0E22\\u0E01\\u0E32\\u0E23\\u0E17\\u0E31\\u0E49\\u0E07\\u0E2B\\u0E21\\u0E14\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, SoreviewComponent_div_6_option_6_Template, 2, 6, \"option\", 43);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.salegroup);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.DateGroupsaleman);\n  }\n}\nfunction SoreviewComponent_ng_template_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"label\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const r_r5 = ctx.result;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"\", r_r5.name, \" (\", r_r5.accountnum, \")\");\n  }\n}\nfunction SoreviewComponent_th_71_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 28);\n    i0.ɵɵtext(1, \"Edit\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SoreviewComponent_tr_77_td_46_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"td\", 55)(1, \"label\", 57);\n    i0.ɵɵlistener(\"click\", function SoreviewComponent_tr_77_td_46_Template_label_click_1_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const item_r8 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.editviewsore(item_r8.id));\n    });\n    i0.ɵɵtext(2, \"\\u0E41\\u0E01\\u0E49\\u0E44\\u0E02\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r8 = i0.ɵɵnextContext().$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(1, _c4, ctx_r2.getclosesyncdata(item_r8.stcheck)));\n  }\n}\nfunction SoreviewComponent_tr_77_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\", 45)(1, \"td\", 46)(2, \"input\", 47);\n    i0.ɵɵlistener(\"click\", function SoreviewComponent_tr_77_Template_input_click_2_listener($event) {\n      const i_r7 = i0.ɵɵrestoreView(_r6).index;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.selectsingel(i_r7, $event.target.checked));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(3, \"td\", 48);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\", 49);\n    i0.ɵɵlistener(\"click\", function SoreviewComponent_tr_77_Template_td_click_5_listener() {\n      const item_r8 = i0.ɵɵrestoreView(_r6).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      const templateAddfile_r9 = i0.ɵɵreference(95);\n      return i0.ɵɵresetView(ctx_r2.OpenUploadPDF(templateAddfile_r9, item_r8.id));\n    });\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\", 48);\n    i0.ɵɵtext(8);\n    i0.ɵɵpipe(9, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"td\", 50);\n    i0.ɵɵpipe(11, \"date\");\n    i0.ɵɵtext(12);\n    i0.ɵɵpipe(13, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"td\", 48);\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"td\", 51);\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"td\", 51);\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"td\", 51);\n    i0.ɵɵtext(21);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"td\", 51);\n    i0.ɵɵtext(23);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"td\", 52);\n    i0.ɵɵtext(25);\n    i0.ɵɵpipe(26, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"td\", 52);\n    i0.ɵɵtext(28);\n    i0.ɵɵpipe(29, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"td\", 52);\n    i0.ɵɵtext(31);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"td\", 52);\n    i0.ɵɵtext(33);\n    i0.ɵɵpipe(34, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(35, \"td\", 48);\n    i0.ɵɵtext(36);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(37, \"td\", 48);\n    i0.ɵɵtext(38);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(39, \"td\", 48);\n    i0.ɵɵtext(40);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(41, \"td\", 48);\n    i0.ɵɵtext(42);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(43, \"td\", 46)(44, \"button\", 53);\n    i0.ɵɵlistener(\"click\", function SoreviewComponent_tr_77_Template_button_click_44_listener() {\n      const item_r8 = i0.ɵɵrestoreView(_r6).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      const template_r10 = i0.ɵɵreference(91);\n      return i0.ɵɵresetView(ctx_r2.getsaloderlist(item_r8.id, false, template_r10, item_r8.filetype, item_r8.filename));\n    });\n    i0.ɵɵtext(45, \" View \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(46, SoreviewComponent_tr_77_td_46_Template, 3, 3, \"td\", 54);\n    i0.ɵɵelementStart(47, \"td\", 55)(48, \"label\", 56);\n    i0.ɵɵlistener(\"click\", function SoreviewComponent_tr_77_Template_label_click_48_listener() {\n      const item_r8 = i0.ɵɵrestoreView(_r6).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.deletesaloder(item_r8.id));\n    });\n    i0.ɵɵtext(49, \"\\u0E25\\u0E1A\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(50, \"td\", 48);\n    i0.ɵɵtext(51);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r8 = ctx.$implicit;\n    const i_r7 = ctx.index;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(62, _c4, ctx_r2.getclosesyncdata(item_r8.stcheck)))(\"checked\", item_r8.checksync);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(64, _c3, ctx_r2.getColorFile(item_r8.filetype)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i_r7 + 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(66, _c3, ctx_r2.getColorFile(item_r8.filetype)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r8.id);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(68, _c3, ctx_r2.getColorFile(item_r8.filetype)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(9, 42, item_r8.ShippingDateRequested, \"dd/MM/yyyy\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate1(\"tooltip\", \"\\u0E41\\u0E01\\u0E49\\u0E44\\u0E02\\u0E04\\u0E23\\u0E31\\u0E49\\u0E07\\u0E25\\u0E48\\u0E32\\u0E2A\\u0E38\\u0E14 \", i0.ɵɵpipeBind3(11, 45, item_r8.lastupdate, \"dd/MM/yyyy HH:mm:ss\", \"UTC\"), \"\");\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(70, _c3, ctx_r2.getColorFile(item_r8.filetype)))(\"ngStyle\", i0.ɵɵpureFunction1(72, _c3, ctx_r2.getColorFile(item_r8.filetype)));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind3(13, 49, item_r8.timeedit, \"HH:mm:ss\", \"UTC\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(74, _c3, ctx_r2.getColorFile(item_r8.filetype)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r8.SalesId);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(76, _c3, ctx_r2.getColorFile(item_r8.filetype)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r8.salesname);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(78, _c3, ctx_r2.getColorFile(item_r8.filetype)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r8.InvName);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(80, _c3, ctx_r2.getColorFile(item_r8.filetype)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r8.regnum);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(82, _c3, ctx_r2.getColorFile(item_r8.filetype)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r8.DeliveryName);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(84, _c3, ctx_r2.getColorFile(item_r8.filetype)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(26, 53, item_r8.amount, \"1.2-2\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(86, _c3, ctx_r2.getColorFile(item_r8.filetype)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(29, 56, item_r8.price, \"1.2-2\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(88, _c3, ctx_r2.getColorFile(item_r8.filetype)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r8.vattype);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(90, _c3, ctx_r2.getColorFile(item_r8.filetype)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(34, 59, item_r8.totalweight, \"1.2-2\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(92, _c3, ctx_r2.getColorFile(item_r8.filetype)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r8.DlvMode);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(94, _c3, ctx_r2.getColorFile(item_r8.filetype)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r8.paymenttype);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(96, _c3, ctx_r2.getColorFile(item_r8.filetype)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r8.CustomerRef);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(98, _c3, ctx_r2.getColorFile(item_r8.filetype)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r8.remark);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.ennablecustomer == false);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(100, _c3, ctx_r2.getColorst(item_r8.stcheck)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", item_r8.textstatus, \" \");\n  }\n}\nfunction SoreviewComponent_ng_template_90_tr_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 73);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\", 74);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\", 73);\n    i0.ɵɵtext(6);\n    i0.ɵɵpipe(7, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"td\", 73);\n    i0.ɵɵtext(9);\n    i0.ɵɵpipe(10, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"td\", 73);\n    i0.ɵɵtext(12);\n    i0.ɵɵpipe(13, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"td\", 73);\n    i0.ɵɵtext(15);\n    i0.ɵɵpipe(16, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"td\", 75);\n    i0.ɵɵtext(18);\n    i0.ɵɵpipe(19, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"td\", 75);\n    i0.ɵɵtext(21);\n    i0.ɵɵpipe(22, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"td\", 75);\n    i0.ɵɵtext(24);\n    i0.ɵɵpipe(25, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"td\", 76);\n    i0.ɵɵtext(27);\n    i0.ɵɵpipe(28, \"number\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r13 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r13.ItemId);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r13.Name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(7, 13, item_r13.packqty, \"1.2-2\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(10, 16, item_r13.SalesQty, \"1.2-2\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(13, 19, item_r13.totalweight, \"1.2-2\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(16, 22, item_r13.PriceUnit, \"1.2-2\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(37, _c3, ctx_r2.getColordis1(item_r13.disstate)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind2(19, 25, item_r13.IVZ_Percent1_CT, \"1.2-2\"), \"%\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(39, _c3, ctx_r2.getColordis2(item_r13.disstate)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind2(22, 28, item_r13.IVZ_Percent2_CT, \"1.2-2\"), \"%\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(41, _c3, ctx_r2.getColordis3(item_r13.disstate)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind2(25, 31, item_r13.IVZ_Percent3_CT, \"1.2-2\"), \"%\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(28, 34, item_r13.LineAmount, \"1.2-2\"));\n  }\n}\nfunction SoreviewComponent_ng_template_90_tab_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tab\", 77)(1, \"div\", 78)(2, \"form\", 79, 5);\n    i0.ɵɵelement(4, \"img\", 80);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"src\", ctx_r2.nameUrl, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction SoreviewComponent_ng_template_90_a_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 81);\n    i0.ɵɵtext(1, \"View PDF\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵpropertyInterpolate(\"href\", ctx_r2.nameUrl, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction SoreviewComponent_ng_template_90_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 58)(1, \"h4\", 59);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 60);\n    i0.ɵɵlistener(\"click\", function SoreviewComponent_ng_template_90_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.modalRef.hide());\n    });\n    i0.ɵɵelementStart(4, \"span\", 61);\n    i0.ɵɵtext(5, \"\\u00D7\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(6, \"div\", 62)(7, \"tabset\", null, 4)(9, \"tab\", 63)(10, \"div\", 64)(11, \"table\", 23)(12, \"thead\")(13, \"tr\")(14, \"th\", 65);\n    i0.ɵɵtext(15, \"\\u0E23\\u0E2B\\u0E31\\u0E2A\\u0E2A\\u0E34\\u0E19\\u0E04\\u0E49\\u0E32\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"th\", 65);\n    i0.ɵɵtext(17, \"\\u0E0A\\u0E37\\u0E48\\u0E2D\\u0E2A\\u0E34\\u0E19\\u0E04\\u0E49\\u0E32\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"th\", 66);\n    i0.ɵɵtext(19, \"Pack\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"th\", 66);\n    i0.ɵɵtext(21, \"\\u0E08\\u0E33\\u0E19\\u0E27\\u0E19\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"th\", 66);\n    i0.ɵɵtext(23, \"\\u0E19\\u0E49\\u0E33\\u0E2B\\u0E19\\u0E31\\u0E01\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"th\", 66);\n    i0.ɵɵtext(25, \"\\u0E23\\u0E32\\u0E04\\u0E32\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"th\", 67);\n    i0.ɵɵtext(27, \"\\u0E2A\\u0E48\\u0E27\\u0E19\\u0E25\\u0E14\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"th\", 66);\n    i0.ɵɵtext(29, \"\\u0E21\\u0E39\\u0E25\\u0E04\\u0E48\\u0E32\\u0E2A\\u0E38\\u0E17\\u0E18\\u0E34\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(30, \"tbody\");\n    i0.ɵɵtemplate(31, SoreviewComponent_ng_template_90_tr_31_Template, 29, 43, \"tr\", 68);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵtemplate(32, SoreviewComponent_ng_template_90_tab_32_Template, 5, 1, \"tab\", 69);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(33, \"div\", 70);\n    i0.ɵɵtemplate(34, SoreviewComponent_ng_template_90_a_34_Template, 2, 1, \"a\", 71);\n    i0.ɵɵelementStart(35, \"button\", 72);\n    i0.ɵɵlistener(\"click\", function SoreviewComponent_ng_template_90_Template_button_click_35_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.modalRef.hide());\n    });\n    i0.ɵɵtext(36, \"Close\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"Sale Oder Detail : \", ctx_r2.showIDso, \"\");\n    i0.ɵɵadvance(29);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.salelistview);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.Cktype && ctx_r2.CkNull);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.Cktype && ctx_r2.CkNull);\n  }\n}\nfunction SoreviewComponent_ng_template_92_button_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 85);\n    i0.ɵɵlistener(\"click\", function SoreviewComponent_ng_template_92_button_3_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.confirm());\n    });\n    i0.ɵɵtext(1, \"\\u0E1B\\u0E34\\u0E14\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SoreviewComponent_ng_template_92_button_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 86);\n    i0.ɵɵlistener(\"click\", function SoreviewComponent_ng_template_92_button_4_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r15);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      const templateAddfile_r9 = i0.ɵɵreference(95);\n      return i0.ɵɵresetView(ctx_r2.decline(templateAddfile_r9));\n    });\n    i0.ɵɵtext(1, \"\\u0E17\\u0E33\\u0E23\\u0E32\\u0E22\\u0E01\\u0E32\\u0E23\\u0E43\\u0E2B\\u0E21\\u0E48\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SoreviewComponent_ng_template_92_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 82);\n    i0.ɵɵtext(1);\n    i0.ɵɵelement(2, \"br\");\n    i0.ɵɵtemplate(3, SoreviewComponent_ng_template_92_button_3_Template, 2, 0, \"button\", 83)(4, SoreviewComponent_ng_template_92_button_4_Template, 2, 0, \"button\", 84);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.textload, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.btnPDF);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.btnREpdf);\n  }\n}\nfunction SoreviewComponent_ng_template_94_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 58)(1, \"h4\", 59);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 60);\n    i0.ɵɵlistener(\"click\", function SoreviewComponent_ng_template_94_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.modalRef.hide());\n    });\n    i0.ɵɵelementStart(4, \"span\", 61);\n    i0.ɵɵtext(5, \"\\u00D7\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(6, \"div\", 36)(7, \"div\", 87)(8, \"label\", 88);\n    i0.ɵɵtext(9, \"Input file PDF\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"input\", 89);\n    i0.ɵɵlistener(\"change\", function SoreviewComponent_ng_template_94_Template_input_change_10_listener($event) {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.handleFileInput($event.target.files));\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(11, \"div\", 70)(12, \"button\", 90);\n    i0.ɵɵlistener(\"click\", function SoreviewComponent_ng_template_94_Template_button_click_12_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r2 = i0.ɵɵnextContext();\n      const template_r10 = i0.ɵɵreference(91);\n      const templateShow_r17 = i0.ɵɵreference(93);\n      return i0.ɵɵresetView(ctx_r2.onUpload(ctx_r2.showIDso, templateShow_r17, template_r10));\n    });\n    i0.ɵɵtext(13, \"Upload\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"button\", 72);\n    i0.ɵɵlistener(\"click\", function SoreviewComponent_ng_template_94_Template_button_click_14_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.modalRef.hide());\n    });\n    i0.ɵɵtext(15, \"Close\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"UploadPDF : \", ctx_r2.showIDso, \"\");\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"disabled\", ctx_r2.selectedFile == null);\n  }\n}\nexport let SoreviewComponent = /*#__PURE__*/(() => {\n  class SoreviewComponent {\n    getColorst(disst) {\n      if (disst == 1) {\n        return '#05C1FF';\n      } else if (disst = 3) {\n        return '#FF9505';\n      } else if (disst == 2) {\n        return '#C305FF';\n      } else if (disst == 3) {\n        return '#FF05D5';\n      } else if (disst == 4) {\n        return '#0AC103';\n      }\n    }\n    getColorsalegroup(disst) {\n      return 'rgb(55, 116, 230)';\n    }\n    getclosesyncdata(disst) {\n      /* if(disst==3){\n         return 'none';\n       } else if(disst==1){\n         return 'block';\n       }*/\n      return 'block';\n    }\n    getColordis1(disst) {\n      var st = disst;\n      var fi = st.substring(0, 1);\n      switch (fi) {\n        case '1':\n          return 'red';\n      }\n    }\n    getColordis2(disst) {\n      var st = disst;\n      var fi = st.substring(1, 2);\n      switch (fi) {\n        case '1':\n          return 'red';\n      }\n    }\n    getColordis3(disst) {\n      var st = disst;\n      var fi = st.substring(2, 3);\n      switch (fi) {\n        case '1':\n          return 'red';\n      }\n    }\n    getColorFile(type) {\n      if (type !== \"\") {\n        return '#0317ee';\n      }\n    }\n    constructor(modalService, http, service, router, calendar) {\n      this.modalService = modalService;\n      this.http = http;\n      this.service = service;\n      this.router = router;\n      this.calendar = calendar;\n      this.mdlSampleIsOpen = false;\n      this.alt = '';\n      this.checkreload = true;\n      this.options = {\n        fieldSeparator: ',',\n        quoteStrings: '\"',\n        decimalseparator: '.',\n        showLabels: true,\n        showTitle: true,\n        useBom: true,\n        noDownload: false,\n        headers: [\"เลขที่ SO\", \"วันที่\", \"พนักงานขาย\", 'ลูกค้า', 'มูลค่าสินค้า', 'มูลค่าสุทธิ', 'VAT/No VAT', \"น้ำหนักรวม\", \"ประเภทขนส่ง\", \"เงินสด/เครดิต\", \"Note ภายใน\"]\n      };\n      this.salegroup = '';\n      this.customer = '';\n      this.DateGroupsaleman = [];\n      this.datalogin = [];\n      this.fromdate = '';\n      this.todate = '';\n      this.loaddata = [];\n      this.permisstiondata = [];\n      this.exportbtn = true;\n      this.searchbtn = true;\n      this.selectallsync = false;\n      this.saleoderreviewdata = [];\n      this.syncidso = [];\n      this.savedata = [];\n      this.salelistview = [];\n      this.company = 'ค้นหาลูกค้า';\n      this.customers = [];\n      this.typesync = '1';\n      this.syncbtn = false;\n      this.ennablecustomer = false;\n      this.rootSelectionString = \".js-rssfeed\";\n      this.config = {\n        ignoreBackdropClick: true,\n        class: 'modal-lg modal-dialog800'\n      };\n      this.configAddflie = {\n        ignoreBackdropClick: true,\n        class: 'modal-md'\n      };\n      this.selectedFile = null;\n      this.btnPDF = false;\n      this.btnREpdf = false;\n      this.search = text$ =>\n      //Autocomplete ลูกค้า\n      text$.pipe(debounceTime(200), map(term => term === '' ? [] : this.customers.filter(v => v.name.toLowerCase().indexOf(term.toLowerCase()) > -1 || v.accountnum.toLowerCase().indexOf(term.toLowerCase()) > -1).slice(0, 50)));\n      this.formatter = x => x.name + ' (' + x.accountnum + ')';\n      localStorage.removeItem('DataSOderlist');\n      this.btnloadfile = false;\n      this.url = service.geturlservice();\n      this.fromDate = calendar.getPrev(calendar.getToday(), 'd', 30);\n      this.toDate = calendar.getNext(calendar.getToday(), 'm', 3);\n      this.datalogin = JSON.parse(sessionStorage.getItem('login'));\n      this.fromdate = '';\n      this.todate = '';\n      this.loaddata = JSON.parse(localStorage.getItem('DataSOderreview'));\n      this.Datatodate = new Date(this.toDate.year, this.toDate.month - 1, this.toDate.day);\n      this.Datafromdate = new Date(this.fromDate.year, this.fromDate.month - 1, this.fromDate.day);\n      this.getdate();\n      this.getuser();\n      this.permisstiondata = JSON.parse(sessionStorage.getItem('menu'));\n      if (this.datalogin == null) {\n        this.router.navigate(['login']);\n      } else {\n        this.getuser();\n        this.exportbtn = !this.permisstiondata[5].flag_print;\n        this.searchbtn = !this.permisstiondata[5].flag_action;\n        if (this.searchbtn == false) {\n          this.syncbtn = true;\n        }\n      }\n    }\n    getuser() {\n      if (this.datalogin != undefined) {\n        if (this.datalogin[0].id_group_user == '153Admin' || this.datalogin[0].id_group_user == '1860Administrator') {\n          this.groupsale = '';\n          this.testclose = true;\n        } else {\n          this.testclose = false;\n          this.salegroup = this.datalogin[0].salegroup;\n        }\n      }\n    }\n    getdate() {\n      this.fromdate = `${this.Datafromdate.getFullYear()}-${this.Datafromdate.getMonth() + 1}-${this.Datafromdate.getDate()}`;\n      this.todate = `${this.Datatodate.getFullYear()}-${this.Datatodate.getMonth() + 1}-${this.Datatodate.getDate()}`;\n    }\n    ngOnInit() {\n      this.getgroupsaleman();\n      this.getcostomerauto();\n      if (this.datalogin[0].accountnum != undefined) {\n        this.ennablecustomer = true;\n      }\n    }\n    getcostomerauto() {\n      var idsale = this.datalogin[0].salegroup;\n      if (this.datalogin[0].id_group_user == '153Admin' || this.datalogin[0].id_group_user == '1860Administrator') {\n        //.salegroup==='admin'\n        idsale = '%20';\n      } else {\n        idsale = this.datalogin[0].salegroup;\n      }\n      this.http.get(this.url + 'customerauto/' + idsale).subscribe(res => {\n        this.customers = res;\n      });\n    }\n    SalesOrderdownload() {\n      location.href = 'http://119.59.112.47/node/apinano/api/downloadfile/SalesOrder.txt';\n      clearInterval(this.settimedownload);\n      this.Searchsolist();\n    }\n    loadsalEXTfile() {\n      if (this.syncidso.length < 1) {\n        //alert('กรุณาเลือกข้อมูลที่ต้องการ Sync');\n        return;\n      } else {\n        this.settimedownloadext = setInterval(() => this.SalesEXTdownload(), 500);\n      }\n    }\n    SalesEXTdownload() {\n      location.href = 'http://119.59.112.47/node/apinano/api/downloadfile/SalesEXT.txt';\n      clearInterval(this.settimedownloadext);\n    }\n    downloadsyncfile() {\n      if (confirm('ต้องการ Dowload TXT File ใช่หรือไม่')) {\n        this.http.get(this.url + 'checkfile').subscribe(res => {}, error => {\n          if (error.status == 200) {\n            location.href = 'http://119.59.112.47/node/apinano/api/downloadfile';\n            this.btnloadfile = false;\n          } else {\n            alert('ไม่พบไฟล์ที่ ค้นหา');\n          }\n        });\n      } else {\n        return;\n      }\n    }\n    gettype(type) {\n      if (type === \"application/pdf\") {\n        return true;\n      } else {\n        return false;\n      }\n    }\n    gettypeNull(type) {\n      if (type == \"\") {\n        return false;\n      } else {\n        return true;\n      }\n    }\n    getsaloderlist(valueid, check, template, type, nameFile) {\n      this.salelistview = [];\n      this.showIDso = valueid;\n      this.Cktype = this.gettype(type);\n      this.CkNull = this.gettypeNull(type);\n      // alert(this.CkNull)\n      //this.nameUrl= 'assets/PDF/'+ nameFile;\n      this.nameUrl = 'http://119.59.112.47/assets/PDF/' + nameFile;\n      // this.nameUrl='http://119.59.112.47/assets/PDF/SW-1811admin-0240-1543213724302.PDF';\n      // alert( this.nameUrl +'/'+ this.Cktype)\n      this.http.get(this.url + 'find_saleline/' + valueid).subscribe(res => {\n        if (res.length > 0) {\n          this.salelistview = res;\n          this.modalRef = this.modalService.show(template, this.config);\n          if (check == true) {\n            //this.printpdf();\n          }\n        }\n      });\n    }\n    OpenUploadPDF(template, idSo) {\n      this.btnPDF = false;\n      this.btnREpdf = false;\n      this.showIDso = \"\";\n      this.showIDso = idSo;\n      this.selectedFile = null;\n      this.modalRef = this.modalService.show(template, this.configAddflie);\n    }\n    snydatatoax(value) {\n      /*this.http.post<any>('syncso/Service.asmx/SyncData','idSo:'+value).subscribe(res=>{\n        alert(res);\n      },error=>{\n        alert(JSON.stringify(error));\n      })*/\n      const Http = new XMLHttpRequest();\n      const url = 'syncso/Service.asmx/SyncData?idSo=' + value;\n      Http.open(\"GET\", url);\n      Http.send();\n      Http.onreadystatechange = e => {\n        if (Http.readyState == 4 && Http.status == 200) {\n          this.btnloadfile = true;\n          alert('Sync ข้อมูล เสร็จสิ้น');\n          this.Searchsolist();\n        }\n      };\n    }\n    getgroupsaleman() {\n      this.DateGroupsaleman = [];\n      this.http.get(this.url + 'salesman').subscribe(res => {\n        if (res.length > 0) {\n          this.Searchsolist();\n          this.DateGroupsaleman = res;\n        } else {\n          alert('ไม่สามารถติดต่อข้อมูลรหัส saleman ได้');\n        }\n      });\n    }\n    deletesaloder(value) {\n      if (confirm('ต้องการลบ รายการที่เลือกใช่หรือไม่')) {\n        if (value != undefined) {\n          var urlpost = `${this.url}${'delete_sale_line'}/${value}/${this.datalogin[0].salegroup}`;\n          this.http.post(urlpost, '').subscribe(res => {\n            // this.setinheader= setInterval(this.savesaleoderlistsave(),);\n            alert('ลบข้อมูลเสร็จสิ้น');\n            this.Searchsolist();\n          });\n        } else {}\n      } else {\n        return;\n      }\n    }\n    Searchsolist() {\n      this.getdate();\n      if (this.getcustomer == undefined) {\n        this.customer = '';\n      } else {\n        this.customer = this.getcustomer.name;\n      }\n      var datasalegroup = '';\n      if (this.customer == '') {\n        this.customer = '%20';\n      }\n      if (this.datalogin[0].accountnum != undefined) {\n        this.customer = this.datalogin[0].accountnum;\n      }\n      if (this.fromdate == '') {\n        this.fromdate = `${this.fromDate}`;\n      }\n      if (this.todate == '') {\n        this.todate = `${this.toDate}`;\n      }\n      if (this.groupsale == '') {\n        if (this.datalogin[0].id_group_user == '153Admin' || this.datalogin[0].id_group_user == '1860Administrator') {\n          datasalegroup = `${this.salegroup}`;\n        } else {\n          datasalegroup = `${this.datalogin[0].salegroup}`;\n        }\n      }\n      if (this.groupsale !== '') {\n        if (this.datalogin[0].id_group_user == '153Admin' || this.datalogin[0].id_group_user == '1860Administrator') {\n          datasalegroup = `${this.salegroup}`;\n        } else {\n          datasalegroup = `${this.datalogin[0].salegroup}`;\n        }\n      }\n      if (datasalegroup == '') {\n        datasalegroup = '%20';\n      }\n      if (this.loaddata != null && JSON.stringify(this.loaddata) != '[]') {\n        datasalegroup = this.loaddata[0].datasalegroup;\n        this.customer = this.loaddata[0].customer;\n        this.fromdate = this.loaddata[0].fromdate;\n        this.todate = this.loaddata[0].todate;\n        this.Datatodate = new Date(this.loaddata[0].todate);\n        this.Datafromdate = new Date(this.loaddata[0].fromdate);\n        //alert(JSON.stringify(this.loaddata));\n        this.company = this.customer;\n        this.loaddata = [];\n      }\n      this.soreviewlist = [];\n      this.saleoderreviewdata = [];\n      this.savedata = [];\n      this.http.get(this.url + 'solist_review/' + this.fromdate + '/' + this.todate + '/' + datasalegroup + '/' + this.customer + '/' + this.typesync).subscribe(res => {\n        if (res.length > 0) {\n          this.savedata = [{\n            fromdate: this.fromdate,\n            todate: this.todate,\n            datasalegroup: datasalegroup,\n            customer: this.customer\n          }];\n          if (datasalegroup == '%20') {\n            this.salegroup = '';\n          }\n          if (this.customer == '%20') {\n            this.customer = '';\n          }\n          this.soreviewlist = res;\n          for (var i = 0; i < res.length; i++) {\n            this.saleoderreviewdata.push({\n              dateid: res[i].dateid,\n              ShippingDateRequested: res[i].ShippingDateRequested,\n              id: res[i].id,\n              SalesGroup: res[i].SalesGroup,\n              SalesId: res[i].SalesId,\n              salesname: res[i].salesname,\n              amount: res[i].amount,\n              discount: res[i].discount,\n              price: res[i].price,\n              totalweight: res[i].totalweight,\n              DlvMode: res[i].DlvMode,\n              paymenttype: res[i].paymenttype,\n              statest: res[i].statest,\n              CustomerRef: res[i].CustomerRef,\n              remark: res[i].remark,\n              vattype: res[i].vattype,\n              state: res[i].state,\n              textstatus: res[i].textstatus,\n              stcheck: res[i].stcheck,\n              checksync: false,\n              timeedit: res[i].timeedit,\n              lastupdate: res[i].lastupdate,\n              filetype: res[i].filetype,\n              filename: res[i].filename,\n              InvName: res[i].InvName,\n              DeliveryName: res[i].DeliveryName,\n              regnum: res[i].regnum\n            });\n          }\n          //alert(JSON.stringify(this.saleoderreviewdata));\n        } else {\n          this.soreviewlist = [];\n          this.saleoderreviewdata = [];\n          if (datasalegroup == '%20') {\n            this.salegroup = '';\n          }\n          if (this.customer == '%20') {\n            this.customer = '';\n          }\n          alert('ไม่พบข้อมูลที่ค้นหา');\n          //this.openModal(true,'ไม่พบข้อมูลที่ค้นหา',false);\n        }\n      }, error => {\n        alert('เกิดปัญหาในการ Process ข้อมูล กรุณาลองใหม่อีกครั้ง');\n        //this.openModal(true,'เกิดปัญหาในการ Process ข้อมูล กรุณาลองใหม่อีกครั้ง',false);\n      });\n    }\n    selectallsyncdata(valuecheck) {\n      //alert(valuecheck);\n      if (valuecheck == true && this.searchbtn == false) {\n        this.syncbtn = false;\n      } else {\n        this.syncbtn = true;\n      }\n      this.selectallsync = valuecheck;\n      for (var i = 0; i < this.saleoderreviewdata.length; i++) {\n        this.saleoderreviewdata[i].checksync = valuecheck;\n      }\n    }\n    selectsingel(i, valuecheck) {\n      if (valuecheck == true && this.searchbtn == false) {\n        this.syncbtn = false;\n      } else {\n        this.syncbtn = true;\n      }\n      this.saleoderreviewdata[i].checksync = valuecheck;\n      var ch = 0;\n      for (var x = 0; x < this.saleoderreviewdata.length; x++) {\n        if (this.saleoderreviewdata[x].checksync == true) {\n          this.syncbtn = false;\n          ch++;\n        } else {\n          ch--;\n        }\n      }\n      //alert('ch='+ch+'  data= '+this.saleoderreviewdata.length);\n      if (ch == this.saleoderreviewdata.length) {\n        this.selectallsync = true;\n        //alert(1);\n      } else {\n        this.selectallsync = false;\n        //alert(2);\n      }\n    }\n    syncdatasaleoder() {\n      var idsync = '';\n      this.syncidso = [];\n      //lert(this.saleoderreviewdata.length);\n      //return;\n      if (this.saleoderreviewdata.length < 1) {\n        //alert('กรุณาเลือกข้อมูลที่ต้องการ Sync');\n      } else {\n        for (var i = 0; i < this.saleoderreviewdata.length; i++) {\n          if (this.saleoderreviewdata[i].checksync == true) {\n            this.syncidso.push({\n              idso: this.saleoderreviewdata[i].id\n            });\n          }\n        }\n      }\n      if (this.syncidso.length > 0) {\n        for (var i = 0; i < this.syncidso.length; i++) {\n          if (this.syncidso.length == 1) {\n            idsync += ',|' + this.syncidso[i].idso + '|';\n          } else {\n            idsync += ',|' + this.syncidso[i].idso + '|';\n          }\n        }\n        const Http = new XMLHttpRequest();\n        const url = 'syncso/Service.asmx/SyncData?idSo=' + idsync.substring(1);\n        Http.open(\"GET\", url);\n        Http.send();\n        Http.onreadystatechange = e => {\n          if (Http.readyState == 4 && Http.status == 200) {\n            this.settimedownload = setInterval(() => this.SalesOrderdownload(), 500);\n          }\n        };\n        this.btnloadfile = true;\n        //alert('Sync ข้อมูล เสร็จสิ้น');\n        this.Searchsolist();\n      } else {\n        alert('กรุณาเลือกข้อมูลที่ต้องการ Sync');\n        return;\n      }\n    }\n    exportdataexcel() {\n      if (this.soreviewlist == undefined) {\n        alert('ไม่พบข้อมูล');\n        //this.openModal(true,'ไม่พบข้อมูล',false);\n      } else {\n        new Angular5Csv(this.soreviewlist, 'Soreview', this.options);\n      }\n    }\n    editviewsore(valueid) {\n      localStorage.setItem('DataSOderreview', JSON.stringify(this.savedata));\n      this.savedata = [];\n      // alert('Fucntion กำลัง ปิดปรับปรุง');\n      //this.openModal(true,'Fucntion กำลัง ปิดปรับปรุง',false);\n      this.loaddata = [];\n      this.router.navigate(['/editsaloderreview'], {\n        queryParams: {\n          idso: valueid\n        }\n      });\n    }\n    openModal(open, text, load) {\n      this.mdlSampleIsOpen = open;\n      this.alt = text;\n      this.checkreload = load;\n    }\n    closemodel(cl) {\n      this.mdlSampleIsOpen = cl;\n      if (this.checkreload == true) {\n        location.reload();\n      }\n    }\n    selectTab(tabId) {\n      this.staticTabs.tabs[tabId].active = true;\n    }\n    onUpload(showIDso, templateShow, template) {\n      this.modalRef.hide();\n      this.textload = \"กำลังอัพโหลดไฟล์ โปรดรอ.\";\n      this.openModal2(templateShow);\n      const fd = new FormData();\n      fd.append('PDF', this.selectedFile, this.selectedFile.name);\n      this.http.post(this.url + showIDso + '/uploadPDF', fd, {\n        reportProgress: true,\n        observe: 'events'\n      }).subscribe(event => {\n        if (event.type === HttpEventType.UploadProgress) {} else if (event.type === HttpEventType.Response) {\n          console.log(event);\n          if (event.body.success === true) {\n            this.textload = \"อัพโหลดไฟล์เสร็จสิ้น\";\n            this.btnPDF = true;\n            this.btnREpdf = false;\n            /*  success: true,\n            _path : DIRPDF,\n            _name : res.req.file.filename,\n            _mimetype : res.req.file.mimetype\n              */\n            this.updataFlie(showIDso, event.body._name, event.body._mimetype);\n            /*   alert(JSON.stringify(event.body))\n               alert('upload เสร็จสิ้น : >>>'+event.body.success )*/\n          } else {\n            this.textload = \"เกิดปัญหาในการ upload กรุณาทำรายการใหม่\";\n            this.btnPDF = false;\n            this.btnREpdf = true;\n            //alert('เกิดปัญหาในการ upload กรุณาทำรายการใหม่' )\n          }\n        }\n      });\n    }\n    updataFlie(_idSo, _name, _mimetype) {\n      //updataDPF_idSo\n      this.http.post(this.url + 'updataDPF_idSo', {\n        idSo: _idSo,\n        _name: _name,\n        _mimetype: _mimetype\n      }).subscribe(res => {\n        //  alert(res)\n        if (res == true) {\n          this.textload = \"ทำรายการเสร็จสิ้น\";\n          this.btnPDF = true;\n          this.btnREpdf = false;\n        } else {\n          this.textload = \"เกิดปัญหาในการ เพิ่มรายการ SaleOrder\";\n          this.btnPDF = false;\n          this.btnREpdf = true;\n        }\n      });\n    }\n    openModal2(templateShow) {\n      this.modalRef = this.modalService.show(templateShow, {\n        class: 'modal-sm',\n        backdrop: \"static\"\n      });\n    }\n    confirm() {\n      this.modalRef.hide();\n      this.Searchsolist();\n    }\n    decline(template) {\n      this.selectedFile = null;\n      this.modalRef.hide();\n      this.modalRef = this.modalService.show(template, this.config);\n    }\n    handleFileInput(file) {\n      this.textload = \"\";\n      if (file.item(0).type == \"application/pdf\" || file.item(0).type == \"image/jpeg\") {\n        this.selectedFile = file.item(0);\n        this.textload = this.selectedFile.name;\n      } else {\n        alert('ชนิดไฟล์ไม่ถูกต้อง กรุณาเลือกเป็นไฟล์ .PDF หรือ เป็นไฟล์ .jpeg');\n        this.textload = \"\";\n      }\n    }\n    static {\n      this.ɵfac = function SoreviewComponent_Factory(t) {\n        return new (t || SoreviewComponent)(i0.ɵɵdirectiveInject(i1.BsModalService), i0.ɵɵdirectiveInject(i2.HttpClient), i0.ɵɵdirectiveInject(i3.WebapiService), i0.ɵɵdirectiveInject(i4.Router), i0.ɵɵdirectiveInject(i5.NgbCalendar));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: SoreviewComponent,\n        selectors: [[\"app-soreview\"]],\n        viewQuery: function SoreviewComponent_Query(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵviewQuery(_c0, 5);\n          }\n          if (rf & 2) {\n            let _t;\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.staticTabs = _t.first);\n          }\n        },\n        decls: 96,\n        vars: 23,\n        consts: [[\"rt\", \"\"], [\"template\", \"\"], [\"templateShow\", \"\"], [\"templateAddfile\", \"\"], [\"staticTabs\", \"\"], [\"imageForm\", \"ngForm\"], [2, \"padding-top\", \"60px\"], [1, \"container-fluid\", 2, \"padding-right\", \"5px\", \"padding-left\", \"5px\"], [1, \"p-sm-1\", \"bg-secondary\", \"text-white\", \"text-center\"], [1, \"form-row\"], [\"class\", \"col-md-2 mb-2\", 4, \"ngIf\"], [1, \"col-md-2\", \"mb-2\"], [\"id\", \"typeahead-template\", \"type\", \"text\", \"name\", \"getcustomer\", 1, \"form-control\", \"form-control-sm\", 3, \"ngModelChange\", \"placeholder\", \"ngModel\", \"ngbTypeahead\", \"resultTemplate\", \"inputFormatter\"], [1, \"col-xs-12\", \"col-12\", \"col-md-2\", \"form-group\"], [\"type\", \"text\", \"placeholder\", \"DD/MM/YYYY\", \"bsDatepicker\", \"\", 1, \"form-control\", \"form-control-sm\", 3, \"ngModelChange\", \"bsConfig\", \"ngModel\"], [1, \"col-xs-12\", \"col-12\", \"col-lg-1\", \"form-group\"], [\"name\", \"typesync\", \"id\", \"typesync\", 1, \"custom-select\", \"custom-select-sm\", 3, \"change\", \"ngModelChange\", \"ngModel\"], [\"value\", \"3\", 1, \"text-black-50\"], [\"value\", \"1\", 1, \"text-black-50\"], [1, \"col-md-3\", \"mb-3\", \"col-12\", \"text-center\", \"text-sm-center\", \"text-md-left\", \"text-lg-left\", 2, \"padding-right\", \"0px\", \"padding-left\", \"0px\"], [\"type\", \"submit\", 1, \"btn\", \"btn-primary\", \"btn-sm\", \"font-weight-light\", 2, \"width\", \"60px\", 3, \"click\", \"disabled\"], [\"type\", \"submit\", 1, \"btn\", \"btn-primary\", \"btn-sm\", \"font-weight-light\", 2, \"margin-left\", \"3px\", \"width\", \"68px\", 3, \"click\", \"disabled\"], [\"type\", \"submit\", 1, \"btn\", \"btn-primary\", \"btn-sm\", \"font-weight-light\", 2, \"margin-left\", \"3px\", \"width\", \"68px\", 3, \"mouseup\", \"mousedown\", \"disabled\"], [1, \"table\", \"table-hover\", \"table-bordered\", \"table-sm\"], [1, \"text-sm-center\", \"bg-light\"], [\"scope\", \"col\", 1, \"font-weight-normal\"], [\"for\", \"selectallsync\"], [\"type\", \"checkbox\", \"name\", \"selectallsync\", \"id\", \"selectallsync\", 3, \"click\", \"checked\"], [\"scope\", \"col\", \"width\", \"60px\", 1, \"text-sm-center\"], [\"scope\", \"col\", \"class\", \"text-sm-center\", \"width\", \"60px\", 4, \"ngIf\"], [\"class\", \"text-sm-left\", 4, \"ngFor\", \"ngForOf\"], [\"id\", \"mdlSample\", \"role\", \"dialog\", 1, \"modal\", \"fade\", 3, \"ngStyle\"], [1, \"modal-dialog\", \"modal-md\"], [1, \"modal-content\"], [1, \"modal-header\", \"colhaederal\"], [1, \"modal-title\"], [1, \"modal-body\"], [\"align\", \"right\", 1, \"modal-footer\"], [\"type\", \"button\", \"id\", \"btnClose\", 1, \"btn\", \"btn-danger\", 3, \"click\"], [1, \"fa\", \"fa-times\", \"fa-fw\"], [\"multiple\", \"\", 1, \"custom-select\", \"custom-select-sm\", 3, \"ngModelChange\", \"ngModel\"], [\"selected\", \"\", \"value\", \"\"], [\"value\", \"%20\"], [3, \"ngStyle\", \"value\", 4, \"ngFor\", \"ngForOf\"], [3, \"ngStyle\", \"value\"], [1, \"text-sm-left\"], [1, \"text-sm-center\", \"font-weight-normal\"], [\"type\", \"checkbox\", \"name\", \"\", \"id\", \"\", 3, \"click\", \"ngStyle\", \"checked\"], [1, \"text-sm-center\", \"font-weight-normal\", 3, \"ngStyle\"], [1, \"text-sm-center\", \"font-weight-normal\", 3, \"click\", \"ngStyle\"], [1, \"text-sm-center\", \"font-weight-normal\", 3, \"tooltip\", \"ngStyle\"], [1, \"font-weight-normal\", 3, \"ngStyle\"], [1, \"text-sm-right\", \"font-weight-normal\", 3, \"ngStyle\"], [1, \"btn\", \"btn-link\", \"font-weight-normal\", 2, \"padding\", \"0px\", 3, \"click\"], [\"class\", \"text-center\", 4, \"ngIf\"], [1, \"text-center\"], [1, \"text-danger\", \"btn-link\", \"btn-sm\", 3, \"click\"], [1, \"text-info\", \"btn-link\", \"btn-sm\", 3, \"click\", \"ngStyle\"], [1, \"modal-header\"], [1, \"modal-title\", \"pull-left\"], [\"type\", \"button\", \"aria-label\", \"Close\", 1, \"close\", \"pull-right\", 3, \"click\"], [\"aria-hidden\", \"true\"], [1, \"modal-body\", 2, \"padding\", \"3px\"], [\"heading\", \"Sale Oder list\", 2, \"padding-top\", \"5px\"], [2, \"overflow-x\", \"auto\"], [1, \"text-center\", \"font-weight-normal\"], [1, \"font-weight-normal\"], [\"colspan\", \"3\", 1, \"font-weight-normal\", \"text-center\"], [4, \"ngFor\", \"ngForOf\"], [\"heading\", \"File\", 4, \"ngIf\"], [1, \"modal-footer\"], [\"target\", \"_blank\", \"type\", \"button\", \"class\", \"btn btn-primary\", 3, \"href\", 4, \"ngIf\"], [\"type\", \"button\", 1, \"btn\", \"btn-secondary\", 3, \"click\"], [1, \"text-center\", \"font-weight-sm\"], [1, \"text-left\", \"font-weight-sm\"], [1, \"text-right\", \"font-weight-sm\", 3, \"ngStyle\"], [1, \"text-right\", \"font-weight-sm\"], [\"heading\", \"File\"], [1, \"card\", \"card-body\"], [2, \"text-align\", \"center\"], [2, \"width\", \"100%\", 3, \"src\"], [\"target\", \"_blank\", \"type\", \"button\", 1, \"btn\", \"btn-primary\", 3, \"href\"], [1, \"modal-body\", \"text-center\"], [\"type\", \"button\", \"class\", \"btn btn-default\", 3, \"click\", 4, \"ngIf\"], [\"type\", \"button\", \"class\", \"btn btn-primary\", 3, \"click\", 4, \"ngIf\"], [\"type\", \"button\", 1, \"btn\", \"btn-default\", 3, \"click\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", 3, \"click\"], [1, \"form-group\", 2, \"margin-bottom\", \"0rem\"], [\"for\", \"exampleFormControlFile1\"], [\"type\", \"file\", \"accept\", \"application/pdf, image/*\", \"id\", \"exampleFormControlFile1\", 1, \"form-control-file\", 3, \"change\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", 3, \"click\", \"disabled\"]],\n        template: function SoreviewComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            const _r1 = i0.ɵɵgetCurrentView();\n            i0.ɵɵelement(0, \"app-topmenu\");\n            i0.ɵɵelementStart(1, \"section\", 6)(2, \"div\", 7)(3, \"h5\", 8);\n            i0.ɵɵtext(4, \"Sale Order Review\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(5, \"div\", 9);\n            i0.ɵɵtemplate(6, SoreviewComponent_div_6_Template, 7, 2, \"div\", 10);\n            i0.ɵɵelementStart(7, \"div\", 11);\n            i0.ɵɵtemplate(8, SoreviewComponent_ng_template_8_Template, 3, 2, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n            i0.ɵɵelementStart(10, \"input\", 12);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function SoreviewComponent_Template_input_ngModelChange_10_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.getcustomer, $event) || (ctx.getcustomer = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(11, \"div\", 13)(12, \"input\", 14);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function SoreviewComponent_Template_input_ngModelChange_12_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.Datafromdate, $event) || (ctx.Datafromdate = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(13, \"div\", 13)(14, \"input\", 14);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function SoreviewComponent_Template_input_ngModelChange_14_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.Datatodate, $event) || (ctx.Datatodate = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(15, \"div\", 15)(16, \"select\", 16);\n            i0.ɵɵlistener(\"change\", function SoreviewComponent_Template_select_change_16_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.Searchsolist());\n            });\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function SoreviewComponent_Template_select_ngModelChange_16_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.typesync, $event) || (ctx.typesync = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementStart(17, \"option\", 17);\n            i0.ɵɵtext(18, \"\\u0E01\\u0E33\\u0E25\\u0E31\\u0E07 Sync\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(19, \"option\", 18);\n            i0.ɵɵtext(20, \"\\u0E23\\u0E2D Sync\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(21, \"div\", 19)(22, \"button\", 20);\n            i0.ɵɵlistener(\"click\", function SoreviewComponent_Template_button_click_22_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.Searchsolist());\n            });\n            i0.ɵɵtext(23, \"Search\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(24, \"button\", 21);\n            i0.ɵɵlistener(\"click\", function SoreviewComponent_Template_button_click_24_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.exportdataexcel());\n            });\n            i0.ɵɵtext(25, \"Export\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(26, \"button\", 22);\n            i0.ɵɵlistener(\"mouseup\", function SoreviewComponent_Template_button_mouseup_26_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.syncdatasaleoder());\n            })(\"mousedown\", function SoreviewComponent_Template_button_mousedown_26_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.loadsalEXTfile());\n            });\n            i0.ɵɵtext(27, \"SyncData\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(28, \"table\", 23)(29, \"thead\")(30, \"tr\", 24)(31, \"th\", 25)(32, \"label\", 26);\n            i0.ɵɵtext(33, \"All\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(34, \"input\", 27);\n            i0.ɵɵlistener(\"click\", function SoreviewComponent_Template_input_click_34_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.selectallsyncdata($event.target.checked));\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(35, \"th\", 25);\n            i0.ɵɵtext(36, \"Item\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(37, \"th\", 25);\n            i0.ɵɵtext(38, \"\\u0E40\\u0E25\\u0E02\\u0E17\\u0E35\\u0E48 SO\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(39, \"th\", 25);\n            i0.ɵɵtext(40, \"\\u0E27\\u0E31\\u0E19\\u0E17\\u0E35\\u0E48\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(41, \"th\", 25);\n            i0.ɵɵtext(42, \"\\u0E40\\u0E27\\u0E25\\u0E32\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(43, \"th\", 25);\n            i0.ɵɵtext(44, \"\\u0E1E\\u0E19\\u0E31\\u0E01\\u0E07\\u0E32\\u0E19\\u0E02\\u0E32\\u0E22\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(45, \"th\", 25);\n            i0.ɵɵtext(46, \"\\u0E25\\u0E39\\u0E01\\u0E04\\u0E49\\u0E32\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(47, \"th\", 25);\n            i0.ɵɵtext(48, \"\\u0E0A\\u0E37\\u0E48\\u0E2D INV\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(49, \"th\", 25);\n            i0.ɵɵtext(50, \"TAX ID\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(51, \"th\", 25);\n            i0.ɵɵtext(52, \"\\u0E0A\\u0E37\\u0E48\\u0E2D\\u0E02\\u0E19\\u0E2A\\u0E48\\u0E07\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(53, \"th\", 25);\n            i0.ɵɵtext(54, \"\\u0E21\\u0E39\\u0E25\\u0E04\\u0E48\\u0E32\\u0E2A\\u0E34\\u0E19\\u0E04\\u0E49\\u0E32\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(55, \"th\", 25);\n            i0.ɵɵtext(56, \"\\u0E21\\u0E39\\u0E25\\u0E04\\u0E48\\u0E32\\u0E2A\\u0E38\\u0E17\\u0E18\\u0E34\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(57, \"th\", 25);\n            i0.ɵɵtext(58, \"VAT/No VAT\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(59, \"th\", 25);\n            i0.ɵɵtext(60, \"\\u0E19\\u0E49\\u0E33\\u0E2B\\u0E19\\u0E31\\u0E01\\u0E23\\u0E27\\u0E21\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(61, \"th\", 25);\n            i0.ɵɵtext(62, \"\\u0E1B\\u0E23\\u0E30\\u0E40\\u0E20\\u0E17\\u0E02\\u0E19\\u0E2A\\u0E48\\u0E07\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(63, \"th\", 25);\n            i0.ɵɵtext(64, \"\\u0E40\\u0E07\\u0E34\\u0E19\\u0E2A\\u0E14/\\u0E40\\u0E04\\u0E23\\u0E14\\u0E34\\u0E15\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(65, \"th\", 25);\n            i0.ɵɵtext(66, \"Note \\u0E20\\u0E32\\u0E22\\u0E43\\u0E19\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(67, \"th\", 25);\n            i0.ɵɵtext(68, \"\\u0E2B\\u0E21\\u0E32\\u0E22\\u0E40\\u0E2B\\u0E15\\u0E38\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(69, \"th\", 28);\n            i0.ɵɵtext(70, \"View\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(71, SoreviewComponent_th_71_Template, 2, 0, \"th\", 29);\n            i0.ɵɵelementStart(72, \"th\", 28);\n            i0.ɵɵtext(73, \"Delete\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(74, \"th\", 28);\n            i0.ɵɵtext(75, \"Sync Status\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(76, \"tbody\");\n            i0.ɵɵtemplate(77, SoreviewComponent_tr_77_Template, 52, 102, \"tr\", 30);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(78, \"div\", 31)(79, \"div\", 32)(80, \"div\", 33)(81, \"div\", 34)(82, \"h4\", 35);\n            i0.ɵɵtext(83, \"Report\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(84, \"div\", 36);\n            i0.ɵɵtext(85);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(86, \"div\", 37)(87, \"button\", 38);\n            i0.ɵɵlistener(\"click\", function SoreviewComponent_Template_button_click_87_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.closemodel(false));\n            });\n            i0.ɵɵelement(88, \"i\", 39);\n            i0.ɵɵtext(89, \" \\u0E1B\\u0E34\\u0E14\");\n            i0.ɵɵelementEnd()()()()()();\n            i0.ɵɵtemplate(90, SoreviewComponent_ng_template_90_Template, 37, 4, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor)(92, SoreviewComponent_ng_template_92_Template, 5, 3, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor)(94, SoreviewComponent_ng_template_94_Template, 16, 2, \"ng-template\", null, 3, i0.ɵɵtemplateRefExtractor);\n          }\n          if (rf & 2) {\n            const rt_r18 = i0.ɵɵreference(9);\n            i0.ɵɵadvance(6);\n            i0.ɵɵproperty(\"ngIf\", ctx.testclose);\n            i0.ɵɵadvance(4);\n            i0.ɵɵpropertyInterpolate(\"placeholder\", ctx.company);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.getcustomer);\n            i0.ɵɵproperty(\"ngbTypeahead\", ctx.search)(\"resultTemplate\", rt_r18)(\"inputFormatter\", ctx.formatter);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"bsConfig\", i0.ɵɵpureFunction0(19, _c1));\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.Datafromdate);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"bsConfig\", i0.ɵɵpureFunction0(20, _c1));\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.Datatodate);\n            i0.ɵɵadvance(2);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.typesync);\n            i0.ɵɵadvance(6);\n            i0.ɵɵproperty(\"disabled\", ctx.searchbtn);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"disabled\", ctx.exportbtn);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"disabled\", ctx.syncbtn);\n            i0.ɵɵadvance(8);\n            i0.ɵɵproperty(\"checked\", ctx.selectallsync);\n            i0.ɵɵadvance(37);\n            i0.ɵɵproperty(\"ngIf\", ctx.ennablecustomer == false);\n            i0.ɵɵadvance(6);\n            i0.ɵɵproperty(\"ngForOf\", ctx.saleoderreviewdata);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(21, _c2, ctx.mdlSampleIsOpen ? \"block\" : \"none\"));\n            i0.ɵɵadvance(7);\n            i0.ɵɵtextInterpolate(ctx.alt);\n          }\n        },\n        styles: [\".lg[_ngcontent-%COMP%], .modal-dialog800[_ngcontent-%COMP%]{max-width:800px}\"]\n      });\n    }\n  }\n  return SoreviewComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}