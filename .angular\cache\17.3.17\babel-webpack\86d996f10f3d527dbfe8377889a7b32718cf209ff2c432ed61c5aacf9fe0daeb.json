{"ast": null, "code": "/*! DataTables 1.13.11\n * ©2008-2024 SpryMedia Ltd - datatables.net/license\n */\n\nimport jQuery from 'jquery';\n\n// DataTables code uses $ internally, but we want to be able to\n// reassign $ with the `use` method, so it is a regular var.\nvar $ = jQuery;\nvar DataTable = function (selector, options) {\n  // Check if called with a window or jQuery object for DOM less applications\n  // This is for backwards compatibility\n  if (DataTable.factory(selector, options)) {\n    return DataTable;\n  }\n\n  // When creating with `new`, create a new DataTable, returning the API instance\n  if (this instanceof DataTable) {\n    return $(selector).DataTable(options);\n  } else {\n    // Argument switching\n    options = selector;\n  }\n\n  /**\n   * Perform a jQuery selector action on the table's TR elements (from the tbody) and\n   * return the resulting jQuery object.\n   *  @param {string|node|jQuery} sSelector jQuery selector or node collection to act on\n   *  @param {object} [oOpts] Optional parameters for modifying the rows to be included\n   *  @param {string} [oOpts.filter=none] Select TR elements that meet the current filter\n   *    criterion (\"applied\") or all TR elements (i.e. no filter).\n   *  @param {string} [oOpts.order=current] Order of the TR elements in the processed array.\n   *    Can be either 'current', whereby the current sorting of the table is used, or\n   *    'original' whereby the original order the data was read into the table is used.\n   *  @param {string} [oOpts.page=all] Limit the selection to the currently displayed page\n   *    (\"current\") or not (\"all\"). If 'current' is given, then order is assumed to be\n   *    'current' and filter is 'applied', regardless of what they might be given as.\n   *  @returns {object} jQuery object, filtered by the given selector.\n   *  @dtopt API\n   *  @deprecated Since v1.10\n   *\n   *  @example\n   *    $(document).ready(function() {\n   *      var oTable = $('#example').dataTable();\n   *\n   *      // Highlight every second row\n   *      oTable.$('tr:odd').css('backgroundColor', 'blue');\n   *    } );\n   *\n   *  @example\n   *    $(document).ready(function() {\n   *      var oTable = $('#example').dataTable();\n   *\n   *      // Filter to rows with 'Webkit' in them, add a background colour and then\n   *      // remove the filter, thus highlighting the 'Webkit' rows only.\n   *      oTable.fnFilter('Webkit');\n   *      oTable.$('tr', {\"search\": \"applied\"}).css('backgroundColor', 'blue');\n   *      oTable.fnFilter('');\n   *    } );\n   */\n  this.$ = function (sSelector, oOpts) {\n    return this.api(true).$(sSelector, oOpts);\n  };\n\n  /**\n   * Almost identical to $ in operation, but in this case returns the data for the matched\n   * rows - as such, the jQuery selector used should match TR row nodes or TD/TH cell nodes\n   * rather than any descendants, so the data can be obtained for the row/cell. If matching\n   * rows are found, the data returned is the original data array/object that was used to\n   * create the row (or a generated array if from a DOM source).\n   *\n   * This method is often useful in-combination with $ where both functions are given the\n   * same parameters and the array indexes will match identically.\n   *  @param {string|node|jQuery} sSelector jQuery selector or node collection to act on\n   *  @param {object} [oOpts] Optional parameters for modifying the rows to be included\n   *  @param {string} [oOpts.filter=none] Select elements that meet the current filter\n   *    criterion (\"applied\") or all elements (i.e. no filter).\n   *  @param {string} [oOpts.order=current] Order of the data in the processed array.\n   *    Can be either 'current', whereby the current sorting of the table is used, or\n   *    'original' whereby the original order the data was read into the table is used.\n   *  @param {string} [oOpts.page=all] Limit the selection to the currently displayed page\n   *    (\"current\") or not (\"all\"). If 'current' is given, then order is assumed to be\n   *    'current' and filter is 'applied', regardless of what they might be given as.\n   *  @returns {array} Data for the matched elements. If any elements, as a result of the\n   *    selector, were not TR, TD or TH elements in the DataTable, they will have a null\n   *    entry in the array.\n   *  @dtopt API\n   *  @deprecated Since v1.10\n   *\n   *  @example\n   *    $(document).ready(function() {\n   *      var oTable = $('#example').dataTable();\n   *\n   *      // Get the data from the first row in the table\n   *      var data = oTable._('tr:first');\n   *\n   *      // Do something useful with the data\n   *      alert( \"First cell is: \"+data[0] );\n   *    } );\n   *\n   *  @example\n   *    $(document).ready(function() {\n   *      var oTable = $('#example').dataTable();\n   *\n   *      // Filter to 'Webkit' and get all data for\n   *      oTable.fnFilter('Webkit');\n   *      var data = oTable._('tr', {\"search\": \"applied\"});\n   *\n   *      // Do something with the data\n   *      alert( data.length+\" rows matched the search\" );\n   *    } );\n   */\n  this._ = function (sSelector, oOpts) {\n    return this.api(true).rows(sSelector, oOpts).data();\n  };\n\n  /**\n   * Create a DataTables Api instance, with the currently selected tables for\n   * the Api's context.\n   * @param {boolean} [traditional=false] Set the API instance's context to be\n   *   only the table referred to by the `DataTable.ext.iApiIndex` option, as was\n   *   used in the API presented by DataTables 1.9- (i.e. the traditional mode),\n   *   or if all tables captured in the jQuery object should be used.\n   * @return {DataTables.Api}\n   */\n  this.api = function (traditional) {\n    return traditional ? new _Api(_fnSettingsFromNode(this[_ext.iApiIndex])) : new _Api(this);\n  };\n\n  /**\n   * Add a single new row or multiple rows of data to the table. Please note\n   * that this is suitable for client-side processing only - if you are using\n   * server-side processing (i.e. \"bServerSide\": true), then to add data, you\n   * must add it to the data source, i.e. the server-side, through an Ajax call.\n   *  @param {array|object} data The data to be added to the table. This can be:\n   *    <ul>\n   *      <li>1D array of data - add a single row with the data provided</li>\n   *      <li>2D array of arrays - add multiple rows in a single call</li>\n   *      <li>object - data object when using <i>mData</i></li>\n   *      <li>array of objects - multiple data objects when using <i>mData</i></li>\n   *    </ul>\n   *  @param {bool} [redraw=true] redraw the table or not\n   *  @returns {array} An array of integers, representing the list of indexes in\n   *    <i>aoData</i> ({@link DataTable.models.oSettings}) that have been added to\n   *    the table.\n   *  @dtopt API\n   *  @deprecated Since v1.10\n   *\n   *  @example\n   *    // Global var for counter\n   *    var giCount = 2;\n   *\n   *    $(document).ready(function() {\n   *      $('#example').dataTable();\n   *    } );\n   *\n   *    function fnClickAddRow() {\n   *      $('#example').dataTable().fnAddData( [\n   *        giCount+\".1\",\n   *        giCount+\".2\",\n   *        giCount+\".3\",\n   *        giCount+\".4\" ]\n   *      );\n   *\n   *      giCount++;\n   *    }\n   */\n  this.fnAddData = function (data, redraw) {\n    var api = this.api(true);\n\n    /* Check if we want to add multiple rows or not */\n    var rows = Array.isArray(data) && (Array.isArray(data[0]) || $.isPlainObject(data[0])) ? api.rows.add(data) : api.row.add(data);\n    if (redraw === undefined || redraw) {\n      api.draw();\n    }\n    return rows.flatten().toArray();\n  };\n\n  /**\n   * This function will make DataTables recalculate the column sizes, based on the data\n   * contained in the table and the sizes applied to the columns (in the DOM, CSS or\n   * through the sWidth parameter). This can be useful when the width of the table's\n   * parent element changes (for example a window resize).\n   *  @param {boolean} [bRedraw=true] Redraw the table or not, you will typically want to\n   *  @dtopt API\n   *  @deprecated Since v1.10\n   *\n   *  @example\n   *    $(document).ready(function() {\n   *      var oTable = $('#example').dataTable( {\n   *        \"sScrollY\": \"200px\",\n   *        \"bPaginate\": false\n   *      } );\n   *\n   *      $(window).on('resize', function () {\n   *        oTable.fnAdjustColumnSizing();\n   *      } );\n   *    } );\n   */\n  this.fnAdjustColumnSizing = function (bRedraw) {\n    var api = this.api(true).columns.adjust();\n    var settings = api.settings()[0];\n    var scroll = settings.oScroll;\n    if (bRedraw === undefined || bRedraw) {\n      api.draw(false);\n    } else if (scroll.sX !== \"\" || scroll.sY !== \"\") {\n      /* If not redrawing, but scrolling, we want to apply the new column sizes anyway */\n      _fnScrollDraw(settings);\n    }\n  };\n\n  /**\n   * Quickly and simply clear a table\n   *  @param {bool} [bRedraw=true] redraw the table or not\n   *  @dtopt API\n   *  @deprecated Since v1.10\n   *\n   *  @example\n   *    $(document).ready(function() {\n   *      var oTable = $('#example').dataTable();\n   *\n   *      // Immediately 'nuke' the current rows (perhaps waiting for an Ajax callback...)\n   *      oTable.fnClearTable();\n   *    } );\n   */\n  this.fnClearTable = function (bRedraw) {\n    var api = this.api(true).clear();\n    if (bRedraw === undefined || bRedraw) {\n      api.draw();\n    }\n  };\n\n  /**\n   * The exact opposite of 'opening' a row, this function will close any rows which\n   * are currently 'open'.\n   *  @param {node} nTr the table row to 'close'\n   *  @returns {int} 0 on success, or 1 if failed (can't find the row)\n   *  @dtopt API\n   *  @deprecated Since v1.10\n   *\n   *  @example\n   *    $(document).ready(function() {\n   *      var oTable;\n   *\n   *      // 'open' an information row when a row is clicked on\n   *      $('#example tbody tr').click( function () {\n   *        if ( oTable.fnIsOpen(this) ) {\n   *          oTable.fnClose( this );\n   *        } else {\n   *          oTable.fnOpen( this, \"Temporary row opened\", \"info_row\" );\n   *        }\n   *      } );\n   *\n   *      oTable = $('#example').dataTable();\n   *    } );\n   */\n  this.fnClose = function (nTr) {\n    this.api(true).row(nTr).child.hide();\n  };\n\n  /**\n   * Remove a row for the table\n   *  @param {mixed} target The index of the row from aoData to be deleted, or\n   *    the TR element you want to delete\n   *  @param {function|null} [callBack] Callback function\n   *  @param {bool} [redraw=true] Redraw the table or not\n   *  @returns {array} The row that was deleted\n   *  @dtopt API\n   *  @deprecated Since v1.10\n   *\n   *  @example\n   *    $(document).ready(function() {\n   *      var oTable = $('#example').dataTable();\n   *\n   *      // Immediately remove the first row\n   *      oTable.fnDeleteRow( 0 );\n   *    } );\n   */\n  this.fnDeleteRow = function (target, callback, redraw) {\n    var api = this.api(true);\n    var rows = api.rows(target);\n    var settings = rows.settings()[0];\n    var data = settings.aoData[rows[0][0]];\n    rows.remove();\n    if (callback) {\n      callback.call(this, settings, data);\n    }\n    if (redraw === undefined || redraw) {\n      api.draw();\n    }\n    return data;\n  };\n\n  /**\n   * Restore the table to it's original state in the DOM by removing all of DataTables\n   * enhancements, alterations to the DOM structure of the table and event listeners.\n   *  @param {boolean} [remove=false] Completely remove the table from the DOM\n   *  @dtopt API\n   *  @deprecated Since v1.10\n   *\n   *  @example\n   *    $(document).ready(function() {\n   *      // This example is fairly pointless in reality, but shows how fnDestroy can be used\n   *      var oTable = $('#example').dataTable();\n   *      oTable.fnDestroy();\n   *    } );\n   */\n  this.fnDestroy = function (remove) {\n    this.api(true).destroy(remove);\n  };\n\n  /**\n   * Redraw the table\n   *  @param {bool} [complete=true] Re-filter and resort (if enabled) the table before the draw.\n   *  @dtopt API\n   *  @deprecated Since v1.10\n   *\n   *  @example\n   *    $(document).ready(function() {\n   *      var oTable = $('#example').dataTable();\n   *\n   *      // Re-draw the table - you wouldn't want to do it here, but it's an example :-)\n   *      oTable.fnDraw();\n   *    } );\n   */\n  this.fnDraw = function (complete) {\n    // Note that this isn't an exact match to the old call to _fnDraw - it takes\n    // into account the new data, but can hold position.\n    this.api(true).draw(complete);\n  };\n\n  /**\n   * Filter the input based on data\n   *  @param {string} sInput String to filter the table on\n   *  @param {int|null} [iColumn] Column to limit filtering to\n   *  @param {bool} [bRegex=false] Treat as regular expression or not\n   *  @param {bool} [bSmart=true] Perform smart filtering or not\n   *  @param {bool} [bShowGlobal=true] Show the input global filter in it's input box(es)\n   *  @param {bool} [bCaseInsensitive=true] Do case-insensitive matching (true) or not (false)\n   *  @dtopt API\n   *  @deprecated Since v1.10\n   *\n   *  @example\n   *    $(document).ready(function() {\n   *      var oTable = $('#example').dataTable();\n   *\n   *      // Sometime later - filter...\n   *      oTable.fnFilter( 'test string' );\n   *    } );\n   */\n  this.fnFilter = function (sInput, iColumn, bRegex, bSmart, bShowGlobal, bCaseInsensitive) {\n    var api = this.api(true);\n    if (iColumn === null || iColumn === undefined) {\n      api.search(sInput, bRegex, bSmart, bCaseInsensitive);\n    } else {\n      api.column(iColumn).search(sInput, bRegex, bSmart, bCaseInsensitive);\n    }\n    api.draw();\n  };\n\n  /**\n   * Get the data for the whole table, an individual row or an individual cell based on the\n   * provided parameters.\n   *  @param {int|node} [src] A TR row node, TD/TH cell node or an integer. If given as\n   *    a TR node then the data source for the whole row will be returned. If given as a\n   *    TD/TH cell node then iCol will be automatically calculated and the data for the\n   *    cell returned. If given as an integer, then this is treated as the aoData internal\n   *    data index for the row (see fnGetPosition) and the data for that row used.\n   *  @param {int} [col] Optional column index that you want the data of.\n   *  @returns {array|object|string} If mRow is undefined, then the data for all rows is\n   *    returned. If mRow is defined, just data for that row, and is iCol is\n   *    defined, only data for the designated cell is returned.\n   *  @dtopt API\n   *  @deprecated Since v1.10\n   *\n   *  @example\n   *    // Row data\n   *    $(document).ready(function() {\n   *      oTable = $('#example').dataTable();\n   *\n   *      oTable.$('tr').click( function () {\n   *        var data = oTable.fnGetData( this );\n   *        // ... do something with the array / object of data for the row\n   *      } );\n   *    } );\n   *\n   *  @example\n   *    // Individual cell data\n   *    $(document).ready(function() {\n   *      oTable = $('#example').dataTable();\n   *\n   *      oTable.$('td').click( function () {\n   *        var sData = oTable.fnGetData( this );\n   *        alert( 'The cell clicked on had the value of '+sData );\n   *      } );\n   *    } );\n   */\n  this.fnGetData = function (src, col) {\n    var api = this.api(true);\n    if (src !== undefined) {\n      var type = src.nodeName ? src.nodeName.toLowerCase() : '';\n      return col !== undefined || type == 'td' || type == 'th' ? api.cell(src, col).data() : api.row(src).data() || null;\n    }\n    return api.data().toArray();\n  };\n\n  /**\n   * Get an array of the TR nodes that are used in the table's body. Note that you will\n   * typically want to use the '$' API method in preference to this as it is more\n   * flexible.\n   *  @param {int} [iRow] Optional row index for the TR element you want\n   *  @returns {array|node} If iRow is undefined, returns an array of all TR elements\n   *    in the table's body, or iRow is defined, just the TR element requested.\n   *  @dtopt API\n   *  @deprecated Since v1.10\n   *\n   *  @example\n   *    $(document).ready(function() {\n   *      var oTable = $('#example').dataTable();\n   *\n   *      // Get the nodes from the table\n   *      var nNodes = oTable.fnGetNodes( );\n   *    } );\n   */\n  this.fnGetNodes = function (iRow) {\n    var api = this.api(true);\n    return iRow !== undefined ? api.row(iRow).node() : api.rows().nodes().flatten().toArray();\n  };\n\n  /**\n   * Get the array indexes of a particular cell from it's DOM element\n   * and column index including hidden columns\n   *  @param {node} node this can either be a TR, TD or TH in the table's body\n   *  @returns {int} If nNode is given as a TR, then a single index is returned, or\n   *    if given as a cell, an array of [row index, column index (visible),\n   *    column index (all)] is given.\n   *  @dtopt API\n   *  @deprecated Since v1.10\n   *\n   *  @example\n   *    $(document).ready(function() {\n   *      $('#example tbody td').click( function () {\n   *        // Get the position of the current data from the node\n   *        var aPos = oTable.fnGetPosition( this );\n   *\n   *        // Get the data array for this row\n   *        var aData = oTable.fnGetData( aPos[0] );\n   *\n   *        // Update the data array and return the value\n   *        aData[ aPos[1] ] = 'clicked';\n   *        this.innerHTML = 'clicked';\n   *      } );\n   *\n   *      // Init DataTables\n   *      oTable = $('#example').dataTable();\n   *    } );\n   */\n  this.fnGetPosition = function (node) {\n    var api = this.api(true);\n    var nodeName = node.nodeName.toUpperCase();\n    if (nodeName == 'TR') {\n      return api.row(node).index();\n    } else if (nodeName == 'TD' || nodeName == 'TH') {\n      var cell = api.cell(node).index();\n      return [cell.row, cell.columnVisible, cell.column];\n    }\n    return null;\n  };\n\n  /**\n   * Check to see if a row is 'open' or not.\n   *  @param {node} nTr the table row to check\n   *  @returns {boolean} true if the row is currently open, false otherwise\n   *  @dtopt API\n   *  @deprecated Since v1.10\n   *\n   *  @example\n   *    $(document).ready(function() {\n   *      var oTable;\n   *\n   *      // 'open' an information row when a row is clicked on\n   *      $('#example tbody tr').click( function () {\n   *        if ( oTable.fnIsOpen(this) ) {\n   *          oTable.fnClose( this );\n   *        } else {\n   *          oTable.fnOpen( this, \"Temporary row opened\", \"info_row\" );\n   *        }\n   *      } );\n   *\n   *      oTable = $('#example').dataTable();\n   *    } );\n   */\n  this.fnIsOpen = function (nTr) {\n    return this.api(true).row(nTr).child.isShown();\n  };\n\n  /**\n   * This function will place a new row directly after a row which is currently\n   * on display on the page, with the HTML contents that is passed into the\n   * function. This can be used, for example, to ask for confirmation that a\n   * particular record should be deleted.\n   *  @param {node} nTr The table row to 'open'\n   *  @param {string|node|jQuery} mHtml The HTML to put into the row\n   *  @param {string} sClass Class to give the new TD cell\n   *  @returns {node} The row opened. Note that if the table row passed in as the\n   *    first parameter, is not found in the table, this method will silently\n   *    return.\n   *  @dtopt API\n   *  @deprecated Since v1.10\n   *\n   *  @example\n   *    $(document).ready(function() {\n   *      var oTable;\n   *\n   *      // 'open' an information row when a row is clicked on\n   *      $('#example tbody tr').click( function () {\n   *        if ( oTable.fnIsOpen(this) ) {\n   *          oTable.fnClose( this );\n   *        } else {\n   *          oTable.fnOpen( this, \"Temporary row opened\", \"info_row\" );\n   *        }\n   *      } );\n   *\n   *      oTable = $('#example').dataTable();\n   *    } );\n   */\n  this.fnOpen = function (nTr, mHtml, sClass) {\n    return this.api(true).row(nTr).child(mHtml, sClass).show().child()[0];\n  };\n\n  /**\n   * Change the pagination - provides the internal logic for pagination in a simple API\n   * function. With this function you can have a DataTables table go to the next,\n   * previous, first or last pages.\n   *  @param {string|int} mAction Paging action to take: \"first\", \"previous\", \"next\" or \"last\"\n   *    or page number to jump to (integer), note that page 0 is the first page.\n   *  @param {bool} [bRedraw=true] Redraw the table or not\n   *  @dtopt API\n   *  @deprecated Since v1.10\n   *\n   *  @example\n   *    $(document).ready(function() {\n   *      var oTable = $('#example').dataTable();\n   *      oTable.fnPageChange( 'next' );\n   *    } );\n   */\n  this.fnPageChange = function (mAction, bRedraw) {\n    var api = this.api(true).page(mAction);\n    if (bRedraw === undefined || bRedraw) {\n      api.draw(false);\n    }\n  };\n\n  /**\n   * Show a particular column\n   *  @param {int} iCol The column whose display should be changed\n   *  @param {bool} bShow Show (true) or hide (false) the column\n   *  @param {bool} [bRedraw=true] Redraw the table or not\n   *  @dtopt API\n   *  @deprecated Since v1.10\n   *\n   *  @example\n   *    $(document).ready(function() {\n   *      var oTable = $('#example').dataTable();\n   *\n   *      // Hide the second column after initialisation\n   *      oTable.fnSetColumnVis( 1, false );\n   *    } );\n   */\n  this.fnSetColumnVis = function (iCol, bShow, bRedraw) {\n    var api = this.api(true).column(iCol).visible(bShow);\n    if (bRedraw === undefined || bRedraw) {\n      api.columns.adjust().draw();\n    }\n  };\n\n  /**\n   * Get the settings for a particular table for external manipulation\n   *  @returns {object} DataTables settings object. See\n   *    {@link DataTable.models.oSettings}\n   *  @dtopt API\n   *  @deprecated Since v1.10\n   *\n   *  @example\n   *    $(document).ready(function() {\n   *      var oTable = $('#example').dataTable();\n   *      var oSettings = oTable.fnSettings();\n   *\n   *      // Show an example parameter from the settings\n   *      alert( oSettings._iDisplayStart );\n   *    } );\n   */\n  this.fnSettings = function () {\n    return _fnSettingsFromNode(this[_ext.iApiIndex]);\n  };\n\n  /**\n   * Sort the table by a particular column\n   *  @param {int} iCol the data index to sort on. Note that this will not match the\n   *    'display index' if you have hidden data entries\n   *  @dtopt API\n   *  @deprecated Since v1.10\n   *\n   *  @example\n   *    $(document).ready(function() {\n   *      var oTable = $('#example').dataTable();\n   *\n   *      // Sort immediately with columns 0 and 1\n   *      oTable.fnSort( [ [0,'asc'], [1,'asc'] ] );\n   *    } );\n   */\n  this.fnSort = function (aaSort) {\n    this.api(true).order(aaSort).draw();\n  };\n\n  /**\n   * Attach a sort listener to an element for a given column\n   *  @param {node} nNode the element to attach the sort listener to\n   *  @param {int} iColumn the column that a click on this node will sort on\n   *  @param {function} [fnCallback] callback function when sort is run\n   *  @dtopt API\n   *  @deprecated Since v1.10\n   *\n   *  @example\n   *    $(document).ready(function() {\n   *      var oTable = $('#example').dataTable();\n   *\n   *      // Sort on column 1, when 'sorter' is clicked on\n   *      oTable.fnSortListener( document.getElementById('sorter'), 1 );\n   *    } );\n   */\n  this.fnSortListener = function (nNode, iColumn, fnCallback) {\n    this.api(true).order.listener(nNode, iColumn, fnCallback);\n  };\n\n  /**\n   * Update a table cell or row - this method will accept either a single value to\n   * update the cell with, an array of values with one element for each column or\n   * an object in the same format as the original data source. The function is\n   * self-referencing in order to make the multi column updates easier.\n   *  @param {object|array|string} mData Data to update the cell/row with\n   *  @param {node|int} mRow TR element you want to update or the aoData index\n   *  @param {int} [iColumn] The column to update, give as null or undefined to\n   *    update a whole row.\n   *  @param {bool} [bRedraw=true] Redraw the table or not\n   *  @param {bool} [bAction=true] Perform pre-draw actions or not\n   *  @returns {int} 0 on success, 1 on error\n   *  @dtopt API\n   *  @deprecated Since v1.10\n   *\n   *  @example\n   *    $(document).ready(function() {\n   *      var oTable = $('#example').dataTable();\n   *      oTable.fnUpdate( 'Example update', 0, 0 ); // Single cell\n   *      oTable.fnUpdate( ['a', 'b', 'c', 'd', 'e'], $('tbody tr')[0] ); // Row\n   *    } );\n   */\n  this.fnUpdate = function (mData, mRow, iColumn, bRedraw, bAction) {\n    var api = this.api(true);\n    if (iColumn === undefined || iColumn === null) {\n      api.row(mRow).data(mData);\n    } else {\n      api.cell(mRow, iColumn).data(mData);\n    }\n    if (bAction === undefined || bAction) {\n      api.columns.adjust();\n    }\n    if (bRedraw === undefined || bRedraw) {\n      api.draw();\n    }\n    return 0;\n  };\n\n  /**\n   * Provide a common method for plug-ins to check the version of DataTables being used, in order\n   * to ensure compatibility.\n   *  @param {string} sVersion Version string to check for, in the format \"X.Y.Z\". Note that the\n   *    formats \"X\" and \"X.Y\" are also acceptable.\n   *  @returns {boolean} true if this version of DataTables is greater or equal to the required\n   *    version, or false if this version of DataTales is not suitable\n   *  @method\n   *  @dtopt API\n   *  @deprecated Since v1.10\n   *\n   *  @example\n   *    $(document).ready(function() {\n   *      var oTable = $('#example').dataTable();\n   *      alert( oTable.fnVersionCheck( '1.9.0' ) );\n   *    } );\n   */\n  this.fnVersionCheck = _ext.fnVersionCheck;\n  var _that = this;\n  var emptyInit = options === undefined;\n  var len = this.length;\n  if (emptyInit) {\n    options = {};\n  }\n  this.oApi = this.internal = _ext.internal;\n\n  // Extend with old style plug-in API methods\n  for (var fn in DataTable.ext.internal) {\n    if (fn) {\n      this[fn] = _fnExternApiFunc(fn);\n    }\n  }\n  this.each(function () {\n    // For each initialisation we want to give it a clean initialisation\n    // object that can be bashed around\n    var o = {};\n    var oInit = len > 1 ?\n    // optimisation for single table case\n    _fnExtend(o, options, true) : options;\n\n    /*global oInit,_that,emptyInit*/\n    var i = 0,\n      iLen,\n      j,\n      jLen,\n      k,\n      kLen;\n    var sId = this.getAttribute('id');\n    var bInitHandedOff = false;\n    var defaults = DataTable.defaults;\n    var $this = $(this);\n\n    /* Sanity check */\n    if (this.nodeName.toLowerCase() != 'table') {\n      _fnLog(null, 0, 'Non-table node initialisation (' + this.nodeName + ')', 2);\n      return;\n    }\n\n    /* Backwards compatibility for the defaults */\n    _fnCompatOpts(defaults);\n    _fnCompatCols(defaults.column);\n\n    /* Convert the camel-case defaults to Hungarian */\n    _fnCamelToHungarian(defaults, defaults, true);\n    _fnCamelToHungarian(defaults.column, defaults.column, true);\n\n    /* Setting up the initialisation object */\n    _fnCamelToHungarian(defaults, $.extend(oInit, $this.data()), true);\n\n    /* Check to see if we are re-initialising a table */\n    var allSettings = DataTable.settings;\n    for (i = 0, iLen = allSettings.length; i < iLen; i++) {\n      var s = allSettings[i];\n\n      /* Base check on table node */\n      if (s.nTable == this || s.nTHead && s.nTHead.parentNode == this || s.nTFoot && s.nTFoot.parentNode == this) {\n        var bRetrieve = oInit.bRetrieve !== undefined ? oInit.bRetrieve : defaults.bRetrieve;\n        var bDestroy = oInit.bDestroy !== undefined ? oInit.bDestroy : defaults.bDestroy;\n        if (emptyInit || bRetrieve) {\n          return s.oInstance;\n        } else if (bDestroy) {\n          s.oInstance.fnDestroy();\n          break;\n        } else {\n          _fnLog(s, 0, 'Cannot reinitialise DataTable', 3);\n          return;\n        }\n      }\n\n      /* If the element we are initialising has the same ID as a table which was previously\n       * initialised, but the table nodes don't match (from before) then we destroy the old\n       * instance by simply deleting it. This is under the assumption that the table has been\n       * destroyed by other methods. Anyone using non-id selectors will need to do this manually\n       */\n      if (s.sTableId == this.id) {\n        allSettings.splice(i, 1);\n        break;\n      }\n    }\n\n    /* Ensure the table has an ID - required for accessibility */\n    if (sId === null || sId === \"\") {\n      sId = \"DataTables_Table_\" + DataTable.ext._unique++;\n      this.id = sId;\n    }\n\n    /* Create the settings object for this table and set some of the default parameters */\n    var oSettings = $.extend(true, {}, DataTable.models.oSettings, {\n      \"sDestroyWidth\": $this[0].style.width,\n      \"sInstance\": sId,\n      \"sTableId\": sId\n    });\n    oSettings.nTable = this;\n    oSettings.oApi = _that.internal;\n    oSettings.oInit = oInit;\n    allSettings.push(oSettings);\n\n    // Need to add the instance after the instance after the settings object has been added\n    // to the settings array, so we can self reference the table instance if more than one\n    oSettings.oInstance = _that.length === 1 ? _that : $this.dataTable();\n\n    // Backwards compatibility, before we apply all the defaults\n    _fnCompatOpts(oInit);\n    _fnLanguageCompat(oInit.oLanguage);\n\n    // If the length menu is given, but the init display length is not, use the length menu\n    if (oInit.aLengthMenu && !oInit.iDisplayLength) {\n      oInit.iDisplayLength = Array.isArray(oInit.aLengthMenu[0]) ? oInit.aLengthMenu[0][0] : oInit.aLengthMenu[0];\n    }\n\n    // Apply the defaults and init options to make a single init object will all\n    // options defined from defaults and instance options.\n    oInit = _fnExtend($.extend(true, {}, defaults), oInit);\n\n    // Map the initialisation options onto the settings object\n    _fnMap(oSettings.oFeatures, oInit, [\"bPaginate\", \"bLengthChange\", \"bFilter\", \"bSort\", \"bSortMulti\", \"bInfo\", \"bProcessing\", \"bAutoWidth\", \"bSortClasses\", \"bServerSide\", \"bDeferRender\"]);\n    _fnMap(oSettings, oInit, [\"asStripeClasses\", \"ajax\", \"fnServerData\", \"fnFormatNumber\", \"sServerMethod\", \"aaSorting\", \"aaSortingFixed\", \"aLengthMenu\", \"sPaginationType\", \"sAjaxSource\", \"sAjaxDataProp\", \"iStateDuration\", \"sDom\", \"bSortCellsTop\", \"iTabIndex\", \"fnStateLoadCallback\", \"fnStateSaveCallback\", \"renderer\", \"searchDelay\", \"rowId\", [\"iCookieDuration\", \"iStateDuration\"],\n    // backwards compat\n    [\"oSearch\", \"oPreviousSearch\"], [\"aoSearchCols\", \"aoPreSearchCols\"], [\"iDisplayLength\", \"_iDisplayLength\"]]);\n    _fnMap(oSettings.oScroll, oInit, [[\"sScrollX\", \"sX\"], [\"sScrollXInner\", \"sXInner\"], [\"sScrollY\", \"sY\"], [\"bScrollCollapse\", \"bCollapse\"]]);\n    _fnMap(oSettings.oLanguage, oInit, \"fnInfoCallback\");\n\n    /* Callback functions which are array driven */\n    _fnCallbackReg(oSettings, 'aoDrawCallback', oInit.fnDrawCallback, 'user');\n    _fnCallbackReg(oSettings, 'aoServerParams', oInit.fnServerParams, 'user');\n    _fnCallbackReg(oSettings, 'aoStateSaveParams', oInit.fnStateSaveParams, 'user');\n    _fnCallbackReg(oSettings, 'aoStateLoadParams', oInit.fnStateLoadParams, 'user');\n    _fnCallbackReg(oSettings, 'aoStateLoaded', oInit.fnStateLoaded, 'user');\n    _fnCallbackReg(oSettings, 'aoRowCallback', oInit.fnRowCallback, 'user');\n    _fnCallbackReg(oSettings, 'aoRowCreatedCallback', oInit.fnCreatedRow, 'user');\n    _fnCallbackReg(oSettings, 'aoHeaderCallback', oInit.fnHeaderCallback, 'user');\n    _fnCallbackReg(oSettings, 'aoFooterCallback', oInit.fnFooterCallback, 'user');\n    _fnCallbackReg(oSettings, 'aoInitComplete', oInit.fnInitComplete, 'user');\n    _fnCallbackReg(oSettings, 'aoPreDrawCallback', oInit.fnPreDrawCallback, 'user');\n    oSettings.rowIdFn = _fnGetObjectDataFn(oInit.rowId);\n\n    /* Browser support detection */\n    _fnBrowserDetect(oSettings);\n    var oClasses = oSettings.oClasses;\n    $.extend(oClasses, DataTable.ext.classes, oInit.oClasses);\n    $this.addClass(oClasses.sTable);\n    if (oSettings.iInitDisplayStart === undefined) {\n      /* Display start point, taking into account the save saving */\n      oSettings.iInitDisplayStart = oInit.iDisplayStart;\n      oSettings._iDisplayStart = oInit.iDisplayStart;\n    }\n    if (oInit.iDeferLoading !== null) {\n      oSettings.bDeferLoading = true;\n      var tmp = Array.isArray(oInit.iDeferLoading);\n      oSettings._iRecordsDisplay = tmp ? oInit.iDeferLoading[0] : oInit.iDeferLoading;\n      oSettings._iRecordsTotal = tmp ? oInit.iDeferLoading[1] : oInit.iDeferLoading;\n    }\n\n    /* Language definitions */\n    var oLanguage = oSettings.oLanguage;\n    $.extend(true, oLanguage, oInit.oLanguage);\n    if (oLanguage.sUrl) {\n      /* Get the language definitions from a file - because this Ajax call makes the language\n       * get async to the remainder of this function we use bInitHandedOff to indicate that\n       * _fnInitialise will be fired by the returned Ajax handler, rather than the constructor\n       */\n      $.ajax({\n        dataType: 'json',\n        url: oLanguage.sUrl,\n        success: function (json) {\n          _fnCamelToHungarian(defaults.oLanguage, json);\n          _fnLanguageCompat(json);\n          $.extend(true, oLanguage, json, oSettings.oInit.oLanguage);\n          _fnCallbackFire(oSettings, null, 'i18n', [oSettings]);\n          _fnInitialise(oSettings);\n        },\n        error: function () {\n          // Error occurred loading language file, continue on as best we can\n          _fnInitialise(oSettings);\n        }\n      });\n      bInitHandedOff = true;\n    } else {\n      _fnCallbackFire(oSettings, null, 'i18n', [oSettings]);\n    }\n\n    /*\n     * Stripes\n     */\n    if (oInit.asStripeClasses === null) {\n      oSettings.asStripeClasses = [oClasses.sStripeOdd, oClasses.sStripeEven];\n    }\n\n    /* Remove row stripe classes if they are already on the table row */\n    var stripeClasses = oSettings.asStripeClasses;\n    var rowOne = $this.children('tbody').find('tr').eq(0);\n    if ($.inArray(true, $.map(stripeClasses, function (el, i) {\n      return rowOne.hasClass(el);\n    })) !== -1) {\n      $('tbody tr', this).removeClass(stripeClasses.join(' '));\n      oSettings.asDestroyStripes = stripeClasses.slice();\n    }\n\n    /*\n     * Columns\n     * See if we should load columns automatically or use defined ones\n     */\n    var anThs = [];\n    var aoColumnsInit;\n    var nThead = this.getElementsByTagName('thead');\n    if (nThead.length !== 0) {\n      _fnDetectHeader(oSettings.aoHeader, nThead[0]);\n      anThs = _fnGetUniqueThs(oSettings);\n    }\n\n    /* If not given a column array, generate one with nulls */\n    if (oInit.aoColumns === null) {\n      aoColumnsInit = [];\n      for (i = 0, iLen = anThs.length; i < iLen; i++) {\n        aoColumnsInit.push(null);\n      }\n    } else {\n      aoColumnsInit = oInit.aoColumns;\n    }\n\n    /* Add the columns */\n    for (i = 0, iLen = aoColumnsInit.length; i < iLen; i++) {\n      _fnAddColumn(oSettings, anThs ? anThs[i] : null);\n    }\n\n    /* Apply the column definitions */\n    _fnApplyColumnDefs(oSettings, oInit.aoColumnDefs, aoColumnsInit, function (iCol, oDef) {\n      _fnColumnOptions(oSettings, iCol, oDef);\n    });\n\n    /* HTML5 attribute detection - build an mData object automatically if the\n     * attributes are found\n     */\n    if (rowOne.length) {\n      var a = function (cell, name) {\n        return cell.getAttribute('data-' + name) !== null ? name : null;\n      };\n      $(rowOne[0]).children('th, td').each(function (i, cell) {\n        var col = oSettings.aoColumns[i];\n        if (!col) {\n          _fnLog(oSettings, 0, 'Incorrect column count', 18);\n        }\n        if (col.mData === i) {\n          var sort = a(cell, 'sort') || a(cell, 'order');\n          var filter = a(cell, 'filter') || a(cell, 'search');\n          if (sort !== null || filter !== null) {\n            col.mData = {\n              _: i + '.display',\n              sort: sort !== null ? i + '.@data-' + sort : undefined,\n              type: sort !== null ? i + '.@data-' + sort : undefined,\n              filter: filter !== null ? i + '.@data-' + filter : undefined\n            };\n            col._isArrayHost = true;\n            _fnColumnOptions(oSettings, i);\n          }\n        }\n      });\n    }\n    var features = oSettings.oFeatures;\n    var loadedInit = function () {\n      /*\n       * Sorting\n       * @todo For modularisation (1.11) this needs to do into a sort start up handler\n       */\n\n      // If aaSorting is not defined, then we use the first indicator in asSorting\n      // in case that has been altered, so the default sort reflects that option\n      if (oInit.aaSorting === undefined) {\n        var sorting = oSettings.aaSorting;\n        for (i = 0, iLen = sorting.length; i < iLen; i++) {\n          sorting[i][1] = oSettings.aoColumns[i].asSorting[0];\n        }\n      }\n\n      /* Do a first pass on the sorting classes (allows any size changes to be taken into\n       * account, and also will apply sorting disabled classes if disabled\n       */\n      _fnSortingClasses(oSettings);\n      if (features.bSort) {\n        _fnCallbackReg(oSettings, 'aoDrawCallback', function () {\n          if (oSettings.bSorted) {\n            var aSort = _fnSortFlatten(oSettings);\n            var sortedColumns = {};\n            $.each(aSort, function (i, val) {\n              sortedColumns[val.src] = val.dir;\n            });\n            _fnCallbackFire(oSettings, null, 'order', [oSettings, aSort, sortedColumns]);\n            _fnSortAria(oSettings);\n          }\n        });\n      }\n      _fnCallbackReg(oSettings, 'aoDrawCallback', function () {\n        if (oSettings.bSorted || _fnDataSource(oSettings) === 'ssp' || features.bDeferRender) {\n          _fnSortingClasses(oSettings);\n        }\n      }, 'sc');\n\n      /*\n       * Final init\n       * Cache the header, body and footer as required, creating them if needed\n       */\n\n      // Work around for Webkit bug 83867 - store the caption-side before removing from doc\n      var captions = $this.children('caption').each(function () {\n        this._captionSide = $(this).css('caption-side');\n      });\n      var thead = $this.children('thead');\n      if (thead.length === 0) {\n        thead = $('<thead/>').appendTo($this);\n      }\n      oSettings.nTHead = thead[0];\n      var tbody = $this.children('tbody');\n      if (tbody.length === 0) {\n        tbody = $('<tbody/>').insertAfter(thead);\n      }\n      oSettings.nTBody = tbody[0];\n      var tfoot = $this.children('tfoot');\n      if (tfoot.length === 0 && captions.length > 0 && (oSettings.oScroll.sX !== \"\" || oSettings.oScroll.sY !== \"\")) {\n        // If we are a scrolling table, and no footer has been given, then we need to create\n        // a tfoot element for the caption element to be appended to\n        tfoot = $('<tfoot/>').appendTo($this);\n      }\n      if (tfoot.length === 0 || tfoot.children().length === 0) {\n        $this.addClass(oClasses.sNoFooter);\n      } else if (tfoot.length > 0) {\n        oSettings.nTFoot = tfoot[0];\n        _fnDetectHeader(oSettings.aoFooter, oSettings.nTFoot);\n      }\n\n      /* Check if there is data passing into the constructor */\n      if (oInit.aaData) {\n        for (i = 0; i < oInit.aaData.length; i++) {\n          _fnAddData(oSettings, oInit.aaData[i]);\n        }\n      } else if (oSettings.bDeferLoading || _fnDataSource(oSettings) == 'dom') {\n        /* Grab the data from the page - only do this when deferred loading or no Ajax\n         * source since there is no point in reading the DOM data if we are then going\n         * to replace it with Ajax data\n         */\n        _fnAddTr(oSettings, $(oSettings.nTBody).children('tr'));\n      }\n\n      /* Copy the data index array */\n      oSettings.aiDisplay = oSettings.aiDisplayMaster.slice();\n\n      /* Initialisation complete - table can be drawn */\n      oSettings.bInitialised = true;\n\n      /* Check if we need to initialise the table (it might not have been handed off to the\n       * language processor)\n       */\n      if (bInitHandedOff === false) {\n        _fnInitialise(oSettings);\n      }\n    };\n\n    /* Must be done after everything which can be overridden by the state saving! */\n    _fnCallbackReg(oSettings, 'aoDrawCallback', _fnSaveState, 'state_save');\n    if (oInit.bStateSave) {\n      features.bStateSave = true;\n      _fnLoadState(oSettings, oInit, loadedInit);\n    } else {\n      loadedInit();\n    }\n  });\n  _that = null;\n  return this;\n};\n\n/*\n * It is useful to have variables which are scoped locally so only the\n * DataTables functions can access them and they don't leak into global space.\n * At the same time these functions are often useful over multiple files in the\n * core and API, so we list, or at least document, all variables which are used\n * by DataTables as private variables here. This also ensures that there is no\n * clashing of variable names and that they can easily referenced for reuse.\n */\n\n// Defined else where\n//  _selector_run\n//  _selector_opts\n//  _selector_first\n//  _selector_row_indexes\n\nvar _ext; // DataTable.ext\nvar _Api; // DataTable.Api\nvar _api_register; // DataTable.Api.register\nvar _api_registerPlural; // DataTable.Api.registerPlural\n\nvar _re_dic = {};\nvar _re_new_lines = /[\\r\\n\\u2028]/g;\nvar _re_html = /<.*?>/g;\n\n// This is not strict ISO8601 - Date.parse() is quite lax, although\n// implementations differ between browsers.\nvar _re_date = /^\\d{2,4}[\\.\\/\\-]\\d{1,2}[\\.\\/\\-]\\d{1,2}([T ]{1}\\d{1,2}[:\\.]\\d{2}([\\.:]\\d{2})?)?$/;\n\n// Escape regular expression special characters\nvar _re_escape_regex = new RegExp('(\\\\' + ['/', '.', '*', '+', '?', '|', '(', ')', '[', ']', '{', '}', '\\\\', '$', '^', '-'].join('|\\\\') + ')', 'g');\n\n// https://en.wikipedia.org/wiki/Foreign_exchange_market\n// - \\u20BD - Russian ruble.\n// - \\u20a9 - South Korean Won\n// - \\u20BA - Turkish Lira\n// - \\u20B9 - Indian Rupee\n// - R - Brazil (R$) and South Africa\n// - fr - Swiss Franc\n// - kr - Swedish krona, Norwegian krone and Danish krone\n// - \\u2009 is thin space and \\u202F is narrow no-break space, both used in many\n// - Ƀ - Bitcoin\n// - Ξ - Ethereum\n//   standards as thousands separators.\nvar _re_formatted_numeric = /['\\u00A0,$£€¥%\\u2009\\u202F\\u20BD\\u20a9\\u20BArfkɃΞ]/gi;\nvar _empty = function (d) {\n  return !d || d === true || d === '-' ? true : false;\n};\nvar _intVal = function (s) {\n  var integer = parseInt(s, 10);\n  return !isNaN(integer) && isFinite(s) ? integer : null;\n};\n\n// Convert from a formatted number with characters other than `.` as the\n// decimal place, to a Javascript number\nvar _numToDecimal = function (num, decimalPoint) {\n  // Cache created regular expressions for speed as this function is called often\n  if (!_re_dic[decimalPoint]) {\n    _re_dic[decimalPoint] = new RegExp(_fnEscapeRegex(decimalPoint), 'g');\n  }\n  return typeof num === 'string' && decimalPoint !== '.' ? num.replace(/\\./g, '').replace(_re_dic[decimalPoint], '.') : num;\n};\nvar _isNumber = function (d, decimalPoint, formatted) {\n  var type = typeof d;\n  var strType = type === 'string';\n  if (type === 'number' || type === 'bigint') {\n    return true;\n  }\n\n  // If empty return immediately so there must be a number if it is a\n  // formatted string (this stops the string \"k\", or \"kr\", etc being detected\n  // as a formatted number for currency\n  if (_empty(d)) {\n    return true;\n  }\n  if (decimalPoint && strType) {\n    d = _numToDecimal(d, decimalPoint);\n  }\n  if (formatted && strType) {\n    d = d.replace(_re_formatted_numeric, '');\n  }\n  return !isNaN(parseFloat(d)) && isFinite(d);\n};\n\n// A string without HTML in it can be considered to be HTML still\nvar _isHtml = function (d) {\n  return _empty(d) || typeof d === 'string';\n};\nvar _htmlNumeric = function (d, decimalPoint, formatted) {\n  if (_empty(d)) {\n    return true;\n  }\n  var html = _isHtml(d);\n  return !html ? null : _isNumber(_stripHtml(d), decimalPoint, formatted) ? true : null;\n};\nvar _pluck = function (a, prop, prop2) {\n  var out = [];\n  var i = 0,\n    ien = a.length;\n\n  // Could have the test in the loop for slightly smaller code, but speed\n  // is essential here\n  if (prop2 !== undefined) {\n    for (; i < ien; i++) {\n      if (a[i] && a[i][prop]) {\n        out.push(a[i][prop][prop2]);\n      }\n    }\n  } else {\n    for (; i < ien; i++) {\n      if (a[i]) {\n        out.push(a[i][prop]);\n      }\n    }\n  }\n  return out;\n};\n\n// Basically the same as _pluck, but rather than looping over `a` we use `order`\n// as the indexes to pick from `a`\nvar _pluck_order = function (a, order, prop, prop2) {\n  var out = [];\n  var i = 0,\n    ien = order.length;\n\n  // Could have the test in the loop for slightly smaller code, but speed\n  // is essential here\n  if (prop2 !== undefined) {\n    for (; i < ien; i++) {\n      if (a[order[i]][prop]) {\n        out.push(a[order[i]][prop][prop2]);\n      }\n    }\n  } else {\n    for (; i < ien; i++) {\n      out.push(a[order[i]][prop]);\n    }\n  }\n  return out;\n};\nvar _range = function (len, start) {\n  var out = [];\n  var end;\n  if (start === undefined) {\n    start = 0;\n    end = len;\n  } else {\n    end = start;\n    start = len;\n  }\n  for (var i = start; i < end; i++) {\n    out.push(i);\n  }\n  return out;\n};\nvar _removeEmpty = function (a) {\n  var out = [];\n  for (var i = 0, ien = a.length; i < ien; i++) {\n    if (a[i]) {\n      // careful - will remove all falsy values!\n      out.push(a[i]);\n    }\n  }\n  return out;\n};\nvar _stripHtml = function (d) {\n  return d.replace(_re_html, '') // Complete tags\n  .replace(/<script/i, ''); // Safety for incomplete script tag\n};\n\n/**\n * Determine if all values in the array are unique. This means we can short\n * cut the _unique method at the cost of a single loop. A sorted array is used\n * to easily check the values.\n *\n * @param  {array} src Source array\n * @return {boolean} true if all unique, false otherwise\n * @ignore\n */\nvar _areAllUnique = function (src) {\n  if (src.length < 2) {\n    return true;\n  }\n  var sorted = src.slice().sort();\n  var last = sorted[0];\n  for (var i = 1, ien = sorted.length; i < ien; i++) {\n    if (sorted[i] === last) {\n      return false;\n    }\n    last = sorted[i];\n  }\n  return true;\n};\n\n/**\n * Find the unique elements in a source array.\n *\n * @param  {array} src Source array\n * @return {array} Array of unique items\n * @ignore\n */\nvar _unique = function (src) {\n  if (_areAllUnique(src)) {\n    return src.slice();\n  }\n\n  // A faster unique method is to use object keys to identify used values,\n  // but this doesn't work with arrays or objects, which we must also\n  // consider. See jsperf.com/compare-array-unique-versions/4 for more\n  // information.\n  var out = [],\n    val,\n    i,\n    ien = src.length,\n    j,\n    k = 0;\n  again: for (i = 0; i < ien; i++) {\n    val = src[i];\n    for (j = 0; j < k; j++) {\n      if (out[j] === val) {\n        continue again;\n      }\n    }\n    out.push(val);\n    k++;\n  }\n  return out;\n};\n\n// Surprisingly this is faster than [].concat.apply\n// https://jsperf.com/flatten-an-array-loop-vs-reduce/2\nvar _flatten = function (out, val) {\n  if (Array.isArray(val)) {\n    for (var i = 0; i < val.length; i++) {\n      _flatten(out, val[i]);\n    }\n  } else {\n    out.push(val);\n  }\n  return out;\n};\nvar _includes = function (search, start) {\n  if (start === undefined) {\n    start = 0;\n  }\n  return this.indexOf(search, start) !== -1;\n};\n\n// Array.isArray polyfill.\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/isArray\nif (!Array.isArray) {\n  Array.isArray = function (arg) {\n    return Object.prototype.toString.call(arg) === '[object Array]';\n  };\n}\nif (!Array.prototype.includes) {\n  Array.prototype.includes = _includes;\n}\n\n// .trim() polyfill\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String/trim\nif (!String.prototype.trim) {\n  String.prototype.trim = function () {\n    return this.replace(/^[\\s\\uFEFF\\xA0]+|[\\s\\uFEFF\\xA0]+$/g, '');\n  };\n}\nif (!String.prototype.includes) {\n  String.prototype.includes = _includes;\n}\n\n/**\n * DataTables utility methods\n * \n * This namespace provides helper methods that DataTables uses internally to\n * create a DataTable, but which are not exclusively used only for DataTables.\n * These methods can be used by extension authors to save the duplication of\n * code.\n *\n *  @namespace\n */\nDataTable.util = {\n  /**\n   * Throttle the calls to a function. Arguments and context are maintained\n   * for the throttled function.\n   *\n   * @param {function} fn Function to be called\n   * @param {integer} freq Call frequency in mS\n   * @return {function} Wrapped function\n   */\n  throttle: function (fn, freq) {\n    var frequency = freq !== undefined ? freq : 200,\n      last,\n      timer;\n    return function () {\n      var that = this,\n        now = +new Date(),\n        args = arguments;\n      if (last && now < last + frequency) {\n        clearTimeout(timer);\n        timer = setTimeout(function () {\n          last = undefined;\n          fn.apply(that, args);\n        }, frequency);\n      } else {\n        last = now;\n        fn.apply(that, args);\n      }\n    };\n  },\n  /**\n   * Escape a string such that it can be used in a regular expression\n   *\n   *  @param {string} val string to escape\n   *  @returns {string} escaped string\n   */\n  escapeRegex: function (val) {\n    return val.replace(_re_escape_regex, '\\\\$1');\n  },\n  /**\n   * Create a function that will write to a nested object or array\n   * @param {*} source JSON notation string\n   * @returns Write function\n   */\n  set: function (source) {\n    if ($.isPlainObject(source)) {\n      /* Unlike get, only the underscore (global) option is used for for\n       * setting data since we don't know the type here. This is why an object\n       * option is not documented for `mData` (which is read/write), but it is\n       * for `mRender` which is read only.\n       */\n      return DataTable.util.set(source._);\n    } else if (source === null) {\n      // Nothing to do when the data source is null\n      return function () {};\n    } else if (typeof source === 'function') {\n      return function (data, val, meta) {\n        source(data, 'set', val, meta);\n      };\n    } else if (typeof source === 'string' && (source.indexOf('.') !== -1 || source.indexOf('[') !== -1 || source.indexOf('(') !== -1)) {\n      // Like the get, we need to get data from a nested object\n      var setData = function (data, val, src) {\n        var a = _fnSplitObjNotation(src),\n          b;\n        var aLast = a[a.length - 1];\n        var arrayNotation, funcNotation, o, innerSrc;\n        for (var i = 0, iLen = a.length - 1; i < iLen; i++) {\n          // Protect against prototype pollution\n          if (a[i] === '__proto__' || a[i] === 'constructor') {\n            throw new Error('Cannot set prototype values');\n          }\n\n          // Check if we are dealing with an array notation request\n          arrayNotation = a[i].match(__reArray);\n          funcNotation = a[i].match(__reFn);\n          if (arrayNotation) {\n            a[i] = a[i].replace(__reArray, '');\n            data[a[i]] = [];\n\n            // Get the remainder of the nested object to set so we can recurse\n            b = a.slice();\n            b.splice(0, i + 1);\n            innerSrc = b.join('.');\n\n            // Traverse each entry in the array setting the properties requested\n            if (Array.isArray(val)) {\n              for (var j = 0, jLen = val.length; j < jLen; j++) {\n                o = {};\n                setData(o, val[j], innerSrc);\n                data[a[i]].push(o);\n              }\n            } else {\n              // We've been asked to save data to an array, but it\n              // isn't array data to be saved. Best that can be done\n              // is to just save the value.\n              data[a[i]] = val;\n            }\n\n            // The inner call to setData has already traversed through the remainder\n            // of the source and has set the data, thus we can exit here\n            return;\n          } else if (funcNotation) {\n            // Function call\n            a[i] = a[i].replace(__reFn, '');\n            data = data[a[i]](val);\n          }\n\n          // If the nested object doesn't currently exist - since we are\n          // trying to set the value - create it\n          if (data[a[i]] === null || data[a[i]] === undefined) {\n            data[a[i]] = {};\n          }\n          data = data[a[i]];\n        }\n\n        // Last item in the input - i.e, the actual set\n        if (aLast.match(__reFn)) {\n          // Function call\n          data = data[aLast.replace(__reFn, '')](val);\n        } else {\n          // If array notation is used, we just want to strip it and use the property name\n          // and assign the value. If it isn't used, then we get the result we want anyway\n          data[aLast.replace(__reArray, '')] = val;\n        }\n      };\n      return function (data, val) {\n        // meta is also passed in, but not used\n        return setData(data, val, source);\n      };\n    } else {\n      // Array or flat object mapping\n      return function (data, val) {\n        // meta is also passed in, but not used\n        data[source] = val;\n      };\n    }\n  },\n  /**\n   * Create a function that will read nested objects from arrays, based on JSON notation\n   * @param {*} source JSON notation string\n   * @returns Value read\n   */\n  get: function (source) {\n    if ($.isPlainObject(source)) {\n      // Build an object of get functions, and wrap them in a single call\n      var o = {};\n      $.each(source, function (key, val) {\n        if (val) {\n          o[key] = DataTable.util.get(val);\n        }\n      });\n      return function (data, type, row, meta) {\n        var t = o[type] || o._;\n        return t !== undefined ? t(data, type, row, meta) : data;\n      };\n    } else if (source === null) {\n      // Give an empty string for rendering / sorting etc\n      return function (data) {\n        // type, row and meta also passed, but not used\n        return data;\n      };\n    } else if (typeof source === 'function') {\n      return function (data, type, row, meta) {\n        return source(data, type, row, meta);\n      };\n    } else if (typeof source === 'string' && (source.indexOf('.') !== -1 || source.indexOf('[') !== -1 || source.indexOf('(') !== -1)) {\n      /* If there is a . in the source string then the data source is in a\n       * nested object so we loop over the data for each level to get the next\n       * level down. On each loop we test for undefined, and if found immediately\n       * return. This allows entire objects to be missing and sDefaultContent to\n       * be used if defined, rather than throwing an error\n       */\n      var fetchData = function (data, type, src) {\n        var arrayNotation, funcNotation, out, innerSrc;\n        if (src !== \"\") {\n          var a = _fnSplitObjNotation(src);\n          for (var i = 0, iLen = a.length; i < iLen; i++) {\n            // Check if we are dealing with special notation\n            arrayNotation = a[i].match(__reArray);\n            funcNotation = a[i].match(__reFn);\n            if (arrayNotation) {\n              // Array notation\n              a[i] = a[i].replace(__reArray, '');\n\n              // Condition allows simply [] to be passed in\n              if (a[i] !== \"\") {\n                data = data[a[i]];\n              }\n              out = [];\n\n              // Get the remainder of the nested object to get\n              a.splice(0, i + 1);\n              innerSrc = a.join('.');\n\n              // Traverse each entry in the array getting the properties requested\n              if (Array.isArray(data)) {\n                for (var j = 0, jLen = data.length; j < jLen; j++) {\n                  out.push(fetchData(data[j], type, innerSrc));\n                }\n              }\n\n              // If a string is given in between the array notation indicators, that\n              // is used to join the strings together, otherwise an array is returned\n              var join = arrayNotation[0].substring(1, arrayNotation[0].length - 1);\n              data = join === \"\" ? out : out.join(join);\n\n              // The inner call to fetchData has already traversed through the remainder\n              // of the source requested, so we exit from the loop\n              break;\n            } else if (funcNotation) {\n              // Function call\n              a[i] = a[i].replace(__reFn, '');\n              data = data[a[i]]();\n              continue;\n            }\n            if (data === null || data[a[i]] === null) {\n              return null;\n            } else if (data === undefined || data[a[i]] === undefined) {\n              return undefined;\n            }\n            data = data[a[i]];\n          }\n        }\n        return data;\n      };\n      return function (data, type) {\n        // row and meta also passed, but not used\n        return fetchData(data, type, source);\n      };\n    } else {\n      // Array or flat object mapping\n      return function (data, type) {\n        // row and meta also passed, but not used\n        return data[source];\n      };\n    }\n  }\n};\n\n/**\n * Create a mapping object that allows camel case parameters to be looked up\n * for their Hungarian counterparts. The mapping is stored in a private\n * parameter called `_hungarianMap` which can be accessed on the source object.\n *  @param {object} o\n *  @memberof DataTable#oApi\n */\nfunction _fnHungarianMap(o) {\n  var hungarian = 'a aa ai ao as b fn i m o s ',\n    match,\n    newKey,\n    map = {};\n  $.each(o, function (key, val) {\n    match = key.match(/^([^A-Z]+?)([A-Z])/);\n    if (match && hungarian.indexOf(match[1] + ' ') !== -1) {\n      newKey = key.replace(match[0], match[2].toLowerCase());\n      map[newKey] = key;\n      if (match[1] === 'o') {\n        _fnHungarianMap(o[key]);\n      }\n    }\n  });\n  o._hungarianMap = map;\n}\n\n/**\n * Convert from camel case parameters to Hungarian, based on a Hungarian map\n * created by _fnHungarianMap.\n *  @param {object} src The model object which holds all parameters that can be\n *    mapped.\n *  @param {object} user The object to convert from camel case to Hungarian.\n *  @param {boolean} force When set to `true`, properties which already have a\n *    Hungarian value in the `user` object will be overwritten. Otherwise they\n *    won't be.\n *  @memberof DataTable#oApi\n */\nfunction _fnCamelToHungarian(src, user, force) {\n  if (!src._hungarianMap) {\n    _fnHungarianMap(src);\n  }\n  var hungarianKey;\n  $.each(user, function (key, val) {\n    hungarianKey = src._hungarianMap[key];\n    if (hungarianKey !== undefined && (force || user[hungarianKey] === undefined)) {\n      // For objects, we need to buzz down into the object to copy parameters\n      if (hungarianKey.charAt(0) === 'o') {\n        // Copy the camelCase options over to the hungarian\n        if (!user[hungarianKey]) {\n          user[hungarianKey] = {};\n        }\n        $.extend(true, user[hungarianKey], user[key]);\n        _fnCamelToHungarian(src[hungarianKey], user[hungarianKey], force);\n      } else {\n        user[hungarianKey] = user[key];\n      }\n    }\n  });\n}\n\n/**\n * Language compatibility - when certain options are given, and others aren't, we\n * need to duplicate the values over, in order to provide backwards compatibility\n * with older language files.\n *  @param {object} oSettings dataTables settings object\n *  @memberof DataTable#oApi\n */\nfunction _fnLanguageCompat(lang) {\n  // Note the use of the Hungarian notation for the parameters in this method as\n  // this is called after the mapping of camelCase to Hungarian\n  var defaults = DataTable.defaults.oLanguage;\n\n  // Default mapping\n  var defaultDecimal = defaults.sDecimal;\n  if (defaultDecimal) {\n    _addNumericSort(defaultDecimal);\n  }\n  if (lang) {\n    var zeroRecords = lang.sZeroRecords;\n\n    // Backwards compatibility - if there is no sEmptyTable given, then use the same as\n    // sZeroRecords - assuming that is given.\n    if (!lang.sEmptyTable && zeroRecords && defaults.sEmptyTable === \"No data available in table\") {\n      _fnMap(lang, lang, 'sZeroRecords', 'sEmptyTable');\n    }\n\n    // Likewise with loading records\n    if (!lang.sLoadingRecords && zeroRecords && defaults.sLoadingRecords === \"Loading...\") {\n      _fnMap(lang, lang, 'sZeroRecords', 'sLoadingRecords');\n    }\n\n    // Old parameter name of the thousands separator mapped onto the new\n    if (lang.sInfoThousands) {\n      lang.sThousands = lang.sInfoThousands;\n    }\n    var decimal = lang.sDecimal;\n    if (decimal && defaultDecimal !== decimal) {\n      _addNumericSort(decimal);\n    }\n  }\n}\n\n/**\n * Map one parameter onto another\n *  @param {object} o Object to map\n *  @param {*} knew The new parameter name\n *  @param {*} old The old parameter name\n */\nvar _fnCompatMap = function (o, knew, old) {\n  if (o[knew] !== undefined) {\n    o[old] = o[knew];\n  }\n};\n\n/**\n * Provide backwards compatibility for the main DT options. Note that the new\n * options are mapped onto the old parameters, so this is an external interface\n * change only.\n *  @param {object} init Object to map\n */\nfunction _fnCompatOpts(init) {\n  _fnCompatMap(init, 'ordering', 'bSort');\n  _fnCompatMap(init, 'orderMulti', 'bSortMulti');\n  _fnCompatMap(init, 'orderClasses', 'bSortClasses');\n  _fnCompatMap(init, 'orderCellsTop', 'bSortCellsTop');\n  _fnCompatMap(init, 'order', 'aaSorting');\n  _fnCompatMap(init, 'orderFixed', 'aaSortingFixed');\n  _fnCompatMap(init, 'paging', 'bPaginate');\n  _fnCompatMap(init, 'pagingType', 'sPaginationType');\n  _fnCompatMap(init, 'pageLength', 'iDisplayLength');\n  _fnCompatMap(init, 'searching', 'bFilter');\n\n  // Boolean initialisation of x-scrolling\n  if (typeof init.sScrollX === 'boolean') {\n    init.sScrollX = init.sScrollX ? '100%' : '';\n  }\n  if (typeof init.scrollX === 'boolean') {\n    init.scrollX = init.scrollX ? '100%' : '';\n  }\n\n  // Column search objects are in an array, so it needs to be converted\n  // element by element\n  var searchCols = init.aoSearchCols;\n  if (searchCols) {\n    for (var i = 0, ien = searchCols.length; i < ien; i++) {\n      if (searchCols[i]) {\n        _fnCamelToHungarian(DataTable.models.oSearch, searchCols[i]);\n      }\n    }\n  }\n}\n\n/**\n * Provide backwards compatibility for column options. Note that the new options\n * are mapped onto the old parameters, so this is an external interface change\n * only.\n *  @param {object} init Object to map\n */\nfunction _fnCompatCols(init) {\n  _fnCompatMap(init, 'orderable', 'bSortable');\n  _fnCompatMap(init, 'orderData', 'aDataSort');\n  _fnCompatMap(init, 'orderSequence', 'asSorting');\n  _fnCompatMap(init, 'orderDataType', 'sortDataType');\n\n  // orderData can be given as an integer\n  var dataSort = init.aDataSort;\n  if (typeof dataSort === 'number' && !Array.isArray(dataSort)) {\n    init.aDataSort = [dataSort];\n  }\n}\n\n/**\n * Browser feature detection for capabilities, quirks\n *  @param {object} settings dataTables settings object\n *  @memberof DataTable#oApi\n */\nfunction _fnBrowserDetect(settings) {\n  // We don't need to do this every time DataTables is constructed, the values\n  // calculated are specific to the browser and OS configuration which we\n  // don't expect to change between initialisations\n  if (!DataTable.__browser) {\n    var browser = {};\n    DataTable.__browser = browser;\n\n    // Scrolling feature / quirks detection\n    var n = $('<div/>').css({\n      position: 'fixed',\n      top: 0,\n      left: $(window).scrollLeft() * -1,\n      // allow for scrolling\n      height: 1,\n      width: 1,\n      overflow: 'hidden'\n    }).append($('<div/>').css({\n      position: 'absolute',\n      top: 1,\n      left: 1,\n      width: 100,\n      overflow: 'scroll'\n    }).append($('<div/>').css({\n      width: '100%',\n      height: 10\n    }))).appendTo('body');\n    var outer = n.children();\n    var inner = outer.children();\n\n    // Numbers below, in order, are:\n    // inner.offsetWidth, inner.clientWidth, outer.offsetWidth, outer.clientWidth\n    //\n    // IE6 XP:                           100 100 100  83\n    // IE7 Vista:                        100 100 100  83\n    // IE 8+ Windows:                     83  83 100  83\n    // Evergreen Windows:                 83  83 100  83\n    // Evergreen Mac with scrollbars:     85  85 100  85\n    // Evergreen Mac without scrollbars: 100 100 100 100\n\n    // Get scrollbar width\n    browser.barWidth = outer[0].offsetWidth - outer[0].clientWidth;\n\n    // IE6/7 will oversize a width 100% element inside a scrolling element, to\n    // include the width of the scrollbar, while other browsers ensure the inner\n    // element is contained without forcing scrolling\n    browser.bScrollOversize = inner[0].offsetWidth === 100 && outer[0].clientWidth !== 100;\n\n    // In rtl text layout, some browsers (most, but not all) will place the\n    // scrollbar on the left, rather than the right.\n    browser.bScrollbarLeft = Math.round(inner.offset().left) !== 1;\n\n    // IE8- don't provide height and width for getBoundingClientRect\n    browser.bBounding = n[0].getBoundingClientRect().width ? true : false;\n    n.remove();\n  }\n  $.extend(settings.oBrowser, DataTable.__browser);\n  settings.oScroll.iBarWidth = DataTable.__browser.barWidth;\n}\n\n/**\n * Array.prototype reduce[Right] method, used for browsers which don't support\n * JS 1.6. Done this way to reduce code size, since we iterate either way\n *  @param {object} settings dataTables settings object\n *  @memberof DataTable#oApi\n */\nfunction _fnReduce(that, fn, init, start, end, inc) {\n  var i = start,\n    value,\n    isSet = false;\n  if (init !== undefined) {\n    value = init;\n    isSet = true;\n  }\n  while (i !== end) {\n    if (!that.hasOwnProperty(i)) {\n      continue;\n    }\n    value = isSet ? fn(value, that[i], i, that) : that[i];\n    isSet = true;\n    i += inc;\n  }\n  return value;\n}\n\n/**\n * Add a column to the list used for the table with default values\n *  @param {object} oSettings dataTables settings object\n *  @param {node} nTh The th element for this column\n *  @memberof DataTable#oApi\n */\nfunction _fnAddColumn(oSettings, nTh) {\n  // Add column to aoColumns array\n  var oDefaults = DataTable.defaults.column;\n  var iCol = oSettings.aoColumns.length;\n  var oCol = $.extend({}, DataTable.models.oColumn, oDefaults, {\n    \"nTh\": nTh ? nTh : document.createElement('th'),\n    \"sTitle\": oDefaults.sTitle ? oDefaults.sTitle : nTh ? nTh.innerHTML : '',\n    \"aDataSort\": oDefaults.aDataSort ? oDefaults.aDataSort : [iCol],\n    \"mData\": oDefaults.mData ? oDefaults.mData : iCol,\n    idx: iCol\n  });\n  oSettings.aoColumns.push(oCol);\n\n  // Add search object for column specific search. Note that the `searchCols[ iCol ]`\n  // passed into extend can be undefined. This allows the user to give a default\n  // with only some of the parameters defined, and also not give a default\n  var searchCols = oSettings.aoPreSearchCols;\n  searchCols[iCol] = $.extend({}, DataTable.models.oSearch, searchCols[iCol]);\n\n  // Use the default column options function to initialise classes etc\n  _fnColumnOptions(oSettings, iCol, $(nTh).data());\n}\n\n/**\n * Apply options for a column\n *  @param {object} oSettings dataTables settings object\n *  @param {int} iCol column index to consider\n *  @param {object} oOptions object with sType, bVisible and bSearchable etc\n *  @memberof DataTable#oApi\n */\nfunction _fnColumnOptions(oSettings, iCol, oOptions) {\n  var oCol = oSettings.aoColumns[iCol];\n  var oClasses = oSettings.oClasses;\n  var th = $(oCol.nTh);\n\n  // Try to get width information from the DOM. We can't get it from CSS\n  // as we'd need to parse the CSS stylesheet. `width` option can override\n  if (!oCol.sWidthOrig) {\n    // Width attribute\n    oCol.sWidthOrig = th.attr('width') || null;\n\n    // Style attribute\n    var t = (th.attr('style') || '').match(/width:\\s*(\\d+[pxem%]+)/);\n    if (t) {\n      oCol.sWidthOrig = t[1];\n    }\n  }\n\n  /* User specified column options */\n  if (oOptions !== undefined && oOptions !== null) {\n    // Backwards compatibility\n    _fnCompatCols(oOptions);\n\n    // Map camel case parameters to their Hungarian counterparts\n    _fnCamelToHungarian(DataTable.defaults.column, oOptions, true);\n\n    /* Backwards compatibility for mDataProp */\n    if (oOptions.mDataProp !== undefined && !oOptions.mData) {\n      oOptions.mData = oOptions.mDataProp;\n    }\n    if (oOptions.sType) {\n      oCol._sManualType = oOptions.sType;\n    }\n\n    // `class` is a reserved word in Javascript, so we need to provide\n    // the ability to use a valid name for the camel case input\n    if (oOptions.className && !oOptions.sClass) {\n      oOptions.sClass = oOptions.className;\n    }\n    if (oOptions.sClass) {\n      th.addClass(oOptions.sClass);\n    }\n    var origClass = oCol.sClass;\n    $.extend(oCol, oOptions);\n    _fnMap(oCol, oOptions, \"sWidth\", \"sWidthOrig\");\n\n    // Merge class from previously defined classes with this one, rather than just\n    // overwriting it in the extend above\n    if (origClass !== oCol.sClass) {\n      oCol.sClass = origClass + ' ' + oCol.sClass;\n    }\n\n    /* iDataSort to be applied (backwards compatibility), but aDataSort will take\n     * priority if defined\n     */\n    if (oOptions.iDataSort !== undefined) {\n      oCol.aDataSort = [oOptions.iDataSort];\n    }\n    _fnMap(oCol, oOptions, \"aDataSort\");\n\n    // Fall back to the aria-label attribute on the table header if no ariaTitle is\n    // provided.\n    if (!oCol.ariaTitle) {\n      oCol.ariaTitle = th.attr(\"aria-label\");\n    }\n  }\n\n  /* Cache the data get and set functions for speed */\n  var mDataSrc = oCol.mData;\n  var mData = _fnGetObjectDataFn(mDataSrc);\n  var mRender = oCol.mRender ? _fnGetObjectDataFn(oCol.mRender) : null;\n  var attrTest = function (src) {\n    return typeof src === 'string' && src.indexOf('@') !== -1;\n  };\n  oCol._bAttrSrc = $.isPlainObject(mDataSrc) && (attrTest(mDataSrc.sort) || attrTest(mDataSrc.type) || attrTest(mDataSrc.filter));\n  oCol._setter = null;\n  oCol.fnGetData = function (rowData, type, meta) {\n    var innerData = mData(rowData, type, undefined, meta);\n    return mRender && type ? mRender(innerData, type, rowData, meta) : innerData;\n  };\n  oCol.fnSetData = function (rowData, val, meta) {\n    return _fnSetObjectDataFn(mDataSrc)(rowData, val, meta);\n  };\n\n  // Indicate if DataTables should read DOM data as an object or array\n  // Used in _fnGetRowElements\n  if (typeof mDataSrc !== 'number' && !oCol._isArrayHost) {\n    oSettings._rowReadObject = true;\n  }\n\n  /* Feature sorting overrides column specific when off */\n  if (!oSettings.oFeatures.bSort) {\n    oCol.bSortable = false;\n    th.addClass(oClasses.sSortableNone); // Have to add class here as order event isn't called\n  }\n\n  /* Check that the class assignment is correct for sorting */\n  var bAsc = $.inArray('asc', oCol.asSorting) !== -1;\n  var bDesc = $.inArray('desc', oCol.asSorting) !== -1;\n  if (!oCol.bSortable || !bAsc && !bDesc) {\n    oCol.sSortingClass = oClasses.sSortableNone;\n    oCol.sSortingClassJUI = \"\";\n  } else if (bAsc && !bDesc) {\n    oCol.sSortingClass = oClasses.sSortableAsc;\n    oCol.sSortingClassJUI = oClasses.sSortJUIAscAllowed;\n  } else if (!bAsc && bDesc) {\n    oCol.sSortingClass = oClasses.sSortableDesc;\n    oCol.sSortingClassJUI = oClasses.sSortJUIDescAllowed;\n  } else {\n    oCol.sSortingClass = oClasses.sSortable;\n    oCol.sSortingClassJUI = oClasses.sSortJUI;\n  }\n}\n\n/**\n * Adjust the table column widths for new data. Note: you would probably want to\n * do a redraw after calling this function!\n *  @param {object} settings dataTables settings object\n *  @memberof DataTable#oApi\n */\nfunction _fnAdjustColumnSizing(settings) {\n  /* Not interested in doing column width calculation if auto-width is disabled */\n  if (settings.oFeatures.bAutoWidth !== false) {\n    var columns = settings.aoColumns;\n    _fnCalculateColumnWidths(settings);\n    for (var i = 0, iLen = columns.length; i < iLen; i++) {\n      columns[i].nTh.style.width = columns[i].sWidth;\n    }\n  }\n  var scroll = settings.oScroll;\n  if (scroll.sY !== '' || scroll.sX !== '') {\n    _fnScrollDraw(settings);\n  }\n  _fnCallbackFire(settings, null, 'column-sizing', [settings]);\n}\n\n/**\n * Convert the index of a visible column to the index in the data array (take account\n * of hidden columns)\n *  @param {object} oSettings dataTables settings object\n *  @param {int} iMatch Visible column index to lookup\n *  @returns {int} i the data index\n *  @memberof DataTable#oApi\n */\nfunction _fnVisibleToColumnIndex(oSettings, iMatch) {\n  var aiVis = _fnGetColumns(oSettings, 'bVisible');\n  return typeof aiVis[iMatch] === 'number' ? aiVis[iMatch] : null;\n}\n\n/**\n * Convert the index of an index in the data array and convert it to the visible\n *   column index (take account of hidden columns)\n *  @param {int} iMatch Column index to lookup\n *  @param {object} oSettings dataTables settings object\n *  @returns {int} i the data index\n *  @memberof DataTable#oApi\n */\nfunction _fnColumnIndexToVisible(oSettings, iMatch) {\n  var aiVis = _fnGetColumns(oSettings, 'bVisible');\n  var iPos = $.inArray(iMatch, aiVis);\n  return iPos !== -1 ? iPos : null;\n}\n\n/**\n * Get the number of visible columns\n *  @param {object} oSettings dataTables settings object\n *  @returns {int} i the number of visible columns\n *  @memberof DataTable#oApi\n */\nfunction _fnVisbleColumns(oSettings) {\n  var vis = 0;\n\n  // No reduce in IE8, use a loop for now\n  $.each(oSettings.aoColumns, function (i, col) {\n    if (col.bVisible && $(col.nTh).css('display') !== 'none') {\n      vis++;\n    }\n  });\n  return vis;\n}\n\n/**\n * Get an array of column indexes that match a given property\n *  @param {object} oSettings dataTables settings object\n *  @param {string} sParam Parameter in aoColumns to look for - typically\n *    bVisible or bSearchable\n *  @returns {array} Array of indexes with matched properties\n *  @memberof DataTable#oApi\n */\nfunction _fnGetColumns(oSettings, sParam) {\n  var a = [];\n  $.map(oSettings.aoColumns, function (val, i) {\n    if (val[sParam]) {\n      a.push(i);\n    }\n  });\n  return a;\n}\n\n/**\n * Calculate the 'type' of a column\n *  @param {object} settings dataTables settings object\n *  @memberof DataTable#oApi\n */\nfunction _fnColumnTypes(settings) {\n  var columns = settings.aoColumns;\n  var data = settings.aoData;\n  var types = DataTable.ext.type.detect;\n  var i, ien, j, jen, k, ken;\n  var col, cell, detectedType, cache;\n\n  // For each column, spin over the \n  for (i = 0, ien = columns.length; i < ien; i++) {\n    col = columns[i];\n    cache = [];\n    if (!col.sType && col._sManualType) {\n      col.sType = col._sManualType;\n    } else if (!col.sType) {\n      for (j = 0, jen = types.length; j < jen; j++) {\n        for (k = 0, ken = data.length; k < ken; k++) {\n          // Use a cache array so we only need to get the type data\n          // from the formatter once (when using multiple detectors)\n          if (cache[k] === undefined) {\n            cache[k] = _fnGetCellData(settings, k, i, 'type');\n          }\n          detectedType = types[j](cache[k], settings);\n\n          // If null, then this type can't apply to this column, so\n          // rather than testing all cells, break out. There is an\n          // exception for the last type which is `html`. We need to\n          // scan all rows since it is possible to mix string and HTML\n          // types\n          if (!detectedType && j !== types.length - 1) {\n            break;\n          }\n\n          // Only a single match is needed for html type since it is\n          // bottom of the pile and very similar to string - but it\n          // must not be empty\n          if (detectedType === 'html' && !_empty(cache[k])) {\n            break;\n          }\n        }\n\n        // Type is valid for all data points in the column - use this\n        // type\n        if (detectedType) {\n          col.sType = detectedType;\n          break;\n        }\n      }\n\n      // Fall back - if no type was detected, always use string\n      if (!col.sType) {\n        col.sType = 'string';\n      }\n    }\n  }\n}\n\n/**\n * Take the column definitions and static columns arrays and calculate how\n * they relate to column indexes. The callback function will then apply the\n * definition found for a column to a suitable configuration object.\n *  @param {object} oSettings dataTables settings object\n *  @param {array} aoColDefs The aoColumnDefs array that is to be applied\n *  @param {array} aoCols The aoColumns array that defines columns individually\n *  @param {function} fn Callback function - takes two parameters, the calculated\n *    column index and the definition for that column.\n *  @memberof DataTable#oApi\n */\nfunction _fnApplyColumnDefs(oSettings, aoColDefs, aoCols, fn) {\n  var i, iLen, j, jLen, k, kLen, def;\n  var columns = oSettings.aoColumns;\n\n  // Column definitions with aTargets\n  if (aoColDefs) {\n    /* Loop over the definitions array - loop in reverse so first instance has priority */\n    for (i = aoColDefs.length - 1; i >= 0; i--) {\n      def = aoColDefs[i];\n\n      /* Each definition can target multiple columns, as it is an array */\n      var aTargets = def.target !== undefined ? def.target : def.targets !== undefined ? def.targets : def.aTargets;\n      if (!Array.isArray(aTargets)) {\n        aTargets = [aTargets];\n      }\n      for (j = 0, jLen = aTargets.length; j < jLen; j++) {\n        if (typeof aTargets[j] === 'number' && aTargets[j] >= 0) {\n          /* Add columns that we don't yet know about */\n          while (columns.length <= aTargets[j]) {\n            _fnAddColumn(oSettings);\n          }\n\n          /* Integer, basic index */\n          fn(aTargets[j], def);\n        } else if (typeof aTargets[j] === 'number' && aTargets[j] < 0) {\n          /* Negative integer, right to left column counting */\n          fn(columns.length + aTargets[j], def);\n        } else if (typeof aTargets[j] === 'string') {\n          /* Class name matching on TH element */\n          for (k = 0, kLen = columns.length; k < kLen; k++) {\n            if (aTargets[j] == \"_all\" || $(columns[k].nTh).hasClass(aTargets[j])) {\n              fn(k, def);\n            }\n          }\n        }\n      }\n    }\n  }\n\n  // Statically defined columns array\n  if (aoCols) {\n    for (i = 0, iLen = aoCols.length; i < iLen; i++) {\n      fn(i, aoCols[i]);\n    }\n  }\n}\n\n/**\n * Add a data array to the table, creating DOM node etc. This is the parallel to\n * _fnGatherData, but for adding rows from a Javascript source, rather than a\n * DOM source.\n *  @param {object} oSettings dataTables settings object\n *  @param {array} aData data array to be added\n *  @param {node} [nTr] TR element to add to the table - optional. If not given,\n *    DataTables will create a row automatically\n *  @param {array} [anTds] Array of TD|TH elements for the row - must be given\n *    if nTr is.\n *  @returns {int} >=0 if successful (index of new aoData entry), -1 if failed\n *  @memberof DataTable#oApi\n */\nfunction _fnAddData(oSettings, aDataIn, nTr, anTds) {\n  /* Create the object for storing information about this new row */\n  var iRow = oSettings.aoData.length;\n  var oData = $.extend(true, {}, DataTable.models.oRow, {\n    src: nTr ? 'dom' : 'data',\n    idx: iRow\n  });\n  oData._aData = aDataIn;\n  oSettings.aoData.push(oData);\n\n  /* Create the cells */\n  var nTd, sThisType;\n  var columns = oSettings.aoColumns;\n\n  // Invalidate the column types as the new data needs to be revalidated\n  for (var i = 0, iLen = columns.length; i < iLen; i++) {\n    columns[i].sType = null;\n  }\n\n  /* Add to the display array */\n  oSettings.aiDisplayMaster.push(iRow);\n  var id = oSettings.rowIdFn(aDataIn);\n  if (id !== undefined) {\n    oSettings.aIds[id] = oData;\n  }\n\n  /* Create the DOM information, or register it if already present */\n  if (nTr || !oSettings.oFeatures.bDeferRender) {\n    _fnCreateTr(oSettings, iRow, nTr, anTds);\n  }\n  return iRow;\n}\n\n/**\n * Add one or more TR elements to the table. Generally we'd expect to\n * use this for reading data from a DOM sourced table, but it could be\n * used for an TR element. Note that if a TR is given, it is used (i.e.\n * it is not cloned).\n *  @param {object} settings dataTables settings object\n *  @param {array|node|jQuery} trs The TR element(s) to add to the table\n *  @returns {array} Array of indexes for the added rows\n *  @memberof DataTable#oApi\n */\nfunction _fnAddTr(settings, trs) {\n  var row;\n\n  // Allow an individual node to be passed in\n  if (!(trs instanceof $)) {\n    trs = $(trs);\n  }\n  return trs.map(function (i, el) {\n    row = _fnGetRowElements(settings, el);\n    return _fnAddData(settings, row.data, el, row.cells);\n  });\n}\n\n/**\n * Take a TR element and convert it to an index in aoData\n *  @param {object} oSettings dataTables settings object\n *  @param {node} n the TR element to find\n *  @returns {int} index if the node is found, null if not\n *  @memberof DataTable#oApi\n */\nfunction _fnNodeToDataIndex(oSettings, n) {\n  return n._DT_RowIndex !== undefined ? n._DT_RowIndex : null;\n}\n\n/**\n * Take a TD element and convert it into a column data index (not the visible index)\n *  @param {object} oSettings dataTables settings object\n *  @param {int} iRow The row number the TD/TH can be found in\n *  @param {node} n The TD/TH element to find\n *  @returns {int} index if the node is found, -1 if not\n *  @memberof DataTable#oApi\n */\nfunction _fnNodeToColumnIndex(oSettings, iRow, n) {\n  return $.inArray(n, oSettings.aoData[iRow].anCells);\n}\n\n/**\n * Get the data for a given cell from the internal cache, taking into account data mapping\n *  @param {object} settings dataTables settings object\n *  @param {int} rowIdx aoData row id\n *  @param {int} colIdx Column index\n *  @param {string} type data get type ('display', 'type' 'filter|search' 'sort|order')\n *  @returns {*} Cell data\n *  @memberof DataTable#oApi\n */\nfunction _fnGetCellData(settings, rowIdx, colIdx, type) {\n  if (type === 'search') {\n    type = 'filter';\n  } else if (type === 'order') {\n    type = 'sort';\n  }\n  var draw = settings.iDraw;\n  var col = settings.aoColumns[colIdx];\n  var rowData = settings.aoData[rowIdx]._aData;\n  var defaultContent = col.sDefaultContent;\n  var cellData = col.fnGetData(rowData, type, {\n    settings: settings,\n    row: rowIdx,\n    col: colIdx\n  });\n  if (cellData === undefined) {\n    if (settings.iDrawError != draw && defaultContent === null) {\n      _fnLog(settings, 0, \"Requested unknown parameter \" + (typeof col.mData == 'function' ? '{function}' : \"'\" + col.mData + \"'\") + \" for row \" + rowIdx + \", column \" + colIdx, 4);\n      settings.iDrawError = draw;\n    }\n    return defaultContent;\n  }\n\n  // When the data source is null and a specific data type is requested (i.e.\n  // not the original data), we can use default column data\n  if ((cellData === rowData || cellData === null) && defaultContent !== null && type !== undefined) {\n    cellData = defaultContent;\n  } else if (typeof cellData === 'function') {\n    // If the data source is a function, then we run it and use the return,\n    // executing in the scope of the data object (for instances)\n    return cellData.call(rowData);\n  }\n  if (cellData === null && type === 'display') {\n    return '';\n  }\n  if (type === 'filter') {\n    var fomatters = DataTable.ext.type.search;\n    if (fomatters[col.sType]) {\n      cellData = fomatters[col.sType](cellData);\n    }\n  }\n  return cellData;\n}\n\n/**\n * Set the value for a specific cell, into the internal data cache\n *  @param {object} settings dataTables settings object\n *  @param {int} rowIdx aoData row id\n *  @param {int} colIdx Column index\n *  @param {*} val Value to set\n *  @memberof DataTable#oApi\n */\nfunction _fnSetCellData(settings, rowIdx, colIdx, val) {\n  var col = settings.aoColumns[colIdx];\n  var rowData = settings.aoData[rowIdx]._aData;\n  col.fnSetData(rowData, val, {\n    settings: settings,\n    row: rowIdx,\n    col: colIdx\n  });\n}\n\n// Private variable that is used to match action syntax in the data property object\nvar __reArray = /\\[.*?\\]$/;\nvar __reFn = /\\(\\)$/;\n\n/**\n * Split string on periods, taking into account escaped periods\n * @param  {string} str String to split\n * @return {array} Split string\n */\nfunction _fnSplitObjNotation(str) {\n  return $.map(str.match(/(\\\\.|[^\\.])+/g) || [''], function (s) {\n    return s.replace(/\\\\\\./g, '.');\n  });\n}\n\n/**\n * Return a function that can be used to get data from a source object, taking\n * into account the ability to use nested objects as a source\n *  @param {string|int|function} mSource The data source for the object\n *  @returns {function} Data get function\n *  @memberof DataTable#oApi\n */\nvar _fnGetObjectDataFn = DataTable.util.get;\n\n/**\n * Return a function that can be used to set data from a source object, taking\n * into account the ability to use nested objects as a source\n *  @param {string|int|function} mSource The data source for the object\n *  @returns {function} Data set function\n *  @memberof DataTable#oApi\n */\nvar _fnSetObjectDataFn = DataTable.util.set;\n\n/**\n * Return an array with the full table data\n *  @param {object} oSettings dataTables settings object\n *  @returns array {array} aData Master data array\n *  @memberof DataTable#oApi\n */\nfunction _fnGetDataMaster(settings) {\n  return _pluck(settings.aoData, '_aData');\n}\n\n/**\n * Nuke the table\n *  @param {object} oSettings dataTables settings object\n *  @memberof DataTable#oApi\n */\nfunction _fnClearTable(settings) {\n  settings.aoData.length = 0;\n  settings.aiDisplayMaster.length = 0;\n  settings.aiDisplay.length = 0;\n  settings.aIds = {};\n}\n\n/**\n* Take an array of integers (index array) and remove a target integer (value - not\n* the key!)\n*  @param {array} a Index array to target\n*  @param {int} iTarget value to find\n*  @memberof DataTable#oApi\n*/\nfunction _fnDeleteIndex(a, iTarget, splice) {\n  var iTargetIndex = -1;\n  for (var i = 0, iLen = a.length; i < iLen; i++) {\n    if (a[i] == iTarget) {\n      iTargetIndex = i;\n    } else if (a[i] > iTarget) {\n      a[i]--;\n    }\n  }\n  if (iTargetIndex != -1 && splice === undefined) {\n    a.splice(iTargetIndex, 1);\n  }\n}\n\n/**\n * Mark cached data as invalid such that a re-read of the data will occur when\n * the cached data is next requested. Also update from the data source object.\n *\n * @param {object} settings DataTables settings object\n * @param {int}    rowIdx   Row index to invalidate\n * @param {string} [src]    Source to invalidate from: undefined, 'auto', 'dom'\n *     or 'data'\n * @param {int}    [colIdx] Column index to invalidate. If undefined the whole\n *     row will be invalidated\n * @memberof DataTable#oApi\n *\n * @todo For the modularisation of v1.11 this will need to become a callback, so\n *   the sort and filter methods can subscribe to it. That will required\n *   initialisation options for sorting, which is why it is not already baked in\n */\nfunction _fnInvalidate(settings, rowIdx, src, colIdx) {\n  var row = settings.aoData[rowIdx];\n  var i, ien;\n  var cellWrite = function (cell, col) {\n    // This is very frustrating, but in IE if you just write directly\n    // to innerHTML, and elements that are overwritten are GC'ed,\n    // even if there is a reference to them elsewhere\n    while (cell.childNodes.length) {\n      cell.removeChild(cell.firstChild);\n    }\n    cell.innerHTML = _fnGetCellData(settings, rowIdx, col, 'display');\n  };\n\n  // Are we reading last data from DOM or the data object?\n  if (src === 'dom' || (!src || src === 'auto') && row.src === 'dom') {\n    // Read the data from the DOM\n    row._aData = _fnGetRowElements(settings, row, colIdx, colIdx === undefined ? undefined : row._aData).data;\n  } else {\n    // Reading from data object, update the DOM\n    var cells = row.anCells;\n    if (cells) {\n      if (colIdx !== undefined) {\n        cellWrite(cells[colIdx], colIdx);\n      } else {\n        for (i = 0, ien = cells.length; i < ien; i++) {\n          cellWrite(cells[i], i);\n        }\n      }\n    }\n  }\n\n  // For both row and cell invalidation, the cached data for sorting and\n  // filtering is nulled out\n  row._aSortData = null;\n  row._aFilterData = null;\n\n  // Invalidate the type for a specific column (if given) or all columns since\n  // the data might have changed\n  var cols = settings.aoColumns;\n  if (colIdx !== undefined) {\n    cols[colIdx].sType = null;\n  } else {\n    for (i = 0, ien = cols.length; i < ien; i++) {\n      cols[i].sType = null;\n    }\n\n    // Update DataTables special `DT_*` attributes for the row\n    _fnRowAttributes(settings, row);\n  }\n}\n\n/**\n * Build a data source object from an HTML row, reading the contents of the\n * cells that are in the row.\n *\n * @param {object} settings DataTables settings object\n * @param {node|object} TR element from which to read data or existing row\n *   object from which to re-read the data from the cells\n * @param {int} [colIdx] Optional column index\n * @param {array|object} [d] Data source object. If `colIdx` is given then this\n *   parameter should also be given and will be used to write the data into.\n *   Only the column in question will be written\n * @returns {object} Object with two parameters: `data` the data read, in\n *   document order, and `cells` and array of nodes (they can be useful to the\n *   caller, so rather than needing a second traversal to get them, just return\n *   them from here).\n * @memberof DataTable#oApi\n */\nfunction _fnGetRowElements(settings, row, colIdx, d) {\n  var tds = [],\n    td = row.firstChild,\n    name,\n    col,\n    o,\n    i = 0,\n    contents,\n    columns = settings.aoColumns,\n    objectRead = settings._rowReadObject;\n\n  // Allow the data object to be passed in, or construct\n  d = d !== undefined ? d : objectRead ? {} : [];\n  var attr = function (str, td) {\n    if (typeof str === 'string') {\n      var idx = str.indexOf('@');\n      if (idx !== -1) {\n        var attr = str.substring(idx + 1);\n        var setter = _fnSetObjectDataFn(str);\n        setter(d, td.getAttribute(attr));\n      }\n    }\n  };\n\n  // Read data from a cell and store into the data object\n  var cellProcess = function (cell) {\n    if (colIdx === undefined || colIdx === i) {\n      col = columns[i];\n      contents = cell.innerHTML.trim();\n      if (col && col._bAttrSrc) {\n        var setter = _fnSetObjectDataFn(col.mData._);\n        setter(d, contents);\n        attr(col.mData.sort, cell);\n        attr(col.mData.type, cell);\n        attr(col.mData.filter, cell);\n      } else {\n        // Depending on the `data` option for the columns the data can\n        // be read to either an object or an array.\n        if (objectRead) {\n          if (!col._setter) {\n            // Cache the setter function\n            col._setter = _fnSetObjectDataFn(col.mData);\n          }\n          col._setter(d, contents);\n        } else {\n          d[i] = contents;\n        }\n      }\n    }\n    i++;\n  };\n  if (td) {\n    // `tr` element was passed in\n    while (td) {\n      name = td.nodeName.toUpperCase();\n      if (name == \"TD\" || name == \"TH\") {\n        cellProcess(td);\n        tds.push(td);\n      }\n      td = td.nextSibling;\n    }\n  } else {\n    // Existing row object passed in\n    tds = row.anCells;\n    for (var j = 0, jen = tds.length; j < jen; j++) {\n      cellProcess(tds[j]);\n    }\n  }\n\n  // Read the ID from the DOM if present\n  var rowNode = row.firstChild ? row : row.nTr;\n  if (rowNode) {\n    var id = rowNode.getAttribute('id');\n    if (id) {\n      _fnSetObjectDataFn(settings.rowId)(d, id);\n    }\n  }\n  return {\n    data: d,\n    cells: tds\n  };\n}\n/**\n * Create a new TR element (and it's TD children) for a row\n *  @param {object} oSettings dataTables settings object\n *  @param {int} iRow Row to consider\n *  @param {node} [nTrIn] TR element to add to the table - optional. If not given,\n *    DataTables will create a row automatically\n *  @param {array} [anTds] Array of TD|TH elements for the row - must be given\n *    if nTr is.\n *  @memberof DataTable#oApi\n */\nfunction _fnCreateTr(oSettings, iRow, nTrIn, anTds) {\n  var row = oSettings.aoData[iRow],\n    rowData = row._aData,\n    cells = [],\n    nTr,\n    nTd,\n    oCol,\n    i,\n    iLen,\n    create;\n  if (row.nTr === null) {\n    nTr = nTrIn || document.createElement('tr');\n    row.nTr = nTr;\n    row.anCells = cells;\n\n    /* Use a private property on the node to allow reserve mapping from the node\n     * to the aoData array for fast look up\n     */\n    nTr._DT_RowIndex = iRow;\n\n    /* Special parameters can be given by the data source to be used on the row */\n    _fnRowAttributes(oSettings, row);\n\n    /* Process each column */\n    for (i = 0, iLen = oSettings.aoColumns.length; i < iLen; i++) {\n      oCol = oSettings.aoColumns[i];\n      create = nTrIn ? false : true;\n      nTd = create ? document.createElement(oCol.sCellType) : anTds[i];\n      if (!nTd) {\n        _fnLog(oSettings, 0, 'Incorrect column count', 18);\n      }\n      nTd._DT_CellIndex = {\n        row: iRow,\n        column: i\n      };\n      cells.push(nTd);\n\n      // Need to create the HTML if new, or if a rendering function is defined\n      if (create || (oCol.mRender || oCol.mData !== i) && (!$.isPlainObject(oCol.mData) || oCol.mData._ !== i + '.display')) {\n        nTd.innerHTML = _fnGetCellData(oSettings, iRow, i, 'display');\n      }\n\n      /* Add user defined class */\n      if (oCol.sClass) {\n        nTd.className += ' ' + oCol.sClass;\n      }\n\n      // Visibility - add or remove as required\n      if (oCol.bVisible && !nTrIn) {\n        nTr.appendChild(nTd);\n      } else if (!oCol.bVisible && nTrIn) {\n        nTd.parentNode.removeChild(nTd);\n      }\n      if (oCol.fnCreatedCell) {\n        oCol.fnCreatedCell.call(oSettings.oInstance, nTd, _fnGetCellData(oSettings, iRow, i), rowData, iRow, i);\n      }\n    }\n    _fnCallbackFire(oSettings, 'aoRowCreatedCallback', null, [nTr, rowData, iRow, cells]);\n  }\n}\n\n/**\n * Add attributes to a row based on the special `DT_*` parameters in a data\n * source object.\n *  @param {object} settings DataTables settings object\n *  @param {object} DataTables row object for the row to be modified\n *  @memberof DataTable#oApi\n */\nfunction _fnRowAttributes(settings, row) {\n  var tr = row.nTr;\n  var data = row._aData;\n  if (tr) {\n    var id = settings.rowIdFn(data);\n    if (id) {\n      tr.id = id;\n    }\n    if (data.DT_RowClass) {\n      // Remove any classes added by DT_RowClass before\n      var a = data.DT_RowClass.split(' ');\n      row.__rowc = row.__rowc ? _unique(row.__rowc.concat(a)) : a;\n      $(tr).removeClass(row.__rowc.join(' ')).addClass(data.DT_RowClass);\n    }\n    if (data.DT_RowAttr) {\n      $(tr).attr(data.DT_RowAttr);\n    }\n    if (data.DT_RowData) {\n      $(tr).data(data.DT_RowData);\n    }\n  }\n}\n\n/**\n * Create the HTML header for the table\n *  @param {object} oSettings dataTables settings object\n *  @memberof DataTable#oApi\n */\nfunction _fnBuildHead(oSettings) {\n  var i, ien, cell, row, column;\n  var thead = oSettings.nTHead;\n  var tfoot = oSettings.nTFoot;\n  var createHeader = $('th, td', thead).length === 0;\n  var classes = oSettings.oClasses;\n  var columns = oSettings.aoColumns;\n  if (createHeader) {\n    row = $('<tr/>').appendTo(thead);\n  }\n  for (i = 0, ien = columns.length; i < ien; i++) {\n    column = columns[i];\n    cell = $(column.nTh).addClass(column.sClass);\n    if (createHeader) {\n      cell.appendTo(row);\n    }\n\n    // 1.11 move into sorting\n    if (oSettings.oFeatures.bSort) {\n      cell.addClass(column.sSortingClass);\n      if (column.bSortable !== false) {\n        cell.attr('tabindex', oSettings.iTabIndex).attr('aria-controls', oSettings.sTableId);\n        _fnSortAttachListener(oSettings, column.nTh, i);\n      }\n    }\n    if (column.sTitle != cell[0].innerHTML) {\n      cell.html(column.sTitle);\n    }\n    _fnRenderer(oSettings, 'header')(oSettings, cell, column, classes);\n  }\n  if (createHeader) {\n    _fnDetectHeader(oSettings.aoHeader, thead);\n  }\n\n  /* Deal with the footer - add classes if required */\n  $(thead).children('tr').children('th, td').addClass(classes.sHeaderTH);\n  $(tfoot).children('tr').children('th, td').addClass(classes.sFooterTH);\n\n  // Cache the footer cells. Note that we only take the cells from the first\n  // row in the footer. If there is more than one row the user wants to\n  // interact with, they need to use the table().foot() method. Note also this\n  // allows cells to be used for multiple columns using colspan\n  if (tfoot !== null) {\n    var cells = oSettings.aoFooter[0];\n    for (i = 0, ien = cells.length; i < ien; i++) {\n      column = columns[i];\n      if (column) {\n        column.nTf = cells[i].cell;\n        if (column.sClass) {\n          $(column.nTf).addClass(column.sClass);\n        }\n      } else {\n        _fnLog(oSettings, 0, 'Incorrect column count', 18);\n      }\n    }\n  }\n}\n\n/**\n * Draw the header (or footer) element based on the column visibility states. The\n * methodology here is to use the layout array from _fnDetectHeader, modified for\n * the instantaneous column visibility, to construct the new layout. The grid is\n * traversed over cell at a time in a rows x columns grid fashion, although each\n * cell insert can cover multiple elements in the grid - which is tracks using the\n * aApplied array. Cell inserts in the grid will only occur where there isn't\n * already a cell in that position.\n *  @param {object} oSettings dataTables settings object\n *  @param array {objects} aoSource Layout array from _fnDetectHeader\n *  @param {boolean} [bIncludeHidden=false] If true then include the hidden columns in the calc,\n *  @memberof DataTable#oApi\n */\nfunction _fnDrawHead(oSettings, aoSource, bIncludeHidden) {\n  var i, iLen, j, jLen, k, kLen, n, nLocalTr;\n  var aoLocal = [];\n  var aApplied = [];\n  var iColumns = oSettings.aoColumns.length;\n  var iRowspan, iColspan;\n  if (!aoSource) {\n    return;\n  }\n  if (bIncludeHidden === undefined) {\n    bIncludeHidden = false;\n  }\n\n  /* Make a copy of the master layout array, but without the visible columns in it */\n  for (i = 0, iLen = aoSource.length; i < iLen; i++) {\n    aoLocal[i] = aoSource[i].slice();\n    aoLocal[i].nTr = aoSource[i].nTr;\n\n    /* Remove any columns which are currently hidden */\n    for (j = iColumns - 1; j >= 0; j--) {\n      if (!oSettings.aoColumns[j].bVisible && !bIncludeHidden) {\n        aoLocal[i].splice(j, 1);\n      }\n    }\n\n    /* Prep the applied array - it needs an element for each row */\n    aApplied.push([]);\n  }\n  for (i = 0, iLen = aoLocal.length; i < iLen; i++) {\n    nLocalTr = aoLocal[i].nTr;\n\n    /* All cells are going to be replaced, so empty out the row */\n    if (nLocalTr) {\n      while (n = nLocalTr.firstChild) {\n        nLocalTr.removeChild(n);\n      }\n    }\n    for (j = 0, jLen = aoLocal[i].length; j < jLen; j++) {\n      iRowspan = 1;\n      iColspan = 1;\n\n      /* Check to see if there is already a cell (row/colspan) covering our target\n       * insert point. If there is, then there is nothing to do.\n       */\n      if (aApplied[i][j] === undefined) {\n        nLocalTr.appendChild(aoLocal[i][j].cell);\n        aApplied[i][j] = 1;\n\n        /* Expand the cell to cover as many rows as needed */\n        while (aoLocal[i + iRowspan] !== undefined && aoLocal[i][j].cell == aoLocal[i + iRowspan][j].cell) {\n          aApplied[i + iRowspan][j] = 1;\n          iRowspan++;\n        }\n\n        /* Expand the cell to cover as many columns as needed */\n        while (aoLocal[i][j + iColspan] !== undefined && aoLocal[i][j].cell == aoLocal[i][j + iColspan].cell) {\n          /* Must update the applied array over the rows for the columns */\n          for (k = 0; k < iRowspan; k++) {\n            aApplied[i + k][j + iColspan] = 1;\n          }\n          iColspan++;\n        }\n\n        /* Do the actual expansion in the DOM */\n        $(aoLocal[i][j].cell).attr('rowspan', iRowspan).attr('colspan', iColspan);\n      }\n    }\n  }\n}\n\n/**\n * Insert the required TR nodes into the table for display\n *  @param {object} oSettings dataTables settings object\n *  @param ajaxComplete true after ajax call to complete rendering\n *  @memberof DataTable#oApi\n */\nfunction _fnDraw(oSettings, ajaxComplete) {\n  // Allow for state saving and a custom start position\n  _fnStart(oSettings);\n\n  /* Provide a pre-callback function which can be used to cancel the draw is false is returned */\n  var aPreDraw = _fnCallbackFire(oSettings, 'aoPreDrawCallback', 'preDraw', [oSettings]);\n  if ($.inArray(false, aPreDraw) !== -1) {\n    _fnProcessingDisplay(oSettings, false);\n    return;\n  }\n  var anRows = [];\n  var iRowCount = 0;\n  var asStripeClasses = oSettings.asStripeClasses;\n  var iStripes = asStripeClasses.length;\n  var oLang = oSettings.oLanguage;\n  var bServerSide = _fnDataSource(oSettings) == 'ssp';\n  var aiDisplay = oSettings.aiDisplay;\n  var iDisplayStart = oSettings._iDisplayStart;\n  var iDisplayEnd = oSettings.fnDisplayEnd();\n  oSettings.bDrawing = true;\n\n  /* Server-side processing draw intercept */\n  if (oSettings.bDeferLoading) {\n    oSettings.bDeferLoading = false;\n    oSettings.iDraw++;\n    _fnProcessingDisplay(oSettings, false);\n  } else if (!bServerSide) {\n    oSettings.iDraw++;\n  } else if (!oSettings.bDestroying && !ajaxComplete) {\n    _fnAjaxUpdate(oSettings);\n    return;\n  }\n  if (aiDisplay.length !== 0) {\n    var iStart = bServerSide ? 0 : iDisplayStart;\n    var iEnd = bServerSide ? oSettings.aoData.length : iDisplayEnd;\n    for (var j = iStart; j < iEnd; j++) {\n      var iDataIndex = aiDisplay[j];\n      var aoData = oSettings.aoData[iDataIndex];\n      if (aoData.nTr === null) {\n        _fnCreateTr(oSettings, iDataIndex);\n      }\n      var nRow = aoData.nTr;\n\n      /* Remove the old striping classes and then add the new one */\n      if (iStripes !== 0) {\n        var sStripe = asStripeClasses[iRowCount % iStripes];\n        if (aoData._sRowStripe != sStripe) {\n          $(nRow).removeClass(aoData._sRowStripe).addClass(sStripe);\n          aoData._sRowStripe = sStripe;\n        }\n      }\n\n      // Row callback functions - might want to manipulate the row\n      // iRowCount and j are not currently documented. Are they at all\n      // useful?\n      _fnCallbackFire(oSettings, 'aoRowCallback', null, [nRow, aoData._aData, iRowCount, j, iDataIndex]);\n      anRows.push(nRow);\n      iRowCount++;\n    }\n  } else {\n    /* Table is empty - create a row with an empty message in it */\n    var sZero = oLang.sZeroRecords;\n    if (oSettings.iDraw == 1 && _fnDataSource(oSettings) == 'ajax') {\n      sZero = oLang.sLoadingRecords;\n    } else if (oLang.sEmptyTable && oSettings.fnRecordsTotal() === 0) {\n      sZero = oLang.sEmptyTable;\n    }\n    anRows[0] = $('<tr/>', {\n      'class': iStripes ? asStripeClasses[0] : ''\n    }).append($('<td />', {\n      'valign': 'top',\n      'colSpan': _fnVisbleColumns(oSettings),\n      'class': oSettings.oClasses.sRowEmpty\n    }).html(sZero))[0];\n  }\n\n  /* Header and footer callbacks */\n  _fnCallbackFire(oSettings, 'aoHeaderCallback', 'header', [$(oSettings.nTHead).children('tr')[0], _fnGetDataMaster(oSettings), iDisplayStart, iDisplayEnd, aiDisplay]);\n  _fnCallbackFire(oSettings, 'aoFooterCallback', 'footer', [$(oSettings.nTFoot).children('tr')[0], _fnGetDataMaster(oSettings), iDisplayStart, iDisplayEnd, aiDisplay]);\n  var body = $(oSettings.nTBody);\n  body.children().detach();\n  body.append($(anRows));\n\n  /* Call all required callback functions for the end of a draw */\n  _fnCallbackFire(oSettings, 'aoDrawCallback', 'draw', [oSettings]);\n\n  /* Draw is complete, sorting and filtering must be as well */\n  oSettings.bSorted = false;\n  oSettings.bFiltered = false;\n  oSettings.bDrawing = false;\n}\n\n/**\n * Redraw the table - taking account of the various features which are enabled\n *  @param {object} oSettings dataTables settings object\n *  @param {boolean} [holdPosition] Keep the current paging position. By default\n *    the paging is reset to the first page\n *  @memberof DataTable#oApi\n */\nfunction _fnReDraw(settings, holdPosition) {\n  var features = settings.oFeatures,\n    sort = features.bSort,\n    filter = features.bFilter;\n  if (sort) {\n    _fnSort(settings);\n  }\n  if (filter) {\n    _fnFilterComplete(settings, settings.oPreviousSearch);\n  } else {\n    // No filtering, so we want to just use the display master\n    settings.aiDisplay = settings.aiDisplayMaster.slice();\n  }\n  if (holdPosition !== true) {\n    settings._iDisplayStart = 0;\n  }\n\n  // Let any modules know about the draw hold position state (used by\n  // scrolling internally)\n  settings._drawHold = holdPosition;\n  _fnDraw(settings);\n  settings._drawHold = false;\n}\n\n/**\n * Add the options to the page HTML for the table\n *  @param {object} oSettings dataTables settings object\n *  @memberof DataTable#oApi\n */\nfunction _fnAddOptionsHtml(oSettings) {\n  var classes = oSettings.oClasses;\n  var table = $(oSettings.nTable);\n  var holding = $('<div/>').insertBefore(table); // Holding element for speed\n  var features = oSettings.oFeatures;\n\n  // All DataTables are wrapped in a div\n  var insert = $('<div/>', {\n    id: oSettings.sTableId + '_wrapper',\n    'class': classes.sWrapper + (oSettings.nTFoot ? '' : ' ' + classes.sNoFooter)\n  });\n  oSettings.nHolding = holding[0];\n  oSettings.nTableWrapper = insert[0];\n  oSettings.nTableReinsertBefore = oSettings.nTable.nextSibling;\n\n  /* Loop over the user set positioning and place the elements as needed */\n  var aDom = oSettings.sDom.split('');\n  var featureNode, cOption, nNewNode, cNext, sAttr, j;\n  for (var i = 0; i < aDom.length; i++) {\n    featureNode = null;\n    cOption = aDom[i];\n    if (cOption == '<') {\n      /* New container div */\n      nNewNode = $('<div/>')[0];\n\n      /* Check to see if we should append an id and/or a class name to the container */\n      cNext = aDom[i + 1];\n      if (cNext == \"'\" || cNext == '\"') {\n        sAttr = \"\";\n        j = 2;\n        while (aDom[i + j] != cNext) {\n          sAttr += aDom[i + j];\n          j++;\n        }\n\n        /* Replace jQuery UI constants @todo depreciated */\n        if (sAttr == \"H\") {\n          sAttr = classes.sJUIHeader;\n        } else if (sAttr == \"F\") {\n          sAttr = classes.sJUIFooter;\n        }\n\n        /* The attribute can be in the format of \"#id.class\", \"#id\" or \"class\" This logic\n         * breaks the string into parts and applies them as needed\n         */\n        if (sAttr.indexOf('.') != -1) {\n          var aSplit = sAttr.split('.');\n          nNewNode.id = aSplit[0].substr(1, aSplit[0].length - 1);\n          nNewNode.className = aSplit[1];\n        } else if (sAttr.charAt(0) == \"#\") {\n          nNewNode.id = sAttr.substr(1, sAttr.length - 1);\n        } else {\n          nNewNode.className = sAttr;\n        }\n        i += j; /* Move along the position array */\n      }\n      insert.append(nNewNode);\n      insert = $(nNewNode);\n    } else if (cOption == '>') {\n      /* End container div */\n      insert = insert.parent();\n    }\n    // @todo Move options into their own plugins?\n    else if (cOption == 'l' && features.bPaginate && features.bLengthChange) {\n      /* Length */\n      featureNode = _fnFeatureHtmlLength(oSettings);\n    } else if (cOption == 'f' && features.bFilter) {\n      /* Filter */\n      featureNode = _fnFeatureHtmlFilter(oSettings);\n    } else if (cOption == 'r' && features.bProcessing) {\n      /* pRocessing */\n      featureNode = _fnFeatureHtmlProcessing(oSettings);\n    } else if (cOption == 't') {\n      /* Table */\n      featureNode = _fnFeatureHtmlTable(oSettings);\n    } else if (cOption == 'i' && features.bInfo) {\n      /* Info */\n      featureNode = _fnFeatureHtmlInfo(oSettings);\n    } else if (cOption == 'p' && features.bPaginate) {\n      /* Pagination */\n      featureNode = _fnFeatureHtmlPaginate(oSettings);\n    } else if (DataTable.ext.feature.length !== 0) {\n      /* Plug-in features */\n      var aoFeatures = DataTable.ext.feature;\n      for (var k = 0, kLen = aoFeatures.length; k < kLen; k++) {\n        if (cOption == aoFeatures[k].cFeature) {\n          featureNode = aoFeatures[k].fnInit(oSettings);\n          break;\n        }\n      }\n    }\n\n    /* Add to the 2D features array */\n    if (featureNode) {\n      var aanFeatures = oSettings.aanFeatures;\n      if (!aanFeatures[cOption]) {\n        aanFeatures[cOption] = [];\n      }\n      aanFeatures[cOption].push(featureNode);\n      insert.append(featureNode);\n    }\n  }\n\n  /* Built our DOM structure - replace the holding div with what we want */\n  holding.replaceWith(insert);\n  oSettings.nHolding = null;\n}\n\n/**\n * Use the DOM source to create up an array of header cells. The idea here is to\n * create a layout grid (array) of rows x columns, which contains a reference\n * to the cell that that point in the grid (regardless of col/rowspan), such that\n * any column / row could be removed and the new grid constructed\n *  @param array {object} aLayout Array to store the calculated layout in\n *  @param {node} nThead The header/footer element for the table\n *  @memberof DataTable#oApi\n */\nfunction _fnDetectHeader(aLayout, nThead) {\n  var nTrs = $(nThead).children('tr');\n  var nTr, nCell;\n  var i, k, l, iLen, jLen, iColShifted, iColumn, iColspan, iRowspan;\n  var bUnique;\n  var fnShiftCol = function (a, i, j) {\n    var k = a[i];\n    while (k[j]) {\n      j++;\n    }\n    return j;\n  };\n  aLayout.splice(0, aLayout.length);\n\n  /* We know how many rows there are in the layout - so prep it */\n  for (i = 0, iLen = nTrs.length; i < iLen; i++) {\n    aLayout.push([]);\n  }\n\n  /* Calculate a layout array */\n  for (i = 0, iLen = nTrs.length; i < iLen; i++) {\n    nTr = nTrs[i];\n    iColumn = 0;\n\n    /* For every cell in the row... */\n    nCell = nTr.firstChild;\n    while (nCell) {\n      if (nCell.nodeName.toUpperCase() == \"TD\" || nCell.nodeName.toUpperCase() == \"TH\") {\n        /* Get the col and rowspan attributes from the DOM and sanitise them */\n        iColspan = nCell.getAttribute('colspan') * 1;\n        iRowspan = nCell.getAttribute('rowspan') * 1;\n        iColspan = !iColspan || iColspan === 0 || iColspan === 1 ? 1 : iColspan;\n        iRowspan = !iRowspan || iRowspan === 0 || iRowspan === 1 ? 1 : iRowspan;\n\n        /* There might be colspan cells already in this row, so shift our target\n         * accordingly\n         */\n        iColShifted = fnShiftCol(aLayout, i, iColumn);\n\n        /* Cache calculation for unique columns */\n        bUnique = iColspan === 1 ? true : false;\n\n        /* If there is col / rowspan, copy the information into the layout grid */\n        for (l = 0; l < iColspan; l++) {\n          for (k = 0; k < iRowspan; k++) {\n            aLayout[i + k][iColShifted + l] = {\n              \"cell\": nCell,\n              \"unique\": bUnique\n            };\n            aLayout[i + k].nTr = nTr;\n          }\n        }\n      }\n      nCell = nCell.nextSibling;\n    }\n  }\n}\n\n/**\n * Get an array of unique th elements, one for each column\n *  @param {object} oSettings dataTables settings object\n *  @param {node} nHeader automatically detect the layout from this node - optional\n *  @param {array} aLayout thead/tfoot layout from _fnDetectHeader - optional\n *  @returns array {node} aReturn list of unique th's\n *  @memberof DataTable#oApi\n */\nfunction _fnGetUniqueThs(oSettings, nHeader, aLayout) {\n  var aReturn = [];\n  if (!aLayout) {\n    aLayout = oSettings.aoHeader;\n    if (nHeader) {\n      aLayout = [];\n      _fnDetectHeader(aLayout, nHeader);\n    }\n  }\n  for (var i = 0, iLen = aLayout.length; i < iLen; i++) {\n    for (var j = 0, jLen = aLayout[i].length; j < jLen; j++) {\n      if (aLayout[i][j].unique && (!aReturn[j] || !oSettings.bSortCellsTop)) {\n        aReturn[j] = aLayout[i][j].cell;\n      }\n    }\n  }\n  return aReturn;\n}\n\n/**\n * Set the start position for draw\n *  @param {object} oSettings dataTables settings object\n */\nfunction _fnStart(oSettings) {\n  var bServerSide = _fnDataSource(oSettings) == 'ssp';\n  var iInitDisplayStart = oSettings.iInitDisplayStart;\n\n  // Check and see if we have an initial draw position from state saving\n  if (iInitDisplayStart !== undefined && iInitDisplayStart !== -1) {\n    oSettings._iDisplayStart = bServerSide ? iInitDisplayStart : iInitDisplayStart >= oSettings.fnRecordsDisplay() ? 0 : iInitDisplayStart;\n    oSettings.iInitDisplayStart = -1;\n  }\n}\n\n/**\n * Create an Ajax call based on the table's settings, taking into account that\n * parameters can have multiple forms, and backwards compatibility.\n *\n * @param {object} oSettings dataTables settings object\n * @param {array} data Data to send to the server, required by\n *     DataTables - may be augmented by developer callbacks\n * @param {function} fn Callback function to run when data is obtained\n */\nfunction _fnBuildAjax(oSettings, data, fn) {\n  // Compatibility with 1.9-, allow fnServerData and event to manipulate\n  _fnCallbackFire(oSettings, 'aoServerParams', 'serverParams', [data]);\n\n  // Convert to object based for 1.10+ if using the old array scheme which can\n  // come from server-side processing or serverParams\n  if (data && Array.isArray(data)) {\n    var tmp = {};\n    var rbracket = /(.*?)\\[\\]$/;\n    $.each(data, function (key, val) {\n      var match = val.name.match(rbracket);\n      if (match) {\n        // Support for arrays\n        var name = match[0];\n        if (!tmp[name]) {\n          tmp[name] = [];\n        }\n        tmp[name].push(val.value);\n      } else {\n        tmp[val.name] = val.value;\n      }\n    });\n    data = tmp;\n  }\n  var ajaxData;\n  var ajax = oSettings.ajax;\n  var instance = oSettings.oInstance;\n  var callback = function (json) {\n    var status = oSettings.jqXHR ? oSettings.jqXHR.status : null;\n    if (json === null || typeof status === 'number' && status == 204) {\n      json = {};\n      _fnAjaxDataSrc(oSettings, json, []);\n    }\n    var error = json.error || json.sError;\n    if (error) {\n      _fnLog(oSettings, 0, error);\n    }\n    oSettings.json = json;\n    _fnCallbackFire(oSettings, null, 'xhr', [oSettings, json, oSettings.jqXHR]);\n    fn(json);\n  };\n  if ($.isPlainObject(ajax) && ajax.data) {\n    ajaxData = ajax.data;\n    var newData = typeof ajaxData === 'function' ? ajaxData(data, oSettings) :\n    // fn can manipulate data or return\n    ajaxData; // an object object or array to merge\n\n    // If the function returned something, use that alone\n    data = typeof ajaxData === 'function' && newData ? newData : $.extend(true, data, newData);\n\n    // Remove the data property as we've resolved it already and don't want\n    // jQuery to do it again (it is restored at the end of the function)\n    delete ajax.data;\n  }\n  var baseAjax = {\n    \"data\": data,\n    \"success\": callback,\n    \"dataType\": \"json\",\n    \"cache\": false,\n    \"type\": oSettings.sServerMethod,\n    \"error\": function (xhr, error, thrown) {\n      var ret = _fnCallbackFire(oSettings, null, 'xhr', [oSettings, null, oSettings.jqXHR]);\n      if ($.inArray(true, ret) === -1) {\n        if (error == \"parsererror\") {\n          _fnLog(oSettings, 0, 'Invalid JSON response', 1);\n        } else if (xhr.readyState === 4) {\n          _fnLog(oSettings, 0, 'Ajax error', 7);\n        }\n      }\n      _fnProcessingDisplay(oSettings, false);\n    }\n  };\n\n  // Store the data submitted for the API\n  oSettings.oAjaxData = data;\n\n  // Allow plug-ins and external processes to modify the data\n  _fnCallbackFire(oSettings, null, 'preXhr', [oSettings, data]);\n  if (oSettings.fnServerData) {\n    // DataTables 1.9- compatibility\n    oSettings.fnServerData.call(instance, oSettings.sAjaxSource, $.map(data, function (val, key) {\n      // Need to convert back to 1.9 trad format\n      return {\n        name: key,\n        value: val\n      };\n    }), callback, oSettings);\n  } else if (oSettings.sAjaxSource || typeof ajax === 'string') {\n    // DataTables 1.9- compatibility\n    oSettings.jqXHR = $.ajax($.extend(baseAjax, {\n      url: ajax || oSettings.sAjaxSource\n    }));\n  } else if (typeof ajax === 'function') {\n    // Is a function - let the caller define what needs to be done\n    oSettings.jqXHR = ajax.call(instance, data, callback, oSettings);\n  } else {\n    // Object to extend the base settings\n    oSettings.jqXHR = $.ajax($.extend(baseAjax, ajax));\n\n    // Restore for next time around\n    ajax.data = ajaxData;\n  }\n}\n\n/**\n * Update the table using an Ajax call\n *  @param {object} settings dataTables settings object\n *  @returns {boolean} Block the table drawing or not\n *  @memberof DataTable#oApi\n */\nfunction _fnAjaxUpdate(settings) {\n  settings.iDraw++;\n  _fnProcessingDisplay(settings, true);\n\n  // Keep track of drawHold state to handle scrolling after the Ajax call\n  var drawHold = settings._drawHold;\n  _fnBuildAjax(settings, _fnAjaxParameters(settings), function (json) {\n    settings._drawHold = drawHold;\n    _fnAjaxUpdateDraw(settings, json);\n    settings._drawHold = false;\n  });\n}\n\n/**\n * Build up the parameters in an object needed for a server-side processing\n * request. Note that this is basically done twice, is different ways - a modern\n * method which is used by default in DataTables 1.10 which uses objects and\n * arrays, or the 1.9- method with is name / value pairs. 1.9 method is used if\n * the sAjaxSource option is used in the initialisation, or the legacyAjax\n * option is set.\n *  @param {object} oSettings dataTables settings object\n *  @returns {bool} block the table drawing or not\n *  @memberof DataTable#oApi\n */\nfunction _fnAjaxParameters(settings) {\n  var columns = settings.aoColumns,\n    columnCount = columns.length,\n    features = settings.oFeatures,\n    preSearch = settings.oPreviousSearch,\n    preColSearch = settings.aoPreSearchCols,\n    i,\n    data = [],\n    dataProp,\n    column,\n    columnSearch,\n    sort = _fnSortFlatten(settings),\n    displayStart = settings._iDisplayStart,\n    displayLength = features.bPaginate !== false ? settings._iDisplayLength : -1;\n  var param = function (name, value) {\n    data.push({\n      'name': name,\n      'value': value\n    });\n  };\n\n  // DataTables 1.9- compatible method\n  param('sEcho', settings.iDraw);\n  param('iColumns', columnCount);\n  param('sColumns', _pluck(columns, 'sName').join(','));\n  param('iDisplayStart', displayStart);\n  param('iDisplayLength', displayLength);\n\n  // DataTables 1.10+ method\n  var d = {\n    draw: settings.iDraw,\n    columns: [],\n    order: [],\n    start: displayStart,\n    length: displayLength,\n    search: {\n      value: preSearch.sSearch,\n      regex: preSearch.bRegex\n    }\n  };\n  for (i = 0; i < columnCount; i++) {\n    column = columns[i];\n    columnSearch = preColSearch[i];\n    dataProp = typeof column.mData == \"function\" ? 'function' : column.mData;\n    d.columns.push({\n      data: dataProp,\n      name: column.sName,\n      searchable: column.bSearchable,\n      orderable: column.bSortable,\n      search: {\n        value: columnSearch.sSearch,\n        regex: columnSearch.bRegex\n      }\n    });\n    param(\"mDataProp_\" + i, dataProp);\n    if (features.bFilter) {\n      param('sSearch_' + i, columnSearch.sSearch);\n      param('bRegex_' + i, columnSearch.bRegex);\n      param('bSearchable_' + i, column.bSearchable);\n    }\n    if (features.bSort) {\n      param('bSortable_' + i, column.bSortable);\n    }\n  }\n  if (features.bFilter) {\n    param('sSearch', preSearch.sSearch);\n    param('bRegex', preSearch.bRegex);\n  }\n  if (features.bSort) {\n    $.each(sort, function (i, val) {\n      d.order.push({\n        column: val.col,\n        dir: val.dir\n      });\n      param('iSortCol_' + i, val.col);\n      param('sSortDir_' + i, val.dir);\n    });\n    param('iSortingCols', sort.length);\n  }\n\n  // If the legacy.ajax parameter is null, then we automatically decide which\n  // form to use, based on sAjaxSource\n  var legacy = DataTable.ext.legacy.ajax;\n  if (legacy === null) {\n    return settings.sAjaxSource ? data : d;\n  }\n\n  // Otherwise, if legacy has been specified then we use that to decide on the\n  // form\n  return legacy ? data : d;\n}\n\n/**\n * Data the data from the server (nuking the old) and redraw the table\n *  @param {object} oSettings dataTables settings object\n *  @param {object} json json data return from the server.\n *  @param {string} json.sEcho Tracking flag for DataTables to match requests\n *  @param {int} json.iTotalRecords Number of records in the data set, not accounting for filtering\n *  @param {int} json.iTotalDisplayRecords Number of records in the data set, accounting for filtering\n *  @param {array} json.aaData The data to display on this page\n *  @param {string} [json.sColumns] Column ordering (sName, comma separated)\n *  @memberof DataTable#oApi\n */\nfunction _fnAjaxUpdateDraw(settings, json) {\n  // v1.10 uses camelCase variables, while 1.9 uses Hungarian notation.\n  // Support both\n  var compat = function (old, modern) {\n    return json[old] !== undefined ? json[old] : json[modern];\n  };\n  var data = _fnAjaxDataSrc(settings, json);\n  var draw = compat('sEcho', 'draw');\n  var recordsTotal = compat('iTotalRecords', 'recordsTotal');\n  var recordsFiltered = compat('iTotalDisplayRecords', 'recordsFiltered');\n  if (draw !== undefined) {\n    // Protect against out of sequence returns\n    if (draw * 1 < settings.iDraw) {\n      return;\n    }\n    settings.iDraw = draw * 1;\n  }\n\n  // No data in returned object, so rather than an array, we show an empty table\n  if (!data) {\n    data = [];\n  }\n  _fnClearTable(settings);\n  settings._iRecordsTotal = parseInt(recordsTotal, 10);\n  settings._iRecordsDisplay = parseInt(recordsFiltered, 10);\n  for (var i = 0, ien = data.length; i < ien; i++) {\n    _fnAddData(settings, data[i]);\n  }\n  settings.aiDisplay = settings.aiDisplayMaster.slice();\n  _fnDraw(settings, true);\n  if (!settings._bInitComplete) {\n    _fnInitComplete(settings, json);\n  }\n  _fnProcessingDisplay(settings, false);\n}\n\n/**\n * Get the data from the JSON data source to use for drawing a table. Using\n * `_fnGetObjectDataFn` allows the data to be sourced from a property of the\n * source object, or from a processing function.\n *  @param {object} oSettings dataTables settings object\n *  @param  {object} json Data source object / array from the server\n *  @return {array} Array of data to use\n */\nfunction _fnAjaxDataSrc(oSettings, json, write) {\n  var dataSrc = $.isPlainObject(oSettings.ajax) && oSettings.ajax.dataSrc !== undefined ? oSettings.ajax.dataSrc : oSettings.sAjaxDataProp; // Compatibility with 1.9-.\n\n  if (!write) {\n    if (dataSrc === 'data') {\n      // If the default, then we still want to support the old style, and safely ignore\n      // it if possible\n      return json.aaData || json[dataSrc];\n    }\n    return dataSrc !== \"\" ? _fnGetObjectDataFn(dataSrc)(json) : json;\n  }\n\n  // set\n  _fnSetObjectDataFn(dataSrc)(json, write);\n}\n\n/**\n * Generate the node required for filtering text\n *  @returns {node} Filter control element\n *  @param {object} oSettings dataTables settings object\n *  @memberof DataTable#oApi\n */\nfunction _fnFeatureHtmlFilter(settings) {\n  var classes = settings.oClasses;\n  var tableId = settings.sTableId;\n  var language = settings.oLanguage;\n  var previousSearch = settings.oPreviousSearch;\n  var features = settings.aanFeatures;\n  var input = '<input type=\"search\" class=\"' + classes.sFilterInput + '\"/>';\n  var str = language.sSearch;\n  str = str.match(/_INPUT_/) ? str.replace('_INPUT_', input) : str + input;\n  var filter = $('<div/>', {\n    'id': !features.f ? tableId + '_filter' : null,\n    'class': classes.sFilter\n  }).append($('<label/>').append(str));\n  var searchFn = function (event) {\n    /* Update all other filter input elements for the new display */\n    var n = features.f;\n    var val = !this.value ? \"\" : this.value; // mental IE8 fix :-(\n    if (previousSearch['return'] && event.key !== \"Enter\") {\n      return;\n    }\n    /* Now do the filter */\n    if (val != previousSearch.sSearch) {\n      _fnFilterComplete(settings, {\n        \"sSearch\": val,\n        \"bRegex\": previousSearch.bRegex,\n        \"bSmart\": previousSearch.bSmart,\n        \"bCaseInsensitive\": previousSearch.bCaseInsensitive,\n        \"return\": previousSearch['return']\n      });\n\n      // Need to redraw, without resorting\n      settings._iDisplayStart = 0;\n      _fnDraw(settings);\n    }\n  };\n  var searchDelay = settings.searchDelay !== null ? settings.searchDelay : _fnDataSource(settings) === 'ssp' ? 400 : 0;\n  var jqFilter = $('input', filter).val(previousSearch.sSearch).attr('placeholder', language.sSearchPlaceholder).on('keyup.DT search.DT input.DT paste.DT cut.DT', searchDelay ? _fnThrottle(searchFn, searchDelay) : searchFn).on('mouseup.DT', function (e) {\n    // Edge fix! Edge 17 does not trigger anything other than mouse events when clicking\n    // on the clear icon (Edge bug 17584515). This is safe in other browsers as `searchFn`\n    // checks the value to see if it has changed. In other browsers it won't have.\n    setTimeout(function () {\n      searchFn.call(jqFilter[0], e);\n    }, 10);\n  }).on('keypress.DT', function (e) {\n    /* Prevent form submission */\n    if (e.keyCode == 13) {\n      return false;\n    }\n  }).attr('aria-controls', tableId);\n\n  // Update the input elements whenever the table is filtered\n  $(settings.nTable).on('search.dt.DT', function (ev, s) {\n    if (settings === s) {\n      // IE9 throws an 'unknown error' if document.activeElement is used\n      // inside an iframe or frame...\n      try {\n        if (jqFilter[0] !== document.activeElement) {\n          jqFilter.val(previousSearch.sSearch);\n        }\n      } catch (e) {}\n    }\n  });\n  return filter[0];\n}\n\n/**\n * Filter the table using both the global filter and column based filtering\n *  @param {object} oSettings dataTables settings object\n *  @param {object} oSearch search information\n *  @param {int} [iForce] force a research of the master array (1) or not (undefined or 0)\n *  @memberof DataTable#oApi\n */\nfunction _fnFilterComplete(oSettings, oInput, iForce) {\n  var oPrevSearch = oSettings.oPreviousSearch;\n  var aoPrevSearch = oSettings.aoPreSearchCols;\n  var fnSaveFilter = function (oFilter) {\n    /* Save the filtering values */\n    oPrevSearch.sSearch = oFilter.sSearch;\n    oPrevSearch.bRegex = oFilter.bRegex;\n    oPrevSearch.bSmart = oFilter.bSmart;\n    oPrevSearch.bCaseInsensitive = oFilter.bCaseInsensitive;\n    oPrevSearch['return'] = oFilter['return'];\n  };\n  var fnRegex = function (o) {\n    // Backwards compatibility with the bEscapeRegex option\n    return o.bEscapeRegex !== undefined ? !o.bEscapeRegex : o.bRegex;\n  };\n\n  // Resolve any column types that are unknown due to addition or invalidation\n  // @todo As per sort - can this be moved into an event handler?\n  _fnColumnTypes(oSettings);\n\n  /* In server-side processing all filtering is done by the server, so no point hanging around here */\n  if (_fnDataSource(oSettings) != 'ssp') {\n    /* Global filter */\n    _fnFilter(oSettings, oInput.sSearch, iForce, fnRegex(oInput), oInput.bSmart, oInput.bCaseInsensitive);\n    fnSaveFilter(oInput);\n\n    /* Now do the individual column filter */\n    for (var i = 0; i < aoPrevSearch.length; i++) {\n      _fnFilterColumn(oSettings, aoPrevSearch[i].sSearch, i, fnRegex(aoPrevSearch[i]), aoPrevSearch[i].bSmart, aoPrevSearch[i].bCaseInsensitive);\n    }\n\n    /* Custom filtering */\n    _fnFilterCustom(oSettings);\n  } else {\n    fnSaveFilter(oInput);\n  }\n\n  /* Tell the draw function we have been filtering */\n  oSettings.bFiltered = true;\n  _fnCallbackFire(oSettings, null, 'search', [oSettings]);\n}\n\n/**\n * Apply custom filtering functions\n *  @param {object} oSettings dataTables settings object\n *  @memberof DataTable#oApi\n */\nfunction _fnFilterCustom(settings) {\n  var filters = DataTable.ext.search;\n  var displayRows = settings.aiDisplay;\n  var row, rowIdx;\n  for (var i = 0, ien = filters.length; i < ien; i++) {\n    var rows = [];\n\n    // Loop over each row and see if it should be included\n    for (var j = 0, jen = displayRows.length; j < jen; j++) {\n      rowIdx = displayRows[j];\n      row = settings.aoData[rowIdx];\n      if (filters[i](settings, row._aFilterData, rowIdx, row._aData, j)) {\n        rows.push(rowIdx);\n      }\n    }\n\n    // So the array reference doesn't break set the results into the\n    // existing array\n    displayRows.length = 0;\n    $.merge(displayRows, rows);\n  }\n}\n\n/**\n * Filter the table on a per-column basis\n *  @param {object} oSettings dataTables settings object\n *  @param {string} sInput string to filter on\n *  @param {int} iColumn column to filter\n *  @param {bool} bRegex treat search string as a regular expression or not\n *  @param {bool} bSmart use smart filtering or not\n *  @param {bool} bCaseInsensitive Do case insensitive matching or not\n *  @memberof DataTable#oApi\n */\nfunction _fnFilterColumn(settings, searchStr, colIdx, regex, smart, caseInsensitive) {\n  if (searchStr === '') {\n    return;\n  }\n  var data;\n  var out = [];\n  var display = settings.aiDisplay;\n  var rpSearch = _fnFilterCreateSearch(searchStr, regex, smart, caseInsensitive);\n  for (var i = 0; i < display.length; i++) {\n    data = settings.aoData[display[i]]._aFilterData[colIdx];\n    if (rpSearch.test(data)) {\n      out.push(display[i]);\n    }\n  }\n  settings.aiDisplay = out;\n}\n\n/**\n * Filter the data table based on user input and draw the table\n *  @param {object} settings dataTables settings object\n *  @param {string} input string to filter on\n *  @param {int} force optional - force a research of the master array (1) or not (undefined or 0)\n *  @param {bool} regex treat as a regular expression or not\n *  @param {bool} smart perform smart filtering or not\n *  @param {bool} caseInsensitive Do case insensitive matching or not\n *  @memberof DataTable#oApi\n */\nfunction _fnFilter(settings, input, force, regex, smart, caseInsensitive) {\n  var rpSearch = _fnFilterCreateSearch(input, regex, smart, caseInsensitive);\n  var prevSearch = settings.oPreviousSearch.sSearch;\n  var displayMaster = settings.aiDisplayMaster;\n  var display, invalidated, i;\n  var filtered = [];\n\n  // Need to take account of custom filtering functions - always filter\n  if (DataTable.ext.search.length !== 0) {\n    force = true;\n  }\n\n  // Check if any of the rows were invalidated\n  invalidated = _fnFilterData(settings);\n\n  // If the input is blank - we just want the full data set\n  if (input.length <= 0) {\n    settings.aiDisplay = displayMaster.slice();\n  } else {\n    // New search - start from the master array\n    if (invalidated || force || regex || prevSearch.length > input.length || input.indexOf(prevSearch) !== 0 || settings.bSorted // On resort, the display master needs to be\n    // re-filtered since indexes will have changed\n    ) {\n      settings.aiDisplay = displayMaster.slice();\n    }\n\n    // Search the display array\n    display = settings.aiDisplay;\n    for (i = 0; i < display.length; i++) {\n      if (rpSearch.test(settings.aoData[display[i]]._sFilterRow)) {\n        filtered.push(display[i]);\n      }\n    }\n    settings.aiDisplay = filtered;\n  }\n}\n\n/**\n * Build a regular expression object suitable for searching a table\n *  @param {string} sSearch string to search for\n *  @param {bool} bRegex treat as a regular expression or not\n *  @param {bool} bSmart perform smart filtering or not\n *  @param {bool} bCaseInsensitive Do case insensitive matching or not\n *  @returns {RegExp} constructed object\n *  @memberof DataTable#oApi\n */\nfunction _fnFilterCreateSearch(search, regex, smart, caseInsensitive) {\n  search = regex ? search : _fnEscapeRegex(search);\n  if (smart) {\n    /* For smart filtering we want to allow the search to work regardless of\n     * word order. We also want double quoted text to be preserved, so word\n     * order is important - a la google. So this is what we want to\n     * generate:\n     * \n     * ^(?=.*?\\bone\\b)(?=.*?\\btwo three\\b)(?=.*?\\bfour\\b).*$\n     */\n    var a = $.map(search.match(/[\"\\u201C][^\"\\u201D]+[\"\\u201D]|[^ ]+/g) || [''], function (word) {\n      if (word.charAt(0) === '\"') {\n        var m = word.match(/^\"(.*)\"$/);\n        word = m ? m[1] : word;\n      } else if (word.charAt(0) === '\\u201C') {\n        var m = word.match(/^\\u201C(.*)\\u201D$/);\n        word = m ? m[1] : word;\n      }\n      return word.replace('\"', '');\n    });\n    search = '^(?=.*?' + a.join(')(?=.*?') + ').*$';\n  }\n  return new RegExp(search, caseInsensitive ? 'i' : '');\n}\n\n/**\n * Escape a string such that it can be used in a regular expression\n *  @param {string} sVal string to escape\n *  @returns {string} escaped string\n *  @memberof DataTable#oApi\n */\nvar _fnEscapeRegex = DataTable.util.escapeRegex;\nvar __filter_div = $('<div>')[0];\nvar __filter_div_textContent = __filter_div.textContent !== undefined;\n\n// Update the filtering data for each row if needed (by invalidation or first run)\nfunction _fnFilterData(settings) {\n  var columns = settings.aoColumns;\n  var column;\n  var i, j, ien, jen, filterData, cellData, row;\n  var wasInvalidated = false;\n  for (i = 0, ien = settings.aoData.length; i < ien; i++) {\n    row = settings.aoData[i];\n    if (!row._aFilterData) {\n      filterData = [];\n      for (j = 0, jen = columns.length; j < jen; j++) {\n        column = columns[j];\n        if (column.bSearchable) {\n          cellData = _fnGetCellData(settings, i, j, 'filter');\n\n          // Search in DataTables 1.10 is string based. In 1.11 this\n          // should be altered to also allow strict type checking.\n          if (cellData === null) {\n            cellData = '';\n          }\n          if (typeof cellData !== 'string' && cellData.toString) {\n            cellData = cellData.toString();\n          }\n        } else {\n          cellData = '';\n        }\n\n        // If it looks like there is an HTML entity in the string,\n        // attempt to decode it so sorting works as expected. Note that\n        // we could use a single line of jQuery to do this, but the DOM\n        // method used here is much faster https://jsperf.com/html-decode\n        if (cellData.indexOf && cellData.indexOf('&') !== -1) {\n          __filter_div.innerHTML = cellData;\n          cellData = __filter_div_textContent ? __filter_div.textContent : __filter_div.innerText;\n        }\n        if (cellData.replace) {\n          cellData = cellData.replace(/[\\r\\n\\u2028]/g, '');\n        }\n        filterData.push(cellData);\n      }\n      row._aFilterData = filterData;\n      row._sFilterRow = filterData.join('  ');\n      wasInvalidated = true;\n    }\n  }\n  return wasInvalidated;\n}\n\n/**\n * Convert from the internal Hungarian notation to camelCase for external\n * interaction\n *  @param {object} obj Object to convert\n *  @returns {object} Inverted object\n *  @memberof DataTable#oApi\n */\nfunction _fnSearchToCamel(obj) {\n  return {\n    search: obj.sSearch,\n    smart: obj.bSmart,\n    regex: obj.bRegex,\n    caseInsensitive: obj.bCaseInsensitive\n  };\n}\n\n/**\n * Convert from camelCase notation to the internal Hungarian. We could use the\n * Hungarian convert function here, but this is cleaner\n *  @param {object} obj Object to convert\n *  @returns {object} Inverted object\n *  @memberof DataTable#oApi\n */\nfunction _fnSearchToHung(obj) {\n  return {\n    sSearch: obj.search,\n    bSmart: obj.smart,\n    bRegex: obj.regex,\n    bCaseInsensitive: obj.caseInsensitive\n  };\n}\n\n/**\n * Generate the node required for the info display\n *  @param {object} oSettings dataTables settings object\n *  @returns {node} Information element\n *  @memberof DataTable#oApi\n */\nfunction _fnFeatureHtmlInfo(settings) {\n  var tid = settings.sTableId,\n    nodes = settings.aanFeatures.i,\n    n = $('<div/>', {\n      'class': settings.oClasses.sInfo,\n      'id': !nodes ? tid + '_info' : null\n    });\n  if (!nodes) {\n    // Update display on each draw\n    settings.aoDrawCallback.push({\n      \"fn\": _fnUpdateInfo,\n      \"sName\": \"information\"\n    });\n    n.attr('role', 'status').attr('aria-live', 'polite');\n\n    // Table is described by our info div\n    $(settings.nTable).attr('aria-describedby', tid + '_info');\n  }\n  return n[0];\n}\n\n/**\n * Update the information elements in the display\n *  @param {object} settings dataTables settings object\n *  @memberof DataTable#oApi\n */\nfunction _fnUpdateInfo(settings) {\n  /* Show information about the table */\n  var nodes = settings.aanFeatures.i;\n  if (nodes.length === 0) {\n    return;\n  }\n  var lang = settings.oLanguage,\n    start = settings._iDisplayStart + 1,\n    end = settings.fnDisplayEnd(),\n    max = settings.fnRecordsTotal(),\n    total = settings.fnRecordsDisplay(),\n    out = total ? lang.sInfo : lang.sInfoEmpty;\n  if (total !== max) {\n    /* Record set after filtering */\n    out += ' ' + lang.sInfoFiltered;\n  }\n\n  // Convert the macros\n  out += lang.sInfoPostFix;\n  out = _fnInfoMacros(settings, out);\n  var callback = lang.fnInfoCallback;\n  if (callback !== null) {\n    out = callback.call(settings.oInstance, settings, start, end, max, total, out);\n  }\n  $(nodes).html(out);\n}\nfunction _fnInfoMacros(settings, str) {\n  // When infinite scrolling, we are always starting at 1. _iDisplayStart is used only\n  // internally\n  var formatter = settings.fnFormatNumber,\n    start = settings._iDisplayStart + 1,\n    len = settings._iDisplayLength,\n    vis = settings.fnRecordsDisplay(),\n    all = len === -1;\n  return str.replace(/_START_/g, formatter.call(settings, start)).replace(/_END_/g, formatter.call(settings, settings.fnDisplayEnd())).replace(/_MAX_/g, formatter.call(settings, settings.fnRecordsTotal())).replace(/_TOTAL_/g, formatter.call(settings, vis)).replace(/_PAGE_/g, formatter.call(settings, all ? 1 : Math.ceil(start / len))).replace(/_PAGES_/g, formatter.call(settings, all ? 1 : Math.ceil(vis / len)));\n}\n\n/**\n * Draw the table for the first time, adding all required features\n *  @param {object} settings dataTables settings object\n *  @memberof DataTable#oApi\n */\nfunction _fnInitialise(settings) {\n  var i,\n    iLen,\n    iAjaxStart = settings.iInitDisplayStart;\n  var columns = settings.aoColumns,\n    column;\n  var features = settings.oFeatures;\n  var deferLoading = settings.bDeferLoading; // value modified by the draw\n\n  /* Ensure that the table data is fully initialised */\n  if (!settings.bInitialised) {\n    setTimeout(function () {\n      _fnInitialise(settings);\n    }, 200);\n    return;\n  }\n\n  /* Show the display HTML options */\n  _fnAddOptionsHtml(settings);\n\n  /* Build and draw the header / footer for the table */\n  _fnBuildHead(settings);\n  _fnDrawHead(settings, settings.aoHeader);\n  _fnDrawHead(settings, settings.aoFooter);\n\n  /* Okay to show that something is going on now */\n  _fnProcessingDisplay(settings, true);\n\n  /* Calculate sizes for columns */\n  if (features.bAutoWidth) {\n    _fnCalculateColumnWidths(settings);\n  }\n  for (i = 0, iLen = columns.length; i < iLen; i++) {\n    column = columns[i];\n    if (column.sWidth) {\n      column.nTh.style.width = _fnStringToCss(column.sWidth);\n    }\n  }\n  _fnCallbackFire(settings, null, 'preInit', [settings]);\n\n  // If there is default sorting required - let's do it. The sort function\n  // will do the drawing for us. Otherwise we draw the table regardless of the\n  // Ajax source - this allows the table to look initialised for Ajax sourcing\n  // data (show 'loading' message possibly)\n  _fnReDraw(settings);\n\n  // Server-side processing init complete is done by _fnAjaxUpdateDraw\n  var dataSrc = _fnDataSource(settings);\n  if (dataSrc != 'ssp' || deferLoading) {\n    // if there is an ajax source load the data\n    if (dataSrc == 'ajax') {\n      _fnBuildAjax(settings, [], function (json) {\n        var aData = _fnAjaxDataSrc(settings, json);\n\n        // Got the data - add it to the table\n        for (i = 0; i < aData.length; i++) {\n          _fnAddData(settings, aData[i]);\n        }\n\n        // Reset the init display for cookie saving. We've already done\n        // a filter, and therefore cleared it before. So we need to make\n        // it appear 'fresh'\n        settings.iInitDisplayStart = iAjaxStart;\n        _fnReDraw(settings);\n        _fnProcessingDisplay(settings, false);\n        _fnInitComplete(settings, json);\n      }, settings);\n    } else {\n      _fnProcessingDisplay(settings, false);\n      _fnInitComplete(settings);\n    }\n  }\n}\n\n/**\n * Draw the table for the first time, adding all required features\n *  @param {object} oSettings dataTables settings object\n *  @param {object} [json] JSON from the server that completed the table, if using Ajax source\n *    with client-side processing (optional)\n *  @memberof DataTable#oApi\n */\nfunction _fnInitComplete(settings, json) {\n  settings._bInitComplete = true;\n\n  // When data was added after the initialisation (data or Ajax) we need to\n  // calculate the column sizing\n  if (json || settings.oInit.aaData) {\n    _fnAdjustColumnSizing(settings);\n  }\n  _fnCallbackFire(settings, null, 'plugin-init', [settings, json]);\n  _fnCallbackFire(settings, 'aoInitComplete', 'init', [settings, json]);\n}\nfunction _fnLengthChange(settings, val) {\n  var len = parseInt(val, 10);\n  settings._iDisplayLength = len;\n  _fnLengthOverflow(settings);\n\n  // Fire length change event\n  _fnCallbackFire(settings, null, 'length', [settings, len]);\n}\n\n/**\n * Generate the node required for user display length changing\n *  @param {object} settings dataTables settings object\n *  @returns {node} Display length feature node\n *  @memberof DataTable#oApi\n */\nfunction _fnFeatureHtmlLength(settings) {\n  var classes = settings.oClasses,\n    tableId = settings.sTableId,\n    menu = settings.aLengthMenu,\n    d2 = Array.isArray(menu[0]),\n    lengths = d2 ? menu[0] : menu,\n    language = d2 ? menu[1] : menu;\n  var select = $('<select/>', {\n    'name': tableId + '_length',\n    'aria-controls': tableId,\n    'class': classes.sLengthSelect\n  });\n  for (var i = 0, ien = lengths.length; i < ien; i++) {\n    select[0][i] = new Option(typeof language[i] === 'number' ? settings.fnFormatNumber(language[i]) : language[i], lengths[i]);\n  }\n  var div = $('<div><label/></div>').addClass(classes.sLength);\n  if (!settings.aanFeatures.l) {\n    div[0].id = tableId + '_length';\n  }\n  div.children().append(settings.oLanguage.sLengthMenu.replace('_MENU_', select[0].outerHTML));\n\n  // Can't use `select` variable as user might provide their own and the\n  // reference is broken by the use of outerHTML\n  $('select', div).val(settings._iDisplayLength).on('change.DT', function (e) {\n    _fnLengthChange(settings, $(this).val());\n    _fnDraw(settings);\n  });\n\n  // Update node value whenever anything changes the table's length\n  $(settings.nTable).on('length.dt.DT', function (e, s, len) {\n    if (settings === s) {\n      $('select', div).val(len);\n    }\n  });\n  return div[0];\n}\n\n/* * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *\n * Note that most of the paging logic is done in\n * DataTable.ext.pager\n */\n\n/**\n * Generate the node required for default pagination\n *  @param {object} oSettings dataTables settings object\n *  @returns {node} Pagination feature node\n *  @memberof DataTable#oApi\n */\nfunction _fnFeatureHtmlPaginate(settings) {\n  var type = settings.sPaginationType,\n    plugin = DataTable.ext.pager[type],\n    modern = typeof plugin === 'function',\n    redraw = function (settings) {\n      _fnDraw(settings);\n    },\n    node = $('<div/>').addClass(settings.oClasses.sPaging + type)[0],\n    features = settings.aanFeatures;\n  if (!modern) {\n    plugin.fnInit(settings, node, redraw);\n  }\n\n  /* Add a draw callback for the pagination on first instance, to update the paging display */\n  if (!features.p) {\n    node.id = settings.sTableId + '_paginate';\n    settings.aoDrawCallback.push({\n      \"fn\": function (settings) {\n        if (modern) {\n          var start = settings._iDisplayStart,\n            len = settings._iDisplayLength,\n            visRecords = settings.fnRecordsDisplay(),\n            all = len === -1,\n            page = all ? 0 : Math.ceil(start / len),\n            pages = all ? 1 : Math.ceil(visRecords / len),\n            buttons = plugin(page, pages),\n            i,\n            ien;\n          for (i = 0, ien = features.p.length; i < ien; i++) {\n            _fnRenderer(settings, 'pageButton')(settings, features.p[i], i, buttons, page, pages);\n          }\n        } else {\n          plugin.fnUpdate(settings, redraw);\n        }\n      },\n      \"sName\": \"pagination\"\n    });\n  }\n  return node;\n}\n\n/**\n * Alter the display settings to change the page\n *  @param {object} settings DataTables settings object\n *  @param {string|int} action Paging action to take: \"first\", \"previous\",\n *    \"next\" or \"last\" or page number to jump to (integer)\n *  @param [bool] redraw Automatically draw the update or not\n *  @returns {bool} true page has changed, false - no change\n *  @memberof DataTable#oApi\n */\nfunction _fnPageChange(settings, action, redraw) {\n  var start = settings._iDisplayStart,\n    len = settings._iDisplayLength,\n    records = settings.fnRecordsDisplay();\n  if (records === 0 || len === -1) {\n    start = 0;\n  } else if (typeof action === \"number\") {\n    start = action * len;\n    if (start > records) {\n      start = 0;\n    }\n  } else if (action == \"first\") {\n    start = 0;\n  } else if (action == \"previous\") {\n    start = len >= 0 ? start - len : 0;\n    if (start < 0) {\n      start = 0;\n    }\n  } else if (action == \"next\") {\n    if (start + len < records) {\n      start += len;\n    }\n  } else if (action == \"last\") {\n    start = Math.floor((records - 1) / len) * len;\n  } else {\n    _fnLog(settings, 0, \"Unknown paging action: \" + action, 5);\n  }\n  var changed = settings._iDisplayStart !== start;\n  settings._iDisplayStart = start;\n  if (changed) {\n    _fnCallbackFire(settings, null, 'page', [settings]);\n    if (redraw) {\n      _fnDraw(settings);\n    }\n  } else {\n    // No change event - paging was called, but no change\n    _fnCallbackFire(settings, null, 'page-nc', [settings]);\n  }\n  return changed;\n}\n\n/**\n * Generate the node required for the processing node\n *  @param {object} settings dataTables settings object\n *  @returns {node} Processing element\n *  @memberof DataTable#oApi\n */\nfunction _fnFeatureHtmlProcessing(settings) {\n  return $('<div/>', {\n    'id': !settings.aanFeatures.r ? settings.sTableId + '_processing' : null,\n    'class': settings.oClasses.sProcessing,\n    'role': 'status'\n  }).html(settings.oLanguage.sProcessing).append('<div><div></div><div></div><div></div><div></div></div>').insertBefore(settings.nTable)[0];\n}\n\n/**\n * Display or hide the processing indicator\n *  @param {object} settings dataTables settings object\n *  @param {bool} show Show the processing indicator (true) or not (false)\n *  @memberof DataTable#oApi\n */\nfunction _fnProcessingDisplay(settings, show) {\n  if (settings.oFeatures.bProcessing) {\n    $(settings.aanFeatures.r).css('display', show ? 'block' : 'none');\n  }\n  _fnCallbackFire(settings, null, 'processing', [settings, show]);\n}\n\n/**\n * Add any control elements for the table - specifically scrolling\n *  @param {object} settings dataTables settings object\n *  @returns {node} Node to add to the DOM\n *  @memberof DataTable#oApi\n */\nfunction _fnFeatureHtmlTable(settings) {\n  var table = $(settings.nTable);\n\n  // Scrolling from here on in\n  var scroll = settings.oScroll;\n  if (scroll.sX === '' && scroll.sY === '') {\n    return settings.nTable;\n  }\n  var scrollX = scroll.sX;\n  var scrollY = scroll.sY;\n  var classes = settings.oClasses;\n  var caption = table.children('caption');\n  var captionSide = caption.length ? caption[0]._captionSide : null;\n  var headerClone = $(table[0].cloneNode(false));\n  var footerClone = $(table[0].cloneNode(false));\n  var footer = table.children('tfoot');\n  var _div = '<div/>';\n  var size = function (s) {\n    return !s ? null : _fnStringToCss(s);\n  };\n  if (!footer.length) {\n    footer = null;\n  }\n\n  /*\n   * The HTML structure that we want to generate in this function is:\n   *  div - scroller\n   *    div - scroll head\n   *      div - scroll head inner\n   *        table - scroll head table\n   *          thead - thead\n   *    div - scroll body\n   *      table - table (master table)\n   *        thead - thead clone for sizing\n   *        tbody - tbody\n   *    div - scroll foot\n   *      div - scroll foot inner\n   *        table - scroll foot table\n   *          tfoot - tfoot\n   */\n  var scroller = $(_div, {\n    'class': classes.sScrollWrapper\n  }).append($(_div, {\n    'class': classes.sScrollHead\n  }).css({\n    overflow: 'hidden',\n    position: 'relative',\n    border: 0,\n    width: scrollX ? size(scrollX) : '100%'\n  }).append($(_div, {\n    'class': classes.sScrollHeadInner\n  }).css({\n    'box-sizing': 'content-box',\n    width: scroll.sXInner || '100%'\n  }).append(headerClone.removeAttr('id').css('margin-left', 0).append(captionSide === 'top' ? caption : null).append(table.children('thead'))))).append($(_div, {\n    'class': classes.sScrollBody\n  }).css({\n    position: 'relative',\n    overflow: 'auto',\n    width: size(scrollX)\n  }).append(table));\n  if (footer) {\n    scroller.append($(_div, {\n      'class': classes.sScrollFoot\n    }).css({\n      overflow: 'hidden',\n      border: 0,\n      width: scrollX ? size(scrollX) : '100%'\n    }).append($(_div, {\n      'class': classes.sScrollFootInner\n    }).append(footerClone.removeAttr('id').css('margin-left', 0).append(captionSide === 'bottom' ? caption : null).append(table.children('tfoot')))));\n  }\n  var children = scroller.children();\n  var scrollHead = children[0];\n  var scrollBody = children[1];\n  var scrollFoot = footer ? children[2] : null;\n\n  // When the body is scrolled, then we also want to scroll the headers\n  if (scrollX) {\n    $(scrollBody).on('scroll.DT', function (e) {\n      var scrollLeft = this.scrollLeft;\n      scrollHead.scrollLeft = scrollLeft;\n      if (footer) {\n        scrollFoot.scrollLeft = scrollLeft;\n      }\n    });\n  }\n  $(scrollBody).css('max-height', scrollY);\n  if (!scroll.bCollapse) {\n    $(scrollBody).css('height', scrollY);\n  }\n  settings.nScrollHead = scrollHead;\n  settings.nScrollBody = scrollBody;\n  settings.nScrollFoot = scrollFoot;\n\n  // On redraw - align columns\n  settings.aoDrawCallback.push({\n    \"fn\": _fnScrollDraw,\n    \"sName\": \"scrolling\"\n  });\n  return scroller[0];\n}\n\n/**\n * Update the header, footer and body tables for resizing - i.e. column\n * alignment.\n *\n * Welcome to the most horrible function DataTables. The process that this\n * function follows is basically:\n *   1. Re-create the table inside the scrolling div\n *   2. Take live measurements from the DOM\n *   3. Apply the measurements to align the columns\n *   4. Clean up\n *\n *  @param {object} settings dataTables settings object\n *  @memberof DataTable#oApi\n */\nfunction _fnScrollDraw(settings) {\n  // Given that this is such a monster function, a lot of variables are use\n  // to try and keep the minimised size as small as possible\n  var scroll = settings.oScroll,\n    scrollX = scroll.sX,\n    scrollXInner = scroll.sXInner,\n    scrollY = scroll.sY,\n    barWidth = scroll.iBarWidth,\n    divHeader = $(settings.nScrollHead),\n    divHeaderStyle = divHeader[0].style,\n    divHeaderInner = divHeader.children('div'),\n    divHeaderInnerStyle = divHeaderInner[0].style,\n    divHeaderTable = divHeaderInner.children('table'),\n    divBodyEl = settings.nScrollBody,\n    divBody = $(divBodyEl),\n    divBodyStyle = divBodyEl.style,\n    divFooter = $(settings.nScrollFoot),\n    divFooterInner = divFooter.children('div'),\n    divFooterTable = divFooterInner.children('table'),\n    header = $(settings.nTHead),\n    table = $(settings.nTable),\n    tableEl = table[0],\n    tableStyle = tableEl.style,\n    footer = settings.nTFoot ? $(settings.nTFoot) : null,\n    browser = settings.oBrowser,\n    ie67 = browser.bScrollOversize,\n    dtHeaderCells = _pluck(settings.aoColumns, 'nTh'),\n    headerTrgEls,\n    footerTrgEls,\n    headerSrcEls,\n    footerSrcEls,\n    headerCopy,\n    footerCopy,\n    headerWidths = [],\n    footerWidths = [],\n    headerContent = [],\n    footerContent = [],\n    idx,\n    correction,\n    sanityWidth,\n    zeroOut = function (nSizer) {\n      var style = nSizer.style;\n      style.paddingTop = \"0\";\n      style.paddingBottom = \"0\";\n      style.borderTopWidth = \"0\";\n      style.borderBottomWidth = \"0\";\n      style.height = 0;\n    };\n\n  // If the scrollbar visibility has changed from the last draw, we need to\n  // adjust the column sizes as the table width will have changed to account\n  // for the scrollbar\n  var scrollBarVis = divBodyEl.scrollHeight > divBodyEl.clientHeight;\n  if (settings.scrollBarVis !== scrollBarVis && settings.scrollBarVis !== undefined) {\n    settings.scrollBarVis = scrollBarVis;\n    _fnAdjustColumnSizing(settings);\n    return; // adjust column sizing will call this function again\n  } else {\n    settings.scrollBarVis = scrollBarVis;\n  }\n\n  /*\n   * 1. Re-create the table inside the scrolling div\n   */\n\n  // Remove the old minimised thead and tfoot elements in the inner table\n  table.children('thead, tfoot').remove();\n  if (footer) {\n    footerCopy = footer.clone().prependTo(table);\n    footerTrgEls = footer.find('tr'); // the original tfoot is in its own table and must be sized\n    footerSrcEls = footerCopy.find('tr');\n    footerCopy.find('[id]').removeAttr('id');\n  }\n\n  // Clone the current header and footer elements and then place it into the inner table\n  headerCopy = header.clone().prependTo(table);\n  headerTrgEls = header.find('tr'); // original header is in its own table\n  headerSrcEls = headerCopy.find('tr');\n  headerCopy.find('th, td').removeAttr('tabindex');\n  headerCopy.find('[id]').removeAttr('id');\n\n  /*\n   * 2. Take live measurements from the DOM - do not alter the DOM itself!\n   */\n\n  // Remove old sizing and apply the calculated column widths\n  // Get the unique column headers in the newly created (cloned) header. We want to apply the\n  // calculated sizes to this header\n  if (!scrollX) {\n    divBodyStyle.width = '100%';\n    divHeader[0].style.width = '100%';\n  }\n  $.each(_fnGetUniqueThs(settings, headerCopy), function (i, el) {\n    idx = _fnVisibleToColumnIndex(settings, i);\n    el.style.width = settings.aoColumns[idx].sWidth;\n  });\n  if (footer) {\n    _fnApplyToChildren(function (n) {\n      n.style.width = \"\";\n    }, footerSrcEls);\n  }\n\n  // Size the table as a whole\n  sanityWidth = table.outerWidth();\n  if (scrollX === \"\") {\n    // No x scrolling\n    tableStyle.width = \"100%\";\n\n    // IE7 will make the width of the table when 100% include the scrollbar\n    // - which is shouldn't. When there is a scrollbar we need to take this\n    // into account.\n    if (ie67 && (table.find('tbody').height() > divBodyEl.offsetHeight || divBody.css('overflow-y') == \"scroll\")) {\n      tableStyle.width = _fnStringToCss(table.outerWidth() - barWidth);\n    }\n\n    // Recalculate the sanity width\n    sanityWidth = table.outerWidth();\n  } else if (scrollXInner !== \"\") {\n    // legacy x scroll inner has been given - use it\n    tableStyle.width = _fnStringToCss(scrollXInner);\n\n    // Recalculate the sanity width\n    sanityWidth = table.outerWidth();\n  }\n\n  // Hidden header should have zero height, so remove padding and borders. Then\n  // set the width based on the real headers\n\n  // Apply all styles in one pass\n  _fnApplyToChildren(zeroOut, headerSrcEls);\n\n  // Read all widths in next pass\n  _fnApplyToChildren(function (nSizer) {\n    var style = window.getComputedStyle ? window.getComputedStyle(nSizer).width : _fnStringToCss($(nSizer).width());\n    headerContent.push(nSizer.innerHTML);\n    headerWidths.push(style);\n  }, headerSrcEls);\n\n  // Apply all widths in final pass\n  _fnApplyToChildren(function (nToSize, i) {\n    nToSize.style.width = headerWidths[i];\n  }, headerTrgEls);\n  $(headerSrcEls).css('height', 0);\n\n  /* Same again with the footer if we have one */\n  if (footer) {\n    _fnApplyToChildren(zeroOut, footerSrcEls);\n    _fnApplyToChildren(function (nSizer) {\n      footerContent.push(nSizer.innerHTML);\n      footerWidths.push(_fnStringToCss($(nSizer).css('width')));\n    }, footerSrcEls);\n    _fnApplyToChildren(function (nToSize, i) {\n      nToSize.style.width = footerWidths[i];\n    }, footerTrgEls);\n    $(footerSrcEls).height(0);\n  }\n\n  /*\n   * 3. Apply the measurements\n   */\n\n  // \"Hide\" the header and footer that we used for the sizing. We need to keep\n  // the content of the cell so that the width applied to the header and body\n  // both match, but we want to hide it completely. We want to also fix their\n  // width to what they currently are\n  _fnApplyToChildren(function (nSizer, i) {\n    nSizer.innerHTML = '<div class=\"dataTables_sizing\">' + headerContent[i] + '</div>';\n    nSizer.childNodes[0].style.height = \"0\";\n    nSizer.childNodes[0].style.overflow = \"hidden\";\n    nSizer.style.width = headerWidths[i];\n  }, headerSrcEls);\n  if (footer) {\n    _fnApplyToChildren(function (nSizer, i) {\n      nSizer.innerHTML = '<div class=\"dataTables_sizing\">' + footerContent[i] + '</div>';\n      nSizer.childNodes[0].style.height = \"0\";\n      nSizer.childNodes[0].style.overflow = \"hidden\";\n      nSizer.style.width = footerWidths[i];\n    }, footerSrcEls);\n  }\n\n  // Sanity check that the table is of a sensible width. If not then we are going to get\n  // misalignment - try to prevent this by not allowing the table to shrink below its min width\n  if (Math.round(table.outerWidth()) < Math.round(sanityWidth)) {\n    // The min width depends upon if we have a vertical scrollbar visible or not */\n    correction = divBodyEl.scrollHeight > divBodyEl.offsetHeight || divBody.css('overflow-y') == \"scroll\" ? sanityWidth + barWidth : sanityWidth;\n\n    // IE6/7 are a law unto themselves...\n    if (ie67 && (divBodyEl.scrollHeight > divBodyEl.offsetHeight || divBody.css('overflow-y') == \"scroll\")) {\n      tableStyle.width = _fnStringToCss(correction - barWidth);\n    }\n\n    // And give the user a warning that we've stopped the table getting too small\n    if (scrollX === \"\" || scrollXInner !== \"\") {\n      _fnLog(settings, 1, 'Possible column misalignment', 6);\n    }\n  } else {\n    correction = '100%';\n  }\n\n  // Apply to the container elements\n  divBodyStyle.width = _fnStringToCss(correction);\n  divHeaderStyle.width = _fnStringToCss(correction);\n  if (footer) {\n    settings.nScrollFoot.style.width = _fnStringToCss(correction);\n  }\n\n  /*\n   * 4. Clean up\n   */\n  if (!scrollY) {\n    /* IE7< puts a vertical scrollbar in place (when it shouldn't be) due to subtracting\n     * the scrollbar height from the visible display, rather than adding it on. We need to\n     * set the height in order to sort this. Don't want to do it in any other browsers.\n     */\n    if (ie67) {\n      divBodyStyle.height = _fnStringToCss(tableEl.offsetHeight + barWidth);\n    }\n  }\n\n  /* Finally set the width's of the header and footer tables */\n  var iOuterWidth = table.outerWidth();\n  divHeaderTable[0].style.width = _fnStringToCss(iOuterWidth);\n  divHeaderInnerStyle.width = _fnStringToCss(iOuterWidth);\n\n  // Figure out if there are scrollbar present - if so then we need a the header and footer to\n  // provide a bit more space to allow \"overflow\" scrolling (i.e. past the scrollbar)\n  var bScrolling = table.height() > divBodyEl.clientHeight || divBody.css('overflow-y') == \"scroll\";\n  var padding = 'padding' + (browser.bScrollbarLeft ? 'Left' : 'Right');\n  divHeaderInnerStyle[padding] = bScrolling ? barWidth + \"px\" : \"0px\";\n  if (footer) {\n    divFooterTable[0].style.width = _fnStringToCss(iOuterWidth);\n    divFooterInner[0].style.width = _fnStringToCss(iOuterWidth);\n    divFooterInner[0].style[padding] = bScrolling ? barWidth + \"px\" : \"0px\";\n  }\n\n  // Correct DOM ordering for colgroup - comes before the thead\n  table.children('colgroup').insertBefore(table.children('thead'));\n\n  /* Adjust the position of the header in case we loose the y-scrollbar */\n  divBody.trigger('scroll');\n\n  // If sorting or filtering has occurred, jump the scrolling back to the top\n  // only if we aren't holding the position\n  if ((settings.bSorted || settings.bFiltered) && !settings._drawHold) {\n    divBodyEl.scrollTop = 0;\n  }\n}\n\n/**\n * Apply a given function to the display child nodes of an element array (typically\n * TD children of TR rows\n *  @param {function} fn Method to apply to the objects\n *  @param array {nodes} an1 List of elements to look through for display children\n *  @param array {nodes} an2 Another list (identical structure to the first) - optional\n *  @memberof DataTable#oApi\n */\nfunction _fnApplyToChildren(fn, an1, an2) {\n  var index = 0,\n    i = 0,\n    iLen = an1.length;\n  var nNode1, nNode2;\n  while (i < iLen) {\n    nNode1 = an1[i].firstChild;\n    nNode2 = an2 ? an2[i].firstChild : null;\n    while (nNode1) {\n      if (nNode1.nodeType === 1) {\n        if (an2) {\n          fn(nNode1, nNode2, index);\n        } else {\n          fn(nNode1, index);\n        }\n        index++;\n      }\n      nNode1 = nNode1.nextSibling;\n      nNode2 = an2 ? nNode2.nextSibling : null;\n    }\n    i++;\n  }\n}\nvar __re_html_remove = /<.*?>/g;\n\n/**\n * Calculate the width of columns for the table\n *  @param {object} oSettings dataTables settings object\n *  @memberof DataTable#oApi\n */\nfunction _fnCalculateColumnWidths(oSettings) {\n  var table = oSettings.nTable,\n    columns = oSettings.aoColumns,\n    scroll = oSettings.oScroll,\n    scrollY = scroll.sY,\n    scrollX = scroll.sX,\n    scrollXInner = scroll.sXInner,\n    columnCount = columns.length,\n    visibleColumns = _fnGetColumns(oSettings, 'bVisible'),\n    headerCells = $('th', oSettings.nTHead),\n    tableWidthAttr = table.getAttribute('width'),\n    // from DOM element\n    tableContainer = table.parentNode,\n    userInputs = false,\n    i,\n    column,\n    columnIdx,\n    width,\n    outerWidth,\n    browser = oSettings.oBrowser,\n    ie67 = browser.bScrollOversize;\n  var styleWidth = table.style.width;\n  if (styleWidth && styleWidth.indexOf('%') !== -1) {\n    tableWidthAttr = styleWidth;\n  }\n\n  /* Convert any user input sizes into pixel sizes */\n  var sizes = _fnConvertToWidth(_pluck(columns, 'sWidthOrig'), tableContainer);\n  for (i = 0; i < visibleColumns.length; i++) {\n    column = columns[visibleColumns[i]];\n    if (column.sWidth !== null) {\n      column.sWidth = sizes[i];\n      userInputs = true;\n    }\n  }\n\n  /* If the number of columns in the DOM equals the number that we have to\n   * process in DataTables, then we can use the offsets that are created by\n   * the web- browser. No custom sizes can be set in order for this to happen,\n   * nor scrolling used\n   */\n  if (ie67 || !userInputs && !scrollX && !scrollY && columnCount == _fnVisbleColumns(oSettings) && columnCount == headerCells.length) {\n    for (i = 0; i < columnCount; i++) {\n      var colIdx = _fnVisibleToColumnIndex(oSettings, i);\n      if (colIdx !== null) {\n        columns[colIdx].sWidth = _fnStringToCss(headerCells.eq(i).width());\n      }\n    }\n  } else {\n    // Otherwise construct a single row, worst case, table with the widest\n    // node in the data, assign any user defined widths, then insert it into\n    // the DOM and allow the browser to do all the hard work of calculating\n    // table widths\n    var tmpTable = $(table).clone() // don't use cloneNode - IE8 will remove events on the main table\n    .css('visibility', 'hidden').removeAttr('id');\n\n    // Clean up the table body\n    tmpTable.find('tbody tr').remove();\n    var tr = $('<tr/>').appendTo(tmpTable.find('tbody'));\n\n    // Clone the table header and footer - we can't use the header / footer\n    // from the cloned table, since if scrolling is active, the table's\n    // real header and footer are contained in different table tags\n    tmpTable.find('thead, tfoot').remove();\n    tmpTable.append($(oSettings.nTHead).clone()).append($(oSettings.nTFoot).clone());\n\n    // Remove any assigned widths from the footer (from scrolling)\n    tmpTable.find('tfoot th, tfoot td').css('width', '');\n\n    // Apply custom sizing to the cloned header\n    headerCells = _fnGetUniqueThs(oSettings, tmpTable.find('thead')[0]);\n    for (i = 0; i < visibleColumns.length; i++) {\n      column = columns[visibleColumns[i]];\n      headerCells[i].style.width = column.sWidthOrig !== null && column.sWidthOrig !== '' ? _fnStringToCss(column.sWidthOrig) : '';\n\n      // For scrollX we need to force the column width otherwise the\n      // browser will collapse it. If this width is smaller than the\n      // width the column requires, then it will have no effect\n      if (column.sWidthOrig && scrollX) {\n        $(headerCells[i]).append($('<div/>').css({\n          width: column.sWidthOrig,\n          margin: 0,\n          padding: 0,\n          border: 0,\n          height: 1\n        }));\n      }\n    }\n\n    // Find the widest cell for each column and put it into the table\n    if (oSettings.aoData.length) {\n      for (i = 0; i < visibleColumns.length; i++) {\n        columnIdx = visibleColumns[i];\n        column = columns[columnIdx];\n        $(_fnGetWidestNode(oSettings, columnIdx)).clone(false).append(column.sContentPadding).appendTo(tr);\n      }\n    }\n\n    // Tidy the temporary table - remove name attributes so there aren't\n    // duplicated in the dom (radio elements for example)\n    $('[name]', tmpTable).removeAttr('name');\n\n    // Table has been built, attach to the document so we can work with it.\n    // A holding element is used, positioned at the top of the container\n    // with minimal height, so it has no effect on if the container scrolls\n    // or not. Otherwise it might trigger scrolling when it actually isn't\n    // needed\n    var holder = $('<div/>').css(scrollX || scrollY ? {\n      position: 'absolute',\n      top: 0,\n      left: 0,\n      height: 1,\n      right: 0,\n      overflow: 'hidden'\n    } : {}).append(tmpTable).appendTo(tableContainer);\n\n    // When scrolling (X or Y) we want to set the width of the table as \n    // appropriate. However, when not scrolling leave the table width as it\n    // is. This results in slightly different, but I think correct behaviour\n    if (scrollX && scrollXInner) {\n      tmpTable.width(scrollXInner);\n    } else if (scrollX) {\n      tmpTable.css('width', 'auto');\n      tmpTable.removeAttr('width');\n\n      // If there is no width attribute or style, then allow the table to\n      // collapse\n      if (tmpTable.width() < tableContainer.clientWidth && tableWidthAttr) {\n        tmpTable.width(tableContainer.clientWidth);\n      }\n    } else if (scrollY) {\n      tmpTable.width(tableContainer.clientWidth);\n    } else if (tableWidthAttr) {\n      tmpTable.width(tableWidthAttr);\n    }\n\n    // Get the width of each column in the constructed table - we need to\n    // know the inner width (so it can be assigned to the other table's\n    // cells) and the outer width so we can calculate the full width of the\n    // table. This is safe since DataTables requires a unique cell for each\n    // column, but if ever a header can span multiple columns, this will\n    // need to be modified.\n    var total = 0;\n    for (i = 0; i < visibleColumns.length; i++) {\n      var cell = $(headerCells[i]);\n      var border = cell.outerWidth() - cell.width();\n\n      // Use getBounding... where possible (not IE8-) because it can give\n      // sub-pixel accuracy, which we then want to round up!\n      var bounding = browser.bBounding ? Math.ceil(headerCells[i].getBoundingClientRect().width) : cell.outerWidth();\n\n      // Total is tracked to remove any sub-pixel errors as the outerWidth\n      // of the table might not equal the total given here (IE!).\n      total += bounding;\n\n      // Width for each column to use\n      columns[visibleColumns[i]].sWidth = _fnStringToCss(bounding - border);\n    }\n    table.style.width = _fnStringToCss(total);\n\n    // Finished with the table - ditch it\n    holder.remove();\n  }\n\n  // If there is a width attr, we want to attach an event listener which\n  // allows the table sizing to automatically adjust when the window is\n  // resized. Use the width attr rather than CSS, since we can't know if the\n  // CSS is a relative value or absolute - DOM read is always px.\n  if (tableWidthAttr) {\n    table.style.width = _fnStringToCss(tableWidthAttr);\n  }\n  if ((tableWidthAttr || scrollX) && !oSettings._reszEvt) {\n    var bindResize = function () {\n      $(window).on('resize.DT-' + oSettings.sInstance, _fnThrottle(function () {\n        _fnAdjustColumnSizing(oSettings);\n      }));\n    };\n\n    // IE6/7 will crash if we bind a resize event handler on page load.\n    // To be removed in 1.11 which drops IE6/7 support\n    if (ie67) {\n      setTimeout(bindResize, 1000);\n    } else {\n      bindResize();\n    }\n    oSettings._reszEvt = true;\n  }\n}\n\n/**\n * Throttle the calls to a function. Arguments and context are maintained for\n * the throttled function\n *  @param {function} fn Function to be called\n *  @param {int} [freq=200] call frequency in mS\n *  @returns {function} wrapped function\n *  @memberof DataTable#oApi\n */\nvar _fnThrottle = DataTable.util.throttle;\n\n/**\n * Convert a set of CSS units width to pixels (e.g. 2em)\n *  @param {string[]} widths widths to be converted\n *  @param {node} parent parent to get the with for (required for relative widths) - optional\n *  @returns {int[]} widths in pixels\n *  @memberof DataTable#oApi\n */\nfunction _fnConvertToWidth(widths, parent) {\n  var els = [];\n  var results = [];\n\n  // Add the elements in a single loop so we only need to reflow once\n  for (var i = 0; i < widths.length; i++) {\n    if (widths[i]) {\n      els.push($('<div/>').css('width', _fnStringToCss(widths[i])).appendTo(parent || document.body));\n    } else {\n      els.push(null);\n    }\n  }\n\n  // Get the sizes (will reflow once)\n  for (var i = 0; i < widths.length; i++) {\n    results.push(els[i] ? els[i][0].offsetWidth : null);\n  }\n\n  // Tidy\n  $(els).remove();\n  return results;\n}\n\n/**\n * Get the widest node\n *  @param {object} settings dataTables settings object\n *  @param {int} colIdx column of interest\n *  @returns {node} widest table node\n *  @memberof DataTable#oApi\n */\nfunction _fnGetWidestNode(settings, colIdx) {\n  var idx = _fnGetMaxLenString(settings, colIdx);\n  if (idx < 0) {\n    return null;\n  }\n  var data = settings.aoData[idx];\n  return !data.nTr ?\n  // Might not have been created when deferred rendering\n  $('<td/>').html(_fnGetCellData(settings, idx, colIdx, 'display'))[0] : data.anCells[colIdx];\n}\n\n/**\n * Get the maximum strlen for each data column\n *  @param {object} settings dataTables settings object\n *  @param {int} colIdx column of interest\n *  @returns {string} max string length for each column\n *  @memberof DataTable#oApi\n */\nfunction _fnGetMaxLenString(settings, colIdx) {\n  var s,\n    max = -1,\n    maxIdx = -1;\n  for (var i = 0, ien = settings.aoData.length; i < ien; i++) {\n    s = _fnGetCellData(settings, i, colIdx, 'display') + '';\n    s = s.replace(__re_html_remove, '');\n    s = s.replace(/&nbsp;/g, ' ');\n    if (s.length > max) {\n      max = s.length;\n      maxIdx = i;\n    }\n  }\n  return maxIdx;\n}\n\n/**\n * Append a CSS unit (only if required) to a string\n *  @param {string} value to css-ify\n *  @returns {string} value with css unit\n *  @memberof DataTable#oApi\n */\nfunction _fnStringToCss(s) {\n  if (s === null) {\n    return '0px';\n  }\n  if (typeof s == 'number') {\n    return s < 0 ? '0px' : s + 'px';\n  }\n\n  // Check it has a unit character already\n  return s.match(/\\d$/) ? s + 'px' : s;\n}\nfunction _fnSortFlatten(settings) {\n  var i,\n    iLen,\n    k,\n    kLen,\n    aSort = [],\n    aiOrig = [],\n    aoColumns = settings.aoColumns,\n    aDataSort,\n    iCol,\n    sType,\n    srcCol,\n    fixed = settings.aaSortingFixed,\n    fixedObj = $.isPlainObject(fixed),\n    nestedSort = [],\n    add = function (a) {\n      if (a.length && !Array.isArray(a[0])) {\n        // 1D array\n        nestedSort.push(a);\n      } else {\n        // 2D array\n        $.merge(nestedSort, a);\n      }\n    };\n\n  // Build the sort array, with pre-fix and post-fix options if they have been\n  // specified\n  if (Array.isArray(fixed)) {\n    add(fixed);\n  }\n  if (fixedObj && fixed.pre) {\n    add(fixed.pre);\n  }\n  add(settings.aaSorting);\n  if (fixedObj && fixed.post) {\n    add(fixed.post);\n  }\n  for (i = 0; i < nestedSort.length; i++) {\n    srcCol = nestedSort[i][0];\n    aDataSort = aoColumns[srcCol].aDataSort;\n    for (k = 0, kLen = aDataSort.length; k < kLen; k++) {\n      iCol = aDataSort[k];\n      sType = aoColumns[iCol].sType || 'string';\n      if (nestedSort[i]._idx === undefined) {\n        nestedSort[i]._idx = $.inArray(nestedSort[i][1], aoColumns[iCol].asSorting);\n      }\n      aSort.push({\n        src: srcCol,\n        col: iCol,\n        dir: nestedSort[i][1],\n        index: nestedSort[i]._idx,\n        type: sType,\n        formatter: DataTable.ext.type.order[sType + \"-pre\"]\n      });\n    }\n  }\n  return aSort;\n}\n\n/**\n * Change the order of the table\n *  @param {object} oSettings dataTables settings object\n *  @memberof DataTable#oApi\n *  @todo This really needs split up!\n */\nfunction _fnSort(oSettings) {\n  var i,\n    ien,\n    iLen,\n    j,\n    jLen,\n    k,\n    kLen,\n    sDataType,\n    nTh,\n    aiOrig = [],\n    oExtSort = DataTable.ext.type.order,\n    aoData = oSettings.aoData,\n    aoColumns = oSettings.aoColumns,\n    aDataSort,\n    data,\n    iCol,\n    sType,\n    oSort,\n    formatters = 0,\n    sortCol,\n    displayMaster = oSettings.aiDisplayMaster,\n    aSort;\n\n  // Resolve any column types that are unknown due to addition or invalidation\n  // @todo Can this be moved into a 'data-ready' handler which is called when\n  //   data is going to be used in the table?\n  _fnColumnTypes(oSettings);\n  aSort = _fnSortFlatten(oSettings);\n  for (i = 0, ien = aSort.length; i < ien; i++) {\n    sortCol = aSort[i];\n\n    // Track if we can use the fast sort algorithm\n    if (sortCol.formatter) {\n      formatters++;\n    }\n\n    // Load the data needed for the sort, for each cell\n    _fnSortData(oSettings, sortCol.col);\n  }\n\n  /* No sorting required if server-side or no sorting array */\n  if (_fnDataSource(oSettings) != 'ssp' && aSort.length !== 0) {\n    // Create a value - key array of the current row positions such that we can use their\n    // current position during the sort, if values match, in order to perform stable sorting\n    for (i = 0, iLen = displayMaster.length; i < iLen; i++) {\n      aiOrig[displayMaster[i]] = i;\n    }\n\n    /* Do the sort - here we want multi-column sorting based on a given data source (column)\n     * and sorting function (from oSort) in a certain direction. It's reasonably complex to\n     * follow on it's own, but this is what we want (example two column sorting):\n     *  fnLocalSorting = function(a,b){\n     *    var iTest;\n     *    iTest = oSort['string-asc']('data11', 'data12');\n     *      if (iTest !== 0)\n     *        return iTest;\n     *    iTest = oSort['numeric-desc']('data21', 'data22');\n     *    if (iTest !== 0)\n     *      return iTest;\n     *    return oSort['numeric-asc']( aiOrig[a], aiOrig[b] );\n     *  }\n     * Basically we have a test for each sorting column, if the data in that column is equal,\n     * test the next column. If all columns match, then we use a numeric sort on the row\n     * positions in the original data array to provide a stable sort.\n     *\n     * Note - I know it seems excessive to have two sorting methods, but the first is around\n     * 15% faster, so the second is only maintained for backwards compatibility with sorting\n     * methods which do not have a pre-sort formatting function.\n     */\n    if (formatters === aSort.length) {\n      // All sort types have formatting functions\n      displayMaster.sort(function (a, b) {\n        var x,\n          y,\n          k,\n          test,\n          sort,\n          len = aSort.length,\n          dataA = aoData[a]._aSortData,\n          dataB = aoData[b]._aSortData;\n        for (k = 0; k < len; k++) {\n          sort = aSort[k];\n          x = dataA[sort.col];\n          y = dataB[sort.col];\n          test = x < y ? -1 : x > y ? 1 : 0;\n          if (test !== 0) {\n            return sort.dir === 'asc' ? test : -test;\n          }\n        }\n        x = aiOrig[a];\n        y = aiOrig[b];\n        return x < y ? -1 : x > y ? 1 : 0;\n      });\n    } else {\n      // Depreciated - remove in 1.11 (providing a plug-in option)\n      // Not all sort types have formatting methods, so we have to call their sorting\n      // methods.\n      displayMaster.sort(function (a, b) {\n        var x,\n          y,\n          k,\n          l,\n          test,\n          sort,\n          fn,\n          len = aSort.length,\n          dataA = aoData[a]._aSortData,\n          dataB = aoData[b]._aSortData;\n        for (k = 0; k < len; k++) {\n          sort = aSort[k];\n          x = dataA[sort.col];\n          y = dataB[sort.col];\n          fn = oExtSort[sort.type + \"-\" + sort.dir] || oExtSort[\"string-\" + sort.dir];\n          test = fn(x, y);\n          if (test !== 0) {\n            return test;\n          }\n        }\n        x = aiOrig[a];\n        y = aiOrig[b];\n        return x < y ? -1 : x > y ? 1 : 0;\n      });\n    }\n  }\n\n  /* Tell the draw function that we have sorted the data */\n  oSettings.bSorted = true;\n}\nfunction _fnSortAria(settings) {\n  var label;\n  var nextSort;\n  var columns = settings.aoColumns;\n  var aSort = _fnSortFlatten(settings);\n  var oAria = settings.oLanguage.oAria;\n\n  // ARIA attributes - need to loop all columns, to update all (removing old\n  // attributes as needed)\n  for (var i = 0, iLen = columns.length; i < iLen; i++) {\n    var col = columns[i];\n    var asSorting = col.asSorting;\n    var sTitle = col.ariaTitle || col.sTitle.replace(/<.*?>/g, \"\");\n    var th = col.nTh;\n\n    // IE7 is throwing an error when setting these properties with jQuery's\n    // attr() and removeAttr() methods...\n    th.removeAttribute('aria-sort');\n\n    /* In ARIA only the first sorting column can be marked as sorting - no multi-sort option */\n    if (col.bSortable) {\n      if (aSort.length > 0 && aSort[0].col == i) {\n        th.setAttribute('aria-sort', aSort[0].dir == \"asc\" ? \"ascending\" : \"descending\");\n        nextSort = asSorting[aSort[0].index + 1] || asSorting[0];\n      } else {\n        nextSort = asSorting[0];\n      }\n      label = sTitle + (nextSort === \"asc\" ? oAria.sSortAscending : oAria.sSortDescending);\n    } else {\n      label = sTitle;\n    }\n    th.setAttribute('aria-label', label);\n  }\n}\n\n/**\n * Function to run on user sort request\n *  @param {object} settings dataTables settings object\n *  @param {node} attachTo node to attach the handler to\n *  @param {int} colIdx column sorting index\n *  @param {boolean} [append=false] Append the requested sort to the existing\n *    sort if true (i.e. multi-column sort)\n *  @param {function} [callback] callback function\n *  @memberof DataTable#oApi\n */\nfunction _fnSortListener(settings, colIdx, append, callback) {\n  var col = settings.aoColumns[colIdx];\n  var sorting = settings.aaSorting;\n  var asSorting = col.asSorting;\n  var nextSortIdx;\n  var next = function (a, overflow) {\n    var idx = a._idx;\n    if (idx === undefined) {\n      idx = $.inArray(a[1], asSorting);\n    }\n    return idx + 1 < asSorting.length ? idx + 1 : overflow ? null : 0;\n  };\n\n  // Convert to 2D array if needed\n  if (typeof sorting[0] === 'number') {\n    sorting = settings.aaSorting = [sorting];\n  }\n\n  // If appending the sort then we are multi-column sorting\n  if (append && settings.oFeatures.bSortMulti) {\n    // Are we already doing some kind of sort on this column?\n    var sortIdx = $.inArray(colIdx, _pluck(sorting, '0'));\n    if (sortIdx !== -1) {\n      // Yes, modify the sort\n      nextSortIdx = next(sorting[sortIdx], true);\n      if (nextSortIdx === null && sorting.length === 1) {\n        nextSortIdx = 0; // can't remove sorting completely\n      }\n      if (nextSortIdx === null) {\n        sorting.splice(sortIdx, 1);\n      } else {\n        sorting[sortIdx][1] = asSorting[nextSortIdx];\n        sorting[sortIdx]._idx = nextSortIdx;\n      }\n    } else {\n      // No sort on this column yet\n      sorting.push([colIdx, asSorting[0], 0]);\n      sorting[sorting.length - 1]._idx = 0;\n    }\n  } else if (sorting.length && sorting[0][0] == colIdx) {\n    // Single column - already sorting on this column, modify the sort\n    nextSortIdx = next(sorting[0]);\n    sorting.length = 1;\n    sorting[0][1] = asSorting[nextSortIdx];\n    sorting[0]._idx = nextSortIdx;\n  } else {\n    // Single column - sort only on this column\n    sorting.length = 0;\n    sorting.push([colIdx, asSorting[0]]);\n    sorting[0]._idx = 0;\n  }\n\n  // Run the sort by calling a full redraw\n  _fnReDraw(settings);\n\n  // callback used for async user interaction\n  if (typeof callback == 'function') {\n    callback(settings);\n  }\n}\n\n/**\n * Attach a sort handler (click) to a node\n *  @param {object} settings dataTables settings object\n *  @param {node} attachTo node to attach the handler to\n *  @param {int} colIdx column sorting index\n *  @param {function} [callback] callback function\n *  @memberof DataTable#oApi\n */\nfunction _fnSortAttachListener(settings, attachTo, colIdx, callback) {\n  var col = settings.aoColumns[colIdx];\n  _fnBindAction(attachTo, {}, function (e) {\n    /* If the column is not sortable - don't to anything */\n    if (col.bSortable === false) {\n      return;\n    }\n\n    // If processing is enabled use a timeout to allow the processing\n    // display to be shown - otherwise to it synchronously\n    if (settings.oFeatures.bProcessing) {\n      _fnProcessingDisplay(settings, true);\n      setTimeout(function () {\n        _fnSortListener(settings, colIdx, e.shiftKey, callback);\n\n        // In server-side processing, the draw callback will remove the\n        // processing display\n        if (_fnDataSource(settings) !== 'ssp') {\n          _fnProcessingDisplay(settings, false);\n        }\n      }, 0);\n    } else {\n      _fnSortListener(settings, colIdx, e.shiftKey, callback);\n    }\n  });\n}\n\n/**\n * Set the sorting classes on table's body, Note: it is safe to call this function\n * when bSort and bSortClasses are false\n *  @param {object} oSettings dataTables settings object\n *  @memberof DataTable#oApi\n */\nfunction _fnSortingClasses(settings) {\n  var oldSort = settings.aLastSort;\n  var sortClass = settings.oClasses.sSortColumn;\n  var sort = _fnSortFlatten(settings);\n  var features = settings.oFeatures;\n  var i, ien, colIdx;\n  if (features.bSort && features.bSortClasses) {\n    // Remove old sorting classes\n    for (i = 0, ien = oldSort.length; i < ien; i++) {\n      colIdx = oldSort[i].src;\n\n      // Remove column sorting\n      $(_pluck(settings.aoData, 'anCells', colIdx)).removeClass(sortClass + (i < 2 ? i + 1 : 3));\n    }\n\n    // Add new column sorting\n    for (i = 0, ien = sort.length; i < ien; i++) {\n      colIdx = sort[i].src;\n      $(_pluck(settings.aoData, 'anCells', colIdx)).addClass(sortClass + (i < 2 ? i + 1 : 3));\n    }\n  }\n  settings.aLastSort = sort;\n}\n\n// Get the data to sort a column, be it from cache, fresh (populating the\n// cache), or from a sort formatter\nfunction _fnSortData(settings, idx) {\n  // Custom sorting function - provided by the sort data type\n  var column = settings.aoColumns[idx];\n  var customSort = DataTable.ext.order[column.sSortDataType];\n  var customData;\n  if (customSort) {\n    customData = customSort.call(settings.oInstance, settings, idx, _fnColumnIndexToVisible(settings, idx));\n  }\n\n  // Use / populate cache\n  var row, cellData;\n  var formatter = DataTable.ext.type.order[column.sType + \"-pre\"];\n  for (var i = 0, ien = settings.aoData.length; i < ien; i++) {\n    row = settings.aoData[i];\n    if (!row._aSortData) {\n      row._aSortData = [];\n    }\n    if (!row._aSortData[idx] || customSort) {\n      cellData = customSort ? customData[i] :\n      // If there was a custom sort function, use data from there\n      _fnGetCellData(settings, i, idx, 'sort');\n      row._aSortData[idx] = formatter ? formatter(cellData) : cellData;\n    }\n  }\n}\n\n/**\n * Save the state of a table\n *  @param {object} oSettings dataTables settings object\n *  @memberof DataTable#oApi\n */\nfunction _fnSaveState(settings) {\n  if (settings._bLoadingState) {\n    return;\n  }\n\n  /* Store the interesting variables */\n  var state = {\n    time: +new Date(),\n    start: settings._iDisplayStart,\n    length: settings._iDisplayLength,\n    order: $.extend(true, [], settings.aaSorting),\n    search: _fnSearchToCamel(settings.oPreviousSearch),\n    columns: $.map(settings.aoColumns, function (col, i) {\n      return {\n        visible: col.bVisible,\n        search: _fnSearchToCamel(settings.aoPreSearchCols[i])\n      };\n    })\n  };\n  settings.oSavedState = state;\n  _fnCallbackFire(settings, \"aoStateSaveParams\", 'stateSaveParams', [settings, state]);\n  if (settings.oFeatures.bStateSave && !settings.bDestroying) {\n    settings.fnStateSaveCallback.call(settings.oInstance, settings, state);\n  }\n}\n\n/**\n * Attempt to load a saved table state\n *  @param {object} oSettings dataTables settings object\n *  @param {object} oInit DataTables init object so we can override settings\n *  @param {function} callback Callback to execute when the state has been loaded\n *  @memberof DataTable#oApi\n */\nfunction _fnLoadState(settings, oInit, callback) {\n  if (!settings.oFeatures.bStateSave) {\n    callback();\n    return;\n  }\n  var loaded = function (state) {\n    _fnImplementState(settings, state, callback);\n  };\n  var state = settings.fnStateLoadCallback.call(settings.oInstance, settings, loaded);\n  if (state !== undefined) {\n    _fnImplementState(settings, state, callback);\n  }\n  // otherwise, wait for the loaded callback to be executed\n\n  return true;\n}\nfunction _fnImplementState(settings, s, callback) {\n  var i, ien;\n  var columns = settings.aoColumns;\n  settings._bLoadingState = true;\n\n  // When StateRestore was introduced the state could now be implemented at any time\n  // Not just initialisation. To do this an api instance is required in some places\n  var api = settings._bInitComplete ? new DataTable.Api(settings) : null;\n  if (!s || !s.time) {\n    settings._bLoadingState = false;\n    callback();\n    return;\n  }\n\n  // Allow custom and plug-in manipulation functions to alter the saved data set and\n  // cancelling of loading by returning false\n  var abStateLoad = _fnCallbackFire(settings, 'aoStateLoadParams', 'stateLoadParams', [settings, s]);\n  if ($.inArray(false, abStateLoad) !== -1) {\n    settings._bLoadingState = false;\n    callback();\n    return;\n  }\n\n  // Reject old data\n  var duration = settings.iStateDuration;\n  if (duration > 0 && s.time < +new Date() - duration * 1000) {\n    settings._bLoadingState = false;\n    callback();\n    return;\n  }\n\n  // Number of columns have changed - all bets are off, no restore of settings\n  if (s.columns && columns.length !== s.columns.length) {\n    settings._bLoadingState = false;\n    callback();\n    return;\n  }\n\n  // Store the saved state so it might be accessed at any time\n  settings.oLoadedState = $.extend(true, {}, s);\n\n  // Page Length\n  if (s.length !== undefined) {\n    // If already initialised just set the value directly so that the select element is also updated\n    if (api) {\n      api.page.len(s.length);\n    } else {\n      settings._iDisplayLength = s.length;\n    }\n  }\n\n  // Restore key features - todo - for 1.11 this needs to be done by\n  // subscribed events\n  if (s.start !== undefined) {\n    if (api === null) {\n      settings._iDisplayStart = s.start;\n      settings.iInitDisplayStart = s.start;\n    } else {\n      _fnPageChange(settings, s.start / settings._iDisplayLength);\n    }\n  }\n\n  // Order\n  if (s.order !== undefined) {\n    settings.aaSorting = [];\n    $.each(s.order, function (i, col) {\n      settings.aaSorting.push(col[0] >= columns.length ? [0, col[1]] : col);\n    });\n  }\n\n  // Search\n  if (s.search !== undefined) {\n    $.extend(settings.oPreviousSearch, _fnSearchToHung(s.search));\n  }\n\n  // Columns\n  if (s.columns) {\n    for (i = 0, ien = s.columns.length; i < ien; i++) {\n      var col = s.columns[i];\n\n      // Visibility\n      if (col.visible !== undefined) {\n        // If the api is defined, the table has been initialised so we need to use it rather than internal settings\n        if (api) {\n          // Don't redraw the columns on every iteration of this loop, we will do this at the end instead\n          api.column(i).visible(col.visible, false);\n        } else {\n          columns[i].bVisible = col.visible;\n        }\n      }\n\n      // Search\n      if (col.search !== undefined) {\n        $.extend(settings.aoPreSearchCols[i], _fnSearchToHung(col.search));\n      }\n    }\n\n    // If the api is defined then we need to adjust the columns once the visibility has been changed\n    if (api) {\n      api.columns.adjust();\n    }\n  }\n  settings._bLoadingState = false;\n  _fnCallbackFire(settings, 'aoStateLoaded', 'stateLoaded', [settings, s]);\n  callback();\n}\n;\n\n/**\n * Return the settings object for a particular table\n *  @param {node} table table we are using as a dataTable\n *  @returns {object} Settings object - or null if not found\n *  @memberof DataTable#oApi\n */\nfunction _fnSettingsFromNode(table) {\n  var settings = DataTable.settings;\n  var idx = $.inArray(table, _pluck(settings, 'nTable'));\n  return idx !== -1 ? settings[idx] : null;\n}\n\n/**\n * Log an error message\n *  @param {object} settings dataTables settings object\n *  @param {int} level log error messages, or display them to the user\n *  @param {string} msg error message\n *  @param {int} tn Technical note id to get more information about the error.\n *  @memberof DataTable#oApi\n */\nfunction _fnLog(settings, level, msg, tn) {\n  msg = 'DataTables warning: ' + (settings ? 'table id=' + settings.sTableId + ' - ' : '') + msg;\n  if (tn) {\n    msg += '. For more information about this error, please see ' + 'https://datatables.net/tn/' + tn;\n  }\n  if (!level) {\n    // Backwards compatibility pre 1.10\n    var ext = DataTable.ext;\n    var type = ext.sErrMode || ext.errMode;\n    if (settings) {\n      _fnCallbackFire(settings, null, 'error', [settings, tn, msg]);\n    }\n    if (type == 'alert') {\n      alert(msg);\n    } else if (type == 'throw') {\n      throw new Error(msg);\n    } else if (typeof type == 'function') {\n      type(settings, tn, msg);\n    }\n  } else if (window.console && console.log) {\n    console.log(msg);\n  }\n}\n\n/**\n * See if a property is defined on one object, if so assign it to the other object\n *  @param {object} ret target object\n *  @param {object} src source object\n *  @param {string} name property\n *  @param {string} [mappedName] name to map too - optional, name used if not given\n *  @memberof DataTable#oApi\n */\nfunction _fnMap(ret, src, name, mappedName) {\n  if (Array.isArray(name)) {\n    $.each(name, function (i, val) {\n      if (Array.isArray(val)) {\n        _fnMap(ret, src, val[0], val[1]);\n      } else {\n        _fnMap(ret, src, val);\n      }\n    });\n    return;\n  }\n  if (mappedName === undefined) {\n    mappedName = name;\n  }\n  if (src[name] !== undefined) {\n    ret[mappedName] = src[name];\n  }\n}\n\n/**\n * Extend objects - very similar to jQuery.extend, but deep copy objects, and\n * shallow copy arrays. The reason we need to do this, is that we don't want to\n * deep copy array init values (such as aaSorting) since the dev wouldn't be\n * able to override them, but we do want to deep copy arrays.\n *  @param {object} out Object to extend\n *  @param {object} extender Object from which the properties will be applied to\n *      out\n *  @param {boolean} breakRefs If true, then arrays will be sliced to take an\n *      independent copy with the exception of the `data` or `aaData` parameters\n *      if they are present. This is so you can pass in a collection to\n *      DataTables and have that used as your data source without breaking the\n *      references\n *  @returns {object} out Reference, just for convenience - out === the return.\n *  @memberof DataTable#oApi\n *  @todo This doesn't take account of arrays inside the deep copied objects.\n */\nfunction _fnExtend(out, extender, breakRefs) {\n  var val;\n  for (var prop in extender) {\n    if (extender.hasOwnProperty(prop)) {\n      val = extender[prop];\n      if ($.isPlainObject(val)) {\n        if (!$.isPlainObject(out[prop])) {\n          out[prop] = {};\n        }\n        $.extend(true, out[prop], val);\n      } else if (breakRefs && prop !== 'data' && prop !== 'aaData' && Array.isArray(val)) {\n        out[prop] = val.slice();\n      } else {\n        out[prop] = val;\n      }\n    }\n  }\n  return out;\n}\n\n/**\n * Bind an event handers to allow a click or return key to activate the callback.\n * This is good for accessibility since a return on the keyboard will have the\n * same effect as a click, if the element has focus.\n *  @param {element} n Element to bind the action to\n *  @param {object} oData Data object to pass to the triggered function\n *  @param {function} fn Callback function for when the event is triggered\n *  @memberof DataTable#oApi\n */\nfunction _fnBindAction(n, oData, fn) {\n  $(n).on('click.DT', oData, function (e) {\n    $(n).trigger('blur'); // Remove focus outline for mouse users\n    fn(e);\n  }).on('keypress.DT', oData, function (e) {\n    if (e.which === 13) {\n      e.preventDefault();\n      fn(e);\n    }\n  }).on('selectstart.DT', function () {\n    /* Take the brutal approach to cancelling text selection */\n    return false;\n  });\n}\n\n/**\n * Register a callback function. Easily allows a callback function to be added to\n * an array store of callback functions that can then all be called together.\n *  @param {object} oSettings dataTables settings object\n *  @param {string} sStore Name of the array storage for the callbacks in oSettings\n *  @param {function} fn Function to be called back\n *  @param {string} sName Identifying name for the callback (i.e. a label)\n *  @memberof DataTable#oApi\n */\nfunction _fnCallbackReg(oSettings, sStore, fn, sName) {\n  if (fn) {\n    oSettings[sStore].push({\n      \"fn\": fn,\n      \"sName\": sName\n    });\n  }\n}\n\n/**\n * Fire callback functions and trigger events. Note that the loop over the\n * callback array store is done backwards! Further note that you do not want to\n * fire off triggers in time sensitive applications (for example cell creation)\n * as its slow.\n *  @param {object} settings dataTables settings object\n *  @param {string} callbackArr Name of the array storage for the callbacks in\n *      oSettings\n *  @param {string} eventName Name of the jQuery custom event to trigger. If\n *      null no trigger is fired\n *  @param {array} args Array of arguments to pass to the callback function /\n *      trigger\n *  @memberof DataTable#oApi\n */\nfunction _fnCallbackFire(settings, callbackArr, eventName, args) {\n  var ret = [];\n  if (callbackArr) {\n    ret = $.map(settings[callbackArr].slice().reverse(), function (val, i) {\n      return val.fn.apply(settings.oInstance, args);\n    });\n  }\n  if (eventName !== null) {\n    var e = $.Event(eventName + '.dt');\n    var table = $(settings.nTable);\n    table.trigger(e, args);\n\n    // If not yet attached to the document, trigger the event\n    // on the body directly to sort of simulate the bubble\n    if (table.parents('body').length === 0) {\n      $('body').trigger(e, args);\n    }\n    ret.push(e.result);\n  }\n  return ret;\n}\nfunction _fnLengthOverflow(settings) {\n  var start = settings._iDisplayStart,\n    end = settings.fnDisplayEnd(),\n    len = settings._iDisplayLength;\n\n  /* If we have space to show extra rows (backing up from the end point - then do so */\n  if (start >= end) {\n    start = end - len;\n  }\n\n  // Keep the start record on the current page\n  start -= start % len;\n  if (len === -1 || start < 0) {\n    start = 0;\n  }\n  settings._iDisplayStart = start;\n}\nfunction _fnRenderer(settings, type) {\n  var renderer = settings.renderer;\n  var host = DataTable.ext.renderer[type];\n  if ($.isPlainObject(renderer) && renderer[type]) {\n    // Specific renderer for this type. If available use it, otherwise use\n    // the default.\n    return host[renderer[type]] || host._;\n  } else if (typeof renderer === 'string') {\n    // Common renderer - if there is one available for this type use it,\n    // otherwise use the default\n    return host[renderer] || host._;\n  }\n\n  // Use the default\n  return host._;\n}\n\n/**\n * Detect the data source being used for the table. Used to simplify the code\n * a little (ajax) and to make it compress a little smaller.\n *\n *  @param {object} settings dataTables settings object\n *  @returns {string} Data source\n *  @memberof DataTable#oApi\n */\nfunction _fnDataSource(settings) {\n  if (settings.oFeatures.bServerSide) {\n    return 'ssp';\n  } else if (settings.ajax || settings.sAjaxSource) {\n    return 'ajax';\n  }\n  return 'dom';\n}\n\n/**\n * Computed structure of the DataTables API, defined by the options passed to\n * `DataTable.Api.register()` when building the API.\n *\n * The structure is built in order to speed creation and extension of the Api\n * objects since the extensions are effectively pre-parsed.\n *\n * The array is an array of objects with the following structure, where this\n * base array represents the Api prototype base:\n *\n *     [\n *       {\n *         name:      'data'                -- string   - Property name\n *         val:       function () {},       -- function - Api method (or undefined if just an object\n *         methodExt: [ ... ],              -- array    - Array of Api object definitions to extend the method result\n *         propExt:   [ ... ]               -- array    - Array of Api object definitions to extend the property\n *       },\n *       {\n *         name:     'row'\n *         val:       {},\n *         methodExt: [ ... ],\n *         propExt:   [\n *           {\n *             name:      'data'\n *             val:       function () {},\n *             methodExt: [ ... ],\n *             propExt:   [ ... ]\n *           },\n *           ...\n *         ]\n *       }\n *     ]\n *\n * @type {Array}\n * @ignore\n */\nvar __apiStruct = [];\n\n/**\n * `Array.prototype` reference.\n *\n * @type object\n * @ignore\n */\nvar __arrayProto = Array.prototype;\n\n/**\n * Abstraction for `context` parameter of the `Api` constructor to allow it to\n * take several different forms for ease of use.\n *\n * Each of the input parameter types will be converted to a DataTables settings\n * object where possible.\n *\n * @param  {string|node|jQuery|object} mixed DataTable identifier. Can be one\n *   of:\n *\n *   * `string` - jQuery selector. Any DataTables' matching the given selector\n *     with be found and used.\n *   * `node` - `TABLE` node which has already been formed into a DataTable.\n *   * `jQuery` - A jQuery object of `TABLE` nodes.\n *   * `object` - DataTables settings object\n *   * `DataTables.Api` - API instance\n * @return {array|null} Matching DataTables settings objects. `null` or\n *   `undefined` is returned if no matching DataTable is found.\n * @ignore\n */\nvar _toSettings = function (mixed) {\n  var idx, jq;\n  var settings = DataTable.settings;\n  var tables = $.map(settings, function (el, i) {\n    return el.nTable;\n  });\n  if (!mixed) {\n    return [];\n  } else if (mixed.nTable && mixed.oApi) {\n    // DataTables settings object\n    return [mixed];\n  } else if (mixed.nodeName && mixed.nodeName.toLowerCase() === 'table') {\n    // Table node\n    idx = $.inArray(mixed, tables);\n    return idx !== -1 ? [settings[idx]] : null;\n  } else if (mixed && typeof mixed.settings === 'function') {\n    return mixed.settings().toArray();\n  } else if (typeof mixed === 'string') {\n    // jQuery selector\n    jq = $(mixed);\n  } else if (mixed instanceof $) {\n    // jQuery object (also DataTables instance)\n    jq = mixed;\n  }\n  if (jq) {\n    return jq.map(function (i) {\n      idx = $.inArray(this, tables);\n      return idx !== -1 ? settings[idx] : null;\n    }).toArray();\n  }\n};\n\n/**\n * DataTables API class - used to control and interface with  one or more\n * DataTables enhanced tables.\n *\n * The API class is heavily based on jQuery, presenting a chainable interface\n * that you can use to interact with tables. Each instance of the API class has\n * a \"context\" - i.e. the tables that it will operate on. This could be a single\n * table, all tables on a page or a sub-set thereof.\n *\n * Additionally the API is designed to allow you to easily work with the data in\n * the tables, retrieving and manipulating it as required. This is done by\n * presenting the API class as an array like interface. The contents of the\n * array depend upon the actions requested by each method (for example\n * `rows().nodes()` will return an array of nodes, while `rows().data()` will\n * return an array of objects or arrays depending upon your table's\n * configuration). The API object has a number of array like methods (`push`,\n * `pop`, `reverse` etc) as well as additional helper methods (`each`, `pluck`,\n * `unique` etc) to assist your working with the data held in a table.\n *\n * Most methods (those which return an Api instance) are chainable, which means\n * the return from a method call also has all of the methods available that the\n * top level object had. For example, these two calls are equivalent:\n *\n *     // Not chained\n *     api.row.add( {...} );\n *     api.draw();\n *\n *     // Chained\n *     api.row.add( {...} ).draw();\n *\n * @class DataTable.Api\n * @param {array|object|string|jQuery} context DataTable identifier. This is\n *   used to define which DataTables enhanced tables this API will operate on.\n *   Can be one of:\n *\n *   * `string` - jQuery selector. Any DataTables' matching the given selector\n *     with be found and used.\n *   * `node` - `TABLE` node which has already been formed into a DataTable.\n *   * `jQuery` - A jQuery object of `TABLE` nodes.\n *   * `object` - DataTables settings object\n * @param {array} [data] Data to initialise the Api instance with.\n *\n * @example\n *   // Direct initialisation during DataTables construction\n *   var api = $('#example').DataTable();\n *\n * @example\n *   // Initialisation using a DataTables jQuery object\n *   var api = $('#example').dataTable().api();\n *\n * @example\n *   // Initialisation as a constructor\n *   var api = new $.fn.DataTable.Api( 'table.dataTable' );\n */\n_Api = function (context, data) {\n  if (!(this instanceof _Api)) {\n    return new _Api(context, data);\n  }\n  var settings = [];\n  var ctxSettings = function (o) {\n    var a = _toSettings(o);\n    if (a) {\n      settings.push.apply(settings, a);\n    }\n  };\n  if (Array.isArray(context)) {\n    for (var i = 0, ien = context.length; i < ien; i++) {\n      ctxSettings(context[i]);\n    }\n  } else {\n    ctxSettings(context);\n  }\n\n  // Remove duplicates\n  this.context = _unique(settings);\n\n  // Initial data\n  if (data) {\n    $.merge(this, data);\n  }\n\n  // selector\n  this.selector = {\n    rows: null,\n    cols: null,\n    opts: null\n  };\n  _Api.extend(this, this, __apiStruct);\n};\nDataTable.Api = _Api;\n\n// Don't destroy the existing prototype, just extend it. Required for jQuery 2's\n// isPlainObject.\n$.extend(_Api.prototype, {\n  any: function () {\n    return this.count() !== 0;\n  },\n  concat: __arrayProto.concat,\n  context: [],\n  // array of table settings objects\n\n  count: function () {\n    return this.flatten().length;\n  },\n  each: function (fn) {\n    for (var i = 0, ien = this.length; i < ien; i++) {\n      fn.call(this, this[i], i, this);\n    }\n    return this;\n  },\n  eq: function (idx) {\n    var ctx = this.context;\n    return ctx.length > idx ? new _Api(ctx[idx], this[idx]) : null;\n  },\n  filter: function (fn) {\n    var a = [];\n    if (__arrayProto.filter) {\n      a = __arrayProto.filter.call(this, fn, this);\n    } else {\n      // Compatibility for browsers without EMCA-252-5 (JS 1.6)\n      for (var i = 0, ien = this.length; i < ien; i++) {\n        if (fn.call(this, this[i], i, this)) {\n          a.push(this[i]);\n        }\n      }\n    }\n    return new _Api(this.context, a);\n  },\n  flatten: function () {\n    var a = [];\n    return new _Api(this.context, a.concat.apply(a, this.toArray()));\n  },\n  join: __arrayProto.join,\n  indexOf: __arrayProto.indexOf || function (obj, start) {\n    for (var i = start || 0, ien = this.length; i < ien; i++) {\n      if (this[i] === obj) {\n        return i;\n      }\n    }\n    return -1;\n  },\n  iterator: function (flatten, type, fn, alwaysNew) {\n    var a = [],\n      ret,\n      i,\n      ien,\n      j,\n      jen,\n      context = this.context,\n      rows,\n      items,\n      item,\n      selector = this.selector;\n\n    // Argument shifting\n    if (typeof flatten === 'string') {\n      alwaysNew = fn;\n      fn = type;\n      type = flatten;\n      flatten = false;\n    }\n    for (i = 0, ien = context.length; i < ien; i++) {\n      var apiInst = new _Api(context[i]);\n      if (type === 'table') {\n        ret = fn.call(apiInst, context[i], i);\n        if (ret !== undefined) {\n          a.push(ret);\n        }\n      } else if (type === 'columns' || type === 'rows') {\n        // this has same length as context - one entry for each table\n        ret = fn.call(apiInst, context[i], this[i], i);\n        if (ret !== undefined) {\n          a.push(ret);\n        }\n      } else if (type === 'column' || type === 'column-rows' || type === 'row' || type === 'cell') {\n        // columns and rows share the same structure.\n        // 'this' is an array of column indexes for each context\n        items = this[i];\n        if (type === 'column-rows') {\n          rows = _selector_row_indexes(context[i], selector.opts);\n        }\n        for (j = 0, jen = items.length; j < jen; j++) {\n          item = items[j];\n          if (type === 'cell') {\n            ret = fn.call(apiInst, context[i], item.row, item.column, i, j);\n          } else {\n            ret = fn.call(apiInst, context[i], item, i, j, rows);\n          }\n          if (ret !== undefined) {\n            a.push(ret);\n          }\n        }\n      }\n    }\n    if (a.length || alwaysNew) {\n      var api = new _Api(context, flatten ? a.concat.apply([], a) : a);\n      var apiSelector = api.selector;\n      apiSelector.rows = selector.rows;\n      apiSelector.cols = selector.cols;\n      apiSelector.opts = selector.opts;\n      return api;\n    }\n    return this;\n  },\n  lastIndexOf: __arrayProto.lastIndexOf || function (obj, start) {\n    // Bit cheeky...\n    return this.indexOf.apply(this.toArray.reverse(), arguments);\n  },\n  length: 0,\n  map: function (fn) {\n    var a = [];\n    if (__arrayProto.map) {\n      a = __arrayProto.map.call(this, fn, this);\n    } else {\n      // Compatibility for browsers without EMCA-252-5 (JS 1.6)\n      for (var i = 0, ien = this.length; i < ien; i++) {\n        a.push(fn.call(this, this[i], i));\n      }\n    }\n    return new _Api(this.context, a);\n  },\n  pluck: function (prop) {\n    var fn = DataTable.util.get(prop);\n    return this.map(function (el) {\n      return fn(el);\n    });\n  },\n  pop: __arrayProto.pop,\n  push: __arrayProto.push,\n  // Does not return an API instance\n  reduce: __arrayProto.reduce || function (fn, init) {\n    return _fnReduce(this, fn, init, 0, this.length, 1);\n  },\n  reduceRight: __arrayProto.reduceRight || function (fn, init) {\n    return _fnReduce(this, fn, init, this.length - 1, -1, -1);\n  },\n  reverse: __arrayProto.reverse,\n  // Object with rows, columns and opts\n  selector: null,\n  shift: __arrayProto.shift,\n  slice: function () {\n    return new _Api(this.context, this);\n  },\n  sort: __arrayProto.sort,\n  // ? name - order?\n\n  splice: __arrayProto.splice,\n  toArray: function () {\n    return __arrayProto.slice.call(this);\n  },\n  to$: function () {\n    return $(this);\n  },\n  toJQuery: function () {\n    return $(this);\n  },\n  unique: function () {\n    return new _Api(this.context, _unique(this));\n  },\n  unshift: __arrayProto.unshift\n});\n_Api.extend = function (scope, obj, ext) {\n  // Only extend API instances and static properties of the API\n  if (!ext.length || !obj || !(obj instanceof _Api) && !obj.__dt_wrapper) {\n    return;\n  }\n  var i,\n    ien,\n    struct,\n    methodScoping = function (scope, fn, struc) {\n      return function () {\n        var ret = fn.apply(scope, arguments);\n\n        // Method extension\n        _Api.extend(ret, ret, struc.methodExt);\n        return ret;\n      };\n    };\n  for (i = 0, ien = ext.length; i < ien; i++) {\n    struct = ext[i];\n\n    // Value\n    obj[struct.name] = struct.type === 'function' ? methodScoping(scope, struct.val, struct) : struct.type === 'object' ? {} : struct.val;\n    obj[struct.name].__dt_wrapper = true;\n\n    // Property extension\n    _Api.extend(scope, obj[struct.name], struct.propExt);\n  }\n};\n\n// @todo - Is there need for an augment function?\n// _Api.augment = function ( inst, name )\n// {\n// \t// Find src object in the structure from the name\n// \tvar parts = name.split('.');\n\n// \t_Api.extend( inst, obj );\n// };\n\n//     [\n//       {\n//         name:      'data'                -- string   - Property name\n//         val:       function () {},       -- function - Api method (or undefined if just an object\n//         methodExt: [ ... ],              -- array    - Array of Api object definitions to extend the method result\n//         propExt:   [ ... ]               -- array    - Array of Api object definitions to extend the property\n//       },\n//       {\n//         name:     'row'\n//         val:       {},\n//         methodExt: [ ... ],\n//         propExt:   [\n//           {\n//             name:      'data'\n//             val:       function () {},\n//             methodExt: [ ... ],\n//             propExt:   [ ... ]\n//           },\n//           ...\n//         ]\n//       }\n//     ]\n\n_Api.register = _api_register = function (name, val) {\n  if (Array.isArray(name)) {\n    for (var j = 0, jen = name.length; j < jen; j++) {\n      _Api.register(name[j], val);\n    }\n    return;\n  }\n  var i,\n    ien,\n    heir = name.split('.'),\n    struct = __apiStruct,\n    key,\n    method;\n  var find = function (src, name) {\n    for (var i = 0, ien = src.length; i < ien; i++) {\n      if (src[i].name === name) {\n        return src[i];\n      }\n    }\n    return null;\n  };\n  for (i = 0, ien = heir.length; i < ien; i++) {\n    method = heir[i].indexOf('()') !== -1;\n    key = method ? heir[i].replace('()', '') : heir[i];\n    var src = find(struct, key);\n    if (!src) {\n      src = {\n        name: key,\n        val: {},\n        methodExt: [],\n        propExt: [],\n        type: 'object'\n      };\n      struct.push(src);\n    }\n    if (i === ien - 1) {\n      src.val = val;\n      src.type = typeof val === 'function' ? 'function' : $.isPlainObject(val) ? 'object' : 'other';\n    } else {\n      struct = method ? src.methodExt : src.propExt;\n    }\n  }\n};\n_Api.registerPlural = _api_registerPlural = function (pluralName, singularName, val) {\n  _Api.register(pluralName, val);\n  _Api.register(singularName, function () {\n    var ret = val.apply(this, arguments);\n    if (ret === this) {\n      // Returned item is the API instance that was passed in, return it\n      return this;\n    } else if (ret instanceof _Api) {\n      // New API instance returned, want the value from the first item\n      // in the returned array for the singular result.\n      return ret.length ? Array.isArray(ret[0]) ? new _Api(ret.context, ret[0]) :\n      // Array results are 'enhanced'\n      ret[0] : undefined;\n    }\n\n    // Non-API return - just fire it back\n    return ret;\n  });\n};\n\n/**\n * Selector for HTML tables. Apply the given selector to the give array of\n * DataTables settings objects.\n *\n * @param {string|integer} [selector] jQuery selector string or integer\n * @param  {array} Array of DataTables settings objects to be filtered\n * @return {array}\n * @ignore\n */\nvar __table_selector = function (selector, a) {\n  if (Array.isArray(selector)) {\n    return $.map(selector, function (item) {\n      return __table_selector(item, a);\n    });\n  }\n\n  // Integer is used to pick out a table by index\n  if (typeof selector === 'number') {\n    return [a[selector]];\n  }\n\n  // Perform a jQuery selector on the table nodes\n  var nodes = $.map(a, function (el, i) {\n    return el.nTable;\n  });\n  return $(nodes).filter(selector).map(function (i) {\n    // Need to translate back from the table node to the settings\n    var idx = $.inArray(this, nodes);\n    return a[idx];\n  }).toArray();\n};\n\n/**\n * Context selector for the API's context (i.e. the tables the API instance\n * refers to.\n *\n * @name    DataTable.Api#tables\n * @param {string|integer} [selector] Selector to pick which tables the iterator\n *   should operate on. If not given, all tables in the current context are\n *   used. This can be given as a jQuery selector (for example `':gt(0)'`) to\n *   select multiple tables or as an integer to select a single table.\n * @returns {DataTable.Api} Returns a new API instance if a selector is given.\n */\n_api_register('tables()', function (selector) {\n  // A new instance is created if there was a selector specified\n  return selector !== undefined && selector !== null ? new _Api(__table_selector(selector, this.context)) : this;\n});\n_api_register('table()', function (selector) {\n  var tables = this.tables(selector);\n  var ctx = tables.context;\n\n  // Truncate to the first matched table\n  return ctx.length ? new _Api(ctx[0]) : tables;\n});\n_api_registerPlural('tables().nodes()', 'table().node()', function () {\n  return this.iterator('table', function (ctx) {\n    return ctx.nTable;\n  }, 1);\n});\n_api_registerPlural('tables().body()', 'table().body()', function () {\n  return this.iterator('table', function (ctx) {\n    return ctx.nTBody;\n  }, 1);\n});\n_api_registerPlural('tables().header()', 'table().header()', function () {\n  return this.iterator('table', function (ctx) {\n    return ctx.nTHead;\n  }, 1);\n});\n_api_registerPlural('tables().footer()', 'table().footer()', function () {\n  return this.iterator('table', function (ctx) {\n    return ctx.nTFoot;\n  }, 1);\n});\n_api_registerPlural('tables().containers()', 'table().container()', function () {\n  return this.iterator('table', function (ctx) {\n    return ctx.nTableWrapper;\n  }, 1);\n});\n\n/**\n * Redraw the tables in the current context.\n */\n_api_register('draw()', function (paging) {\n  return this.iterator('table', function (settings) {\n    if (paging === 'page') {\n      _fnDraw(settings);\n    } else {\n      if (typeof paging === 'string') {\n        paging = paging === 'full-hold' ? false : true;\n      }\n      _fnReDraw(settings, paging === false);\n    }\n  });\n});\n\n/**\n * Get the current page index.\n *\n * @return {integer} Current page index (zero based)\n */ /**\n    * Set the current page.\n    *\n    * Note that if you attempt to show a page which does not exist, DataTables will\n    * not throw an error, but rather reset the paging.\n    *\n    * @param {integer|string} action The paging action to take. This can be one of:\n    *  * `integer` - The page index to jump to\n    *  * `string` - An action to take:\n    *    * `first` - Jump to first page.\n    *    * `next` - Jump to the next page\n    *    * `previous` - Jump to previous page\n    *    * `last` - Jump to the last page.\n    * @returns {DataTables.Api} this\n    */\n_api_register('page()', function (action) {\n  if (action === undefined) {\n    return this.page.info().page; // not an expensive call\n  }\n\n  // else, have an action to take on all tables\n  return this.iterator('table', function (settings) {\n    _fnPageChange(settings, action);\n  });\n});\n\n/**\n * Paging information for the first table in the current context.\n *\n * If you require paging information for another table, use the `table()` method\n * with a suitable selector.\n *\n * @return {object} Object with the following properties set:\n *  * `page` - Current page index (zero based - i.e. the first page is `0`)\n *  * `pages` - Total number of pages\n *  * `start` - Display index for the first record shown on the current page\n *  * `end` - Display index for the last record shown on the current page\n *  * `length` - Display length (number of records). Note that generally `start\n *    + length = end`, but this is not always true, for example if there are\n *    only 2 records to show on the final page, with a length of 10.\n *  * `recordsTotal` - Full data set length\n *  * `recordsDisplay` - Data set length once the current filtering criterion\n *    are applied.\n */\n_api_register('page.info()', function (action) {\n  if (this.context.length === 0) {\n    return undefined;\n  }\n  var settings = this.context[0],\n    start = settings._iDisplayStart,\n    len = settings.oFeatures.bPaginate ? settings._iDisplayLength : -1,\n    visRecords = settings.fnRecordsDisplay(),\n    all = len === -1;\n  return {\n    \"page\": all ? 0 : Math.floor(start / len),\n    \"pages\": all ? 1 : Math.ceil(visRecords / len),\n    \"start\": start,\n    \"end\": settings.fnDisplayEnd(),\n    \"length\": len,\n    \"recordsTotal\": settings.fnRecordsTotal(),\n    \"recordsDisplay\": visRecords,\n    \"serverSide\": _fnDataSource(settings) === 'ssp'\n  };\n});\n\n/**\n * Get the current page length.\n *\n * @return {integer} Current page length. Note `-1` indicates that all records\n *   are to be shown.\n */ /**\n    * Set the current page length.\n    *\n    * @param {integer} Page length to set. Use `-1` to show all records.\n    * @returns {DataTables.Api} this\n    */\n_api_register('page.len()', function (len) {\n  // Note that we can't call this function 'length()' because `length`\n  // is a Javascript property of functions which defines how many arguments\n  // the function expects.\n  if (len === undefined) {\n    return this.context.length !== 0 ? this.context[0]._iDisplayLength : undefined;\n  }\n\n  // else, set the page length\n  return this.iterator('table', function (settings) {\n    _fnLengthChange(settings, len);\n  });\n});\nvar __reload = function (settings, holdPosition, callback) {\n  // Use the draw event to trigger a callback\n  if (callback) {\n    var api = new _Api(settings);\n    api.one('draw', function () {\n      callback(api.ajax.json());\n    });\n  }\n  if (_fnDataSource(settings) == 'ssp') {\n    _fnReDraw(settings, holdPosition);\n  } else {\n    _fnProcessingDisplay(settings, true);\n\n    // Cancel an existing request\n    var xhr = settings.jqXHR;\n    if (xhr && xhr.readyState !== 4) {\n      xhr.abort();\n    }\n\n    // Trigger xhr\n    _fnBuildAjax(settings, [], function (json) {\n      _fnClearTable(settings);\n      var data = _fnAjaxDataSrc(settings, json);\n      for (var i = 0, ien = data.length; i < ien; i++) {\n        _fnAddData(settings, data[i]);\n      }\n      _fnReDraw(settings, holdPosition);\n      _fnProcessingDisplay(settings, false);\n    });\n  }\n};\n\n/**\n * Get the JSON response from the last Ajax request that DataTables made to the\n * server. Note that this returns the JSON from the first table in the current\n * context.\n *\n * @return {object} JSON received from the server.\n */\n_api_register('ajax.json()', function () {\n  var ctx = this.context;\n  if (ctx.length > 0) {\n    return ctx[0].json;\n  }\n\n  // else return undefined;\n});\n\n/**\n * Get the data submitted in the last Ajax request\n */\n_api_register('ajax.params()', function () {\n  var ctx = this.context;\n  if (ctx.length > 0) {\n    return ctx[0].oAjaxData;\n  }\n\n  // else return undefined;\n});\n\n/**\n * Reload tables from the Ajax data source. Note that this function will\n * automatically re-draw the table when the remote data has been loaded.\n *\n * @param {boolean} [reset=true] Reset (default) or hold the current paging\n *   position. A full re-sort and re-filter is performed when this method is\n *   called, which is why the pagination reset is the default action.\n * @returns {DataTables.Api} this\n */\n_api_register('ajax.reload()', function (callback, resetPaging) {\n  return this.iterator('table', function (settings) {\n    __reload(settings, resetPaging === false, callback);\n  });\n});\n\n/**\n * Get the current Ajax URL. Note that this returns the URL from the first\n * table in the current context.\n *\n * @return {string} Current Ajax source URL\n */ /**\n    * Set the Ajax URL. Note that this will set the URL for all tables in the\n    * current context.\n    *\n    * @param {string} url URL to set.\n    * @returns {DataTables.Api} this\n    */\n_api_register('ajax.url()', function (url) {\n  var ctx = this.context;\n  if (url === undefined) {\n    // get\n    if (ctx.length === 0) {\n      return undefined;\n    }\n    ctx = ctx[0];\n    return ctx.ajax ? $.isPlainObject(ctx.ajax) ? ctx.ajax.url : ctx.ajax : ctx.sAjaxSource;\n  }\n\n  // set\n  return this.iterator('table', function (settings) {\n    if ($.isPlainObject(settings.ajax)) {\n      settings.ajax.url = url;\n    } else {\n      settings.ajax = url;\n    }\n    // No need to consider sAjaxSource here since DataTables gives priority\n    // to `ajax` over `sAjaxSource`. So setting `ajax` here, renders any\n    // value of `sAjaxSource` redundant.\n  });\n});\n\n/**\n * Load data from the newly set Ajax URL. Note that this method is only\n * available when `ajax.url()` is used to set a URL. Additionally, this method\n * has the same effect as calling `ajax.reload()` but is provided for\n * convenience when setting a new URL. Like `ajax.reload()` it will\n * automatically redraw the table once the remote data has been loaded.\n *\n * @returns {DataTables.Api} this\n */\n_api_register('ajax.url().load()', function (callback, resetPaging) {\n  // Same as a reload, but makes sense to present it for easy access after a\n  // url change\n  return this.iterator('table', function (ctx) {\n    __reload(ctx, resetPaging === false, callback);\n  });\n});\nvar _selector_run = function (type, selector, selectFn, settings, opts) {\n  var out = [],\n    res,\n    a,\n    i,\n    ien,\n    j,\n    jen,\n    selectorType = typeof selector;\n\n  // Can't just check for isArray here, as an API or jQuery instance might be\n  // given with their array like look\n  if (!selector || selectorType === 'string' || selectorType === 'function' || selector.length === undefined) {\n    selector = [selector];\n  }\n  for (i = 0, ien = selector.length; i < ien; i++) {\n    // Only split on simple strings - complex expressions will be jQuery selectors\n    a = selector[i] && selector[i].split && !selector[i].match(/[\\[\\(:]/) ? selector[i].split(',') : [selector[i]];\n    for (j = 0, jen = a.length; j < jen; j++) {\n      res = selectFn(typeof a[j] === 'string' ? a[j].trim() : a[j]);\n      if (res && res.length) {\n        out = out.concat(res);\n      }\n    }\n  }\n\n  // selector extensions\n  var ext = _ext.selector[type];\n  if (ext.length) {\n    for (i = 0, ien = ext.length; i < ien; i++) {\n      out = ext[i](settings, opts, out);\n    }\n  }\n  return _unique(out);\n};\nvar _selector_opts = function (opts) {\n  if (!opts) {\n    opts = {};\n  }\n\n  // Backwards compatibility for 1.9- which used the terminology filter rather\n  // than search\n  if (opts.filter && opts.search === undefined) {\n    opts.search = opts.filter;\n  }\n  return $.extend({\n    search: 'none',\n    order: 'current',\n    page: 'all'\n  }, opts);\n};\nvar _selector_first = function (inst) {\n  // Reduce the API instance to the first item found\n  for (var i = 0, ien = inst.length; i < ien; i++) {\n    if (inst[i].length > 0) {\n      // Assign the first element to the first item in the instance\n      // and truncate the instance and context\n      inst[0] = inst[i];\n      inst[0].length = 1;\n      inst.length = 1;\n      inst.context = [inst.context[i]];\n      return inst;\n    }\n  }\n\n  // Not found - return an empty instance\n  inst.length = 0;\n  return inst;\n};\nvar _selector_row_indexes = function (settings, opts) {\n  var i,\n    ien,\n    tmp,\n    a = [],\n    displayFiltered = settings.aiDisplay,\n    displayMaster = settings.aiDisplayMaster;\n  var search = opts.search,\n    // none, applied, removed\n    order = opts.order,\n    // applied, current, index (original - compatibility with 1.9)\n    page = opts.page; // all, current\n\n  if (_fnDataSource(settings) == 'ssp') {\n    // In server-side processing mode, most options are irrelevant since\n    // rows not shown don't exist and the index order is the applied order\n    // Removed is a special case - for consistency just return an empty\n    // array\n    return search === 'removed' ? [] : _range(0, displayMaster.length);\n  } else if (page == 'current') {\n    // Current page implies that order=current and filter=applied, since it is\n    // fairly senseless otherwise, regardless of what order and search actually\n    // are\n    for (i = settings._iDisplayStart, ien = settings.fnDisplayEnd(); i < ien; i++) {\n      a.push(displayFiltered[i]);\n    }\n  } else if (order == 'current' || order == 'applied') {\n    if (search == 'none') {\n      a = displayMaster.slice();\n    } else if (search == 'applied') {\n      a = displayFiltered.slice();\n    } else if (search == 'removed') {\n      // O(n+m) solution by creating a hash map\n      var displayFilteredMap = {};\n      for (var i = 0, ien = displayFiltered.length; i < ien; i++) {\n        displayFilteredMap[displayFiltered[i]] = null;\n      }\n      a = $.map(displayMaster, function (el) {\n        return !displayFilteredMap.hasOwnProperty(el) ? el : null;\n      });\n    }\n  } else if (order == 'index' || order == 'original') {\n    for (i = 0, ien = settings.aoData.length; i < ien; i++) {\n      if (search == 'none') {\n        a.push(i);\n      } else {\n        // applied | removed\n        tmp = $.inArray(i, displayFiltered);\n        if (tmp === -1 && search == 'removed' || tmp >= 0 && search == 'applied') {\n          a.push(i);\n        }\n      }\n    }\n  }\n  return a;\n};\n\n/* * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *\n * Rows\n *\n * {}          - no selector - use all available rows\n * {integer}   - row aoData index\n * {node}      - TR node\n * {string}    - jQuery selector to apply to the TR elements\n * {array}     - jQuery array of nodes, or simply an array of TR nodes\n *\n */\nvar __row_selector = function (settings, selector, opts) {\n  var rows;\n  var run = function (sel) {\n    var selInt = _intVal(sel);\n    var i, ien;\n    var aoData = settings.aoData;\n\n    // Short cut - selector is a number and no options provided (default is\n    // all records, so no need to check if the index is in there, since it\n    // must be - dev error if the index doesn't exist).\n    if (selInt !== null && !opts) {\n      return [selInt];\n    }\n    if (!rows) {\n      rows = _selector_row_indexes(settings, opts);\n    }\n    if (selInt !== null && $.inArray(selInt, rows) !== -1) {\n      // Selector - integer\n      return [selInt];\n    } else if (sel === null || sel === undefined || sel === '') {\n      // Selector - none\n      return rows;\n    }\n\n    // Selector - function\n    if (typeof sel === 'function') {\n      return $.map(rows, function (idx) {\n        var row = aoData[idx];\n        return sel(idx, row._aData, row.nTr) ? idx : null;\n      });\n    }\n\n    // Selector - node\n    if (sel.nodeName) {\n      var rowIdx = sel._DT_RowIndex; // Property added by DT for fast lookup\n      var cellIdx = sel._DT_CellIndex;\n      if (rowIdx !== undefined) {\n        // Make sure that the row is actually still present in the table\n        return aoData[rowIdx] && aoData[rowIdx].nTr === sel ? [rowIdx] : [];\n      } else if (cellIdx) {\n        return aoData[cellIdx.row] && aoData[cellIdx.row].nTr === sel.parentNode ? [cellIdx.row] : [];\n      } else {\n        var host = $(sel).closest('*[data-dt-row]');\n        return host.length ? [host.data('dt-row')] : [];\n      }\n    }\n\n    // ID selector. Want to always be able to select rows by id, regardless\n    // of if the tr element has been created or not, so can't rely upon\n    // jQuery here - hence a custom implementation. This does not match\n    // Sizzle's fast selector or HTML4 - in HTML5 the ID can be anything,\n    // but to select it using a CSS selector engine (like Sizzle or\n    // querySelect) it would need to need to be escaped for some characters.\n    // DataTables simplifies this for row selectors since you can select\n    // only a row. A # indicates an id any anything that follows is the id -\n    // unescaped.\n    if (typeof sel === 'string' && sel.charAt(0) === '#') {\n      // get row index from id\n      var rowObj = settings.aIds[sel.replace(/^#/, '')];\n      if (rowObj !== undefined) {\n        return [rowObj.idx];\n      }\n\n      // need to fall through to jQuery in case there is DOM id that\n      // matches\n    }\n\n    // Get nodes in the order from the `rows` array with null values removed\n    var nodes = _removeEmpty(_pluck_order(settings.aoData, rows, 'nTr'));\n\n    // Selector - jQuery selector string, array of nodes or jQuery object/\n    // As jQuery's .filter() allows jQuery objects to be passed in filter,\n    // it also allows arrays, so this will cope with all three options\n    return $(nodes).filter(sel).map(function () {\n      return this._DT_RowIndex;\n    }).toArray();\n  };\n  return _selector_run('row', selector, run, settings, opts);\n};\n_api_register('rows()', function (selector, opts) {\n  // argument shifting\n  if (selector === undefined) {\n    selector = '';\n  } else if ($.isPlainObject(selector)) {\n    opts = selector;\n    selector = '';\n  }\n  opts = _selector_opts(opts);\n  var inst = this.iterator('table', function (settings) {\n    return __row_selector(settings, selector, opts);\n  }, 1);\n\n  // Want argument shifting here and in __row_selector?\n  inst.selector.rows = selector;\n  inst.selector.opts = opts;\n  return inst;\n});\n_api_register('rows().nodes()', function () {\n  return this.iterator('row', function (settings, row) {\n    return settings.aoData[row].nTr || undefined;\n  }, 1);\n});\n_api_register('rows().data()', function () {\n  return this.iterator(true, 'rows', function (settings, rows) {\n    return _pluck_order(settings.aoData, rows, '_aData');\n  }, 1);\n});\n_api_registerPlural('rows().cache()', 'row().cache()', function (type) {\n  return this.iterator('row', function (settings, row) {\n    var r = settings.aoData[row];\n    return type === 'search' ? r._aFilterData : r._aSortData;\n  }, 1);\n});\n_api_registerPlural('rows().invalidate()', 'row().invalidate()', function (src) {\n  return this.iterator('row', function (settings, row) {\n    _fnInvalidate(settings, row, src);\n  });\n});\n_api_registerPlural('rows().indexes()', 'row().index()', function () {\n  return this.iterator('row', function (settings, row) {\n    return row;\n  }, 1);\n});\n_api_registerPlural('rows().ids()', 'row().id()', function (hash) {\n  var a = [];\n  var context = this.context;\n\n  // `iterator` will drop undefined values, but in this case we want them\n  for (var i = 0, ien = context.length; i < ien; i++) {\n    for (var j = 0, jen = this[i].length; j < jen; j++) {\n      var id = context[i].rowIdFn(context[i].aoData[this[i][j]]._aData);\n      a.push((hash === true ? '#' : '') + id);\n    }\n  }\n  return new _Api(context, a);\n});\n_api_registerPlural('rows().remove()', 'row().remove()', function () {\n  var that = this;\n  this.iterator('row', function (settings, row, thatIdx) {\n    var data = settings.aoData;\n    var rowData = data[row];\n    var i, ien, j, jen;\n    var loopRow, loopCells;\n    data.splice(row, 1);\n\n    // Update the cached indexes\n    for (i = 0, ien = data.length; i < ien; i++) {\n      loopRow = data[i];\n      loopCells = loopRow.anCells;\n\n      // Rows\n      if (loopRow.nTr !== null) {\n        loopRow.nTr._DT_RowIndex = i;\n      }\n\n      // Cells\n      if (loopCells !== null) {\n        for (j = 0, jen = loopCells.length; j < jen; j++) {\n          loopCells[j]._DT_CellIndex.row = i;\n        }\n      }\n    }\n\n    // Delete from the display arrays\n    _fnDeleteIndex(settings.aiDisplayMaster, row);\n    _fnDeleteIndex(settings.aiDisplay, row);\n    _fnDeleteIndex(that[thatIdx], row, false); // maintain local indexes\n\n    // For server-side processing tables - subtract the deleted row from the count\n    if (settings._iRecordsDisplay > 0) {\n      settings._iRecordsDisplay--;\n    }\n\n    // Check for an 'overflow' they case for displaying the table\n    _fnLengthOverflow(settings);\n\n    // Remove the row's ID reference if there is one\n    var id = settings.rowIdFn(rowData._aData);\n    if (id !== undefined) {\n      delete settings.aIds[id];\n    }\n  });\n  this.iterator('table', function (settings) {\n    for (var i = 0, ien = settings.aoData.length; i < ien; i++) {\n      settings.aoData[i].idx = i;\n    }\n  });\n  return this;\n});\n_api_register('rows.add()', function (rows) {\n  var newRows = this.iterator('table', function (settings) {\n    var row, i, ien;\n    var out = [];\n    for (i = 0, ien = rows.length; i < ien; i++) {\n      row = rows[i];\n      if (row.nodeName && row.nodeName.toUpperCase() === 'TR') {\n        out.push(_fnAddTr(settings, row)[0]);\n      } else {\n        out.push(_fnAddData(settings, row));\n      }\n    }\n    return out;\n  }, 1);\n\n  // Return an Api.rows() extended instance, so rows().nodes() etc can be used\n  var modRows = this.rows(-1);\n  modRows.pop();\n  $.merge(modRows, newRows);\n  return modRows;\n});\n\n/**\n *\n */\n_api_register('row()', function (selector, opts) {\n  return _selector_first(this.rows(selector, opts));\n});\n_api_register('row().data()', function (data) {\n  var ctx = this.context;\n  if (data === undefined) {\n    // Get\n    return ctx.length && this.length ? ctx[0].aoData[this[0]]._aData : undefined;\n  }\n\n  // Set\n  var row = ctx[0].aoData[this[0]];\n  row._aData = data;\n\n  // If the DOM has an id, and the data source is an array\n  if (Array.isArray(data) && row.nTr && row.nTr.id) {\n    _fnSetObjectDataFn(ctx[0].rowId)(data, row.nTr.id);\n  }\n\n  // Automatically invalidate\n  _fnInvalidate(ctx[0], this[0], 'data');\n  return this;\n});\n_api_register('row().node()', function () {\n  var ctx = this.context;\n  return ctx.length && this.length ? ctx[0].aoData[this[0]].nTr || null : null;\n});\n_api_register('row.add()', function (row) {\n  // Allow a jQuery object to be passed in - only a single row is added from\n  // it though - the first element in the set\n  if (row instanceof $ && row.length) {\n    row = row[0];\n  }\n  var rows = this.iterator('table', function (settings) {\n    if (row.nodeName && row.nodeName.toUpperCase() === 'TR') {\n      return _fnAddTr(settings, row)[0];\n    }\n    return _fnAddData(settings, row);\n  });\n\n  // Return an Api.rows() extended instance, with the newly added row selected\n  return this.row(rows[0]);\n});\n$(document).on('plugin-init.dt', function (e, context) {\n  var api = new _Api(context);\n  var namespace = 'on-plugin-init';\n  var stateSaveParamsEvent = 'stateSaveParams.' + namespace;\n  var destroyEvent = 'destroy. ' + namespace;\n  api.on(stateSaveParamsEvent, function (e, settings, d) {\n    // This could be more compact with the API, but it is a lot faster as a simple\n    // internal loop\n    var idFn = settings.rowIdFn;\n    var data = settings.aoData;\n    var ids = [];\n    for (var i = 0; i < data.length; i++) {\n      if (data[i]._detailsShow) {\n        ids.push('#' + idFn(data[i]._aData));\n      }\n    }\n    d.childRows = ids;\n  });\n  api.on(destroyEvent, function () {\n    api.off(stateSaveParamsEvent + ' ' + destroyEvent);\n  });\n  var loaded = api.state.loaded();\n  if (loaded && loaded.childRows) {\n    api.rows($.map(loaded.childRows, function (id) {\n      return id.replace(/:/g, '\\\\:');\n    })).every(function () {\n      _fnCallbackFire(context, null, 'requestChild', [this]);\n    });\n  }\n});\nvar __details_add = function (ctx, row, data, klass) {\n  // Convert to array of TR elements\n  var rows = [];\n  var addRow = function (r, k) {\n    // Recursion to allow for arrays of jQuery objects\n    if (Array.isArray(r) || r instanceof $) {\n      for (var i = 0, ien = r.length; i < ien; i++) {\n        addRow(r[i], k);\n      }\n      return;\n    }\n\n    // If we get a TR element, then just add it directly - up to the dev\n    // to add the correct number of columns etc\n    if (r.nodeName && r.nodeName.toLowerCase() === 'tr') {\n      rows.push(r);\n    } else {\n      // Otherwise create a row with a wrapper\n      var created = $('<tr><td></td></tr>').addClass(k);\n      $('td', created).addClass(k).html(r)[0].colSpan = _fnVisbleColumns(ctx);\n      rows.push(created[0]);\n    }\n  };\n  addRow(data, klass);\n  if (row._details) {\n    row._details.detach();\n  }\n  row._details = $(rows);\n\n  // If the children were already shown, that state should be retained\n  if (row._detailsShow) {\n    row._details.insertAfter(row.nTr);\n  }\n};\n\n// Make state saving of child row details async to allow them to be batch processed\nvar __details_state = DataTable.util.throttle(function (ctx) {\n  _fnSaveState(ctx[0]);\n}, 500);\nvar __details_remove = function (api, idx) {\n  var ctx = api.context;\n  if (ctx.length) {\n    var row = ctx[0].aoData[idx !== undefined ? idx : api[0]];\n    if (row && row._details) {\n      row._details.remove();\n      row._detailsShow = undefined;\n      row._details = undefined;\n      $(row.nTr).removeClass('dt-hasChild');\n      __details_state(ctx);\n    }\n  }\n};\nvar __details_display = function (api, show) {\n  var ctx = api.context;\n  if (ctx.length && api.length) {\n    var row = ctx[0].aoData[api[0]];\n    if (row._details) {\n      row._detailsShow = show;\n      if (show) {\n        row._details.insertAfter(row.nTr);\n        $(row.nTr).addClass('dt-hasChild');\n      } else {\n        row._details.detach();\n        $(row.nTr).removeClass('dt-hasChild');\n      }\n      _fnCallbackFire(ctx[0], null, 'childRow', [show, api.row(api[0])]);\n      __details_events(ctx[0]);\n      __details_state(ctx);\n    }\n  }\n};\nvar __details_events = function (settings) {\n  var api = new _Api(settings);\n  var namespace = '.dt.DT_details';\n  var drawEvent = 'draw' + namespace;\n  var colvisEvent = 'column-sizing' + namespace;\n  var destroyEvent = 'destroy' + namespace;\n  var data = settings.aoData;\n  api.off(drawEvent + ' ' + colvisEvent + ' ' + destroyEvent);\n  if (_pluck(data, '_details').length > 0) {\n    // On each draw, insert the required elements into the document\n    api.on(drawEvent, function (e, ctx) {\n      if (settings !== ctx) {\n        return;\n      }\n      api.rows({\n        page: 'current'\n      }).eq(0).each(function (idx) {\n        // Internal data grab\n        var row = data[idx];\n        if (row._detailsShow) {\n          row._details.insertAfter(row.nTr);\n        }\n      });\n    });\n\n    // Column visibility change - update the colspan\n    api.on(colvisEvent, function (e, ctx, idx, vis) {\n      if (settings !== ctx) {\n        return;\n      }\n\n      // Update the colspan for the details rows (note, only if it already has\n      // a colspan)\n      var row,\n        visible = _fnVisbleColumns(ctx);\n      for (var i = 0, ien = data.length; i < ien; i++) {\n        row = data[i];\n        if (row._details) {\n          row._details.each(function () {\n            var el = $(this).children('td');\n            if (el.length == 1) {\n              el.attr('colspan', visible);\n            }\n          });\n        }\n      }\n    });\n\n    // Table destroyed - nuke any child rows\n    api.on(destroyEvent, function (e, ctx) {\n      if (settings !== ctx) {\n        return;\n      }\n      for (var i = 0, ien = data.length; i < ien; i++) {\n        if (data[i]._details) {\n          __details_remove(api, i);\n        }\n      }\n    });\n  }\n};\n\n// Strings for the method names to help minification\nvar _emp = '';\nvar _child_obj = _emp + 'row().child';\nvar _child_mth = _child_obj + '()';\n\n// data can be:\n//  tr\n//  string\n//  jQuery or array of any of the above\n_api_register(_child_mth, function (data, klass) {\n  var ctx = this.context;\n  if (data === undefined) {\n    // get\n    return ctx.length && this.length ? ctx[0].aoData[this[0]]._details : undefined;\n  } else if (data === true) {\n    // show\n    this.child.show();\n  } else if (data === false) {\n    // remove\n    __details_remove(this);\n  } else if (ctx.length && this.length) {\n    // set\n    __details_add(ctx[0], ctx[0].aoData[this[0]], data, klass);\n  }\n  return this;\n});\n_api_register([_child_obj + '.show()', _child_mth + '.show()' // only when `child()` was called with parameters (without\n], function (show) {\n  // it returns an object and this method is not executed)\n  __details_display(this, true);\n  return this;\n});\n_api_register([_child_obj + '.hide()', _child_mth + '.hide()' // only when `child()` was called with parameters (without\n], function () {\n  // it returns an object and this method is not executed)\n  __details_display(this, false);\n  return this;\n});\n_api_register([_child_obj + '.remove()', _child_mth + '.remove()' // only when `child()` was called with parameters (without\n], function () {\n  // it returns an object and this method is not executed)\n  __details_remove(this);\n  return this;\n});\n_api_register(_child_obj + '.isShown()', function () {\n  var ctx = this.context;\n  if (ctx.length && this.length) {\n    // _detailsShown as false or undefined will fall through to return false\n    return ctx[0].aoData[this[0]]._detailsShow || false;\n  }\n  return false;\n});\n\n/* * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *\n * Columns\n *\n * {integer}           - column index (>=0 count from left, <0 count from right)\n * \"{integer}:visIdx\"  - visible column index (i.e. translate to column index)  (>=0 count from left, <0 count from right)\n * \"{integer}:visible\" - alias for {integer}:visIdx  (>=0 count from left, <0 count from right)\n * \"{string}:name\"     - column name\n * \"{string}\"          - jQuery selector on column header nodes\n *\n */\n\n// can be an array of these items, comma separated list, or an array of comma\n// separated lists\n\nvar __re_column_selector = /^([^:]+):(name|visIdx|visible)$/;\n\n// r1 and r2 are redundant - but it means that the parameters match for the\n// iterator callback in columns().data()\nvar __columnData = function (settings, column, r1, r2, rows) {\n  var a = [];\n  for (var row = 0, ien = rows.length; row < ien; row++) {\n    a.push(_fnGetCellData(settings, rows[row], column));\n  }\n  return a;\n};\nvar __column_selector = function (settings, selector, opts) {\n  var columns = settings.aoColumns,\n    names = _pluck(columns, 'sName'),\n    nodes = _pluck(columns, 'nTh');\n  var run = function (s) {\n    var selInt = _intVal(s);\n\n    // Selector - all\n    if (s === '') {\n      return _range(columns.length);\n    }\n\n    // Selector - index\n    if (selInt !== null) {\n      return [selInt >= 0 ? selInt :\n      // Count from left\n      columns.length + selInt // Count from right (+ because its a negative value)\n      ];\n    }\n\n    // Selector = function\n    if (typeof s === 'function') {\n      var rows = _selector_row_indexes(settings, opts);\n      return $.map(columns, function (col, idx) {\n        return s(idx, __columnData(settings, idx, 0, 0, rows), nodes[idx]) ? idx : null;\n      });\n    }\n\n    // jQuery or string selector\n    var match = typeof s === 'string' ? s.match(__re_column_selector) : '';\n    if (match) {\n      switch (match[2]) {\n        case 'visIdx':\n        case 'visible':\n          var idx = parseInt(match[1], 10);\n          // Visible index given, convert to column index\n          if (idx < 0) {\n            // Counting from the right\n            var visColumns = $.map(columns, function (col, i) {\n              return col.bVisible ? i : null;\n            });\n            return [visColumns[visColumns.length + idx]];\n          }\n          // Counting from the left\n          return [_fnVisibleToColumnIndex(settings, idx)];\n        case 'name':\n          // match by name. `names` is column index complete and in order\n          return $.map(names, function (name, i) {\n            return name === match[1] ? i : null;\n          });\n        default:\n          return [];\n      }\n    }\n\n    // Cell in the table body\n    if (s.nodeName && s._DT_CellIndex) {\n      return [s._DT_CellIndex.column];\n    }\n\n    // jQuery selector on the TH elements for the columns\n    var jqResult = $(nodes).filter(s).map(function () {\n      return $.inArray(this, nodes); // `nodes` is column index complete and in order\n    }).toArray();\n    if (jqResult.length || !s.nodeName) {\n      return jqResult;\n    }\n\n    // Otherwise a node which might have a `dt-column` data attribute, or be\n    // a child or such an element\n    var host = $(s).closest('*[data-dt-column]');\n    return host.length ? [host.data('dt-column')] : [];\n  };\n  return _selector_run('column', selector, run, settings, opts);\n};\nvar __setColumnVis = function (settings, column, vis) {\n  var cols = settings.aoColumns,\n    col = cols[column],\n    data = settings.aoData,\n    row,\n    cells,\n    i,\n    ien,\n    tr;\n\n  // Get\n  if (vis === undefined) {\n    return col.bVisible;\n  }\n\n  // Set\n  // No change\n  if (col.bVisible === vis) {\n    return;\n  }\n  if (vis) {\n    // Insert column\n    // Need to decide if we should use appendChild or insertBefore\n    var insertBefore = $.inArray(true, _pluck(cols, 'bVisible'), column + 1);\n    for (i = 0, ien = data.length; i < ien; i++) {\n      tr = data[i].nTr;\n      cells = data[i].anCells;\n      if (tr) {\n        // insertBefore can act like appendChild if 2nd arg is null\n        tr.insertBefore(cells[column], cells[insertBefore] || null);\n      }\n    }\n  } else {\n    // Remove column\n    $(_pluck(settings.aoData, 'anCells', column)).detach();\n  }\n\n  // Common actions\n  col.bVisible = vis;\n};\n_api_register('columns()', function (selector, opts) {\n  // argument shifting\n  if (selector === undefined) {\n    selector = '';\n  } else if ($.isPlainObject(selector)) {\n    opts = selector;\n    selector = '';\n  }\n  opts = _selector_opts(opts);\n  var inst = this.iterator('table', function (settings) {\n    return __column_selector(settings, selector, opts);\n  }, 1);\n\n  // Want argument shifting here and in _row_selector?\n  inst.selector.cols = selector;\n  inst.selector.opts = opts;\n  return inst;\n});\n_api_registerPlural('columns().header()', 'column().header()', function (selector, opts) {\n  return this.iterator('column', function (settings, column) {\n    return settings.aoColumns[column].nTh;\n  }, 1);\n});\n_api_registerPlural('columns().footer()', 'column().footer()', function (selector, opts) {\n  return this.iterator('column', function (settings, column) {\n    return settings.aoColumns[column].nTf;\n  }, 1);\n});\n_api_registerPlural('columns().data()', 'column().data()', function () {\n  return this.iterator('column-rows', __columnData, 1);\n});\n_api_registerPlural('columns().dataSrc()', 'column().dataSrc()', function () {\n  return this.iterator('column', function (settings, column) {\n    return settings.aoColumns[column].mData;\n  }, 1);\n});\n_api_registerPlural('columns().cache()', 'column().cache()', function (type) {\n  return this.iterator('column-rows', function (settings, column, i, j, rows) {\n    return _pluck_order(settings.aoData, rows, type === 'search' ? '_aFilterData' : '_aSortData', column);\n  }, 1);\n});\n_api_registerPlural('columns().nodes()', 'column().nodes()', function () {\n  return this.iterator('column-rows', function (settings, column, i, j, rows) {\n    return _pluck_order(settings.aoData, rows, 'anCells', column);\n  }, 1);\n});\n_api_registerPlural('columns().visible()', 'column().visible()', function (vis, calc) {\n  var that = this;\n  var ret = this.iterator('column', function (settings, column) {\n    if (vis === undefined) {\n      return settings.aoColumns[column].bVisible;\n    } // else\n    __setColumnVis(settings, column, vis);\n  });\n\n  // Group the column visibility changes\n  if (vis !== undefined) {\n    this.iterator('table', function (settings) {\n      // Redraw the header after changes\n      _fnDrawHead(settings, settings.aoHeader);\n      _fnDrawHead(settings, settings.aoFooter);\n\n      // Update colspan for no records display. Child rows and extensions will use their own\n      // listeners to do this - only need to update the empty table item here\n      if (!settings.aiDisplay.length) {\n        $(settings.nTBody).find('td[colspan]').attr('colspan', _fnVisbleColumns(settings));\n      }\n      _fnSaveState(settings);\n\n      // Second loop once the first is done for events\n      that.iterator('column', function (settings, column) {\n        _fnCallbackFire(settings, null, 'column-visibility', [settings, column, vis, calc]);\n      });\n      if (calc === undefined || calc) {\n        that.columns.adjust();\n      }\n    });\n  }\n  return ret;\n});\n_api_registerPlural('columns().indexes()', 'column().index()', function (type) {\n  return this.iterator('column', function (settings, column) {\n    return type === 'visible' ? _fnColumnIndexToVisible(settings, column) : column;\n  }, 1);\n});\n_api_register('columns.adjust()', function () {\n  return this.iterator('table', function (settings) {\n    _fnAdjustColumnSizing(settings);\n  }, 1);\n});\n_api_register('column.index()', function (type, idx) {\n  if (this.context.length !== 0) {\n    var ctx = this.context[0];\n    if (type === 'fromVisible' || type === 'toData') {\n      return _fnVisibleToColumnIndex(ctx, idx);\n    } else if (type === 'fromData' || type === 'toVisible') {\n      return _fnColumnIndexToVisible(ctx, idx);\n    }\n  }\n});\n_api_register('column()', function (selector, opts) {\n  return _selector_first(this.columns(selector, opts));\n});\nvar __cell_selector = function (settings, selector, opts) {\n  var data = settings.aoData;\n  var rows = _selector_row_indexes(settings, opts);\n  var cells = _removeEmpty(_pluck_order(data, rows, 'anCells'));\n  var allCells = $(_flatten([], cells));\n  var row;\n  var columns = settings.aoColumns.length;\n  var a, i, ien, j, o, host;\n  var run = function (s) {\n    var fnSelector = typeof s === 'function';\n    if (s === null || s === undefined || fnSelector) {\n      // All cells and function selectors\n      a = [];\n      for (i = 0, ien = rows.length; i < ien; i++) {\n        row = rows[i];\n        for (j = 0; j < columns; j++) {\n          o = {\n            row: row,\n            column: j\n          };\n          if (fnSelector) {\n            // Selector - function\n            host = data[row];\n            if (s(o, _fnGetCellData(settings, row, j), host.anCells ? host.anCells[j] : null)) {\n              a.push(o);\n            }\n          } else {\n            // Selector - all\n            a.push(o);\n          }\n        }\n      }\n      return a;\n    }\n\n    // Selector - index\n    if ($.isPlainObject(s)) {\n      // Valid cell index and its in the array of selectable rows\n      return s.column !== undefined && s.row !== undefined && $.inArray(s.row, rows) !== -1 ? [s] : [];\n    }\n\n    // Selector - jQuery filtered cells\n    var jqResult = allCells.filter(s).map(function (i, el) {\n      return {\n        // use a new object, in case someone changes the values\n        row: el._DT_CellIndex.row,\n        column: el._DT_CellIndex.column\n      };\n    }).toArray();\n    if (jqResult.length || !s.nodeName) {\n      return jqResult;\n    }\n\n    // Otherwise the selector is a node, and there is one last option - the\n    // element might be a child of an element which has dt-row and dt-column\n    // data attributes\n    host = $(s).closest('*[data-dt-row]');\n    return host.length ? [{\n      row: host.data('dt-row'),\n      column: host.data('dt-column')\n    }] : [];\n  };\n  return _selector_run('cell', selector, run, settings, opts);\n};\n_api_register('cells()', function (rowSelector, columnSelector, opts) {\n  // Argument shifting\n  if ($.isPlainObject(rowSelector)) {\n    // Indexes\n    if (rowSelector.row === undefined) {\n      // Selector options in first parameter\n      opts = rowSelector;\n      rowSelector = null;\n    } else {\n      // Cell index objects in first parameter\n      opts = columnSelector;\n      columnSelector = null;\n    }\n  }\n  if ($.isPlainObject(columnSelector)) {\n    opts = columnSelector;\n    columnSelector = null;\n  }\n\n  // Cell selector\n  if (columnSelector === null || columnSelector === undefined) {\n    return this.iterator('table', function (settings) {\n      return __cell_selector(settings, rowSelector, _selector_opts(opts));\n    });\n  }\n\n  // The default built in options need to apply to row and columns\n  var internalOpts = opts ? {\n    page: opts.page,\n    order: opts.order,\n    search: opts.search\n  } : {};\n\n  // Row + column selector\n  var columns = this.columns(columnSelector, internalOpts);\n  var rows = this.rows(rowSelector, internalOpts);\n  var i, ien, j, jen;\n  var cellsNoOpts = this.iterator('table', function (settings, idx) {\n    var a = [];\n    for (i = 0, ien = rows[idx].length; i < ien; i++) {\n      for (j = 0, jen = columns[idx].length; j < jen; j++) {\n        a.push({\n          row: rows[idx][i],\n          column: columns[idx][j]\n        });\n      }\n    }\n    return a;\n  }, 1);\n\n  // There is currently only one extension which uses a cell selector extension\n  // It is a _major_ performance drag to run this if it isn't needed, so this is\n  // an extension specific check at the moment\n  var cells = opts && opts.selected ? this.cells(cellsNoOpts, opts) : cellsNoOpts;\n  $.extend(cells.selector, {\n    cols: columnSelector,\n    rows: rowSelector,\n    opts: opts\n  });\n  return cells;\n});\n_api_registerPlural('cells().nodes()', 'cell().node()', function () {\n  return this.iterator('cell', function (settings, row, column) {\n    var data = settings.aoData[row];\n    return data && data.anCells ? data.anCells[column] : undefined;\n  }, 1);\n});\n_api_register('cells().data()', function () {\n  return this.iterator('cell', function (settings, row, column) {\n    return _fnGetCellData(settings, row, column);\n  }, 1);\n});\n_api_registerPlural('cells().cache()', 'cell().cache()', function (type) {\n  type = type === 'search' ? '_aFilterData' : '_aSortData';\n  return this.iterator('cell', function (settings, row, column) {\n    return settings.aoData[row][type][column];\n  }, 1);\n});\n_api_registerPlural('cells().render()', 'cell().render()', function (type) {\n  return this.iterator('cell', function (settings, row, column) {\n    return _fnGetCellData(settings, row, column, type);\n  }, 1);\n});\n_api_registerPlural('cells().indexes()', 'cell().index()', function () {\n  return this.iterator('cell', function (settings, row, column) {\n    return {\n      row: row,\n      column: column,\n      columnVisible: _fnColumnIndexToVisible(settings, column)\n    };\n  }, 1);\n});\n_api_registerPlural('cells().invalidate()', 'cell().invalidate()', function (src) {\n  return this.iterator('cell', function (settings, row, column) {\n    _fnInvalidate(settings, row, src, column);\n  });\n});\n_api_register('cell()', function (rowSelector, columnSelector, opts) {\n  return _selector_first(this.cells(rowSelector, columnSelector, opts));\n});\n_api_register('cell().data()', function (data) {\n  var ctx = this.context;\n  var cell = this[0];\n  if (data === undefined) {\n    // Get\n    return ctx.length && cell.length ? _fnGetCellData(ctx[0], cell[0].row, cell[0].column) : undefined;\n  }\n\n  // Set\n  _fnSetCellData(ctx[0], cell[0].row, cell[0].column, data);\n  _fnInvalidate(ctx[0], cell[0].row, 'data', cell[0].column);\n  return this;\n});\n\n/**\n * Get current ordering (sorting) that has been applied to the table.\n *\n * @returns {array} 2D array containing the sorting information for the first\n *   table in the current context. Each element in the parent array represents\n *   a column being sorted upon (i.e. multi-sorting with two columns would have\n *   2 inner arrays). The inner arrays may have 2 or 3 elements. The first is\n *   the column index that the sorting condition applies to, the second is the\n *   direction of the sort (`desc` or `asc`) and, optionally, the third is the\n *   index of the sorting order from the `column.sorting` initialisation array.\n */ /**\n    * Set the ordering for the table.\n    *\n    * @param {integer} order Column index to sort upon.\n    * @param {string} direction Direction of the sort to be applied (`asc` or `desc`)\n    * @returns {DataTables.Api} this\n    */ /**\n       * Set the ordering for the table.\n       *\n       * @param {array} order 1D array of sorting information to be applied.\n       * @param {array} [...] Optional additional sorting conditions\n       * @returns {DataTables.Api} this\n       */ /**\n          * Set the ordering for the table.\n          *\n          * @param {array} order 2D array of sorting information to be applied.\n          * @returns {DataTables.Api} this\n          */\n_api_register('order()', function (order, dir) {\n  var ctx = this.context;\n  if (order === undefined) {\n    // get\n    return ctx.length !== 0 ? ctx[0].aaSorting : undefined;\n  }\n\n  // set\n  if (typeof order === 'number') {\n    // Simple column / direction passed in\n    order = [[order, dir]];\n  } else if (order.length && !Array.isArray(order[0])) {\n    // Arguments passed in (list of 1D arrays)\n    order = Array.prototype.slice.call(arguments);\n  }\n  // otherwise a 2D array was passed in\n\n  return this.iterator('table', function (settings) {\n    settings.aaSorting = order.slice();\n  });\n});\n\n/**\n * Attach a sort listener to an element for a given column\n *\n * @param {node|jQuery|string} node Identifier for the element(s) to attach the\n *   listener to. This can take the form of a single DOM node, a jQuery\n *   collection of nodes or a jQuery selector which will identify the node(s).\n * @param {integer} column the column that a click on this node will sort on\n * @param {function} [callback] callback function when sort is run\n * @returns {DataTables.Api} this\n */\n_api_register('order.listener()', function (node, column, callback) {\n  return this.iterator('table', function (settings) {\n    _fnSortAttachListener(settings, node, column, callback);\n  });\n});\n_api_register('order.fixed()', function (set) {\n  if (!set) {\n    var ctx = this.context;\n    var fixed = ctx.length ? ctx[0].aaSortingFixed : undefined;\n    return Array.isArray(fixed) ? {\n      pre: fixed\n    } : fixed;\n  }\n  return this.iterator('table', function (settings) {\n    settings.aaSortingFixed = $.extend(true, {}, set);\n  });\n});\n\n// Order by the selected column(s)\n_api_register(['columns().order()', 'column().order()'], function (dir) {\n  var that = this;\n  return this.iterator('table', function (settings, i) {\n    var sort = [];\n    $.each(that[i], function (j, col) {\n      sort.push([col, dir]);\n    });\n    settings.aaSorting = sort;\n  });\n});\n_api_register('search()', function (input, regex, smart, caseInsen) {\n  var ctx = this.context;\n  if (input === undefined) {\n    // get\n    return ctx.length !== 0 ? ctx[0].oPreviousSearch.sSearch : undefined;\n  }\n\n  // set\n  return this.iterator('table', function (settings) {\n    if (!settings.oFeatures.bFilter) {\n      return;\n    }\n    _fnFilterComplete(settings, $.extend({}, settings.oPreviousSearch, {\n      \"sSearch\": input + \"\",\n      \"bRegex\": regex === null ? false : regex,\n      \"bSmart\": smart === null ? true : smart,\n      \"bCaseInsensitive\": caseInsen === null ? true : caseInsen\n    }), 1);\n  });\n});\n_api_registerPlural('columns().search()', 'column().search()', function (input, regex, smart, caseInsen) {\n  return this.iterator('column', function (settings, column) {\n    var preSearch = settings.aoPreSearchCols;\n    if (input === undefined) {\n      // get\n      return preSearch[column].sSearch;\n    }\n\n    // set\n    if (!settings.oFeatures.bFilter) {\n      return;\n    }\n    $.extend(preSearch[column], {\n      \"sSearch\": input + \"\",\n      \"bRegex\": regex === null ? false : regex,\n      \"bSmart\": smart === null ? true : smart,\n      \"bCaseInsensitive\": caseInsen === null ? true : caseInsen\n    });\n    _fnFilterComplete(settings, settings.oPreviousSearch, 1);\n  });\n});\n\n/*\n * State API methods\n */\n\n_api_register('state()', function () {\n  return this.context.length ? this.context[0].oSavedState : null;\n});\n_api_register('state.clear()', function () {\n  return this.iterator('table', function (settings) {\n    // Save an empty object\n    settings.fnStateSaveCallback.call(settings.oInstance, settings, {});\n  });\n});\n_api_register('state.loaded()', function () {\n  return this.context.length ? this.context[0].oLoadedState : null;\n});\n_api_register('state.save()', function () {\n  return this.iterator('table', function (settings) {\n    _fnSaveState(settings);\n  });\n});\n\n/**\n * Set the jQuery or window object to be used by DataTables\n *\n * @param {*} module Library / container object\n * @param {string} [type] Library or container type `lib`, `win` or `datetime`.\n *   If not provided, automatic detection is attempted.\n */\nDataTable.use = function (module, type) {\n  if (type === 'lib' || module.fn) {\n    $ = module;\n  } else if (type == 'win' || module.document) {\n    window = module;\n    document = module.document;\n  } else if (type === 'datetime' || module.type === 'DateTime') {\n    DataTable.DateTime = module;\n  }\n};\n\n/**\n * CommonJS factory function pass through. This will check if the arguments\n * given are a window object or a jQuery object. If so they are set\n * accordingly.\n * @param {*} root Window\n * @param {*} jq jQUery\n * @returns {boolean} Indicator\n */\nDataTable.factory = function (root, jq) {\n  var is = false;\n\n  // Test if the first parameter is a window object\n  if (root && root.document) {\n    window = root;\n    document = root.document;\n  }\n\n  // Test if the second parameter is a jQuery object\n  if (jq && jq.fn && jq.fn.jquery) {\n    $ = jq;\n    is = true;\n  }\n  return is;\n};\n\n/**\n * Provide a common method for plug-ins to check the version of DataTables being\n * used, in order to ensure compatibility.\n *\n *  @param {string} version Version string to check for, in the format \"X.Y.Z\".\n *    Note that the formats \"X\" and \"X.Y\" are also acceptable.\n *  @returns {boolean} true if this version of DataTables is greater or equal to\n *    the required version, or false if this version of DataTales is not\n *    suitable\n *  @static\n *  @dtopt API-Static\n *\n *  @example\n *    alert( $.fn.dataTable.versionCheck( '1.9.0' ) );\n */\nDataTable.versionCheck = DataTable.fnVersionCheck = function (version) {\n  var aThis = DataTable.version.split('.');\n  var aThat = version.split('.');\n  var iThis, iThat;\n  for (var i = 0, iLen = aThat.length; i < iLen; i++) {\n    iThis = parseInt(aThis[i], 10) || 0;\n    iThat = parseInt(aThat[i], 10) || 0;\n\n    // Parts are the same, keep comparing\n    if (iThis === iThat) {\n      continue;\n    }\n\n    // Parts are different, return immediately\n    return iThis > iThat;\n  }\n  return true;\n};\n\n/**\n * Check if a `<table>` node is a DataTable table already or not.\n *\n *  @param {node|jquery|string} table Table node, jQuery object or jQuery\n *      selector for the table to test. Note that if more than more than one\n *      table is passed on, only the first will be checked\n *  @returns {boolean} true the table given is a DataTable, or false otherwise\n *  @static\n *  @dtopt API-Static\n *\n *  @example\n *    if ( ! $.fn.DataTable.isDataTable( '#example' ) ) {\n *      $('#example').dataTable();\n *    }\n */\nDataTable.isDataTable = DataTable.fnIsDataTable = function (table) {\n  var t = $(table).get(0);\n  var is = false;\n  if (table instanceof DataTable.Api) {\n    return true;\n  }\n  $.each(DataTable.settings, function (i, o) {\n    var head = o.nScrollHead ? $('table', o.nScrollHead)[0] : null;\n    var foot = o.nScrollFoot ? $('table', o.nScrollFoot)[0] : null;\n    if (o.nTable === t || head === t || foot === t) {\n      is = true;\n    }\n  });\n  return is;\n};\n\n/**\n * Get all DataTable tables that have been initialised - optionally you can\n * select to get only currently visible tables.\n *\n *  @param {boolean} [visible=false] Flag to indicate if you want all (default)\n *    or visible tables only.\n *  @returns {array} Array of `table` nodes (not DataTable instances) which are\n *    DataTables\n *  @static\n *  @dtopt API-Static\n *\n *  @example\n *    $.each( $.fn.dataTable.tables(true), function () {\n *      $(table).DataTable().columns.adjust();\n *    } );\n */\nDataTable.tables = DataTable.fnTables = function (visible) {\n  var api = false;\n  if ($.isPlainObject(visible)) {\n    api = visible.api;\n    visible = visible.visible;\n  }\n  var a = $.map(DataTable.settings, function (o) {\n    if (!visible || visible && $(o.nTable).is(':visible')) {\n      return o.nTable;\n    }\n  });\n  return api ? new _Api(a) : a;\n};\n\n/**\n * Convert from camel case parameters to Hungarian notation. This is made public\n * for the extensions to provide the same ability as DataTables core to accept\n * either the 1.9 style Hungarian notation, or the 1.10+ style camelCase\n * parameters.\n *\n *  @param {object} src The model object which holds all parameters that can be\n *    mapped.\n *  @param {object} user The object to convert from camel case to Hungarian.\n *  @param {boolean} force When set to `true`, properties which already have a\n *    Hungarian value in the `user` object will be overwritten. Otherwise they\n *    won't be.\n */\nDataTable.camelToHungarian = _fnCamelToHungarian;\n\n/**\n *\n */\n_api_register('$()', function (selector, opts) {\n  var rows = this.rows(opts).nodes(),\n    // Get all rows\n    jqRows = $(rows);\n  return $([].concat(jqRows.filter(selector).toArray(), jqRows.find(selector).toArray()));\n});\n\n// jQuery functions to operate on the tables\n$.each(['on', 'one', 'off'], function (i, key) {\n  _api_register(key + '()', function /* event, handler */\n  () {\n    var args = Array.prototype.slice.call(arguments);\n\n    // Add the `dt` namespace automatically if it isn't already present\n    args[0] = $.map(args[0].split(/\\s/), function (e) {\n      return !e.match(/\\.dt\\b/) ? e + '.dt' : e;\n    }).join(' ');\n    var inst = $(this.tables().nodes());\n    inst[key].apply(inst, args);\n    return this;\n  });\n});\n_api_register('clear()', function () {\n  return this.iterator('table', function (settings) {\n    _fnClearTable(settings);\n  });\n});\n_api_register('settings()', function () {\n  return new _Api(this.context, this.context);\n});\n_api_register('init()', function () {\n  var ctx = this.context;\n  return ctx.length ? ctx[0].oInit : null;\n});\n_api_register('data()', function () {\n  return this.iterator('table', function (settings) {\n    return _pluck(settings.aoData, '_aData');\n  }).flatten();\n});\n_api_register('destroy()', function (remove) {\n  remove = remove || false;\n  return this.iterator('table', function (settings) {\n    var classes = settings.oClasses;\n    var table = settings.nTable;\n    var tbody = settings.nTBody;\n    var thead = settings.nTHead;\n    var tfoot = settings.nTFoot;\n    var jqTable = $(table);\n    var jqTbody = $(tbody);\n    var jqWrapper = $(settings.nTableWrapper);\n    var rows = $.map(settings.aoData, function (r) {\n      return r.nTr;\n    });\n    var i, ien;\n\n    // Flag to note that the table is currently being destroyed - no action\n    // should be taken\n    settings.bDestroying = true;\n\n    // Fire off the destroy callbacks for plug-ins etc\n    _fnCallbackFire(settings, \"aoDestroyCallback\", \"destroy\", [settings]);\n\n    // If not being removed from the document, make all columns visible\n    if (!remove) {\n      new _Api(settings).columns().visible(true);\n    }\n\n    // Blitz all `DT` namespaced events (these are internal events, the\n    // lowercase, `dt` events are user subscribed and they are responsible\n    // for removing them\n    jqWrapper.off('.DT').find(':not(tbody *)').off('.DT');\n    $(window).off('.DT-' + settings.sInstance);\n\n    // When scrolling we had to break the table up - restore it\n    if (table != thead.parentNode) {\n      jqTable.children('thead').detach();\n      jqTable.append(thead);\n    }\n    if (tfoot && table != tfoot.parentNode) {\n      jqTable.children('tfoot').detach();\n      jqTable.append(tfoot);\n    }\n    settings.aaSorting = [];\n    settings.aaSortingFixed = [];\n    _fnSortingClasses(settings);\n    $(rows).removeClass(settings.asStripeClasses.join(' '));\n    $('th, td', thead).removeClass(classes.sSortable + ' ' + classes.sSortableAsc + ' ' + classes.sSortableDesc + ' ' + classes.sSortableNone);\n\n    // Add the TR elements back into the table in their original order\n    jqTbody.children().detach();\n    jqTbody.append(rows);\n    var orig = settings.nTableWrapper.parentNode;\n\n    // Remove the DataTables generated nodes, events and classes\n    var removedMethod = remove ? 'remove' : 'detach';\n    jqTable[removedMethod]();\n    jqWrapper[removedMethod]();\n\n    // If we need to reattach the table to the document\n    if (!remove && orig) {\n      // insertBefore acts like appendChild if !arg[1]\n      orig.insertBefore(table, settings.nTableReinsertBefore);\n\n      // Restore the width of the original table - was read from the style property,\n      // so we can restore directly to that\n      jqTable.css('width', settings.sDestroyWidth).removeClass(classes.sTable);\n\n      // If the were originally stripe classes - then we add them back here.\n      // Note this is not fool proof (for example if not all rows had stripe\n      // classes - but it's a good effort without getting carried away\n      ien = settings.asDestroyStripes.length;\n      if (ien) {\n        jqTbody.children().each(function (i) {\n          $(this).addClass(settings.asDestroyStripes[i % ien]);\n        });\n      }\n    }\n\n    /* Remove the settings object from the settings array */\n    var idx = $.inArray(settings, DataTable.settings);\n    if (idx !== -1) {\n      DataTable.settings.splice(idx, 1);\n    }\n  });\n});\n\n// Add the `every()` method for rows, columns and cells in a compact form\n$.each(['column', 'row', 'cell'], function (i, type) {\n  _api_register(type + 's().every()', function (fn) {\n    var opts = this.selector.opts;\n    var api = this;\n    return this.iterator(type, function (settings, arg1, arg2, arg3, arg4) {\n      // Rows and columns:\n      //  arg1 - index\n      //  arg2 - table counter\n      //  arg3 - loop counter\n      //  arg4 - undefined\n      // Cells:\n      //  arg1 - row index\n      //  arg2 - column index\n      //  arg3 - table counter\n      //  arg4 - loop counter\n      fn.call(api[type](arg1, type === 'cell' ? arg2 : opts, type === 'cell' ? opts : undefined), arg1, arg2, arg3, arg4);\n    });\n  });\n});\n\n// i18n method for extensions to be able to use the language object from the\n// DataTable\n_api_register('i18n()', function (token, def, plural) {\n  var ctx = this.context[0];\n  var resolved = _fnGetObjectDataFn(token)(ctx.oLanguage);\n  if (resolved === undefined) {\n    resolved = def;\n  }\n  if (plural !== undefined && $.isPlainObject(resolved)) {\n    resolved = resolved[plural] !== undefined ? resolved[plural] : resolved._;\n  }\n  return typeof resolved === 'string' ? resolved.replace('%d', plural) // nb: plural might be undefined,\n  : resolved;\n});\n/**\n * Version string for plug-ins to check compatibility. Allowed format is\n * `a.b.c-d` where: a:int, b:int, c:int, d:string(dev|beta|alpha). `d` is used\n * only for non-release builds. See https://semver.org/ for more information.\n *  @member\n *  @type string\n *  @default Version number\n */\nDataTable.version = \"1.13.11\";\n\n/**\n * Private data store, containing all of the settings objects that are\n * created for the tables on a given page.\n *\n * Note that the `DataTable.settings` object is aliased to\n * `jQuery.fn.dataTableExt` through which it may be accessed and\n * manipulated, or `jQuery.fn.dataTable.settings`.\n *  @member\n *  @type array\n *  @default []\n *  @private\n */\nDataTable.settings = [];\n\n/**\n * Object models container, for the various models that DataTables has\n * available to it. These models define the objects that are used to hold\n * the active state and configuration of the table.\n *  @namespace\n */\nDataTable.models = {};\n\n/**\n * Template object for the way in which DataTables holds information about\n * search information for the global filter and individual column filters.\n *  @namespace\n */\nDataTable.models.oSearch = {\n  /**\n   * Flag to indicate if the filtering should be case insensitive or not\n   *  @type boolean\n   *  @default true\n   */\n  \"bCaseInsensitive\": true,\n  /**\n   * Applied search term\n   *  @type string\n   *  @default <i>Empty string</i>\n   */\n  \"sSearch\": \"\",\n  /**\n   * Flag to indicate if the search term should be interpreted as a\n   * regular expression (true) or not (false) and therefore and special\n   * regex characters escaped.\n   *  @type boolean\n   *  @default false\n   */\n  \"bRegex\": false,\n  /**\n   * Flag to indicate if DataTables is to use its smart filtering or not.\n   *  @type boolean\n   *  @default true\n   */\n  \"bSmart\": true,\n  /**\n   * Flag to indicate if DataTables should only trigger a search when\n   * the return key is pressed.\n   *  @type boolean\n   *  @default false\n   */\n  \"return\": false\n};\n\n/**\n * Template object for the way in which DataTables holds information about\n * each individual row. This is the object format used for the settings\n * aoData array.\n *  @namespace\n */\nDataTable.models.oRow = {\n  /**\n   * TR element for the row\n   *  @type node\n   *  @default null\n   */\n  \"nTr\": null,\n  /**\n   * Array of TD elements for each row. This is null until the row has been\n   * created.\n   *  @type array nodes\n   *  @default []\n   */\n  \"anCells\": null,\n  /**\n   * Data object from the original data source for the row. This is either\n   * an array if using the traditional form of DataTables, or an object if\n   * using mData options. The exact type will depend on the passed in\n   * data from the data source, or will be an array if using DOM a data\n   * source.\n   *  @type array|object\n   *  @default []\n   */\n  \"_aData\": [],\n  /**\n   * Sorting data cache - this array is ostensibly the same length as the\n   * number of columns (although each index is generated only as it is\n   * needed), and holds the data that is used for sorting each column in the\n   * row. We do this cache generation at the start of the sort in order that\n   * the formatting of the sort data need be done only once for each cell\n   * per sort. This array should not be read from or written to by anything\n   * other than the master sorting methods.\n   *  @type array\n   *  @default null\n   *  @private\n   */\n  \"_aSortData\": null,\n  /**\n   * Per cell filtering data cache. As per the sort data cache, used to\n   * increase the performance of the filtering in DataTables\n   *  @type array\n   *  @default null\n   *  @private\n   */\n  \"_aFilterData\": null,\n  /**\n   * Filtering data cache. This is the same as the cell filtering cache, but\n   * in this case a string rather than an array. This is easily computed with\n   * a join on `_aFilterData`, but is provided as a cache so the join isn't\n   * needed on every search (memory traded for performance)\n   *  @type array\n   *  @default null\n   *  @private\n   */\n  \"_sFilterRow\": null,\n  /**\n   * Cache of the class name that DataTables has applied to the row, so we\n   * can quickly look at this variable rather than needing to do a DOM check\n   * on className for the nTr property.\n   *  @type string\n   *  @default <i>Empty string</i>\n   *  @private\n   */\n  \"_sRowStripe\": \"\",\n  /**\n   * Denote if the original data source was from the DOM, or the data source\n   * object. This is used for invalidating data, so DataTables can\n   * automatically read data from the original source, unless uninstructed\n   * otherwise.\n   *  @type string\n   *  @default null\n   *  @private\n   */\n  \"src\": null,\n  /**\n   * Index in the aoData array. This saves an indexOf lookup when we have the\n   * object, but want to know the index\n   *  @type integer\n   *  @default -1\n   *  @private\n   */\n  \"idx\": -1\n};\n\n/**\n * Template object for the column information object in DataTables. This object\n * is held in the settings aoColumns array and contains all the information that\n * DataTables needs about each individual column.\n *\n * Note that this object is related to {@link DataTable.defaults.column}\n * but this one is the internal data store for DataTables's cache of columns.\n * It should NOT be manipulated outside of DataTables. Any configuration should\n * be done through the initialisation options.\n *  @namespace\n */\nDataTable.models.oColumn = {\n  /**\n   * Column index. This could be worked out on-the-fly with $.inArray, but it\n   * is faster to just hold it as a variable\n   *  @type integer\n   *  @default null\n   */\n  \"idx\": null,\n  /**\n   * A list of the columns that sorting should occur on when this column\n   * is sorted. That this property is an array allows multi-column sorting\n   * to be defined for a column (for example first name / last name columns\n   * would benefit from this). The values are integers pointing to the\n   * columns to be sorted on (typically it will be a single integer pointing\n   * at itself, but that doesn't need to be the case).\n   *  @type array\n   */\n  \"aDataSort\": null,\n  /**\n   * Define the sorting directions that are applied to the column, in sequence\n   * as the column is repeatedly sorted upon - i.e. the first value is used\n   * as the sorting direction when the column if first sorted (clicked on).\n   * Sort it again (click again) and it will move on to the next index.\n   * Repeat until loop.\n   *  @type array\n   */\n  \"asSorting\": null,\n  /**\n   * Flag to indicate if the column is searchable, and thus should be included\n   * in the filtering or not.\n   *  @type boolean\n   */\n  \"bSearchable\": null,\n  /**\n   * Flag to indicate if the column is sortable or not.\n   *  @type boolean\n   */\n  \"bSortable\": null,\n  /**\n   * Flag to indicate if the column is currently visible in the table or not\n   *  @type boolean\n   */\n  \"bVisible\": null,\n  /**\n   * Store for manual type assignment using the `column.type` option. This\n   * is held in store so we can manipulate the column's `sType` property.\n   *  @type string\n   *  @default null\n   *  @private\n   */\n  \"_sManualType\": null,\n  /**\n   * Flag to indicate if HTML5 data attributes should be used as the data\n   * source for filtering or sorting. True is either are.\n   *  @type boolean\n   *  @default false\n   *  @private\n   */\n  \"_bAttrSrc\": false,\n  /**\n   * Developer definable function that is called whenever a cell is created (Ajax source,\n   * etc) or processed for input (DOM source). This can be used as a compliment to mRender\n   * allowing you to modify the DOM element (add background colour for example) when the\n   * element is available.\n   *  @type function\n   *  @param {element} nTd The TD node that has been created\n   *  @param {*} sData The Data for the cell\n   *  @param {array|object} oData The data for the whole row\n   *  @param {int} iRow The row index for the aoData data store\n   *  @default null\n   */\n  \"fnCreatedCell\": null,\n  /**\n   * Function to get data from a cell in a column. You should <b>never</b>\n   * access data directly through _aData internally in DataTables - always use\n   * the method attached to this property. It allows mData to function as\n   * required. This function is automatically assigned by the column\n   * initialisation method\n   *  @type function\n   *  @param {array|object} oData The data array/object for the array\n   *    (i.e. aoData[]._aData)\n   *  @param {string} sSpecific The specific data type you want to get -\n   *    'display', 'type' 'filter' 'sort'\n   *  @returns {*} The data for the cell from the given row's data\n   *  @default null\n   */\n  \"fnGetData\": null,\n  /**\n   * Function to set data for a cell in the column. You should <b>never</b>\n   * set the data directly to _aData internally in DataTables - always use\n   * this method. It allows mData to function as required. This function\n   * is automatically assigned by the column initialisation method\n   *  @type function\n   *  @param {array|object} oData The data array/object for the array\n   *    (i.e. aoData[]._aData)\n   *  @param {*} sValue Value to set\n   *  @default null\n   */\n  \"fnSetData\": null,\n  /**\n   * Property to read the value for the cells in the column from the data\n   * source array / object. If null, then the default content is used, if a\n   * function is given then the return from the function is used.\n   *  @type function|int|string|null\n   *  @default null\n   */\n  \"mData\": null,\n  /**\n   * Partner property to mData which is used (only when defined) to get\n   * the data - i.e. it is basically the same as mData, but without the\n   * 'set' option, and also the data fed to it is the result from mData.\n   * This is the rendering method to match the data method of mData.\n   *  @type function|int|string|null\n   *  @default null\n   */\n  \"mRender\": null,\n  /**\n   * Unique header TH/TD element for this column - this is what the sorting\n   * listener is attached to (if sorting is enabled.)\n   *  @type node\n   *  @default null\n   */\n  \"nTh\": null,\n  /**\n   * Unique footer TH/TD element for this column (if there is one). Not used\n   * in DataTables as such, but can be used for plug-ins to reference the\n   * footer for each column.\n   *  @type node\n   *  @default null\n   */\n  \"nTf\": null,\n  /**\n   * The class to apply to all TD elements in the table's TBODY for the column\n   *  @type string\n   *  @default null\n   */\n  \"sClass\": null,\n  /**\n   * When DataTables calculates the column widths to assign to each column,\n   * it finds the longest string in each column and then constructs a\n   * temporary table and reads the widths from that. The problem with this\n   * is that \"mmm\" is much wider then \"iiii\", but the latter is a longer\n   * string - thus the calculation can go wrong (doing it properly and putting\n   * it into an DOM object and measuring that is horribly(!) slow). Thus as\n   * a \"work around\" we provide this option. It will append its value to the\n   * text that is found to be the longest string for the column - i.e. padding.\n   *  @type string\n   */\n  \"sContentPadding\": null,\n  /**\n   * Allows a default value to be given for a column's data, and will be used\n   * whenever a null data source is encountered (this can be because mData\n   * is set to null, or because the data source itself is null).\n   *  @type string\n   *  @default null\n   */\n  \"sDefaultContent\": null,\n  /**\n   * Name for the column, allowing reference to the column by name as well as\n   * by index (needs a lookup to work by name).\n   *  @type string\n   */\n  \"sName\": null,\n  /**\n   * Custom sorting data type - defines which of the available plug-ins in\n   * afnSortData the custom sorting will use - if any is defined.\n   *  @type string\n   *  @default std\n   */\n  \"sSortDataType\": 'std',\n  /**\n   * Class to be applied to the header element when sorting on this column\n   *  @type string\n   *  @default null\n   */\n  \"sSortingClass\": null,\n  /**\n   * Class to be applied to the header element when sorting on this column -\n   * when jQuery UI theming is used.\n   *  @type string\n   *  @default null\n   */\n  \"sSortingClassJUI\": null,\n  /**\n   * Title of the column - what is seen in the TH element (nTh).\n   *  @type string\n   */\n  \"sTitle\": null,\n  /**\n   * Column sorting and filtering type\n   *  @type string\n   *  @default null\n   */\n  \"sType\": null,\n  /**\n   * Width of the column\n   *  @type string\n   *  @default null\n   */\n  \"sWidth\": null,\n  /**\n   * Width of the column when it was first \"encountered\"\n   *  @type string\n   *  @default null\n   */\n  \"sWidthOrig\": null\n};\n\n/*\n * Developer note: The properties of the object below are given in Hungarian\n * notation, that was used as the interface for DataTables prior to v1.10, however\n * from v1.10 onwards the primary interface is camel case. In order to avoid\n * breaking backwards compatibility utterly with this change, the Hungarian\n * version is still, internally the primary interface, but is is not documented\n * - hence the @name tags in each doc comment. This allows a Javascript function\n * to create a map from Hungarian notation to camel case (going the other direction\n * would require each property to be listed, which would add around 3K to the size\n * of DataTables, while this method is about a 0.5K hit).\n *\n * Ultimately this does pave the way for Hungarian notation to be dropped\n * completely, but that is a massive amount of work and will break current\n * installs (therefore is on-hold until v2).\n */\n\n/**\n * Initialisation options that can be given to DataTables at initialisation\n * time.\n *  @namespace\n */\nDataTable.defaults = {\n  /**\n   * An array of data to use for the table, passed in at initialisation which\n   * will be used in preference to any data which is already in the DOM. This is\n   * particularly useful for constructing tables purely in Javascript, for\n   * example with a custom Ajax call.\n   *  @type array\n   *  @default null\n   *\n   *  @dtopt Option\n   *  @name DataTable.defaults.data\n   *\n   *  @example\n   *    // Using a 2D array data source\n   *    $(document).ready( function () {\n   *      $('#example').dataTable( {\n   *        \"data\": [\n   *          ['Trident', 'Internet Explorer 4.0', 'Win 95+', 4, 'X'],\n   *          ['Trident', 'Internet Explorer 5.0', 'Win 95+', 5, 'C'],\n   *        ],\n   *        \"columns\": [\n   *          { \"title\": \"Engine\" },\n   *          { \"title\": \"Browser\" },\n   *          { \"title\": \"Platform\" },\n   *          { \"title\": \"Version\" },\n   *          { \"title\": \"Grade\" }\n   *        ]\n   *      } );\n   *    } );\n   *\n   *  @example\n   *    // Using an array of objects as a data source (`data`)\n   *    $(document).ready( function () {\n   *      $('#example').dataTable( {\n   *        \"data\": [\n   *          {\n   *            \"engine\":   \"Trident\",\n   *            \"browser\":  \"Internet Explorer 4.0\",\n   *            \"platform\": \"Win 95+\",\n   *            \"version\":  4,\n   *            \"grade\":    \"X\"\n   *          },\n   *          {\n   *            \"engine\":   \"Trident\",\n   *            \"browser\":  \"Internet Explorer 5.0\",\n   *            \"platform\": \"Win 95+\",\n   *            \"version\":  5,\n   *            \"grade\":    \"C\"\n   *          }\n   *        ],\n   *        \"columns\": [\n   *          { \"title\": \"Engine\",   \"data\": \"engine\" },\n   *          { \"title\": \"Browser\",  \"data\": \"browser\" },\n   *          { \"title\": \"Platform\", \"data\": \"platform\" },\n   *          { \"title\": \"Version\",  \"data\": \"version\" },\n   *          { \"title\": \"Grade\",    \"data\": \"grade\" }\n   *        ]\n   *      } );\n   *    } );\n   */\n  \"aaData\": null,\n  /**\n   * If ordering is enabled, then DataTables will perform a first pass sort on\n   * initialisation. You can define which column(s) the sort is performed\n   * upon, and the sorting direction, with this variable. The `sorting` array\n   * should contain an array for each column to be sorted initially containing\n   * the column's index and a direction string ('asc' or 'desc').\n   *  @type array\n   *  @default [[0,'asc']]\n   *\n   *  @dtopt Option\n   *  @name DataTable.defaults.order\n   *\n   *  @example\n   *    // Sort by 3rd column first, and then 4th column\n   *    $(document).ready( function() {\n   *      $('#example').dataTable( {\n   *        \"order\": [[2,'asc'], [3,'desc']]\n   *      } );\n   *    } );\n   *\n   *    // No initial sorting\n   *    $(document).ready( function() {\n   *      $('#example').dataTable( {\n   *        \"order\": []\n   *      } );\n   *    } );\n   */\n  \"aaSorting\": [[0, 'asc']],\n  /**\n   * This parameter is basically identical to the `sorting` parameter, but\n   * cannot be overridden by user interaction with the table. What this means\n   * is that you could have a column (visible or hidden) which the sorting\n   * will always be forced on first - any sorting after that (from the user)\n   * will then be performed as required. This can be useful for grouping rows\n   * together.\n   *  @type array\n   *  @default null\n   *\n   *  @dtopt Option\n   *  @name DataTable.defaults.orderFixed\n   *\n   *  @example\n   *    $(document).ready( function() {\n   *      $('#example').dataTable( {\n   *        \"orderFixed\": [[0,'asc']]\n   *      } );\n   *    } )\n   */\n  \"aaSortingFixed\": [],\n  /**\n   * DataTables can be instructed to load data to display in the table from a\n   * Ajax source. This option defines how that Ajax call is made and where to.\n   *\n   * The `ajax` property has three different modes of operation, depending on\n   * how it is defined. These are:\n   *\n   * * `string` - Set the URL from where the data should be loaded from.\n   * * `object` - Define properties for `jQuery.ajax`.\n   * * `function` - Custom data get function\n   *\n   * `string`\n   * --------\n   *\n   * As a string, the `ajax` property simply defines the URL from which\n   * DataTables will load data.\n   *\n   * `object`\n   * --------\n   *\n   * As an object, the parameters in the object are passed to\n   * [jQuery.ajax](https://api.jquery.com/jQuery.ajax/) allowing fine control\n   * of the Ajax request. DataTables has a number of default parameters which\n   * you can override using this option. Please refer to the jQuery\n   * documentation for a full description of the options available, although\n   * the following parameters provide additional options in DataTables or\n   * require special consideration:\n   *\n   * * `data` - As with jQuery, `data` can be provided as an object, but it\n   *   can also be used as a function to manipulate the data DataTables sends\n   *   to the server. The function takes a single parameter, an object of\n   *   parameters with the values that DataTables has readied for sending. An\n   *   object may be returned which will be merged into the DataTables\n   *   defaults, or you can add the items to the object that was passed in and\n   *   not return anything from the function. This supersedes `fnServerParams`\n   *   from DataTables 1.9-.\n   *\n   * * `dataSrc` - By default DataTables will look for the property `data` (or\n   *   `aaData` for compatibility with DataTables 1.9-) when obtaining data\n   *   from an Ajax source or for server-side processing - this parameter\n   *   allows that property to be changed. You can use Javascript dotted\n   *   object notation to get a data source for multiple levels of nesting, or\n   *   it my be used as a function. As a function it takes a single parameter,\n   *   the JSON returned from the server, which can be manipulated as\n   *   required, with the returned value being that used by DataTables as the\n   *   data source for the table. This supersedes `sAjaxDataProp` from\n   *   DataTables 1.9-.\n   *\n   * * `success` - Should not be overridden it is used internally in\n   *   DataTables. To manipulate / transform the data returned by the server\n   *   use `ajax.dataSrc`, or use `ajax` as a function (see below).\n   *\n   * `function`\n   * ----------\n   *\n   * As a function, making the Ajax call is left up to yourself allowing\n   * complete control of the Ajax request. Indeed, if desired, a method other\n   * than Ajax could be used to obtain the required data, such as Web storage\n   * or an AIR database.\n   *\n   * The function is given four parameters and no return is required. The\n   * parameters are:\n   *\n   * 1. _object_ - Data to send to the server\n   * 2. _function_ - Callback function that must be executed when the required\n   *    data has been obtained. That data should be passed into the callback\n   *    as the only parameter\n   * 3. _object_ - DataTables settings object for the table\n   *\n   * Note that this supersedes `fnServerData` from DataTables 1.9-.\n   *\n   *  @type string|object|function\n   *  @default null\n   *\n   *  @dtopt Option\n   *  @name DataTable.defaults.ajax\n   *  @since 1.10.0\n   *\n   * @example\n   *   // Get JSON data from a file via Ajax.\n   *   // Note DataTables expects data in the form `{ data: [ ...data... ] }` by default).\n   *   $('#example').dataTable( {\n   *     \"ajax\": \"data.json\"\n   *   } );\n   *\n   * @example\n   *   // Get JSON data from a file via Ajax, using `dataSrc` to change\n   *   // `data` to `tableData` (i.e. `{ tableData: [ ...data... ] }`)\n   *   $('#example').dataTable( {\n   *     \"ajax\": {\n   *       \"url\": \"data.json\",\n   *       \"dataSrc\": \"tableData\"\n   *     }\n   *   } );\n   *\n   * @example\n   *   // Get JSON data from a file via Ajax, using `dataSrc` to read data\n   *   // from a plain array rather than an array in an object\n   *   $('#example').dataTable( {\n   *     \"ajax\": {\n   *       \"url\": \"data.json\",\n   *       \"dataSrc\": \"\"\n   *     }\n   *   } );\n   *\n   * @example\n   *   // Manipulate the data returned from the server - add a link to data\n   *   // (note this can, should, be done using `render` for the column - this\n   *   // is just a simple example of how the data can be manipulated).\n   *   $('#example').dataTable( {\n   *     \"ajax\": {\n   *       \"url\": \"data.json\",\n   *       \"dataSrc\": function ( json ) {\n   *         for ( var i=0, ien=json.length ; i<ien ; i++ ) {\n   *           json[i][0] = '<a href=\"/message/'+json[i][0]+'>View message</a>';\n   *         }\n   *         return json;\n   *       }\n   *     }\n   *   } );\n   *\n   * @example\n   *   // Add data to the request\n   *   $('#example').dataTable( {\n   *     \"ajax\": {\n   *       \"url\": \"data.json\",\n   *       \"data\": function ( d ) {\n   *         return {\n   *           \"extra_search\": $('#extra').val()\n   *         };\n   *       }\n   *     }\n   *   } );\n   *\n   * @example\n   *   // Send request as POST\n   *   $('#example').dataTable( {\n   *     \"ajax\": {\n   *       \"url\": \"data.json\",\n   *       \"type\": \"POST\"\n   *     }\n   *   } );\n   *\n   * @example\n   *   // Get the data from localStorage (could interface with a form for\n   *   // adding, editing and removing rows).\n   *   $('#example').dataTable( {\n   *     \"ajax\": function (data, callback, settings) {\n   *       callback(\n   *         JSON.parse( localStorage.getItem('dataTablesData') )\n   *       );\n   *     }\n   *   } );\n   */\n  \"ajax\": null,\n  /**\n   * This parameter allows you to readily specify the entries in the length drop\n   * down menu that DataTables shows when pagination is enabled. It can be\n   * either a 1D array of options which will be used for both the displayed\n   * option and the value, or a 2D array which will use the array in the first\n   * position as the value, and the array in the second position as the\n   * displayed options (useful for language strings such as 'All').\n   *\n   * Note that the `pageLength` property will be automatically set to the\n   * first value given in this array, unless `pageLength` is also provided.\n   *  @type array\n   *  @default [ 10, 25, 50, 100 ]\n   *\n   *  @dtopt Option\n   *  @name DataTable.defaults.lengthMenu\n   *\n   *  @example\n   *    $(document).ready( function() {\n   *      $('#example').dataTable( {\n   *        \"lengthMenu\": [[10, 25, 50, -1], [10, 25, 50, \"All\"]]\n   *      } );\n   *    } );\n   */\n  \"aLengthMenu\": [10, 25, 50, 100],\n  /**\n   * The `columns` option in the initialisation parameter allows you to define\n   * details about the way individual columns behave. For a full list of\n   * column options that can be set, please see\n   * {@link DataTable.defaults.column}. Note that if you use `columns` to\n   * define your columns, you must have an entry in the array for every single\n   * column that you have in your table (these can be null if you don't which\n   * to specify any options).\n   *  @member\n   *\n   *  @name DataTable.defaults.column\n   */\n  \"aoColumns\": null,\n  /**\n   * Very similar to `columns`, `columnDefs` allows you to target a specific\n   * column, multiple columns, or all columns, using the `targets` property of\n   * each object in the array. This allows great flexibility when creating\n   * tables, as the `columnDefs` arrays can be of any length, targeting the\n   * columns you specifically want. `columnDefs` may use any of the column\n   * options available: {@link DataTable.defaults.column}, but it _must_\n   * have `targets` defined in each object in the array. Values in the `targets`\n   * array may be:\n   *   <ul>\n   *     <li>a string - class name will be matched on the TH for the column</li>\n   *     <li>0 or a positive integer - column index counting from the left</li>\n   *     <li>a negative integer - column index counting from the right</li>\n   *     <li>the string \"_all\" - all columns (i.e. assign a default)</li>\n   *   </ul>\n   *  @member\n   *\n   *  @name DataTable.defaults.columnDefs\n   */\n  \"aoColumnDefs\": null,\n  /**\n   * Basically the same as `search`, this parameter defines the individual column\n   * filtering state at initialisation time. The array must be of the same size\n   * as the number of columns, and each element be an object with the parameters\n   * `search` and `escapeRegex` (the latter is optional). 'null' is also\n   * accepted and the default will be used.\n   *  @type array\n   *  @default []\n   *\n   *  @dtopt Option\n   *  @name DataTable.defaults.searchCols\n   *\n   *  @example\n   *    $(document).ready( function() {\n   *      $('#example').dataTable( {\n   *        \"searchCols\": [\n   *          null,\n   *          { \"search\": \"My filter\" },\n   *          null,\n   *          { \"search\": \"^[0-9]\", \"escapeRegex\": false }\n   *        ]\n   *      } );\n   *    } )\n   */\n  \"aoSearchCols\": [],\n  /**\n   * An array of CSS classes that should be applied to displayed rows. This\n   * array may be of any length, and DataTables will apply each class\n   * sequentially, looping when required.\n   *  @type array\n   *  @default null <i>Will take the values determined by the `oClasses.stripe*`\n   *    options</i>\n   *\n   *  @dtopt Option\n   *  @name DataTable.defaults.stripeClasses\n   *\n   *  @example\n   *    $(document).ready( function() {\n   *      $('#example').dataTable( {\n   *        \"stripeClasses\": [ 'strip1', 'strip2', 'strip3' ]\n   *      } );\n   *    } )\n   */\n  \"asStripeClasses\": null,\n  /**\n   * Enable or disable automatic column width calculation. This can be disabled\n   * as an optimisation (it takes some time to calculate the widths) if the\n   * tables widths are passed in using `columns`.\n   *  @type boolean\n   *  @default true\n   *\n   *  @dtopt Features\n   *  @name DataTable.defaults.autoWidth\n   *\n   *  @example\n   *    $(document).ready( function () {\n   *      $('#example').dataTable( {\n   *        \"autoWidth\": false\n   *      } );\n   *    } );\n   */\n  \"bAutoWidth\": true,\n  /**\n   * Deferred rendering can provide DataTables with a huge speed boost when you\n   * are using an Ajax or JS data source for the table. This option, when set to\n   * true, will cause DataTables to defer the creation of the table elements for\n   * each row until they are needed for a draw - saving a significant amount of\n   * time.\n   *  @type boolean\n   *  @default false\n   *\n   *  @dtopt Features\n   *  @name DataTable.defaults.deferRender\n   *\n   *  @example\n   *    $(document).ready( function() {\n   *      $('#example').dataTable( {\n   *        \"ajax\": \"sources/arrays.txt\",\n   *        \"deferRender\": true\n   *      } );\n   *    } );\n   */\n  \"bDeferRender\": false,\n  /**\n   * Replace a DataTable which matches the given selector and replace it with\n   * one which has the properties of the new initialisation object passed. If no\n   * table matches the selector, then the new DataTable will be constructed as\n   * per normal.\n   *  @type boolean\n   *  @default false\n   *\n   *  @dtopt Options\n   *  @name DataTable.defaults.destroy\n   *\n   *  @example\n   *    $(document).ready( function() {\n   *      $('#example').dataTable( {\n   *        \"srollY\": \"200px\",\n   *        \"paginate\": false\n   *      } );\n   *\n   *      // Some time later....\n   *      $('#example').dataTable( {\n   *        \"filter\": false,\n   *        \"destroy\": true\n   *      } );\n   *    } );\n   */\n  \"bDestroy\": false,\n  /**\n   * Enable or disable filtering of data. Filtering in DataTables is \"smart\" in\n   * that it allows the end user to input multiple words (space separated) and\n   * will match a row containing those words, even if not in the order that was\n   * specified (this allow matching across multiple columns). Note that if you\n   * wish to use filtering in DataTables this must remain 'true' - to remove the\n   * default filtering input box and retain filtering abilities, please use\n   * {@link DataTable.defaults.dom}.\n   *  @type boolean\n   *  @default true\n   *\n   *  @dtopt Features\n   *  @name DataTable.defaults.searching\n   *\n   *  @example\n   *    $(document).ready( function () {\n   *      $('#example').dataTable( {\n   *        \"searching\": false\n   *      } );\n   *    } );\n   */\n  \"bFilter\": true,\n  /**\n   * Enable or disable the table information display. This shows information\n   * about the data that is currently visible on the page, including information\n   * about filtered data if that action is being performed.\n   *  @type boolean\n   *  @default true\n   *\n   *  @dtopt Features\n   *  @name DataTable.defaults.info\n   *\n   *  @example\n   *    $(document).ready( function () {\n   *      $('#example').dataTable( {\n   *        \"info\": false\n   *      } );\n   *    } );\n   */\n  \"bInfo\": true,\n  /**\n   * Allows the end user to select the size of a formatted page from a select\n   * menu (sizes are 10, 25, 50 and 100). Requires pagination (`paginate`).\n   *  @type boolean\n   *  @default true\n   *\n   *  @dtopt Features\n   *  @name DataTable.defaults.lengthChange\n   *\n   *  @example\n   *    $(document).ready( function () {\n   *      $('#example').dataTable( {\n   *        \"lengthChange\": false\n   *      } );\n   *    } );\n   */\n  \"bLengthChange\": true,\n  /**\n   * Enable or disable pagination.\n   *  @type boolean\n   *  @default true\n   *\n   *  @dtopt Features\n   *  @name DataTable.defaults.paging\n   *\n   *  @example\n   *    $(document).ready( function () {\n   *      $('#example').dataTable( {\n   *        \"paging\": false\n   *      } );\n   *    } );\n   */\n  \"bPaginate\": true,\n  /**\n   * Enable or disable the display of a 'processing' indicator when the table is\n   * being processed (e.g. a sort). This is particularly useful for tables with\n   * large amounts of data where it can take a noticeable amount of time to sort\n   * the entries.\n   *  @type boolean\n   *  @default false\n   *\n   *  @dtopt Features\n   *  @name DataTable.defaults.processing\n   *\n   *  @example\n   *    $(document).ready( function () {\n   *      $('#example').dataTable( {\n   *        \"processing\": true\n   *      } );\n   *    } );\n   */\n  \"bProcessing\": false,\n  /**\n   * Retrieve the DataTables object for the given selector. Note that if the\n   * table has already been initialised, this parameter will cause DataTables\n   * to simply return the object that has already been set up - it will not take\n   * account of any changes you might have made to the initialisation object\n   * passed to DataTables (setting this parameter to true is an acknowledgement\n   * that you understand this). `destroy` can be used to reinitialise a table if\n   * you need.\n   *  @type boolean\n   *  @default false\n   *\n   *  @dtopt Options\n   *  @name DataTable.defaults.retrieve\n   *\n   *  @example\n   *    $(document).ready( function() {\n   *      initTable();\n   *      tableActions();\n   *    } );\n   *\n   *    function initTable ()\n   *    {\n   *      return $('#example').dataTable( {\n   *        \"scrollY\": \"200px\",\n   *        \"paginate\": false,\n   *        \"retrieve\": true\n   *      } );\n   *    }\n   *\n   *    function tableActions ()\n   *    {\n   *      var table = initTable();\n   *      // perform API operations with oTable\n   *    }\n   */\n  \"bRetrieve\": false,\n  /**\n   * When vertical (y) scrolling is enabled, DataTables will force the height of\n   * the table's viewport to the given height at all times (useful for layout).\n   * However, this can look odd when filtering data down to a small data set,\n   * and the footer is left \"floating\" further down. This parameter (when\n   * enabled) will cause DataTables to collapse the table's viewport down when\n   * the result set will fit within the given Y height.\n   *  @type boolean\n   *  @default false\n   *\n   *  @dtopt Options\n   *  @name DataTable.defaults.scrollCollapse\n   *\n   *  @example\n   *    $(document).ready( function() {\n   *      $('#example').dataTable( {\n   *        \"scrollY\": \"200\",\n   *        \"scrollCollapse\": true\n   *      } );\n   *    } );\n   */\n  \"bScrollCollapse\": false,\n  /**\n   * Configure DataTables to use server-side processing. Note that the\n   * `ajax` parameter must also be given in order to give DataTables a\n   * source to obtain the required data for each draw.\n   *  @type boolean\n   *  @default false\n   *\n   *  @dtopt Features\n   *  @dtopt Server-side\n   *  @name DataTable.defaults.serverSide\n   *\n   *  @example\n   *    $(document).ready( function () {\n   *      $('#example').dataTable( {\n   *        \"serverSide\": true,\n   *        \"ajax\": \"xhr.php\"\n   *      } );\n   *    } );\n   */\n  \"bServerSide\": false,\n  /**\n   * Enable or disable sorting of columns. Sorting of individual columns can be\n   * disabled by the `sortable` option for each column.\n   *  @type boolean\n   *  @default true\n   *\n   *  @dtopt Features\n   *  @name DataTable.defaults.ordering\n   *\n   *  @example\n   *    $(document).ready( function () {\n   *      $('#example').dataTable( {\n   *        \"ordering\": false\n   *      } );\n   *    } );\n   */\n  \"bSort\": true,\n  /**\n   * Enable or display DataTables' ability to sort multiple columns at the\n   * same time (activated by shift-click by the user).\n   *  @type boolean\n   *  @default true\n   *\n   *  @dtopt Options\n   *  @name DataTable.defaults.orderMulti\n   *\n   *  @example\n   *    // Disable multiple column sorting ability\n   *    $(document).ready( function () {\n   *      $('#example').dataTable( {\n   *        \"orderMulti\": false\n   *      } );\n   *    } );\n   */\n  \"bSortMulti\": true,\n  /**\n   * Allows control over whether DataTables should use the top (true) unique\n   * cell that is found for a single column, or the bottom (false - default).\n   * This is useful when using complex headers.\n   *  @type boolean\n   *  @default false\n   *\n   *  @dtopt Options\n   *  @name DataTable.defaults.orderCellsTop\n   *\n   *  @example\n   *    $(document).ready( function() {\n   *      $('#example').dataTable( {\n   *        \"orderCellsTop\": true\n   *      } );\n   *    } );\n   */\n  \"bSortCellsTop\": false,\n  /**\n   * Enable or disable the addition of the classes `sorting\\_1`, `sorting\\_2` and\n   * `sorting\\_3` to the columns which are currently being sorted on. This is\n   * presented as a feature switch as it can increase processing time (while\n   * classes are removed and added) so for large data sets you might want to\n   * turn this off.\n   *  @type boolean\n   *  @default true\n   *\n   *  @dtopt Features\n   *  @name DataTable.defaults.orderClasses\n   *\n   *  @example\n   *    $(document).ready( function () {\n   *      $('#example').dataTable( {\n   *        \"orderClasses\": false\n   *      } );\n   *    } );\n   */\n  \"bSortClasses\": true,\n  /**\n   * Enable or disable state saving. When enabled HTML5 `localStorage` will be\n   * used to save table display information such as pagination information,\n   * display length, filtering and sorting. As such when the end user reloads\n   * the page the display display will match what thy had previously set up.\n   *\n   * Due to the use of `localStorage` the default state saving is not supported\n   * in IE6 or 7. If state saving is required in those browsers, use\n   * `stateSaveCallback` to provide a storage solution such as cookies.\n   *  @type boolean\n   *  @default false\n   *\n   *  @dtopt Features\n   *  @name DataTable.defaults.stateSave\n   *\n   *  @example\n   *    $(document).ready( function () {\n   *      $('#example').dataTable( {\n   *        \"stateSave\": true\n   *      } );\n   *    } );\n   */\n  \"bStateSave\": false,\n  /**\n   * This function is called when a TR element is created (and all TD child\n   * elements have been inserted), or registered if using a DOM source, allowing\n   * manipulation of the TR element (adding classes etc).\n   *  @type function\n   *  @param {node} row \"TR\" element for the current row\n   *  @param {array} data Raw data array for this row\n   *  @param {int} dataIndex The index of this row in the internal aoData array\n   *\n   *  @dtopt Callbacks\n   *  @name DataTable.defaults.createdRow\n   *\n   *  @example\n   *    $(document).ready( function() {\n   *      $('#example').dataTable( {\n   *        \"createdRow\": function( row, data, dataIndex ) {\n   *          // Bold the grade for all 'A' grade browsers\n   *          if ( data[4] == \"A\" )\n   *          {\n   *            $('td:eq(4)', row).html( '<b>A</b>' );\n   *          }\n   *        }\n   *      } );\n   *    } );\n   */\n  \"fnCreatedRow\": null,\n  /**\n   * This function is called on every 'draw' event, and allows you to\n   * dynamically modify any aspect you want about the created DOM.\n   *  @type function\n   *  @param {object} settings DataTables settings object\n   *\n   *  @dtopt Callbacks\n   *  @name DataTable.defaults.drawCallback\n   *\n   *  @example\n   *    $(document).ready( function() {\n   *      $('#example').dataTable( {\n   *        \"drawCallback\": function( settings ) {\n   *          alert( 'DataTables has redrawn the table' );\n   *        }\n   *      } );\n   *    } );\n   */\n  \"fnDrawCallback\": null,\n  /**\n   * Identical to fnHeaderCallback() but for the table footer this function\n   * allows you to modify the table footer on every 'draw' event.\n   *  @type function\n   *  @param {node} foot \"TR\" element for the footer\n   *  @param {array} data Full table data (as derived from the original HTML)\n   *  @param {int} start Index for the current display starting point in the\n   *    display array\n   *  @param {int} end Index for the current display ending point in the\n   *    display array\n   *  @param {array int} display Index array to translate the visual position\n   *    to the full data array\n   *\n   *  @dtopt Callbacks\n   *  @name DataTable.defaults.footerCallback\n   *\n   *  @example\n   *    $(document).ready( function() {\n   *      $('#example').dataTable( {\n   *        \"footerCallback\": function( tfoot, data, start, end, display ) {\n   *          tfoot.getElementsByTagName('th')[0].innerHTML = \"Starting index is \"+start;\n   *        }\n   *      } );\n   *    } )\n   */\n  \"fnFooterCallback\": null,\n  /**\n   * When rendering large numbers in the information element for the table\n   * (i.e. \"Showing 1 to 10 of 57 entries\") DataTables will render large numbers\n   * to have a comma separator for the 'thousands' units (e.g. 1 million is\n   * rendered as \"1,000,000\") to help readability for the end user. This\n   * function will override the default method DataTables uses.\n   *  @type function\n   *  @member\n   *  @param {int} toFormat number to be formatted\n   *  @returns {string} formatted string for DataTables to show the number\n   *\n   *  @dtopt Callbacks\n   *  @name DataTable.defaults.formatNumber\n   *\n   *  @example\n   *    // Format a number using a single quote for the separator (note that\n   *    // this can also be done with the language.thousands option)\n   *    $(document).ready( function() {\n   *      $('#example').dataTable( {\n   *        \"formatNumber\": function ( toFormat ) {\n   *          return toFormat.toString().replace(\n   *            /\\B(?=(\\d{3})+(?!\\d))/g, \"'\"\n   *          );\n   *        };\n   *      } );\n   *    } );\n   */\n  \"fnFormatNumber\": function (toFormat) {\n    return toFormat.toString().replace(/\\B(?=(\\d{3})+(?!\\d))/g, this.oLanguage.sThousands);\n  },\n  /**\n   * This function is called on every 'draw' event, and allows you to\n   * dynamically modify the header row. This can be used to calculate and\n   * display useful information about the table.\n   *  @type function\n   *  @param {node} head \"TR\" element for the header\n   *  @param {array} data Full table data (as derived from the original HTML)\n   *  @param {int} start Index for the current display starting point in the\n   *    display array\n   *  @param {int} end Index for the current display ending point in the\n   *    display array\n   *  @param {array int} display Index array to translate the visual position\n   *    to the full data array\n   *\n   *  @dtopt Callbacks\n   *  @name DataTable.defaults.headerCallback\n   *\n   *  @example\n   *    $(document).ready( function() {\n   *      $('#example').dataTable( {\n   *        \"fheaderCallback\": function( head, data, start, end, display ) {\n   *          head.getElementsByTagName('th')[0].innerHTML = \"Displaying \"+(end-start)+\" records\";\n   *        }\n   *      } );\n   *    } )\n   */\n  \"fnHeaderCallback\": null,\n  /**\n   * The information element can be used to convey information about the current\n   * state of the table. Although the internationalisation options presented by\n   * DataTables are quite capable of dealing with most customisations, there may\n   * be times where you wish to customise the string further. This callback\n   * allows you to do exactly that.\n   *  @type function\n   *  @param {object} oSettings DataTables settings object\n   *  @param {int} start Starting position in data for the draw\n   *  @param {int} end End position in data for the draw\n   *  @param {int} max Total number of rows in the table (regardless of\n   *    filtering)\n   *  @param {int} total Total number of rows in the data set, after filtering\n   *  @param {string} pre The string that DataTables has formatted using it's\n   *    own rules\n   *  @returns {string} The string to be displayed in the information element.\n   *\n   *  @dtopt Callbacks\n   *  @name DataTable.defaults.infoCallback\n   *\n   *  @example\n   *    $('#example').dataTable( {\n   *      \"infoCallback\": function( settings, start, end, max, total, pre ) {\n   *        return start +\" to \"+ end;\n   *      }\n   *    } );\n   */\n  \"fnInfoCallback\": null,\n  /**\n   * Called when the table has been initialised. Normally DataTables will\n   * initialise sequentially and there will be no need for this function,\n   * however, this does not hold true when using external language information\n   * since that is obtained using an async XHR call.\n   *  @type function\n   *  @param {object} settings DataTables settings object\n   *  @param {object} json The JSON object request from the server - only\n   *    present if client-side Ajax sourced data is used\n   *\n   *  @dtopt Callbacks\n   *  @name DataTable.defaults.initComplete\n   *\n   *  @example\n   *    $(document).ready( function() {\n   *      $('#example').dataTable( {\n   *        \"initComplete\": function(settings, json) {\n   *          alert( 'DataTables has finished its initialisation.' );\n   *        }\n   *      } );\n   *    } )\n   */\n  \"fnInitComplete\": null,\n  /**\n   * Called at the very start of each table draw and can be used to cancel the\n   * draw by returning false, any other return (including undefined) results in\n   * the full draw occurring).\n   *  @type function\n   *  @param {object} settings DataTables settings object\n   *  @returns {boolean} False will cancel the draw, anything else (including no\n   *    return) will allow it to complete.\n   *\n   *  @dtopt Callbacks\n   *  @name DataTable.defaults.preDrawCallback\n   *\n   *  @example\n   *    $(document).ready( function() {\n   *      $('#example').dataTable( {\n   *        \"preDrawCallback\": function( settings ) {\n   *          if ( $('#test').val() == 1 ) {\n   *            return false;\n   *          }\n   *        }\n   *      } );\n   *    } );\n   */\n  \"fnPreDrawCallback\": null,\n  /**\n   * This function allows you to 'post process' each row after it have been\n   * generated for each table draw, but before it is rendered on screen. This\n   * function might be used for setting the row class name etc.\n   *  @type function\n   *  @param {node} row \"TR\" element for the current row\n   *  @param {array} data Raw data array for this row\n   *  @param {int} displayIndex The display index for the current table draw\n   *  @param {int} displayIndexFull The index of the data in the full list of\n   *    rows (after filtering)\n   *\n   *  @dtopt Callbacks\n   *  @name DataTable.defaults.rowCallback\n   *\n   *  @example\n   *    $(document).ready( function() {\n   *      $('#example').dataTable( {\n   *        \"rowCallback\": function( row, data, displayIndex, displayIndexFull ) {\n   *          // Bold the grade for all 'A' grade browsers\n   *          if ( data[4] == \"A\" ) {\n   *            $('td:eq(4)', row).html( '<b>A</b>' );\n   *          }\n   *        }\n   *      } );\n   *    } );\n   */\n  \"fnRowCallback\": null,\n  /**\n   * __Deprecated__ The functionality provided by this parameter has now been\n   * superseded by that provided through `ajax`, which should be used instead.\n   *\n   * This parameter allows you to override the default function which obtains\n   * the data from the server so something more suitable for your application.\n   * For example you could use POST data, or pull information from a Gears or\n   * AIR database.\n   *  @type function\n   *  @member\n   *  @param {string} source HTTP source to obtain the data from (`ajax`)\n   *  @param {array} data A key/value pair object containing the data to send\n   *    to the server\n   *  @param {function} callback to be called on completion of the data get\n   *    process that will draw the data on the page.\n   *  @param {object} settings DataTables settings object\n   *\n   *  @dtopt Callbacks\n   *  @dtopt Server-side\n   *  @name DataTable.defaults.serverData\n   *\n   *  @deprecated 1.10. Please use `ajax` for this functionality now.\n   */\n  \"fnServerData\": null,\n  /**\n   * __Deprecated__ The functionality provided by this parameter has now been\n   * superseded by that provided through `ajax`, which should be used instead.\n   *\n   *  It is often useful to send extra data to the server when making an Ajax\n   * request - for example custom filtering information, and this callback\n   * function makes it trivial to send extra information to the server. The\n   * passed in parameter is the data set that has been constructed by\n   * DataTables, and you can add to this or modify it as you require.\n   *  @type function\n   *  @param {array} data Data array (array of objects which are name/value\n   *    pairs) that has been constructed by DataTables and will be sent to the\n   *    server. In the case of Ajax sourced data with server-side processing\n   *    this will be an empty array, for server-side processing there will be a\n   *    significant number of parameters!\n   *  @returns {undefined} Ensure that you modify the data array passed in,\n   *    as this is passed by reference.\n   *\n   *  @dtopt Callbacks\n   *  @dtopt Server-side\n   *  @name DataTable.defaults.serverParams\n   *\n   *  @deprecated 1.10. Please use `ajax` for this functionality now.\n   */\n  \"fnServerParams\": null,\n  /**\n   * Load the table state. With this function you can define from where, and how, the\n   * state of a table is loaded. By default DataTables will load from `localStorage`\n   * but you might wish to use a server-side database or cookies.\n   *  @type function\n   *  @member\n   *  @param {object} settings DataTables settings object\n   *  @param {object} callback Callback that can be executed when done. It\n   *    should be passed the loaded state object.\n   *  @return {object} The DataTables state object to be loaded\n   *\n   *  @dtopt Callbacks\n   *  @name DataTable.defaults.stateLoadCallback\n   *\n   *  @example\n   *    $(document).ready( function() {\n   *      $('#example').dataTable( {\n   *        \"stateSave\": true,\n   *        \"stateLoadCallback\": function (settings, callback) {\n   *          $.ajax( {\n   *            \"url\": \"/state_load\",\n   *            \"dataType\": \"json\",\n   *            \"success\": function (json) {\n   *              callback( json );\n   *            }\n   *          } );\n   *        }\n   *      } );\n   *    } );\n   */\n  \"fnStateLoadCallback\": function (settings) {\n    try {\n      return JSON.parse((settings.iStateDuration === -1 ? sessionStorage : localStorage).getItem('DataTables_' + settings.sInstance + '_' + location.pathname));\n    } catch (e) {\n      return {};\n    }\n  },\n  /**\n   * Callback which allows modification of the saved state prior to loading that state.\n   * This callback is called when the table is loading state from the stored data, but\n   * prior to the settings object being modified by the saved state. Note that for\n   * plug-in authors, you should use the `stateLoadParams` event to load parameters for\n   * a plug-in.\n   *  @type function\n   *  @param {object} settings DataTables settings object\n   *  @param {object} data The state object that is to be loaded\n   *\n   *  @dtopt Callbacks\n   *  @name DataTable.defaults.stateLoadParams\n   *\n   *  @example\n   *    // Remove a saved filter, so filtering is never loaded\n   *    $(document).ready( function() {\n   *      $('#example').dataTable( {\n   *        \"stateSave\": true,\n   *        \"stateLoadParams\": function (settings, data) {\n   *          data.oSearch.sSearch = \"\";\n   *        }\n   *      } );\n   *    } );\n   *\n   *  @example\n   *    // Disallow state loading by returning false\n   *    $(document).ready( function() {\n   *      $('#example').dataTable( {\n   *        \"stateSave\": true,\n   *        \"stateLoadParams\": function (settings, data) {\n   *          return false;\n   *        }\n   *      } );\n   *    } );\n   */\n  \"fnStateLoadParams\": null,\n  /**\n   * Callback that is called when the state has been loaded from the state saving method\n   * and the DataTables settings object has been modified as a result of the loaded state.\n   *  @type function\n   *  @param {object} settings DataTables settings object\n   *  @param {object} data The state object that was loaded\n   *\n   *  @dtopt Callbacks\n   *  @name DataTable.defaults.stateLoaded\n   *\n   *  @example\n   *    // Show an alert with the filtering value that was saved\n   *    $(document).ready( function() {\n   *      $('#example').dataTable( {\n   *        \"stateSave\": true,\n   *        \"stateLoaded\": function (settings, data) {\n   *          alert( 'Saved filter was: '+data.oSearch.sSearch );\n   *        }\n   *      } );\n   *    } );\n   */\n  \"fnStateLoaded\": null,\n  /**\n   * Save the table state. This function allows you to define where and how the state\n   * information for the table is stored By default DataTables will use `localStorage`\n   * but you might wish to use a server-side database or cookies.\n   *  @type function\n   *  @member\n   *  @param {object} settings DataTables settings object\n   *  @param {object} data The state object to be saved\n   *\n   *  @dtopt Callbacks\n   *  @name DataTable.defaults.stateSaveCallback\n   *\n   *  @example\n   *    $(document).ready( function() {\n   *      $('#example').dataTable( {\n   *        \"stateSave\": true,\n   *        \"stateSaveCallback\": function (settings, data) {\n   *          // Send an Ajax request to the server with the state object\n   *          $.ajax( {\n   *            \"url\": \"/state_save\",\n   *            \"data\": data,\n   *            \"dataType\": \"json\",\n   *            \"method\": \"POST\"\n   *            \"success\": function () {}\n   *          } );\n   *        }\n   *      } );\n   *    } );\n   */\n  \"fnStateSaveCallback\": function (settings, data) {\n    try {\n      (settings.iStateDuration === -1 ? sessionStorage : localStorage).setItem('DataTables_' + settings.sInstance + '_' + location.pathname, JSON.stringify(data));\n    } catch (e) {}\n  },\n  /**\n   * Callback which allows modification of the state to be saved. Called when the table\n   * has changed state a new state save is required. This method allows modification of\n   * the state saving object prior to actually doing the save, including addition or\n   * other state properties or modification. Note that for plug-in authors, you should\n   * use the `stateSaveParams` event to save parameters for a plug-in.\n   *  @type function\n   *  @param {object} settings DataTables settings object\n   *  @param {object} data The state object to be saved\n   *\n   *  @dtopt Callbacks\n   *  @name DataTable.defaults.stateSaveParams\n   *\n   *  @example\n   *    // Remove a saved filter, so filtering is never saved\n   *    $(document).ready( function() {\n   *      $('#example').dataTable( {\n   *        \"stateSave\": true,\n   *        \"stateSaveParams\": function (settings, data) {\n   *          data.oSearch.sSearch = \"\";\n   *        }\n   *      } );\n   *    } );\n   */\n  \"fnStateSaveParams\": null,\n  /**\n   * Duration for which the saved state information is considered valid. After this period\n   * has elapsed the state will be returned to the default.\n   * Value is given in seconds.\n   *  @type int\n   *  @default 7200 <i>(2 hours)</i>\n   *\n   *  @dtopt Options\n   *  @name DataTable.defaults.stateDuration\n   *\n   *  @example\n   *    $(document).ready( function() {\n   *      $('#example').dataTable( {\n   *        \"stateDuration\": 60*60*24; // 1 day\n   *      } );\n   *    } )\n   */\n  \"iStateDuration\": 7200,\n  /**\n   * When enabled DataTables will not make a request to the server for the first\n   * page draw - rather it will use the data already on the page (no sorting etc\n   * will be applied to it), thus saving on an XHR at load time. `deferLoading`\n   * is used to indicate that deferred loading is required, but it is also used\n   * to tell DataTables how many records there are in the full table (allowing\n   * the information element and pagination to be displayed correctly). In the case\n   * where a filtering is applied to the table on initial load, this can be\n   * indicated by giving the parameter as an array, where the first element is\n   * the number of records available after filtering and the second element is the\n   * number of records without filtering (allowing the table information element\n   * to be shown correctly).\n   *  @type int | array\n   *  @default null\n   *\n   *  @dtopt Options\n   *  @name DataTable.defaults.deferLoading\n   *\n   *  @example\n   *    // 57 records available in the table, no filtering applied\n   *    $(document).ready( function() {\n   *      $('#example').dataTable( {\n   *        \"serverSide\": true,\n   *        \"ajax\": \"scripts/server_processing.php\",\n   *        \"deferLoading\": 57\n   *      } );\n   *    } );\n   *\n   *  @example\n   *    // 57 records after filtering, 100 without filtering (an initial filter applied)\n   *    $(document).ready( function() {\n   *      $('#example').dataTable( {\n   *        \"serverSide\": true,\n   *        \"ajax\": \"scripts/server_processing.php\",\n   *        \"deferLoading\": [ 57, 100 ],\n   *        \"search\": {\n   *          \"search\": \"my_filter\"\n   *        }\n   *      } );\n   *    } );\n   */\n  \"iDeferLoading\": null,\n  /**\n   * Number of rows to display on a single page when using pagination. If\n   * feature enabled (`lengthChange`) then the end user will be able to override\n   * this to a custom setting using a pop-up menu.\n   *  @type int\n   *  @default 10\n   *\n   *  @dtopt Options\n   *  @name DataTable.defaults.pageLength\n   *\n   *  @example\n   *    $(document).ready( function() {\n   *      $('#example').dataTable( {\n   *        \"pageLength\": 50\n   *      } );\n   *    } )\n   */\n  \"iDisplayLength\": 10,\n  /**\n   * Define the starting point for data display when using DataTables with\n   * pagination. Note that this parameter is the number of records, rather than\n   * the page number, so if you have 10 records per page and want to start on\n   * the third page, it should be \"20\".\n   *  @type int\n   *  @default 0\n   *\n   *  @dtopt Options\n   *  @name DataTable.defaults.displayStart\n   *\n   *  @example\n   *    $(document).ready( function() {\n   *      $('#example').dataTable( {\n   *        \"displayStart\": 20\n   *      } );\n   *    } )\n   */\n  \"iDisplayStart\": 0,\n  /**\n   * By default DataTables allows keyboard navigation of the table (sorting, paging,\n   * and filtering) by adding a `tabindex` attribute to the required elements. This\n   * allows you to tab through the controls and press the enter key to activate them.\n   * The tabindex is default 0, meaning that the tab follows the flow of the document.\n   * You can overrule this using this parameter if you wish. Use a value of -1 to\n   * disable built-in keyboard navigation.\n   *  @type int\n   *  @default 0\n   *\n   *  @dtopt Options\n   *  @name DataTable.defaults.tabIndex\n   *\n   *  @example\n   *    $(document).ready( function() {\n   *      $('#example').dataTable( {\n   *        \"tabIndex\": 1\n   *      } );\n   *    } );\n   */\n  \"iTabIndex\": 0,\n  /**\n   * Classes that DataTables assigns to the various components and features\n   * that it adds to the HTML table. This allows classes to be configured\n   * during initialisation in addition to through the static\n   * {@link DataTable.ext.oStdClasses} object).\n   *  @namespace\n   *  @name DataTable.defaults.classes\n   */\n  \"oClasses\": {},\n  /**\n   * All strings that DataTables uses in the user interface that it creates\n   * are defined in this object, allowing you to modified them individually or\n   * completely replace them all as required.\n   *  @namespace\n   *  @name DataTable.defaults.language\n   */\n  \"oLanguage\": {\n    /**\n     * Strings that are used for WAI-ARIA labels and controls only (these are not\n     * actually visible on the page, but will be read by screenreaders, and thus\n     * must be internationalised as well).\n     *  @namespace\n     *  @name DataTable.defaults.language.aria\n     */\n    \"oAria\": {\n      /**\n       * ARIA label that is added to the table headers when the column may be\n       * sorted ascending by activing the column (click or return when focused).\n       * Note that the column header is prefixed to this string.\n       *  @type string\n       *  @default : activate to sort column ascending\n       *\n       *  @dtopt Language\n       *  @name DataTable.defaults.language.aria.sortAscending\n       *\n       *  @example\n       *    $(document).ready( function() {\n       *      $('#example').dataTable( {\n       *        \"language\": {\n       *          \"aria\": {\n       *            \"sortAscending\": \" - click/return to sort ascending\"\n       *          }\n       *        }\n       *      } );\n       *    } );\n       */\n      \"sSortAscending\": \": activate to sort column ascending\",\n      /**\n       * ARIA label that is added to the table headers when the column may be\n       * sorted descending by activing the column (click or return when focused).\n       * Note that the column header is prefixed to this string.\n       *  @type string\n       *  @default : activate to sort column ascending\n       *\n       *  @dtopt Language\n       *  @name DataTable.defaults.language.aria.sortDescending\n       *\n       *  @example\n       *    $(document).ready( function() {\n       *      $('#example').dataTable( {\n       *        \"language\": {\n       *          \"aria\": {\n       *            \"sortDescending\": \" - click/return to sort descending\"\n       *          }\n       *        }\n       *      } );\n       *    } );\n       */\n      \"sSortDescending\": \": activate to sort column descending\"\n    },\n    /**\n     * Pagination string used by DataTables for the built-in pagination\n     * control types.\n     *  @namespace\n     *  @name DataTable.defaults.language.paginate\n     */\n    \"oPaginate\": {\n      /**\n       * Text to use when using the 'full_numbers' type of pagination for the\n       * button to take the user to the first page.\n       *  @type string\n       *  @default First\n       *\n       *  @dtopt Language\n       *  @name DataTable.defaults.language.paginate.first\n       *\n       *  @example\n       *    $(document).ready( function() {\n       *      $('#example').dataTable( {\n       *        \"language\": {\n       *          \"paginate\": {\n       *            \"first\": \"First page\"\n       *          }\n       *        }\n       *      } );\n       *    } );\n       */\n      \"sFirst\": \"First\",\n      /**\n       * Text to use when using the 'full_numbers' type of pagination for the\n       * button to take the user to the last page.\n       *  @type string\n       *  @default Last\n       *\n       *  @dtopt Language\n       *  @name DataTable.defaults.language.paginate.last\n       *\n       *  @example\n       *    $(document).ready( function() {\n       *      $('#example').dataTable( {\n       *        \"language\": {\n       *          \"paginate\": {\n       *            \"last\": \"Last page\"\n       *          }\n       *        }\n       *      } );\n       *    } );\n       */\n      \"sLast\": \"Last\",\n      /**\n       * Text to use for the 'next' pagination button (to take the user to the\n       * next page).\n       *  @type string\n       *  @default Next\n       *\n       *  @dtopt Language\n       *  @name DataTable.defaults.language.paginate.next\n       *\n       *  @example\n       *    $(document).ready( function() {\n       *      $('#example').dataTable( {\n       *        \"language\": {\n       *          \"paginate\": {\n       *            \"next\": \"Next page\"\n       *          }\n       *        }\n       *      } );\n       *    } );\n       */\n      \"sNext\": \"Next\",\n      /**\n       * Text to use for the 'previous' pagination button (to take the user to\n       * the previous page).\n       *  @type string\n       *  @default Previous\n       *\n       *  @dtopt Language\n       *  @name DataTable.defaults.language.paginate.previous\n       *\n       *  @example\n       *    $(document).ready( function() {\n       *      $('#example').dataTable( {\n       *        \"language\": {\n       *          \"paginate\": {\n       *            \"previous\": \"Previous page\"\n       *          }\n       *        }\n       *      } );\n       *    } );\n       */\n      \"sPrevious\": \"Previous\"\n    },\n    /**\n     * This string is shown in preference to `zeroRecords` when the table is\n     * empty of data (regardless of filtering). Note that this is an optional\n     * parameter - if it is not given, the value of `zeroRecords` will be used\n     * instead (either the default or given value).\n     *  @type string\n     *  @default No data available in table\n     *\n     *  @dtopt Language\n     *  @name DataTable.defaults.language.emptyTable\n     *\n     *  @example\n     *    $(document).ready( function() {\n     *      $('#example').dataTable( {\n     *        \"language\": {\n     *          \"emptyTable\": \"No data available in table\"\n     *        }\n     *      } );\n     *    } );\n     */\n    \"sEmptyTable\": \"No data available in table\",\n    /**\n     * This string gives information to the end user about the information\n     * that is current on display on the page. The following tokens can be\n     * used in the string and will be dynamically replaced as the table\n     * display updates. This tokens can be placed anywhere in the string, or\n     * removed as needed by the language requires:\n     *\n     * * `\\_START\\_` - Display index of the first record on the current page\n     * * `\\_END\\_` - Display index of the last record on the current page\n     * * `\\_TOTAL\\_` - Number of records in the table after filtering\n     * * `\\_MAX\\_` - Number of records in the table without filtering\n     * * `\\_PAGE\\_` - Current page number\n     * * `\\_PAGES\\_` - Total number of pages of data in the table\n     *\n     *  @type string\n     *  @default Showing _START_ to _END_ of _TOTAL_ entries\n     *\n     *  @dtopt Language\n     *  @name DataTable.defaults.language.info\n     *\n     *  @example\n     *    $(document).ready( function() {\n     *      $('#example').dataTable( {\n     *        \"language\": {\n     *          \"info\": \"Showing page _PAGE_ of _PAGES_\"\n     *        }\n     *      } );\n     *    } );\n     */\n    \"sInfo\": \"Showing _START_ to _END_ of _TOTAL_ entries\",\n    /**\n     * Display information string for when the table is empty. Typically the\n     * format of this string should match `info`.\n     *  @type string\n     *  @default Showing 0 to 0 of 0 entries\n     *\n     *  @dtopt Language\n     *  @name DataTable.defaults.language.infoEmpty\n     *\n     *  @example\n     *    $(document).ready( function() {\n     *      $('#example').dataTable( {\n     *        \"language\": {\n     *          \"infoEmpty\": \"No entries to show\"\n     *        }\n     *      } );\n     *    } );\n     */\n    \"sInfoEmpty\": \"Showing 0 to 0 of 0 entries\",\n    /**\n     * When a user filters the information in a table, this string is appended\n     * to the information (`info`) to give an idea of how strong the filtering\n     * is. The variable _MAX_ is dynamically updated.\n     *  @type string\n     *  @default (filtered from _MAX_ total entries)\n     *\n     *  @dtopt Language\n     *  @name DataTable.defaults.language.infoFiltered\n     *\n     *  @example\n     *    $(document).ready( function() {\n     *      $('#example').dataTable( {\n     *        \"language\": {\n     *          \"infoFiltered\": \" - filtering from _MAX_ records\"\n     *        }\n     *      } );\n     *    } );\n     */\n    \"sInfoFiltered\": \"(filtered from _MAX_ total entries)\",\n    /**\n     * If can be useful to append extra information to the info string at times,\n     * and this variable does exactly that. This information will be appended to\n     * the `info` (`infoEmpty` and `infoFiltered` in whatever combination they are\n     * being used) at all times.\n     *  @type string\n     *  @default <i>Empty string</i>\n     *\n     *  @dtopt Language\n     *  @name DataTable.defaults.language.infoPostFix\n     *\n     *  @example\n     *    $(document).ready( function() {\n     *      $('#example').dataTable( {\n     *        \"language\": {\n     *          \"infoPostFix\": \"All records shown are derived from real information.\"\n     *        }\n     *      } );\n     *    } );\n     */\n    \"sInfoPostFix\": \"\",\n    /**\n     * This decimal place operator is a little different from the other\n     * language options since DataTables doesn't output floating point\n     * numbers, so it won't ever use this for display of a number. Rather,\n     * what this parameter does is modify the sort methods of the table so\n     * that numbers which are in a format which has a character other than\n     * a period (`.`) as a decimal place will be sorted numerically.\n     *\n     * Note that numbers with different decimal places cannot be shown in\n     * the same table and still be sortable, the table must be consistent.\n     * However, multiple different tables on the page can use different\n     * decimal place characters.\n     *  @type string\n     *  @default \n     *\n     *  @dtopt Language\n     *  @name DataTable.defaults.language.decimal\n     *\n     *  @example\n     *    $(document).ready( function() {\n     *      $('#example').dataTable( {\n     *        \"language\": {\n     *          \"decimal\": \",\"\n     *          \"thousands\": \".\"\n     *        }\n     *      } );\n     *    } );\n     */\n    \"sDecimal\": \"\",\n    /**\n     * DataTables has a build in number formatter (`formatNumber`) which is\n     * used to format large numbers that are used in the table information.\n     * By default a comma is used, but this can be trivially changed to any\n     * character you wish with this parameter.\n     *  @type string\n     *  @default ,\n     *\n     *  @dtopt Language\n     *  @name DataTable.defaults.language.thousands\n     *\n     *  @example\n     *    $(document).ready( function() {\n     *      $('#example').dataTable( {\n     *        \"language\": {\n     *          \"thousands\": \"'\"\n     *        }\n     *      } );\n     *    } );\n     */\n    \"sThousands\": \",\",\n    /**\n     * Detail the action that will be taken when the drop down menu for the\n     * pagination length option is changed. The '_MENU_' variable is replaced\n     * with a default select list of 10, 25, 50 and 100, and can be replaced\n     * with a custom select box if required.\n     *  @type string\n     *  @default Show _MENU_ entries\n     *\n     *  @dtopt Language\n     *  @name DataTable.defaults.language.lengthMenu\n     *\n     *  @example\n     *    // Language change only\n     *    $(document).ready( function() {\n     *      $('#example').dataTable( {\n     *        \"language\": {\n     *          \"lengthMenu\": \"Display _MENU_ records\"\n     *        }\n     *      } );\n     *    } );\n     *\n     *  @example\n     *    // Language and options change\n     *    $(document).ready( function() {\n     *      $('#example').dataTable( {\n     *        \"language\": {\n     *          \"lengthMenu\": 'Display <select>'+\n     *            '<option value=\"10\">10</option>'+\n     *            '<option value=\"20\">20</option>'+\n     *            '<option value=\"30\">30</option>'+\n     *            '<option value=\"40\">40</option>'+\n     *            '<option value=\"50\">50</option>'+\n     *            '<option value=\"-1\">All</option>'+\n     *            '</select> records'\n     *        }\n     *      } );\n     *    } );\n     */\n    \"sLengthMenu\": \"Show _MENU_ entries\",\n    /**\n     * When using Ajax sourced data and during the first draw when DataTables is\n     * gathering the data, this message is shown in an empty row in the table to\n     * indicate to the end user the the data is being loaded. Note that this\n     * parameter is not used when loading data by server-side processing, just\n     * Ajax sourced data with client-side processing.\n     *  @type string\n     *  @default Loading...\n     *\n     *  @dtopt Language\n     *  @name DataTable.defaults.language.loadingRecords\n     *\n     *  @example\n     *    $(document).ready( function() {\n     *      $('#example').dataTable( {\n     *        \"language\": {\n     *          \"loadingRecords\": \"Please wait - loading...\"\n     *        }\n     *      } );\n     *    } );\n     */\n    \"sLoadingRecords\": \"Loading...\",\n    /**\n     * Text which is displayed when the table is processing a user action\n     * (usually a sort command or similar).\n     *  @type string\n     *\n     *  @dtopt Language\n     *  @name DataTable.defaults.language.processing\n     *\n     *  @example\n     *    $(document).ready( function() {\n     *      $('#example').dataTable( {\n     *        \"language\": {\n     *          \"processing\": \"DataTables is currently busy\"\n     *        }\n     *      } );\n     *    } );\n     */\n    \"sProcessing\": \"\",\n    /**\n     * Details the actions that will be taken when the user types into the\n     * filtering input text box. The variable \"_INPUT_\", if used in the string,\n     * is replaced with the HTML text box for the filtering input allowing\n     * control over where it appears in the string. If \"_INPUT_\" is not given\n     * then the input box is appended to the string automatically.\n     *  @type string\n     *  @default Search:\n     *\n     *  @dtopt Language\n     *  @name DataTable.defaults.language.search\n     *\n     *  @example\n     *    // Input text box will be appended at the end automatically\n     *    $(document).ready( function() {\n     *      $('#example').dataTable( {\n     *        \"language\": {\n     *          \"search\": \"Filter records:\"\n     *        }\n     *      } );\n     *    } );\n     *\n     *  @example\n     *    // Specify where the filter should appear\n     *    $(document).ready( function() {\n     *      $('#example').dataTable( {\n     *        \"language\": {\n     *          \"search\": \"Apply filter _INPUT_ to table\"\n     *        }\n     *      } );\n     *    } );\n     */\n    \"sSearch\": \"Search:\",\n    /**\n     * Assign a `placeholder` attribute to the search `input` element\n     *  @type string\n     *  @default \n     *\n     *  @dtopt Language\n     *  @name DataTable.defaults.language.searchPlaceholder\n     */\n    \"sSearchPlaceholder\": \"\",\n    /**\n     * All of the language information can be stored in a file on the\n     * server-side, which DataTables will look up if this parameter is passed.\n     * It must store the URL of the language file, which is in a JSON format,\n     * and the object has the same properties as the oLanguage object in the\n     * initialiser object (i.e. the above parameters). Please refer to one of\n     * the example language files to see how this works in action.\n     *  @type string\n     *  @default <i>Empty string - i.e. disabled</i>\n     *\n     *  @dtopt Language\n     *  @name DataTable.defaults.language.url\n     *\n     *  @example\n     *    $(document).ready( function() {\n     *      $('#example').dataTable( {\n     *        \"language\": {\n     *          \"url\": \"https://www.sprymedia.co.uk/dataTables/lang.txt\"\n     *        }\n     *      } );\n     *    } );\n     */\n    \"sUrl\": \"\",\n    /**\n     * Text shown inside the table records when the is no information to be\n     * displayed after filtering. `emptyTable` is shown when there is simply no\n     * information in the table at all (regardless of filtering).\n     *  @type string\n     *  @default No matching records found\n     *\n     *  @dtopt Language\n     *  @name DataTable.defaults.language.zeroRecords\n     *\n     *  @example\n     *    $(document).ready( function() {\n     *      $('#example').dataTable( {\n     *        \"language\": {\n     *          \"zeroRecords\": \"No records to display\"\n     *        }\n     *      } );\n     *    } );\n     */\n    \"sZeroRecords\": \"No matching records found\"\n  },\n  /**\n   * This parameter allows you to have define the global filtering state at\n   * initialisation time. As an object the `search` parameter must be\n   * defined, but all other parameters are optional. When `regex` is true,\n   * the search string will be treated as a regular expression, when false\n   * (default) it will be treated as a straight string. When `smart`\n   * DataTables will use it's smart filtering methods (to word match at\n   * any point in the data), when false this will not be done.\n   *  @namespace\n   *  @extends DataTable.models.oSearch\n   *\n   *  @dtopt Options\n   *  @name DataTable.defaults.search\n   *\n   *  @example\n   *    $(document).ready( function() {\n   *      $('#example').dataTable( {\n   *        \"search\": {\"search\": \"Initial search\"}\n   *      } );\n   *    } )\n   */\n  \"oSearch\": $.extend({}, DataTable.models.oSearch),\n  /**\n   * __Deprecated__ The functionality provided by this parameter has now been\n   * superseded by that provided through `ajax`, which should be used instead.\n   *\n   * By default DataTables will look for the property `data` (or `aaData` for\n   * compatibility with DataTables 1.9-) when obtaining data from an Ajax\n   * source or for server-side processing - this parameter allows that\n   * property to be changed. You can use Javascript dotted object notation to\n   * get a data source for multiple levels of nesting.\n   *  @type string\n   *  @default data\n   *\n   *  @dtopt Options\n   *  @dtopt Server-side\n   *  @name DataTable.defaults.ajaxDataProp\n   *\n   *  @deprecated 1.10. Please use `ajax` for this functionality now.\n   */\n  \"sAjaxDataProp\": \"data\",\n  /**\n   * __Deprecated__ The functionality provided by this parameter has now been\n   * superseded by that provided through `ajax`, which should be used instead.\n   *\n   * You can instruct DataTables to load data from an external\n   * source using this parameter (use aData if you want to pass data in you\n   * already have). Simply provide a url a JSON object can be obtained from.\n   *  @type string\n   *  @default null\n   *\n   *  @dtopt Options\n   *  @dtopt Server-side\n   *  @name DataTable.defaults.ajaxSource\n   *\n   *  @deprecated 1.10. Please use `ajax` for this functionality now.\n   */\n  \"sAjaxSource\": null,\n  /**\n   * This initialisation variable allows you to specify exactly where in the\n   * DOM you want DataTables to inject the various controls it adds to the page\n   * (for example you might want the pagination controls at the top of the\n   * table). DIV elements (with or without a custom class) can also be added to\n   * aid styling. The follow syntax is used:\n   *   <ul>\n   *     <li>The following options are allowed:\n   *       <ul>\n   *         <li>'l' - Length changing</li>\n   *         <li>'f' - Filtering input</li>\n   *         <li>'t' - The table!</li>\n   *         <li>'i' - Information</li>\n   *         <li>'p' - Pagination</li>\n   *         <li>'r' - pRocessing</li>\n   *       </ul>\n   *     </li>\n   *     <li>The following constants are allowed:\n   *       <ul>\n   *         <li>'H' - jQueryUI theme \"header\" classes ('fg-toolbar ui-widget-header ui-corner-tl ui-corner-tr ui-helper-clearfix')</li>\n   *         <li>'F' - jQueryUI theme \"footer\" classes ('fg-toolbar ui-widget-header ui-corner-bl ui-corner-br ui-helper-clearfix')</li>\n   *       </ul>\n   *     </li>\n   *     <li>The following syntax is expected:\n   *       <ul>\n   *         <li>'&lt;' and '&gt;' - div elements</li>\n   *         <li>'&lt;\"class\" and '&gt;' - div with a class</li>\n   *         <li>'&lt;\"#id\" and '&gt;' - div with an ID</li>\n   *       </ul>\n   *     </li>\n   *     <li>Examples:\n   *       <ul>\n   *         <li>'&lt;\"wrapper\"flipt&gt;'</li>\n   *         <li>'&lt;lf&lt;t&gt;ip&gt;'</li>\n   *       </ul>\n   *     </li>\n   *   </ul>\n   *  @type string\n   *  @default lfrtip <i>(when `jQueryUI` is false)</i> <b>or</b>\n   *    <\"H\"lfr>t<\"F\"ip> <i>(when `jQueryUI` is true)</i>\n   *\n   *  @dtopt Options\n   *  @name DataTable.defaults.dom\n   *\n   *  @example\n   *    $(document).ready( function() {\n   *      $('#example').dataTable( {\n   *        \"dom\": '&lt;\"top\"i&gt;rt&lt;\"bottom\"flp&gt;&lt;\"clear\"&gt;'\n   *      } );\n   *    } );\n   */\n  \"sDom\": \"lfrtip\",\n  /**\n   * Search delay option. This will throttle full table searches that use the\n   * DataTables provided search input element (it does not effect calls to\n   * `dt-api search()`, providing a delay before the search is made.\n   *  @type integer\n   *  @default 0\n   *\n   *  @dtopt Options\n   *  @name DataTable.defaults.searchDelay\n   *\n   *  @example\n   *    $(document).ready( function() {\n   *      $('#example').dataTable( {\n   *        \"searchDelay\": 200\n   *      } );\n   *    } )\n   */\n  \"searchDelay\": null,\n  /**\n   * DataTables features six different built-in options for the buttons to\n   * display for pagination control:\n   *\n   * * `numbers` - Page number buttons only\n   * * `simple` - 'Previous' and 'Next' buttons only\n   * * 'simple_numbers` - 'Previous' and 'Next' buttons, plus page numbers\n   * * `full` - 'First', 'Previous', 'Next' and 'Last' buttons\n   * * `full_numbers` - 'First', 'Previous', 'Next' and 'Last' buttons, plus page numbers\n   * * `first_last_numbers` - 'First' and 'Last' buttons, plus page numbers\n   *  \n   * Further methods can be added using {@link DataTable.ext.oPagination}.\n   *  @type string\n   *  @default simple_numbers\n   *\n   *  @dtopt Options\n   *  @name DataTable.defaults.pagingType\n   *\n   *  @example\n   *    $(document).ready( function() {\n   *      $('#example').dataTable( {\n   *        \"pagingType\": \"full_numbers\"\n   *      } );\n   *    } )\n   */\n  \"sPaginationType\": \"simple_numbers\",\n  /**\n   * Enable horizontal scrolling. When a table is too wide to fit into a\n   * certain layout, or you have a large number of columns in the table, you\n   * can enable x-scrolling to show the table in a viewport, which can be\n   * scrolled. This property can be `true` which will allow the table to\n   * scroll horizontally when needed, or any CSS unit, or a number (in which\n   * case it will be treated as a pixel measurement). Setting as simply `true`\n   * is recommended.\n   *  @type boolean|string\n   *  @default <i>blank string - i.e. disabled</i>\n   *\n   *  @dtopt Features\n   *  @name DataTable.defaults.scrollX\n   *\n   *  @example\n   *    $(document).ready( function() {\n   *      $('#example').dataTable( {\n   *        \"scrollX\": true,\n   *        \"scrollCollapse\": true\n   *      } );\n   *    } );\n   */\n  \"sScrollX\": \"\",\n  /**\n   * This property can be used to force a DataTable to use more width than it\n   * might otherwise do when x-scrolling is enabled. For example if you have a\n   * table which requires to be well spaced, this parameter is useful for\n   * \"over-sizing\" the table, and thus forcing scrolling. This property can by\n   * any CSS unit, or a number (in which case it will be treated as a pixel\n   * measurement).\n   *  @type string\n   *  @default <i>blank string - i.e. disabled</i>\n   *\n   *  @dtopt Options\n   *  @name DataTable.defaults.scrollXInner\n   *\n   *  @example\n   *    $(document).ready( function() {\n   *      $('#example').dataTable( {\n   *        \"scrollX\": \"100%\",\n   *        \"scrollXInner\": \"110%\"\n   *      } );\n   *    } );\n   */\n  \"sScrollXInner\": \"\",\n  /**\n   * Enable vertical scrolling. Vertical scrolling will constrain the DataTable\n   * to the given height, and enable scrolling for any data which overflows the\n   * current viewport. This can be used as an alternative to paging to display\n   * a lot of data in a small area (although paging and scrolling can both be\n   * enabled at the same time). This property can be any CSS unit, or a number\n   * (in which case it will be treated as a pixel measurement).\n   *  @type string\n   *  @default <i>blank string - i.e. disabled</i>\n   *\n   *  @dtopt Features\n   *  @name DataTable.defaults.scrollY\n   *\n   *  @example\n   *    $(document).ready( function() {\n   *      $('#example').dataTable( {\n   *        \"scrollY\": \"200px\",\n   *        \"paginate\": false\n   *      } );\n   *    } );\n   */\n  \"sScrollY\": \"\",\n  /**\n   * __Deprecated__ The functionality provided by this parameter has now been\n   * superseded by that provided through `ajax`, which should be used instead.\n   *\n   * Set the HTTP method that is used to make the Ajax call for server-side\n   * processing or Ajax sourced data.\n   *  @type string\n   *  @default GET\n   *\n   *  @dtopt Options\n   *  @dtopt Server-side\n   *  @name DataTable.defaults.serverMethod\n   *\n   *  @deprecated 1.10. Please use `ajax` for this functionality now.\n   */\n  \"sServerMethod\": \"GET\",\n  /**\n   * DataTables makes use of renderers when displaying HTML elements for\n   * a table. These renderers can be added or modified by plug-ins to\n   * generate suitable mark-up for a site. For example the Bootstrap\n   * integration plug-in for DataTables uses a paging button renderer to\n   * display pagination buttons in the mark-up required by Bootstrap.\n   *\n   * For further information about the renderers available see\n   * DataTable.ext.renderer\n   *  @type string|object\n   *  @default null\n   *\n   *  @name DataTable.defaults.renderer\n   *\n   */\n  \"renderer\": null,\n  /**\n   * Set the data property name that DataTables should use to get a row's id\n   * to set as the `id` property in the node.\n   *  @type string\n   *  @default DT_RowId\n   *\n   *  @name DataTable.defaults.rowId\n   */\n  \"rowId\": \"DT_RowId\"\n};\n_fnHungarianMap(DataTable.defaults);\n\n/*\n * Developer note - See note in model.defaults.js about the use of Hungarian\n * notation and camel case.\n */\n\n/**\n * Column options that can be given to DataTables at initialisation time.\n *  @namespace\n */\nDataTable.defaults.column = {\n  /**\n   * Define which column(s) an order will occur on for this column. This\n   * allows a column's ordering to take multiple columns into account when\n   * doing a sort or use the data from a different column. For example first\n   * name / last name columns make sense to do a multi-column sort over the\n   * two columns.\n   *  @type array|int\n   *  @default null <i>Takes the value of the column index automatically</i>\n   *\n   *  @name DataTable.defaults.column.orderData\n   *  @dtopt Columns\n   *\n   *  @example\n   *    // Using `columnDefs`\n   *    $(document).ready( function() {\n   *      $('#example').dataTable( {\n   *        \"columnDefs\": [\n   *          { \"orderData\": [ 0, 1 ], \"targets\": [ 0 ] },\n   *          { \"orderData\": [ 1, 0 ], \"targets\": [ 1 ] },\n   *          { \"orderData\": 2, \"targets\": [ 2 ] }\n   *        ]\n   *      } );\n   *    } );\n   *\n   *  @example\n   *    // Using `columns`\n   *    $(document).ready( function() {\n   *      $('#example').dataTable( {\n   *        \"columns\": [\n   *          { \"orderData\": [ 0, 1 ] },\n   *          { \"orderData\": [ 1, 0 ] },\n   *          { \"orderData\": 2 },\n   *          null,\n   *          null\n   *        ]\n   *      } );\n   *    } );\n   */\n  \"aDataSort\": null,\n  \"iDataSort\": -1,\n  /**\n   * You can control the default ordering direction, and even alter the\n   * behaviour of the sort handler (i.e. only allow ascending ordering etc)\n   * using this parameter.\n   *  @type array\n   *  @default [ 'asc', 'desc' ]\n   *\n   *  @name DataTable.defaults.column.orderSequence\n   *  @dtopt Columns\n   *\n   *  @example\n   *    // Using `columnDefs`\n   *    $(document).ready( function() {\n   *      $('#example').dataTable( {\n   *        \"columnDefs\": [\n   *          { \"orderSequence\": [ \"asc\" ], \"targets\": [ 1 ] },\n   *          { \"orderSequence\": [ \"desc\", \"asc\", \"asc\" ], \"targets\": [ 2 ] },\n   *          { \"orderSequence\": [ \"desc\" ], \"targets\": [ 3 ] }\n   *        ]\n   *      } );\n   *    } );\n   *\n   *  @example\n   *    // Using `columns`\n   *    $(document).ready( function() {\n   *      $('#example').dataTable( {\n   *        \"columns\": [\n   *          null,\n   *          { \"orderSequence\": [ \"asc\" ] },\n   *          { \"orderSequence\": [ \"desc\", \"asc\", \"asc\" ] },\n   *          { \"orderSequence\": [ \"desc\" ] },\n   *          null\n   *        ]\n   *      } );\n   *    } );\n   */\n  \"asSorting\": ['asc', 'desc'],\n  /**\n   * Enable or disable filtering on the data in this column.\n   *  @type boolean\n   *  @default true\n   *\n   *  @name DataTable.defaults.column.searchable\n   *  @dtopt Columns\n   *\n   *  @example\n   *    // Using `columnDefs`\n   *    $(document).ready( function() {\n   *      $('#example').dataTable( {\n   *        \"columnDefs\": [\n   *          { \"searchable\": false, \"targets\": [ 0 ] }\n   *        ] } );\n   *    } );\n   *\n   *  @example\n   *    // Using `columns`\n   *    $(document).ready( function() {\n   *      $('#example').dataTable( {\n   *        \"columns\": [\n   *          { \"searchable\": false },\n   *          null,\n   *          null,\n   *          null,\n   *          null\n   *        ] } );\n   *    } );\n   */\n  \"bSearchable\": true,\n  /**\n   * Enable or disable ordering on this column.\n   *  @type boolean\n   *  @default true\n   *\n   *  @name DataTable.defaults.column.orderable\n   *  @dtopt Columns\n   *\n   *  @example\n   *    // Using `columnDefs`\n   *    $(document).ready( function() {\n   *      $('#example').dataTable( {\n   *        \"columnDefs\": [\n   *          { \"orderable\": false, \"targets\": [ 0 ] }\n   *        ] } );\n   *    } );\n   *\n   *  @example\n   *    // Using `columns`\n   *    $(document).ready( function() {\n   *      $('#example').dataTable( {\n   *        \"columns\": [\n   *          { \"orderable\": false },\n   *          null,\n   *          null,\n   *          null,\n   *          null\n   *        ] } );\n   *    } );\n   */\n  \"bSortable\": true,\n  /**\n   * Enable or disable the display of this column.\n   *  @type boolean\n   *  @default true\n   *\n   *  @name DataTable.defaults.column.visible\n   *  @dtopt Columns\n   *\n   *  @example\n   *    // Using `columnDefs`\n   *    $(document).ready( function() {\n   *      $('#example').dataTable( {\n   *        \"columnDefs\": [\n   *          { \"visible\": false, \"targets\": [ 0 ] }\n   *        ] } );\n   *    } );\n   *\n   *  @example\n   *    // Using `columns`\n   *    $(document).ready( function() {\n   *      $('#example').dataTable( {\n   *        \"columns\": [\n   *          { \"visible\": false },\n   *          null,\n   *          null,\n   *          null,\n   *          null\n   *        ] } );\n   *    } );\n   */\n  \"bVisible\": true,\n  /**\n   * Developer definable function that is called whenever a cell is created (Ajax source,\n   * etc) or processed for input (DOM source). This can be used as a compliment to mRender\n   * allowing you to modify the DOM element (add background colour for example) when the\n   * element is available.\n   *  @type function\n   *  @param {element} td The TD node that has been created\n   *  @param {*} cellData The Data for the cell\n   *  @param {array|object} rowData The data for the whole row\n   *  @param {int} row The row index for the aoData data store\n   *  @param {int} col The column index for aoColumns\n   *\n   *  @name DataTable.defaults.column.createdCell\n   *  @dtopt Columns\n   *\n   *  @example\n   *    $(document).ready( function() {\n   *      $('#example').dataTable( {\n   *        \"columnDefs\": [ {\n   *          \"targets\": [3],\n   *          \"createdCell\": function (td, cellData, rowData, row, col) {\n   *            if ( cellData == \"1.7\" ) {\n   *              $(td).css('color', 'blue')\n   *            }\n   *          }\n   *        } ]\n   *      });\n   *    } );\n   */\n  \"fnCreatedCell\": null,\n  /**\n   * This parameter has been replaced by `data` in DataTables to ensure naming\n   * consistency. `dataProp` can still be used, as there is backwards\n   * compatibility in DataTables for this option, but it is strongly\n   * recommended that you use `data` in preference to `dataProp`.\n   *  @name DataTable.defaults.column.dataProp\n   */\n\n  /**\n   * This property can be used to read data from any data source property,\n   * including deeply nested objects / properties. `data` can be given in a\n   * number of different ways which effect its behaviour:\n   *\n   * * `integer` - treated as an array index for the data source. This is the\n   *   default that DataTables uses (incrementally increased for each column).\n   * * `string` - read an object property from the data source. There are\n   *   three 'special' options that can be used in the string to alter how\n   *   DataTables reads the data from the source object:\n   *    * `.` - Dotted Javascript notation. Just as you use a `.` in\n   *      Javascript to read from nested objects, so to can the options\n   *      specified in `data`. For example: `browser.version` or\n   *      `browser.name`. If your object parameter name contains a period, use\n   *      `\\\\` to escape it - i.e. `first\\\\.name`.\n   *    * `[]` - Array notation. DataTables can automatically combine data\n   *      from and array source, joining the data with the characters provided\n   *      between the two brackets. For example: `name[, ]` would provide a\n   *      comma-space separated list from the source array. If no characters\n   *      are provided between the brackets, the original array source is\n   *      returned.\n   *    * `()` - Function notation. Adding `()` to the end of a parameter will\n   *      execute a function of the name given. For example: `browser()` for a\n   *      simple function on the data source, `browser.version()` for a\n   *      function in a nested property or even `browser().version` to get an\n   *      object property if the function called returns an object. Note that\n   *      function notation is recommended for use in `render` rather than\n   *      `data` as it is much simpler to use as a renderer.\n   * * `null` - use the original data source for the row rather than plucking\n   *   data directly from it. This action has effects on two other\n   *   initialisation options:\n   *    * `defaultContent` - When null is given as the `data` option and\n   *      `defaultContent` is specified for the column, the value defined by\n   *      `defaultContent` will be used for the cell.\n   *    * `render` - When null is used for the `data` option and the `render`\n   *      option is specified for the column, the whole data source for the\n   *      row is used for the renderer.\n   * * `function` - the function given will be executed whenever DataTables\n   *   needs to set or get the data for a cell in the column. The function\n   *   takes three parameters:\n   *    * Parameters:\n   *      * `{array|object}` The data source for the row\n   *      * `{string}` The type call data requested - this will be 'set' when\n   *        setting data or 'filter', 'display', 'type', 'sort' or undefined\n   *        when gathering data. Note that when `undefined` is given for the\n   *        type DataTables expects to get the raw data for the object back<\n   *      * `{*}` Data to set when the second parameter is 'set'.\n   *    * Return:\n   *      * The return value from the function is not required when 'set' is\n   *        the type of call, but otherwise the return is what will be used\n   *        for the data requested.\n   *\n   * Note that `data` is a getter and setter option. If you just require\n   * formatting of data for output, you will likely want to use `render` which\n   * is simply a getter and thus simpler to use.\n   *\n   * Note that prior to DataTables 1.9.2 `data` was called `mDataProp`. The\n   * name change reflects the flexibility of this property and is consistent\n   * with the naming of mRender. If 'mDataProp' is given, then it will still\n   * be used by DataTables, as it automatically maps the old name to the new\n   * if required.\n   *\n   *  @type string|int|function|null\n   *  @default null <i>Use automatically calculated column index</i>\n   *\n   *  @name DataTable.defaults.column.data\n   *  @dtopt Columns\n   *\n   *  @example\n   *    // Read table data from objects\n   *    // JSON structure for each row:\n   *    //   {\n   *    //      \"engine\": {value},\n   *    //      \"browser\": {value},\n   *    //      \"platform\": {value},\n   *    //      \"version\": {value},\n   *    //      \"grade\": {value}\n   *    //   }\n   *    $(document).ready( function() {\n   *      $('#example').dataTable( {\n   *        \"ajaxSource\": \"sources/objects.txt\",\n   *        \"columns\": [\n   *          { \"data\": \"engine\" },\n   *          { \"data\": \"browser\" },\n   *          { \"data\": \"platform\" },\n   *          { \"data\": \"version\" },\n   *          { \"data\": \"grade\" }\n   *        ]\n   *      } );\n   *    } );\n   *\n   *  @example\n   *    // Read information from deeply nested objects\n   *    // JSON structure for each row:\n   *    //   {\n   *    //      \"engine\": {value},\n   *    //      \"browser\": {value},\n   *    //      \"platform\": {\n   *    //         \"inner\": {value}\n   *    //      },\n   *    //      \"details\": [\n   *    //         {value}, {value}\n   *    //      ]\n   *    //   }\n   *    $(document).ready( function() {\n   *      $('#example').dataTable( {\n   *        \"ajaxSource\": \"sources/deep.txt\",\n   *        \"columns\": [\n   *          { \"data\": \"engine\" },\n   *          { \"data\": \"browser\" },\n   *          { \"data\": \"platform.inner\" },\n   *          { \"data\": \"details.0\" },\n   *          { \"data\": \"details.1\" }\n   *        ]\n   *      } );\n   *    } );\n   *\n   *  @example\n   *    // Using `data` as a function to provide different information for\n   *    // sorting, filtering and display. In this case, currency (price)\n   *    $(document).ready( function() {\n   *      $('#example').dataTable( {\n   *        \"columnDefs\": [ {\n   *          \"targets\": [ 0 ],\n   *          \"data\": function ( source, type, val ) {\n   *            if (type === 'set') {\n   *              source.price = val;\n   *              // Store the computed display and filter values for efficiency\n   *              source.price_display = val==\"\" ? \"\" : \"$\"+numberFormat(val);\n   *              source.price_filter  = val==\"\" ? \"\" : \"$\"+numberFormat(val)+\" \"+val;\n   *              return;\n   *            }\n   *            else if (type === 'display') {\n   *              return source.price_display;\n   *            }\n   *            else if (type === 'filter') {\n   *              return source.price_filter;\n   *            }\n   *            // 'sort', 'type' and undefined all just use the integer\n   *            return source.price;\n   *          }\n   *        } ]\n   *      } );\n   *    } );\n   *\n   *  @example\n   *    // Using default content\n   *    $(document).ready( function() {\n   *      $('#example').dataTable( {\n   *        \"columnDefs\": [ {\n   *          \"targets\": [ 0 ],\n   *          \"data\": null,\n   *          \"defaultContent\": \"Click to edit\"\n   *        } ]\n   *      } );\n   *    } );\n   *\n   *  @example\n   *    // Using array notation - outputting a list from an array\n   *    $(document).ready( function() {\n   *      $('#example').dataTable( {\n   *        \"columnDefs\": [ {\n   *          \"targets\": [ 0 ],\n   *          \"data\": \"name[, ]\"\n   *        } ]\n   *      } );\n   *    } );\n   *\n   */\n  \"mData\": null,\n  /**\n   * This property is the rendering partner to `data` and it is suggested that\n   * when you want to manipulate data for display (including filtering,\n   * sorting etc) without altering the underlying data for the table, use this\n   * property. `render` can be considered to be the the read only companion to\n   * `data` which is read / write (then as such more complex). Like `data`\n   * this option can be given in a number of different ways to effect its\n   * behaviour:\n   *\n   * * `integer` - treated as an array index for the data source. This is the\n   *   default that DataTables uses (incrementally increased for each column).\n   * * `string` - read an object property from the data source. There are\n   *   three 'special' options that can be used in the string to alter how\n   *   DataTables reads the data from the source object:\n   *    * `.` - Dotted Javascript notation. Just as you use a `.` in\n   *      Javascript to read from nested objects, so to can the options\n   *      specified in `data`. For example: `browser.version` or\n   *      `browser.name`. If your object parameter name contains a period, use\n   *      `\\\\` to escape it - i.e. `first\\\\.name`.\n   *    * `[]` - Array notation. DataTables can automatically combine data\n   *      from and array source, joining the data with the characters provided\n   *      between the two brackets. For example: `name[, ]` would provide a\n   *      comma-space separated list from the source array. If no characters\n   *      are provided between the brackets, the original array source is\n   *      returned.\n   *    * `()` - Function notation. Adding `()` to the end of a parameter will\n   *      execute a function of the name given. For example: `browser()` for a\n   *      simple function on the data source, `browser.version()` for a\n   *      function in a nested property or even `browser().version` to get an\n   *      object property if the function called returns an object.\n   * * `object` - use different data for the different data types requested by\n   *   DataTables ('filter', 'display', 'type' or 'sort'). The property names\n   *   of the object is the data type the property refers to and the value can\n   *   defined using an integer, string or function using the same rules as\n   *   `render` normally does. Note that an `_` option _must_ be specified.\n   *   This is the default value to use if you haven't specified a value for\n   *   the data type requested by DataTables.\n   * * `function` - the function given will be executed whenever DataTables\n   *   needs to set or get the data for a cell in the column. The function\n   *   takes three parameters:\n   *    * Parameters:\n   *      * {array|object} The data source for the row (based on `data`)\n   *      * {string} The type call data requested - this will be 'filter',\n   *        'display', 'type' or 'sort'.\n   *      * {array|object} The full data source for the row (not based on\n   *        `data`)\n   *    * Return:\n   *      * The return value from the function is what will be used for the\n   *        data requested.\n   *\n   *  @type string|int|function|object|null\n   *  @default null Use the data source value.\n   *\n   *  @name DataTable.defaults.column.render\n   *  @dtopt Columns\n   *\n   *  @example\n   *    // Create a comma separated list from an array of objects\n   *    $(document).ready( function() {\n   *      $('#example').dataTable( {\n   *        \"ajaxSource\": \"sources/deep.txt\",\n   *        \"columns\": [\n   *          { \"data\": \"engine\" },\n   *          { \"data\": \"browser\" },\n   *          {\n   *            \"data\": \"platform\",\n   *            \"render\": \"[, ].name\"\n   *          }\n   *        ]\n   *      } );\n   *    } );\n   *\n   *  @example\n   *    // Execute a function to obtain data\n   *    $(document).ready( function() {\n   *      $('#example').dataTable( {\n   *        \"columnDefs\": [ {\n   *          \"targets\": [ 0 ],\n   *          \"data\": null, // Use the full data source object for the renderer's source\n   *          \"render\": \"browserName()\"\n   *        } ]\n   *      } );\n   *    } );\n   *\n   *  @example\n   *    // As an object, extracting different data for the different types\n   *    // This would be used with a data source such as:\n   *    //   { \"phone\": 5552368, \"phone_filter\": \"5552368 555-2368\", \"phone_display\": \"555-2368\" }\n   *    // Here the `phone` integer is used for sorting and type detection, while `phone_filter`\n   *    // (which has both forms) is used for filtering for if a user inputs either format, while\n   *    // the formatted phone number is the one that is shown in the table.\n   *    $(document).ready( function() {\n   *      $('#example').dataTable( {\n   *        \"columnDefs\": [ {\n   *          \"targets\": [ 0 ],\n   *          \"data\": null, // Use the full data source object for the renderer's source\n   *          \"render\": {\n   *            \"_\": \"phone\",\n   *            \"filter\": \"phone_filter\",\n   *            \"display\": \"phone_display\"\n   *          }\n   *        } ]\n   *      } );\n   *    } );\n   *\n   *  @example\n   *    // Use as a function to create a link from the data source\n   *    $(document).ready( function() {\n   *      $('#example').dataTable( {\n   *        \"columnDefs\": [ {\n   *          \"targets\": [ 0 ],\n   *          \"data\": \"download_link\",\n   *          \"render\": function ( data, type, full ) {\n   *            return '<a href=\"'+data+'\">Download</a>';\n   *          }\n   *        } ]\n   *      } );\n   *    } );\n   */\n  \"mRender\": null,\n  /**\n   * Change the cell type created for the column - either TD cells or TH cells. This\n   * can be useful as TH cells have semantic meaning in the table body, allowing them\n   * to act as a header for a row (you may wish to add scope='row' to the TH elements).\n   *  @type string\n   *  @default td\n   *\n   *  @name DataTable.defaults.column.cellType\n   *  @dtopt Columns\n   *\n   *  @example\n   *    // Make the first column use TH cells\n   *    $(document).ready( function() {\n   *      $('#example').dataTable( {\n   *        \"columnDefs\": [ {\n   *          \"targets\": [ 0 ],\n   *          \"cellType\": \"th\"\n   *        } ]\n   *      } );\n   *    } );\n   */\n  \"sCellType\": \"td\",\n  /**\n   * Class to give to each cell in this column.\n   *  @type string\n   *  @default <i>Empty string</i>\n   *\n   *  @name DataTable.defaults.column.class\n   *  @dtopt Columns\n   *\n   *  @example\n   *    // Using `columnDefs`\n   *    $(document).ready( function() {\n   *      $('#example').dataTable( {\n   *        \"columnDefs\": [\n   *          { \"class\": \"my_class\", \"targets\": [ 0 ] }\n   *        ]\n   *      } );\n   *    } );\n   *\n   *  @example\n   *    // Using `columns`\n   *    $(document).ready( function() {\n   *      $('#example').dataTable( {\n   *        \"columns\": [\n   *          { \"class\": \"my_class\" },\n   *          null,\n   *          null,\n   *          null,\n   *          null\n   *        ]\n   *      } );\n   *    } );\n   */\n  \"sClass\": \"\",\n  /**\n   * When DataTables calculates the column widths to assign to each column,\n   * it finds the longest string in each column and then constructs a\n   * temporary table and reads the widths from that. The problem with this\n   * is that \"mmm\" is much wider then \"iiii\", but the latter is a longer\n   * string - thus the calculation can go wrong (doing it properly and putting\n   * it into an DOM object and measuring that is horribly(!) slow). Thus as\n   * a \"work around\" we provide this option. It will append its value to the\n   * text that is found to be the longest string for the column - i.e. padding.\n   * Generally you shouldn't need this!\n   *  @type string\n   *  @default <i>Empty string<i>\n   *\n   *  @name DataTable.defaults.column.contentPadding\n   *  @dtopt Columns\n   *\n   *  @example\n   *    // Using `columns`\n   *    $(document).ready( function() {\n   *      $('#example').dataTable( {\n   *        \"columns\": [\n   *          null,\n   *          null,\n   *          null,\n   *          {\n   *            \"contentPadding\": \"mmm\"\n   *          }\n   *        ]\n   *      } );\n   *    } );\n   */\n  \"sContentPadding\": \"\",\n  /**\n   * Allows a default value to be given for a column's data, and will be used\n   * whenever a null data source is encountered (this can be because `data`\n   * is set to null, or because the data source itself is null).\n   *  @type string\n   *  @default null\n   *\n   *  @name DataTable.defaults.column.defaultContent\n   *  @dtopt Columns\n   *\n   *  @example\n   *    // Using `columnDefs`\n   *    $(document).ready( function() {\n   *      $('#example').dataTable( {\n   *        \"columnDefs\": [\n   *          {\n   *            \"data\": null,\n   *            \"defaultContent\": \"Edit\",\n   *            \"targets\": [ -1 ]\n   *          }\n   *        ]\n   *      } );\n   *    } );\n   *\n   *  @example\n   *    // Using `columns`\n   *    $(document).ready( function() {\n   *      $('#example').dataTable( {\n   *        \"columns\": [\n   *          null,\n   *          null,\n   *          null,\n   *          {\n   *            \"data\": null,\n   *            \"defaultContent\": \"Edit\"\n   *          }\n   *        ]\n   *      } );\n   *    } );\n   */\n  \"sDefaultContent\": null,\n  /**\n   * This parameter is only used in DataTables' server-side processing. It can\n   * be exceptionally useful to know what columns are being displayed on the\n   * client side, and to map these to database fields. When defined, the names\n   * also allow DataTables to reorder information from the server if it comes\n   * back in an unexpected order (i.e. if you switch your columns around on the\n   * client-side, your server-side code does not also need updating).\n   *  @type string\n   *  @default <i>Empty string</i>\n   *\n   *  @name DataTable.defaults.column.name\n   *  @dtopt Columns\n   *\n   *  @example\n   *    // Using `columnDefs`\n   *    $(document).ready( function() {\n   *      $('#example').dataTable( {\n   *        \"columnDefs\": [\n   *          { \"name\": \"engine\", \"targets\": [ 0 ] },\n   *          { \"name\": \"browser\", \"targets\": [ 1 ] },\n   *          { \"name\": \"platform\", \"targets\": [ 2 ] },\n   *          { \"name\": \"version\", \"targets\": [ 3 ] },\n   *          { \"name\": \"grade\", \"targets\": [ 4 ] }\n   *        ]\n   *      } );\n   *    } );\n   *\n   *  @example\n   *    // Using `columns`\n   *    $(document).ready( function() {\n   *      $('#example').dataTable( {\n   *        \"columns\": [\n   *          { \"name\": \"engine\" },\n   *          { \"name\": \"browser\" },\n   *          { \"name\": \"platform\" },\n   *          { \"name\": \"version\" },\n   *          { \"name\": \"grade\" }\n   *        ]\n   *      } );\n   *    } );\n   */\n  \"sName\": \"\",\n  /**\n   * Defines a data source type for the ordering which can be used to read\n   * real-time information from the table (updating the internally cached\n   * version) prior to ordering. This allows ordering to occur on user\n   * editable elements such as form inputs.\n   *  @type string\n   *  @default std\n   *\n   *  @name DataTable.defaults.column.orderDataType\n   *  @dtopt Columns\n   *\n   *  @example\n   *    // Using `columnDefs`\n   *    $(document).ready( function() {\n   *      $('#example').dataTable( {\n   *        \"columnDefs\": [\n   *          { \"orderDataType\": \"dom-text\", \"targets\": [ 2, 3 ] },\n   *          { \"type\": \"numeric\", \"targets\": [ 3 ] },\n   *          { \"orderDataType\": \"dom-select\", \"targets\": [ 4 ] },\n   *          { \"orderDataType\": \"dom-checkbox\", \"targets\": [ 5 ] }\n   *        ]\n   *      } );\n   *    } );\n   *\n   *  @example\n   *    // Using `columns`\n   *    $(document).ready( function() {\n   *      $('#example').dataTable( {\n   *        \"columns\": [\n   *          null,\n   *          null,\n   *          { \"orderDataType\": \"dom-text\" },\n   *          { \"orderDataType\": \"dom-text\", \"type\": \"numeric\" },\n   *          { \"orderDataType\": \"dom-select\" },\n   *          { \"orderDataType\": \"dom-checkbox\" }\n   *        ]\n   *      } );\n   *    } );\n   */\n  \"sSortDataType\": \"std\",\n  /**\n   * The title of this column.\n   *  @type string\n   *  @default null <i>Derived from the 'TH' value for this column in the\n   *    original HTML table.</i>\n   *\n   *  @name DataTable.defaults.column.title\n   *  @dtopt Columns\n   *\n   *  @example\n   *    // Using `columnDefs`\n   *    $(document).ready( function() {\n   *      $('#example').dataTable( {\n   *        \"columnDefs\": [\n   *          { \"title\": \"My column title\", \"targets\": [ 0 ] }\n   *        ]\n   *      } );\n   *    } );\n   *\n   *  @example\n   *    // Using `columns`\n   *    $(document).ready( function() {\n   *      $('#example').dataTable( {\n   *        \"columns\": [\n   *          { \"title\": \"My column title\" },\n   *          null,\n   *          null,\n   *          null,\n   *          null\n   *        ]\n   *      } );\n   *    } );\n   */\n  \"sTitle\": null,\n  /**\n   * The type allows you to specify how the data for this column will be\n   * ordered. Four types (string, numeric, date and html (which will strip\n   * HTML tags before ordering)) are currently available. Note that only date\n   * formats understood by Javascript's Date() object will be accepted as type\n   * date. For example: \"Mar 26, 2008 5:03 PM\". May take the values: 'string',\n   * 'numeric', 'date' or 'html' (by default). Further types can be adding\n   * through plug-ins.\n   *  @type string\n   *  @default null <i>Auto-detected from raw data</i>\n   *\n   *  @name DataTable.defaults.column.type\n   *  @dtopt Columns\n   *\n   *  @example\n   *    // Using `columnDefs`\n   *    $(document).ready( function() {\n   *      $('#example').dataTable( {\n   *        \"columnDefs\": [\n   *          { \"type\": \"html\", \"targets\": [ 0 ] }\n   *        ]\n   *      } );\n   *    } );\n   *\n   *  @example\n   *    // Using `columns`\n   *    $(document).ready( function() {\n   *      $('#example').dataTable( {\n   *        \"columns\": [\n   *          { \"type\": \"html\" },\n   *          null,\n   *          null,\n   *          null,\n   *          null\n   *        ]\n   *      } );\n   *    } );\n   */\n  \"sType\": null,\n  /**\n   * Defining the width of the column, this parameter may take any CSS value\n   * (3em, 20px etc). DataTables applies 'smart' widths to columns which have not\n   * been given a specific width through this interface ensuring that the table\n   * remains readable.\n   *  @type string\n   *  @default null <i>Automatic</i>\n   *\n   *  @name DataTable.defaults.column.width\n   *  @dtopt Columns\n   *\n   *  @example\n   *    // Using `columnDefs`\n   *    $(document).ready( function() {\n   *      $('#example').dataTable( {\n   *        \"columnDefs\": [\n   *          { \"width\": \"20%\", \"targets\": [ 0 ] }\n   *        ]\n   *      } );\n   *    } );\n   *\n   *  @example\n   *    // Using `columns`\n   *    $(document).ready( function() {\n   *      $('#example').dataTable( {\n   *        \"columns\": [\n   *          { \"width\": \"20%\" },\n   *          null,\n   *          null,\n   *          null,\n   *          null\n   *        ]\n   *      } );\n   *    } );\n   */\n  \"sWidth\": null\n};\n_fnHungarianMap(DataTable.defaults.column);\n\n/**\n * DataTables settings object - this holds all the information needed for a\n * given table, including configuration, data and current application of the\n * table options. DataTables does not have a single instance for each DataTable\n * with the settings attached to that instance, but rather instances of the\n * DataTable \"class\" are created on-the-fly as needed (typically by a\n * $().dataTable() call) and the settings object is then applied to that\n * instance.\n *\n * Note that this object is related to {@link DataTable.defaults} but this\n * one is the internal data store for DataTables's cache of columns. It should\n * NOT be manipulated outside of DataTables. Any configuration should be done\n * through the initialisation options.\n *  @namespace\n *  @todo Really should attach the settings object to individual instances so we\n *    don't need to create new instances on each $().dataTable() call (if the\n *    table already exists). It would also save passing oSettings around and\n *    into every single function. However, this is a very significant\n *    architecture change for DataTables and will almost certainly break\n *    backwards compatibility with older installations. This is something that\n *    will be done in 2.0.\n */\nDataTable.models.oSettings = {\n  /**\n   * Primary features of DataTables and their enablement state.\n   *  @namespace\n   */\n  \"oFeatures\": {\n    /**\n     * Flag to say if DataTables should automatically try to calculate the\n     * optimum table and columns widths (true) or not (false).\n     * Note that this parameter will be set by the initialisation routine. To\n     * set a default use {@link DataTable.defaults}.\n     *  @type boolean\n     */\n    \"bAutoWidth\": null,\n    /**\n     * Delay the creation of TR and TD elements until they are actually\n     * needed by a driven page draw. This can give a significant speed\n     * increase for Ajax source and Javascript source data, but makes no\n     * difference at all for DOM and server-side processing tables.\n     * Note that this parameter will be set by the initialisation routine. To\n     * set a default use {@link DataTable.defaults}.\n     *  @type boolean\n     */\n    \"bDeferRender\": null,\n    /**\n     * Enable filtering on the table or not. Note that if this is disabled\n     * then there is no filtering at all on the table, including fnFilter.\n     * To just remove the filtering input use sDom and remove the 'f' option.\n     * Note that this parameter will be set by the initialisation routine. To\n     * set a default use {@link DataTable.defaults}.\n     *  @type boolean\n     */\n    \"bFilter\": null,\n    /**\n     * Table information element (the 'Showing x of y records' div) enable\n     * flag.\n     * Note that this parameter will be set by the initialisation routine. To\n     * set a default use {@link DataTable.defaults}.\n     *  @type boolean\n     */\n    \"bInfo\": null,\n    /**\n     * Present a user control allowing the end user to change the page size\n     * when pagination is enabled.\n     * Note that this parameter will be set by the initialisation routine. To\n     * set a default use {@link DataTable.defaults}.\n     *  @type boolean\n     */\n    \"bLengthChange\": null,\n    /**\n     * Pagination enabled or not. Note that if this is disabled then length\n     * changing must also be disabled.\n     * Note that this parameter will be set by the initialisation routine. To\n     * set a default use {@link DataTable.defaults}.\n     *  @type boolean\n     */\n    \"bPaginate\": null,\n    /**\n     * Processing indicator enable flag whenever DataTables is enacting a\n     * user request - typically an Ajax request for server-side processing.\n     * Note that this parameter will be set by the initialisation routine. To\n     * set a default use {@link DataTable.defaults}.\n     *  @type boolean\n     */\n    \"bProcessing\": null,\n    /**\n     * Server-side processing enabled flag - when enabled DataTables will\n     * get all data from the server for every draw - there is no filtering,\n     * sorting or paging done on the client-side.\n     * Note that this parameter will be set by the initialisation routine. To\n     * set a default use {@link DataTable.defaults}.\n     *  @type boolean\n     */\n    \"bServerSide\": null,\n    /**\n     * Sorting enablement flag.\n     * Note that this parameter will be set by the initialisation routine. To\n     * set a default use {@link DataTable.defaults}.\n     *  @type boolean\n     */\n    \"bSort\": null,\n    /**\n     * Multi-column sorting\n     * Note that this parameter will be set by the initialisation routine. To\n     * set a default use {@link DataTable.defaults}.\n     *  @type boolean\n     */\n    \"bSortMulti\": null,\n    /**\n     * Apply a class to the columns which are being sorted to provide a\n     * visual highlight or not. This can slow things down when enabled since\n     * there is a lot of DOM interaction.\n     * Note that this parameter will be set by the initialisation routine. To\n     * set a default use {@link DataTable.defaults}.\n     *  @type boolean\n     */\n    \"bSortClasses\": null,\n    /**\n     * State saving enablement flag.\n     * Note that this parameter will be set by the initialisation routine. To\n     * set a default use {@link DataTable.defaults}.\n     *  @type boolean\n     */\n    \"bStateSave\": null\n  },\n  /**\n   * Scrolling settings for a table.\n   *  @namespace\n   */\n  \"oScroll\": {\n    /**\n     * When the table is shorter in height than sScrollY, collapse the\n     * table container down to the height of the table (when true).\n     * Note that this parameter will be set by the initialisation routine. To\n     * set a default use {@link DataTable.defaults}.\n     *  @type boolean\n     */\n    \"bCollapse\": null,\n    /**\n     * Width of the scrollbar for the web-browser's platform. Calculated\n     * during table initialisation.\n     *  @type int\n     *  @default 0\n     */\n    \"iBarWidth\": 0,\n    /**\n     * Viewport width for horizontal scrolling. Horizontal scrolling is\n     * disabled if an empty string.\n     * Note that this parameter will be set by the initialisation routine. To\n     * set a default use {@link DataTable.defaults}.\n     *  @type string\n     */\n    \"sX\": null,\n    /**\n     * Width to expand the table to when using x-scrolling. Typically you\n     * should not need to use this.\n     * Note that this parameter will be set by the initialisation routine. To\n     * set a default use {@link DataTable.defaults}.\n     *  @type string\n     *  @deprecated\n     */\n    \"sXInner\": null,\n    /**\n     * Viewport height for vertical scrolling. Vertical scrolling is disabled\n     * if an empty string.\n     * Note that this parameter will be set by the initialisation routine. To\n     * set a default use {@link DataTable.defaults}.\n     *  @type string\n     */\n    \"sY\": null\n  },\n  /**\n   * Language information for the table.\n   *  @namespace\n   *  @extends DataTable.defaults.oLanguage\n   */\n  \"oLanguage\": {\n    /**\n     * Information callback function. See\n     * {@link DataTable.defaults.fnInfoCallback}\n     *  @type function\n     *  @default null\n     */\n    \"fnInfoCallback\": null\n  },\n  /**\n   * Browser support parameters\n   *  @namespace\n   */\n  \"oBrowser\": {\n    /**\n     * Indicate if the browser incorrectly calculates width:100% inside a\n     * scrolling element (IE6/7)\n     *  @type boolean\n     *  @default false\n     */\n    \"bScrollOversize\": false,\n    /**\n     * Determine if the vertical scrollbar is on the right or left of the\n     * scrolling container - needed for rtl language layout, although not\n     * all browsers move the scrollbar (Safari).\n     *  @type boolean\n     *  @default false\n     */\n    \"bScrollbarLeft\": false,\n    /**\n     * Flag for if `getBoundingClientRect` is fully supported or not\n     *  @type boolean\n     *  @default false\n     */\n    \"bBounding\": false,\n    /**\n     * Browser scrollbar width\n     *  @type integer\n     *  @default 0\n     */\n    \"barWidth\": 0\n  },\n  \"ajax\": null,\n  /**\n   * Array referencing the nodes which are used for the features. The\n   * parameters of this object match what is allowed by sDom - i.e.\n   *   <ul>\n   *     <li>'l' - Length changing</li>\n   *     <li>'f' - Filtering input</li>\n   *     <li>'t' - The table!</li>\n   *     <li>'i' - Information</li>\n   *     <li>'p' - Pagination</li>\n   *     <li>'r' - pRocessing</li>\n   *   </ul>\n   *  @type array\n   *  @default []\n   */\n  \"aanFeatures\": [],\n  /**\n   * Store data information - see {@link DataTable.models.oRow} for detailed\n   * information.\n   *  @type array\n   *  @default []\n   */\n  \"aoData\": [],\n  /**\n   * Array of indexes which are in the current display (after filtering etc)\n   *  @type array\n   *  @default []\n   */\n  \"aiDisplay\": [],\n  /**\n   * Array of indexes for display - no filtering\n   *  @type array\n   *  @default []\n   */\n  \"aiDisplayMaster\": [],\n  /**\n   * Map of row ids to data indexes\n   *  @type object\n   *  @default {}\n   */\n  \"aIds\": {},\n  /**\n   * Store information about each column that is in use\n   *  @type array\n   *  @default []\n   */\n  \"aoColumns\": [],\n  /**\n   * Store information about the table's header\n   *  @type array\n   *  @default []\n   */\n  \"aoHeader\": [],\n  /**\n   * Store information about the table's footer\n   *  @type array\n   *  @default []\n   */\n  \"aoFooter\": [],\n  /**\n   * Store the applied global search information in case we want to force a\n   * research or compare the old search to a new one.\n   * Note that this parameter will be set by the initialisation routine. To\n   * set a default use {@link DataTable.defaults}.\n   *  @namespace\n   *  @extends DataTable.models.oSearch\n   */\n  \"oPreviousSearch\": {},\n  /**\n   * Store the applied search for each column - see\n   * {@link DataTable.models.oSearch} for the format that is used for the\n   * filtering information for each column.\n   *  @type array\n   *  @default []\n   */\n  \"aoPreSearchCols\": [],\n  /**\n   * Sorting that is applied to the table. Note that the inner arrays are\n   * used in the following manner:\n   * <ul>\n   *   <li>Index 0 - column number</li>\n   *   <li>Index 1 - current sorting direction</li>\n   * </ul>\n   * Note that this parameter will be set by the initialisation routine. To\n   * set a default use {@link DataTable.defaults}.\n   *  @type array\n   *  @todo These inner arrays should really be objects\n   */\n  \"aaSorting\": null,\n  /**\n   * Sorting that is always applied to the table (i.e. prefixed in front of\n   * aaSorting).\n   * Note that this parameter will be set by the initialisation routine. To\n   * set a default use {@link DataTable.defaults}.\n   *  @type array\n   *  @default []\n   */\n  \"aaSortingFixed\": [],\n  /**\n   * Classes to use for the striping of a table.\n   * Note that this parameter will be set by the initialisation routine. To\n   * set a default use {@link DataTable.defaults}.\n   *  @type array\n   *  @default []\n   */\n  \"asStripeClasses\": null,\n  /**\n   * If restoring a table - we should restore its striping classes as well\n   *  @type array\n   *  @default []\n   */\n  \"asDestroyStripes\": [],\n  /**\n   * If restoring a table - we should restore its width\n   *  @type int\n   *  @default 0\n   */\n  \"sDestroyWidth\": 0,\n  /**\n   * Callback functions array for every time a row is inserted (i.e. on a draw).\n   *  @type array\n   *  @default []\n   */\n  \"aoRowCallback\": [],\n  /**\n   * Callback functions for the header on each draw.\n   *  @type array\n   *  @default []\n   */\n  \"aoHeaderCallback\": [],\n  /**\n   * Callback function for the footer on each draw.\n   *  @type array\n   *  @default []\n   */\n  \"aoFooterCallback\": [],\n  /**\n   * Array of callback functions for draw callback functions\n   *  @type array\n   *  @default []\n   */\n  \"aoDrawCallback\": [],\n  /**\n   * Array of callback functions for row created function\n   *  @type array\n   *  @default []\n   */\n  \"aoRowCreatedCallback\": [],\n  /**\n   * Callback functions for just before the table is redrawn. A return of\n   * false will be used to cancel the draw.\n   *  @type array\n   *  @default []\n   */\n  \"aoPreDrawCallback\": [],\n  /**\n   * Callback functions for when the table has been initialised.\n   *  @type array\n   *  @default []\n   */\n  \"aoInitComplete\": [],\n  /**\n   * Callbacks for modifying the settings to be stored for state saving, prior to\n   * saving state.\n   *  @type array\n   *  @default []\n   */\n  \"aoStateSaveParams\": [],\n  /**\n   * Callbacks for modifying the settings that have been stored for state saving\n   * prior to using the stored values to restore the state.\n   *  @type array\n   *  @default []\n   */\n  \"aoStateLoadParams\": [],\n  /**\n   * Callbacks for operating on the settings object once the saved state has been\n   * loaded\n   *  @type array\n   *  @default []\n   */\n  \"aoStateLoaded\": [],\n  /**\n   * Cache the table ID for quick access\n   *  @type string\n   *  @default <i>Empty string</i>\n   */\n  \"sTableId\": \"\",\n  /**\n   * The TABLE node for the main table\n   *  @type node\n   *  @default null\n   */\n  \"nTable\": null,\n  /**\n   * Permanent ref to the thead element\n   *  @type node\n   *  @default null\n   */\n  \"nTHead\": null,\n  /**\n   * Permanent ref to the tfoot element - if it exists\n   *  @type node\n   *  @default null\n   */\n  \"nTFoot\": null,\n  /**\n   * Permanent ref to the tbody element\n   *  @type node\n   *  @default null\n   */\n  \"nTBody\": null,\n  /**\n   * Cache the wrapper node (contains all DataTables controlled elements)\n   *  @type node\n   *  @default null\n   */\n  \"nTableWrapper\": null,\n  /**\n   * Indicate if when using server-side processing the loading of data\n   * should be deferred until the second draw.\n   * Note that this parameter will be set by the initialisation routine. To\n   * set a default use {@link DataTable.defaults}.\n   *  @type boolean\n   *  @default false\n   */\n  \"bDeferLoading\": false,\n  /**\n   * Indicate if all required information has been read in\n   *  @type boolean\n   *  @default false\n   */\n  \"bInitialised\": false,\n  /**\n   * Information about open rows. Each object in the array has the parameters\n   * 'nTr' and 'nParent'\n   *  @type array\n   *  @default []\n   */\n  \"aoOpenRows\": [],\n  /**\n   * Dictate the positioning of DataTables' control elements - see\n   * {@link DataTable.model.oInit.sDom}.\n   * Note that this parameter will be set by the initialisation routine. To\n   * set a default use {@link DataTable.defaults}.\n   *  @type string\n   *  @default null\n   */\n  \"sDom\": null,\n  /**\n   * Search delay (in mS)\n   *  @type integer\n   *  @default null\n   */\n  \"searchDelay\": null,\n  /**\n   * Which type of pagination should be used.\n   * Note that this parameter will be set by the initialisation routine. To\n   * set a default use {@link DataTable.defaults}.\n   *  @type string\n   *  @default two_button\n   */\n  \"sPaginationType\": \"two_button\",\n  /**\n   * The state duration (for `stateSave`) in seconds.\n   * Note that this parameter will be set by the initialisation routine. To\n   * set a default use {@link DataTable.defaults}.\n   *  @type int\n   *  @default 0\n   */\n  \"iStateDuration\": 0,\n  /**\n   * Array of callback functions for state saving. Each array element is an\n   * object with the following parameters:\n   *   <ul>\n   *     <li>function:fn - function to call. Takes two parameters, oSettings\n   *       and the JSON string to save that has been thus far created. Returns\n   *       a JSON string to be inserted into a json object\n   *       (i.e. '\"param\": [ 0, 1, 2]')</li>\n   *     <li>string:sName - name of callback</li>\n   *   </ul>\n   *  @type array\n   *  @default []\n   */\n  \"aoStateSave\": [],\n  /**\n   * Array of callback functions for state loading. Each array element is an\n   * object with the following parameters:\n   *   <ul>\n   *     <li>function:fn - function to call. Takes two parameters, oSettings\n   *       and the object stored. May return false to cancel state loading</li>\n   *     <li>string:sName - name of callback</li>\n   *   </ul>\n   *  @type array\n   *  @default []\n   */\n  \"aoStateLoad\": [],\n  /**\n   * State that was saved. Useful for back reference\n   *  @type object\n   *  @default null\n   */\n  \"oSavedState\": null,\n  /**\n   * State that was loaded. Useful for back reference\n   *  @type object\n   *  @default null\n   */\n  \"oLoadedState\": null,\n  /**\n   * Source url for AJAX data for the table.\n   * Note that this parameter will be set by the initialisation routine. To\n   * set a default use {@link DataTable.defaults}.\n   *  @type string\n   *  @default null\n   */\n  \"sAjaxSource\": null,\n  /**\n   * Property from a given object from which to read the table data from. This\n   * can be an empty string (when not server-side processing), in which case\n   * it is  assumed an an array is given directly.\n   * Note that this parameter will be set by the initialisation routine. To\n   * set a default use {@link DataTable.defaults}.\n   *  @type string\n   */\n  \"sAjaxDataProp\": null,\n  /**\n   * The last jQuery XHR object that was used for server-side data gathering.\n   * This can be used for working with the XHR information in one of the\n   * callbacks\n   *  @type object\n   *  @default null\n   */\n  \"jqXHR\": null,\n  /**\n   * JSON returned from the server in the last Ajax request\n   *  @type object\n   *  @default undefined\n   */\n  \"json\": undefined,\n  /**\n   * Data submitted as part of the last Ajax request\n   *  @type object\n   *  @default undefined\n   */\n  \"oAjaxData\": undefined,\n  /**\n   * Function to get the server-side data.\n   * Note that this parameter will be set by the initialisation routine. To\n   * set a default use {@link DataTable.defaults}.\n   *  @type function\n   */\n  \"fnServerData\": null,\n  /**\n   * Functions which are called prior to sending an Ajax request so extra\n   * parameters can easily be sent to the server\n   *  @type array\n   *  @default []\n   */\n  \"aoServerParams\": [],\n  /**\n   * Send the XHR HTTP method - GET or POST (could be PUT or DELETE if\n   * required).\n   * Note that this parameter will be set by the initialisation routine. To\n   * set a default use {@link DataTable.defaults}.\n   *  @type string\n   */\n  \"sServerMethod\": null,\n  /**\n   * Format numbers for display.\n   * Note that this parameter will be set by the initialisation routine. To\n   * set a default use {@link DataTable.defaults}.\n   *  @type function\n   */\n  \"fnFormatNumber\": null,\n  /**\n   * List of options that can be used for the user selectable length menu.\n   * Note that this parameter will be set by the initialisation routine. To\n   * set a default use {@link DataTable.defaults}.\n   *  @type array\n   *  @default []\n   */\n  \"aLengthMenu\": null,\n  /**\n   * Counter for the draws that the table does. Also used as a tracker for\n   * server-side processing\n   *  @type int\n   *  @default 0\n   */\n  \"iDraw\": 0,\n  /**\n   * Indicate if a redraw is being done - useful for Ajax\n   *  @type boolean\n   *  @default false\n   */\n  \"bDrawing\": false,\n  /**\n   * Draw index (iDraw) of the last error when parsing the returned data\n   *  @type int\n   *  @default -1\n   */\n  \"iDrawError\": -1,\n  /**\n   * Paging display length\n   *  @type int\n   *  @default 10\n   */\n  \"_iDisplayLength\": 10,\n  /**\n   * Paging start point - aiDisplay index\n   *  @type int\n   *  @default 0\n   */\n  \"_iDisplayStart\": 0,\n  /**\n   * Server-side processing - number of records in the result set\n   * (i.e. before filtering), Use fnRecordsTotal rather than\n   * this property to get the value of the number of records, regardless of\n   * the server-side processing setting.\n   *  @type int\n   *  @default 0\n   *  @private\n   */\n  \"_iRecordsTotal\": 0,\n  /**\n   * Server-side processing - number of records in the current display set\n   * (i.e. after filtering). Use fnRecordsDisplay rather than\n   * this property to get the value of the number of records, regardless of\n   * the server-side processing setting.\n   *  @type boolean\n   *  @default 0\n   *  @private\n   */\n  \"_iRecordsDisplay\": 0,\n  /**\n   * The classes to use for the table\n   *  @type object\n   *  @default {}\n   */\n  \"oClasses\": {},\n  /**\n   * Flag attached to the settings object so you can check in the draw\n   * callback if filtering has been done in the draw. Deprecated in favour of\n   * events.\n   *  @type boolean\n   *  @default false\n   *  @deprecated\n   */\n  \"bFiltered\": false,\n  /**\n   * Flag attached to the settings object so you can check in the draw\n   * callback if sorting has been done in the draw. Deprecated in favour of\n   * events.\n   *  @type boolean\n   *  @default false\n   *  @deprecated\n   */\n  \"bSorted\": false,\n  /**\n   * Indicate that if multiple rows are in the header and there is more than\n   * one unique cell per column, if the top one (true) or bottom one (false)\n   * should be used for sorting / title by DataTables.\n   * Note that this parameter will be set by the initialisation routine. To\n   * set a default use {@link DataTable.defaults}.\n   *  @type boolean\n   */\n  \"bSortCellsTop\": null,\n  /**\n   * Initialisation object that is used for the table\n   *  @type object\n   *  @default null\n   */\n  \"oInit\": null,\n  /**\n   * Destroy callback functions - for plug-ins to attach themselves to the\n   * destroy so they can clean up markup and events.\n   *  @type array\n   *  @default []\n   */\n  \"aoDestroyCallback\": [],\n  /**\n   * Get the number of records in the current record set, before filtering\n   *  @type function\n   */\n  \"fnRecordsTotal\": function () {\n    return _fnDataSource(this) == 'ssp' ? this._iRecordsTotal * 1 : this.aiDisplayMaster.length;\n  },\n  /**\n   * Get the number of records in the current record set, after filtering\n   *  @type function\n   */\n  \"fnRecordsDisplay\": function () {\n    return _fnDataSource(this) == 'ssp' ? this._iRecordsDisplay * 1 : this.aiDisplay.length;\n  },\n  /**\n   * Get the display end point - aiDisplay index\n   *  @type function\n   */\n  \"fnDisplayEnd\": function () {\n    var len = this._iDisplayLength,\n      start = this._iDisplayStart,\n      calc = start + len,\n      records = this.aiDisplay.length,\n      features = this.oFeatures,\n      paginate = features.bPaginate;\n    if (features.bServerSide) {\n      return paginate === false || len === -1 ? start + records : Math.min(start + len, this._iRecordsDisplay);\n    } else {\n      return !paginate || calc > records || len === -1 ? records : calc;\n    }\n  },\n  /**\n   * The DataTables object for this table\n   *  @type object\n   *  @default null\n   */\n  \"oInstance\": null,\n  /**\n   * Unique identifier for each instance of the DataTables object. If there\n   * is an ID on the table node, then it takes that value, otherwise an\n   * incrementing internal counter is used.\n   *  @type string\n   *  @default null\n   */\n  \"sInstance\": null,\n  /**\n   * tabindex attribute value that is added to DataTables control elements, allowing\n   * keyboard navigation of the table and its controls.\n   */\n  \"iTabIndex\": 0,\n  /**\n   * DIV container for the footer scrolling table if scrolling\n   */\n  \"nScrollHead\": null,\n  /**\n   * DIV container for the footer scrolling table if scrolling\n   */\n  \"nScrollFoot\": null,\n  /**\n   * Last applied sort\n   *  @type array\n   *  @default []\n   */\n  \"aLastSort\": [],\n  /**\n   * Stored plug-in instances\n   *  @type object\n   *  @default {}\n   */\n  \"oPlugins\": {},\n  /**\n   * Function used to get a row's id from the row's data\n   *  @type function\n   *  @default null\n   */\n  \"rowIdFn\": null,\n  /**\n   * Data location where to store a row's id\n   *  @type string\n   *  @default null\n   */\n  \"rowId\": null\n};\n\n/**\n * Extension object for DataTables that is used to provide all extension\n * options.\n *\n * Note that the `DataTable.ext` object is available through\n * `jQuery.fn.dataTable.ext` where it may be accessed and manipulated. It is\n * also aliased to `jQuery.fn.dataTableExt` for historic reasons.\n *  @namespace\n *  @extends DataTable.models.ext\n */\n\n/**\n * DataTables extensions\n * \n * This namespace acts as a collection area for plug-ins that can be used to\n * extend DataTables capabilities. Indeed many of the build in methods\n * use this method to provide their own capabilities (sorting methods for\n * example).\n *\n * Note that this namespace is aliased to `jQuery.fn.dataTableExt` for legacy\n * reasons\n *\n *  @namespace\n */\nDataTable.ext = _ext = {\n  /**\n   * Buttons. For use with the Buttons extension for DataTables. This is\n   * defined here so other extensions can define buttons regardless of load\n   * order. It is _not_ used by DataTables core.\n   *\n   *  @type object\n   *  @default {}\n   */\n  buttons: {},\n  /**\n   * Element class names\n   *\n   *  @type object\n   *  @default {}\n   */\n  classes: {},\n  /**\n   * DataTables build type (expanded by the download builder)\n   *\n   *  @type string\n   */\n  builder: \"-source-\",\n  /**\n   * Error reporting.\n   * \n   * How should DataTables report an error. Can take the value 'alert',\n   * 'throw', 'none' or a function.\n   *\n   *  @type string|function\n   *  @default alert\n   */\n  errMode: \"alert\",\n  /**\n   * Feature plug-ins.\n   * \n   * This is an array of objects which describe the feature plug-ins that are\n   * available to DataTables. These feature plug-ins are then available for\n   * use through the `dom` initialisation option.\n   * \n   * Each feature plug-in is described by an object which must have the\n   * following properties:\n   * \n   * * `fnInit` - function that is used to initialise the plug-in,\n   * * `cFeature` - a character so the feature can be enabled by the `dom`\n   *   instillation option. This is case sensitive.\n   *\n   * The `fnInit` function has the following input parameters:\n   *\n   * 1. `{object}` DataTables settings object: see\n   *    {@link DataTable.models.oSettings}\n   *\n   * And the following return is expected:\n   * \n   * * {node|null} The element which contains your feature. Note that the\n   *   return may also be void if your plug-in does not require to inject any\n   *   DOM elements into DataTables control (`dom`) - for example this might\n   *   be useful when developing a plug-in which allows table control via\n   *   keyboard entry\n   *\n   *  @type array\n   *\n   *  @example\n   *    $.fn.dataTable.ext.features.push( {\n   *      \"fnInit\": function( oSettings ) {\n   *        return new TableTools( { \"oDTSettings\": oSettings } );\n   *      },\n   *      \"cFeature\": \"T\"\n   *    } );\n   */\n  feature: [],\n  /**\n   * Row searching.\n   * \n   * This method of searching is complimentary to the default type based\n   * searching, and a lot more comprehensive as it allows you complete control\n   * over the searching logic. Each element in this array is a function\n   * (parameters described below) that is called for every row in the table,\n   * and your logic decides if it should be included in the searching data set\n   * or not.\n   *\n   * Searching functions have the following input parameters:\n   *\n   * 1. `{object}` DataTables settings object: see\n   *    {@link DataTable.models.oSettings}\n   * 2. `{array|object}` Data for the row to be processed (same as the\n   *    original format that was passed in as the data source, or an array\n   *    from a DOM data source\n   * 3. `{int}` Row index ({@link DataTable.models.oSettings.aoData}), which\n   *    can be useful to retrieve the `TR` element if you need DOM interaction.\n   *\n   * And the following return is expected:\n   *\n   * * {boolean} Include the row in the searched result set (true) or not\n   *   (false)\n   *\n   * Note that as with the main search ability in DataTables, technically this\n   * is \"filtering\", since it is subtractive. However, for consistency in\n   * naming we call it searching here.\n   *\n   *  @type array\n   *  @default []\n   *\n   *  @example\n   *    // The following example shows custom search being applied to the\n   *    // fourth column (i.e. the data[3] index) based on two input values\n   *    // from the end-user, matching the data in a certain range.\n   *    $.fn.dataTable.ext.search.push(\n   *      function( settings, data, dataIndex ) {\n   *        var min = document.getElementById('min').value * 1;\n   *        var max = document.getElementById('max').value * 1;\n   *        var version = data[3] == \"-\" ? 0 : data[3]*1;\n   *\n   *        if ( min == \"\" && max == \"\" ) {\n   *          return true;\n   *        }\n   *        else if ( min == \"\" && version < max ) {\n   *          return true;\n   *        }\n   *        else if ( min < version && \"\" == max ) {\n   *          return true;\n   *        }\n   *        else if ( min < version && version < max ) {\n   *          return true;\n   *        }\n   *        return false;\n   *      }\n   *    );\n   */\n  search: [],\n  /**\n   * Selector extensions\n   *\n   * The `selector` option can be used to extend the options available for the\n   * selector modifier options (`selector-modifier` object data type) that\n   * each of the three built in selector types offer (row, column and cell +\n   * their plural counterparts). For example the Select extension uses this\n   * mechanism to provide an option to select only rows, columns and cells\n   * that have been marked as selected by the end user (`{selected: true}`),\n   * which can be used in conjunction with the existing built in selector\n   * options.\n   *\n   * Each property is an array to which functions can be pushed. The functions\n   * take three attributes:\n   *\n   * * Settings object for the host table\n   * * Options object (`selector-modifier` object type)\n   * * Array of selected item indexes\n   *\n   * The return is an array of the resulting item indexes after the custom\n   * selector has been applied.\n   *\n   *  @type object\n   */\n  selector: {\n    cell: [],\n    column: [],\n    row: []\n  },\n  /**\n   * Internal functions, exposed for used in plug-ins.\n   * \n   * Please note that you should not need to use the internal methods for\n   * anything other than a plug-in (and even then, try to avoid if possible).\n   * The internal function may change between releases.\n   *\n   *  @type object\n   *  @default {}\n   */\n  internal: {},\n  /**\n   * Legacy configuration options. Enable and disable legacy options that\n   * are available in DataTables.\n   *\n   *  @type object\n   */\n  legacy: {\n    /**\n     * Enable / disable DataTables 1.9 compatible server-side processing\n     * requests\n     *\n     *  @type boolean\n     *  @default null\n     */\n    ajax: null\n  },\n  /**\n   * Pagination plug-in methods.\n   * \n   * Each entry in this object is a function and defines which buttons should\n   * be shown by the pagination rendering method that is used for the table:\n   * {@link DataTable.ext.renderer.pageButton}. The renderer addresses how the\n   * buttons are displayed in the document, while the functions here tell it\n   * what buttons to display. This is done by returning an array of button\n   * descriptions (what each button will do).\n   *\n   * Pagination types (the four built in options and any additional plug-in\n   * options defined here) can be used through the `paginationType`\n   * initialisation parameter.\n   *\n   * The functions defined take two parameters:\n   *\n   * 1. `{int} page` The current page index\n   * 2. `{int} pages` The number of pages in the table\n   *\n   * Each function is expected to return an array where each element of the\n   * array can be one of:\n   *\n   * * `first` - Jump to first page when activated\n   * * `last` - Jump to last page when activated\n   * * `previous` - Show previous page when activated\n   * * `next` - Show next page when activated\n   * * `{int}` - Show page of the index given\n   * * `{array}` - A nested array containing the above elements to add a\n   *   containing 'DIV' element (might be useful for styling).\n   *\n   * Note that DataTables v1.9- used this object slightly differently whereby\n   * an object with two functions would be defined for each plug-in. That\n   * ability is still supported by DataTables 1.10+ to provide backwards\n   * compatibility, but this option of use is now decremented and no longer\n   * documented in DataTables 1.10+.\n   *\n   *  @type object\n   *  @default {}\n   *\n   *  @example\n   *    // Show previous, next and current page buttons only\n   *    $.fn.dataTableExt.oPagination.current = function ( page, pages ) {\n   *      return [ 'previous', page, 'next' ];\n   *    };\n   */\n  pager: {},\n  renderer: {\n    pageButton: {},\n    header: {}\n  },\n  /**\n   * Ordering plug-ins - custom data source\n   * \n   * The extension options for ordering of data available here is complimentary\n   * to the default type based ordering that DataTables typically uses. It\n   * allows much greater control over the the data that is being used to\n   * order a column, but is necessarily therefore more complex.\n   * \n   * This type of ordering is useful if you want to do ordering based on data\n   * live from the DOM (for example the contents of an 'input' element) rather\n   * than just the static string that DataTables knows of.\n   * \n   * The way these plug-ins work is that you create an array of the values you\n   * wish to be ordering for the column in question and then return that\n   * array. The data in the array much be in the index order of the rows in\n   * the table (not the currently ordering order!). Which order data gathering\n   * function is run here depends on the `dt-init columns.orderDataType`\n   * parameter that is used for the column (if any).\n   *\n   * The functions defined take two parameters:\n   *\n   * 1. `{object}` DataTables settings object: see\n   *    {@link DataTable.models.oSettings}\n   * 2. `{int}` Target column index\n   *\n   * Each function is expected to return an array:\n   *\n   * * `{array}` Data for the column to be ordering upon\n   *\n   *  @type array\n   *\n   *  @example\n   *    // Ordering using `input` node values\n   *    $.fn.dataTable.ext.order['dom-text'] = function  ( settings, col )\n   *    {\n   *      return this.api().column( col, {order:'index'} ).nodes().map( function ( td, i ) {\n   *        return $('input', td).val();\n   *      } );\n   *    }\n   */\n  order: {},\n  /**\n   * Type based plug-ins.\n   *\n   * Each column in DataTables has a type assigned to it, either by automatic\n   * detection or by direct assignment using the `type` option for the column.\n   * The type of a column will effect how it is ordering and search (plug-ins\n   * can also make use of the column type if required).\n   *\n   * @namespace\n   */\n  type: {\n    /**\n     * Type detection functions.\n     *\n     * The functions defined in this object are used to automatically detect\n     * a column's type, making initialisation of DataTables super easy, even\n     * when complex data is in the table.\n     *\n     * The functions defined take two parameters:\n     *\n        *  1. `{*}` Data from the column cell to be analysed\n        *  2. `{settings}` DataTables settings object. This can be used to\n        *     perform context specific type detection - for example detection\n        *     based on language settings such as using a comma for a decimal\n        *     place. Generally speaking the options from the settings will not\n        *     be required\n     *\n     * Each function is expected to return:\n     *\n     * * `{string|null}` Data type detected, or null if unknown (and thus\n     *   pass it on to the other type detection functions.\n     *\n     *  @type array\n     *\n     *  @example\n     *    // Currency type detection plug-in:\n     *    $.fn.dataTable.ext.type.detect.push(\n     *      function ( data, settings ) {\n     *        // Check the numeric part\n     *        if ( ! data.substring(1).match(/[0-9]/) ) {\n     *          return null;\n     *        }\n     *\n     *        // Check prefixed by currency\n     *        if ( data.charAt(0) == '$' || data.charAt(0) == '&pound;' ) {\n     *          return 'currency';\n     *        }\n     *        return null;\n     *      }\n     *    );\n     */\n    detect: [],\n    /**\n     * Type based search formatting.\n     *\n     * The type based searching functions can be used to pre-format the\n     * data to be search on. For example, it can be used to strip HTML\n     * tags or to de-format telephone numbers for numeric only searching.\n     *\n     * Note that is a search is not defined for a column of a given type,\n     * no search formatting will be performed.\n     * \n     * Pre-processing of searching data plug-ins - When you assign the sType\n     * for a column (or have it automatically detected for you by DataTables\n     * or a type detection plug-in), you will typically be using this for\n     * custom sorting, but it can also be used to provide custom searching\n     * by allowing you to pre-processing the data and returning the data in\n     * the format that should be searched upon. This is done by adding\n     * functions this object with a parameter name which matches the sType\n     * for that target column. This is the corollary of <i>afnSortData</i>\n     * for searching data.\n     *\n     * The functions defined take a single parameter:\n     *\n        *  1. `{*}` Data from the column cell to be prepared for searching\n     *\n     * Each function is expected to return:\n     *\n     * * `{string|null}` Formatted string that will be used for the searching.\n     *\n     *  @type object\n     *  @default {}\n     *\n     *  @example\n     *    $.fn.dataTable.ext.type.search['title-numeric'] = function ( d ) {\n     *      return d.replace(/\\n/g,\" \").replace( /<.*?>/g, \"\" );\n     *    }\n     */\n    search: {},\n    /**\n     * Type based ordering.\n     *\n     * The column type tells DataTables what ordering to apply to the table\n     * when a column is sorted upon. The order for each type that is defined,\n     * is defined by the functions available in this object.\n     *\n     * Each ordering option can be described by three properties added to\n     * this object:\n     *\n     * * `{type}-pre` - Pre-formatting function\n     * * `{type}-asc` - Ascending order function\n     * * `{type}-desc` - Descending order function\n     *\n     * All three can be used together, only `{type}-pre` or only\n     * `{type}-asc` and `{type}-desc` together. It is generally recommended\n     * that only `{type}-pre` is used, as this provides the optimal\n     * implementation in terms of speed, although the others are provided\n     * for compatibility with existing Javascript sort functions.\n     *\n     * `{type}-pre`: Functions defined take a single parameter:\n     *\n        *  1. `{*}` Data from the column cell to be prepared for ordering\n     *\n     * And return:\n     *\n     * * `{*}` Data to be sorted upon\n     *\n     * `{type}-asc` and `{type}-desc`: Functions are typical Javascript sort\n     * functions, taking two parameters:\n     *\n        *  1. `{*}` Data to compare to the second parameter\n        *  2. `{*}` Data to compare to the first parameter\n     *\n     * And returning:\n     *\n     * * `{*}` Ordering match: <0 if first parameter should be sorted lower\n     *   than the second parameter, ===0 if the two parameters are equal and\n     *   >0 if the first parameter should be sorted height than the second\n     *   parameter.\n     * \n     *  @type object\n     *  @default {}\n     *\n     *  @example\n     *    // Numeric ordering of formatted numbers with a pre-formatter\n     *    $.extend( $.fn.dataTable.ext.type.order, {\n     *      \"string-pre\": function(x) {\n     *        a = (a === \"-\" || a === \"\") ? 0 : a.replace( /[^\\d\\-\\.]/g, \"\" );\n     *        return parseFloat( a );\n     *      }\n     *    } );\n     *\n     *  @example\n     *    // Case-sensitive string ordering, with no pre-formatting method\n     *    $.extend( $.fn.dataTable.ext.order, {\n     *      \"string-case-asc\": function(x,y) {\n     *        return ((x < y) ? -1 : ((x > y) ? 1 : 0));\n     *      },\n     *      \"string-case-desc\": function(x,y) {\n     *        return ((x < y) ? 1 : ((x > y) ? -1 : 0));\n     *      }\n     *    } );\n     */\n    order: {}\n  },\n  /**\n   * Unique DataTables instance counter\n   *\n   * @type int\n   * @private\n   */\n  _unique: 0,\n  //\n  // Depreciated\n  // The following properties are retained for backwards compatibility only.\n  // The should not be used in new projects and will be removed in a future\n  // version\n  //\n\n  /**\n   * Version check function.\n   *  @type function\n   *  @depreciated Since 1.10\n   */\n  fnVersionCheck: DataTable.fnVersionCheck,\n  /**\n   * Index for what 'this' index API functions should use\n   *  @type int\n   *  @deprecated Since v1.10\n   */\n  iApiIndex: 0,\n  /**\n   * jQuery UI class container\n   *  @type object\n   *  @deprecated Since v1.10\n   */\n  oJUIClasses: {},\n  /**\n   * Software version\n   *  @type string\n   *  @deprecated Since v1.10\n   */\n  sVersion: DataTable.version\n};\n\n//\n// Backwards compatibility. Alias to pre 1.10 Hungarian notation counter parts\n//\n$.extend(_ext, {\n  afnFiltering: _ext.search,\n  aTypes: _ext.type.detect,\n  ofnSearch: _ext.type.search,\n  oSort: _ext.type.order,\n  afnSortData: _ext.order,\n  aoFeatures: _ext.feature,\n  oApi: _ext.internal,\n  oStdClasses: _ext.classes,\n  oPagination: _ext.pager\n});\n$.extend(DataTable.ext.classes, {\n  \"sTable\": \"dataTable\",\n  \"sNoFooter\": \"no-footer\",\n  /* Paging buttons */\n  \"sPageButton\": \"paginate_button\",\n  \"sPageButtonActive\": \"current\",\n  \"sPageButtonDisabled\": \"disabled\",\n  /* Striping classes */\n  \"sStripeOdd\": \"odd\",\n  \"sStripeEven\": \"even\",\n  /* Empty row */\n  \"sRowEmpty\": \"dataTables_empty\",\n  /* Features */\n  \"sWrapper\": \"dataTables_wrapper\",\n  \"sFilter\": \"dataTables_filter\",\n  \"sInfo\": \"dataTables_info\",\n  \"sPaging\": \"dataTables_paginate paging_\",\n  /* Note that the type is postfixed */\n  \"sLength\": \"dataTables_length\",\n  \"sProcessing\": \"dataTables_processing\",\n  /* Sorting */\n  \"sSortAsc\": \"sorting_asc\",\n  \"sSortDesc\": \"sorting_desc\",\n  \"sSortable\": \"sorting\",\n  /* Sortable in both directions */\n  \"sSortableAsc\": \"sorting_desc_disabled\",\n  \"sSortableDesc\": \"sorting_asc_disabled\",\n  \"sSortableNone\": \"sorting_disabled\",\n  \"sSortColumn\": \"sorting_\",\n  /* Note that an int is postfixed for the sorting order */\n\n  /* Filtering */\n  \"sFilterInput\": \"\",\n  /* Page length */\n  \"sLengthSelect\": \"\",\n  /* Scrolling */\n  \"sScrollWrapper\": \"dataTables_scroll\",\n  \"sScrollHead\": \"dataTables_scrollHead\",\n  \"sScrollHeadInner\": \"dataTables_scrollHeadInner\",\n  \"sScrollBody\": \"dataTables_scrollBody\",\n  \"sScrollFoot\": \"dataTables_scrollFoot\",\n  \"sScrollFootInner\": \"dataTables_scrollFootInner\",\n  /* Misc */\n  \"sHeaderTH\": \"\",\n  \"sFooterTH\": \"\",\n  // Deprecated\n  \"sSortJUIAsc\": \"\",\n  \"sSortJUIDesc\": \"\",\n  \"sSortJUI\": \"\",\n  \"sSortJUIAscAllowed\": \"\",\n  \"sSortJUIDescAllowed\": \"\",\n  \"sSortJUIWrapper\": \"\",\n  \"sSortIcon\": \"\",\n  \"sJUIHeader\": \"\",\n  \"sJUIFooter\": \"\"\n});\nvar extPagination = DataTable.ext.pager;\nfunction _numbers(page, pages) {\n  var numbers = [],\n    buttons = extPagination.numbers_length,\n    half = Math.floor(buttons / 2),\n    i = 1;\n  if (pages <= buttons) {\n    numbers = _range(0, pages);\n  } else if (page <= half) {\n    numbers = _range(0, buttons - 2);\n    numbers.push('ellipsis');\n    numbers.push(pages - 1);\n  } else if (page >= pages - 1 - half) {\n    numbers = _range(pages - (buttons - 2), pages);\n    numbers.splice(0, 0, 'ellipsis'); // no unshift in ie6\n    numbers.splice(0, 0, 0);\n  } else {\n    numbers = _range(page - half + 2, page + half - 1);\n    numbers.push('ellipsis');\n    numbers.push(pages - 1);\n    numbers.splice(0, 0, 'ellipsis');\n    numbers.splice(0, 0, 0);\n  }\n  numbers.DT_el = 'span';\n  return numbers;\n}\n$.extend(extPagination, {\n  simple: function (page, pages) {\n    return ['previous', 'next'];\n  },\n  full: function (page, pages) {\n    return ['first', 'previous', 'next', 'last'];\n  },\n  numbers: function (page, pages) {\n    return [_numbers(page, pages)];\n  },\n  simple_numbers: function (page, pages) {\n    return ['previous', _numbers(page, pages), 'next'];\n  },\n  full_numbers: function (page, pages) {\n    return ['first', 'previous', _numbers(page, pages), 'next', 'last'];\n  },\n  first_last_numbers: function (page, pages) {\n    return ['first', _numbers(page, pages), 'last'];\n  },\n  // For testing and plug-ins to use\n  _numbers: _numbers,\n  // Number of number buttons (including ellipsis) to show. _Must be odd!_\n  numbers_length: 7\n});\n$.extend(true, DataTable.ext.renderer, {\n  pageButton: {\n    _: function (settings, host, idx, buttons, page, pages) {\n      var classes = settings.oClasses;\n      var lang = settings.oLanguage.oPaginate;\n      var aria = settings.oLanguage.oAria.paginate || {};\n      var btnDisplay, btnClass;\n      var attach = function (container, buttons) {\n        var i, ien, node, button;\n        var disabledClass = classes.sPageButtonDisabled;\n        var clickHandler = function (e) {\n          _fnPageChange(settings, e.data.action, true);\n        };\n        for (i = 0, ien = buttons.length; i < ien; i++) {\n          button = buttons[i];\n          if (Array.isArray(button)) {\n            var inner = $('<' + (button.DT_el || 'div') + '/>').appendTo(container);\n            attach(inner, button);\n          } else {\n            var disabled = false;\n            btnDisplay = null;\n            btnClass = button;\n            switch (button) {\n              case 'ellipsis':\n                container.append('<span class=\"ellipsis\">&#x2026;</span>');\n                break;\n              case 'first':\n                btnDisplay = lang.sFirst;\n                if (page === 0) {\n                  disabled = true;\n                }\n                break;\n              case 'previous':\n                btnDisplay = lang.sPrevious;\n                if (page === 0) {\n                  disabled = true;\n                }\n                break;\n              case 'next':\n                btnDisplay = lang.sNext;\n                if (pages === 0 || page === pages - 1) {\n                  disabled = true;\n                }\n                break;\n              case 'last':\n                btnDisplay = lang.sLast;\n                if (pages === 0 || page === pages - 1) {\n                  disabled = true;\n                }\n                break;\n              default:\n                btnDisplay = settings.fnFormatNumber(button + 1);\n                btnClass = page === button ? classes.sPageButtonActive : '';\n                break;\n            }\n            if (btnDisplay !== null) {\n              var tag = settings.oInit.pagingTag || 'a';\n              if (disabled) {\n                btnClass += ' ' + disabledClass;\n              }\n              node = $('<' + tag + '>', {\n                'class': classes.sPageButton + ' ' + btnClass,\n                'aria-controls': settings.sTableId,\n                'aria-disabled': disabled ? 'true' : null,\n                'aria-label': aria[button],\n                'role': 'link',\n                'aria-current': btnClass === classes.sPageButtonActive ? 'page' : null,\n                'data-dt-idx': button,\n                'tabindex': disabled ? -1 : settings.iTabIndex,\n                'id': idx === 0 && typeof button === 'string' ? settings.sTableId + '_' + button : null\n              }).html(btnDisplay).appendTo(container);\n              _fnBindAction(node, {\n                action: button\n              }, clickHandler);\n            }\n          }\n        }\n      };\n\n      // IE9 throws an 'unknown error' if document.activeElement is used\n      // inside an iframe or frame. Try / catch the error. Not good for\n      // accessibility, but neither are frames.\n      var activeEl;\n      try {\n        // Because this approach is destroying and recreating the paging\n        // elements, focus is lost on the select button which is bad for\n        // accessibility. So we want to restore focus once the draw has\n        // completed\n        activeEl = $(host).find(document.activeElement).data('dt-idx');\n      } catch (e) {}\n      attach($(host).empty(), buttons);\n      if (activeEl !== undefined) {\n        $(host).find('[data-dt-idx=' + activeEl + ']').trigger('focus');\n      }\n    }\n  }\n});\n\n// Built in type detection. See model.ext.aTypes for information about\n// what is required from this methods.\n$.extend(DataTable.ext.type.detect, [\n// Plain numbers - first since V8 detects some plain numbers as dates\n// e.g. Date.parse('55') (but not all, e.g. Date.parse('22')...).\nfunction (d, settings) {\n  var decimal = settings.oLanguage.sDecimal;\n  return _isNumber(d, decimal) ? 'num' + decimal : null;\n},\n// Dates (only those recognised by the browser's Date.parse)\nfunction (d, settings) {\n  // V8 tries _very_ hard to make a string passed into `Date.parse()`\n  // valid, so we need to use a regex to restrict date formats. Use a\n  // plug-in for anything other than ISO8601 style strings\n  if (d && !(d instanceof Date) && !_re_date.test(d)) {\n    return null;\n  }\n  var parsed = Date.parse(d);\n  return parsed !== null && !isNaN(parsed) || _empty(d) ? 'date' : null;\n},\n// Formatted numbers\nfunction (d, settings) {\n  var decimal = settings.oLanguage.sDecimal;\n  return _isNumber(d, decimal, true) ? 'num-fmt' + decimal : null;\n},\n// HTML numeric\nfunction (d, settings) {\n  var decimal = settings.oLanguage.sDecimal;\n  return _htmlNumeric(d, decimal) ? 'html-num' + decimal : null;\n},\n// HTML numeric, formatted\nfunction (d, settings) {\n  var decimal = settings.oLanguage.sDecimal;\n  return _htmlNumeric(d, decimal, true) ? 'html-num-fmt' + decimal : null;\n},\n// HTML (this is strict checking - there must be html)\nfunction (d, settings) {\n  return _empty(d) || typeof d === 'string' && d.indexOf('<') !== -1 ? 'html' : null;\n}]);\n\n// Filter formatting functions. See model.ext.ofnSearch for information about\n// what is required from these methods.\n// \n// Note that additional search methods are added for the html numbers and\n// html formatted numbers by `_addNumericSort()` when we know what the decimal\n// place is\n\n$.extend(DataTable.ext.type.search, {\n  html: function (data) {\n    return _empty(data) ? data : typeof data === 'string' ? data.replace(_re_new_lines, \" \").replace(_re_html, \"\") : '';\n  },\n  string: function (data) {\n    return _empty(data) ? data : typeof data === 'string' ? data.replace(_re_new_lines, \" \") : data;\n  }\n});\nvar __numericReplace = function (d, decimalPlace, re1, re2) {\n  if (d !== 0 && (!d || d === '-')) {\n    return -Infinity;\n  }\n  var type = typeof d;\n  if (type === 'number' || type === 'bigint') {\n    return d;\n  }\n\n  // If a decimal place other than `.` is used, it needs to be given to the\n  // function so we can detect it and replace with a `.` which is the only\n  // decimal place Javascript recognises - it is not locale aware.\n  if (decimalPlace) {\n    d = _numToDecimal(d, decimalPlace);\n  }\n  if (d.replace) {\n    if (re1) {\n      d = d.replace(re1, '');\n    }\n    if (re2) {\n      d = d.replace(re2, '');\n    }\n  }\n  return d * 1;\n};\n\n// Add the numeric 'deformatting' functions for sorting and search. This is done\n// in a function to provide an easy ability for the language options to add\n// additional methods if a non-period decimal place is used.\nfunction _addNumericSort(decimalPlace) {\n  $.each({\n    // Plain numbers\n    \"num\": function (d) {\n      return __numericReplace(d, decimalPlace);\n    },\n    // Formatted numbers\n    \"num-fmt\": function (d) {\n      return __numericReplace(d, decimalPlace, _re_formatted_numeric);\n    },\n    // HTML numeric\n    \"html-num\": function (d) {\n      return __numericReplace(d, decimalPlace, _re_html);\n    },\n    // HTML numeric, formatted\n    \"html-num-fmt\": function (d) {\n      return __numericReplace(d, decimalPlace, _re_html, _re_formatted_numeric);\n    }\n  }, function (key, fn) {\n    // Add the ordering method\n    _ext.type.order[key + decimalPlace + '-pre'] = fn;\n\n    // For HTML types add a search formatter that will strip the HTML\n    if (key.match(/^html\\-/)) {\n      _ext.type.search[key + decimalPlace] = _ext.type.search.html;\n    }\n  });\n}\n\n// Default sort methods\n$.extend(_ext.type.order, {\n  // Dates\n  \"date-pre\": function (d) {\n    var ts = Date.parse(d);\n    return isNaN(ts) ? -Infinity : ts;\n  },\n  // html\n  \"html-pre\": function (a) {\n    return _empty(a) ? '' : a.replace ? a.replace(/<.*?>/g, \"\").toLowerCase() : a + '';\n  },\n  // string\n  \"string-pre\": function (a) {\n    // This is a little complex, but faster than always calling toString,\n    // https://jsperf.com/tostring-v-check\n    return _empty(a) ? '' : typeof a === 'string' ? a.toLowerCase() : !a.toString ? '' : a.toString();\n  },\n  // string-asc and -desc are retained only for compatibility with the old\n  // sort methods\n  \"string-asc\": function (x, y) {\n    return x < y ? -1 : x > y ? 1 : 0;\n  },\n  \"string-desc\": function (x, y) {\n    return x < y ? 1 : x > y ? -1 : 0;\n  }\n});\n\n// Numeric sorting types - order doesn't matter here\n_addNumericSort('');\n$.extend(true, DataTable.ext.renderer, {\n  header: {\n    _: function (settings, cell, column, classes) {\n      // No additional mark-up required\n      // Attach a sort listener to update on sort - note that using the\n      // `DT` namespace will allow the event to be removed automatically\n      // on destroy, while the `dt` namespaced event is the one we are\n      // listening for\n      $(settings.nTable).on('order.dt.DT', function (e, ctx, sorting, columns) {\n        if (settings !== ctx) {\n          // need to check this this is the host\n          return; // table, not a nested one\n        }\n        var colIdx = column.idx;\n        cell.removeClass(classes.sSortAsc + ' ' + classes.sSortDesc).addClass(columns[colIdx] == 'asc' ? classes.sSortAsc : columns[colIdx] == 'desc' ? classes.sSortDesc : column.sSortingClass);\n      });\n    },\n    jqueryui: function (settings, cell, column, classes) {\n      $('<div/>').addClass(classes.sSortJUIWrapper).append(cell.contents()).append($('<span/>').addClass(classes.sSortIcon + ' ' + column.sSortingClassJUI)).appendTo(cell);\n\n      // Attach a sort listener to update on sort\n      $(settings.nTable).on('order.dt.DT', function (e, ctx, sorting, columns) {\n        if (settings !== ctx) {\n          return;\n        }\n        var colIdx = column.idx;\n        cell.removeClass(classes.sSortAsc + \" \" + classes.sSortDesc).addClass(columns[colIdx] == 'asc' ? classes.sSortAsc : columns[colIdx] == 'desc' ? classes.sSortDesc : column.sSortingClass);\n        cell.find('span.' + classes.sSortIcon).removeClass(classes.sSortJUIAsc + \" \" + classes.sSortJUIDesc + \" \" + classes.sSortJUI + \" \" + classes.sSortJUIAscAllowed + \" \" + classes.sSortJUIDescAllowed).addClass(columns[colIdx] == 'asc' ? classes.sSortJUIAsc : columns[colIdx] == 'desc' ? classes.sSortJUIDesc : column.sSortingClassJUI);\n      });\n    }\n  }\n});\n\n/*\n * Public helper functions. These aren't used internally by DataTables, or\n * called by any of the options passed into DataTables, but they can be used\n * externally by developers working with DataTables. They are helper functions\n * to make working with DataTables a little bit easier.\n */\n\nvar __htmlEscapeEntities = function (d) {\n  if (Array.isArray(d)) {\n    d = d.join(',');\n  }\n  return typeof d === 'string' ? d.replace(/&/g, '&amp;').replace(/</g, '&lt;').replace(/>/g, '&gt;').replace(/\"/g, '&quot;') : d;\n};\n\n// Common logic for moment, luxon or a date action\nfunction __mld(dt, momentFn, luxonFn, dateFn, arg1) {\n  if (window.moment) {\n    return dt[momentFn](arg1);\n  } else if (window.luxon) {\n    return dt[luxonFn](arg1);\n  }\n  return dateFn ? dt[dateFn](arg1) : dt;\n}\nvar __mlWarning = false;\nfunction __mldObj(d, format, locale) {\n  var dt;\n  if (window.moment) {\n    dt = window.moment.utc(d, format, locale, true);\n    if (!dt.isValid()) {\n      return null;\n    }\n  } else if (window.luxon) {\n    dt = format && typeof d === 'string' ? window.luxon.DateTime.fromFormat(d, format) : window.luxon.DateTime.fromISO(d);\n    if (!dt.isValid) {\n      return null;\n    }\n    dt.setLocale(locale);\n  } else if (!format) {\n    // No format given, must be ISO\n    dt = new Date(d);\n  } else {\n    if (!__mlWarning) {\n      alert('DataTables warning: Formatted date without Moment.js or Luxon - https://datatables.net/tn/17');\n    }\n    __mlWarning = true;\n  }\n  return dt;\n}\n\n// Wrapper for date, datetime and time which all operate the same way with the exception of\n// the output string for auto locale support\nfunction __mlHelper(localeString) {\n  return function (from, to, locale, def) {\n    // Luxon and Moment support\n    // Argument shifting\n    if (arguments.length === 0) {\n      locale = 'en';\n      to = null; // means toLocaleString\n      from = null; // means iso8601\n    } else if (arguments.length === 1) {\n      locale = 'en';\n      to = from;\n      from = null;\n    } else if (arguments.length === 2) {\n      locale = to;\n      to = from;\n      from = null;\n    }\n    var typeName = 'datetime-' + to;\n\n    // Add type detection and sorting specific to this date format - we need to be able to identify\n    // date type columns as such, rather than as numbers in extensions. Hence the need for this.\n    if (!DataTable.ext.type.order[typeName]) {\n      // The renderer will give the value to type detect as the type!\n      DataTable.ext.type.detect.unshift(function (d) {\n        return d === typeName ? typeName : false;\n      });\n\n      // The renderer gives us Moment, Luxon or Date obects for the sorting, all of which have a\n      // `valueOf` which gives milliseconds epoch\n      DataTable.ext.type.order[typeName + '-asc'] = function (a, b) {\n        var x = a.valueOf();\n        var y = b.valueOf();\n        return x === y ? 0 : x < y ? -1 : 1;\n      };\n      DataTable.ext.type.order[typeName + '-desc'] = function (a, b) {\n        var x = a.valueOf();\n        var y = b.valueOf();\n        return x === y ? 0 : x > y ? -1 : 1;\n      };\n    }\n    return function (d, type) {\n      // Allow for a default value\n      if (d === null || d === undefined) {\n        if (def === '--now') {\n          // We treat everything as UTC further down, so no changes are\n          // made, as such need to get the local date / time as if it were\n          // UTC\n          var local = new Date();\n          d = new Date(Date.UTC(local.getFullYear(), local.getMonth(), local.getDate(), local.getHours(), local.getMinutes(), local.getSeconds()));\n        } else {\n          d = '';\n        }\n      }\n      if (type === 'type') {\n        // Typing uses the type name for fast matching\n        return typeName;\n      }\n      if (d === '') {\n        return type !== 'sort' ? '' : __mldObj('0000-01-01 00:00:00', null, locale);\n      }\n\n      // Shortcut. If `from` and `to` are the same, we are using the renderer to\n      // format for ordering, not display - its already in the display format.\n      if (to !== null && from === to && type !== 'sort' && type !== 'type' && !(d instanceof Date)) {\n        return d;\n      }\n      var dt = __mldObj(d, from, locale);\n      if (dt === null) {\n        return d;\n      }\n      if (type === 'sort') {\n        return dt;\n      }\n      var formatted = to === null ? __mld(dt, 'toDate', 'toJSDate', '')[localeString]() : __mld(dt, 'format', 'toFormat', 'toISOString', to);\n\n      // XSS protection\n      return type === 'display' ? __htmlEscapeEntities(formatted) : formatted;\n    };\n  };\n}\n\n// Based on locale, determine standard number formatting\n// Fallback for legacy browsers is US English\nvar __thousands = ',';\nvar __decimal = '.';\nif (window.Intl !== undefined) {\n  try {\n    var num = new Intl.NumberFormat().formatToParts(100000.1);\n    for (var i = 0; i < num.length; i++) {\n      if (num[i].type === 'group') {\n        __thousands = num[i].value;\n      } else if (num[i].type === 'decimal') {\n        __decimal = num[i].value;\n      }\n    }\n  } catch (e) {\n    // noop\n  }\n}\n\n// Formatted date time detection - use by declaring the formats you are going to use\nDataTable.datetime = function (format, locale) {\n  var typeName = 'datetime-detect-' + format;\n  if (!locale) {\n    locale = 'en';\n  }\n  if (!DataTable.ext.type.order[typeName]) {\n    DataTable.ext.type.detect.unshift(function (d) {\n      var dt = __mldObj(d, format, locale);\n      return d === '' || dt ? typeName : false;\n    });\n    DataTable.ext.type.order[typeName + '-pre'] = function (d) {\n      return __mldObj(d, format, locale) || 0;\n    };\n  }\n};\n\n/**\n * Helpers for `columns.render`.\n *\n * The options defined here can be used with the `columns.render` initialisation\n * option to provide a display renderer. The following functions are defined:\n *\n * * `number` - Will format numeric data (defined by `columns.data`) for\n *   display, retaining the original unformatted data for sorting and filtering.\n *   It takes 5 parameters:\n *   * `string` - Thousands grouping separator\n *   * `string` - Decimal point indicator\n *   * `integer` - Number of decimal points to show\n *   * `string` (optional) - Prefix.\n *   * `string` (optional) - Postfix (/suffix).\n * * `text` - Escape HTML to help prevent XSS attacks. It has no optional\n *   parameters.\n *\n * @example\n *   // Column definition using the number renderer\n *   {\n *     data: \"salary\",\n *     render: $.fn.dataTable.render.number( '\\'', '.', 0, '$' )\n *   }\n *\n * @namespace\n */\nDataTable.render = {\n  date: __mlHelper('toLocaleDateString'),\n  datetime: __mlHelper('toLocaleString'),\n  time: __mlHelper('toLocaleTimeString'),\n  number: function (thousands, decimal, precision, prefix, postfix) {\n    // Auto locale detection\n    if (thousands === null || thousands === undefined) {\n      thousands = __thousands;\n    }\n    if (decimal === null || decimal === undefined) {\n      decimal = __decimal;\n    }\n    return {\n      display: function (d) {\n        if (typeof d !== 'number' && typeof d !== 'string') {\n          return d;\n        }\n        if (d === '' || d === null) {\n          return d;\n        }\n        var negative = d < 0 ? '-' : '';\n        var flo = parseFloat(d);\n\n        // If NaN then there isn't much formatting that we can do - just\n        // return immediately, escaping any HTML (this was supposed to\n        // be a number after all)\n        if (isNaN(flo)) {\n          return __htmlEscapeEntities(d);\n        }\n        flo = flo.toFixed(precision);\n        d = Math.abs(flo);\n        var intPart = parseInt(d, 10);\n        var floatPart = precision ? decimal + (d - intPart).toFixed(precision).substring(2) : '';\n\n        // If zero, then can't have a negative prefix\n        if (intPart === 0 && parseFloat(floatPart) === 0) {\n          negative = '';\n        }\n        return negative + (prefix || '') + intPart.toString().replace(/\\B(?=(\\d{3})+(?!\\d))/g, thousands) + floatPart + (postfix || '');\n      }\n    };\n  },\n  text: function () {\n    return {\n      display: __htmlEscapeEntities,\n      filter: __htmlEscapeEntities\n    };\n  }\n};\n\n/*\n * This is really a good bit rubbish this method of exposing the internal methods\n * publicly... - To be fixed in 2.0 using methods on the prototype\n */\n\n/**\n * Create a wrapper function for exporting an internal functions to an external API.\n *  @param {string} fn API function name\n *  @returns {function} wrapped function\n *  @memberof DataTable#internal\n */\nfunction _fnExternApiFunc(fn) {\n  return function () {\n    var args = [_fnSettingsFromNode(this[DataTable.ext.iApiIndex])].concat(Array.prototype.slice.call(arguments));\n    return DataTable.ext.internal[fn].apply(this, args);\n  };\n}\n\n/**\n * Reference to internal functions for use by plug-in developers. Note that\n * these methods are references to internal functions and are considered to be\n * private. If you use these methods, be aware that they are liable to change\n * between versions.\n *  @namespace\n */\n$.extend(DataTable.ext.internal, {\n  _fnExternApiFunc: _fnExternApiFunc,\n  _fnBuildAjax: _fnBuildAjax,\n  _fnAjaxUpdate: _fnAjaxUpdate,\n  _fnAjaxParameters: _fnAjaxParameters,\n  _fnAjaxUpdateDraw: _fnAjaxUpdateDraw,\n  _fnAjaxDataSrc: _fnAjaxDataSrc,\n  _fnAddColumn: _fnAddColumn,\n  _fnColumnOptions: _fnColumnOptions,\n  _fnAdjustColumnSizing: _fnAdjustColumnSizing,\n  _fnVisibleToColumnIndex: _fnVisibleToColumnIndex,\n  _fnColumnIndexToVisible: _fnColumnIndexToVisible,\n  _fnVisbleColumns: _fnVisbleColumns,\n  _fnGetColumns: _fnGetColumns,\n  _fnColumnTypes: _fnColumnTypes,\n  _fnApplyColumnDefs: _fnApplyColumnDefs,\n  _fnHungarianMap: _fnHungarianMap,\n  _fnCamelToHungarian: _fnCamelToHungarian,\n  _fnLanguageCompat: _fnLanguageCompat,\n  _fnBrowserDetect: _fnBrowserDetect,\n  _fnAddData: _fnAddData,\n  _fnAddTr: _fnAddTr,\n  _fnNodeToDataIndex: _fnNodeToDataIndex,\n  _fnNodeToColumnIndex: _fnNodeToColumnIndex,\n  _fnGetCellData: _fnGetCellData,\n  _fnSetCellData: _fnSetCellData,\n  _fnSplitObjNotation: _fnSplitObjNotation,\n  _fnGetObjectDataFn: _fnGetObjectDataFn,\n  _fnSetObjectDataFn: _fnSetObjectDataFn,\n  _fnGetDataMaster: _fnGetDataMaster,\n  _fnClearTable: _fnClearTable,\n  _fnDeleteIndex: _fnDeleteIndex,\n  _fnInvalidate: _fnInvalidate,\n  _fnGetRowElements: _fnGetRowElements,\n  _fnCreateTr: _fnCreateTr,\n  _fnBuildHead: _fnBuildHead,\n  _fnDrawHead: _fnDrawHead,\n  _fnDraw: _fnDraw,\n  _fnReDraw: _fnReDraw,\n  _fnAddOptionsHtml: _fnAddOptionsHtml,\n  _fnDetectHeader: _fnDetectHeader,\n  _fnGetUniqueThs: _fnGetUniqueThs,\n  _fnFeatureHtmlFilter: _fnFeatureHtmlFilter,\n  _fnFilterComplete: _fnFilterComplete,\n  _fnFilterCustom: _fnFilterCustom,\n  _fnFilterColumn: _fnFilterColumn,\n  _fnFilter: _fnFilter,\n  _fnFilterCreateSearch: _fnFilterCreateSearch,\n  _fnEscapeRegex: _fnEscapeRegex,\n  _fnFilterData: _fnFilterData,\n  _fnFeatureHtmlInfo: _fnFeatureHtmlInfo,\n  _fnUpdateInfo: _fnUpdateInfo,\n  _fnInfoMacros: _fnInfoMacros,\n  _fnInitialise: _fnInitialise,\n  _fnInitComplete: _fnInitComplete,\n  _fnLengthChange: _fnLengthChange,\n  _fnFeatureHtmlLength: _fnFeatureHtmlLength,\n  _fnFeatureHtmlPaginate: _fnFeatureHtmlPaginate,\n  _fnPageChange: _fnPageChange,\n  _fnFeatureHtmlProcessing: _fnFeatureHtmlProcessing,\n  _fnProcessingDisplay: _fnProcessingDisplay,\n  _fnFeatureHtmlTable: _fnFeatureHtmlTable,\n  _fnScrollDraw: _fnScrollDraw,\n  _fnApplyToChildren: _fnApplyToChildren,\n  _fnCalculateColumnWidths: _fnCalculateColumnWidths,\n  _fnThrottle: _fnThrottle,\n  _fnConvertToWidth: _fnConvertToWidth,\n  _fnGetWidestNode: _fnGetWidestNode,\n  _fnGetMaxLenString: _fnGetMaxLenString,\n  _fnStringToCss: _fnStringToCss,\n  _fnSortFlatten: _fnSortFlatten,\n  _fnSort: _fnSort,\n  _fnSortAria: _fnSortAria,\n  _fnSortListener: _fnSortListener,\n  _fnSortAttachListener: _fnSortAttachListener,\n  _fnSortingClasses: _fnSortingClasses,\n  _fnSortData: _fnSortData,\n  _fnSaveState: _fnSaveState,\n  _fnLoadState: _fnLoadState,\n  _fnImplementState: _fnImplementState,\n  _fnSettingsFromNode: _fnSettingsFromNode,\n  _fnLog: _fnLog,\n  _fnMap: _fnMap,\n  _fnBindAction: _fnBindAction,\n  _fnCallbackReg: _fnCallbackReg,\n  _fnCallbackFire: _fnCallbackFire,\n  _fnLengthOverflow: _fnLengthOverflow,\n  _fnRenderer: _fnRenderer,\n  _fnDataSource: _fnDataSource,\n  _fnRowAttributes: _fnRowAttributes,\n  _fnExtend: _fnExtend,\n  _fnCalculateEnd: function () {} // Used by a lot of plug-ins, but redundant\n  // in 1.10, so this dead-end function is\n  // added to prevent errors\n});\n\n// jQuery access\n$.fn.dataTable = DataTable;\n\n// Provide access to the host jQuery object (circular reference)\nDataTable.$ = $;\n\n// Legacy aliases\n$.fn.dataTableSettings = DataTable.settings;\n$.fn.dataTableExt = DataTable.ext;\n\n// With a capital `D` we return a DataTables API instance rather than a\n// jQuery object\n$.fn.DataTable = function (opts) {\n  return $(this).dataTable(opts).api();\n};\n\n// All properties that are available to $.fn.dataTable should also be\n// available on $.fn.DataTable\n$.each(DataTable, function (prop, val) {\n  $.fn.DataTable[prop] = val;\n});\nexport default DataTable;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}