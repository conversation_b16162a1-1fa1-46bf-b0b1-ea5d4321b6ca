{"ast": null, "code": "import { WebapiService } from './../webapi.service';\nimport { HttpClient } from '@angular/common/http';\nimport { NgbCalendar } from '@ng-bootstrap/ng-bootstrap';\nimport { Router } from '@angular/router';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"./../webapi.service\";\nimport * as i3 from \"@angular/common/http\";\nimport * as i4 from \"@ng-bootstrap/ng-bootstrap\";\nconst _c0 = () => ({\n  dateInputFormat: \"DD/MM/YYYY\"\n});\nconst _c1 = a0 => ({\n  \"display\": a0,\n  \"opacity\": 1\n});\nconst _c2 = a0 => ({\n  \"color\": a0\n});\nfunction CoverpageComponent_div_6_option_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 82);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r3 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", item_r3.groupid);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\" \", item_r3.groupid, \" (\", item_r3.name, \") \");\n  }\n}\nfunction CoverpageComponent_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 5)(1, \"select\", 80);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function CoverpageComponent_div_6_Template_select_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.saleid, $event) || (ctx_r1.saleid = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementStart(2, \"option\", 81);\n    i0.ɵɵtext(3, \"--\\u0E40\\u0E25\\u0E37\\u0E2D\\u0E01\\u0E23\\u0E2B\\u0E31\\u0E2A\\u0E1E\\u0E19\\u0E31\\u0E01\\u0E07\\u0E32\\u0E19\\u0E02\\u0E32\\u0E22--\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"option\", 8);\n    i0.ɵɵtext(5, \"\\u0E40\\u0E25\\u0E37\\u0E2D\\u0E01\\u0E23\\u0E32\\u0E22\\u0E01\\u0E32\\u0E23\\u0E17\\u0E31\\u0E49\\u0E07\\u0E2B\\u0E21\\u0E14\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, CoverpageComponent_div_6_option_6_Template, 2, 3, \"option\", 9);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.saleid);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.DateGroupsaleman);\n  }\n}\nfunction CoverpageComponent_option_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 82);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r4 = ctx.$implicit;\n    i0.ɵɵpropertyInterpolate(\"value\", item_r4.deliver);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r4.deliver);\n  }\n}\nfunction CoverpageComponent_tr_78_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\", 83)(1, \"td\", 84);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\", 84);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\", 84);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\", 84);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\", 85);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"td\", 85);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"td\", 85);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"td\", 84);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"td\", 84);\n    i0.ɵɵtext(18);\n    i0.ɵɵpipe(19, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"td\", 84);\n    i0.ɵɵtext(21);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"td\", 84);\n    i0.ɵɵtext(23);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"td\", 84);\n    i0.ɵɵtext(25);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"td\", 84);\n    i0.ɵɵtext(27);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"td\", 86)(29, \"input\", 87);\n    i0.ɵɵlistener(\"click\", function CoverpageComponent_tr_78_Template_input_click_29_listener($event) {\n      const ctx_r5 = i0.ɵɵrestoreView(_r5);\n      const item_r7 = ctx_r5.$implicit;\n      const i_r8 = ctx_r5.index;\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.clickchecksaleheaderlist(i_r8, $event.target.checked, item_r7.id, item_r7.cusname, item_r7.Desin, item_r7.Desdlv, item_r7.AXID));\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const item_r7 = ctx.$implicit;\n    const i_r8 = ctx.index;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(32, _c2, ctx_r1.getColorslineprint(item_r7.flag_print)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i_r8 + 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(34, _c2, ctx_r1.getColorslineprint(item_r7.flag_print)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r7.id);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(36, _c2, ctx_r1.getColorslineprint(item_r7.flag_print)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r7.dateid);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(38, _c2, ctx_r1.getColorslineprint(item_r7.flag_print)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r7.SalesId);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(40, _c2, ctx_r1.getColorslineprint(item_r7.flag_print)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r7.cusname);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(42, _c2, ctx_r1.getColorslineprint(item_r7.flag_print)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\"(\", item_r7.Desin, \") \", item_r7.invaddress, \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(44, _c2, ctx_r1.getColorslineprint(item_r7.flag_print)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\"(\", item_r7.Desdlv, \") \", item_r7.deliveryaddress, \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(46, _c2, ctx_r1.getColorslineprint(item_r7.flag_print)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r7.vattype);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(48, _c2, ctx_r1.getColorslineprint(item_r7.flag_print)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(19, 29, item_r7.totalweight, \"1.2-2\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(50, _c2, ctx_r1.getColorslineprint(item_r7.flag_print)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r7.DlvMode);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(52, _c2, ctx_r1.getColorslineprint(item_r7.flag_print)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r7.remark);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(54, _c2, ctx_r1.getColorslineprint(item_r7.flag_print)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r7.CustomerRef);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(56, _c2, ctx_r1.getColorslineprint(item_r7.flag_print)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r7.Payment);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"checked\", item_r7.check);\n  }\n}\nfunction CoverpageComponent_tr_106_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\", 83)(1, \"td\", 88);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\", 89);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\", 88);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\", 88);\n    i0.ɵɵtext(8);\n    i0.ɵɵpipe(9, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"td\", 88);\n    i0.ɵɵtext(11);\n    i0.ɵɵpipe(12, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(13, \"td\", 86);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r9 = ctx.$implicit;\n    const i_r10 = ctx.index;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i_r10 + 1);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r9.productcode);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r9.numpcs);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(9, 5, item_r9.numpacking, \"1.0-2\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(12, 8, item_r9.numpage, \"1.0-0\"));\n  }\n}\nexport let CoverpageComponent = /*#__PURE__*/(() => {\n  class CoverpageComponent {\n    printPX() {\n      this.pxHaerder = 70;\n    }\n    getColorslineprint(disst) {\n      if (disst == 1) {\n        return '#0AC103';\n      }\n    }\n    constructor(router, service, http, calendar) {\n      this.router = router;\n      this.service = service;\n      this.http = http;\n      this.calendar = calendar;\n      this.mdlSampleIsOpen = false;\n      this.checkreload = true;\n      this.deliveryselect = '0';\n      this.showgetrecipientname = false;\n      this.recipientname = '';\n      this.customername = 'ชื่อลูกค้า';\n      this.customeraddress = 'ที่อยู่ลูกค้า';\n      this.pageprintall = 0;\n      this.allid = [];\n      this.alt = '';\n      this.checkallheader = false;\n      this.checkallsaleline = false;\n      this.numgrouplist = [];\n      this.salelinelist = [];\n      this.headerlist = [];\n      this.seachheaderlist = [];\n      this.url = '';\n      this.saleid = '';\n      this.fromdate = '';\n      this.DateGroupsaleman = [];\n      this.todate = '';\n      this.delivery = '0';\n      this.deliverytype = [{\n        'deliver': 'รถบริษัท'\n      }, {\n        'deliver': 'รับเอง'\n      }, {\n        'deliver': 'ขนส่ง'\n      }];\n      this.addressdata = [];\n      this.saletype = '1';\n      this.printst = '3';\n      this.idsetidprint = '';\n      this.daliverrydata = [];\n      this.permisstiondata = [];\n      this.exportbtn = true;\n      this.searchbtn = true;\n      this.uncheck = false;\n      this.alerttext = '';\n      this.addressname = 'กรุณาเลือกที่อยู่';\n      this.selectname = '';\n      this.selectinv = '';\n      this.selectdlv = '';\n      this.pxHaerder = 70;\n      this.Axidshow = '';\n      localStorage.removeItem('DataSOderreview');\n      localStorage.removeItem('DataSOderlist');\n      this.url = service.geturlservice();\n      this.fromDate = calendar.getPrev(calendar.getToday(), 'd', 30);\n      this.toDate = calendar.getNext(calendar.getToday(), 'm', 3);\n      this.datalogin = JSON.parse(sessionStorage.getItem('login'));\n      this.Datatodate = new Date(this.toDate.year, this.toDate.month - 1, this.toDate.day);\n      this.Datafromdate = new Date(this.fromDate.year, this.fromDate.month - 1, this.fromDate.day);\n      this.getdate();\n      this.pxHaerder = 70;\n      this.permisstiondata = JSON.parse(sessionStorage.getItem('menu'));\n      if (this.datalogin == null) {\n        this.router.navigate(['login']);\n      } else {\n        this.getuser();\n        this.exportbtn = !this.permisstiondata[12].flag_print;\n        this.searchbtn = !this.permisstiondata[12].flag_action;\n      }\n    }\n    ngOnInit() {\n      this.getgroupsaleman();\n    }\n    getallselect() {}\n    getuser() {\n      if (this.datalogin[0].id_group_user == '153Admin' || this.datalogin[0].id_group_user == '1860Administrator') {\n        this.groupsale = '';\n        this.testclose = true;\n      } else {\n        this.testclose = false;\n        this.saleid = this.datalogin[0].salegroup;\n      }\n    }\n    getdate() {\n      this.fromdate = `${this.Datafromdate.getFullYear()}-${this.Datafromdate.getMonth() + 1}-${this.Datafromdate.getDate()}`;\n      this.todate = `${this.Datatodate.getFullYear()}-${this.Datatodate.getMonth() + 1}-${this.Datatodate.getDate()}`;\n    }\n    Searchsaleheader() {\n      this.pxHaerder = 60;\n      this.getdate();\n      this.headerlist = [];\n      var datasalegroup = '';\n      if (this.saleid == '') {\n        this.openModal(true, 'กรุณาป้อนรหัสพนักงานขาย หรือ วันที่ต้องการค้นหา', false);\n      } else {\n        if (this.saleid == '') {\n          this.saleid = '%20';\n        }\n        if (this.fromdate == '') {\n          this.fromdate = `${this.fromDate}`;\n        }\n        if (this.todate == '') {\n          this.todate = `${this.toDate}`;\n        }\n        if (this.saleid == '') {\n          if (this.datalogin[0].salegroup == 'admin') {\n            datasalegroup = `${this.saleid}`;\n          } else {\n            datasalegroup = `${this.datalogin[0].salegroup}`;\n          }\n        }\n        if (this.saleid !== '') {\n          if (this.datalogin[0].salegroup == 'admin') {\n            datasalegroup = `${this.saleid}`;\n          } else {\n            datasalegroup = `${this.datalogin[0].salegroup}`;\n          }\n        }\n        if (this.delivery == '0') {\n          alert('กรุณาเลือกประเภทการขนส่ง');\n          return;\n        }\n        this.http.get(this.url + 'get_sale_header/' + datasalegroup + '/' + this.fromdate + '/' + this.todate + '/' + this.delivery).subscribe(res => {\n          if (res.length > 0) {\n            for (var i = 0; i < res.length; i++) {\n              this.headerlist.push({\n                id: res[i].id,\n                AXID: res[i].AXID,\n                SalesId: res[i].SalesId,\n                CustAccount: res[i].CustAccount,\n                SalesName: res[i].description[0],\n                Desin: res[i].InvName,\n                Desdlv: res[i].DeliveryName,\n                DlvMode: res[i].DlvMode,\n                address: res[i].address,\n                check: false,\n                dateid: res[i].dateid,\n                vattype: res[i].vattype,\n                totalweight: res[i].totalweight,\n                Payment: res[i].Payment,\n                locationno: res[i].locationno,\n                deliveryaddress: res[i].deliveryaddress,\n                invaddress: res[i].invaddress,\n                CustomerRef: res[i].CustomerRef,\n                remark: res[i].remark,\n                InvAddress: res[i].InvAddress,\n                flag_print: res[i].flag_print,\n                gsale: res[i].gsale,\n                cusname: res[i].cusname\n              });\n            }\n            this.seachheaderlist = this.headerlist;\n            if (datasalegroup == '%20') {\n              this.saleid = '';\n            }\n          } else {\n            this.openModal(true, 'ไม่พบข้อมูลที่ค้นหา', false);\n            if (datasalegroup == '%20') {\n              this.saleid = '';\n            }\n          }\n        });\n      }\n    }\n    clickallchecksaleheaderlist(checked) {\n      for (var i = 0; i < this.headerlist.length; i++) {\n        this.headerlist[i].check = checked;\n        this.customername = this.headerlist[0].SalesName;\n      }\n    }\n    loadaddresscoverpage(value) {\n      this.daliverrydata = [];\n      this.http.get(this.url + 'get_addresscoverpage/' + value).subscribe(res => {\n        if (res.length > 0) {\n          this.addressname = res[0].invadd;\n        } else {\n          this.addressname = this.headerlist[0].deliveryaddress;\n        }\n      });\n    }\n    searchselectsale() {\n      if (this.saletype === '1') {\n        this.seachheaderlist = this.headerlist;\n      } else {\n        this.seachheaderlist = this.headerlist.filter(v => v.gsale.toLowerCase().indexOf(this.saletype.toLowerCase()) > -1).slice(0, 50);\n      }\n    }\n    searchselectprint() {\n      if (this.printst === '3') {\n        this.seachheaderlist = this.headerlist;\n      } else {\n        //alert(JSON.stringify(this.headerlist[0]));\n        this.seachheaderlist = this.headerlist.filter(v => v.flag_print.toLowerCase().indexOf(this.printst.toLowerCase()) > -1).slice(0, 50);\n      }\n    }\n    SelectName(name) {\n      if (name.target.value == 'N') {\n        this.recipientname = this.selectname;\n      } else if (name.target.value == 'I') {\n        this.recipientname = this.selectinv;\n      } else if (name.target.value == 'D') {\n        this.recipientname = this.selectdlv;\n      }\n    }\n    deliveryaddressselect(event) {\n      this.addressname = event.target.value;\n    }\n    clickchecksaleheaderlist(index, checked, idso, name, Inv, Dlv, axid) {\n      this.Axidshow = axid;\n      this.idsetidprint = idso;\n      if (checked == true) {\n        this.uncheck = false;\n        var lono = '';\n        if (this.headerlist[index].locationno == null && this.headerlist[index].locationno == undefined) {\n          lono = this.headerlist[index].InvAddress;\n        } else {\n          lono = this.headerlist[index].locationno[0];\n        }\n        this.addressname = this.headerlist[index].invaddress;\n        // alert(this.headerlist[index].invaddress);\n        // this.loadaddresscoverpage(lono);\n        this.recipientname = name;\n        this.selectname = name;\n        this.selectinv = Inv;\n        this.selectdlv = Dlv;\n        //alert(  this.selectname+'/'+this.selectinv+'/'+this.selectdlv);\n      }\n      var ch = 0;\n      if (checked == true) {\n        for (var i = 0; i < this.headerlist.length; i++) {\n          this.headerlist[i].check = false;\n        }\n      }\n      this.headerlist[index].check = checked;\n      if (this.headerlist[index].check == true) {\n        this.customeraddress = this.headerlist[index].address;\n        this.customername = this.headerlist[index].SalesName;\n      } else {\n        this.customername = 'ชื่อลูกค้า';\n        this.customeraddress = 'ที่อยู่ลูกค้า';\n      }\n      for (var i = 0; i < this.headerlist.length; i++) {\n        if (this.headerlist[i].check == true) {\n          ch++;\n        } else {\n          ch--;\n        }\n      }\n      if (ch == this.headerlist.length) {\n        this.checkallheader = true;\n      } else {\n        this.checkallheader = false;\n      }\n    }\n    clickallchecksalelist(checked) {\n      this.pxHaerder = 70;\n      for (var i = 0; i < this.salelinelist.length; i++) {\n        this.salelinelist[i].check = checked;\n      }\n      this.countpageallprint();\n    }\n    clickchecksalelinelist(index, checked) {\n      var ch = 0;\n      this.salelinelist[index].check = checked;\n      for (var i = 0; i < this.salelinelist.length; i++) {\n        if (this.salelinelist[i].check == true) {\n          ch++;\n        } else {\n          ch--;\n        }\n      }\n      if (ch == this.salelinelist.length) {\n        this.checkallsaleline = true;\n      } else {\n        this.checkallsaleline = false;\n      }\n      this.countpageallprint();\n    }\n    countpageallprint() {\n      this.pageprintall = 0;\n      var pageall = 0;\n      for (var i = 0; i < this.salelinelist.length; i++) {\n        pageall += this.salelinelist[i].numpage;\n      }\n      this.pageprintall = Math.ceil(pageall);\n    }\n    getdataderay() {\n      if (this.allid.length > 0) {\n        var persan = 0;\n        var nopersan = 0;\n        var numpagefi = 0;\n        var numpackfi = 0;\n        var packnum = 0;\n        var allpk = 0;\n        this.http.get(this.url + 'get_sale_linebyid/' + this.allid[0].id).subscribe(res => {\n          if (res.length > 0) {\n            for (var i = 0; i < res.length; i++) {\n              packnum = res[i].sumsaleqty / res[i].taxpackagingqty;\n              allpk = packnum / res[i].qty;\n              persan = packnum % res[i].qty;\n              //persan=res[i].sumsaleqty%res[i].qty;\n              //nopersan=res[i].sumsaleqty/res[i].qty;\n              if (persan == 0 || persan < 1.0) {\n                numpagefi = allpk * res[i].pack;\n                if (numpagefi < 1.0) {\n                  numpagefi = 1;\n                }\n                //numpagefi=nopersan*res[i].pack\n              } else {\n                //numpackfi=  nopersan;\n                numpagefi = allpk * res[i].pack;\n              }\n              // alert(numpackfi)\n              this.salelinelist.push({\n                productcode: res[i].grp,\n                numgroup: res[i].numgroup,\n                numpcs: res[i].salesqty,\n                numpacking: packnum,\n                numpage: numpagefi,\n                check: false\n              });\n            }\n            this.countpageallprint();\n          } else {\n            this.openModal(true, 'ไม่พบข้อมูลที่ค้นหา', false);\n          }\n        });\n        this.allid.splice(0, 1);\n        if (this.allid.length < 1) {\n          clearInterval(this.timerInterval);\n        }\n      }\n    }\n    clickloadcoverinfo() {\n      this.salelinelist = [];\n      for (var i = 0; i < this.headerlist.length; i++) {\n        if (this.headerlist[i].check == false) {} else {\n          this.allid.push({\n            id: this.headerlist[i].id\n          });\n        }\n      }\n      this.timerInterval = setInterval(() => this.getdataderay(), 100);\n    }\n    getgroupsaleman() {\n      this.DateGroupsaleman = [];\n      this.http.get(this.url + 'salesman').subscribe(res => {\n        if (res.length > 0) {\n          this.DateGroupsaleman = res;\n        } else {\n          alert('ไม่สามารถติดต่อข้อมูลรหัส saleman ได้');\n        }\n      });\n    }\n    printpage() {\n      window.print();\n    }\n    openModal(open, text, load) {\n      this.mdlSampleIsOpen = open;\n      this.alt = text;\n      this.checkreload = load;\n    }\n    closemodel(cl) {\n      this.mdlSampleIsOpen = cl;\n      if (this.checkreload == true) {\n        location.reload();\n      }\n    }\n    setflagprint() {\n      var body = {\n        id: this.idsetidprint\n      };\n      this.http.post(this.url + 'set_printsaleheader', body).subscribe(res => {\n        if (res == true) {\n          this.print();\n        }\n      });\n    }\n    clrscrdataprint() {\n      this.daliverrydata = [];\n      this.addressname == 'กรุณาเลือกที่อยู่';\n      this.recipientname = '';\n    }\n    print() {\n      let popupWin;\n      if (this.recipientname != '' && this.addressname != 'กรุณาเลือกที่อยู่') {\n        popupWin = window.open('', '_blank');\n        popupWin.document.open();\n        popupWin.document.write(`\n    <html>\n    <head>\n      <title>ใบแปะหน้ากล่อง</title>\n      <meta http-equiv=\"Content-Type\" content=\"text/html; charset=UTF-8\" />\n      <meta http-equiv=\"Content-Type\" content=\"text/html; charset=iso-8859-1\">\n<meta http-equiv=\"Content-Language\" content=\"en-us\">\n<meta http-equiv=\"Content-Script-Type\" content=\"text/javascript\">\n<meta name=\"GENERATOR\" content=\"TrackInternet._Default Class\">\n      \n      <script src=\"https://code.jquery.com/jquery-3.3.1.js\" ></script>\n<script src=\"https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.14.3/umd/popper.min.js\" ></script>\n<script src=\"https://stackpath.bootstrapcdn.com/bootstrap/4.1.3/js/bootstrap.min.js\"></script>\n      <style>\n.textcen{\ntext-align: center;\n}\n.he{\n  border-width:5px;  \nborder-style:solid;\nborder-color: black;\npadding-bottom:0px;\n}\n.footertable\n{\n  text-align: center;\n  position : absolute;\n  bottom : 0;\n  width:98%;\n  height : 150px;\n  margin-top : 0px;\n    \n    \n}\n\n\n      </style>\n    </head>\n<body onafterprint=\"saveprintsucc()\" onload=\"window.print();window.close()\">\n<div class=\"container-fluid \">\n        \n            <div class=\"col-md-12\">\n         \n            <div class=\" he \">\n            <lable ><p style=\" font-size: 25px;text-align: right;line-height: 2px;margin-right: 13%; \"><b>${this.Axidshow}</b></p> </lable>\n            <h1 class=\"textcen\" style=\" font-size: 50px;\">*** กรุณาอย่าวางของหนักทับ *** </p></h1>\n               </div>\n               <div class=\"textcen p-1\">\n                   <span class=\"textcen\" ><h2 style=\" font-size: ${this.pxHaerder}px\">${this.recipientname}</h2></span>\n               </div>\n               <div class=\" textcen p-1\">\n                   <div class=\"textcen\" ><h2 style=\" font-size: 50px\"></h2></div>\n               </div>\n               <div class=\"textcen p-1\">\n                   <div class=\"textcen\" ><h2 style=\" font-size: 40px\"> ${this.addressname}\n                    </h2></div>\n               </div>\n               <div class=\"he footertable\">\n              \n                   <h1  class=\"textcen\" style=\"font-size: 50px\">*** กรุณาอย่าวางของหนักทับ ***</h1>\n               </div>\n\n            </div>\n        \n            \n    </div>\n</body>\n  </html>`);\n      } else if (this.addressname == 'กรุณาเลือกที่อยู่') {\n        this.alerttext = 'กรุณาเลือก ที่อยู่!!!!!';\n      } else {\n        this.alerttext = 'กรุณาป้อน ชื่อผู้รับ!!!!';\n      }\n      popupWin.document.close();\n      //this.daliverrydata=[];\n    }\n    static {\n      this.ɵfac = function CoverpageComponent_Factory(t) {\n        return new (t || CoverpageComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.WebapiService), i0.ɵɵdirectiveInject(i3.HttpClient), i0.ɵɵdirectiveInject(i4.NgbCalendar));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: CoverpageComponent,\n        selectors: [[\"app-coverpage\"]],\n        decls: 192,\n        vars: 35,\n        consts: [[1, \"container-fluid\"], [2, \"padding-top\", \"60px\"], [1, \"p-sm-1\", \"bg-secondary\", \"text-white\", \"text-center\"], [1, \"form-row\"], [\"class\", \"col-md-2 mb-2\", 4, \"ngIf\"], [1, \"col-md-2\", \"mb-2\"], [\"name\", \"\", \"id\", \"\", 1, \"custom-select\", \"custom-select-sm\", 3, \"ngModelChange\", \"ngModel\"], [\"selected\", \"\", \"value\", \"0\"], [\"value\", \"1\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"col-md-1\", \"mb-1\"], [\"name\", \"\", \"id\", \"\", 1, \"custom-select\", \"custom-select-sm\", 3, \"ngModelChange\", \"change\", \"ngModel\"], [\"value\", \"5637170908\"], [\"value\", \"5637170910\"], [\"value\", \"3\"], [\"value\", \"0\"], [1, \"col-xs-12\", \"col-12\", \"col-md-2\", \"form-group\"], [\"type\", \"text\", \"placeholder\", \"DD/MM/YYYY\", \"bsDatepicker\", \"\", 1, \"form-control\", \"form-control-sm\", 3, \"ngModelChange\", \"bsConfig\", \"ngModel\"], [1, \"col-md-2\", \"mb-2\", \"col-12\", \"text-center\", \"text-sm-center\", \"text-md-left\", \"text-lg-left\"], [\"type\", \"submit\", 1, \"btn\", \"btn-primary\", \"btn-sm\", \"font-weight-light\", 3, \"click\", \"disabled\"], [\"id\", \"accordion\"], [1, \"card\"], [\"id\", \"headingOne\", 1, \"card-header\", 2, \"padding-top\", \"0px\", \"padding-bottom\", \"0px\"], [1, \"mb-0\"], [\"data-toggle\", \"collapse\", \"data-target\", \"#collapseOne\", \"aria-expanded\", \"true\", \"aria-controls\", \"collapseOne\", 1, \"btn\", \"btn-link\"], [\"id\", \"collapseOne\", \"aria-labelledby\", \"headingOne\", \"data-parent\", \"#accordion\", 1, \"collapse\", \"show\"], [1, \"card-body\"], [1, \"table\", \"table-hover\", \"table-bordered\", \"table-sm\"], [1, \"text-sm-center\", \"bg-light\"], [\"scope\", \"col\", 1, \"font-weight-normal\"], [\"scope\", \"col\", 1, \"font-weight-light\"], [\"class\", \"text-sm-left\", 4, \"ngFor\", \"ngForOf\"], [\"id\", \"headingTwo\", 1, \"card-header\", 2, \"padding-top\", \"0px\", \"padding-bottom\", \"0px\"], [1, \"mb-0\", 2, \"vertical-align\", \"central\"], [\"data-toggle\", \"collapse\", \"data-target\", \"#collapseTwo\", \"aria-expanded\", \"false\", \"aria-controls\", \"collapseTwo\", 1, \"btn\", \"btn-link\", \"collapsed\", 3, \"click\"], [\"id\", \"collapseTwo\", \"aria-labelledby\", \"headingTwo\", \"data-parent\", \"#accordion\", 1, \"collapse\"], [\"scope\", \"col\", 1, \"font-weight-normal\", \"text-center\"], [\"data-toggle\", \"modal\", \"data-target\", \"#exampleModals\", \"aria-expanded\", \"true\", 1, \"btn\", \"btn-link\", 3, \"click\", \"disabled\"], [1, \"badge\", \"badge-primary\", \"badge-pill\"], [\"id\", \"exampleModals\", \"tabindex\", \"-1\", \"role\", \"dialog\", \"aria-labelledby\", \"exampleModalLabel\", \"aria-hidden\", \"true\", 1, \"modal\", \"fade\"], [\"role\", \"document\", 1, \"modal-dialog\"], [1, \"modal-content\"], [1, \"modal-header\"], [\"id\", \"exampleModalLabel\", 1, \"modal-title\"], [\"type\", \"button\", \"data-dismiss\", \"modal\", \"aria-label\", \"Close\", 1, \"close\"], [\"aria-hidden\", \"true\"], [1, \"modal-body\"], [1, \"form-group\"], [1, \"custom-select\", \"custom-select-sm\", 3, \"change\"], [\"value\", \"D\"], [\"value\", \"I\"], [\"selected\", \"\", \"value\", \"N\"], [\"for\", \"\"], [\"type\", \"text\", \"name\", \"recipientname\", \"id\", \"recipientname\", 1, \"form-control\", 3, \"ngModelChange\", \"ngModel\"], [\"type\", \"text\", \"name\", \"Axidshow\", \"id\", \"Axidshow\", 1, \"form-control\", 3, \"ngModelChange\", \"ngModel\"], [1, \"custom-select\", \"custom-select-sm\", 3, \"ngModelChange\", \"ngModel\"], [\"value\", \"60\"], [\"value\", \"65\"], [\"value\", \"70\"], [\"value\", \"75\"], [\"value\", \"80\"], [\"value\", \"85\"], [\"for\", \"addressname\"], [\"name\", \"addressname\", \"id\", \"addressname\", \"cols\", \"30\", \"rows\", \"4\", 1, \"form-control\", 3, \"ngModelChange\", \"ngModel\"], [1, \"modal-footer\"], [1, \"text-danger\"], [\"type\", \"button\", \"data-dismiss\", \"modal\", 1, \"btn\", \"btn-secondary\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", 3, \"click\", \"disabled\"], [\"id\", \"mdlSample\", \"role\", \"dialog\", 1, \"modal\", \"fade\", 3, \"ngStyle\"], [1, \"modal-dialog\", \"modal-md\"], [1, \"modal-header\", \"colhaederal\"], [1, \"modal-title\"], [\"align\", \"right\", 1, \"modal-footer\"], [\"type\", \"button\", \"id\", \"btnClose\", 1, \"btn\", \"btn-danger\", 3, \"click\"], [1, \"fa\", \"fa-times\", \"fa-fw\"], [\"id\", \"exampleModalLabel\", \"tabindex\", \"-1\", \"role\", \"dialog\", \"aria-labelledby\", \"exampleModalLabel\", \"aria-hidden\", \"true\", 1, \"modal\", \"fade\"], [1, \"input-group\"], [\"type\", \"text\", \"name\", \"recipientname\", \"id\", \"recipientname\", 3, \"ngModelChange\", \"ngModel\"], [\"type\", \"button\", \"data-dismiss\", \"modal\", 1, \"btn\", \"btn-secondary\", 3, \"click\", \"disabled\"], [\"href\", i0.ɵɵtrustConstantResourceUrl`coverpage.component.css`], [\"multiple\", \"\", 1, \"custom-select\", \"custom-select-sm\", 3, \"ngModelChange\", \"ngModel\"], [\"selected\", \"\", \"disabled\", \"\", \"value\", \"\"], [3, \"value\"], [1, \"text-sm-left\"], [1, \"text-sm-center\", \"font-weight-normal\", 3, \"ngStyle\"], [1, \"font-weight-normal\", 3, \"ngStyle\"], [1, \"text-center\"], [\"type\", \"checkbox\", 3, \"click\", \"checked\"], [1, \"text-sm-center\", \"font-weight-normal\", \"text-center\"], [1, \"text-sm-left\", \"font-weight-normal\", \"text-center\"]],\n        template: function CoverpageComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelement(0, \"app-topmenu\");\n            i0.ɵɵelementStart(1, \"div\", 0)(2, \"section\", 1)(3, \"h5\", 2);\n            i0.ɵɵtext(4, \"Cover Page Printing\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(5, \"div\", 3);\n            i0.ɵɵtemplate(6, CoverpageComponent_div_6_Template, 7, 2, \"div\", 4);\n            i0.ɵɵelementStart(7, \"div\", 5)(8, \"select\", 6);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function CoverpageComponent_Template_select_ngModelChange_8_listener($event) {\n              i0.ɵɵtwoWayBindingSet(ctx.delivery, $event) || (ctx.delivery = $event);\n              return $event;\n            });\n            i0.ɵɵelementStart(9, \"option\", 7);\n            i0.ɵɵtext(10, \"\\u0E01\\u0E23\\u0E38\\u0E13\\u0E32\\u0E40\\u0E25\\u0E37\\u0E2D\\u0E01\\u0E40\\u0E07\\u0E37\\u0E48\\u0E2D\\u0E19\\u0E44\\u0E02\\u0E01\\u0E32\\u0E23\\u0E02\\u0E19\\u0E2A\\u0E48\\u0E07\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(11, \"option\", 8);\n            i0.ɵɵtext(12, \"\\u0E40\\u0E25\\u0E37\\u0E2D\\u0E01\\u0E17\\u0E38\\u0E38\\u0E01\\u0E23\\u0E32\\u0E22\\u0E01\\u0E32\\u0E23\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(13, CoverpageComponent_option_13_Template, 2, 2, \"option\", 9);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(14, \"div\", 10)(15, \"select\", 11);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function CoverpageComponent_Template_select_ngModelChange_15_listener($event) {\n              i0.ɵɵtwoWayBindingSet(ctx.saletype, $event) || (ctx.saletype = $event);\n              return $event;\n            });\n            i0.ɵɵlistener(\"change\", function CoverpageComponent_Template_select_change_15_listener() {\n              return ctx.searchselectsale();\n            });\n            i0.ɵɵelementStart(16, \"option\", 8);\n            i0.ɵɵtext(17, \"\\u0E40\\u0E25\\u0E37\\u0E2D\\u0E01\\u0E17\\u0E38\\u0E38\\u0E01\\u0E23\\u0E32\\u0E22\\u0E01\\u0E32\\u0E23\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(18, \"option\", 12);\n            i0.ɵɵtext(19, \"Sale \\u0E01\\u0E23\\u0E38\\u0E07\\u0E40\\u0E17\\u0E1E\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(20, \"option\", 13);\n            i0.ɵɵtext(21, \"Sale \\u0E15\\u0E48\\u0E32\\u0E07\\u0E08\\u0E31\\u0E07\\u0E2B\\u0E27\\u0E31\\u0E14\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(22, \"div\", 10)(23, \"select\", 11);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function CoverpageComponent_Template_select_ngModelChange_23_listener($event) {\n              i0.ɵɵtwoWayBindingSet(ctx.printst, $event) || (ctx.printst = $event);\n              return $event;\n            });\n            i0.ɵɵlistener(\"change\", function CoverpageComponent_Template_select_change_23_listener() {\n              return ctx.searchselectprint();\n            });\n            i0.ɵɵelementStart(24, \"option\", 14);\n            i0.ɵɵtext(25, \"\\u0E40\\u0E25\\u0E37\\u0E2D\\u0E01\\u0E17\\u0E38\\u0E38\\u0E01\\u0E23\\u0E32\\u0E22\\u0E01\\u0E32\\u0E23\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(26, \"option\", 15);\n            i0.ɵɵtext(27, \"\\u0E22\\u0E31\\u0E07\\u0E44\\u0E21\\u0E48 Print\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(28, \"option\", 8);\n            i0.ɵɵtext(29, \"Print \\u0E41\\u0E25\\u0E49\\u0E27\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(30, \"div\", 16)(31, \"input\", 17);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function CoverpageComponent_Template_input_ngModelChange_31_listener($event) {\n              i0.ɵɵtwoWayBindingSet(ctx.Datafromdate, $event) || (ctx.Datafromdate = $event);\n              return $event;\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(32, \"div\", 16)(33, \"input\", 17);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function CoverpageComponent_Template_input_ngModelChange_33_listener($event) {\n              i0.ɵɵtwoWayBindingSet(ctx.Datatodate, $event) || (ctx.Datatodate = $event);\n              return $event;\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(34, \"div\", 18)(35, \"button\", 19);\n            i0.ɵɵlistener(\"click\", function CoverpageComponent_Template_button_click_35_listener() {\n              return ctx.Searchsaleheader();\n            });\n            i0.ɵɵtext(36, \"Search\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(37, \"div\", 20)(38, \"div\", 21)(39, \"div\", 22)(40, \"h5\", 23)(41, \"button\", 24);\n            i0.ɵɵtext(42, \" Sale Order List \");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(43, \"div\", 25)(44, \"div\", 26)(45, \"div\", 0)(46, \"table\", 27)(47, \"thead\")(48, \"tr\", 28)(49, \"th\", 29);\n            i0.ɵɵtext(50, \"Item\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(51, \"th\", 30);\n            i0.ɵɵtext(52, \"\\u0E40\\u0E25\\u0E02\\u0E17\\u0E35\\u0E48 SO\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(53, \"th\", 29);\n            i0.ɵɵtext(54, \"\\u0E27\\u0E31\\u0E19\\u0E17\\u0E35\\u0E48\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(55, \"th\", 29);\n            i0.ɵɵtext(56, \"\\u0E1E\\u0E19\\u0E31\\u0E01\\u0E07\\u0E32\\u0E19\\u0E02\\u0E32\\u0E22\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(57, \"th\", 29);\n            i0.ɵɵtext(58, \"\\u0E25\\u0E39\\u0E01\\u0E04\\u0E49\\u0E32\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(59, \"th\", 29);\n            i0.ɵɵtext(60, \"\\u0E17\\u0E35\\u0E48\\u0E2D\\u0E22\\u0E39\\u0E48 INV\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(61, \"th\", 29);\n            i0.ɵɵtext(62, \"\\u0E17\\u0E35\\u0E48\\u0E2D\\u0E22\\u0E39\\u0E48\\u0E02\\u0E19\\u0E2A\\u0E48\\u0E07\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(63, \"th\", 29);\n            i0.ɵɵtext(64, \"VAT/No VAT\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(65, \"th\", 29);\n            i0.ɵɵtext(66, \"\\u0E19\\u0E49\\u0E33\\u0E2B\\u0E19\\u0E31\\u0E01\\u0E23\\u0E27\\u0E21\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(67, \"th\", 29);\n            i0.ɵɵtext(68, \"\\u0E1B\\u0E23\\u0E30\\u0E40\\u0E20\\u0E17\\u0E02\\u0E19\\u0E2A\\u0E48\\u0E07\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(69, \"th\", 29);\n            i0.ɵɵtext(70, \"\\u0E2B\\u0E21\\u0E32\\u0E22\\u0E40\\u0E2B\\u0E15\\u0E38\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(71, \"th\", 29);\n            i0.ɵɵtext(72, \"Note \\u0E20\\u0E32\\u0E22\\u0E43\\u0E19\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(73, \"th\", 29);\n            i0.ɵɵtext(74, \"\\u0E40\\u0E07\\u0E34\\u0E19\\u0E2A\\u0E14/\\u0E40\\u0E04\\u0E23\\u0E14\\u0E34\\u0E15\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(75, \"th\", 29);\n            i0.ɵɵtext(76, \"Print\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(77, \"tbody\");\n            i0.ɵɵtemplate(78, CoverpageComponent_tr_78_Template, 30, 58, \"tr\", 31);\n            i0.ɵɵelementEnd()()()()()();\n            i0.ɵɵelementStart(79, \"div\", 21)(80, \"div\", 32)(81, \"h5\", 33)(82, \"button\", 34);\n            i0.ɵɵlistener(\"click\", function CoverpageComponent_Template_button_click_82_listener() {\n              return ctx.clickloadcoverinfo();\n            });\n            i0.ɵɵtext(83, \" Cover Page Info \");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(84, \"div\", 35)(85, \"div\", 26)(86, \"table\", 27)(87, \"thead\")(88, \"tr\", 28)(89, \"th\", 36);\n            i0.ɵɵtext(90, \"Item\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(91, \"th\", 36);\n            i0.ɵɵtext(92, \"\\u0E23\\u0E2B\\u0E31\\u0E2A\\u0E2A\\u0E34\\u0E19\\u0E04\\u0E49\\u0E32\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(93, \"th\", 36);\n            i0.ɵɵtext(94, \"\\u0E08\\u0E33\\u0E19\\u0E27\\u0E19\\u0E0A\\u0E34\\u0E49\\u0E19\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(95, \"th\", 36);\n            i0.ɵɵtext(96, \"\\u0E08\\u0E33\\u0E19\\u0E27\\u0E19\\u0E25\\u0E31\\u0E07/\\u0E21\\u0E31\\u0E14\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(97, \"th\", 36);\n            i0.ɵɵtext(98, \"\\u0E08\\u0E33\\u0E19\\u0E27\\u0E19\\u0E41\\u0E1C\\u0E48\\u0E19\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(99, \"th\")(100, \"button\", 37);\n            i0.ɵɵlistener(\"click\", function CoverpageComponent_Template_button_click_100_listener() {\n              return ctx.printPX();\n            });\n            i0.ɵɵtext(101, \"Print\");\n            i0.ɵɵelementStart(102, \"span\", 38);\n            i0.ɵɵtext(103);\n            i0.ɵɵpipe(104, \"number\");\n            i0.ɵɵelementEnd()()()()();\n            i0.ɵɵelementStart(105, \"tbody\");\n            i0.ɵɵtemplate(106, CoverpageComponent_tr_106_Template, 14, 11, \"tr\", 31);\n            i0.ɵɵelementEnd()()()()()()()();\n            i0.ɵɵelementStart(107, \"div\", 39)(108, \"div\", 40)(109, \"div\", 41)(110, \"div\", 42)(111, \"h5\", 43);\n            i0.ɵɵtext(112);\n            i0.ɵɵpipe(113, \"number\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(114, \"button\", 44)(115, \"span\", 45);\n            i0.ɵɵtext(116, \"\\u00D7\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(117, \"div\", 46)(118, \"div\", 47)(119, \"select\", 48);\n            i0.ɵɵlistener(\"change\", function CoverpageComponent_Template_select_change_119_listener($event) {\n              return ctx.SelectName($event);\n            });\n            i0.ɵɵelementStart(120, \"option\", 49);\n            i0.ɵɵtext(121, \"\\u0E0A\\u0E37\\u0E48\\u0E2D\\u0E02\\u0E19\\u0E2A\\u0E48\\u0E07\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(122, \"option\", 50);\n            i0.ɵɵtext(123, \"\\u0E0A\\u0E37\\u0E48\\u0E2D\\u0E27\\u0E32\\u0E07\\u0E1A\\u0E34\\u0E25\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(124, \"option\", 51);\n            i0.ɵɵtext(125, \"\\u0E0A\\u0E37\\u0E48\\u0E2D\\u0E25\\u0E39\\u0E01\\u0E04\\u0E49\\u0E32\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(126, \"div\", 47)(127, \"label\", 52);\n            i0.ɵɵtext(128, \"\\u0E1B\\u0E49\\u0E2D\\u0E19\\u0E0A\\u0E37\\u0E48\\u0E2D\\u0E1C\\u0E39\\u0E49\\u0E23\\u0E31\\u0E1A\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(129, \"input\", 53);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function CoverpageComponent_Template_input_ngModelChange_129_listener($event) {\n              i0.ɵɵtwoWayBindingSet(ctx.recipientname, $event) || (ctx.recipientname = $event);\n              return $event;\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(130, \"div\", 47)(131, \"label\", 52);\n            i0.ɵɵtext(132, \"\\u0E40\\u0E25\\u0E02\\u0E17\\u0E35\\u0E48 SOAX\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(133, \"input\", 54);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function CoverpageComponent_Template_input_ngModelChange_133_listener($event) {\n              i0.ɵɵtwoWayBindingSet(ctx.Axidshow, $event) || (ctx.Axidshow = $event);\n              return $event;\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(134, \"div\", 47)(135, \"label\", 52);\n            i0.ɵɵtext(136, \"\\u0E02\\u0E19\\u0E32\\u0E14\\u0E15\\u0E31\\u0E27\\u0E2D\\u0E31\\u0E01\\u0E29\\u0E23\\u0E0A\\u0E37\\u0E48\\u0E2D\\u0E1C\\u0E39\\u0E49\\u0E23\\u0E31\\u0E1A\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(137, \"select\", 55);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function CoverpageComponent_Template_select_ngModelChange_137_listener($event) {\n              i0.ɵɵtwoWayBindingSet(ctx.pxHaerder, $event) || (ctx.pxHaerder = $event);\n              return $event;\n            });\n            i0.ɵɵelementStart(138, \"option\", 56);\n            i0.ɵɵtext(139, \"60\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(140, \"option\", 57);\n            i0.ɵɵtext(141, \"65\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(142, \"option\", 58);\n            i0.ɵɵtext(143, \"70\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(144, \"option\", 59);\n            i0.ɵɵtext(145, \"75\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(146, \"option\", 60);\n            i0.ɵɵtext(147, \"80\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(148, \"option\", 61);\n            i0.ɵɵtext(149, \"85\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(150, \"div\", 47)(151, \"label\", 62);\n            i0.ɵɵtext(152, \"\\u0E17\\u0E35\\u0E48\\u0E2D\\u0E22\\u0E39\\u0E48\\u0E08\\u0E31\\u0E14\\u0E2A\\u0E48\\u0E07\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(153, \"textarea\", 63);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function CoverpageComponent_Template_textarea_ngModelChange_153_listener($event) {\n              i0.ɵɵtwoWayBindingSet(ctx.addressname, $event) || (ctx.addressname = $event);\n              return $event;\n            });\n            i0.ɵɵtext(154, \"\\n                    \");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(155, \"div\", 64)(156, \"label\", 65);\n            i0.ɵɵtext(157);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(158, \"button\", 66);\n            i0.ɵɵtext(159, \"Close\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(160, \"button\", 67);\n            i0.ɵɵlistener(\"click\", function CoverpageComponent_Template_button_click_160_listener() {\n              return ctx.setflagprint();\n            });\n            i0.ɵɵtext(161, \"\\u0E15\\u0E01\\u0E25\\u0E07\");\n            i0.ɵɵelementEnd()()()()();\n            i0.ɵɵelementStart(162, \"div\", 68)(163, \"div\", 69)(164, \"div\", 41)(165, \"div\", 70)(166, \"h4\", 71);\n            i0.ɵɵtext(167, \"Report\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(168, \"div\", 46);\n            i0.ɵɵtext(169);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(170, \"div\", 72)(171, \"button\", 73);\n            i0.ɵɵlistener(\"click\", function CoverpageComponent_Template_button_click_171_listener() {\n              return ctx.closemodel(false);\n            });\n            i0.ɵɵelement(172, \"i\", 74);\n            i0.ɵɵtext(173, \" \\u0E1B\\u0E34\\u0E14\");\n            i0.ɵɵelementEnd()()()()();\n            i0.ɵɵelementStart(174, \"div\", 75)(175, \"div\", 40)(176, \"div\", 41)(177, \"div\", 42);\n            i0.ɵɵelement(178, \"h5\", 43);\n            i0.ɵɵelementStart(179, \"button\", 44)(180, \"span\", 45);\n            i0.ɵɵtext(181, \"\\u00D7\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(182, \"div\", 46)(183, \"div\", 76)(184, \"div\", 47)(185, \"label\", 52);\n            i0.ɵɵtext(186, \"\\u0E1B\\u0E49\\u0E2D\\u0E19\\u0E0A\\u0E37\\u0E48\\u0E2D\\u0E1C\\u0E39\\u0E49\\u0E23\\u0E31\\u0E1A\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(187, \"input\", 77);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function CoverpageComponent_Template_input_ngModelChange_187_listener($event) {\n              i0.ɵɵtwoWayBindingSet(ctx.recipientname, $event) || (ctx.recipientname = $event);\n              return $event;\n            });\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(188, \"div\", 64)(189, \"button\", 78);\n            i0.ɵɵlistener(\"click\", function CoverpageComponent_Template_button_click_189_listener() {\n              return ctx.print();\n            });\n            i0.ɵɵtext(190, \"\\u0E15\\u0E01\\u0E25\\u0E07\");\n            i0.ɵɵelementEnd()()()()();\n            i0.ɵɵelement(191, \"link\", 79);\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(6);\n            i0.ɵɵproperty(\"ngIf\", ctx.testclose);\n            i0.ɵɵadvance(2);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.delivery);\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"ngForOf\", ctx.deliverytype);\n            i0.ɵɵadvance(2);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.saletype);\n            i0.ɵɵadvance(8);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.printst);\n            i0.ɵɵadvance(8);\n            i0.ɵɵproperty(\"bsConfig\", i0.ɵɵpureFunction0(31, _c0));\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.Datafromdate);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"bsConfig\", i0.ɵɵpureFunction0(32, _c0));\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.Datatodate);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"disabled\", ctx.searchbtn);\n            i0.ɵɵadvance(43);\n            i0.ɵɵproperty(\"ngForOf\", ctx.seachheaderlist);\n            i0.ɵɵadvance(22);\n            i0.ɵɵproperty(\"disabled\", ctx.searchbtn);\n            i0.ɵɵadvance(3);\n            i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(104, 25, ctx.pageprintall, \"1.0-0\"));\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"ngForOf\", ctx.salelinelist);\n            i0.ɵɵadvance(6);\n            i0.ɵɵtextInterpolate1(\"\\u0E1B\\u0E49\\u0E2D\\u0E19\\u0E0A\\u0E37\\u0E48\\u0E2D\\u0E1C\\u0E39\\u0E49\\u0E23\\u0E31\\u0E1A \\u0E41\\u0E25\\u0E30 \\u0E17\\u0E35\\u0E48\\u0E2D\\u0E22\\u0E39\\u0E48\\u0E02\\u0E19\\u0E2A\\u0E48\\u0E07 \\u0E08\\u0E33\\u0E19\\u0E27\\u0E19\\u0E41\\u0E1C\\u0E48\\u0E19\\u0E17\\u0E31\\u0E49\\u0E07\\u0E2B\\u0E21\\u0E14 \", i0.ɵɵpipeBind2(113, 28, ctx.pageprintall, \"1.0-0\"), \"\");\n            i0.ɵɵadvance(17);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.recipientname);\n            i0.ɵɵadvance(4);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.Axidshow);\n            i0.ɵɵadvance(4);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.pxHaerder);\n            i0.ɵɵadvance(16);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.addressname);\n            i0.ɵɵadvance(4);\n            i0.ɵɵtextInterpolate(ctx.alerttext);\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"disabled\", ctx.searchbtn);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(33, _c1, ctx.mdlSampleIsOpen ? \"block\" : \"none\"));\n            i0.ɵɵadvance(7);\n            i0.ɵɵtextInterpolate1(\"\", ctx.alt, \" \");\n            i0.ɵɵadvance(18);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.recipientname);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"disabled\", ctx.searchbtn);\n          }\n        },\n        styles: [\".headpage[_ngcontent-%COMP%]{font-size:65px;text-align:center}\"]\n      });\n    }\n  }\n  return CoverpageComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}