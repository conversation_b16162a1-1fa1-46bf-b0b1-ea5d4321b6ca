{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.EmptyError = void 0;\nvar createErrorClass_1 = require(\"./createErrorClass\");\nexports.EmptyError = createErrorClass_1.createErrorClass(function (_super) {\n  return function EmptyErrorImpl() {\n    _super(this);\n    this.name = 'EmptyError';\n    this.message = 'no elements in sequence';\n  };\n});\n//# sourceMappingURL=EmptyError.js.map", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}