{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Estonian [et]\n//! author : <PERSON> : https://github.com/madhenry\n//! improvements : Il<PERSON><PERSON> : https://github.com/ragulka\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  function processRelativeTime(number, withoutSuffix, key, isFuture) {\n    var format = {\n      s: ['mõne sekundi', 'mõni sekund', 'paar sekundit'],\n      ss: [number + 'sekundi', number + 'sekundit'],\n      m: ['ühe minuti', 'üks minut'],\n      mm: [number + ' minuti', number + ' minutit'],\n      h: ['ühe tunni', 'tund aega', 'üks tund'],\n      hh: [number + ' tunni', number + ' tundi'],\n      d: ['ühe päeva', 'üks päev'],\n      M: ['kuu aja', 'kuu aega', 'üks kuu'],\n      MM: [number + ' kuu', number + ' kuud'],\n      y: ['ühe aasta', 'aasta', 'üks aasta'],\n      yy: [number + ' aasta', number + ' aastat']\n    };\n    if (withoutSuffix) {\n      return format[key][2] ? format[key][2] : format[key][1];\n    }\n    return isFuture ? format[key][0] : format[key][1];\n  }\n  var et = moment.defineLocale('et', {\n    months: 'jaanuar_veebruar_märts_aprill_mai_juuni_juuli_august_september_oktoober_november_detsember'.split('_'),\n    monthsShort: 'jaan_veebr_märts_apr_mai_juuni_juuli_aug_sept_okt_nov_dets'.split('_'),\n    weekdays: 'pühapäev_esmaspäev_teisipäev_kolmapäev_neljapäev_reede_laupäev'.split('_'),\n    weekdaysShort: 'P_E_T_K_N_R_L'.split('_'),\n    weekdaysMin: 'P_E_T_K_N_R_L'.split('_'),\n    longDateFormat: {\n      LT: 'H:mm',\n      LTS: 'H:mm:ss',\n      L: 'DD.MM.YYYY',\n      LL: 'D. MMMM YYYY',\n      LLL: 'D. MMMM YYYY H:mm',\n      LLLL: 'dddd, D. MMMM YYYY H:mm'\n    },\n    calendar: {\n      sameDay: '[Täna,] LT',\n      nextDay: '[Homme,] LT',\n      nextWeek: '[Järgmine] dddd LT',\n      lastDay: '[Eile,] LT',\n      lastWeek: '[Eelmine] dddd LT',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: '%s pärast',\n      past: '%s tagasi',\n      s: processRelativeTime,\n      ss: processRelativeTime,\n      m: processRelativeTime,\n      mm: processRelativeTime,\n      h: processRelativeTime,\n      hh: processRelativeTime,\n      d: processRelativeTime,\n      dd: '%d päeva',\n      M: processRelativeTime,\n      MM: processRelativeTime,\n      y: processRelativeTime,\n      yy: processRelativeTime\n    },\n    dayOfMonthOrdinalParse: /\\d{1,2}\\./,\n    ordinal: '%d.',\n    week: {\n      dow: 1,\n      // Monday is the first day of the week.\n      doy: 4 // The week that contains Jan 4th is the first week of the year.\n    }\n  });\n  return et;\n});", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}