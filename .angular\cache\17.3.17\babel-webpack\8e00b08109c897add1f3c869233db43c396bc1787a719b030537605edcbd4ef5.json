{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.shareReplay = void 0;\nvar ReplaySubject_1 = require(\"../ReplaySubject\");\nvar share_1 = require(\"./share\");\nfunction shareReplay(configOrBufferSize, windowTime, scheduler) {\n  var _a, _b, _c;\n  var bufferSize;\n  var refCount = false;\n  if (configOrBufferSize && typeof configOrBufferSize === 'object') {\n    _a = configOrBufferSize.bufferSize, bufferSize = _a === void 0 ? Infinity : _a, _b = configOrBufferSize.windowTime, windowTime = _b === void 0 ? Infinity : _b, _c = configOrBufferSize.refCount, refCount = _c === void 0 ? false : _c, scheduler = configOrBufferSize.scheduler;\n  } else {\n    bufferSize = configOrBufferSize !== null && configOrBufferSize !== void 0 ? configOrBufferSize : Infinity;\n  }\n  return share_1.share({\n    connector: function () {\n      return new ReplaySubject_1.ReplaySubject(bufferSize, windowTime, scheduler);\n    },\n    resetOnError: true,\n    resetOnComplete: false,\n    resetOnRefCountZero: refCount\n  });\n}\nexports.shareReplay = shareReplay;\n//# sourceMappingURL=shareReplay.js.map", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}