{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.pairwise = void 0;\nvar lift_1 = require(\"../util/lift\");\nvar OperatorSubscriber_1 = require(\"./OperatorSubscriber\");\nfunction pairwise() {\n  return lift_1.operate(function (source, subscriber) {\n    var prev;\n    var hasPrev = false;\n    source.subscribe(OperatorSubscriber_1.createOperatorSubscriber(subscriber, function (value) {\n      var p = prev;\n      prev = value;\n      hasPrev && subscriber.next([p, value]);\n      hasPrev = true;\n    }));\n  });\n}\nexports.pairwise = pairwise;\n//# sourceMappingURL=pairwise.js.map", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}