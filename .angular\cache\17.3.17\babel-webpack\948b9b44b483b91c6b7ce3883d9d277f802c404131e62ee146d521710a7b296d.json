{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.window = void 0;\nvar Subject_1 = require(\"../Subject\");\nvar lift_1 = require(\"../util/lift\");\nvar OperatorSubscriber_1 = require(\"./OperatorSubscriber\");\nvar noop_1 = require(\"../util/noop\");\nvar innerFrom_1 = require(\"../observable/innerFrom\");\nfunction window(windowBoundaries) {\n  return lift_1.operate(function (source, subscriber) {\n    var windowSubject = new Subject_1.Subject();\n    subscriber.next(windowSubject.asObservable());\n    var errorHandler = function (err) {\n      windowSubject.error(err);\n      subscriber.error(err);\n    };\n    source.subscribe(OperatorSubscriber_1.createOperatorSubscriber(subscriber, function (value) {\n      return windowSubject === null || windowSubject === void 0 ? void 0 : windowSubject.next(value);\n    }, function () {\n      windowSubject.complete();\n      subscriber.complete();\n    }, errorHandler));\n    innerFrom_1.innerFrom(windowBoundaries).subscribe(OperatorSubscriber_1.createOperatorSubscriber(subscriber, function () {\n      windowSubject.complete();\n      subscriber.next(windowSubject = new Subject_1.Subject());\n    }, noop_1.noop, errorHandler));\n    return function () {\n      windowSubject === null || windowSubject === void 0 ? void 0 : windowSubject.unsubscribe();\n      windowSubject = null;\n    };\n  });\n}\nexports.window = window;\n//# sourceMappingURL=window.js.map", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}