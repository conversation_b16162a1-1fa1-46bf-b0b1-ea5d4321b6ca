{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.from = void 0;\nvar scheduled_1 = require(\"../scheduled/scheduled\");\nvar innerFrom_1 = require(\"./innerFrom\");\nfunction from(input, scheduler) {\n  return scheduler ? scheduled_1.scheduled(input, scheduler) : innerFrom_1.innerFrom(input);\n}\nexports.from = from;\n//# sourceMappingURL=from.js.map", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}