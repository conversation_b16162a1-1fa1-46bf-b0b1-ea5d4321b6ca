{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.arrRemove = void 0;\nfunction arrRemove(arr, item) {\n  if (arr) {\n    var index = arr.indexOf(item);\n    0 <= index && arr.splice(index, 1);\n  }\n}\nexports.arrRemove = arrRemove;\n//# sourceMappingURL=arrRemove.js.map", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}