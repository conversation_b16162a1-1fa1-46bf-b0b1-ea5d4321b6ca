{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.timeoutWith = void 0;\nvar async_1 = require(\"../scheduler/async\");\nvar isDate_1 = require(\"../util/isDate\");\nvar timeout_1 = require(\"./timeout\");\nfunction timeoutWith(due, withObservable, scheduler) {\n  var first;\n  var each;\n  var _with;\n  scheduler = scheduler !== null && scheduler !== void 0 ? scheduler : async_1.async;\n  if (isDate_1.isValidDate(due)) {\n    first = due;\n  } else if (typeof due === 'number') {\n    each = due;\n  }\n  if (withObservable) {\n    _with = function () {\n      return withObservable;\n    };\n  } else {\n    throw new TypeError('No observable provided to switch to');\n  }\n  if (first == null && each == null) {\n    throw new TypeError('No timeout provided.');\n  }\n  return timeout_1.timeout({\n    first: first,\n    each: each,\n    scheduler: scheduler,\n    with: _with\n  });\n}\nexports.timeoutWith = timeoutWith;\n//# sourceMappingURL=timeoutWith.js.map", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}