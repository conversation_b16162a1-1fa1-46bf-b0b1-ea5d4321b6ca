{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.last = void 0;\nvar EmptyError_1 = require(\"../util/EmptyError\");\nvar filter_1 = require(\"./filter\");\nvar takeLast_1 = require(\"./takeLast\");\nvar throwIfEmpty_1 = require(\"./throwIfEmpty\");\nvar defaultIfEmpty_1 = require(\"./defaultIfEmpty\");\nvar identity_1 = require(\"../util/identity\");\nfunction last(predicate, defaultValue) {\n  var hasDefaultValue = arguments.length >= 2;\n  return function (source) {\n    return source.pipe(predicate ? filter_1.filter(function (v, i) {\n      return predicate(v, i, source);\n    }) : identity_1.identity, takeLast_1.takeLast(1), hasDefaultValue ? defaultIfEmpty_1.defaultIfEmpty(defaultValue) : throwIfEmpty_1.throwIfEmpty(function () {\n      return new EmptyError_1.EmptyError();\n    }));\n  };\n}\nexports.last = last;\n//# sourceMappingURL=last.js.map", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}