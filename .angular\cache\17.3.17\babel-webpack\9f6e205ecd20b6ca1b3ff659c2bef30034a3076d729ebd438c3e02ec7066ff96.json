{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.never = exports.NEVER = void 0;\nvar Observable_1 = require(\"../Observable\");\nvar noop_1 = require(\"../util/noop\");\nexports.NEVER = new Observable_1.Observable(noop_1.noop);\nfunction never() {\n  return exports.NEVER;\n}\nexports.never = never;\n//# sourceMappingURL=never.js.map", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}