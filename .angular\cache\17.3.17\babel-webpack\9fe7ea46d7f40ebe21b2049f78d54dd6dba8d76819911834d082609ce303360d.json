{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.publish = void 0;\nvar Subject_1 = require(\"../Subject\");\nvar multicast_1 = require(\"./multicast\");\nvar connect_1 = require(\"./connect\");\nfunction publish(selector) {\n  return selector ? function (source) {\n    return connect_1.connect(selector)(source);\n  } : function (source) {\n    return multicast_1.multicast(new Subject_1.Subject())(source);\n  };\n}\nexports.publish = publish;\n//# sourceMappingURL=publish.js.map", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}