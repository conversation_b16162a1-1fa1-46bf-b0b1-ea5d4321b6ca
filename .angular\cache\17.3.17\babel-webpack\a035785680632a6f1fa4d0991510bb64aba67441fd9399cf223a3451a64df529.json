{"ast": null, "code": "\"use strict\";\n\nvar __extends = this && this.__extends || function () {\n  var extendStatics = Object.setPrototypeOf || {\n    __proto__: []\n  } instanceof Array && function (d, b) {\n    d.__proto__ = b;\n  } || function (d, b) {\n    for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p];\n  };\n  return function (d, b) {\n    extendStatics(d, b);\n    function __() {\n      this.constructor = d;\n    }\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n  };\n}();\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nvar core_1 = require(\"@angular/core\");\nvar forms_1 = require(\"@angular/forms\");\nvar platform_browser_1 = require(\"@angular/platform-browser\");\nvar select_item_1 = require(\"./select-item\");\nvar select_pipes_1 = require(\"./select-pipes\");\nvar common_1 = require(\"./common\");\nvar styles = \"\\n  .ui-select-toggle {\\n    position: relative;\\n  }\\n\\n  /* Fix caret going into new line in Firefox */\\n  .ui-select-placeholder {\\n    float: left;\\n  }\\n  \\n  /* Fix Bootstrap dropdown position when inside a input-group */\\n  .input-group > .dropdown {\\n    /* Instead of relative */\\n    position: static;\\n  }\\n  \\n  .ui-select-match > .btn {\\n    /* Instead of center because of .btn */\\n    text-align: left !important;\\n  }\\n  \\n  .ui-select-match > .caret {\\n    position: absolute;\\n    top: 45%;\\n    right: 15px;\\n  }\\n  \\n  .ui-disabled {\\n    background-color: #eceeef;\\n    border-radius: 4px;\\n    position: absolute;\\n    width: 100%;\\n    height: 100%;\\n    z-index: 5;\\n    opacity: 0.6;\\n    top: 0;\\n    left: 0;\\n    cursor: not-allowed;\\n  }\\n  \\n  .ui-select-choices {\\n    width: 100%;\\n    height: auto;\\n    max-height: 200px;\\n    overflow-x: hidden;\\n    margin-top: 0;\\n  }\\n  \\n  .ui-select-multiple .ui-select-choices {\\n    margin-top: 1px;\\n  }\\n  .ui-select-choices-row>a {\\n      display: block;\\n      padding: 3px 20px;\\n      clear: both;\\n      font-weight: 400;\\n      line-height: 1.42857143;\\n      color: #333;\\n      white-space: nowrap;\\n  }\\n  .ui-select-choices-row.active>a {\\n      color: #fff;\\n      text-decoration: none;\\n      outline: 0;\\n      background-color: #428bca;\\n  }\\n  \\n  .ui-select-multiple {\\n    height: auto;\\n    padding:3px 3px 0 3px;\\n  }\\n  \\n  .ui-select-multiple input.ui-select-search {\\n    background-color: transparent !important; /* To prevent double background when disabled */\\n    border: none;\\n    outline: none;\\n    box-shadow: none;\\n    height: 1.6666em;\\n    padding: 0;\\n    margin-bottom: 3px;\\n    \\n  }\\n  .ui-select-match .close {\\n      font-size: 1.6em;\\n      line-height: 0.75;\\n  }\\n  \\n  .ui-select-multiple .ui-select-match-item {\\n    outline: 0;\\n    margin: 0 3px 3px 0;\\n  }\\n  .ui-select-toggle > .caret {\\n      position: absolute;\\n      height: 10px;\\n      top: 50%;\\n      right: 10px;\\n      margin-top: -2px;\\n  }\\n\";\nvar SelectComponent = function () {\n  function SelectComponent(element, sanitizer) {\n    this.sanitizer = sanitizer;\n    this.allowClear = false;\n    this.placeholder = '';\n    this.idField = 'id';\n    this.textField = 'text';\n    this.childrenField = 'children';\n    this.multiple = false;\n    this.data = new core_1.EventEmitter();\n    this.selected = new core_1.EventEmitter();\n    this.removed = new core_1.EventEmitter();\n    this.typed = new core_1.EventEmitter();\n    this.opened = new core_1.EventEmitter();\n    this.options = [];\n    this.itemObjects = [];\n    this.onChange = Function.prototype;\n    this.onTouched = Function.prototype;\n    this.inputMode = false;\n    this._optionsOpened = false;\n    this.inputValue = '';\n    this._items = [];\n    this._disabled = false;\n    this._active = [];\n    this.element = element;\n    this.clickedOutside = this.clickedOutside.bind(this);\n  }\n  Object.defineProperty(SelectComponent.prototype, \"items\", {\n    set: function (value) {\n      var _this = this;\n      if (!value) {\n        this._items = this.itemObjects = [];\n      } else {\n        this._items = value.filter(function (item) {\n          if (typeof item === 'string' || typeof item === 'object' && item && item[_this.textField] && item[_this.idField]) {\n            return item;\n          }\n        });\n        this.itemObjects = this._items.map(function (item) {\n          return typeof item === 'string' ? new select_item_1.SelectItem(item) : new select_item_1.SelectItem({\n            id: item[_this.idField],\n            text: item[_this.textField],\n            children: item[_this.childrenField]\n          });\n        });\n      }\n    },\n    enumerable: true,\n    configurable: true\n  });\n  Object.defineProperty(SelectComponent.prototype, \"disabled\", {\n    get: function () {\n      return this._disabled;\n    },\n    set: function (value) {\n      this._disabled = value;\n      if (this._disabled === true) {\n        this.hideOptions();\n      }\n    },\n    enumerable: true,\n    configurable: true\n  });\n  Object.defineProperty(SelectComponent.prototype, \"active\", {\n    get: function () {\n      return this._active;\n    },\n    set: function (selectedItems) {\n      var _this = this;\n      if (!selectedItems || selectedItems.length === 0) {\n        this._active = [];\n      } else {\n        var areItemsStrings_1 = typeof selectedItems[0] === 'string';\n        this._active = selectedItems.map(function (item) {\n          var data = areItemsStrings_1 ? item : {\n            id: item[_this.idField],\n            text: item[_this.textField]\n          };\n          return new select_item_1.SelectItem(data);\n        });\n      }\n    },\n    enumerable: true,\n    configurable: true\n  });\n  Object.defineProperty(SelectComponent.prototype, \"optionsOpened\", {\n    get: function () {\n      return this._optionsOpened;\n    },\n    set: function (value) {\n      this._optionsOpened = value;\n      this.opened.emit(value);\n    },\n    enumerable: true,\n    configurable: true\n  });\n  SelectComponent.prototype.sanitize = function (html) {\n    return this.sanitizer.bypassSecurityTrustHtml(html);\n  };\n  SelectComponent.prototype.inputEvent = function (e, isUpMode) {\n    if (isUpMode === void 0) {\n      isUpMode = false;\n    }\n    // tab\n    if (e.keyCode === 9) {\n      return;\n    }\n    if (isUpMode && (e.keyCode === 37 || e.keyCode === 39 || e.keyCode === 38 || e.keyCode === 40 || e.keyCode === 13)) {\n      e.preventDefault();\n      return;\n    }\n    // backspace\n    if (!isUpMode && e.keyCode === 8) {\n      var el = this.element.nativeElement.querySelector('div.ui-select-container > input');\n      if (!el.value || el.value.length <= 0) {\n        if (this.active.length > 0) {\n          this.remove(this.active[this.active.length - 1]);\n        }\n        e.preventDefault();\n      }\n    }\n    // esc\n    if (!isUpMode && e.keyCode === 27) {\n      this.hideOptions();\n      this.element.nativeElement.children[0].focus();\n      e.preventDefault();\n      return;\n    }\n    // del\n    if (!isUpMode && e.keyCode === 46) {\n      if (this.active.length > 0) {\n        this.remove(this.active[this.active.length - 1]);\n      }\n      e.preventDefault();\n    }\n    // left\n    if (!isUpMode && e.keyCode === 37 && this._items.length > 0) {\n      this.behavior.first();\n      e.preventDefault();\n      return;\n    }\n    // right\n    if (!isUpMode && e.keyCode === 39 && this._items.length > 0) {\n      this.behavior.last();\n      e.preventDefault();\n      return;\n    }\n    // up\n    if (!isUpMode && e.keyCode === 38) {\n      this.behavior.prev();\n      e.preventDefault();\n      return;\n    }\n    // down\n    if (!isUpMode && e.keyCode === 40) {\n      this.behavior.next();\n      e.preventDefault();\n      return;\n    }\n    // enter\n    if (!isUpMode && e.keyCode === 13) {\n      if (this.active.indexOf(this.activeOption) === -1) {\n        this.selectActiveMatch();\n        this.behavior.next();\n      }\n      e.preventDefault();\n      return;\n    }\n    var target = e.target || e.srcElement;\n    if (target && target.value) {\n      this.inputValue = target.value;\n      this.behavior.filter(new RegExp(common_1.escapeRegexp(this.inputValue), 'ig'));\n      this.doEvent('typed', this.inputValue);\n    } else {\n      this.open();\n    }\n  };\n  SelectComponent.prototype.ngOnInit = function () {\n    this.behavior = this.firstItemHasChildren ? new ChildrenBehavior(this) : new GenericBehavior(this);\n  };\n  SelectComponent.prototype.remove = function (item) {\n    if (this._disabled === true) {\n      return;\n    }\n    if (this.multiple === true && this.active) {\n      var index = this.active.indexOf(item);\n      this.active.splice(index, 1);\n      this.data.next(this.active);\n      this.doEvent('removed', item);\n    }\n    if (this.multiple === false) {\n      this.active = [];\n      this.data.next(this.active);\n      this.doEvent('removed', item);\n    }\n  };\n  SelectComponent.prototype.doEvent = function (type, value) {\n    if (this[type] && value) {\n      this[type].next(value);\n    }\n    this.onTouched();\n    if (type === 'selected' || type === 'removed') {\n      this.onChange(this.active);\n    }\n  };\n  SelectComponent.prototype.clickedOutside = function () {\n    this.inputMode = false;\n    this.optionsOpened = false;\n  };\n  Object.defineProperty(SelectComponent.prototype, \"firstItemHasChildren\", {\n    get: function () {\n      return this.itemObjects[0] && this.itemObjects[0].hasChildren();\n    },\n    enumerable: true,\n    configurable: true\n  });\n  SelectComponent.prototype.writeValue = function (val) {\n    this.active = val;\n    this.data.emit(this.active);\n  };\n  SelectComponent.prototype.validate = function (c) {\n    var controlValue = c ? c.value : [];\n    if (!controlValue) {\n      controlValue = [];\n    }\n    return controlValue.length > 0 ? null : {\n      ng2SelectEmptyError: {\n        valid: false\n      }\n    };\n  };\n  SelectComponent.prototype.registerOnChange = function (fn) {\n    this.onChange = fn;\n  };\n  SelectComponent.prototype.registerOnTouched = function (fn) {\n    this.onTouched = fn;\n  };\n  SelectComponent.prototype.matchClick = function (e) {\n    if (this._disabled === true) {\n      return;\n    }\n    this.inputMode = !this.inputMode;\n    if (this.inputMode === true && (this.multiple === true && e || this.multiple === false)) {\n      this.focusToInput();\n      this.open();\n    }\n  };\n  SelectComponent.prototype.mainClick = function (event) {\n    if (this.inputMode === true || this._disabled === true) {\n      return;\n    }\n    if (event.keyCode === 46) {\n      event.preventDefault();\n      this.inputEvent(event);\n      return;\n    }\n    if (event.keyCode === 8) {\n      event.preventDefault();\n      this.inputEvent(event, true);\n      return;\n    }\n    if (event.keyCode === 9 || event.keyCode === 13 || event.keyCode === 27 || event.keyCode >= 37 && event.keyCode <= 40) {\n      event.preventDefault();\n      return;\n    }\n    this.inputMode = true;\n    var value = String.fromCharCode(96 <= event.keyCode && event.keyCode <= 105 ? event.keyCode - 48 : event.keyCode).toLowerCase();\n    this.focusToInput(value);\n    this.open();\n    var target = event.target || event.srcElement;\n    target.value = value;\n    this.inputEvent(event);\n  };\n  SelectComponent.prototype.selectActive = function (value) {\n    this.activeOption = value;\n  };\n  SelectComponent.prototype.isActive = function (value) {\n    return this.activeOption.id === value.id;\n  };\n  SelectComponent.prototype.removeClick = function (value, event) {\n    event.stopPropagation();\n    this.remove(value);\n  };\n  SelectComponent.prototype.focusToInput = function (value) {\n    var _this = this;\n    if (value === void 0) {\n      value = '';\n    }\n    setTimeout(function () {\n      var el = _this.element.nativeElement.querySelector('div.ui-select-container > input');\n      if (el) {\n        el.focus();\n        el.value = value;\n      }\n    }, 0);\n  };\n  SelectComponent.prototype.open = function () {\n    var _this = this;\n    this.options = this.itemObjects.filter(function (option) {\n      return _this.multiple === false || _this.multiple === true && !_this.active.find(function (o) {\n        return option.text === o.text;\n      });\n    });\n    if (this.options.length > 0) {\n      this.behavior.first();\n    }\n    this.optionsOpened = true;\n  };\n  SelectComponent.prototype.hideOptions = function () {\n    this.inputMode = false;\n    this.optionsOpened = false;\n  };\n  SelectComponent.prototype.selectActiveMatch = function () {\n    this.selectMatch(this.activeOption);\n  };\n  SelectComponent.prototype.selectMatch = function (value, e) {\n    if (e === void 0) {\n      e = void 0;\n    }\n    if (e) {\n      e.stopPropagation();\n      e.preventDefault();\n    }\n    if (this.options.length <= 0) {\n      return;\n    }\n    if (this.multiple === true) {\n      this.active.push(value);\n      this.data.next(this.active);\n    }\n    if (this.multiple === false) {\n      this.active[0] = value;\n      this.data.next(this.active[0]);\n    }\n    this.doEvent('selected', value);\n    this.hideOptions();\n    if (this.multiple === true) {\n      this.focusToInput('');\n    } else {\n      this.focusToInput(select_pipes_1.stripTags(value.text));\n      this.element.nativeElement.querySelector('.ui-select-container').focus();\n    }\n  };\n  SelectComponent.decorators = [{\n    type: core_1.Component,\n    args: [{\n      selector: 'ng-select',\n      styles: [styles],\n      providers: [{\n        provide: forms_1.NG_VALUE_ACCESSOR,\n        /* tslint:disable */\n        useExisting: core_1.forwardRef(function () {\n          return SelectComponent;\n        }),\n        /* tslint:enable */\n        multi: true\n      }, {\n        provide: forms_1.NG_VALIDATORS,\n        useExisting: core_1.forwardRef(function () {\n          return SelectComponent;\n        }),\n        multi: true\n      }],\n      template: \"\\n  <div tabindex=\\\"0\\\"\\n     *ngIf=\\\"multiple === false\\\"\\n     (keyup)=\\\"mainClick($event)\\\"\\n     [offClick]=\\\"clickedOutside\\\"\\n     class=\\\"ui-select-container dropdown open\\\">\\n    <div [ngClass]=\\\"{'ui-disabled': disabled}\\\"></div>\\n    <div class=\\\"ui-select-match\\\"\\n         *ngIf=\\\"!inputMode\\\">\\n      <span tabindex=\\\"-1\\\"\\n          class=\\\"btn btn-default btn-secondary form-control ui-select-toggle\\\"\\n          (click)=\\\"matchClick($event)\\\"\\n          style=\\\"outline: 0;\\\">\\n        <span *ngIf=\\\"active.length <= 0\\\" class=\\\"ui-select-placeholder text-muted\\\">{{placeholder}}</span>\\n        <span *ngIf=\\\"active.length > 0\\\" class=\\\"ui-select-match-text pull-left\\\"\\n              [ngClass]=\\\"{'ui-select-allow-clear': allowClear && active.length > 0}\\\"\\n              [innerHTML]=\\\"sanitize(active[0].text)\\\"></span>\\n        <i class=\\\"dropdown-toggle pull-right\\\"></i>\\n        <i class=\\\"caret pull-right\\\"></i>\\n        <a *ngIf=\\\"allowClear && active.length>0\\\" class=\\\"btn btn-xs btn-link pull-right\\\" style=\\\"margin-right: 10px; padding: 0;\\\" (click)=\\\"removeClick(active[0], $event)\\\">\\n           <i class=\\\"glyphicon glyphicon-remove\\\"></i>\\n        </a>\\n      </span>\\n    </div>\\n    <input type=\\\"text\\\" autocomplete=\\\"false\\\" tabindex=\\\"-1\\\"\\n           (keydown)=\\\"inputEvent($event)\\\"\\n           (keyup)=\\\"inputEvent($event, true)\\\"\\n           [disabled]=\\\"disabled\\\"\\n           class=\\\"form-control ui-select-search\\\"\\n           *ngIf=\\\"inputMode\\\"\\n           placeholder=\\\"{{active.length <= 0 ? placeholder : ''}}\\\">\\n     <!-- options template -->\\n     <ul *ngIf=\\\"optionsOpened && options && options.length > 0 && !firstItemHasChildren\\\"\\n          class=\\\"ui-select-choices dropdown-menu\\\" role=\\\"menu\\\">\\n        <li *ngFor=\\\"let o of options\\\" role=\\\"menuitem\\\">\\n          <div class=\\\"ui-select-choices-row\\\"\\n               [class.active]=\\\"isActive(o)\\\"\\n               (mouseenter)=\\\"selectActive(o)\\\"\\n               (click)=\\\"selectMatch(o, $event)\\\">\\n            <a href=\\\"javascript:void(0)\\\" class=\\\"dropdown-item\\\">\\n              <div [innerHtml]=\\\"sanitize(o.text | highlight:inputValue)\\\"></div>\\n            </a>\\n          </div>\\n        </li>\\n      </ul>\\n  \\n      <ul *ngIf=\\\"optionsOpened && options && options.length > 0 && firstItemHasChildren\\\"\\n          class=\\\"ui-select-choices dropdown-menu\\\" role=\\\"menu\\\">\\n        <li *ngFor=\\\"let c of options; let index=index\\\" role=\\\"menuitem\\\">\\n          <div class=\\\"divider dropdown-divider\\\" *ngIf=\\\"index > 0\\\"></div>\\n          <div class=\\\"dropdown-header\\\">{{c.text}}</div>\\n  \\n          <div *ngFor=\\\"let o of c.children\\\"\\n               class=\\\"ui-select-choices-row\\\"\\n               [class.active]=\\\"isActive(o)\\\"\\n               (mouseenter)=\\\"selectActive(o)\\\"\\n               (click)=\\\"selectMatch(o, $event)\\\"\\n               [ngClass]=\\\"{'active': isActive(o)}\\\">\\n            <a href=\\\"javascript:void(0)\\\" class=\\\"dropdown-item\\\">\\n              <div [innerHtml]=\\\"sanitize(o.text | highlight:inputValue)\\\"></div>\\n            </a>\\n          </div>\\n        </li>\\n      </ul>\\n  </div>\\n\\n  <div tabindex=\\\"0\\\"\\n     *ngIf=\\\"multiple === true\\\"\\n     (keyup)=\\\"mainClick($event)\\\"\\n     (focus)=\\\"focusToInput('')\\\"\\n     [offClick]=\\\"clickedOutside\\\"\\n     class=\\\"ui-select-container ui-select-multiple dropdown form-control open\\\">\\n    <div [ngClass]=\\\"{'ui-disabled': disabled}\\\"></div>\\n    <span class=\\\"ui-select-match\\\">\\n        <span *ngFor=\\\"let a of active\\\">\\n            <span class=\\\"ui-select-match-item btn btn-default btn-secondary btn-xs\\\"\\n                  tabindex=\\\"-1\\\"\\n                  type=\\\"button\\\"\\n                  [ngClass]=\\\"{'btn-default': true}\\\">\\n               <a class=\\\"close\\\"\\n                  style=\\\"margin-left: 5px; padding: 0;\\\"\\n                  (click)=\\\"removeClick(a, $event)\\\">&times;</a>\\n               <span [innerHtml]=\\\"sanitize(a.text)\\\"></span>\\n           </span>\\n        </span>\\n    </span>\\n    <input type=\\\"text\\\"\\n           (keydown)=\\\"inputEvent($event)\\\"\\n           (keyup)=\\\"inputEvent($event, true)\\\"\\n           (click)=\\\"matchClick($event)\\\"\\n           [disabled]=\\\"disabled\\\"\\n           autocomplete=\\\"false\\\"\\n           autocorrect=\\\"off\\\"\\n           autocapitalize=\\\"off\\\"\\n           spellcheck=\\\"false\\\"\\n           class=\\\"form-control ui-select-search\\\"\\n           placeholder=\\\"{{active.length <= 0 ? placeholder : ''}}\\\"\\n           role=\\\"combobox\\\">\\n     <!-- options template -->\\n     <ul *ngIf=\\\"optionsOpened && options && options.length > 0 && !firstItemHasChildren\\\"\\n          class=\\\"ui-select-choices dropdown-menu\\\" role=\\\"menu\\\">\\n        <li *ngFor=\\\"let o of options\\\" role=\\\"menuitem\\\">\\n          <div class=\\\"ui-select-choices-row\\\"\\n               [class.active]=\\\"isActive(o)\\\"\\n               (mouseenter)=\\\"selectActive(o)\\\"\\n               (click)=\\\"selectMatch(o, $event)\\\">\\n            <a href=\\\"javascript:void(0)\\\" class=\\\"dropdown-item\\\">\\n              <div [innerHtml]=\\\"sanitize(o.text | highlight:inputValue)\\\"></div>\\n            </a>\\n          </div>\\n        </li>\\n      </ul>\\n  \\n      <ul *ngIf=\\\"optionsOpened && options && options.length > 0 && firstItemHasChildren\\\"\\n          class=\\\"ui-select-choices dropdown-menu\\\" role=\\\"menu\\\">\\n        <li *ngFor=\\\"let c of options; let index=index\\\" role=\\\"menuitem\\\">\\n          <div class=\\\"divider dropdown-divider\\\" *ngIf=\\\"index > 0\\\"></div>\\n          <div class=\\\"dropdown-header\\\">{{c.text}}</div>\\n  \\n          <div *ngFor=\\\"let o of c.children\\\"\\n               class=\\\"ui-select-choices-row\\\"\\n               [class.active]=\\\"isActive(o)\\\"\\n               (mouseenter)=\\\"selectActive(o)\\\"\\n               (click)=\\\"selectMatch(o, $event)\\\"\\n               [ngClass]=\\\"{'active': isActive(o)}\\\">\\n            <a href=\\\"javascript:void(0)\\\" class=\\\"dropdown-item\\\">\\n              <div [innerHtml]=\\\"sanitize(o.text | highlight:inputValue)\\\"></div>\\n            </a>\\n          </div>\\n        </li>\\n      </ul>\\n  </div>\\n  \"\n    }]\n  }];\n  /** @nocollapse */\n  SelectComponent.ctorParameters = function () {\n    return [{\n      type: core_1.ElementRef\n    }, {\n      type: platform_browser_1.DomSanitizer\n    }];\n  };\n  SelectComponent.propDecorators = {\n    'allowClear': [{\n      type: core_1.Input\n    }],\n    'placeholder': [{\n      type: core_1.Input\n    }],\n    'idField': [{\n      type: core_1.Input\n    }],\n    'textField': [{\n      type: core_1.Input\n    }],\n    'childrenField': [{\n      type: core_1.Input\n    }],\n    'multiple': [{\n      type: core_1.Input\n    }],\n    'items': [{\n      type: core_1.Input\n    }],\n    'disabled': [{\n      type: core_1.Input\n    }],\n    'active': [{\n      type: core_1.Input\n    }],\n    'data': [{\n      type: core_1.Output\n    }],\n    'selected': [{\n      type: core_1.Output\n    }],\n    'removed': [{\n      type: core_1.Output\n    }],\n    'typed': [{\n      type: core_1.Output\n    }],\n    'opened': [{\n      type: core_1.Output\n    }]\n  };\n  return SelectComponent;\n}();\nexports.SelectComponent = SelectComponent;\nvar Behavior = function () {\n  function Behavior(actor) {\n    this.optionsMap = new Map();\n    this.actor = actor;\n  }\n  Behavior.prototype.fillOptionsMap = function () {\n    var _this = this;\n    this.optionsMap.clear();\n    var startPos = 0;\n    this.actor.itemObjects.map(function (item) {\n      startPos = item.fillChildrenHash(_this.optionsMap, startPos);\n    });\n  };\n  Behavior.prototype.ensureHighlightVisible = function (optionsMap) {\n    if (optionsMap === void 0) {\n      optionsMap = void 0;\n    }\n    var container = this.actor.element.nativeElement.querySelector('.ui-select-choices-content');\n    if (!container) {\n      return;\n    }\n    var choices = container.querySelectorAll('.ui-select-choices-row');\n    if (choices.length < 1) {\n      return;\n    }\n    var activeIndex = this.getActiveIndex(optionsMap);\n    if (activeIndex < 0) {\n      return;\n    }\n    var highlighted = choices[activeIndex];\n    if (!highlighted) {\n      return;\n    }\n    var posY = highlighted.offsetTop + highlighted.clientHeight - container.scrollTop;\n    var height = container.offsetHeight;\n    if (posY > height) {\n      container.scrollTop += posY - height;\n    } else if (posY < highlighted.clientHeight) {\n      container.scrollTop -= highlighted.clientHeight - posY;\n    }\n  };\n  Behavior.prototype.getActiveIndex = function (optionsMap) {\n    if (optionsMap === void 0) {\n      optionsMap = void 0;\n    }\n    var ai = this.actor.options.indexOf(this.actor.activeOption);\n    if (ai < 0 && optionsMap !== void 0) {\n      ai = optionsMap.get(this.actor.activeOption.id);\n    }\n    return ai;\n  };\n  return Behavior;\n}();\nexports.Behavior = Behavior;\nvar GenericBehavior = function (_super) {\n  __extends(GenericBehavior, _super);\n  function GenericBehavior(actor) {\n    return _super.call(this, actor) || this;\n  }\n  GenericBehavior.prototype.first = function () {\n    this.actor.activeOption = this.actor.options[0];\n    _super.prototype.ensureHighlightVisible.call(this);\n  };\n  GenericBehavior.prototype.last = function () {\n    this.actor.activeOption = this.actor.options[this.actor.options.length - 1];\n    _super.prototype.ensureHighlightVisible.call(this);\n  };\n  GenericBehavior.prototype.prev = function () {\n    var index = this.actor.options.indexOf(this.actor.activeOption);\n    this.actor.activeOption = this.actor.options[index - 1 < 0 ? this.actor.options.length - 1 : index - 1];\n    _super.prototype.ensureHighlightVisible.call(this);\n  };\n  GenericBehavior.prototype.next = function () {\n    var index = this.actor.options.indexOf(this.actor.activeOption);\n    this.actor.activeOption = this.actor.options[index + 1 > this.actor.options.length - 1 ? 0 : index + 1];\n    _super.prototype.ensureHighlightVisible.call(this);\n  };\n  GenericBehavior.prototype.filter = function (query) {\n    var _this = this;\n    var options = this.actor.itemObjects.filter(function (option) {\n      return select_pipes_1.stripTags(option.text).match(query) && (_this.actor.multiple === false || _this.actor.multiple === true && _this.actor.active.map(function (item) {\n        return item.id;\n      }).indexOf(option.id) < 0);\n    });\n    this.actor.options = options;\n    if (this.actor.options.length > 0) {\n      this.actor.activeOption = this.actor.options[0];\n      _super.prototype.ensureHighlightVisible.call(this);\n    }\n  };\n  return GenericBehavior;\n}(Behavior);\nexports.GenericBehavior = GenericBehavior;\nvar ChildrenBehavior = function (_super) {\n  __extends(ChildrenBehavior, _super);\n  function ChildrenBehavior(actor) {\n    return _super.call(this, actor) || this;\n  }\n  ChildrenBehavior.prototype.first = function () {\n    this.actor.activeOption = this.actor.options[0].children[0];\n    this.fillOptionsMap();\n    this.ensureHighlightVisible(this.optionsMap);\n  };\n  ChildrenBehavior.prototype.last = function () {\n    this.actor.activeOption = this.actor.options[this.actor.options.length - 1].children[this.actor.options[this.actor.options.length - 1].children.length - 1];\n    this.fillOptionsMap();\n    this.ensureHighlightVisible(this.optionsMap);\n  };\n  ChildrenBehavior.prototype.prev = function () {\n    var _this = this;\n    var indexParent = this.actor.options.findIndex(function (option) {\n      return _this.actor.activeOption.parent && _this.actor.activeOption.parent.id === option.id;\n    });\n    var index = this.actor.options[indexParent].children.findIndex(function (option) {\n      return _this.actor.activeOption && _this.actor.activeOption.id === option.id;\n    });\n    this.actor.activeOption = this.actor.options[indexParent].children[index - 1];\n    if (!this.actor.activeOption) {\n      if (this.actor.options[indexParent - 1]) {\n        this.actor.activeOption = this.actor.options[indexParent - 1].children[this.actor.options[indexParent - 1].children.length - 1];\n      }\n    }\n    if (!this.actor.activeOption) {\n      this.last();\n    }\n    this.fillOptionsMap();\n    this.ensureHighlightVisible(this.optionsMap);\n  };\n  ChildrenBehavior.prototype.next = function () {\n    var _this = this;\n    var indexParent = this.actor.options.findIndex(function (option) {\n      return _this.actor.activeOption.parent && _this.actor.activeOption.parent.id === option.id;\n    });\n    var index = this.actor.options[indexParent].children.findIndex(function (option) {\n      return _this.actor.activeOption && _this.actor.activeOption.id === option.id;\n    });\n    this.actor.activeOption = this.actor.options[indexParent].children[index + 1];\n    if (!this.actor.activeOption) {\n      if (this.actor.options[indexParent + 1]) {\n        this.actor.activeOption = this.actor.options[indexParent + 1].children[0];\n      }\n    }\n    if (!this.actor.activeOption) {\n      this.first();\n    }\n    this.fillOptionsMap();\n    this.ensureHighlightVisible(this.optionsMap);\n  };\n  ChildrenBehavior.prototype.filter = function (query) {\n    var options = [];\n    var optionsMap = new Map();\n    var startPos = 0;\n    for (var _i = 0, _a = this.actor.itemObjects; _i < _a.length; _i++) {\n      var si = _a[_i];\n      var children = si.children.filter(function (option) {\n        return query.test(option.text);\n      });\n      startPos = si.fillChildrenHash(optionsMap, startPos);\n      if (children.length > 0) {\n        var newSi = si.getSimilar();\n        newSi.children = children;\n        options.push(newSi);\n      }\n    }\n    this.actor.options = options;\n    if (this.actor.options.length > 0) {\n      this.actor.activeOption = this.actor.options[0].children[0];\n      _super.prototype.ensureHighlightVisible.call(this, optionsMap);\n    }\n  };\n  return ChildrenBehavior;\n}(Behavior);\nexports.ChildrenBehavior = ChildrenBehavior;", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}