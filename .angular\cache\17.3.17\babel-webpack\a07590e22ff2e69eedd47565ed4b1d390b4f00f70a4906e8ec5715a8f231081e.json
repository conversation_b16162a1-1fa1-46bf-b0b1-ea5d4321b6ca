{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.ignoreElements = void 0;\nvar lift_1 = require(\"../util/lift\");\nvar OperatorSubscriber_1 = require(\"./OperatorSubscriber\");\nvar noop_1 = require(\"../util/noop\");\nfunction ignoreElements() {\n  return lift_1.operate(function (source, subscriber) {\n    source.subscribe(OperatorSubscriber_1.createOperatorSubscriber(subscriber, noop_1.noop));\n  });\n}\nexports.ignoreElements = ignoreElements;\n//# sourceMappingURL=ignoreElements.js.map", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}