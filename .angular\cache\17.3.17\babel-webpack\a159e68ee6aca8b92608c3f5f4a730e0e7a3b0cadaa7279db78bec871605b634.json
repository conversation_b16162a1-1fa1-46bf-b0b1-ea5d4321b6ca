{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.TimeInterval = exports.timeInterval = void 0;\nvar async_1 = require(\"../scheduler/async\");\nvar lift_1 = require(\"../util/lift\");\nvar OperatorSubscriber_1 = require(\"./OperatorSubscriber\");\nfunction timeInterval(scheduler) {\n  if (scheduler === void 0) {\n    scheduler = async_1.asyncScheduler;\n  }\n  return lift_1.operate(function (source, subscriber) {\n    var last = scheduler.now();\n    source.subscribe(OperatorSubscriber_1.createOperatorSubscriber(subscriber, function (value) {\n      var now = scheduler.now();\n      var interval = now - last;\n      last = now;\n      subscriber.next(new TimeInterval(value, interval));\n    }));\n  });\n}\nexports.timeInterval = timeInterval;\nvar TimeInterval = function () {\n  function TimeInterval(value, interval) {\n    this.value = value;\n    this.interval = interval;\n  }\n  return TimeInterval;\n}();\nexports.TimeInterval = TimeInterval;\n//# sourceMappingURL=timeInterval.js.map", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}