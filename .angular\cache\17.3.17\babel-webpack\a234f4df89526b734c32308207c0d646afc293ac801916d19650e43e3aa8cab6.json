{"ast": null, "code": "import { innerFrom } from '../observable/innerFrom';\nimport { Subject } from '../Subject';\nimport { SafeSubscriber } from '../Subscriber';\nimport { operate } from '../util/lift';\nexport function share(options = {}) {\n  const {\n    connector = () => new Subject(),\n    resetOnError = true,\n    resetOnComplete = true,\n    resetOnRefCountZero = true\n  } = options;\n  return wrapperSource => {\n    let connection;\n    let resetConnection;\n    let subject;\n    let refCount = 0;\n    let hasCompleted = false;\n    let hasErrored = false;\n    const cancelReset = () => {\n      resetConnection === null || resetConnection === void 0 ? void 0 : resetConnection.unsubscribe();\n      resetConnection = undefined;\n    };\n    const reset = () => {\n      cancelReset();\n      connection = subject = undefined;\n      hasCompleted = hasErrored = false;\n    };\n    const resetAndUnsubscribe = () => {\n      const conn = connection;\n      reset();\n      conn === null || conn === void 0 ? void 0 : conn.unsubscribe();\n    };\n    return operate((source, subscriber) => {\n      refCount++;\n      if (!hasErrored && !hasCompleted) {\n        cancelReset();\n      }\n      const dest = subject = subject !== null && subject !== void 0 ? subject : connector();\n      subscriber.add(() => {\n        refCount--;\n        if (refCount === 0 && !hasErrored && !hasCompleted) {\n          resetConnection = handleReset(resetAndUnsubscribe, resetOnRefCountZero);\n        }\n      });\n      dest.subscribe(subscriber);\n      if (!connection && refCount > 0) {\n        connection = new SafeSubscriber({\n          next: value => dest.next(value),\n          error: err => {\n            hasErrored = true;\n            cancelReset();\n            resetConnection = handleReset(reset, resetOnError, err);\n            dest.error(err);\n          },\n          complete: () => {\n            hasCompleted = true;\n            cancelReset();\n            resetConnection = handleReset(reset, resetOnComplete);\n            dest.complete();\n          }\n        });\n        innerFrom(source).subscribe(connection);\n      }\n    })(wrapperSource);\n  };\n}\nfunction handleReset(reset, on, ...args) {\n  if (on === true) {\n    reset();\n    return;\n  }\n  if (on === false) {\n    return;\n  }\n  const onSubscriber = new SafeSubscriber({\n    next: () => {\n      onSubscriber.unsubscribe();\n      reset();\n    }\n  });\n  return innerFrom(on(...args)).subscribe(onSubscriber);\n}\n//# sourceMappingURL=share.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}