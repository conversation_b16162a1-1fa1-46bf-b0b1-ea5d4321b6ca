{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Injectable, forwardRef, EventEmitter, Component, ChangeDetectionStrategy, ViewEncapsulation, Input, Output, NgModule } from '@angular/core';\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\nimport { BehaviorSubject } from 'rxjs';\nimport { MiniStore, MiniState } from 'ngx-bootstrap/mini-ngrx';\nimport * as i4 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nfunction TimepickerComponent_td_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\");\n    i0.ɵɵtext(1, \"\\xA0\\xA0\\xA0\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TimepickerComponent_td_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"td\")(1, \"a\", 1);\n    i0.ɵɵlistener(\"click\", function TimepickerComponent_td_7_Template_a_click_1_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.changeMinutes(ctx_r1.minuteStep));\n    });\n    i0.ɵɵelement(2, \"span\", 2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"disabled\", !ctx_r1.canIncrementMinutes || !ctx_r1.isEditable);\n  }\n}\nfunction TimepickerComponent_td_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\");\n    i0.ɵɵtext(1, \"\\xA0\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TimepickerComponent_td_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"td\")(1, \"a\", 1);\n    i0.ɵɵlistener(\"click\", function TimepickerComponent_td_9_Template_a_click_1_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.changeSeconds(ctx_r1.secondsStep));\n    });\n    i0.ɵɵelement(2, \"span\", 2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"disabled\", !ctx_r1.canIncrementSeconds || !ctx_r1.isEditable);\n  }\n}\nfunction TimepickerComponent_td_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\");\n    i0.ɵɵtext(1, \"\\xA0\\xA0\\xA0\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TimepickerComponent_td_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"td\");\n  }\n}\nfunction TimepickerComponent_td_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\");\n    i0.ɵɵtext(1, \"\\xA0:\\xA0\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TimepickerComponent_td_16_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"td\", 4)(1, \"input\", 5);\n    i0.ɵɵlistener(\"wheel\", function TimepickerComponent_td_16_Template_input_wheel_1_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      ctx_r1.prevDef($event);\n      return i0.ɵɵresetView(ctx_r1.changeMinutes(ctx_r1.minuteStep * ctx_r1.wheelSign($event), \"wheel\"));\n    })(\"keydown.ArrowUp\", function TimepickerComponent_td_16_Template_input_keydown_ArrowUp_1_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.changeMinutes(ctx_r1.minuteStep, \"key\"));\n    })(\"keydown.ArrowDown\", function TimepickerComponent_td_16_Template_input_keydown_ArrowDown_1_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.changeMinutes(-ctx_r1.minuteStep, \"key\"));\n    })(\"change\", function TimepickerComponent_td_16_Template_input_change_1_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.updateMinutes($event.target));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"has-error\", ctx_r1.invalidMinutes);\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"is-invalid\", ctx_r1.invalidMinutes);\n    i0.ɵɵproperty(\"placeholder\", ctx_r1.minutesPlaceholder)(\"readonly\", ctx_r1.readonlyInput)(\"disabled\", ctx_r1.disabled)(\"value\", ctx_r1.minutes);\n    i0.ɵɵattribute(\"aria-label\", ctx_r1.labelMinutes);\n  }\n}\nfunction TimepickerComponent_td_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\");\n    i0.ɵɵtext(1, \"\\xA0:\\xA0\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TimepickerComponent_td_18_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"td\", 4)(1, \"input\", 5);\n    i0.ɵɵlistener(\"wheel\", function TimepickerComponent_td_18_Template_input_wheel_1_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext();\n      ctx_r1.prevDef($event);\n      return i0.ɵɵresetView(ctx_r1.changeSeconds(ctx_r1.secondsStep * ctx_r1.wheelSign($event), \"wheel\"));\n    })(\"keydown.ArrowUp\", function TimepickerComponent_td_18_Template_input_keydown_ArrowUp_1_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.changeSeconds(ctx_r1.secondsStep, \"key\"));\n    })(\"keydown.ArrowDown\", function TimepickerComponent_td_18_Template_input_keydown_ArrowDown_1_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.changeSeconds(-ctx_r1.secondsStep, \"key\"));\n    })(\"change\", function TimepickerComponent_td_18_Template_input_change_1_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.updateSeconds($event.target));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"has-error\", ctx_r1.invalidSeconds);\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"is-invalid\", ctx_r1.invalidSeconds);\n    i0.ɵɵproperty(\"placeholder\", ctx_r1.secondsPlaceholder)(\"readonly\", ctx_r1.readonlyInput)(\"disabled\", ctx_r1.disabled)(\"value\", ctx_r1.seconds);\n    i0.ɵɵattribute(\"aria-label\", ctx_r1.labelSeconds);\n  }\n}\nfunction TimepickerComponent_td_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\");\n    i0.ɵɵtext(1, \"\\xA0\\xA0\\xA0\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TimepickerComponent_td_20_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"td\")(1, \"button\", 8);\n    i0.ɵɵlistener(\"click\", function TimepickerComponent_td_20_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.toggleMeridian());\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"disabled\", !ctx_r1.isEditable || !ctx_r1.canToggleMeridian);\n    i0.ɵɵproperty(\"disabled\", !ctx_r1.isEditable || !ctx_r1.canToggleMeridian);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"\", ctx_r1.meridian, \" \");\n  }\n}\nfunction TimepickerComponent_td_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\");\n    i0.ɵɵtext(1, \"\\xA0\\xA0\\xA0\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TimepickerComponent_td_26_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"td\")(1, \"a\", 1);\n    i0.ɵɵlistener(\"click\", function TimepickerComponent_td_26_Template_a_click_1_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.changeMinutes(-ctx_r1.minuteStep));\n    });\n    i0.ɵɵelement(2, \"span\", 7);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"disabled\", !ctx_r1.canDecrementMinutes || !ctx_r1.isEditable);\n  }\n}\nfunction TimepickerComponent_td_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\");\n    i0.ɵɵtext(1, \"\\xA0\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TimepickerComponent_td_28_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"td\")(1, \"a\", 1);\n    i0.ɵɵlistener(\"click\", function TimepickerComponent_td_28_Template_a_click_1_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.changeSeconds(-ctx_r1.secondsStep));\n    });\n    i0.ɵɵelement(2, \"span\", 7);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"disabled\", !ctx_r1.canDecrementSeconds || !ctx_r1.isEditable);\n  }\n}\nfunction TimepickerComponent_td_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\");\n    i0.ɵɵtext(1, \"\\xA0\\xA0\\xA0\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TimepickerComponent_td_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"td\");\n  }\n}\nlet TimepickerActions = /*#__PURE__*/(() => {\n  class TimepickerActions {\n    static {\n      this.WRITE_VALUE = '[timepicker] write value from ng model';\n    }\n    static {\n      this.CHANGE_HOURS = '[timepicker] change hours';\n    }\n    static {\n      this.CHANGE_MINUTES = '[timepicker] change minutes';\n    }\n    static {\n      this.CHANGE_SECONDS = '[timepicker] change seconds';\n    }\n    static {\n      this.SET_TIME_UNIT = '[timepicker] set time unit';\n    }\n    static {\n      this.UPDATE_CONTROLS = '[timepicker] update controls';\n    }\n    writeValue(value) {\n      return {\n        type: TimepickerActions.WRITE_VALUE,\n        payload: value\n      };\n    }\n    changeHours(event) {\n      return {\n        type: TimepickerActions.CHANGE_HOURS,\n        payload: event\n      };\n    }\n    changeMinutes(event) {\n      return {\n        type: TimepickerActions.CHANGE_MINUTES,\n        payload: event\n      };\n    }\n    changeSeconds(event) {\n      return {\n        type: TimepickerActions.CHANGE_SECONDS,\n        payload: event\n      };\n    }\n    setTime(value) {\n      return {\n        type: TimepickerActions.SET_TIME_UNIT,\n        payload: value\n      };\n    }\n    updateControls(value) {\n      return {\n        type: TimepickerActions.UPDATE_CONTROLS,\n        payload: value\n      };\n    }\n    static {\n      this.ɵfac = function TimepickerActions_Factory(t) {\n        return new (t || TimepickerActions)();\n      };\n    }\n    static {\n      this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n        token: TimepickerActions,\n        factory: TimepickerActions.ɵfac,\n        providedIn: 'platform'\n      });\n    }\n  }\n  return TimepickerActions;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nconst dex = 10;\nconst hoursPerDay = 24;\nconst hoursPerDayHalf = 12;\nconst minutesPerHour = 60;\nconst secondsPerMinute = 60;\nfunction isValidDate(value) {\n  if (!value) {\n    return false;\n  }\n  if (value instanceof Date && isNaN(value.getHours())) {\n    return false;\n  }\n  if (typeof value === 'string') {\n    return isValidDate(new Date(value));\n  }\n  return true;\n}\nfunction isValidLimit(controls, newDate) {\n  if (controls.min && newDate < controls.min) {\n    return false;\n  }\n  if (controls.max && newDate > controls.max) {\n    return false;\n  }\n  return true;\n}\nfunction toNumber(value) {\n  if (typeof value === 'undefined') {\n    return NaN;\n  }\n  if (typeof value === 'number') {\n    return value;\n  }\n  return parseInt(value, dex);\n}\nfunction isNumber(value) {\n  return !isNaN(toNumber(value));\n}\nfunction parseHours(value, isPM = false) {\n  const hour = toNumber(value);\n  if (isNaN(hour) || hour < 0 || hour > (isPM ? hoursPerDayHalf : hoursPerDay)) {\n    return NaN;\n  }\n  return hour;\n}\nfunction parseMinutes(value) {\n  const minute = toNumber(value);\n  if (isNaN(minute) || minute < 0 || minute > minutesPerHour) {\n    return NaN;\n  }\n  return minute;\n}\nfunction parseSeconds(value) {\n  const seconds = toNumber(value);\n  if (isNaN(seconds) || seconds < 0 || seconds > secondsPerMinute) {\n    return NaN;\n  }\n  return seconds;\n}\nfunction parseTime(value) {\n  if (typeof value === 'string') {\n    return new Date(value);\n  }\n  return value;\n}\nfunction changeTime(value, diff) {\n  if (!value) {\n    return changeTime(createDate(new Date(), 0, 0, 0), diff);\n  }\n  if (!diff) {\n    return value;\n  }\n  let hour = value.getHours();\n  let minutes = value.getMinutes();\n  let seconds = value.getSeconds();\n  if (diff.hour) {\n    hour = hour + toNumber(diff.hour);\n  }\n  if (diff.minute) {\n    minutes = minutes + toNumber(diff.minute);\n  }\n  if (diff.seconds) {\n    seconds = seconds + toNumber(diff.seconds);\n  }\n  return createDate(value, hour, minutes, seconds);\n}\nfunction setTime(value, opts) {\n  let hour = parseHours(opts.hour);\n  const minute = parseMinutes(opts.minute);\n  const seconds = parseSeconds(opts.seconds) || 0;\n  if (opts.isPM && hour !== 12) {\n    hour += hoursPerDayHalf;\n  }\n  if (!value) {\n    if (!isNaN(hour) && !isNaN(minute)) {\n      return createDate(new Date(), hour, minute, seconds);\n    }\n    return value;\n  }\n  if (isNaN(hour) || isNaN(minute)) {\n    return value;\n  }\n  return createDate(value, hour, minute, seconds);\n}\nfunction createDate(value, hours, minutes, seconds) {\n  const newValue = new Date(value.getFullYear(), value.getMonth(), value.getDate(), hours, minutes, seconds, value.getMilliseconds());\n  // #3139 ensure date part remains unchanged\n  newValue.setFullYear(value.getFullYear());\n  newValue.setMonth(value.getMonth());\n  newValue.setDate(value.getDate());\n  return newValue;\n}\nfunction padNumber(value) {\n  const _value = value.toString();\n  if (_value.length > 1) {\n    return _value;\n  }\n  return `0${_value}`;\n}\nfunction isHourInputValid(hours, isPM) {\n  return !isNaN(parseHours(hours, isPM));\n}\nfunction isMinuteInputValid(minutes) {\n  return !isNaN(parseMinutes(minutes));\n}\nfunction isSecondInputValid(seconds) {\n  return !isNaN(parseSeconds(seconds));\n}\nfunction isInputLimitValid(diff, max, min) {\n  const newDate = setTime(new Date(), diff);\n  if (!newDate) {\n    return false;\n  }\n  if (max && newDate > max) {\n    return false;\n  }\n  if (min && newDate < min) {\n    return false;\n  }\n  return true;\n}\nfunction isOneOfDatesEmpty(hours, minutes, seconds) {\n  return hours.length === 0 || minutes.length === 0 || seconds.length === 0;\n}\nfunction isInputValid(hours, minutes = '0', seconds = '0', isPM) {\n  return isHourInputValid(hours, isPM) && isMinuteInputValid(minutes) && isSecondInputValid(seconds);\n}\nfunction canChangeValue(state, event) {\n  if (state.readonlyInput || state.disabled) {\n    return false;\n  }\n  if (event) {\n    if (event.source === 'wheel' && !state.mousewheel) {\n      return false;\n    }\n    if (event.source === 'key' && !state.arrowkeys) {\n      return false;\n    }\n  }\n  return true;\n}\nfunction canChangeHours(event, controls) {\n  if (!event.step) {\n    return false;\n  }\n  if (event.step > 0 && !controls.canIncrementHours) {\n    return false;\n  }\n  if (event.step < 0 && !controls.canDecrementHours) {\n    return false;\n  }\n  return true;\n}\nfunction canChangeMinutes(event, controls) {\n  if (!event.step) {\n    return false;\n  }\n  if (event.step > 0 && !controls.canIncrementMinutes) {\n    return false;\n  }\n  if (event.step < 0 && !controls.canDecrementMinutes) {\n    return false;\n  }\n  return true;\n}\nfunction canChangeSeconds(event, controls) {\n  if (!event.step) {\n    return false;\n  }\n  if (event.step > 0 && !controls.canIncrementSeconds) {\n    return false;\n  }\n  if (event.step < 0 && !controls.canDecrementSeconds) {\n    return false;\n  }\n  return true;\n}\nfunction getControlsValue(state) {\n  const {\n    hourStep,\n    minuteStep,\n    secondsStep,\n    readonlyInput,\n    disabled,\n    mousewheel,\n    arrowkeys,\n    showSpinners,\n    showMeridian,\n    showSeconds,\n    meridians,\n    min,\n    max\n  } = state;\n  return {\n    hourStep,\n    minuteStep,\n    secondsStep,\n    readonlyInput,\n    disabled,\n    mousewheel,\n    arrowkeys,\n    showSpinners,\n    showMeridian,\n    showSeconds,\n    meridians,\n    min,\n    max\n  };\n}\nfunction timepickerControls(value, state) {\n  const hoursPerDay = 24;\n  const hoursPerDayHalf = 12;\n  const {\n    min,\n    max,\n    hourStep,\n    minuteStep,\n    secondsStep,\n    showSeconds\n  } = state;\n  const res = {\n    canIncrementHours: true,\n    canIncrementMinutes: true,\n    canIncrementSeconds: true,\n    canDecrementHours: true,\n    canDecrementMinutes: true,\n    canDecrementSeconds: true,\n    canToggleMeridian: true\n  };\n  if (!value) {\n    return res;\n  }\n  // compare dates\n  if (max) {\n    const _newHour = changeTime(value, {\n      hour: hourStep\n    });\n    res.canIncrementHours = max > _newHour && value.getHours() + hourStep < hoursPerDay;\n    if (!res.canIncrementHours) {\n      const _newMinutes = changeTime(value, {\n        minute: minuteStep\n      });\n      res.canIncrementMinutes = showSeconds ? max > _newMinutes : max >= _newMinutes;\n    }\n    if (!res.canIncrementMinutes) {\n      const _newSeconds = changeTime(value, {\n        seconds: secondsStep\n      });\n      res.canIncrementSeconds = max >= _newSeconds;\n    }\n    if (value.getHours() < hoursPerDayHalf) {\n      res.canToggleMeridian = changeTime(value, {\n        hour: hoursPerDayHalf\n      }) < max;\n    }\n  }\n  if (min) {\n    const _newHour = changeTime(value, {\n      hour: -hourStep\n    });\n    res.canDecrementHours = min < _newHour;\n    if (!res.canDecrementHours) {\n      const _newMinutes = changeTime(value, {\n        minute: -minuteStep\n      });\n      res.canDecrementMinutes = showSeconds ? min < _newMinutes : min <= _newMinutes;\n    }\n    if (!res.canDecrementMinutes) {\n      const _newSeconds = changeTime(value, {\n        seconds: -secondsStep\n      });\n      res.canDecrementSeconds = min <= _newSeconds;\n    }\n    if (value.getHours() >= hoursPerDayHalf) {\n      res.canToggleMeridian = changeTime(value, {\n        hour: -hoursPerDayHalf\n      }) > min;\n    }\n  }\n  return res;\n}\n\n/** Provides default configuration values for timepicker */\nlet TimepickerConfig = /*#__PURE__*/(() => {\n  class TimepickerConfig {\n    constructor() {\n      /** hours change step */\n      this.hourStep = 1;\n      /** minutes change step */\n      this.minuteStep = 5;\n      /** seconds changes step */\n      this.secondsStep = 10;\n      /** if true works in 12H mode and displays AM/PM. If false works in 24H mode and hides AM/PM */\n      this.showMeridian = true;\n      /** meridian labels based on locale */\n      this.meridians = ['AM', 'PM'];\n      /** if true hours and minutes fields will be readonly */\n      this.readonlyInput = false;\n      /** if true hours and minutes fields will be disabled */\n      this.disabled = false;\n      /** if true emptyTime is not marked as invalid */\n      this.allowEmptyTime = false;\n      /** if true scroll inside hours and minutes inputs will change time */\n      this.mousewheel = true;\n      /** if true the values of hours and minutes can be changed using the up/down arrow keys on the keyboard */\n      this.arrowkeys = true;\n      /** if true spinner arrows above and below the inputs will be shown */\n      this.showSpinners = true;\n      /** show seconds in timepicker */\n      this.showSeconds = false;\n      /** show minutes in timepicker */\n      this.showMinutes = true;\n      /** placeholder for hours field in timepicker */\n      this.hoursPlaceholder = 'HH';\n      /** placeholder for minutes field in timepicker */\n      this.minutesPlaceholder = 'MM';\n      /** placeholder for seconds field in timepicker */\n      this.secondsPlaceholder = 'SS';\n      /** hours aria label */\n      this.ariaLabelHours = 'hours';\n      /** minutes aria label */\n      this.ariaLabelMinutes = 'minutes';\n      /** seconds aria label */\n      this.ariaLabelSeconds = 'seconds';\n    }\n    static {\n      this.ɵfac = function TimepickerConfig_Factory(t) {\n        return new (t || TimepickerConfig)();\n      };\n    }\n    static {\n      this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n        token: TimepickerConfig,\n        factory: TimepickerConfig.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return TimepickerConfig;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nconst initialState = {\n  value: void 0,\n  config: new TimepickerConfig(),\n  controls: {\n    canIncrementHours: true,\n    canIncrementMinutes: true,\n    canIncrementSeconds: true,\n    canDecrementHours: true,\n    canDecrementMinutes: true,\n    canDecrementSeconds: true,\n    canToggleMeridian: true\n  }\n};\nfunction timepickerReducer(state = initialState, action) {\n  switch (action.type) {\n    case TimepickerActions.WRITE_VALUE:\n      {\n        return Object.assign({}, state, {\n          value: action.payload\n        });\n      }\n    case TimepickerActions.CHANGE_HOURS:\n      {\n        if (!canChangeValue(state.config, action.payload) || !canChangeHours(action.payload, state.controls)) {\n          return state;\n        }\n        const _newTime = changeTime(state.value, {\n          hour: action.payload.step\n        });\n        if ((state.config.max || state.config.min) && !isValidLimit(state.config, _newTime)) {\n          return state;\n        }\n        return Object.assign({}, state, {\n          value: _newTime\n        });\n      }\n    case TimepickerActions.CHANGE_MINUTES:\n      {\n        if (!canChangeValue(state.config, action.payload) || !canChangeMinutes(action.payload, state.controls)) {\n          return state;\n        }\n        const _newTime = changeTime(state.value, {\n          minute: action.payload.step\n        });\n        if ((state.config.max || state.config.min) && !isValidLimit(state.config, _newTime)) {\n          return state;\n        }\n        return Object.assign({}, state, {\n          value: _newTime\n        });\n      }\n    case TimepickerActions.CHANGE_SECONDS:\n      {\n        if (!canChangeValue(state.config, action.payload) || !canChangeSeconds(action.payload, state.controls)) {\n          return state;\n        }\n        const _newTime = changeTime(state.value, {\n          seconds: action.payload.step\n        });\n        if ((state.config.max || state.config.min) && !isValidLimit(state.config, _newTime)) {\n          return state;\n        }\n        return Object.assign({}, state, {\n          value: _newTime\n        });\n      }\n    case TimepickerActions.SET_TIME_UNIT:\n      {\n        if (!canChangeValue(state.config)) {\n          return state;\n        }\n        const _newTime = setTime(state.value, action.payload);\n        return Object.assign({}, state, {\n          value: _newTime\n        });\n      }\n    case TimepickerActions.UPDATE_CONTROLS:\n      {\n        const _newControlsState = timepickerControls(state.value, action.payload);\n        const _newState = {\n          value: state.value,\n          config: action.payload,\n          controls: _newControlsState\n        };\n        if (state.config.showMeridian !== _newState.config.showMeridian) {\n          if (state.value) {\n            _newState.value = new Date(state.value);\n          }\n        }\n        return Object.assign({}, state, _newState);\n      }\n    default:\n      return state;\n  }\n}\nlet TimepickerStore = /*#__PURE__*/(() => {\n  class TimepickerStore extends MiniStore {\n    constructor() {\n      const _dispatcher = new BehaviorSubject({\n        type: '[mini-ngrx] dispatcher init'\n      });\n      const state = new MiniState(initialState, _dispatcher, timepickerReducer);\n      super(_dispatcher, timepickerReducer, state);\n    }\n    static {\n      this.ɵfac = function TimepickerStore_Factory(t) {\n        return new (t || TimepickerStore)();\n      };\n    }\n    static {\n      this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n        token: TimepickerStore,\n        factory: TimepickerStore.ɵfac,\n        providedIn: 'platform'\n      });\n    }\n  }\n  return TimepickerStore;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nconst TIMEPICKER_CONTROL_VALUE_ACCESSOR = {\n  provide: NG_VALUE_ACCESSOR,\n  useExisting: forwardRef(() => TimepickerComponent),\n  multi: true\n};\nlet TimepickerComponent = /*#__PURE__*/(() => {\n  class TimepickerComponent {\n    constructor(_config, _cd, _store, _timepickerActions) {\n      this._cd = _cd;\n      this._store = _store;\n      this._timepickerActions = _timepickerActions;\n      /** hours change step */\n      this.hourStep = 1;\n      /** minutes change step */\n      this.minuteStep = 5;\n      /** seconds change step */\n      this.secondsStep = 10;\n      /** if true hours and minutes fields will be readonly */\n      this.readonlyInput = false;\n      /** if true hours and minutes fields will be disabled */\n      this.disabled = false;\n      /** if true scroll inside hours and minutes inputs will change time */\n      this.mousewheel = true;\n      /** if true the values of hours and minutes can be changed using the up/down arrow keys on the keyboard */\n      this.arrowkeys = true;\n      /** if true spinner arrows above and below the inputs will be shown */\n      this.showSpinners = true;\n      /** if true meridian button will be shown */\n      this.showMeridian = true;\n      /** show minutes in timepicker */\n      this.showMinutes = true;\n      /** show seconds in timepicker */\n      this.showSeconds = false;\n      /** meridian labels based on locale */\n      this.meridians = ['AM', 'PM'];\n      /** placeholder for hours field in timepicker */\n      this.hoursPlaceholder = 'HH';\n      /** placeholder for minutes field in timepicker */\n      this.minutesPlaceholder = 'MM';\n      /** placeholder for seconds field in timepicker */\n      this.secondsPlaceholder = 'SS';\n      /** emits true if value is a valid date */\n      this.isValid = new EventEmitter();\n      /** emits value of meridian*/\n      this.meridianChange = new EventEmitter();\n      // ui variables\n      this.hours = '';\n      this.minutes = '';\n      this.seconds = '';\n      this.meridian = '';\n      // min\\max validation for input fields\n      this.invalidHours = false;\n      this.invalidMinutes = false;\n      this.invalidSeconds = false;\n      // aria-label variables\n      this.labelHours = 'hours';\n      this.labelMinutes = 'minutes';\n      this.labelSeconds = 'seconds';\n      // time picker controls state\n      this.canIncrementHours = true;\n      this.canIncrementMinutes = true;\n      this.canIncrementSeconds = true;\n      this.canDecrementHours = true;\n      this.canDecrementMinutes = true;\n      this.canDecrementSeconds = true;\n      this.canToggleMeridian = true;\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      this.onChange = Function.prototype;\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      this.onTouched = Function.prototype;\n      this.config = _config;\n      Object.assign(this, this.config);\n      this.timepickerSub = _store.select(state => state.value).subscribe(value => {\n        // update UI values if date changed\n        this._renderTime(value);\n        this.onChange(value);\n        this._store.dispatch(this._timepickerActions.updateControls(getControlsValue(this)));\n      });\n      _store.select(state => state.controls).subscribe(controlsState => {\n        const isTimepickerInputValid = isInputValid(this.hours, this.minutes, this.seconds, this.isPM());\n        const isValid = this.config.allowEmptyTime ? this.isOneOfDatesIsEmpty() || isTimepickerInputValid : isTimepickerInputValid;\n        this.isValid.emit(isValid);\n        Object.assign(this, controlsState);\n        _cd.markForCheck();\n      });\n    }\n    /** @deprecated - please use `isEditable` instead */\n    get isSpinnersVisible() {\n      return this.showSpinners && !this.readonlyInput;\n    }\n    get isEditable() {\n      return !(this.readonlyInput || this.disabled);\n    }\n    resetValidation() {\n      this.invalidHours = false;\n      this.invalidMinutes = false;\n      this.invalidSeconds = false;\n    }\n    isPM() {\n      return this.showMeridian && this.meridian === this.meridians[1];\n    }\n    prevDef($event) {\n      $event.preventDefault();\n    }\n    wheelSign($event) {\n      return Math.sign($event.deltaY || 0) * -1;\n    }\n    ngOnChanges() {\n      this._store.dispatch(this._timepickerActions.updateControls(getControlsValue(this)));\n    }\n    changeHours(step, source = '') {\n      this.resetValidation();\n      this._store.dispatch(this._timepickerActions.changeHours({\n        step,\n        source\n      }));\n    }\n    changeMinutes(step, source = '') {\n      this.resetValidation();\n      this._store.dispatch(this._timepickerActions.changeMinutes({\n        step,\n        source\n      }));\n    }\n    changeSeconds(step, source = '') {\n      this.resetValidation();\n      this._store.dispatch(this._timepickerActions.changeSeconds({\n        step,\n        source\n      }));\n    }\n    updateHours(target) {\n      this.resetValidation();\n      this.hours = target.value;\n      const isTimepickerInputValid = isHourInputValid(this.hours, this.isPM()) && this.isValidLimit();\n      const isValid = this.config.allowEmptyTime ? this.isOneOfDatesIsEmpty() || isTimepickerInputValid : isTimepickerInputValid;\n      if (!isValid) {\n        this.invalidHours = true;\n        this.isValid.emit(false);\n        this.onChange(null);\n        return;\n      }\n      this._updateTime();\n    }\n    updateMinutes(target) {\n      this.resetValidation();\n      this.minutes = target.value;\n      const isTimepickerInputValid = isMinuteInputValid(this.minutes) && this.isValidLimit();\n      const isValid = this.config.allowEmptyTime ? this.isOneOfDatesIsEmpty() || isTimepickerInputValid : isTimepickerInputValid;\n      if (!isValid) {\n        this.invalidMinutes = true;\n        this.isValid.emit(false);\n        this.onChange(null);\n        return;\n      }\n      this._updateTime();\n    }\n    updateSeconds(target) {\n      this.resetValidation();\n      this.seconds = target.value;\n      const isTimepickerInputValid = isSecondInputValid(this.seconds) && this.isValidLimit();\n      const isValid = this.config.allowEmptyTime ? this.isOneOfDatesIsEmpty() || isTimepickerInputValid : isTimepickerInputValid;\n      if (!isValid) {\n        this.invalidSeconds = true;\n        this.isValid.emit(false);\n        this.onChange(null);\n        return;\n      }\n      this._updateTime();\n    }\n    isValidLimit() {\n      return isInputLimitValid({\n        hour: this.hours,\n        minute: this.minutes,\n        seconds: this.seconds,\n        isPM: this.isPM()\n      }, this.max, this.min);\n    }\n    isOneOfDatesIsEmpty() {\n      return isOneOfDatesEmpty(this.hours, this.minutes, this.seconds);\n    }\n    _updateTime() {\n      const _seconds = this.showSeconds ? this.seconds : void 0;\n      const _minutes = this.showMinutes ? this.minutes : void 0;\n      const isTimepickerInputValid = isInputValid(this.hours, _minutes, _seconds, this.isPM());\n      const isValid = this.config.allowEmptyTime ? this.isOneOfDatesIsEmpty() || isTimepickerInputValid : isTimepickerInputValid;\n      if (!isValid) {\n        this.isValid.emit(false);\n        this.onChange(null);\n        return;\n      }\n      this._store.dispatch(this._timepickerActions.setTime({\n        hour: this.hours,\n        minute: this.minutes,\n        seconds: this.seconds,\n        isPM: this.isPM()\n      }));\n    }\n    toggleMeridian() {\n      if (!this.showMeridian || !this.isEditable) {\n        return;\n      }\n      const _hoursPerDayHalf = 12;\n      this._store.dispatch(this._timepickerActions.changeHours({\n        step: _hoursPerDayHalf,\n        source: ''\n      }));\n    }\n    /**\n     * Write a new value to the element.\n     */\n    writeValue(obj) {\n      if (isValidDate(obj)) {\n        this.resetValidation();\n        this._store.dispatch(this._timepickerActions.writeValue(parseTime(obj)));\n      } else if (obj == null) {\n        this._store.dispatch(this._timepickerActions.writeValue());\n      }\n    }\n    /**\n     * Set the function to be called when the control receives a change event.\n     */\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    registerOnChange(fn) {\n      this.onChange = fn;\n    }\n    /**\n     * Set the function to be called when the control receives a touch event.\n     */\n    registerOnTouched(fn) {\n      this.onTouched = fn;\n    }\n    /**\n     * This function is called when the control status changes to or from \"disabled\".\n     * Depending on the value, it will enable or disable the appropriate DOM element.\n     *\n     * @param isDisabled\n     */\n    setDisabledState(isDisabled) {\n      this.disabled = isDisabled;\n      this._cd.markForCheck();\n    }\n    ngOnDestroy() {\n      this.timepickerSub?.unsubscribe();\n    }\n    _renderTime(value) {\n      if (!value || !isValidDate(value)) {\n        this.hours = '';\n        this.minutes = '';\n        this.seconds = '';\n        this.meridian = this.meridians[0];\n        this.meridianChange.emit(this.meridian);\n        return;\n      }\n      const _value = parseTime(value);\n      if (!_value) {\n        return;\n      }\n      const _hoursPerDayHalf = 12;\n      let _hours = _value.getHours();\n      if (this.showMeridian) {\n        this.meridian = this.meridians[_hours >= _hoursPerDayHalf ? 1 : 0];\n        this.meridianChange.emit(this.meridian);\n        _hours = _hours % _hoursPerDayHalf;\n        // should be 12 PM, not 00 PM\n        if (_hours === 0) {\n          _hours = _hoursPerDayHalf;\n        }\n      }\n      this.hours = padNumber(_hours);\n      this.minutes = padNumber(_value.getMinutes());\n      this.seconds = padNumber(_value.getUTCSeconds());\n    }\n    static {\n      this.ɵfac = function TimepickerComponent_Factory(t) {\n        return new (t || TimepickerComponent)(i0.ɵɵdirectiveInject(TimepickerConfig), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(TimepickerStore), i0.ɵɵdirectiveInject(TimepickerActions));\n      };\n    }\n    static {\n      this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n        type: TimepickerComponent,\n        selectors: [[\"timepicker\"]],\n        inputs: {\n          hourStep: \"hourStep\",\n          minuteStep: \"minuteStep\",\n          secondsStep: \"secondsStep\",\n          readonlyInput: \"readonlyInput\",\n          disabled: \"disabled\",\n          mousewheel: \"mousewheel\",\n          arrowkeys: \"arrowkeys\",\n          showSpinners: \"showSpinners\",\n          showMeridian: \"showMeridian\",\n          showMinutes: \"showMinutes\",\n          showSeconds: \"showSeconds\",\n          meridians: \"meridians\",\n          min: \"min\",\n          max: \"max\",\n          hoursPlaceholder: \"hoursPlaceholder\",\n          minutesPlaceholder: \"minutesPlaceholder\",\n          secondsPlaceholder: \"secondsPlaceholder\"\n        },\n        outputs: {\n          isValid: \"isValid\",\n          meridianChange: \"meridianChange\"\n        },\n        features: [i0.ɵɵProvidersFeature([TIMEPICKER_CONTROL_VALUE_ACCESSOR, TimepickerStore]), i0.ɵɵNgOnChangesFeature],\n        decls: 31,\n        vars: 33,\n        consts: [[1, \"text-center\", 3, \"hidden\"], [\"href\", \"javascript:void(0);\", 1, \"btn\", \"btn-link\", 3, \"click\"], [1, \"bs-chevron\", \"bs-chevron-up\"], [4, \"ngIf\"], [1, \"form-group\", \"mb-3\"], [\"type\", \"text\", \"maxlength\", \"2\", 1, \"form-control\", \"text-center\", \"bs-timepicker-field\", 3, \"wheel\", \"keydown.ArrowUp\", \"keydown.ArrowDown\", \"change\", \"placeholder\", \"readonly\", \"disabled\", \"value\"], [\"class\", \"form-group mb-3\", 3, \"has-error\", 4, \"ngIf\"], [1, \"bs-chevron\", \"bs-chevron-down\"], [\"type\", \"button\", 1, \"btn\", \"btn-default\", \"text-center\", 3, \"click\", \"disabled\"]],\n        template: function TimepickerComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"table\")(1, \"tbody\")(2, \"tr\", 0)(3, \"td\")(4, \"a\", 1);\n            i0.ɵɵlistener(\"click\", function TimepickerComponent_Template_a_click_4_listener() {\n              return ctx.changeHours(ctx.hourStep);\n            });\n            i0.ɵɵelement(5, \"span\", 2);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵtemplate(6, TimepickerComponent_td_6_Template, 2, 0, \"td\", 3)(7, TimepickerComponent_td_7_Template, 3, 2, \"td\", 3)(8, TimepickerComponent_td_8_Template, 2, 0, \"td\", 3)(9, TimepickerComponent_td_9_Template, 3, 2, \"td\", 3)(10, TimepickerComponent_td_10_Template, 2, 0, \"td\", 3)(11, TimepickerComponent_td_11_Template, 1, 0, \"td\", 3);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(12, \"tr\")(13, \"td\", 4)(14, \"input\", 5);\n            i0.ɵɵlistener(\"wheel\", function TimepickerComponent_Template_input_wheel_14_listener($event) {\n              ctx.prevDef($event);\n              return ctx.changeHours(ctx.hourStep * ctx.wheelSign($event), \"wheel\");\n            })(\"keydown.ArrowUp\", function TimepickerComponent_Template_input_keydown_ArrowUp_14_listener() {\n              return ctx.changeHours(ctx.hourStep, \"key\");\n            })(\"keydown.ArrowDown\", function TimepickerComponent_Template_input_keydown_ArrowDown_14_listener() {\n              return ctx.changeHours(-ctx.hourStep, \"key\");\n            })(\"change\", function TimepickerComponent_Template_input_change_14_listener($event) {\n              return ctx.updateHours($event.target);\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵtemplate(15, TimepickerComponent_td_15_Template, 2, 0, \"td\", 3)(16, TimepickerComponent_td_16_Template, 2, 9, \"td\", 6)(17, TimepickerComponent_td_17_Template, 2, 0, \"td\", 3)(18, TimepickerComponent_td_18_Template, 2, 9, \"td\", 6)(19, TimepickerComponent_td_19_Template, 2, 0, \"td\", 3)(20, TimepickerComponent_td_20_Template, 3, 4, \"td\", 3);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(21, \"tr\", 0)(22, \"td\")(23, \"a\", 1);\n            i0.ɵɵlistener(\"click\", function TimepickerComponent_Template_a_click_23_listener() {\n              return ctx.changeHours(-ctx.hourStep);\n            });\n            i0.ɵɵelement(24, \"span\", 7);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵtemplate(25, TimepickerComponent_td_25_Template, 2, 0, \"td\", 3)(26, TimepickerComponent_td_26_Template, 3, 2, \"td\", 3)(27, TimepickerComponent_td_27_Template, 2, 0, \"td\", 3)(28, TimepickerComponent_td_28_Template, 3, 2, \"td\", 3)(29, TimepickerComponent_td_29_Template, 2, 0, \"td\", 3)(30, TimepickerComponent_td_30_Template, 1, 0, \"td\", 3);\n            i0.ɵɵelementEnd()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"hidden\", !ctx.showSpinners);\n            i0.ɵɵadvance(2);\n            i0.ɵɵclassProp(\"disabled\", !ctx.canIncrementHours || !ctx.isEditable);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngIf\", ctx.showMinutes);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.showMinutes);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.showSeconds);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.showSeconds);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.showMeridian);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.showMeridian);\n            i0.ɵɵadvance(2);\n            i0.ɵɵclassProp(\"has-error\", ctx.invalidHours);\n            i0.ɵɵadvance();\n            i0.ɵɵclassProp(\"is-invalid\", ctx.invalidHours);\n            i0.ɵɵproperty(\"placeholder\", ctx.hoursPlaceholder)(\"readonly\", ctx.readonlyInput)(\"disabled\", ctx.disabled)(\"value\", ctx.hours);\n            i0.ɵɵattribute(\"aria-label\", ctx.labelHours);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.showMinutes);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.showMinutes);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.showSeconds);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.showSeconds);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.showMeridian);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.showMeridian);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"hidden\", !ctx.showSpinners);\n            i0.ɵɵadvance(2);\n            i0.ɵɵclassProp(\"disabled\", !ctx.canDecrementHours || !ctx.isEditable);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngIf\", ctx.showMinutes);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.showMinutes);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.showSeconds);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.showSeconds);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.showMeridian);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.showMeridian);\n          }\n        },\n        dependencies: [i4.NgIf],\n        styles: [\".bs-chevron{border-style:solid;display:block;width:9px;height:9px;position:relative;border-width:3px 0px 0 3px}.bs-chevron-up{transform:rotate(45deg);top:2px}.bs-chevron-down{transform:rotate(-135deg);top:-2px}.bs-timepicker-field{width:65px;padding:.375rem .55rem}\\n\"],\n        encapsulation: 2,\n        changeDetection: 0\n      });\n    }\n  }\n  return TimepickerComponent;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet TimepickerModule = /*#__PURE__*/(() => {\n  class TimepickerModule {\n    static forRoot() {\n      return {\n        ngModule: TimepickerModule,\n        providers: [TimepickerActions, TimepickerStore]\n      };\n    }\n    static {\n      this.ɵfac = function TimepickerModule_Factory(t) {\n        return new (t || TimepickerModule)();\n      };\n    }\n    static {\n      this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n        type: TimepickerModule\n      });\n    }\n    static {\n      this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n        providers: [TimepickerStore],\n        imports: [CommonModule]\n      });\n    }\n  }\n  return TimepickerModule;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TimepickerActions, TimepickerComponent, TimepickerConfig, TimepickerModule, TimepickerStore };\n//# sourceMappingURL=ngx-bootstrap-timepicker.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}