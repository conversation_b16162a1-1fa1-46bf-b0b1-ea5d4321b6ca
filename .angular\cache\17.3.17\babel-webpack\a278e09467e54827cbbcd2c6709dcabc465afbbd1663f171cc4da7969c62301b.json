{"ast": null, "code": "import { WebapiService } from './../webapi.service';\nimport { ActivatedRoute, Router } from '../../../node_modules/@angular/router';\nimport { HttpClient } from '@angular/common/http';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../node_modules/@angular/router\";\nimport * as i2 from \"@angular/common/http\";\nimport * as i3 from \"./../webapi.service\";\nconst _c0 = () => [\"/customerlist\"];\nconst _c1 = () => [\"/productlist\"];\nconst _c2 = () => [\"/pricemaster\"];\nconst _c3 = () => [\"/sorecord\"];\nconst _c4 = () => [\"/solist1\"];\nconst _c5 = () => [\"/soreview\"];\nconst _c6 = () => [\"/salestatus\"];\nconst _c7 = () => [\"/sohistory\"];\nconst _c8 = () => [\"/salestock\"];\nconst _c9 = () => [\"/invoicecashlist\"];\nconst _c10 = () => [\"/invoicecreditlist\"];\nconst _c11 = () => [\"/completeinvoice\"];\nconst _c12 = () => [\"/masterpackage\"];\nconst _c13 = () => [\"/productgroup\"];\nconst _c14 = () => [\"/coverpage\"];\nconst _c15 = () => [\"/users\"];\nconst _c16 = () => [\"/permission\"];\nfunction TopmenuComponent_li_10_a_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 30);\n    i0.ɵɵtext(1, \"Customer\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(1, _c0));\n  }\n}\nfunction TopmenuComponent_li_10_a_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 30);\n    i0.ɵɵtext(1, \"Product List\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(1, _c1));\n  }\n}\nfunction TopmenuComponent_li_10_a_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 30);\n    i0.ɵɵtext(1, \"Price Master\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(1, _c2));\n  }\n}\nfunction TopmenuComponent_li_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 9)(1, \"a\", 27);\n    i0.ɵɵtext(2, \" Master Data \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 28);\n    i0.ɵɵtemplate(4, TopmenuComponent_li_10_a_4_Template, 2, 2, \"a\", 29)(5, TopmenuComponent_li_10_a_5_Template, 2, 2, \"a\", 29)(6, TopmenuComponent_li_10_a_6_Template, 2, 2, \"a\", 29);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.flag[0].flag_view);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.flag[1].flag_view);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.flag[2].flag_view);\n  }\n}\nfunction TopmenuComponent_li_11_a_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 30);\n    i0.ɵɵtext(1, \"Sale Order\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(1, _c3));\n  }\n}\nfunction TopmenuComponent_li_11_a_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 30);\n    i0.ɵɵtext(1, \"Sale Order List\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(1, _c4));\n  }\n}\nfunction TopmenuComponent_li_11_a_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 30);\n    i0.ɵɵtext(1, \"Sale Order Review\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(1, _c5));\n  }\n}\nfunction TopmenuComponent_li_11_a_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 30);\n    i0.ɵɵtext(1, \"Sales Order Status\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(1, _c6));\n  }\n}\nfunction TopmenuComponent_li_11_a_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 30);\n    i0.ɵɵtext(1, \"Sale Order History\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(1, _c7));\n  }\n}\nfunction TopmenuComponent_li_11_a_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 30);\n    i0.ɵɵtext(1, \"Sale Stock\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(1, _c8));\n  }\n}\nfunction TopmenuComponent_li_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 9)(1, \"a\", 27);\n    i0.ɵɵtext(2, \" Sale Order \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 28);\n    i0.ɵɵtemplate(4, TopmenuComponent_li_11_a_4_Template, 2, 2, \"a\", 29)(5, TopmenuComponent_li_11_a_5_Template, 2, 2, \"a\", 29)(6, TopmenuComponent_li_11_a_6_Template, 2, 2, \"a\", 29);\n    i0.ɵɵelement(7, \"div\", 31);\n    i0.ɵɵtemplate(8, TopmenuComponent_li_11_a_8_Template, 2, 2, \"a\", 29)(9, TopmenuComponent_li_11_a_9_Template, 2, 2, \"a\", 29)(10, TopmenuComponent_li_11_a_10_Template, 2, 2, \"a\", 29);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.flag[3].flag_view);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.flag[4].flag_view);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.flag[5].flag_view);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.flag[7].flag_view);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.flag[6].flag_view);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.flag[8].flag_view);\n  }\n}\nfunction TopmenuComponent_li_12_a_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 30);\n    i0.ɵɵtext(1, \"Cash Invoice List\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(1, _c9));\n  }\n}\nfunction TopmenuComponent_li_12_a_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 30);\n    i0.ɵɵtext(1, \"Credit Invoice List\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(1, _c10));\n  }\n}\nfunction TopmenuComponent_li_12_a_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 30);\n    i0.ɵɵtext(1, \"Completed Invoice List\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(1, _c11));\n  }\n}\nfunction TopmenuComponent_li_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 9)(1, \"a\", 27);\n    i0.ɵɵtext(2, \" Invoice \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 28);\n    i0.ɵɵtemplate(4, TopmenuComponent_li_12_a_4_Template, 2, 2, \"a\", 29)(5, TopmenuComponent_li_12_a_5_Template, 2, 2, \"a\", 29);\n    i0.ɵɵelement(6, \"div\", 31);\n    i0.ɵɵtemplate(7, TopmenuComponent_li_12_a_7_Template, 2, 2, \"a\", 29);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.flag[9].flag_view);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.flag[10].flag_view);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.flag[11].flag_view);\n  }\n}\nfunction TopmenuComponent_li_13_a_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 30);\n    i0.ɵɵtext(1, \"Master Package\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(1, _c12));\n  }\n}\nfunction TopmenuComponent_li_13_a_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 33);\n    i0.ɵɵtext(1, \"Grouping Product\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(1, _c13));\n  }\n}\nfunction TopmenuComponent_li_13_a_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 30);\n    i0.ɵɵtext(1, \"Cover Page\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(1, _c14));\n  }\n}\nfunction TopmenuComponent_li_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 9)(1, \"a\", 27);\n    i0.ɵɵtext(2, \" Transport Info \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 28);\n    i0.ɵɵtemplate(4, TopmenuComponent_li_13_a_4_Template, 2, 2, \"a\", 29)(5, TopmenuComponent_li_13_a_5_Template, 2, 2, \"a\", 32);\n    i0.ɵɵelement(6, \"div\", 31);\n    i0.ɵɵtemplate(7, TopmenuComponent_li_13_a_7_Template, 2, 2, \"a\", 29);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.flag[12].flag_view);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.flag[13].flag_view);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.flag[14].flag_view);\n  }\n}\nfunction TopmenuComponent_li_14_a_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 30);\n    i0.ɵɵtext(1, \"User Accounts\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(1, _c15));\n  }\n}\nfunction TopmenuComponent_li_14_a_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 30);\n    i0.ɵɵtext(1, \"Permission Setting\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(1, _c16));\n  }\n}\nfunction TopmenuComponent_li_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 9)(1, \"a\", 27);\n    i0.ɵɵtext(2, \" Security Control \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 28);\n    i0.ɵɵtemplate(4, TopmenuComponent_li_14_a_4_Template, 2, 2, \"a\", 29)(5, TopmenuComponent_li_14_a_5_Template, 2, 2, \"a\", 29);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.flag[15].flag_view);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.flag[16].flag_view);\n  }\n}\nexport let TopmenuComponent = /*#__PURE__*/(() => {\n  class TopmenuComponent {\n    constructor(route, rout, http, servive) {\n      this.route = route;\n      this.rout = rout;\n      this.http = http;\n      this.servive = servive;\n      this.mdlSampleIsOpen = false;\n      this.alt = '';\n      this.checkreload = true;\n      this.menulit = [];\n      this.flag = [];\n      this.Master = true;\n      this.Sale = true;\n      this.Invoice = true;\n      this.Transport = true;\n      this.Report = true;\n      this.Security = true;\n      this.menulog = [];\n      this.nameUrl = 'assets/img/default-image.png';\n      this.CKuser = '';\n      this.url = this.servive.geturlservice();\n      this.getsessionStorage();\n      this.setpermissionmenu(this.memu);\n      /*if(this.Name !=[]){\n        alert('1')\n        if(this.Name==[]){\n          this.http.get<any>(this.url + 'find_permission/' + this.Name[0].id_group_user).subscribe((res: menulist[]) => {\n            if(res.length>0) {\n              this.setpermissionmenu(res);\n              \n            }\n          \n              },error =>{\n             \n                console.log(error);\n              })\n          } else {\n            alert('2')\n       this.setpermissionmenu(this.memu);\n          \n          }\n      }else{\n        this.rout.navigate(['login']);\n      } */\n    }\n    getPDF() {}\n    getsessionStorage() {\n      if (JSON.parse(sessionStorage.getItem('login')) == null || JSON.parse(sessionStorage.getItem('menu')) == null) {\n        this.rout.navigate(['login']);\n        sessionStorage.clear();\n      } else {\n        this.Name = JSON.parse(sessionStorage.getItem('login'));\n        // this.Name=servive.setuserlogin();\n        //alert(JSON.stringify(this.Name[0].id_group_user));\n        this.memu = JSON.parse(sessionStorage.getItem('menu'));\n        this.CKuser = sessionStorage.getItem('salegroup');\n        this.CKusrt(this.CKuser);\n      }\n    }\n    CKusrt(user) {\n      if (user == null) {\n        this.nameUrl = 'assets/img/ระบบการสั่งซื้อสินค้าออนไลน์(Customer).pdf';\n      } else {\n        this.nameUrl = 'assets/img/ระบบการสั่งซื้อสินค้าออนไลน์(Admin).pdf';\n      }\n    }\n    logoutbtn() {\n      sessionStorage.clear();\n      localStorage.clear();\n      this.servive.getuserlogin(undefined);\n      this.servive.getmenulogin(undefined);\n      this.servive.getalert(false);\n      this.rout.navigate(['login']);\n      sessionStorage.removeItem('cashDateTo');\n      sessionStorage.removeItem('cashDateFrom');\n      sessionStorage.removeItem('cecreditDateFrom');\n      sessionStorage.removeItem('cecreditDateFrom');\n    }\n    clicklogout() {\n      this.openModal(true, 'ออกจากระบบเสร็จสิ้น', true);\n    }\n    ngOnInit() {}\n    setpermissionmenu(value) {\n      if (value != undefined) {\n        this.flag = value;\n        /*alert(this.flag[0].flag_view);*/\n        if (this.flag[0].flag_view == false && this.flag[1].flag_view == false && this.flag[2].flag_view == false) {\n          this.Master = false;\n        }\n        if (this.flag[3].flag_view == false && this.flag[4].flag_view == false && this.flag[5].flag_view == false && this.flag[6].flag_view == false) {\n          this.Sale = false;\n        }\n        if (this.flag[7].flag_view == false && this.flag[8].flag_view == false && this.flag[9].flag_view == false) {\n          this.Invoice = false;\n        }\n        if (this.flag[10].flag_view == false && this.flag[11].flag_view == false && this.flag[12].flag_view == false) {\n          this.Transport = false;\n        }\n        if (this.flag[13].flag_view == false && this.flag[14].flag_view == false) {\n          this.Security = false;\n        }\n      } else {\n        this.Master = false;\n        this.Sale = false;\n        this.Invoice = false;\n        this.Transport = false;\n        this.Security = false;\n        this.rout.navigate[''];\n      }\n    }\n    get alldetailuser() {\n      return this.nameuser;\n    }\n    testclick() {\n      alert('ok');\n    }\n    /*sandhome() {\n      this.router.navigate(['users']);\n    }*/\n    openModal(open, text, load) {\n      this.mdlSampleIsOpen = open;\n      this.alt = text;\n      this.checkreload = load;\n    }\n    closemodel(cl) {\n      this.mdlSampleIsOpen = false;\n      if (this.checkreload == true) {\n        this.logoutbtn();\n      }\n    }\n    static {\n      this.ɵfac = function TopmenuComponent_Factory(t) {\n        return new (t || TopmenuComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.HttpClient), i0.ɵɵdirectiveInject(i3.WebapiService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: TopmenuComponent,\n        selectors: [[\"app-topmenu\"]],\n        decls: 41,\n        vars: 7,\n        consts: [[1, \"container-fluid\"], [1, \"navbar\", \"navbar-expand-xl\", \"navbar-dark\", \"bg-success\", \"fixed-top\"], [\"type\", \"button\", \"data-toggle\", \"collapse\", \"data-target\", \"#navbarResponsive\", \"aria-controls\", \"navbarResponsive\", \"aria-expanded\", \"false\", \"aria-label\", \"Toggle navigation\", 1, \"navbar-toggler\"], [1, \"navbar-toggler-icon\"], [\"target\", \"_blank\", \"tooltip\", \"\\u0E04\\u0E39\\u0E48\\u0E21\\u0E37\\u0E2D\\u0E01\\u0E32\\u0E23\\u0E43\\u0E0A\\u0E49\\u0E07\\u0E32\\u0E19\\u0E23\\u0E30\\u0E1A\\u0E1A\", \"placement\", \"bottom\", 1, \"navbar-brand\", \"justify-content-center\", 3, \"href\"], [\"href\", \"\", 1, \"navbar-brand\", \"justify-content-center\"], [\"id\", \"navbarResponsive\", 1, \"collapse\", \"navbar-collapse\"], [1, \"navbar-nav\", \"mr-auto\"], [\"class\", \"nav-item dropdown active\", 4, \"ngIf\"], [1, \"nav-item\", \"dropdown\", \"active\"], [\"href\", \"\", \"id\", \"navbarDropdown\", \"role\", \"button\", \"aria-haspopup\", \"true\", \"aria-expanded\", \"false\", 1, \"nav-link\", 3, \"click\"], [\"id\", \"navbarDropdown\", \"role\", \"button\", \"data-toggle\", \"dropdown\", \"aria-haspopup\", \"true\", \"aria-expanded\", \"false\", 1, \"nav-link\"], [\"id\", \"exampleModal\", \"tabindex\", \"-1\", \"role\", \"dialog\", \"aria-labelledby\", \"exampleModalLabel\", \"aria-hidden\", \"true\", 1, \"modal\", \"fade\"], [\"role\", \"document\", 1, \"modal-dialog\"], [1, \"modal-content\"], [1, \"modal-header\"], [\"id\", \"exampleModalLabel\", 1, \"modal-title\"], [\"type\", \"button\", \"data-dismiss\", \"modal\", \"aria-label\", \"Close\", 1, \"close\"], [\"aria-hidden\", \"true\"], [1, \"modal-body\"], [1, \"input-group\"], [1, \"custom-file\"], [\"type\", \"file\", \"id\", \"inputGroupFile04\", 1, \"custom-file-input\"], [\"for\", \"inputGroupFile04\", 1, \"custom-file-label\"], [1, \"modal-footer\"], [\"type\", \"button\", \"data-dismiss\", \"modal\", 1, \"btn\", \"btn-secondary\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\"], [\"href\", \"#\", \"id\", \"navbarDropdown\", \"role\", \"button\", \"data-toggle\", \"dropdown\", \"aria-haspopup\", \"true\", \"aria-expanded\", \"false\", 1, \"nav-link\", \"dropdown-toggle\"], [\"aria-labelledby\", \"navbarDropdown\", 1, \"dropdown-menu\"], [\"class\", \"dropdown-item\", \"href\", \"\", 3, \"routerLink\", 4, \"ngIf\"], [\"href\", \"\", 1, \"dropdown-item\", 3, \"routerLink\"], [1, \"dropdown-divider\"], [\"class\", \"dropdown-item\", 3, \"routerLink\", 4, \"ngIf\"], [1, \"dropdown-item\", 3, \"routerLink\"]],\n        template: function TopmenuComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"nav\", 1)(2, \"button\", 2);\n            i0.ɵɵelement(3, \"span\", 3);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(4, \"a\", 4);\n            i0.ɵɵtext(5, \"Sale Order Online\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(6, \"div\");\n            i0.ɵɵelement(7, \"a\", 5);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(8, \"div\", 6)(9, \"ul\", 7);\n            i0.ɵɵtemplate(10, TopmenuComponent_li_10_Template, 7, 3, \"li\", 8)(11, TopmenuComponent_li_11_Template, 11, 6, \"li\", 8)(12, TopmenuComponent_li_12_Template, 8, 3, \"li\", 8)(13, TopmenuComponent_li_13_Template, 8, 3, \"li\", 8)(14, TopmenuComponent_li_14_Template, 6, 2, \"li\", 8);\n            i0.ɵɵelementStart(15, \"li\", 9)(16, \"a\", 10);\n            i0.ɵɵlistener(\"click\", function TopmenuComponent_Template_a_click_16_listener() {\n              return ctx.logoutbtn();\n            });\n            i0.ɵɵtext(17, \" Logout \");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(18, \"li\", 9)(19, \"a\", 11);\n            i0.ɵɵtext(20);\n            i0.ɵɵelementEnd()()()()();\n            i0.ɵɵelementStart(21, \"div\", 12)(22, \"div\", 13)(23, \"div\", 14)(24, \"div\", 15)(25, \"h5\", 16);\n            i0.ɵɵtext(26, \"Upload Invoice\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(27, \"button\", 17)(28, \"span\", 18);\n            i0.ɵɵtext(29, \"\\u00D7\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(30, \"div\", 19)(31, \"div\", 20)(32, \"div\", 21);\n            i0.ɵɵelement(33, \"input\", 22);\n            i0.ɵɵelementStart(34, \"label\", 23);\n            i0.ɵɵtext(35, \"Choose file\");\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(36, \"div\", 24)(37, \"button\", 25);\n            i0.ɵɵtext(38, \"Close\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(39, \"button\", 26);\n            i0.ɵɵtext(40, \"Upload\");\n            i0.ɵɵelementEnd()()()()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(4);\n            i0.ɵɵpropertyInterpolate(\"href\", ctx.nameUrl, i0.ɵɵsanitizeUrl);\n            i0.ɵɵadvance(6);\n            i0.ɵɵproperty(\"ngIf\", ctx.Master);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.Sale);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.Invoice);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.Transport);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.Security);\n            i0.ɵɵadvance(6);\n            i0.ɵɵtextInterpolate1(\" Welcome \", ctx.Name[0].name_user, \" \");\n          }\n        }\n      });\n    }\n  }\n  return TopmenuComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}