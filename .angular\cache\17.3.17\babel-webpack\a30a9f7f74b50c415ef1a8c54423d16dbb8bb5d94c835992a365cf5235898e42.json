{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.catchError = void 0;\nvar innerFrom_1 = require(\"../observable/innerFrom\");\nvar OperatorSubscriber_1 = require(\"./OperatorSubscriber\");\nvar lift_1 = require(\"../util/lift\");\nfunction catchError(selector) {\n  return lift_1.operate(function (source, subscriber) {\n    var innerSub = null;\n    var syncUnsub = false;\n    var handledResult;\n    innerSub = source.subscribe(OperatorSubscriber_1.createOperatorSubscriber(subscriber, undefined, undefined, function (err) {\n      handledResult = innerFrom_1.innerFrom(selector(err, catchError(selector)(source)));\n      if (innerSub) {\n        innerSub.unsubscribe();\n        innerSub = null;\n        handledResult.subscribe(subscriber);\n      } else {\n        syncUnsub = true;\n      }\n    }));\n    if (syncUnsub) {\n      innerSub.unsubscribe();\n      innerSub = null;\n      handledResult.subscribe(subscriber);\n    }\n  });\n}\nexports.catchError = catchError;\n//# sourceMappingURL=catchError.js.map", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}