{"ast": null, "code": "import { Angular5Csv } from 'angular5-csv/dist/Angular5-csv';\nimport { debounceTime, map } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/common/http\";\nimport * as i3 from \"./../webapi.service\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"../../../node_modules/@angular/forms/index\";\nimport * as i6 from \"../topmenu/topmenu.component\";\nconst _c0 = a0 => ({\n  \"display\": a0,\n  \"opacity\": 1\n});\nfunction SalestockComponent_option_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 24);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r1 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", item_r1.name);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", item_r1.name, \" \");\n  }\n}\nfunction SalestockComponent_tr_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"th\", 25);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\", 25);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\", 25);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\", 25);\n    i0.ɵɵtext(8);\n    i0.ɵɵpipe(9, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"td\", 25);\n    i0.ɵɵtext(11);\n    i0.ɵɵpipe(12, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"td\", 25);\n    i0.ɵɵtext(14);\n    i0.ɵɵpipe(15, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"td\", 25);\n    i0.ɵɵtext(17);\n    i0.ɵɵpipe(18, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"td\", 25);\n    i0.ɵɵtext(20);\n    i0.ɵɵpipe(21, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"td\", 25);\n    i0.ɵɵtext(23);\n    i0.ɵɵpipe(24, \"number\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r2 = ctx.$implicit;\n    const i_r3 = ctx.index;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i_r3 + 1);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r2.itemid);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r2.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(9, 9, item_r2.avastock, \"1.2-2\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(12, 12, item_r2.ordered, \"1.2-2\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(15, 15, item_r2.phystock, \"1.2-2\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(18, 18, item_r2.po, \"1.2-2\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(21, 21, item_r2.prod, \"1.2-2\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(24, 24, item_r2.reserve, \"1.2-2\"));\n  }\n}\nexport let SalestockComponent = /*#__PURE__*/(() => {\n  class SalestockComponent {\n    constructor(router, http, service) {\n      this.router = router;\n      this.http = http;\n      this.service = service;\n      this.mdlSampleIsOpen = false;\n      this.alt = '';\n      this.checkreload = true;\n      this.options = {\n        fieldSeparator: ',',\n        quoteStrings: '\"',\n        decimalseparator: '.',\n        showLabels: true,\n        showTitle: true,\n        useBom: true,\n        noDownload: false,\n        headers: [\"คลัง\", \"รหัสกลุ่มสินค้า\", \"รหัสสินค้า\", \"กลุ่มสินค้า\", \"ชื่อสินค้า\", \"ราคาตั้ง\", \"จำนวนต่อ Pack\", \"หน่วยนับ\", \"หน่วยของแพ็ค\", \"น้ำหนัก\"]\n      };\n      this.Inventlocationid = '';\n      this.itemgroupid = '';\n      this.itemid = '';\n      this.catname = '';\n      this.nameproduct = '';\n      this.productlistauto = [];\n      this.permisstiondata = [];\n      this.exportbtn = true;\n      this.searchbtn = true;\n      this.productlistse = [];\n      this.ProductCategoryList = [];\n      this.groupitem = '';\n      this.productlistold = [];\n      this.search = text$ => text$.pipe(debounceTime(200), map(term => term === '' ? [] : this.productlistauto.filter(v => v.itemid.toLowerCase().indexOf(term.toLowerCase()) > -1).slice(0, 10)));\n      this.formatter = x => x.itemid;\n      this.searchname = text$ => text$.pipe(debounceTime(200), map(term => term === '' ? [] : this.productlistauto.filter(v => v.name.toLowerCase().indexOf(term.toLowerCase()) > -1).slice(0, 10)));\n      this.formattername = x => x.name;\n      localStorage.removeItem('DataSOderreview');\n      localStorage.removeItem('DataSOderlist');\n      this.name = 'กำลังนำเข้าข้อมูลกรุณารอสักครู่.....';\n      this.Searchporductlist();\n      this.url = service.geturlservice();\n      this.LoadCategory();\n      /*this.permisstiondata=JSON.parse(sessionStorage.getItem('menu'))\n      this.salegroup=JSON.stringify(sessionStorage.getItem('salegroup'))\n      this.exportbtn=!this.permisstiondata[1].flag_print;\n      this.searchbtn=!this.permisstiondata[1].flag_action;*/\n      this.salegroup = JSON.stringify(sessionStorage.getItem('salegroup'));\n      this.permisstiondata = JSON.parse(sessionStorage.getItem('menu'));\n      if (this.permisstiondata == null) {\n        this.router.navigate(['login']);\n      } else {\n        this.permisstiondata = JSON.parse(sessionStorage.getItem('menu'));\n        this.salegroup = JSON.stringify(sessionStorage.getItem('salegroup'));\n        this.exportbtn = !this.permisstiondata[1].flag_print;\n        this.searchbtn = !this.permisstiondata[1].flag_action;\n      }\n    }\n    ngOnInit() {\n      this.getproductauto(this.salegroup);\n    }\n    getproductauto(accountnum) {\n      this.http.get(this.url + 'productauto/admin').subscribe(res => {\n        if (res.length > 0) {\n          this.productlistauto = res;\n        }\n      });\n    }\n    toggleWithGreeting(tooltip, greeting) {\n      if (tooltip.isOpen()) {\n        tooltip.close();\n      } else {\n        tooltip.open({\n          greeting\n        });\n      }\n    }\n    syncdataproduct(tool) {\n      this.toggleWithGreeting(tool, '');\n      const Http = new XMLHttpRequest();\n      const url = 'syncso/Service.asmx/PullingData?iFile=Product';\n      Http.open(\"GET\", url);\n      Http.send();\n      Http.onreadystatechange = e => {\n        if (Http.readyState == 4 && Http.status == 200) {\n          this.name = 'นำเข้าข้อมูล เสร็จสิ้น';\n          if (confirm('นำเข้าข้อมูล เสร็จสิ้น')) {\n            this.toggleWithGreeting(tool, '');\n          } else {\n            this.toggleWithGreeting(tool, '');\n          }\n        }\n      };\n    }\n    LoadCategory() {\n      this.http.get('http://*************/SoAPI/api/values/GetCategoryAll').subscribe(res => {\n        this.ProductCategoryList = res;\n      });\n    }\n    FilterProduct() {\n      if (this.itemcode == '') {\n        this.productlist = this.productlistold;\n      } else {\n        this.productlistse = this.productlistold;\n        this.productlist = this.productlistse.filter(v => v.itemid.toLowerCase().indexOf(this.itemcode.toLowerCase()) > -1).slice(0, 50);\n      }\n    }\n    FilterProductName() {\n      if (this.itemname == '') {\n        this.productlist = this.productlistold;\n      } else {\n        this.productlistse = this.productlistold;\n        this.productlist = this.productlistse.filter(v => v.name.toLowerCase().indexOf(this.itemname.toLowerCase()) > -1).slice(0, 50);\n      }\n    }\n    FilterProductcategory() {\n      var itemgroup = 'FG-NN';\n      if (this.groupitem == '0') {\n        this.productlist = this.productlistse;\n      } else {\n        this.productlistse = this.productlistold;\n        var catelist = [];\n        catelist = this.groupitem.toString().split(',');\n        this.productlist = this.productlistse.filter(itemX => catelist.includes(itemX.category));\n        //alert(this.productlist.length);\n      }\n    }\n    Searchporductlist() {\n      this.productlist = [];\n      var Inventlocationid = '';\n      var itemgroupid = '';\n      var itemid = '';\n      var catname = '';\n      var nameproduct = '';\n      if (this.itemcode == undefined || this.itemcode == '') {\n        this.itemid = '';\n      } else {\n        this.itemid = this.itemcode.itemid;\n      }\n      if (this.itemname == undefined || this.itemname == '') {\n        this.nameproduct = '';\n      } else {\n        this.nameproduct = this.itemname.itemid;\n      }\n      if (this.Inventlocationid == '') {\n        Inventlocationid = '%20';\n      } else {\n        Inventlocationid = this.Inventlocationid;\n      }\n      if (this.itemgroupid == '') {\n        itemgroupid = '%20';\n      } else {\n        itemgroupid = this.itemgroupid;\n      }\n      if (this.itemid == '') {\n        itemid = '%20';\n      } else {\n        itemid = this.itemid;\n      }\n      if (this.catname == '') {\n        catname = '%20';\n      } else {\n        catname = this.catname;\n      }\n      if (this.nameproduct == '') {\n        nameproduct = '%20';\n      } else {\n        nameproduct = this.nameproduct;\n      }\n      // var inventlocationid = req.body.inventlocationid;\n      // var itemgroupid = req.body.itemgroupid; +  + '/' +  + '/' +  + '/' +  + '/' +\n      // var itemid = req.body.itemid;\n      // var catname = req.body.catname;\n      // var name = req.body.name;\n      var body = {\n        inventlocationid: Inventlocationid == '%20' ? '' : Inventlocationid,\n        itemgroupid: itemgroupid == '%20' ? '' : itemgroupid,\n        itemid: itemid == '%20' ? '' : itemid,\n        catname: catname == '%20' ? '' : catname,\n        name: nameproduct == '%20' ? '' : nameproduct\n      };\n      this.http.get('http://*************/SoAPI/api/values/GetStockCountGetAll').subscribe(res => {\n        if (res.length > 0) {\n          this.productlistse = res;\n          this.productlistold = res;\n          this.productlist = res;\n          if (Inventlocationid == '%20') {\n            this.Inventlocationid = '';\n          }\n          if (itemgroupid == '%20') {\n            this.itemgroupid = '';\n          }\n          if (itemid == '%20') {\n            this.itemid = '';\n          }\n          if (catname == '%20') {\n            this.catname = '';\n          }\n          if (nameproduct == '%20') {\n            this.nameproduct = '';\n          }\n        } else {\n          if (Inventlocationid == '%20') {\n            this.Inventlocationid = '';\n          }\n          if (itemgroupid == '%20') {\n            this.itemgroupid = '';\n          }\n          if (itemid == '%20') {\n            this.itemid = '';\n          }\n          if (catname == '%20') {\n            this.catname = '';\n          }\n          if (nameproduct == '%20') {\n            this.nameproduct = '';\n          }\n          this.openModal(true, 'ไม่พบข้อมูลที่ค้นหา', false);\n        }\n      });\n    }\n    exportdataexcel() {\n      if (this.productlist == undefined) {\n        this.openModal(true, 'ไม่พบข้อมูล', false);\n      }\n      new Angular5Csv(this.productlist, 'ProduceList', this.options);\n    }\n    openModal(open, text, load) {\n      this.mdlSampleIsOpen = open;\n      this.alt = text;\n      this.checkreload = load;\n    }\n    closemodel(cl) {\n      this.mdlSampleIsOpen = cl;\n      if (this.checkreload == true) {\n        location.reload();\n      }\n    }\n    static {\n      this.ɵfac = function SalestockComponent_Factory(t) {\n        return new (t || SalestockComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.HttpClient), i0.ɵɵdirectiveInject(i3.WebapiService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: SalestockComponent,\n        selectors: [[\"app-salestock\"]],\n        decls: 51,\n        vars: 9,\n        consts: [[1, \"container-fluid\", 2, \"padding-right\", \"5px\", \"padding-left\", \"5px\"], [2, \"padding-top\", \"60px\"], [1, \"p-sm-1\", \"bg-secondary\", \"text-white\", \"text-center\"], [1, \"form-row\"], [1, \"col-md-2\", \"mb-2\"], [\"id\", \"typeahead-template\", \"placeholder\", \"\\u0E23\\u0E2B\\u0E31\\u0E2A\\u0E2A\\u0E34\\u0E19\\u0E04\\u0E49\\u0E32\", \"type\", \"text\", \"name\", \"itemcode\", 1, \"form-control\", \"form-control-sm\", 3, \"keyup\", \"ngModelChange\", \"ngModel\"], [\"id\", \"typeahead-template\", \"placeholder\", \"\\u0E0A\\u0E37\\u0E48\\u0E2D\\u0E2A\\u0E34\\u0E19\\u0E04\\u0E49\\u0E32\", \"type\", \"text\", \"name\", \"itemname\", 1, \"form-control\", \"form-control-sm\", 3, \"keyup\", \"ngModelChange\", \"ngModel\"], [\"multiple\", \"\", 1, \"custom-select\", \"custom-select-sm\", 3, \"ngModelChange\", \"change\", \"ngModel\"], [\"selected\", \"\", \"value\", \"0\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"col-md-2\", \"mb-3\", \"col-12\", \"text-center\", \"text-sm-center\", \"text-md-center\", \"text-lg-left\", 2, \"padding-right\", \"0px\", \"padding-left\", \"0px\"], [1, \"table\", \"table-hover\", \"table-bordered\", \"table-sm\"], [1, \"text-sm-center\", \"font-weight-light\", \"bg-light\"], [\"scope\", \"col\", 1, \"font-weight-normal\"], [4, \"ngFor\", \"ngForOf\"], [\"id\", \"mdlSample\", \"role\", \"dialog\", 1, \"modal\", \"fade\", 3, \"ngStyle\"], [1, \"modal-dialog\", \"modal-md\"], [1, \"modal-content\"], [1, \"modal-header\", \"colhaederal\"], [1, \"modal-title\"], [1, \"modal-body\"], [\"align\", \"right\", 1, \"modal-footer\"], [\"type\", \"button\", \"id\", \"btnClose\", 1, \"btn\", \"btn-danger\", 3, \"click\"], [1, \"fa\", \"fa-times\", \"fa-fw\"], [3, \"value\"], [1, \"text-sm-center\", \"font-weight-normal\"]],\n        template: function SalestockComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelement(0, \"app-topmenu\");\n            i0.ɵɵelementStart(1, \"div\", 0)(2, \"section\", 1)(3, \"h5\", 2);\n            i0.ɵɵtext(4, \"Sale Stock\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(5, \"div\", 3)(6, \"div\", 4)(7, \"input\", 5);\n            i0.ɵɵlistener(\"keyup\", function SalestockComponent_Template_input_keyup_7_listener() {\n              return ctx.FilterProduct();\n            });\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function SalestockComponent_Template_input_ngModelChange_7_listener($event) {\n              i0.ɵɵtwoWayBindingSet(ctx.itemcode, $event) || (ctx.itemcode = $event);\n              return $event;\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(8, \"div\", 4)(9, \"input\", 6);\n            i0.ɵɵlistener(\"keyup\", function SalestockComponent_Template_input_keyup_9_listener() {\n              return ctx.FilterProductName();\n            });\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function SalestockComponent_Template_input_ngModelChange_9_listener($event) {\n              i0.ɵɵtwoWayBindingSet(ctx.itemname, $event) || (ctx.itemname = $event);\n              return $event;\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(10, \"div\", 4)(11, \"select\", 7);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function SalestockComponent_Template_select_ngModelChange_11_listener($event) {\n              i0.ɵɵtwoWayBindingSet(ctx.groupitem, $event) || (ctx.groupitem = $event);\n              return $event;\n            });\n            i0.ɵɵlistener(\"change\", function SalestockComponent_Template_select_change_11_listener() {\n              return ctx.FilterProductcategory();\n            });\n            i0.ɵɵelementStart(12, \"option\", 8);\n            i0.ɵɵtext(13, \"--\\u0E40\\u0E25\\u0E37\\u0E2D\\u0E01\\u0E23\\u0E32\\u0E22\\u0E01\\u0E32\\u0E23\\u0E17\\u0E31\\u0E49\\u0E07\\u0E2B\\u0E21\\u0E14--\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(14, SalestockComponent_option_14_Template, 2, 2, \"option\", 9);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelement(15, \"div\", 10);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(16, \"table\", 11)(17, \"thead\")(18, \"tr\", 12)(19, \"th\", 13);\n            i0.ɵɵtext(20, \"\\u0E25\\u0E33\\u0E14\\u0E31\\u0E1A\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(21, \"th\", 13);\n            i0.ɵɵtext(22, \"\\u0E23\\u0E2B\\u0E31\\u0E2A\\u0E2A\\u0E34\\u0E19\\u0E04\\u0E49\\u0E32\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(23, \"th\", 13);\n            i0.ɵɵtext(24, \"\\u0E0A\\u0E37\\u0E48\\u0E2D\\u0E2A\\u0E34\\u0E19\\u0E04\\u0E49\\u0E32\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(25, \"th\", 13);\n            i0.ɵɵtext(26, \"AVASTOCK\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(27, \"th\", 13);\n            i0.ɵɵtext(28, \"ORDERED\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(29, \"th\", 13);\n            i0.ɵɵtext(30, \"PHYSTOCK\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(31, \"th\", 13);\n            i0.ɵɵtext(32, \"PO\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(33, \"th\", 13);\n            i0.ɵɵtext(34, \"PROD\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(35, \"th\", 13);\n            i0.ɵɵtext(36, \"RESERVE\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(37, \"tbody\");\n            i0.ɵɵtemplate(38, SalestockComponent_tr_38_Template, 25, 27, \"tr\", 14);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(39, \"div\", 15)(40, \"div\", 16)(41, \"div\", 17)(42, \"div\", 18)(43, \"h4\", 19);\n            i0.ɵɵtext(44, \"Report\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(45, \"div\", 20);\n            i0.ɵɵtext(46);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(47, \"div\", 21)(48, \"button\", 22);\n            i0.ɵɵlistener(\"click\", function SalestockComponent_Template_button_click_48_listener() {\n              return ctx.closemodel(false);\n            });\n            i0.ɵɵelement(49, \"i\", 23);\n            i0.ɵɵtext(50, \" \\u0E1B\\u0E34\\u0E14\");\n            i0.ɵɵelementEnd()()()()()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(7);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.itemcode);\n            i0.ɵɵadvance(2);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.itemname);\n            i0.ɵɵadvance(2);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.groupitem);\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"ngForOf\", ctx.ProductCategoryList);\n            i0.ɵɵadvance(24);\n            i0.ɵɵproperty(\"ngForOf\", ctx.productlist);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(7, _c0, ctx.mdlSampleIsOpen ? \"block\" : \"none\"));\n            i0.ɵɵadvance(7);\n            i0.ɵɵtextInterpolate(ctx.alt);\n          }\n        },\n        dependencies: [i4.NgForOf, i4.NgStyle, i5.NgSelectOption, i5.ɵNgSelectMultipleOption, i5.DefaultValueAccessor, i5.SelectMultipleControlValueAccessor, i5.NgControlStatus, i5.NgModel, i6.TopmenuComponent, i4.DecimalPipe]\n      });\n    }\n  }\n  return SalestockComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}