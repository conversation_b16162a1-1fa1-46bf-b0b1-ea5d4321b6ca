{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.auditTime = void 0;\nvar async_1 = require(\"../scheduler/async\");\nvar audit_1 = require(\"./audit\");\nvar timer_1 = require(\"../observable/timer\");\nfunction auditTime(duration, scheduler) {\n  if (scheduler === void 0) {\n    scheduler = async_1.asyncScheduler;\n  }\n  return audit_1.audit(function () {\n    return timer_1.timer(duration, scheduler);\n  });\n}\nexports.auditTime = auditTime;\n//# sourceMappingURL=auditTime.js.map", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}