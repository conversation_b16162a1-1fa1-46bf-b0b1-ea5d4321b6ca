{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nvar core_1 = require(\"@angular/core\");\nvar common_1 = require(\"./common\");\nvar HighlightPipe = function () {\n  function HighlightPipe() {}\n  HighlightPipe.prototype.transform = function (value, query) {\n    if (query.length < 1) {\n      return value;\n    }\n    if (query) {\n      var tagRE = new RegExp('<[^<>]*>', 'ig');\n      // get ist of tags\n      var tagList = value.match(tagRE);\n      // Replace tags with token\n      var tmpValue = value.replace(tagRE, '$!$');\n      // Replace search words\n      value = tmpValue.replace(new RegExp(common_1.escapeRegexp(query), 'gi'), '<strong>$&</strong>');\n      // Reinsert HTML\n      for (var i = 0; value.indexOf('$!$') > -1; i++) {\n        value = value.replace('$!$', tagList[i]);\n      }\n    }\n    return value;\n  };\n  HighlightPipe.decorators = [{\n    type: core_1.Pipe,\n    args: [{\n      name: 'highlight'\n    }]\n  }];\n  /** @nocollapse */\n  HighlightPipe.ctorParameters = function () {\n    return [];\n  };\n  return HighlightPipe;\n}();\nexports.HighlightPipe = HighlightPipe;\nfunction stripTags(input) {\n  var tags = /<\\/?([a-z][a-z0-9]*)\\b[^>]*>/gi;\n  var commentsAndPhpTags = /<!--[\\s\\S]*?-->|<\\?(?:php)?[\\s\\S]*?\\?>/gi;\n  return input.replace(commentsAndPhpTags, '').replace(tags, '');\n}\nexports.stripTags = stripTags;", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}