{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.connectable = void 0;\nvar Subject_1 = require(\"../Subject\");\nvar Observable_1 = require(\"../Observable\");\nvar defer_1 = require(\"./defer\");\nvar DEFAULT_CONFIG = {\n  connector: function () {\n    return new Subject_1.Subject();\n  },\n  resetOnDisconnect: true\n};\nfunction connectable(source, config) {\n  if (config === void 0) {\n    config = DEFAULT_CONFIG;\n  }\n  var connection = null;\n  var connector = config.connector,\n    _a = config.resetOnDisconnect,\n    resetOnDisconnect = _a === void 0 ? true : _a;\n  var subject = connector();\n  var result = new Observable_1.Observable(function (subscriber) {\n    return subject.subscribe(subscriber);\n  });\n  result.connect = function () {\n    if (!connection || connection.closed) {\n      connection = defer_1.defer(function () {\n        return source;\n      }).subscribe(subject);\n      if (resetOnDisconnect) {\n        connection.add(function () {\n          return subject = connector();\n        });\n      }\n    }\n    return connection;\n  };\n  return result;\n}\nexports.connectable = connectable;\n//# sourceMappingURL=connectable.js.map", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}