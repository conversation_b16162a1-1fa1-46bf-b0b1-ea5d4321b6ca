{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.toArray = void 0;\nvar reduce_1 = require(\"./reduce\");\nvar lift_1 = require(\"../util/lift\");\nvar arrReducer = function (arr, value) {\n  return arr.push(value), arr;\n};\nfunction toArray() {\n  return lift_1.operate(function (source, subscriber) {\n    reduce_1.reduce(arrReducer, [])(source).subscribe(subscriber);\n  });\n}\nexports.toArray = toArray;\n//# sourceMappingURL=toArray.js.map", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}