{"ast": null, "code": "import { HttpClient } from '@angular/common/http';\nimport { Router } from '@angular/router';\nimport { NgbCalendar } from '@ng-bootstrap/ng-bootstrap';\nimport { Angular5Csv } from 'angular5-csv/dist/Angular5-csv';\nimport { BsModalService } from 'ngx-bootstrap/modal';\nimport { debounceTime, map } from 'rxjs/operators';\nimport { WebapiService } from './../webapi.service';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"ngx-bootstrap/modal\";\nimport * as i2 from \"@angular/common/http\";\nimport * as i3 from \"./../webapi.service\";\nimport * as i4 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i5 from \"@angular/router\";\nimport * as i6 from \"ngx-bootstrap/tooltip\";\nimport * as i7 from \"@angular/common\";\nimport * as i8 from \"../../../node_modules/@angular/forms/index\";\nimport * as i9 from \"ngx-bootstrap/datepicker\";\nimport * as i10 from \"ngx-bootstrap/tabs\";\nimport * as i11 from \"../topmenu/topmenu.component\";\nconst _c0 = () => ({\n  dateInputFormat: \"DD/MM/YYYY\"\n});\nconst _c1 = a0 => ({\n  \"display\": a0,\n  \"opacity\": 1\n});\nconst _c2 = a0 => ({\n  \"color\": a0\n});\nfunction SalestatusComponent_div_6_option_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 73);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r4 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", item_r4.groupid);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\" \", item_r4.groupid, \" (\", item_r4.name, \") \");\n  }\n}\nfunction SalestatusComponent_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 8)(1, \"select\", 69);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SalestatusComponent_div_6_Template_select_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.salegroup, $event) || (ctx_r2.salegroup = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementStart(2, \"option\", 70);\n    i0.ɵɵtext(3, \"--\\u0E40\\u0E25\\u0E37\\u0E2D\\u0E01\\u0E23\\u0E2B\\u0E31\\u0E2A\\u0E1E\\u0E19\\u0E31\\u0E01\\u0E07\\u0E32\\u0E19\\u0E02\\u0E32\\u0E22--\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"option\", 71);\n    i0.ɵɵtext(5, \"\\u0E40\\u0E25\\u0E37\\u0E2D\\u0E01\\u0E23\\u0E32\\u0E22\\u0E01\\u0E32\\u0E23\\u0E17\\u0E31\\u0E49\\u0E07\\u0E2B\\u0E21\\u0E14\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, SalestatusComponent_div_6_option_6_Template, 2, 3, \"option\", 72);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.salegroup);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.DateGroupsaleman);\n  }\n}\nfunction SalestatusComponent_ng_template_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"label\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const r_r5 = ctx.result;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"\", r_r5.name, \" (\", r_r5.accountnum, \")\");\n  }\n}\nfunction SalestatusComponent_tr_97_td_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 81);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r7 = i0.ɵɵnextContext().$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(2, _c2, ctx_r2.getColorst(item_r7.salesstatus)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r2.getstatustype(item_r7.salesstatus));\n  }\n}\nfunction SalestatusComponent_tr_97_td_39_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 81);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r7 = i0.ɵɵnextContext().$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(2, _c2, ctx_r2.getColorsdaft(item_r7.stcheck)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r7.textstatus);\n  }\n}\nfunction SalestatusComponent_tr_97_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\", 74)(1, \"td\", 75);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\", 75);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\", 75);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\", 76);\n    i0.ɵɵtext(8);\n    i0.ɵɵpipe(9, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"td\", 77);\n    i0.ɵɵpipe(11, \"date\");\n    i0.ɵɵtext(12);\n    i0.ɵɵpipe(13, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"td\", 76);\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"td\", 59);\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"td\", 59);\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"td\", 59);\n    i0.ɵɵtext(21);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"td\", 78);\n    i0.ɵɵtext(23);\n    i0.ɵɵpipe(24, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"td\", 76);\n    i0.ɵɵtext(26);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"td\", 76);\n    i0.ɵɵtext(28);\n    i0.ɵɵpipe(29, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"td\", 76);\n    i0.ɵɵtext(31);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"td\", 76);\n    i0.ɵɵtext(33);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(34, \"td\", 78);\n    i0.ɵɵtext(35);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(36, \"td\", 78);\n    i0.ɵɵtext(37);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(38, SalestatusComponent_tr_97_td_38_Template, 2, 4, \"td\", 79)(39, SalestatusComponent_tr_97_td_39_Template, 2, 4, \"td\", 79);\n    i0.ɵɵelementStart(40, \"td\", 76)(41, \"button\", 80);\n    i0.ɵɵlistener(\"click\", function SalestatusComponent_tr_97_Template_button_click_41_listener() {\n      const item_r7 = i0.ɵɵrestoreView(_r6).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      const templateView_r8 = i0.ɵɵreference(171);\n      return i0.ɵɵresetView(ctx_r2.getsaloderhistory(item_r7.salesid, templateView_r8));\n    });\n    i0.ɵɵtext(42, \" View \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const item_r7 = ctx.$implicit;\n    const i_r9 = ctx.index;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i_r9 + 1);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r7.purchorderformnum);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r7.salesid);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(9, 20, item_r7.createddatetime, \"dd/MM/yyyy\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate1(\"tooltip\", \"\\u0E27\\u0E31\\u0E19\\u0E17\\u0E35\\u0E48\\u0E15\\u0E49\\u0E2D\\u0E07\\u0E01\\u0E32\\u0E23\\u0E2A\\u0E34\\u0E19\\u0E04\\u0E49\\u0E32 \", i0.ɵɵpipeBind2(11, 23, item_r7.shippingdaterequested, \"dd/MM/yyyy\"), \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(13, 26, item_r7.createddatetime, \"HH:mm:ss\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(item_r7.salesgroup);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r7.salesname);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r7.invname);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r7.deliveryname);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(24, 29, item_r7.amount, \"1.2-2\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r2.getVatttype(item_r7.taxgroup));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(29, 32, item_r7.tottalWeight, \"1.2-2\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r2.getdlvtype(item_r7.dlvmode));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r7.payment);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r7.noteinternal);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r7.remark);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r7.stcheck != 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r7.stcheck == 0);\n  }\n}\nfunction SalestatusComponent_tr_98_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 82);\n    i0.ɵɵtext(2, \" \\u0E44\\u0E21\\u0E48\\u0E21\\u0E35\\u0E23\\u0E32\\u0E22\\u0E01\\u0E32\\u0E23 \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction SalestatusComponent_tr_147_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"tr\");\n  }\n}\nfunction SalestatusComponent_ng_template_170_tr_39_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 87);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\", 88);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\", 87);\n    i0.ɵɵtext(6);\n    i0.ɵɵpipe(7, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"td\", 87);\n    i0.ɵɵtext(9);\n    i0.ɵɵpipe(10, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"td\", 87);\n    i0.ɵɵtext(12);\n    i0.ɵɵpipe(13, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"td\", 87);\n    i0.ɵɵtext(15);\n    i0.ɵɵpipe(16, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"td\", 87);\n    i0.ɵɵtext(18);\n    i0.ɵɵpipe(19, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"td\", 87);\n    i0.ɵɵtext(21);\n    i0.ɵɵpipe(22, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"td\", 87);\n    i0.ɵɵtext(24);\n    i0.ɵɵpipe(25, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"td\", 89);\n    i0.ɵɵtext(27);\n    i0.ɵɵpipe(28, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"td\", 89);\n    i0.ɵɵtext(30);\n    i0.ɵɵpipe(31, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"td\", 89);\n    i0.ɵɵtext(33);\n    i0.ɵɵpipe(34, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(35, \"td\", 89);\n    i0.ɵɵtext(36);\n    i0.ɵɵpipe(37, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(38, \"td\", 90);\n    i0.ɵɵtext(39);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r11 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r11.itemid);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r11.iname);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(7, 15, item_r11.pack, \"1.2-2\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(10, 18, item_r11.qtyLine, \"1.2-2\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(13, 21, item_r11.remainqty, \"1.2-2\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(16, 24, item_r11.qtydlv, \"1.2-2\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(19, 27, item_r11.weight, \"1.2-2\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(22, 30, item_r11.avastock, \"1.2-2\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(25, 33, item_r11.salesprice, \"1.2-2\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind2(28, 36, item_r11.ivZ_PERCENT1_CT, \"1.2-2\"), \"%\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind2(31, 39, item_r11.ivZ_PERCENT2_CT, \"1.2-2\"), \"%\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind2(34, 42, item_r11.ivZ_PERCENT3_CT, \"1.2-2\"), \"%\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(37, 45, item_r11.lineamount, \"1.2-2\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(48, _c2, ctx_r2.getColorst(item_r11.lsalestatus)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r2.getstatustype(item_r11.lsalestatus));\n  }\n}\nfunction SalestatusComponent_ng_template_170_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 52)(1, \"h4\", 83);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 84);\n    i0.ɵɵlistener(\"click\", function SalestatusComponent_ng_template_170_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.modalRef.hide());\n    });\n    i0.ɵɵelementStart(4, \"span\", 55);\n    i0.ɵɵtext(5, \"\\u00D7\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(6, \"div\", 43)(7, \"tabset\", null, 2)(9, \"tab\", 85)(10, \"div\", 57)(11, \"table\", 35)(12, \"thead\")(13, \"tr\")(14, \"th\", 58);\n    i0.ɵɵtext(15, \"\\u0E23\\u0E2B\\u0E31\\u0E2A\\u0E2A\\u0E34\\u0E19\\u0E04\\u0E49\\u0E32\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"th\", 58);\n    i0.ɵɵtext(17, \"\\u0E0A\\u0E37\\u0E48\\u0E2D\\u0E2A\\u0E34\\u0E19\\u0E04\\u0E49\\u0E32\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"th\", 59);\n    i0.ɵɵtext(19, \"Pack\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"th\", 59);\n    i0.ɵɵtext(21, \"\\u0E08\\u0E33\\u0E19\\u0E27\\u0E19\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"th\", 59);\n    i0.ɵɵtext(23, \"\\u0E04\\u0E49\\u0E32\\u0E07\\u0E2A\\u0E48\\u0E07\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"th\", 59);\n    i0.ɵɵtext(25, \"\\u0E2A\\u0E48\\u0E07\\u0E41\\u0E25\\u0E49\\u0E27\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"th\", 59);\n    i0.ɵɵtext(27, \"\\u0E19\\u0E49\\u0E33\\u0E2B\\u0E19\\u0E31\\u0E01\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"th\", 59);\n    i0.ɵɵtext(29, \"In Stock\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"th\", 59);\n    i0.ɵɵtext(31, \"\\u0E23\\u0E32\\u0E04\\u0E32\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"th\", 60);\n    i0.ɵɵtext(33, \"\\u0E2A\\u0E48\\u0E27\\u0E19\\u0E25\\u0E14\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(34, \"th\", 59);\n    i0.ɵɵtext(35, \"\\u0E21\\u0E39\\u0E25\\u0E04\\u0E48\\u0E32\\u0E2A\\u0E38\\u0E17\\u0E18\\u0E34\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(36, \"th\", 59);\n    i0.ɵɵtext(37, \"\\u0E2A\\u0E16\\u0E32\\u0E19\\u0E30\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(38, \"tbody\");\n    i0.ɵɵtemplate(39, SalestatusComponent_ng_template_170_tr_39_Template, 40, 50, \"tr\", 61);\n    i0.ɵɵelementEnd()()()()()();\n    i0.ɵɵelementStart(40, \"div\", 62)(41, \"button\", 86);\n    i0.ɵɵlistener(\"click\", function SalestatusComponent_ng_template_170_Template_button_click_41_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.modalRef.hide());\n    });\n    i0.ɵɵtext(42, \"Close\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"Sale Oder Detail : \", ctx_r2.showIDso, \"\");\n    i0.ɵɵadvance(37);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.salelistview);\n  }\n}\nexport let SalestatusComponent = /*#__PURE__*/(() => {\n  class SalestatusComponent {\n    getstatustype(type) {\n      if (type == 0) {\n        return 'รอเข้าระบบ AX';\n      } else if (type == 1) {\n        return 'Openorder';\n      } else if (type == 2) {\n        return 'Delivered';\n      } else if (type == 3) {\n        return 'Invoiced';\n      } else if (type == 4) {\n        return 'Canceled';\n      }\n    }\n    getVatttype(type) {\n      if (type == 'DOM') {\n        return 'VAT';\n      } else if (type == 'OVS') {\n        return 'VAT';\n      } else if (type == 'NoVAT') {\n        return 'NO VAT';\n      } else if (type == 'NO VAT') {\n        return 'NO VAT';\n      } else if (type == 'VAT') {\n        return 'VAT';\n      } else if (type == 'VAT ไม่ยื่น') {\n        return 'VAT ไม่ยื่น';\n      } else {\n        return '';\n      }\n    }\n    getdlvtype(type) {\n      if (type == '01-ขนส่ง') {\n        return 'ขนส่ง';\n      } else if (type == '02รถบริษัท') {\n        return 'รถบริษัท';\n      } else if (type == '03-รับเอง') {\n        return 'รับเอง';\n      } else {\n        return 'KERRY';\n      }\n    }\n    getColorst(disst) {\n      if (disst == 0) {\n        return '#05C1FF';\n      } else if (disst == 1) {\n        return '#FF9505';\n      } else if (disst == 2) {\n        return '#C305FF';\n      } else if (disst == 3) {\n        return '#FF05D5';\n      } else if (disst == 4) {\n        return '#0540FF';\n      } else if (disst == 5) {\n        return '#0AC103';\n      }\n    }\n    getColorsdaft(disst) {\n      if (disst == 0) {\n        return '#7FFF00';\n      }\n    }\n    getColordis1(disst) {\n      var st = disst;\n      var fi = st.substring(0, 1);\n      switch (fi) {\n        case '1':\n          return 'red';\n      }\n    }\n    getColordis2(disst) {\n      var st = disst;\n      var fi = st.substring(1, 2);\n      switch (fi) {\n        case '1':\n          return 'red';\n      }\n    }\n    getColordis3(disst) {\n      var st = disst;\n      var fi = st.substring(2, 3);\n      switch (fi) {\n        case '1':\n          return 'red';\n      }\n    }\n    constructor(modalService, http, service, calendar, router) {\n      this.modalService = modalService;\n      this.http = http;\n      this.service = service;\n      this.calendar = calendar;\n      this.router = router;\n      this.mdlSampleIsOpen = false;\n      this.alt = '';\n      this.checkreload = true;\n      this.options = {\n        fieldSeparator: ',',\n        quoteStrings: '\"',\n        decimalseparator: '.',\n        showLabels: true,\n        showTitle: true,\n        useBom: true,\n        noDownload: false,\n        headers: [\"เลขที่ SO\", \"วันที่\", \"พนักงานขาย\", 'ลูกค้า', 'มูลค่าสินค้า', 'มูลค่าสุทธิ', 'VAT/No VAT', \"ประเภทขนส่ง\", \"เงินสด/เครดิต\", \"Note ภายใน\", \"หมายเหตุ\", \"สถานะ\"]\n      };\n      this.sohitorylistfilter = [];\n      this.fromdate = '';\n      this.todate = '';\n      this.salegroup = '';\n      this.customer = '';\n      this.DateGroupsaleman = [];\n      this.Name = [];\n      this.typesync = '111';\n      this.dateshipping = '';\n      this.dateshippingto = '';\n      this.datalogin = [];\n      this.chackuser = false;\n      this.salelistview = [];\n      this.discustomer = false;\n      this.permisstiondata = [];\n      this.exportbtn = true;\n      this.searchbtn = true;\n      this.company = 'ค้นหาลูกค้า';\n      this.customers = [];\n      this.config = {\n        ignoreBackdropClick: false,\n        class: 'modal-lg modalcinfig'\n      };\n      this.nameUrl = '';\n      this.paymenttype = 'All';\n      this.Ckviewsohitorylistfilter = false;\n      this.deletehistorybtn = false;\n      this.search = text$ =>\n      //Autocomplete ลูกค้า\n      text$.pipe(debounceTime(200), map(term => term === '' ? [] : this.customers.filter(v => v.name.toLowerCase().indexOf(term.toLowerCase()) > -1 || v.accountnum.toLowerCase().indexOf(term.toLowerCase()) > -1).slice(0, 50)));\n      this.formatter = x => x.name + ' (' + x.accountnum + ')';\n      localStorage.removeItem('DataSOderreview');\n      localStorage.removeItem('DataSOderlist');\n      this.url = service.geturlservice();\n      this.Name = JSON.parse(sessionStorage.getItem('login'));\n      this.fromDate = calendar.getPrev(calendar.getToday(), 'd', 30);\n      this.toDate = calendar.getNext(calendar.getToday(), 'm', 3);\n      this.datalogin = JSON.parse(sessionStorage.getItem('login'));\n      this.fromdate = '';\n      this.todate = '';\n      this.Datatodate = new Date(this.toDate.year, this.toDate.month - 1, this.toDate.day);\n      this.Datafromdate = new Date(this.fromDate.year, this.fromDate.month - 1, this.fromDate.day);\n      this.getdate();\n      this.permisstiondata = JSON.parse(sessionStorage.getItem('menu'));\n      if (this.permisstiondata == null) {\n        this.router.navigate(['login']);\n      } else {\n        this.getuser();\n        this.searchsohistory();\n        this.exportbtn = !this.permisstiondata[6].flag_print;\n        this.searchbtn = !this.permisstiondata[6].flag_action;\n      }\n    }\n    getuser() {\n      if (this.datalogin[0].id_group_user == '153Admin' || this.datalogin[0].id_group_user == '1860Administrator') {\n        this.groupsale = '';\n        this.testclose = true;\n        this.deletehistorybtn = true;\n      } else {\n        this.testclose = false;\n        this.groupsale = this.datalogin[0].salegroup;\n      }\n    }\n    searchselect() {\n      // alert(this.typesync+'//'+this.paymenttype)\n      if (this.typesync === '111' && this.paymenttype === 'All') {\n        this.sohitorylistfilter = this.sohitorylist;\n      } else if (this.typesync == '0' || this.typesync == '1' || this.typesync == '2' || this.typesync == '3' || this.typesync == '4') {\n        if (this.paymenttype === 'All') {\n          this.sohitorylistfilter = this.sohitorylist.filter(v => v.salesstatus.toString().toLowerCase().indexOf(this.typesync.toLowerCase()) > -1).slice(0, 2000);\n        } else {\n          this.sohitorylistfilter = this.sohitorylist.filter(v => v.salesstatus.toString().toLowerCase().indexOf(this.typesync.toLowerCase()) > -1).filter(v => v.payment.toLowerCase().indexOf(this.paymenttype.toLowerCase()) > -1).slice(0, 2000);\n        }\n        //this.sohitorylistfilter=this.sohitorylist.filter(v => v.state.toLowerCase().indexOf(this.typesync.toLowerCase()) && v.paymenttype.toLowerCase().indexOf(this.paymenttype.toLowerCase()) > -1).slice(0, 50);\n      } else {\n        var se = '';\n        if (this.typesync == '5') {\n          se = '0'; //\n        } else if (this.typesync == '6') {\n          se = '1';\n        } else if (this.typesync == '7') {\n          se = '3';\n        }\n        if (this.paymenttype === 'All') {\n          this.sohitorylistfilter = this.sohitorylist.filter(v => v.salesstatus.toString().toLowerCase().indexOf(se.toLowerCase()) > -1).slice(0, 2000);\n        } else {\n          this.sohitorylistfilter = this.sohitorylist.filter(v => v.salesstatus.toString().toLowerCase().indexOf(se.toLowerCase()) > -1).filter(v => v.payment.toLowerCase().indexOf(this.paymenttype.toLowerCase()) > -1).slice(0, 2000);\n        }\n        this.Cknum();\n        //  this.sohitorylistfilter=this.sohitorylist.filter(v => v.stcheck.toLowerCase().indexOf(se.toLowerCase()) && v.paymenttype.toLowerCase().indexOf(this.paymenttype.toLowerCase()) > -1).slice(0, 50);\n      }\n    }\n    //paymenttype\n    searchpaymenttype() {\n      //alert(this.typesync+'//'+this.paymenttype)\n      if (this.typesync === '111' && this.paymenttype === 'All') {\n        this.sohitorylistfilter = this.sohitorylist;\n      } else if (this.typesync == '0' || this.typesync == '1' || this.typesync == '2' || this.typesync == '3' || this.typesync == '4') {\n        if (this.paymenttype === 'All') {\n          this.sohitorylistfilter = this.sohitorylist.filter(v => v.salesstatus.toString().toLowerCase().indexOf(this.typesync.toLowerCase()) > -1).slice(0, 2000);\n        } else {\n          this.sohitorylistfilter = this.sohitorylist.filter(v => v.salesstatus.toString().toLowerCase().indexOf(this.typesync.toLowerCase()) > -1).filter(v => v.payment.toLowerCase().indexOf(this.paymenttype.toLowerCase()) > -1).slice(0, 2000);\n        }\n        //this.sohitorylistfilter=this.sohitorylist.filter(v => v.state.toLowerCase().indexOf(this.typesync.toLowerCase()) && v.paymenttype.toLowerCase().indexOf(this.paymenttype.toLowerCase()) > -1).slice(0, 50);\n      } else {\n        var se = '';\n        if (this.typesync == '5') {\n          se = '0'; //\n        } else if (this.typesync == '6') {\n          se = '1';\n        } else if (this.typesync == '7') {\n          se = '3';\n        }\n        if (this.paymenttype === 'All') {\n          this.sohitorylistfilter = this.sohitorylist.filter(v => v.salesstatus.toString().toLowerCase().indexOf(se.toLowerCase()) > -1).slice(0, 2000);\n        } else {\n          this.sohitorylistfilter = this.sohitorylist.filter(v => v.salesstatus.toString().toLowerCase().indexOf(se.toLowerCase()) > -1).filter(v => v.payment.toLowerCase().indexOf(this.paymenttype.toLowerCase()) > -1).slice(0, 2000);\n        }\n      }\n      this.Cknum();\n    }\n    Cknum() {\n      if (this.sohitorylistfilter.length < 1) {\n        this.Ckviewsohitorylistfilter = true;\n      } else {\n        this.Ckviewsohitorylistfilter = false;\n      }\n    }\n    /* <option  class=\"text-black-50\" value=\"111\">ทุกรายการ</option>\n     <option  class=\"text-black-50\" value=\"0\">รอเข้าระบบ AX</option>\n    <option  class=\"text-black-50\" value=\"1\">Openorder</option>\n    <option class=\"text-black-50\" value=\"2\">Delivered</option>\n    <option class=\"text-black-50\" value=\"3\">Invoiced</option>\n    <option class=\"text-black-50\" value=\"4\">Canceled</option>\n    <option class=\"text-black-50\" value=\"5\">Order Draft</option>\n    <option class=\"text-black-50\" value=\"6\">รอ Sync</option>\n    <option class=\"text-black-50\" value=\"7\">กำลัง Sync</option> */\n    //Autocomplete ลูกค้า\n    /*text$.pipe(\n      debounceTime(200),\n      map(term => term === '' ? []\n        : this.customers.filter(v => v.name.toLowerCase().indexOf(term.toLowerCase()) > -1 ||  v.accountnum.toLowerCase().indexOf(term.toLowerCase()) > -1).slice(0, 50))\n    );\n    formatter = (x: {name: string,accountnum :string}) => x.name + ' ('+x.accountnum+')'; */\n    deletehistory() {\n      //alert(this.Deletefromdate);\n      if (this.Deletefromdate == undefined || this.Deletetodate == undefined) {\n        alert('กรุณาเลือกวันที่ ต้องการลบ Order History');\n        return;\n      }\n      var fmonth = this.Deletefromdate.getMonth() + 1;\n      var fday = this.Deletefromdate.getDate();\n      var fmonthst = '';\n      var fdayst = '';\n      var tmonth = this.Deletetodate.getMonth() + 1;\n      var tday = this.Deletetodate.getDate();\n      var tmonthst = '';\n      var tdayst = '';\n      if (fmonth <= 9) {\n        fmonthst = '0' + fmonth;\n      } else {\n        fmonthst = fmonth;\n      }\n      if (fday <= 9) {\n        fdayst = '0' + fday;\n      } else {\n        fdayst = fday;\n      }\n      if (tmonth <= 9) {\n        tmonthst = '0' + tmonth;\n      } else {\n        tmonthst = tmonth;\n      }\n      if (tday <= 9) {\n        tdayst = '0' + tday;\n      } else {\n        tdayst = tday;\n      }\n      var fromdaate = this.Deletefromdate.getFullYear() + '-' + fmonthst + '-' + fdayst;\n      var todate = this.Deletetodate.getFullYear() + '-' + tmonthst + '-' + tdayst;\n      if (confirm('ต้องการ ลบ Order History ใช่ หรือ ไม่')) {\n        const Http = new XMLHttpRequest();\n        const url = 'syncso/Service.asmx/DeleteHistory?D1=' + fromdaate + '&D2=' + todate;\n        Http.open(\"GET\", url);\n        Http.send();\n        Http.onreadystatechange = e => {\n          if (Http.readyState == 4 && Http.status == 200) {\n            alert('ลบ Order History เสร็จสิ้น');\n          }\n        };\n      }\n    }\n    getdate() {\n      this.fromdate = `${this.Datafromdate.getFullYear()}-${this.Datafromdate.getMonth() + 1}-${this.Datafromdate.getDate()}`;\n      this.todate = `${this.Datatodate.getFullYear()}-${this.Datatodate.getMonth() + 1}-${this.Datatodate.getDate()}`;\n    }\n    ngOnInit() {\n      if (this.Name[0].salegroup == 'admin') {\n        this.chackuser = true;\n      }\n      this.getuser();\n      this.getgroupsaleman();\n      this.getcostomerauto();\n    }\n    getcostomerauto() {\n      var idsale = this.datalogin[0].salegroup;\n      if (this.datalogin[0].id_group_user == '153Admin' || this.datalogin[0].id_group_user == '1860Administrator') {\n        idsale = '%20';\n      } else {\n        idsale = this.datalogin[0].salegroup;\n      }\n      this.http.get(this.url + 'customerauto/' + idsale).subscribe(res => {\n        this.customers = res;\n      });\n    }\n    gettype(type) {\n      if (type === \"application/pdf\") {\n        return true;\n      } else {\n        return false;\n      }\n    }\n    gettypeNull(type) {\n      if (type == \"\") {\n        return false;\n      } else {\n        return true;\n      }\n    }\n    getColorFile(type) {\n      if (type !== \"\") {\n        return '#0317ee';\n      }\n    }\n    getsaloderhistory(valueid, template) {\n      this.salelistview = [];\n      this.modalRef = this.modalService.show(template, this.config);\n      this.http.get('http://119.59.112.47/SoAPI/api/values/GetSaleLine/' + valueid).subscribe(res => {\n        if (res.length > 0) {\n          this.salelistview = [];\n          this.salelistview = res;\n          //  this.Cktype = this.gettype(filetype);\n          //  this.CkNull = this.gettypeNull(filetype);\n          //  this.nameUrl='http://119.59.112.47/assets/PDF/'+filename;\n        }\n      });\n    }\n    Searchsohistory(saleid) {\n      this.getdate();\n      var datasalegroup = '';\n      if (this.fromdate == '') {\n        this.fromdate = `${this.fromDate}`;\n      }\n      if (this.todate == '') {\n        this.todate = `${this.toDate}`;\n      }\n      if (saleid == '') {\n        if (this.datalogin[0].salegroup == 'admin') {\n          datasalegroup = '%20';\n        } else {\n          datasalegroup = `${this.datalogin[0].salegroup}`;\n        }\n      }\n      if (saleid !== '') {\n        if (this.datalogin[0].salegroup == 'admin') {\n          datasalegroup = '%20';\n        } else {\n          datasalegroup = `${this.datalogin[0].salegroup}`;\n        }\n      }\n      if (datasalegroup == '') {\n        datasalegroup = '%20';\n      }\n      if (this.customer == '') {\n        this.customer = '%20';\n      }\n      if (this.customer == undefined) {\n        this.customer = '%20';\n      }\n      this.sohitorylist = [];\n      this.http.get('http://119.59.112.47/SoAPI/api/values/get/' + this.fromdate + '/' + this.todate + '/' + this.customer + '/' + datasalegroup).subscribe(res => {\n        if (res.length > 0) {\n          if (datasalegroup == '%20') {\n            this.salegroup = '';\n          }\n          if (this.customer == '%20') {\n            this.customer = '';\n          }\n          this.sohitorylist = res;\n          this.sohitorylistfilter = this.sohitorylist;\n        } else {\n          if (datasalegroup == '%20') {\n            this.salegroup = '';\n          }\n          if (this.customer == '%20') {\n            this.customer = '';\n          }\n          this.sohitorylist = [];\n          alert('ไม่พบข้อมูลที่ค้นหา');\n          //this.openModal(true,'ไม่พบข้อมูลที่ค้นหา',false)\n        }\n        clearInterval(this.Interval);\n      });\n    }\n    searchsohistory() {\n      this.getdate();\n      if (this.getcustomer == undefined) {\n        this.customer = '';\n      } else {\n        this.customer = this.getcustomer.name;\n      }\n      var datasalegroup = '';\n      if (this.fromdate == '') {\n        this.fromdate = `${this.fromDate}`;\n      }\n      if (this.todate == '') {\n        this.todate = `${this.toDate}`;\n      }\n      if (this.groupsale == '') {\n        if (this.datalogin[0].id_group_user == '153Admin' || this.datalogin[0].id_group_user == '1860Administrator') {\n          datasalegroup = `${this.salegroup}`;\n        } else {\n          datasalegroup = `${this.datalogin[0].salegroup}`;\n        }\n      }\n      if (this.groupsale !== '') {\n        if (this.datalogin[0].id_group_user == '153Admin' || this.datalogin[0].id_group_user == '1860Administrator') {\n          datasalegroup = `${this.salegroup}`;\n        } else {\n          datasalegroup = `${this.datalogin[0].salegroup}`;\n        }\n      }\n      if (datasalegroup == '') {\n        datasalegroup = '%20';\n      }\n      if (this.customer == '') {\n        this.customer = '%20';\n      }\n      if (this.Name[0].accountnum != undefined) {\n        this.discustomer = true;\n        this.customer = this.Name[0].accountnum;\n      }\n      if (this.customer == undefined) {\n        this.customer = '%20';\n      }\n      this.sohitorylist = [];\n      this.http.get('http://119.59.112.47/SoAPI/api/values/get/' + this.fromdate + '/' + this.todate + '/' + this.customer + '/' + datasalegroup).subscribe(res => {\n        if (res.length > 0) {\n          if (datasalegroup == '%20') {\n            this.salegroup = '';\n          }\n          if (this.customer == '%20') {\n            this.customer = '';\n          }\n          this.sohitorylist = res;\n          this.sohitorylistfilter = this.sohitorylist;\n        } else {\n          this.sohitorylist = [];\n          if (datasalegroup == '%20') {\n            this.salegroup = '';\n          }\n          if (this.customer == '%20') {\n            this.customer = '';\n          }\n          alert('ไม่พบข้อมูลที่ค้นหา');\n          //this.openModal(true,'ไม่พบข้อมูลที่ค้นหา',false)\n        }\n        clearInterval(this.Interval);\n      });\n    }\n    getgroupsaleman() {\n      this.DateGroupsaleman = [];\n      this.http.get(this.url + 'salesman').subscribe(res => {\n        if (res.length > 0) {\n          this.DateGroupsaleman = res;\n        } else {\n          alert('ไม่สามารถติดต่อข้อมูลรหัส saleman ได้');\n        }\n      });\n    }\n    exportdataexcel() {\n      if (this.sohitorylist == undefined) {\n        alert('ไม่พบข้อมูล');\n        //this.openModal(true,'ไม่พบข้อมูล',false);\n      } else {\n        new Angular5Csv(this.sohitorylist, 'Sohistory', this.options);\n      }\n    }\n    openModal(open, text, load) {\n      this.mdlSampleIsOpen = open;\n      this.alt = text;\n      this.checkreload = load;\n    }\n    closemodel(cl) {\n      this.mdlSampleIsOpen = cl;\n      if (this.checkreload == true) {\n        location.reload();\n      }\n    }\n    static {\n      this.ɵfac = function SalestatusComponent_Factory(t) {\n        return new (t || SalestatusComponent)(i0.ɵɵdirectiveInject(i1.BsModalService), i0.ɵɵdirectiveInject(i2.HttpClient), i0.ɵɵdirectiveInject(i3.WebapiService), i0.ɵɵdirectiveInject(i4.NgbCalendar), i0.ɵɵdirectiveInject(i5.Router));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: SalestatusComponent,\n        selectors: [[\"app-salestatus\"]],\n        decls: 172,\n        vars: 29,\n        consts: [[\"rt\", \"\"], [\"templateView\", \"\"], [\"staticTabs\", \"\"], [2, \"padding-top\", \"60px\"], [1, \"container-fluid\", \"col-md-12\", \"col-xs-12\", \"col-sm-12\", \"col-12\", 2, \"padding-right\", \"5px\", \"padding-left\", \"5px\"], [1, \"p-sm-1\", \"bg-secondary\", \"text-white\", \"text-center\", \"font-weight-light\", \"col-md-12\", \"col-xs-12\", \"col-sm-12\", \"col-12\"], [1, \"form-row\"], [\"class\", \"col-md-2 mb-2\", 4, \"ngIf\"], [1, \"col-md-2\", \"mb-2\"], [\"id\", \"typeahead-template\", \"type\", \"text\", \"name\", \"getcustomer\", 1, \"form-control\", \"form-control-sm\", 3, \"ngModelChange\", \"placeholder\", \"ngModel\", \"ngbTypeahead\", \"resultTemplate\", \"inputFormatter\"], [1, \"col-xs-12\", \"col-12\", \"col-md-2\", \"form-group\"], [\"type\", \"text\", \"placeholder\", \"DD/MM/YYYY\", \"bsDatepicker\", \"\", 1, \"form-control\", \"form-control-sm\", \"form-control-md\", 3, \"ngModelChange\", \"bsConfig\", \"ngModel\"], [1, \"col-xs-12\", \"col-12\", \"col-md-1\", \"form-group\"], [\"name\", \"\", \"id\", \"\", 1, \"custom-select\", \"custom-select-sm\", 3, \"change\", \"ngModelChange\", \"ngModel\"], [\"value\", \"111\", 1, \"text-black-50\"], [\"value\", \"1\", 1, \"text-black-50\"], [\"value\", \"2\", 1, \"text-black-50\"], [\"value\", \"3\", 1, \"text-black-50\"], [\"value\", \"4\", 1, \"text-black-50\"], [\"value\", \"5\", 1, \"text-black-50\"], [\"value\", \"6\", 1, \"text-black-50\"], [1, \"custom-select\", \"custom-select-sm\", 3, \"change\", \"ngModelChange\", \"ngModel\"], [\"value\", \"All\", 1, \"text-black-50\"], [\"value\", \"TT\", 1, \"text-black-50\"], [\"value\", \"N01\", 1, \"text-black-50\"], [\"value\", \"N07\", 1, \"text-black-50\"], [\"value\", \"COD\", 1, \"text-black-50\"], [\"value\", \"N15\", 1, \"text-black-50\"], [\"value\", \"N30\", 1, \"text-black-50\"], [\"value\", \"N60\", 1, \"text-black-50\"], [\"value\", \"N90\", 1, \"text-black-50\"], [\"value\", \"N120\", 1, \"text-black-50\"], [1, \"col-md-2\", \"mb-2\", \"col-12\", \"text-center\", \"text-sm-center\", \"text-md-left\", \"text-lg-left\", 2, \"padding-right\", \"0px\", \"padding-left\", \"0px\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", \"btn-sm\", \"font-weight-light\", 2, \"width\", \"60px\", 3, \"click\", \"disabled\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", \"btn-sm\", \"font-weight-light\", 2, \"margin-left\", \"3px\", \"width\", \"68px\", 3, \"click\", \"disabled\"], [1, \"table\", \"table-hover\", \"table-bordered\", \"table-sm\"], [1, \"text-sm-center\", \"bg-light\"], [\"scope\", \"col\", 1, \"font-weight-normal\"], [\"scope\", \"col\", 1, \"font-weight-light\"], [\"class\", \"text-sm-left\", 4, \"ngFor\", \"ngForOf\"], [4, \"ngIf\"], [\"id\", \"mdlSample\", \"role\", \"dialog\", 1, \"modal\", \"fade\", 3, \"ngStyle\"], [1, \"modal-dialog\", \"modal-md\"], [1, \"modal-content\"], [1, \"modal-header\", \"colhaederal\"], [1, \"modal-title\"], [1, \"modal-body\"], [\"align\", \"right\", 1, \"modal-footer\"], [\"type\", \"button\", \"id\", \"btnClose\", 1, \"btn\", \"btn-danger\", 3, \"click\"], [1, \"fa\", \"fa-times\", \"fa-fw\"], [\"id\", \"ViewDetailSaleoder\", \"tabindex\", \"-1\", \"role\", \"dialog\", \"aria-labelledby\", \"exampleModalLabel\", \"aria-hidden\", \"true\", 1, \"modal\", \"fade\", \"bd-example-modal-lg\"], [\"role\", \"document\", 1, \"modal-dialog\", \"modal-lg\"], [1, \"modal-header\"], [\"id\", \"exampleModalLabel\", 1, \"modal-title\"], [\"type\", \"button\", \"data-dismiss\", \"modal\", \"aria-label\", \"Close\", 1, \"close\"], [\"aria-hidden\", \"true\"], [1, \"modal-body\", \"modal-lg\"], [2, \"overflow-x\", \"auto\"], [1, \"text-center\", \"font-weight-normal\"], [1, \"font-weight-normal\"], [\"colspan\", \"3\", 1, \"font-weight-normal\", \"text-center\"], [4, \"ngFor\", \"ngForOf\"], [1, \"modal-footer\"], [\"type\", \"button\", \"data-dismiss\", \"modal\", 1, \"btn\", \"btn-secondary\"], [\"id\", \"DeleteHistory\", \"tabindex\", \"-1\", \"role\", \"dialog\", \"aria-labelledby\", \"exampleModalLabel\", \"aria-hidden\", \"true\", 1, \"modal\", \"fade\", \"bd-example-modal-sm\"], [\"role\", \"document\", 1, \"modal-dialog\", \"modal-sm\"], [1, \"modal-body\", \"modal-sm\"], [1, \"col-xs-12\", \"col-12\", \"col-md-12\", \"form-group\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", 3, \"click\"], [\"multiple\", \"\", 1, \"custom-select\", \"custom-select-sm\", \"custom-select-md\", 3, \"ngModelChange\", \"ngModel\"], [\"selected\", \"\", \"value\", \"\"], [\"value\", \"%20\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [3, \"value\"], [1, \"text-sm-left\"], [1, \"text-sm-center\", \"font-weight-light\"], [1, \"text-sm-center\", \"font-weight-normal\"], [1, \"text-sm-center\", \"font-weight-normal\", 3, \"tooltip\"], [1, \"text-sm-right\", \"font-weight-normal\"], [\"class\", \" text-sm-center font-weight-normal\", 3, \"ngStyle\", 4, \"ngIf\"], [1, \"btn\", \"btn-link\", \"font-weight-normal\", 2, \"padding\", \"0pt\", 3, \"click\"], [1, \"text-sm-center\", \"font-weight-normal\", 3, \"ngStyle\"], [\"colspan\", \"18\", 1, \"text-center\", \"font-weight-normal\", 2, \"text-align\", \"center\"], [1, \"modal-title\", \"pull-left\"], [\"type\", \"button\", \"aria-label\", \"Close\", 1, \"close\", \"pull-right\", 3, \"click\"], [\"heading\", \"Sale Oder list\", 2, \"padding-top\", \"5px\"], [\"type\", \"button\", 1, \"btn\", \"btn-secondary\", 3, \"click\"], [1, \"text-center\", \"font-weight-sm\"], [1, \"text-left\", \"font-weight-sm\"], [1, \"text-right\", \"font-weight-sm\"], [1, \"text-right\", \"font-weight-sm\", 3, \"ngStyle\"]],\n        template: function SalestatusComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            const _r1 = i0.ɵɵgetCurrentView();\n            i0.ɵɵelement(0, \"app-topmenu\");\n            i0.ɵɵelementStart(1, \"section\", 3)(2, \"div\", 4)(3, \"h5\", 5);\n            i0.ɵɵtext(4, \"Sales Order Status\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(5, \"div\", 6);\n            i0.ɵɵtemplate(6, SalestatusComponent_div_6_Template, 7, 2, \"div\", 7);\n            i0.ɵɵelementStart(7, \"div\", 8);\n            i0.ɵɵtemplate(8, SalestatusComponent_ng_template_8_Template, 3, 2, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n            i0.ɵɵelementStart(10, \"input\", 9);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function SalestatusComponent_Template_input_ngModelChange_10_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.getcustomer, $event) || (ctx.getcustomer = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(11, \"div\", 10)(12, \"input\", 11);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function SalestatusComponent_Template_input_ngModelChange_12_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.Datafromdate, $event) || (ctx.Datafromdate = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(13, \"div\", 10)(14, \"input\", 11);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function SalestatusComponent_Template_input_ngModelChange_14_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.Datatodate, $event) || (ctx.Datatodate = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(15, \"div\", 12)(16, \"select\", 13);\n            i0.ɵɵlistener(\"change\", function SalestatusComponent_Template_select_change_16_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.searchselect());\n            });\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function SalestatusComponent_Template_select_ngModelChange_16_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.typesync, $event) || (ctx.typesync = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementStart(17, \"option\", 14);\n            i0.ɵɵtext(18, \"\\u0E17\\u0E38\\u0E01\\u0E23\\u0E32\\u0E22\\u0E01\\u0E32\\u0E23\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(19, \"option\", 15);\n            i0.ɵɵtext(20, \"Openorder\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(21, \"option\", 16);\n            i0.ɵɵtext(22, \"Delivered\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(23, \"option\", 17);\n            i0.ɵɵtext(24, \"Invoiced\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(25, \"option\", 18);\n            i0.ɵɵtext(26, \"Canceled\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(27, \"option\", 19);\n            i0.ɵɵtext(28, \"Order Draft\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(29, \"option\", 20);\n            i0.ɵɵtext(30, \"\\u0E23\\u0E2D\\u0E40\\u0E02\\u0E49\\u0E32\\u0E23\\u0E30\\u0E1A\\u0E1A AX\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(31, \"div\", 12)(32, \"select\", 21);\n            i0.ɵɵlistener(\"change\", function SalestatusComponent_Template_select_change_32_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.searchpaymenttype());\n            });\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function SalestatusComponent_Template_select_ngModelChange_32_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.paymenttype, $event) || (ctx.paymenttype = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementStart(33, \"option\", 22);\n            i0.ɵɵtext(34, \"\\u0E17\\u0E38\\u0E01\\u0E23\\u0E32\\u0E22\\u0E01\\u0E32\\u0E23\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(35, \"option\", 23);\n            i0.ɵɵtext(36, \"TT\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(37, \"option\", 24);\n            i0.ɵɵtext(38, \"N01\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(39, \"option\", 25);\n            i0.ɵɵtext(40, \"N07\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(41, \"option\", 26);\n            i0.ɵɵtext(42, \"COD\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(43, \"option\", 27);\n            i0.ɵɵtext(44, \"N15\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(45, \"option\", 28);\n            i0.ɵɵtext(46, \"N30\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(47, \"option\", 29);\n            i0.ɵɵtext(48, \"N60\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(49, \"option\", 30);\n            i0.ɵɵtext(50, \"N90\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(51, \"option\", 31);\n            i0.ɵɵtext(52, \"N120\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(53, \"div\", 32)(54, \"button\", 33);\n            i0.ɵɵlistener(\"click\", function SalestatusComponent_Template_button_click_54_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.searchsohistory());\n            });\n            i0.ɵɵtext(55, \"Search\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(56, \"button\", 34);\n            i0.ɵɵlistener(\"click\", function SalestatusComponent_Template_button_click_56_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.exportdataexcel());\n            });\n            i0.ɵɵtext(57, \"Export\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(58, \"table\", 35)(59, \"thead\")(60, \"tr\", 36)(61, \"th\", 37);\n            i0.ɵɵtext(62, \"Item\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(63, \"th\", 38);\n            i0.ɵɵtext(64, \"\\u0E40\\u0E25\\u0E02\\u0E17\\u0E35\\u0E48 SO\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(65, \"th\", 38);\n            i0.ɵɵtext(66, \"\\u0E40\\u0E25\\u0E02\\u0E17\\u0E35\\u0E48 SO AX\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(67, \"th\", 37);\n            i0.ɵɵtext(68, \"\\u0E27\\u0E31\\u0E19\\u0E17\\u0E35\\u0E48\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(69, \"th\", 37);\n            i0.ɵɵtext(70, \"\\u0E40\\u0E27\\u0E25\\u0E32\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(71, \"th\", 37);\n            i0.ɵɵtext(72, \"\\u0E1E\\u0E19\\u0E31\\u0E01\\u0E07\\u0E32\\u0E19\\u0E02\\u0E32\\u0E22\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(73, \"th\", 37);\n            i0.ɵɵtext(74, \"\\u0E25\\u0E39\\u0E01\\u0E04\\u0E49\\u0E32\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(75, \"th\", 37);\n            i0.ɵɵtext(76, \"\\u0E0A\\u0E37\\u0E48\\u0E2D INV\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(77, \"th\", 37);\n            i0.ɵɵtext(78, \"\\u0E0A\\u0E37\\u0E48\\u0E2D\\u0E02\\u0E19\\u0E2A\\u0E48\\u0E07\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(79, \"th\", 37);\n            i0.ɵɵtext(80, \"\\u0E21\\u0E39\\u0E25\\u0E04\\u0E48\\u0E32\\u0E2A\\u0E38\\u0E17\\u0E18\\u0E34\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(81, \"th\", 37);\n            i0.ɵɵtext(82, \"VAT/No VAT\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(83, \"th\", 37);\n            i0.ɵɵtext(84, \"\\u0E19\\u0E49\\u0E33\\u0E2B\\u0E19\\u0E31\\u0E01 \\u0E23\\u0E27\\u0E21\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(85, \"th\", 37);\n            i0.ɵɵtext(86, \"\\u0E1B\\u0E23\\u0E30\\u0E40\\u0E20\\u0E17\\u0E02\\u0E19\\u0E2A\\u0E48\\u0E07\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(87, \"th\", 37);\n            i0.ɵɵtext(88, \"\\u0E40\\u0E07\\u0E34\\u0E19\\u0E2A\\u0E14/\\u0E40\\u0E04\\u0E23\\u0E14\\u0E34\\u0E15\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(89, \"th\", 37);\n            i0.ɵɵtext(90, \"Note \\u0E20\\u0E32\\u0E22\\u0E43\\u0E19\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(91, \"th\", 37);\n            i0.ɵɵtext(92, \"\\u0E2B\\u0E21\\u0E32\\u0E22\\u0E40\\u0E2B\\u0E15\\u0E38\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(93, \"th\", 37);\n            i0.ɵɵtext(94, \"\\u0E2A\\u0E16\\u0E32\\u0E19\\u0E30\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(95, \"th\", 37);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(96, \"tbody\");\n            i0.ɵɵtemplate(97, SalestatusComponent_tr_97_Template, 43, 35, \"tr\", 39)(98, SalestatusComponent_tr_98_Template, 3, 0, \"tr\", 40);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(99, \"div\", 41)(100, \"div\", 42)(101, \"div\", 43)(102, \"div\", 44)(103, \"h4\", 45);\n            i0.ɵɵtext(104, \"Report\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(105, \"div\", 46);\n            i0.ɵɵtext(106);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(107, \"div\", 47)(108, \"button\", 48);\n            i0.ɵɵlistener(\"click\", function SalestatusComponent_Template_button_click_108_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.closemodel(false));\n            });\n            i0.ɵɵelement(109, \"i\", 49);\n            i0.ɵɵtext(110, \" \\u0E1B\\u0E34\\u0E14\");\n            i0.ɵɵelementEnd()()()()();\n            i0.ɵɵelementStart(111, \"body\")(112, \"div\", 50)(113, \"div\", 51)(114, \"div\", 43)(115, \"div\", 52)(116, \"h5\", 53);\n            i0.ɵɵtext(117, \"Sale Oder Detail\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(118, \"button\", 54)(119, \"span\", 55);\n            i0.ɵɵtext(120, \"\\u00D7\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(121, \"div\", 56)(122, \"div\", 57)(123, \"table\", 35)(124, \"thead\")(125, \"tr\")(126, \"th\", 58);\n            i0.ɵɵtext(127, \"\\u0E23\\u0E2B\\u0E31\\u0E2A\\u0E2A\\u0E34\\u0E19\\u0E04\\u0E49\\u0E32\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(128, \"th\", 58);\n            i0.ɵɵtext(129, \"\\u0E0A\\u0E37\\u0E48\\u0E2D\\u0E2A\\u0E34\\u0E19\\u0E04\\u0E49\\u0E32\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(130, \"th\", 59);\n            i0.ɵɵtext(131, \"Pack\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(132, \"th\", 59);\n            i0.ɵɵtext(133, \"\\u0E08\\u0E33\\u0E19\\u0E27\\u0E19\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(134, \"th\", 59);\n            i0.ɵɵtext(135, \"\\u0E04\\u0E49\\u0E32\\u0E07\\u0E2A\\u0E48\\u0E07\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(136, \"th\", 59);\n            i0.ɵɵtext(137, \"\\u0E2A\\u0E48\\u0E07\\u0E41\\u0E25\\u0E49\\u0E27\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(138, \"th\", 59);\n            i0.ɵɵtext(139, \"\\u0E19\\u0E49\\u0E33\\u0E2B\\u0E19\\u0E31\\u0E01\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(140, \"th\", 59);\n            i0.ɵɵtext(141, \"\\u0E23\\u0E32\\u0E04\\u0E32\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(142, \"th\", 60);\n            i0.ɵɵtext(143, \"\\u0E2A\\u0E48\\u0E27\\u0E19\\u0E25\\u0E14\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(144, \"th\", 59);\n            i0.ɵɵtext(145, \"\\u0E21\\u0E39\\u0E25\\u0E04\\u0E48\\u0E32\\u0E2A\\u0E38\\u0E17\\u0E18\\u0E34\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(146, \"tbody\");\n            i0.ɵɵtemplate(147, SalestatusComponent_tr_147_Template, 1, 0, \"tr\", 61);\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(148, \"div\", 62)(149, \"button\", 63);\n            i0.ɵɵtext(150, \"Close\");\n            i0.ɵɵelementEnd()()()()();\n            i0.ɵɵelementStart(151, \"div\", 64)(152, \"div\", 65)(153, \"div\", 43)(154, \"div\", 52)(155, \"h5\", 53);\n            i0.ɵɵtext(156, \"DeleteHistory\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(157, \"button\", 54)(158, \"span\", 55);\n            i0.ɵɵtext(159, \"\\u00D7\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(160, \"div\", 66)(161, \"div\", 67)(162, \"input\", 11);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function SalestatusComponent_Template_input_ngModelChange_162_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.Deletefromdate, $event) || (ctx.Deletefromdate = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(163, \"div\", 67)(164, \"input\", 11);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function SalestatusComponent_Template_input_ngModelChange_164_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.Deletetodate, $event) || (ctx.Deletetodate = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(165, \"div\", 62)(166, \"button\", 63);\n            i0.ɵɵtext(167, \"Close\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(168, \"button\", 68);\n            i0.ɵɵlistener(\"click\", function SalestatusComponent_Template_button_click_168_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.deletehistory());\n            });\n            i0.ɵɵtext(169, \"Delete\");\n            i0.ɵɵelementEnd()()()()()()();\n            i0.ɵɵtemplate(170, SalestatusComponent_ng_template_170_Template, 43, 2, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n          }\n          if (rf & 2) {\n            const rt_r12 = i0.ɵɵreference(9);\n            i0.ɵɵadvance(6);\n            i0.ɵɵproperty(\"ngIf\", ctx.testclose);\n            i0.ɵɵadvance(4);\n            i0.ɵɵpropertyInterpolate(\"placeholder\", ctx.company);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.getcustomer);\n            i0.ɵɵproperty(\"ngbTypeahead\", ctx.search)(\"resultTemplate\", rt_r12)(\"inputFormatter\", ctx.formatter);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"bsConfig\", i0.ɵɵpureFunction0(23, _c0));\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.Datafromdate);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"bsConfig\", i0.ɵɵpureFunction0(24, _c0));\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.Datatodate);\n            i0.ɵɵadvance(2);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.typesync);\n            i0.ɵɵadvance(16);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.paymenttype);\n            i0.ɵɵadvance(22);\n            i0.ɵɵproperty(\"disabled\", ctx.searchbtn);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"disabled\", ctx.exportbtn);\n            i0.ɵɵadvance(41);\n            i0.ɵɵproperty(\"ngForOf\", ctx.sohitorylistfilter);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.Ckviewsohitorylistfilter);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(25, _c1, ctx.mdlSampleIsOpen ? \"block\" : \"none\"));\n            i0.ɵɵadvance(7);\n            i0.ɵɵtextInterpolate(ctx.alt);\n            i0.ɵɵadvance(41);\n            i0.ɵɵproperty(\"ngForOf\", ctx.salelistview);\n            i0.ɵɵadvance(15);\n            i0.ɵɵproperty(\"bsConfig\", i0.ɵɵpureFunction0(27, _c0));\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.Deletefromdate);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"bsConfig\", i0.ɵɵpureFunction0(28, _c0));\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.Deletetodate);\n          }\n        },\n        dependencies: [i6.TooltipDirective, i7.NgForOf, i7.NgIf, i7.NgStyle, i8.NgSelectOption, i8.ɵNgSelectMultipleOption, i8.DefaultValueAccessor, i8.SelectControlValueAccessor, i8.SelectMultipleControlValueAccessor, i8.NgControlStatus, i8.NgModel, i4.NgbTypeahead, i9.BsDatepickerDirective, i9.BsDatepickerInputDirective, i10.TabDirective, i10.TabsetComponent, i11.TopmenuComponent, i7.DecimalPipe, i7.DatePipe]\n      });\n    }\n  }\n  return SalestatusComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}