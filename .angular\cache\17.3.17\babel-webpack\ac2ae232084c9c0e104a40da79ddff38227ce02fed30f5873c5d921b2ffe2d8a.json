{"ast": null, "code": "import { HttpClient } from '@angular/common/http';\nimport { Angular5Csv } from 'angular5-csv/dist/Angular5-csv';\nimport { WebapiService } from '../webapi.service';\nimport { Router } from '@angular/router';\nimport { debounceTime, map } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"../webapi.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"../../../node_modules/@angular/forms/index\";\nimport * as i6 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i7 from \"../topmenu/topmenu.component\";\nconst _c0 = a0 => ({\n  \"display\": a0,\n  \"opacity\": 1\n});\nfunction CustomerlistComponent_div_6_option_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 37);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r4 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", item_r4.groupid);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\" \", item_r4.groupid, \" (\", item_r4.name, \") \");\n  }\n}\nfunction CustomerlistComponent_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 10)(1, \"select\", 34);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function CustomerlistComponent_div_6_Template_select_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.Salegroup, $event) || (ctx_r2.Salegroup = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementStart(2, \"option\", 35);\n    i0.ɵɵtext(3, \"--\\u0E40\\u0E25\\u0E37\\u0E2D\\u0E01\\u0E23\\u0E2B\\u0E31\\u0E2A\\u0E1E\\u0E19\\u0E31\\u0E01\\u0E07\\u0E32\\u0E19\\u0E02\\u0E32\\u0E22--\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, CustomerlistComponent_div_6_option_4_Template, 2, 3, \"option\", 36);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.Salegroup);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.DateGroupsaleman);\n  }\n}\nfunction CustomerlistComponent_ng_template_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"label\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const r_r5 = ctx.result;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"\", r_r5.name, \" (\", r_r5.accountnum, \")\");\n  }\n}\nfunction CustomerlistComponent_ng_template_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"label\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const r_r6 = ctx.result;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"\", r_r6.name, \" (\", r_r6.accountnum, \")\");\n  }\n}\nfunction CustomerlistComponent_ng_template_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"label\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const r_r7 = ctx.result;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(r_r7.address);\n  }\n}\nfunction CustomerlistComponent_ng_template_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"b\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(2, \"!\");\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r2.name);\n  }\n}\nfunction CustomerlistComponent_tr_49_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 38);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\", 38);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\", 38);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\", 38);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\", 39);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"td\", 39);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"td\", 39);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r9 = ctx.$implicit;\n    const i_r10 = ctx.index;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i_r10 + 1);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r9.custgroup);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r9.accountnum);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r9.paymtermid);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r9.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r9.invoiceaddress);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r9.deliveryaddress);\n  }\n}\nexport let CustomerlistComponent = /*#__PURE__*/(() => {\n  class CustomerlistComponent {\n    constructor(http, service, router) {\n      this.http = http;\n      this.service = service;\n      this.router = router;\n      this.mdlSampleIsOpen = false;\n      this.alt = '';\n      this.checkreload = true;\n      this.options = {\n        fieldSeparator: ',',\n        quoteStrings: '\"',\n        decimalseparator: '.',\n        showLabels: true,\n        showTitle: true,\n        useBom: true,\n        noDownload: false,\n        headers: [\"กลุ่มลูกค้า\", \"รหัสลูกค้า\", \"ชื่อลูกค้า\", \"ที่อยู่วางบิลหลัก\", \"ที่อยู่ส่งสินค้าหลัก\", \"รหัสผู้ขาย\"]\n      };\n      this.Salegroup = '';\n      this.groupcustomer = '';\n      this.codecustomer = '';\n      this.namecustomer = '';\n      this.addressblin = '';\n      this.numitem = 0;\n      this.toolsssop = true;\n      this.welcame = false;\n      this.Name = [];\n      this.DateGroupsaleman = [];\n      this.chackuser = false;\n      this.CodeSo = '';\n      this.permisstiondata = [];\n      this.exportbtn = true;\n      this.searchbtn = true;\n      this.customers = [];\n      this.customeraddressauto = [];\n      this.companycode = 'ค้นหารหัสลูกค้า';\n      this.companyname = 'ค้นหาชื่อลูกค้า';\n      this.enablecustomer = false;\n      this.search = text$ =>\n      //Autocomplete ลูกค้า\n      text$.pipe(debounceTime(200), map(term => term === '' ? [] : this.customers.filter(v => v.name.toLowerCase().indexOf(term.toLowerCase()) > -1 || v.accountnum.toLowerCase().indexOf(term.toLowerCase()) > -1).slice(0, 50)));\n      this.formatter = x => x.name;\n      this.searchcode = text$ =>\n      //Autocomplete ลูกค้า\n      text$.pipe(debounceTime(200), map(term => term === '' ? [] : this.customers.filter(v => v.name.toLowerCase().indexOf(term.toLowerCase()) > -1 || v.accountnum.toLowerCase().indexOf(term.toLowerCase()) > -1).slice(0, 50)));\n      this.formattercode = x => x.accountnum;\n      this.searchaddress = text$ =>\n      //Autocomplete ลูกค้า\n      text$.pipe(debounceTime(200), map(term => term === '' ? [] : this.customeraddressauto.filter(v => v.address.toLowerCase().indexOf(term.toLowerCase()) > -1).slice(0, 50)));\n      this.formatteraddress = x => x.address;\n      localStorage.removeItem('DataSOderreview');\n      localStorage.removeItem('DataSOderlist');\n      this.name = 'กำลังนำเข้าข้อมูลกรุณารอสักครู่.....';\n      this.url = this.service.urlApi;\n      this.Name = JSON.parse(sessionStorage.getItem('login'));\n      /* alert(this.service.setalert());*/\n      this.datalogin = JSON.parse(sessionStorage.getItem('login'));\n      if (this.datalogin == null) {\n        this.router.navigate(['login']);\n      } else {\n        this.groupsale = this.Name[0].salegroup;\n        this.permisstiondata = JSON.parse(sessionStorage.getItem('menu'));\n        this.exportbtn = !this.permisstiondata[0].flag_print;\n        this.searchbtn = !this.permisstiondata[0].flag_action;\n      }\n      /* this.openModal(true,'ยินดีต้อนรับ',false);*/\n    }\n    ngOnInit() {\n      this.getcostomerauto();\n      var idgroup = '';\n      if (this.groupsale == 'admin') {\n        idgroup = '%20';\n      } else {\n        idgroup = this.groupsale;\n      }\n      this.Interval = setInterval(() => this.Searchcustomer(idgroup), 500);\n      if (this.groupsale == 'admin') {\n        this.chackuser = true;\n      }\n    }\n    toggleWithGreeting(tooltip, greeting) {\n      if (tooltip.isOpen()) {\n        tooltip.close();\n      } else {\n        tooltip.open({\n          greeting\n        });\n      }\n    }\n    closetooltip() {\n      this.toolsssop = false;\n    }\n    getgroupsaleman() {\n      this.DateGroupsaleman = [];\n      this.http.get(this.url + 'salesman').subscribe(res => {\n        if (res.length > 0) {\n          this.DateGroupsaleman = res;\n        } else {\n          alert('ไม่สามารถติดต่อข้อมูลรหัส saleman ได้');\n        }\n      });\n    }\n    syncdatacustomer(tool) {\n      this.toggleWithGreeting(tool, '');\n      const Http = new XMLHttpRequest();\n      // const url='syncso/Service.asmx/PullingData';\n      const url = 'syncso/Service.asmx/PullingData?iFile=customer';\n      Http.open(\"GET\", url);\n      Http.send();\n      // Http.open(\"POST\", url);\n      // Http.setRequestHeader(\"Content-Type\", \"application/json;charset=UTF-8\");\n      // Http.send(JSON.stringify({ \"iFile\": \"customer\" }));\n      Http.onreadystatechange = e => {\n        if (Http.readyState == 4 && Http.status == 200) {\n          this.name = 'นำเข้าข้อมูล เสร็จสิ้น';\n          if (confirm('นำเข้าข้อมูล เสร็จสิ้น')) {\n            this.toggleWithGreeting(tool, '');\n          } else {\n            this.toggleWithGreeting(tool, '');\n          }\n        }\n      };\n    }\n    getcostomerauto() {\n      var idsale = this.Name[0].salegroup;\n      if (this.Name[0].salegroup === 'admin') {\n        idsale = '%20';\n      } else {\n        idsale = this.Name[0].salegroup;\n      }\n      this.http.get(this.url + 'customerauto/' + idsale).subscribe(res => {\n        this.customers = res;\n        this.getcostomeraddressauto();\n      });\n    }\n    getcostomeraddressauto() {\n      var idsale = this.Name[0].salegroup;\n      if (this.Name[0].salegroup === 'admin') {\n        idsale = '%20';\n      } else {\n        idsale = this.Name[0].salegroup;\n      }\n      this.http.get(this.url + 'customeraddressauto/' + idsale).subscribe(res => {\n        this.customeraddressauto = res;\n      });\n    }\n    Searchcustomer(saleid) {\n      var groupcustomer = '';\n      var codecustomer = '';\n      var namecustomer = '';\n      var addressblin = '';\n      if (this.groupcustomer == '') {\n        groupcustomer = '%20';\n      }\n      if (this.codecustomer == '') {\n        codecustomer = '%20';\n      }\n      if (this.namecustomer == '') {\n        namecustomer = '%20';\n      }\n      if (this.addressblin == '') {\n        addressblin = '%20';\n      }\n      this.customerlists = [];\n      this.http.get(this.url + 'customer_list/' + saleid + '/' + codecustomer + '/' + namecustomer + '/' + addressblin + '/' + groupcustomer).subscribe(res => {\n        if (res.length > 0) {\n          this.getgroupsaleman();\n          this.customerlists = res;\n          if (codecustomer == '%20') {\n            this.codecustomer = '';\n          }\n          if (namecustomer == '%20') {\n            this.namecustomer = '';\n          }\n          if (addressblin == '%20') {\n            this.addressblin = '';\n          }\n          if (groupcustomer == '%20') {\n            this.groupcustomer = '';\n          }\n        } else {\n          this.getgroupsaleman();\n          if (codecustomer == '%20') {\n            this.codecustomer = '';\n          }\n          if (namecustomer == '%20') {\n            this.namecustomer = '';\n          }\n          if (addressblin == '%20') {\n            this.addressblin = '';\n          }\n          if (groupcustomer == '%20') {\n            this.groupcustomer = '';\n          }\n          this.openModal(true, 'ไม่พบข้อมูลที่ค้นหา', false);\n        }\n        clearInterval(this.Interval);\n      });\n    }\n    Searchcustomerclick() {\n      //alert(JSON.stringify('code/'+JSON.stringify(this.getcustomercode))+'  name/'+JSON.stringify(this.getcustomername));\n      var datacodeSo = '';\n      var groupcustomer = '';\n      var codecustomer = '';\n      var namecustomer = '';\n      var addressblin = '';\n      if (this.getcustomeraddress == undefined || this.getcustomeraddress == '') {\n        this.addressblin = '';\n      } else {\n        this.addressblin = this.getcustomeraddress.locationno;\n      }\n      if (this.getcustomercode == undefined || this.getcustomercode == '') {\n        this.codecustomer = '';\n      } else {\n        this.codecustomer = this.getcustomercode.accountnum;\n      }\n      if (this.getcustomername == undefined || this.getcustomername == '') {\n        this.namecustomer = '';\n      } else {\n        this.namecustomer = this.getcustomername.name;\n      }\n      if (this.groupcustomer == '') {\n        groupcustomer = '%20';\n      } else {\n        groupcustomer = this.groupcustomer;\n      }\n      if (this.codecustomer == '') {\n        codecustomer = '%20';\n      } else {\n        codecustomer = this.codecustomer;\n      }\n      if (this.namecustomer == '') {\n        namecustomer = '%20';\n      } else {\n        namecustomer = this.namecustomer;\n      }\n      if (this.addressblin == '') {\n        addressblin = '%20';\n      } else {\n        addressblin = this.addressblin;\n      }\n      if (this.Salegroup == '') {\n        if (this.Name[0].salegroup == 'admin') {\n          datacodeSo = `${this.Salegroup}`;\n        } else {\n          datacodeSo = `${this.Name[0].salegroup}`;\n        }\n      }\n      if (this.Salegroup !== '') {\n        if (this.Name[0].salegroup == 'admin') {\n          datacodeSo = `${this.Salegroup}`;\n        } else {\n          datacodeSo = `${this.Name[0].salegroup}`;\n        }\n      }\n      if (datacodeSo == '') {\n        datacodeSo = '%20';\n      }\n      this.customerlists = [];\n      this.http.get(this.url + 'customer_list/' + datacodeSo + '/' + codecustomer + '/' + namecustomer + '/' + addressblin + '/' + groupcustomer).subscribe(res => {\n        if (res.length > 0) {\n          this.customerlists = res;\n          if (codecustomer == '%20') {\n            this.codecustomer = '';\n          }\n          if (namecustomer == '%20') {\n            this.namecustomer = '';\n          }\n          if (addressblin == '%20') {\n            this.addressblin = '';\n          }\n          if (groupcustomer == '%20') {\n            this.groupcustomer = '';\n          }\n        } else {\n          if (codecustomer == '%20') {\n            this.codecustomer = '';\n          }\n          if (namecustomer == '%20') {\n            this.namecustomer = '';\n          }\n          if (addressblin == '%20') {\n            this.addressblin = '';\n          }\n          if (groupcustomer == '%20') {\n            this.groupcustomer = '';\n          }\n          this.openModal(true, 'ไม่พบข้อมูลที่ค้นหา', false);\n        }\n      });\n    }\n    exportdataexcel() {\n      if (this.customerlists == undefined) {\n        this.openModal(true, 'ไม่พบข้อมูล', false);\n      } else {\n        new Angular5Csv(this.customerlists, 'CustomerList', this.options);\n      }\n    }\n    openModal(open, text, load) {\n      this.mdlSampleIsOpen = open;\n      this.alt = text;\n      this.checkreload = load;\n    }\n    closemodel(cl) {\n      this.mdlSampleIsOpen = cl;\n      if (this.checkreload == true) {\n        /* location.reload();*/\n      }\n    }\n    static {\n      this.ɵfac = function CustomerlistComponent_Factory(t) {\n        return new (t || CustomerlistComponent)(i0.ɵɵdirectiveInject(i1.HttpClient), i0.ɵɵdirectiveInject(i2.WebapiService), i0.ɵɵdirectiveInject(i3.Router));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: CustomerlistComponent,\n        selectors: [[\"app-customerlist\"]],\n        decls: 62,\n        vars: 25,\n        consts: [[\"rtcode\", \"\"], [\"rt\", \"\"], [\"rtaddress\", \"\"], [\"tipContent\", \"\"], [\"t2\", \"ngbTooltip\"], [1, \"container-fluid\", 2, \"padding-right\", \"5px\", \"padding-left\", \"5px\"], [2, \"padding-top\", \"60px\"], [1, \"p-sm-1\", \"bg-secondary\", \"text-white\", \"text-center\"], [1, \"form-row\"], [\"class\", \"col-md-2 mb-2\", 4, \"ngIf\"], [1, \"col-md-2\", \"mb-2\"], [\"id\", \"typeahead-template\", \"type\", \"text\", \"name\", \"getcustomercode\", 1, \"form-control\", \"form-control-sm\", 3, \"ngModelChange\", \"placeholder\", \"ngModel\", \"ngbTypeahead\", \"resultTemplate\", \"inputFormatter\"], [\"id\", \"typeahead-template\", \"type\", \"text\", \"name\", \"getcustomername\", 1, \"form-control\", \"form-control-sm\", 3, \"ngModelChange\", \"placeholder\", \"ngModel\", \"ngbTypeahead\", \"resultTemplate\", \"inputFormatter\"], [\"id\", \"typeahead-template\", \"placeholder\", \"\\u0E17\\u0E35\\u0E48\\u0E2D\\u0E22\\u0E39\\u0E48\\u0E27\\u0E32\\u0E07\\u0E1A\\u0E34\\u0E25\\u0E2B\\u0E25\\u0E31\\u0E01\", \"type\", \"text\", \"name\", \"getcustomeraddress\", 1, \"form-control\", \"form-control-sm\", 3, \"ngModelChange\", \"ngModel\", \"ngbTypeahead\", \"resultTemplate\", \"inputFormatter\"], [\"id\", \"groupcustomer\", \"type\", \"text\", \"name\", \"groupcustomer\", \"placeholder\", \"\\u0E04\\u0E49\\u0E19\\u0E2B\\u0E32\\u0E01\\u0E25\\u0E38\\u0E48\\u0E21\\u0E25\\u0E39\\u0E01\\u0E04\\u0E49\\u0E32\", 1, \"form-control\", \"form-control-sm\", 3, \"ngModelChange\", \"ngModel\"], [1, \"col-md-2\", \"mb-3\", \"col-12\", \"text-center\", \"text-sm-center\", \"text-md-center\", \"text-lg-left\", 2, \"padding-right\", \"0px\", \"padding-left\", \"0px\"], [1, \"btn\", \"btn-primary\", \"btn-sm\", \"font-weight-light\", 2, \"width\", \"60px\", 3, \"click\", \"disabled\"], [1, \"btn\", \"btn-primary\", \"btn-sm\", \"font-weight-light\", 2, \"margin-left\", \"3px\", \"width\", \"60px\", 3, \"disabled\"], [\"triggers\", \"manual\", 1, \"btn\", \"btn-primary\", \"btn-sm\", \"font-weight-light\", 2, \"margin-left\", \"3px\", \"width\", \"60px\", 3, \"click\", \"disabled\", \"ngbTooltip\"], [1, \"table\", \"table-hover\", \"table-bordered\", \"table-sm\"], [1, \"text-sm-center\", \"font-weight-light\", \"bg-light\"], [\"scope\", \"col\", 1, \"font-weight-normal\", \"text-center\", 2, \"width\", \"55px\"], [\"scope\", \"col\", 1, \"font-weight-normal\", \"text-center\", 2, \"width\", \"90px\"], [\"scope\", \"col\", 1, \"font-weight-normal\", \"text-center\"], [4, \"ngFor\", \"ngForOf\"], [\"id\", \"mdlSample\", \"role\", \"dialog\", 1, \"modal\", \"fade\", 3, \"ngStyle\"], [1, \"modal-dialog\", \"modal-md\"], [1, \"modal-content\"], [1, \"modal-header\", \"colhaederal\"], [1, \"modal-title\"], [1, \"modal-body\"], [\"align\", \"right\", 1, \"modal-footer\"], [\"type\", \"button\", \"id\", \"btnClose\", 1, \"btn\", \"btn-danger\", 3, \"click\"], [1, \"fa\", \"fa-times\", \"fa-fw\"], [\"multiple\", \"\", 1, \"custom-select\", \"custom-select-sm\", 3, \"ngModelChange\", \"ngModel\"], [\"selected\", \"\", \"disabled\", \"\", \"value\", \"\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [3, \"value\"], [1, \"text-center\", \"font-weight-normal\"], [1, \"font-weight-normal\"]],\n        template: function CustomerlistComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            const _r1 = i0.ɵɵgetCurrentView();\n            i0.ɵɵelement(0, \"app-topmenu\");\n            i0.ɵɵelementStart(1, \"div\", 5)(2, \"section\", 6)(3, \"h5\", 7);\n            i0.ɵɵtext(4, \"Customer List\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(5, \"div\", 8);\n            i0.ɵɵtemplate(6, CustomerlistComponent_div_6_Template, 5, 2, \"div\", 9);\n            i0.ɵɵelementStart(7, \"div\", 10);\n            i0.ɵɵtemplate(8, CustomerlistComponent_ng_template_8_Template, 3, 2, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n            i0.ɵɵelementStart(10, \"input\", 11);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function CustomerlistComponent_Template_input_ngModelChange_10_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.getcustomercode, $event) || (ctx.getcustomercode = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(11, \"div\", 10);\n            i0.ɵɵtemplate(12, CustomerlistComponent_ng_template_12_Template, 3, 2, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n            i0.ɵɵelementStart(14, \"input\", 12);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function CustomerlistComponent_Template_input_ngModelChange_14_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.getcustomername, $event) || (ctx.getcustomername = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(15, \"div\", 10);\n            i0.ɵɵtemplate(16, CustomerlistComponent_ng_template_16_Template, 3, 1, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor);\n            i0.ɵɵelementStart(18, \"input\", 13);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function CustomerlistComponent_Template_input_ngModelChange_18_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.getcustomeraddress, $event) || (ctx.getcustomeraddress = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(19, \"div\", 10)(20, \"input\", 14);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function CustomerlistComponent_Template_input_ngModelChange_20_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.groupcustomer, $event) || (ctx.groupcustomer = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(21, \"div\", 15)(22, \"button\", 16);\n            i0.ɵɵlistener(\"click\", function CustomerlistComponent_Template_button_click_22_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.Searchcustomerclick());\n            });\n            i0.ɵɵtext(23, \"Search\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(24, \"button\", 17);\n            i0.ɵɵtext(25, \"Export\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(26, CustomerlistComponent_ng_template_26_Template, 3, 1, \"ng-template\", null, 3, i0.ɵɵtemplateRefExtractor);\n            i0.ɵɵelementStart(28, \"button\", 18, 4);\n            i0.ɵɵlistener(\"click\", function CustomerlistComponent_Template_button_click_28_listener() {\n              i0.ɵɵrestoreView(_r1);\n              const t2_r8 = i0.ɵɵreference(29);\n              return i0.ɵɵresetView(ctx.syncdatacustomer(t2_r8));\n            });\n            i0.ɵɵtext(30, \"Import\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(31, \"table\", 19)(32, \"thead\")(33, \"tr\", 20)(34, \"th\", 21);\n            i0.ɵɵtext(35, \"Item\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(36, \"th\", 22);\n            i0.ɵɵtext(37, \"\\u0E01\\u0E25\\u0E38\\u0E48\\u0E21\\u0E25\\u0E39\\u0E01\\u0E04\\u0E49\\u0E32\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(38, \"th\", 22);\n            i0.ɵɵtext(39, \"\\u0E23\\u0E2B\\u0E31\\u0E2A\\u0E25\\u0E39\\u0E01\\u0E04\\u0E49\\u0E32\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(40, \"th\", 22);\n            i0.ɵɵtext(41, \"\\u0E1B\\u0E23\\u0E30\\u0E40\\u0E20\\u0E17\\u0E01\\u0E32\\u0E23\\u0E0A\\u0E33\\u0E23\\u0E30\\u0E40\\u0E07\\u0E34\\u0E19\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(42, \"th\", 23);\n            i0.ɵɵtext(43, \"\\u0E0A\\u0E37\\u0E48\\u0E2D\\u0E25\\u0E39\\u0E01\\u0E04\\u0E49\\u0E32\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(44, \"th\", 23);\n            i0.ɵɵtext(45, \"\\u0E17\\u0E35\\u0E48\\u0E2D\\u0E22\\u0E39\\u0E48\\u0E27\\u0E32\\u0E07\\u0E1A\\u0E34\\u0E25\\u0E2B\\u0E25\\u0E31\\u0E01\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(46, \"th\", 23);\n            i0.ɵɵtext(47, \"\\u0E17\\u0E35\\u0E48\\u0E2D\\u0E22\\u0E39\\u0E48\\u0E2A\\u0E48\\u0E07\\u0E2A\\u0E34\\u0E19\\u0E04\\u0E49\\u0E32\\u0E2B\\u0E25\\u0E31\\u0E01\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(48, \"tbody\");\n            i0.ɵɵtemplate(49, CustomerlistComponent_tr_49_Template, 15, 7, \"tr\", 24);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(50, \"div\", 25)(51, \"div\", 26)(52, \"div\", 27)(53, \"div\", 28)(54, \"h4\", 29);\n            i0.ɵɵtext(55, \"Report\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(56, \"div\", 30);\n            i0.ɵɵtext(57);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(58, \"div\", 31)(59, \"button\", 32);\n            i0.ɵɵlistener(\"click\", function CustomerlistComponent_Template_button_click_59_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.closemodel(false));\n            });\n            i0.ɵɵelement(60, \"i\", 33);\n            i0.ɵɵtext(61, \" \\u0E1B\\u0E34\\u0E14\");\n            i0.ɵɵelementEnd()()()()()()();\n          }\n          if (rf & 2) {\n            const rtcode_r11 = i0.ɵɵreference(9);\n            const rt_r12 = i0.ɵɵreference(13);\n            const rtaddress_r13 = i0.ɵɵreference(17);\n            const tipContent_r14 = i0.ɵɵreference(27);\n            i0.ɵɵadvance(6);\n            i0.ɵɵproperty(\"ngIf\", ctx.chackuser);\n            i0.ɵɵadvance(4);\n            i0.ɵɵpropertyInterpolate(\"placeholder\", ctx.companycode);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.getcustomercode);\n            i0.ɵɵproperty(\"ngbTypeahead\", ctx.searchcode)(\"resultTemplate\", rtcode_r11)(\"inputFormatter\", ctx.formattercode);\n            i0.ɵɵadvance(4);\n            i0.ɵɵpropertyInterpolate(\"placeholder\", ctx.companyname);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.getcustomername);\n            i0.ɵɵproperty(\"ngbTypeahead\", ctx.search)(\"resultTemplate\", rt_r12)(\"inputFormatter\", ctx.formatter);\n            i0.ɵɵadvance(4);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.getcustomeraddress);\n            i0.ɵɵproperty(\"ngbTypeahead\", ctx.searchaddress)(\"resultTemplate\", rtaddress_r13)(\"inputFormatter\", ctx.formatteraddress);\n            i0.ɵɵadvance(2);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.groupcustomer);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"disabled\", ctx.searchbtn);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"disabled\", ctx.exportbtn);\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"disabled\", ctx.searchbtn)(\"ngbTooltip\", tipContent_r14);\n            i0.ɵɵadvance(21);\n            i0.ɵɵproperty(\"ngForOf\", ctx.customerlists);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(23, _c0, ctx.mdlSampleIsOpen ? \"block\" : \"none\"));\n            i0.ɵɵadvance(7);\n            i0.ɵɵtextInterpolate(ctx.alt);\n          }\n        },\n        dependencies: [i4.NgForOf, i4.NgIf, i4.NgStyle, i5.NgSelectOption, i5.ɵNgSelectMultipleOption, i5.DefaultValueAccessor, i5.SelectMultipleControlValueAccessor, i5.NgControlStatus, i5.NgModel, i6.NgbTooltip, i6.NgbTypeahead, i7.TopmenuComponent]\n      });\n    }\n  }\n  return CustomerlistComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}