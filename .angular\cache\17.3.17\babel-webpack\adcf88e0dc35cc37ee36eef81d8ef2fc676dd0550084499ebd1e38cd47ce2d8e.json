{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.concatMapTo = void 0;\nvar concatMap_1 = require(\"./concatMap\");\nvar isFunction_1 = require(\"../util/isFunction\");\nfunction concatMapTo(innerObservable, resultSelector) {\n  return isFunction_1.isFunction(resultSelector) ? concatMap_1.concatMap(function () {\n    return innerObservable;\n  }, resultSelector) : concatMap_1.concatMap(function () {\n    return innerObservable;\n  });\n}\nexports.concatMapTo = concatMapTo;\n//# sourceMappingURL=concatMapTo.js.map", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}