{"ast": null, "code": "import { BehaviorSubject, queueScheduler, Observable } from 'rxjs';\nimport { observeOn, scan, map, distinctUntilChanged } from 'rxjs/operators';\nclass MiniState extends BehaviorSubject {\n  constructor(_initialState, actionsDispatcher$, reducer) {\n    super(_initialState);\n    const actionInQueue$ = actionsDispatcher$.pipe(observeOn(queueScheduler));\n    const state$ = actionInQueue$.pipe(scan((state, action) => {\n      if (!action) {\n        return state;\n      }\n      return reducer(state, action);\n    }, _initialState));\n    state$.subscribe(value => this.next(value));\n  }\n}\n\n/**\n * @copyright ngrx\n */\nclass MiniStore extends Observable {\n  constructor(_dispatcher,\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  _reducer,\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  state$) {\n    super();\n    this._dispatcher = _dispatcher;\n    this._reducer = _reducer;\n    this.source = state$;\n  }\n  select(pathOrMapFn) {\n    // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n    // @ts-ignore\n    const mapped$ = this.source?.pipe(map(pathOrMapFn)) || new Observable().pipe(map(pathOrMapFn));\n    return mapped$.pipe(distinctUntilChanged());\n  }\n  lift(operator) {\n    const store = new MiniStore(this._dispatcher, this._reducer, this);\n    store.operator = operator;\n    return store;\n  }\n  dispatch(action) {\n    this._dispatcher.next(action);\n  }\n  next(action) {\n    this._dispatcher.next(action);\n  }\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  error(err) {\n    this._dispatcher.error(err);\n  }\n  complete() {\n    /*noop*/\n  }\n}\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MiniState, MiniStore };\n//# sourceMappingURL=ngx-bootstrap-mini-ngrx.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}