{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Cambodian [km]\n//! author : <PERSON><PERSON><PERSON> : https://github.com/k<PERSON><PERSON><PERSON>\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  var symbolMap = {\n      1: '១',\n      2: '២',\n      3: '៣',\n      4: '៤',\n      5: '៥',\n      6: '៦',\n      7: '៧',\n      8: '៨',\n      9: '៩',\n      0: '០'\n    },\n    numberMap = {\n      '១': '1',\n      '២': '2',\n      '៣': '3',\n      '៤': '4',\n      '៥': '5',\n      '៦': '6',\n      '៧': '7',\n      '៨': '8',\n      '៩': '9',\n      '០': '0'\n    };\n  var km = moment.defineLocale('km', {\n    months: 'មករា_កុម្ភៈ_មីនា_មេសា_ឧសភា_មិថុនា_កក្កដា_សីហា_កញ្ញា_តុលា_វិច្ឆិកា_ធ្នូ'.split('_'),\n    monthsShort: 'មករា_កុម្ភៈ_មីនា_មេសា_ឧសភា_មិថុនា_កក្កដា_សីហា_កញ្ញា_តុលា_វិច្ឆិកា_ធ្នូ'.split('_'),\n    weekdays: 'អាទិត្យ_ច័ន្ទ_អង្គារ_ពុធ_ព្រហស្បតិ៍_សុក្រ_សៅរ៍'.split('_'),\n    weekdaysShort: 'អា_ច_អ_ព_ព្រ_សុ_ស'.split('_'),\n    weekdaysMin: 'អា_ច_អ_ព_ព្រ_សុ_ស'.split('_'),\n    weekdaysParseExact: true,\n    longDateFormat: {\n      LT: 'HH:mm',\n      LTS: 'HH:mm:ss',\n      L: 'DD/MM/YYYY',\n      LL: 'D MMMM YYYY',\n      LLL: 'D MMMM YYYY HH:mm',\n      LLLL: 'dddd, D MMMM YYYY HH:mm'\n    },\n    meridiemParse: /ព្រឹក|ល្ងាច/,\n    isPM: function (input) {\n      return input === 'ល្ងាច';\n    },\n    meridiem: function (hour, minute, isLower) {\n      if (hour < 12) {\n        return 'ព្រឹក';\n      } else {\n        return 'ល្ងាច';\n      }\n    },\n    calendar: {\n      sameDay: '[ថ្ងៃនេះ ម៉ោង] LT',\n      nextDay: '[ស្អែក ម៉ោង] LT',\n      nextWeek: 'dddd [ម៉ោង] LT',\n      lastDay: '[ម្សិលមិញ ម៉ោង] LT',\n      lastWeek: 'dddd [សប្តាហ៍មុន] [ម៉ោង] LT',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: '%sទៀត',\n      past: '%sមុន',\n      s: 'ប៉ុន្មានវិនាទី',\n      ss: '%d វិនាទី',\n      m: 'មួយនាទី',\n      mm: '%d នាទី',\n      h: 'មួយម៉ោង',\n      hh: '%d ម៉ោង',\n      d: 'មួយថ្ងៃ',\n      dd: '%d ថ្ងៃ',\n      M: 'មួយខែ',\n      MM: '%d ខែ',\n      y: 'មួយឆ្នាំ',\n      yy: '%d ឆ្នាំ'\n    },\n    dayOfMonthOrdinalParse: /ទី\\d{1,2}/,\n    ordinal: 'ទី%d',\n    preparse: function (string) {\n      return string.replace(/[១២៣៤៥៦៧៨៩០]/g, function (match) {\n        return numberMap[match];\n      });\n    },\n    postformat: function (string) {\n      return string.replace(/\\d/g, function (match) {\n        return symbolMap[match];\n      });\n    },\n    week: {\n      dow: 1,\n      // Monday is the first day of the week.\n      doy: 4 // The week that contains Jan 4th is the first week of the year.\n    }\n  });\n  return km;\n});", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}