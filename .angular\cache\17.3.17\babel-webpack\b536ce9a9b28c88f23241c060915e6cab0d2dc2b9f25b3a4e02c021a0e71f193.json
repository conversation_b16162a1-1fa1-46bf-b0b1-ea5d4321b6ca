{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.joinAllInternals = void 0;\nvar identity_1 = require(\"../util/identity\");\nvar mapOneOrManyArgs_1 = require(\"../util/mapOneOrManyArgs\");\nvar pipe_1 = require(\"../util/pipe\");\nvar mergeMap_1 = require(\"./mergeMap\");\nvar toArray_1 = require(\"./toArray\");\nfunction joinAllInternals(joinFn, project) {\n  return pipe_1.pipe(toArray_1.toArray(), mergeMap_1.mergeMap(function (sources) {\n    return joinFn(sources);\n  }), project ? mapOneOrManyArgs_1.mapOneOrManyArgs(project) : identity_1.identity);\n}\nexports.joinAllInternals = joinAllInternals;\n//# sourceMappingURL=joinAllInternals.js.map", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}