{"ast": null, "code": "/**\n * This file includes polyfills needed by <PERSON><PERSON> and is loaded before the app.\n * You can add your own extra polyfills to this file.\n *\n * This file is divided into 2 sections:\n *   1. Browser polyfills. These are applied before loading ZoneJS and are sorted by browsers.\n *   2. Application imports. Files imported after ZoneJS that should be loaded before your main\n *      file.\n *\n * The current setup is for so-called \"evergreen\" browsers; the last versions of browsers that\n * automatically update themselves. This includes Safari >= 10, Chrome >= 55 (including Opera),\n * Edge >= 13 on the desktop, and iOS 10 and Chrome on mobile.\n *\n * Learn more in https://angular.io/docs/ts/latest/guide/browser-support.html\n */\n/***************************************************************************************************\n * BROWSER POLYFILLS\n */\n/** IE9, IE10 and IE11 requires all of the following polyfills. **/\nimport 'core-js/es6/array';\nimport 'core-js/es6/date';\nimport 'core-js/es6/function';\nimport 'core-js/es6/map';\nimport 'core-js/es6/math';\nimport 'core-js/es6/number';\nimport 'core-js/es6/object';\nimport 'core-js/es6/parse-float';\nimport 'core-js/es6/parse-int';\nimport 'core-js/es6/regexp';\nimport 'core-js/es6/set';\nimport 'core-js/es6/string';\nimport 'core-js/es6/symbol';\nimport 'core-js/es6/weak-map';\n/** IE10 and IE11 requires the following for NgClass support on SVG elements */\nimport 'classlist.js'; // Run `npm install --save classlist.js`.\n/** IE10 and IE11 requires the following for the Reflect API. */\nimport 'core-js/es6/reflect';\n/** Evergreen browsers require these. **/\n// Used for reflect-metadata in JIT. If you use AOT (and only Angular decorators), you can remove.\nimport 'core-js/es7/reflect';\n/**\n * Web Animations `@angular/platform-browser/animations`\n * Only required if AnimationBuilder is used within the application and using IE/Edge or Safari.\n * Standard animation support in Angular DOES NOT require any polyfills (as of Angular 6.0).\n **/\nimport 'web-animations-js'; // Run `npm install --save web-animations-js`.\n/**\n * By default, zone.js will patch all possible macroTask and DomEvents\n * user can disable parts of macroTask/DomEvents patch by setting following flags\n */\n// (window as any).__Zone_disable_requestAnimationFrame = true; // disable patch requestAnimationFrame\n// (window as any).__Zone_disable_on_property = true; // disable patch onProperty such as onclick\n// (window as any).__zone_symbol__BLACK_LISTED_EVENTS = ['scroll', 'mousemove']; // disable patch specified eventNames\n/*\n* in IE/Edge developer tools, the addEventListener will also be wrapped by zone.js\n* with the following flag, it will bypass `zone.js` patch for IE/Edge\n*/\n// (window as any).__Zone_enable_cross_context_check = true;\n/***************************************************************************************************\n * Zone JS is required by default for Angular itself.\n */\nimport 'zone.js'; // Included with Angular CLI.\n/***************************************************************************************************\n * APPLICATION IMPORTS\n */", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}