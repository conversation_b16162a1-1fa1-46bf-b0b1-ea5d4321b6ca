{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.dematerialize = void 0;\nvar Notification_1 = require(\"../Notification\");\nvar lift_1 = require(\"../util/lift\");\nvar OperatorSubscriber_1 = require(\"./OperatorSubscriber\");\nfunction dematerialize() {\n  return lift_1.operate(function (source, subscriber) {\n    source.subscribe(OperatorSubscriber_1.createOperatorSubscriber(subscriber, function (notification) {\n      return Notification_1.observeNotification(notification, subscriber);\n    }));\n  });\n}\nexports.dematerialize = dematerialize;\n//# sourceMappingURL=dematerialize.js.map", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}