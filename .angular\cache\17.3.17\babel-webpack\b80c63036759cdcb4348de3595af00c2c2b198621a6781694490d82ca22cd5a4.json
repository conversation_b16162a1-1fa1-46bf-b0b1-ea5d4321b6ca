{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.timeout = exports.TimeoutError = void 0;\nvar async_1 = require(\"../scheduler/async\");\nvar isDate_1 = require(\"../util/isDate\");\nvar lift_1 = require(\"../util/lift\");\nvar innerFrom_1 = require(\"../observable/innerFrom\");\nvar createErrorClass_1 = require(\"../util/createErrorClass\");\nvar OperatorSubscriber_1 = require(\"./OperatorSubscriber\");\nvar executeSchedule_1 = require(\"../util/executeSchedule\");\nexports.TimeoutError = createErrorClass_1.createErrorClass(function (_super) {\n  return function TimeoutErrorImpl(info) {\n    if (info === void 0) {\n      info = null;\n    }\n    _super(this);\n    this.message = 'Timeout has occurred';\n    this.name = 'TimeoutError';\n    this.info = info;\n  };\n});\nfunction timeout(config, schedulerArg) {\n  var _a = isDate_1.isValidDate(config) ? {\n      first: config\n    } : typeof config === 'number' ? {\n      each: config\n    } : config,\n    first = _a.first,\n    each = _a.each,\n    _b = _a.with,\n    _with = _b === void 0 ? timeoutErrorFactory : _b,\n    _c = _a.scheduler,\n    scheduler = _c === void 0 ? schedulerArg !== null && schedulerArg !== void 0 ? schedulerArg : async_1.asyncScheduler : _c,\n    _d = _a.meta,\n    meta = _d === void 0 ? null : _d;\n  if (first == null && each == null) {\n    throw new TypeError('No timeout provided.');\n  }\n  return lift_1.operate(function (source, subscriber) {\n    var originalSourceSubscription;\n    var timerSubscription;\n    var lastValue = null;\n    var seen = 0;\n    var startTimer = function (delay) {\n      timerSubscription = executeSchedule_1.executeSchedule(subscriber, scheduler, function () {\n        try {\n          originalSourceSubscription.unsubscribe();\n          innerFrom_1.innerFrom(_with({\n            meta: meta,\n            lastValue: lastValue,\n            seen: seen\n          })).subscribe(subscriber);\n        } catch (err) {\n          subscriber.error(err);\n        }\n      }, delay);\n    };\n    originalSourceSubscription = source.subscribe(OperatorSubscriber_1.createOperatorSubscriber(subscriber, function (value) {\n      timerSubscription === null || timerSubscription === void 0 ? void 0 : timerSubscription.unsubscribe();\n      seen++;\n      subscriber.next(lastValue = value);\n      each > 0 && startTimer(each);\n    }, undefined, undefined, function () {\n      if (!(timerSubscription === null || timerSubscription === void 0 ? void 0 : timerSubscription.closed)) {\n        timerSubscription === null || timerSubscription === void 0 ? void 0 : timerSubscription.unsubscribe();\n      }\n      lastValue = null;\n    }));\n    !seen && startTimer(first != null ? typeof first === 'number' ? first : +first - scheduler.now() : each);\n  });\n}\nexports.timeout = timeout;\nfunction timeoutErrorFactory(info) {\n  throw new exports.TimeoutError(info);\n}\n//# sourceMappingURL=timeout.js.map", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}