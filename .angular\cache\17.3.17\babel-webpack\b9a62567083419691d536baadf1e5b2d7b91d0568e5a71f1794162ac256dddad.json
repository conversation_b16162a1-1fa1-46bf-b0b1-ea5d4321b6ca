{"ast": null, "code": "import * as i0 from \"@angular/core\";\nconst PADDING = \"000000\";\nexport let ReversepipePipe = /*#__PURE__*/(() => {\n  class ReversepipePipe {\n    constructor() {\n      // TODO comes from configuration settings\n      this.DECIMAL_SEPARATOR = \".\";\n      this.THOUSANDS_SEPARATOR = \",\";\n    }\n    transform(value, fractionSize = 2) {\n      let [integer, fraction = \"\"] = (value || \"\").toString().split(this.DECIMAL_SEPARATOR);\n      fraction = fractionSize > 0 ? this.DECIMAL_SEPARATOR + (fraction + PADDING).substring(0, fractionSize) : \"\";\n      integer = integer.replace(/\\B(?=(\\d{3})+(?!\\d))/g, this.THOUSANDS_SEPARATOR);\n      return integer + fraction;\n    }\n    parse(value, fractionSize = 2) {\n      let [integer, fraction = \"\"] = (value || \"\").split(this.DECIMAL_SEPARATOR);\n      integer = integer.replace(new RegExp(this.THOUSANDS_SEPARATOR, \"g\"), \"\");\n      fraction = parseInt(fraction, 10) > 0 && fractionSize > 0 ? this.DECIMAL_SEPARATOR + (fraction + PADDING).substring(0, fractionSize) : \"\";\n      return integer + fraction;\n    }\n    static {\n      this.ɵfac = function ReversepipePipe_Factory(t) {\n        return new (t || ReversepipePipe)();\n      };\n    }\n    static {\n      this.ɵpipe = /*@__PURE__*/i0.ɵɵdefinePipe({\n        name: \"myCurrency\",\n        type: ReversepipePipe,\n        pure: true\n      });\n    }\n  }\n  return ReversepipePipe;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}