{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.Scheduler = void 0;\nvar dateTimestampProvider_1 = require(\"./scheduler/dateTimestampProvider\");\nvar Scheduler = function () {\n  function Scheduler(schedulerActionCtor, now) {\n    if (now === void 0) {\n      now = Scheduler.now;\n    }\n    this.schedulerActionCtor = schedulerActionCtor;\n    this.now = now;\n  }\n  Scheduler.prototype.schedule = function (work, delay, state) {\n    if (delay === void 0) {\n      delay = 0;\n    }\n    return new this.schedulerActionCtor(this, work).schedule(state, delay);\n  };\n  Scheduler.now = dateTimestampProvider_1.dateTimestampProvider.now;\n  return Scheduler;\n}();\nexports.Scheduler = Scheduler;\n//# sourceMappingURL=Scheduler.js.map", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}