{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.performanceTimestampProvider = void 0;\nexports.performanceTimestampProvider = {\n  now: function () {\n    return (exports.performanceTimestampProvider.delegate || performance).now();\n  },\n  delegate: undefined\n};\n//# sourceMappingURL=performanceTimestampProvider.js.map", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}