{"ast": null, "code": "import { HttpEventType } from '@angular/common/http';\nimport { Angular5Csv } from 'angular5-csv/dist/Angular5-csv';\nimport { debounceTime, map } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"ng2-img-max\";\nimport * as i2 from \"ngx-bootstrap/modal\";\nimport * as i3 from \"@angular/forms\";\nimport * as i4 from \"@angular/common/http\";\nimport * as i5 from \"../webapi.service\";\nimport * as i6 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i7 from \"@angular/router\";\nconst _c0 = () => ({\n  dateInputFormat: \"DD/MM/YYYY\"\n});\nconst _c1 = a0 => ({\n  \"display\": a0,\n  \"opacity\": 1\n});\nconst _c2 = a0 => ({\n  itemsPerPage: 10,\n  currentPage: a0\n});\nconst _c3 = a0 => ({\n  \"color\": a0\n});\nfunction CompleteinvoiceComponent_div_7_option_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 103);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r4 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", item_r4.groupid);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\" \", item_r4.groupid, \" (\", item_r4.name, \") \");\n  }\n}\nfunction CompleteinvoiceComponent_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 20)(1, \"select\", 99);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function CompleteinvoiceComponent_div_7_Template_select_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.datasalegroup, $event) || (ctx_r2.datasalegroup = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementStart(2, \"option\", 100);\n    i0.ɵɵtext(3, \"--\\u0E40\\u0E25\\u0E37\\u0E2D\\u0E01\\u0E23\\u0E2B\\u0E31\\u0E2A\\u0E1E\\u0E19\\u0E31\\u0E01\\u0E07\\u0E32\\u0E19\\u0E02\\u0E32\\u0E22--\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"option\", 101);\n    i0.ɵɵtext(5, \"\\u0E40\\u0E25\\u0E37\\u0E2D\\u0E01\\u0E23\\u0E32\\u0E22\\u0E01\\u0E32\\u0E23\\u0E17\\u0E31\\u0E49\\u0E07\\u0E2B\\u0E21\\u0E14\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, CompleteinvoiceComponent_div_7_option_6_Template, 2, 3, \"option\", 102);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.datasalegroup);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.DateGroupsaleman);\n  }\n}\nfunction CompleteinvoiceComponent_ng_template_17_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"label\", 104);\n    i0.ɵɵlistener(\"mousedown\", function CompleteinvoiceComponent_ng_template_17_Template_label_mousedown_1_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.searchselectprint());\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const r_r6 = ctx.result;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"\", r_r6.name, \" (\", r_r6.accountnum, \")\");\n  }\n}\nfunction CompleteinvoiceComponent_button_47_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 37);\n    i0.ɵɵlistener(\"click\", function CompleteinvoiceComponent_button_47_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.SearchCompleteED(ctx_r2.keyboardStrApprove, ctx_r2.keyboardStrpaymenttype));\n    });\n    i0.ɵɵtext(1, \"Revert\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"disabled\", ctx_r2.searchbtn);\n  }\n}\nfunction CompleteinvoiceComponent_button_50_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 105);\n    i0.ɵɵlistener(\"click\", function CompleteinvoiceComponent_button_50_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r2 = i0.ɵɵnextContext();\n      const ShowDeleteINV_r9 = i0.ɵɵreference(296);\n      return i0.ɵɵresetView(ctx_r2.GetDelete(ShowDeleteINV_r9, ctx_r2.Datafromdate, ctx_r2.Datatodate));\n    });\n    i0.ɵɵtext(1, \"Delete\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CompleteinvoiceComponent_th_67_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"th\", 44);\n  }\n}\nfunction CompleteinvoiceComponent_th_68_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"th\", 44);\n  }\n}\nfunction CompleteinvoiceComponent_th_69_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"th\", 44);\n  }\n}\nfunction CompleteinvoiceComponent_tr_72_td_19_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"td\", 110)(1, \"button\", 112);\n    i0.ɵɵlistener(\"click\", function CompleteinvoiceComponent_tr_72_td_19_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const item_r12 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      const templateView_r13 = i0.ɵɵreference(286);\n      return i0.ɵɵresetView(ctx_r2.openModalbookbank(item_r12.Invoicingname, item_r12.Billno, item_r12.Orderaccount, templateView_r13, item_r12.Salegroup));\n    });\n    i0.ɵɵtext(2, \" Bookbank \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r12 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", item_r12.CK === \"\\u0E2D\\u0E19\\u0E38\\u0E21\\u0E31\\u0E15\\u0E34\\u0E41\\u0E25\\u0E49\\u0E27\" || item_r12.CK == \"\\u0E23\\u0E2D\\u0E2D\\u0E19\\u0E38\\u0E21\\u0E31\\u0E15\\u0E34\\u0E01\\u0E25\\u0E31\\u0E1A\");\n  }\n}\nfunction CompleteinvoiceComponent_tr_72_td_20_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"td\", 110)(1, \"button\", 113);\n    i0.ɵɵlistener(\"click\", function CompleteinvoiceComponent_tr_72_td_20_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const item_r12 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      const ModalApprove_r15 = i0.ɵɵreference(288);\n      return i0.ɵɵresetView(ctx_r2.OpenModalRevert(item_r12.Invoicingname, item_r12.Billno, ModalApprove_r15, item_r12.Orderaccount, item_r12.Salegroup));\n    });\n    i0.ɵɵtext(2, \" Revert \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r12 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", item_r12.CK != \"\\u0E23\\u0E2D\\u0E2D\\u0E19\\u0E38\\u0E21\\u0E31\\u0E15\\u0E34\\u0E01\\u0E25\\u0E31\\u0E1A\");\n  }\n}\nfunction CompleteinvoiceComponent_tr_72_td_21_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"td\", 110)(1, \"button\", 113);\n    i0.ɵɵlistener(\"click\", function CompleteinvoiceComponent_tr_72_td_21_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const item_r12 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      const ModalApprove_r15 = i0.ɵɵreference(288);\n      return i0.ɵɵresetView(ctx_r2.OpenModalApprove(item_r12.Invoicingname, item_r12.Billno, ModalApprove_r15, item_r12.Orderaccount, item_r12.Salegroup));\n    });\n    i0.ɵɵtext(2, \" Approve \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r12 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", item_r12.CK != \"\\u0E23\\u0E2D\\u0E2D\\u0E19\\u0E38\\u0E21\\u0E31\\u0E15\\u0E34\" || item_r12.CK == \"\\u0E23\\u0E2D\\u0E2D\\u0E19\\u0E38\\u0E21\\u0E31\\u0E15\\u0E34\\u0E01\\u0E25\\u0E31\\u0E1A\");\n  }\n}\nfunction CompleteinvoiceComponent_tr_72_td_22_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"td\", 110)(1, \"button\", 113);\n    i0.ɵɵlistener(\"click\", function CompleteinvoiceComponent_tr_72_td_22_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const item_r12 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      const Modalwait_r18 = i0.ɵɵreference(294);\n      return i0.ɵɵresetView(ctx_r2.getIdBillwaitModal(item_r12.Invoicingname, item_r12.Billno, Modalwait_r18, item_r12.Orderaccount, item_r12.Salegroup));\n    });\n    i0.ɵɵtext(2, \" wait \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r12 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", item_r12.approve === \"\\u0E2D\\u0E19\\u0E38\\u0E21\\u0E31\\u0E15\\u0E34\\u0E41\\u0E25\\u0E49\\u0E27\" || item_r12.CK != \"\\u0E23\\u0E2D\\u0E2D\\u0E19\\u0E38\\u0E21\\u0E31\\u0E15\\u0E34\");\n  }\n}\nfunction CompleteinvoiceComponent_tr_72_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\", 46);\n    i0.ɵɵelementContainerStart(1);\n    i0.ɵɵelementStart(2, \"td\", 106);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"td\", 106);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"td\", 107);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"td\", 106);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"td\", 108);\n    i0.ɵɵtext(11);\n    i0.ɵɵpipe(12, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"td\", 108);\n    i0.ɵɵtext(14);\n    i0.ɵɵpipe(15, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"td\", 108);\n    i0.ɵɵtext(17);\n    i0.ɵɵpipe(18, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(19, CompleteinvoiceComponent_tr_72_td_19_Template, 3, 1, \"td\", 109)(20, CompleteinvoiceComponent_tr_72_td_20_Template, 3, 1, \"td\", 109)(21, CompleteinvoiceComponent_tr_72_td_21_Template, 3, 1, \"td\", 109)(22, CompleteinvoiceComponent_tr_72_td_22_Template, 3, 1, \"td\", 109);\n    i0.ɵɵelementStart(23, \"td\", 110)(24, \"button\", 111);\n    i0.ɵɵlistener(\"click\", function CompleteinvoiceComponent_tr_72_Template_button_click_24_listener() {\n      const item_r12 = i0.ɵɵrestoreView(_r10).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      const ModalApproveSuccess_r19 = i0.ɵɵreference(292);\n      return i0.ɵɵresetView(ctx_r2.openModalsuccess(ModalApproveSuccess_r19, item_r12.Invoicingname, item_r12.Billno, item_r12.Orderaccount, item_r12.Salegroup));\n    });\n    i0.ɵɵtext(25, \" View \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r12 = ctx.$implicit;\n    const i_r20 = ctx.index;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(27, _c3, ctx_r2.getColor(item_r12.approve, item_r12.remarkstatus, item_r12.CK)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"\", i_r20 + 1, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(29, _c3, ctx_r2.getColor(item_r12.approve, item_r12.remarkstatus, item_r12.CK)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r12.Salegroup);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(31, _c3, ctx_r2.getColor(item_r12.approve, item_r12.remarkstatus, item_r12.CK)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"\", item_r12.Invoicingname, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(33, _c3, ctx_r2.getColor(item_r12.approve, item_r12.remarkstatus, item_r12.CK)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r12.Billno);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(35, _c3, ctx_r2.getColor(item_r12.approve, item_r12.remarkstatus, item_r12.CK)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(12, 18, item_r12.Salesbalance, \"1.2-2\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(37, _c3, ctx_r2.getColor(item_r12.approve, item_r12.remarkstatus, item_r12.CK)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(15, 21, item_r12.Sumtax, \"1.2-2\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(39, _c3, ctx_r2.getColor(item_r12.approve, item_r12.remarkstatus, item_r12.CK)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(18, 24, item_r12.Invoiceamount, \"1.2-2\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.searchbtn);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.testclose && ctx_r2.SearchCompleteED_);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.testclose && ctx_r2.SearchComplete_);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.testclose);\n  }\n}\nfunction CompleteinvoiceComponent_th_86_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"th\", 49);\n  }\n}\nfunction CompleteinvoiceComponent_th_87_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"th\", 49);\n  }\n}\nfunction CompleteinvoiceComponent_th_88_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"th\", 49);\n  }\n}\nfunction CompleteinvoiceComponent_tr_137_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r21 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 106);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\", 114);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\", 114);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\", 106);\n    i0.ɵɵtext(8);\n    i0.ɵɵpipe(9, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"td\", 106);\n    i0.ɵɵtext(11);\n    i0.ɵɵpipe(12, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"td\", 106);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"td\", 115);\n    i0.ɵɵtext(16);\n    i0.ɵɵpipe(17, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"td\", 115);\n    i0.ɵɵtext(19);\n    i0.ɵɵpipe(20, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"td\", 115);\n    i0.ɵɵtext(22);\n    i0.ɵɵpipe(23, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"td\", 116)(25, \"input\", 117);\n    i0.ɵɵlistener(\"click\", function CompleteinvoiceComponent_tr_137_Template_input_click_25_listener($event) {\n      const itembill_r22 = i0.ɵɵrestoreView(_r21).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.checkIfAllSelected($event.target.checked, itembill_r22.noid));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(26, \"td\", 110)(27, \"button\", 118);\n    i0.ɵɵlistener(\"click\", function CompleteinvoiceComponent_tr_137_Template_button_click_27_listener() {\n      const itembill_r22 = i0.ɵɵrestoreView(_r21).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.openModalBill(false, \"\", false, itembill_r22.invoiceid, itembill_r22.orderaccount, itembill_r22.salesid, itembill_r22.invoicename, itembill_r22.invoicedate, itembill_r22.duedate, itembill_r22.delivery, itembill_r22.salesbalance, itembill_r22.sumtax, itembill_r22.invoiceamount, itembill_r22.saleman, itembill_r22.remark, itembill_r22.urlimg, itembill_r22.imgbookbank));\n    });\n    i0.ɵɵtext(28);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const itembill_r22 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(35, _c3, ctx_r2.getColor2(itembill_r22.remarkst)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(itembill_r22.noid + 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(37, _c3, ctx_r2.getColor2(itembill_r22.remarkst)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(itembill_r22.salesid);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(39, _c3, ctx_r2.getColor2(itembill_r22.remarkst)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(itembill_r22.invoiceid);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(41, _c3, ctx_r2.getColor2(itembill_r22.remarkst)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(9, 20, itembill_r22.invoicedate, \"dd/MM/yyyy\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(43, _c3, ctx_r2.getColor2(itembill_r22.remarkst)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(12, 23, itembill_r22.duedate, \"dd/MM/yyyy\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(45, _c3, ctx_r2.getColor2(itembill_r22.remarkst)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(itembill_r22.payment);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(47, _c3, ctx_r2.getColor2(itembill_r22.remarkst)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(17, 26, itembill_r22.salesbalance, \"1.2-2\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(49, _c3, ctx_r2.getColor2(itembill_r22.remarkst)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(20, 29, itembill_r22.sumtax, \"1.2-2\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(51, _c3, ctx_r2.getColor2(itembill_r22.remarkst)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(23, 32, itembill_r22.invoiceamount, \"1.2-2\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"checked\", itembill_r22.check);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", itembill_r22.numrun, \" \");\n  }\n}\nfunction CompleteinvoiceComponent_button_151_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r23 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 119);\n    i0.ɵɵlistener(\"click\", function CompleteinvoiceComponent_button_151_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r23);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onUpload());\n    });\n    i0.ɵɵtext(1, \"Approve\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"disabled\", ctx_r2.total == 0);\n  }\n}\nfunction CompleteinvoiceComponent_button_152_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r24 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 119);\n    i0.ɵɵlistener(\"click\", function CompleteinvoiceComponent_button_152_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r24);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onRevert());\n    });\n    i0.ɵɵtext(1, \"Revert\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"disabled\", ctx_r2.total == 0);\n  }\n}\nfunction CompleteinvoiceComponent_ng_template_163_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"b\");\n    i0.ɵɵtext(1, \"Images Billno\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CompleteinvoiceComponent_ng_template_164_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 120);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵpropertyInterpolate(\"src\", ctx_r2.ImageBillno, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction CompleteinvoiceComponent_ng_template_166_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"b\");\n    i0.ɵɵtext(1, \"Images BookBank\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CompleteinvoiceComponent_ng_template_167_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 120);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵpropertyInterpolate(\"src\", ctx_r2.ImageBookbank, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction CompleteinvoiceComponent_ng_template_169_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"b\");\n    i0.ɵɵtext(1, \"Detail INV \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CompleteinvoiceComponent_ng_template_170_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 121)(1, \"table\", 40)(2, \"thead\")(3, \"tr\");\n    i0.ɵɵelement(4, \"th\", 122);\n    i0.ɵɵelementStart(5, \"th\", 123);\n    i0.ɵɵtext(6, \"detail\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(7, \"tbody\")(8, \"tr\")(9, \"th\");\n    i0.ɵɵtext(10, \"Sales Id\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"td\", 124);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"tr\")(14, \"th\");\n    i0.ɵɵtext(15, \"SO No\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"td\", 124);\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"tr\")(19, \"th\");\n    i0.ɵɵtext(20, \"Invoice No\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"td\", 124);\n    i0.ɵɵtext(22);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"tr\")(24, \"th\");\n    i0.ɵɵtext(25, \"OrderAccount\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"td\", 124);\n    i0.ɵɵtext(27);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(28, \"tr\")(29, \"th\");\n    i0.ɵɵtext(30, \"Invoicingname\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"td\", 124);\n    i0.ɵɵtext(32);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(33, \"tr\")(34, \"th\");\n    i0.ɵɵtext(35, \"Invoice Date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(36, \"td\", 124);\n    i0.ɵɵtext(37);\n    i0.ɵɵpipe(38, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(39, \"tr\")(40, \"th\");\n    i0.ɵɵtext(41, \"Invoice DueDate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(42, \"td\", 124);\n    i0.ɵɵtext(43);\n    i0.ɵɵpipe(44, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(45, \"tr\")(46, \"th\");\n    i0.ɵɵtext(47, \"Deliveryname\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(48, \"td\", 124);\n    i0.ɵɵtext(49);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(50, \"tr\")(51, \"th\");\n    i0.ɵɵtext(52, \"\\u0E21\\u0E39\\u0E25\\u0E04\\u0E48\\u0E32\\u0E2A\\u0E34\\u0E19\\u0E04\\u0E49\\u0E32\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(53, \"td\", 124);\n    i0.ɵɵtext(54);\n    i0.ɵɵpipe(55, \"number\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(56, \"tr\")(57, \"th\");\n    i0.ɵɵtext(58, \"VAT\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(59, \"td\", 124);\n    i0.ɵɵtext(60);\n    i0.ɵɵpipe(61, \"number\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(62, \"tr\")(63, \"th\");\n    i0.ɵɵtext(64, \"\\u0E21\\u0E39\\u0E25\\u0E04\\u0E48\\u0E32\\u0E23\\u0E27\\u0E21\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(65, \"td\", 124);\n    i0.ɵɵtext(66);\n    i0.ɵɵpipe(67, \"number\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(68, \"tr\")(69, \"th\");\n    i0.ɵɵtext(70, \"Comment\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(71, \"td\", 124);\n    i0.ɵɵtext(72);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(12);\n    i0.ɵɵtextInterpolate(ctx_r2.SalesId);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r2.SONo);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r2.InvoiceNo);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r2.OrderAccount);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r2.Invoicingname);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind2(38, 12, ctx_r2.InvoiceDate, \"dd/MM/yyyy\"), \" \");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(44, 15, ctx_r2.DueDate, \"dd/MM/yyyy\"));\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r2.Deliveryname);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(55, 18, ctx_r2.productsun, \"1.2-2\"));\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(61, 21, ctx_r2.VAT, \"1.2-2\"));\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(67, 24, ctx_r2.suntotal, \"1.2-2\"));\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.remarkshow, \" \");\n  }\n}\nfunction CompleteinvoiceComponent_tr_247_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r25 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 106);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\", 106);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\", 106);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"th\", 106);\n    i0.ɵɵtext(8);\n    i0.ɵɵpipe(9, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"th\", 106);\n    i0.ɵɵtext(11);\n    i0.ɵɵpipe(12, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"td\", 115);\n    i0.ɵɵtext(14);\n    i0.ɵɵpipe(15, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"td\", 115);\n    i0.ɵɵtext(17);\n    i0.ɵɵpipe(18, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"td\", 115);\n    i0.ɵɵtext(20);\n    i0.ɵɵpipe(21, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"td\", 125)(23, \"button\", 126);\n    i0.ɵɵlistener(\"click\", function CompleteinvoiceComponent_tr_247_Template_button_click_23_listener() {\n      const item_r26 = i0.ɵɵrestoreView(_r25).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.openModalimg(false, \"\", false, item_r26.nameimg, item_r26.invoiceid, item_r26.orderaccount, item_r26.salesid, item_r26.invoicename, item_r26.invoicedate, item_r26.duedate, item_r26.delivery, item_r26.salesbalance, item_r26.sumtax, item_r26.invoiceamount, item_r26.saleman, item_r26.remark));\n    });\n    i0.ɵɵtext(24);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const item_r26 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(32, _c3, ctx_r2.getColor2(item_r26.remarkst)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r26.noid + 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(34, _c3, ctx_r2.getColor2(item_r26.remarkst)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r26.salesid);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(36, _c3, ctx_r2.getColor2(item_r26.remarkst)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r26.invoiceid);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(38, _c3, ctx_r2.getColor2(item_r26.remarkst)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(9, 17, item_r26.invoicedate, \"dd/MM/yyyy\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(40, _c3, ctx_r2.getColor2(item_r26.remarkst)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(12, 20, item_r26.duedate, \"dd/MM/yyyy\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(42, _c3, ctx_r2.getColor2(item_r26.remarkst)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(15, 23, item_r26.salesbalance, \"1.2-2\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(44, _c3, ctx_r2.getColor2(item_r26.remarkst)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(18, 26, item_r26.sumtax, \"1.2-2\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(46, _c3, ctx_r2.getColor2(item_r26.remarkst)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(21, 29, item_r26.invoiceamount, \"1.2-2\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", item_r26.numrun, \" \");\n  }\n}\nfunction CompleteinvoiceComponent_ng_template_268_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"b\", 127);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"Images : \", ctx_r2.altimg, \"\");\n  }\n}\nfunction CompleteinvoiceComponent_ng_template_269_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 120);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵpropertyInterpolate(\"src\", ctx_r2.ImageBillno, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction CompleteinvoiceComponent_ng_template_271_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"b\", 127);\n    i0.ɵɵtext(1, \"Detail INV \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CompleteinvoiceComponent_ng_template_272_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 121)(1, \"table\", 40)(2, \"thead\")(3, \"tr\");\n    i0.ɵɵelement(4, \"th\", 122);\n    i0.ɵɵelementStart(5, \"th\", 123);\n    i0.ɵɵtext(6, \"detail\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(7, \"tbody\")(8, \"tr\")(9, \"th\");\n    i0.ɵɵtext(10, \"Sales Id\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"td\", 124);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"tr\")(14, \"th\");\n    i0.ɵɵtext(15, \"SO No\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"td\", 124);\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"tr\")(19, \"th\");\n    i0.ɵɵtext(20, \"Invoice No\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"td\", 124);\n    i0.ɵɵtext(22);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"tr\")(24, \"th\");\n    i0.ɵɵtext(25, \"OrderAccount\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"td\", 124);\n    i0.ɵɵtext(27);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(28, \"tr\")(29, \"th\");\n    i0.ɵɵtext(30, \"Invoicingname\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"td\", 124);\n    i0.ɵɵtext(32);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(33, \"tr\")(34, \"th\");\n    i0.ɵɵtext(35, \"Invoice Date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(36, \"td\", 124);\n    i0.ɵɵtext(37);\n    i0.ɵɵpipe(38, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(39, \"tr\")(40, \"th\");\n    i0.ɵɵtext(41, \"Invoice DueDate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(42, \"td\", 124);\n    i0.ɵɵtext(43);\n    i0.ɵɵpipe(44, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(45, \"tr\")(46, \"th\");\n    i0.ɵɵtext(47, \"Deliveryname\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(48, \"td\", 124);\n    i0.ɵɵtext(49);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(50, \"tr\")(51, \"th\");\n    i0.ɵɵtext(52, \"\\u0E21\\u0E39\\u0E25\\u0E04\\u0E48\\u0E32\\u0E2A\\u0E34\\u0E19\\u0E04\\u0E49\\u0E32\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(53, \"td\", 124);\n    i0.ɵɵtext(54);\n    i0.ɵɵpipe(55, \"number\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(56, \"tr\")(57, \"th\");\n    i0.ɵɵtext(58, \"VAT\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(59, \"td\", 124);\n    i0.ɵɵtext(60);\n    i0.ɵɵpipe(61, \"number\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(62, \"tr\")(63, \"th\");\n    i0.ɵɵtext(64, \"\\u0E21\\u0E39\\u0E25\\u0E04\\u0E48\\u0E32\\u0E23\\u0E27\\u0E21\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(65, \"td\", 124);\n    i0.ɵɵtext(66);\n    i0.ɵɵpipe(67, \"number\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(68, \"tr\")(69, \"th\");\n    i0.ɵɵtext(70, \"Comment\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(71, \"td\", 124);\n    i0.ɵɵtext(72);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(12);\n    i0.ɵɵtextInterpolate(ctx_r2.SalesId);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r2.SONo);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r2.InvoiceNo);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r2.OrderAccount);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r2.Invoicingname);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind2(38, 12, ctx_r2.InvoiceDate, \"dd/MM/yyyy\"), \" \");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(44, 15, ctx_r2.DueDate, \"dd/MM/yyyy\"));\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r2.Deliveryname);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(55, 18, ctx_r2.productsun, \"1.2-2\"));\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(61, 21, ctx_r2.VAT, \"1.2-2\"));\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(67, 24, ctx_r2.suntotal, \"1.2-2\"));\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.remarkshow, \" \");\n  }\n}\nfunction CompleteinvoiceComponent_ng_template_277_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r27 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 62)(1, \"h4\", 128);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 129);\n    i0.ɵɵlistener(\"click\", function CompleteinvoiceComponent_ng_template_277_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r27);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.modalRef.hide());\n    });\n    i0.ɵɵelementStart(4, \"span\", 65);\n    i0.ɵɵtext(5, \"\\u00D7\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(6, \"div\", 56)(7, \"div\", 130)(8, \"label\", 131);\n    i0.ɵɵtext(9, \"Input file PDF\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"input\", 132);\n    i0.ɵɵlistener(\"change\", function CompleteinvoiceComponent_ng_template_277_Template_input_change_10_listener($event) {\n      i0.ɵɵrestoreView(_r27);\n      const ctx_r2 = i0.ɵɵnextContext();\n      const templateShow_r28 = i0.ɵɵreference(280);\n      return i0.ɵɵresetView(ctx_r2.handleFileInput($event.target.files, templateShow_r28));\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(11, \"div\", 133)(12, \"button\", 134);\n    i0.ɵɵlistener(\"click\", function CompleteinvoiceComponent_ng_template_277_Template_button_click_12_listener() {\n      i0.ɵɵrestoreView(_r27);\n      const ctx_r2 = i0.ɵɵnextContext();\n      const template_r29 = i0.ɵɵreference(278);\n      const templateShow_r28 = i0.ɵɵreference(280);\n      return i0.ɵɵresetView(ctx_r2.onUpload(ctx_r2.showIDso, templateShow_r28, template_r29));\n    });\n    i0.ɵɵtext(13, \"Upload\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"button\", 135);\n    i0.ɵɵlistener(\"click\", function CompleteinvoiceComponent_ng_template_277_Template_button_click_14_listener() {\n      i0.ɵɵrestoreView(_r27);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.modalRef.hide());\n    });\n    i0.ɵɵtext(15, \"Close\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"UploadPDF : \", ctx_r2.showIDso, \"\");\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"disabled\", ctx_r2.selectedFile == null);\n  }\n}\nfunction CompleteinvoiceComponent_ng_template_279_button_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r30 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 139);\n    i0.ɵɵlistener(\"click\", function CompleteinvoiceComponent_ng_template_279_button_4_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r30);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.confirmimg());\n    });\n    i0.ɵɵtext(1, \"\\u0E1B\\u0E34\\u0E14\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CompleteinvoiceComponent_ng_template_279_button_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r31 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 139);\n    i0.ɵɵlistener(\"click\", function CompleteinvoiceComponent_ng_template_279_button_5_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r31);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.confirm());\n    });\n    i0.ɵɵtext(1, \"\\u0E1B\\u0E34\\u0E14\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CompleteinvoiceComponent_ng_template_279_button_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r32 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 140);\n    i0.ɵɵlistener(\"click\", function CompleteinvoiceComponent_ng_template_279_button_6_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r32);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      const template_r29 = i0.ɵɵreference(278);\n      return i0.ɵɵresetView(ctx_r2.decline(template_r29));\n    });\n    i0.ɵɵtext(1, \"\\u0E17\\u0E33\\u0E23\\u0E32\\u0E22\\u0E01\\u0E32\\u0E23\\u0E43\\u0E2B\\u0E21\\u0E48\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CompleteinvoiceComponent_ng_template_279_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 136)(1, \"P\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"br\");\n    i0.ɵɵtemplate(4, CompleteinvoiceComponent_ng_template_279_button_4_Template, 2, 0, \"button\", 137)(5, CompleteinvoiceComponent_ng_template_279_button_5_Template, 2, 0, \"button\", 137)(6, CompleteinvoiceComponent_ng_template_279_button_6_Template, 2, 0, \"button\", 138);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r2.textload, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.Btnimg);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.btnPDF);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.btnREpdf);\n  }\n}\nfunction CompleteinvoiceComponent_ng_template_281_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"b\");\n    i0.ɵɵtext(1, \"Images Billno\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CompleteinvoiceComponent_ng_template_281_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 143);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵpropertyInterpolate(\"src\", ctx_r2.ImageBillno, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction CompleteinvoiceComponent_ng_template_281_ngb_tab_5_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"b\");\n    i0.ɵɵtext(1, \"Images BookBank\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CompleteinvoiceComponent_ng_template_281_ngb_tab_5_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 143);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵpropertyInterpolate(\"src\", ctx_r2.ImageBookbank, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction CompleteinvoiceComponent_ng_template_281_ngb_tab_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ngb-tab\");\n    i0.ɵɵtemplate(1, CompleteinvoiceComponent_ng_template_281_ngb_tab_5_ng_template_1_Template, 2, 0, \"ng-template\", 82)(2, CompleteinvoiceComponent_ng_template_281_ngb_tab_5_ng_template_2_Template, 1, 1, \"ng-template\", 83);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CompleteinvoiceComponent_ng_template_281_ng_template_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"b\");\n    i0.ɵɵtext(1, \"Detail INV \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CompleteinvoiceComponent_ng_template_281_ng_template_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 121)(1, \"table\", 40)(2, \"thead\")(3, \"tr\");\n    i0.ɵɵelement(4, \"th\", 122);\n    i0.ɵɵelementStart(5, \"th\", 123);\n    i0.ɵɵtext(6, \"detail\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(7, \"tbody\")(8, \"tr\")(9, \"th\");\n    i0.ɵɵtext(10, \"Sales Id\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"td\", 124);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"tr\")(14, \"th\");\n    i0.ɵɵtext(15, \"SO No\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"td\", 124);\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"tr\")(19, \"th\");\n    i0.ɵɵtext(20, \"Invoice No\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"td\", 124);\n    i0.ɵɵtext(22);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"tr\")(24, \"th\");\n    i0.ɵɵtext(25, \"OrderAccount\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"td\", 124);\n    i0.ɵɵtext(27);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(28, \"tr\")(29, \"th\");\n    i0.ɵɵtext(30, \"Invoicingname\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"td\", 124);\n    i0.ɵɵtext(32);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(33, \"tr\")(34, \"th\");\n    i0.ɵɵtext(35, \"Invoice Date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(36, \"td\", 124);\n    i0.ɵɵtext(37);\n    i0.ɵɵpipe(38, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(39, \"tr\")(40, \"th\");\n    i0.ɵɵtext(41, \"Invoice DueDate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(42, \"td\", 124);\n    i0.ɵɵtext(43);\n    i0.ɵɵpipe(44, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(45, \"tr\")(46, \"th\");\n    i0.ɵɵtext(47, \"Deliveryname\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(48, \"td\", 124);\n    i0.ɵɵtext(49);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(50, \"tr\")(51, \"th\");\n    i0.ɵɵtext(52, \"\\u0E21\\u0E39\\u0E25\\u0E04\\u0E48\\u0E32\\u0E2A\\u0E34\\u0E19\\u0E04\\u0E49\\u0E32\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(53, \"td\", 124);\n    i0.ɵɵtext(54);\n    i0.ɵɵpipe(55, \"number\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(56, \"tr\")(57, \"th\");\n    i0.ɵɵtext(58, \"VAT\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(59, \"td\", 124);\n    i0.ɵɵtext(60);\n    i0.ɵɵpipe(61, \"number\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(62, \"tr\")(63, \"th\");\n    i0.ɵɵtext(64, \"\\u0E21\\u0E39\\u0E25\\u0E04\\u0E48\\u0E32\\u0E23\\u0E27\\u0E21\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(65, \"td\", 124);\n    i0.ɵɵtext(66);\n    i0.ɵɵpipe(67, \"number\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(68, \"tr\")(69, \"th\");\n    i0.ɵɵtext(70, \"Comment\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(71, \"td\", 124);\n    i0.ɵɵtext(72);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(12);\n    i0.ɵɵtextInterpolate(ctx_r2.SalesId);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r2.SONo);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r2.InvoiceNo);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r2.OrderAccount);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r2.Invoicingname);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(38, 12, ctx_r2.InvoiceDate, \"dd/MM/yyyy\"));\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(44, 15, ctx_r2.DueDate, \"dd/MM/yyyy\"));\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r2.Deliveryname);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(55, 18, ctx_r2.productsun, \"1.2-2\"));\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(61, 21, ctx_r2.VAT, \"1.2-2\"));\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(67, 24, ctx_r2.suntotal, \"1.2-2\"));\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.remarkshow, \" \");\n  }\n}\nfunction CompleteinvoiceComponent_ng_template_281_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r33 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 141)(1, \"ngb-tabset\")(2, \"ngb-tab\");\n    i0.ɵɵtemplate(3, CompleteinvoiceComponent_ng_template_281_ng_template_3_Template, 2, 0, \"ng-template\", 82)(4, CompleteinvoiceComponent_ng_template_281_ng_template_4_Template, 1, 1, \"ng-template\", 83);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, CompleteinvoiceComponent_ng_template_281_ngb_tab_5_Template, 3, 0, \"ngb-tab\", 142);\n    i0.ɵɵelementStart(6, \"ngb-tab\");\n    i0.ɵɵtemplate(7, CompleteinvoiceComponent_ng_template_281_ng_template_7_Template, 2, 0, \"ng-template\", 84)(8, CompleteinvoiceComponent_ng_template_281_ng_template_8_Template, 73, 27, \"ng-template\", 83);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(9, \"div\", 133)(10, \"button\", 135);\n    i0.ɵɵlistener(\"click\", function CompleteinvoiceComponent_ng_template_281_Template_button_click_10_listener() {\n      i0.ɵɵrestoreView(_r33);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.modalRefviewDetail.hide());\n    });\n    i0.ɵɵtext(11, \"Close\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.CkimgBookbank);\n  }\n}\nfunction CompleteinvoiceComponent_ng_template_283_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"b\");\n    i0.ɵɵtext(1, \"Images Billno\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CompleteinvoiceComponent_ng_template_283_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 143);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵpropertyInterpolate(\"src\", ctx_r2.ImageBillno, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction CompleteinvoiceComponent_ng_template_283_ng_template_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"b\");\n    i0.ɵɵtext(1, \"Images BookBank\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CompleteinvoiceComponent_ng_template_283_ng_template_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 143);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵpropertyInterpolate(\"src\", ctx_r2.ImageBookbank, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction CompleteinvoiceComponent_ng_template_283_ng_template_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"b\");\n    i0.ɵɵtext(1, \"Detail INV \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CompleteinvoiceComponent_ng_template_283_ng_template_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 121)(1, \"table\", 40)(2, \"thead\")(3, \"tr\");\n    i0.ɵɵelement(4, \"th\", 122);\n    i0.ɵɵelementStart(5, \"th\", 123);\n    i0.ɵɵtext(6, \"detail\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(7, \"tbody\")(8, \"tr\")(9, \"th\");\n    i0.ɵɵtext(10, \"Sales Id\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"td\", 124);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"tr\")(14, \"th\");\n    i0.ɵɵtext(15, \"SO No\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"td\", 124);\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"tr\")(19, \"th\");\n    i0.ɵɵtext(20, \"Invoice No\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"td\", 124);\n    i0.ɵɵtext(22);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"tr\")(24, \"th\");\n    i0.ɵɵtext(25, \"OrderAccount\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"td\", 124);\n    i0.ɵɵtext(27);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(28, \"tr\")(29, \"th\");\n    i0.ɵɵtext(30, \"Invoicingname\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"td\", 124);\n    i0.ɵɵtext(32);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(33, \"tr\")(34, \"th\");\n    i0.ɵɵtext(35, \"Invoice Date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(36, \"td\", 124);\n    i0.ɵɵtext(37);\n    i0.ɵɵpipe(38, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(39, \"tr\")(40, \"th\");\n    i0.ɵɵtext(41, \"Invoice DueDate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(42, \"td\", 124);\n    i0.ɵɵtext(43);\n    i0.ɵɵpipe(44, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(45, \"tr\")(46, \"th\");\n    i0.ɵɵtext(47, \"Deliveryname\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(48, \"td\", 124);\n    i0.ɵɵtext(49);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(50, \"tr\")(51, \"th\");\n    i0.ɵɵtext(52, \"\\u0E21\\u0E39\\u0E25\\u0E04\\u0E48\\u0E32\\u0E2A\\u0E34\\u0E19\\u0E04\\u0E49\\u0E32\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(53, \"td\", 124);\n    i0.ɵɵtext(54);\n    i0.ɵɵpipe(55, \"number\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(56, \"tr\")(57, \"th\");\n    i0.ɵɵtext(58, \"VAT\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(59, \"td\", 124);\n    i0.ɵɵtext(60);\n    i0.ɵɵpipe(61, \"number\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(62, \"tr\")(63, \"th\");\n    i0.ɵɵtext(64, \"\\u0E21\\u0E39\\u0E25\\u0E04\\u0E48\\u0E32\\u0E23\\u0E27\\u0E21\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(65, \"td\", 124);\n    i0.ɵɵtext(66);\n    i0.ɵɵpipe(67, \"number\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(68, \"tr\")(69, \"th\");\n    i0.ɵɵtext(70, \"Comment\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(71, \"td\", 124);\n    i0.ɵɵtext(72);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(12);\n    i0.ɵɵtextInterpolate(ctx_r2.SalesId);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r2.SONo);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r2.InvoiceNo);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r2.OrderAccount);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r2.Invoicingname);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(38, 12, ctx_r2.InvoiceDate, \"dd/MM/yyyy\"));\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(44, 15, ctx_r2.DueDate, \"dd/MM/yyyy\"));\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r2.Deliveryname);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(55, 18, ctx_r2.productsun, \"1.2-2\"));\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(61, 21, ctx_r2.VAT, \"1.2-2\"));\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(67, 24, ctx_r2.suntotal, \"1.2-2\"));\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.remarkshow, \" \");\n  }\n}\nfunction CompleteinvoiceComponent_ng_template_283_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r34 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 141)(1, \"ngb-tabset\")(2, \"ngb-tab\");\n    i0.ɵɵtemplate(3, CompleteinvoiceComponent_ng_template_283_ng_template_3_Template, 2, 0, \"ng-template\", 82)(4, CompleteinvoiceComponent_ng_template_283_ng_template_4_Template, 1, 1, \"ng-template\", 83);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"ngb-tab\");\n    i0.ɵɵtemplate(6, CompleteinvoiceComponent_ng_template_283_ng_template_6_Template, 2, 0, \"ng-template\", 82)(7, CompleteinvoiceComponent_ng_template_283_ng_template_7_Template, 1, 1, \"ng-template\", 83);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"ngb-tab\");\n    i0.ɵɵtemplate(9, CompleteinvoiceComponent_ng_template_283_ng_template_9_Template, 2, 0, \"ng-template\", 84)(10, CompleteinvoiceComponent_ng_template_283_ng_template_10_Template, 73, 27, \"ng-template\", 83);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(11, \"div\", 133)(12, \"button\", 135);\n    i0.ɵɵlistener(\"click\", function CompleteinvoiceComponent_ng_template_283_Template_button_click_12_listener() {\n      i0.ɵɵrestoreView(_r34);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.modalRefviewDetail.hide());\n    });\n    i0.ɵɵtext(13, \"Close\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction CompleteinvoiceComponent_ng_template_285_tr_40_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r36 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 106);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\", 114);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\", 114);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\", 106);\n    i0.ɵɵtext(8);\n    i0.ɵɵpipe(9, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"td\", 106);\n    i0.ɵɵtext(11);\n    i0.ɵɵpipe(12, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"td\", 106);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"td\", 115);\n    i0.ɵɵtext(16);\n    i0.ɵɵpipe(17, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"td\", 115);\n    i0.ɵɵtext(19);\n    i0.ɵɵpipe(20, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"td\", 115);\n    i0.ɵɵtext(22);\n    i0.ɵɵpipe(23, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"td\", 116)(25, \"input\", 117);\n    i0.ɵɵlistener(\"click\", function CompleteinvoiceComponent_ng_template_285_tr_40_Template_input_click_25_listener($event) {\n      const itembill_r37 = i0.ɵɵrestoreView(_r36).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.checkIfAllSelectedbookbank($event.target.checked, itembill_r37.noid));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(26, \"td\", 110)(27, \"button\", 155);\n    i0.ɵɵlistener(\"click\", function CompleteinvoiceComponent_ng_template_285_tr_40_Template_button_click_27_listener() {\n      const itembill_r37 = i0.ɵɵrestoreView(_r36).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      const templateViewDetail_r38 = i0.ɵɵreference(282);\n      return i0.ɵɵresetView(ctx_r2.openModalviewDetail(templateViewDetail_r38, itembill_r37.invoiceid, itembill_r37.orderaccount, itembill_r37.salesid, itembill_r37.invoicename, itembill_r37.invoicedate, itembill_r37.duedate, itembill_r37.delivery, itembill_r37.salesbalance, itembill_r37.sumtax, itembill_r37.invoiceamount, itembill_r37.saleman, itembill_r37.remark, itembill_r37.urlimg, itembill_r37.imgbookbank));\n    });\n    i0.ɵɵtext(28);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const itembill_r37 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(35, _c3, ctx_r2.getColorbookbank(itembill_r37.remarkst, itembill_r37.imgbookbank)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(itembill_r37.noid + 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(37, _c3, ctx_r2.getColorbookbank(itembill_r37.remarkst, itembill_r37.imgbookbank)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(itembill_r37.salesid);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(39, _c3, ctx_r2.getColorbookbank(itembill_r37.remarkst, itembill_r37.imgbookbank)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(itembill_r37.invoiceid);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(41, _c3, ctx_r2.getColorbookbank(itembill_r37.remarkst, itembill_r37.imgbookbank)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(9, 20, itembill_r37.invoicedate, \"dd/MM/yyyy\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(43, _c3, ctx_r2.getColorbookbank(itembill_r37.remarkst, itembill_r37.imgbookbank)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(12, 23, itembill_r37.duedate, \"dd/MM/yyyy\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(45, _c3, ctx_r2.getColorbookbank(itembill_r37.remarkst, itembill_r37.imgbookbank)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(itembill_r37.payment);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(47, _c3, ctx_r2.getColorbookbank(itembill_r37.remarkst, itembill_r37.imgbookbank)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(17, 26, itembill_r37.salesbalance, \"1.2-2\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(49, _c3, ctx_r2.getColorbookbank(itembill_r37.remarkst, itembill_r37.imgbookbank)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(20, 29, itembill_r37.sumtax, \"1.2-2\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(51, _c3, ctx_r2.getColorbookbank(itembill_r37.remarkst, itembill_r37.imgbookbank)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(23, 32, itembill_r37.invoiceamount, \"1.2-2\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"checked\", itembill_r37.check);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", itembill_r37.numrun, \" \");\n  }\n}\nfunction CompleteinvoiceComponent_ng_template_285_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r35 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 62)(1, \"h4\", 128);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 129);\n    i0.ɵɵlistener(\"click\", function CompleteinvoiceComponent_ng_template_285_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r35);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.modalRefupbookbank.hide());\n    });\n    i0.ɵɵelementStart(4, \"span\", 65);\n    i0.ɵɵtext(5, \"\\u00D7\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(6, \"div\", 141)(7, \"div\", 144)(8, \"select\", 29);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function CompleteinvoiceComponent_ng_template_285_Template_select_ngModelChange_8_listener($event) {\n      i0.ɵɵrestoreView(_r35);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.viewBookbank, $event) || (ctx_r2.viewBookbank = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"change\", function CompleteinvoiceComponent_ng_template_285_Template_select_change_8_listener() {\n      i0.ɵɵrestoreView(_r35);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.searchviewBookbank());\n    });\n    i0.ɵɵelementStart(9, \"option\", 30);\n    i0.ɵɵtext(10, \"\\u0E40\\u0E25\\u0E37\\u0E2D\\u0E01\\u0E17\\u0E38\\u0E38\\u0E01\\u0E23\\u0E32\\u0E22\\u0E01\\u0E32\\u0E23\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"option\", 145);\n    i0.ɵɵtext(12, \"\\u0E23\\u0E2Dupbookbank\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"option\", 146);\n    i0.ɵɵtext(14, \"\\u0E23\\u0E2D Approve\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(15, \"div\", 68)(16, \"table\", 69)(17, \"thead\")(18, \"tr\", 41);\n    i0.ɵɵelement(19, \"th\", 42);\n    i0.ɵɵelementStart(20, \"th\", 42);\n    i0.ɵɵtext(21, \"SO No\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"th\", 42);\n    i0.ɵɵtext(23, \"Invoice No\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"th\", 42);\n    i0.ɵɵtext(25, \"Invoice Date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"th\", 42);\n    i0.ɵɵtext(27, \"Due Date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"th\", 70);\n    i0.ɵɵtext(29, \"\\u0E0A\\u0E33\\u0E23\\u0E30\\u0E40\\u0E07\\u0E34\\u0E19\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"th\", 70);\n    i0.ɵɵtext(31, \"\\u0E21\\u0E39\\u0E25\\u0E04\\u0E48\\u0E32\\u0E2A\\u0E34\\u0E19\\u0E04\\u0E49\\u0E32\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"th\", 42);\n    i0.ɵɵtext(33, \"VAT\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(34, \"th\", 70);\n    i0.ɵɵtext(35, \"\\u0E21\\u0E39\\u0E25\\u0E04\\u0E48\\u0E32\\u0E23\\u0E27\\u0E21\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(36, \"th\", 71)(37, \"input\", 72);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function CompleteinvoiceComponent_ng_template_285_Template_input_ngModelChange_37_listener($event) {\n      i0.ɵɵrestoreView(_r35);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.testcheck, $event) || (ctx_r2.testcheck = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"click\", function CompleteinvoiceComponent_ng_template_285_Template_input_click_37_listener($event) {\n      i0.ɵɵrestoreView(_r35);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.selectAllbookbank($event.target.checked));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(38, \"td\", 49);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(39, \"tbody\");\n    i0.ɵɵtemplate(40, CompleteinvoiceComponent_ng_template_285_tr_40_Template, 29, 53, \"tr\", 73);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(41, \"div\", 147)(42, \"input\", 148, 12);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function CompleteinvoiceComponent_ng_template_285_Template_input_ngModelChange_42_listener($event) {\n      i0.ɵɵrestoreView(_r35);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.Flielist, $event) || (ctx_r2.Flielist = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"change\", function CompleteinvoiceComponent_ng_template_285_Template_input_change_42_listener($event) {\n      i0.ɵɵrestoreView(_r35);\n      const ctx_r2 = i0.ɵɵnextContext();\n      const templateShow_r28 = i0.ɵɵreference(280);\n      return i0.ɵɵresetView(ctx_r2.handleFileInput($event.target.files, templateShow_r28));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(44, \"label\", 149);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function CompleteinvoiceComponent_ng_template_285_Template_label_ngModelChange_44_listener($event) {\n      i0.ɵɵrestoreView(_r35);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.textload, $event) || (ctx_r2.textload = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtext(45);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(46, \"div\", 150);\n    i0.ɵɵlistener(\"collapsed\", function CompleteinvoiceComponent_ng_template_285_Template_div_collapsed_46_listener() {\n      i0.ɵɵrestoreView(_r35);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.collapsed());\n    })(\"expanded\", function CompleteinvoiceComponent_ng_template_285_Template_div_expanded_46_listener() {\n      i0.ɵɵrestoreView(_r35);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.expanded());\n    });\n    i0.ɵɵelementStart(47, \"div\", 151)(48, \"form\", 152, 13);\n    i0.ɵɵelement(50, \"img\", 153);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(51, \"div\")(52, \"div\", 74);\n    i0.ɵɵtext(53);\n    i0.ɵɵpipe(54, \"number\");\n    i0.ɵɵpipe(55, \"number\");\n    i0.ɵɵpipe(56, \"number\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(57, \"div\", 133)(58, \"button\", 154);\n    i0.ɵɵlistener(\"click\", function CompleteinvoiceComponent_ng_template_285_Template_button_click_58_listener() {\n      i0.ɵɵrestoreView(_r35);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.isCollapsed = !ctx_r2.isCollapsed);\n    });\n    i0.ɵɵtext(59, \"ViewBookbank \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(60, \"button\", 134);\n    i0.ɵɵlistener(\"click\", function CompleteinvoiceComponent_ng_template_285_Template_button_click_60_listener() {\n      i0.ɵɵrestoreView(_r35);\n      const ctx_r2 = i0.ɵɵnextContext();\n      const templateShow_r28 = i0.ɵɵreference(280);\n      const templateView_r13 = i0.ɵɵreference(286);\n      return i0.ɵɵresetView(ctx_r2.onUploadbookbank(templateShow_r28, templateView_r13));\n    });\n    i0.ɵɵtext(61, \"UploadBookbank\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(62, \"button\", 135);\n    i0.ɵɵlistener(\"click\", function CompleteinvoiceComponent_ng_template_285_Template_button_click_62_listener() {\n      i0.ɵɵrestoreView(_r35);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.endtemplateView());\n    });\n    i0.ɵɵtext(63, \"Close\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" Company : \", ctx_r2.showIDso, \"\");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.viewBookbank);\n    i0.ɵɵadvance(29);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.testcheck);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.Bookbank);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.Flielist);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.textload);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"\", ctx_r2.textload, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"collapse\", ctx_r2.isCollapsed);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"src\", ctx_r2.imageUrl, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate3(\" \\u0E21\\u0E39\\u0E25\\u0E04\\u0E48\\u0E32\\u0E23\\u0E27\\u0E21 : \", i0.ɵɵpipeBind2(54, 15, ctx_r2.trueproductprice, \"1.2-2\"), \" | Vat : \", i0.ɵɵpipeBind2(55, 18, ctx_r2.truesumvat, \"1.2-2\"), \" | \\u0E23\\u0E32\\u0E04\\u0E32\\u0E2A\\u0E38\\u0E17\\u0E18\\u0E34 : \", i0.ɵɵpipeBind2(56, 21, ctx_r2.truesumprice, \"1.2-2\"), \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"disabled\", ctx_r2.selectedFile == null);\n    i0.ɵɵattribute(\"aria-expanded\", !ctx_r2.isCollapsed);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r2.selectedFile == null || ctx_r2.total == 0);\n  }\n}\nfunction CompleteinvoiceComponent_ng_template_287_tr_33_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r40 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 106);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\", 114);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\", 114);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\", 106);\n    i0.ɵɵtext(8);\n    i0.ɵɵpipe(9, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"td\", 106);\n    i0.ɵɵtext(11);\n    i0.ɵɵpipe(12, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"td\", 106);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"td\", 115);\n    i0.ɵɵtext(16);\n    i0.ɵɵpipe(17, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"td\", 115);\n    i0.ɵɵtext(19);\n    i0.ɵɵpipe(20, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"td\", 115);\n    i0.ɵɵtext(22);\n    i0.ɵɵpipe(23, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"td\", 116)(25, \"input\", 117);\n    i0.ɵɵlistener(\"click\", function CompleteinvoiceComponent_ng_template_287_tr_33_Template_input_click_25_listener($event) {\n      const itembill_r41 = i0.ɵɵrestoreView(_r40).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.checkIfAllSelected($event.target.checked, itembill_r41.noid));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(26, \"td\", 110)(27, \"button\", 155);\n    i0.ɵɵlistener(\"click\", function CompleteinvoiceComponent_ng_template_287_tr_33_Template_button_click_27_listener() {\n      const itembill_r41 = i0.ɵɵrestoreView(_r40).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      const templateViewDetailApprove_r42 = i0.ɵɵreference(284);\n      return i0.ɵɵresetView(ctx_r2.openModalBillViewDetail(templateViewDetailApprove_r42, itembill_r41.invoiceid, itembill_r41.orderaccount, itembill_r41.salesid, itembill_r41.invoicename, itembill_r41.invoicedate, itembill_r41.duedate, itembill_r41.delivery, itembill_r41.salesbalance, itembill_r41.sumtax, itembill_r41.invoiceamount, itembill_r41.saleman, itembill_r41.remark, itembill_r41.urlimg, itembill_r41.imgbookbank));\n    });\n    i0.ɵɵtext(28);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const itembill_r41 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(35, _c3, ctx_r2.getColor2(itembill_r41.remarkst)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(itembill_r41.noid + 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(37, _c3, ctx_r2.getColor2(itembill_r41.remarkst)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(itembill_r41.salesid);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(39, _c3, ctx_r2.getColor2(itembill_r41.remarkst)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(itembill_r41.invoiceid);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(41, _c3, ctx_r2.getColor2(itembill_r41.remarkst)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(9, 20, itembill_r41.invoicedate, \"dd/MM/yyyy\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(43, _c3, ctx_r2.getColor2(itembill_r41.remarkst)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(12, 23, itembill_r41.duedate, \"dd/MM/yyyy\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(45, _c3, ctx_r2.getColor2(itembill_r41.remarkst)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(itembill_r41.payment);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(47, _c3, ctx_r2.getColor2(itembill_r41.remarkst)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(17, 26, itembill_r41.salesbalance, \"1.2-2\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(49, _c3, ctx_r2.getColor2(itembill_r41.remarkst)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(20, 29, itembill_r41.sumtax, \"1.2-2\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(51, _c3, ctx_r2.getColor2(itembill_r41.remarkst)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(23, 32, itembill_r41.invoiceamount, \"1.2-2\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"checked\", itembill_r41.check);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", itembill_r41.numrun, \" \");\n  }\n}\nfunction CompleteinvoiceComponent_ng_template_287_button_45_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r44 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 134);\n    i0.ɵɵlistener(\"click\", function CompleteinvoiceComponent_ng_template_287_button_45_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r44);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      const templateShow_r28 = i0.ɵɵreference(280);\n      return i0.ɵɵresetView(ctx_r2.onUpload(templateShow_r28));\n    });\n    i0.ɵɵtext(1, \"Approve\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r2.total == 0);\n  }\n}\nfunction CompleteinvoiceComponent_ng_template_287_button_46_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r45 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 134);\n    i0.ɵɵlistener(\"click\", function CompleteinvoiceComponent_ng_template_287_button_46_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r45);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      const templateShow_r28 = i0.ɵɵreference(280);\n      return i0.ɵɵresetView(ctx_r2.onRevert(templateShow_r28));\n    });\n    i0.ɵɵtext(1, \"Revert\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r2.total == 0);\n  }\n}\nfunction CompleteinvoiceComponent_ng_template_287_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r39 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 62)(1, \"h4\", 128);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 129);\n    i0.ɵɵlistener(\"click\", function CompleteinvoiceComponent_ng_template_287_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r39);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.ModalRefApprove.hide());\n    });\n    i0.ɵɵelementStart(4, \"span\", 65);\n    i0.ɵɵtext(5, \"\\u00D7\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(6, \"div\", 141)(7, \"pagination-controls\", 67);\n    i0.ɵɵlistener(\"pageChange\", function CompleteinvoiceComponent_ng_template_287_Template_pagination_controls_pageChange_7_listener($event) {\n      i0.ɵɵrestoreView(_r39);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.p = $event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 68)(9, \"table\", 69)(10, \"thead\")(11, \"tr\", 41);\n    i0.ɵɵelement(12, \"th\", 42);\n    i0.ɵɵelementStart(13, \"th\", 42);\n    i0.ɵɵtext(14, \"SO No\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"th\", 42);\n    i0.ɵɵtext(16, \"Invoice No\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"th\", 42);\n    i0.ɵɵtext(18, \"Invoice Date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"th\", 42);\n    i0.ɵɵtext(20, \"Due Date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"th\", 70);\n    i0.ɵɵtext(22, \"\\u0E0A\\u0E33\\u0E23\\u0E30\\u0E40\\u0E07\\u0E34\\u0E19\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"th\", 70);\n    i0.ɵɵtext(24, \"\\u0E21\\u0E39\\u0E25\\u0E04\\u0E48\\u0E32\\u0E2A\\u0E34\\u0E19\\u0E04\\u0E49\\u0E32\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"th\", 42);\n    i0.ɵɵtext(26, \"VAT\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"th\", 70);\n    i0.ɵɵtext(28, \"\\u0E21\\u0E39\\u0E25\\u0E04\\u0E48\\u0E32\\u0E23\\u0E27\\u0E21\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"th\", 71)(30, \"input\", 72);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function CompleteinvoiceComponent_ng_template_287_Template_input_ngModelChange_30_listener($event) {\n      i0.ɵɵrestoreView(_r39);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.testcheck, $event) || (ctx_r2.testcheck = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"click\", function CompleteinvoiceComponent_ng_template_287_Template_input_click_30_listener($event) {\n      i0.ɵɵrestoreView(_r39);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.selectAll($event.target.checked));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(31, \"td\", 49);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(32, \"tbody\");\n    i0.ɵɵtemplate(33, CompleteinvoiceComponent_ng_template_287_tr_33_Template, 29, 53, \"tr\", 73);\n    i0.ɵɵpipe(34, \"paginate\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(35, \"div\", 74);\n    i0.ɵɵtext(36);\n    i0.ɵɵpipe(37, \"number\");\n    i0.ɵɵpipe(38, \"number\");\n    i0.ɵɵpipe(39, \"number\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(40, \"div\", 156)(41, \"div\", 77);\n    i0.ɵɵtext(42);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(43, \"button\", 157);\n    i0.ɵɵlistener(\"click\", function CompleteinvoiceComponent_ng_template_287_Template_button_click_43_listener() {\n      i0.ɵɵrestoreView(_r39);\n      const ctx_r2 = i0.ɵɵnextContext();\n      const ModalremarkNew_r43 = i0.ɵɵreference(290);\n      return i0.ɵɵresetView(ctx_r2.Modalremark(ModalremarkNew_r43));\n    });\n    i0.ɵɵtext(44, \" Comment \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(45, CompleteinvoiceComponent_ng_template_287_button_45_Template, 2, 1, \"button\", 158)(46, CompleteinvoiceComponent_ng_template_287_button_46_Template, 2, 1, \"button\", 158);\n    i0.ɵɵelementStart(47, \"button\", 135);\n    i0.ɵɵlistener(\"click\", function CompleteinvoiceComponent_ng_template_287_Template_button_click_47_listener() {\n      i0.ɵɵrestoreView(_r39);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.ModalRefApprove.hide());\n    });\n    i0.ɵɵtext(48, \"Close\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"BillNo : \", ctx_r2.showbillno, \" \");\n    i0.ɵɵadvance(28);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.testcheck);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind2(34, 9, ctx_r2.BillId, i0.ɵɵpureFunction1(21, _c2, ctx_r2.p)));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate3(\" \\u0E21\\u0E39\\u0E25\\u0E04\\u0E48\\u0E32\\u0E23\\u0E27\\u0E21 : \", i0.ɵɵpipeBind2(37, 12, ctx_r2.trueproductprice, \"1.2-2\"), \" | Vat : \", i0.ɵɵpipeBind2(38, 15, ctx_r2.truesumvat, \"1.2-2\"), \" | \\u0E23\\u0E32\\u0E04\\u0E32\\u0E2A\\u0E38\\u0E17\\u0E18\\u0E34 : \", i0.ɵɵpipeBind2(39, 18, ctx_r2.truesumprice, \"1.2-2\"), \" \");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \\u0E23\\u0E32\\u0E22\\u0E17\\u0E35\\u0E48\\u0E40\\u0E25\\u0E37\\u0E2D\\u0E01\\u0E17\\u0E31\\u0E49\\u0E07\\u0E2B\\u0E21\\u0E14 : \", ctx_r2.total, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.SearchComplete_ && ctx_r2.datalogin[0].id_group_user == \"1860Administrator\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.SearchCompleteED_);\n  }\n}\nfunction CompleteinvoiceComponent_ng_template_289_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r47 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"button\", 134);\n    i0.ɵɵlistener(\"click\", function CompleteinvoiceComponent_ng_template_289_div_7_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r47);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      const templateShow_r28 = i0.ɵɵreference(280);\n      return i0.ɵɵresetView(ctx_r2.addremark(templateShow_r28));\n    });\n    i0.ɵɵtext(2, \"\\u0E1A\\u0E31\\u0E19\\u0E17\\u0E36\\u0E01\\u0E23\\u0E32\\u0E22\\u0E25\\u0E30\\u0E40\\u0E2D\\u0E35\\u0E22\\u0E14\\u0E41\\u0E1A\\u0E1A\\u0E44\\u0E21\\u0E48 Approve \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r2.total == 0 || ctx_r2.Remark == \"\");\n  }\n}\nfunction CompleteinvoiceComponent_ng_template_289_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r48 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"button\", 134);\n    i0.ɵɵlistener(\"click\", function CompleteinvoiceComponent_ng_template_289_div_8_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r48);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.addremarkRevert());\n    });\n    i0.ɵɵtext(2, \"\\u0E1A\\u0E31\\u0E19\\u0E17\\u0E36\\u0E01\\u0E23\\u0E32\\u0E22\\u0E25\\u0E30\\u0E40\\u0E2D\\u0E35\\u0E22\\u0E14\\u0E41\\u0E1A\\u0E1A\\u0E44\\u0E21\\u0E48 Revert \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r2.total == 0 || ctx_r2.Remark == \"\");\n  }\n}\nfunction CompleteinvoiceComponent_ng_template_289_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r46 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 66);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementStart(2, \"div\", 159)(3, \"div\", 160)(4, \"label\", 161);\n    i0.ɵɵtext(5, \"Comment:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"textarea\", 162);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function CompleteinvoiceComponent_ng_template_289_Template_textarea_ngModelChange_6_listener($event) {\n      i0.ɵɵrestoreView(_r46);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.Remark, $event) || (ctx_r2.Remark = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(7, CompleteinvoiceComponent_ng_template_289_div_7_Template, 3, 1, \"div\", 142)(8, CompleteinvoiceComponent_ng_template_289_div_8_Template, 3, 1, \"div\", 142);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"\", ctx_r2.altBill, \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.Remark);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.SearchComplete_);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.SearchCompleteED_);\n  }\n}\nfunction CompleteinvoiceComponent_ng_template_291_tr_32_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r50 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 106);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\", 165);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\", 165);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"th\", 165);\n    i0.ɵɵtext(8);\n    i0.ɵɵpipe(9, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"th\", 165);\n    i0.ɵɵtext(11);\n    i0.ɵɵpipe(12, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"td\", 115);\n    i0.ɵɵtext(14);\n    i0.ɵɵpipe(15, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"td\", 115);\n    i0.ɵɵtext(17);\n    i0.ɵɵpipe(18, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"td\", 115);\n    i0.ɵɵtext(20);\n    i0.ɵɵpipe(21, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"td\", 125)(23, \"button\", 166);\n    i0.ɵɵlistener(\"click\", function CompleteinvoiceComponent_ng_template_291_tr_32_Template_button_click_23_listener() {\n      const item_r51 = i0.ɵɵrestoreView(_r50).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      const templateViewDetailApprove_r42 = i0.ɵɵreference(284);\n      return i0.ɵɵresetView(ctx_r2.openModalimg(templateViewDetailApprove_r42, item_r51.nameimg, item_r51.invoiceid, item_r51.orderaccount, item_r51.salesid, item_r51.invoicename, item_r51.invoicedate, item_r51.duedate, item_r51.delivery, item_r51.salesbalance, item_r51.sumtax, item_r51.invoiceamount, item_r51.saleman, item_r51.remark, item_r51.imgbookbank));\n    });\n    i0.ɵɵtext(24);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const item_r51 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(32, _c3, ctx_r2.getColor2(item_r51.remarkst)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r51.noid + 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(34, _c3, ctx_r2.getColor2(item_r51.remarkst)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r51.salesid);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(36, _c3, ctx_r2.getColor2(item_r51.remarkst)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r51.invoiceid);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(38, _c3, ctx_r2.getColor2(item_r51.remarkst)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(9, 17, item_r51.invoicedate, \"dd/MM/yyyy\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(40, _c3, ctx_r2.getColor2(item_r51.remarkst)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(12, 20, item_r51.duedate, \"dd/MM/yyyy\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(42, _c3, ctx_r2.getColor2(item_r51.remarkst)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(15, 23, item_r51.salesbalance, \"1.2-2\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(44, _c3, ctx_r2.getColor2(item_r51.remarkst)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(18, 26, item_r51.sumtax, \"1.2-2\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(46, _c3, ctx_r2.getColor2(item_r51.remarkst)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(21, 29, item_r51.invoiceamount, \"1.2-2\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", item_r51.numrun, \" \");\n  }\n}\nfunction CompleteinvoiceComponent_ng_template_291_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r49 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 62)(1, \"h5\", 128);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 129);\n    i0.ɵɵlistener(\"click\", function CompleteinvoiceComponent_ng_template_291_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r49);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.ModalRefApprove.hide());\n    });\n    i0.ɵɵelementStart(4, \"span\", 65);\n    i0.ɵɵtext(5, \"\\u00D7\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(6, \"div\", 141)(7, \"div\", 89)(8, \"div\", 90)(9, \"input\", 163);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function CompleteinvoiceComponent_ng_template_291_Template_input_ngModelChange_9_listener($event) {\n      i0.ɵɵrestoreView(_r49);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.dateinvid, $event) || (ctx_r2.dateinvid = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"change\", function CompleteinvoiceComponent_ng_template_291_Template_input_change_9_listener($event) {\n      i0.ɵɵrestoreView(_r49);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.searchSuccess($event.target.value));\n    })(\"keyup\", function CompleteinvoiceComponent_ng_template_291_Template_input_keyup_9_listener($event) {\n      i0.ɵɵrestoreView(_r49);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.searchSuccess($event.target.value));\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(10, \"pagination-controls\", 67);\n    i0.ɵɵlistener(\"pageChange\", function CompleteinvoiceComponent_ng_template_291_Template_pagination_controls_pageChange_10_listener($event) {\n      i0.ɵɵrestoreView(_r49);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.p = $event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 92)(12, \"table\", 93)(13, \"thead\")(14, \"tr\");\n    i0.ɵɵelement(15, \"th\", 42);\n    i0.ɵɵelementStart(16, \"th\", 42);\n    i0.ɵɵtext(17, \"SO No\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"th\", 164);\n    i0.ɵɵtext(19, \"Invoice No\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"th\", 164);\n    i0.ɵɵtext(21, \"Invoice Date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"th\", 42);\n    i0.ɵɵtext(23, \"Due Date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"th\", 42);\n    i0.ɵɵtext(25, \"\\u0E21\\u0E39\\u0E25\\u0E04\\u0E48\\u0E32\\u0E2A\\u0E34\\u0E19\\u0E04\\u0E49\\u0E32\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"th\", 42);\n    i0.ɵɵtext(27, \"VAT\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"th\", 42);\n    i0.ɵɵtext(29, \"\\u0E21\\u0E39\\u0E25\\u0E04\\u0E48\\u0E32\\u0E23\\u0E27\\u0E21\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(30, \"th\", 42);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(31, \"tbody\");\n    i0.ɵɵtemplate(32, CompleteinvoiceComponent_ng_template_291_tr_32_Template, 25, 48, \"tr\", 73);\n    i0.ɵɵpipe(33, \"paginate\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(34, \"div\", 156)(35, \"button\", 135);\n    i0.ɵɵlistener(\"click\", function CompleteinvoiceComponent_ng_template_291_Template_button_click_35_listener() {\n      i0.ɵɵrestoreView(_r49);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.ModalRefApprove.hide());\n    });\n    i0.ɵɵtext(36, \"Close\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"Success : \", ctx_r2.showbillno, \" \");\n    i0.ɵɵadvance(7);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.dateinvid);\n    i0.ɵɵadvance(23);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind2(33, 3, ctx_r2.INVSuccess, i0.ɵɵpureFunction1(6, _c2, ctx_r2.p)));\n  }\n}\nfunction CompleteinvoiceComponent_ng_template_293_tr_31_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r53 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 106);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\", 114);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\", 114);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\", 106);\n    i0.ɵɵtext(8);\n    i0.ɵɵpipe(9, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"td\", 106);\n    i0.ɵɵtext(11);\n    i0.ɵɵpipe(12, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"td\", 106);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"td\", 115);\n    i0.ɵɵtext(16);\n    i0.ɵɵpipe(17, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"td\", 115);\n    i0.ɵɵtext(19);\n    i0.ɵɵpipe(20, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"td\", 115);\n    i0.ɵɵtext(22);\n    i0.ɵɵpipe(23, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"td\", 110)(25, \"button\", 155);\n    i0.ɵɵlistener(\"click\", function CompleteinvoiceComponent_ng_template_293_tr_31_Template_button_click_25_listener() {\n      const itembill_r54 = i0.ɵɵrestoreView(_r53).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      const templateViewDetailApprove_r42 = i0.ɵɵreference(284);\n      return i0.ɵɵresetView(ctx_r2.openModalBillViewDetail(templateViewDetailApprove_r42, itembill_r54.invoiceid, itembill_r54.orderaccount, itembill_r54.salesid, itembill_r54.invoicename, itembill_r54.invoicedate, itembill_r54.duedate, itembill_r54.delivery, itembill_r54.salesbalance, itembill_r54.sumtax, itembill_r54.invoiceamount, itembill_r54.saleman, itembill_r54.remark, itembill_r54.urlimg, itembill_r54.imgbookbank));\n    });\n    i0.ɵɵtext(26);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const itembill_r54 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(34, _c3, ctx_r2.getColor2(itembill_r54.remarkst)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(itembill_r54.noid + 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(36, _c3, ctx_r2.getColor2(itembill_r54.remarkst)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(itembill_r54.salesid);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(38, _c3, ctx_r2.getColor2(itembill_r54.remarkst)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(itembill_r54.invoiceid);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(40, _c3, ctx_r2.getColor2(itembill_r54.remarkst)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(9, 19, itembill_r54.invoicedate, \"dd/MM/yyyy\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(42, _c3, ctx_r2.getColor2(itembill_r54.remarkst)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(12, 22, itembill_r54.duedate, \"dd/MM/yyyy\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(44, _c3, ctx_r2.getColor2(itembill_r54.remarkst)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(itembill_r54.payment);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(46, _c3, ctx_r2.getColor2(itembill_r54.remarkst)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(17, 25, itembill_r54.salesbalance, \"1.2-2\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(48, _c3, ctx_r2.getColor2(itembill_r54.remarkst)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(20, 28, itembill_r54.sumtax, \"1.2-2\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(50, _c3, ctx_r2.getColor2(itembill_r54.remarkst)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(23, 31, itembill_r54.invoiceamount, \"1.2-2\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", itembill_r54.numrun, \" \");\n  }\n}\nfunction CompleteinvoiceComponent_ng_template_293_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r52 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 62)(1, \"h4\", 128);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 129);\n    i0.ɵɵlistener(\"click\", function CompleteinvoiceComponent_ng_template_293_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r52);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.ModalRefApprove.hide());\n    });\n    i0.ɵɵelementStart(4, \"span\", 65);\n    i0.ɵɵtext(5, \"\\u00D7\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(6, \"div\", 141)(7, \"pagination-controls\", 67);\n    i0.ɵɵlistener(\"pageChange\", function CompleteinvoiceComponent_ng_template_293_Template_pagination_controls_pageChange_7_listener($event) {\n      i0.ɵɵrestoreView(_r52);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.p = $event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 68)(9, \"table\", 69)(10, \"thead\")(11, \"tr\", 41);\n    i0.ɵɵelement(12, \"th\", 42);\n    i0.ɵɵelementStart(13, \"th\", 42);\n    i0.ɵɵtext(14, \"SO No\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"th\", 42);\n    i0.ɵɵtext(16, \"Invoice No\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"th\", 42);\n    i0.ɵɵtext(18, \"Invoice Date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"th\", 42);\n    i0.ɵɵtext(20, \"Due Date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"th\", 70);\n    i0.ɵɵtext(22, \"\\u0E0A\\u0E33\\u0E23\\u0E30\\u0E40\\u0E07\\u0E34\\u0E19\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"th\", 70);\n    i0.ɵɵtext(24, \"\\u0E21\\u0E39\\u0E25\\u0E04\\u0E48\\u0E32\\u0E2A\\u0E34\\u0E19\\u0E04\\u0E49\\u0E32\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"th\", 42);\n    i0.ɵɵtext(26, \"VAT\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"th\", 70);\n    i0.ɵɵtext(28, \"\\u0E21\\u0E39\\u0E25\\u0E04\\u0E48\\u0E32\\u0E23\\u0E27\\u0E21\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(29, \"td\", 49);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(30, \"tbody\");\n    i0.ɵɵtemplate(31, CompleteinvoiceComponent_ng_template_293_tr_31_Template, 27, 52, \"tr\", 73);\n    i0.ɵɵpipe(32, \"paginate\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(33, \"div\", 156)(34, \"div\", 95);\n    i0.ɵɵtext(35);\n    i0.ɵɵpipe(36, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(37, \"div\", 96);\n    i0.ɵɵtext(38);\n    i0.ɵɵpipe(39, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(40, \"div\", 95);\n    i0.ɵɵtext(41);\n    i0.ɵɵpipe(42, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(43, \"button\", 135);\n    i0.ɵɵlistener(\"click\", function CompleteinvoiceComponent_ng_template_293_Template_button_click_43_listener() {\n      i0.ɵɵrestoreView(_r52);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.ModalRefApprove.hide());\n    });\n    i0.ɵɵtext(44, \"Close\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"BillNo : \", ctx_r2.showbillno, \" \");\n    i0.ɵɵadvance(29);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind2(32, 5, ctx_r2.BillId, i0.ɵɵpureFunction1(17, _c2, ctx_r2.p)));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \\u0E21\\u0E39\\u0E25\\u0E04\\u0E48\\u0E32\\u0E2A\\u0E34\\u0E19\\u0E04\\u0E49\\u0E32 : \", i0.ɵɵpipeBind2(36, 8, ctx_r2.productpricesuccess, \"1.2-2\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \\u0E21\\u0E39\\u0E25\\u0E04\\u0E48\\u0E32\\u0E20\\u0E32\\u0E29\\u0E35 : \", i0.ɵɵpipeBind2(39, 11, ctx_r2.sumvatsuccess, \"1.2-2\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \\u0E21\\u0E39\\u0E25\\u0E04\\u0E48\\u0E32\\u0E23\\u0E27\\u0E21 : \", i0.ɵɵpipeBind2(42, 14, ctx_r2.sumpricesuccess, \"1.2-2\"), \" \");\n  }\n}\nfunction CompleteinvoiceComponent_ng_template_295_P_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"P\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelement(2, \"br\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelement(4, \"br\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"\\u0E08\\u0E33\\u0E19\\u0E27\\u0E19\\u0E23\\u0E32\\u0E22\\u0E01\\u0E32\\u0E23\\u0E17\\u0E31\\u0E49\\u0E07\\u0E2B\\u0E21\\u0E14 : \", ctx_r2.numDataDelete, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\\u0E08\\u0E33\\u0E19\\u0E27\\u0E19\\u0E23\\u0E39\\u0E1B BooKBank : \", ctx_r2.numBookbankDelete, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\\u0E08\\u0E33\\u0E19\\u0E27\\u0E19\\u0E23\\u0E39\\u0E1B Invoice : \", ctx_r2.numINVDelete, \" \");\n  }\n}\nfunction CompleteinvoiceComponent_ng_template_295_p_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r2.txtDeleteShow);\n  }\n}\nfunction CompleteinvoiceComponent_ng_template_295_div_9_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \\u0E08\\u0E33\\u0E19\\u0E27\\u0E19\\u0E23\\u0E32\\u0E22\\u0E01\\u0E32\\u0E23\\u0E17\\u0E31\\u0E49\\u0E07\\u0E2B\\u0E21\\u0E14\\u0E17\\u0E35\\u0E48\\u0E25\\u0E1A : \", ctx_r2.numDataDelete, \" \");\n  }\n}\nfunction CompleteinvoiceComponent_ng_template_295_div_9_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \\u0E08\\u0E33\\u0E19\\u0E27\\u0E19\\u0E23\\u0E39\\u0E1B BooKBank \\u0E17\\u0E35\\u0E48\\u0E25\\u0E1A : \", ctx_r2.numBookbankDelete, \" \");\n  }\n}\nfunction CompleteinvoiceComponent_ng_template_295_div_9_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \\u0E08\\u0E33\\u0E19\\u0E27\\u0E19\\u0E23\\u0E39\\u0E1B Invoice \\u0E17\\u0E35\\u0E48\\u0E25\\u0E1A : \", ctx_r2.numINVDelete, \" \");\n  }\n}\nfunction CompleteinvoiceComponent_ng_template_295_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, CompleteinvoiceComponent_ng_template_295_div_9_div_1_Template, 2, 1, \"div\", 142)(2, CompleteinvoiceComponent_ng_template_295_div_9_div_2_Template, 2, 1, \"div\", 142)(3, CompleteinvoiceComponent_ng_template_295_div_9_div_3_Template, 2, 1, \"div\", 142);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.CKbookbank && ctx_r2.CkINV);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.CKbookbank);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.CkINV);\n  }\n}\nfunction CompleteinvoiceComponent_ng_template_295_button_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r56 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 172);\n    i0.ɵɵlistener(\"click\", function CompleteinvoiceComponent_ng_template_295_button_10_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r56);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.SearchCompleteINV_Delete(ctx_r2.Formdate, ctx_r2.Todate));\n    });\n    i0.ɵɵtext(1, \"\\u0E22\\u0E37\\u0E19\\u0E22\\u0E31\\u0E19\\u0E01\\u0E32\\u0E23\\u0E17\\u0E33\\u0E23\\u0E32\\u0E22\\u0E01\\u0E32\\u0E23\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CompleteinvoiceComponent_ng_template_295_button_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r57 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 173);\n    i0.ɵɵlistener(\"click\", function CompleteinvoiceComponent_ng_template_295_button_12_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r57);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.DeleteINV());\n    });\n    i0.ɵɵtext(1, \"\\u0E25\\u0E1A\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r2.numDataDelete == 0);\n  }\n}\nfunction CompleteinvoiceComponent_ng_template_295_button_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r58 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 174);\n    i0.ɵɵlistener(\"click\", function CompleteinvoiceComponent_ng_template_295_button_15_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r58);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      const ShowDeleteINVDetail_r59 = i0.ɵɵreference(298);\n      return i0.ɵɵresetView(ctx_r2.OpenDeleteINVDetail(ShowDeleteINVDetail_r59));\n    });\n    i0.ɵɵtext(1, \"\\u0E23\\u0E32\\u0E22\\u0E01\\u0E32\\u0E23\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r2.numDataDelete == 0);\n  }\n}\nfunction CompleteinvoiceComponent_ng_template_295_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r55 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 167)(1, \"P\");\n    i0.ɵɵtext(2, \"\\u0E15\\u0E49\\u0E2D\\u0E07\\u0E01\\u0E32\\u0E23\\u0E08\\u0E30\\u0E25\\u0E1A\\u0E23\\u0E32\\u0E22\\u0E01\\u0E32\\u0E23 Invoice \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"P\");\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"date\");\n    i0.ɵɵpipe(6, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(7, CompleteinvoiceComponent_ng_template_295_P_7_Template, 6, 3, \"P\", 142)(8, CompleteinvoiceComponent_ng_template_295_p_8_Template, 2, 1, \"p\", 142)(9, CompleteinvoiceComponent_ng_template_295_div_9_Template, 4, 3, \"div\", 142)(10, CompleteinvoiceComponent_ng_template_295_button_10_Template, 2, 0, \"button\", 168);\n    i0.ɵɵelement(11, \"br\");\n    i0.ɵɵtemplate(12, CompleteinvoiceComponent_ng_template_295_button_12_Template, 2, 1, \"button\", 169);\n    i0.ɵɵelementStart(13, \"button\", 170);\n    i0.ɵɵlistener(\"click\", function CompleteinvoiceComponent_ng_template_295_Template_button_click_13_listener() {\n      i0.ɵɵrestoreView(_r55);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.modalDeleteClose());\n    });\n    i0.ɵɵtext(14, \"\\u0E1B\\u0E34\\u0E14\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(15, CompleteinvoiceComponent_ng_template_295_button_15_Template, 2, 1, \"button\", 171);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate2(\"\\u0E08\\u0E32\\u0E01\\u0E27\\u0E31\\u0E19\\u0E17\\u0E35\\u0E48 \", i0.ɵɵpipeBind2(5, 8, ctx_r2.Formdate, \"dd/MM/yyyy\"), \" \\u0E16\\u0E36\\u0E07 \", i0.ɵɵpipeBind2(6, 11, ctx_r2.Todate, \"dd/MM/yyyy\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.btnInvDelete);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.CKDeleteload);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.CKDeleteload);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.btnInvDelete);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.btnInvDelete && ctx_r2.CKDeleteload);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.btnInvDelete);\n  }\n}\nfunction CompleteinvoiceComponent_ng_template_297_tr_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"th\", 49);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"th\", 49);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\", 49);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\", 176);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\", 49);\n    i0.ɵɵtext(10);\n    i0.ɵɵpipe(11, \"date\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r61 = ctx.$implicit;\n    const i_r62 = ctx.index;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i_r62 + 1);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r61.salesid);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r61.invoiceid);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r61.invoicingname);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(11, 5, item_r61.billingnotesdate, \"dd/MM/yyyy\"));\n  }\n}\nfunction CompleteinvoiceComponent_ng_template_297_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r60 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 136)(1, \"table\", 40)(2, \"thead\")(3, \"tr\", 41)(4, \"th\", 175);\n    i0.ɵɵtext(5, \"Item\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"th\", 175);\n    i0.ɵɵtext(7, \"SO No\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"th\", 175);\n    i0.ɵɵtext(9, \"Invoice No\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"th\", 175);\n    i0.ɵɵtext(11, \"Name Invoice No\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"th\", 175);\n    i0.ɵɵtext(13, \"Date\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(14, \"tbody\");\n    i0.ɵɵtemplate(15, CompleteinvoiceComponent_ng_template_297_tr_15_Template, 12, 8, \"tr\", 73);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"button\", 139);\n    i0.ɵɵlistener(\"click\", function CompleteinvoiceComponent_ng_template_297_Template_button_click_16_listener() {\n      i0.ɵɵrestoreView(_r60);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.modalDeleteINVDetail.hide());\n    });\n    i0.ɵɵtext(17, \"\\u0E1B\\u0E34\\u0E14\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(15);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.DataDelete);\n  }\n}\nexport let CompleteinvoiceComponent = /*#__PURE__*/(() => {\n  class CompleteinvoiceComponent {\n    getColor(country, stRemark, CK) {\n      var stcolor;\n      if (country === 'อนุมัติแล้ว' && stRemark == 0 && (CK === 'อนุมัติแล้ว' || CK === 'รออนุมัติกลับ')) {\n        stcolor = 'อนุมัติแล้ว';\n      } else if (country === 'รออนุมัติ' && stRemark == 0 && CK === 'รออนุมัติ') {\n        stcolor = 'รออนุมัติใหม่';\n      } else if (country === 'รออนุมัติ' && stRemark == 1 && (CK === 'รอbookbank' || CK === 'รออนุมัติกลับ')) {\n        stcolor = 'ผิดปกติ';\n      } else if (country === 'รออนุมัติ' && stRemark == 0 && (CK === 'รอbookbank' || CK === 'รออนุมัติกลับ')) {\n        stcolor = 'รออนุมัติ';\n      } else {\n        stcolor = 'ผิดปกติ';\n      }\n      switch (stcolor) {\n        case 'อนุมัติแล้ว':\n          return 'green';\n        case 'รออนุมัติ':\n          return 'red';\n        case 'ผิดปกติ':\n          return '#969595';\n        case 'รออนุมัติใหม่':\n          return '#0e0dde';\n      }\n    }\n    getColor2(country) {\n      if (country == 1) {\n        country = false;\n      } else {\n        country = true;\n      }\n      switch (country) {\n        case true:\n          return 'green';\n        case false:\n          return '#969595';\n      }\n    }\n    getColorbookbank(country, bookbank) {\n      if (country == 1 && bookbank != 'A') {\n        country = '1';\n      } else if (country == 1 && bookbank === 'A') {\n        country = '1';\n      } else if (country == 0 && bookbank !== 'A') {\n        country = '2';\n      } else if (country == 0 && bookbank === 'A') {\n        country = '3';\n      } else {\n        country = '3';\n      }\n      switch (country) {\n        case '1':\n          return '#969595';\n        case '2':\n          return '#0e0dde';\n        case '3':\n          return 'green';\n      }\n    }\n    constructor(ng2ImgMax, modalService, fb, chRef, http, service, calendar, route, router) {\n      this.ng2ImgMax = ng2ImgMax;\n      this.modalService = modalService;\n      this.fb = fb;\n      this.chRef = chRef;\n      this.http = http;\n      this.service = service;\n      this.calendar = calendar;\n      this.route = route;\n      this.router = router;\n      this.disabled = false;\n      this.ShowFilter = false;\n      this.limitSelection = false;\n      this.dropdownSettings = {};\n      this.options = {\n        fieldSeparator: ',',\n        quoteStrings: '\"',\n        decimalseparator: '.',\n        showLabels: true,\n        showTitle: true,\n        useBom: true,\n        noDownload: false,\n        headers: [\"SALESGROUP\", \"INVOICE NO\", \"Customer NO\", \"Customer Name\", \"INVOICEDATE\", \"SaleOrder\", \"เงือนไข\", \"DUEDATE\", \"เงินก่อนภาษี\", \"VAT\", \"รวมทั้งสิ้น\", \"สถานะ Approve\", \"Remark\"]\n      };\n      this.options2 = {\n        fieldSeparator: ',',\n        quoteStrings: '\"',\n        decimalseparator: '.',\n        showLabels: true,\n        showTitle: true,\n        useBom: true,\n        noDownload: false,\n        headers: [\"SALESGROUP\", \"Customer NO\", \"Customer Name\", \"Bill no\", \"เงินก่อนภาษี\", \"VAT\", \"รวมทั้งสิ้น\", \"type\", \"สถานะ Approve\"]\n      };\n      this.Bill_approve = [];\n      this.Dataexcl = [];\n      this.BillId = [];\n      this.groupimg = [];\n      this.mdlSampleIsOpen = false;\n      this.alt = '';\n      this.checkreload = true;\n      this.mdlSampleIsOpenApprove = false;\n      this.altApprove = '';\n      this.checkreloadApprove = true;\n      this.mdlSampleIsOpenBill = false;\n      this.altBill = '';\n      this.checkreloadBill = true;\n      this.mdlSampleIsOpen2 = false;\n      this.alt2 = '';\n      this.checkreload2 = true;\n      this.mdlSampleIsOpen3 = false;\n      this.alt3 = '';\n      this.checkreload3 = true;\n      this.mdlSampleIsOpensuccess = false;\n      this.altsuccess = '';\n      this.checkreloadsuccess = true;\n      this.customer = '';\n      this.Paymenttype = [];\n      this.StApprove = [];\n      this.selectPayment = '';\n      this.selectApprove = '';\n      this.paymenttype = [{\n        idpayment: '0',\n        value: 'เงินสด'\n      }, {\n        idpayment: '1',\n        value: 'เครดิส'\n      }];\n      this.Modelpaymenttype = {\n        id: '',\n        value: ''\n      };\n      this.stApprove = [{\n        idApprove: '1',\n        value: 'Approval แล้ว'\n      }, {\n        idApprove: '0',\n        value: 'ยังไม่ได้ Approve'\n      }];\n      this.ModelStApprove = {\n        id: '',\n        value: ''\n      };\n      this.ImageIN = '../assets/img/default-image.png';\n      this.ImageBill = 'default-image.png';\n      this.ImageBookbank = '';\n      this.ImageBillno = '';\n      this.test2 = \"BI18-02557-2018-8-14-400.jpg\";\n      this.imgtest = \"../../assets/imageBill/\";\n      this.showtest = this.imgtest + this.test2;\n      this.keyboardStrApprove = '';\n      this.keyboardStrpaymenttype = '';\n      this.total = 0;\n      this.load = '';\n      this.productprice = 0;\n      this.sumvat = 0;\n      this.sumprice = 0;\n      this.trueproductprice = 0;\n      this.truesumvat = 0;\n      this.truesumprice = 0;\n      this.dateshipping = '';\n      this.dateshippingto = '';\n      this.testclose = false;\n      this.dataTable = [];\n      this.mdlSampleIsOpenimg = false;\n      this.altimg = '';\n      this.checkreloadimg = true;\n      this.fromdate = '';\n      this.todate = '';\n      this.SearchCompleteED_ = false;\n      this.SearchComplete_ = false;\n      this.permisstiondata = [];\n      this.exportbtn = true;\n      this.searchbtn = true;\n      this.config = {\n        ignoreBackdropClick: true,\n        class: 'modal-md'\n      };\n      this.configview = {\n        ignoreBackdropClick: true,\n        class: 'modal-lg '\n      };\n      this.textload = \"\";\n      this.selectedFile = null;\n      this.namebookbank = '';\n      this.btnPDF = false;\n      this.btnREpdf = false;\n      this.isCollapsed = true;\n      this.imageUrl = \"assets/img/default-image.png\";\n      this.urlimgBookbank = '';\n      this.CK = false;\n      this.staticAlertClosed = false;\n      this.urlimgDefault = '';\n      this.printst = '3';\n      this.headerlist = [];\n      this.seachheaderlist = [];\n      this.viewBookbank = 'A';\n      this.Bookbank = [];\n      this.INVSuccess = [];\n      this.Btnimg = false;\n      this.GetDeleteCK = true;\n      this.Formdate = '';\n      this.Todate = '';\n      this.btnInvDelete = true;\n      this.numDataDelete = 0;\n      this.numINVDelete = 0;\n      this.numBookbankDelete = 0;\n      this.CKDeleteload = true;\n      this.txtDeleteShow = \"\";\n      this.saleid = \"\";\n      this.customers = [];\n      /* this.paymenttype=[{idpayment: 'N01', value: 'เงินสด'}, {idpayment: 'N07', value: 'เครดิส'}];\n       [\n         {\"idpayment\":'N01', \"paymentName\" :'เงินสด' },\n         {\"idpayment\":'N07', \"paymentName\" :'เครดิส' },\n       ]*/\n      /* this.stApprove=[{idApprove: '1', value: 'Approval แล้ว'}, {idApprove: '0', value: 'ยังไม่ได้ Approve'}];\n       [\n         {\"idApprove\":'1', \"ApproveName\" :'Approval แล้ว' },\n         {\"idApprove\":'0', \"ApproveName\" :'ยังไม่ได้ Approve' },\n       ]\n      */\n      /////\n      this.search = text$ =>\n      //Autocomplete ลูกค้า\n      text$.pipe(debounceTime(200), map(term => term === '' ? [] : this.customers.filter(v => v.name.toLowerCase().indexOf(term.toLowerCase()) > -1 || v.accountnum.toLowerCase().indexOf(term.toLowerCase()) > -1).slice(0, 50)));\n      this.formatter = x => x.name + ' (' + x.accountnum + ')';\n      this.url = service.geturlservice();\n      localStorage.removeItem('DataSOderreview');\n      localStorage.removeItem('DataSOderlist');\n      this.urlimg = service.geturlserviceIMG();\n      this.urlimgDefault = service.geturlimgDefault();\n      this.urlimgBookbank = service.geturlserviceIMGbookbank();\n      this.fromDate = calendar.getPrev(calendar.getToday(), 'd', 365);\n      this.toDate = calendar.getToday();\n      this.datalogin = JSON.parse(sessionStorage.getItem('login'));\n      // alert(this.seachheaderlist);\n      if (this.datalogin == null) {\n        this.router.navigate(['login']);\n      }\n      this.getuser();\n      this.selectPayment = '';\n      this.selectApprove = '';\n      this.dateinvid = '';\n      this.dataExport = '';\n      this.calendarimg = \"../../assets/img/if_Paul-27_2534341.ico\";\n      //  this.groupsale=this.dataloginthis.fromDate.day;\n      this.getgroupsaleman();\n      if (sessionStorage.getItem('complDateTo') != null && sessionStorage.getItem('complDateFrom') != null) {\n        this.Datatodate = new Date(sessionStorage.getItem('complDateTo'));\n        this.Datafromdate = new Date(sessionStorage.getItem('complDateFrom'));\n      } else {\n        this.fromDate = calendar.getPrev(calendar.getToday(), 'd', 365);\n        this.toDate = calendar.getToday();\n        this.Datatodate = new Date(this.toDate.year, this.toDate.month - 1, this.toDate.day);\n        this.Datafromdate = new Date(this.toDate.year, this.toDate.month - 1, 1);\n      }\n      //this.Datatodate= new Date(this.toDate.year,this.toDate.month-1,this.toDate.day);\n      //  this.Datafromdate= new Date(this.fromDate.year, this.fromDate.month-1, 1);\n      //this.Datafromdate= new Date(this.toDate.year, this.toDate.month-1, 1);\n      this.permisstiondata = JSON.parse(sessionStorage.getItem('menu'));\n      this.exportbtn = !this.permisstiondata[9].flag_print;\n      this.searchbtn = !this.permisstiondata[9].flag_action;\n    }\n    //////\n    //ดึงรหัสลูกค้ามาใช้ใน Autocomplete\n    getcostomerauto() {\n      var idsale = this.datalogin[0].salegroup;\n      if (this.datalogin[0].id_group_user == '153Admin' || this.datalogin[0].id_group_user == '1860Administrator') {\n        idsale = '%20';\n      } else {\n        idsale = this.datalogin[0].salegroup;\n      }\n      this.http.get(this.url + 'customerauto/' + idsale).subscribe(res => {\n        this.customers = res;\n      });\n    }\n    cancel() {\n      this.Customer = undefined;\n      this.searchselectprint();\n    }\n    searchviewBookbank() {\n      let Ck = false;\n      this.testcheck = Ck;\n      this.selectAll(this.testcheck);\n      if (this.viewBookbank === '3') {\n        this.Bookbank = this.BillId;\n      } else {\n        if (this.viewBookbank === 'A') {\n          // alert('ทำงาน')\n          this.Bookbank = this.BillId.filter(v => v.checkbank.toLowerCase().indexOf(this.viewBookbank.toLowerCase()) > -1);\n        } else {\n          this.Bookbank = this.BillId.filter(v => v.checkbank.toLowerCase().indexOf(this.viewBookbank.toLowerCase()) > -1);\n        }\n      }\n    }\n    searchSuccess(data) {\n      if (this.dateinvid === '') {\n        // alert(this.dateinvid);\n        this.INVSuccess = this.BillId;\n      } else {\n        this.INVSuccess = this.BillId.filter(v => v.invoiceid.toLowerCase().indexOf(this.dateinvid.toLowerCase()) > -1);\n      }\n    }\n    searchselectprint2() {\n      this.seachheaderlist = this.dataComplete.filter(v => v.Invoicingname.toLowerCase().indexOf(this.Customer.name.toLowerCase()) > -1);\n      // console.log(this.seachheaderlist)\n      console.log(this.Customer);\n      this.productprice = 0;\n      this.sumvat = 0;\n      this.sumprice = 0;\n      if (this.seachheaderlist.length > 0) {\n        for (var i = 0; i < this.seachheaderlist.length; i++) {\n          this.productprice += this.seachheaderlist[i].Salesbalance;\n          this.sumvat += this.seachheaderlist[i].Sumtax;\n          this.sumprice += this.seachheaderlist[i].Invoiceamount;\n        }\n      }\n    }\n    searchselectprint() {\n      //alert(this.printst)\n      if (this.dataComplete != undefined) {\n        if (this.printst === '3') {\n          if (this.Customer == undefined) {\n            this.seachheaderlist = this.dataComplete;\n            // alert(1);\n            this.productprice = 0;\n            this.sumvat = 0;\n            this.sumprice = 0;\n            if (this.seachheaderlist.length > 0) {\n              for (var i = 0; i < this.seachheaderlist.length; i++) {\n                this.productprice += this.seachheaderlist[i].Salesbalance;\n                this.sumvat += this.seachheaderlist[i].Sumtax;\n                this.sumprice += this.seachheaderlist[i].Invoiceamount;\n              }\n            }\n          } else {\n            setTimeout(() => this.searchselectprint2(), 600);\n            // alert(this.seachheaderlist.length);\n          }\n        } else {\n          //alert(JSON.stringify(this.headerlist[0]));\n          //   alert(JSON.stringify(this.Customer))\n          if (this.Customer == undefined) {\n            this.seachheaderlist = this.dataComplete.filter(v => v.CK.toLowerCase().indexOf(this.printst.toLowerCase()) > -1);\n          } else {\n            this.seachheaderlist = this.dataComplete.filter(v => v.CK.toLowerCase().indexOf(this.printst.toLowerCase()) > -1 && v.Invoicingname.toLowerCase().indexOf(this.Customer.name.toLowerCase()) > -1);\n          }\n          //  this.seachheaderlist = this.dataComplete.filter(v => v.CK.toLowerCase().indexOf(this.printst.toLowerCase()) > -1 && v.Invoicingname.toLowerCase().indexOf(this.Customer.toLowerCase()) > -1);\n          this.productprice = 0;\n          this.sumvat = 0;\n          this.sumprice = 0;\n          if (this.seachheaderlist.length > 0) {\n            for (var i = 0; i < this.seachheaderlist.length; i++) {\n              this.productprice += this.seachheaderlist[i].Salesbalance;\n              this.sumvat += this.seachheaderlist[i].Sumtax;\n              this.sumprice += this.seachheaderlist[i].Invoiceamount;\n            }\n          }\n        }\n      }\n    }\n    getuser() {\n      if (this.datalogin[0].id_group_user == '153Admin' || this.datalogin[0].id_group_user == '1860Administrator') {\n        this.datasalegroup = '';\n        this.testclose = true;\n      } else {\n        this.testclose = false;\n        this.datasalegroup = this.datalogin[0].salegroup;\n      }\n    }\n    getgroupsaleman() {\n      this.DateGroupsaleman = [];\n      this.http.get(this.url + 'salesman').subscribe(res => {\n        if (res.length > 0) {\n          this.DateGroupsaleman = res;\n          this.getcostomerauto();\n        } else {\n          alert('ไม่สามารถติดต่อข้อมูลรหัส saleman ได้');\n        }\n      });\n    }\n    onItemSelect(item) {\n      console.log('onItemSelect', item);\n    }\n    onSelectAll(items) {\n      console.log('onSelectAll', items);\n    }\n    toogleShowFilter() {\n      this.ShowFilter = !this.ShowFilter;\n      this.dropdownSettings = Object.assign({}, this.dropdownSettings, {\n        allowSearchFilter: this.ShowFilter\n      });\n    }\n    handleLimitSelection() {\n      if (this.limitSelection) {\n        this.dropdownSettings = Object.assign({}, this.dropdownSettings, {\n          limitSelection: 2\n        });\n      } else {\n        this.dropdownSettings = Object.assign({}, this.dropdownSettings, {\n          limitSelection: null\n        });\n      }\n    }\n    ngOnInit() {}\n    getdate() {\n      this.fromdate = `${this.Datafromdate.getFullYear()}-${this.Datafromdate.getMonth() + 1}-${this.Datafromdate.getDate()}`;\n      this.todate = `${this.Datatodate.getFullYear()}-${this.Datatodate.getMonth() + 1}-${this.Datatodate.getDate()}`;\n    }\n    getpaymenttype() {\n      this.http.get(this.url + 'paymenttype').subscribe(res => {\n        alert(JSON.stringify(res));\n        if (res.length > 0) {\n          for (var i = 0; i < res.length; i++) {\n            this.Paymenttype.push({\n              idpayment: res[i].idpayment,\n              paymentName: res[i].paymenttype\n            });\n          }\n        } else {}\n      });\n    }\n    SearchCompleteED(keyboardStrApprove, keyboardStrpaymenttype) {\n      this.GetDeleteCK = true;\n      this.dataComplete = [];\n      this.seachheaderlist = [];\n      this.printst = \"อนุมัติแล้ว\";\n      this.productprice = 0;\n      this.sumvat = 0;\n      this.sumprice = 0;\n      this.SearchCompleteED_ = true;\n      this.SearchComplete_ = false;\n      this.load = '';\n      var datacodeSo = '';\n      this.getdate();\n      if (keyboardStrpaymenttype == '' || keyboardStrApprove == '') {\n        this.openModal(true, 'กรุณาเลือกเลือกสถาะนะ', false);\n        // alert('เกิดปัญหาในการ Process ข้อมูล กรุณารายการใหม่อีกครั้ง')\n      } else {\n        if (this.datasalegroup == '' && keyboardStrApprove == '') {\n          this.openModal(true, 'กรุณาเลือก Sale หรือ เงินสด/เครดิต ถ้าไม่ระบุวันที่ วันที่จะย้อนหลังเป็นเวลา 365 วัน', false);\n          this.productprice = 0;\n          this.sumvat = 0;\n          this.sumprice = 0;\n        } else {\n          if (this.todate == '') {\n            this.todate = `${this.toDate}`;\n            sessionStorage.setItem('complDateFrom', this.todate);\n          } else {\n            sessionStorage.setItem('complDateFrom', this.todate);\n          }\n          if (this.fromdate == '') {\n            this.fromdate = `${this.fromDate}`;\n            sessionStorage.setItem('complDateFrom', this.fromdate);\n          } else {\n            sessionStorage.setItem('complDateFrom', this.fromdate);\n          }\n          /*if(this.keyboardStrpaymenttype==''){\n           this.keyboardStrpaymenttype='%20';\n          }*/\n          if (this.datasalegroup == '') {\n            if (this.datalogin[0].id_group_user == '153Admin' || this.datalogin[0].id_group_user == '1860Administrator') {\n              datacodeSo = `${this.datasalegroup}`;\n            } else {\n              datacodeSo = `${this.datalogin[0].salegroup}`;\n            }\n          }\n          if (this.datasalegroup !== '') {\n            if (this.datalogin[0].id_group_user == '153Admin' || this.datalogin[0].id_group_user == '1860Administrator') {\n              datacodeSo = `${this.datasalegroup}`;\n            } else {\n              datacodeSo = `${this.datalogin[0].salegroup}`;\n            }\n          }\n          if (datacodeSo == '') {\n            datacodeSo = '%20';\n          }\n          this.dataExport = '';\n          var data = `${this.fromdate}/${this.todate}/${datacodeSo}/${keyboardStrpaymenttype}/${keyboardStrApprove}`;\n          this.dataComplete = [];\n          this.http.get(this.url + 'completed_invoice_searchED/' + data).subscribe(res => {\n            if (res.length > 0) {\n              this.dataComplete = res;\n              this.seachheaderlist = res;\n              /* if(this.keyboardStrApprove=='%20'){\n                 this.keyboardStrApprove='';\n               }\n               if(this.keyboardStrpaymenttype=='%20'){\n                 this.keyboardStrpaymenttype='';\n               }  */\n              this.sum();\n              /* if(this.datalogin[0].salegroup =='admin'){\n                 this.datasalegroup='';\n               }*/\n            } else {\n              /* if(this.datalogin[0].salegroup =='admin'){\n                 this.datasalegroup='';\n               }*/\n              if (this.keyboardStrApprove == '%20') {\n                this.keyboardStrApprove = '';\n              }\n              if (this.keyboardStrpaymenttype == '%20') {\n                this.keyboardStrpaymenttype = '';\n              }\n              this.openModal(true, 'ไม่พบข้อมูล', false);\n              this.dataComplete = [];\n              this.seachheaderlist = [];\n              this.productprice = 0;\n              this.sumvat = 0;\n              this.sumprice = 0;\n            }\n          }, error => {\n            status = error.status;\n            //this.openModal(true,'เกิดปัญหาในการ Process ข้อมูล กรุณารายการใหม่อีกครั้ง',false);\n            alert('เกิดปัญหาในการ Process ข้อมูล กรุณารายการใหม่อีกครั้ง');\n          });\n        }\n      }\n    }\n    SearchComplete() {\n      this.GetDeleteCK = true;\n      // this.printst=\"3\";\n      this.SearchCompleteED_ = false;\n      this.SearchComplete_ = true;\n      this.load = '';\n      var datacodeSo = '';\n      this.dataComplete = [];\n      this.productprice = 0;\n      this.sumvat = 0;\n      this.sumprice = 0;\n      this.getdate();\n      if (this.datasalegroup == '' && this.keyboardStrpaymenttype == '' && this.keyboardStrApprove == '') {\n        this.dataComplete = [];\n        this.seachheaderlist = [];\n        this.openModal(true, 'กรุณาป้อนข้อมูลอย่างน้อย 1 ช่อง ถ้าไม่ระบุวันที่ วันที่จะย้อนหลังเป็นเวลา 365 วัน', false);\n        this.productprice = 0;\n        this.sumvat = 0;\n        this.sumprice = 0;\n      } else {\n        if (this.todate == '') {\n          this.todate = `${this.toDate}`;\n          sessionStorage.setItem('complDateTo', this.todate);\n        } else {\n          sessionStorage.setItem('complDateTo', this.todate);\n        }\n        if (this.fromdate == '') {\n          this.fromdate = `${this.fromDate}`;\n          sessionStorage.setItem('complDateFrom', this.fromdate);\n        } else {\n          sessionStorage.setItem('complDateFrom', this.fromdate);\n        }\n        if (this.keyboardStrpaymenttype == '') {\n          this.keyboardStrpaymenttype = '%20';\n        }\n        if (this.keyboardStrApprove == '') {\n          this.keyboardStrApprove = '%20';\n        }\n        if (this.datasalegroup == '') {\n          if (this.datalogin[0].id_group_user == '153Admin' || this.datalogin[0].id_group_user == '1860Administrator') {\n            datacodeSo = `${this.datasalegroup}`;\n          } else {\n            datacodeSo = `${this.datalogin[0].salegroup}`;\n          }\n        }\n        if (this.datasalegroup !== '') {\n          if (this.datalogin[0].id_group_user == '153Admin' || this.datalogin[0].id_group_user == '1860Administrator') {\n            datacodeSo = `${this.datasalegroup}`;\n          } else {\n            datacodeSo = `${this.datalogin[0].salegroup}`;\n          }\n        }\n        //alert(this.datasalegroup)\n        if (datacodeSo == '') {\n          datacodeSo = '%20';\n        }\n        this.dataExport = '';\n        var data = `${this.fromdate}/${this.todate}/${datacodeSo}/${this.keyboardStrApprove}/${this.keyboardStrpaymenttype}`;\n        //alert(data)\n        this.dataExport = data;\n        this.dataComplete = [];\n        this.seachheaderlist = [];\n        this.http.get(this.url + 'completed_invoice_search/' + data).subscribe(res => {\n          if (res.length > 0) {\n            this.dataComplete = res;\n            this.seachheaderlist = res;\n            if (this.keyboardStrApprove == '%20') {\n              this.keyboardStrApprove = '';\n            }\n            if (this.keyboardStrpaymenttype == '%20') {\n              this.keyboardStrpaymenttype = '';\n            }\n            this.sum();\n            this.searchselectprint();\n            /* if(this.datalogin[0].salegroup =='admin'){\n               this.datasalegroup='';\n             }*/\n          } else {\n            /* if(this.datalogin[0].salegroup =='admin'){\n               this.datasalegroup='';\n             }*/\n            if (this.keyboardStrApprove == '%20') {\n              this.keyboardStrApprove = '';\n            }\n            if (this.keyboardStrpaymenttype == '%20') {\n              this.keyboardStrpaymenttype = '';\n            }\n            this.openModal(true, 'ไม่พบข้อมูล', false);\n            this.dataComplete = [];\n            this.seachheaderlist = [];\n            this.productprice = 0;\n            this.sumvat = 0;\n            this.sumprice = 0;\n          }\n        }, error => {\n          status = error.status;\n          alert('เกิดปัญหาในการ Process ข้อมูล กรุณารายการใหม่อีกครั้ง');\n          //this.openModal(true,'เกิดปัญหาในการ Process ข้อมูล กรุณารายการใหม่อีกครั้ง',false);\n        });\n      }\n    }\n    SearchCompleteNoPrintst() {\n      this.GetDeleteCK = true;\n      //this.printst=\"3\";\n      this.SearchCompleteED_ = false;\n      this.SearchComplete_ = true;\n      this.load = '';\n      var datacodeSo = '';\n      this.dataComplete = [];\n      this.productprice = 0;\n      this.sumvat = 0;\n      this.sumprice = 0;\n      this.getdate();\n      if (this.datasalegroup == '' && this.keyboardStrpaymenttype == '' && this.keyboardStrApprove == '') {\n        this.dataComplete = [];\n        this.seachheaderlist = [];\n        this.openModal(true, 'กรุณาป้อนข้อมูลอย่างน้อย 1 ช่อง ถ้าไม่ระบุวันที่ วันที่จะย้อนหลังเป็นเวลา 365 วัน', false);\n        this.productprice = 0;\n        this.sumvat = 0;\n        this.sumprice = 0;\n      } else {\n        if (this.todate == '') {\n          this.todate = `${this.toDate}`;\n        }\n        if (this.fromdate == '') {\n          this.fromdate = `${this.fromDate}`;\n        }\n        if (this.keyboardStrpaymenttype == '') {\n          this.keyboardStrpaymenttype = '%20';\n        }\n        if (this.keyboardStrApprove == '') {\n          this.keyboardStrApprove = '%20';\n        }\n        if (this.datasalegroup == '') {\n          if (this.datalogin[0].id_group_user == '153Admin' || this.datalogin[0].id_group_user == '1860Administrator') {\n            datacodeSo = `${this.datasalegroup}`;\n          } else {\n            datacodeSo = `${this.datalogin[0].salegroup}`;\n          }\n        }\n        if (this.datasalegroup !== '') {\n          if (this.datalogin[0].id_group_user == '153Admin' || this.datalogin[0].id_group_user == '1860Administrator') {\n            datacodeSo = `${this.datasalegroup}`;\n          } else {\n            datacodeSo = `${this.datalogin[0].salegroup}`;\n          }\n        }\n        //alert(this.datasalegroup)\n        if (datacodeSo == '') {\n          datacodeSo = '%20';\n        }\n        this.dataExport = '';\n        var data = `${this.fromdate}/${this.todate}/${datacodeSo}/${this.keyboardStrApprove}/${this.keyboardStrpaymenttype}`;\n        //alert(data)\n        this.dataExport = data;\n        this.dataComplete = [];\n        this.seachheaderlist = [];\n        this.http.get(this.url + 'completed_invoice_search/' + data).subscribe(res => {\n          if (res.length > 0) {\n            this.dataComplete = res;\n            this.seachheaderlist = res;\n            if (this.keyboardStrApprove == '%20') {\n              this.keyboardStrApprove = '';\n            }\n            if (this.keyboardStrpaymenttype == '%20') {\n              this.keyboardStrpaymenttype = '';\n            }\n            this.sum();\n            /* if(this.datalogin[0].salegroup =='admin'){\n               this.datasalegroup='';\n             }*/\n            this.searchselectprint();\n          } else {\n            /* if(this.datalogin[0].salegroup =='admin'){\n               this.datasalegroup='';\n             }*/\n            if (this.keyboardStrApprove == '%20') {\n              this.keyboardStrApprove = '';\n            }\n            if (this.keyboardStrpaymenttype == '%20') {\n              this.keyboardStrpaymenttype = '';\n            }\n            this.openModal(true, 'ไม่พบข้อมูล', false);\n            this.dataComplete = [];\n            this.seachheaderlist = [];\n            this.productprice = 0;\n            this.sumvat = 0;\n            this.sumprice = 0;\n          }\n        }, error => {\n          status = error.status;\n          alert('เกิดปัญหาในการ Process ข้อมูล กรุณารายการใหม่อีกครั้ง');\n          //this.openModal(true,'เกิดปัญหาในการ Process ข้อมูล กรุณารายการใหม่อีกครั้ง',false);\n        });\n      }\n    }\n    GetExport() {\n      this.GetDeleteCK = true;\n      this.http.get(this.url + 'completed_invoice_export/' + this.dataExport).subscribe(res => {\n        if (res.length > 0) {\n          this.DataExport = res;\n          if (this.DataExport == undefined) {\n            this.openModal(true, 'ไม่พบข้อมูล', false);\n            this.dataExport = '';\n          } else {\n            new Angular5Csv(this.DataExport, 'Completed Invoice', this.options);\n            /*  alert(JSON.stringify(this.DataExport));*/\n            this.dataExport = '';\n          }\n        } else {}\n      }, error => {\n        status = error.status;\n        this.openModal(true, 'เกิดปัญหาในการ Process ข้อมูล กรุณาทำรายการใหม่อีกครั้ง', false);\n      });\n    }\n    newGetExport(data, productprice, sumvat, sumprice) {\n      if (data == []) {\n        alert('ไม่พบข้อมูล');\n      } else {\n        this.Dataexcl = [];\n        var type;\n        //{\"Salegroup\":\"S001\",\"Orderaccount\":\"N00149\",\"Invoicingname\":\"หจก. ซี. เอส. อีเลคทริค ซัพพลาย (สำนักงานใหญ่)\",\n        //\"Billno\":\"  \",\"Salesbalance\":995.38,\"Sumtax\":69.68,\"Invoiceamount\":1065.06,\"approve\":\"รออนุมัติ\",\"remarkstatus\":\"0\",\n        //\"CK\":\"รอbookbank\"}\n        //[\"SALESGROUP\",\"Customer NO\",\"Customer Name\",\"Bill no\",\"เงินก่อนภาษี\",\"VAT\",\"รวมทั้งสิ้น\",\"type\",\"สถานะ Approve\"]\n        console.log(JSON.stringify(data));\n        const format = num => {\n          const n = String(num),\n            p = n.indexOf('.');\n          return n.replace(/\\d(?=(?:\\d{3})+(?:\\.|$))/g, (m, i) => p < 0 || i < p ? `${m},` : m);\n        };\n        for (var i = 0; i < data.length; i++) {\n          if (data[i].Billno.trim() == \"\") {\n            type = \"เงินสด\";\n          } else {\n            type = \"เครดิต\";\n          }\n          this.Dataexcl.push({\n            SALESGROUP: data[i].Salegroup,\n            CustomerNO: data[i].Orderaccount,\n            CustomerName: data[i].Invoicingname,\n            Billno: data[i].Billno,\n            Moneybeforetax: format(parseFloat(data[i].Salesbalance).toFixed(2)),\n            vat: format(parseFloat(data[i].Sumtax).toFixed(2)),\n            sumtotal: format(parseFloat(data[i].Invoiceamount).toFixed(2)),\n            type: type,\n            typeApprove: data[i].approve\n          });\n        }\n        this.Dataexcl.push({\n          SALESGROUP: \"\",\n          CustomerNO: \"\",\n          CustomerName: \"\",\n          Billno: \"\",\n          Moneybeforetax: format(parseFloat(productprice).toFixed(2)),\n          vat: format(parseFloat(sumvat).toFixed(2)),\n          sumtotal: format(parseFloat(sumprice).toFixed(2)),\n          type: \"\",\n          typeApprove: \"\"\n        });\n        new Angular5Csv(this.Dataexcl, 'Completed Invoice', this.options2);\n      }\n    }\n    exportdataexcel() {\n      this.DataExport = [];\n      this.GetExport();\n      if (this.DataExport == undefined) {\n        this.openModal(true, 'ไม่พบข้อมูล', false);\n      } else {\n        new Angular5Csv(this.DataExport, 'Completed Invoice', this.options);\n      }\n    }\n    sum() {\n      this.productprice = 0;\n      this.sumvat = 0;\n      this.sumprice = 0;\n      if (this.dataComplete.length > 0) {\n        for (var i = 0; i < this.dataComplete.length; i++) {\n          this.productprice += this.dataComplete[i].Salesbalance;\n          this.sumvat += this.dataComplete[i].Sumtax;\n          this.sumprice += this.dataComplete[i].Invoiceamount;\n        }\n      }\n    }\n    sumTrue() {\n      this.truesumprice = 0.00;\n      this.truesumvat = 0.00;\n      this.trueproductprice = 0.00;\n      for (var i = 0; i < this.BillId.length; i++) {\n        if (this.BillId[i].check == true) {\n          this.trueproductprice += this.BillId[i].salesbalance;\n          this.truesumvat += this.BillId[i].sumtax;\n          this.truesumprice += this.BillId[i].invoiceamount;\n        }\n      }\n    }\n    selectPaymenttype(idpayment) {\n      this.selectPayment = idpayment;\n    }\n    selectStApprove(idApprove) {\n      this.selectApprove = idApprove;\n    }\n    OpenModalRevert(Invoicingname, Billno, ModalApprove, Orderaccount, saleid) {\n      this.saleid = saleid;\n      this.getIdBillRevert(Orderaccount, Billno, saleid);\n      this.ModalRefApprove = this.modalService.show(ModalApprove, {\n        class: 'modal-lg',\n        ignoreBackdropClick: true\n      });\n    }\n    getIdBillRevert(Invoicingname, billid, saleid) {\n      this.BillId = [];\n      this.total = 0;\n      this.Remark = '';\n      this.testcheck = false;\n      this.truesumprice = 0.00;\n      this.truesumvat = 0.00;\n      this.trueproductprice = 0.00;\n      this.SearchRevertBillno = '';\n      this.SearchRevertINVname = '';\n      this.SearchRevertINVname = Invoicingname;\n      var databillid = '';\n      this.getdate();\n      if (billid == '') {\n        databillid = '%20';\n      } else {\n        databillid = billid;\n        this.SearchRevertBillno = billid;\n      }\n      if (saleid == \"\") {\n        saleid = '%20';\n      }\n      this.http.get(this.url + 'get_bill_ID_revert/' + databillid + '/' + Invoicingname + '/' + this.fromdate + '/' + this.todate + '/' + saleid).subscribe(res => {\n        this.billnoDataSearch = res;\n        if (res.length > 0) {\n          /*var test1 = res[0].attachedfile.splie();*/\n          for (var i = 0; i < res.length; i++) {\n            var alllength = JSON.stringify(res[i].attachedfile).length;\n            var St1 = JSON.stringify(res[i].attachedfile).substring(2, 4);\n            var St2 = JSON.stringify(res[i].attachedfile).substring(alllength - 8, alllength - 5);\n            var St3 = JSON.stringify(res[i].attachedfile).substring(alllength - 19, alllength - 15);\n            var sumst = `${St1}-${St2}${St3}`;\n            for (var v = 0; v < res.length; v++) {\n              var alllengthbook = JSON.stringify(res[i].attachedfile).length;\n              var St1book = JSON.stringify(res[i].attachedfileBookbank).substring(2, 4);\n              var St2book = JSON.stringify(res[i].attachedfileBookbank).substring(alllengthbook - 8, alllengthbook - 5);\n              var St3book = JSON.stringify(res[i].attachedfileBookbank).substring(alllengthbook - 19, alllengthbook - 15);\n              var sumstbook = `${St1book}-${St2book}${St3book}`;\n            }\n            var checkbank;\n            if (res[i].attachedfileBookbank === 'A') {\n              checkbank = res[i].attachedfileBookbank;\n            } else {\n              checkbank = 'B';\n            }\n            this.BillId.push({\n              noid: i,\n              idbill: res[i].id,\n              salesid: res[i].salesid,\n              invoiceid: res[i].invoiceid,\n              invoicename: res[i].invoicingname,\n              orderaccount: res[i].orderaccount,\n              invoicedate: res[i].invoicedate,\n              duedate: res[i].duedate,\n              salesbalance: res[i].salesbalance,\n              sumtax: res[i].sumtax,\n              invoiceamount: res[i].invoiceamount,\n              billno: res[i].billno,\n              approve: res[i].approve,\n              payment: res[i].payment,\n              urlimg: res[i].attachedfile,\n              delivery: res[i].deliveryname,\n              check: false,\n              nameimg: res[i].attachedfile,\n              saleman: res[i].salegroup,\n              remark: res[i].remark,\n              remarkst: res[i].remarkstatus,\n              numrun: sumst,\n              imgbookbank: res[i].attachedfileBookbank,\n              numrunbank: sumstbook,\n              checkbank: checkbank\n            });\n          }\n          this.showbill();\n        } else {\n          this.showbillno = 'ไม่มีรายการ';\n        }\n      }, error => {\n        status = error.status;\n        //this.openModal(true,'เกิดปัญหาในการ Process ข้อมูล กรุณารายการใหม่อีกครั้ง',false);\n        alert('เกิดปัญหาในการ Process ข้อมูล กรุณารายการใหม่อีกครั้ง');\n      });\n    }\n    getIdBillwaitModal(Invoicingname, Billno, Modalwait, Orderaccount, sale) {\n      this.ModalRefApprove = this.modalService.show(Modalwait, {\n        class: 'modal-lg',\n        ignoreBackdropClick: true\n      });\n      this.getIdBillwait(Orderaccount, Billno, sale);\n    }\n    getIdBillwait(Orderaccount, billid, sale) {\n      this.BillId = [];\n      this.total = 0;\n      this.SearchRevertBillno = '';\n      this.SearchRevertINVname = '';\n      this.Remark = '';\n      this.testcheck = false;\n      this.SearchRevertINVname = Orderaccount;\n      this.truesumprice = 0.00;\n      this.truesumvat = 0.00;\n      this.trueproductprice = 0.00;\n      var databillid = '';\n      this.getdate();\n      if (billid == '') {\n        databillid = '%20';\n        this.SearchRevertBillno = '%20';\n      } else {\n        databillid = billid;\n        this.SearchRevertBillno = billid;\n      }\n      this.http.get(this.url + 'get_bill_ID/' + databillid + '/' + Orderaccount + '/' + this.fromdate + '/' + this.todate + '/' + sale).subscribe(res => {\n        this.billnoDataSearch = res;\n        if (res.length > 0) {\n          /*var test1 = res[0].attachedfile.splie();*/\n          for (var i = 0; i < res.length; i++) {\n            var alllength = JSON.stringify(res[i].attachedfile).length;\n            var St1 = JSON.stringify(res[i].attachedfile).substring(2, 4);\n            var St2 = JSON.stringify(res[i].attachedfile).substring(alllength - 8, alllength - 5);\n            var St3 = JSON.stringify(res[i].attachedfile).substring(alllength - 19, alllength - 15);\n            var sumst = `${St1}-${St2}${St3}`;\n            for (var v = 0; v < res.length; v++) {\n              var alllengthbook = JSON.stringify(res[i].attachedfileBookbank).length;\n              var St1book = JSON.stringify(res[i].attachedfileBookbank).substring(2, 4);\n              var St2book = JSON.stringify(res[i].attachedfileBookbank).substring(alllengthbook - 8, alllengthbook - 5);\n              var St3book = JSON.stringify(res[i].attachedfileBookbank).substring(alllengthbook - 19, alllengthbook - 15);\n              var sumstbook = `${St1book}-${St2book}${St3book}`;\n            }\n            var checkbank;\n            if (res[i].attachedfileBookbank === 'A') {\n              checkbank = res[i].attachedfileBookbank;\n            } else {\n              checkbank = 'B';\n            }\n            this.BillId.push({\n              noid: i,\n              idbill: res[i].id,\n              salesid: res[i].salesid,\n              invoiceid: res[i].invoiceid,\n              invoicename: res[i].invoicingname,\n              orderaccount: res[i].orderaccount,\n              invoicedate: res[i].invoicedate,\n              duedate: res[i].duedate,\n              salesbalance: res[i].salesbalance,\n              sumtax: res[i].sumtax,\n              invoiceamount: res[i].invoiceamount,\n              billno: res[i].billno,\n              approve: res[i].approve,\n              payment: res[i].payment,\n              urlimg: res[i].attachedfile,\n              delivery: res[i].deliveryname,\n              check: false,\n              nameimg: res[i].attachedfile,\n              saleman: res[i].salegroup,\n              remark: res[i].remark,\n              remarkst: res[i].remarkstatus,\n              numrun: sumst,\n              imgbookbank: res[i].attachedfileBookbank,\n              numrunbank: sumstbook,\n              checkbank: checkbank\n            });\n          }\n          this.showbill();\n          this.sumsuccess();\n        } else {\n          this.showbillno = 'ไม่มีรายการ';\n        }\n      }, error => {\n        status = error.status;\n        // this.openModal(true,'เกิดปัญหาในการ Process ข้อมูล กรุณารายการใหม่อีกครั้ง',false);\n        alert('เกิดปัญหาในการ Process ข้อมูล กรุณารายการใหม่อีกครั้ง');\n      });\n    }\n    OpenModalApprove(Invoicingname, billid, template, Orderaccount, saleid) {\n      this.saleid = saleid;\n      this.getIdBill(Orderaccount, billid, saleid);\n      this.ModalRefApprove = this.modalService.show(template, {\n        class: 'modal-lg',\n        ignoreBackdropClick: true\n      });\n    }\n    getIdBill(Invoicingname, billid, saleid) {\n      this.BillId = [];\n      this.total = 0;\n      this.SearchRevertBillno = '';\n      this.SearchRevertINVname = '';\n      this.Remark = '';\n      this.testcheck = false;\n      this.SearchRevertINVname = Invoicingname;\n      this.truesumprice = 0.00;\n      this.truesumvat = 0.00;\n      this.trueproductprice = 0.00;\n      var databillid = '';\n      this.getdate();\n      if (billid == '') {\n        databillid = '%20';\n        this.SearchRevertBillno = '%20';\n      } else {\n        databillid = billid;\n        this.SearchRevertBillno = billid;\n      }\n      this.http.get(this.url + 'get_bill_ID/' + databillid + '/' + Invoicingname + '/' + this.fromdate + '/' + this.todate + '/' + saleid).subscribe(res => {\n        this.billnoDataSearch = res;\n        if (res.length > 0) {\n          /*var test1 = res[0].attachedfile.splie();*/\n          for (var i = 0; i < res.length; i++) {\n            var alllength = JSON.stringify(res[i].attachedfile).length;\n            var St1 = JSON.stringify(res[i].attachedfile).substring(2, 4);\n            var St2 = JSON.stringify(res[i].attachedfile).substring(alllength - 8, alllength - 5);\n            var St3 = JSON.stringify(res[i].attachedfile).substring(alllength - 19, alllength - 15);\n            var sumst = `${St1}-${St2}${St3}`;\n            for (var v = 0; v < res.length; v++) {\n              var alllengthbook = JSON.stringify(res[i].attachedfileBookbank).length;\n              var St1book = JSON.stringify(res[i].attachedfileBookbank).substring(2, 4);\n              var St2book = JSON.stringify(res[i].attachedfileBookbank).substring(alllengthbook - 8, alllengthbook - 5);\n              var St3book = JSON.stringify(res[i].attachedfileBookbank).substring(alllengthbook - 19, alllengthbook - 15);\n              var sumstbook = `${St1book}-${St2book}${St3book}`;\n            }\n            var checkbank;\n            if (res[i].attachedfileBookbank === 'A') {\n              checkbank = res[i].attachedfileBookbank;\n            } else {\n              checkbank = 'B';\n            }\n            this.BillId.push({\n              noid: i,\n              idbill: res[i].id,\n              salesid: res[i].salesid,\n              invoiceid: res[i].invoiceid,\n              invoicename: res[i].invoicingname,\n              orderaccount: res[i].orderaccount,\n              invoicedate: res[i].invoicedate,\n              duedate: res[i].duedate,\n              salesbalance: res[i].salesbalance,\n              sumtax: res[i].sumtax,\n              invoiceamount: res[i].invoiceamount,\n              billno: res[i].billno,\n              approve: res[i].approve,\n              payment: res[i].payment,\n              urlimg: res[i].attachedfile,\n              delivery: res[i].deliveryname,\n              check: false,\n              nameimg: res[i].attachedfile,\n              saleman: res[i].salegroup,\n              remark: res[i].remark,\n              remarkst: res[i].remarkstatus,\n              numrun: sumst,\n              imgbookbank: res[i].attachedfileBookbank,\n              numrunbank: sumstbook,\n              checkbank: checkbank\n            });\n          }\n          this.showbill();\n        } else {\n          this.showbillno = 'ไม่มีรายการ';\n        }\n      }, error => {\n        status = error.status;\n        // this.openModal(true,',false);\n        alert('เกิดปัญหาในการ Process ข้อมูล กรุณารายการใหม่อีกครั้ง');\n      });\n    }\n    getIdBill_nobookbank(Invoicingname, billid, Idcostomer, saleid) {\n      this.BillId = [];\n      this.total = 0;\n      this.billnoDataSearch = [];\n      this.Bookbank = [];\n      this.SearchRevertBillno = '';\n      this.SearchRevertINVname = '';\n      this.Remark = '';\n      this.testcheck = false;\n      this.SearchRevertINVname = Idcostomer; //Invoicingname\n      this.truesumprice = 0.00;\n      this.truesumvat = 0.00;\n      this.trueproductprice = 0.00;\n      var databillid = '';\n      this.getdate();\n      if (billid == '') {\n        databillid = '%20';\n        this.SearchRevertBillno = '%20';\n      } else {\n        databillid = billid;\n        this.SearchRevertBillno = billid;\n      }\n      if (saleid == \"\") {\n        saleid = \"%20\";\n      }\n      this.http.get(this.url + 'get_bill_ID_nobookbank/' + databillid + '/' + Idcostomer + '/' + this.fromdate + '/' + this.todate + '/' + saleid).subscribe(res => {\n        this.billnoDataSearch = res;\n        if (res.length > 0) {\n          /*var test1 = res[0].attachedfile.splie();*/\n          for (var i = 0; i < res.length; i++) {\n            var alllength = JSON.stringify(res[i].attachedfile).length;\n            var St1 = JSON.stringify(res[i].attachedfile).substring(2, 4);\n            var St2 = JSON.stringify(res[i].attachedfile).substring(alllength - 8, alllength - 5);\n            var St3 = JSON.stringify(res[i].attachedfile).substring(alllength - 19, alllength - 15);\n            var sumst = `${St1}-${St2}${St3}`;\n            for (var v = 0; v < res.length; v++) {\n              var alllengthbook = JSON.stringify(res[i].attachedfileBookbank).length;\n              var St1book = JSON.stringify(res[i].attachedfileBookbank).substring(2, 4);\n              var St2book = JSON.stringify(res[i].attachedfileBookbank).substring(alllengthbook - 8, alllengthbook - 5);\n              var St3book = JSON.stringify(res[i].attachedfileBookbank).substring(alllengthbook - 19, alllengthbook - 15);\n              var sumstbook = `${St1book}-${St2book}${St3book}`;\n            }\n            var checkbank;\n            if (res[i].attachedfileBookbank === 'A') {\n              checkbank = res[i].attachedfileBookbank;\n            } else {\n              checkbank = 'B';\n            }\n            this.BillId.push({\n              noid: i,\n              idbill: res[i].id,\n              salesid: res[i].salesid,\n              invoiceid: res[i].invoiceid,\n              invoicename: res[i].invoicingname,\n              orderaccount: res[i].orderaccount,\n              invoicedate: res[i].invoicedate,\n              duedate: res[i].duedate,\n              salesbalance: res[i].salesbalance,\n              sumtax: res[i].sumtax,\n              invoiceamount: res[i].invoiceamount,\n              billno: res[i].billno,\n              approve: res[i].approve,\n              payment: res[i].payment,\n              urlimg: res[i].attachedfile,\n              delivery: res[i].deliveryname,\n              check: false,\n              nameimg: res[i].attachedfile,\n              saleman: res[i].salegroup,\n              remark: res[i].remark,\n              remarkst: res[i].remarkstatus,\n              numrun: sumst,\n              imgbookbank: res[i].attachedfileBookbank,\n              numrunbank: sumstbook,\n              checkbank: checkbank\n            });\n          }\n          this.showbill();\n          this.searchviewBookbank();\n        } else {\n          this.showbillno = 'ไม่มีรายการ';\n        }\n      }, error => {\n        status = error.status;\n        //this.openModal(true,'',false);\n        alert('เกิดปัญหาในการ Process ข้อมูล กรุณารายการใหม่อีกครั้ง');\n      });\n    }\n    getViewnum() {\n      if (this.BillId.length > 0) {\n        for (var i = 0; i < this.BillId.length; i++) {\n          this.groupimg.push({\n            id: i + 1,\n            nameimg: this.BillId[i].numrun\n          });\n        }\n      }\n    }\n    getIdBillSuccess(Invoicingname, billid, Orderaccount, saleid) {\n      this.BillId = [];\n      this.clients = [];\n      this.dataTable = [];\n      this.INVSuccess = [];\n      this.total = 0;\n      this.testcheck = false;\n      this.truesumprice = 0.00;\n      this.truesumvat = 0.00;\n      this.trueproductprice = 0.00;\n      this.getdate();\n      var databillid = '';\n      var example = [] = [];\n      this.billno = '';\n      this.nameinv = '';\n      this.nameinv = Orderaccount;\n      if (billid == '') {\n        databillid = '%20';\n        this.billno = '%20';\n      } else {\n        databillid = billid;\n        this.billno = billid;\n      }\n      this.http.get(this.url + 'get_bill_ID_Success/' + databillid + '/' + Orderaccount + '/' + this.fromdate + '/' + this.todate + '/%20' + '/' + saleid).subscribe(res => {\n        if (res.length > 0) {\n          for (var i = 0; i < res.length; i++) {\n            var alllength = JSON.stringify(res[i].attachedfile).length;\n            var St1 = JSON.stringify(res[i].attachedfile).substring(2, 4);\n            var St2 = JSON.stringify(res[i].attachedfile).substring(alllength - 8, alllength - 5);\n            var St3 = JSON.stringify(res[i].attachedfile).substring(alllength - 19, alllength - 15);\n            var sumst = `${St1}-${St2}${St3}`;\n            for (var v = 0; v < res.length; v++) {\n              var alllengthbook = JSON.stringify(res[i].attachedfileBookbank).length;\n              var St1book = JSON.stringify(res[i].attachedfileBookbank).substring(2, 4);\n              var St2book = JSON.stringify(res[i].attachedfileBookbank).substring(alllengthbook - 8, alllengthbook - 5);\n              var St3book = JSON.stringify(res[i].attachedfileBookbank).substring(alllengthbook - 19, alllengthbook - 15);\n              var sumstbook = `${St1book}-${St2book}${St3book}`;\n            }\n            var checkbank;\n            if (res[i].attachedfileBookbank === 'A') {\n              checkbank = res[i].attachedfileBookbank;\n            } else {\n              checkbank = 'B';\n            }\n            this.BillId.push({\n              noid: i,\n              idbill: res[i].id,\n              salesid: res[i].salesid,\n              invoiceid: res[i].invoiceid,\n              invoicename: res[i].invoicingname,\n              orderaccount: res[i].orderaccount,\n              invoicedate: res[i].invoicedate,\n              duedate: res[i].duedate,\n              salesbalance: res[i].salesbalance,\n              sumtax: res[i].sumtax,\n              invoiceamount: res[i].invoiceamount,\n              billno: res[i].billno,\n              approve: res[i].approve,\n              payment: res[i].payment,\n              urlimg: res[i].attachedfile,\n              delivery: res[i].deliveryname,\n              check: false,\n              nameimg: res[i].attachedfile,\n              saleman: res[i].salegroup,\n              remark: res[i].remark,\n              remarkst: res[i].remarkstatus,\n              numrun: sumst,\n              imgbookbank: res[i].attachedfileBookbank,\n              numrunbank: sumstbook,\n              checkbank: checkbank\n            });\n          }\n          this.showbill();\n          this.sumsuccess();\n          this.INVSuccess = this.BillId;\n          /* this.clients = res;\n          // You'll have to wait that changeDetection occurs and projects data into\n           // the HTML template, you can ask Angular to that for you ;-)\n                        this.chRef.checkNoChanges();\n           example = $('#example');\n            this.dataTable = example.DataTable({\n             'paging'      : true,\n             'lengthChange': false,\n             'searching'   : true,\n             'ordering'    : true,\n             'info'        : true,\n             'autoWidth'   : true\n           });\n                     this.showbill();\n          */\n        } else {\n          this.showbillno = 'ไม่มีรายการ';\n          this.productpricesuccess = 0;\n          this.sumvatsuccess = 0;\n          this.sumpricesuccess = 0;\n        }\n      }, error => {\n        //  this.openModal(true,'เกิดปัญหาในการ Process ข้อมูล กรุณารายการใหม่อีกครั้ง',false);\n        alert('เกิดปัญหาในการ Process ข้อมูล กรุณารายการใหม่อีกครั้ง');\n      });\n    }\n    getIdBillSuccessINV() {\n      this.getdate();\n      if (this.dateinvid == '') {\n        this.dateinvid = '%20';\n      }\n      this.BillId = [];\n      this.http.get(this.url + 'get_bill_ID_SuccessCH/' + this.billno + '/' + this.nameinv + '/' + this.fromdate + '/' + this.todate + '/' + this.dateinvid).subscribe(res => {\n        if (res.length > 0) {\n          for (var i = 0; i < res.length; i++) {\n            var alllength = JSON.stringify(res[i].attachedfile).length;\n            var St1 = JSON.stringify(res[i].attachedfile).substring(2, 4);\n            var St2 = JSON.stringify(res[i].attachedfile).substring(alllength - 8, alllength - 5);\n            var St3 = JSON.stringify(res[i].attachedfile).substring(alllength - 19, alllength - 15);\n            var sumst = `${St1}-${St2}${St3}`;\n            for (var v = 0; v < res.length; v++) {\n              var alllengthbook = JSON.stringify(res[i].attachedfileBookbank).length;\n              var St1book = JSON.stringify(res[i].attachedfileBookbank).substring(2, 4);\n              var St2book = JSON.stringify(res[i].attachedfileBookbank).substring(alllengthbook - 8, alllengthbook - 5);\n              var St3book = JSON.stringify(res[i].attachedfileBookbank).substring(alllengthbook - 19, alllengthbook - 15);\n              var sumstbook = `${St1book}-${St2book}${St3book}`;\n            }\n            var checkbank;\n            if (res[i].attachedfileBookbank === 'A') {\n              checkbank = res[i].attachedfileBookbank;\n            } else {\n              checkbank = 'B';\n            }\n            this.BillId.push({\n              noid: i,\n              idbill: res[i].id,\n              salesid: res[i].salesid,\n              invoiceid: res[i].invoiceid,\n              invoicename: res[i].invoicingname,\n              orderaccount: res[i].orderaccount,\n              invoicedate: res[i].invoicedate,\n              duedate: res[i].duedate,\n              salesbalance: res[i].salesbalance,\n              sumtax: res[i].sumtax,\n              invoiceamount: res[i].invoiceamount,\n              billno: res[i].billno,\n              approve: res[i].approve,\n              payment: res[i].payment,\n              urlimg: res[i].attachedfile,\n              delivery: res[i].deliveryname,\n              check: false,\n              nameimg: res[i].attachedfile,\n              saleman: res[i].salegroup,\n              remark: res[i].remark,\n              remarkst: res[i].remarkstatus,\n              numrun: sumst,\n              imgbookbank: res[i].attachedfileBookbank,\n              numrunbank: sumstbook,\n              checkbank: checkbank\n            });\n          }\n          if (this.dateinvid = '%20') {\n            this.dateinvid = '';\n          }\n          this.sumsuccess();\n        } else {\n          this.productpricesuccess = 0;\n          this.sumvatsuccess = 0;\n          this.sumpricesuccess = 0;\n        }\n      }, error => {\n        // this.openModal(true,,false);\n        alert('เกิดปัญหาในการ Process ข้อมูล');\n      });\n    }\n    sumsuccess() {\n      this.productpricesuccess = 0;\n      this.sumvatsuccess = 0;\n      this.sumpricesuccess = 0;\n      if (this.BillId.length > 0) {\n        for (var i = 0; i < this.BillId.length; i++) {\n          this.productpricesuccess += this.BillId[i].salesbalance;\n          this.sumvatsuccess += this.BillId[i].sumtax;\n          this.sumpricesuccess += this.BillId[i].invoiceamount;\n        }\n      }\n    }\n    showbill() {\n      if (this.BillId[0].billno == '') {\n        this.showbillno = this.BillId[0].invoicename;\n      } else {\n        this.showbillno = this.BillId[0].invoicename + ' :  ' + this.BillId[0].billno;\n      }\n    }\n    selectAllbookbank(checked) {\n      for (var i = 0; i < this.BillId.length; i++) {\n        if (this.viewBookbank === 'A') {\n          if (this.BillId[i].checkbank === 'A') {\n            this.BillId[i].check = checked;\n          }\n        } else if (this.viewBookbank === 'B') {\n          if (this.BillId[i].checkbank === 'B') {\n            this.BillId[i].check = checked;\n          }\n        } else {\n          this.BillId[i].check = checked;\n        }\n      }\n      this.GetbillTrue();\n      this.total = this.Bill_approve.length;\n      this.sumTrue();\n    }\n    checkIfAllSelectedbookbank(checked, index) {\n      this.BillId[index].check = checked;\n      this.total = 0;\n      this.GetbillTrue();\n      this.total = this.Bill_approve.length;\n      this.getcaheck();\n      this.sumTrue();\n    }\n    selectAll(checked) {\n      for (var i = 0; i < this.BillId.length; i++) {\n        this.BillId[i].check = checked;\n      }\n      this.GetbillTrue();\n      this.total = this.Bill_approve.length;\n      this.sumTrue();\n    }\n    checkIfAllSelected(checked, index) {\n      this.BillId[index].check = checked;\n      this.total = 0;\n      this.GetbillTrue();\n      this.total = this.Bill_approve.length;\n      this.getcaheck();\n      this.sumTrue();\n    }\n    GetbillTrue() {\n      this.Bill_approve = [];\n      for (var i = 0; i < this.BillId.length; i++) {\n        if (this.BillId[i].check == true) {\n          this.Bill_approve.push({\n            id: this.BillId[i].invoiceid,\n            billno: this.BillId[i].billno\n          });\n        }\n      }\n    }\n    getcaheck() {\n      var ch = 0;\n      for (var i = 0; i < this.BillId.length; i++) {\n        if (this.BillId[i].check == true) {\n          ch++;\n        } else {\n          ch--;\n        }\n      }\n      if (ch == this.BillId.length) {\n        this.testcheck = true;\n      } else {\n        this.testcheck = false;\n      }\n    }\n    deletelallid(value) {\n      this.Bill_approve.splice(value, 1);\n    }\n    newUpLoadApprove(st, remark) {\n      this.productprice = 0;\n      this.sumvat = 0;\n      this.sumprice = 0;\n      this.http.post(this.url + 'update_invoice_ByApproveNEW', {\n        Data: this.Bill_approve,\n        stapp: st,\n        remark: ', ' + remark\n      }).subscribe(res => {\n        if (res == true) {\n          this.SearchCompleteNoPrintst();\n          // this.getIdBill(this.SearchRevertINVname,this.SearchRevertBillno);\n          /* this.openModalalert(false,'',false);\n           this.openModal2(true,'บันทึกข้อมูลเรียบร้อย',true); */\n          this.textload = \"บันทึกข้อมูลเรียบร้อย\";\n          this.btnPDF = true;\n          this.testcheck = false;\n          this.ModalRefApprove.hide();\n          setTimeout(() => this.modalRef.hide(), 5000);\n        } else {\n          alert('เกิดปัญหาในการ Process ข้อมูล');\n        }\n      }, error => {\n        alert('เกิดปัญหาในการ Process ข้อมูล');\n        // this.openModal(true,'เกิดปัญหาในการ Process ข้อมูล',false);\n      });\n    }\n    UpLoadApprove(st, remark) {\n      this.productprice = 0;\n      this.sumvat = 0;\n      this.sumprice = 0;\n      this.load = 'รายการที่ : ' + this.Bill_approve.length.toString();\n      if (this.Bill_approve.length != 0) {\n        this.http.post(this.url + 'update_invoice_ByApprove', {\n          invoiceid: this.Bill_approve[0].id,\n          stapp: st,\n          remark: ',' + remark\n        }).subscribe(res => {\n          if (res == true) {\n            this.deletelallid(0);\n            this.load = 'รายการที่ : ' + this.Bill_approve.length.toString();\n          } else {}\n        }, error => {\n          alert('เกิดปัญหาในการ Process ข้อมูล');\n          // this.openModal(true,'เกิดปัญหาในการ Process ข้อมูล',false);\n        });\n      } else {\n        if (this.Bill_approve.length == 0) {\n          clearInterval(this.setInterval);\n          this.SearchCompleteNoPrintst();\n          this.getIdBill(this.SearchRevertINVname, this.SearchRevertBillno, this.saleid);\n          // this.searchselectprint();\n          /* this.openModalalert(false,'',false);\n           this.openModal2(true,'บันทึกข้อมูลเรียบร้อย',true); */\n          this.textload = \"บันทึกข้อมูล Remark เรียบร้อย\";\n          this.btnPDF = true;\n          this.testcheck = false;\n        }\n      }\n    }\n    newUpLoadremark(remark, st, typeupload, saleid) {\n      this.productprice = 0;\n      this.sumvat = 0;\n      this.sumprice = 0;\n      this.http.post(this.url + 'update_invoice_ByremarkNEW', {\n        Data: this.Bill_approve,\n        remark: ', ' + remark,\n        st: st\n      }).subscribe(res => {\n        if (res == true) {\n          this.openModalalert(false, 'กำลังบันทึกข้อมูล', false);\n          this.Remark = '';\n          this.selectAll(false);\n          // alert(typeupload)\n          if (typeupload === 'Revert') {\n            //  alert('1')\n            this.getIdBillRevert(this.SearchRevertINVname, this.SearchRevertBillno, saleid);\n            this.SearchCompleteED(this.keyboardStrApprove, this.keyboardStrpaymenttype);\n          } else {\n            // alert('2')\n            this.getIdBill(this.SearchRevertINVname, this.SearchRevertBillno, saleid);\n            this.SearchComplete();\n          }\n          // this.SearchComplete();\n          this.ModalremarkRef.hide();\n          this.textload = \"บันทึกข้อมูล Remark เรียบร้อย\";\n          this.btnPDF = true;\n          this.testcheck = false;\n        } else {\n          alert('เกิดปัญหาในการ Process ข้อมูล');\n        }\n      }, error => {\n        //  this.openModal(true,'เกิดปัญหาในการ Process ข้อมูล',false);\n        alert('เกิดปัญหาในการ Process ข้อมูล');\n      });\n    }\n    UpLoadremark(remark, st, typeupload) {\n      this.productprice = 0;\n      this.sumvat = 0;\n      this.sumprice = 0;\n      this.load = 'รายการที่ : ' + this.Bill_approve.length.toString();\n      if (this.Bill_approve.length != 0) {\n        this.http.post(this.url + 'update_invoice_Byremark', {\n          invoiceid: this.Bill_approve[0].id,\n          remark: ',' + remark,\n          st: st\n        }).subscribe(res => {\n          if (res == true) {\n            this.Remark = '';\n            this.selectAll(false);\n            // alert(typeupload)\n            if (typeupload === 'Revert') {\n              this.getIdBillRevert(this.SearchRevertINVname, this.SearchRevertBillno, this.saleid);\n            } else {\n              this.getIdBill(this.SearchRevertINVname, this.SearchRevertBillno, this.saleid);\n            }\n            this.SearchCompleteNoPrintst();\n            //  this.searchselectprint();\n            this.textload = \"บันทึกข้อมูล Remark เรียบร้อย\";\n            this.btnPDF = true;\n            this.testcheck = false;\n          } else {}\n        }, error => {\n          //  this.openModal(true,'เกิดปัญหาในการ Process ข้อมูล',false);\n          alert('เกิดปัญหาในการ Process ข้อมูล');\n        });\n      } else {\n        if (this.Bill_approve.length == 0) {\n          clearInterval(this.setInterval);\n          this.Remark = '';\n          this.selectAll(false);\n          // alert(typeupload)\n          if (typeupload === 'Revert') {\n            this.getIdBillRevert(this.SearchRevertINVname, this.SearchRevertBillno, this.saleid);\n          } else {\n            this.getIdBill(this.SearchRevertINVname, this.SearchRevertBillno, this.saleid);\n          }\n          // this.openModalalert(false,'',false);\n          // this.openModal2(true,'บันทึกข้อมูล Remark เรียบร้อย ',true);\n          // this.getIdBillRevert(this.SearchRevertINVname,this.SearchRevertBillno)\n          this.SearchCompleteNoPrintst();\n          this.textload = \"บันทึกข้อมูลเรียบร้อย\";\n          this.btnPDF = true;\n          this.testcheck = false;\n        }\n      }\n    }\n    addremarkRevert() {\n      var remark = '';\n      var st = '1';\n      var typeupload = 'Revert';\n      remark = this.Remark;\n      this.GetbillTrue();\n      if (this.Bill_approve.length > 0) {\n        this.openModalalert(true, 'กำลังบันทึกข้อมูล', false);\n        // this.setInterval=setInterval(() => this.UpLoadremark(remark,st,typeupload), 400);\n        this.newUpLoadremark(remark, st, typeupload, this.saleid);\n      } else {}\n    }\n    newUpLoadRevert(st, remark) {\n      this.productprice = 0;\n      this.sumvat = 0;\n      this.sumprice = 0;\n      this.http.post(this.url + 'update_invoice_ByRevertNEW', {\n        Data: this.Bill_approve,\n        stapp: st,\n        remark: ', ' + remark\n      }).subscribe(res => {\n        if (res == true) {\n          this.SearchCompleteED(this.keyboardStrApprove, this.keyboardStrpaymenttype);\n          this.getIdBillRevert(this.SearchRevertINVname, this.SearchRevertBillno, this.saleid);\n          this.textload = \"บันทึกข้อมูลเรียบร้อย\";\n          this.btnPDF = true;\n          this.testcheck = false;\n        } else {\n          alert('เกิดปัญหาในการ Process ข้อมูล');\n        }\n      }, error => {\n        // this.openModal(true,'เกิดปัญหาในการ Process ข้อมูล',false);\n        alert('เกิดปัญหาในการ Process ข้อมูล');\n      });\n    }\n    UpLoadRevert(st, remark) {\n      this.productprice = 0;\n      this.sumvat = 0;\n      this.sumprice = 0;\n      this.load = 'รายการที่ : ' + this.Bill_approve.length.toString();\n      if (this.Bill_approve.length != 0) {\n        this.http.post(this.url + 'update_invoice_ByRevert', {\n          Data: this.Bill_approve[0].id,\n          stapp: st,\n          remark: remark\n        }).subscribe(res => {\n          if (res == true) {\n            this.deletelallid(0);\n            this.load = 'รายการที่ : ' + this.Bill_approve.length.toString();\n          } else {}\n        }, error => {\n          // this.openModal(true,'เกิดปัญหาในการ Process ข้อมูล',false);\n          alert('เกิดปัญหาในการ Process ข้อมูล');\n        });\n      } else {\n        if (this.Bill_approve.length == 0) {\n          clearInterval(this.setInterval);\n          this.SearchCompleteED(this.keyboardStrApprove, this.keyboardStrpaymenttype);\n          this.getIdBillRevert(this.SearchRevertINVname, this.SearchRevertBillno, this.saleid);\n          /*this.openModalalert(false,'',false);\n          this.openModal2(true,'บันทึกข้อมูลเรียบร้อย Revert',true);*/\n          this.textload = \"บันทึกข้อมูลเรียบร้อย\";\n          this.btnPDF = true;\n          this.testcheck = false;\n        }\n      }\n    }\n    addremark(template) {\n      var remark = '';\n      var st = '1';\n      var typeupload = 'Approve';\n      remark = this.Remark;\n      this.GetbillTrue();\n      this.ModalremarkRef.hide();\n      if (this.Bill_approve.length > 0) {\n        // this.openModalalert(true,'กำลังบันทึกข้อมูล',false);\n        this.textload = \"กำลังบันทึกข้อมูล\";\n        this.openModalshow(template);\n        this.btnPDF = false;\n        //this.setInterval=setInterval(() => this.UpLoadremark(remark,st,typeupload), 400);\n        this.newUpLoadremark(remark, st, typeupload, this.saleid);\n      } else {}\n    }\n    onUpload(template) {\n      this.Bill_approve = [];\n      var remark = '';\n      if (this.Remark == '') {\n        remark = '';\n      } else {\n        remark = this.Remark;\n      }\n      var st = 'อนุมัติแล้ว';\n      this.GetbillTrue();\n      if (this.Bill_approve.length > 0) {\n        //  this.openModalalert(true,'กำลังบันทึกข้อมูล',false);\n        this.textload = \"กำลังบันทึกข้อมูล\";\n        this.openModalshow(template);\n        this.btnPDF = false;\n        //this.setInterval=setInterval(() => this.UpLoadApprove(st,remark), 400);\n        this.newUpLoadApprove(st, remark);\n      } else {}\n    }\n    onRevert(template) {\n      this.Bill_approve = [];\n      var remark = '';\n      if (this.Remark == '') {\n        remark = '';\n      } else {\n        remark = this.Remark;\n      }\n      var st = 'รออนุมัติ';\n      this.GetbillTrue();\n      if (this.Bill_approve.length > 0) {\n        // this.openModalalert(true,'กำลังบันทึกข้อมูล',false);\n        this.textload = \"กำลังบันทึกข้อมูล\";\n        this.openModalshow(template);\n        this.btnPDF = false;\n        // this.setInterval=setInterval(() => this.UpLoadRevert(st,remark), 400);\n        this.newUpLoadRevert(st, remark);\n      } else {}\n    }\n    openModalBillViewDetail(ViewDetail, Invoiceid, orderaccount, salesid, invoicename, invoicedate, duedate, delivery, salesbalance, sumtax, invoiceamount, saleman, remark, imgBill, imgbookbank) {\n      var I3remark = remark.substring(0, 4);\n      if (I3remark === ' , ,') {\n        this.remarkshow = remark.substring(4);\n      } else {\n        this.remarkshow = remark.substring(2);\n      }\n      this.SalesId = '';\n      this.SONo = '';\n      this.InvoiceNo = '';\n      this.OrderAccount = '';\n      this.Invoicingname = '';\n      this.InvoiceDate = '';\n      this.DueDate = '';\n      this.Deliveryname = '';\n      this.productsun = '';\n      this.VAT = '';\n      this.suntotal = '';\n      this.SalesId = saleman;\n      this.SONo = salesid;\n      this.InvoiceNo = Invoiceid;\n      this.OrderAccount = orderaccount;\n      this.Invoicingname = invoicename;\n      this.InvoiceDate = invoicedate;\n      this.DueDate = duedate;\n      this.Deliveryname = delivery;\n      this.productsun = salesbalance;\n      this.VAT = sumtax;\n      this.suntotal = invoiceamount;\n      /*this.remarkshow=I3remark;\n      this.mdlSampleIsOpenBill = open;\n      this.altBill=text;\n      this.checkreloadBill=load;*/\n      this.setImgInvoice(imgBill);\n      this.setImgBill(imgBill);\n      this.setImgBookbank(imgbookbank);\n      this.modalRefviewDetail = this.modalService.show(ViewDetail, {\n        class: 'modal-lg'\n      });\n    }\n    closemodelBill(cl) {\n      this.mdlSampleIsOpenBill = cl;\n      if (this.checkreloadBill == true) {\n        this.SearchComplete();\n      }\n    }\n    openModalimg(ViewDetailApprove, nameimage, invid, orderaccount, salesid, invoicename, invoicedate, duedate, delivery, salesbalance, sumtax, invoiceamount, saleman, remark, imgbookbank) {\n      this.SalesId = '';\n      this.SONo = '';\n      this.InvoiceNo = '';\n      this.OrderAccount = '';\n      this.Invoicingname = '';\n      this.InvoiceDate = '';\n      this.DueDate = '';\n      this.Deliveryname = '';\n      this.productsun = '';\n      this.VAT = '';\n      this.suntotal = '';\n      this.remarkshow = '';\n      var I3remark = remark.substring(0, 4);\n      if (I3remark === ' , ,') {\n        this.remarkshow = remark.substring(4);\n      } else {\n        this.remarkshow = remark.substring(2);\n      }\n      /* this.mdlSampleIsOpenimg = open;*/\n      this.altimg = '';\n      this.altimg = invid;\n      /* this.checkreloadimg=load;*/\n      this.setImgBill(nameimage);\n      this.setImgBookbank(imgbookbank);\n      this.mdlSampleIsOpensuccess = false;\n      this.SalesId = saleman;\n      this.SONo = salesid;\n      this.InvoiceNo = invid;\n      this.OrderAccount = orderaccount;\n      this.Invoicingname = invoicename;\n      this.InvoiceDate = invoicedate;\n      this.DueDate = duedate;\n      this.Deliveryname = delivery;\n      this.productsun = salesbalance;\n      this.VAT = sumtax;\n      this.suntotal = invoiceamount;\n      this.modalRefviewDetail = this.modalService.show(ViewDetailApprove, {\n        class: 'modal-lg'\n      });\n    }\n    closemodelimg(cl) {\n      /*this.mdlSampleIsOpenimg=cl;*/\n      if (this.checkreloadimg == false) {}\n    }\n    openModal(open, text, load) {\n      this.mdlSampleIsOpen = open;\n      this.alt = text;\n      this.checkreload = load;\n    }\n    closemodel(cl) {\n      this.mdlSampleIsOpen = cl;\n      if (this.checkreload == true) {}\n    }\n    /*openModalapprove(open : boolean,text: string,load:boolean,billID) : void {\n    this.BillId=[];\n       this.mdlSampleIsOpenApprove = open;\n    this.altApprove=text;\n    this.checkreloadApprove=load;\n    }*/\n    openModal2(open, text, load) {\n      this.mdlSampleIsOpen2 = open;\n      this.alt2 = text;\n      this.checkreload2 = load;\n    }\n    openModalalert(open, text, load) {\n      this.mdlSampleIsOpen3 = open;\n      this.alt3 = text;\n      this.checkreload3 = load;\n    }\n    closemodelapprove(cl) {\n      this.mdlSampleIsOpenApprove = cl;\n      if (this.checkreloadApprove == true) {}\n    }\n    closemodel2(cl) {\n      this.mdlSampleIsOpen2 = cl;\n      if (this.checkreload2 == true) {}\n    }\n    openModalsuccess(viewsuccess, invname, billno, Orderaccount, saleid) {\n      var databillid = '';\n      /* if(billno==''){\n        databillid='%20'\n      }else{\n        databillid=billno;\n      }\n      this.http.get<any>(this.url +'get_Group_IMG/'+databillid +'/'+ invname + '/' + this.fromdate+'/'+ this.todate )\n      .subscribe((res: any[]) => {\n        if(res.length>0){\n            alert(JSON.stringify(res))\n        }\n      });*/\n      this.dateinvid = '';\n      this.getIdBillSuccess(invname, billno, Orderaccount, saleid);\n      this.ModalRefApprove = this.modalService.show(viewsuccess, {\n        class: 'modal-lg',\n        ignoreBackdropClick: true\n      });\n    }\n    closemodelsuccess(cl) {\n      this.mdlSampleIsOpensuccess = cl;\n      if (this.checkreloadsuccess == false) {}\n    }\n    setImgInvoice(nameimage) {\n      var nameimg = `${nameimage}`;\n      this.ImageIN = '../assets/imageINV/' + nameimg;\n    }\n    setImgBill(nameimage) {\n      this.test2 = nameimage;\n      this.ImageBillno = this.urlimg + this.test2;\n    }\n    setImgBookbank(nameimage) {\n      this.test2 = nameimage;\n      if (nameimage === 'A') {\n        this.ImageBookbank = this.urlimgDefault;\n      } else {\n        this.ImageBookbank = this.urlimgBookbank + nameimage;\n      }\n    }\n    handleFileInput(file, modelshow) {\n      this.load = '';\n      this.selectedFile = '';\n      this.Btnimg = false;\n      this.btnPDF = false;\n      if (file.item(0).type == \"image/jpeg\") {\n        //this.openModal2(true,'กำลังปรับขนาดไฟล์',false) && file.item(0).size <= (1024*1024*5)\n        //แจ้งเตือนไฟล์\n        //this.textload=\"กำลังปรับขนาดไฟล์\";\n        //this.modalRefshow = this.modalService.show(modelshow,\n        //    {class: 'modal-sm' , backdrop: \"static\" }\n        //   );\n        let image = file.item(0);\n        this.selectedFile = image;\n        this.textload = this.selectedFile.name;\n        //Show image preview\n        var reader = new FileReader();\n        reader.onload = event => {\n          this.imageUrl = event.target.result;\n          //  this.modalRefshow.hide();\n        };\n        reader.readAsDataURL(this.selectedFile);\n        //this.openModal2(false,'',false)\n        //   this.modalRefshow.hide();\n        // this.Btnimg=true\n        this.end();\n        error => {\n          //  this.openModal(true,error,false)\n          alert('เกิดข้อผิดพลาด');\n        };\n        /*this.textload=\"กำลังปรับขนาดไฟล์\";\n        this.modalRefshow = this.modalService.show(modelshow,\n            {class: 'modal-sm' , backdrop: \"static\" }\n           );\n        let image = file.item(0);\n        this.ng2ImgMax.resizeImage(image, 1024, 768).subscribe(\n          result => {\n            this.selectedFile = result;\n            this.textload= this.selectedFile.name;\n                   var reader = new FileReader();\n            reader.onload = (event:any) => {\n              this.imageUrl = event.target.result;\n                   }\n            reader.readAsDataURL(this.selectedFile);\n                   this.end()\n           },\n          error => {\n                 alert('เกิดข้อผิดพลาด')\n          }\n        */\n        /*  this.selectedFile = file.item(0);*/\n      } else {\n        this.textload = '';\n        //this.openModal(true,'ชนิดไฟล์รูปภาพไม่ถูกต้อง กรุณาเลือกชนิดไฟล์เป็น .jpeg และ ไฟล์ขนาดไม่เกิน 5120 kb',false)\n        alert('ชนิดไฟล์รูปภาพไม่ถูกต้อง กรุณาเลือกชนิดไฟล์เป็น .jpeg');\n        this.imageUrl = \"assets/img/default-image.png\";\n        return false;\n      }\n    }\n    end() {\n      //this.textload='ปิด'\n      this.Btnimg = true;\n      this.btnPDF = false;\n      this.modalRefshow.hide();\n    }\n    OpenUploadbookbank(template, idSo) {\n      this.btnPDF = false;\n      this.btnREpdf = false;\n      this.showIDso = \"\";\n      this.showIDso = idSo;\n      this.textload = \"\";\n      this.imageUrl = \"assets/img/default-image.png\";\n      this.selectedFile = null;\n      this.modalRef = this.modalService.show(template, this.config);\n    }\n    onUploadbookbank(templateShow, template) {\n      this.modalRefupbookbank.hide();\n      this.textload = \"กำลังอัพโหลดไฟล์ โปรดรอ.\";\n      this.openModalshow(templateShow);\n      const fd = new FormData();\n      let time = new Date().getFullYear() + '-' + new Date().getMonth() + '-' + new Date().getDate();\n      fd.append('bookbank', this.selectedFile, this.selectedFile.name);\n      this.http.post(this.url + this.namebookbank + '-' + time + '/uploadbookbank', fd, {\n        reportProgress: true,\n        observe: 'events'\n      }).subscribe(event => {\n        if (event.type === HttpEventType.UploadProgress) {} else if (event.type === HttpEventType.Response) {\n          console.log(event);\n          if (event.body.success === true) {\n            this.textload = \"อัพโหลดไฟล์เสร็จสิ้น\";\n            this.textload = \"โปรดรอ.\";\n            this.btnPDF = false;\n            this.Btnimg = false;\n            //this.updataFlie(this.Bill_approve[i].id,event.body._name)\n            // alert(event.body._namebook)\n            // this.setInterval=setInterval(() => this.updataFlie(event.body._namebook), 700);\n            this.newupdataFlie(event.body._namebook);\n          } else {\n            this.textload = \"เกิดปัญหาในการ upload กรุณาทำรายการใหม่\";\n            this.btnPDF = false;\n            this.Btnimg = false;\n            this.btnREpdf = true;\n            //alert('เกิดปัญหาในการ upload กรุณาทำรายการใหม่' )\n          }\n        }\n      });\n    }\n    newupdataFlie(_name) {\n      this.http.post(this.url + 'updatabookbank_idSoNEW', {\n        Data: this.Bill_approve,\n        _name: _name\n      }).subscribe(res => {\n        //  alert(res)\n        if (res == true) {\n          this.textload = \"บันทึกข้อมูลเสร็จสิ้น\";\n          this.SearchCompleteNoPrintst();\n          this.getIdBill_nobookbank(this.btnPDF, this.SearchRevertBillno, this.SearchRevertINVname, this.datalogin[0].salegroup);\n          this.Flielist = undefined;\n          this.btnPDF = true;\n          setTimeout(() => this.modalRef.hide(), 5000);\n        } else {\n          // this.textload=\"เกิดปัญหาในการ เพิ่มรายการ\"\n        }\n      }, error => {\n        //เกิดปัญหาในการ Process ข้อมูล\n        // this.openModalsuccess(true,'เกิดปัญหาในการ Process ข้อมูล',false);\n        alert('เกิดปัญหาในการ Process ข้อมูล');\n        // this.openModal(true,'',false);\n      });\n    }\n    updataFlie(_name) {\n      //updataDPF_idSo\n      if (this.Bill_approve.length != 0) {\n        this.http.post(this.url + 'updatabookbank_idSo', {\n          idSo: this.Bill_approve[0].id,\n          _name: _name\n        }).subscribe(res => {\n          //  alert(res)\n          if (res == true) {\n            this.deletelallid(0);\n          } else {\n            // this.textload=\"เกิดปัญหาในการ เพิ่มรายการ\"\n          }\n        });\n      } else {\n        if (this.Bill_approve.length == 0) {\n          clearInterval(this.setInterval);\n          this.btnPDF = true;\n        }\n      }\n      /*\n      do {\n        //alert(num)\n        this.http.post<any>(this.url+'updatabookbank_idSo',{\n          idSo : this.Bill_approve[num].id,\n          _name : _name\n        }).subscribe(res=> {\n            //  alert(res)\n            if(res==true){\n              //this.textload=\"ทำรายการเสร็จสิ้น\"\n                   num++;\n            }else{\n                  // this.textload=\"เกิดปัญหาในการ เพิ่มรายการ\"\n                 }\n                 })\n            console.log(num)\n      } while (num <= this.Bill_approve.length );\n        */\n    }\n    openModalshow(templateShow) {\n      this.modalRef = this.modalService.show(templateShow, {\n        class: 'modal-sm'\n      });\n    }\n    openModalbookbank(Invoicingname, Billno, Idcostomer, templateShow, saleid) {\n      this.Flielist = undefined;\n      this.modalRefupbookbank = this.modalService.show(templateShow,\n      // Object.assign({}, { class: 'modal-lg', backdrop: \"static\" })\n      {\n        class: 'modal-lg',\n        backdrop: \"static\"\n      });\n      this.showIDso = Invoicingname;\n      this.textload = \"\";\n      this.imageUrl = \"assets/img/default-image.png\";\n      this.selectedFile = null;\n      this.namebookbank = Idcostomer;\n      this.viewBookbank = 'A';\n      this.getIdBill_nobookbank(Invoicingname, Billno, Idcostomer, saleid);\n    }\n    confirmimg() {\n      this.modalRefshow.hide();\n    }\n    confirm() {\n      this.modalRef.hide();\n      this.textload = \"\";\n      this.selectedFile = null;\n      // this.Searchsolist();\n    }\n    decline(template) {\n      this.selectedFile = null;\n      this.modalRef.hide();\n      this.modalRef = this.modalService.show(template, this.config);\n    }\n    openModalviewDetail(template, Invoiceid, orderaccount, salesid, invoicename, invoicedate, duedate, delivery, salesbalance, sumtax, invoiceamount, saleman, remark, imgBill, imgbookbank) {\n      var I3remark = remark.substring(0, 4);\n      if (I3remark === ' , ,') {\n        this.remarkshow = remark.substring(4);\n      } else {\n        this.remarkshow = remark.substring(2);\n      }\n      this.SalesId = '';\n      this.SONo = '';\n      this.InvoiceNo = '';\n      this.OrderAccount = '';\n      this.Invoicingname = '';\n      this.InvoiceDate = '';\n      this.DueDate = '';\n      this.Deliveryname = '';\n      this.productsun = '';\n      this.VAT = '';\n      this.suntotal = '';\n      this.SalesId = saleman;\n      this.SONo = salesid;\n      this.InvoiceNo = Invoiceid;\n      this.OrderAccount = orderaccount;\n      this.Invoicingname = invoicename;\n      this.InvoiceDate = invoicedate;\n      this.DueDate = duedate;\n      this.Deliveryname = delivery;\n      this.productsun = salesbalance;\n      this.VAT = sumtax;\n      this.suntotal = invoiceamount;\n      /* this.setImgInvoice(imgBill);*/\n      this.setImgBill(imgBill);\n      if (imgbookbank === 'A') {\n        this.CkimgBookbank = false;\n      } else {\n        this.CkimgBookbank = true;\n        this.setImgBookbank(imgbookbank);\n      }\n      this.modalRefviewDetail = this.modalService.show(template, {\n        class: 'modal-lg'\n      });\n    }\n    Modalremark(template) {\n      this.ModalremarkRef = this.modalService.show(template, {\n        class: 'modal-lg'\n      });\n    }\n    endtemplateView() {\n      this.modalRefupbookbank.hide();\n      this.isCollapsed = true;\n    }\n    GetDelete(template, formdate, todate) {\n      this.CKbookbank = false;\n      this.CkINV = false;\n      this.CKDeleteload = true;\n      this.txtDeleteShow = \"\";\n      this.Formdate = formdate;\n      this.Todate = todate;\n      //this.SearchCompleteINV_Delete(this.Formdate,this.Todate);\n      this.modalDelete = this.modalService.show(template, {\n        class: 'modal-sm',\n        backdrop: \"static\"\n      });\n    }\n    OpenDeleteINVDetail(template) {\n      this.modalDeleteINVDetail = this.modalService.show(template, {\n        class: 'modal-lg'\n      });\n    }\n    ///ฟังชั่น ลบ INV จาก เวลา\n    modalDeleteClose() {\n      this.DataDelete = [];\n      this.DataNameBookbank = [];\n      this.DataNameINV = [];\n      this.btnInvDelete = true;\n      this.numDataDelete = 0;\n      this.modalDelete.hide();\n    }\n    SearchCompleteINV_Delete(formdate, todate) {\n      //this.Formdate = formdate.getFullYear() +'-'+formdate.getMonth()+1 +'-'+formdate.getDate()\n      // this.Todate = todate.getFullYear() +'-'+todate.getMonth()+1 +'-'+todate.getDate()\n      //alert(formdate);\n      this.DataDelete = [];\n      this.http.post(this.url + 'get_inv_completed', {\n        fromdate: formdate,\n        todate: todate\n      }).subscribe(res => {\n        this.DataDelete = res;\n        this.btnInvDelete = false;\n        this.numDataDelete = this.DataDelete.length;\n        this.SearchNameINV(formdate, todate);\n        this.SearchNameINV_BookBank(formdate, todate);\n      }, error => {\n        alert('เกิดปัญหาในการ Process ข้อมูล');\n      });\n    }\n    DeleteINV() {\n      if (confirm('ต้องการลบ รายการที่เลือกใช่หรือไม่')) {\n        this.CKbookbank = false;\n        this.CkINV = false;\n        this.deleteIMGBookbank();\n        this.deleteIMGINV();\n        this.txtDeleteShow = \"กำลังดำเนินการ...\";\n        setTimeout(() => this.CKIMG(), 5000);\n      } else {}\n    }\n    /*DeleteINVTest()  {\n      if(confirm('ต้องการลบ รายการที่เลือกใช่หรือไม่ test')){\n        this.CKbookbank=false;\n        this.CkINV=false;\n      this.deleteIMGBookbank();\n      this.deleteIMGINV()\n      this.txtDeleteShow=\"กำลังดำเนินการ...\"\n      setTimeout(() => this.CKIMG() , 5000);\n           } else {\n           }\n       }*/\n    CKIMG() {\n      //console.log(this.CKbookbank +'--'+ this.CkINV )\n      if (this.CKbookbank == true && this.CkINV == true) {\n        this.DeleteListInv();\n      } else {\n        alert('ลบรูปผิดพลาด');\n      }\n    }\n    DeleteListInv() {\n      this.http.post(this.url + 'delete_invoice_completed', {\n        Data: this.DataDelete\n      }).subscribe(res => {\n        if (res == true) {\n          this.DataDelete = [];\n          this.CKDeleteload = false;\n        } else {\n          alert('เกิดข้อผิดพลาดในการลบ');\n        }\n      }, error => {\n        alert('เกิดปัญหาในการ Process ข้อมูล');\n      });\n    }\n    deleteIMGBookbank() {\n      this.CKbookbank = false;\n      this.http.post(this.url + 'delete_nameInvBookbank', {\n        Data: this.DataNameBookbank\n      }).subscribe(res => {\n        this.CKbookbank = true;\n      }, err => {\n        this.CKbookbank = false;\n      });\n    }\n    deleteIMGINV() {\n      this.CkINV = false;\n      this.http.post(this.url + 'Delete_nameInv', {\n        Data: this.DataNameINV\n      }).subscribe(res => {\n        if (res == true) {\n          this.CkINV = true;\n        }\n      });\n    }\n    SearchNameINV_BookBank(formdate, todate) {\n      this.DataNameBookbank = [];\n      this.http.post(this.url + 'get_NameBookbank_completed', {\n        fromdate: formdate,\n        todate: todate\n      }).subscribe(res => {\n        this.DataNameBookbank = res;\n        /*  this.btnInvDelete =false;\n          this.numDataDelete = this.DataDelete.length*/\n        this.numBookbankDelete = this.DataNameBookbank.length;\n      }, error => {\n        alert('เกิดปัญหาในการ Process ข้อมูล');\n      });\n    }\n    SearchNameINV(formdate, todate) {\n      this.DataNameINV = [];\n      this.http.post(this.url + 'get_NameInv_completed', {\n        fromdate: formdate,\n        todate: todate\n      }).subscribe(res => {\n        this.DataNameINV = res;\n        this.numINVDelete = this.DataNameINV.length;\n      }, error => {\n        alert('เกิดปัญหาในการ Process ข้อมูล');\n      });\n    }\n    static {\n      this.ɵfac = function CompleteinvoiceComponent_Factory(t) {\n        return new (t || CompleteinvoiceComponent)(i0.ɵɵdirectiveInject(i1.Ng2ImgMaxService), i0.ɵɵdirectiveInject(i2.BsModalService), i0.ɵɵdirectiveInject(i3.FormBuilder), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i4.HttpClient), i0.ɵɵdirectiveInject(i5.WebapiService), i0.ɵɵdirectiveInject(i6.NgbCalendar), i0.ɵɵdirectiveInject(i7.ActivatedRoute), i0.ɵɵdirectiveInject(i7.Router));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: CompleteinvoiceComponent,\n        selectors: [[\"app-completeinvoice\"]],\n        decls: 299,\n        vars: 104,\n        consts: [[\"rt\", \"\"], [\"template\", \"\"], [\"templateShow\", \"\"], [\"templateViewDetail\", \"\"], [\"templateViewDetailApprove\", \"\"], [\"templateView\", \"\"], [\"ModalApprove\", \"\"], [\"ModalremarkNew\", \"\"], [\"ModalApproveSuccess\", \"\"], [\"Modalwait\", \"\"], [\"ShowDeleteINV\", \"\"], [\"ShowDeleteINVDetail\", \"\"], [\"Image\", \"\"], [\"imageForm\", \"ngForm\"], [2, \"padding-top\", \"60px\"], [1, \"container-fluid\", 2, \"padding-right\", \"5px\", \"padding-left\", \"5px\"], [1, \"p-sm-1\", \"bg-secondary\", \"text-white\", \"text-center\"], [\"novalidate\", \"\", 1, \"needs-validation\"], [1, \"form-row\"], [\"class\", \"col-md-2 mb-2\", 4, \"ngIf\"], [1, \"col-md-2\", \"mb-2\"], [1, \"custom-select\", \"custom-select-sm\", 3, \"ngModelChange\", \"ngModel\"], [\"value\", \"\", 3, \"selected\"], [\"value\", \"1\", 3, \"selected\"], [\"value\", \"0\", 3, \"selected\"], [2, \"position\", \"relative\", \"margin-top\", \"5px\"], [\"id\", \"typeahead-template\", \"placeholder\", \"\\u0E0A\\u0E37\\u0E48\\u0E2D\\u0E25\\u0E39\\u0E01\\u0E04\\u0E49\\u0E32\", \"type\", \"text\", \"name\", \"model\", 1, \"form-control\", \"form-control-sm\", 3, \"ngModelChange\", \"ngModel\", \"ngbTypeahead\", \"resultTemplate\", \"inputFormatter\"], [2, \"position\", \"absolute\", \"z-index\", \"10\", \"width\", \"15%\", \"height\", \"100%\", \"top\", \"1px\", \"right\", \"1px\", \"font-size\", \"18px\", \"cursor\", \"pointer\", \"text-align\", \"center\", 3, \"click\"], [1, \"col-md-1\", \"mb-1\"], [\"name\", \"\", \"id\", \"\", 1, \"custom-select\", \"custom-select-sm\", 3, \"ngModelChange\", \"change\", \"ngModel\"], [\"value\", \"3\"], [\"value\", \"\\u0E23\\u0E2Dbookbank\"], [\"value\", \"\\u0E23\\u0E2D\\u0E2D\\u0E19\\u0E38\\u0E21\\u0E31\\u0E15\\u0E34\"], [\"value\", \"\\u0E2D\\u0E19\\u0E38\\u0E21\\u0E31\\u0E15\\u0E34\\u0E41\\u0E25\\u0E49\\u0E27\"], [1, \"col-xs-12\", \"col-12\", \"col-md-2\", \"form-group\"], [\"type\", \"text\", \"placeholder\", \"DD/MM/YYYY\", \"bsDatepicker\", \"\", 1, \"form-control\", \"form-control-sm\", 3, \"ngModelChange\", \"bsConfig\", \"ngModel\"], [1, \"col-md-2\", \"mb-3\", \"col-12\", \"text-center\", \"text-sm-center\", \"text-md-center\", \"text-lg-left\", 2, \"padding-right\", \"0px\", \"padding-left\", \"0px\"], [\"type\", \"submit\", 1, \"btn\", \"btn-primary\", \"btn-sm\", \"font-weight-light\", 2, \"width\", \"58px\", \"margin-right\", \"3px\", 3, \"click\", \"disabled\"], [\"style\", \" width: 58px;margin-right: 3px;\", \"class\", \"btn btn-primary btn-sm font-weight-light\", \"type\", \"submit\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [\"style\", \" width: 58px;\", \"class\", \"btn btn-primary btn-sm font-weight-light\", \"type\", \"submit\", 3, \"click\", 4, \"ngIf\"], [1, \"table\", \"table-hover\", \"table-bordered\", \"table-sm\"], [1, \"text-sm-center\", \"bg-light\"], [\"scope\", \"col\", 1, \"text-sm-center\", \"font-weight-normal\"], [\"scope\", \"col\", \"class\", \"text-sm-center\", \"width\", \"60px\", 4, \"ngIf\"], [\"scope\", \"col\", \"width\", \"60px\", 1, \"text-sm-center\"], [\"class\", \"text-sm-left \", 4, \"ngFor\", \"ngForOf\"], [1, \"text-sm-left\"], [\"colspan\", \"4\", 1, \"text-sm-right\", \"font-weight-normal\"], [1, \"text-sm-right\", \"font-weight-normal\"], [1, \"text-sm-center\", \"font-weight-light\"], [\"class\", \"text-sm-center font-weight-light\", 4, \"ngIf\"], [\"id\", \"mdlSample\", \"role\", \"dialog\", 1, \"modal\", \"fade\", 3, \"ngStyle\"], [1, \"modal-dialog\", \"modal-md\"], [1, \"modal-content\"], [1, \"modal-header\", \"colhaederal\"], [1, \"modal-title\"], [1, \"modal-body\"], [\"align\", \"right\", 1, \"modal-footer\"], [\"type\", \"button\", \"id\", \"btnClose\", 1, \"btn\", \"btn-danger\", 3, \"click\"], [1, \"fa\", \"fa-times\", \"fa-fw\"], [\"id\", \"ModalApprove\", \"tabindex\", \"-1\", \"role\", \"dialog\", \"aria-labelledby\", \"exampleModalLabel\", \"aria-hidden\", \"true\", 1, \"modal\", \"fade\"], [\"role\", \"document\", 1, \"modal-dialog\", \"modal-lg\"], [1, \"modal-header\"], [\"id\", \"exampleModalLabel\", 1, \"modal-title\", 2, \"color\", \"red\"], [\"type\", \"button\", \"data-dismiss\", \"modal\", \"aria-label\", \"Close\", 1, \"close\"], [\"aria-hidden\", \"true\"], [1, \"modal-body\", 2, \"padding\", \"5px\"], [1, \"text-right\", \"ngx-pagination\", 2, \"margin-bottom\", \"0px\", 3, \"pageChange\"], [2, \"height\", \"400px\", \"overflow-y\", \"auto\"], [1, \"table\", \"table-hover\", \"table-bordered\", \"table-sm\", 2, \"margin-bottom\", \"0px\"], [\"scope\", \"\", 1, \"text-sm-center\", \"font-weight-normal\"], [\"scope\", \"col\", \"width\", \"30px\", 1, \"text-sm-center\"], [\"type\", \"checkbox\", 3, \"ngModelChange\", \"click\", \"ngModel\"], [4, \"ngFor\", \"ngForOf\"], [\"role\", \"alert\", 1, \"alert\", \"alert-success\", \"text-right\", 2, \"margin-bottom\", \"0px\"], [\"id\", \"collapseExample\", 1, \"collapse\", 2, \"margin-top\", \"5px\"], [1, \"modal-footer\", 2, \"padding\", \"3px\"], [\"role\", \"alert\", 1, \"alert\", \"alert-success\", 2, \"width\", \"200px\", \"top\", \"7px\"], [\"data-toggle\", \"modal\", \"aria-expanded\", \"true\", \"data-target\", \"#Modalremark\", 1, \"btn\", \"btn-primary\"], [\"type\", \"button\", \"class\", \"btn btn-primary\", \"data-dismiss\", \"modal\", \"aria-expanded\", \"false\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [\"type\", \"button\", \"id\", \"btnClose\", \"data-dismiss\", \"modal\", \"aria-expanded\", \"false\", 1, \"btn\", \"btn-danger\"], [\"id\", \"ModalViewIMG\", 1, \"modal\", \"fade\"], [\"ngbTabTitle\", \"\", \"target\", \"_blank\"], [\"ngbTabContent\", \"\"], [\"ngbTabTitle\", \"\", \"target\", \"_blank\", \"href\", \"localhost:4200/assets/img/default-image.png\"], [\"align\", \"right\", 1, \"modal-footer\", 2, \"padding\", \"5px\"], [\"type\", \"button\", \"id\", \"btnClose\", \"data-dismiss\", \"modal\", \"aria-expanded\", \"false\", 1, \"btn\", \"btn-danger\", 3, \"click\"], [\"type\", \"button\", \"id\", \"btnClose\", \"disabled\", \"\", 1, \"btn\", \"btn-danger\"], [\"id\", \"ModalApprovesuccess\", \"tabindex\", \"-1\", \"role\", \"dialog\", \"aria-labelledby\", \"exampleModalLabel\", \"aria-hidden\", \"true\", 1, \"modal\", \"fade\"], [1, \"form-row\", 2, \"margin-bottom\", \"5px\", \"margin-left\", \"10px\", \"margin-right\", \"0px\"], [1, \"col-md-3\"], [\"id\", \"fromdate \", \"type\", \"text\", \"placeholder\", \"\\u0E04\\u0E49\\u0E19\\u0E2B\\u0E32\\u0E40\\u0E25\\u0E02 INV\", 1, \"form-control\", \"form-control-sm\", 3, \"ngModelChange\", \"ngModel\"], [2, \"overflow-y\", \"auto\"], [\"id\", \"example\", \"cellspacing\", \"0\", 1, \"table\", \"table-hover\", \"table-bordered\", \"table-sm\"], [\"align\", \"right\", 1, \"modal-footer\", 2, \"padding\", \"3px\"], [\"role\", \"alert\", 1, \"alert\", \"alert-success\", 2, \"width\", \"400px\", \"top\", \"7px\"], [\"role\", \"alert\", 1, \"alert\", \"alert-success\", 2, \"width\", \"300px\", \"top\", \"7px\"], [\"type\", \"button\", \"id\", \"btnClose\", \"data-dismiss\", \"modal\", 1, \"btn\", \"btn-danger\"], [\"id\", \"ModalApprovesuccessIMG\", \"tabindex\", \"-1\", \"role\", \"dialog\", \"aria-labelledby\", \"exampleModalLabel\", \"aria-hidden\", \"true\", 1, \"modal\", \"fade\"], [\"multiple\", \"\", 1, \"custom-select\", \"custom-select-sm\", 3, \"ngModelChange\", \"ngModel\"], [\"selected\", \"\", \"disabled\", \"\", \"value\", \"\"], [\"value\", \"1\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [3, \"value\"], [3, \"mousedown\"], [\"type\", \"submit\", 1, \"btn\", \"btn-primary\", \"btn-sm\", \"font-weight-light\", 2, \"width\", \"58px\", 3, \"click\"], [1, \"text-sm-center\", \"font-weight-normal\", 3, \"ngStyle\"], [1, \"text-sm-left\", \"font-weight-normal\", 3, \"ngStyle\"], [1, \"text-sm-right\", \"text-right\", \"font-weight-normal\", 3, \"ngStyle\"], [\"class\", \"text-center bg-light\", 4, \"ngIf\"], [1, \"text-center\", \"bg-light\"], [1, \"btn\", \"btn\", \"btn-success\", \"font-weight-light\", 2, \"padding\", \"0pt\", \";width\", \"99%\", 3, \"click\"], [1, \"btn\", \"btn\", \"btn-success\", \"font-weight-light\", 2, \"padding\", \"0pt\", \";width\", \"99%\", 3, \"click\", \"disabled\"], [\"aria-expanded\", \"true\", 1, \"btn\", \"btn\", \"btn-warning\", \"font-weight-light\", 2, \"padding\", \"0pt\", \"width\", \"99%\", 3, \"click\", \"disabled\"], [1, \"text-sm-center\", \"font-weight-normal\", 2, \"width\", \"400px\", 3, \"ngStyle\"], [1, \"text-sm-right\", \"font-weight-normal\", 2, \"text-align\", \"right\", 3, \"ngStyle\"], [1, \"text-center\"], [\"type\", \"checkbox\", 3, \"click\", \"checked\"], [\"data-toggle\", \"modal\", \"aria-expanded\", \"true\", \"data-target\", \"#ModalViewIMG\", \"aria-controls\", \"collapseOne\", 1, \"btn\", \"btn-link\", \"font-weight-light\", 2, \"padding\", \"0pt\", 3, \"click\"], [\"type\", \"button\", \"data-dismiss\", \"modal\", \"aria-expanded\", \"false\", 1, \"btn\", \"btn-primary\", 3, \"click\", \"disabled\"], [2, \"margin\", \"5px\", \"width\", \"99%\", \"height\", \"500px\", 3, \"src\"], [2, \"margin\", \"5px\", \"height\", \"500px\"], [\"scope\", \"col\", 2, \"width\", \"140px\"], [\"scope\", \"col\"], [1, \"text-sm-left\", \"font-weight-normal\"], [1, \"text-sm-center\", \"font-weight-normal\"], [\"data-toggle\", \"modal\", \"aria-expanded\", \"true\", \"data-target\", \"#ModalApprovesuccessIMG\", 1, \"btn\", \"btn-link\", \"font-weight-light\", 2, \"padding\", \"0pt\", 3, \"click\"], [2, \"color\", \"red\"], [1, \"modal-title\", \"pull-left\"], [\"type\", \"button\", \"aria-label\", \"Close\", 1, \"close\", \"pull-right\", 3, \"click\"], [1, \"form-group\", 2, \"margin-bottom\", \"0rem\"], [\"for\", \"exampleFormControlFile1\"], [\"type\", \"file\", \"accept\", \"application/pdf, image/*\", \"id\", \"exampleFormControlFile1\", 1, \"form-control-file\", 3, \"change\"], [1, \"modal-footer\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", 3, \"click\", \"disabled\"], [\"type\", \"button\", 1, \"btn\", \"btn-secondary\", 3, \"click\"], [1, \"modal-body\", \"text-center\"], [\"type\", \"button\", \"class\", \"btn btn-default\", 3, \"click\", 4, \"ngIf\"], [\"type\", \"button\", \"class\", \"btn btn-primary\", 3, \"click\", 4, \"ngIf\"], [\"type\", \"button\", 1, \"btn\", \"btn-default\", 3, \"click\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", 3, \"click\"], [1, \"modal-body\", 2, \"padding\", \"3px\"], [4, \"ngIf\"], [2, \"margin\", \"5px\", \"width\", \"99%\", 3, \"src\"], [1, \"col-md-3\", \"mb-3\", \"col-sm-6\"], [\"value\", \"A\"], [\"value\", \"B\"], [1, \"custom-file\", \"col-md-9\", 2, \"margin-bottom\", \"10px\"], [\"type\", \"file\", \"accept\", \"image/*\", \"id\", \"inputGroupFile01\", \"aria-describedby\", \"inputGroupFileAddon01\", 1, \"custom-file-input\", 3, \"ngModelChange\", \"change\", \"ngModel\"], [\"for\", \"inputGroupFile01\", 1, \"custom-file-label\", 3, \"ngModelChange\", \"ngModel\"], [\"id\", \"collapseEvent\", 1, \"card\", \"card-block\", \"card-header\", 3, \"collapsed\", \"expanded\", \"collapse\"], [1, \"card\", \"card-body\"], [2, \"text-align\", \"center\"], [2, \"width\", \"100%\", 3, \"src\"], [\"type\", \"button\", \"aria-controls\", \"collapseEvent\", 1, \"btn\", \"btn-primary\", 3, \"click\", \"disabled\"], [\"aria-controls\", \"collapseOne\", 1, \"btn\", \"btn-link\", \"font-weight-light\", 2, \"padding\", \"0pt\", 3, \"click\"], [1, \"modal-footer\", 2, \"padding\", \"5px\"], [1, \"btn\", \"btn-primary\", 3, \"click\"], [\"type\", \"button\", \"class\", \"btn btn-primary\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [1, \"card\", \"card-body\", 2, \"padding\", \"5px\"], [1, \"form-group\"], [\"for\", \"comment\"], [\"rows\", \"5\", \"id\", \"comment\", 1, \"form-control\", 3, \"ngModelChange\", \"ngModel\"], [\"id\", \"fromdate \", \"type\", \"text\", \"placeholder\", \"\\u0E04\\u0E49\\u0E19\\u0E2B\\u0E32\\u0E40\\u0E25\\u0E02 INV\", 1, \"form-control\", \"form-control-sm\", 3, \"ngModelChange\", \"change\", \"keyup\", \"ngModel\"], [\"nowrap\", \"\", \"scope\", \"col\", 1, \"text-sm-center\", \"font-weight-normal\"], [\"nowrap\", \"\", 1, \"text-sm-center\", \"font-weight-normal\", 3, \"ngStyle\"], [1, \"btn\", \"btn-link\", \"font-weight-light\", 2, \"padding\", \"0pt\", 3, \"click\"], [1, \"modal-body\", \"text-lg-left\"], [\"type\", \"button\", \"style\", \"margin-bottom : 10px;\", \"class\", \"btn btn-default\", 3, \"click\", 4, \"ngIf\"], [\"type\", \"button\", \"style\", \"float:left;\", \"class\", \"btn btn-default\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [\"type\", \"button\", 1, \"btn\", \"btn-default\", 2, \"float\", \"right\", \"margin-left\", \"3px\", 3, \"click\"], [\"type\", \"button\", \"style\", \"float:right;\", \"class\", \"btn btn-default\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [\"type\", \"button\", 1, \"btn\", \"btn-default\", 2, \"margin-bottom\", \"10px\", 3, \"click\"], [\"type\", \"button\", 1, \"btn\", \"btn-default\", 2, \"float\", \"left\", 3, \"click\", \"disabled\"], [\"type\", \"button\", 1, \"btn\", \"btn-default\", 2, \"float\", \"right\", 3, \"click\", \"disabled\"], [\"scope\", \"col\", 1, \"font-weight-light\"], [1, \"text-sm-left\", \"font-weight-light\"]],\n        template: function CompleteinvoiceComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            const _r1 = i0.ɵɵgetCurrentView();\n            i0.ɵɵelement(0, \"app-topmenu\");\n            i0.ɵɵelementStart(1, \"section\", 14)(2, \"div\", 15)(3, \"h5\", 16);\n            i0.ɵɵtext(4, \"Completed Invoice List\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(5, \"div\", 17)(6, \"div\", 18);\n            i0.ɵɵtemplate(7, CompleteinvoiceComponent_div_7_Template, 7, 2, \"div\", 19);\n            i0.ɵɵelementStart(8, \"div\", 20)(9, \"select\", 21);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function CompleteinvoiceComponent_Template_select_ngModelChange_9_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.keyboardStrApprove, $event) || (ctx.keyboardStrApprove = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementStart(10, \"option\", 22);\n            i0.ɵɵtext(11, \"\\u0E2A\\u0E16\\u0E32\\u0E19\\u0E30 Approve\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(12, \"option\", 23);\n            i0.ɵɵtext(13, \"Approval \\u0E41\\u0E25\\u0E49\\u0E27\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(14, \"option\", 24);\n            i0.ɵɵtext(15, \"\\u0E22\\u0E31\\u0E07\\u0E44\\u0E21\\u0E48\\u0E44\\u0E14\\u0E49 Approve\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(16, \"div\", 25);\n            i0.ɵɵtemplate(17, CompleteinvoiceComponent_ng_template_17_Template, 3, 2, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n            i0.ɵɵelementStart(19, \"input\", 26);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function CompleteinvoiceComponent_Template_input_ngModelChange_19_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.Customer, $event) || (ctx.Customer = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(20, \"div\", 27);\n            i0.ɵɵlistener(\"click\", function CompleteinvoiceComponent_Template_div_click_20_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.cancel());\n            });\n            i0.ɵɵtext(21, \"x\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(22, \"div\", 28)(23, \"select\", 21);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function CompleteinvoiceComponent_Template_select_ngModelChange_23_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.keyboardStrpaymenttype, $event) || (ctx.keyboardStrpaymenttype = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementStart(24, \"option\", 22);\n            i0.ɵɵtext(25, \"\\u0E40\\u0E07\\u0E37\\u0E48\\u0E2D\\u0E19\\u0E44\\u0E02\\u0E0A\\u0E33\\u0E23\\u0E30\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(26, \"option\", 24);\n            i0.ɵɵtext(27, \"\\u0E40\\u0E07\\u0E34\\u0E19\\u0E2A\\u0E14\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(28, \"option\", 23);\n            i0.ɵɵtext(29, \"\\u0E40\\u0E04\\u0E23\\u0E14\\u0E34\\u0E15\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(30, \"div\", 28)(31, \"select\", 29);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function CompleteinvoiceComponent_Template_select_ngModelChange_31_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.printst, $event) || (ctx.printst = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵlistener(\"change\", function CompleteinvoiceComponent_Template_select_change_31_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.searchselectprint());\n            });\n            i0.ɵɵelementStart(32, \"option\", 30);\n            i0.ɵɵtext(33, \"\\u0E40\\u0E25\\u0E37\\u0E2D\\u0E01\\u0E17\\u0E38\\u0E38\\u0E01\\u0E23\\u0E32\\u0E22\\u0E01\\u0E32\\u0E23\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(34, \"option\", 31);\n            i0.ɵɵtext(35, \"\\u0E23\\u0E2Dbookbank\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(36, \"option\", 32);\n            i0.ɵɵtext(37, \"\\u0E23\\u0E2D\\u0E2D\\u0E19\\u0E38\\u0E21\\u0E31\\u0E15\\u0E34\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(38, \"option\", 33);\n            i0.ɵɵtext(39, \"approve \\u0E41\\u0E25\\u0E49\\u0E27\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(40, \"div\", 34)(41, \"input\", 35);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function CompleteinvoiceComponent_Template_input_ngModelChange_41_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.Datafromdate, $event) || (ctx.Datafromdate = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(42, \"div\", 34)(43, \"input\", 35);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function CompleteinvoiceComponent_Template_input_ngModelChange_43_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.Datatodate, $event) || (ctx.Datatodate = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(44, \"div\", 36)(45, \"button\", 37);\n            i0.ɵɵlistener(\"click\", function CompleteinvoiceComponent_Template_button_click_45_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.SearchComplete());\n            });\n            i0.ɵɵtext(46, \"Search\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(47, CompleteinvoiceComponent_button_47_Template, 2, 1, \"button\", 38);\n            i0.ɵɵelementStart(48, \"button\", 37);\n            i0.ɵɵlistener(\"click\", function CompleteinvoiceComponent_Template_button_click_48_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.newGetExport(ctx.seachheaderlist, ctx.productprice, ctx.sumvat, ctx.sumprice));\n            });\n            i0.ɵɵtext(49, \"Export\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(50, CompleteinvoiceComponent_button_50_Template, 2, 0, \"button\", 39);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(51, \"table\", 40)(52, \"thead\")(53, \"tr\", 41);\n            i0.ɵɵelement(54, \"th\", 42);\n            i0.ɵɵelementStart(55, \"th\", 42);\n            i0.ɵɵtext(56, \"\\u0E1E\\u0E19\\u0E31\\u0E01\\u0E07\\u0E32\\u0E19\\u0E02\\u0E32\\u0E22\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(57, \"th\", 42);\n            i0.ɵɵtext(58, \"\\u0E0A\\u0E37\\u0E48\\u0E2D\\u0E25\\u0E39\\u0E01\\u0E04\\u0E49\\u0E32\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(59, \"th\", 42);\n            i0.ɵɵtext(60, \"Bill No\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(61, \"th\", 42);\n            i0.ɵɵtext(62, \"\\u0E21\\u0E39\\u0E25\\u0E04\\u0E48\\u0E32\\u0E2A\\u0E34\\u0E19\\u0E04\\u0E49\\u0E32\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(63, \"th\", 42);\n            i0.ɵɵtext(64, \"VAT\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(65, \"th\", 42);\n            i0.ɵɵtext(66, \"\\u0E21\\u0E39\\u0E25\\u0E04\\u0E48\\u0E32\\u0E23\\u0E27\\u0E21\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(67, CompleteinvoiceComponent_th_67_Template, 1, 0, \"th\", 43)(68, CompleteinvoiceComponent_th_68_Template, 1, 0, \"th\", 43)(69, CompleteinvoiceComponent_th_69_Template, 1, 0, \"th\", 43);\n            i0.ɵɵelement(70, \"th\", 44);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(71, \"tbody\");\n            i0.ɵɵtemplate(72, CompleteinvoiceComponent_tr_72_Template, 26, 41, \"tr\", 45);\n            i0.ɵɵelementStart(73, \"tr\", 46)(74, \"td\", 47);\n            i0.ɵɵtext(75, \"\\u0E21\\u0E39\\u0E25\\u0E04\\u0E48\\u0E32\\u0E23\\u0E27\\u0E21\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(76, \"td\", 48);\n            i0.ɵɵtext(77);\n            i0.ɵɵpipe(78, \"number\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(79, \"td\", 48);\n            i0.ɵɵtext(80);\n            i0.ɵɵpipe(81, \"number\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(82, \"td\", 48);\n            i0.ɵɵtext(83);\n            i0.ɵɵpipe(84, \"number\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(85, \"th\", 49);\n            i0.ɵɵtemplate(86, CompleteinvoiceComponent_th_86_Template, 1, 0, \"th\", 50)(87, CompleteinvoiceComponent_th_87_Template, 1, 0, \"th\", 50)(88, CompleteinvoiceComponent_th_88_Template, 1, 0, \"th\", 50);\n            i0.ɵɵelementEnd()()()()();\n            i0.ɵɵelementStart(89, \"div\", 51)(90, \"div\", 52)(91, \"div\", 53)(92, \"div\", 54)(93, \"h4\", 55);\n            i0.ɵɵtext(94, \"Report\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(95, \"div\", 56);\n            i0.ɵɵtext(96);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(97, \"div\", 57)(98, \"button\", 58);\n            i0.ɵɵlistener(\"click\", function CompleteinvoiceComponent_Template_button_click_98_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.closemodel(false));\n            });\n            i0.ɵɵelement(99, \"i\", 59);\n            i0.ɵɵtext(100, \" \\u0E1B\\u0E34\\u0E14\");\n            i0.ɵɵelementEnd()()()()();\n            i0.ɵɵelementStart(101, \"div\", 60)(102, \"div\", 61)(103, \"div\", 53)(104, \"div\", 62)(105, \"h5\", 63);\n            i0.ɵɵtext(106);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(107, \"button\", 64)(108, \"span\", 65);\n            i0.ɵɵtext(109, \"\\u00D7\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(110, \"div\", 66)(111, \"pagination-controls\", 67);\n            i0.ɵɵlistener(\"pageChange\", function CompleteinvoiceComponent_Template_pagination_controls_pageChange_111_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.p = $event);\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(112, \"div\", 68)(113, \"table\", 69)(114, \"thead\")(115, \"tr\", 41);\n            i0.ɵɵelement(116, \"th\", 42);\n            i0.ɵɵelementStart(117, \"th\", 42);\n            i0.ɵɵtext(118, \"SO No\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(119, \"th\", 42);\n            i0.ɵɵtext(120, \"Invoice No\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(121, \"th\", 42);\n            i0.ɵɵtext(122, \"Invoice Date\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(123, \"th\", 42);\n            i0.ɵɵtext(124, \"Due Date\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(125, \"th\", 70);\n            i0.ɵɵtext(126, \"\\u0E0A\\u0E33\\u0E23\\u0E30\\u0E40\\u0E07\\u0E34\\u0E19\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(127, \"th\", 70);\n            i0.ɵɵtext(128, \"\\u0E21\\u0E39\\u0E25\\u0E04\\u0E48\\u0E32\\u0E2A\\u0E34\\u0E19\\u0E04\\u0E49\\u0E32\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(129, \"th\", 42);\n            i0.ɵɵtext(130, \"VAT\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(131, \"th\", 70);\n            i0.ɵɵtext(132, \"\\u0E21\\u0E39\\u0E25\\u0E04\\u0E48\\u0E32\\u0E23\\u0E27\\u0E21\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(133, \"th\", 71)(134, \"input\", 72);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function CompleteinvoiceComponent_Template_input_ngModelChange_134_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.testcheck, $event) || (ctx.testcheck = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵlistener(\"click\", function CompleteinvoiceComponent_Template_input_click_134_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.selectAll($event.target.checked));\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelement(135, \"td\", 49);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(136, \"tbody\");\n            i0.ɵɵtemplate(137, CompleteinvoiceComponent_tr_137_Template, 29, 53, \"tr\", 73);\n            i0.ɵɵpipe(138, \"paginate\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(139, \"div\")(140, \"div\", 74);\n            i0.ɵɵtext(141);\n            i0.ɵɵpipe(142, \"number\");\n            i0.ɵɵpipe(143, \"number\");\n            i0.ɵɵpipe(144, \"number\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelement(145, \"div\", 75);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(146, \"div\", 76)(147, \"div\", 77);\n            i0.ɵɵtext(148);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(149, \"button\", 78);\n            i0.ɵɵtext(150, \" Comment \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(151, CompleteinvoiceComponent_button_151_Template, 2, 1, \"button\", 79)(152, CompleteinvoiceComponent_button_152_Template, 2, 1, \"button\", 79);\n            i0.ɵɵelementStart(153, \"button\", 80);\n            i0.ɵɵelement(154, \"i\", 59);\n            i0.ɵɵtext(155, \"Close\");\n            i0.ɵɵelementEnd()()()()();\n            i0.ɵɵelementStart(156, \"div\", 81)(157, \"div\", 61)(158, \"div\", 53)(159, \"div\", 66);\n            i0.ɵɵtext(160);\n            i0.ɵɵelementStart(161, \"ngb-tabset\")(162, \"ngb-tab\");\n            i0.ɵɵtemplate(163, CompleteinvoiceComponent_ng_template_163_Template, 2, 0, \"ng-template\", 82)(164, CompleteinvoiceComponent_ng_template_164_Template, 1, 1, \"ng-template\", 83);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(165, \"ngb-tab\");\n            i0.ɵɵtemplate(166, CompleteinvoiceComponent_ng_template_166_Template, 2, 0, \"ng-template\", 82)(167, CompleteinvoiceComponent_ng_template_167_Template, 1, 1, \"ng-template\", 83);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(168, \"ngb-tab\");\n            i0.ɵɵtemplate(169, CompleteinvoiceComponent_ng_template_169_Template, 2, 0, \"ng-template\", 84)(170, CompleteinvoiceComponent_ng_template_170_Template, 73, 27, \"ng-template\", 83);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(171, \"div\", 85)(172, \"button\", 86);\n            i0.ɵɵlistener(\"click\", function CompleteinvoiceComponent_Template_button_click_172_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.closemodelBill(false));\n            });\n            i0.ɵɵelement(173, \"i\", 59);\n            i0.ɵɵtext(174, \" \\u0E1B\\u0E34\\u0E14\");\n            i0.ɵɵelementEnd()()()()();\n            i0.ɵɵelementStart(175, \"div\", 51)(176, \"div\", 52)(177, \"div\", 53)(178, \"div\", 54)(179, \"h4\", 55);\n            i0.ɵɵtext(180, \"Report\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(181, \"div\", 56);\n            i0.ɵɵtext(182);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(183, \"div\", 57)(184, \"button\", 58);\n            i0.ɵɵlistener(\"click\", function CompleteinvoiceComponent_Template_button_click_184_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.closemodel(false));\n            });\n            i0.ɵɵelement(185, \"i\", 59);\n            i0.ɵɵtext(186, \" \\u0E1B\\u0E34\\u0E14\");\n            i0.ɵɵelementEnd()()()()();\n            i0.ɵɵelementStart(187, \"div\", 51)(188, \"div\", 52)(189, \"div\", 53)(190, \"div\", 54)(191, \"h4\", 55);\n            i0.ɵɵtext(192, \"Report\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(193, \"div\", 56);\n            i0.ɵɵtext(194);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(195, \"div\", 57)(196, \"button\", 58);\n            i0.ɵɵlistener(\"click\", function CompleteinvoiceComponent_Template_button_click_196_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.closemodel2(false));\n            });\n            i0.ɵɵelement(197, \"i\", 59);\n            i0.ɵɵtext(198, \" \\u0E1B\\u0E34\\u0E14\");\n            i0.ɵɵelementEnd()()()()();\n            i0.ɵɵelementStart(199, \"div\", 51)(200, \"div\", 52)(201, \"div\", 53)(202, \"div\", 54)(203, \"h4\", 55);\n            i0.ɵɵtext(204, \"Report\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(205, \"div\", 56);\n            i0.ɵɵtext(206);\n            i0.ɵɵelement(207, \"br\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(208, \"div\", 57)(209, \"button\", 87);\n            i0.ɵɵelement(210, \"i\", 59);\n            i0.ɵɵtext(211, \" \\u0E1B\\u0E34\\u0E14\");\n            i0.ɵɵelementEnd()()()()();\n            i0.ɵɵelementStart(212, \"div\", 88)(213, \"div\", 61)(214, \"div\", 53)(215, \"div\", 62)(216, \"h5\", 63);\n            i0.ɵɵtext(217);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(218, \"button\", 64)(219, \"span\", 65);\n            i0.ɵɵtext(220, \"\\u00D7\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(221, \"div\", 66)(222, \"div\", 89)(223, \"div\", 90)(224, \"input\", 91);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function CompleteinvoiceComponent_Template_input_ngModelChange_224_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.dateinvid, $event) || (ctx.dateinvid = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(225, \"pagination-controls\", 67);\n            i0.ɵɵlistener(\"pageChange\", function CompleteinvoiceComponent_Template_pagination_controls_pageChange_225_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.p = $event);\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(226, \"div\", 92)(227, \"table\", 93)(228, \"thead\")(229, \"tr\");\n            i0.ɵɵelement(230, \"th\", 42);\n            i0.ɵɵelementStart(231, \"th\", 42);\n            i0.ɵɵtext(232, \"SO No\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(233, \"th\", 42);\n            i0.ɵɵtext(234, \"Invoice No\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(235, \"th\", 42);\n            i0.ɵɵtext(236, \"Invoice Date\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(237, \"th\", 42);\n            i0.ɵɵtext(238, \"Due Date\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(239, \"th\", 42);\n            i0.ɵɵtext(240, \"\\u0E21\\u0E39\\u0E25\\u0E04\\u0E48\\u0E32\\u0E2A\\u0E34\\u0E19\\u0E04\\u0E49\\u0E32\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(241, \"th\", 42);\n            i0.ɵɵtext(242, \"VAT\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(243, \"th\", 42);\n            i0.ɵɵtext(244, \"\\u0E21\\u0E39\\u0E25\\u0E04\\u0E48\\u0E32\\u0E23\\u0E27\\u0E21\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(245, \"th\", 42);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(246, \"tbody\");\n            i0.ɵɵtemplate(247, CompleteinvoiceComponent_tr_247_Template, 25, 48, \"tr\", 73);\n            i0.ɵɵpipe(248, \"paginate\");\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(249, \"div\", 94)(250, \"div\", 95);\n            i0.ɵɵtext(251);\n            i0.ɵɵpipe(252, \"number\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(253, \"div\", 96);\n            i0.ɵɵtext(254);\n            i0.ɵɵpipe(255, \"number\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(256, \"div\", 95);\n            i0.ɵɵtext(257);\n            i0.ɵɵpipe(258, \"number\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(259, \"button\", 97);\n            i0.ɵɵelement(260, \"i\", 59);\n            i0.ɵɵtext(261, \" \\u0E1B\\u0E34\\u0E14\");\n            i0.ɵɵelementEnd()()()()();\n            i0.ɵɵelementStart(262, \"div\", 98)(263, \"div\", 61)(264, \"div\", 53)(265, \"div\", 66)(266, \"ngb-tabset\")(267, \"ngb-tab\");\n            i0.ɵɵtemplate(268, CompleteinvoiceComponent_ng_template_268_Template, 2, 1, \"ng-template\", 84)(269, CompleteinvoiceComponent_ng_template_269_Template, 1, 1, \"ng-template\", 83);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(270, \"ngb-tab\");\n            i0.ɵɵtemplate(271, CompleteinvoiceComponent_ng_template_271_Template, 2, 0, \"ng-template\", 84)(272, CompleteinvoiceComponent_ng_template_272_Template, 73, 27, \"ng-template\", 83);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(273, \"div\", 94)(274, \"button\", 97);\n            i0.ɵɵelement(275, \"i\", 59);\n            i0.ɵɵtext(276, \" \\u0E1B\\u0E34\\u0E14\");\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵtemplate(277, CompleteinvoiceComponent_ng_template_277_Template, 16, 2, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor)(279, CompleteinvoiceComponent_ng_template_279_Template, 7, 4, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor)(281, CompleteinvoiceComponent_ng_template_281_Template, 12, 1, \"ng-template\", null, 3, i0.ɵɵtemplateRefExtractor)(283, CompleteinvoiceComponent_ng_template_283_Template, 14, 0, \"ng-template\", null, 4, i0.ɵɵtemplateRefExtractor)(285, CompleteinvoiceComponent_ng_template_285_Template, 64, 24, \"ng-template\", null, 5, i0.ɵɵtemplateRefExtractor)(287, CompleteinvoiceComponent_ng_template_287_Template, 49, 23, \"ng-template\", null, 6, i0.ɵɵtemplateRefExtractor)(289, CompleteinvoiceComponent_ng_template_289_Template, 9, 4, \"ng-template\", null, 7, i0.ɵɵtemplateRefExtractor)(291, CompleteinvoiceComponent_ng_template_291_Template, 37, 8, \"ng-template\", null, 8, i0.ɵɵtemplateRefExtractor)(293, CompleteinvoiceComponent_ng_template_293_Template, 45, 19, \"ng-template\", null, 9, i0.ɵɵtemplateRefExtractor)(295, CompleteinvoiceComponent_ng_template_295_Template, 16, 14, \"ng-template\", null, 10, i0.ɵɵtemplateRefExtractor)(297, CompleteinvoiceComponent_ng_template_297_Template, 18, 1, \"ng-template\", null, 11, i0.ɵɵtemplateRefExtractor);\n            i0.ɵɵelementEnd();\n          }\n          if (rf & 2) {\n            const rt_r63 = i0.ɵɵreference(18);\n            i0.ɵɵadvance(7);\n            i0.ɵɵproperty(\"ngIf\", ctx.testclose);\n            i0.ɵɵadvance(2);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.keyboardStrApprove);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"selected\", ctx.keyboardStrApprove == \"\\u0E2A\\u0E16\\u0E32\\u0E19\\u0E30 Approve\");\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"selected\", ctx.keyboardStrApprove == \"Approval \\u0E41\\u0E25\\u0E49\\u0E27\");\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"selected\", ctx.keyboardStrApprove == \"\\u0E22\\u0E31\\u0E07\\u0E44\\u0E21\\u0E48\\u0E44\\u0E14\\u0E49 Approve\");\n            i0.ɵɵadvance(5);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.Customer);\n            i0.ɵɵproperty(\"ngbTypeahead\", ctx.search)(\"resultTemplate\", rt_r63)(\"inputFormatter\", ctx.formatter);\n            i0.ɵɵadvance(4);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.keyboardStrpaymenttype);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"selected\", ctx.keyboardStrpaymenttype == \"\\u0E40\\u0E07\\u0E37\\u0E48\\u0E2D\\u0E19\\u0E44\\u0E02\\u0E0A\\u0E33\\u0E23\\u0E30\");\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"selected\", ctx.keyboardStrpaymenttype == \"\\u0E40\\u0E07\\u0E34\\u0E19\\u0E2A\\u0E14\");\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"selected\", ctx.keyboardStrpaymenttype == \"\\u0E40\\u0E04\\u0E23\\u0E14\\u0E34\\u0E15\");\n            i0.ɵɵadvance(3);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.printst);\n            i0.ɵɵadvance(10);\n            i0.ɵɵproperty(\"bsConfig\", i0.ɵɵpureFunction0(90, _c0));\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.Datafromdate);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"bsConfig\", i0.ɵɵpureFunction0(91, _c0));\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.Datatodate);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"disabled\", ctx.searchbtn);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngIf\", ctx.testclose);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"disabled\", ctx.dataExport === \"\" || ctx.exportbtn);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngIf\", ctx.datalogin[0].salegroup == \"admin\" && (ctx.datalogin[0].id_group_user == \"1860Administrator\" || ctx.datalogin[0].id_group_user == \"153Admin\"));\n            i0.ɵɵadvance(17);\n            i0.ɵɵproperty(\"ngIf\", !ctx.searchbtn);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.testclose);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", !ctx.testclose);\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"ngForOf\", ctx.seachheaderlist);\n            i0.ɵɵadvance(5);\n            i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(78, 57, ctx.productprice, \"1.2-2\"));\n            i0.ɵɵadvance(3);\n            i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(81, 60, ctx.sumvat, \"1.2-2\"));\n            i0.ɵɵadvance(3);\n            i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(84, 63, ctx.sumprice, \"1.2-2\"));\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"ngIf\", !ctx.searchbtn);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.testclose);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", !ctx.testclose);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(92, _c1, ctx.mdlSampleIsOpen ? \"block\" : \"none\"));\n            i0.ɵɵadvance(7);\n            i0.ɵɵtextInterpolate(ctx.alt);\n            i0.ɵɵadvance(10);\n            i0.ɵɵtextInterpolate1(\"BillNo : \", ctx.showbillno, \" \");\n            i0.ɵɵadvance(28);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.testcheck);\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind2(138, 66, ctx.BillId, i0.ɵɵpureFunction1(94, _c2, ctx.p)));\n            i0.ɵɵadvance(4);\n            i0.ɵɵtextInterpolate3(\" \\u0E21\\u0E39\\u0E25\\u0E04\\u0E48\\u0E32\\u0E23\\u0E27\\u0E21 : \", i0.ɵɵpipeBind2(142, 69, ctx.trueproductprice, \"1.2-2\"), \" | Vat : \", i0.ɵɵpipeBind2(143, 72, ctx.truesumvat, \"1.2-2\"), \" | \\u0E23\\u0E32\\u0E04\\u0E32\\u0E2A\\u0E38\\u0E17\\u0E18\\u0E34 : \", i0.ɵɵpipeBind2(144, 75, ctx.truesumprice, \"1.2-2\"), \" \");\n            i0.ɵɵadvance(7);\n            i0.ɵɵtextInterpolate1(\" \\u0E23\\u0E32\\u0E22\\u0E17\\u0E35\\u0E48\\u0E40\\u0E25\\u0E37\\u0E2D\\u0E01\\u0E17\\u0E31\\u0E49\\u0E07\\u0E2B\\u0E21\\u0E14 : \", ctx.total, \" \");\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"ngIf\", ctx.SearchComplete_);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.SearchCompleteED_);\n            i0.ɵɵadvance(8);\n            i0.ɵɵtextInterpolate1(\"\", ctx.altBill, \" \");\n            i0.ɵɵadvance(15);\n            i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(96, _c1, ctx.mdlSampleIsOpen ? \"block\" : \"none\"));\n            i0.ɵɵadvance(7);\n            i0.ɵɵtextInterpolate(ctx.alt);\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(98, _c1, ctx.mdlSampleIsOpen2 ? \"block\" : \"none\"));\n            i0.ɵɵadvance(7);\n            i0.ɵɵtextInterpolate(ctx.alt2);\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(100, _c1, ctx.mdlSampleIsOpen3 ? \"block\" : \"none\"));\n            i0.ɵɵadvance(7);\n            i0.ɵɵtextInterpolate2(\"\", ctx.alt3, \" \", ctx.load, \" \");\n            i0.ɵɵadvance(11);\n            i0.ɵɵtextInterpolate1(\"ApproveSuccess : \", ctx.showbillno, \" \");\n            i0.ɵɵadvance(7);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.dateinvid);\n            i0.ɵɵadvance(23);\n            i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind2(248, 78, ctx.BillId, i0.ɵɵpureFunction1(102, _c2, ctx.p)));\n            i0.ɵɵadvance(4);\n            i0.ɵɵtextInterpolate1(\" \\u0E21\\u0E39\\u0E25\\u0E04\\u0E48\\u0E32\\u0E2A\\u0E34\\u0E19\\u0E04\\u0E49\\u0E32 : \", i0.ɵɵpipeBind2(252, 81, ctx.productpricesuccess, \"1.2-2\"), \" \");\n            i0.ɵɵadvance(3);\n            i0.ɵɵtextInterpolate1(\" \\u0E21\\u0E39\\u0E25\\u0E04\\u0E48\\u0E32\\u0E20\\u0E32\\u0E29\\u0E35 : \", i0.ɵɵpipeBind2(255, 84, ctx.sumvatsuccess, \"1.2-2\"), \" \");\n            i0.ɵɵadvance(3);\n            i0.ɵɵtextInterpolate1(\" \\u0E21\\u0E39\\u0E25\\u0E04\\u0E48\\u0E32\\u0E23\\u0E27\\u0E21 : \", i0.ɵɵpipeBind2(258, 87, ctx.sumpricesuccess, \"1.2-2\"), \" \");\n          }\n        },\n        styles: [\".ngx-pagination[_ngcontent-%COMP%]{margin-bottom:0}.top-170[_ngcontent-%COMP%]{top:170px}\"]\n      });\n    }\n  }\n  return CompleteinvoiceComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}