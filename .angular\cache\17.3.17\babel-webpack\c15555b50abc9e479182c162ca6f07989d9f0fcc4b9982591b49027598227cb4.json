{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.skipWhile = void 0;\nvar lift_1 = require(\"../util/lift\");\nvar OperatorSubscriber_1 = require(\"./OperatorSubscriber\");\nfunction skipWhile(predicate) {\n  return lift_1.operate(function (source, subscriber) {\n    var taking = false;\n    var index = 0;\n    source.subscribe(OperatorSubscriber_1.createOperatorSubscriber(subscriber, function (value) {\n      return (taking || (taking = !predicate(value, index++))) && subscriber.next(value);\n    }));\n  });\n}\nexports.skipWhile = skipWhile;\n//# sourceMappingURL=skipWhile.js.map", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}