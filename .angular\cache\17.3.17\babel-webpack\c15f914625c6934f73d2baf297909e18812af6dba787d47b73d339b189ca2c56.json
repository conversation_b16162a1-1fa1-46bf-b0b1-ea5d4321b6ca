{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.lastValueFrom = void 0;\nvar EmptyError_1 = require(\"./util/EmptyError\");\nfunction lastValueFrom(source, config) {\n  var hasConfig = typeof config === 'object';\n  return new Promise(function (resolve, reject) {\n    var _hasValue = false;\n    var _value;\n    source.subscribe({\n      next: function (value) {\n        _value = value;\n        _hasValue = true;\n      },\n      error: reject,\n      complete: function () {\n        if (_hasValue) {\n          resolve(_value);\n        } else if (hasConfig) {\n          resolve(config.defaultValue);\n        } else {\n          reject(new EmptyError_1.EmptyError());\n        }\n      }\n    });\n  });\n}\nexports.lastValueFrom = lastValueFrom;\n//# sourceMappingURL=lastValueFrom.js.map", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}