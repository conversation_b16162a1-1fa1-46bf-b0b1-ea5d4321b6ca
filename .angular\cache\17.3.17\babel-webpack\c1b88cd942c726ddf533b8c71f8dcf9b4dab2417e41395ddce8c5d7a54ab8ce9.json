{"ast": null, "code": "\"use strict\";\n\nvar __values = this && this.__values || function (o) {\n  var s = typeof Symbol === \"function\" && Symbol.iterator,\n    m = s && o[s],\n    i = 0;\n  if (m) return m.call(o);\n  if (o && typeof o.length === \"number\") return {\n    next: function () {\n      if (o && i >= o.length) o = void 0;\n      return {\n        value: o && o[i++],\n        done: !o\n      };\n    }\n  };\n  throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.takeLast = void 0;\nvar empty_1 = require(\"../observable/empty\");\nvar lift_1 = require(\"../util/lift\");\nvar OperatorSubscriber_1 = require(\"./OperatorSubscriber\");\nfunction takeLast(count) {\n  return count <= 0 ? function () {\n    return empty_1.EMPTY;\n  } : lift_1.operate(function (source, subscriber) {\n    var buffer = [];\n    source.subscribe(OperatorSubscriber_1.createOperatorSubscriber(subscriber, function (value) {\n      buffer.push(value);\n      count < buffer.length && buffer.shift();\n    }, function () {\n      var e_1, _a;\n      try {\n        for (var buffer_1 = __values(buffer), buffer_1_1 = buffer_1.next(); !buffer_1_1.done; buffer_1_1 = buffer_1.next()) {\n          var value = buffer_1_1.value;\n          subscriber.next(value);\n        }\n      } catch (e_1_1) {\n        e_1 = {\n          error: e_1_1\n        };\n      } finally {\n        try {\n          if (buffer_1_1 && !buffer_1_1.done && (_a = buffer_1.return)) _a.call(buffer_1);\n        } finally {\n          if (e_1) throw e_1.error;\n        }\n      }\n      subscriber.complete();\n    }, undefined, function () {\n      buffer = null;\n    }));\n  });\n}\nexports.takeLast = takeLast;\n//# sourceMappingURL=takeLast.js.map", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}