{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.finalize = void 0;\nvar lift_1 = require(\"../util/lift\");\nfunction finalize(callback) {\n  return lift_1.operate(function (source, subscriber) {\n    try {\n      source.subscribe(subscriber);\n    } finally {\n      subscriber.add(callback);\n    }\n  });\n}\nexports.finalize = finalize;\n//# sourceMappingURL=finalize.js.map", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}