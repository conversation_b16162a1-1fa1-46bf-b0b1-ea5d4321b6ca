{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.max = void 0;\nvar reduce_1 = require(\"./reduce\");\nvar isFunction_1 = require(\"../util/isFunction\");\nfunction max(comparer) {\n  return reduce_1.reduce(isFunction_1.isFunction(comparer) ? function (x, y) {\n    return comparer(x, y) > 0 ? x : y;\n  } : function (x, y) {\n    return x > y ? x : y;\n  });\n}\nexports.max = max;\n//# sourceMappingURL=max.js.map", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}