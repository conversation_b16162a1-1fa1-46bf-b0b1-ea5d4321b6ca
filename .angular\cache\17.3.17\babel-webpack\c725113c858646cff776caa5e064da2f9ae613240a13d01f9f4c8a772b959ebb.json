{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Bambara [bm]\n//! author : <PERSON><PERSON><PERSON> Comment : https://github.com/estellecomment\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  var bm = moment.defineLocale('bm', {\n    months: 'Zanwuyekalo_Fewuruyekalo_Marisikalo_Awirilikalo_Mɛkalo_Zuwɛnkalo_Zuluyekalo_Utikalo_Sɛtanburukalo_ɔkutɔburukalo_Nowanburukalo_Desanburukalo'.split('_'),\n    monthsShort: 'Zan_Few_Mar_Awi_Mɛ_Zuw_Zul_Uti_Sɛt_ɔku_Now_Des'.split('_'),\n    weekdays: 'Kari_Ntɛnɛn_Tarata_Araba_Alamisa_Juma_Sibiri'.split('_'),\n    weekdaysShort: 'Kar_Ntɛ_Tar_Ara_Ala_Jum_Sib'.split('_'),\n    weekdaysMin: 'Ka_Nt_Ta_Ar_Al_Ju_Si'.split('_'),\n    longDateFormat: {\n      LT: 'HH:mm',\n      LTS: 'HH:mm:ss',\n      L: 'DD/MM/YYYY',\n      LL: 'MMMM [tile] D [san] YYYY',\n      LLL: 'MMMM [tile] D [san] YYYY [lɛrɛ] HH:mm',\n      LLLL: 'dddd MMMM [tile] D [san] YYYY [lɛrɛ] HH:mm'\n    },\n    calendar: {\n      sameDay: '[Bi lɛrɛ] LT',\n      nextDay: '[Sini lɛrɛ] LT',\n      nextWeek: 'dddd [don lɛrɛ] LT',\n      lastDay: '[Kunu lɛrɛ] LT',\n      lastWeek: 'dddd [tɛmɛnen lɛrɛ] LT',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: '%s kɔnɔ',\n      past: 'a bɛ %s bɔ',\n      s: 'sanga dama dama',\n      ss: 'sekondi %d',\n      m: 'miniti kelen',\n      mm: 'miniti %d',\n      h: 'lɛrɛ kelen',\n      hh: 'lɛrɛ %d',\n      d: 'tile kelen',\n      dd: 'tile %d',\n      M: 'kalo kelen',\n      MM: 'kalo %d',\n      y: 'san kelen',\n      yy: 'san %d'\n    },\n    week: {\n      dow: 1,\n      // Monday is the first day of the week.\n      doy: 4 // The week that contains Jan 4th is the first week of the year.\n    }\n  });\n  return bm;\n});", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}