{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nvar core_1 = require(\"@angular/core\");\nvar common_1 = require(\"@angular/common\");\nvar DataTable_1 = require(\"./DataTable\");\nvar DefaultSorter_1 = require(\"./DefaultSorter\");\nvar Paginator_1 = require(\"./Paginator\");\nvar BootstrapPaginator_1 = require(\"./BootstrapPaginator\");\nvar DataTableModule = function () {\n  function DataTableModule() {}\n  DataTableModule.decorators = [{\n    type: core_1.NgModule,\n    args: [{\n      imports: [common_1.CommonModule],\n      declarations: [DataTable_1.DataTable, DefaultSorter_1.DefaultSorter, Paginator_1.Paginator, BootstrapPaginator_1.BootstrapPaginator],\n      exports: [DataTable_1.DataTable, DefaultSorter_1.DefaultSorter, Paginator_1.Paginator, BootstrapPaginator_1.BootstrapPaginator]\n    }]\n  }];\n  return DataTableModule;\n}();\nexports.DataTableModule = DataTableModule;\n//# sourceMappingURL=DataTableModule.js.map", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}