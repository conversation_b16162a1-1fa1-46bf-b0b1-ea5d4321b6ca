{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../webapi.service\";\nimport * as i2 from \"@angular/common/http\";\nimport * as i3 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i4 from \"@angular/forms\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"../../../node_modules/@angular/forms/index\";\nimport * as i7 from \"../topmenu/topmenu.component\";\nfunction UsersComponent_ngb_alert_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"ngb-alert\", 96);\n    i0.ɵɵlistener(\"close\", function UsersComponent_ngb_alert_2_Template_ngb_alert_close_0_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.staticAlertClosed = true);\n    });\n    i0.ɵɵtext(1, \" Save Data Completed!\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction UsersComponent_button_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 97);\n    i0.ɵɵlistener(\"click\", function UsersComponent_button_13_Template_button_click_0_listener() {\n      const usergroup_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.SetGroup(usergroup_r6));\n    })(\"click\", function UsersComponent_button_13_Template_button_click_0_listener() {\n      const usergroup_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.SetGroup(usergroup_r6));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const usergroup_r6 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(usergroup_r6.des_group);\n  }\n}\nfunction UsersComponent_tr_43_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\", 98)(1, \"td\", 99);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\", 99);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\", 99);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\", 99);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\", 99);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"td\", 100)(12, \"button\", 101);\n    i0.ɵɵlistener(\"click\", function UsersComponent_tr_43_Template_button_click_12_listener() {\n      const userlogin_r8 = i0.ɵɵrestoreView(_r7).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.Edituser(userlogin_r8));\n    });\n    i0.ɵɵtext(13, \"Edit\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"td\", 100)(15, \"button\", 102);\n    i0.ɵɵlistener(\"click\", function UsersComponent_tr_43_Template_button_click_15_listener() {\n      const userlogin_r8 = i0.ɵɵrestoreView(_r7).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.setdel(userlogin_r8));\n    });\n    i0.ɵɵtext(16, \"Delete\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const userlogin_r8 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", userlogin_r8.name_user, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", userlogin_r8.des_group, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", userlogin_r8.salegroup, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", userlogin_r8.login_user, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", userlogin_r8.pw, \" \");\n  }\n}\nfunction UsersComponent_tr_69_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\", 98)(1, \"td\", 99);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\", 99);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\", 99);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\", 99);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\", 99);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"td\", 100)(12, \"button\", 103);\n    i0.ɵɵlistener(\"click\", function UsersComponent_tr_69_Template_button_click_12_listener() {\n      const itme_r10 = i0.ɵɵrestoreView(_r9).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.Editcustomer(itme_r10));\n    });\n    i0.ɵɵtext(13, \"Edit\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"td\", 100)(15, \"button\", 104);\n    i0.ɵɵlistener(\"click\", function UsersComponent_tr_69_Template_button_click_15_listener() {\n      const itme_r10 = i0.ɵɵrestoreView(_r9).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.setdelcustomer(itme_r10));\n    });\n    i0.ɵɵtext(16, \"Delete\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const itme_r10 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", itme_r10.name_user, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", itme_r10.des_group, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", itme_r10.salegroup, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", itme_r10.login_user, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", itme_r10.pw, \" \");\n  }\n}\nfunction UsersComponent_ng_template_72_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 34)(1, \"h4\", 105);\n    i0.ɵɵtext(2, \"Add Group\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 106);\n    i0.ɵɵlistener(\"click\", function UsersComponent_ng_template_72_Template_button_click_3_listener() {\n      const d_r13 = i0.ɵɵrestoreView(_r12).dismiss;\n      return i0.ɵɵresetView(d_r13(\"Cross click\"));\n    });\n    i0.ɵɵelementStart(4, \"span\", 37);\n    i0.ɵɵtext(5, \"\\u00D7\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(6, \"div\", 38)(7, \"fieldset\", 107)(8, \"div\", 45)(9, \"label\", 90);\n    i0.ɵɵtext(10, \"Name Group : \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(11, \"input\", 108);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(12, \"div\", 51)(13, \"button\", 109);\n    i0.ɵɵlistener(\"click\", function UsersComponent_ng_template_72_Template_button_click_13_listener() {\n      const c_r14 = i0.ɵɵrestoreView(_r12).close;\n      return i0.ɵɵresetView(c_r14(\"Close click\"));\n    });\n    i0.ɵɵtext(14, \"Close\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"button\", 110);\n    i0.ɵɵlistener(\"click\", function UsersComponent_ng_template_72_Template_button_click_15_listener() {\n      const c_r14 = i0.ɵɵrestoreView(_r12).close;\n      const ctx_r2 = i0.ɵɵnextContext();\n      const form_r11 = i0.ɵɵreference(71);\n      return i0.ɵɵresetView(c_r14(ctx_r2.onSubmit(form_r11.value)));\n    });\n    i0.ɵɵtext(16, \"Save changes\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction UsersComponent_option_90_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 111);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const usergroup_r15 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", usergroup_r15.id_group_user);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", usergroup_r15.des_group, \" \");\n  }\n}\nfunction UsersComponent_option_96_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 111);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r16 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", item_r16.accountnum);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\" \", item_r16.accountnum, \" (\", item_r16.name, \") \");\n  }\n}\nfunction UsersComponent_label_101_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"label\", 112);\n    i0.ɵɵtext(1, \" *format Email Error\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction UsersComponent_label_105_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"label\", 112);\n    i0.ɵɵtext(1, \" *format Mobile Error\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction UsersComponent_option_126_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 111);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r17 = ctx.$implicit;\n    i0.ɵɵpropertyInterpolate(\"value\", item_r17.id_group_user);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r17.des_group);\n  }\n}\nfunction UsersComponent_option_127_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 113);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r18 = ctx.$implicit;\n    i0.ɵɵpropertyInterpolate(\"value\", item_r18.id_group_user);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r18.des_group);\n  }\n}\nfunction UsersComponent_option_133_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 111);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r19 = ctx.$implicit;\n    i0.ɵɵpropertyInterpolate(\"value\", item_r19.groupid);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\" \", item_r19.groupid, \" (\", item_r19.name, \")\");\n  }\n}\nfunction UsersComponent_option_134_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 113);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const itemSALE_r20 = ctx.$implicit;\n    i0.ɵɵpropertyInterpolate(\"value\", itemSALE_r20.groupid);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\" \", itemSALE_r20.groupid, \" (\", itemSALE_r20.name, \")\");\n  }\n}\nfunction UsersComponent_label_141_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"label\", 112);\n    i0.ɵɵtext(1, \" *format Email Error\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction UsersComponent_label_145_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"label\", 112);\n    i0.ɵɵtext(1, \" *format Mobile Error\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction UsersComponent_label_149_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"label\", 112);\n    i0.ɵɵtext(1, \" *Username is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction UsersComponent_label_154_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"label\", 112);\n    i0.ɵɵtext(1, \" *Password is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction UsersComponent_label_158_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"label\", 112);\n    i0.ɵɵtext(1, \" *Repeat password is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction UsersComponent_p_160_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 114);\n    i0.ɵɵtext(1, \"Password does not match\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction UsersComponent_option_200_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 111);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const usergroup_r21 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", usergroup_r21.id_group_user);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", usergroup_r21.des_group, \" \");\n  }\n}\nfunction UsersComponent_option_206_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 111);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r22 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", item_r22.groupid);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\" \", item_r22.groupid, \" (\", item_r22.name, \") \");\n  }\n}\nfunction UsersComponent_label_210_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"label\", 112);\n    i0.ɵɵtext(1, \" *Enter Name\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction UsersComponent_label_215_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"label\", 112);\n    i0.ɵɵtext(1, \" *Enter Email\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction UsersComponent_label_216_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"label\", 112);\n    i0.ɵɵtext(1, \" *format Email Error\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction UsersComponent_label_220_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"label\", 112);\n    i0.ɵɵtext(1, \" *Enter Mobile \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction UsersComponent_label_221_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"label\", 112);\n    i0.ɵɵtext(1, \" *format Mobile Error\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction UsersComponent_label_226_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"label\", 112);\n    i0.ɵɵtext(1, \" *Enter Username is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction UsersComponent_label_231_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"label\", 112);\n    i0.ɵɵtext(1, \" *Password is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction UsersComponent_label_235_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"label\", 112);\n    i0.ɵɵtext(1, \" *Repeat password is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction UsersComponent_p_237_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 114);\n    i0.ɵɵtext(1, \"Password does not match\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction UsersComponent_option_257_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 111);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r23 = ctx.$implicit;\n    i0.ɵɵpropertyInterpolate(\"value\", item_r23.id_group_user);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r23.des_group);\n  }\n}\nfunction UsersComponent_option_258_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 113);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r24 = ctx.$implicit;\n    i0.ɵɵpropertyInterpolate(\"value\", item_r24.id_group_user);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r24.des_group);\n  }\n}\nfunction UsersComponent_option_263_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 111);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r25 = ctx.$implicit;\n    i0.ɵɵpropertyInterpolate(\"value\", item_r25.groupid);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\" \", item_r25.groupid, \" (\", item_r25.name, \")\");\n  }\n}\nfunction UsersComponent_option_264_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 113);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const itemSALE_r26 = ctx.$implicit;\n    i0.ɵɵpropertyInterpolate(\"value\", itemSALE_r26.groupid);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\" \", itemSALE_r26.groupid, \" (\", itemSALE_r26.name, \")\");\n  }\n}\nfunction UsersComponent_label_267_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"label\", 112);\n    i0.ɵɵtext(1, \" *Enter Name\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction UsersComponent_label_271_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"label\", 112);\n    i0.ɵɵtext(1, \" *Enter Email\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction UsersComponent_label_272_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"label\", 112);\n    i0.ɵɵtext(1, \" *format Email Error\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction UsersComponent_label_276_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"label\", 112);\n    i0.ɵɵtext(1, \" *Enter Mobile \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction UsersComponent_label_277_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"label\", 112);\n    i0.ɵɵtext(1, \" *format Mobile Error\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction UsersComponent_label_281_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"label\", 112);\n    i0.ɵɵtext(1, \" *Username is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction UsersComponent_label_286_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"label\", 112);\n    i0.ɵɵtext(1, \" *Password is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction UsersComponent_label_290_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"label\", 112);\n    i0.ɵɵtext(1, \" *Repeat password is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction UsersComponent_p_292_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 114);\n    i0.ɵɵtext(1, \"Password does not match\");\n    i0.ɵɵelementEnd();\n  }\n}\nexport let UsersComponent = /*#__PURE__*/(() => {\n  class UsersComponent {\n    constructor(service, http, modalService, formBuilder) {\n      this.service = service;\n      this.http = http;\n      this.modalService = modalService;\n      this.formBuilder = formBuilder;\n      this.clsoedelete = true;\n      this.chdel = false;\n      this.staticAlertClosed = true;\n      this.message = '';\n      this.mProducts = [];\n      this.max = 2000;\n      this.keyboardStrGroup = '1';\n      this.keyboardStrApprove = '';\n      /* values = '';\n       onKey(value: string) {\n         this.datausername = value ;\n         const Urlapi = this.service.getDummy2();\n         const Urluserlist = this.service.getUrlUserlist();\n         const urlcheckuser = `${ Urlapi }${ Urluserlist }/%20/${ this.datausername }`;\n         this.http.get<any[]>(urlcheckuser).subscribe(res => {\n           if (res.length < 1) {\n             alert('ผ่าน')\n           }else{\n             alert('ไม่ผ่าน')\n           }\n         });\n       }*/\n      this.datalogin = [];\n      this.urlserver = service.geturlservice();\n      localStorage.removeItem('DataSOderreview');\n      localStorage.removeItem('DataSOderlist');\n      this.LoadGroup();\n      // this.test();\n      this.selectedValue = '';\n      this.selectedValuesaleman = '';\n      this.passwordFormGroup = this.formBuilder.group({\n        password: ['', Validators.required],\n        repeatPassword: ['', Validators.required]\n      }, {\n        validator: RegistrationValidator.validate.bind(this)\n      });\n      this.registrationFormGroup = this.formBuilder.group({\n        username: ['', Validators.required],\n        Group: ['', Validators.required],\n        Groupsale: ['', Validators.required],\n        name_user: ['', Validators.required],\n        Tel: ['', Validators.required],\n        email: ['', Validators.required],\n        passwordFormGroup: this.passwordFormGroup\n      });\n      this.EdpasswordFormGroup = this.formBuilder.group({\n        Edpassword: ['', Validators.required],\n        EdrepeatPassword: ['', Validators.required]\n      }, {\n        validator: EdRegistrationValidator.validate.bind(this)\n      });\n      this.EdpasswordFormGroup2 = this.formBuilder.group({\n        Edpassword2: ['', Validators.required],\n        EdrepeatPassword2: ['', Validators.required]\n      }, {\n        validator: EdRegistrationValidator2.validate.bind(this)\n      });\n      this.EditFormGrouped = this.formBuilder.group({\n        edusername: ['', Validators.required],\n        edname_user: ['', Validators.required],\n        edmobile: ['', Validators.required],\n        edemail: ['', Validators.required],\n        editFormGrouped: this.EdpasswordFormGroup\n      });\n      this.customerFormGroup = this.formBuilder.group({\n        Group: ['', Validators.required],\n        Groupcustomer: ['', Validators.required],\n        Tel: [],\n        email: []\n      });\n      this.EdcustomerFormGroup = this.formBuilder.group({\n        edusername: ['', Validators.required],\n        edname_user: ['', Validators.required],\n        EdcustomerFormGroup: this.EdpasswordFormGroup2\n      });\n      this.EdcustomerFormGroup2 = this.formBuilder.group({\n        edmobile: [],\n        edemail: []\n      });\n    }\n    test() {\n      this.http.get('http://192.168.1.128:9090/node/apinano/api/gw').subscribe(res => {\n        alert(JSON.stringify(res.Stations.Station[1].Province));\n      });\n    }\n    setopen() {\n      this.clsoedelete = true;\n      this.chdel = false;\n    }\n    opendelete(event) {\n      this.clsoedelete = false;\n      this.chdel = false;\n    }\n    Cset() {\n      this.setdes_group = \" \";\n      this.setid_group = \" \";\n      this.setopen();\n    }\n    SetGroup(usergroup) {\n      this.setid_group = usergroup.id_group_user;\n      this.setdes_group = usergroup.des_group;\n      this.chdel = false;\n    }\n    SaveGroup() {\n      const dataNameGroup = this.setdes_group;\n      const dataid = this.setid_group;\n      const url = `${this.urlserver}update_usergroup`;\n      this.http.post(url, {\n        id_group_user: dataid,\n        des_group: dataNameGroup\n      }).subscribe(res => {\n        console.log(res);\n        if (res === true) {\n          this.staticAlertClosed = false;\n          setTimeout(() => this.staticAlertClosed = true, 7000);\n          this.LoadGroup();\n        } else {\n          alert('Failed to save!!');\n        }\n      });\n      this.setdes_group = \" \";\n      this.setid_group = \" \";\n    }\n    deleteGroup() {\n      const dataid = this.setid_group;\n      const url = `${this.urlserver}delete_usergroup`;\n      this.http.post(url, {\n        id_user_group: dataid\n      }).subscribe(res => {\n        console.log(res);\n        if (res === true) {\n          this.staticAlertClosed = false;\n          setTimeout(() => this.staticAlertClosed = true, 7000);\n          this.LoadGroup();\n        } else {\n          alert('Failed to save!!');\n        }\n      });\n      this.setdes_group = \" \";\n      this.setid_group = \" \";\n    }\n    setdelcustomer(itme) {\n      this.namedel = '';\n      this.id_del = '';\n      this.nameshowdelete = '';\n      this.namedel = itme.accountnum;\n      this.id_del = itme.id_user;\n      this.nameshowdelete = itme.name_user;\n    }\n    Deletecustomer() {\n      const Dataid_del = this.id_del;\n      const Dateaccountnum = this.namedel;\n      const url = `${this.urlserver}delete_customer`;\n      this.http.post(url, {\n        id_user: Dataid_del,\n        id_accountnum: Dateaccountnum\n      }).subscribe(res => {\n        console.log(res);\n        if (res === true) {\n          this.staticAlertClosed = false;\n          setTimeout(() => this.staticAlertClosed = true, 7000);\n          this.namedel = '';\n          this.id_del = '';\n          this.nameshowdelete = '';\n          this.LoaduserCustomer();\n        } else {\n          alert('Failed to save!!.');\n        }\n      });\n    }\n    setdel(userlogin) {\n      this.namedel = '';\n      this.id_del = '';\n      this.namedel = userlogin.name_user;\n      this.id_del = userlogin.id_user;\n    }\n    Deleteuser() {\n      const Dataid_del = this.id_del;\n      const url = `${this.urlserver}delete_user`;\n      this.http.post(url, {\n        id_user: Dataid_del\n      }).subscribe(res => {\n        console.log(res);\n        if (res === true) {\n          this.staticAlertClosed = false;\n          setTimeout(() => this.staticAlertClosed = true, 7000);\n          this.id_del = \"\";\n          this.Loaduser();\n        } else {\n          alert('Failed to save!!.');\n        }\n      });\n    }\n    setgroup(des_name, Groupsale) {\n      this.DateGroupHave = [];\n      this.http.get(this.urlserver + 'des_group_data/' + des_name).subscribe(res => {\n        if (res.length > 0) {\n          this.DateGroupHave = res;\n          this.setgroupNull(des_name, Groupsale);\n        } else {}\n      });\n    }\n    setgroupNull(des_name, Groupsale) {\n      this.DateGroupNone = [];\n      this.http.get(this.urlserver + 'des_group/' + des_name).subscribe(res => {\n        if (res.length > 0) {\n          this.DateGroupNone = res;\n          this.setgroupsaleman(Groupsale);\n        } else {}\n      });\n    }\n    setgroupsaleman(Groupsale) {\n      this.DateGroupHavesaleman = [];\n      this.http.get(this.urlserver + 'groupsaleman/' + Groupsale).subscribe(res => {\n        if (res.length > 0) {\n          this.DateGroupHavesaleman = res;\n          this.setgroupNullsaleman(Groupsale);\n        } else {}\n      });\n    }\n    setgroupNullsaleman(Groupsale) {\n      this.DateGroupNonesaleman = [];\n      this.http.get(this.urlserver + 'groupsaleman_NO/' + Groupsale).subscribe(res => {\n        if (res.length > 0) {\n          this.DateGroupNonesaleman = res;\n        } else {}\n      });\n    }\n    getgroupsaleman() {\n      this.selectedValue = \"\";\n      this.selectedValuesaleman = \"\";\n      this.name_user = \"\";\n      this.email = \"\";\n      this.Tel = \"\";\n      this.username = \"\";\n      this.passwordFormGroup.value.password = \"\";\n      this.passwordFormGroup.value.repeatPassword = \"\";\n      this.DateGroupsaleman = [];\n      this.http.get(this.urlserver + 'salesman_full').subscribe(res => {\n        if (res.length > 0) {\n          this.DateGroupsaleman = res;\n        } else {\n          alert('ไม่สามารถติดข้อมูลรหัส saleman ได้');\n        }\n      });\n    }\n    Editcustomer(itme) {\n      this.customerID = '';\n      this.customerID = itme.accountnum;\n      this.id_user_customer = '';\n      this.id_user_customer = itme.id_user;\n      this.id_Saleman = '';\n      this.des_group = '';\n      this.dataid = '';\n      this.setgroup(itme.des_group, itme.salegroup);\n      this.edname_user = itme.name_user;\n      this.edemail = itme.email;\n      this.edmobile = itme.mobile;\n      this.edusername = itme.login_user;\n      this.Edpassword2 = itme.pw;\n      this.EdrepeatPassword2 = itme.pw;\n      this.id_group_user = itme.id_group_user;\n      this.dataid = itme.id_user;\n      this.des_group = itme.id_group_user;\n      this.id_Saleman = itme.salegroup;\n    }\n    onClickEdcustomer() {\n      this.datauser = this.EdcustomerFormGroup.value.edusername;\n      this.dataEmail = this.EdcustomerFormGroup2.value.edemail;\n      this.datatel = this.EdcustomerFormGroup2.value.edmobile;\n      const datapassword = this.EdpasswordFormGroup2.value.Edpassword2;\n      if (this.dataEmail == '' || this.dataEmail === undefined) {\n        this.dataEmail = '%20';\n      }\n      if (this.datatel == '' || this.datatel === undefined) {\n        this.datatel = '%20';\n      }\n      const url = `${this.urlserver}update_user_customer`;\n      this.http.post(url, {\n        id_user: this.id_user_customer,\n        email: this.dataEmail,\n        mobile: this.datatel,\n        pw: datapassword,\n        customerid: this.customerID\n      }).subscribe(res => {\n        console.log(res);\n        if (res === true) {\n          this.staticAlertClosed = false;\n          setTimeout(() => this.staticAlertClosed = true, 7000);\n          this.des_group = '';\n          this.LoaduserCustomer();\n          /* this.EditFormGrouped.value.id_User=\"\";\n          this.EditFormGrouped.value.edname_user=\"\";\n          this.EditFormGrouped.value.edemail=\"\";\n          this.EditFormGrouped.value.edmobile=\"\";\n          this.EditFormGrouped.value.edusername=\"\";\n          this.EdpasswordFormGroup.value.Edpassword=\"\";*/\n        }\n      });\n    }\n    Edituser(userlogin) {\n      this.id_Saleman = '';\n      this.des_group = '';\n      this.dataid = '';\n      this.setgroup(userlogin.des_group, userlogin.salegroup);\n      this.edname_user = userlogin.name_user;\n      this.edemail = userlogin.email;\n      this.edmobile = userlogin.mobile;\n      this.edusername = userlogin.login_user;\n      this.Edpassword = userlogin.pw;\n      this.EdrepeatPassword = userlogin.pw;\n      this.id_group_user = userlogin.id_group_user;\n      this.dataid = userlogin.id_user;\n      this.des_group = userlogin.id_group_user;\n      this.id_Saleman = userlogin.salegroup;\n    }\n    onClickEdUser() {\n      this.datauser = this.EditFormGrouped.value.edname_user;\n      this.dataName = this.EditFormGrouped.value.edname_user;\n      this.dataEmail = this.EditFormGrouped.value.edemail;\n      this.datatel = this.EditFormGrouped.value.edmobile;\n      this.datausername = this.EditFormGrouped.value.edusername;\n      const datapassword = this.EdpasswordFormGroup.value.Edpassword;\n      const dataNameGroup = this.EditFormGrouped.value.des_group;\n      const url = `${this.urlserver}update_user`;\n      this.http.post(url, {\n        id_group_user: this.des_group,\n        id_user: this.dataid,\n        name_user: this.dataName,\n        email: this.dataEmail,\n        mobile: this.datatel,\n        login_user: this.datausername,\n        pw: datapassword,\n        salesmanid: this.id_Saleman\n      }).subscribe(res => {\n        console.log(res);\n        if (res === true) {\n          this.staticAlertClosed = false;\n          setTimeout(() => this.staticAlertClosed = true, 7000);\n          this.des_group = '';\n          this.dataid = '';\n          this.Loaduser();\n          /* this.EditFormGrouped.value.id_User=\"\";\n          /:id_group_user/:id_user/:name_user/:email/:mobile/:login_user/:pw/:salesmanid\n          this.EditFormGrouped.value.edname_user=\"\";\n          this.EditFormGrouped.value.edemail=\"\";\n          this.EditFormGrouped.value.edmobile=\"\";\n          this.EditFormGrouped.value.edusername=\"\";\n          this.EdpasswordFormGroup.value.Edpassword=\"\";*/\n        }\n      });\n    }\n    selectvat(value) {\n      this.des_group = value;\n    }\n    selectgroupsaleman(value) {\n      this.id_Saleman = '';\n      this.id_Saleman = value;\n    }\n    addcustomer() {\n      this.selectedValuecustomer = \"\";\n      this.LoadGroupCustomer();\n    }\n    ngOnInit() {}\n    onClickRegister() {\n      this.datauser = this.getRandomInt(this.max);\n      this.datauser += this.registrationFormGroup.value.name_user;\n      this.dataName = this.registrationFormGroup.value.name_user;\n      this.dataEmail = this.registrationFormGroup.value.email;\n      this.datatel = this.registrationFormGroup.value.Tel;\n      this.datausername = this.registrationFormGroup.value.username;\n      this.datapassword1 = this.registrationFormGroup.value.passwordFormGroup.password;\n      const url = `${this.urlserver}create_user`;\n      const urlcheckuser = `${this.urlserver}find_user/%20/${this.datausername}`;\n      this.http.get(urlcheckuser).subscribe(res => {\n        if (res.length < 1) {\n          this.http.post(url, {\n            id_group_user: this.selectedValue,\n            id_user: this.datauser,\n            name_user: this.dataName,\n            email: this.dataEmail,\n            mobile: this.datatel,\n            login_user: this.datausername,\n            pw: this.datapassword1,\n            salesmanid: this.selectedValuesaleman\n          }).subscribe(res => {\n            console.log(res);\n            if (res === true) {\n              this.staticAlertClosed = false;\n              setTimeout(() => this.staticAlertClosed = true, 7000);\n              this.LoadGroup();\n              this.selectedValue = \"\";\n              this.selectedValuesaleman = \"\";\n              this.name_user = \"\";\n              this.email = \"\";\n              this.username = \"\";\n              this.Tel = \"\";\n              this.id_Saleman = '';\n              this.des_group = '';\n              this.dataid = '';\n            }\n          });\n          this.selectedValue = \" \";\n          this.name_user = \"\";\n          this.email = \"\";\n          this.username = \"\";\n          this.Tel = \"\";\n          this.id_Saleman = '';\n          this.des_group = '';\n          this.dataid = '';\n        } else {\n          alert('มี Username นี้ อยู่ในระบบแล้ว');\n        }\n      });\n    }\n    onClickRegisterCustomer() {\n      let dateusersum;\n      let url;\n      this.datatel = '';\n      this.dataEmail = '';\n      dateusersum = this.getRandomInt(this.max);\n      dateusersum += this.selectedValuecustomer;\n      if (this.customerFormGroup.value.email === undefined || this.customerFormGroup.value.email == '') {\n        this.datatel = '';\n      } else {\n        this.dataEmail = this.customerFormGroup.value.email;\n      }\n      if (this.customerFormGroup.value.Tel === undefined || this.customerFormGroup.value.Tel == '') {\n        this.dataEmail = '';\n      } else {\n        this.datatel = this.customerFormGroup.value.Tel;\n      }\n      /*/:id_group_user/:id_user/:email/:mobile/:customerid*/\n      url = `${this.urlserver}create_customer`;\n      const urlcheckuser = `${this.urlserver}find_user_customer/${this.selectedValuecustomer}`;\n      this.http.get(urlcheckuser).subscribe(res => {\n        if (res.length < 1) {\n          this.http.post(url, {\n            id_group_user: this.selectedValue,\n            id_user: dateusersum,\n            email: this.dataEmail,\n            mobile: this.datatel,\n            customerid: this.selectedValuecustomer\n          }).subscribe(res => {\n            if (res === true) {\n              this.staticAlertClosed = false;\n              setTimeout(() => this.staticAlertClosed = true, 3000);\n              this.selectedValue = \"\";\n              this.selectedValuecustomer = \"\";\n              this.customerFormGroup.value.email = \"\";\n              this.customerFormGroup.value.Tel = \"\";\n              this.Tel = \"\";\n              this.LoaduserCustomer();\n            }\n          }, error => {\n            alert('เกิดปัญหาในการ Process ข้อมูล กรุณาทำรายการใหม่อีกครั้ง');\n          });\n          this.selectedValue = \"\";\n          this.customerFormGroup.value.email = \"\";\n          this.customerFormGroup.value.Tel = \"\";\n          this.email = \"\";\n          this.selectedValuecustomer = \"\";\n          this.Tel = \"\";\n        } else {\n          alert('มี Username นี้ อยู่ในระบบแล้ว');\n        }\n      }, error => {\n        alert('เกิดปัญหาในการ Process ข้อมูล กรุณารายการใหม่อีกครั้ง');\n      });\n    }\n    openModelClass(content) {\n      this.modalService.open(content, {});\n    }\n    openModelAdduser(contentuser) {\n      this.modalService.open(contentuser, {});\n    }\n    Loaduser() {\n      this.userlogins = [];\n      this.http.get(this.urlserver + 'userlist').subscribe(res => {\n        this.userlogins = res;\n      });\n    }\n    LoadGroup() {\n      this.usergroups = [];\n      this.http.get(this.urlserver + 'usergroup').subscribe(res => {\n        //this.service.DataGroup().then\n        this.usergroups = res;\n        this.Loaduser();\n      });\n    }\n    LoadGroupCustomer() {\n      this.usergroupCustomer = [];\n      this.http.get(this.urlserver + 'customer_list').subscribe(res => {\n        this.usergroupCustomer = res;\n      });\n    }\n    LoaduserCustomer() {\n      this.userCustomer = [];\n      this.http.get(this.urlserver + 'user_customer').subscribe(res => {\n        this.userCustomer = res;\n      });\n    }\n    onSubmit(value) {\n      this.data1 = this.getRandomInt(this.max);\n      this.data1 += value.user_identification.Group_name;\n      this.data2 = value.user_identification.Group_name;\n      const url = `${this.urlserver}create_usergroup`;\n      this.http.post(url, {\n        id_group_user: this.data1,\n        des_group: this.data2\n      }).subscribe(res => {\n        console.log(res);\n        if (res === true) {\n          this.staticAlertClosed = false;\n          setTimeout(() => this.staticAlertClosed = true, 7000);\n          this.LoadGroup();\n        }\n      });\n    }\n    /* onSubmitadduser(value){\n     this.dataName= value.user_date.Name;\n     this.dataEmail=value.user_date.Email;\n     this.datatel=value.user_date.Tel;\n     this.datausername=value.user_date.Username;\n     this.datapassword1=value.user_date.Password1;\n     this.datapassword2=value.user_date.Password2;\n     alert(this.selectedValue + ' ' +  this.dataName+ ' '+ this.dataEmail+ '  ' +this.datatel+ ' ' +this.datausername+ ' ' +this.datapassword1 )\n     }*/\n    cn() {\n      this.selectedValue = \" \";\n      this.name_user = \"\";\n      this.email = \"\";\n      this.username = \"\";\n      this.Tel = \"\";\n    }\n    getgroup(usergroup) {\n      this.dataGroup = usergroup.des_group;\n      alert(this.dataGroup);\n    }\n    getRandomInt(max) {\n      return Math.floor(Math.random() * Math.floor(max));\n    }\n    getuserlist(event) {\n      this.datausername = this.registrationFormGroup.value.username;\n      this.datetest = event.target.value;\n      const url = `${this.urlserver}find_user/%20/${this.datetest}`;\n      this.http.get(url).subscribe(res => {\n        this.datetest2 = res;\n        alert(res.length);\n      });\n    }\n    static {\n      this.ɵfac = function UsersComponent_Factory(t) {\n        return new (t || UsersComponent)(i0.ɵɵdirectiveInject(i1.WebapiService), i0.ɵɵdirectiveInject(i2.HttpClient), i0.ɵɵdirectiveInject(i3.NgbModal), i0.ɵɵdirectiveInject(i4.FormBuilder));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: UsersComponent,\n        selectors: [[\"app-users\"]],\n        decls: 338,\n        vars: 81,\n        consts: [[\"form\", \"ngForm\"], [\"content\", \"\"], [2, \"padding-top\", \"60px\"], [\"type\", \"success\", \"style\", \"margin-bottom: 5px;\", 3, \"close\", 4, \"ngIf\"], [1, \"p-sm-1\", \"bg-secondary\", \"text-white\", \"text-center\"], [1, \"container-fluid\"], [1, \"row\"], [1, \"col-sm-3\"], [1, \"p-sm-1\", \"bg-secondary\", \"text-white\", \"font-weight-light\"], [1, \"btn\", \"btn-success\", 2, \"margin-bottom\", \"5px\", 3, \"click\"], [1, \"btn-group-vertical\", \"list-group\"], [\"style\", \"text-align: left; margin-top: 4px; padding: 10px;\", \"data-toggle\", \"modal\", \"data-target\", \"#EdModalgroup\", \"type\", \"button\", \"class\", \"btn btn-outline-success \", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"col-sm-9\"], [\"id\", \"accordionExample\", 1, \"accordion\"], [1, \"card\"], [\"id\", \"headingOne\", 1, \"card-header\", 2, \"padding-top\", \"0px\", \"padding-bottom\", \"0px\"], [1, \"mb-0\"], [\"type\", \"button\", \"data-toggle\", \"collapse\", \"data-target\", \"#collapseOne\", \"aria-expanded\", \"true\", \"aria-controls\", \"collapseOne\", 1, \"btn\", \"btn-link\"], [\"id\", \"collapseOne\", \"aria-labelledby\", \"headingOne\", \"data-parent\", \"#accordionExample\", 1, \"collapse\", \"show\"], [1, \"card-body\", 2, \"padding\", \"5px\"], [\"data-toggle\", \"modal\", \"data-target\", \"#ModalAddUser\", 1, \"btn\", \"btn-success\", 2, \"margin-bottom\", \"5px\", 3, \"click\"], [1, \"table\", \"table-hover\", \"table-bordered\", \"table-sm\"], [1, \"font-weight-light\", \"text-sm-center\"], [\"scope\", \"col\", 1, \"text-sm-center\"], [\"scope\", \"col\", \"width\", \"60px\", 1, \"text-sm-center\"], [\"class\", \"font-weight-light text-center\", 4, \"ngFor\", \"ngForOf\"], [\"id\", \"headingTwo\", 1, \"card-header\", 2, \"padding-top\", \"0px\", \"padding-bottom\", \"0px\"], [\"type\", \"button\", \"data-toggle\", \"collapse\", \"data-target\", \"#collapseTwo\", \"aria-expanded\", \"false\", \"aria-controls\", \"collapseTwo\", 1, \"btn\", \"btn-link\", \"collapsed\", 3, \"click\"], [\"id\", \"collapseTwo\", \"aria-labelledby\", \"headingTwo\", \"data-parent\", \"#accordionExample\", 1, \"collapse\"], [\"data-toggle\", \"modal\", \"data-target\", \"#ModalAddCustomer\", 1, \"btn\", \"btn-success\", 2, \"margin-bottom\", \"5px\", 3, \"click\"], [\"role\", \"form\", 3, \"submit\"], [\"id\", \"ModalAddCustomer\", \"tabindex\", \"-1\", \"role\", \"dialog\", \"aria-labelledby\", \"ModalAddUser\", \"aria-hidden\", \"true\", 1, \"modal\", \"fade\", 2, \"top\", \"-100px\"], [\"role\", \"document\", 1, \"modal-dialog\"], [1, \"modal-content\"], [1, \"modal-header\"], [\"id\", \"exampleModalLabel\", 1, \"modal-title\"], [\"type\", \"button\", \"data-dismiss\", \"modal\", \"aria-label\", \"Close\", 1, \"close\", 3, \"click\"], [\"aria-hidden\", \"true\"], [1, \"modal-body\"], [3, \"ngSubmit\", \"formGroup\"], [\"for\", \"exampleFormControlSelect1\"], [\"placeholder\", \"Select Group\", \"formControlName\", \"Group\", \"name\", \"Group\", 1, \"form-control\", 3, \"ngModelChange\", \"ngModel\"], [\"selected\", \"\", \"value\", \"\", \"disabled\", \"\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"placeholder\", \"Select Group\", \"formControlName\", \"Groupcustomer\", \"name\", \"Groupcustomer\", 1, \"form-control\", 3, \"ngModelChange\", \"ngModel\"], [1, \"form-group\"], [\"for\", \"email\"], [\"class\", \"bg-alert\", 4, \"ngIf\"], [\"type\", \"text\", \"pattern\", \"^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\\\\.[a-zA-Z0-9.]+$\", \"name\", \"email\", \"placeholder\", \"Email\", \"formControlName\", \"email\", 1, \"form-control\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"Tel\"], [\"type\", \"text\", \"pattern\", \"^((\\\\\\\\+91-?)|0)?[0-9]{10}$\", \"name\", \"Tel\", \"placeholder\", \"0801234567\", \"formControlName\", \"Tel\", 1, \"form-control\", 3, \"ngModelChange\", \"ngModel\"], [1, \"modal-footer\"], [\"type\", \"button\", \"data-dismiss\", \"modal\", 1, \"btn\", \"btn-secondary\", 3, \"click\"], [\"type\", \"button\", \"data-dismiss\", \"modal\", 1, \"btn\", \"btn-primary\", 3, \"click\", \"disabled\"], [\"id\", \"Editcoutomer\", \"tabindex\", \"-1\", \"role\", \"dialog\", \"aria-labelledby\", \"Editcoutomer\", \"aria-hidden\", \"true\", 1, \"modal\", \"fade\", 2, \"top\", \"-100px\"], [\"type\", \"button\", \"data-dismiss\", \"modal\", \"aria-label\", \"Close\", 1, \"close\"], [3, \"formGroup\"], [\"for\", \"namme\"], [\"type\", \"text\", \"name\", \"Group\", 1, \"form-control\", 3, \"change\"], [\"selected\", \"\", 3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"disabled\", \"\"], [\"type\", \"text\", \"name\", \"Groupsale\", 1, \"form-control\", 3, \"change\"], [\"type\", \"text\", \"type\", \"text\", \"name\", \"edname_user\", \"placeholder\", \"Name\", \"formControlName\", \"edname_user\", \"readonly\", \"\", 1, \"form-control\", 3, \"ngModelChange\", \"ngModel\"], [\"type\", \"text\", \"pattern\", \"^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\\\\.[a-zA-Z0-9.]+$\", \"name\", \"edemail\", \"placeholder\", \"Email\", \"formControlName\", \"edemail\", 1, \"form-control\", 3, \"ngModelChange\", \"ngModel\"], [\"type\", \"text\", \"pattern\", \"^((\\\\\\\\+91-?)|0)?[0-9]{10}$\", \"name\", \"edmobile\", \"placeholder\", \"0801234567\", \"formControlName\", \"edmobile\", 1, \"form-control\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"usernamme\"], [\"type\", \"text\", \"type\", \"text\", \"name\", \"edusername\", \"formControlName\", \"edusername\", \"readonly\", \"\", 1, \"form-control\", 3, \"ngModelChange\", \"ngModel\"], [2, \"margin-bottom\", \"5px\", 3, \"formGroup\"], [\"for\", \"password\"], [\"type\", \"password\", \"name\", \"Edpassword2\", \"formControlName\", \"Edpassword2\", 1, \"form-control\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"repeatPassword\"], [\"type\", \"password\", \"name\", \"EdrepeatPassword2\", \"formControlName\", \"EdrepeatPassword2\", 1, \"form-control\"], [\"class\", \"alert  bg-danger\", 4, \"ngIf\"], [\"id\", \"Delcustomer\", \"tabindex\", \"-1\", \"role\", \"dialog\", \"aria-labelledby\", \"DelModal\", \"aria-hidden\", \"true\", 1, \"modal\", \"fade\", 2, \"top\", \"-100px\"], [\"type\", \"button\", \"data-dismiss\", \"modal\", 1, \"btn\", \"btn-secondary\"], [\"type\", \"button\", \"data-dismiss\", \"modal\", 1, \"btn\", \"btn-primary\", 3, \"click\"], [\"id\", \"ModalAddUser\", \"tabindex\", \"-1\", \"role\", \"dialog\", \"aria-labelledby\", \"ModalAddUser\", \"aria-hidden\", \"true\", 1, \"modal\", \"fade\", 2, \"top\", \"-100px\"], [\"placeholder\", \"Select Group\", \"formControlName\", \"Groupsale\", \"name\", \"Groupsale\", 1, \"form-control\", 3, \"ngModelChange\", \"ngModel\"], [\"type\", \"text\", \"type\", \"text\", \"name\", \"name_user\", \"placeholder\", \"Name\", \"formControlName\", \"name_user\", 1, \"form-control\", 3, \"ngModelChange\", \"ngModel\"], [1, \"form-group\", 2, \"margin-bottom\", \"5px\"], [\"type\", \"text\", \"type\", \"text\", \"name\", \"username\", \"formControlName\", \"username\", \"placeholder\", \"Enter Username\", 1, \"form-control\", 3, \"ngModelChange\", \"ngModel\"], [\"type\", \"password\", \"name\", \"password\", \"placeholder\", \"Enter Password\", \"formControlName\", \"password\", 1, \"form-control\"], [\"type\", \"password\", \"name\", \"repeatPassword\", \"placeholder\", \"Enter RepeatPassword\", \"formControlName\", \"repeatPassword\", 1, \"form-control\"], [\"id\", \"Edituser\", \"tabindex\", \"-1\", \"role\", \"dialog\", \"aria-labelledby\", \"Edituser\", \"aria-hidden\", \"true\", 1, \"modal\", \"fade\", 2, \"top\", \"-100px\"], [\"type\", \"text\", 1, \"form-control\", 3, \"change\"], [\"type\", \"text\", \"type\", \"text\", \"name\", \"edname_user\", \"placeholder\", \"Name\", \"formControlName\", \"edname_user\", 1, \"form-control\", 3, \"ngModelChange\", \"ngModel\"], [\"type\", \"password\", \"name\", \"Edpassword\", \"formControlName\", \"Edpassword\", 1, \"form-control\", 3, \"ngModelChange\", \"ngModel\"], [\"type\", \"password\", \"name\", \"EdrepeatPassword\", \"formControlName\", \"EdrepeatPassword\", 1, \"form-control\", 3, \"ngModelChange\", \"ngModel\"], [\"id\", \"DelModal\", \"tabindex\", \"-1\", \"role\", \"dialog\", \"aria-labelledby\", \"DelModal\", \"aria-hidden\", \"true\", 1, \"modal\", \"fade\", 2, \"top\", \"-100px\"], [\"id\", \"EdModalgroup\", \"tabindex\", \"-1\", \"role\", \"dialog\", \"aria-labelledby\", \"EdModalgroup\", \"aria-hidden\", \"true\", 1, \"modal\", \"fade\", 2, \"top\", \"-100px\"], [\"for\", \"last_name\"], [\"name\", \"Group_name\", \"type\", \"text\", \"id\", \"Group_name\", \"placeholder\", \"Name Group\", \"required\", \"\", 1, \"form-control\", 3, \"ngModelChange\", \"ngModel\"], [1, \"custom-control\", \"custom-checkbox\"], [\"type\", \"checkbox\", \"id\", \"customCheck1\", 1, \"custom-control-input\", 3, \"ngModelChange\", \"click\", \"ngModel\"], [\"for\", \"customCheck1\", 1, \"custom-control-label\"], [\"type\", \"button\", \"data-dismiss\", \"modal\", 1, \"btn\", \"btn-light\", 2, \"float\", \"left\", 3, \"click\", \"disabled\"], [\"type\", \"success\", 2, \"margin-bottom\", \"5px\", 3, \"close\"], [\"data-toggle\", \"modal\", \"data-target\", \"#EdModalgroup\", \"type\", \"button\", 1, \"btn\", \"btn-outline-success\", 2, \"text-align\", \"left\", \"margin-top\", \"4px\", \"padding\", \"10px\", 3, \"click\"], [1, \"font-weight-light\", \"text-center\"], [1, \"text-sm-center\", \"font-weight-normal\"], [1, \"text-center\"], [\"type\", \"button\", \"data-toggle\", \"modal\", \"data-target\", \"#Edituser\", 1, \"btn\", \"btn-warning\", 2, \"height\", \"29px\", \"padding-top\", \"0px\", 3, \"click\"], [\"type\", \"button\", \"data-toggle\", \"modal\", \"data-target\", \"#DelModal\", 1, \"btn\", \"btn-danger\", 2, \"height\", \"29px\", \"padding-top\", \"0px\", 3, \"click\"], [\"type\", \"button\", \"data-toggle\", \"modal\", \"data-target\", \"#Editcoutomer\", 1, \"btn\", \"btn-warning\", 2, \"height\", \"29px\", \"padding-top\", \"0px\", 3, \"click\"], [\"type\", \"button\", \"data-toggle\", \"modal\", \"data-target\", \"#Delcustomer\", 1, \"btn\", \"btn-danger\", 2, \"height\", \"29px\", \"padding-top\", \"0px\", 3, \"click\"], [1, \"modal-title\"], [\"type\", \"button\", \"aria-label\", \"Close\", 1, \"close\", 3, \"click\"], [\"ngModelGroup\", \"user_identification\"], [\"ngModel\", \"\", \"name\", \"Group_name\", \"type\", \"text\", \"id\", \"Group_name\", \"placeholder\", \"Name Group\", \"required\", \"\", 1, \"form-control\"], [\"type\", \"button\", 1, \"btn\", \"btn-light\", 3, \"click\"], [\"type\", \"submit\", 1, \"btn\", \"btn-primary\", 3, \"click\"], [3, \"value\"], [1, \"bg-alert\"], [\"selected\", \"\", 3, \"value\"], [1, \"alert\", \"bg-danger\"]],\n        template: function UsersComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            const _r1 = i0.ɵɵgetCurrentView();\n            i0.ɵɵelement(0, \"app-topmenu\");\n            i0.ɵɵelementStart(1, \"section\", 2);\n            i0.ɵɵtemplate(2, UsersComponent_ngb_alert_2_Template, 2, 0, \"ngb-alert\", 3);\n            i0.ɵɵelementStart(3, \"h5\", 4);\n            i0.ɵɵtext(4, \"User Accounts\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(5, \"div\", 5)(6, \"div\", 6)(7, \"div\", 7)(8, \"h5\", 8);\n            i0.ɵɵtext(9, \"User Group\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(10, \"button\", 9);\n            i0.ɵɵlistener(\"click\", function UsersComponent_Template_button_click_10_listener() {\n              i0.ɵɵrestoreView(_r1);\n              const content_r4 = i0.ɵɵreference(73);\n              return i0.ɵɵresetView(ctx.openModelClass(content_r4));\n            });\n            i0.ɵɵtext(11, \"ADD \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(12, \"div\", 10);\n            i0.ɵɵtemplate(13, UsersComponent_button_13_Template, 2, 1, \"button\", 11);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(14, \"div\", 12)(15, \"h5\", 8);\n            i0.ɵɵtext(16, \"User List - Viewer\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(17, \"div\", 13)(18, \"div\", 14)(19, \"div\", 15)(20, \"h5\", 16)(21, \"button\", 17);\n            i0.ɵɵtext(22, \" Group Sales \");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(23, \"div\", 18)(24, \"div\", 19)(25, \"button\", 20);\n            i0.ɵɵlistener(\"click\", function UsersComponent_Template_button_click_25_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.getgroupsaleman());\n            });\n            i0.ɵɵtext(26, \"Add User \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(27, \"table\", 21)(28, \"thead\")(29, \"tr\", 22)(30, \"th\", 23);\n            i0.ɵɵtext(31, \"User Name\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(32, \"th\", 23);\n            i0.ɵɵtext(33, \"User Group\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(34, \"th\", 23);\n            i0.ɵɵtext(35, \"Sales Group\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(36, \"th\", 23);\n            i0.ɵɵtext(37, \"Log In Name\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(38, \"th\", 23);\n            i0.ɵɵtext(39, \"Password\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(40, \"th\", 24)(41, \"th\", 24);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(42, \"tbody\");\n            i0.ɵɵtemplate(43, UsersComponent_tr_43_Template, 17, 5, \"tr\", 25);\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(44, \"div\", 14)(45, \"div\", 26)(46, \"h5\", 16)(47, \"button\", 27);\n            i0.ɵɵlistener(\"click\", function UsersComponent_Template_button_click_47_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.LoaduserCustomer());\n            });\n            i0.ɵɵtext(48, \" Group Customer \");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(49, \"div\", 28)(50, \"div\", 19)(51, \"button\", 29);\n            i0.ɵɵlistener(\"click\", function UsersComponent_Template_button_click_51_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.addcustomer());\n            });\n            i0.ɵɵtext(52, \"Add Customer \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(53, \"table\", 21)(54, \"thead\")(55, \"tr\", 22)(56, \"th\", 23);\n            i0.ɵɵtext(57, \"User Name\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(58, \"th\", 23);\n            i0.ɵɵtext(59, \"User Group\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(60, \"th\", 23);\n            i0.ɵɵtext(61, \"Sales Group\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(62, \"th\", 23);\n            i0.ɵɵtext(63, \"Log In Name\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(64, \"th\", 23);\n            i0.ɵɵtext(65, \"Password\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(66, \"th\", 24)(67, \"th\", 24);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(68, \"tbody\");\n            i0.ɵɵtemplate(69, UsersComponent_tr_69_Template, 17, 5, \"tr\", 25);\n            i0.ɵɵelementEnd()()()()()()()()()()();\n            i0.ɵɵelementStart(70, \"form\", 30, 0);\n            i0.ɵɵlistener(\"submit\", function UsersComponent_Template_form_submit_70_listener() {\n              i0.ɵɵrestoreView(_r1);\n              const form_r11 = i0.ɵɵreference(71);\n              return i0.ɵɵresetView(ctx.onSubmit(form_r11.value));\n            });\n            i0.ɵɵtemplate(72, UsersComponent_ng_template_72_Template, 17, 0, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(74, \"div\", 31)(75, \"div\", 32)(76, \"div\", 33)(77, \"div\", 34)(78, \"h5\", 35);\n            i0.ɵɵtext(79, \"Add User Customer\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(80, \"button\", 36);\n            i0.ɵɵlistener(\"click\", function UsersComponent_Template_button_click_80_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.cn());\n            });\n            i0.ɵɵelementStart(81, \"span\", 37);\n            i0.ɵɵtext(82, \"\\u00D7\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(83, \"div\", 38)(84, \"form\", 39);\n            i0.ɵɵlistener(\"ngSubmit\", function UsersComponent_Template_form_ngSubmit_84_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.onClickRegisterCustomer());\n            });\n            i0.ɵɵelementStart(85, \"label\", 40);\n            i0.ɵɵtext(86, \"Select Group : \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(87, \"select\", 41);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function UsersComponent_Template_select_ngModelChange_87_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.selectedValue, $event) || (ctx.selectedValue = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementStart(88, \"option\", 42);\n            i0.ɵɵtext(89, \"--Select Group--\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(90, UsersComponent_option_90_Template, 2, 2, \"option\", 43);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(91, \"label\", 40);\n            i0.ɵɵtext(92, \"Select IDsaleman : \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(93, \"select\", 44);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function UsersComponent_Template_select_ngModelChange_93_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.selectedValuecustomer, $event) || (ctx.selectedValuecustomer = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementStart(94, \"option\", 42);\n            i0.ɵɵtext(95, \"--Select Customer--\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(96, UsersComponent_option_96_Template, 2, 3, \"option\", 43);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(97, \"div\", 45)(98, \"div\", 45)(99, \"label\", 46);\n            i0.ɵɵtext(100, \"Email : \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(101, UsersComponent_label_101_Template, 2, 0, \"label\", 47);\n            i0.ɵɵelementStart(102, \"input\", 48);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function UsersComponent_Template_input_ngModelChange_102_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.email, $event) || (ctx.email = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(103, \"label\", 49);\n            i0.ɵɵtext(104, \"Mobile : \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(105, UsersComponent_label_105_Template, 2, 0, \"label\", 47);\n            i0.ɵɵelementStart(106, \"input\", 50);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function UsersComponent_Template_input_ngModelChange_106_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.Tel, $event) || (ctx.Tel = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(107, \"div\", 51)(108, \"button\", 52);\n            i0.ɵɵlistener(\"click\", function UsersComponent_Template_button_click_108_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.cn());\n            });\n            i0.ɵɵtext(109, \"Close\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(110, \"button\", 53);\n            i0.ɵɵlistener(\"click\", function UsersComponent_Template_button_click_110_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.onClickRegisterCustomer());\n            });\n            i0.ɵɵtext(111, \"Save changes\");\n            i0.ɵɵelementEnd()()()()();\n            i0.ɵɵelementStart(112, \"div\", 54)(113, \"div\", 32)(114, \"div\", 33)(115, \"div\", 34)(116, \"h5\", 35);\n            i0.ɵɵtext(117, \"Edist Customer\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(118, \"button\", 55)(119, \"span\", 37);\n            i0.ɵɵtext(120, \"\\u00D7\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(121, \"div\", 38)(122, \"form\", 56)(123, \"label\", 57);\n            i0.ɵɵtext(124, \"Select Group : \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(125, \"select\", 58);\n            i0.ɵɵlistener(\"change\", function UsersComponent_Template_select_change_125_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.selectvat($event.target.value));\n            });\n            i0.ɵɵtemplate(126, UsersComponent_option_126_Template, 2, 2, \"option\", 43)(127, UsersComponent_option_127_Template, 2, 2, \"option\", 59);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(128, \"div\", 45)(129, \"fieldset\", 60)(130, \"label\", 57);\n            i0.ɵɵtext(131, \"Select GroupSalesman : \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(132, \"select\", 61);\n            i0.ɵɵlistener(\"change\", function UsersComponent_Template_select_change_132_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.selectgroupsaleman($event.target.value));\n            });\n            i0.ɵɵtemplate(133, UsersComponent_option_133_Template, 2, 3, \"option\", 43)(134, UsersComponent_option_134_Template, 2, 3, \"option\", 59);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(135, \"label\", 57);\n            i0.ɵɵtext(136, \"Name : \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(137, \"input\", 62);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function UsersComponent_Template_input_ngModelChange_137_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.edname_user, $event) || (ctx.edname_user = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(138, \"div\", 56)(139, \"label\", 46);\n            i0.ɵɵtext(140, \"Email : \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(141, UsersComponent_label_141_Template, 2, 0, \"label\", 47);\n            i0.ɵɵelementStart(142, \"input\", 63);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function UsersComponent_Template_input_ngModelChange_142_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.edemail, $event) || (ctx.edemail = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(143, \"label\", 49);\n            i0.ɵɵtext(144, \"Mobile : \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(145, UsersComponent_label_145_Template, 2, 0, \"label\", 47);\n            i0.ɵɵelementStart(146, \"input\", 64);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function UsersComponent_Template_input_ngModelChange_146_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.edmobile, $event) || (ctx.edmobile = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(147, \"label\", 65);\n            i0.ɵɵtext(148, \"Username : \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(149, UsersComponent_label_149_Template, 2, 0, \"label\", 47);\n            i0.ɵɵelementStart(150, \"input\", 66);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function UsersComponent_Template_input_ngModelChange_150_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.edusername, $event) || (ctx.edusername = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(151, \"div\", 67)(152, \"label\", 68);\n            i0.ɵɵtext(153, \"Password : \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(154, UsersComponent_label_154_Template, 2, 0, \"label\", 47);\n            i0.ɵɵelementStart(155, \"input\", 69);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function UsersComponent_Template_input_ngModelChange_155_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.Edpassword2, $event) || (ctx.Edpassword2 = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(156, \"label\", 70);\n            i0.ɵɵtext(157, \"Repeat Password : \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(158, UsersComponent_label_158_Template, 2, 0, \"label\", 47);\n            i0.ɵɵelement(159, \"input\", 71);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(160, UsersComponent_p_160_Template, 2, 0, \"p\", 72);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(161, \"div\", 51)(162, \"button\", 52);\n            i0.ɵɵlistener(\"click\", function UsersComponent_Template_button_click_162_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.cn());\n            });\n            i0.ɵɵtext(163, \"Close\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(164, \"button\", 53);\n            i0.ɵɵlistener(\"click\", function UsersComponent_Template_button_click_164_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.onClickEdcustomer());\n            });\n            i0.ɵɵtext(165, \"Save changes\");\n            i0.ɵɵelementEnd()()()()();\n            i0.ɵɵelementStart(166, \"div\", 73)(167, \"div\", 32)(168, \"div\", 33)(169, \"div\", 34)(170, \"h5\", 35);\n            i0.ɵɵtext(171, \"Delete User Customer\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(172, \"button\", 55)(173, \"span\", 37);\n            i0.ɵɵtext(174, \"\\u00D7\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(175, \"div\", 38);\n            i0.ɵɵtext(176);\n            i0.ɵɵelement(177, \"br\");\n            i0.ɵɵtext(178);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(179, \"div\", 51)(180, \"button\", 74);\n            i0.ɵɵtext(181, \"Close\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(182, \"button\", 75);\n            i0.ɵɵlistener(\"click\", function UsersComponent_Template_button_click_182_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.Deletecustomer());\n            });\n            i0.ɵɵtext(183, \"Delete\");\n            i0.ɵɵelementEnd()()()()();\n            i0.ɵɵelementStart(184, \"div\", 76)(185, \"div\", 32)(186, \"div\", 33)(187, \"div\", 34)(188, \"h5\", 35);\n            i0.ɵɵtext(189, \"Add User\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(190, \"button\", 36);\n            i0.ɵɵlistener(\"click\", function UsersComponent_Template_button_click_190_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.cn());\n            });\n            i0.ɵɵelementStart(191, \"span\", 37);\n            i0.ɵɵtext(192, \"\\u00D7\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(193, \"div\", 38)(194, \"form\", 39);\n            i0.ɵɵlistener(\"ngSubmit\", function UsersComponent_Template_form_ngSubmit_194_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.onClickRegister());\n            });\n            i0.ɵɵelementStart(195, \"label\", 40);\n            i0.ɵɵtext(196, \"Select Group : \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(197, \"select\", 41);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function UsersComponent_Template_select_ngModelChange_197_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.selectedValue, $event) || (ctx.selectedValue = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementStart(198, \"option\", 42);\n            i0.ɵɵtext(199, \"--Select Group--\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(200, UsersComponent_option_200_Template, 2, 2, \"option\", 43);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(201, \"label\", 40);\n            i0.ɵɵtext(202, \"Select IDsaleman : \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(203, \"select\", 77);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function UsersComponent_Template_select_ngModelChange_203_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.selectedValuesaleman, $event) || (ctx.selectedValuesaleman = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementStart(204, \"option\", 42);\n            i0.ɵɵtext(205, \"--Select IDsaleman--\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(206, UsersComponent_option_206_Template, 2, 3, \"option\", 43);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(207, \"div\", 45)(208, \"label\", 57);\n            i0.ɵɵtext(209, \"Name : \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(210, UsersComponent_label_210_Template, 2, 0, \"label\", 47);\n            i0.ɵɵelementStart(211, \"input\", 78);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function UsersComponent_Template_input_ngModelChange_211_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.name_user, $event) || (ctx.name_user = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(212, \"div\", 45)(213, \"label\", 46);\n            i0.ɵɵtext(214, \"Email : \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(215, UsersComponent_label_215_Template, 2, 0, \"label\", 47)(216, UsersComponent_label_216_Template, 2, 0, \"label\", 47);\n            i0.ɵɵelementStart(217, \"input\", 48);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function UsersComponent_Template_input_ngModelChange_217_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.email, $event) || (ctx.email = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(218, \"label\", 49);\n            i0.ɵɵtext(219, \"Mobile : \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(220, UsersComponent_label_220_Template, 2, 0, \"label\", 47)(221, UsersComponent_label_221_Template, 2, 0, \"label\", 47);\n            i0.ɵɵelementStart(222, \"input\", 50);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function UsersComponent_Template_input_ngModelChange_222_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.Tel, $event) || (ctx.Tel = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(223, \"div\", 79)(224, \"label\", 65);\n            i0.ɵɵtext(225, \"Username : \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(226, UsersComponent_label_226_Template, 2, 0, \"label\", 47);\n            i0.ɵɵelementStart(227, \"input\", 80);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function UsersComponent_Template_input_ngModelChange_227_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.username, $event) || (ctx.username = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(228, \"div\", 56)(229, \"label\", 68);\n            i0.ɵɵtext(230, \"Password : \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(231, UsersComponent_label_231_Template, 2, 0, \"label\", 47);\n            i0.ɵɵelement(232, \"input\", 81);\n            i0.ɵɵelementStart(233, \"label\", 70);\n            i0.ɵɵtext(234, \"Repeat Password : \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(235, UsersComponent_label_235_Template, 2, 0, \"label\", 47);\n            i0.ɵɵelement(236, \"input\", 82);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵtemplate(237, UsersComponent_p_237_Template, 2, 0, \"p\", 72);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(238, \"div\", 51)(239, \"button\", 52);\n            i0.ɵɵlistener(\"click\", function UsersComponent_Template_button_click_239_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.cn());\n            });\n            i0.ɵɵtext(240, \"Close\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(241, \"button\", 53);\n            i0.ɵɵlistener(\"click\", function UsersComponent_Template_button_click_241_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.onClickRegister());\n            });\n            i0.ɵɵtext(242, \"Save changes\");\n            i0.ɵɵelementEnd()()()()();\n            i0.ɵɵelementStart(243, \"div\", 83)(244, \"div\", 32)(245, \"div\", 33)(246, \"div\", 34)(247, \"h5\", 35);\n            i0.ɵɵtext(248, \"Edist User\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(249, \"button\", 55)(250, \"span\", 37);\n            i0.ɵɵtext(251, \"\\u00D7\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(252, \"div\", 38)(253, \"form\", 39);\n            i0.ɵɵlistener(\"ngSubmit\", function UsersComponent_Template_form_ngSubmit_253_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.onClickEdUser());\n            });\n            i0.ɵɵelementStart(254, \"label\", 57);\n            i0.ɵɵtext(255, \"Select Group : \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(256, \"select\", 84);\n            i0.ɵɵlistener(\"change\", function UsersComponent_Template_select_change_256_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.selectvat($event.target.value));\n            });\n            i0.ɵɵtemplate(257, UsersComponent_option_257_Template, 2, 2, \"option\", 43)(258, UsersComponent_option_258_Template, 2, 2, \"option\", 59);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(259, \"div\", 45)(260, \"label\", 57);\n            i0.ɵɵtext(261, \"Select GroupSalesman : \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(262, \"select\", 84);\n            i0.ɵɵlistener(\"change\", function UsersComponent_Template_select_change_262_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.selectgroupsaleman($event.target.value));\n            });\n            i0.ɵɵtemplate(263, UsersComponent_option_263_Template, 2, 3, \"option\", 43)(264, UsersComponent_option_264_Template, 2, 3, \"option\", 59);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(265, \"label\", 57);\n            i0.ɵɵtext(266, \"Name : \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(267, UsersComponent_label_267_Template, 2, 0, \"label\", 47);\n            i0.ɵɵelementStart(268, \"input\", 85);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function UsersComponent_Template_input_ngModelChange_268_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.edname_user, $event) || (ctx.edname_user = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(269, \"label\", 46);\n            i0.ɵɵtext(270, \"Email : \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(271, UsersComponent_label_271_Template, 2, 0, \"label\", 47)(272, UsersComponent_label_272_Template, 2, 0, \"label\", 47);\n            i0.ɵɵelementStart(273, \"input\", 63);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function UsersComponent_Template_input_ngModelChange_273_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.edemail, $event) || (ctx.edemail = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(274, \"label\", 49);\n            i0.ɵɵtext(275, \"Mobile : \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(276, UsersComponent_label_276_Template, 2, 0, \"label\", 47)(277, UsersComponent_label_277_Template, 2, 0, \"label\", 47);\n            i0.ɵɵelementStart(278, \"input\", 64);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function UsersComponent_Template_input_ngModelChange_278_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.edmobile, $event) || (ctx.edmobile = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(279, \"label\", 65);\n            i0.ɵɵtext(280, \"Username : \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(281, UsersComponent_label_281_Template, 2, 0, \"label\", 47);\n            i0.ɵɵelementStart(282, \"input\", 66);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function UsersComponent_Template_input_ngModelChange_282_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.edusername, $event) || (ctx.edusername = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(283, \"div\", 67)(284, \"label\", 68);\n            i0.ɵɵtext(285, \"Password : \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(286, UsersComponent_label_286_Template, 2, 0, \"label\", 47);\n            i0.ɵɵelementStart(287, \"input\", 86);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function UsersComponent_Template_input_ngModelChange_287_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.Edpassword, $event) || (ctx.Edpassword = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(288, \"label\", 70);\n            i0.ɵɵtext(289, \"Repeat Password : \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(290, UsersComponent_label_290_Template, 2, 0, \"label\", 47);\n            i0.ɵɵelementStart(291, \"input\", 87);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function UsersComponent_Template_input_ngModelChange_291_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.EdrepeatPassword, $event) || (ctx.EdrepeatPassword = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵtemplate(292, UsersComponent_p_292_Template, 2, 0, \"p\", 72);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(293, \"div\", 51)(294, \"button\", 52);\n            i0.ɵɵlistener(\"click\", function UsersComponent_Template_button_click_294_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.cn());\n            });\n            i0.ɵɵtext(295, \"Close\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(296, \"button\", 53);\n            i0.ɵɵlistener(\"click\", function UsersComponent_Template_button_click_296_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.onClickEdUser());\n            });\n            i0.ɵɵtext(297, \"Save changes\");\n            i0.ɵɵelementEnd()()()()();\n            i0.ɵɵelementStart(298, \"div\", 88)(299, \"div\", 32)(300, \"div\", 33)(301, \"div\", 34)(302, \"h5\", 35);\n            i0.ɵɵtext(303, \"Delete User\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(304, \"button\", 55)(305, \"span\", 37);\n            i0.ɵɵtext(306, \"\\u00D7\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(307, \"div\", 38);\n            i0.ɵɵtext(308);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(309, \"div\", 51)(310, \"button\", 74);\n            i0.ɵɵtext(311, \"Close\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(312, \"button\", 75);\n            i0.ɵɵlistener(\"click\", function UsersComponent_Template_button_click_312_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.Deleteuser());\n            });\n            i0.ɵɵtext(313, \"Delete\");\n            i0.ɵɵelementEnd()()()()();\n            i0.ɵɵelementStart(314, \"div\", 89)(315, \"div\", 32)(316, \"div\", 33)(317, \"div\", 34)(318, \"h5\", 35);\n            i0.ɵɵtext(319, \"Edit Group User\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(320, \"button\", 36);\n            i0.ɵɵlistener(\"click\", function UsersComponent_Template_button_click_320_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.Cset());\n            });\n            i0.ɵɵelementStart(321, \"span\", 37);\n            i0.ɵɵtext(322, \"\\u00D7\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(323, \"div\", 38)(324, \"label\", 90);\n            i0.ɵɵtext(325, \"Name Group : \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(326, \"input\", 91);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function UsersComponent_Template_input_ngModelChange_326_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.setdes_group, $event) || (ctx.setdes_group = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(327, \"div\", 92)(328, \"input\", 93);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function UsersComponent_Template_input_ngModelChange_328_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.chdel, $event) || (ctx.chdel = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵlistener(\"click\", function UsersComponent_Template_input_click_328_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.opendelete($event.target.checked));\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(329, \"label\", 94);\n            i0.ɵɵtext(330, \"Would you like to delete a group?\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(331, \"div\", 51)(332, \"button\", 95);\n            i0.ɵɵlistener(\"click\", function UsersComponent_Template_button_click_332_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.deleteGroup());\n            });\n            i0.ɵɵtext(333, \"Delete\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(334, \"button\", 52);\n            i0.ɵɵlistener(\"click\", function UsersComponent_Template_button_click_334_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.Cset());\n            });\n            i0.ɵɵtext(335, \"Close\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(336, \"button\", 75);\n            i0.ɵɵlistener(\"click\", function UsersComponent_Template_button_click_336_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.SaveGroup());\n            });\n            i0.ɵɵtext(337, \"Save changes\");\n            i0.ɵɵelementEnd()()()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngIf\", !ctx.staticAlertClosed);\n            i0.ɵɵadvance(11);\n            i0.ɵɵproperty(\"ngForOf\", ctx.usergroups);\n            i0.ɵɵadvance(30);\n            i0.ɵɵproperty(\"ngForOf\", ctx.userlogins);\n            i0.ɵɵadvance(26);\n            i0.ɵɵproperty(\"ngForOf\", ctx.userCustomer);\n            i0.ɵɵadvance(15);\n            i0.ɵɵproperty(\"formGroup\", ctx.customerFormGroup);\n            i0.ɵɵadvance(3);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedValue);\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"ngForOf\", ctx.usergroups);\n            i0.ɵɵadvance(3);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedValuecustomer);\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"ngForOf\", ctx.usergroupCustomer);\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"ngIf\", ctx.customerFormGroup.controls[\"email\"].errors[\"pattern\"] && ctx.customerFormGroup.controls[\"email\"].touched);\n            i0.ɵɵadvance();\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.email);\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"ngIf\", ctx.customerFormGroup.controls[\"Tel\"].errors[\"pattern\"] && ctx.customerFormGroup.controls[\"Tel\"].touched);\n            i0.ɵɵadvance();\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.Tel);\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"disabled\", !ctx.customerFormGroup.valid);\n            i0.ɵɵadvance(12);\n            i0.ɵɵproperty(\"formGroup\", ctx.EdcustomerFormGroup);\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"ngForOf\", ctx.DateGroupNone);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngForOf\", ctx.DateGroupHave);\n            i0.ɵɵadvance(6);\n            i0.ɵɵproperty(\"ngForOf\", ctx.DateGroupNonesaleman);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngForOf\", ctx.DateGroupHavesaleman);\n            i0.ɵɵadvance(3);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.edname_user);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"formGroup\", ctx.EdcustomerFormGroup2);\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"ngIf\", ctx.EdcustomerFormGroup2.controls[\"edemail\"].errors[\"pattern\"] && ctx.EdcustomerFormGroup2.controls[\"edemail\"].touched);\n            i0.ɵɵadvance();\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.edemail);\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"ngIf\", ctx.EdcustomerFormGroup2.controls[\"edmobile\"].errors[\"pattern\"] && ctx.EdcustomerFormGroup2.controls[\"edmobile\"].touched);\n            i0.ɵɵadvance();\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.edmobile);\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"ngIf\", ctx.EdcustomerFormGroup.controls[\"edusername\"].errors[\"required\"] && ctx.EdcustomerFormGroup.controls[\"edusername\"].touched);\n            i0.ɵɵadvance();\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.edusername);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"formGroup\", ctx.EdpasswordFormGroup2);\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"ngIf\", ctx.EdpasswordFormGroup2.controls[\"Edpassword2\"].errors[\"required\"] && ctx.EdpasswordFormGroup2.controls[\"Edpassword2\"].touched);\n            i0.ɵɵadvance();\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.Edpassword2);\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"ngIf\", ctx.EdpasswordFormGroup2.controls[\"EdrepeatPassword2\"].errors[\"required\"] && ctx.EdpasswordFormGroup2.controls[\"EdrepeatPassword2\"].touched);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngIf\", ctx.EdpasswordFormGroup2.errors[\"doesMatchPassword\"]);\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"disabled\", !ctx.EdcustomerFormGroup.valid);\n            i0.ɵɵadvance(12);\n            i0.ɵɵtextInterpolate1(\" Would you like to delete. \", ctx.namedel, \" \");\n            i0.ɵɵadvance(2);\n            i0.ɵɵtextInterpolate1(\" \", ctx.nameshowdelete, \" \");\n            i0.ɵɵadvance(16);\n            i0.ɵɵproperty(\"formGroup\", ctx.registrationFormGroup);\n            i0.ɵɵadvance(3);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedValue);\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"ngForOf\", ctx.usergroups);\n            i0.ɵɵadvance(3);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedValuesaleman);\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"ngForOf\", ctx.DateGroupsaleman);\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"ngIf\", ctx.registrationFormGroup.controls[\"name_user\"].errors[\"required\"] && ctx.registrationFormGroup.controls[\"name_user\"].touched);\n            i0.ɵɵadvance();\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.name_user);\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"ngIf\", ctx.registrationFormGroup.controls[\"email\"].errors[\"required\"] && ctx.registrationFormGroup.controls[\"email\"].touched);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.registrationFormGroup.controls[\"email\"].errors[\"pattern\"] && ctx.registrationFormGroup.controls[\"email\"].touched);\n            i0.ɵɵadvance();\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.email);\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"ngIf\", ctx.registrationFormGroup.controls[\"Tel\"].errors[\"required\"] && ctx.registrationFormGroup.controls[\"Tel\"].touched);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.registrationFormGroup.controls[\"Tel\"].errors[\"pattern\"] && ctx.registrationFormGroup.controls[\"Tel\"].touched);\n            i0.ɵɵadvance();\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.Tel);\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"ngIf\", ctx.registrationFormGroup.controls[\"username\"].errors[\"required\"] && ctx.registrationFormGroup.controls[\"username\"].touched);\n            i0.ɵɵadvance();\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.username);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"formGroup\", ctx.passwordFormGroup);\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"ngIf\", ctx.passwordFormGroup.controls[\"password\"].errors[\"required\"] && ctx.passwordFormGroup.controls[\"password\"].touched);\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"ngIf\", ctx.passwordFormGroup.controls[\"repeatPassword\"].errors[\"required\"] && ctx.passwordFormGroup.controls[\"repeatPassword\"].touched);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngIf\", ctx.passwordFormGroup.errors[\"doesMatchPassword\"]);\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"disabled\", !ctx.registrationFormGroup.valid);\n            i0.ɵɵadvance(12);\n            i0.ɵɵproperty(\"formGroup\", ctx.EditFormGrouped);\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"ngForOf\", ctx.DateGroupNone);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngForOf\", ctx.DateGroupHave);\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"ngForOf\", ctx.DateGroupNonesaleman);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngForOf\", ctx.DateGroupHavesaleman);\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"ngIf\", ctx.EditFormGrouped.controls[\"edname_user\"].errors[\"required\"] && ctx.EditFormGrouped.controls[\"edname_user\"].touched);\n            i0.ɵɵadvance();\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.edname_user);\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"ngIf\", ctx.EditFormGrouped.controls[\"edemail\"].errors[\"required\"] && ctx.EditFormGrouped.controls[\"edemail\"].touched);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.EditFormGrouped.controls[\"edemail\"].errors[\"pattern\"] && ctx.EditFormGrouped.controls[\"edemail\"].touched);\n            i0.ɵɵadvance();\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.edemail);\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"ngIf\", ctx.EditFormGrouped.controls[\"edmobile\"].errors[\"required\"] && ctx.EditFormGrouped.controls[\"edmobile\"].touched);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.EditFormGrouped.controls[\"edmobile\"].errors[\"pattern\"] && ctx.EditFormGrouped.controls[\"edmobile\"].touched);\n            i0.ɵɵadvance();\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.edmobile);\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"ngIf\", ctx.EditFormGrouped.controls[\"edusername\"].errors[\"required\"] && ctx.EditFormGrouped.controls[\"edusername\"].touched);\n            i0.ɵɵadvance();\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.edusername);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"formGroup\", ctx.EdpasswordFormGroup);\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"ngIf\", ctx.EdpasswordFormGroup.controls[\"Edpassword\"].errors[\"required\"] && ctx.EdpasswordFormGroup.controls[\"Edpassword\"].touched);\n            i0.ɵɵadvance();\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.Edpassword);\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"ngIf\", ctx.EdpasswordFormGroup.controls[\"EdrepeatPassword\"].errors[\"required\"] && ctx.EdpasswordFormGroup.controls[\"EdrepeatPassword\"].touched);\n            i0.ɵɵadvance();\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.EdrepeatPassword);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.EdpasswordFormGroup.errors[\"doesMatchPassword\"]);\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"disabled\", !ctx.EditFormGrouped.valid);\n            i0.ɵɵadvance(12);\n            i0.ɵɵtextInterpolate1(\" Would you like to delete. \", ctx.namedel, \" \");\n            i0.ɵɵadvance(18);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.setdes_group);\n            i0.ɵɵadvance(2);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.chdel);\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"disabled\", ctx.chdel == false);\n          }\n        },\n        dependencies: [i5.NgForOf, i5.NgIf, i6.ɵNgNoValidate, i6.NgSelectOption, i6.ɵNgSelectMultipleOption, i6.DefaultValueAccessor, i6.CheckboxControlValueAccessor, i6.SelectControlValueAccessor, i6.NgControlStatus, i6.NgControlStatusGroup, i6.RequiredValidator, i6.PatternValidator, i6.NgModel, i6.NgModelGroup, i6.NgForm, i6.FormGroupDirective, i6.FormControlName, i3.NgbAlert, i7.TopmenuComponent],\n        styles: [\".ng-valid[required],.ng-valid.required{border-left:5px solid #42A948}.ng-invalid:not(form){border-left:5px solid #a94442}.test{color:red;background:#42a948}\\n\", \".bg-alert{color:red}.alert{position:relative;padding:.75rem 1.25rem;margin-bottom:5px;border:1px solid transparent;border-radius:.25rem}\\n\"],\n        encapsulation: 2\n      });\n    }\n  }\n  return UsersComponent;\n})();\nexport class RegistrationValidator {\n  static validate(registrationFormGroup) {\n    let password = registrationFormGroup.controls[\"password\"].value;\n    let repeatPassword = registrationFormGroup.controls[\"repeatPassword\"].value;\n    if (repeatPassword.length <= 0) {\n      return null;\n    }\n    if (repeatPassword !== password) {\n      return {\n        doesMatchPassword: true\n      };\n    }\n    return null;\n  }\n}\nexport class EdRegistrationValidator {\n  static validate(EditFormGrouped) {\n    let Edpassword = EditFormGrouped.controls[\"Edpassword\"].value;\n    let EdrepeatPassword = EditFormGrouped.controls[\"EdrepeatPassword\"].value;\n    if (EdrepeatPassword.length <= 0) {\n      return null;\n    }\n    if (EdrepeatPassword !== Edpassword) {\n      return {\n        doesMatchPassword: true\n      };\n    }\n    return null;\n  }\n}\nexport class EdRegistrationValidator2 {\n  static validate(EditFormGrouped2) {\n    let Edpassword2 = EditFormGrouped2.controls[\"Edpassword2\"].value;\n    let EdrepeatPassword2 = EditFormGrouped2.controls[\"EdrepeatPassword2\"].value;\n    if (EdrepeatPassword2.length <= 0) {\n      return null;\n    }\n    if (EdrepeatPassword2 !== Edpassword2) {\n      return {\n        doesMatchPassword: true\n      };\n    }\n    return null;\n  }\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}