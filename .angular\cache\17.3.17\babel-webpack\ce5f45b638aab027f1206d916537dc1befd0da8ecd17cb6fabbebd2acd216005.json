{"ast": null, "code": "/*!\n\npica\nhttps://github.com/nodeca/pica\n\n*/\n\n(function (f) {\n  if (typeof exports === \"object\" && typeof module !== \"undefined\") {\n    module.exports = f();\n  } else if (typeof define === \"function\" && define.amd) {\n    define([], f);\n  } else {\n    var g;\n    if (typeof window !== \"undefined\") {\n      g = window;\n    } else if (typeof global !== \"undefined\") {\n      g = global;\n    } else if (typeof self !== \"undefined\") {\n      g = self;\n    } else {\n      g = this;\n    }\n    g.pica = f();\n  }\n})(function () {\n  var define, module, exports;\n  return function () {\n    function r(e, n, t) {\n      function o(i, f) {\n        if (!n[i]) {\n          if (!e[i]) {\n            var c = \"function\" == typeof require && require;\n            if (!f && c) return c(i, !0);\n            if (u) return u(i, !0);\n            var a = new Error(\"Cannot find module '\" + i + \"'\");\n            throw a.code = \"MODULE_NOT_FOUND\", a;\n          }\n          var p = n[i] = {\n            exports: {}\n          };\n          e[i][0].call(p.exports, function (r) {\n            var n = e[i][1][r];\n            return o(n || r);\n          }, p, p.exports, r, e, n, t);\n        }\n        return n[i].exports;\n      }\n      for (var u = \"function\" == typeof require && require, i = 0; i < t.length; i++) o(t[i]);\n      return o;\n    }\n    return r;\n  }()({\n    1: [function (_dereq_, module, exports) {\n      // Collection of math functions\n      //\n      // 1. Combine components together\n      // 2. Has async init to load wasm modules\n      //\n      'use strict';\n\n      var Multimath = _dereq_('multimath');\n      var mm_unsharp_mask = _dereq_('./mm_unsharp_mask');\n      var mm_resize = _dereq_('./mm_resize');\n      function MathLib(requested_features) {\n        var __requested_features = requested_features || [];\n        var features = {\n          js: __requested_features.indexOf('js') >= 0,\n          wasm: __requested_features.indexOf('wasm') >= 0\n        };\n        Multimath.call(this, features);\n        this.features = {\n          js: features.js,\n          wasm: features.wasm && this.has_wasm()\n        };\n        this.use(mm_unsharp_mask);\n        this.use(mm_resize);\n      }\n      MathLib.prototype = Object.create(Multimath.prototype);\n      MathLib.prototype.constructor = MathLib;\n      MathLib.prototype.resizeAndUnsharp = function resizeAndUnsharp(options, cache) {\n        var result = this.resize(options, cache);\n        if (options.unsharpAmount) {\n          this.unsharp_mask(result, options.toWidth, options.toHeight, options.unsharpAmount, options.unsharpRadius, options.unsharpThreshold);\n        }\n        return result;\n      };\n      module.exports = MathLib;\n    }, {\n      \"./mm_resize\": 4,\n      \"./mm_unsharp_mask\": 9,\n      \"multimath\": 19\n    }],\n    2: [function (_dereq_, module, exports) {\n      // Resize convolvers, pure JS implementation\n      //\n      'use strict';\n\n      // Precision of fixed FP values\n      //var FIXED_FRAC_BITS = 14;\n      function clampTo8(i) {\n        return i < 0 ? 0 : i > 255 ? 255 : i;\n      }\n      function clampNegative(i) {\n        return i >= 0 ? i : 0;\n      } // Convolve image data in horizontal direction. Can be used for:\n      //\n      // 1. bitmap with premultiplied alpha\n      // 2. bitmap without alpha (all values 255)\n      //\n      // Notes:\n      //\n      // - output is transposed\n      // - output resolution is ~15 bits per channel(for better precision).\n      //\n\n      function convolveHor(src, dest, srcW, srcH, destW, filters) {\n        var r, g, b, a;\n        var filterPtr, filterShift, filterSize;\n        var srcPtr, srcY, destX, filterVal;\n        var srcOffset = 0,\n          destOffset = 0; // For each row\n\n        for (srcY = 0; srcY < srcH; srcY++) {\n          filterPtr = 0; // Apply precomputed filters to each destination row point\n\n          for (destX = 0; destX < destW; destX++) {\n            // Get the filter that determines the current output pixel.\n            filterShift = filters[filterPtr++];\n            filterSize = filters[filterPtr++];\n            srcPtr = srcOffset + filterShift * 4 | 0;\n            r = g = b = a = 0; // Apply the filter to the row to get the destination pixel r, g, b, a\n\n            for (; filterSize > 0; filterSize--) {\n              filterVal = filters[filterPtr++]; // Use reverse order to workaround deopts in old v8 (node v.10)\n              // Big thanks to @mraleph (Vyacheslav Egorov) for the tip.\n\n              a = a + filterVal * src[srcPtr + 3] | 0;\n              b = b + filterVal * src[srcPtr + 2] | 0;\n              g = g + filterVal * src[srcPtr + 1] | 0;\n              r = r + filterVal * src[srcPtr] | 0;\n              srcPtr = srcPtr + 4 | 0;\n            } // Store 15 bits between passes for better precision\n            // Instead of shift to 14 (FIXED_FRAC_BITS), shift to 7 only\n            //\n\n            dest[destOffset + 3] = clampNegative(a >> 7);\n            dest[destOffset + 2] = clampNegative(b >> 7);\n            dest[destOffset + 1] = clampNegative(g >> 7);\n            dest[destOffset] = clampNegative(r >> 7);\n            destOffset = destOffset + srcH * 4 | 0;\n          }\n          destOffset = (srcY + 1) * 4 | 0;\n          srcOffset = (srcY + 1) * srcW * 4 | 0;\n        }\n      } // Supplementary method for `convolveHor()`\n      //\n\n      function convolveVert(src, dest, srcW, srcH, destW, filters) {\n        var r, g, b, a;\n        var filterPtr, filterShift, filterSize;\n        var srcPtr, srcY, destX, filterVal;\n        var srcOffset = 0,\n          destOffset = 0; // For each row\n\n        for (srcY = 0; srcY < srcH; srcY++) {\n          filterPtr = 0; // Apply precomputed filters to each destination row point\n\n          for (destX = 0; destX < destW; destX++) {\n            // Get the filter that determines the current output pixel.\n            filterShift = filters[filterPtr++];\n            filterSize = filters[filterPtr++];\n            srcPtr = srcOffset + filterShift * 4 | 0;\n            r = g = b = a = 0; // Apply the filter to the row to get the destination pixel r, g, b, a\n\n            for (; filterSize > 0; filterSize--) {\n              filterVal = filters[filterPtr++]; // Use reverse order to workaround deopts in old v8 (node v.10)\n              // Big thanks to @mraleph (Vyacheslav Egorov) for the tip.\n\n              a = a + filterVal * src[srcPtr + 3] | 0;\n              b = b + filterVal * src[srcPtr + 2] | 0;\n              g = g + filterVal * src[srcPtr + 1] | 0;\n              r = r + filterVal * src[srcPtr] | 0;\n              srcPtr = srcPtr + 4 | 0;\n            } // Sync with premultiplied version for exact result match\n\n            r >>= 7;\n            g >>= 7;\n            b >>= 7;\n            a >>= 7; // Bring this value back in range + round result.\n            //\n\n            dest[destOffset + 3] = clampTo8(a + (1 << 13) >> 14);\n            dest[destOffset + 2] = clampTo8(b + (1 << 13) >> 14);\n            dest[destOffset + 1] = clampTo8(g + (1 << 13) >> 14);\n            dest[destOffset] = clampTo8(r + (1 << 13) >> 14);\n            destOffset = destOffset + srcH * 4 | 0;\n          }\n          destOffset = (srcY + 1) * 4 | 0;\n          srcOffset = (srcY + 1) * srcW * 4 | 0;\n        }\n      } // Premultiply & convolve image data in horizontal direction. Can be used for:\n      //\n      // - Any bitmap data, extracted with `.getImageData()` method (with\n      //   non-premultiplied alpha)\n      //\n      // For images without alpha channel this method is slower than `convolveHor()`\n      //\n\n      function convolveHorWithPre(src, dest, srcW, srcH, destW, filters) {\n        var r, g, b, a, alpha;\n        var filterPtr, filterShift, filterSize;\n        var srcPtr, srcY, destX, filterVal;\n        var srcOffset = 0,\n          destOffset = 0; // For each row\n\n        for (srcY = 0; srcY < srcH; srcY++) {\n          filterPtr = 0; // Apply precomputed filters to each destination row point\n\n          for (destX = 0; destX < destW; destX++) {\n            // Get the filter that determines the current output pixel.\n            filterShift = filters[filterPtr++];\n            filterSize = filters[filterPtr++];\n            srcPtr = srcOffset + filterShift * 4 | 0;\n            r = g = b = a = 0; // Apply the filter to the row to get the destination pixel r, g, b, a\n\n            for (; filterSize > 0; filterSize--) {\n              filterVal = filters[filterPtr++]; // Use reverse order to workaround deopts in old v8 (node v.10)\n              // Big thanks to @mraleph (Vyacheslav Egorov) for the tip.\n\n              alpha = src[srcPtr + 3];\n              a = a + filterVal * alpha | 0;\n              b = b + filterVal * src[srcPtr + 2] * alpha | 0;\n              g = g + filterVal * src[srcPtr + 1] * alpha | 0;\n              r = r + filterVal * src[srcPtr] * alpha | 0;\n              srcPtr = srcPtr + 4 | 0;\n            } // Premultiply is (* alpha / 255).\n            // Postpone division for better performance\n\n            b = b / 255 | 0;\n            g = g / 255 | 0;\n            r = r / 255 | 0; // Store 15 bits between passes for better precision\n            // Instead of shift to 14 (FIXED_FRAC_BITS), shift to 7 only\n            //\n\n            dest[destOffset + 3] = clampNegative(a >> 7);\n            dest[destOffset + 2] = clampNegative(b >> 7);\n            dest[destOffset + 1] = clampNegative(g >> 7);\n            dest[destOffset] = clampNegative(r >> 7);\n            destOffset = destOffset + srcH * 4 | 0;\n          }\n          destOffset = (srcY + 1) * 4 | 0;\n          srcOffset = (srcY + 1) * srcW * 4 | 0;\n        }\n      } // Supplementary method for `convolveHorWithPre()`\n      //\n\n      function convolveVertWithPre(src, dest, srcW, srcH, destW, filters) {\n        var r, g, b, a;\n        var filterPtr, filterShift, filterSize;\n        var srcPtr, srcY, destX, filterVal;\n        var srcOffset = 0,\n          destOffset = 0; // For each row\n\n        for (srcY = 0; srcY < srcH; srcY++) {\n          filterPtr = 0; // Apply precomputed filters to each destination row point\n\n          for (destX = 0; destX < destW; destX++) {\n            // Get the filter that determines the current output pixel.\n            filterShift = filters[filterPtr++];\n            filterSize = filters[filterPtr++];\n            srcPtr = srcOffset + filterShift * 4 | 0;\n            r = g = b = a = 0; // Apply the filter to the row to get the destination pixel r, g, b, a\n\n            for (; filterSize > 0; filterSize--) {\n              filterVal = filters[filterPtr++]; // Use reverse order to workaround deopts in old v8 (node v.10)\n              // Big thanks to @mraleph (Vyacheslav Egorov) for the tip.\n\n              a = a + filterVal * src[srcPtr + 3] | 0;\n              b = b + filterVal * src[srcPtr + 2] | 0;\n              g = g + filterVal * src[srcPtr + 1] | 0;\n              r = r + filterVal * src[srcPtr] | 0;\n              srcPtr = srcPtr + 4 | 0;\n            } // Downscale to leave room for un-premultiply\n\n            r >>= 7;\n            g >>= 7;\n            b >>= 7;\n            a >>= 7; // Un-premultiply\n\n            a = clampTo8(a + (1 << 13) >> 14);\n            if (a > 0) {\n              r = r * 255 / a | 0;\n              g = g * 255 / a | 0;\n              b = b * 255 / a | 0;\n            } // Bring this value back in range + round result.\n            // Shift value = FIXED_FRAC_BITS + 7\n            //\n\n            dest[destOffset + 3] = a;\n            dest[destOffset + 2] = clampTo8(b + (1 << 13) >> 14);\n            dest[destOffset + 1] = clampTo8(g + (1 << 13) >> 14);\n            dest[destOffset] = clampTo8(r + (1 << 13) >> 14);\n            destOffset = destOffset + srcH * 4 | 0;\n          }\n          destOffset = (srcY + 1) * 4 | 0;\n          srcOffset = (srcY + 1) * srcW * 4 | 0;\n        }\n      }\n      module.exports = {\n        convolveHor: convolveHor,\n        convolveVert: convolveVert,\n        convolveHorWithPre: convolveHorWithPre,\n        convolveVertWithPre: convolveVertWithPre\n      };\n    }, {}],\n    3: [function (_dereq_, module, exports) {\n      // This is autogenerated file from math.wasm, don't edit.\n      //\n      'use strict';\n\n      /* eslint-disable max-len */\n      module.exports = 'AGFzbQEAAAAADAZkeWxpbmsAAAAAAAEYA2AGf39/f39/AGAAAGAIf39/f39/f38AAg8BA2VudgZtZW1vcnkCAAADBwYBAAAAAAIGBgF/AEEACweUAQgRX193YXNtX2NhbGxfY3RvcnMAAAtjb252b2x2ZUhvcgABDGNvbnZvbHZlVmVydAACEmNvbnZvbHZlSG9yV2l0aFByZQADE2NvbnZvbHZlVmVydFdpdGhQcmUABApjb252b2x2ZUhWAAUMX19kc29faGFuZGxlAwAYX193YXNtX2FwcGx5X2RhdGFfcmVsb2NzAAAKyA4GAwABC4wDARB/AkAgA0UNACAERQ0AIANBAnQhFQNAQQAhE0EAIQsDQCALQQJqIQcCfyALQQF0IAVqIgYuAQIiC0UEQEEAIQhBACEGQQAhCUEAIQogBwwBCyASIAYuAQBqIQhBACEJQQAhCiALIRRBACEOIAchBkEAIQ8DQCAFIAZBAXRqLgEAIhAgACAIQQJ0aigCACIRQRh2bCAPaiEPIBFB/wFxIBBsIAlqIQkgEUEQdkH/AXEgEGwgDmohDiARQQh2Qf8BcSAQbCAKaiEKIAhBAWohCCAGQQFqIQYgFEEBayIUDQALIAlBB3UhCCAKQQd1IQYgDkEHdSEJIA9BB3UhCiAHIAtqCyELIAEgDEEBdCIHaiAIQQAgCEEAShs7AQAgASAHQQJyaiAGQQAgBkEAShs7AQAgASAHQQRyaiAJQQAgCUEAShs7AQAgASAHQQZyaiAKQQAgCkEAShs7AQAgDCAVaiEMIBNBAWoiEyAERw0ACyANQQFqIg0gAmwhEiANQQJ0IQwgAyANRw0ACwsL2gMBD38CQCADRQ0AIARFDQAgAkECdCEUA0AgCyEMQQAhE0EAIQIDQCACQQJqIQYCfyACQQF0IAVqIgcuAQIiAkUEQEEAIQhBACEHQQAhCkEAIQkgBgwBCyAHLgEAQQJ0IBJqIQhBACEJIAIhCkEAIQ0gBiEHQQAhDkEAIQ8DQCAFIAdBAXRqLgEAIhAgACAIQQF0IhFqLwEAbCAJaiEJIAAgEUEGcmovAQAgEGwgDmohDiAAIBFBBHJqLwEAIBBsIA9qIQ8gACARQQJyai8BACAQbCANaiENIAhBBGohCCAHQQFqIQcgCkEBayIKDQALIAlBB3UhCCANQQd1IQcgDkEHdSEKIA9BB3UhCSACIAZqCyECIAEgDEECdGogB0GAQGtBDnUiBkH/ASAGQf8BSBsiBkEAIAZBAEobQQh0QYD+A3EgCUGAQGtBDnUiBkH/ASAGQf8BSBsiBkEAIAZBAEobQRB0QYCA/AdxIApBgEBrQQ51IgZB/wEgBkH/AUgbIgZBACAGQQBKG0EYdHJyIAhBgEBrQQ51IgZB/wEgBkH/AUgbIgZBACAGQQBKG3I2AgAgAyAMaiEMIBNBAWoiEyAERw0ACyAUIAtBAWoiC2whEiADIAtHDQALCwuSAwEQfwJAIANFDQAgBEUNACADQQJ0IRUDQEEAIRNBACEGA0AgBkECaiEIAn8gBkEBdCAFaiIGLgECIgdFBEBBACEJQQAhDEEAIQ1BACEOIAgMAQsgEiAGLgEAaiEJQQAhDkEAIQ1BACEMIAchFEEAIQ8gCCEGA0AgBSAGQQF0ai4BACAAIAlBAnRqKAIAIhBBGHZsIhEgD2ohDyARIBBBEHZB/wFxbCAMaiEMIBEgEEEIdkH/AXFsIA1qIQ0gESAQQf8BcWwgDmohDiAJQQFqIQkgBkEBaiEGIBRBAWsiFA0ACyAPQQd1IQkgByAIagshBiABIApBAXQiCGogDkH/AW1BB3UiB0EAIAdBAEobOwEAIAEgCEECcmogDUH/AW1BB3UiB0EAIAdBAEobOwEAIAEgCEEEcmogDEH/AW1BB3UiB0EAIAdBAEobOwEAIAEgCEEGcmogCUEAIAlBAEobOwEAIAogFWohCiATQQFqIhMgBEcNAAsgC0EBaiILIAJsIRIgC0ECdCEKIAMgC0cNAAsLC4IEAQ9/AkAgA0UNACAERQ0AIAJBAnQhFANAIAshDEEAIRJBACEHA0AgB0ECaiEKAn8gB0EBdCAFaiICLgECIhNFBEBBACEIQQAhCUEAIQYgCiEHQQAMAQsgAi4BAEECdCARaiEJQQAhByATIQJBACENIAohBkEAIQ5BACEPA0AgBSAGQQF0ai4BACIIIAAgCUEBdCIQai8BAGwgB2ohByAAIBBBBnJqLwEAIAhsIA5qIQ4gACAQQQRyai8BACAIbCAPaiEPIAAgEEECcmovAQAgCGwgDWohDSAJQQRqIQkgBkEBaiEGIAJBAWsiAg0ACyAHQQd1IQggDUEHdSEJIA9BB3UhBiAKIBNqIQcgDkEHdQtBgEBrQQ51IgJB/wEgAkH/AUgbIgJBACACQQBKGyIKQf8BcQRAIAlB/wFsIAJtIQkgCEH/AWwgAm0hCCAGQf8BbCACbSEGCyABIAxBAnRqIAlBgEBrQQ51IgJB/wEgAkH/AUgbIgJBACACQQBKG0EIdEGA/gNxIAZBgEBrQQ51IgJB/wEgAkH/AUgbIgJBACACQQBKG0EQdEGAgPwHcSAKQRh0ciAIQYBAa0EOdSICQf8BIAJB/wFIGyICQQAgAkEAShtycjYCACADIAxqIQwgEkEBaiISIARHDQALIBQgC0EBaiILbCERIAMgC0cNAAsLC0AAIAcEQEEAIAIgAyAEIAUgABADIAJBACAEIAUgBiABEAQPC0EAIAIgAyAEIAUgABABIAJBACAEIAUgBiABEAIL';\n    }, {}],\n    4: [function (_dereq_, module, exports) {\n      'use strict';\n\n      module.exports = {\n        name: 'resize',\n        fn: _dereq_('./resize'),\n        wasm_fn: _dereq_('./resize_wasm'),\n        wasm_src: _dereq_('./convolve_wasm_base64')\n      };\n    }, {\n      \"./convolve_wasm_base64\": 3,\n      \"./resize\": 5,\n      \"./resize_wasm\": 8\n    }],\n    5: [function (_dereq_, module, exports) {\n      'use strict';\n\n      var createFilters = _dereq_('./resize_filter_gen');\n      var _require = _dereq_('./convolve'),\n        convolveHor = _require.convolveHor,\n        convolveVert = _require.convolveVert,\n        convolveHorWithPre = _require.convolveHorWithPre,\n        convolveVertWithPre = _require.convolveVertWithPre;\n      function hasAlpha(src, width, height) {\n        var ptr = 3,\n          len = width * height * 4 | 0;\n        while (ptr < len) {\n          if (src[ptr] !== 255) return true;\n          ptr = ptr + 4 | 0;\n        }\n        return false;\n      }\n      function resetAlpha(dst, width, height) {\n        var ptr = 3,\n          len = width * height * 4 | 0;\n        while (ptr < len) {\n          dst[ptr] = 0xFF;\n          ptr = ptr + 4 | 0;\n        }\n      }\n      module.exports = function resize(options) {\n        var src = options.src;\n        var srcW = options.width;\n        var srcH = options.height;\n        var destW = options.toWidth;\n        var destH = options.toHeight;\n        var scaleX = options.scaleX || options.toWidth / options.width;\n        var scaleY = options.scaleY || options.toHeight / options.height;\n        var offsetX = options.offsetX || 0;\n        var offsetY = options.offsetY || 0;\n        var dest = options.dest || new Uint8Array(destW * destH * 4);\n        var filter = typeof options.filter === 'undefined' ? 'mks2013' : options.filter;\n        var filtersX = createFilters(filter, srcW, destW, scaleX, offsetX),\n          filtersY = createFilters(filter, srcH, destH, scaleY, offsetY);\n        var tmp = new Uint16Array(destW * srcH * 4); // Autodetect if alpha channel exists, and use appropriate method\n\n        if (hasAlpha(src, srcW, srcH)) {\n          convolveHorWithPre(src, tmp, srcW, srcH, destW, filtersX);\n          convolveVertWithPre(tmp, dest, srcH, destW, destH, filtersY);\n        } else {\n          convolveHor(src, tmp, srcW, srcH, destW, filtersX);\n          convolveVert(tmp, dest, srcH, destW, destH, filtersY);\n          resetAlpha(dest, destW, destH);\n        }\n        return dest;\n      };\n    }, {\n      \"./convolve\": 2,\n      \"./resize_filter_gen\": 6\n    }],\n    6: [function (_dereq_, module, exports) {\n      // Calculate convolution filters for each destination point,\n      // and pack data to Int16Array:\n      //\n      // [ shift, length, data..., shift2, length2, data..., ... ]\n      //\n      // - shift - offset in src image\n      // - length - filter length (in src points)\n      // - data - filter values sequence\n      //\n      'use strict';\n\n      var FILTER_INFO = _dereq_('./resize_filter_info'); // Precision of fixed FP values\n\n      var FIXED_FRAC_BITS = 14;\n      function toFixedPoint(num) {\n        return Math.round(num * ((1 << FIXED_FRAC_BITS) - 1));\n      }\n      module.exports = function resizeFilterGen(filter, srcSize, destSize, scale, offset) {\n        var filterFunction = FILTER_INFO.filter[filter].fn;\n        var scaleInverted = 1.0 / scale;\n        var scaleClamped = Math.min(1.0, scale); // For upscale\n        // Filter window (averaging interval), scaled to src image\n\n        var srcWindow = FILTER_INFO.filter[filter].win / scaleClamped;\n        var destPixel, srcPixel, srcFirst, srcLast, filterElementSize, floatFilter, fxpFilter, total, pxl, idx, floatVal, filterTotal, filterVal;\n        var leftNotEmpty, rightNotEmpty, filterShift, filterSize;\n        var maxFilterElementSize = Math.floor((srcWindow + 1) * 2);\n        var packedFilter = new Int16Array((maxFilterElementSize + 2) * destSize);\n        var packedFilterPtr = 0;\n        var slowCopy = !packedFilter.subarray || !packedFilter.set; // For each destination pixel calculate source range and built filter values\n\n        for (destPixel = 0; destPixel < destSize; destPixel++) {\n          // Scaling should be done relative to central pixel point\n          srcPixel = (destPixel + 0.5) * scaleInverted + offset;\n          srcFirst = Math.max(0, Math.floor(srcPixel - srcWindow));\n          srcLast = Math.min(srcSize - 1, Math.ceil(srcPixel + srcWindow));\n          filterElementSize = srcLast - srcFirst + 1;\n          floatFilter = new Float32Array(filterElementSize);\n          fxpFilter = new Int16Array(filterElementSize);\n          total = 0.0; // Fill filter values for calculated range\n\n          for (pxl = srcFirst, idx = 0; pxl <= srcLast; pxl++, idx++) {\n            floatVal = filterFunction((pxl + 0.5 - srcPixel) * scaleClamped);\n            total += floatVal;\n            floatFilter[idx] = floatVal;\n          } // Normalize filter, convert to fixed point and accumulate conversion error\n\n          filterTotal = 0;\n          for (idx = 0; idx < floatFilter.length; idx++) {\n            filterVal = floatFilter[idx] / total;\n            filterTotal += filterVal;\n            fxpFilter[idx] = toFixedPoint(filterVal);\n          } // Compensate normalization error, to minimize brightness drift\n\n          fxpFilter[destSize >> 1] += toFixedPoint(1.0 - filterTotal); //\n          // Now pack filter to useable form\n          //\n          // 1. Trim heading and tailing zero values, and compensate shitf/length\n          // 2. Put all to single array in this format:\n          //\n          //    [ pos shift, data length, value1, value2, value3, ... ]\n          //\n\n          leftNotEmpty = 0;\n          while (leftNotEmpty < fxpFilter.length && fxpFilter[leftNotEmpty] === 0) {\n            leftNotEmpty++;\n          }\n          if (leftNotEmpty < fxpFilter.length) {\n            rightNotEmpty = fxpFilter.length - 1;\n            while (rightNotEmpty > 0 && fxpFilter[rightNotEmpty] === 0) {\n              rightNotEmpty--;\n            }\n            filterShift = srcFirst + leftNotEmpty;\n            filterSize = rightNotEmpty - leftNotEmpty + 1;\n            packedFilter[packedFilterPtr++] = filterShift; // shift\n\n            packedFilter[packedFilterPtr++] = filterSize; // size\n\n            if (!slowCopy) {\n              packedFilter.set(fxpFilter.subarray(leftNotEmpty, rightNotEmpty + 1), packedFilterPtr);\n              packedFilterPtr += filterSize;\n            } else {\n              // fallback for old IE < 11, without subarray/set methods\n              for (idx = leftNotEmpty; idx <= rightNotEmpty; idx++) {\n                packedFilter[packedFilterPtr++] = fxpFilter[idx];\n              }\n            }\n          } else {\n            // zero data, write header only\n            packedFilter[packedFilterPtr++] = 0; // shift\n\n            packedFilter[packedFilterPtr++] = 0; // size\n          }\n        }\n        return packedFilter;\n      };\n    }, {\n      \"./resize_filter_info\": 7\n    }],\n    7: [function (_dereq_, module, exports) {\n      // Filter definitions to build tables for\n      // resizing convolvers.\n      //\n      // Presets for quality 0..3. Filter functions + window size\n      //\n      'use strict';\n\n      var filter = {\n        // Nearest neibor\n        box: {\n          win: 0.5,\n          fn: function fn(x) {\n            if (x < 0) x = -x;\n            return x < 0.5 ? 1.0 : 0.0;\n          }\n        },\n        // // Hamming\n        hamming: {\n          win: 1.0,\n          fn: function fn(x) {\n            if (x < 0) x = -x;\n            if (x >= 1.0) {\n              return 0.0;\n            }\n            if (x < 1.19209290E-07) {\n              return 1.0;\n            }\n            var xpi = x * Math.PI;\n            return Math.sin(xpi) / xpi * (0.54 + 0.46 * Math.cos(xpi / 1.0));\n          }\n        },\n        // Lanczos, win = 2\n        lanczos2: {\n          win: 2.0,\n          fn: function fn(x) {\n            if (x < 0) x = -x;\n            if (x >= 2.0) {\n              return 0.0;\n            }\n            if (x < 1.19209290E-07) {\n              return 1.0;\n            }\n            var xpi = x * Math.PI;\n            return Math.sin(xpi) / xpi * Math.sin(xpi / 2.0) / (xpi / 2.0);\n          }\n        },\n        // Lanczos, win = 3\n        lanczos3: {\n          win: 3.0,\n          fn: function fn(x) {\n            if (x < 0) x = -x;\n            if (x >= 3.0) {\n              return 0.0;\n            }\n            if (x < 1.19209290E-07) {\n              return 1.0;\n            }\n            var xpi = x * Math.PI;\n            return Math.sin(xpi) / xpi * Math.sin(xpi / 3.0) / (xpi / 3.0);\n          }\n        },\n        // Magic Kernel Sharp 2013, win = 2.5\n        // http://johncostella.com/magic/\n        mks2013: {\n          win: 2.5,\n          fn: function fn(x) {\n            if (x < 0) x = -x;\n            if (x >= 2.5) {\n              return 0.0;\n            }\n            if (x >= 1.5) {\n              return -0.125 * (x - 2.5) * (x - 2.5);\n            }\n            if (x >= 0.5) {\n              return 0.25 * (4 * x * x - 11 * x + 7);\n            }\n            return 1.0625 - 1.75 * x * x;\n          }\n        }\n      };\n      module.exports = {\n        filter: filter,\n        // Legacy mapping\n        f2q: {\n          box: 0,\n          hamming: 1,\n          lanczos2: 2,\n          lanczos3: 3\n        },\n        q2f: ['box', 'hamming', 'lanczos2', 'lanczos3']\n      };\n    }, {}],\n    8: [function (_dereq_, module, exports) {\n      'use strict';\n\n      var createFilters = _dereq_('./resize_filter_gen');\n      function hasAlpha(src, width, height) {\n        var ptr = 3,\n          len = width * height * 4 | 0;\n        while (ptr < len) {\n          if (src[ptr] !== 255) return true;\n          ptr = ptr + 4 | 0;\n        }\n        return false;\n      }\n      function resetAlpha(dst, width, height) {\n        var ptr = 3,\n          len = width * height * 4 | 0;\n        while (ptr < len) {\n          dst[ptr] = 0xFF;\n          ptr = ptr + 4 | 0;\n        }\n      }\n      function asUint8Array(src) {\n        return new Uint8Array(src.buffer, 0, src.byteLength);\n      }\n      var IS_LE = true; // should not crash everything on module load in old browsers\n\n      try {\n        IS_LE = new Uint32Array(new Uint8Array([1, 0, 0, 0]).buffer)[0] === 1;\n      } catch (__) {}\n      function copyInt16asLE(src, target, target_offset) {\n        if (IS_LE) {\n          target.set(asUint8Array(src), target_offset);\n          return;\n        }\n        for (var ptr = target_offset, i = 0; i < src.length; i++) {\n          var data = src[i];\n          target[ptr++] = data & 0xFF;\n          target[ptr++] = data >> 8 & 0xFF;\n        }\n      }\n      module.exports = function resize_wasm(options) {\n        var src = options.src;\n        var srcW = options.width;\n        var srcH = options.height;\n        var destW = options.toWidth;\n        var destH = options.toHeight;\n        var scaleX = options.scaleX || options.toWidth / options.width;\n        var scaleY = options.scaleY || options.toHeight / options.height;\n        var offsetX = options.offsetX || 0.0;\n        var offsetY = options.offsetY || 0.0;\n        var dest = options.dest || new Uint8Array(destW * destH * 4);\n        var filter = typeof options.filter === 'undefined' ? 'mks2013' : options.filter;\n        var filtersX = createFilters(filter, srcW, destW, scaleX, offsetX),\n          filtersY = createFilters(filter, srcH, destH, scaleY, offsetY); // destination is 0 too.\n\n        var src_offset = 0;\n        var src_size = Math.max(src.byteLength, dest.byteLength); // buffer between convolve passes\n\n        var tmp_offset = this.__align(src_offset + src_size);\n        var tmp_size = srcH * destW * 4 * 2; // 2 bytes per channel\n\n        var filtersX_offset = this.__align(tmp_offset + tmp_size);\n        var filtersY_offset = this.__align(filtersX_offset + filtersX.byteLength);\n        var alloc_bytes = filtersY_offset + filtersY.byteLength;\n        var instance = this.__instance('resize', alloc_bytes); //\n        // Fill memory block with data to process\n        //\n\n        var mem = new Uint8Array(this.__memory.buffer);\n        var mem32 = new Uint32Array(this.__memory.buffer); // 32-bit copy is much faster in chrome\n\n        var src32 = new Uint32Array(src.buffer);\n        mem32.set(src32); // We should guarantee LE bytes order. Filters are not big, so\n        // speed difference is not significant vs direct .set()\n\n        copyInt16asLE(filtersX, mem, filtersX_offset);\n        copyInt16asLE(filtersY, mem, filtersY_offset); // Now call webassembly method\n        // emsdk does method names with '_'\n\n        var fn = instance.exports.convolveHV || instance.exports._convolveHV;\n        if (hasAlpha(src, srcW, srcH)) {\n          fn(filtersX_offset, filtersY_offset, tmp_offset, srcW, srcH, destW, destH, 1);\n        } else {\n          fn(filtersX_offset, filtersY_offset, tmp_offset, srcW, srcH, destW, destH, 0);\n          resetAlpha(dest, destW, destH);\n        } //\n        // Copy data back to typed array\n        //\n        // 32-bit copy is much faster in chrome\n\n        var dest32 = new Uint32Array(dest.buffer);\n        dest32.set(new Uint32Array(this.__memory.buffer, 0, destH * destW));\n        return dest;\n      };\n    }, {\n      \"./resize_filter_gen\": 6\n    }],\n    9: [function (_dereq_, module, exports) {\n      'use strict';\n\n      module.exports = {\n        name: 'unsharp_mask',\n        fn: _dereq_('./unsharp_mask'),\n        wasm_fn: _dereq_('./unsharp_mask_wasm'),\n        wasm_src: _dereq_('./unsharp_mask_wasm_base64')\n      };\n    }, {\n      \"./unsharp_mask\": 10,\n      \"./unsharp_mask_wasm\": 11,\n      \"./unsharp_mask_wasm_base64\": 12\n    }],\n    10: [function (_dereq_, module, exports) {\n      // Unsharp mask filter\n      //\n      // http://stackoverflow.com/a/23322820/1031804\n      // USM(O) = O + (2 * (Amount / 100) * (O - GB))\n      // GB - gaussian blur.\n      //\n      // Image is converted from RGB to HSV, unsharp mask is applied to the\n      // brightness channel and then image is converted back to RGB.\n      //\n      'use strict';\n\n      var glur_mono16 = _dereq_('glur/mono16');\n      function hsv_v16(img, width, height) {\n        var size = width * height;\n        var out = new Uint16Array(size);\n        var r, g, b, max;\n        for (var i = 0; i < size; i++) {\n          r = img[4 * i];\n          g = img[4 * i + 1];\n          b = img[4 * i + 2];\n          max = r >= g && r >= b ? r : g >= b && g >= r ? g : b;\n          out[i] = max << 8;\n        }\n        return out;\n      }\n      module.exports = function unsharp(img, width, height, amount, radius, threshold) {\n        var v1, v2, vmul;\n        var diff, iTimes4;\n        if (amount === 0 || radius < 0.5) {\n          return;\n        }\n        if (radius > 2.0) {\n          radius = 2.0;\n        }\n        var brightness = hsv_v16(img, width, height);\n        var blured = new Uint16Array(brightness); // copy, because blur modify src\n\n        glur_mono16(blured, width, height, radius);\n        var amountFp = amount / 100 * 0x1000 + 0.5 | 0;\n        var thresholdFp = threshold << 8;\n        var size = width * height;\n        /* eslint-disable indent */\n\n        for (var i = 0; i < size; i++) {\n          v1 = brightness[i];\n          diff = v1 - blured[i];\n          if (Math.abs(diff) >= thresholdFp) {\n            // add unsharp mask to the brightness channel\n            v2 = v1 + (amountFp * diff + 0x800 >> 12); // Both v1 and v2 are within [0.0 .. 255.0] (0000-FF00) range, never going into\n            // [255.003 .. 255.996] (FF01-FFFF). This allows to round this value as (x+.5)|0\n            // later without overflowing.\n\n            v2 = v2 > 0xff00 ? 0xff00 : v2;\n            v2 = v2 < 0x0000 ? 0x0000 : v2; // Avoid division by 0. V=0 means rgb(0,0,0), unsharp with unsharpAmount>0 cannot\n            // change this value (because diff between colors gets inflated), so no need to verify correctness.\n\n            v1 = v1 !== 0 ? v1 : 1; // Multiplying V in HSV model by a constant is equivalent to multiplying each component\n            // in RGB by the same constant (same for HSL), see also:\n            // https://beesbuzz.biz/code/16-hsv-color-transforms\n\n            vmul = (v2 << 12) / v1 | 0; // Result will be in [0..255] range because:\n            //  - all numbers are positive\n            //  - r,g,b <= (v1/256)\n            //  - r,g,b,(v1/256),(v2/256) <= 255\n            // So highest this number can get is X*255/X+0.5=255.5 which is < 256 and rounds down.\n\n            iTimes4 = i * 4;\n            img[iTimes4] = img[iTimes4] * vmul + 0x800 >> 12; // R\n\n            img[iTimes4 + 1] = img[iTimes4 + 1] * vmul + 0x800 >> 12; // G\n\n            img[iTimes4 + 2] = img[iTimes4 + 2] * vmul + 0x800 >> 12; // B\n          }\n        }\n      };\n    }, {\n      \"glur/mono16\": 18\n    }],\n    11: [function (_dereq_, module, exports) {\n      'use strict';\n\n      module.exports = function unsharp(img, width, height, amount, radius, threshold) {\n        if (amount === 0 || radius < 0.5) {\n          return;\n        }\n        if (radius > 2.0) {\n          radius = 2.0;\n        }\n        var pixels = width * height;\n        var img_bytes_cnt = pixels * 4;\n        var hsv_bytes_cnt = pixels * 2;\n        var blur_bytes_cnt = pixels * 2;\n        var blur_line_byte_cnt = Math.max(width, height) * 4; // float32 array\n\n        var blur_coeffs_byte_cnt = 8 * 4; // float32 array\n\n        var img_offset = 0;\n        var hsv_offset = img_bytes_cnt;\n        var blur_offset = hsv_offset + hsv_bytes_cnt;\n        var blur_tmp_offset = blur_offset + blur_bytes_cnt;\n        var blur_line_offset = blur_tmp_offset + blur_bytes_cnt;\n        var blur_coeffs_offset = blur_line_offset + blur_line_byte_cnt;\n        var instance = this.__instance('unsharp_mask', img_bytes_cnt + hsv_bytes_cnt + blur_bytes_cnt * 2 + blur_line_byte_cnt + blur_coeffs_byte_cnt, {\n          exp: Math.exp\n        }); // 32-bit copy is much faster in chrome\n\n        var img32 = new Uint32Array(img.buffer);\n        var mem32 = new Uint32Array(this.__memory.buffer);\n        mem32.set(img32); // HSL\n\n        var fn = instance.exports.hsv_v16 || instance.exports._hsv_v16;\n        fn(img_offset, hsv_offset, width, height); // BLUR\n\n        fn = instance.exports.blurMono16 || instance.exports._blurMono16;\n        fn(hsv_offset, blur_offset, blur_tmp_offset, blur_line_offset, blur_coeffs_offset, width, height, radius); // UNSHARP\n\n        fn = instance.exports.unsharp || instance.exports._unsharp;\n        fn(img_offset, img_offset, hsv_offset, blur_offset, width, height, amount, threshold); // 32-bit copy is much faster in chrome\n\n        img32.set(new Uint32Array(this.__memory.buffer, 0, pixels));\n      };\n    }, {}],\n    12: [function (_dereq_, module, exports) {\n      // This is autogenerated file from math.wasm, don't edit.\n      //\n      'use strict';\n\n      /* eslint-disable max-len */\n      module.exports = '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';\n    }, {}],\n    13: [function (_dereq_, module, exports) {\n      'use strict';\n\n      var GC_INTERVAL = 100;\n      function Pool(create, idle) {\n        this.create = create;\n        this.available = [];\n        this.acquired = {};\n        this.lastId = 1;\n        this.timeoutId = 0;\n        this.idle = idle || 2000;\n      }\n      Pool.prototype.acquire = function () {\n        var _this = this;\n        var resource;\n        if (this.available.length !== 0) {\n          resource = this.available.pop();\n        } else {\n          resource = this.create();\n          resource.id = this.lastId++;\n          resource.release = function () {\n            return _this.release(resource);\n          };\n        }\n        this.acquired[resource.id] = resource;\n        return resource;\n      };\n      Pool.prototype.release = function (resource) {\n        var _this2 = this;\n        delete this.acquired[resource.id];\n        resource.lastUsed = Date.now();\n        this.available.push(resource);\n        if (this.timeoutId === 0) {\n          this.timeoutId = setTimeout(function () {\n            return _this2.gc();\n          }, GC_INTERVAL);\n        }\n      };\n      Pool.prototype.gc = function () {\n        var _this3 = this;\n        var now = Date.now();\n        this.available = this.available.filter(function (resource) {\n          if (now - resource.lastUsed > _this3.idle) {\n            resource.destroy();\n            return false;\n          }\n          return true;\n        });\n        if (this.available.length !== 0) {\n          this.timeoutId = setTimeout(function () {\n            return _this3.gc();\n          }, GC_INTERVAL);\n        } else {\n          this.timeoutId = 0;\n        }\n      };\n      module.exports = Pool;\n    }, {}],\n    14: [function (_dereq_, module, exports) {\n      // Add intermediate resizing steps when scaling down by a very large factor.\n      //\n      // For example, when resizing 10000x10000 down to 10x10, it'll resize it to\n      // 300x300 first.\n      //\n      // It's needed because tiler has issues when the entire tile is scaled down\n      // to a few pixels (1024px source tile with border size 3 should result in\n      // at least 3+3+2 = 8px target tile, so max scale factor is 128 here).\n      //\n      // Also, adding intermediate steps can speed up processing if we use lower\n      // quality algorithms for first stages.\n      //\n      'use strict';\n\n      // min size = 0 results in infinite loop,\n      // min size = 1 can consume large amount of memory\n      var MIN_INNER_TILE_SIZE = 2;\n      module.exports = function createStages(fromWidth, fromHeight, toWidth, toHeight, srcTileSize, destTileBorder) {\n        var scaleX = toWidth / fromWidth;\n        var scaleY = toHeight / fromHeight; // derived from createRegions equation:\n        // innerTileWidth = pixelFloor(srcTileSize * scaleX) - 2 * destTileBorder;\n\n        var minScale = (2 * destTileBorder + MIN_INNER_TILE_SIZE + 1) / srcTileSize; // refuse to scale image multiple times by less than twice each time,\n        // it could only happen because of invalid options\n\n        if (minScale > 0.5) return [[toWidth, toHeight]];\n        var stageCount = Math.ceil(Math.log(Math.min(scaleX, scaleY)) / Math.log(minScale)); // no additional resizes are necessary,\n        // stageCount can be zero or be negative when enlarging the image\n\n        if (stageCount <= 1) return [[toWidth, toHeight]];\n        var result = [];\n        for (var i = 0; i < stageCount; i++) {\n          var width = Math.round(Math.pow(Math.pow(fromWidth, stageCount - i - 1) * Math.pow(toWidth, i + 1), 1 / stageCount));\n          var height = Math.round(Math.pow(Math.pow(fromHeight, stageCount - i - 1) * Math.pow(toHeight, i + 1), 1 / stageCount));\n          result.push([width, height]);\n        }\n        return result;\n      };\n    }, {}],\n    15: [function (_dereq_, module, exports) {\n      // Split original image into multiple 1024x1024 chunks to reduce memory usage\n      // (images have to be unpacked into typed arrays for resizing) and allow\n      // parallel processing of multiple tiles at a time.\n      //\n      'use strict';\n\n      /*\n       * pixelFloor and pixelCeil are modified versions of Math.floor and Math.ceil\n       * functions which take into account floating point arithmetic errors.\n       * Those errors can cause undesired increments/decrements of sizes and offsets:\n       * Math.ceil(36 / (36 / 500)) = 501\n       * pixelCeil(36 / (36 / 500)) = 500\n       */\n      var PIXEL_EPSILON = 1e-5;\n      function pixelFloor(x) {\n        var nearest = Math.round(x);\n        if (Math.abs(x - nearest) < PIXEL_EPSILON) {\n          return nearest;\n        }\n        return Math.floor(x);\n      }\n      function pixelCeil(x) {\n        var nearest = Math.round(x);\n        if (Math.abs(x - nearest) < PIXEL_EPSILON) {\n          return nearest;\n        }\n        return Math.ceil(x);\n      }\n      module.exports = function createRegions(options) {\n        var scaleX = options.toWidth / options.width;\n        var scaleY = options.toHeight / options.height;\n        var innerTileWidth = pixelFloor(options.srcTileSize * scaleX) - 2 * options.destTileBorder;\n        var innerTileHeight = pixelFloor(options.srcTileSize * scaleY) - 2 * options.destTileBorder; // prevent infinite loop, this should never happen\n\n        if (innerTileWidth < 1 || innerTileHeight < 1) {\n          throw new Error('Internal error in pica: target tile width/height is too small.');\n        }\n        var x, y;\n        var innerX, innerY, toTileWidth, toTileHeight;\n        var tiles = [];\n        var tile; // we go top-to-down instead of left-to-right to make image displayed from top to\n        // doesn in the browser\n\n        for (innerY = 0; innerY < options.toHeight; innerY += innerTileHeight) {\n          for (innerX = 0; innerX < options.toWidth; innerX += innerTileWidth) {\n            x = innerX - options.destTileBorder;\n            if (x < 0) {\n              x = 0;\n            }\n            toTileWidth = innerX + innerTileWidth + options.destTileBorder - x;\n            if (x + toTileWidth >= options.toWidth) {\n              toTileWidth = options.toWidth - x;\n            }\n            y = innerY - options.destTileBorder;\n            if (y < 0) {\n              y = 0;\n            }\n            toTileHeight = innerY + innerTileHeight + options.destTileBorder - y;\n            if (y + toTileHeight >= options.toHeight) {\n              toTileHeight = options.toHeight - y;\n            }\n            tile = {\n              toX: x,\n              toY: y,\n              toWidth: toTileWidth,\n              toHeight: toTileHeight,\n              toInnerX: innerX,\n              toInnerY: innerY,\n              toInnerWidth: innerTileWidth,\n              toInnerHeight: innerTileHeight,\n              offsetX: x / scaleX - pixelFloor(x / scaleX),\n              offsetY: y / scaleY - pixelFloor(y / scaleY),\n              scaleX: scaleX,\n              scaleY: scaleY,\n              x: pixelFloor(x / scaleX),\n              y: pixelFloor(y / scaleY),\n              width: pixelCeil(toTileWidth / scaleX),\n              height: pixelCeil(toTileHeight / scaleY)\n            };\n            tiles.push(tile);\n          }\n        }\n        return tiles;\n      };\n    }, {}],\n    16: [function (_dereq_, module, exports) {\n      'use strict';\n\n      function objClass(obj) {\n        return Object.prototype.toString.call(obj);\n      }\n      module.exports.isCanvas = function isCanvas(element) {\n        var cname = objClass(element);\n        return cname === '[object HTMLCanvasElement]'\n        /* browser */ || cname === '[object OffscreenCanvas]' || cname === '[object Canvas]'\n        /* node-canvas */;\n      };\n      module.exports.isImage = function isImage(element) {\n        return objClass(element) === '[object HTMLImageElement]';\n      };\n      module.exports.isImageBitmap = function isImageBitmap(element) {\n        return objClass(element) === '[object ImageBitmap]';\n      };\n      module.exports.limiter = function limiter(concurrency) {\n        var active = 0,\n          queue = [];\n        function roll() {\n          if (active < concurrency && queue.length) {\n            active++;\n            queue.shift()();\n          }\n        }\n        return function limit(fn) {\n          return new Promise(function (resolve, reject) {\n            queue.push(function () {\n              fn().then(function (result) {\n                resolve(result);\n                active--;\n                roll();\n              }, function (err) {\n                reject(err);\n                active--;\n                roll();\n              });\n            });\n            roll();\n          });\n        };\n      };\n      module.exports.cib_quality_name = function cib_quality_name(num) {\n        switch (num) {\n          case 0:\n            return 'pixelated';\n          case 1:\n            return 'low';\n          case 2:\n            return 'medium';\n        }\n        return 'high';\n      };\n      module.exports.cib_support = function cib_support(createCanvas) {\n        return Promise.resolve().then(function () {\n          if (typeof createImageBitmap === 'undefined') {\n            return false;\n          }\n          var c = createCanvas(100, 100);\n          return createImageBitmap(c, 0, 0, 100, 100, {\n            resizeWidth: 10,\n            resizeHeight: 10,\n            resizeQuality: 'high'\n          }).then(function (bitmap) {\n            var status = bitmap.width === 10; // Branch below is filtered on upper level. We do not call resize\n            // detection for basic ImageBitmap.\n            //\n            // https://developer.mozilla.org/en-US/docs/Web/API/ImageBitmap\n            // old Crome 51 has ImageBitmap without .close(). Then this code\n            // will throw and return 'false' as expected.\n            //\n\n            bitmap.close();\n            c = null;\n            return status;\n          });\n        })[\"catch\"](function () {\n          return false;\n        });\n      };\n      module.exports.worker_offscreen_canvas_support = function worker_offscreen_canvas_support() {\n        return new Promise(function (resolve, reject) {\n          if (typeof OffscreenCanvas === 'undefined') {\n            // if OffscreenCanvas is present, we assume browser supports Worker and built-in Promise as well\n            resolve(false);\n            return;\n          }\n          function workerPayload(self) {\n            if (typeof createImageBitmap === 'undefined') {\n              self.postMessage(false);\n              return;\n            }\n            Promise.resolve().then(function () {\n              var canvas = new OffscreenCanvas(10, 10); // test that 2d context can be used in worker\n\n              var ctx = canvas.getContext('2d');\n              ctx.rect(0, 0, 1, 1); // test that cib can be used to return image bitmap from worker\n\n              return createImageBitmap(canvas, 0, 0, 1, 1);\n            }).then(function () {\n              return self.postMessage(true);\n            }, function () {\n              return self.postMessage(false);\n            });\n          }\n          var code = btoa(\"(\".concat(workerPayload.toString(), \")(self);\"));\n          var w = new Worker(\"data:text/javascript;base64,\".concat(code));\n          w.onmessage = function (ev) {\n            return resolve(ev.data);\n          };\n          w.onerror = reject;\n        }).then(function (result) {\n          return result;\n        }, function () {\n          return false;\n        });\n      }; // Check if canvas.getContext('2d').getImageData can be used,\n      // FireFox randomizes the output of that function in `privacy.resistFingerprinting` mode\n\n      module.exports.can_use_canvas = function can_use_canvas(createCanvas) {\n        var usable = false;\n        try {\n          var canvas = createCanvas(2, 1);\n          var ctx = canvas.getContext('2d');\n          var d = ctx.createImageData(2, 1);\n          d.data[0] = 12;\n          d.data[1] = 23;\n          d.data[2] = 34;\n          d.data[3] = 255;\n          d.data[4] = 45;\n          d.data[5] = 56;\n          d.data[6] = 67;\n          d.data[7] = 255;\n          ctx.putImageData(d, 0, 0);\n          d = null;\n          d = ctx.getImageData(0, 0, 2, 1);\n          if (d.data[0] === 12 && d.data[1] === 23 && d.data[2] === 34 && d.data[3] === 255 && d.data[4] === 45 && d.data[5] === 56 && d.data[6] === 67 && d.data[7] === 255) {\n            usable = true;\n          }\n        } catch (err) {}\n        return usable;\n      }; // Check if createImageBitmap(img, sx, sy, sw, sh) signature works correctly\n      // with JPEG images oriented with Exif;\n      // https://bugs.chromium.org/p/chromium/issues/detail?id=1220671\n      // TODO: remove after it's fixed in chrome for at least 2 releases\n\n      module.exports.cib_can_use_region = function cib_can_use_region() {\n        return new Promise(function (resolve) {\n          // `Image` check required for use in `ServiceWorker`\n          if (typeof Image === 'undefined' || typeof createImageBitmap === 'undefined') {\n            resolve(false);\n            return;\n          }\n          var image = new Image();\n          image.src = 'data:image/jpeg;base64,' + '/9j/4QBiRXhpZgAATU0AKgAAAAgABQESAAMAAAABAAYAAAEaAAUAAAABAAAASgEbAAUAA' + 'AABAAAAUgEoAAMAAAABAAIAAAITAAMAAAABAAEAAAAAAAAAAABIAAAAAQAAAEgAAAAB/9' + 'sAQwAEAwMEAwMEBAMEBQQEBQYKBwYGBgYNCQoICg8NEBAPDQ8OERMYFBESFxIODxUcFRc' + 'ZGRsbGxAUHR8dGh8YGhsa/9sAQwEEBQUGBQYMBwcMGhEPERoaGhoaGhoaGhoaGhoaGhoa' + 'GhoaGhoaGhoaGhoaGhoaGhoaGhoaGhoaGhoaGhoaGhoa/8IAEQgAAQACAwERAAIRAQMRA' + 'f/EABQAAQAAAAAAAAAAAAAAAAAAAAf/xAAUAQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAA' + 'IQAxAAAAF/P//EABQQAQAAAAAAAAAAAAAAAAAAAAD/2gAIAQEAAQUCf//EABQRAQAAAAA' + 'AAAAAAAAAAAAAAAD/2gAIAQMBAT8Bf//EABQRAQAAAAAAAAAAAAAAAAAAAAD/2gAIAQIB' + 'AT8Bf//EABQQAQAAAAAAAAAAAAAAAAAAAAD/2gAIAQEABj8Cf//EABQQAQAAAAAAAAAAA' + 'AAAAAAAAAD/2gAIAQEAAT8hf//aAAwDAQACAAMAAAAQH//EABQRAQAAAAAAAAAAAAAAAA' + 'AAAAD/2gAIAQMBAT8Qf//EABQRAQAAAAAAAAAAAAAAAAAAAAD/2gAIAQIBAT8Qf//EABQ' + 'QAQAAAAAAAAAAAAAAAAAAAAD/2gAIAQEAAT8Qf//Z';\n          image.onload = function () {\n            createImageBitmap(image, 0, 0, image.width, image.height).then(function (bitmap) {\n              if (bitmap.width === image.width && bitmap.height === image.height) {\n                resolve(true);\n              } else {\n                resolve(false);\n              }\n            }, function () {\n              return resolve(false);\n            });\n          };\n          image.onerror = function () {\n            return resolve(false);\n          };\n        });\n      };\n    }, {}],\n    17: [function (_dereq_, module, exports) {\n      // Web Worker wrapper for image resize function\n      'use strict';\n\n      module.exports = function () {\n        var MathLib = _dereq_('./mathlib');\n        var mathLib;\n        /* eslint-disable no-undef */\n\n        onmessage = function onmessage(ev) {\n          var tileOpts = ev.data.opts;\n          var returnBitmap = false;\n          if (!tileOpts.src && tileOpts.srcBitmap) {\n            var canvas = new OffscreenCanvas(tileOpts.width, tileOpts.height);\n            var ctx = canvas.getContext('2d');\n            ctx.drawImage(tileOpts.srcBitmap, 0, 0);\n            tileOpts.src = ctx.getImageData(0, 0, tileOpts.width, tileOpts.height).data;\n            canvas.width = canvas.height = 0;\n            canvas = null;\n            tileOpts.srcBitmap.close();\n            tileOpts.srcBitmap = null; // Temporary force out data to typed array, because Chrome have artefacts\n            // https://github.com/nodeca/pica/issues/223\n            // returnBitmap = true;\n          }\n          if (!mathLib) mathLib = new MathLib(ev.data.features); // Use multimath's sync auto-init. Avoid Promise use in old browsers,\n          // because polyfills are not propagated to webworker.\n\n          var data = mathLib.resizeAndUnsharp(tileOpts);\n          if (returnBitmap) {\n            var toImageData = new ImageData(new Uint8ClampedArray(data), tileOpts.toWidth, tileOpts.toHeight);\n            var _canvas = new OffscreenCanvas(tileOpts.toWidth, tileOpts.toHeight);\n            var _ctx = _canvas.getContext('2d');\n            _ctx.putImageData(toImageData, 0, 0);\n            createImageBitmap(_canvas).then(function (bitmap) {\n              postMessage({\n                bitmap: bitmap\n              }, [bitmap]);\n            });\n          } else {\n            postMessage({\n              data: data\n            }, [data.buffer]);\n          }\n        };\n      };\n    }, {\n      \"./mathlib\": 1\n    }],\n    18: [function (_dereq_, module, exports) {\n      // Calculate Gaussian blur of an image using IIR filter\n      // The method is taken from Intel's white paper and code example attached to it:\n      // https://software.intel.com/en-us/articles/iir-gaussian-blur-filter\n      // -implementation-using-intel-advanced-vector-extensions\n\n      var a0, a1, a2, a3, b1, b2, left_corner, right_corner;\n      function gaussCoef(sigma) {\n        if (sigma < 0.5) {\n          sigma = 0.5;\n        }\n        var a = Math.exp(0.726 * 0.726) / sigma,\n          g1 = Math.exp(-a),\n          g2 = Math.exp(-2 * a),\n          k = (1 - g1) * (1 - g1) / (1 + 2 * a * g1 - g2);\n        a0 = k;\n        a1 = k * (a - 1) * g1;\n        a2 = k * (a + 1) * g1;\n        a3 = -k * g2;\n        b1 = 2 * g1;\n        b2 = -g2;\n        left_corner = (a0 + a1) / (1 - b1 - b2);\n        right_corner = (a2 + a3) / (1 - b1 - b2);\n\n        // Attempt to force type to FP32.\n        return new Float32Array([a0, a1, a2, a3, b1, b2, left_corner, right_corner]);\n      }\n      function convolveMono16(src, out, line, coeff, width, height) {\n        // takes src image and writes the blurred and transposed result into out\n\n        var prev_src, curr_src, curr_out, prev_out, prev_prev_out;\n        var src_index, out_index, line_index;\n        var i, j;\n        var coeff_a0, coeff_a1, coeff_b1, coeff_b2;\n        for (i = 0; i < height; i++) {\n          src_index = i * width;\n          out_index = i;\n          line_index = 0;\n\n          // left to right\n          prev_src = src[src_index];\n          prev_prev_out = prev_src * coeff[6];\n          prev_out = prev_prev_out;\n          coeff_a0 = coeff[0];\n          coeff_a1 = coeff[1];\n          coeff_b1 = coeff[4];\n          coeff_b2 = coeff[5];\n          for (j = 0; j < width; j++) {\n            curr_src = src[src_index];\n            curr_out = curr_src * coeff_a0 + prev_src * coeff_a1 + prev_out * coeff_b1 + prev_prev_out * coeff_b2;\n            prev_prev_out = prev_out;\n            prev_out = curr_out;\n            prev_src = curr_src;\n            line[line_index] = prev_out;\n            line_index++;\n            src_index++;\n          }\n          src_index--;\n          line_index--;\n          out_index += height * (width - 1);\n\n          // right to left\n          prev_src = src[src_index];\n          prev_prev_out = prev_src * coeff[7];\n          prev_out = prev_prev_out;\n          curr_src = prev_src;\n          coeff_a0 = coeff[2];\n          coeff_a1 = coeff[3];\n          for (j = width - 1; j >= 0; j--) {\n            curr_out = curr_src * coeff_a0 + prev_src * coeff_a1 + prev_out * coeff_b1 + prev_prev_out * coeff_b2;\n            prev_prev_out = prev_out;\n            prev_out = curr_out;\n            prev_src = curr_src;\n            curr_src = src[src_index];\n            out[out_index] = line[line_index] + prev_out;\n            src_index--;\n            line_index--;\n            out_index -= height;\n          }\n        }\n      }\n      function blurMono16(src, width, height, radius) {\n        // Quick exit on zero radius\n        if (!radius) {\n          return;\n        }\n        var out = new Uint16Array(src.length),\n          tmp_line = new Float32Array(Math.max(width, height));\n        var coeff = gaussCoef(radius);\n        convolveMono16(src, out, tmp_line, coeff, width, height, radius);\n        convolveMono16(out, src, tmp_line, coeff, height, width, radius);\n      }\n      module.exports = blurMono16;\n    }, {}],\n    19: [function (_dereq_, module, exports) {\n      'use strict';\n\n      var assign = _dereq_('object-assign');\n      var base64decode = _dereq_('./lib/base64decode');\n      var hasWebAssembly = _dereq_('./lib/wa_detect');\n      var DEFAULT_OPTIONS = {\n        js: true,\n        wasm: true\n      };\n      function MultiMath(options) {\n        if (!(this instanceof MultiMath)) return new MultiMath(options);\n        var opts = assign({}, DEFAULT_OPTIONS, options || {});\n        this.options = opts;\n        this.__cache = {};\n        this.__init_promise = null;\n        this.__modules = opts.modules || {};\n        this.__memory = null;\n        this.__wasm = {};\n        this.__isLE = new Uint32Array(new Uint8Array([1, 0, 0, 0]).buffer)[0] === 1;\n        if (!this.options.js && !this.options.wasm) {\n          throw new Error('mathlib: at least \"js\" or \"wasm\" should be enabled');\n        }\n      }\n      MultiMath.prototype.has_wasm = hasWebAssembly;\n      MultiMath.prototype.use = function (module) {\n        this.__modules[module.name] = module;\n\n        // Pin the best possible implementation\n        if (this.options.wasm && this.has_wasm() && module.wasm_fn) {\n          this[module.name] = module.wasm_fn;\n        } else {\n          this[module.name] = module.fn;\n        }\n        return this;\n      };\n      MultiMath.prototype.init = function () {\n        if (this.__init_promise) return this.__init_promise;\n        if (!this.options.js && this.options.wasm && !this.has_wasm()) {\n          return Promise.reject(new Error('mathlib: only \"wasm\" was enabled, but it\\'s not supported'));\n        }\n        var self = this;\n        this.__init_promise = Promise.all(Object.keys(self.__modules).map(function (name) {\n          var module = self.__modules[name];\n          if (!self.options.wasm || !self.has_wasm() || !module.wasm_fn) return null;\n\n          // If already compiled - exit\n          if (self.__wasm[name]) return null;\n\n          // Compile wasm source\n          return WebAssembly.compile(self.__base64decode(module.wasm_src)).then(function (m) {\n            self.__wasm[name] = m;\n          });\n        })).then(function () {\n          return self;\n        });\n        return this.__init_promise;\n      };\n\n      ////////////////////////////////////////////////////////////////////////////////\n      // Methods below are for internal use from plugins\n\n      // Simple decode base64 to typed array. Useful to load embedded webassembly\n      // code. You probably don't need to call this method directly.\n      //\n      MultiMath.prototype.__base64decode = base64decode;\n\n      // Increase current memory to include specified number of bytes. Do nothing if\n      // size is already ok. You probably don't need to call this method directly,\n      // because it will be invoked from `.__instance()`.\n      //\n      MultiMath.prototype.__reallocate = function mem_grow_to(bytes) {\n        if (!this.__memory) {\n          this.__memory = new WebAssembly.Memory({\n            initial: Math.ceil(bytes / (64 * 1024))\n          });\n          return this.__memory;\n        }\n        var mem_size = this.__memory.buffer.byteLength;\n        if (mem_size < bytes) {\n          this.__memory.grow(Math.ceil((bytes - mem_size) / (64 * 1024)));\n        }\n        return this.__memory;\n      };\n\n      // Returns instantinated webassembly item by name, with specified memory size\n      // and environment.\n      // - use cache if available\n      // - do sync module init, if async init was not called earlier\n      // - allocate memory if not enougth\n      // - can export functions to webassembly via \"env_extra\",\n      //   for example, { exp: Math.exp }\n      //\n      MultiMath.prototype.__instance = function instance(name, memsize, env_extra) {\n        if (memsize) this.__reallocate(memsize);\n\n        // If .init() was not called, do sync compile\n        if (!this.__wasm[name]) {\n          var module = this.__modules[name];\n          this.__wasm[name] = new WebAssembly.Module(this.__base64decode(module.wasm_src));\n        }\n        if (!this.__cache[name]) {\n          var env_base = {\n            memoryBase: 0,\n            memory: this.__memory,\n            tableBase: 0,\n            table: new WebAssembly.Table({\n              initial: 0,\n              element: 'anyfunc'\n            })\n          };\n          this.__cache[name] = new WebAssembly.Instance(this.__wasm[name], {\n            env: assign(env_base, env_extra || {})\n          });\n        }\n        return this.__cache[name];\n      };\n\n      // Helper to calculate memory aligh for pointers. Webassembly does not require\n      // this, but you may wish to experiment. Default base = 8;\n      //\n      MultiMath.prototype.__align = function align(number, base) {\n        base = base || 8;\n        var reminder = number % base;\n        return number + (reminder ? base - reminder : 0);\n      };\n      module.exports = MultiMath;\n    }, {\n      \"./lib/base64decode\": 20,\n      \"./lib/wa_detect\": 21,\n      \"object-assign\": 22\n    }],\n    20: [function (_dereq_, module, exports) {\n      // base64 decode str -> Uint8Array, to load WA modules\n      //\n      'use strict';\n\n      var BASE64_MAP = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';\n      module.exports = function base64decode(str) {\n        var input = str.replace(/[\\r\\n=]/g, ''),\n          // remove CR/LF & padding to simplify scan\n          max = input.length;\n        var out = new Uint8Array(max * 3 >> 2);\n\n        // Collect by 6*4 bits (3 bytes)\n\n        var bits = 0;\n        var ptr = 0;\n        for (var idx = 0; idx < max; idx++) {\n          if (idx % 4 === 0 && idx) {\n            out[ptr++] = bits >> 16 & 0xFF;\n            out[ptr++] = bits >> 8 & 0xFF;\n            out[ptr++] = bits & 0xFF;\n          }\n          bits = bits << 6 | BASE64_MAP.indexOf(input.charAt(idx));\n        }\n\n        // Dump tail\n\n        var tailbits = max % 4 * 6;\n        if (tailbits === 0) {\n          out[ptr++] = bits >> 16 & 0xFF;\n          out[ptr++] = bits >> 8 & 0xFF;\n          out[ptr++] = bits & 0xFF;\n        } else if (tailbits === 18) {\n          out[ptr++] = bits >> 10 & 0xFF;\n          out[ptr++] = bits >> 2 & 0xFF;\n        } else if (tailbits === 12) {\n          out[ptr++] = bits >> 4 & 0xFF;\n        }\n        return out;\n      };\n    }, {}],\n    21: [function (_dereq_, module, exports) {\n      // Detect WebAssembly support.\n      // - Check global WebAssembly object\n      // - Try to load simple module (can be disabled via CSP)\n      //\n      'use strict';\n\n      var wa;\n      module.exports = function hasWebAssembly() {\n        // use cache if called before;\n        if (typeof wa !== 'undefined') return wa;\n        wa = false;\n        if (typeof WebAssembly === 'undefined') return wa;\n\n        // If WebAssenbly is disabled, code can throw on compile\n        try {\n          // https://github.com/brion/min-wasm-fail/blob/master/min-wasm-fail.in.js\n          // Additional check that WA internals are correct\n\n          /* eslint-disable comma-spacing, max-len */\n          var bin = new Uint8Array([0, 97, 115, 109, 1, 0, 0, 0, 1, 6, 1, 96, 1, 127, 1, 127, 3, 2, 1, 0, 5, 3, 1, 0, 1, 7, 8, 1, 4, 116, 101, 115, 116, 0, 0, 10, 16, 1, 14, 0, 32, 0, 65, 1, 54, 2, 0, 32, 0, 40, 2, 0, 11]);\n          var module = new WebAssembly.Module(bin);\n          var instance = new WebAssembly.Instance(module, {});\n\n          // test storing to and loading from a non-zero location via a parameter.\n          // Safari on iOS 11.2.5 returns 0 unexpectedly at non-zero locations\n          if (instance.exports.test(4) !== 0) wa = true;\n          return wa;\n        } catch (__) {}\n        return wa;\n      };\n    }, {}],\n    22: [function (_dereq_, module, exports) {\n      /*\n      object-assign\n      (c) Sindre Sorhus\n      @license MIT\n      */\n\n      'use strict';\n\n      /* eslint-disable no-unused-vars */\n      var getOwnPropertySymbols = Object.getOwnPropertySymbols;\n      var hasOwnProperty = Object.prototype.hasOwnProperty;\n      var propIsEnumerable = Object.prototype.propertyIsEnumerable;\n      function toObject(val) {\n        if (val === null || val === undefined) {\n          throw new TypeError('Object.assign cannot be called with null or undefined');\n        }\n        return Object(val);\n      }\n      function shouldUseNative() {\n        try {\n          if (!Object.assign) {\n            return false;\n          }\n\n          // Detect buggy property enumeration order in older V8 versions.\n\n          // https://bugs.chromium.org/p/v8/issues/detail?id=4118\n          var test1 = new String('abc'); // eslint-disable-line no-new-wrappers\n          test1[5] = 'de';\n          if (Object.getOwnPropertyNames(test1)[0] === '5') {\n            return false;\n          }\n\n          // https://bugs.chromium.org/p/v8/issues/detail?id=3056\n          var test2 = {};\n          for (var i = 0; i < 10; i++) {\n            test2['_' + String.fromCharCode(i)] = i;\n          }\n          var order2 = Object.getOwnPropertyNames(test2).map(function (n) {\n            return test2[n];\n          });\n          if (order2.join('') !== '0123456789') {\n            return false;\n          }\n\n          // https://bugs.chromium.org/p/v8/issues/detail?id=3056\n          var test3 = {};\n          'abcdefghijklmnopqrst'.split('').forEach(function (letter) {\n            test3[letter] = letter;\n          });\n          if (Object.keys(Object.assign({}, test3)).join('') !== 'abcdefghijklmnopqrst') {\n            return false;\n          }\n          return true;\n        } catch (err) {\n          // We don't expect any of the above to throw, but better to be safe.\n          return false;\n        }\n      }\n      module.exports = shouldUseNative() ? Object.assign : function (target, source) {\n        var from;\n        var to = toObject(target);\n        var symbols;\n        for (var s = 1; s < arguments.length; s++) {\n          from = Object(arguments[s]);\n          for (var key in from) {\n            if (hasOwnProperty.call(from, key)) {\n              to[key] = from[key];\n            }\n          }\n          if (getOwnPropertySymbols) {\n            symbols = getOwnPropertySymbols(from);\n            for (var i = 0; i < symbols.length; i++) {\n              if (propIsEnumerable.call(from, symbols[i])) {\n                to[symbols[i]] = from[symbols[i]];\n              }\n            }\n          }\n        }\n        return to;\n      };\n    }, {}],\n    23: [function (_dereq_, module, exports) {\n      var bundleFn = arguments[3];\n      var sources = arguments[4];\n      var cache = arguments[5];\n      var stringify = JSON.stringify;\n      module.exports = function (fn, options) {\n        var wkey;\n        var cacheKeys = Object.keys(cache);\n        for (var i = 0, l = cacheKeys.length; i < l; i++) {\n          var key = cacheKeys[i];\n          var exp = cache[key].exports;\n          // Using babel as a transpiler to use esmodule, the export will always\n          // be an object with the default export as a property of it. To ensure\n          // the existing api and babel esmodule exports are both supported we\n          // check for both\n          if (exp === fn || exp && exp.default === fn) {\n            wkey = key;\n            break;\n          }\n        }\n        if (!wkey) {\n          wkey = Math.floor(Math.pow(16, 8) * Math.random()).toString(16);\n          var wcache = {};\n          for (var i = 0, l = cacheKeys.length; i < l; i++) {\n            var key = cacheKeys[i];\n            wcache[key] = key;\n          }\n          sources[wkey] = ['function(require,module,exports){' + fn + '(self); }', wcache];\n        }\n        var skey = Math.floor(Math.pow(16, 8) * Math.random()).toString(16);\n        var scache = {};\n        scache[wkey] = wkey;\n        sources[skey] = ['function(require,module,exports){' +\n        // try to call default if defined to also support babel esmodule exports\n        'var f = require(' + stringify(wkey) + ');' + '(f.default ? f.default : f)(self);' + '}', scache];\n        var workerSources = {};\n        resolveSources(skey);\n        function resolveSources(key) {\n          workerSources[key] = true;\n          for (var depPath in sources[key][1]) {\n            var depKey = sources[key][1][depPath];\n            if (!workerSources[depKey]) {\n              resolveSources(depKey);\n            }\n          }\n        }\n        var src = '(' + bundleFn + ')({' + Object.keys(workerSources).map(function (key) {\n          return stringify(key) + ':[' + sources[key][0] + ',' + stringify(sources[key][1]) + ']';\n        }).join(',') + '},{},[' + stringify(skey) + '])';\n        var URL = window.URL || window.webkitURL || window.mozURL || window.msURL;\n        var blob = new Blob([src], {\n          type: 'text/javascript'\n        });\n        if (options && options.bare) {\n          return blob;\n        }\n        var workerUrl = URL.createObjectURL(blob);\n        var worker = new Worker(workerUrl);\n        worker.objectURL = workerUrl;\n        return worker;\n      };\n    }, {}],\n    \"/index.js\": [function (_dereq_, module, exports) {\n      'use strict';\n\n      function _slicedToArray(arr, i) {\n        return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest();\n      }\n      function _nonIterableRest() {\n        throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n      }\n      function _unsupportedIterableToArray(o, minLen) {\n        if (!o) return;\n        if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n        var n = Object.prototype.toString.call(o).slice(8, -1);\n        if (n === \"Object\" && o.constructor) n = o.constructor.name;\n        if (n === \"Map\" || n === \"Set\") return Array.from(o);\n        if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n      }\n      function _arrayLikeToArray(arr, len) {\n        if (len == null || len > arr.length) len = arr.length;\n        for (var i = 0, arr2 = new Array(len); i < len; i++) {\n          arr2[i] = arr[i];\n        }\n        return arr2;\n      }\n      function _iterableToArrayLimit(arr, i) {\n        var _i = arr == null ? null : typeof Symbol !== \"undefined\" && arr[Symbol.iterator] || arr[\"@@iterator\"];\n        if (_i == null) return;\n        var _arr = [];\n        var _n = true;\n        var _d = false;\n        var _s, _e;\n        try {\n          for (_i = _i.call(arr); !(_n = (_s = _i.next()).done); _n = true) {\n            _arr.push(_s.value);\n            if (i && _arr.length === i) break;\n          }\n        } catch (err) {\n          _d = true;\n          _e = err;\n        } finally {\n          try {\n            if (!_n && _i[\"return\"] != null) _i[\"return\"]();\n          } finally {\n            if (_d) throw _e;\n          }\n        }\n        return _arr;\n      }\n      function _arrayWithHoles(arr) {\n        if (Array.isArray(arr)) return arr;\n      }\n      var assign = _dereq_('object-assign');\n      var webworkify = _dereq_('webworkify');\n      var MathLib = _dereq_('./lib/mathlib');\n      var Pool = _dereq_('./lib/pool');\n      var utils = _dereq_('./lib/utils');\n      var worker = _dereq_('./lib/worker');\n      var createStages = _dereq_('./lib/stepper');\n      var createRegions = _dereq_('./lib/tiler');\n      var filter_info = _dereq_('./lib/mm_resize/resize_filter_info'); // Deduplicate pools & limiters with the same configs\n      // when user creates multiple pica instances.\n\n      var singletones = {};\n      var NEED_SAFARI_FIX = false;\n      try {\n        if (typeof navigator !== 'undefined' && navigator.userAgent) {\n          NEED_SAFARI_FIX = navigator.userAgent.indexOf('Safari') >= 0;\n        }\n      } catch (e) {}\n      var concurrency = 1;\n      if (typeof navigator !== 'undefined') {\n        concurrency = Math.min(navigator.hardwareConcurrency || 1, 4);\n      }\n      var DEFAULT_PICA_OPTS = {\n        tile: 1024,\n        concurrency: concurrency,\n        features: ['js', 'wasm', 'ww'],\n        idle: 2000,\n        createCanvas: function createCanvas(width, height) {\n          var tmpCanvas = document.createElement('canvas');\n          tmpCanvas.width = width;\n          tmpCanvas.height = height;\n          return tmpCanvas;\n        }\n      };\n      var DEFAULT_RESIZE_OPTS = {\n        filter: 'mks2013',\n        unsharpAmount: 0,\n        unsharpRadius: 0.0,\n        unsharpThreshold: 0\n      };\n      var CAN_NEW_IMAGE_DATA = false;\n      var CAN_CREATE_IMAGE_BITMAP = false;\n      var CAN_USE_CANVAS_GET_IMAGE_DATA = false;\n      var CAN_USE_OFFSCREEN_CANVAS = false;\n      var CAN_USE_CIB_REGION_FOR_IMAGE = false;\n      function workerFabric() {\n        return {\n          value: webworkify(worker),\n          destroy: function destroy() {\n            this.value.terminate();\n            if (typeof window !== 'undefined') {\n              var url = window.URL || window.webkitURL || window.mozURL || window.msURL;\n              if (url && url.revokeObjectURL && this.value.objectURL) {\n                url.revokeObjectURL(this.value.objectURL);\n              }\n            }\n          }\n        };\n      } ////////////////////////////////////////////////////////////////////////////////\n      // API methods\n\n      function Pica(options) {\n        if (!(this instanceof Pica)) return new Pica(options);\n        this.options = assign({}, DEFAULT_PICA_OPTS, options || {});\n        var limiter_key = \"lk_\".concat(this.options.concurrency); // Share limiters to avoid multiple parallel workers when user creates\n        // multiple pica instances.\n\n        this.__limit = singletones[limiter_key] || utils.limiter(this.options.concurrency);\n        if (!singletones[limiter_key]) singletones[limiter_key] = this.__limit; // List of supported features, according to options & browser/node.js\n\n        this.features = {\n          js: false,\n          // pure JS implementation, can be disabled for testing\n          wasm: false,\n          // webassembly implementation for heavy functions\n          cib: false,\n          // resize via createImageBitmap (only FF at this moment)\n          ww: false // webworkers\n        };\n        this.__workersPool = null; // Store requested features for webworkers\n\n        this.__requested_features = [];\n        this.__mathlib = null;\n      }\n      Pica.prototype.init = function () {\n        var _this = this;\n        if (this.__initPromise) return this.__initPromise; // Test if we can create ImageData without canvas and memory copy\n\n        if (typeof ImageData !== 'undefined' && typeof Uint8ClampedArray !== 'undefined') {\n          try {\n            /* eslint-disable no-new */\n            new ImageData(new Uint8ClampedArray(400), 10, 10);\n            CAN_NEW_IMAGE_DATA = true;\n          } catch (__) {}\n        } // ImageBitmap can be effective in 2 places:\n        //\n        // 1. Threaded jpeg unpack (basic)\n        // 2. Built-in resize (blocked due problem in chrome, see issue #89)\n        //\n        // For basic use we also need ImageBitmap wo support .close() method,\n        // see https://developer.mozilla.org/ru/docs/Web/API/ImageBitmap\n\n        if (typeof ImageBitmap !== 'undefined') {\n          if (ImageBitmap.prototype && ImageBitmap.prototype.close) {\n            CAN_CREATE_IMAGE_BITMAP = true;\n          } else {\n            this.debug('ImageBitmap does not support .close(), disabled');\n          }\n        }\n        var features = this.options.features.slice();\n        if (features.indexOf('all') >= 0) {\n          features = ['cib', 'wasm', 'js', 'ww'];\n        }\n        this.__requested_features = features;\n        this.__mathlib = new MathLib(features); // Check WebWorker support if requested\n\n        if (features.indexOf('ww') >= 0) {\n          if (typeof window !== 'undefined' && 'Worker' in window) {\n            // IE <= 11 don't allow to create webworkers from string. We should check it.\n            // https://connect.microsoft.com/IE/feedback/details/801810/web-workers-from-blob-urls-in-ie-10-and-11\n            try {\n              var wkr = _dereq_('webworkify')(function () {});\n              wkr.terminate();\n              this.features.ww = true; // pool uniqueness depends on pool config + webworker config\n\n              var wpool_key = \"wp_\".concat(JSON.stringify(this.options));\n              if (singletones[wpool_key]) {\n                this.__workersPool = singletones[wpool_key];\n              } else {\n                this.__workersPool = new Pool(workerFabric, this.options.idle);\n                singletones[wpool_key] = this.__workersPool;\n              }\n            } catch (__) {}\n          }\n        }\n        var initMath = this.__mathlib.init().then(function (mathlib) {\n          // Copy detected features\n          assign(_this.features, mathlib.features);\n        });\n        var checkCibResize;\n        if (!CAN_CREATE_IMAGE_BITMAP) {\n          checkCibResize = Promise.resolve(false);\n        } else {\n          checkCibResize = utils.cib_support(this.options.createCanvas).then(function (status) {\n            if (_this.features.cib && features.indexOf('cib') < 0) {\n              _this.debug('createImageBitmap() resize supported, but disabled by config');\n              return;\n            }\n            if (features.indexOf('cib') >= 0) _this.features.cib = status;\n          });\n        }\n        CAN_USE_CANVAS_GET_IMAGE_DATA = utils.can_use_canvas(this.options.createCanvas);\n        var checkOffscreenCanvas;\n        if (CAN_CREATE_IMAGE_BITMAP && CAN_NEW_IMAGE_DATA && features.indexOf('ww') !== -1) {\n          checkOffscreenCanvas = utils.worker_offscreen_canvas_support();\n        } else {\n          checkOffscreenCanvas = Promise.resolve(false);\n        }\n        checkOffscreenCanvas = checkOffscreenCanvas.then(function (result) {\n          CAN_USE_OFFSCREEN_CANVAS = result;\n        }); // we use createImageBitmap to crop image data and pass it to workers,\n        // so need to check whether function works correctly;\n        // https://bugs.chromium.org/p/chromium/issues/detail?id=1220671\n\n        var checkCibRegion = utils.cib_can_use_region().then(function (result) {\n          CAN_USE_CIB_REGION_FOR_IMAGE = result;\n        }); // Init math lib. That's async because can load some\n\n        this.__initPromise = Promise.all([initMath, checkCibResize, checkOffscreenCanvas, checkCibRegion]).then(function () {\n          return _this;\n        });\n        return this.__initPromise;\n      }; // Call resizer in webworker or locally, depending on config\n\n      Pica.prototype.__invokeResize = function (tileOpts, opts) {\n        var _this2 = this;\n\n        // Share cache between calls:\n        //\n        // - wasm instance\n        // - wasm memory object\n        //\n        opts.__mathCache = opts.__mathCache || {};\n        return Promise.resolve().then(function () {\n          if (!_this2.features.ww) {\n            // not possible to have ImageBitmap here if user disabled WW\n            return {\n              data: _this2.__mathlib.resizeAndUnsharp(tileOpts, opts.__mathCache)\n            };\n          }\n          return new Promise(function (resolve, reject) {\n            var w = _this2.__workersPool.acquire();\n            if (opts.cancelToken) opts.cancelToken[\"catch\"](function (err) {\n              return reject(err);\n            });\n            w.value.onmessage = function (ev) {\n              w.release();\n              if (ev.data.err) reject(ev.data.err);else resolve(ev.data);\n            };\n            var transfer = [];\n            if (tileOpts.src) transfer.push(tileOpts.src.buffer);\n            if (tileOpts.srcBitmap) transfer.push(tileOpts.srcBitmap);\n            w.value.postMessage({\n              opts: tileOpts,\n              features: _this2.__requested_features,\n              preload: {\n                wasm_nodule: _this2.__mathlib.__\n              }\n            }, transfer);\n          });\n        });\n      }; // this function can return promise if createImageBitmap is used\n\n      Pica.prototype.__extractTileData = function (tile, from, opts, stageEnv, extractTo) {\n        if (this.features.ww && CAN_USE_OFFSCREEN_CANVAS && (\n        // createImageBitmap doesn't work for images (Image, ImageBitmap) with Exif orientation in Chrome,\n        // can use canvas because canvas doesn't have orientation;\n        // see https://bugs.chromium.org/p/chromium/issues/detail?id=1220671\n        utils.isCanvas(from) || CAN_USE_CIB_REGION_FOR_IMAGE)) {\n          this.debug('Create tile for OffscreenCanvas');\n          return createImageBitmap(stageEnv.srcImageBitmap || from, tile.x, tile.y, tile.width, tile.height).then(function (bitmap) {\n            extractTo.srcBitmap = bitmap;\n            return extractTo;\n          });\n        } // Extract tile RGBA buffer, depending on input type\n\n        if (utils.isCanvas(from)) {\n          if (!stageEnv.srcCtx) stageEnv.srcCtx = from.getContext('2d'); // If input is Canvas - extract region data directly\n\n          this.debug('Get tile pixel data');\n          extractTo.src = stageEnv.srcCtx.getImageData(tile.x, tile.y, tile.width, tile.height).data;\n          return extractTo;\n        } // If input is Image or decoded to ImageBitmap,\n        // draw region to temporary canvas and extract data from it\n        //\n        // Note! Attempt to reuse this canvas causes significant slowdown in chrome\n        //\n\n        this.debug('Draw tile imageBitmap/image to temporary canvas');\n        var tmpCanvas = this.options.createCanvas(tile.width, tile.height);\n        var tmpCtx = tmpCanvas.getContext('2d');\n        tmpCtx.globalCompositeOperation = 'copy';\n        tmpCtx.drawImage(stageEnv.srcImageBitmap || from, tile.x, tile.y, tile.width, tile.height, 0, 0, tile.width, tile.height);\n        this.debug('Get tile pixel data');\n        extractTo.src = tmpCtx.getImageData(0, 0, tile.width, tile.height).data; // Safari 12 workaround\n        // https://github.com/nodeca/pica/issues/199\n\n        tmpCanvas.width = tmpCanvas.height = 0;\n        return extractTo;\n      };\n      Pica.prototype.__landTileData = function (tile, result, stageEnv) {\n        var toImageData;\n        this.debug('Convert raw rgba tile result to ImageData');\n        if (result.bitmap) {\n          stageEnv.toCtx.drawImage(result.bitmap, tile.toX, tile.toY);\n          return null;\n        }\n        if (CAN_NEW_IMAGE_DATA) {\n          // this branch is for modern browsers\n          // If `new ImageData()` & Uint8ClampedArray suported\n          toImageData = new ImageData(new Uint8ClampedArray(result.data), tile.toWidth, tile.toHeight);\n        } else {\n          // fallback for `node-canvas` and old browsers\n          // (IE11 has ImageData but does not support `new ImageData()`)\n          toImageData = stageEnv.toCtx.createImageData(tile.toWidth, tile.toHeight);\n          if (toImageData.data.set) {\n            toImageData.data.set(result.data);\n          } else {\n            // IE9 don't have `.set()`\n            for (var i = toImageData.data.length - 1; i >= 0; i--) {\n              toImageData.data[i] = result.data[i];\n            }\n          }\n        }\n        this.debug('Draw tile');\n        if (NEED_SAFARI_FIX) {\n          // Safari draws thin white stripes between tiles without this fix\n          stageEnv.toCtx.putImageData(toImageData, tile.toX, tile.toY, tile.toInnerX - tile.toX, tile.toInnerY - tile.toY, tile.toInnerWidth + 1e-5, tile.toInnerHeight + 1e-5);\n        } else {\n          stageEnv.toCtx.putImageData(toImageData, tile.toX, tile.toY, tile.toInnerX - tile.toX, tile.toInnerY - tile.toY, tile.toInnerWidth, tile.toInnerHeight);\n        }\n        return null;\n      };\n      Pica.prototype.__tileAndResize = function (from, to, opts) {\n        var _this3 = this;\n        var stageEnv = {\n          srcCtx: null,\n          srcImageBitmap: null,\n          isImageBitmapReused: false,\n          toCtx: null\n        };\n        var processTile = function processTile(tile) {\n          return _this3.__limit(function () {\n            if (opts.canceled) return opts.cancelToken;\n            var tileOpts = {\n              width: tile.width,\n              height: tile.height,\n              toWidth: tile.toWidth,\n              toHeight: tile.toHeight,\n              scaleX: tile.scaleX,\n              scaleY: tile.scaleY,\n              offsetX: tile.offsetX,\n              offsetY: tile.offsetY,\n              filter: opts.filter,\n              unsharpAmount: opts.unsharpAmount,\n              unsharpRadius: opts.unsharpRadius,\n              unsharpThreshold: opts.unsharpThreshold\n            };\n            _this3.debug('Invoke resize math');\n            return Promise.resolve(tileOpts).then(function (tileOpts) {\n              return _this3.__extractTileData(tile, from, opts, stageEnv, tileOpts);\n            }).then(function (tileOpts) {\n              _this3.debug('Invoke resize math');\n              return _this3.__invokeResize(tileOpts, opts);\n            }).then(function (result) {\n              if (opts.canceled) return opts.cancelToken;\n              stageEnv.srcImageData = null;\n              return _this3.__landTileData(tile, result, stageEnv);\n            });\n          });\n        }; // Need to normalize data source first. It can be canvas or image.\n        // If image - try to decode in background if possible\n\n        return Promise.resolve().then(function () {\n          stageEnv.toCtx = to.getContext('2d');\n          if (utils.isCanvas(from)) return null;\n          if (utils.isImageBitmap(from)) {\n            stageEnv.srcImageBitmap = from;\n            stageEnv.isImageBitmapReused = true;\n            return null;\n          }\n          if (utils.isImage(from)) {\n            // try do decode image in background for faster next operations;\n            // if we're using offscreen canvas, cib is called per tile, so not needed here\n            if (!CAN_CREATE_IMAGE_BITMAP) return null;\n            _this3.debug('Decode image via createImageBitmap');\n            return createImageBitmap(from).then(function (imageBitmap) {\n              stageEnv.srcImageBitmap = imageBitmap;\n            }) // Suppress error to use fallback, if method fails\n            // https://github.com/nodeca/pica/issues/190\n\n            /* eslint-disable no-unused-vars */[\"catch\"](function (e) {\n              return null;\n            });\n          }\n          throw new Error('Pica: \".from\" should be Image, Canvas or ImageBitmap');\n        }).then(function () {\n          if (opts.canceled) return opts.cancelToken;\n          _this3.debug('Calculate tiles'); //\n          // Here we are with \"normalized\" source,\n          // follow to tiling\n          //\n\n          var regions = createRegions({\n            width: opts.width,\n            height: opts.height,\n            srcTileSize: _this3.options.tile,\n            toWidth: opts.toWidth,\n            toHeight: opts.toHeight,\n            destTileBorder: opts.__destTileBorder\n          });\n          var jobs = regions.map(function (tile) {\n            return processTile(tile);\n          });\n          function cleanup(stageEnv) {\n            if (stageEnv.srcImageBitmap) {\n              if (!stageEnv.isImageBitmapReused) stageEnv.srcImageBitmap.close();\n              stageEnv.srcImageBitmap = null;\n            }\n          }\n          _this3.debug('Process tiles');\n          return Promise.all(jobs).then(function () {\n            _this3.debug('Finished!');\n            cleanup(stageEnv);\n            return to;\n          }, function (err) {\n            cleanup(stageEnv);\n            throw err;\n          });\n        });\n      };\n      Pica.prototype.__processStages = function (stages, from, to, opts) {\n        var _this4 = this;\n        if (opts.canceled) return opts.cancelToken;\n        var _stages$shift = stages.shift(),\n          _stages$shift2 = _slicedToArray(_stages$shift, 2),\n          toWidth = _stages$shift2[0],\n          toHeight = _stages$shift2[1];\n        var isLastStage = stages.length === 0; // Optimization for legacy filters -\n        // only use user-defined quality for the last stage,\n        // use simpler (Hamming) filter for the first stages where\n        // scale factor is large enough (more than 2-3)\n        //\n        // For advanced filters (mks2013 and custom) - skip optimization,\n        // because need to apply sharpening every time\n\n        var filter;\n        if (isLastStage || filter_info.q2f.indexOf(opts.filter) < 0) filter = opts.filter;else if (opts.filter === 'box') filter = 'box';else filter = 'hamming';\n        opts = assign({}, opts, {\n          toWidth: toWidth,\n          toHeight: toHeight,\n          filter: filter\n        });\n        var tmpCanvas;\n        if (!isLastStage) {\n          // create temporary canvas\n          tmpCanvas = this.options.createCanvas(toWidth, toHeight);\n        }\n        return this.__tileAndResize(from, isLastStage ? to : tmpCanvas, opts).then(function () {\n          if (isLastStage) return to;\n          opts.width = toWidth;\n          opts.height = toHeight;\n          return _this4.__processStages(stages, tmpCanvas, to, opts);\n        }).then(function (res) {\n          if (tmpCanvas) {\n            // Safari 12 workaround\n            // https://github.com/nodeca/pica/issues/199\n            tmpCanvas.width = tmpCanvas.height = 0;\n          }\n          return res;\n        });\n      };\n      Pica.prototype.__resizeViaCreateImageBitmap = function (from, to, opts) {\n        var _this5 = this;\n        var toCtx = to.getContext('2d');\n        this.debug('Resize via createImageBitmap()');\n        return createImageBitmap(from, {\n          resizeWidth: opts.toWidth,\n          resizeHeight: opts.toHeight,\n          resizeQuality: utils.cib_quality_name(filter_info.f2q[opts.filter])\n        }).then(function (imageBitmap) {\n          if (opts.canceled) return opts.cancelToken; // if no unsharp - draw directly to output canvas\n\n          if (!opts.unsharpAmount) {\n            toCtx.drawImage(imageBitmap, 0, 0);\n            imageBitmap.close();\n            toCtx = null;\n            _this5.debug('Finished!');\n            return to;\n          }\n          _this5.debug('Unsharp result');\n          var tmpCanvas = _this5.options.createCanvas(opts.toWidth, opts.toHeight);\n          var tmpCtx = tmpCanvas.getContext('2d');\n          tmpCtx.drawImage(imageBitmap, 0, 0);\n          imageBitmap.close();\n          var iData = tmpCtx.getImageData(0, 0, opts.toWidth, opts.toHeight);\n          _this5.__mathlib.unsharp_mask(iData.data, opts.toWidth, opts.toHeight, opts.unsharpAmount, opts.unsharpRadius, opts.unsharpThreshold);\n          toCtx.putImageData(iData, 0, 0); // Safari 12 workaround\n          // https://github.com/nodeca/pica/issues/199\n\n          tmpCanvas.width = tmpCanvas.height = 0;\n          iData = tmpCtx = tmpCanvas = toCtx = null;\n          _this5.debug('Finished!');\n          return to;\n        });\n      };\n      Pica.prototype.resize = function (from, to, options) {\n        var _this6 = this;\n        this.debug('Start resize...');\n        var opts = assign({}, DEFAULT_RESIZE_OPTS);\n        if (!isNaN(options)) {\n          opts = assign(opts, {\n            quality: options\n          });\n        } else if (options) {\n          opts = assign(opts, options);\n        }\n        opts.toWidth = to.width;\n        opts.toHeight = to.height;\n        opts.width = from.naturalWidth || from.width;\n        opts.height = from.naturalHeight || from.height; // Legacy `.quality` option\n\n        if (Object.prototype.hasOwnProperty.call(opts, 'quality')) {\n          if (opts.quality < 0 || opts.quality > 3) {\n            throw new Error(\"Pica: .quality should be [0..3], got \".concat(opts.quality));\n          }\n          opts.filter = filter_info.q2f[opts.quality];\n        } // Prevent stepper from infinite loop\n\n        if (to.width === 0 || to.height === 0) {\n          return Promise.reject(new Error(\"Invalid output size: \".concat(to.width, \"x\").concat(to.height)));\n        }\n        if (opts.unsharpRadius > 2) opts.unsharpRadius = 2;\n        opts.canceled = false;\n        if (opts.cancelToken) {\n          // Wrap cancelToken to avoid successive resolve & set flag\n          opts.cancelToken = opts.cancelToken.then(function (data) {\n            opts.canceled = true;\n            throw data;\n          }, function (err) {\n            opts.canceled = true;\n            throw err;\n          });\n        }\n        var DEST_TILE_BORDER = 3; // Max possible filter window size\n\n        opts.__destTileBorder = Math.ceil(Math.max(DEST_TILE_BORDER, 2.5 * opts.unsharpRadius | 0));\n        return this.init().then(function () {\n          if (opts.canceled) return opts.cancelToken; // if createImageBitmap supports resize, just do it and return\n\n          if (_this6.features.cib) {\n            if (filter_info.q2f.indexOf(opts.filter) >= 0) {\n              return _this6.__resizeViaCreateImageBitmap(from, to, opts);\n            }\n            _this6.debug('cib is enabled, but not supports provided filter, fallback to manual math');\n          }\n          if (!CAN_USE_CANVAS_GET_IMAGE_DATA) {\n            var err = new Error('Pica: cannot use getImageData on canvas, ' + \"make sure fingerprinting protection isn't enabled\");\n            err.code = 'ERR_GET_IMAGE_DATA';\n            throw err;\n          } //\n          // No easy way, let's resize manually via arrays\n          //\n\n          var stages = createStages(opts.width, opts.height, opts.toWidth, opts.toHeight, _this6.options.tile, opts.__destTileBorder);\n          return _this6.__processStages(stages, from, to, opts);\n        });\n      }; // RGBA buffer resize\n      //\n\n      Pica.prototype.resizeBuffer = function (options) {\n        var _this7 = this;\n        var opts = assign({}, DEFAULT_RESIZE_OPTS, options); // Legacy `.quality` option\n\n        if (Object.prototype.hasOwnProperty.call(opts, 'quality')) {\n          if (opts.quality < 0 || opts.quality > 3) {\n            throw new Error(\"Pica: .quality should be [0..3], got \".concat(opts.quality));\n          }\n          opts.filter = filter_info.q2f[opts.quality];\n        }\n        return this.init().then(function () {\n          return _this7.__mathlib.resizeAndUnsharp(opts);\n        });\n      };\n      Pica.prototype.toBlob = function (canvas, mimeType, quality) {\n        mimeType = mimeType || 'image/png';\n        return new Promise(function (resolve) {\n          if (canvas.toBlob) {\n            canvas.toBlob(function (blob) {\n              return resolve(blob);\n            }, mimeType, quality);\n            return;\n          }\n          if (canvas.convertToBlob) {\n            resolve(canvas.convertToBlob({\n              type: mimeType,\n              quality: quality\n            }));\n            return;\n          } // Fallback for old browsers\n\n          var asString = atob(canvas.toDataURL(mimeType, quality).split(',')[1]);\n          var len = asString.length;\n          var asBuffer = new Uint8Array(len);\n          for (var i = 0; i < len; i++) {\n            asBuffer[i] = asString.charCodeAt(i);\n          }\n          resolve(new Blob([asBuffer], {\n            type: mimeType\n          }));\n        });\n      };\n      Pica.prototype.debug = function () {};\n      module.exports = Pica;\n    }, {\n      \"./lib/mathlib\": 1,\n      \"./lib/mm_resize/resize_filter_info\": 7,\n      \"./lib/pool\": 13,\n      \"./lib/stepper\": 14,\n      \"./lib/tiler\": 15,\n      \"./lib/utils\": 16,\n      \"./lib/worker\": 17,\n      \"object-assign\": 22,\n      \"webworkify\": 23\n    }]\n  }, {}, [])(\"/index.js\");\n});", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}