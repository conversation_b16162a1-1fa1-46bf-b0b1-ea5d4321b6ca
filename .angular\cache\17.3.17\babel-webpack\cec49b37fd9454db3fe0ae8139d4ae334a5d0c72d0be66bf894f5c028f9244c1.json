{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.switchScan = void 0;\nvar switchMap_1 = require(\"./switchMap\");\nvar lift_1 = require(\"../util/lift\");\nfunction switchScan(accumulator, seed) {\n  return lift_1.operate(function (source, subscriber) {\n    var state = seed;\n    switchMap_1.switchMap(function (value, index) {\n      return accumulator(state, value, index);\n    }, function (_, innerValue) {\n      return state = innerValue, innerValue;\n    })(source).subscribe(subscriber);\n    return function () {\n      state = null;\n    };\n  });\n}\nexports.switchScan = switchScan;\n//# sourceMappingURL=switchScan.js.map", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}