{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.createFind = exports.find = void 0;\nvar lift_1 = require(\"../util/lift\");\nvar OperatorSubscriber_1 = require(\"./OperatorSubscriber\");\nfunction find(predicate, thisArg) {\n  return lift_1.operate(createFind(predicate, thisArg, 'value'));\n}\nexports.find = find;\nfunction createFind(predicate, thisArg, emit) {\n  var findIndex = emit === 'index';\n  return function (source, subscriber) {\n    var index = 0;\n    source.subscribe(OperatorSubscriber_1.createOperatorSubscriber(subscriber, function (value) {\n      var i = index++;\n      if (predicate.call(thisArg, value, i, source)) {\n        subscriber.next(findIndex ? i : value);\n        subscriber.complete();\n      }\n    }, function () {\n      subscriber.next(findIndex ? -1 : undefined);\n      subscriber.complete();\n    }));\n  };\n}\nexports.createFind = createFind;\n//# sourceMappingURL=find.js.map", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}