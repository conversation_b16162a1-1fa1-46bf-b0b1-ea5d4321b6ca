{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Vietnamese [vi]\n//! author : <PERSON> : https://github.com/bangnk\n//! author : <PERSON><PERSON> Kira : https://github.com/chienkira\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  var vi = moment.defineLocale('vi', {\n    months: 'tháng 1_tháng 2_tháng 3_tháng 4_tháng 5_tháng 6_tháng 7_tháng 8_tháng 9_tháng 10_tháng 11_tháng 12'.split('_'),\n    monthsShort: 'Thg 01_Thg 02_Thg 03_Thg 04_Thg 05_Thg 06_Thg 07_Thg 08_Thg 09_Thg 10_Thg 11_Thg 12'.split('_'),\n    monthsParseExact: true,\n    weekdays: 'chủ nhật_thứ hai_thứ ba_thứ tư_thứ năm_thứ sáu_thứ bảy'.split('_'),\n    weekdaysShort: 'CN_T2_T3_T4_T5_T6_T7'.split('_'),\n    weekdaysMin: 'CN_T2_T3_T4_T5_T6_T7'.split('_'),\n    weekdaysParseExact: true,\n    meridiemParse: /sa|ch/i,\n    isPM: function (input) {\n      return /^ch$/i.test(input);\n    },\n    meridiem: function (hours, minutes, isLower) {\n      if (hours < 12) {\n        return isLower ? 'sa' : 'SA';\n      } else {\n        return isLower ? 'ch' : 'CH';\n      }\n    },\n    longDateFormat: {\n      LT: 'HH:mm',\n      LTS: 'HH:mm:ss',\n      L: 'DD/MM/YYYY',\n      LL: 'D MMMM [năm] YYYY',\n      LLL: 'D MMMM [năm] YYYY HH:mm',\n      LLLL: 'dddd, D MMMM [năm] YYYY HH:mm',\n      l: 'DD/M/YYYY',\n      ll: 'D MMM YYYY',\n      lll: 'D MMM YYYY HH:mm',\n      llll: 'ddd, D MMM YYYY HH:mm'\n    },\n    calendar: {\n      sameDay: '[Hôm nay lúc] LT',\n      nextDay: '[Ngày mai lúc] LT',\n      nextWeek: 'dddd [tuần tới lúc] LT',\n      lastDay: '[Hôm qua lúc] LT',\n      lastWeek: 'dddd [tuần trước lúc] LT',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: '%s tới',\n      past: '%s trước',\n      s: 'vài giây',\n      ss: '%d giây',\n      m: 'một phút',\n      mm: '%d phút',\n      h: 'một giờ',\n      hh: '%d giờ',\n      d: 'một ngày',\n      dd: '%d ngày',\n      w: 'một tuần',\n      ww: '%d tuần',\n      M: 'một tháng',\n      MM: '%d tháng',\n      y: 'một năm',\n      yy: '%d năm'\n    },\n    dayOfMonthOrdinalParse: /\\d{1,2}/,\n    ordinal: function (number) {\n      return number;\n    },\n    week: {\n      dow: 1,\n      // Monday is the first day of the week.\n      doy: 4 // The week that contains Jan 4th is the first week of the year.\n    }\n  });\n  return vi;\n});", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}