{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.findIndex = void 0;\nvar lift_1 = require(\"../util/lift\");\nvar find_1 = require(\"./find\");\nfunction findIndex(predicate, thisArg) {\n  return lift_1.operate(find_1.createFind(predicate, thisArg, 'index'));\n}\nexports.findIndex = findIndex;\n//# sourceMappingURL=findIndex.js.map", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}