{"ast": null, "code": "import { HttpClient, HttpEventType } from '@angular/common/http';\nimport { ChangeDetectorRef, TemplateRef } from '@angular/core';\nimport { FormBuilder } from '@angular/forms';\nimport { Router } from '@angular/router';\nimport { NgbCalendar } from '@ng-bootstrap/ng-bootstrap';\nimport { Angular5Csv } from 'angular5-csv/dist/Angular5-csv';\nimport { Ng2ImgMaxService } from 'ng2-img-max';\nimport { BsModalService } from 'ngx-bootstrap/modal';\nimport { WebapiService } from '../webapi.service';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"ng2-img-max\";\nimport * as i2 from \"ngx-bootstrap/modal\";\nimport * as i3 from \"@angular/forms\";\nimport * as i4 from \"@angular/common/http\";\nimport * as i5 from \"../webapi.service\";\nimport * as i6 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i7 from \"@angular/router\";\nexport let CompleteINVComponent = /*#__PURE__*/(() => {\n  class CompleteINVComponent {\n    getColor(country, stRemark, CK) {\n      var stcolor;\n      if (country === 'อนุมัติแล้ว' && stRemark == 0 && CK === 'อนุมัติแล้ว') {\n        stcolor = 'อนุมัติแล้ว';\n      } else if (country === 'รออนุมัติ' && stRemark == 0 && CK === 'รออนุมัติ') {\n        stcolor = 'รออนุมัติใหม่';\n      } else if (country === 'รออนุมัติ' && stRemark == 1 && CK === 'รอbookbank') {\n        stcolor = 'รออนุมัติ';\n      } else if (country === 'รออนุมัติ' && stRemark == 0 && CK === 'รอbookbank') {\n        stcolor = 'รออนุมัติ';\n      } else {\n        stcolor = 'ผิดปกติ';\n      }\n      switch (stcolor) {\n        case 'อนุมัติแล้ว':\n          return 'green';\n        case 'รออนุมัติ':\n          return 'red';\n        case 'ผิดปกติ':\n          return '#ff9900';\n        case 'รออนุมัติใหม่':\n          return '#0e0dde';\n      }\n    }\n    getColor2(country) {\n      if (country == 1) {\n        country = false;\n      } else {\n        country = true;\n      }\n      switch (country) {\n        case true:\n          return 'green';\n        case false:\n          return '#ff9900';\n      }\n    }\n    getColorbookbank(country, bookbank) {\n      if (country == 1 && bookbank != 'A') {\n        country = '1';\n      } else if (country == 1 && bookbank === 'A') {\n        country = '1';\n      } else if (country == 0 && bookbank !== 'A') {\n        country = '2';\n      } else if (country == 0 && bookbank === 'A') {\n        country = '3';\n      } else {\n        country = '3';\n      }\n      switch (country) {\n        case '1':\n          return '#ff9900';\n        case '2':\n          return '#0e0dde';\n        case '3':\n          return 'green';\n      }\n    }\n    constructor(ng2ImgMax, modalService, fb, chRef, http, service, calendar, router) {\n      this.ng2ImgMax = ng2ImgMax;\n      this.modalService = modalService;\n      this.fb = fb;\n      this.chRef = chRef;\n      this.http = http;\n      this.service = service;\n      this.calendar = calendar;\n      this.router = router;\n      this.disabled = false;\n      this.ShowFilter = false;\n      this.limitSelection = false;\n      this.dropdownSettings = {};\n      this.options = {\n        fieldSeparator: ',',\n        quoteStrings: '\"',\n        decimalseparator: '.',\n        showLabels: true,\n        showTitle: true,\n        useBom: true,\n        noDownload: false,\n        headers: [\"SALESGROUP\", \"INVOICE NO\", \"Customer NO\", \"Customer Name\", \"INVOICEDATE\", \"SaleOrder\", \"เงือนไข\", \"DUEDATE\", \"เงินก่อนภาษี\", \"VAT\", \"รวมทั้งสิ้น\", \"สถานะ Approve\", \"Remark\"]\n      };\n      this.Bill_approve = [];\n      this.BillId = [];\n      this.groupimg = [];\n      this.mdlSampleIsOpen = false;\n      this.alt = '';\n      this.checkreload = true;\n      this.mdlSampleIsOpenApprove = false;\n      this.altApprove = '';\n      this.checkreloadApprove = true;\n      this.mdlSampleIsOpenBill = false;\n      this.altBill = '';\n      this.checkreloadBill = true;\n      this.mdlSampleIsOpen2 = false;\n      this.alt2 = '';\n      this.checkreload2 = true;\n      this.mdlSampleIsOpen3 = false;\n      this.alt3 = '';\n      this.checkreload3 = true;\n      this.mdlSampleIsOpensuccess = false;\n      this.altsuccess = '';\n      this.checkreloadsuccess = true;\n      this.customer = '';\n      this.Paymenttype = [];\n      this.StApprove = [];\n      this.selectPayment = '';\n      this.selectApprove = '';\n      this.paymenttype = [{\n        idpayment: '0',\n        value: 'เงินสด'\n      }, {\n        idpayment: '1',\n        value: 'เครดิส'\n      }];\n      this.Modelpaymenttype = {\n        id: '',\n        value: ''\n      };\n      this.stApprove = [{\n        idApprove: '1',\n        value: 'Approval แล้ว'\n      }, {\n        idApprove: '0',\n        value: 'ยังไม่ได้ Approve'\n      }];\n      this.ModelStApprove = {\n        id: '',\n        value: ''\n      };\n      this.ImageIN = '../assets/img/default-image.png';\n      this.ImageBill = 'default-image.png';\n      this.ImageBookbank = '';\n      this.ImageBillno = '';\n      this.test2 = \"BI18-02557-2018-8-14-400.jpg\";\n      this.imgtest = \"../../assets/imageBill/\";\n      this.showtest = this.imgtest + this.test2;\n      this.keyboardStrApprove = '';\n      this.keyboardStrpaymenttype = '';\n      this.total = 0;\n      this.load = '';\n      this.productprice = 0;\n      this.sumvat = 0;\n      this.sumprice = 0;\n      this.trueproductprice = 0;\n      this.truesumvat = 0;\n      this.truesumprice = 0;\n      this.dateshipping = '';\n      this.dateshippingto = '';\n      this.testclose = false;\n      this.dataTable = [];\n      this.mdlSampleIsOpenimg = false;\n      this.altimg = '';\n      this.checkreloadimg = true;\n      this.fromdate = '';\n      this.todate = '';\n      this.SearchCompleteED_ = false;\n      this.SearchComplete_ = false;\n      this.permisstiondata = [];\n      this.exportbtn = true;\n      this.searchbtn = true;\n      this.config = {\n        ignoreBackdropClick: true,\n        class: 'modal-md'\n      };\n      this.configview = {\n        ignoreBackdropClick: true,\n        class: 'modal-lg '\n      };\n      this.textload = \"\";\n      this.selectedFile = null;\n      this.namebookbank = '';\n      this.btnPDF = false;\n      this.btnREpdf = false;\n      this.isCollapsed = true;\n      this.imageUrl = \"assets/img/default-image.png\";\n      this.urlimgBookbank = '';\n      this.CK = false;\n      this.staticAlertClosed = false;\n      this.urlimgDefault = '';\n      this.printst = '3';\n      this.headerlist = [];\n      this.seachheaderlist = [];\n      this.viewBookbank = 'A';\n      this.Bookbank = [];\n      this.INVSuccess = [];\n      this.Btnimg = false;\n      this.url = service.geturlservice();\n      localStorage.removeItem('DataSOderreview');\n      localStorage.removeItem('DataSOderlist');\n      this.urlimg = service.geturlserviceIMG();\n      this.urlimgDefault = service.geturlimgDefault();\n      this.urlimgBookbank = service.geturlserviceIMGbookbank();\n      this.fromDate = calendar.getPrev(calendar.getToday(), 'd', 365);\n      this.toDate = calendar.getToday();\n      this.datalogin = JSON.parse(sessionStorage.getItem('login'));\n      if (this.datalogin == null) {\n        this.router.navigate(['login']);\n      }\n      this.getuser();\n      this.selectPayment = '';\n      this.selectApprove = '';\n      this.dateinvid = '';\n      this.dataExport = '';\n      this.calendarimg = \"../../assets/img/if_Paul-27_2534341.ico\";\n      //  this.groupsale=this.datalogin;\n      this.getgroupsaleman();\n      this.Datatodate = new Date(this.toDate.year, this.toDate.month - 1, this.toDate.day);\n      this.Datafromdate = new Date(this.fromDate.year, this.fromDate.month - 1, this.fromDate.day);\n      this.permisstiondata = JSON.parse(sessionStorage.getItem('menu'));\n      this.exportbtn = !this.permisstiondata[9].flag_print;\n      this.searchbtn = !this.permisstiondata[9].flag_action;\n    }\n    ngOnInit() {}\n    searchviewBookbank() {\n      let Ck = false;\n      this.testcheck = Ck;\n      this.selectAll(this.testcheck);\n      if (this.viewBookbank === '3') {\n        this.Bookbank = this.BillId;\n      } else {\n        if (this.viewBookbank === 'A') {\n          // alert('ทำงาน')\n          this.Bookbank = this.BillId.filter(v => v.checkbank.toLowerCase().indexOf(this.viewBookbank.toLowerCase()) > -1).slice(0, 50);\n        } else {\n          this.Bookbank = this.BillId.filter(v => v.checkbank.toLowerCase().indexOf(this.viewBookbank.toLowerCase()) > -1).slice(0, 50);\n        }\n      }\n    }\n    searchSuccess(data) {\n      if (this.dateinvid === '') {\n        // alert(this.dateinvid);\n        this.INVSuccess = this.BillId;\n      } else {\n        this.INVSuccess = this.BillId.filter(v => v.invoiceid.toLowerCase().indexOf(this.dateinvid.toLowerCase()) > -1).slice(0, 50);\n      }\n    }\n    searchselectprint() {\n      if (this.printst === '3') {\n        this.seachheaderlist = this.dataComplete;\n        this.productprice = 0;\n        this.sumvat = 0;\n        this.sumprice = 0;\n        if (this.seachheaderlist.length > 0) {\n          for (var i = 0; i < this.seachheaderlist.length; i++) {\n            this.productprice += this.seachheaderlist[i].Salesbalance;\n            this.sumvat += this.seachheaderlist[i].Sumtax;\n            this.sumprice += this.seachheaderlist[i].Invoiceamount;\n          }\n        }\n      } else {\n        //alert(JSON.stringify(this.headerlist[0]));\n        this.seachheaderlist = this.dataComplete.filter(v => v.CK.toLowerCase().indexOf(this.printst.toLowerCase()) > -1).slice(0, 50);\n        this.productprice = 0;\n        this.sumvat = 0;\n        this.sumprice = 0;\n        if (this.seachheaderlist.length > 0) {\n          for (var i = 0; i < this.seachheaderlist.length; i++) {\n            this.productprice += this.seachheaderlist[i].Salesbalance;\n            this.sumvat += this.seachheaderlist[i].Sumtax;\n            this.sumprice += this.seachheaderlist[i].Invoiceamount;\n          }\n        }\n      }\n    }\n    //[{\"id_group_user\":\"153Admin\",\"id_user\":\"701MaxZa001\",\"name_user\":\"MaxISR\",\"email\":\"<EMAIL>\",\"mobile\":\"0964454540\",\"login_user\":\"admin\",\"salegroup\":\"admin\",\"flag_close\":0}]\n    getuser() {\n      if (this.datalogin[0].id_group_user == '153Admin') {\n        this.datasalegroup = '';\n        this.testclose = true;\n      } else {\n        this.testclose = false;\n        this.datasalegroup = this.datalogin[0].salegroup;\n      }\n    }\n    getgroupsaleman() {\n      this.DateGroupsaleman = [];\n      this.http.get(this.url + 'salesman').subscribe(res => {\n        if (res.length > 0) {\n          this.DateGroupsaleman = res;\n        } else {\n          alert('ไม่สามารถติดต่อข้อมูลรหัส saleman ได้');\n        }\n      });\n    }\n    onItemSelect(item) {\n      console.log('onItemSelect', item);\n    }\n    onSelectAll(items) {\n      console.log('onSelectAll', items);\n    }\n    toogleShowFilter() {\n      this.ShowFilter = !this.ShowFilter;\n      this.dropdownSettings = Object.assign({}, this.dropdownSettings, {\n        allowSearchFilter: this.ShowFilter\n      });\n    }\n    handleLimitSelection() {\n      if (this.limitSelection) {\n        this.dropdownSettings = Object.assign({}, this.dropdownSettings, {\n          limitSelection: 2\n        });\n      } else {\n        this.dropdownSettings = Object.assign({}, this.dropdownSettings, {\n          limitSelection: null\n        });\n      }\n    }\n    getdate() {\n      this.fromdate = `${this.Datafromdate.getFullYear()}-${this.Datafromdate.getMonth() + 1}-${this.Datafromdate.getDate()}`;\n      this.todate = `${this.Datatodate.getFullYear()}-${this.Datatodate.getMonth() + 1}-${this.Datatodate.getDate()}`;\n    }\n    getpaymenttype() {\n      this.http.get(this.url + 'paymenttype').subscribe(res => {\n        alert(JSON.stringify(res));\n        if (res.length > 0) {\n          for (var i = 0; i < res.length; i++) {\n            this.Paymenttype.push({\n              idpayment: res[i].idpayment,\n              paymentName: res[i].paymenttype\n            });\n          }\n        } else {}\n      });\n    }\n    SearchCompleteED() {\n      this.dataComplete = [];\n      this.seachheaderlist = [];\n      this.printst = \"อนุมัติแล้ว\";\n      this.productprice = 0;\n      this.sumvat = 0;\n      this.sumprice = 0;\n      this.SearchCompleteED_ = true;\n      this.SearchComplete_ = false;\n      this.load = '';\n      var datacodeSo = '';\n      this.getdate();\n      if (this.keyboardStrApprove == '' || this.keyboardStrApprove == '0') {\n        this.openModal(true, 'กรุณาเลือกเลือกสถาะนะ เป็นapproveแล้ว', false);\n        // alert('เกิดปัญหาในการ Process ข้อมูล กรุณารายการใหม่อีกครั้ง')\n      } else {\n        if (this.datasalegroup == '' && this.keyboardStrpaymenttype == '') {\n          this.openModal(true, 'กรุณาเลือก Sale หรือ เงินสด/เครดิต ถ้าไม่ระบุวันที่ วันที่จะย้อนหลังเป็นเวลา 365 วัน', false);\n          this.productprice = 0;\n          this.sumvat = 0;\n          this.sumprice = 0;\n        } else {\n          if (this.todate == '') {\n            this.todate = `${this.toDate}`;\n          }\n          if (this.fromdate == '') {\n            this.fromdate = `${this.fromDate}`;\n          }\n          if (this.keyboardStrpaymenttype == '') {\n            this.keyboardStrpaymenttype = '%20';\n          }\n          if (this.datasalegroup == '') {\n            if (this.datalogin[0].salegroup == 'admin') {\n              datacodeSo = `${this.datasalegroup}`;\n            } else {\n              datacodeSo = `${this.datalogin[0].salegroup}`;\n            }\n          }\n          if (this.datasalegroup !== '') {\n            if (this.datalogin[0].salegroup == 'admin') {\n              datacodeSo = `${this.datasalegroup}`;\n            } else {\n              datacodeSo = `${this.datalogin[0].salegroup}`;\n            }\n          }\n          if (datacodeSo == '') {\n            datacodeSo = '%20';\n          }\n          this.dataExport = '';\n          var data = `${this.fromdate}/${this.todate}/${datacodeSo}/${this.keyboardStrpaymenttype}`;\n          this.dataComplete = [];\n          this.http.get(this.url + 'completed_invoice_searchED/' + data).subscribe(res => {\n            if (res.length > 0) {\n              this.dataComplete = res;\n              this.seachheaderlist = res;\n              if (this.keyboardStrApprove == '%20') {\n                this.keyboardStrApprove = '';\n              }\n              if (this.keyboardStrpaymenttype == '%20') {\n                this.keyboardStrpaymenttype = '';\n              }\n              this.sum();\n              /* if(this.datalogin[0].salegroup =='admin'){\n                 this.datasalegroup='';\n               }*/\n            } else {\n              /* if(this.datalogin[0].salegroup =='admin'){\n                 this.datasalegroup='';\n               }*/\n              if (this.keyboardStrApprove == '%20') {\n                this.keyboardStrApprove = '';\n              }\n              if (this.keyboardStrpaymenttype == '%20') {\n                this.keyboardStrpaymenttype = '';\n              }\n              this.openModal(true, 'ไม่พบข้อมูล', false);\n              this.dataComplete = [];\n              this.seachheaderlist = [];\n              this.productprice = 0;\n              this.sumvat = 0;\n              this.sumprice = 0;\n            }\n          }, error => {\n            status = error.status;\n            //this.openModal(true,'เกิดปัญหาในการ Process ข้อมูล กรุณารายการใหม่อีกครั้ง',false);\n            alert('เกิดปัญหาในการ Process ข้อมูล กรุณารายการใหม่อีกครั้ง');\n          });\n        }\n      }\n    }\n    SearchComplete() {\n      this.printst = \"3\";\n      this.SearchCompleteED_ = false;\n      this.SearchComplete_ = true;\n      this.load = '';\n      var datacodeSo = '';\n      this.dataComplete = [];\n      this.productprice = 0;\n      this.sumvat = 0;\n      this.sumprice = 0;\n      this.getdate();\n      if (this.datasalegroup == '' && this.keyboardStrpaymenttype == '' && this.keyboardStrApprove == '') {\n        this.dataComplete = [];\n        this.seachheaderlist = [];\n        this.openModal(true, 'กรุณาป้อนข้อมูลอย่างน้อย 1 ช่อง ถ้าไม่ระบุวันที่ วันที่จะย้อนหลังเป็นเวลา 365 วัน', false);\n        this.productprice = 0;\n        this.sumvat = 0;\n        this.sumprice = 0;\n      } else {\n        if (this.todate == '') {\n          this.todate = `${this.toDate}`;\n        }\n        if (this.fromdate == '') {\n          this.fromdate = `${this.fromDate}`;\n        }\n        if (this.keyboardStrpaymenttype == '') {\n          this.keyboardStrpaymenttype = '%20';\n        }\n        if (this.keyboardStrApprove == '') {\n          this.keyboardStrApprove = '%20';\n        }\n        if (this.datasalegroup == '') {\n          if (this.datalogin[0].salegroup == 'admin') {\n            datacodeSo = `${this.datasalegroup}`;\n          } else {\n            datacodeSo = `${this.datalogin[0].salegroup}`;\n          }\n        }\n        if (this.datasalegroup !== '') {\n          if (this.datalogin[0].salegroup == 'admin') {\n            datacodeSo = `${this.datasalegroup}`;\n          } else {\n            datacodeSo = `${this.datalogin[0].salegroup}`;\n          }\n        }\n        //alert(this.datasalegroup)\n        if (datacodeSo == '') {\n          datacodeSo = '%20';\n        }\n        this.dataExport = '';\n        var data = `${this.fromdate}/${this.todate}/${datacodeSo}/${this.keyboardStrApprove}/${this.keyboardStrpaymenttype}`;\n        //alert(data)\n        this.dataExport = data;\n        this.dataComplete = [];\n        this.seachheaderlist = [];\n        this.http.get(this.url + 'completed_invoice_search/' + data).subscribe(res => {\n          if (res.length > 0) {\n            this.dataComplete = res;\n            this.seachheaderlist = res;\n            if (this.keyboardStrApprove == '%20') {\n              this.keyboardStrApprove = '';\n            }\n            if (this.keyboardStrpaymenttype == '%20') {\n              this.keyboardStrpaymenttype = '';\n            }\n            this.sum();\n            /* if(this.datalogin[0].salegroup =='admin'){\n               this.datasalegroup='';\n             }*/\n          } else {\n            /* if(this.datalogin[0].salegroup =='admin'){\n               this.datasalegroup='';\n             }*/\n            if (this.keyboardStrApprove == '%20') {\n              this.keyboardStrApprove = '';\n            }\n            if (this.keyboardStrpaymenttype == '%20') {\n              this.keyboardStrpaymenttype = '';\n            }\n            this.openModal(true, 'ไม่พบข้อมูล', false);\n            this.dataComplete = [];\n            this.seachheaderlist = [];\n            this.productprice = 0;\n            this.sumvat = 0;\n            this.sumprice = 0;\n          }\n        }, error => {\n          status = error.status;\n          alert('เกิดปัญหาในการ Process ข้อมูล กรุณารายการใหม่อีกครั้ง');\n          //this.openModal(true,'เกิดปัญหาในการ Process ข้อมูล กรุณารายการใหม่อีกครั้ง',false);\n        });\n      }\n    }\n    GetExport() {\n      this.http.get(this.url + 'completed_invoice_export/' + this.dataExport).subscribe(res => {\n        if (res.length > 0) {\n          this.DataExport = res;\n          if (this.DataExport == undefined) {\n            this.openModal(true, 'ไม่พบข้อมูล', false);\n            this.dataExport = '';\n          } else {\n            new Angular5Csv(this.DataExport, 'Completed Invoice', this.options);\n            /*  alert(JSON.stringify(this.DataExport));*/\n            this.dataExport = '';\n          }\n        } else {}\n      }, error => {\n        status = error.status;\n        this.openModal(true, 'เกิดปัญหาในการ Process ข้อมูล กรุณาทำรายการใหม่อีกครั้ง', false);\n      });\n    }\n    exportdataexcel() {\n      this.DataExport = [];\n      this.GetExport();\n      if (this.DataExport == undefined) {\n        this.openModal(true, 'ไม่พบข้อมูล', false);\n      } else {\n        new Angular5Csv(this.DataExport, 'Completed Invoice', this.options);\n      }\n    }\n    sum() {\n      this.productprice = 0;\n      this.sumvat = 0;\n      this.sumprice = 0;\n      if (this.dataComplete.length > 0) {\n        for (var i = 0; i < this.dataComplete.length; i++) {\n          this.productprice += this.dataComplete[i].Salesbalance;\n          this.sumvat += this.dataComplete[i].Sumtax;\n          this.sumprice += this.dataComplete[i].Invoiceamount;\n        }\n      }\n    }\n    sumTrue() {\n      this.truesumprice = 0.00;\n      this.truesumvat = 0.00;\n      this.trueproductprice = 0.00;\n      for (var i = 0; i < this.BillId.length; i++) {\n        if (this.BillId[i].check == true) {\n          this.trueproductprice += this.BillId[i].salesbalance;\n          this.truesumvat += this.BillId[i].sumtax;\n          this.truesumprice += this.BillId[i].invoiceamount;\n        }\n      }\n    }\n    selectPaymenttype(idpayment) {\n      this.selectPayment = idpayment;\n    }\n    selectStApprove(idApprove) {\n      this.selectApprove = idApprove;\n    }\n    OpenModalRevert(Invoicingname, Billno, ModalApprove) {\n      this.getIdBillRevert(Invoicingname, Billno);\n      this.ModalRefApprove = this.modalService.show(ModalApprove, {\n        class: 'modal-lg',\n        ignoreBackdropClick: true\n      });\n    }\n    getIdBillRevert(Invoicingname, billid) {\n      this.BillId = [];\n      this.total = 0;\n      this.Remark = '';\n      this.testcheck = false;\n      this.truesumprice = 0.00;\n      this.truesumvat = 0.00;\n      this.trueproductprice = 0.00;\n      this.SearchRevertBillno = '';\n      this.SearchRevertINVname = '';\n      this.SearchRevertINVname = Invoicingname;\n      var databillid = '';\n      this.getdate();\n      if (billid == '') {\n        databillid = '%20';\n      } else {\n        databillid = billid;\n        this.SearchRevertBillno = billid;\n      }\n      this.http.get(this.url + 'get_bill_ID_revert/' + databillid + '/' + Invoicingname + '/' + this.fromdate + '/' + this.todate).subscribe(res => {\n        this.billnoDataSearch = res;\n        if (res.length > 0) {\n          /*var test1 = res[0].attachedfile.splie();*/\n          for (var i = 0; i < res.length; i++) {\n            var alllength = JSON.stringify(res[i].attachedfile).length;\n            var St1 = JSON.stringify(res[i].attachedfile).substring(2, 4);\n            var St2 = JSON.stringify(res[i].attachedfile).substring(alllength - 8, alllength - 5);\n            var St3 = JSON.stringify(res[i].attachedfile).substring(alllength - 19, alllength - 15);\n            var sumst = `${St1}-${St2}${St3}`;\n            for (var v = 0; v < res.length; v++) {\n              var alllengthbook = JSON.stringify(res[i].attachedfileBookbank).length;\n              var St1book = JSON.stringify(res[i].attachedfileBookbank).substring(2, 4);\n              var St2book = JSON.stringify(res[i].attachedfileBookbank).substring(alllengthbook - 8, alllengthbook - 5);\n              var St3book = JSON.stringify(res[i].attachedfileBookbank).substring(alllengthbook - 19, alllengthbook - 15);\n              var sumstbook = `${St1book}-${St2book}${St3book}`;\n            }\n            var checkbank;\n            if (res[i].attachedfileBookbank === 'A') {\n              checkbank = res[i].attachedfileBookbank;\n            } else {\n              checkbank = 'B';\n            }\n            this.BillId.push({\n              noid: i,\n              idbill: res[i].id,\n              salesid: res[i].salesid,\n              invoiceid: res[i].invoiceid,\n              invoicename: res[i].invoicingname,\n              orderaccount: res[i].orderaccount,\n              invoicedate: res[i].invoicedate,\n              duedate: res[i].duedate,\n              salesbalance: res[i].salesbalance,\n              sumtax: res[i].sumtax,\n              invoiceamount: res[i].invoiceamount,\n              billno: res[i].billno,\n              approve: res[i].approve,\n              payment: res[i].payment,\n              urlimg: res[i].attachedfile,\n              delivery: res[i].deliveryname,\n              check: false,\n              nameimg: res[i].attachedfile,\n              saleman: res[i].salegroup,\n              remark: res[i].remark,\n              remarkst: res[i].remarkstatus,\n              numrun: sumst,\n              imgbookbank: res[i].attachedfileBookbank,\n              numrunbank: sumstbook,\n              checkbank: checkbank\n            });\n          }\n          this.showbill();\n        } else {\n          this.showbillno = 'ไม่มีรายการ';\n        }\n      }, error => {\n        status = error.status;\n        //this.openModal(true,'เกิดปัญหาในการ Process ข้อมูล กรุณารายการใหม่อีกครั้ง',false);\n        alert('เกิดปัญหาในการ Process ข้อมูล กรุณารายการใหม่อีกครั้ง');\n      });\n    }\n    getIdBillwaitModal(Invoicingname, Billno, Modalwait) {\n      this.ModalRefApprove = this.modalService.show(Modalwait, {\n        class: 'modal-lg',\n        ignoreBackdropClick: true\n      });\n      this.getIdBillwait(Invoicingname, Billno);\n    }\n    getIdBillwait(Invoicingname, billid) {\n      this.BillId = [];\n      this.total = 0;\n      this.SearchRevertBillno = '';\n      this.SearchRevertINVname = '';\n      this.Remark = '';\n      this.testcheck = false;\n      this.SearchRevertINVname = Invoicingname;\n      this.truesumprice = 0.00;\n      this.truesumvat = 0.00;\n      this.trueproductprice = 0.00;\n      var databillid = '';\n      this.getdate();\n      if (billid == '') {\n        databillid = '%20';\n        this.SearchRevertBillno = '%20';\n      } else {\n        databillid = billid;\n        this.SearchRevertBillno = billid;\n      }\n      this.http.get(this.url + 'get_bill_ID/' + databillid + '/' + Invoicingname + '/' + this.fromdate + '/' + this.todate).subscribe(res => {\n        this.billnoDataSearch = res;\n        if (res.length > 0) {\n          /*var test1 = res[0].attachedfile.splie();*/\n          for (var i = 0; i < res.length; i++) {\n            var alllength = JSON.stringify(res[i].attachedfile).length;\n            var St1 = JSON.stringify(res[i].attachedfile).substring(2, 4);\n            var St2 = JSON.stringify(res[i].attachedfile).substring(alllength - 8, alllength - 5);\n            var St3 = JSON.stringify(res[i].attachedfile).substring(alllength - 19, alllength - 15);\n            var sumst = `${St1}-${St2}${St3}`;\n            for (var v = 0; v < res.length; v++) {\n              var alllengthbook = JSON.stringify(res[i].attachedfileBookbank).length;\n              var St1book = JSON.stringify(res[i].attachedfileBookbank).substring(2, 4);\n              var St2book = JSON.stringify(res[i].attachedfileBookbank).substring(alllengthbook - 8, alllengthbook - 5);\n              var St3book = JSON.stringify(res[i].attachedfileBookbank).substring(alllengthbook - 19, alllengthbook - 15);\n              var sumstbook = `${St1book}-${St2book}${St3book}`;\n            }\n            var checkbank;\n            if (res[i].attachedfileBookbank === 'A') {\n              checkbank = res[i].attachedfileBookbank;\n            } else {\n              checkbank = 'B';\n            }\n            this.BillId.push({\n              noid: i,\n              idbill: res[i].id,\n              salesid: res[i].salesid,\n              invoiceid: res[i].invoiceid,\n              invoicename: res[i].invoicingname,\n              orderaccount: res[i].orderaccount,\n              invoicedate: res[i].invoicedate,\n              duedate: res[i].duedate,\n              salesbalance: res[i].salesbalance,\n              sumtax: res[i].sumtax,\n              invoiceamount: res[i].invoiceamount,\n              billno: res[i].billno,\n              approve: res[i].approve,\n              payment: res[i].payment,\n              urlimg: res[i].attachedfile,\n              delivery: res[i].deliveryname,\n              check: false,\n              nameimg: res[i].attachedfile,\n              saleman: res[i].salegroup,\n              remark: res[i].remark,\n              remarkst: res[i].remarkstatus,\n              numrun: sumst,\n              imgbookbank: res[i].attachedfileBookbank,\n              numrunbank: sumstbook,\n              checkbank: checkbank\n            });\n          }\n          this.showbill();\n          this.sumsuccess();\n        } else {\n          this.showbillno = 'ไม่มีรายการ';\n        }\n      }, error => {\n        status = error.status;\n        // this.openModal(true,'เกิดปัญหาในการ Process ข้อมูล กรุณารายการใหม่อีกครั้ง',false);\n        alert('เกิดปัญหาในการ Process ข้อมูล กรุณารายการใหม่อีกครั้ง');\n      });\n    }\n    OpenModalApprove(Invoicingname, billid, template) {\n      this.getIdBill(Invoicingname, billid);\n      this.ModalRefApprove = this.modalService.show(template, {\n        class: 'modal-lg',\n        ignoreBackdropClick: true\n      });\n    }\n    getIdBill(Invoicingname, billid) {\n      this.BillId = [];\n      this.total = 0;\n      this.SearchRevertBillno = '';\n      this.SearchRevertINVname = '';\n      this.Remark = '';\n      this.testcheck = false;\n      this.SearchRevertINVname = Invoicingname;\n      this.truesumprice = 0.00;\n      this.truesumvat = 0.00;\n      this.trueproductprice = 0.00;\n      var databillid = '';\n      this.getdate();\n      if (billid == '') {\n        databillid = '%20';\n        this.SearchRevertBillno = '%20';\n      } else {\n        databillid = billid;\n        this.SearchRevertBillno = billid;\n      }\n      this.http.get(this.url + 'get_bill_ID/' + databillid + '/' + Invoicingname + '/' + this.fromdate + '/' + this.todate).subscribe(res => {\n        this.billnoDataSearch = res;\n        if (res.length > 0) {\n          /*var test1 = res[0].attachedfile.splie();*/\n          for (var i = 0; i < res.length; i++) {\n            var alllength = JSON.stringify(res[i].attachedfile).length;\n            var St1 = JSON.stringify(res[i].attachedfile).substring(2, 4);\n            var St2 = JSON.stringify(res[i].attachedfile).substring(alllength - 8, alllength - 5);\n            var St3 = JSON.stringify(res[i].attachedfile).substring(alllength - 19, alllength - 15);\n            var sumst = `${St1}-${St2}${St3}`;\n            for (var v = 0; v < res.length; v++) {\n              var alllengthbook = JSON.stringify(res[i].attachedfileBookbank).length;\n              var St1book = JSON.stringify(res[i].attachedfileBookbank).substring(2, 4);\n              var St2book = JSON.stringify(res[i].attachedfileBookbank).substring(alllengthbook - 8, alllengthbook - 5);\n              var St3book = JSON.stringify(res[i].attachedfileBookbank).substring(alllengthbook - 19, alllengthbook - 15);\n              var sumstbook = `${St1book}-${St2book}${St3book}`;\n            }\n            var checkbank;\n            if (res[i].attachedfileBookbank === 'A') {\n              checkbank = res[i].attachedfileBookbank;\n            } else {\n              checkbank = 'B';\n            }\n            this.BillId.push({\n              noid: i,\n              idbill: res[i].id,\n              salesid: res[i].salesid,\n              invoiceid: res[i].invoiceid,\n              invoicename: res[i].invoicingname,\n              orderaccount: res[i].orderaccount,\n              invoicedate: res[i].invoicedate,\n              duedate: res[i].duedate,\n              salesbalance: res[i].salesbalance,\n              sumtax: res[i].sumtax,\n              invoiceamount: res[i].invoiceamount,\n              billno: res[i].billno,\n              approve: res[i].approve,\n              payment: res[i].payment,\n              urlimg: res[i].attachedfile,\n              delivery: res[i].deliveryname,\n              check: false,\n              nameimg: res[i].attachedfile,\n              saleman: res[i].salegroup,\n              remark: res[i].remark,\n              remarkst: res[i].remarkstatus,\n              numrun: sumst,\n              imgbookbank: res[i].attachedfileBookbank,\n              numrunbank: sumstbook,\n              checkbank: checkbank\n            });\n          }\n          this.showbill();\n        } else {\n          this.showbillno = 'ไม่มีรายการ';\n        }\n      }, error => {\n        status = error.status;\n        // this.openModal(true,',false);\n        alert('เกิดปัญหาในการ Process ข้อมูล กรุณารายการใหม่อีกครั้ง');\n      });\n    }\n    getIdBill_nobookbank(Invoicingname, billid) {\n      this.BillId = [];\n      this.total = 0;\n      this.SearchRevertBillno = '';\n      this.SearchRevertINVname = '';\n      this.Remark = '';\n      this.testcheck = false;\n      this.SearchRevertINVname = Invoicingname;\n      this.truesumprice = 0.00;\n      this.truesumvat = 0.00;\n      this.trueproductprice = 0.00;\n      var databillid = '';\n      this.getdate();\n      if (billid == '') {\n        databillid = '%20';\n        this.SearchRevertBillno = '%20';\n      } else {\n        databillid = billid;\n        this.SearchRevertBillno = billid;\n      }\n      this.http.get(this.url + 'get_bill_ID_nobookbank/' + databillid + '/' + Invoicingname + '/' + this.fromdate + '/' + this.todate).subscribe(res => {\n        this.billnoDataSearch = res;\n        if (res.length > 0) {\n          /*var test1 = res[0].attachedfile.splie();*/\n          for (var i = 0; i < res.length; i++) {\n            var alllength = JSON.stringify(res[i].attachedfile).length;\n            var St1 = JSON.stringify(res[i].attachedfile).substring(2, 4);\n            var St2 = JSON.stringify(res[i].attachedfile).substring(alllength - 8, alllength - 5);\n            var St3 = JSON.stringify(res[i].attachedfile).substring(alllength - 19, alllength - 15);\n            var sumst = `${St1}-${St2}${St3}`;\n            for (var v = 0; v < res.length; v++) {\n              var alllengthbook = JSON.stringify(res[i].attachedfileBookbank).length;\n              var St1book = JSON.stringify(res[i].attachedfileBookbank).substring(2, 4);\n              var St2book = JSON.stringify(res[i].attachedfileBookbank).substring(alllengthbook - 8, alllengthbook - 5);\n              var St3book = JSON.stringify(res[i].attachedfileBookbank).substring(alllengthbook - 19, alllengthbook - 15);\n              var sumstbook = `${St1book}-${St2book}${St3book}`;\n            }\n            var checkbank;\n            if (res[i].attachedfileBookbank === 'A') {\n              checkbank = res[i].attachedfileBookbank;\n            } else {\n              checkbank = 'B';\n            }\n            this.BillId.push({\n              noid: i,\n              idbill: res[i].id,\n              salesid: res[i].salesid,\n              invoiceid: res[i].invoiceid,\n              invoicename: res[i].invoicingname,\n              orderaccount: res[i].orderaccount,\n              invoicedate: res[i].invoicedate,\n              duedate: res[i].duedate,\n              salesbalance: res[i].salesbalance,\n              sumtax: res[i].sumtax,\n              invoiceamount: res[i].invoiceamount,\n              billno: res[i].billno,\n              approve: res[i].approve,\n              payment: res[i].payment,\n              urlimg: res[i].attachedfile,\n              delivery: res[i].deliveryname,\n              check: false,\n              nameimg: res[i].attachedfile,\n              saleman: res[i].salegroup,\n              remark: res[i].remark,\n              remarkst: res[i].remarkstatus,\n              numrun: sumst,\n              imgbookbank: res[i].attachedfileBookbank,\n              numrunbank: sumstbook,\n              checkbank: checkbank\n            });\n          }\n          this.showbill();\n          this.searchviewBookbank();\n        } else {\n          this.showbillno = 'ไม่มีรายการ';\n        }\n      }, error => {\n        status = error.status;\n        //this.openModal(true,'',false);\n        alert('เกิดปัญหาในการ Process ข้อมูล กรุณารายการใหม่อีกครั้ง');\n      });\n    }\n    getViewnum() {\n      if (this.BillId.length > 0) {\n        for (var i = 0; i < this.BillId.length; i++) {\n          this.groupimg.push({\n            id: i + 1,\n            nameimg: this.BillId[i].numrun\n          });\n        }\n      }\n    }\n    getIdBillSuccess(Invoicingname, billid) {\n      this.BillId = [];\n      this.clients = [];\n      this.dataTable = [];\n      this.INVSuccess = [];\n      this.total = 0;\n      this.testcheck = false;\n      this.truesumprice = 0.00;\n      this.truesumvat = 0.00;\n      this.trueproductprice = 0.00;\n      this.getdate();\n      var databillid = '';\n      var example = [] = [];\n      this.billno = '';\n      this.nameinv = '';\n      this.nameinv = Invoicingname;\n      if (billid == '') {\n        databillid = '%20';\n        this.billno = '%20';\n      } else {\n        databillid = billid;\n        this.billno = billid;\n      }\n      this.http.get(this.url + 'get_bill_ID_Success/' + databillid + '/' + Invoicingname + '/' + this.fromdate + '/' + this.todate + '/%20').subscribe(res => {\n        if (res.length > 0) {\n          for (var i = 0; i < res.length; i++) {\n            var alllength = JSON.stringify(res[i].attachedfile).length;\n            var St1 = JSON.stringify(res[i].attachedfile).substring(2, 4);\n            var St2 = JSON.stringify(res[i].attachedfile).substring(alllength - 8, alllength - 5);\n            var St3 = JSON.stringify(res[i].attachedfile).substring(alllength - 19, alllength - 15);\n            var sumst = `${St1}-${St2}${St3}`;\n            for (var v = 0; v < res.length; v++) {\n              var alllengthbook = JSON.stringify(res[i].attachedfileBookbank).length;\n              var St1book = JSON.stringify(res[i].attachedfileBookbank).substring(2, 4);\n              var St2book = JSON.stringify(res[i].attachedfileBookbank).substring(alllengthbook - 8, alllengthbook - 5);\n              var St3book = JSON.stringify(res[i].attachedfileBookbank).substring(alllengthbook - 19, alllengthbook - 15);\n              var sumstbook = `${St1book}-${St2book}${St3book}`;\n            }\n            var checkbank;\n            if (res[i].attachedfileBookbank === 'A') {\n              checkbank = res[i].attachedfileBookbank;\n            } else {\n              checkbank = 'B';\n            }\n            this.BillId.push({\n              noid: i,\n              idbill: res[i].id,\n              salesid: res[i].salesid,\n              invoiceid: res[i].invoiceid,\n              invoicename: res[i].invoicingname,\n              orderaccount: res[i].orderaccount,\n              invoicedate: res[i].invoicedate,\n              duedate: res[i].duedate,\n              salesbalance: res[i].salesbalance,\n              sumtax: res[i].sumtax,\n              invoiceamount: res[i].invoiceamount,\n              billno: res[i].billno,\n              approve: res[i].approve,\n              payment: res[i].payment,\n              urlimg: res[i].attachedfile,\n              delivery: res[i].deliveryname,\n              check: false,\n              nameimg: res[i].attachedfile,\n              saleman: res[i].salegroup,\n              remark: res[i].remark,\n              remarkst: res[i].remarkstatus,\n              numrun: sumst,\n              imgbookbank: res[i].attachedfileBookbank,\n              numrunbank: sumstbook,\n              checkbank: checkbank\n            });\n          }\n          this.showbill();\n          this.sumsuccess();\n          this.INVSuccess = this.BillId;\n          /* this.clients = res;\n          // You'll have to wait that changeDetection occurs and projects data into\n           // the HTML template, you can ask Angular to that for you ;-)\n                           this.chRef.checkNoChanges();\n           example = $('#example');\n            this.dataTable = example.DataTable({\n             'paging'      : true,\n             'lengthChange': false,\n             'searching'   : true,\n             'ordering'    : true,\n             'info'        : true,\n             'autoWidth'   : true\n           });\n                        this.showbill();\n          */\n        } else {\n          this.showbillno = 'ไม่มีรายการ';\n          this.productpricesuccess = 0;\n          this.sumvatsuccess = 0;\n          this.sumpricesuccess = 0;\n        }\n      }, error => {\n        //  this.openModal(true,'เกิดปัญหาในการ Process ข้อมูล กรุณารายการใหม่อีกครั้ง',false);\n        alert('เกิดปัญหาในการ Process ข้อมูล กรุณารายการใหม่อีกครั้ง');\n      });\n    }\n    getIdBillSuccessINV() {\n      this.getdate();\n      if (this.dateinvid == '') {\n        this.dateinvid = '%20';\n      }\n      this.BillId = [];\n      this.http.get(this.url + 'get_bill_ID_SuccessCH/' + this.billno + '/' + this.nameinv + '/' + this.fromdate + '/' + this.todate + '/' + this.dateinvid).subscribe(res => {\n        if (res.length > 0) {\n          for (var i = 0; i < res.length; i++) {\n            var alllength = JSON.stringify(res[i].attachedfile).length;\n            var St1 = JSON.stringify(res[i].attachedfile).substring(2, 4);\n            var St2 = JSON.stringify(res[i].attachedfile).substring(alllength - 8, alllength - 5);\n            var St3 = JSON.stringify(res[i].attachedfile).substring(alllength - 19, alllength - 15);\n            var sumst = `${St1}-${St2}${St3}`;\n            for (var v = 0; v < res.length; v++) {\n              var alllengthbook = JSON.stringify(res[i].attachedfileBookbank).length;\n              var St1book = JSON.stringify(res[i].attachedfileBookbank).substring(2, 4);\n              var St2book = JSON.stringify(res[i].attachedfileBookbank).substring(alllengthbook - 8, alllengthbook - 5);\n              var St3book = JSON.stringify(res[i].attachedfileBookbank).substring(alllengthbook - 19, alllengthbook - 15);\n              var sumstbook = `${St1book}-${St2book}${St3book}`;\n            }\n            var checkbank;\n            if (res[i].attachedfileBookbank === 'A') {\n              checkbank = res[i].attachedfileBookbank;\n            } else {\n              checkbank = 'B';\n            }\n            this.BillId.push({\n              noid: i,\n              idbill: res[i].id,\n              salesid: res[i].salesid,\n              invoiceid: res[i].invoiceid,\n              invoicename: res[i].invoicingname,\n              orderaccount: res[i].orderaccount,\n              invoicedate: res[i].invoicedate,\n              duedate: res[i].duedate,\n              salesbalance: res[i].salesbalance,\n              sumtax: res[i].sumtax,\n              invoiceamount: res[i].invoiceamount,\n              billno: res[i].billno,\n              approve: res[i].approve,\n              payment: res[i].payment,\n              urlimg: res[i].attachedfile,\n              delivery: res[i].deliveryname,\n              check: false,\n              nameimg: res[i].attachedfile,\n              saleman: res[i].salegroup,\n              remark: res[i].remark,\n              remarkst: res[i].remarkstatus,\n              numrun: sumst,\n              imgbookbank: res[i].attachedfileBookbank,\n              numrunbank: sumstbook,\n              checkbank: checkbank\n            });\n          }\n          if (this.dateinvid = '%20') {\n            this.dateinvid = '';\n          }\n          this.sumsuccess();\n        } else {\n          this.productpricesuccess = 0;\n          this.sumvatsuccess = 0;\n          this.sumpricesuccess = 0;\n        }\n      }, error => {\n        // this.openModal(true,,false);\n        alert('เกิดปัญหาในการ Process ข้อมูล');\n      });\n    }\n    sumsuccess() {\n      this.productpricesuccess = 0;\n      this.sumvatsuccess = 0;\n      this.sumpricesuccess = 0;\n      if (this.BillId.length > 0) {\n        for (var i = 0; i < this.BillId.length; i++) {\n          this.productpricesuccess += this.BillId[i].salesbalance;\n          this.sumvatsuccess += this.BillId[i].sumtax;\n          this.sumpricesuccess += this.BillId[i].invoiceamount;\n        }\n      }\n    }\n    showbill() {\n      if (this.BillId[0].billno == '') {\n        this.showbillno = this.BillId[0].invoicename;\n      } else {\n        this.showbillno = this.BillId[0].invoicename + ' :  ' + this.BillId[0].billno;\n      }\n    }\n    selectAllbookbank(checked) {\n      for (var i = 0; i < this.BillId.length; i++) {\n        if (this.viewBookbank === 'A') {\n          if (this.BillId[i].checkbank === 'A') {\n            this.BillId[i].check = checked;\n          }\n        } else if (this.viewBookbank === 'B') {\n          if (this.BillId[i].checkbank === 'B') {\n            this.BillId[i].check = checked;\n          }\n        } else {\n          this.BillId[i].check = checked;\n        }\n      }\n      this.GetbillTrue();\n      this.total = this.Bill_approve.length;\n      this.sumTrue();\n    }\n    checkIfAllSelectedbookbank(checked, index) {\n      this.BillId[index].check = checked;\n      this.total = 0;\n      this.GetbillTrue();\n      this.total = this.Bill_approve.length;\n      this.getcaheck();\n      this.sumTrue();\n    }\n    selectAll(checked) {\n      for (var i = 0; i < this.BillId.length; i++) {\n        this.BillId[i].check = checked;\n      }\n      this.GetbillTrue();\n      this.total = this.Bill_approve.length;\n      this.sumTrue();\n    }\n    checkIfAllSelected(checked, index) {\n      this.BillId[index].check = checked;\n      this.total = 0;\n      this.GetbillTrue();\n      this.total = this.Bill_approve.length;\n      this.getcaheck();\n      this.sumTrue();\n    }\n    GetbillTrue() {\n      this.Bill_approve = [];\n      for (var i = 0; i < this.BillId.length; i++) {\n        if (this.BillId[i].check == true) {\n          this.Bill_approve.push({\n            id: this.BillId[i].invoiceid,\n            billno: this.BillId[i].billno\n          });\n        }\n      }\n    }\n    getcaheck() {\n      var ch = 0;\n      for (var i = 0; i < this.BillId.length; i++) {\n        if (this.BillId[i].check == true) {\n          ch++;\n        } else {\n          ch--;\n        }\n      }\n      if (ch == this.BillId.length) {\n        this.testcheck = true;\n      } else {\n        this.testcheck = false;\n      }\n    }\n    deletelallid(value) {\n      this.Bill_approve.splice(value, 1);\n    }\n    UpLoadApprove(st, remark) {\n      this.productprice = 0;\n      this.sumvat = 0;\n      this.sumprice = 0;\n      this.load = 'รายการที่ : ' + this.Bill_approve.length.toString();\n      if (this.Bill_approve.length != 0) {\n        this.http.post(this.url + 'update_invoice_ByApprove', {\n          invoiceid: this.Bill_approve[0].id,\n          stapp: st,\n          remark: ',' + remark\n        }).subscribe(res => {\n          if (res == true) {\n            this.deletelallid(0);\n            this.load = 'รายการที่ : ' + this.Bill_approve.length.toString();\n          } else {}\n        }, error => {\n          alert('เกิดปัญหาในการ Process ข้อมูล');\n          // this.openModal(true,'เกิดปัญหาในการ Process ข้อมูล',false);\n        });\n      } else {\n        if (this.Bill_approve.length == 0) {\n          clearInterval(this.setInterval);\n          this.SearchComplete();\n          this.getIdBill(this.SearchRevertINVname, this.SearchRevertBillno);\n          /* this.openModalalert(false,'',false);\n           this.openModal2(true,'บันทึกข้อมูลเรียบร้อย',true); */\n          this.textload = \"บันทึกข้อมูล Remark เรียบร้อย\";\n          this.btnPDF = true;\n          this.testcheck = false;\n        }\n      }\n    }\n    UpLoadremark(remark, st, typeupload) {\n      this.productprice = 0;\n      this.sumvat = 0;\n      this.sumprice = 0;\n      this.load = 'รายการที่ : ' + this.Bill_approve.length.toString();\n      if (this.Bill_approve.length != 0) {\n        this.http.post(this.url + 'update_invoice_Byremark', {\n          invoiceid: this.Bill_approve[0].id,\n          remark: ',' + remark,\n          st: st\n        }).subscribe(res => {\n          if (res == true) {\n            this.deletelallid(0);\n            this.load = 'รายการที่ : ' + this.Bill_approve.length.toString();\n          } else {}\n        }, error => {\n          //  this.openModal(true,'เกิดปัญหาในการ Process ข้อมูล',false);\n          alert('เกิดปัญหาในการ Process ข้อมูล');\n        });\n      } else {\n        if (this.Bill_approve.length == 0) {\n          clearInterval(this.setInterval);\n          this.Remark = '';\n          this.selectAll(false);\n          // alert(typeupload)\n          if (typeupload === 'Revert') {\n            this.getIdBillRevert(this.SearchRevertINVname, this.SearchRevertBillno);\n          } else {\n            this.getIdBill(this.SearchRevertINVname, this.SearchRevertBillno);\n          }\n          // this.openModalalert(false,'',false);\n          // this.openModal2(true,'บันทึกข้อมูล Remark เรียบร้อย ',true);\n          // this.getIdBillRevert(this.SearchRevertINVname,this.SearchRevertBillno)\n          this.SearchComplete();\n          this.textload = \"บันทึกข้อมูล Remark เรียบร้อย\";\n          this.btnPDF = true;\n          this.testcheck = false;\n        }\n      }\n    }\n    addremarkRevert() {\n      var remark = '';\n      var st = '0';\n      var typeupload = 'Revert';\n      remark = this.Remark;\n      this.GetbillTrue();\n      if (this.Bill_approve.length > 0) {\n        this.openModalalert(true, 'กำลังบันทึกข้อมูล', false);\n        this.setInterval = setInterval(() => this.UpLoadremark(remark, st, typeupload), 400);\n      } else {}\n    }\n    UpLoadRevert(st, remark) {\n      this.productprice = 0;\n      this.sumvat = 0;\n      this.sumprice = 0;\n      this.load = 'รายการที่ : ' + this.Bill_approve.length.toString();\n      if (this.Bill_approve.length != 0) {\n        this.http.post(this.url + 'update_invoice_ByRevert', {\n          invoiceid: this.Bill_approve[0].id,\n          stapp: st,\n          remark: remark\n        }).subscribe(res => {\n          if (res == true) {\n            this.deletelallid(0);\n            this.load = 'รายการที่ : ' + this.Bill_approve.length.toString();\n          } else {}\n        }, error => {\n          // this.openModal(true,'เกิดปัญหาในการ Process ข้อมูล',false);\n          alert('เกิดปัญหาในการ Process ข้อมูล');\n        });\n      } else {\n        if (this.Bill_approve.length == 0) {\n          clearInterval(this.setInterval);\n          this.SearchCompleteED();\n          this.getIdBillRevert(this.SearchRevertINVname, this.SearchRevertBillno);\n          /*this.openModalalert(false,'',false);\n          this.openModal2(true,'บันทึกข้อมูลเรียบร้อย Revert',true);*/\n          this.textload = \"บันทึกข้อมูลเรียบร้อย\";\n          this.btnPDF = true;\n          this.testcheck = false;\n        }\n      }\n    }\n    addremark(template) {\n      var remark = '';\n      var st = '1';\n      var typeupload = 'Approve';\n      remark = this.Remark;\n      this.GetbillTrue();\n      this.ModalremarkRef.hide();\n      if (this.Bill_approve.length > 0) {\n        // this.openModalalert(true,'กำลังบันทึกข้อมูล',false);\n        this.textload = \"กำลังบันทึกข้อมูล\";\n        this.openModalshow(template);\n        this.btnPDF = false;\n        this.setInterval = setInterval(() => this.UpLoadremark(remark, st, typeupload), 400);\n      } else {}\n    }\n    onUpload(template) {\n      this.Bill_approve = [];\n      var remark = '';\n      if (this.Remark == '') {\n        remark = '';\n      } else {\n        remark = this.Remark;\n      }\n      var st = 'อนุมัติแล้ว';\n      this.GetbillTrue();\n      if (this.Bill_approve.length > 0) {\n        //  this.openModalalert(true,'กำลังบันทึกข้อมูล',false);\n        this.textload = \"กำลังบันทึกข้อมูล\";\n        this.openModalshow(template);\n        this.btnPDF = false;\n        this.setInterval = setInterval(() => this.UpLoadApprove(st, remark), 400);\n      } else {}\n    }\n    onRevert(template) {\n      this.Bill_approve = [];\n      var remark = '';\n      if (this.Remark == '') {\n        remark = '';\n      } else {\n        remark = this.Remark;\n      }\n      var st = 'รออนุมัติ';\n      this.GetbillTrue();\n      if (this.Bill_approve.length > 0) {\n        // this.openModalalert(true,'กำลังบันทึกข้อมูล',false);\n        this.textload = \"กำลังบันทึกข้อมูล\";\n        this.openModalshow(template);\n        this.btnPDF = false;\n        this.setInterval = setInterval(() => this.UpLoadRevert(st, remark), 400);\n      } else {}\n    }\n    openModalBillViewDetail(ViewDetail, Invoiceid, orderaccount, salesid, invoicename, invoicedate, duedate, delivery, salesbalance, sumtax, invoiceamount, saleman, remark, imgBill, imgbookbank) {\n      var I3remark = remark.substring(0, 4);\n      if (I3remark === ' , ,') {\n        this.remarkshow = remark.substring(4);\n      } else {\n        this.remarkshow = remark.substring(2);\n      }\n      this.SalesId = '';\n      this.SONo = '';\n      this.InvoiceNo = '';\n      this.OrderAccount = '';\n      this.Invoicingname = '';\n      this.InvoiceDate = '';\n      this.DueDate = '';\n      this.Deliveryname = '';\n      this.productsun = '';\n      this.VAT = '';\n      this.suntotal = '';\n      this.SalesId = saleman;\n      this.SONo = salesid;\n      this.InvoiceNo = Invoiceid;\n      this.OrderAccount = orderaccount;\n      this.Invoicingname = invoicename;\n      this.InvoiceDate = invoicedate;\n      this.DueDate = duedate;\n      this.Deliveryname = delivery;\n      this.productsun = salesbalance;\n      this.VAT = sumtax;\n      this.suntotal = invoiceamount;\n      /*this.remarkshow=I3remark;\n      this.mdlSampleIsOpenBill = open;\n      this.altBill=text;\n      this.checkreloadBill=load;*/\n      this.setImgInvoice(imgBill);\n      this.setImgBill(imgBill);\n      this.setImgBookbank(imgbookbank);\n      this.modalRefviewDetail = this.modalService.show(ViewDetail, {\n        class: 'modal-lg'\n      });\n    }\n    closemodelBill(cl) {\n      this.mdlSampleIsOpenBill = cl;\n      if (this.checkreloadBill == true) {\n        this.SearchComplete();\n      }\n    }\n    openModalimg(ViewDetailApprove, nameimage, invid, orderaccount, salesid, invoicename, invoicedate, duedate, delivery, salesbalance, sumtax, invoiceamount, saleman, remark, imgbookbank) {\n      this.SalesId = '';\n      this.SONo = '';\n      this.InvoiceNo = '';\n      this.OrderAccount = '';\n      this.Invoicingname = '';\n      this.InvoiceDate = '';\n      this.DueDate = '';\n      this.Deliveryname = '';\n      this.productsun = '';\n      this.VAT = '';\n      this.suntotal = '';\n      this.remarkshow = '';\n      var I3remark = remark.substring(0, 4);\n      if (I3remark === ' , ,') {\n        this.remarkshow = remark.substring(4);\n      } else {\n        this.remarkshow = remark.substring(2);\n      }\n      /* this.mdlSampleIsOpenimg = open;*/\n      this.altimg = '';\n      this.altimg = invid;\n      /* this.checkreloadimg=load;*/\n      this.setImgBill(nameimage);\n      this.setImgBookbank(imgbookbank);\n      this.mdlSampleIsOpensuccess = false;\n      this.SalesId = saleman;\n      this.SONo = salesid;\n      this.InvoiceNo = invid;\n      this.OrderAccount = orderaccount;\n      this.Invoicingname = invoicename;\n      this.InvoiceDate = invoicedate;\n      this.DueDate = duedate;\n      this.Deliveryname = delivery;\n      this.productsun = salesbalance;\n      this.VAT = sumtax;\n      this.suntotal = invoiceamount;\n      this.modalRefviewDetail = this.modalService.show(ViewDetailApprove, {\n        class: 'modal-lg'\n      });\n    }\n    closemodelimg(cl) {\n      /*this.mdlSampleIsOpenimg=cl;*/\n      if (this.checkreloadimg == false) {}\n    }\n    openModal(open, text, load) {\n      this.mdlSampleIsOpen = open;\n      this.alt = text;\n      this.checkreload = load;\n    }\n    closemodel(cl) {\n      this.mdlSampleIsOpen = cl;\n      if (this.checkreload == true) {}\n    }\n    /*openModalapprove(open : boolean,text: string,load:boolean,billID) : void {\n    this.BillId=[];\n    \n    this.mdlSampleIsOpenApprove = open;\n    this.altApprove=text;\n    this.checkreloadApprove=load;\n    }*/\n    openModal2(open, text, load) {\n      this.mdlSampleIsOpen2 = open;\n      this.alt2 = text;\n      this.checkreload2 = load;\n    }\n    openModalalert(open, text, load) {\n      this.mdlSampleIsOpen3 = open;\n      this.alt3 = text;\n      this.checkreload3 = load;\n    }\n    closemodelapprove(cl) {\n      this.mdlSampleIsOpenApprove = cl;\n      if (this.checkreloadApprove == true) {}\n    }\n    closemodel2(cl) {\n      this.mdlSampleIsOpen2 = cl;\n      if (this.checkreload2 == true) {}\n    }\n    openModalsuccess(viewsuccess, invname, billno) {\n      var databillid = '';\n      /* if(billno==''){\n        databillid='%20'\n      }else{\n        databillid=billno;\n      }\n      this.http.get<any>(this.url +'get_Group_IMG/'+databillid +'/'+ invname + '/' + this.fromdate+'/'+ this.todate )\n      .subscribe((res: any[]) => {\n        if(res.length>0){\n            alert(JSON.stringify(res))\n        }\n      });*/\n      this.dateinvid = '';\n      this.getIdBillSuccess(invname, billno);\n      this.ModalRefApprove = this.modalService.show(viewsuccess, {\n        class: 'modal-lg',\n        ignoreBackdropClick: true\n      });\n    }\n    closemodelsuccess(cl) {\n      this.mdlSampleIsOpensuccess = cl;\n      if (this.checkreloadsuccess == false) {}\n    }\n    setImgInvoice(nameimage) {\n      var nameimg = `${nameimage}`;\n      this.ImageIN = '../assets/imageINV/' + nameimg;\n    }\n    setImgBill(nameimage) {\n      this.test2 = nameimage;\n      this.ImageBillno = this.urlimg + this.test2;\n    }\n    setImgBookbank(nameimage) {\n      this.test2 = nameimage;\n      if (nameimage === 'A') {\n        this.ImageBookbank = this.urlimgDefault;\n      } else {\n        this.ImageBookbank = this.urlimgBookbank + nameimage;\n      }\n    }\n    handleFileInput(file, modelshow) {\n      this.load = '';\n      this.selectedFile = '';\n      this.Btnimg = false;\n      this.btnPDF = false;\n      if (file.item(0).type == \"image/jpeg\" && file.item(0).size <= 1024 * 1024 * 5) {\n        //this.openModal2(true,'กำลังปรับขนาดไฟล์',false)\n        this.textload = \"กำลังปรับขนาดไฟล์\";\n        this.modalRefshow = this.modalService.show(modelshow, {\n          class: 'modal-sm',\n          backdrop: \"static\"\n        });\n        let image = file.item(0);\n        this.ng2ImgMax.resizeImage(image, 1024, 768).subscribe(result => {\n          this.selectedFile = result;\n          this.textload = this.selectedFile.name;\n          //Show image preview\n          var reader = new FileReader();\n          reader.onload = event => {\n            this.imageUrl = event.target.result;\n            //  this.modalRefshow.hide();\n          };\n          reader.readAsDataURL(this.selectedFile);\n          //this.openModal2(false,'',false)\n          //   this.modalRefshow.hide();\n          // this.Btnimg=true\n          this.end();\n        }, error => {\n          //  this.openModal(true,error,false)\n          alert('เกิดข้อผิดพลาด');\n        });\n        /*  this.selectedFile = file.item(0);*/\n      } else {\n        this.load = '';\n        //this.openModal(true,'ชนิดไฟล์รูปภาพไม่ถูกต้อง กรุณาเลือกชนิดไฟล์เป็น .jpeg และ ไฟล์ขนาดไม่เกิน 5120 kb',false)\n        alert('ชนิดไฟล์รูปภาพไม่ถูกต้อง กรุณาเลือกชนิดไฟล์เป็น .jpeg และ ไฟล์ขนาดไม่เกิน 5120 kb');\n        this.imageUrl = \"assets/img/default-image.png\";\n      }\n    }\n    end() {\n      //this.textload='ปิด'\n      this.Btnimg = true;\n      this.btnPDF = false;\n      this.modalRefshow.hide();\n    }\n    OpenUploadbookbank(template, idSo) {\n      this.btnPDF = false;\n      this.btnREpdf = false;\n      this.showIDso = \"\";\n      this.showIDso = idSo;\n      this.textload = \"\";\n      this.imageUrl = \"assets/img/default-image.png\";\n      this.selectedFile = null;\n      this.modalRef = this.modalService.show(template, this.config);\n    }\n    onUploadbookbank(templateShow, template) {\n      this.modalRef.hide();\n      this.textload = \"กำลังอัพโหลดไฟล์ โปรดรอ.\";\n      this.openModalshow(templateShow);\n      const fd = new FormData();\n      let time = new Date().getFullYear() + '-' + new Date().getMonth() + '-' + new Date().getDate();\n      fd.append('bookbank', this.selectedFile, this.selectedFile.name);\n      this.http.post(this.url + this.namebookbank + '-' + time + '/uploadbookbank', fd, {\n        reportProgress: true,\n        observe: 'events'\n      }).subscribe(event => {\n        if (event.type === HttpEventType.UploadProgress) {} else if (event.type === HttpEventType.Response) {\n          console.log(event);\n          if (event.body.success === true) {\n            this.textload = \"อัพโหลดไฟล์เสร็จสิ้น\";\n            this.textload = \"โปรดรอ.\";\n            this.btnPDF = false;\n            this.Btnimg = false;\n            //this.updataFlie(this.Bill_approve[i].id,event.body._name)\n            // alert(event.body._namebook)\n            this.setInterval = setInterval(() => this.updataFlie(event.body._namebook), 700);\n          } else {\n            this.textload = \"เกิดปัญหาในการ upload กรุณาทำรายการใหม่\";\n            this.btnPDF = false;\n            this.Btnimg = false;\n            this.btnREpdf = true;\n            //alert('เกิดปัญหาในการ upload กรุณาทำรายการใหม่' )\n          }\n        }\n      });\n    }\n    updataFlie(_name) {\n      //updataDPF_idSo\n      if (this.Bill_approve.length != 0) {\n        this.http.post(this.url + 'updatabookbank_idSo', {\n          idSo: this.Bill_approve[0].id,\n          _name: _name\n        }).subscribe(res => {\n          //  alert(res)\n          if (res == true) {\n            this.deletelallid(0);\n          } else {\n            // this.textload=\"เกิดปัญหาในการ เพิ่มรายการ\"\n          }\n        });\n      } else {\n        if (this.Bill_approve.length == 0) {\n          clearInterval(this.setInterval);\n          this.btnPDF = true;\n        }\n      }\n      /*\n      do {\n        //alert(num)\n        this.http.post<any>(this.url+'updatabookbank_idSo',{\n          idSo : this.Bill_approve[num].id,\n          _name : _name\n        }).subscribe(res=> {\n            //  alert(res)\n            if(res==true){\n              //this.textload=\"ทำรายการเสร็จสิ้น\"\n      \n              num++;\n            }else{\n                  // this.textload=\"เกิดปัญหาในการ เพิ่มรายการ\"\n      \n            }\n      \n            })\n            console.log(num)\n      } while (num <= this.Bill_approve.length );\n        */\n    }\n    openModalshow(templateShow) {\n      this.modalRef = this.modalService.show(templateShow, {\n        class: 'modal-sm',\n        backdrop: \"static\"\n      });\n    }\n    openModalbookbank(Invoicingname, Billno, Idcostomer, templateShow) {\n      this.modalRef = this.modalService.show(templateShow,\n      // Object.assign({}, { class: 'modal-lg', backdrop: \"static\" })\n      {\n        class: 'modal-lg',\n        backdrop: \"static\"\n      });\n      this.showIDso = Invoicingname;\n      this.textload = \"\";\n      this.imageUrl = \"assets/img/default-image.png\";\n      this.selectedFile = null;\n      this.namebookbank = Idcostomer;\n      this.viewBookbank = 'A';\n      this.getIdBill_nobookbank(Invoicingname, Billno);\n    }\n    confirmimg() {\n      this.modalRefshow.hide();\n    }\n    confirm() {\n      this.modalRef.hide();\n      // this.Searchsolist();\n    }\n    decline(template) {\n      this.selectedFile = null;\n      this.modalRef.hide();\n      this.modalRef = this.modalService.show(template, this.config);\n    }\n    openModalviewDetail(template, Invoiceid, orderaccount, salesid, invoicename, invoicedate, duedate, delivery, salesbalance, sumtax, invoiceamount, saleman, remark, imgBill, imgbookbank) {\n      var I3remark = remark.substring(0, 4);\n      if (I3remark === ' , ,') {\n        this.remarkshow = remark.substring(4);\n      } else {\n        this.remarkshow = remark.substring(2);\n      }\n      this.SalesId = '';\n      this.SONo = '';\n      this.InvoiceNo = '';\n      this.OrderAccount = '';\n      this.Invoicingname = '';\n      this.InvoiceDate = '';\n      this.DueDate = '';\n      this.Deliveryname = '';\n      this.productsun = '';\n      this.VAT = '';\n      this.suntotal = '';\n      this.SalesId = saleman;\n      this.SONo = salesid;\n      this.InvoiceNo = Invoiceid;\n      this.OrderAccount = orderaccount;\n      this.Invoicingname = invoicename;\n      this.InvoiceDate = invoicedate;\n      this.DueDate = duedate;\n      this.Deliveryname = delivery;\n      this.productsun = salesbalance;\n      this.VAT = sumtax;\n      this.suntotal = invoiceamount;\n      /* this.setImgInvoice(imgBill);*/\n      this.setImgBill(imgBill);\n      if (imgbookbank === 'A') {\n        this.CkimgBookbank = false;\n      } else {\n        this.CkimgBookbank = true;\n        this.setImgBookbank(imgbookbank);\n      }\n      this.modalRefviewDetail = this.modalService.show(template, {\n        class: 'modal-lg'\n      });\n    }\n    Modalremark(template) {\n      this.ModalremarkRef = this.modalService.show(template, {\n        class: 'modal-lg'\n      });\n    }\n    static {\n      this.ɵfac = function CompleteINVComponent_Factory(t) {\n        return new (t || CompleteINVComponent)(i0.ɵɵdirectiveInject(i1.Ng2ImgMaxService), i0.ɵɵdirectiveInject(i2.BsModalService), i0.ɵɵdirectiveInject(i3.FormBuilder), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i4.HttpClient), i0.ɵɵdirectiveInject(i5.WebapiService), i0.ɵɵdirectiveInject(i6.NgbCalendar), i0.ɵɵdirectiveInject(i7.Router));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: CompleteINVComponent,\n        selectors: [[\"app-complete-inv\"]],\n        decls: 2,\n        vars: 0,\n        template: function CompleteINVComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"p\");\n            i0.ɵɵtext(1, \" complete-inv works!\\n\");\n            i0.ɵɵelementEnd();\n          }\n        }\n      });\n    }\n  }\n  return CompleteINVComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}