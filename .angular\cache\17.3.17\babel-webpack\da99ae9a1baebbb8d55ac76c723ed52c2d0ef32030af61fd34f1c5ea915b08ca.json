{"ast": null, "code": "import { HttpClient } from '@angular/common/http';\nimport { WebapiService } from './webapi.service';\nimport { setTheme } from 'ngx-bootstrap/utils';\nimport 'datatables.net';\nimport 'datatables.net-bs4';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"./webapi.service\";\nimport * as i3 from \"@angular/router\";\nexport let AppComponent = /*#__PURE__*/(() => {\n  class AppComponent {\n    constructor(http, service) {\n      this.http = http;\n      this.service = service;\n      this.mdlSampleIsOpen = false;\n      this.alt = '';\n      this.checkreload = true;\n      this.mProducts = [];\n      this.title = 'app';\n      this.service.getUrlGroup();\n      this.service.geturlservice();\n      this.service.getUrluser();\n      setTheme('bs4');\n    }\n    Loaduser() {\n      this.service.feedData().then(result => {\n        this.mProducts = result;\n      });\n    }\n    openModal(open, text, load) {\n      this.mdlSampleIsOpen = open;\n      this.alt = text;\n      this.checkreload = load;\n    }\n    closemodel(cl) {\n      this.mdlSampleIsOpen = cl;\n      if (this.checkreload == true) {\n        location.reload();\n      }\n    }\n    static {\n      this.ɵfac = function AppComponent_Factory(t) {\n        return new (t || AppComponent)(i0.ɵɵdirectiveInject(i1.HttpClient), i0.ɵɵdirectiveInject(i2.WebapiService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: AppComponent,\n        selectors: [[\"app-root\"]],\n        decls: 9,\n        vars: 0,\n        consts: [[\"lang\", \"en\"], [\"charset\", \"UTF-8\"], [\"name\", \"viewport\", \"content\", \"width=device-width, initial-scale=1.0\"], [\"http-equiv\", \"X-UA-Compatible\", \"content\", \"ie=edge\"], [1, \"col-12\", \"col-sm-12\", \"col-md-12\", \"col-lg-12\", 2, \"padding-right\", \"0px\", \"padding-left\", \"0px\", \"width\", \"100%\"]],\n        template: function AppComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"html\", 0)(1, \"head\");\n            i0.ɵɵelement(2, \"meta\", 1)(3, \"meta\", 2)(4, \"meta\", 3)(5, \"title\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(6, \"body\")(7, \"div\", 4);\n            i0.ɵɵelement(8, \"router-outlet\");\n            i0.ɵɵelementEnd()()();\n          }\n        },\n        dependencies: [i3.RouterOutlet]\n      });\n    }\n  }\n  return AppComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}