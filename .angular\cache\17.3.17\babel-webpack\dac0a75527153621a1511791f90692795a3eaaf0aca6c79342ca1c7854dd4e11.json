{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.iterator = exports.getSymbolIterator = void 0;\nfunction getSymbolIterator() {\n  if (typeof Symbol !== 'function' || !Symbol.iterator) {\n    return '@@iterator';\n  }\n  return Symbol.iterator;\n}\nexports.getSymbolIterator = getSymbolIterator;\nexports.iterator = getSymbolIterator();\n//# sourceMappingURL=iterator.js.map", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}