{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.tap = void 0;\nvar isFunction_1 = require(\"../util/isFunction\");\nvar lift_1 = require(\"../util/lift\");\nvar OperatorSubscriber_1 = require(\"./OperatorSubscriber\");\nvar identity_1 = require(\"../util/identity\");\nfunction tap(observerOrNext, error, complete) {\n  var tapObserver = isFunction_1.isFunction(observerOrNext) || error || complete ? {\n    next: observerOrNext,\n    error: error,\n    complete: complete\n  } : observerOrNext;\n  return tapObserver ? lift_1.operate(function (source, subscriber) {\n    var _a;\n    (_a = tapObserver.subscribe) === null || _a === void 0 ? void 0 : _a.call(tapObserver);\n    var isUnsub = true;\n    source.subscribe(OperatorSubscriber_1.createOperatorSubscriber(subscriber, function (value) {\n      var _a;\n      (_a = tapObserver.next) === null || _a === void 0 ? void 0 : _a.call(tapObserver, value);\n      subscriber.next(value);\n    }, function () {\n      var _a;\n      isUnsub = false;\n      (_a = tapObserver.complete) === null || _a === void 0 ? void 0 : _a.call(tapObserver);\n      subscriber.complete();\n    }, function (err) {\n      var _a;\n      isUnsub = false;\n      (_a = tapObserver.error) === null || _a === void 0 ? void 0 : _a.call(tapObserver, err);\n      subscriber.error(err);\n    }, function () {\n      var _a, _b;\n      if (isUnsub) {\n        (_a = tapObserver.unsubscribe) === null || _a === void 0 ? void 0 : _a.call(tapObserver);\n      }\n      (_b = tapObserver.finalize) === null || _b === void 0 ? void 0 : _b.call(tapObserver);\n    }));\n  }) : identity_1.identity;\n}\nexports.tap = tap;\n//# sourceMappingURL=tap.js.map", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}