{"ast": null, "code": "\"use strict\";\n\nvar __read = this && this.__read || function (o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o),\n    r,\n    ar = [],\n    e;\n  try {\n    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n  } catch (error) {\n    e = {\n      error: error\n    };\n  } finally {\n    try {\n      if (r && !r.done && (m = i[\"return\"])) m.call(i);\n    } finally {\n      if (e) throw e.error;\n    }\n  }\n  return ar;\n};\nvar __spreadArray = this && this.__spreadArray || function (to, from) {\n  for (var i = 0, il = from.length, j = to.length; i < il; i++, j++) to[j] = from[i];\n  return to;\n};\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.mapOneOrManyArgs = void 0;\nvar map_1 = require(\"../operators/map\");\nvar isArray = Array.isArray;\nfunction callOrApply(fn, args) {\n  return isArray(args) ? fn.apply(void 0, __spreadArray([], __read(args))) : fn(args);\n}\nfunction mapOneOrManyArgs(fn) {\n  return map_1.map(function (args) {\n    return callOrApply(fn, args);\n  });\n}\nexports.mapOneOrManyArgs = mapOneOrManyArgs;\n//# sourceMappingURL=mapOneOrManyArgs.js.map", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}