{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nvar CsvConfigConsts = function () {\n  function CsvConfigConsts() {}\n  CsvConfigConsts.EOL = \"\\r\\n\";\n  CsvConfigConsts.BOM = \"\\ufeff\";\n  CsvConfigConsts.DEFAULT_FIELD_SEPARATOR = ',';\n  CsvConfigConsts.DEFAULT_DECIMAL_SEPARATOR = '.';\n  CsvConfigConsts.DEFAULT_QUOTE = '\"';\n  CsvConfigConsts.DEFAULT_SHOW_TITLE = false;\n  CsvConfigConsts.DEFAULT_TITLE = 'My Report';\n  CsvConfigConsts.DEFAULT_FILENAME = 'mycsv.csv';\n  CsvConfigConsts.DEFAULT_SHOW_LABELS = false;\n  CsvConfigConsts.DEFAULT_USE_BOM = true;\n  CsvConfigConsts.DEFAULT_HEADER = [];\n  CsvConfigConsts.DEFAULT_NO_DOWNLOAD = false;\n  CsvConfigConsts.DEFAULT_NULL_TO_EMPTY_STRING = false;\n  return CsvConfigConsts;\n}();\nexports.CsvConfigConsts = CsvConfigConsts;\nexports.ConfigDefaults = {\n  filename: CsvConfigConsts.DEFAULT_FILENAME,\n  fieldSeparator: CsvConfigConsts.DEFAULT_FIELD_SEPARATOR,\n  quoteStrings: CsvConfigConsts.DEFAULT_QUOTE,\n  decimalseparator: CsvConfigConsts.DEFAULT_DECIMAL_SEPARATOR,\n  showLabels: CsvConfigConsts.DEFAULT_SHOW_LABELS,\n  showTitle: CsvConfigConsts.DEFAULT_SHOW_TITLE,\n  title: CsvConfigConsts.DEFAULT_TITLE,\n  useBom: CsvConfigConsts.DEFAULT_USE_BOM,\n  headers: CsvConfigConsts.DEFAULT_HEADER,\n  noDownload: CsvConfigConsts.DEFAULT_NO_DOWNLOAD,\n  nullToEmptyString: CsvConfigConsts.DEFAULT_NULL_TO_EMPTY_STRING\n};\nvar Angular5Csv = function () {\n  function Angular5Csv(DataJSON, filename, options) {\n    this.csv = \"\";\n    var config = options || {};\n    this.data = typeof DataJSON != 'object' ? JSON.parse(DataJSON) : DataJSON;\n    this._options = objectAssign({}, exports.ConfigDefaults, config);\n    if (this._options.filename) {\n      this._options.filename = filename;\n    }\n    this.generateCsv();\n  }\n  /**\n   * Generate and Download Csv\n   */\n  Angular5Csv.prototype.generateCsv = function () {\n    if (this._options.useBom) {\n      this.csv += CsvConfigConsts.BOM;\n    }\n    if (this._options.showTitle) {\n      this.csv += this._options.title + '\\r\\n\\n';\n    }\n    this.getHeaders();\n    this.getBody();\n    if (this.csv == '') {\n      console.log(\"Invalid data\");\n      return;\n    }\n    if (this._options.noDownload) {\n      return this.csv;\n    }\n    var blob = new Blob([this.csv], {\n      \"type\": \"text/csv;charset=utf8;\"\n    });\n    if (navigator.msSaveBlob) {\n      var filename = this._options.filename.replace(/ /g, \"_\") + \".csv\";\n      navigator.msSaveBlob(blob, filename);\n    } else {\n      var uri = 'data:attachment/csv;charset=utf-8,' + encodeURI(this.csv);\n      var link = document.createElement(\"a\");\n      link.href = URL.createObjectURL(blob);\n      link.setAttribute('visibility', 'hidden');\n      link.download = this._options.filename.replace(/ /g, \"_\") + \".csv\";\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n    }\n  };\n  /**\n   * Create Headers\n   */\n  Angular5Csv.prototype.getHeaders = function () {\n    var _this = this;\n    if (this._options.headers.length > 0) {\n      var headers = this._options.headers;\n      var row = headers.reduce(function (headerRow, header) {\n        return headerRow + header + _this._options.fieldSeparator;\n      }, '');\n      row = row.slice(0, -1);\n      this.csv += row + CsvConfigConsts.EOL;\n    }\n  };\n  /**\n   * Create Body\n   */\n  Angular5Csv.prototype.getBody = function () {\n    for (var i = 0; i < this.data.length; i++) {\n      var row = \"\";\n      for (var index in this.data[i]) {\n        row += this.formatData(this.data[i][index]) + this._options.fieldSeparator;\n      }\n      row = row.slice(0, -1);\n      this.csv += row + CsvConfigConsts.EOL;\n    }\n  };\n  /**\n   * Format Data\n   * @param {any} data\n   */\n  Angular5Csv.prototype.formatData = function (data) {\n    if (this._options.decimalseparator === 'locale' && Angular5Csv.isFloat(data)) {\n      return data.toLocaleString();\n    }\n    if (this._options.decimalseparator !== '.' && Angular5Csv.isFloat(data)) {\n      return data.toString().replace('.', this._options.decimalseparator);\n    }\n    if (typeof data === 'string') {\n      data = data.replace(/\"/g, '\"\"');\n      if (this._options.quoteStrings || data.indexOf(',') > -1 || data.indexOf('\\n') > -1 || data.indexOf('\\r') > -1) {\n        data = this._options.quoteStrings + data + this._options.quoteStrings;\n      }\n      return data;\n    }\n    if (this._options.nullToEmptyString) {\n      if (data === null) {\n        return data = '';\n      }\n      return data;\n    }\n    if (typeof data === 'boolean') {\n      return data ? 'TRUE' : 'FALSE';\n    }\n    return data;\n  };\n  /**\n   * Check if is Float\n   * @param {any} input\n   */\n  Angular5Csv.isFloat = function (input) {\n    return +input === input && (!isFinite(input) || Boolean(input % 1));\n  };\n  return Angular5Csv;\n}();\nexports.Angular5Csv = Angular5Csv;\nvar hasOwnProperty = Object.prototype.hasOwnProperty;\nvar propIsEnumerable = Object.prototype.propertyIsEnumerable;\n/**\n * Convet to Object\n * @param {any} val\n */\nfunction toObject(val) {\n  if (val === null || val === undefined) {\n    throw new TypeError('Object.assign cannot be called with null or undefined');\n  }\n  return Object(val);\n}\n/**\n * Assign data  to new Object\n * @param {any}   target\n * @param {any[]} ...source\n */\nfunction objectAssign(target) {\n  var source = [];\n  for (var _i = 1; _i < arguments.length; _i++) {\n    source[_i - 1] = arguments[_i];\n  }\n  var from;\n  var to = toObject(target);\n  var symbols;\n  for (var s = 1; s < arguments.length; s++) {\n    from = Object(arguments[s]);\n    for (var key in from) {\n      if (hasOwnProperty.call(from, key)) {\n        to[key] = from[key];\n      }\n    }\n    if (Object.getOwnPropertySymbols) {\n      symbols = Object.getOwnPropertySymbols(from);\n      for (var i = 0; i < symbols.length; i++) {\n        if (propIsEnumerable.call(from, symbols[i])) {\n          to[symbols[i]] = from[symbols[i]];\n        }\n      }\n    }\n  }\n  return to;\n}\n//# sourceMappingURL=Angular5-csv.js.map", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}