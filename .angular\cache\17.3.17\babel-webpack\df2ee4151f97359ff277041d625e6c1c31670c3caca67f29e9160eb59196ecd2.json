{"ast": null, "code": "import { isFunction } from './isFunction';\nimport { isScheduler } from './isScheduler';\nfunction last(arr) {\n  return arr[arr.length - 1];\n}\nexport function popResultSelector(args) {\n  return isFunction(last(args)) ? args.pop() : undefined;\n}\nexport function popScheduler(args) {\n  return isScheduler(last(args)) ? args.pop() : undefined;\n}\nexport function popNumber(args, defaultValue) {\n  return typeof last(args) === 'number' ? args.pop() : defaultValue;\n}\n//# sourceMappingURL=args.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}