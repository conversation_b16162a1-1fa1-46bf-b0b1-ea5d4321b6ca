{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.reportUnhandledError = void 0;\nvar config_1 = require(\"../config\");\nvar timeoutProvider_1 = require(\"../scheduler/timeoutProvider\");\nfunction reportUnhandledError(err) {\n  timeoutProvider_1.timeoutProvider.setTimeout(function () {\n    var onUnhandledError = config_1.config.onUnhandledError;\n    if (onUnhandledError) {\n      onUnhandledError(err);\n    } else {\n      throw err;\n    }\n  });\n}\nexports.reportUnhandledError = reportUnhandledError;\n//# sourceMappingURL=reportUnhandledError.js.map", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}