{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.mergeMapTo = void 0;\nvar mergeMap_1 = require(\"./mergeMap\");\nvar isFunction_1 = require(\"../util/isFunction\");\nfunction mergeMapTo(innerObservable, resultSelector, concurrent) {\n  if (concurrent === void 0) {\n    concurrent = Infinity;\n  }\n  if (isFunction_1.isFunction(resultSelector)) {\n    return mergeMap_1.mergeMap(function () {\n      return innerObservable;\n    }, resultSelector, concurrent);\n  }\n  if (typeof resultSelector === 'number') {\n    concurrent = resultSelector;\n  }\n  return mergeMap_1.mergeMap(function () {\n    return innerObservable;\n  }, concurrent);\n}\nexports.mergeMapTo = mergeMapTo;\n//# sourceMappingURL=mergeMapTo.js.map", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}