{"ast": null, "code": "var __decorate = this && this.__decorate || function (decorators, target, key, desc) {\n  var c = arguments.length,\n    r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc,\n    d;\n  if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n  return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nimport { TooltipModule } from 'ngx-bootstrap/tooltip';\nimport { HashLocationStrategy, LocationStrategy } from '@angular/common';\nimport { HttpClientModule } from '@angular/common/http';\nimport { enableProdMode, NgModule } from '@angular/core';\nimport { BrowserModule } from '@angular/platform-browser';\nimport { BrowserAnimationsModule, NoopAnimationsModule } from '@angular/platform-browser/animations';\nimport { RouterModule } from '@angular/router';\nimport { NgbModule } from '@ng-bootstrap/ng-bootstrap';\nimport { DataTableModule } from \"angular-6-datatable\";\nimport { TextInputAutocompleteModule } from 'angular-text-input-autocomplete';\nimport { polyfill as keyboardEventKeyPolyfill } from 'keyboardevent-key-polyfill';\nimport { Ng2ImgMaxModule } from 'ng2-img-max';\nimport { SelectModule } from 'ng2-select';\nimport { CollapseModule } from 'ngx-bootstrap/collapse';\nimport { BsDatepickerModule } from 'ngx-bootstrap/datepicker';\nimport { ModalModule } from 'ngx-bootstrap/modal';\nimport { TabsModule } from 'ngx-bootstrap/tabs';\nimport { CookieService } from 'ngx-cookie-service';\nimport { NgxPaginationModule } from 'ngx-pagination'; // <-- import the module\nimport { FormsModule, ReactiveFormsModule } from '../../node_modules/@angular/forms';\nimport { AppComponent } from './app.component';\nimport { CompleteINVComponent } from './complete-inv/complete-inv.component';\nimport { CompleteinvoiceComponent } from './completeinvoice/completeinvoice.component';\nimport { CoverpageComponent } from './coverpage/coverpage.component';\nimport { CustomerlistComponent } from './customerlist/customerlist.component';\nimport { EditsaloderreviewComponent } from './editsaloderreview/editsaloderreview.component';\nimport { HomepageComponent } from './homepage/homepage.component';\nimport { InvoicecashlistComponent } from './invoicecashlist/invoicecashlist.component';\nimport { InvoicecreditlistComponent } from './invoicecreditlist/invoicecreditlist.component';\nimport { LoginComponent } from './login/login.component';\nimport { MasterpackageComponent } from './masterpackage/masterpackage.component';\nimport { PDFprintComponent } from './pdfprint/pdfprint.component';\nimport { PermissionComponent } from './permission/permission.component';\nimport { PricemasterComponent } from './pricemaster/pricemaster.component';\nimport { PrintsaleoderlistComponent } from './printsaleoderlist/printsaleoderlist.component';\nimport { ProductgroupComponent } from './productgroup/productgroup.component';\nimport { ProductlistComponent } from './productlist/productlist.component';\nimport { ReversepipePipe } from './reversepipe.pipe';\nimport { SalestatusComponent } from './salestatus/salestatus.component';\nimport { SalestockComponent } from './salestock/salestock.component';\nimport { SoComponent } from './so/so.component';\nimport { SohistoryComponent } from './sohistory/sohistory.component';\nimport { Solist1Component } from './solist1/solist1.component';\nimport { SorecordComponent } from './sorecord/sorecord.component';\nimport { SoreviewComponent } from './soreview/soreview.component';\nimport { SumbyproductComponent } from './sumbyproduct/sumbyproduct.component';\nimport { TopmenuComponent } from './topmenu/topmenu.component';\nimport { TrendproductComponent } from './trendproduct/trendproduct.component';\nimport { UsersComponent } from './users/users.component';\nimport { ViewimagesComponent } from './viewimages/viewimages.component';\nimport { WebapiService } from './webapi.service';\nenableProdMode();\nkeyboardEventKeyPolyfill();\n// Routes\nexport const router = [/* { path: '' , redirectTo:'/login', pathMatch:'full' },*/\n{\n  path: '',\n  component: LoginComponent\n}, {\n  path: 'permission',\n  component: PermissionComponent\n}, {\n  path: 'sorecord',\n  component: SorecordComponent\n}, {\n  path: 'login',\n  component: LoginComponent\n}, {\n  path: 'so',\n  component: SoComponent\n}, {\n  path: 'sohistory',\n  component: SohistoryComponent\n}, {\n  path: 'solist1',\n  component: Solist1Component\n}, {\n  path: 'soreview',\n  component: SoreviewComponent\n}, {\n  path: 'invoicecreditlist',\n  component: InvoicecreditlistComponent\n}, {\n  path: 'invoicecashlist',\n  component: InvoicecashlistComponent\n}, {\n  path: 'customerlist',\n  component: CustomerlistComponent\n}, {\n  path: 'PDFprint',\n  component: PDFprintComponent\n}, {\n  path: 'printsaleoderlist',\n  component: PrintsaleoderlistComponent\n}, {\n  path: 'coverpage',\n  component: CoverpageComponent\n}, {\n  path: 'sumbyproduct',\n  component: SumbyproductComponent\n}, {\n  path: 'trendproduct',\n  component: TrendproductComponent\n}, {\n  path: 'masterpackage',\n  component: MasterpackageComponent\n}, {\n  path: 'productgroup',\n  component: ProductgroupComponent\n}, {\n  path: 'productlist',\n  component: ProductlistComponent\n}, {\n  path: 'editsaloderreview',\n  component: EditsaloderreviewComponent\n}, {\n  path: 'pricemaster',\n  component: PricemasterComponent\n}, {\n  path: 'viewimages',\n  component: ViewimagesComponent\n}, {\n  path: 'topmenu',\n  component: TopmenuComponent\n}, /*  { path: 'completeinvoice' , component: CompleteINVComponent},*/\n{\n  path: 'completeinvoice',\n  component: CompleteinvoiceComponent\n}, {\n  path: 'users',\n  component: UsersComponent\n}, {\n  path: 'salestatus',\n  component: SalestatusComponent\n}, {\n  path: 'salestock',\n  component: SalestockComponent\n}];\nlet AppModule = class AppModule {};\nAppModule = __decorate([NgModule({\n  declarations: [AppComponent, PricemasterComponent, LoginComponent, CompleteinvoiceComponent, CustomerlistComponent, CoverpageComponent, InvoicecreditlistComponent, InvoicecashlistComponent, MasterpackageComponent, PermissionComponent, ProductgroupComponent, ProductlistComponent, SoComponent, SohistoryComponent, Solist1Component, SorecordComponent, SoreviewComponent, SumbyproductComponent, TrendproductComponent, UsersComponent, TopmenuComponent, HomepageComponent, ViewimagesComponent, ReversepipePipe, EditsaloderreviewComponent, PDFprintComponent, PrintsaleoderlistComponent, CompleteINVComponent, SalestatusComponent, SalestockComponent],\n  imports: [TooltipModule, BrowserAnimationsModule, SelectModule, BrowserModule, FormsModule, ReactiveFormsModule, NgbModule, RouterModule.forRoot(router), HttpClientModule, DataTableModule, TextInputAutocompleteModule, NoopAnimationsModule, Ng2ImgMaxModule, NgxPaginationModule, BsDatepickerModule, ModalModule, TabsModule, CollapseModule],\n  /** */\n  providers: [WebapiService, CookieService, {\n    provide: LocationStrategy,\n    useClass: HashLocationStrategy\n  }],\n  bootstrap: [AppComponent]\n})], AppModule);\nexport { AppModule };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}