{"ast": null, "code": "\"use strict\";\n\nvar __read = this && this.__read || function (o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o),\n    r,\n    ar = [],\n    e;\n  try {\n    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n  } catch (error) {\n    e = {\n      error: error\n    };\n  } finally {\n    try {\n      if (r && !r.done && (m = i[\"return\"])) m.call(i);\n    } finally {\n      if (e) throw e.error;\n    }\n  }\n  return ar;\n};\nvar __spreadArray = this && this.__spreadArray || function (to, from) {\n  for (var i = 0, il = from.length, j = to.length; i < il; i++, j++) to[j] = from[i];\n  return to;\n};\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.concat = void 0;\nvar lift_1 = require(\"../util/lift\");\nvar concatAll_1 = require(\"./concatAll\");\nvar args_1 = require(\"../util/args\");\nvar from_1 = require(\"../observable/from\");\nfunction concat() {\n  var args = [];\n  for (var _i = 0; _i < arguments.length; _i++) {\n    args[_i] = arguments[_i];\n  }\n  var scheduler = args_1.popScheduler(args);\n  return lift_1.operate(function (source, subscriber) {\n    concatAll_1.concatAll()(from_1.from(__spreadArray([source], __read(args)), scheduler)).subscribe(subscriber);\n  });\n}\nexports.concat = concat;\n//# sourceMappingURL=concat.js.map", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}