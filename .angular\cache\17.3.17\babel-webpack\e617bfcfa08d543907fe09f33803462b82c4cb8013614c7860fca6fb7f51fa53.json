{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.isIterable = void 0;\nvar iterator_1 = require(\"../symbol/iterator\");\nvar isFunction_1 = require(\"./isFunction\");\nfunction isIterable(input) {\n  return isFunction_1.isFunction(input === null || input === void 0 ? void 0 : input[iterator_1.iterator]);\n}\nexports.isIterable = isIterable;\n//# sourceMappingURL=isIterable.js.map", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}