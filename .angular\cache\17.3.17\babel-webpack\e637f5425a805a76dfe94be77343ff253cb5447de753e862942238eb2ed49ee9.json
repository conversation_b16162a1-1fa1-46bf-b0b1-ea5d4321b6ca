{"ast": null, "code": "// tslint:disable-next-line:import-spacing\nimport { Chart } from 'node_modules/chart.js/dist/chart.js';\nimport * as i0 from \"@angular/core\";\nexport let SumbyproductComponent = /*#__PURE__*/(() => {\n  class SumbyproductComponent {\n    constructor() {}\n    ngOnInit() {\n      this.sumbyproduct = new Chart('myChart', {\n        type: 'bar',\n        data: {\n          labels: ['Red', 'Blue', 'Yellow', 'Green', 'Purple', 'Orange'],\n          datasets: [{\n            label: '# of Votes',\n            data: [12, 19, 3, 5, 2, 3],\n            backgroundColor: ['rgba(255, 99, 132, 0.2)', 'rgba(54, 162, 235, 0.2)', 'rgba(255, 206, 86, 0.2)', 'rgba(75, 192, 192, 0.2)', 'rgba(153, 102, 255, 0.2)', 'rgba(255, 159, 64, 0.2)'],\n            borderColor: ['rgba(255,99,132,1)', 'rgba(54, 162, 235, 1)', 'rgba(255, 206, 86, 1)', 'rgba(75, 192, 192, 1)', 'rgba(153, 102, 255, 1)', 'rgba(255, 159, 64, 1)'],\n            borderWidth: 1\n          }]\n        },\n        options: {\n          scales: {\n            yAxes: [{\n              ticks: {\n                beginAtZero: true\n              }\n            }]\n          }\n        }\n      });\n    }\n    static {\n      this.ɵfac = function SumbyproductComponent_Factory(t) {\n        return new (t || SumbyproductComponent)();\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: SumbyproductComponent,\n        selectors: [[\"app-sumbyproduct\"]],\n        decls: 45,\n        vars: 0,\n        consts: [[2, \"padding-top\", \"60px\"], [1, \"container-fluid\"], [1, \"p-sm-1\", \"bg-secondary\", \"text-white\", \"text-center\"], [1, \"form-row\"], [1, \"col-md-2\", \"mb-3\"], [1, \"custom-select\", \"custom-select-sm\"], [\"selected\", \"\"], [\"value\", \"1\"], [\"value\", \"2\"], [1, \"w-auto\"], [1, \"btn\", \"btn-sm\", \"btn-success\"], [1, \"container-fluid\", 2, \"width\", \"80%\"], [\"id\", \"myChart\"]],\n        template: function SumbyproductComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelement(0, \"app-topmenu\");\n            i0.ɵɵelementStart(1, \"section\", 0)(2, \"div\", 1)(3, \"h5\", 2);\n            i0.ɵɵtext(4, \"Sale Summary By Product\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(5, \"div\", 3)(6, \"div\", 4)(7, \"select\", 5)(8, \"option\", 6);\n            i0.ɵɵtext(9, \"\\u0E40\\u0E25\\u0E37\\u0E2D\\u0E01\\u0E23\\u0E39\\u0E1B\\u0E41\\u0E1A\\u0E1A\\u0E23\\u0E32\\u0E22\\u0E07\\u0E32\\u0E19\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(10, \"option\", 7);\n            i0.ɵɵtext(11, \"\\u0E01\\u0E25\\u0E38\\u0E48\\u0E21\\u0E2A\\u0E34\\u0E19\\u0E04\\u0E49\\u0E32\\u0E22\\u0E2D\\u0E14\\u0E02\\u0E32\\u0E22\\u0E14\\u0E35 50 \\u0E2D\\u0E31\\u0E19\\u0E14\\u0E31\\u0E1A\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(12, \"option\", 7);\n            i0.ɵɵtext(13, \"\\u0E2A\\u0E34\\u0E19\\u0E04\\u0E49\\u0E32\\u0E22\\u0E2D\\u0E14\\u0E02\\u0E32\\u0E22\\u0E14\\u0E35 50 \\u0E2D\\u0E31\\u0E19\\u0E14\\u0E31\\u0E1A\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(14, \"option\", 8);\n            i0.ɵɵtext(15, \"\\u0E01\\u0E25\\u0E38\\u0E48\\u0E21\\u0E2A\\u0E34\\u0E19\\u0E04\\u0E49\\u0E32\\u0E22\\u0E2D\\u0E14\\u0E02\\u0E32\\u0E22\\u0E44\\u0E21\\u0E48\\u0E14\\u0E35 50 \\u0E2D\\u0E31\\u0E19\\u0E14\\u0E31\\u0E1A\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(16, \"option\", 8);\n            i0.ɵɵtext(17, \"\\u0E2A\\u0E34\\u0E19\\u0E04\\u0E49\\u0E32\\u0E22\\u0E2D\\u0E14\\u0E02\\u0E32\\u0E22\\u0E44\\u0E21\\u0E48\\u0E14\\u0E35 50 \\u0E2D\\u0E31\\u0E19\\u0E14\\u0E31\\u0E1A\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(18, \"option\", 7);\n            i0.ɵɵtext(19, \"\\u0E01\\u0E25\\u0E38\\u0E48\\u0E21\\u0E2A\\u0E34\\u0E19\\u0E04\\u0E49\\u0E32\\u0E22\\u0E2D\\u0E14\\u0E02\\u0E32\\u0E22\\u0E14\\u0E35 20 \\u0E2D\\u0E31\\u0E19\\u0E14\\u0E31\\u0E1A\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(20, \"option\", 7);\n            i0.ɵɵtext(21, \"\\u0E2A\\u0E34\\u0E19\\u0E04\\u0E49\\u0E32\\u0E22\\u0E2D\\u0E14\\u0E02\\u0E32\\u0E22\\u0E14\\u0E35 20 \\u0E2D\\u0E31\\u0E19\\u0E14\\u0E31\\u0E1A\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(22, \"option\", 8);\n            i0.ɵɵtext(23, \"\\u0E01\\u0E25\\u0E38\\u0E48\\u0E21\\u0E2A\\u0E34\\u0E19\\u0E04\\u0E49\\u0E32\\u0E22\\u0E2D\\u0E14\\u0E02\\u0E32\\u0E22\\u0E44\\u0E21\\u0E48\\u0E14\\u0E35 20 \\u0E2D\\u0E31\\u0E19\\u0E14\\u0E31\\u0E1A\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(24, \"option\", 8);\n            i0.ɵɵtext(25, \"\\u0E2A\\u0E34\\u0E19\\u0E04\\u0E49\\u0E32\\u0E22\\u0E2D\\u0E14\\u0E02\\u0E32\\u0E22\\u0E44\\u0E21\\u0E48\\u0E14\\u0E35 20 \\u0E2D\\u0E31\\u0E19\\u0E14\\u0E31\\u0E1A\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(26, \"div\", 4)(27, \"select\", 5)(28, \"option\", 6);\n            i0.ɵɵtext(29, \"\\u0E40\\u0E25\\u0E37\\u0E2D\\u0E01\\u0E0A\\u0E48\\u0E27\\u0E07\\u0E40\\u0E27\\u0E25\\u0E32\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(30, \"option\", 7);\n            i0.ɵɵtext(31, \"\\u0E40\\u0E14\\u0E37\\u0E2D\\u0E19\\u0E19\\u0E35\\u0E49\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(32, \"option\", 7);\n            i0.ɵɵtext(33, \"\\u0E40\\u0E14\\u0E37\\u0E2D\\u0E19\\u0E17\\u0E35\\u0E48\\u0E1C\\u0E48\\u0E32\\u0E19\\u0E21\\u0E32\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(34, \"option\", 7);\n            i0.ɵɵtext(35, \"\\u0E44\\u0E15\\u0E23\\u0E21\\u0E32\\u0E2A\\u0E17\\u0E35\\u0E48\\u0E1C\\u0E48\\u0E32\\u0E19\\u0E21\\u0E32\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(36, \"option\", 8);\n            i0.ɵɵtext(37, \"\\u0E1B\\u0E35\\u0E17\\u0E35\\u0E48\\u0E1C\\u0E48\\u0E32\\u0E19\\u0E21\\u0E32\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(38, \"option\", 8);\n            i0.ɵɵtext(39, \"2 \\u0E1B\\u0E35\\u0E17\\u0E35\\u0E48\\u0E1C\\u0E48\\u0E32\\u0E19\\u0E21\\u0E32\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(40, \"div\", 9)(41, \"button\", 10);\n            i0.ɵɵtext(42, \"Search\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(43, \"div\", 11);\n            i0.ɵɵelement(44, \"canvas\", 12);\n            i0.ɵɵelementEnd()()();\n          }\n        },\n        styles: [\"canvas[_ngcontent-%COMP%]{-moz-user-select:none;-webkit-user-select:none;-ms-user-select:none;width:100%;height:80%}\"]\n      });\n    }\n  }\n  return SumbyproductComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}