{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nvar core_1 = require(\"@angular/core\");\nvar DataTable_1 = require(\"./DataTable\");\nvar _ = require(\"lodash\");\nvar BootstrapPaginator = function () {\n  function BootstrapPaginator() {\n    this.rowsOnPageSet = [];\n    this.minRowsOnPage = 0;\n  }\n  BootstrapPaginator.prototype.ngOnChanges = function (changes) {\n    if (changes.rowsOnPageSet) {\n      this.minRowsOnPage = _.min(this.rowsOnPageSet);\n    }\n  };\n  BootstrapPaginator.decorators = [{\n    type: core_1.Component,\n    args: [{\n      selector: \"mfBootstrapPaginator\",\n      template: \"\\n    <mfPaginator #p [mfTable]=\\\"mfTable\\\">\\n        <ul class=\\\"pagination\\\" *ngIf=\\\"p.dataLength > p.rowsOnPage\\\">\\n            <li class=\\\"page-item\\\" [class.disabled]=\\\"p.activePage <= 1\\\" (click)=\\\"p.setPage(1)\\\">\\n                <a class=\\\"page-link\\\" style=\\\"cursor: pointer\\\">&laquo;</a>\\n            </li>\\n            <li class=\\\"page-item\\\" *ngIf=\\\"p.activePage > 4 && p.activePage + 1 > p.lastPage\\\" (click)=\\\"p.setPage(p.activePage - 4)\\\">\\n                <a class=\\\"page-link\\\" style=\\\"cursor: pointer\\\">{{p.activePage-4}}</a>\\n            </li>\\n            <li class=\\\"page-item\\\" *ngIf=\\\"p.activePage > 3 && p.activePage + 2 > p.lastPage\\\" (click)=\\\"p.setPage(p.activePage - 3)\\\">\\n                <a class=\\\"page-link\\\" style=\\\"cursor: pointer\\\">{{p.activePage-3}}</a>\\n            </li>\\n            <li class=\\\"page-item\\\" *ngIf=\\\"p.activePage > 2\\\" (click)=\\\"p.setPage(p.activePage - 2)\\\">\\n                <a class=\\\"page-link\\\" style=\\\"cursor: pointer\\\">{{p.activePage-2}}</a>\\n            </li>\\n            <li class=\\\"page-item\\\" *ngIf=\\\"p.activePage > 1\\\" (click)=\\\"p.setPage(p.activePage - 1)\\\">\\n                <a class=\\\"page-link\\\" style=\\\"cursor: pointer\\\">{{p.activePage-1}}</a>\\n            </li>\\n            <li class=\\\"page-item active\\\">\\n                <a class=\\\"page-link\\\" style=\\\"cursor: pointer\\\">{{p.activePage}}</a>\\n            </li>\\n            <li class=\\\"page-item\\\" *ngIf=\\\"p.activePage + 1 <= p.lastPage\\\" (click)=\\\"p.setPage(p.activePage + 1)\\\">\\n                <a class=\\\"page-link\\\" style=\\\"cursor: pointer\\\">{{p.activePage+1}}</a>\\n            </li>\\n            <li class=\\\"page-item\\\" *ngIf=\\\"p.activePage + 2 <= p.lastPage\\\" (click)=\\\"p.setPage(p.activePage + 2)\\\">\\n                <a class=\\\"page-link\\\" style=\\\"cursor: pointer\\\">{{p.activePage+2}}</a>\\n            </li>\\n            <li class=\\\"page-item\\\" *ngIf=\\\"p.activePage + 3 <= p.lastPage && p.activePage < 3\\\" (click)=\\\"p.setPage(p.activePage + 3)\\\">\\n                <a class=\\\"page-link\\\" style=\\\"cursor: pointer\\\">{{p.activePage+3}}</a>\\n            </li>\\n            <li class=\\\"page-item\\\" *ngIf=\\\"p.activePage + 4 <= p.lastPage && p.activePage < 2\\\" (click)=\\\"p.setPage(p.activePage + 4)\\\">\\n                <a class=\\\"page-link\\\" style=\\\"cursor: pointer\\\">{{p.activePage+4}}</a>\\n            </li>\\n            <li class=\\\"page-item\\\" [class.disabled]=\\\"p.activePage >= p.lastPage\\\" (click)=\\\"p.setPage(p.lastPage)\\\">\\n                <a class=\\\"page-link\\\" style=\\\"cursor: pointer\\\">&raquo;</a>\\n            </li>\\n        </ul>\\n        <ul class=\\\"pagination pull-right float-sm-right\\\" *ngIf=\\\"p.dataLength > minRowsOnPage\\\">\\n            <li class=\\\"page-item\\\" *ngFor=\\\"let rows of rowsOnPageSet\\\" [class.active]=\\\"p.rowsOnPage===rows\\\" (click)=\\\"p.setRowsOnPage(rows)\\\">\\n                <a class=\\\"page-link\\\" style=\\\"cursor: pointer\\\">{{rows}}</a>\\n            </li>\\n        </ul>\\n    </mfPaginator>\\n    \"\n    }]\n  }];\n  BootstrapPaginator.propDecorators = {\n    \"rowsOnPageSet\": [{\n      type: core_1.Input,\n      args: [\"rowsOnPageSet\"]\n    }],\n    \"mfTable\": [{\n      type: core_1.Input,\n      args: [\"mfTable\"]\n    }]\n  };\n  return BootstrapPaginator;\n}();\nexports.BootstrapPaginator = BootstrapPaginator;\n//# sourceMappingURL=BootstrapPaginator.js.map", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}