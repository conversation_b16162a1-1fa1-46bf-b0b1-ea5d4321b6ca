{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.concatMap = void 0;\nvar mergeMap_1 = require(\"./mergeMap\");\nvar isFunction_1 = require(\"../util/isFunction\");\nfunction concatMap(project, resultSelector) {\n  return isFunction_1.isFunction(resultSelector) ? mergeMap_1.mergeMap(project, resultSelector, 1) : mergeMap_1.mergeMap(project, 1);\n}\nexports.concatMap = concatMap;\n//# sourceMappingURL=concatMap.js.map", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}