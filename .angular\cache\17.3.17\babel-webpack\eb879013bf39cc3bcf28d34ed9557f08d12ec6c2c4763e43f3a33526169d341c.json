{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.schedulePromise = void 0;\nvar innerFrom_1 = require(\"../observable/innerFrom\");\nvar observeOn_1 = require(\"../operators/observeOn\");\nvar subscribeOn_1 = require(\"../operators/subscribeOn\");\nfunction schedulePromise(input, scheduler) {\n  return innerFrom_1.innerFrom(input).pipe(subscribeOn_1.subscribeOn(scheduler), observeOn_1.observeOn(scheduler));\n}\nexports.schedulePromise = schedulePromise;\n//# sourceMappingURL=schedulePromise.js.map", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}