{"ast": null, "code": "const {\n  isArray\n} = Array;\nconst {\n  getPrototypeOf,\n  prototype: objectProto,\n  keys: getKeys\n} = Object;\nexport function argsArgArrayOrObject(args) {\n  if (args.length === 1) {\n    const first = args[0];\n    if (isArray(first)) {\n      return {\n        args: first,\n        keys: null\n      };\n    }\n    if (isPOJ<PERSON>(first)) {\n      const keys = getKeys(first);\n      return {\n        args: keys.map(key => first[key]),\n        keys\n      };\n    }\n  }\n  return {\n    args: args,\n    keys: null\n  };\n}\nfunction isPOJO(obj) {\n  return obj && typeof obj === 'object' && getPrototypeOf(obj) === objectProto;\n}\n//# sourceMappingURL=argsArgArrayOrObject.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}