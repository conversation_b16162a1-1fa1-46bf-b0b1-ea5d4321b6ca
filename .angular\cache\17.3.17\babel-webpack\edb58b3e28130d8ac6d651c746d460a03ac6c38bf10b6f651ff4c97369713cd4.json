{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nvar core_1 = require(\"@angular/core\");\nvar common_1 = require(\"@angular/common\");\nvar select_1 = require(\"./select\");\nvar select_pipes_1 = require(\"./select-pipes\");\nvar off_click_1 = require(\"./off-click\");\nvar SelectModule = function () {\n  function SelectModule() {}\n  SelectModule.decorators = [{\n    type: core_1.NgModule,\n    args: [{\n      imports: [common_1.CommonModule],\n      declarations: [select_1.SelectComponent, select_pipes_1.HighlightPipe, off_click_1.OffClickDirective],\n      exports: [select_1.SelectComponent, select_pipes_1.HighlightPipe, off_click_1.OffClickDirective]\n    }]\n  }];\n  /** @nocollapse */\n  SelectModule.ctorParameters = function () {\n    return [];\n  };\n  return SelectModule;\n}();\nexports.SelectModule = SelectModule;", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}