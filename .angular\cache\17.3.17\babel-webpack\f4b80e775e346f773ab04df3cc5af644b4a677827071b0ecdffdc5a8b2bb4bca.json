{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.distinct = void 0;\nvar lift_1 = require(\"../util/lift\");\nvar OperatorSubscriber_1 = require(\"./OperatorSubscriber\");\nvar noop_1 = require(\"../util/noop\");\nvar innerFrom_1 = require(\"../observable/innerFrom\");\nfunction distinct(keySelector, flushes) {\n  return lift_1.operate(function (source, subscriber) {\n    var distinctKeys = new Set();\n    source.subscribe(OperatorSubscriber_1.createOperatorSubscriber(subscriber, function (value) {\n      var key = keySelector ? keySelector(value) : value;\n      if (!distinctKeys.has(key)) {\n        distinctKeys.add(key);\n        subscriber.next(value);\n      }\n    }));\n    flushes && innerFrom_1.innerFrom(flushes).subscribe(OperatorSubscriber_1.createOperatorSubscriber(subscriber, function () {\n      return distinctKeys.clear();\n    }, noop_1.noop));\n  });\n}\nexports.distinct = distinct;\n//# sourceMappingURL=distinct.js.map", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}