{"ast": null, "code": "import { HttpClient, HttpEventType } from '@angular/common/http';\nimport { TemplateRef } from '@angular/core';\nimport { ActivatedRoute, Router } from '@angular/router';\nimport { NgbCalendar } from '@ng-bootstrap/ng-bootstrap';\nimport { Ng2ImgMaxService } from 'ng2-img-max';\nimport { BsModalService } from 'ngx-bootstrap/modal';\nimport { debounceTime, map } from 'rxjs/operators';\nimport { WebapiService } from '../webapi.service';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"ngx-bootstrap/modal\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"ng2-img-max\";\nimport * as i4 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i5 from \"@angular/common/http\";\nimport * as i6 from \"../webapi.service\";\nimport * as i7 from \"@angular/common\";\nimport * as i8 from \"../../../node_modules/@angular/forms/index\";\nimport * as i9 from \"ngx-bootstrap/datepicker\";\nimport * as i10 from \"../topmenu/topmenu.component\";\nconst _c0 = () => ({\n  dateInputFormat: \"DD/MM/YYYY\"\n});\nconst _c1 = a0 => ({\n  \"display\": a0,\n  \"opacity\": 1\n});\nconst _c2 = (a0, a1) => ({\n  \"color\": a0,\n  \"background-color\": a1\n});\nconst _c3 = a0 => ({\n  \"color\": a0\n});\nfunction InvoicecashlistComponent_div_6_option_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 83);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r4 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵpropertyInterpolate(\"value\", item_r4.groupid);\n    i0.ɵɵproperty(\"selected\", ctx_r2.CodeSo == item_r4.groupid)(\"value\", item_r4.groupid);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\" \", item_r4.groupid, \" (\", item_r4.name, \") \");\n  }\n}\nfunction InvoicecashlistComponent_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 12)(1, \"select\", 79);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function InvoicecashlistComponent_div_6_Template_select_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.CodeSo, $event) || (ctx_r2.CodeSo = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementStart(2, \"option\", 80);\n    i0.ɵɵtext(3, \"--\\u0E40\\u0E25\\u0E37\\u0E2D\\u0E01\\u0E23\\u0E2B\\u0E31\\u0E2A\\u0E1E\\u0E19\\u0E31\\u0E01\\u0E07\\u0E32\\u0E19\\u0E02\\u0E32\\u0E22--\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"option\", 81);\n    i0.ɵɵtext(5, \"\\u0E40\\u0E25\\u0E37\\u0E2D\\u0E01\\u0E23\\u0E32\\u0E22\\u0E01\\u0E32\\u0E23\\u0E17\\u0E31\\u0E49\\u0E07\\u0E2B\\u0E21\\u0E14\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, InvoicecashlistComponent_div_6_option_6_Template, 2, 5, \"option\", 82);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.CodeSo);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"selected\", ctx_r2.CodeSo == \"\\u0E40\\u0E25\\u0E37\\u0E2D\\u0E01\\u0E23\\u0E32\\u0E22\\u0E01\\u0E32\\u0E23\\u0E17\\u0E31\\u0E49\\u0E07\\u0E2B\\u0E21\\u0E14\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.DateGroupsaleman);\n  }\n}\nfunction InvoicecashlistComponent_ng_template_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 84);\n    i0.ɵɵlistener(\"click\", function InvoicecashlistComponent_ng_template_8_Template_div_click_0_listener() {\n      const r_r6 = i0.ɵɵrestoreView(_r5).result;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.getcustomersalefunction(r_r6.name));\n    });\n    i0.ɵɵelementStart(1, \"label\", 85);\n    i0.ɵɵlistener(\"mousedown\", function InvoicecashlistComponent_ng_template_8_Template_label_mousedown_1_listener() {\n      const r_r6 = i0.ɵɵrestoreView(_r5).result;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.getcustomersalefunction(r_r6.name));\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const r_r6 = ctx.result;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"\", r_r6.name, \" (\", r_r6.accountnum, \")\");\n  }\n}\nfunction InvoicecashlistComponent_tr_43_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\", 24)(1, \"td\", 86);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\", 86);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\", 86);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\", 87);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\", 88);\n    i0.ɵɵtext(10);\n    i0.ɵɵpipe(11, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"td\", 88);\n    i0.ɵɵtext(13);\n    i0.ɵɵpipe(14, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"td\", 88);\n    i0.ɵɵtext(16);\n    i0.ɵɵpipe(17, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"td\", 89)(19, \"button\", 90);\n    i0.ɵɵlistener(\"click\", function InvoicecashlistComponent_tr_43_Template_button_click_19_listener() {\n      const item_r8 = i0.ɵɵrestoreView(_r7).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.setinvoiceid(item_r8.Orderaccount, item_r8.Salegroup));\n    });\n    i0.ɵɵtext(20, \" Upload \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const item_r8 = ctx.$implicit;\n    const i_r9 = ctx.index;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction2(23, _c2, ctx_r2.getColormin(item_r8.invoicedate, item_r8.Invoiceamount, item_r8.sumMax, item_r8.summin), ctx_r2.getColorminBG(item_r8.typeCK)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"\", i_r9 + 1, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(26, _c3, ctx_r2.getColormin(item_r8.invoicedate, item_r8.Invoiceamount, item_r8.sumMax, item_r8.summin)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r8.Salegroup);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(28, _c3, ctx_r2.getColormin(item_r8.invoicedate, item_r8.Invoiceamount, item_r8.sumMax, item_r8.summin)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r8.Orderaccount);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(30, _c3, ctx_r2.getColormin(item_r8.invoicedate, item_r8.Invoiceamount, item_r8.sumMax, item_r8.summin)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r8.Invoicingname);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(32, _c3, ctx_r2.getColormin(item_r8.invoicedate, item_r8.Invoiceamount, item_r8.sumMax, item_r8.summin)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(11, 14, item_r8.Salesbalance, \"1.2-2\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(34, _c3, ctx_r2.getColormin(item_r8.invoicedate, item_r8.Invoiceamount, item_r8.sumMax, item_r8.summin)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(14, 17, item_r8.Sumtax, \"1.2-2\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(36, _c3, ctx_r2.getColormin(item_r8.invoicedate, item_r8.Invoiceamount, item_r8.sumMax, item_r8.summin)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(17, 20, item_r8.Invoiceamount, \"1.2-2\"));\n  }\n}\nfunction InvoicecashlistComponent_th_105_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"th\", 46);\n  }\n}\nfunction InvoicecashlistComponent_tr_107_button_27_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 98);\n    i0.ɵɵlistener(\"click\", function InvoicecashlistComponent_tr_107_button_27_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const itembill_r13 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      const Modalview_r14 = i0.ɵɵreference(182);\n      return i0.ɵɵresetView(ctx_r2.openModalIMG(Modalview_r14, itembill_r13.imgurl, itembill_r13.typeCK));\n    });\n    i0.ɵɵtext(1, \" View \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction InvoicecashlistComponent_tr_107_button_28_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 99);\n    i0.ɵɵlistener(\"click\", function InvoicecashlistComponent_tr_107_button_28_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r15);\n      const itembill_r13 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      const Modalview_r14 = i0.ɵɵreference(182);\n      return i0.ɵɵresetView(ctx_r2.openModalIMG(Modalview_r14, itembill_r13.imgurl, itembill_r13.typeCK));\n    });\n    i0.ɵɵtext(1, \" View \");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const itembill_r13 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"disabled\", itembill_r13.typeCK !== \"1\");\n  }\n}\nfunction InvoicecashlistComponent_tr_107_td_29_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"td\", 89)(1, \"button\", 100);\n    i0.ɵɵlistener(\"click\", function InvoicecashlistComponent_tr_107_td_29_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const itembill_r13 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.openpdf(itembill_r13.invoiceid));\n    });\n    i0.ɵɵtext(2, \" print \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction InvoicecashlistComponent_tr_107_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 91);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\", 92);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\", 92);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\", 92);\n    i0.ɵɵtext(8);\n    i0.ɵɵpipe(9, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"td\", 92);\n    i0.ɵɵtext(11);\n    i0.ɵɵpipe(12, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"td\", 91);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"td\", 93);\n    i0.ɵɵtext(16);\n    i0.ɵɵpipe(17, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"th\", 93);\n    i0.ɵɵtext(19);\n    i0.ɵɵpipe(20, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"th\", 93);\n    i0.ɵɵtext(22);\n    i0.ɵɵpipe(23, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"td\", 89)(25, \"input\", 94);\n    i0.ɵɵlistener(\"click\", function InvoicecashlistComponent_tr_107_Template_input_click_25_listener($event) {\n      const i_r11 = i0.ɵɵrestoreView(_r10).index;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.checkIfAllSelected($event.target.checked, i_r11, ctx_r2.paymenttype));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(26, \"td\", 89);\n    i0.ɵɵtemplate(27, InvoicecashlistComponent_tr_107_button_27_Template, 2, 0, \"button\", 95)(28, InvoicecashlistComponent_tr_107_button_28_Template, 2, 1, \"button\", 96);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(29, InvoicecashlistComponent_tr_107_td_29_Template, 3, 0, \"td\", 97);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const itembill_r13 = ctx.$implicit;\n    const i_r11 = ctx.index;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction2(37, _c2, ctx_r2.getColor(itembill_r13.invoicedate, itembill_r13.invoiceamount), ctx_r2.getColorminBG(itembill_r13.typeCK)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i_r11 + 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(40, _c3, ctx_r2.getColor(itembill_r13.invoicedate, itembill_r13.invoiceamount)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(itembill_r13.salesid);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(42, _c3, ctx_r2.getColor(itembill_r13.invoicedate, itembill_r13.invoiceamount)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(itembill_r13.invoiceid);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(44, _c3, ctx_r2.getColor(itembill_r13.invoicedate, itembill_r13.invoiceamount)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind2(9, 22, itembill_r13.invoicedate, \"dd/MM/yyyy\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(46, _c3, ctx_r2.getColor(itembill_r13.invoicedate, itembill_r13.invoiceamount)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(12, 25, itembill_r13.duedate, \"dd/MM/yyyy\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(48, _c3, ctx_r2.getColor(itembill_r13.invoicedate, itembill_r13.invoiceamount)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(itembill_r13.payment);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(50, _c3, ctx_r2.getColor(itembill_r13.invoicedate, itembill_r13.invoiceamount)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(17, 28, itembill_r13.salesbalance, \"1.2-2\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(52, _c3, ctx_r2.getColor(itembill_r13.invoicedate, itembill_r13.invoiceamount)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(20, 31, itembill_r13.sumtax, \"1.2-2\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(54, _c3, ctx_r2.getColor(itembill_r13.invoicedate, itembill_r13.invoiceamount)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(23, 34, itembill_r13.invoiceamount, \"1.2-2\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"checked\", itembill_r13.check);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.closeED);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.closeED);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.closeED);\n  }\n}\nfunction InvoicecashlistComponent_tr_108_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 101);\n    i0.ɵɵtext(2, \" \\u0E44\\u0E21\\u0E48\\u0E21\\u0E35\\u0E23\\u0E32\\u0E22\\u0E01\\u0E32\\u0E23 \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction InvoicecashlistComponent_td_122_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"td\", 54);\n  }\n}\nfunction InvoicecashlistComponent_div_130_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 56)(1, \"div\", 102)(2, \"select\", 103);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function InvoicecashlistComponent_div_130_Template_select_ngModelChange_2_listener($event) {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.chDraft, $event) || (ctx_r2.chDraft = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementStart(3, \"option\", 104);\n    i0.ɵɵtext(4, \"\\u0E40\\u0E25\\u0E37\\u0E2D\\u0E01\\u0E1B\\u0E23\\u0E30\\u0E40\\u0E20\\u0E17\\u0E2A\\u0E16\\u0E32\\u0E19\\u0E30\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"option\", 105);\n    i0.ɵɵtext(6, \"\\u0E23\\u0E32\\u0E22\\u0E01\\u0E32\\u0E23\\u0E40\\u0E2A\\u0E23\\u0E47\\u0E08\\u0E2A\\u0E34\\u0E49\\u0E19\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"option\", 106);\n    i0.ɵɵtext(8, \"Draft \\u0E23\\u0E32\\u0E22\\u0E01\\u0E32\\u0E23\\u0E19\\u0E35\\u0E49\\u0E44\\u0E27\\u0E49\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.chDraft);\n  }\n}\nfunction InvoicecashlistComponent_ng_template_181_ng_template_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"b\");\n    i0.ɵɵtext(1, \"Images Billno\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction InvoicecashlistComponent_ng_template_181_ng_template_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 112);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵpropertyInterpolate(\"src\", ctx_r2.ImageBillno, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction InvoicecashlistComponent_ng_template_181_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 107);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementStart(2, \"div\", 108)(3, \"ngb-tabset\")(4, \"ngb-tab\");\n    i0.ɵɵtemplate(5, InvoicecashlistComponent_ng_template_181_ng_template_5_Template, 2, 0, \"ng-template\", 109)(6, InvoicecashlistComponent_ng_template_181_ng_template_6_Template, 1, 1, \"ng-template\", 110);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(7, \"div\", 111)(8, \"button\", 76);\n    i0.ɵɵlistener(\"click\", function InvoicecashlistComponent_ng_template_181_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.modalRefshow.hide());\n    });\n    i0.ɵɵtext(9, \" \\u0E1B\\u0E34\\u0E14\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"\", ctx_r2.altBill, \" \");\n  }\n}\nexport let InvoicecashlistComponent = /*#__PURE__*/(() => {\n  class InvoicecashlistComponent {\n    constructor(modalService, route, router, ng2ImgMax, calendar, http, service) {\n      this.modalService = modalService;\n      this.route = route;\n      this.router = router;\n      this.ng2ImgMax = ng2ImgMax;\n      this.calendar = calendar;\n      this.http = http;\n      this.service = service;\n      this.config = {\n        ignoreBackdropClick: true,\n        class: 'modal-md'\n      };\n      this.configview = {\n        ignoreBackdropClick: true,\n        class: 'modal-lg '\n      };\n      this.Remark = '';\n      this.max = 2000;\n      this.mdlSampleIsOpen = false;\n      this.alt = '';\n      this.checkreload = true;\n      this.mdlSampleIsOpenIMG = false;\n      this.altimg = '';\n      this.mdlSampleIsOpen2 = false;\n      this.alt2 = '';\n      this.checkreload2 = true;\n      this.mdlSampleIsOpensuccess = false;\n      this.altsuccess = '';\n      this.checkreloadsuccess = true;\n      this.idinvi = [];\n      this.allid = [];\n      this.CodeInvoice = '';\n      this.CodeSo = '';\n      this.imginvoice = \"\";\n      this.fromdate = '';\n      this.ImageIN = '';\n      this.todate = '';\n      this.productprice = 0;\n      this.sumvat = 0;\n      this.sumprice = 0;\n      this.setInvoice = '';\n      this.selectedFile = null;\n      this.imgInvoice = '';\n      this.textload = '';\n      this.load = \" สถานะ : Upload : 0 %\";\n      this.imageUrl = \"assets/img/default-image.png\";\n      this.uploadimg = '';\n      this.dateshipping = '';\n      this.dateshippingto = '';\n      this.datalogin = [];\n      this.testclose = false;\n      this.testcheck = false;\n      this.trueproductprice = 0;\n      this.truesumvat = 0;\n      this.truesumprice = 0;\n      this.toDateimg = new Date();\n      this.DataED = '';\n      this.ImageBillno = '';\n      this.urlimg = '';\n      this.closeNoED = false;\n      this.closeED = false;\n      this.customers = [];\n      this.txtcustomer = 'ค้นหาจากลูกค้า';\n      this.numviewpaymenttype = false;\n      this.chDraft = \"0\";\n      this.listINV = '';\n      /////\n      this.search = text$ =>\n      //Autocomplete ลูกค้า\n      text$.pipe(debounceTime(200), map(term => term === '' ? [] : this.customers.filter(v => v.name.toLowerCase().indexOf(term.toLowerCase()) > -1 || v.accountnum.toLowerCase().indexOf(term.toLowerCase()) > -1).slice(0, 50)));\n      this.formatter = x => x.name + ' (' + x.accountnum + ')';\n      this.datalogin = JSON.parse(sessionStorage.getItem('login'));\n      localStorage.removeItem('DataSOderreview');\n      localStorage.removeItem('DataSOderlist');\n      this.datalogin = JSON.parse(sessionStorage.getItem('login'));\n      console.log(sessionStorage.getItem('cashDateTo') + '/' + sessionStorage.getItem('cashDateFrom'));\n      this.paymenttype = 'All';\n      if (this.datalogin == null) {\n        this.router.navigate(['login']);\n      }\n      this.urlimg = service.geturlserviceIMG();\n      if (sessionStorage.getItem('cashDateTo') != null && sessionStorage.getItem('cashDateFrom') != null) {\n        this.Datatodate = new Date(sessionStorage.getItem('cashDateTo'));\n        this.Datafromdate = new Date(sessionStorage.getItem('cashDateFrom'));\n      } else {\n        this.fromDate = calendar.getPrev(calendar.getToday(), 'd', 365);\n        this.toDate = calendar.getToday();\n        this.Datatodate = new Date(this.toDate.year, this.toDate.month - 1, this.toDate.day);\n        this.Datafromdate = new Date(this.toDate.year, this.toDate.month - 1, 1);\n      }\n      this.url = service.geturlservice();\n      this.uploadimg = service.geturlloadimgservice();\n      //this.datalogin[0].salegroup;\n      // this.groupsale=this.datalogin[0].salegroup\n      this.getuser();\n      this.total = 0;\n      this.dateinv = '';\n      this.dateINV = '';\n      this.getgroupsaleman();\n      //  this.Datafromdate= new Date(this.fromDate.year, this.fromDate.month-1, 1);\n      //  this.Datafromdate= new Date(this.fromDate.year, this.fromDate.month-1, 1);\n      /*this.getdate();*/\n      if (this.route.snapshot.queryParams.todate == undefined) {\n        this.getbackto();\n      } else {\n        this.getdatetotoback();\n      }\n      if (this.route.snapshot.queryParams.fromdate == undefined) {\n        this.getbackfromdate();\n      } else {\n        this.getdatefromdatetoback();\n      }\n      if (this.route.snapshot.queryParams.CodeINV == undefined) {} else {\n        this.getidINV();\n      }\n      if (this.route.snapshot.queryParams.Customer == undefined) {} else {\n        this.getbackcustomer();\n      }\n      if (this.route.snapshot.queryParams.CodeSo == undefined) {} else {\n        this.getdataIDsales();\n      }\n      if (this.route.snapshot.queryParams.datatoback == undefined) {} else {\n        this.getDatatoback();\n      }\n    }\n    getDatatoback() {\n      this.datatoback = this.route.snapshot.queryParams.datatoback;\n      this.DataSearch = [];\n      this.DataED = '0';\n      this.sumprice = 0.00;\n      this.sumvat = 0.00;\n      this.productprice = 0.00;\n      this.Datatoback = this.datatoback;\n      this.http.get(this.datatoback).subscribe(res => {\n        if (res.length > 0) {\n          this.DataSearch = res;\n          this.sumprice = 0.00;\n          this.sumvat = 0.00;\n          this.productprice = 0.00;\n          this.sum();\n          /*this.getdate();*/\n        } else {\n          this.sumprice = 0.00;\n          this.sumvat = 0.00;\n          this.productprice = 0.00;\n          alert('ไม่พบข้อมูล');\n        }\n        /*   this.getdate();*/\n      }, error => {\n        alert('เกิดปัญหาในการ Process ข้อมูล กรุณารายการใหม่อีกครั้ง');\n      });\n    }\n    getbackto() {\n      this.todate = `${this.Datatodate.getFullYear()}-${this.Datatodate.getMonth() + 1}-${this.Datatodate.getDate()}`;\n    }\n    getbackfromdate() {\n      this.fromdate = `${this.Datafromdate.getFullYear()}-${this.Datafromdate.getMonth() + 1}-${this.Datafromdate.getDate()}`;\n    }\n    getbackcustomer() {\n      if (this.route.snapshot.queryParams.Customer == '%20') {\n        this.txtcustomer = 'ค้นหาจากลูกค้า';\n      } else {\n        this.txtcustomer = this.route.snapshot.queryParams.Customer;\n      }\n    }\n    getdatetotoback() {\n      this.Datatodate = new Date(this.route.snapshot.queryParams.todate);\n      this.Datafromdate = new Date(this.route.snapshot.queryParams.fromdate);\n    }\n    getdatefromdatetoback() {\n      this.Datafromdate = new Date(this.route.snapshot.queryParams.fromdate);\n    }\n    getdataIDsales() {\n      this.CodeSo = this.route.snapshot.queryParams.CodeSo;\n    }\n    Modalremark(template) {\n      this.ModalremarkRef = this.modalService.show(template, {\n        class: 'modal-lg'\n      });\n    }\n    getidINV() {\n      this.CodeInvoice = this.route.snapshot.queryParams.CodeINV;\n      this.dateINV = this.route.snapshot.queryParams.CodeINV;\n      /* this.CodeSo=this.route.snapshot.queryParams.CodeSo\n       this.Customer=this.route.snapshot.queryParams.Customer*/\n    }\n    getColor(country, sum) {\n      // console.log(typeCK);\n      if (sum < 0) {\n        return '#000';\n      }\n      let today = Number(new Date());\n      let invdate = Number(new Date(country));\n      var invdate7 = today - invdate;\n      /*alert(today);604800000\n      alert(invdate);     return 'green';*/\n      if (invdate7 > 1036800000) {\n        return 'red';\n      } else if (invdate7 <= 1036800000 && invdate7 >= 604800000) {\n        return '#0e0dde';\n      } else {\n        return 'green';\n      }\n    }\n    getColormin(country, sum, summax, summin) {\n      if (summax == summin && sum < 0) {\n        return '#000';\n      }\n      let today = Number(new Date());\n      let invdate = Number(new Date(country));\n      var invdate7 = today - invdate;\n      /*alert(today);604800000 = 7 วัน\n      //1296000000   = 15 วัน\n      //1036800000 = 12วัน\n      //86400000 = 1 วัน\n      alert(invdate);     return 'green';*/\n      if (invdate7 > 1036800000) {\n        return 'red';\n      } else if (invdate7 <= 1036800000 && invdate7 >= 604800000) {\n        return '#0e0dde';\n      } else {\n        return 'green';\n      }\n    }\n    getColorminBG(CK) {\n      if (CK == 1) {\n        return '#969595';\n      } else {\n        return '';\n      }\n    }\n    //////\n    //ดึงรหัสลูกค้ามาใช้ใน Autocomplete\n    getcostomerauto() {\n      var idsale = this.datalogin[0].salegroup;\n      if (this.datalogin[0].id_group_user == '153Admin' || this.datalogin[0].id_group_user == '1860Administrator') {\n        idsale = '%20';\n      } else {\n        idsale = this.datalogin[0].salegroup;\n      }\n      this.http.get(this.url + 'customerauto/' + idsale).subscribe(res => {\n        this.customers = res;\n      });\n    }\n    test(ele) {\n      console.log(ele);\n    }\n    opendelete(event) {\n      this.chDraft = \"0\";\n    }\n    getdate() {\n      this.fromdate = `${this.Datafromdate.getFullYear()}-${this.Datafromdate.getMonth() + 1}-${this.Datafromdate.getDate()}`;\n      this.todate = `${this.Datatodate.getFullYear()}-${this.Datatodate.getMonth() + 1}-${this.Datatodate.getDate()}`;\n    }\n    getgroupsaleman() {\n      this.DateGroupsaleman = [];\n      this.http.get(this.url + 'salesman').subscribe(res => {\n        if (res.length > 0) {\n          this.DateGroupsaleman = res;\n          this.getcostomerauto();\n        } else {\n          alert('ไม่สามารถติดต่อข้อมูลรหัส saleman ได้');\n        }\n      });\n    }\n    getuser() {\n      /* if(this.datalogin[0].salegroup=='admin'){\n         this.CodeSo='';\n         this.testclose=true;\n       }else{\n        this.testclose=false;\n        this.CodeSo=this.datalogin[0].salegroup;\n       }*/\n      if (this.datalogin[0].id_group_user == '153Admin' || this.datalogin[0].id_group_user == '1860Administrator') {\n        this.CodeSo = '';\n        this.testclose = true;\n      } else {\n        this.testclose = false;\n        this.CodeSo = this.datalogin[0].salegroup;\n      }\n    }\n    /* @ViewChild(\"Mycanvas\") Mycanvas;\n     Preview(e : any):void{\n      this.selectedFile = <File>e.target.files[0];\n      let canvas = this.Mycanvas.nativeElement;\n      let context = canvas.getContext('2d');\n      context.clearRect(0, 0, 300, 300);\n         var render = new FileReader();\n      render.onload = function(event){\n        var img = new Image();\n        img.onload = function(){\n          canvas.width = img.width;\n          canvas.height = img.height;\n          context.drawImage(img, 0, 0);\n        };\n        img.src = event.target.result;\n      };\n      render.readAsDataURL(e.target.files[0]);\n        }*/\n    handleFileInput(file) {\n      this.load = '';\n      this.selectedFile = '';\n      console.log(this.Filelist);\n      if (file.item(0).type == \"image/jpeg\") {\n        // this.openModal2(true,'กำลังปรับขนาดไฟล์',false)&& file.item(0).size <= (1024*1024*5)\n        let image = file.item(0);\n        this.selectedFile = image;\n        this.textload = this.selectedFile.name;\n        var reader = new FileReader();\n        reader.onload = event => {\n          this.imageUrl = event.target.result;\n        };\n        reader.readAsDataURL(this.selectedFile);\n        /* this.ng2ImgMax.resizeImage(image, 1024, 768).subscribe(\n           result => {\n             this.selectedFile = image;\n             this.textload= this.selectedFile.name;\n             //Show image preview\n             var reader = new FileReader();\n             reader.onload = (event:any) => {\n               this.imageUrl = event.target.result;\n             }\n             reader.readAsDataURL(this.selectedFile);\n            // this.openModal2(false,'',false)\n           },\n           error => {\n            // this.openModal(true,error,false)\n            alert('กรุณารายการใหม่อีกครั้ง');\n           }\n         );*/\n        console.log(this.Filelist.length);\n        console.log(this.selectedFile);\n      } else {\n        this.load = '';\n        this.openModal(true, 'ชนิดไฟล์รูปภาพไม่ถูกต้อง กรุณาเลือกชนิดไฟล์เป็น .jpeg', false);\n        this.imageUrl = \"assets/img/default-image.png\";\n      }\n    }\n    ngOnInit() {}\n    SearchInvoicecashlistEd() {\n      this.closeED = true;\n      this.DataED = '1';\n      this.DataSearch = [];\n      this.sumprice = 0.00;\n      this.sumvat = 0.00;\n      this.productprice = 0.00;\n      this.load = '';\n      var datacodeSo = '';\n      this.dateINV = '';\n      var dataCustomer = '';\n      this.getdate();\n      if (this.CodeInvoice !== '') {\n        this.dateINV = this.CodeInvoice;\n      }\n      if (this.Customer == undefined && this.CodeInvoice == '' && this.CodeSo == '') {\n        this.openModal(true, 'กรุณาป้อนข้อมูลอย่างน้อย 1 ช่อง ถ้าไม่ระบุวันที่ วันที่จะย้อนหลังเป็นเวลา 365 วัน', false);\n      } else {\n        if (this.fromdate == '') {\n          this.fromdate = `${this.todate}`;\n          sessionStorage.setItem('cashDateFrom', this.fromdate);\n        } else {\n          sessionStorage.setItem('cashDateFrom', this.fromdate);\n        }\n        if (this.todate == '') {\n          this.todate = `${this.fromDate}`;\n          sessionStorage.setItem('cashDateTo', this.todate);\n        } else {\n          sessionStorage.setItem('cashDateTo', this.todate);\n        }\n        if (this.Customer === undefined) {\n          dataCustomer = '%20';\n        } else {\n          dataCustomer = `${this.Customer.accountnum}`;\n        }\n        if (this.CodeInvoice == '') {\n          this.CodeInvoice = '%20';\n        }\n        if (this.CodeSo == '') {\n          if (this.datalogin[0].id_group_user == '153Admin' || this.datalogin[0].id_group_user == '1860Administrator') {\n            datacodeSo = `${this.CodeSo}`;\n          } else {\n            datacodeSo = `${this.datalogin[0].salegroup}`;\n          }\n        }\n        if (this.CodeSo !== '') {\n          if (this.datalogin[0].id_group_user == '153Admin' || this.datalogin[0].id_group_user == '1860Administrator') {\n            datacodeSo = `${this.CodeSo}`;\n          } else {\n            datacodeSo = `${this.datalogin[0].salegroup}`;\n          }\n        }\n        if (datacodeSo == '') {\n          datacodeSo = '%20';\n        }\n        if (dataCustomer === 'undefined') {\n          dataCustomer = '%20';\n        }\n        /*alert(this.url + 'cash_invoice/'+ this.fromdate + '/' + this.todate +'/' + this.CodeSo + '/' + this.CodeInvoice + '/' + this.Customer)*/\n        this.http.get(this.url + 'cash_invoice_modify_INV/' + this.fromdate + '/' + this.todate + '/' + datacodeSo + '/' + this.CodeInvoice + '/' + dataCustomer).subscribe(res => {\n          if (res.length > 0) {\n            this.DataSearch = res;\n            this.sumprice = 0.00;\n            this.sumvat = 0.00;\n            this.productprice = 0.00;\n            this.sum();\n            /*this.getdate();*/\n            if (this.datalogin[0].salegroup == 'admin') {\n              /* this.CodeSo='';*/\n            } else {}\n            if (dataCustomer == '%20') {\n              dataCustomer = '';\n            }\n            if (this.CodeInvoice == '%20') {\n              this.CodeInvoice = '';\n            }\n          } else {\n            if (this.datalogin[0].id_group_user == '153Admin' || this.datalogin[0].id_group_user == '1860Administrator') {\n              /* this.CodeSo='';*/\n            } else {}\n            if (dataCustomer == '%20') {\n              dataCustomer = '';\n            }\n            if (this.CodeInvoice == '%20') {\n              this.CodeInvoice = '';\n            }\n            this.sumprice = 0.00;\n            this.sumvat = 0.00;\n            this.productprice = 0.00;\n            this.openModal(true, 'ไม่พบข้อมูล', false);\n            /*   this.getdate();*/\n          }\n        }, error => {\n          //  this.openModal(true,'เกิดปัญหาในการ Process ข้อมูล กรุณารายการใหม่อีกครั้ง',false);\n          alert('เกิดปัญหาในการ Process ข้อมูล กรุณารายการใหม่อีกครั้ง');\n        });\n      }\n    }\n    SearchInvoicecashlist() {\n      this.closeED = false;\n      this.DataED = '0';\n      this.DataSearch = [];\n      this.sumprice = 0.00;\n      this.sumvat = 0.00;\n      this.productprice = 0.00;\n      this.load = '';\n      var datacodeSo = '';\n      this.dateINV = '';\n      var dataCustomer = '';\n      this.getdate();\n      var datatobackid = this.route.snapshot.queryParams.customerid;\n      var datatobackname = this.route.snapshot.queryParams.Customer;\n      if (this.CodeInvoice !== '') {\n        this.dateINV = this.CodeInvoice;\n      }\n      if (this.Customer === undefined && this.CodeInvoice == '' && this.CodeSo == '' && this.txtcustomer == 'ค้นหาจากลูกค้า') {\n        this.openModal(true, 'กรุณาป้อนข้อมูลอย่างน้อย 1 ช่อง ถ้าไม่ระบุวันที่ วันที่จะย้อนหลังเป็นเวลา 365 วัน', false);\n      } else {\n        if (this.fromdate == '') {\n          this.fromdate = `${this.todate}`;\n          sessionStorage.setItem('cashDateFrom', this.fromdate);\n        } else {\n          sessionStorage.setItem('cashDateFrom', this.fromdate);\n        }\n        if (this.todate == '') {\n          this.todate = `${this.fromDate}`;\n          sessionStorage.setItem('cashDateTo', this.todate);\n        } else {\n          sessionStorage.setItem('cashDateTo', this.todate);\n        }\n        if (this.Customer === undefined && datatobackid === undefined) {\n          dataCustomer = '%20';\n          this.paCustomer = '%20';\n        } else if (datatobackid !== undefined && this.Customer === undefined) {\n          dataCustomer = datatobackid;\n          this.paCustomer = datatobackname;\n          this.paCustomerid = datatobackid;\n        } else {\n          this.txtcustomer = 'ค้นหาจากลูกค้า';\n          dataCustomer = `${this.Customer.accountnum}`;\n          this.paCustomer = `${this.Customer.name}`;\n          this.paCustomerid = `${this.Customer.accountnum}`;\n        }\n        if (this.CodeInvoice == '') {\n          this.CodeInvoice = '%20';\n        }\n        if (this.CodeSo == '') {\n          if (this.datalogin[0].id_group_user == '153Admin' || this.datalogin[0].id_group_user == '1860Administrator') {\n            datacodeSo = `${this.CodeSo}`;\n            this.paCodeSO = `${this.CodeSo}`;\n          } else {\n            datacodeSo = `${this.datalogin[0].salegroup}`;\n            this.paCodeSO = `${this.datalogin[0].salegroup}`;\n          }\n        }\n        if (this.CodeSo !== '') {\n          if (this.datalogin[0].id_group_user == '153Admin' || this.datalogin[0].id_group_user == '1860Administrator') {\n            datacodeSo = `${this.CodeSo}`;\n            this.paCodeSO = `${this.CodeSo}`;\n          } else {\n            datacodeSo = `${this.datalogin[0].salegroup}`;\n            this.paCodeSO = `${this.datalogin[0].salegroup}`;\n          }\n        }\n        if (datacodeSo == '') {\n          datacodeSo = '%20';\n          this.paCodeSO = '%20';\n        }\n        if (dataCustomer === 'undefined') {\n          dataCustomer = '%20';\n          this.paCustomer = '%20';\n        }\n        //  alert(dataCustomer)\n        this.Datatoback = this.url + 'cash_invoice_search/' + this.fromdate + '/' + this.todate + '/' + datacodeSo + '/' + this.CodeInvoice + '/' + dataCustomer;\n        /*alert(this.url + 'cash_invoice/'+ this.fromdate + '/' + this.todate +'/' + this.CodeSo + '/' + this.CodeInvoice + '/' + this.Customer)*/\n        this.http.get(this.url + 'cash_invoice_search/' + this.fromdate + '/' + this.todate + '/' + datacodeSo + '/' + this.CodeInvoice + '/' + dataCustomer).subscribe(res => {\n          if (res.length > 0) {\n            this.DataSearch = res;\n            this.sumprice = 0.00;\n            this.sumvat = 0.00;\n            this.productprice = 0.00;\n            this.sum();\n            /*this.getdate();*/\n            if (this.datalogin[0].salegroup == 'admin') {\n              /* this.CodeSo='';*/\n            } else {}\n            if (dataCustomer == '%20') {\n              dataCustomer = '';\n            }\n            if (this.CodeInvoice == '%20') {\n              this.CodeInvoice = '';\n            }\n          } else {\n            if (this.datalogin[0].salegroup == 'admin') {\n              /* this.CodeSo='';*/\n            } else {}\n            if (dataCustomer == '%20') {\n              dataCustomer = '';\n            }\n            if (this.CodeInvoice == '%20') {\n              this.CodeInvoice = '';\n            }\n            this.sumprice = 0.00;\n            this.sumvat = 0.00;\n            this.productprice = 0.00;\n            this.openModal(true, 'ไม่พบข้อมูล', false);\n            /*   this.getdate();*/\n          }\n        }, error => {\n          //  this.openModal(true,'เกิดปัญหาในการ Process ข้อมูล กรุณารายการใหม่อีกครั้ง',false);\n          alert('เกิดปัญหาในการ Process ข้อมูล กรุณารายการใหม่อีกครั้ง');\n        });\n      }\n    }\n    sum() {\n      if (this.DataSearch.length > 0) {\n        for (var i = 0; i < this.DataSearch.length; i++) {\n          this.productprice += this.DataSearch[i].Salesbalance;\n          this.sumvat += this.DataSearch[i].Sumtax;\n          this.sumprice += this.DataSearch[i].Invoiceamount;\n        }\n      }\n    }\n    searchINV(datasale, dataCustomer) {\n      this.idinvi = [];\n      this.allid = [];\n      this.listINV = '';\n      this.getdate();\n      if (this.dateINV == '') {\n        this.dateinv = '%20';\n      } else {\n        this.dateinv = this.dateINV;\n      }\n      this.http.get(this.url + 'cash_invoice/' + this.fromdate + '/' + this.todate + '/' + datasale + '/' + dataCustomer + '/' + this.dateinv + '/' + this.DataED).subscribe(res => {\n        if (res.length > 0) {\n          this.dateinv = '';\n          for (var i = 0; i < res.length; i++) {\n            this.idinvi.push({\n              id: res[i].id,\n              salesid: res[i].salesid,\n              invoiceid: res[i].invoiceid,\n              invoicedate: res[i].invoicedate,\n              duedate: res[i].duedate,\n              salesbalance: res[i].salesbalance,\n              sumtax: res[i].sumtax,\n              invoiceamount: res[i].invoiceamount,\n              SOno: res[i].salesid,\n              orderaccount: res[i].orderaccount,\n              payment: res[i].payment,\n              imgurl: res[i].attachedfile,\n              check: false,\n              typeCK: res[i].typeCK\n            });\n          }\n          this.getdate();\n          if (this.datalogin[0].id_group_user == '153Admin' || this.datalogin[0].id_group_user == '1860Administrator') {} else {}\n        } else {\n          this.dateinv = '';\n          if (this.datalogin[0].id_group_user == '153Admin' || this.datalogin[0].id_group_user == '1860Administrator') {} else {}\n          this.openModal(true, 'ไม่พบข้อมูล', false);\n          this.getdate();\n        }\n      }, error => {\n        //this.openModal(true,'เกิดปัญหาในการ Process ข้อมูล',false);\n        alert('เกิดปัญหาในการ Process ข้อมูล');\n      });\n    }\n    selectAll(checked) {\n      this.truesumprice = 0.00;\n      this.truesumvat = 0.00;\n      this.trueproductprice = 0.00;\n      for (var i = 0; i < this.idinvi.length; i++) {\n        if (this.paymenttype == this.idinvi[i].payment) {\n          this.idinvi[i].check = checked;\n        } else if (this.paymenttype === 'All') {\n          this.idinvi[i].check = checked;\n        }\n      }\n      this.GetINV();\n      this.total = this.allid.length;\n      this.sumTrue(this.paymenttype);\n    }\n    checkIfAllSelected(checked, index, payment) {\n      this.viewpaymenttype[index].check = checked;\n      this.total = 0;\n      this.GetINV();\n      this.total = this.allid.length;\n      this.getcaheck();\n      this.sumTrue(payment);\n    }\n    sumTrue(payment) {\n      this.truesumprice = 0.00;\n      this.truesumvat = 0.00;\n      this.trueproductprice = 0.00;\n      for (var i = 0; i < this.viewpaymenttype.length; i++) {\n        if (this.viewpaymenttype[i].check == true) {\n          if (payment == this.viewpaymenttype[i].payment) {\n            this.trueproductprice += this.viewpaymenttype[i].salesbalance;\n            this.truesumvat += this.viewpaymenttype[i].sumtax;\n            this.truesumprice += this.viewpaymenttype[i].invoiceamount;\n          } else if (this.paymenttype === 'All') {\n            this.trueproductprice += this.viewpaymenttype[i].salesbalance;\n            this.truesumvat += this.viewpaymenttype[i].sumtax;\n            this.truesumprice += this.viewpaymenttype[i].invoiceamount;\n          }\n        }\n      }\n    }\n    getcaheck() {\n      var ch = 0;\n      for (var i = 0; i < this.viewpaymenttype.length; i++) {\n        if (this.viewpaymenttype[i].check == true) {\n          ch++;\n        } else {\n          ch--;\n        }\n      }\n      if (this.viewpaymenttype.length == ch) {\n        this.testcheck = true;\n      } else {\n        this.testcheck = false;\n      }\n    }\n    GetINV() {\n      this.allid = [];\n      this.listINV = '';\n      for (var i = 0; i < this.viewpaymenttype.length; i++) {\n        if (this.viewpaymenttype[i].check == true) {\n          this.allid.push({\n            id: this.viewpaymenttype[i].invoiceid,\n            SOno: this.viewpaymenttype[i].SOno,\n            orderaccount: this.viewpaymenttype[i].orderaccount\n            //  Productprice: this.viewpaymenttype[i].salesbalance,\n            //  Sumvat: this.viewpaymenttype[i].sumtax,\n            //  Sumprice: this.viewpaymenttype[i].invoiceamount\n          });\n          this.listINV = this.listINV + ',' + this.viewpaymenttype[i].invoiceid;\n        }\n      }\n    }\n    setinvoiceid(dataCustomer, salegroup) {\n      this.Filelist = undefined;\n      this.chDraft = \"0\";\n      this.searchINV(salegroup, dataCustomer);\n      this.truesumprice = 0.00;\n      this.truesumvat = 0.00;\n      this.trueproductprice = 0.00;\n      this.testcheck = false;\n      this.setInvoice = '';\n      this.selectedFile = '';\n      this.textload = '';\n      this.total = 0;\n      this.imageUrl = 'assets/img/default-image.png';\n      this.setInvoice = dataCustomer;\n      this.paymenttype = 'All';\n      // this.viewpaymenttype=this.idinvi;\n      //  this.numviewpaymenttype=0;\n      this.searchviewPaymenttype();\n      this.Cknumviewpaymenttype(this.paymenttype);\n    }\n    onFileSelect(event) {\n      this.selectedFile = event.target.files[0];\n    }\n    onUpload(ele, total) {\n      var datatypeCK = 0;\n      if (ele == \"1\") {\n        datatypeCK = 1;\n      } else if (ele == \"0\") {\n        datatypeCK = 2;\n      } else {\n        alert(\"เลือกประเภทสถานะก่อนทำรายการ\");\n        return false;\n      }\n      if (total < 1) {\n        alert(\"ทำรายการไม่ถูกต้อง กรุณาทำรายการใหม่\");\n        return false;\n      }\n      this.openModal2(true, 'กำลังบันทึกข้อมูล', false);\n      this.toDateimg = new Date();\n      var dateimg = `${this.toDateimg.getFullYear()}-${this.toDateimg.getMonth() + 1}-${this.toDateimg.getDate()}`;\n      var random = `${this.toDateimg.getMilliseconds()}-${this.toDateimg.getMinutes()}`;\n      const fd = new FormData();\n      var Random = this.getRandomInt(this.max);\n      /* var nameimg = this.allid[0].SOno;*/\n      var imgsetInvoice = `${this.allid[0].orderaccount}-${dateimg}-${random}-${Random}.jpg`;\n      var urlimg = `${this.url}${imgsetInvoice}/inv/upload`;\n      console.log(this.selectedFile);\n      if (this.selectedFile == '') {\n        imgsetInvoice = \"\";\n        this.newsaveinvoice(imgsetInvoice, datatypeCK);\n      } else {\n        fd.append('image', this.selectedFile, this.selectedFile.name);\n        this.openModal2(false, '', false);\n        this.openModal2(true, 'กำลังอัพโหลดรูปภาพ', false);\n        this.http.post(urlimg, fd, {\n          reportProgress: true,\n          observe: 'events'\n        }).subscribe(event => {\n          if (event.type === HttpEventType.UploadProgress) {\n            console.log('Upload' + Math.round(event.loaded / event.total * 100) + '%');\n            this.load = ' สถานะ :  Upload ' + Math.round(event.loaded / event.total * 100) + ' %';\n          } else if (event.type === HttpEventType.Response) {\n            console.log(event);\n            if (event.body.success === true) {\n              this.openModal2(false, '', false);\n              this.openModal2(true, 'กำลังบันทึกข้อมูล : ', false);\n              // this.setInterval=setInterval(() => this.saveinvoice(imgsetInvoice), 400);\n              this.newsaveinvoice(imgsetInvoice, datatypeCK);\n            } else {\n              this.openModal2(false, '', false);\n              this.openModal(true, 'ไม่สามารถบันทึกข้อมูลได้', false);\n            }\n          }\n          /* if (event.success === true) {\n             this.openModal2(false,'',false);\n             this.openModal(true,'บันทึกข้อมูลเสร็จสิ้น',false);\n          }else{\n           this.openModal2(false,'',false);\n           this.openModal(true,'ไม่สามารถบันทึกข้อมูลได้',false);\n          } */\n        });\n      }\n      this.DataSearch = [];\n      this.sumprice = 0.00;\n      this.sumvat = 0.00;\n      this.productprice = 0.00;\n    }\n    newsaveinvoice(imgsetInvoice, typeCK) {\n      //  alert(JSON.stringify(this.allid))\n      this.http.post(this.url + 'update_invoiceNEW', {\n        //   Data: this.allid,\n        Data: this.listINV,\n        nameimg: imgsetInvoice,\n        CK: typeCK\n      }).subscribe(res => {\n        this.openModal2(false, '', false);\n        if (res == true) {\n          this.load = '';\n          this.openModalsuccess(true, 'บันทึกข้อมูลเสร็จสิ้น', false);\n          // this.load ='รายการที่ : '+ this.allid.length.toString() + '-' + res.toString();\n          //  alert('โอเคครับ')\n          if (this.closeED == true) {\n            this.SearchInvoicecashlistEd();\n          } else {\n            this.SearchInvoicecashlist();\n          }\n          console.log(this.allid.length + '--------' + res.toString());\n        }\n      }, error => {\n        //เกิดปัญหาในการ Process ข้อมูล\n        // this.openModalsuccess(true,'เกิดปัญหาในการ Process ข้อมูล',false);\n        // this.openModal(true,'เกิดปัญหาในการ Process ข้อมูล',false);\n        alert('เกิดปัญหาในการ Process ข้อมูล');\n      });\n    }\n    saveinvoice(imgsetInvoice) {\n      if (this.allid.length != 0) {\n        this.http.post(this.url + 'update_invoice/' + this.allid[0].id + '/' + imgsetInvoice, '').subscribe(res => {\n          if (res == true) {\n            this.deletelallid(0);\n            this.load = 'รายการที่ : ' + this.allid.length.toString() + '-' + res.toString();\n            console.log(this.allid.length + '--------' + res.toString());\n          }\n        }, error => {\n          this.openModal2(true, 'เกิดปัญหาในการ Process ข้อมูล', false);\n        });\n      } else {\n        if (this.allid.length == 0) {\n          clearInterval(this.setInterval);\n          this.openModal2(false, '', false);\n          this.load = '';\n          this.openModalsuccess(true, 'บันทึกข้อมูลเสร็จสิ้น', false);\n        }\n      }\n    }\n    openModalsuccess(open, text, load) {\n      this.mdlSampleIsOpensuccess = open;\n      this.altsuccess = text;\n      this.checkreloadsuccess = load;\n    }\n    closemodelsuccess(cl) {\n      this.mdlSampleIsOpensuccess = cl;\n      if (this.checkreloadsuccess == false) {\n        //   this.SearchInvoicecashlist();\n        //alert('OK')\n      }\n    }\n    clear() {\n      this.setInvoice = '';\n      this.selectedFile = '';\n      this.textload = '';\n      this.imageUrl = 'assets/img/default-image.png';\n    }\n    openModal(open, text, load) {\n      this.mdlSampleIsOpen = open;\n      this.alt = text;\n      this.checkreload = load;\n    }\n    closemodel(cl) {\n      this.mdlSampleIsOpen = cl;\n      if (this.checkreload == false) {}\n    }\n    closemodel2(cl) {\n      this.mdlSampleIsOpen = cl;\n      if (this.checkreload == false) {}\n    }\n    openModal2(open, text, load) {\n      this.mdlSampleIsOpen2 = open;\n      this.alt2 = text;\n      this.checkreload2 = load;\n    }\n    getRandomInt(max) {\n      return Math.floor(Math.random() * Math.floor(max));\n    }\n    deletelallid(value) {\n      this.allid.splice(value, 1);\n    }\n    /*openModalIMG(open : boolean,text: string,load:boolean,texturl :string) : void {\n      this.mdlSampleIsOpenIMG = open;\n      this.altimg=text;\n      this.setImgBill(texturl)\n    }*/\n    closemodelIMG(cl) {\n      this.mdlSampleIsOpenIMG = cl;\n      if (this.checkreload == false) {}\n    }\n    openModalIMG(template, texturl, CK) {\n      // this.altimg=text;\n      this.setImgBill(texturl, CK);\n      this.modalRefshow = this.modalService.show(template, {\n        class: 'modal-lg'\n      });\n    }\n    setImgBill(nameimage, CK) {\n      this.ImageBillno = this.urlimg + nameimage;\n    }\n    openpdf(valueid) {\n      //this.router.navigate(['/PDFprint', { queryParams: { idINV: valueid }}]);this.fromdate + '/' + this.todate\n      this.router.navigate(['/PDFprint'], {\n        queryParams: {\n          idINV: valueid,\n          fromdate: this.fromdate,\n          todate: this.todate,\n          CodeSo: this.paCodeSO,\n          Customer: this.paCustomer,\n          paINV: this.CodeInvoice,\n          idcustomer: this.paCustomerid,\n          Datatoback: this.Datatoback\n        }\n      });\n      /* this.router.navigate(['PDFprint']);*/\n    }\n    searchviewPaymenttype() {\n      this.viewpaymenttype = [];\n      this.numviewpaymenttype = false;\n      let Ck = false;\n      this.testcheck = Ck;\n      this.selectAll(this.testcheck);\n      // this.numviewpaymenttype= this.viewpaymenttype.length\n      if (this.paymenttype === 'All') {\n        this.viewpaymenttype = this.idinvi;\n        this.Cknumviewpaymenttype(this.paymenttype);\n        // alert(this.paymenttype);\n      } else {\n        this.viewpaymenttype = this.idinvi.filter(v => v.payment.toLowerCase().indexOf(this.paymenttype.toLowerCase()) > -1);\n        this.numviewpaymenttype = this.viewpaymenttype.length;\n        this.Cknumviewpaymenttype(this.paymenttype);\n        //  alert('2');\n      }\n    }\n    Cknumviewpaymenttype(v) {\n      if (v === 'All') {\n        // this.numviewpaymenttype= this.idinvi.length\n        if (this.viewpaymenttype.length < 1) {\n          // this.numviewpaymenttype=true;\n        } else {\n          this.numviewpaymenttype = false;\n        }\n      } else {\n        // this.numviewpaymenttype= this.viewpaymenttype.length\n        if (this.viewpaymenttype.length < 1) {\n          this.numviewpaymenttype = true;\n        } else {\n          this.numviewpaymenttype = false;\n        }\n      }\n    }\n    cancel() {\n      this.Customer = undefined;\n    }\n    static {\n      this.ɵfac = function InvoicecashlistComponent_Factory(t) {\n        return new (t || InvoicecashlistComponent)(i0.ɵɵdirectiveInject(i1.BsModalService), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.Ng2ImgMaxService), i0.ɵɵdirectiveInject(i4.NgbCalendar), i0.ɵɵdirectiveInject(i5.HttpClient), i0.ɵɵdirectiveInject(i6.WebapiService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: InvoicecashlistComponent,\n        selectors: [[\"app-invoicecashlist\"]],\n        decls: 183,\n        vars: 68,\n        consts: [[\"rt\", \"\"], [\"Image\", \"\", \"idFile\", \"\"], [\"imageForm\", \"ngForm\"], [\"Modalview\", \"\"], [2, \"padding-top\", \"60px\"], [1, \"container-fluid\", \"col-md-12\", \"col-xs-12\", \"col-sm-12\", \"col-12\", 2, \"padding-right\", \"5px\", \"padding-left\", \"5px\"], [1, \"p-sm-1\", \"bg-secondary\", \"text-white\", \"text-center\"], [1, \"form-row\"], [\"class\", \"col-md-2 mb-2\", 4, \"ngIf\"], [1, \"col-md-2\", \"mb-2\", 2, \"position\", \"relative\"], [\"id\", \"typeahead-template\", \"type\", \"text\", \"name\", \"model\", 1, \"form-control\", \"form-control-sm\", 3, \"ngModelChange\", \"placeholder\", \"ngModel\", \"ngbTypeahead\", \"resultTemplate\", \"inputFormatter\"], [2, \"position\", \"absolute\", \"z-index\", \"10\", \"width\", \"15%\", \"height\", \"100%\", \"top\", \"1px\", \"right\", \"1px\", \"font-size\", \"18px\", \"cursor\", \"pointer\", \"text-align\", \"center\", 3, \"click\"], [1, \"col-md-2\", \"mb-2\"], [\"id\", \"inputproduct\", \"type\", \"text\", \"name\", \"CodeInvoice\", \"placeholder\", \"\\u0E40\\u0E25\\u0E02\\u0E17\\u0E35\\u0E48 Invoice\", 1, \"form-control\", \"form-control-sm\", 3, \"ngModelChange\", \"ngModel\"], [1, \"col-xs-12\", \"col-12\", \"col-md-2\", \"form-group\"], [\"type\", \"text\", \"placeholder\", \"DD/MM/YYYY\", \"bsDatepicker\", \"\", 1, \"form-control\", \"form-control-sm\", 3, \"ngModelChange\", \"bsConfig\", \"ngModel\"], [1, \"col-md-2\", \"mb-3\", \"col-12\", \"text-center\", \"text-sm-center\", \"text-md-center\", \"text-lg-left\", 2, \"padding-right\", \"0px\", \"padding-left\", \"0px\"], [\"type\", \"submit\", 1, \"btn\", \"btn-primary\", \"btn-sm\", \"font-weight-light\", 2, \"width\", \"55px\", 3, \"click\"], [\"type\", \"submit\", 1, \"btn\", \"btn-primary\", \"btn-sm\", \"font-weight-light\", 2, \"margin-left\", \"3px\", \"width\", \"68px\", 3, \"click\"], [1, \"table\", \"table-hover\", \"table-bordered\", \"table-sm\"], [1, \"text-sm-center\", \"bg-light\"], [\"scope\", \"col\", 1, \"text-center\", \"font-weight-normal\"], [\"scope\", \"col\", \"width\", \"60px\", 1, \"text-sm-center\"], [\"class\", \"text-sm-left\", 4, \"ngFor\", \"ngForOf\"], [1, \"text-sm-left\"], [1, \"text-sm-center\", \"font-weight-light\"], [1, \"text-sm-right\", \"font-weight-normal\"], [1, \"text-sm-right\", \"font-weight-light\"], [\"id\", \"ModalupFile\", \"tabindex\", \"-1\", \"role\", \"dialog\", \"aria-labelledby\", \"exampleModalLabel\", \"aria-hidden\", \"true\", 1, \"modal\", \"fade\"], [\"role\", \"document\", 1, \"modal-dialog\", \"modal-lg\"], [1, \"modal-content\"], [1, \"modal-header\"], [\"id\", \"exampleModalLabel\", 1, \"modal-title\", 2, \"color\", \"red\"], [\"type\", \"button\", \"data-dismiss\", \"modal\", \"aria-label\", \"Close\", 1, \"close\", 3, \"click\"], [\"aria-hidden\", \"true\"], [1, \"modal-body\", 2, \"padding\", \"3px\"], [1, \"form-group\", \"col-md-3\", \"col-sm-6\", \"col-xs-6\"], [1, \"custom-select\", \"custom-select-sm\", 3, \"ngModelChange\", \"change\", \"ngModel\"], [\"value\", \"All\"], [\"value\", \"N01\"], [\"value\", \"N07\"], [\"value\", \"TT\"], [\"value\", \"COD\"], [2, \"overflow-y\", \"auto\"], [\"scope\", \"col\", 1, \"text-sm-center\", \"font-weight-normal\", 2, \"text-align\", \"center\"], [\"nowrap\", \"\", \"scope\", \"col\", 1, \"text-sm-center\", \"font-weight-normal\", 2, \"text-align\", \"center\"], [\"scope\", \"col\", 1, \"text-sm-center\", \"font-weight-normal\"], [\"scope\", \"col\", \"width\", \"30px\", 1, \"text-sm-center\"], [\"type\", \"checkbox\", 3, \"ngModelChange\", \"click\", \"ngModel\"], [\"class\", \"text-sm-center font-weight-normal\", \"scope\", \"col\", 4, \"ngIf\"], [4, \"ngFor\", \"ngForOf\"], [4, \"ngIf\"], [\"colspan\", \"6\", 1, \"text-sm-right\", \"text-md-right\", \"text-right\", \"font-weight-normal\", 2, \"text-align\", \"right\"], [1, \"text-sm-right\", \"font-weight-normal\", 2, \"text-align\", \"right\"], [1, \"text-sm-center\", \"font-weight-normal\"], [\"class\", \"text-sm-center font-weight-normal\", 4, \"ngIf\"], [1, \"form-group\", \"col-sm-6\", \"offset-sm-3\"], [1, \"custom-file\", 2, \"margin-bottom\", \"10px\"], [\"type\", \"file\", \"accept\", \"image/*\", \"id\", \"inputGroupFile01\", \"aria-describedby\", \"inputGroupFileAddon01\", 1, \"custom-file-input\", 3, \"ngModelChange\", \"change\", \"ngModel\"], [\"for\", \"inputGroupFile01\", 1, \"custom-file-label\", 3, \"ngModelChange\", \"ngModel\"], [\"class\", \"form-group col-sm-6 offset-sm-3\", 4, \"ngIf\"], [\"id\", \"collapseExample\", 1, \"collapse\"], [1, \"card\", \"card-body\"], [2, \"text-align\", \"center\"], [2, \"width\", \"70%\", 3, \"src\"], [1, \"modal-footer\", 2, \"padding\", \"3px\"], [\"role\", \"alert\", 1, \"alert\", \"alert-success\", 2, \"width\", \"200px\", \"top\", \"7px\"], [\"type\", \"button\", \"data-dismiss\", \"modal\", 1, \"btn\", \"btn-secondary\", 3, \"click\"], [\"data-toggle\", \"collapse\", \"href\", \"#collapseExample\", \"role\", \"button\", \"aria-expanded\", \"false\", \"aria-controls\", \"collapseExample\", 1, \"btn\", \"btn-primary\", 3, \"disabled\"], [\"type\", \"button\", \"data-dismiss\", \"modal\", 1, \"btn\", \"btn-primary\", 3, \"click\", \"disabled\"], [\"id\", \"mdlSample\", \"role\", \"dialog\", 1, \"modal\", \"fade\", 3, \"ngStyle\"], [1, \"modal-dialog\", \"modal-md\"], [1, \"modal-header\", \"colhaederal\"], [1, \"modal-title\"], [1, \"modal-body\"], [\"align\", \"right\", 1, \"modal-footer\"], [\"type\", \"button\", \"id\", \"btnClose\", 1, \"btn\", \"btn-danger\", 3, \"click\"], [1, \"fa\", \"fa-times\", \"fa-fw\"], [\"type\", \"button\", \"id\", \"btnClose\", \"disabled\", \"\", 1, \"btn\", \"btn-danger\", 3, \"click\"], [\"multiple\", \"\", 1, \"custom-select\", \"custom-select-sm\", 3, \"ngModelChange\", \"ngModel\"], [\"selected\", \"\", \"disabled\", \"\", \"value\", \"\"], [\"value\", \"1\", 3, \"selected\"], [3, \"selected\", \"value\", 4, \"ngFor\", \"ngForOf\"], [3, \"selected\", \"value\"], [3, \"click\"], [3, \"mousedown\"], [1, \"text-center\", \"font-weight-normal\", 3, \"ngStyle\"], [1, \"text-left\", \"font-weight-normal\", 3, \"ngStyle\"], [1, \"text-right\", \"font-weight-normal\", 3, \"ngStyle\"], [1, \"text-center\"], [\"data-toggle\", \"modal\", \"data-target\", \"#ModalupFile\", \"aria-expanded\", \"true\", \"aria-controls\", \"collapseOne\", 1, \"btn\", \"btn\", \"btn-warning\", 2, \"padding\", \"0pt\", 3, \"click\"], [1, \"text-sm-center\", \"font-weight-normal\", 2, \"text-align\", \"center\", 3, \"ngStyle\"], [\"nowrap\", \"\", 1, \"text-sm-center\", \"font-weight-normal\", 2, \"text-align\", \"center\", 3, \"ngStyle\"], [1, \"text-sm-right\", \"font-weight-normal\", 2, \"text-align\", \"right\", 3, \"ngStyle\"], [\"type\", \"checkbox\", 3, \"click\", \"checked\"], [\"class\", \"btn btn btn-warning\", \"style\", \"padding: 0px;\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"btn btn btn-warning\", \"style\", \"padding: 0px;\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [\"class\", \"text-center\", 4, \"ngIf\"], [1, \"btn\", \"btn\", \"btn-warning\", 2, \"padding\", \"0px\", 3, \"click\"], [1, \"btn\", \"btn\", \"btn-warning\", 2, \"padding\", \"0px\", 3, \"click\", \"disabled\"], [\"data-dismiss\", \"modal\", 1, \"btn\", \"btn\", \"btn-warning\", 2, \"padding\", \"0px\", 3, \"click\"], [\"colspan\", \"11\", 1, \"text-center\", \"font-weight-normal\", 2, \"text-align\", \"center\"], [1, \"form-group\", \"col-sm-12\"], [1, \"custom-select\", \"custom-select-sm\", 3, \"ngModelChange\", \"ngModel\"], [\"value\", \"A\"], [\"value\", \"0\"], [\"value\", \"1\"], [1, \"modal-body\", 2, \"padding\", \"5px\"], [1, \"card\", \"card-body\", 2, \"padding\", \"5px\"], [\"ngbTabTitle\", \"\", \"target\", \"_blank\"], [\"ngbTabContent\", \"\"], [\"align\", \"right\", 1, \"modal-footer\", 2, \"padding\", \"5px\"], [2, \"margin\", \"5px\", \"width\", \"99%\", \"height\", \"99%\", 3, \"src\"]],\n        template: function InvoicecashlistComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            const _r1 = i0.ɵɵgetCurrentView();\n            i0.ɵɵelement(0, \"app-topmenu\");\n            i0.ɵɵelementStart(1, \"section\", 4)(2, \"div\", 5)(3, \"h5\", 6);\n            i0.ɵɵtext(4, \"Cash Invoice List\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(5, \"div\", 7);\n            i0.ɵɵtemplate(6, InvoicecashlistComponent_div_6_Template, 7, 3, \"div\", 8);\n            i0.ɵɵelementStart(7, \"div\", 9);\n            i0.ɵɵtemplate(8, InvoicecashlistComponent_ng_template_8_Template, 3, 2, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n            i0.ɵɵelementStart(10, \"input\", 10);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function InvoicecashlistComponent_Template_input_ngModelChange_10_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.Customer, $event) || (ctx.Customer = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(11, \"div\", 11);\n            i0.ɵɵlistener(\"click\", function InvoicecashlistComponent_Template_div_click_11_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.cancel());\n            });\n            i0.ɵɵtext(12, \"x\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(13, \"div\", 12)(14, \"input\", 13);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function InvoicecashlistComponent_Template_input_ngModelChange_14_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.CodeInvoice, $event) || (ctx.CodeInvoice = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(15, \"div\", 14)(16, \"input\", 15);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function InvoicecashlistComponent_Template_input_ngModelChange_16_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.Datafromdate, $event) || (ctx.Datafromdate = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(17, \"div\", 14)(18, \"input\", 15);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function InvoicecashlistComponent_Template_input_ngModelChange_18_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.Datatodate, $event) || (ctx.Datatodate = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(19, \"div\", 16)(20, \"button\", 17);\n            i0.ɵɵlistener(\"click\", function InvoicecashlistComponent_Template_button_click_20_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.SearchInvoicecashlist());\n            });\n            i0.ɵɵtext(21, \"Search\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(22, \"button\", 18);\n            i0.ɵɵlistener(\"click\", function InvoicecashlistComponent_Template_button_click_22_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.SearchInvoicecashlistEd());\n            });\n            i0.ɵɵtext(23, \"Edit INV\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(24, \"table\", 19)(25, \"thead\")(26, \"tr\", 20)(27, \"th\", 21);\n            i0.ɵɵtext(28, \"Item\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(29, \"th\", 21);\n            i0.ɵɵtext(30, \"\\u0E1E\\u0E19\\u0E31\\u0E01\\u0E07\\u0E32\\u0E19\\u0E02\\u0E32\\u0E22\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(31, \"th\", 21);\n            i0.ɵɵtext(32, \"\\u0E23\\u0E2B\\u0E31\\u0E2A\\u0E25\\u0E39\\u0E01\\u0E04\\u0E49\\u0E32\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(33, \"th\", 21);\n            i0.ɵɵtext(34, \"\\u0E0A\\u0E37\\u0E48\\u0E2D\\u0E25\\u0E39\\u0E01\\u0E04\\u0E49\\u0E32\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(35, \"th\", 21);\n            i0.ɵɵtext(36, \"\\u0E21\\u0E39\\u0E25\\u0E04\\u0E48\\u0E32\\u0E2A\\u0E34\\u0E19\\u0E04\\u0E49\\u0E32\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(37, \"th\", 21);\n            i0.ɵɵtext(38, \"VAT\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(39, \"th\", 21);\n            i0.ɵɵtext(40, \"\\u0E21\\u0E39\\u0E25\\u0E04\\u0E48\\u0E32\\u0E23\\u0E27\\u0E21\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(41, \"th\", 22);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(42, \"tbody\");\n            i0.ɵɵtemplate(43, InvoicecashlistComponent_tr_43_Template, 21, 38, \"tr\", 23);\n            i0.ɵɵelementStart(44, \"tr\", 24);\n            i0.ɵɵelement(45, \"td\", 25)(46, \"td\", 25)(47, \"td\", 25);\n            i0.ɵɵelementStart(48, \"td\", 26);\n            i0.ɵɵtext(49, \"\\u0E21\\u0E39\\u0E25\\u0E04\\u0E48\\u0E32\\u0E23\\u0E27\\u0E21\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(50, \"td\", 26);\n            i0.ɵɵtext(51);\n            i0.ɵɵpipe(52, \"number\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(53, \"td\", 26);\n            i0.ɵɵtext(54);\n            i0.ɵɵpipe(55, \"number\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(56, \"td\", 26);\n            i0.ɵɵtext(57);\n            i0.ɵɵpipe(58, \"number\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(59, \"th\", 27);\n            i0.ɵɵelementEnd()()()()();\n            i0.ɵɵelementStart(60, \"div\", 28)(61, \"div\", 29)(62, \"div\", 30)(63, \"div\", 31)(64, \"h5\", 32);\n            i0.ɵɵtext(65);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(66, \"button\", 33);\n            i0.ɵɵlistener(\"click\", function InvoicecashlistComponent_Template_button_click_66_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.clear());\n            });\n            i0.ɵɵelementStart(67, \"span\", 34);\n            i0.ɵɵtext(68, \"\\u00D7\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(69, \"div\", 35)(70, \"div\", 36)(71, \"select\", 37);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function InvoicecashlistComponent_Template_select_ngModelChange_71_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.paymenttype, $event) || (ctx.paymenttype = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵlistener(\"change\", function InvoicecashlistComponent_Template_select_change_71_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.searchviewPaymenttype());\n            });\n            i0.ɵɵelementStart(72, \"option\", 38);\n            i0.ɵɵtext(73, \"\\u0E17\\u0E38\\u0E01\\u0E23\\u0E32\\u0E22\\u0E01\\u0E32\\u0E23\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(74, \"option\", 39);\n            i0.ɵɵtext(75, \"N01\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(76, \"option\", 40);\n            i0.ɵɵtext(77, \"N07\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(78, \"option\", 41);\n            i0.ɵɵtext(79, \"TT\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(80, \"option\", 42);\n            i0.ɵɵtext(81, \"COD\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(82, \"div\", 43)(83, \"table\", 19)(84, \"thead\")(85, \"tr\", 20);\n            i0.ɵɵelement(86, \"th\", 44);\n            i0.ɵɵelementStart(87, \"th\", 45);\n            i0.ɵɵtext(88, \"SO No \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(89, \"th\", 45);\n            i0.ɵɵtext(90, \"Invoice No \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(91, \"th\", 45);\n            i0.ɵɵtext(92, \"Invoice Date \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(93, \"th\", 45);\n            i0.ɵɵtext(94, \"Due Date \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(95, \"th\", 46);\n            i0.ɵɵelementStart(96, \"th\", 44);\n            i0.ɵɵtext(97, \"\\u0E21\\u0E39\\u0E25\\u0E04\\u0E48\\u0E32\\u0E2A\\u0E34\\u0E19\\u0E04\\u0E49\\u0E32\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(98, \"th\", 44);\n            i0.ɵɵtext(99, \"VAT\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(100, \"th\", 44);\n            i0.ɵɵtext(101, \"\\u0E21\\u0E39\\u0E25\\u0E04\\u0E48\\u0E32\\u0E23\\u0E27\\u0E21\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(102, \"th\", 47)(103, \"input\", 48);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function InvoicecashlistComponent_Template_input_ngModelChange_103_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.testcheck, $event) || (ctx.testcheck = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵlistener(\"click\", function InvoicecashlistComponent_Template_input_click_103_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.selectAll($event.target.checked));\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelement(104, \"th\", 46);\n            i0.ɵɵtemplate(105, InvoicecashlistComponent_th_105_Template, 1, 0, \"th\", 49);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(106, \"tbody\");\n            i0.ɵɵtemplate(107, InvoicecashlistComponent_tr_107_Template, 30, 56, \"tr\", 50)(108, InvoicecashlistComponent_tr_108_Template, 3, 0, \"tr\", 51);\n            i0.ɵɵelementStart(109, \"td\", 52);\n            i0.ɵɵtext(110, \"\\u0E21\\u0E39\\u0E25\\u0E04\\u0E48\\u0E32\\u0E23\\u0E27\\u0E21\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(111, \"td\", 53);\n            i0.ɵɵtext(112);\n            i0.ɵɵpipe(113, \"number\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(114, \"th\", 53);\n            i0.ɵɵtext(115);\n            i0.ɵɵpipe(116, \"number\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(117, \"th\", 53);\n            i0.ɵɵtext(118);\n            i0.ɵɵpipe(119, \"number\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(120, \"td\", 54)(121, \"td\", 54);\n            i0.ɵɵtemplate(122, InvoicecashlistComponent_td_122_Template, 1, 0, \"td\", 55);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(123, \"div\", 56)(124, \"div\", 57)(125, \"input\", 58, 1);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function InvoicecashlistComponent_Template_input_ngModelChange_125_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.Filelist, $event) || (ctx.Filelist = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵlistener(\"change\", function InvoicecashlistComponent_Template_input_change_125_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.handleFileInput($event.target.files));\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(128, \"label\", 59);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function InvoicecashlistComponent_Template_label_ngModelChange_128_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.textload, $event) || (ctx.textload = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵtext(129);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵtemplate(130, InvoicecashlistComponent_div_130_Template, 9, 1, \"div\", 60);\n            i0.ɵɵelementStart(131, \"div\", 61)(132, \"div\", 62)(133, \"form\", 63, 2);\n            i0.ɵɵelement(135, \"img\", 64);\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(136, \"div\", 65)(137, \"div\", 66);\n            i0.ɵɵtext(138);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(139, \"button\", 67);\n            i0.ɵɵlistener(\"click\", function InvoicecashlistComponent_Template_button_click_139_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.clear());\n            });\n            i0.ɵɵtext(140, \"Close\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(141, \"button\", 68);\n            i0.ɵɵtext(142, \" ViewImage \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(143, \"button\", 69);\n            i0.ɵɵlistener(\"click\", function InvoicecashlistComponent_Template_button_click_143_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.onUpload(ctx.chDraft, ctx.total));\n            });\n            i0.ɵɵtext(144, \"Upload\");\n            i0.ɵɵelementEnd()()()()();\n            i0.ɵɵelementStart(145, \"div\", 70)(146, \"div\", 71)(147, \"div\", 30)(148, \"div\", 72)(149, \"h4\", 73);\n            i0.ɵɵtext(150, \"Report\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(151, \"div\", 74);\n            i0.ɵɵtext(152);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(153, \"div\", 75)(154, \"button\", 76);\n            i0.ɵɵlistener(\"click\", function InvoicecashlistComponent_Template_button_click_154_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.closemodel(false));\n            });\n            i0.ɵɵelement(155, \"i\", 77);\n            i0.ɵɵtext(156, \" \\u0E1B\\u0E34\\u0E14\");\n            i0.ɵɵelementEnd()()()()();\n            i0.ɵɵelementStart(157, \"div\", 70)(158, \"div\", 71)(159, \"div\", 30)(160, \"div\", 72)(161, \"h4\", 73);\n            i0.ɵɵtext(162, \"Report\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(163, \"div\", 74);\n            i0.ɵɵtext(164);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(165, \"div\", 75)(166, \"button\", 78);\n            i0.ɵɵlistener(\"click\", function InvoicecashlistComponent_Template_button_click_166_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.closemodel2(false));\n            });\n            i0.ɵɵelement(167, \"i\", 77);\n            i0.ɵɵtext(168, \" \\u0E1B\\u0E34\\u0E14\");\n            i0.ɵɵelementEnd()()()()();\n            i0.ɵɵelementStart(169, \"div\", 70)(170, \"div\", 71)(171, \"div\", 30)(172, \"div\", 72)(173, \"h4\", 73);\n            i0.ɵɵtext(174, \"Report\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(175, \"div\", 74);\n            i0.ɵɵtext(176);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(177, \"div\", 75)(178, \"button\", 76);\n            i0.ɵɵlistener(\"click\", function InvoicecashlistComponent_Template_button_click_178_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.closemodelsuccess(false));\n            });\n            i0.ɵɵelement(179, \"i\", 77);\n            i0.ɵɵtext(180, \" \\u0E1B\\u0E34\\u0E14\");\n            i0.ɵɵelementEnd()()()()();\n            i0.ɵɵtemplate(181, InvoicecashlistComponent_ng_template_181_Template, 10, 1, \"ng-template\", null, 3, i0.ɵɵtemplateRefExtractor);\n          }\n          if (rf & 2) {\n            const rt_r19 = i0.ɵɵreference(9);\n            i0.ɵɵadvance(6);\n            i0.ɵɵproperty(\"ngIf\", ctx.testclose);\n            i0.ɵɵadvance(4);\n            i0.ɵɵpropertyInterpolate(\"placeholder\", ctx.txtcustomer);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.Customer);\n            i0.ɵɵproperty(\"ngbTypeahead\", ctx.search)(\"resultTemplate\", rt_r19)(\"inputFormatter\", ctx.formatter);\n            i0.ɵɵadvance(4);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.CodeInvoice);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"bsConfig\", i0.ɵɵpureFunction0(60, _c0));\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.Datafromdate);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"bsConfig\", i0.ɵɵpureFunction0(61, _c0));\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.Datatodate);\n            i0.ɵɵadvance(25);\n            i0.ɵɵproperty(\"ngForOf\", ctx.DataSearch);\n            i0.ɵɵadvance(8);\n            i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(52, 42, ctx.productprice, \"1.2-2\"));\n            i0.ɵɵadvance(3);\n            i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(55, 45, ctx.sumvat, \"1.2-2\"));\n            i0.ɵɵadvance(3);\n            i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(58, 48, ctx.sumprice, \"1.2-2\"));\n            i0.ɵɵadvance(8);\n            i0.ɵɵtextInterpolate1(\"Upload File invoice : \", ctx.setInvoice, \" \");\n            i0.ɵɵadvance(6);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.paymenttype);\n            i0.ɵɵadvance(32);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.testcheck);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngIf\", !ctx.closeED);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngForOf\", ctx.viewpaymenttype);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.numviewpaymenttype);\n            i0.ɵɵadvance(4);\n            i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(113, 51, ctx.trueproductprice, \"1.2-2\"));\n            i0.ɵɵadvance(3);\n            i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind2(116, 54, ctx.truesumvat, \"1.2-2\"), \" \");\n            i0.ɵɵadvance(3);\n            i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind2(119, 57, ctx.truesumprice, \"1.2-2\"), \" \");\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"ngIf\", !ctx.closeED);\n            i0.ɵɵadvance(3);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.Filelist);\n            i0.ɵɵadvance(3);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.textload);\n            i0.ɵɵadvance();\n            i0.ɵɵtextInterpolate1(\"\", ctx.textload, \" \");\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.total != 0);\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"src\", ctx.imageUrl, i0.ɵɵsanitizeUrl);\n            i0.ɵɵadvance(3);\n            i0.ɵɵtextInterpolate1(\" \\u0E23\\u0E32\\u0E22\\u0E17\\u0E35\\u0E48\\u0E40\\u0E25\\u0E37\\u0E2D\\u0E01\\u0E17\\u0E31\\u0E49\\u0E07\\u0E2B\\u0E21\\u0E14 : \", ctx.total, \" \");\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"disabled\", ctx.selectedFile == \"\");\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"disabled\", ctx.total == 0 || ctx.chDraft == \"A\" || ctx.Filelist == undefined && ctx.chDraft == \"0\" && ctx.selectedFile == \"\");\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(62, _c1, ctx.mdlSampleIsOpen ? \"block\" : \"none\"));\n            i0.ɵɵadvance(7);\n            i0.ɵɵtextInterpolate2(\"\", ctx.alt, \" \", ctx.load, \"\");\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(64, _c1, ctx.mdlSampleIsOpen2 ? \"block\" : \"none\"));\n            i0.ɵɵadvance(7);\n            i0.ɵɵtextInterpolate2(\"\", ctx.alt2, \" \", ctx.load, \" \");\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(66, _c1, ctx.mdlSampleIsOpensuccess ? \"block\" : \"none\"));\n            i0.ɵɵadvance(7);\n            i0.ɵɵtextInterpolate2(\"\", ctx.altsuccess, \" \", ctx.load, \" \");\n          }\n        },\n        dependencies: [i7.NgForOf, i7.NgIf, i7.NgStyle, i8.ɵNgNoValidate, i8.NgSelectOption, i8.ɵNgSelectMultipleOption, i8.DefaultValueAccessor, i8.CheckboxControlValueAccessor, i8.SelectControlValueAccessor, i8.SelectMultipleControlValueAccessor, i8.NgControlStatus, i8.NgControlStatusGroup, i8.NgModel, i8.NgForm, i4.NgbTypeahead, i9.BsDatepickerDirective, i9.BsDatepickerInputDirective, i10.TopmenuComponent, i7.DecimalPipe, i7.DatePipe],\n        styles: [\".autocomplete[_ngcontent-%COMP%]{position:relative;display:inline-block}.autocomplete-items[_ngcontent-%COMP%]{position:absolute;border:1px solid #d4d4d4;border-bottom:none;border-top:none;z-index:99;top:100%;left:0;right:0}.autocomplete-items[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]{padding:10px;cursor:pointer;background-color:#fff;border-bottom:1px solid #d4d4d4}.autocomplete-items[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]:hover{background-color:#e9e9e9}.autocomplete-active[_ngcontent-%COMP%]{background-color:#1e90ff!important;color:#fff}.custom-day[_ngcontent-%COMP%]{text-align:center;padding:.185rem .25rem;display:inline-block;height:2rem;width:2rem}.custom-day.focused[_ngcontent-%COMP%]{background-color:#e6e6e6}.custom-day.range[_ngcontent-%COMP%], .custom-day[_ngcontent-%COMP%]:hover{background-color:#0275d8;color:#fff}.custom-day.faded[_ngcontent-%COMP%]{background-color:#0275d880}\"]\n      });\n    }\n  }\n  return InvoicecashlistComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}