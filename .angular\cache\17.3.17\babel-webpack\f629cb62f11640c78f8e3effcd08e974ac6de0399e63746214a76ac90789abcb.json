{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.every = void 0;\nvar lift_1 = require(\"../util/lift\");\nvar OperatorSubscriber_1 = require(\"./OperatorSubscriber\");\nfunction every(predicate, thisArg) {\n  return lift_1.operate(function (source, subscriber) {\n    var index = 0;\n    source.subscribe(OperatorSubscriber_1.createOperatorSubscriber(subscriber, function (value) {\n      if (!predicate.call(thisArg, value, index++, source)) {\n        subscriber.next(false);\n        subscriber.complete();\n      }\n    }, function () {\n      subscriber.next(true);\n      subscriber.complete();\n    }));\n  });\n}\nexports.every = every;\n//# sourceMappingURL=every.js.map", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}