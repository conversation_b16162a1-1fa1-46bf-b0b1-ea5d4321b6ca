{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.audit = void 0;\nvar lift_1 = require(\"../util/lift\");\nvar innerFrom_1 = require(\"../observable/innerFrom\");\nvar OperatorSubscriber_1 = require(\"./OperatorSubscriber\");\nfunction audit(durationSelector) {\n  return lift_1.operate(function (source, subscriber) {\n    var hasValue = false;\n    var lastValue = null;\n    var durationSubscriber = null;\n    var isComplete = false;\n    var endDuration = function () {\n      durationSubscriber === null || durationSubscriber === void 0 ? void 0 : durationSubscriber.unsubscribe();\n      durationSubscriber = null;\n      if (hasValue) {\n        hasValue = false;\n        var value = lastValue;\n        lastValue = null;\n        subscriber.next(value);\n      }\n      isComplete && subscriber.complete();\n    };\n    var cleanupDuration = function () {\n      durationSubscriber = null;\n      isComplete && subscriber.complete();\n    };\n    source.subscribe(OperatorSubscriber_1.createOperatorSubscriber(subscriber, function (value) {\n      hasValue = true;\n      lastValue = value;\n      if (!durationSubscriber) {\n        innerFrom_1.innerFrom(durationSelector(value)).subscribe(durationSubscriber = OperatorSubscriber_1.createOperatorSubscriber(subscriber, endDuration, cleanupDuration));\n      }\n    }, function () {\n      isComplete = true;\n      (!hasValue || !durationSubscriber || durationSubscriber.closed) && subscriber.complete();\n    }));\n  });\n}\nexports.audit = audit;\n//# sourceMappingURL=audit.js.map", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}