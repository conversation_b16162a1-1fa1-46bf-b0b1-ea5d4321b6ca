{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.throttleTime = void 0;\nvar async_1 = require(\"../scheduler/async\");\nvar throttle_1 = require(\"./throttle\");\nvar timer_1 = require(\"../observable/timer\");\nfunction throttleTime(duration, scheduler, config) {\n  if (scheduler === void 0) {\n    scheduler = async_1.asyncScheduler;\n  }\n  var duration$ = timer_1.timer(duration, scheduler);\n  return throttle_1.throttle(function () {\n    return duration$;\n  }, config);\n}\nexports.throttleTime = throttleTime;\n//# sourceMappingURL=throttleTime.js.map", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}