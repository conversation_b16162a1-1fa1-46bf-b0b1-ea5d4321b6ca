{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../topmenu/topmenu.component\";\nexport let HomepageComponent = /*#__PURE__*/(() => {\n  class HomepageComponent {\n    constructor() {}\n    ngOnInit() {}\n    static {\n      this.ɵfac = function HomepageComponent_Factory(t) {\n        return new (t || HomepageComponent)();\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: HomepageComponent,\n        selectors: [[\"app-homepage\"]],\n        decls: 4,\n        vars: 0,\n        consts: [[1, \"container-fluid\"]],\n        template: function HomepageComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelement(0, \"app-topmenu\");\n            i0.ɵɵelementStart(1, \"main\")(2, \"div\", 0);\n            i0.ɵɵelement(3, \"router-outlet\");\n            i0.ɵɵelementEnd()();\n          }\n        },\n        dependencies: [i1.RouterOutlet, i2.TopmenuComponent]\n      });\n    }\n  }\n  return HomepageComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}