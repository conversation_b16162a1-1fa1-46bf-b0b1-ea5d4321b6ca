{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Maltese (Malta) [mt]\n//! author : <PERSON> : https://github.com/alesma\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  var mt = moment.defineLocale('mt', {\n    months: 'Jannar_Frar_Marzu_April_Mejju_Ġunju_Lulju_Awwissu_Settembru_Ottubru_Novembru_Diċembru'.split('_'),\n    monthsShort: 'Jan_Fra_Mar_Apr_Mej_Ġun_Lul_Aww_Set_Ott_Nov_Diċ'.split('_'),\n    weekdays: 'Il-Ħadd_It-Tnejn_It-Tlieta_L-Erbgħa_Il-Ħamis_Il-Ġimgħa_Is-Sibt'.split('_'),\n    weekdaysShort: 'Ħad_Tne_Tli_Erb_Ħam_Ġim_Sib'.split('_'),\n    weekdaysMin: 'Ħa_Tn_Tl_Er_Ħa_Ġi_Si'.split('_'),\n    longDateFormat: {\n      LT: 'HH:mm',\n      LTS: 'HH:mm:ss',\n      L: 'DD/MM/YYYY',\n      LL: 'D MMMM YYYY',\n      LLL: 'D MMMM YYYY HH:mm',\n      LLLL: 'dddd, D MMMM YYYY HH:mm'\n    },\n    calendar: {\n      sameDay: '[Illum fil-]LT',\n      nextDay: '[Għada fil-]LT',\n      nextWeek: 'dddd [fil-]LT',\n      lastDay: '[Il-bieraħ fil-]LT',\n      lastWeek: 'dddd [li għadda] [fil-]LT',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: 'f’ %s',\n      past: '%s ilu',\n      s: 'ftit sekondi',\n      ss: '%d sekondi',\n      m: 'minuta',\n      mm: '%d minuti',\n      h: 'siegħa',\n      hh: '%d siegħat',\n      d: 'ġurnata',\n      dd: '%d ġranet',\n      M: 'xahar',\n      MM: '%d xhur',\n      y: 'sena',\n      yy: '%d sni'\n    },\n    dayOfMonthOrdinalParse: /\\d{1,2}º/,\n    ordinal: '%dº',\n    week: {\n      dow: 1,\n      // Monday is the first day of the week.\n      doy: 4 // The week that contains Jan 4th is the first week of the year.\n    }\n  });\n  return mt;\n});", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}