{"name": "soweb", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "test": "ng test", "lint": "ng lint", "e2e": "ng e2e"}, "private": true, "dependencies": {"@angular/animations": "^6.1.0", "@angular/cdk": "6.4.7", "@angular/common": "^6.0.3", "@angular/compiler": "^6.0.3", "@angular/core": "^6.0.3", "@angular/forms": "^6.0.3", "@angular/http": "^6.0.3", "@angular/material": "6.4.7", "@angular/platform-browser": "^6.0.3", "@angular/platform-browser-dynamic": "^6.0.3", "@angular/router": "^6.0.3", "@ng-bootstrap/ng-bootstrap": "^2.2.0", "angular": "^1.7.3", "angular-6-datatable": "^0.8.0", "angular-text-input-autocomplete": "^0.3.0", "angular5-csv": "^0.2.10", "blueimp-canvas-to-blob": "^3.14.0", "bootstrap": "^4.4.1", "chart.js": "^2.7.2", "classlist.js": "^1.1.20150312", "core-js": "^2.5.4", "cors": "^2.8.4", "datatables.net": "^1.10.19", "datatables.net-bs4": "^1.10.19", "excel": "^1.0.1", "express": "^4.16.3", "file-saver": "^1.3.8", "hammerjs": "^2.0.8", "html2canvas": "^1.0.0-rc.5", "joi": "^13.4.0", "jspdf": "^1.5.3", "keyboardevent-key-polyfill": "^1.1.0", "mssql": "^4.1.0", "ng-auto-complete": "^2.10.5", "ng-src": "^1.0.1", "ng2-img-max": "^2.1.18", "ng2-select": "^2.0.0", "ngx-bootstrap": "^5.6.2", "ngx-cookie-service": "^1.0.10", "ngx-pagination": "^3.2.0", "node": "^10.6.0", "npm": "^6.2.0", "pipe": "^1.1.0", "react-tag-autocomplete": "^5.5.1", "require": "^2.4.20", "rxjs": "^6.0.0", "ts-xlsx": "0.0.11", "web-animations-js": "^2.3.1", "zone.js": "^0.8.26"}, "devDependencies": {"@angular-devkit/build-angular": "~0.6.8", "@angular/cli": "~6.0.8", "@angular/compiler-cli": "^6.0.9", "@angular/language-service": "^6.0.9", "@types/chart.js": "^2.7.28", "@types/core-js": "^2.5.3", "@types/jasmine": "~2.8.6", "@types/jasminewd2": "~2.0.3", "@types/jquery": "^3.3.6", "@types/mssql": "^4.0.8", "@types/node": "^8.9.5", "codelyzer": "~4.2.1", "jasmine-core": "~2.99.1", "jasmine-spec-reporter": "~4.2.1", "jquery": "^3.3.1", "karma": "~1.7.1", "karma-chrome-launcher": "~2.2.0", "karma-coverage-istanbul-reporter": "~2.0.0", "karma-jasmine": "~1.1.1", "karma-jasmine-html-reporter": "^0.2.2", "protractor": "~5.3.0", "ts-node": "~5.0.1", "tslint": "~5.9.1", "typescript": "^2.7.2"}}