import { Component } from '@angular/core';
import { Router } from '@angular/router';
import { HttpClient } from '@angular/common/http';
import { WebapiService } from './webapi.service';
import { element } from 'protractor';
import { setTheme } from 'ngx-bootstrap/utils';
import * as $ from 'jquery';
import 'datatables.net';
import 'datatables.net-bs4';
@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.css'],
  
})
export class AppComponent {
  mdlSampleIsOpen : boolean = false;
  alt='';
  checkreload=true;
  mProducts: any[] = [];
  title = 'app';
  mdataarray: any[];
  constructor(private http: HttpClient, private service: WebapiService) {
    this.service.getUrlGroup();
    this.service.geturlservice();
    this.service.getUrluser();
    setTheme('bs4');
  }
  Loaduser() {
    this.service.feedData().then(result => {
      this.mProducts = result;
    });
  }

  openModal(open : boolean,text: string,load:boolean) : void {
    this.mdlSampleIsOpen = open;
    this.alt=text;
    this.checkreload=load;
}
 closemodel(cl: boolean) {
  this.mdlSampleIsOpen=cl;
  if(this.checkreload==true) {
    location.reload();
  }
 
}
}
