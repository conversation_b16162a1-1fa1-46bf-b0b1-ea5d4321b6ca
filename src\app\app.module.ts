import {TooltipModule} from 'ngx-bootstrap/tooltip';

import { TextInputAutocompleteModule } from 'angular-text-input-autocomplete';
import { polyfill as keyboardEventKeyPolyfill } from 'keyboardevent-key-polyfill';
import {platformBrowserDynamic} from '@angular/platform-browser-dynamic';
import { BrowserModule } from '@angular/platform-browser';
import { NgModule } from '@angular/core';
import { HttpClientModule } from '@angular/common/http';
import { AppComponent } from './app.component';
import { ProductgroupComponent } from './productgroup/productgroup.component';
import { ProductlistComponent } from './productlist/productlist.component';
import { SoComponent } from './so/so.component';
import { SohistoryComponent } from './sohistory/sohistory.component';
import { Solist1Component } from './solist1/solist1.component';
import { SorecordComponent } from './sorecord/sorecord.component';
import { SoreviewComponent } from './soreview/soreview.component';
import { SumbyproductComponent } from './sumbyproduct/sumbyproduct.component';
import { TrendproductComponent } from './trendproduct/trendproduct.component';
import { UsersComponent } from './users/users.component';
import { TopmenuComponent } from './topmenu/topmenu.component';
import { PricemasterComponent } from './pricemaster/pricemaster.component';
import { PermissionComponent } from './permission/permission.component';
import { MasterpackageComponent } from './masterpackage/masterpackage.component';
import { LoginComponent } from './login/login.component';
import { InvoicecreditlistComponent } from './invoicecreditlist/invoicecreditlist.component';
import { InvoicecashlistComponent } from './invoicecashlist/invoicecashlist.component';
import { CustomerlistComponent } from './customerlist/customerlist.component';
import { CoverpageComponent } from './coverpage/coverpage.component';
import { CompleteinvoiceComponent } from './completeinvoice/completeinvoice.component';
import { Routes, RouterModule } from '@angular/router';
import { HomepageComponent } from './homepage/homepage.component';
import { FormsModule, ReactiveFormsModule } from '../../node_modules/@angular/forms';
import { AuthGuard } from './auth.guard';
import { WebapiService } from './webapi.service';
import {NgbModule} from '@ng-bootstrap/ng-bootstrap';
import {DataTableModule} from "angular-6-datatable";
import {BrowserAnimationsModule} from '@angular/platform-browser/animations';
import {NoopAnimationsModule} from '@angular/platform-browser/animations';
import {enableProdMode} from '@angular/core';
import { ViewimagesComponent } from './viewimages/viewimages.component';
import {SelectModule} from 'ng2-select';
import { CookieService } from 'ngx-cookie-service';
import { Ng2ImgMaxModule } from 'ng2-img-max';
import { ReversepipePipe } from './reversepipe.pipe';
import {NgxPaginationModule} from 'ngx-pagination'; // <-- import the module
import { BsDatepickerModule } from 'ngx-bootstrap/datepicker';
import { EditsaloderreviewComponent } from './editsaloderreview/editsaloderreview.component';
import { PDFprintComponent } from './pdfprint/pdfprint.component';
import { PrintsaleoderlistComponent } from './printsaleoderlist/printsaleoderlist.component';
import { HashLocationStrategy, LocationStrategy } from '@angular/common'
import { ModalModule } from 'ngx-bootstrap/modal';
import { TabsModule } from 'ngx-bootstrap/tabs';
import { CollapseModule } from 'ngx-bootstrap/collapse';
import { CompleteINVComponent } from './complete-inv/complete-inv.component';
import { SalestatusComponent } from './salestatus/salestatus.component';
import { SalestockComponent } from './salestock/salestock.component';
enableProdMode();
keyboardEventKeyPolyfill();

// Routes
export const router: Routes = [
 /* { path: '' , redirectTo:'/login', pathMatch:'full' },*/
    { path: '' , component: LoginComponent },
    { path: 'permission' , component: PermissionComponent },
    { path: 'sorecord', component: SorecordComponent},
    { path: 'login' , component: LoginComponent },
    { path: 'so', component: SoComponent },
    { path: 'sohistory', component: SohistoryComponent },
    { path: 'solist1', component: Solist1Component },   
    { path: 'soreview', component: SoreviewComponent},
   


    { path: 'invoicecreditlist' , component: InvoicecreditlistComponent },
    { path: 'invoicecashlist' , component: InvoicecashlistComponent },


    { path: 'customerlist' , component: CustomerlistComponent },
      
    {path: 'PDFprint' , component: PDFprintComponent},
    {path: 'printsaleoderlist' , component:PrintsaleoderlistComponent},



    { path: 'coverpage' , component: CoverpageComponent },  
    { path: 'sumbyproduct' , component: SumbyproductComponent},
    { path: 'trendproduct' , component: TrendproductComponent},

    { path: 'masterpackage' , component: MasterpackageComponent },
    { path: 'productgroup', component: ProductgroupComponent  },
    { path: 'productlist', component: ProductlistComponent },
    {path: 'editsaloderreview' , component: EditsaloderreviewComponent},
    { path: 'pricemaster' , component: PricemasterComponent},
  
    { path: 'viewimages' , component: ViewimagesComponent},
    { path: 'topmenu' , component: TopmenuComponent},
  /*  { path: 'completeinvoice' , component: CompleteINVComponent},*/
    { path: 'completeinvoice' , component: CompleteinvoiceComponent}, 
    { path: 'users' , component: UsersComponent},
    {path:'salestatus', component:SalestatusComponent},
    {path:'salestock',component:SalestockComponent}
    
    

];
@NgModule({
  declarations: [
    AppComponent,
    PricemasterComponent,
    LoginComponent,
    CompleteinvoiceComponent,
    CustomerlistComponent,
    CoverpageComponent,
    InvoicecreditlistComponent,
    InvoicecashlistComponent,
    MasterpackageComponent,
    PermissionComponent,
    ProductgroupComponent,
    ProductlistComponent,
    SoComponent,
    SohistoryComponent,
    Solist1Component,
    SorecordComponent,
    SoreviewComponent,
    SumbyproductComponent,
    TrendproductComponent,
    UsersComponent,
    TopmenuComponent,
    HomepageComponent,
    ViewimagesComponent,
    ReversepipePipe,
    EditsaloderreviewComponent,
    PDFprintComponent,
    PrintsaleoderlistComponent,
    CompleteINVComponent,
    SalestatusComponent,
    SalestockComponent
    

  ],
  imports: [
    TooltipModule.forRoot(),
    BrowserAnimationsModule,
    SelectModule,
    BrowserModule,
    FormsModule,
    ReactiveFormsModule,
    NgbModule.forRoot(),
    RouterModule.forRoot(router),
    HttpClientModule,
    DataTableModule,
    TextInputAutocompleteModule,
    NoopAnimationsModule,
    Ng2ImgMaxModule,
    NgxPaginationModule,
    BsDatepickerModule.forRoot(),
    ModalModule.forRoot(),
    TabsModule.forRoot(),
    CollapseModule.forRoot()
    
  ],
  /** */
  providers: [WebapiService, CookieService, { provide : LocationStrategy, useClass : HashLocationStrategy } ],
  bootstrap: [AppComponent],
 
})

export class AppModule {
}

