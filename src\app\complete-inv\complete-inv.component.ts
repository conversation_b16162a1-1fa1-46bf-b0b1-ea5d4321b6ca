import { HttpClient, HttpEventType } from '@angular/common/http';
import { ChangeDetectorRef, Component, OnInit, TemplateRef } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { Router } from '@angular/router';
import { Ngb<PERSON>alendar, NgbDateStruct } from '@ng-bootstrap/ng-bootstrap';
import { Angular5Csv } from 'angular5-csv/dist/Angular5-csv';
import { Ng2ImgMaxService } from 'ng2-img-max';
import { BsModalService } from 'ngx-bootstrap/modal';
import { BsModalRef } from 'ngx-bootstrap/modal/bs-modal-ref.service';
import { WebapiService } from '../webapi.service';


export interface paymenttype{
  idpayment : string;
  paymentName : string;
}
export interface stApprove{
  idApprove : string;
  ApproveName : string;
}
export interface Group {
  value: string;
  viewValue: string;
}
export interface BillId_approve{
  id: string;
  billno : string;
}



export interface invoiceByBill{
  noid:number;
  idbill: string;
  salesid: string;
  invoiceid : string;
  invoicename : string;
  orderaccount : string;
  invoicedate : string;
  duedate : string;
  salesbalance : number;
  sumtax : number;
  invoiceamount: number;
  billno : string;
  approve : number;
  payment : string;
  urlimg : string;
  delivery:string;
  check: boolean;
  nameimg : string;
  saleman : string;
  remark : string;
  remarkst:Number;
  numrun: string;
  imgbookbank :string;
  numrunbank :string;
  checkbank :string;
}

export interface groupIMG{
  id:number;
  nameimg : string;
}
@Component({
  selector: 'app-complete-inv',
  templateUrl: './complete-inv.component.html',
  styleUrls: ['./complete-inv.component.css']
})
export class CompleteINVComponent implements OnInit {

  getColor(country,stRemark,CK) {
    var stcolor
    if(country==='อนุมัติแล้ว'&& stRemark==0 && CK==='อนุมัติแล้ว'){
      stcolor='อนุมัติแล้ว'
    }else if(country==='รออนุมัติ'&& stRemark==0 && CK==='รออนุมัติ'){
      stcolor='รออนุมัติใหม่'
    }else if(country==='รออนุมัติ'&& stRemark==1 && CK==='รอbookbank'){
      stcolor='รออนุมัติ'
    }else if(country==='รออนุมัติ'&& stRemark==0 && CK==='รอbookbank'){
      stcolor='รออนุมัติ'
    }
    else{
      stcolor='ผิดปกติ'
    }
    switch (stcolor) {
      case 'อนุมัติแล้ว':
        return 'green';
      case 'รออนุมัติ':
        return 'red';
      case 'ผิดปกติ':
        return '#ff9900';
        case 'รออนุมัติใหม่':
        return '#0e0dde';
    }
  }

  getColor2(country) {
    if(country==1){
      country=false
    }else{
      country=true
    }
    switch (country) {
      case true:
        return 'green';
      case false:
        return '#ff9900';
    }
  }

  getColorbookbank(country,bookbank){
    if(country==1 && bookbank!='A'){
      country='1'
    }else if (country==1 && bookbank==='A'){
      country='1'
    }
    else if (country==0 && bookbank!=='A'){
      country='2'
    }
    else if (country==0 && bookbank==='A'){
      country='3'
    }
    else{
      country='3'
    }
    switch (country) {
      case '1':
        return '#ff9900';
      case '2':
        return '#0e0dde';
        case '3':
        return 'green';
    }
  }


  myForm:FormGroup;
  disabled = false;
  ShowFilter = false;
  limitSelection = false;
  dropdownSettings: any = {};

  options = {
    fieldSeparator: ',',
    quoteStrings: '"',
    decimalseparator: '.',
    showLabels: true,
    showTitle: true,
    useBom: true,
    noDownload: false,
    headers: ["SALESGROUP","INVOICE NO","Customer NO","Customer Name","INVOICEDATE","SaleOrder","เงือนไข","DUEDATE","เงินก่อนภาษี","VAT","รวมทั้งสิ้น","สถานะ Approve","Remark"]
  };

  Bill_approve:BillId_approve[]=[];
  BillId: invoiceByBill[]=[];
  groupimg: groupIMG[]=[];

  selectedValue:string;
  mdlSampleIsOpen : boolean = false;
  alt='';
  checkreload=true;

  mdlSampleIsOpenApprove : boolean = false;
  altApprove='';
  checkreloadApprove=true;



  mdlSampleIsOpenBill : boolean = false;
  altBill='';
  checkreloadBill=true;

  mdlSampleIsOpen2 : boolean = false;
  alt2='';
  checkreload2=true;


  mdlSampleIsOpen3 : boolean = false;
  alt3='';
  checkreload3=true;

  mdlSampleIsOpensuccess : boolean = false;
  altsuccess='';
  checkreloadsuccess=true;

  customer='';
  fromDate: NgbDateStruct;
  toDate: NgbDateStruct;
  url: string;
  urlimg: string;
  Paymenttype:paymenttype[]=[];
  StApprove:stApprove[]=[];

  selectPayment='';
  selectApprove='';

  dataComplete:any;

  paymenttype: any[]=[{idpayment: '0', value: 'เงินสด'}, {idpayment: '1', value: 'เครดิส'}];
  Modelpaymenttype: any = {id: '' , value: ''};


  stApprove: any[] =[{idApprove: '1', value: 'Approval แล้ว'}, {idApprove: '0', value: 'ยังไม่ได้ Approve'}];
  ModelStApprove: any = {id: '' , value: ''};

  ImageIN='../assets/img/default-image.png';
  ImageBill='default-image.png';
  ImageBookbank='';
  ImageBillno='';
  test2 :string ="BI18-02557-2018-8-14-400.jpg";
  imgtest :string="../../assets/imageBill/";
  showtest = this.imgtest + this.test2;
  showbillno:string;
  billnoDataSearch:any;

  setInterval: any;

  testcheck: boolean;

	keyboardStrApprove : string = '';
  keyboardStrpaymenttype : string = '';

  total:Number =0 ;
  load='';


  productprice=0;
  sumvat=0;
  sumprice=0;

  trueproductprice=0;
  truesumvat=0;
  truesumprice=0;
  dateshipping='';
  dateshippingto='';

  datalogin: any;
  groupsale:string;
  datasalegroup:string;

  testclose=false;
  checkApprove : boolean;
  checkApproveview : boolean;
  DateGroupsaleman :any;

  clients: any[];
  dataTable: any[]=[];

  mdlSampleIsOpenimg :boolean = false;
  altimg ='';
  checkreloadimg=true;

  billno:string;
  nameinv:string;
  dateinvid:string;

  productpricesuccess:number;
  sumvatsuccess :number;
  sumpricesuccess :number;
  p: number;
  H: number;

  SalesId:string;
  SONo:string;
  InvoiceNo:string;
  OrderAccount:string;
  Invoicingname:string;
  InvoiceDate:string;
  DueDate:string;
  Deliveryname:string;
  productsun:string;
  VAT:string;
  suntotal:string;
  Remark :string;
  remarkshow:string;

  imggroup:string;

  dataExport:string;
  DataExport:any;
  calendarimg :string;
 fromdate='';
  todate='';

  bsValueform :Date;
  bsValueto :Date;
 Datatodate:any;
 Datafromdate:any;
 SearchCompleteED_ :boolean = false;
 SearchComplete_ :boolean = false;

 SearchRevertINVname:string;
 SearchRevertBillno:string;
 permisstiondata:any[]=[];
exportbtn=true;
searchbtn=true;



modalRef: BsModalRef;
modalRefviewDetail: BsModalRef;
ModalRefApprove : BsModalRef;
ModalremarkRef : BsModalRef;
modalRefshow:BsModalRef;

config = {
  ignoreBackdropClick: true,
  class : 'modal-md'
};
configview = {
  ignoreBackdropClick: true,
  class : 'modal-lg '
};
showIDso;
textload="";
selectedFile =null;
namebookbank='';

btnPDF=false;
btnREpdf=false;

Cktype;
CkNull;
nameUrl:string;


isCollapsed = true;
imageUrl: string = "assets/img/default-image.png";
urlimgBookbank=''
CK :boolean =false;
staticAlertClosed=false;
urlimgDefault='';
printst='3';
headerlist=[];
seachheaderlist=[];
viewBookbank='A';
Bookbank=[];
INVSuccess=[]
CkimgBookbank;
Btnimg=false;
  constructor(private ng2ImgMax: Ng2ImgMaxService, private modalService: BsModalService, private fb: FormBuilder, private chRef: ChangeDetectorRef, private http: HttpClient, private service: WebapiService, private calendar: NgbCalendar,private router: Router) {
    this.url=service.geturlservice();
    localStorage.removeItem('DataSOderreview');
    localStorage.removeItem('DataSOderlist');
    this.urlimg = service.geturlserviceIMG();
    this.urlimgDefault=service.geturlimgDefault();
    this.urlimgBookbank= service.geturlserviceIMGbookbank();
    this.fromDate = calendar.getPrev(calendar.getToday(), 'd', 365);
    this.toDate = calendar.getToday();
    this.datalogin=JSON.parse(sessionStorage.getItem('login'))
    if (this.datalogin==null){
      this.router.navigate(['login']);
       }
    this.getuser();
    this.selectPayment ='';
    this.selectApprove = '';
    this.dateinvid='';
    this.dataExport='';
    this.calendarimg="../../assets/img/if_Paul-27_2534341.ico";
  //  this.groupsale=this.datalogin;
    this.getgroupsaleman();
    this.Datatodate= new Date(this.toDate.year,this.toDate.month-1,this.toDate.day);
    this.Datafromdate= new Date(this.fromDate.year, this.fromDate.month-1, this.fromDate.day);
    this.permisstiondata=JSON.parse(sessionStorage.getItem('menu'))
    this.exportbtn=!this.permisstiondata[9].flag_print;
    this.searchbtn=!this.permisstiondata[9].flag_action;

   }

  ngOnInit() {
  }

  searchviewBookbank(){

    let Ck = false
    this.testcheck = Ck
    this.selectAll(this.testcheck);
    if(this.viewBookbank==='3'){
      this.Bookbank=this.BillId;
    } else  {
      if(this.viewBookbank==='A'){
       // alert('ทำงาน')
   this.Bookbank=this.BillId.filter(v => v.checkbank.toLowerCase().indexOf(this.viewBookbank.toLowerCase()) > -1).slice(0, 50);
      }else{
        this.Bookbank=this.BillId.filter(v => v.checkbank.toLowerCase().indexOf(this.viewBookbank.toLowerCase()) > -1).slice(0, 50);
     }


    }


  }

  searchSuccess(data){

    if(this.dateinvid===''){
     // alert(this.dateinvid);
      this.INVSuccess=this.BillId
    }else{
    this.INVSuccess=this.BillId.filter(v => v.invoiceid.toLowerCase().indexOf(this.dateinvid.toLowerCase()) > -1).slice(0, 50);
  }
  }

  searchselectprint(){

    if(this.printst==='3'){
      this.seachheaderlist=this.dataComplete;

      this.productprice=0;
      this.sumvat=0;
      this.sumprice=0;
      if(this.seachheaderlist.length > 0) {
        for(var i=0;i< this.seachheaderlist.length;i++){
          this.productprice+= this.seachheaderlist[i].Salesbalance;
          this.sumvat+= this.seachheaderlist[i].Sumtax;
          this.sumprice+= this.seachheaderlist[i].Invoiceamount;
        }
      }

    } else  {
      //alert(JSON.stringify(this.headerlist[0]));
      this.seachheaderlist=this.dataComplete.filter(v => v.CK.toLowerCase().indexOf(this.printst.toLowerCase()) > -1).slice(0, 50);

      this.productprice=0;
      this.sumvat=0;
      this.sumprice=0;
      if(this.seachheaderlist.length > 0) {
        for(var i=0;i< this.seachheaderlist.length;i++){
          this.productprice+= this.seachheaderlist[i].Salesbalance;
          this.sumvat+= this.seachheaderlist[i].Sumtax;
          this.sumprice+= this.seachheaderlist[i].Invoiceamount;
        }
      }

    }
  }


  //[{"id_group_user":"153Admin","id_user":"701MaxZa001","name_user":"MaxISR","email":"<EMAIL>","mobile":"0964454540","login_user":"admin","salegroup":"admin","flag_close":0}]
  getuser(){
    if(this.datalogin[0].id_group_user=='153Admin'){
      this.datasalegroup='';
      this.testclose=true;
    }else{
      this.testclose=false;
     this.datasalegroup=this.datalogin[0].salegroup;
    }
  }

  getgroupsaleman(){
    this.DateGroupsaleman=[];
    this.http.get<any>(this.url +'salesman' ).subscribe(res => {
      if(res.length > 0){
     this.DateGroupsaleman=res;
          }else{
          alert('ไม่สามารถติดต่อข้อมูลรหัส saleman ได้')
          }
      });
  }

  onItemSelect(item: any) {
    console.log('onItemSelect', item);
  }
  onSelectAll(items: any) {
    console.log('onSelectAll', items);
  }
  toogleShowFilter() {
    this.ShowFilter = !this.ShowFilter;
    this.dropdownSettings = Object.assign({}, this.dropdownSettings, { allowSearchFilter: this.ShowFilter });
  }

  handleLimitSelection() {
    if (this.limitSelection) {
        this.dropdownSettings = Object.assign({}, this.dropdownSettings, { limitSelection: 2 });
    } else {
        this.dropdownSettings = Object.assign({}, this.dropdownSettings, { limitSelection: null });
    }
  }

  getdate(){
    this.fromdate = `${this.Datafromdate.getFullYear()}-${this.Datafromdate.getMonth()+1}-${this.Datafromdate.getDate()}`;
    this.todate = `${this.Datatodate.getFullYear()}-${this.Datatodate.getMonth()+1}-${this.Datatodate.getDate()}`;
   }

  getpaymenttype(){
    this.http.get<any>(this.url + 'paymenttype').subscribe(res => {
      alert(JSON.stringify(res))
      if(res.length > 0){
        for(var i=0; i< res.length; i++) {
          this.Paymenttype.push({
            idpayment: res[i].idpayment,
            paymentName: res[i].paymenttype,
          });}
      }else{

      }
          });

  }

  SearchCompleteED(){
    this.dataComplete=[];
    this.seachheaderlist=[];
    this.printst="อนุมัติแล้ว";
    this.productprice=0;
    this.sumvat=0;
    this.sumprice=0;
    this.SearchCompleteED_ =true;
    this.SearchComplete_ =false;
    this.load ='';
    var datacodeSo='';
    this.getdate();
   if(this.keyboardStrApprove=='' || this.keyboardStrApprove=='0'){
    this.openModal(true,'กรุณาเลือกเลือกสถาะนะ เป็นapproveแล้ว',false);
   // alert('เกิดปัญหาในการ Process ข้อมูล กรุณารายการใหม่อีกครั้ง')
   }else{
       if(this.datasalegroup=='' && this.keyboardStrpaymenttype=='' ) {
      this.openModal(true,'กรุณาเลือก Sale หรือ เงินสด/เครดิต ถ้าไม่ระบุวันที่ วันที่จะย้อนหลังเป็นเวลา 365 วัน',false);
      this.productprice=0;
      this.sumvat=0;
      this.sumprice=0;
     } else{
      if(this.todate==''){
        this.todate = `${this.toDate}`;
      }
      if(this.fromdate=='') {
       this.fromdate = `${this.fromDate}`;
     }

     if(this.keyboardStrpaymenttype==''){
      this.keyboardStrpaymenttype='%20';
    }


    if(this.datasalegroup==''){
      if(this.datalogin[0].salegroup =='admin'){
        datacodeSo =`${this.datasalegroup}`;
      }else{
        datacodeSo = `${this.datalogin[0].salegroup}`;
      }
    }

    if(this.datasalegroup !==''){
      if(this.datalogin[0].salegroup =='admin'){
        datacodeSo =`${this.datasalegroup}`;
      }else{
        datacodeSo = `${this.datalogin[0].salegroup}`;
      }
    }

    if(datacodeSo==''){
      datacodeSo='%20';
    }

    this.dataExport='';

    var data = `${this.fromdate}/${this.todate}/${datacodeSo}/${this.keyboardStrpaymenttype}`;
    this.dataComplete=[];
    this.http.get<any>(this.url +'completed_invoice_searchED/'+data).subscribe(res => {
      if(res.length > 0){
        this.dataComplete=res;
        this.seachheaderlist=res;
        if(this.keyboardStrApprove=='%20'){
          this.keyboardStrApprove='';
        }
        if(this.keyboardStrpaymenttype=='%20'){
          this.keyboardStrpaymenttype='';
        }
        this.sum();
       /* if(this.datalogin[0].salegroup =='admin'){
          this.datasalegroup='';
        }*/
      }else{
       /* if(this.datalogin[0].salegroup =='admin'){
          this.datasalegroup='';
        }*/
        if(this.keyboardStrApprove=='%20'){
          this.keyboardStrApprove='';
        }
        if(this.keyboardStrpaymenttype=='%20'){
          this.keyboardStrpaymenttype='';
        }
        this.openModal(true,'ไม่พบข้อมูล',false);
        this.dataComplete=[];
        this.seachheaderlist=[];
        this.productprice=0;
        this.sumvat=0;
        this.sumprice=0;
      }
      },error=>{
        status=error.status;
        //this.openModal(true,'เกิดปัญหาในการ Process ข้อมูล กรุณารายการใหม่อีกครั้ง',false);
        alert('เกิดปัญหาในการ Process ข้อมูล กรุณารายการใหม่อีกครั้ง')
      });
    }
   }

  }

  SearchComplete(){
    this.printst="3";
    this.SearchCompleteED_ =false;
    this.SearchComplete_ =true;
    this.load ='';
    var datacodeSo='';
    this.dataComplete=[];
    this.productprice=0;
    this.sumvat=0;
    this.sumprice=0;
    this.getdate();

   if(this.datasalegroup=='' && this.keyboardStrpaymenttype=='' && this.keyboardStrApprove=='' ) {
    this.dataComplete=[];
    this.seachheaderlist=[];
      this.openModal(true,'กรุณาป้อนข้อมูลอย่างน้อย 1 ช่อง ถ้าไม่ระบุวันที่ วันที่จะย้อนหลังเป็นเวลา 365 วัน',false);
      this.productprice=0;
      this.sumvat=0;
      this.sumprice=0;
     } else{
      if(this.todate==''){
        this.todate = `${this.toDate}`;
      }
      if(this.fromdate=='') {
       this.fromdate = `${this.fromDate}`;
     }

     if(this.keyboardStrpaymenttype==''){
      this.keyboardStrpaymenttype='%20';
    }
    if(this.keyboardStrApprove==''){
      this.keyboardStrApprove='%20'
    }
    if(this.datasalegroup==''){
      if(this.datalogin[0].salegroup =='admin'){
        datacodeSo =`${this.datasalegroup}`;
      }else{
        datacodeSo = `${this.datalogin[0].salegroup}`;
      }
    }

    if(this.datasalegroup !==''){
      if(this.datalogin[0].salegroup =='admin'){
        datacodeSo =`${this.datasalegroup}`;
      }else{
        datacodeSo = `${this.datalogin[0].salegroup}`;
      }
    }
    //alert(this.datasalegroup)
    if(datacodeSo==''){
      datacodeSo='%20';
    }

    this.dataExport='';

    var data = `${this.fromdate}/${this.todate}/${datacodeSo}/${this.keyboardStrApprove}/${this.keyboardStrpaymenttype}`;
    //alert(data)
    this.dataExport= data;
    this.dataComplete=[];
    this.seachheaderlist=[];
    this.http.get<any>(this.url +'completed_invoice_search/'+data).subscribe(res => {
      if(res.length > 0){
        this.dataComplete=res;
        this.seachheaderlist=res;
        if(this.keyboardStrApprove=='%20'){
          this.keyboardStrApprove='';
        }
        if(this.keyboardStrpaymenttype=='%20'){
          this.keyboardStrpaymenttype='';
        }
        this.sum();
       /* if(this.datalogin[0].salegroup =='admin'){
          this.datasalegroup='';
        }*/
      }else{
       /* if(this.datalogin[0].salegroup =='admin'){
          this.datasalegroup='';
        }*/
        if(this.keyboardStrApprove=='%20'){
          this.keyboardStrApprove='';
        }
        if(this.keyboardStrpaymenttype=='%20'){
          this.keyboardStrpaymenttype='';
        }
        this.openModal(true,'ไม่พบข้อมูล',false);
        this.dataComplete=[];
        this.seachheaderlist=[];
        this.productprice=0;
        this.sumvat=0;
        this.sumprice=0;
      }
      },error=>{
        status=error.status;
        alert('เกิดปัญหาในการ Process ข้อมูล กรุณารายการใหม่อีกครั้ง')
        //this.openModal(true,'เกิดปัญหาในการ Process ข้อมูล กรุณารายการใหม่อีกครั้ง',false);
      });
    }

  }

  GetExport(){
    this.http.get<any>(this.url +'completed_invoice_export/'+this.dataExport).subscribe(res => {
      if(res.length > 0){
        this.DataExport=res;
        if(this.DataExport==undefined) {
          this.openModal(true,'ไม่พบข้อมูล',false);
          this.dataExport='';
        }else {
       new Angular5Csv(this.DataExport,'Completed Invoice', this.options);
         /*  alert(JSON.stringify(this.DataExport));*/
         this.dataExport='';
        }
      }else{

      }
      },error=>{
        status=error.status;
        this.openModal(true,'เกิดปัญหาในการ Process ข้อมูล กรุณาทำรายการใหม่อีกครั้ง',false);
      });
  }

  exportdataexcel() {
    this.DataExport=[];
    this.GetExport();
    if(this.DataExport==undefined) {
      this.openModal(true,'ไม่พบข้อมูล',false);
    }else {
      new Angular5Csv(this.DataExport,'Completed Invoice', this.options);
    }

  }
  sum(){
    this.productprice=0;
    this.sumvat=0;
    this.sumprice=0;
    if(this.dataComplete.length > 0) {
      for(var i=0;i< this.dataComplete.length;i++){
        this.productprice+= this.dataComplete[i].Salesbalance;
        this.sumvat+= this.dataComplete[i].Sumtax;
        this.sumprice+= this.dataComplete[i].Invoiceamount;
      }
    }
  }


  sumTrue(){
    this.truesumprice=0.00;
    this.truesumvat=0.00;
    this.trueproductprice=0.00;
    for (var i=0; i< this.BillId.length;i++){
      if(this.BillId[i].check==true){
          this.trueproductprice += this.BillId[i].salesbalance;
          this.truesumvat += this.BillId[i].sumtax;
          this.truesumprice += this.BillId[i].invoiceamount;
      }

        }
  }

  selectPaymenttype(idpayment){
    this.selectPayment =idpayment;

  }
  selectStApprove(idApprove){
    this.selectApprove=idApprove;

  }

  OpenModalRevert(Invoicingname,Billno,ModalApprove){
    this.getIdBillRevert(Invoicingname,Billno);
    this.ModalRefApprove = this.modalService.show(ModalApprove,
      {class: 'modal-lg', ignoreBackdropClick: true}
     );
  }

  getIdBillRevert(Invoicingname,billid){
    this.BillId=[];
    this.total=0;
    this.Remark='';
    this.testcheck=false;
    this.truesumprice=0.00;
    this.truesumvat=0.00;
    this.trueproductprice=0.00;
    this.SearchRevertBillno='';
    this.SearchRevertINVname='';

    this.SearchRevertINVname =Invoicingname;
    var databillid ='';
    this.getdate();
    if(billid==''){
      databillid='%20'

    }else{
      databillid=billid;
      this.SearchRevertBillno=billid;
    }
    this.http.get<any>(this.url +'get_bill_ID_revert/'+databillid +'/'+ Invoicingname + '/' + this.fromdate+'/'+ this.todate ).subscribe(res => {
       this.billnoDataSearch=res;
      if(res.length > 0){
        /*var test1 = res[0].attachedfile.splie();*/
        for(var i=0; i< res.length; i++) {
          var alllength=JSON.stringify(res[i].attachedfile).length;
          var St1 =JSON.stringify(res[i].attachedfile).substring(2,4);
          var St2=JSON.stringify(res[i].attachedfile).substring(alllength-8,alllength-5);
          var St3=JSON.stringify(res[i].attachedfile).substring(alllength-19,alllength-15);
          var sumst =  `${St1}-${St2}${St3}`;
          for(var v =0; v< res.length; v++){
            var alllengthbook=JSON.stringify(res[i].attachedfileBookbank).length;
            var St1book =JSON.stringify(res[i].attachedfileBookbank).substring(2,4);
            var St2book=JSON.stringify(res[i].attachedfileBookbank).substring(alllengthbook-8,alllengthbook-5);
            var St3book=JSON.stringify(res[i].attachedfileBookbank).substring(alllengthbook-19,alllengthbook-15);
            var sumstbook =  `${St1book}-${St2book}${St3book}`;
          }
          var checkbank;
          if(res[i].attachedfileBookbank==='A'){
            checkbank = res[i].attachedfileBookbank
          }else{
            checkbank='B'
          }

         this.BillId.push({
           noid : i,
           idbill: res[i].id,
           salesid: res[i].salesid,
           invoiceid :  res[i].invoiceid,
           invoicename : res[i].invoicingname,
           orderaccount : res[i].orderaccount,
           invoicedate :  res[i].invoicedate,
           duedate :  res[i].duedate,
           salesbalance :  res[i].salesbalance,
           sumtax :  res[i].sumtax,
           invoiceamount:  res[i].invoiceamount,
           billno : res[i].billno,
           approve : res[i].approve,
           payment : res[i].payment,
           urlimg : res[i].attachedfile,
           delivery : res[i].deliveryname,
           check: false,
           nameimg : res[i].attachedfile,
           saleman :res[i].salegroup,
           remark : res[i].remark,
           remarkst :res[i].remarkstatus,
           numrun: sumst,
           imgbookbank : res[i].attachedfileBookbank,
           numrunbank:sumstbook,
           checkbank : checkbank
         });}

         this.showbill();

    }else{
      this.showbillno = 'ไม่มีรายการ'
     }

      },error=>{
        status=error.status;
        //this.openModal(true,'เกิดปัญหาในการ Process ข้อมูล กรุณารายการใหม่อีกครั้ง',false);
        alert('เกิดปัญหาในการ Process ข้อมูล กรุณารายการใหม่อีกครั้ง')
      });
  }

  getIdBillwaitModal(Invoicingname,Billno,Modalwait){
    this.ModalRefApprove = this.modalService.show(Modalwait,
      {class: 'modal-lg', ignoreBackdropClick: true}
     );
    this.getIdBillwait(Invoicingname,Billno)
    }


  getIdBillwait(Invoicingname,billid){
    this.BillId=[];
    this.total=0;
    this.SearchRevertBillno='';
    this.SearchRevertINVname='';
    this.Remark='';
    this.testcheck=false;
    this.SearchRevertINVname =Invoicingname;
    this.truesumprice=0.00;
    this.truesumvat=0.00;
    this.trueproductprice=0.00;
    var databillid ='';
    this.getdate();
    if(billid==''){
      databillid='%20'
      this.SearchRevertBillno='%20'
    }else{
      databillid=billid;
      this.SearchRevertBillno=billid;
    }
    this.http.get<any>(this.url +'get_bill_ID/'+databillid +'/'+ Invoicingname + '/' + this.fromdate+'/'+ this.todate ).subscribe(res => {
       this.billnoDataSearch=res;
      if(res.length > 0){
        /*var test1 = res[0].attachedfile.splie();*/
        for(var i=0; i< res.length; i++) {
          var alllength=JSON.stringify(res[i].attachedfile).length;
          var St1 =JSON.stringify(res[i].attachedfile).substring(2,4);
          var St2=JSON.stringify(res[i].attachedfile).substring(alllength-8,alllength-5);
          var St3=JSON.stringify(res[i].attachedfile).substring(alllength-19,alllength-15);
          var sumst =  `${St1}-${St2}${St3}`;
          for(var v =0; v< res.length; v++){
            var alllengthbook=JSON.stringify(res[i].attachedfileBookbank).length;
            var St1book =JSON.stringify(res[i].attachedfileBookbank).substring(2,4);
            var St2book=JSON.stringify(res[i].attachedfileBookbank).substring(alllengthbook-8,alllengthbook-5);
            var St3book=JSON.stringify(res[i].attachedfileBookbank).substring(alllengthbook-19,alllengthbook-15);
            var sumstbook =  `${St1book}-${St2book}${St3book}`;
          }
          var checkbank;
          if(res[i].attachedfileBookbank==='A'){
            checkbank = res[i].attachedfileBookbank
          }else{
            checkbank='B'
          }

         this.BillId.push({
           noid : i,
           idbill: res[i].id,
           salesid: res[i].salesid,
           invoiceid :  res[i].invoiceid,
           invoicename : res[i].invoicingname,
           orderaccount : res[i].orderaccount,
           invoicedate :  res[i].invoicedate,
           duedate :  res[i].duedate,
           salesbalance :  res[i].salesbalance,
           sumtax :  res[i].sumtax,
           invoiceamount:  res[i].invoiceamount,
           billno : res[i].billno,
           approve : res[i].approve,
           payment : res[i].payment,
           urlimg : res[i].attachedfile,
           delivery : res[i].deliveryname,
           check: false,
           nameimg : res[i].attachedfile,
           saleman :res[i].salegroup,
           remark : res[i].remark,
           remarkst :res[i].remarkstatus,
           numrun: sumst,
           imgbookbank : res[i].attachedfileBookbank,
           numrunbank:sumstbook,
           checkbank : checkbank
         });}
         this.showbill();
         this.sumsuccess();
    }else{
      this.showbillno = 'ไม่มีรายการ'
     }

      },error=>{
        status=error.status;
       // this.openModal(true,'เกิดปัญหาในการ Process ข้อมูล กรุณารายการใหม่อีกครั้ง',false);
       alert('เกิดปัญหาในการ Process ข้อมูล กรุณารายการใหม่อีกครั้ง');
      });
  }

  OpenModalApprove(Invoicingname,billid,template){
    this.getIdBill(Invoicingname,billid);
    this.ModalRefApprove = this.modalService.show(template,
      {class: 'modal-lg', ignoreBackdropClick: true}
     );
  }

getIdBill(Invoicingname,billid){

  this.BillId=[];
  this.total=0;
  this.SearchRevertBillno='';
  this.SearchRevertINVname='';
  this.Remark='';
  this.testcheck=false;
  this.SearchRevertINVname =Invoicingname;
  this.truesumprice=0.00;
  this.truesumvat=0.00;
  this.trueproductprice=0.00;
  var databillid ='';
  this.getdate();
  if(billid==''){
    databillid='%20'
    this.SearchRevertBillno='%20'
  }else{
    databillid=billid;
    this.SearchRevertBillno=billid;
  }
  this.http.get<any>(this.url +'get_bill_ID/'+databillid +'/'+ Invoicingname + '/' + this.fromdate+'/'+ this.todate ).subscribe(res => {
     this.billnoDataSearch=res;
    if(res.length > 0){
      /*var test1 = res[0].attachedfile.splie();*/
      for(var i=0; i< res.length; i++) {
        var alllength=JSON.stringify(res[i].attachedfile).length;
        var St1 =JSON.stringify(res[i].attachedfile).substring(2,4);
        var St2=JSON.stringify(res[i].attachedfile).substring(alllength-8,alllength-5);
        var St3=JSON.stringify(res[i].attachedfile).substring(alllength-19,alllength-15);
        var sumst =  `${St1}-${St2}${St3}`;
        for(var v =0; v< res.length; v++){
          var alllengthbook=JSON.stringify(res[i].attachedfileBookbank).length;
          var St1book =JSON.stringify(res[i].attachedfileBookbank).substring(2,4);
          var St2book=JSON.stringify(res[i].attachedfileBookbank).substring(alllengthbook-8,alllengthbook-5);
          var St3book=JSON.stringify(res[i].attachedfileBookbank).substring(alllengthbook-19,alllengthbook-15);
          var sumstbook =  `${St1book}-${St2book}${St3book}`;
        }
        var checkbank;
        if(res[i].attachedfileBookbank==='A'){
          checkbank = res[i].attachedfileBookbank
        }else{
          checkbank='B'
        }

       this.BillId.push({
         noid : i,
         idbill: res[i].id,
         salesid: res[i].salesid,
         invoiceid :  res[i].invoiceid,
         invoicename : res[i].invoicingname,
         orderaccount : res[i].orderaccount,
         invoicedate :  res[i].invoicedate,
         duedate :  res[i].duedate,
         salesbalance :  res[i].salesbalance,
         sumtax :  res[i].sumtax,
         invoiceamount:  res[i].invoiceamount,
         billno : res[i].billno,
         approve : res[i].approve,
         payment : res[i].payment,
         urlimg : res[i].attachedfile,
         delivery : res[i].deliveryname,
         check: false,
         nameimg : res[i].attachedfile,
         saleman :res[i].salegroup,
         remark : res[i].remark,
         remarkst :res[i].remarkstatus,
         numrun: sumst,
         imgbookbank : res[i].attachedfileBookbank,
         numrunbank:sumstbook,
         checkbank : checkbank
       });}

       this.showbill();

  }else{
    this.showbillno = 'ไม่มีรายการ'
   }

    },error=>{
      status=error.status;
     // this.openModal(true,',false);
     alert('เกิดปัญหาในการ Process ข้อมูล กรุณารายการใหม่อีกครั้ง');
    });

}


getIdBill_nobookbank(Invoicingname,billid){
  this.BillId=[];
  this.total=0;
  this.SearchRevertBillno='';
  this.SearchRevertINVname='';
  this.Remark='';
  this.testcheck=false;
  this.SearchRevertINVname =Invoicingname;
  this.truesumprice=0.00;
  this.truesumvat=0.00;
  this.trueproductprice=0.00;
  var databillid ='';
  this.getdate();
  if(billid==''){
    databillid='%20'
    this.SearchRevertBillno='%20'
  }else{
    databillid=billid;
    this.SearchRevertBillno=billid;
  }
  this.http.get<any>(this.url +'get_bill_ID_nobookbank/'+databillid +'/'+ Invoicingname + '/' + this.fromdate+'/'+ this.todate ).subscribe(res => {
     this.billnoDataSearch=res;
    if(res.length > 0){
      /*var test1 = res[0].attachedfile.splie();*/
      for(var i=0; i< res.length; i++) {
        var alllength=JSON.stringify(res[i].attachedfile).length;
        var St1 =JSON.stringify(res[i].attachedfile).substring(2,4);
        var St2=JSON.stringify(res[i].attachedfile).substring(alllength-8,alllength-5);
        var St3=JSON.stringify(res[i].attachedfile).substring(alllength-19,alllength-15);
        var sumst =  `${St1}-${St2}${St3}`;
        for(var v =0; v< res.length; v++){
          var alllengthbook=JSON.stringify(res[i].attachedfileBookbank).length;
          var St1book =JSON.stringify(res[i].attachedfileBookbank).substring(2,4);
          var St2book=JSON.stringify(res[i].attachedfileBookbank).substring(alllengthbook-8,alllengthbook-5);
          var St3book=JSON.stringify(res[i].attachedfileBookbank).substring(alllengthbook-19,alllengthbook-15);
          var sumstbook =  `${St1book}-${St2book}${St3book}`;
        }
        var checkbank;
        if(res[i].attachedfileBookbank==='A'){
          checkbank = res[i].attachedfileBookbank
        }else{
          checkbank='B'
        }

       this.BillId.push({
         noid : i,
         idbill: res[i].id,
         salesid: res[i].salesid,
         invoiceid :  res[i].invoiceid,
         invoicename : res[i].invoicingname,
         orderaccount : res[i].orderaccount,
         invoicedate :  res[i].invoicedate,
         duedate :  res[i].duedate,
         salesbalance :  res[i].salesbalance,
         sumtax :  res[i].sumtax,
         invoiceamount:  res[i].invoiceamount,
         billno : res[i].billno,
         approve : res[i].approve,
         payment : res[i].payment,
         urlimg : res[i].attachedfile,
         delivery : res[i].deliveryname,
         check: false,
         nameimg : res[i].attachedfile,
         saleman :res[i].salegroup,
         remark : res[i].remark,
         remarkst :res[i].remarkstatus,
         numrun: sumst,
         imgbookbank : res[i].attachedfileBookbank,
         numrunbank:sumstbook,
         checkbank : checkbank
       });}
       this.showbill();
       this.searchviewBookbank();
  }else{
    this.showbillno = 'ไม่มีรายการ'
   }

    },error=>{
      status=error.status;
      //this.openModal(true,'',false);
      alert('เกิดปัญหาในการ Process ข้อมูล กรุณารายการใหม่อีกครั้ง');
    });

}
getViewnum(){
  if(this.BillId.length>0){
    for(var i=0; i< this.BillId.length; i++){
      this.groupimg.push({
        id:i+1,
        nameimg:this.BillId[i].numrun
      });

    }
  }
}


getIdBillSuccess(Invoicingname,billid){
  this.BillId=[];
  this.clients=[];
  this.dataTable=[];
  this.INVSuccess=[];
  this.total=0;
  this.testcheck=false;
  this.truesumprice=0.00;
  this.truesumvat=0.00;
  this.trueproductprice=0.00;
  this.getdate();
  var databillid ='';
  var  example :any=[]=[];
  this.billno='';
  this.nameinv='';
  this.nameinv=Invoicingname;
  if(billid==''){
    databillid='%20'
    this.billno='%20'
  }else{
    databillid=billid;
    this.billno=billid;
  }
  this.http.get<any>(this.url +'get_bill_ID_Success/'+databillid +'/'+ Invoicingname + '/' + this.fromdate+'/'+ this.todate +'/%20' ).subscribe((res: any[]) => {
    if(res.length > 0){
      for(var i=0; i< res.length; i++) {
        var alllength=JSON.stringify(res[i].attachedfile).length;
        var St1 =JSON.stringify(res[i].attachedfile).substring(2,4);
        var St2=JSON.stringify(res[i].attachedfile).substring(alllength-8,alllength-5);
        var St3=JSON.stringify(res[i].attachedfile).substring(alllength-19,alllength-15);
        var sumst =  `${St1}-${St2}${St3}`;

        for(var v =0; v< res.length; v++){
          var alllengthbook=JSON.stringify(res[i].attachedfileBookbank).length;
          var St1book =JSON.stringify(res[i].attachedfileBookbank).substring(2,4);
          var St2book=JSON.stringify(res[i].attachedfileBookbank).substring(alllengthbook-8,alllengthbook-5);
          var St3book=JSON.stringify(res[i].attachedfileBookbank).substring(alllengthbook-19,alllengthbook-15);
          var sumstbook =  `${St1book}-${St2book}${St3book}`;
        }
        var checkbank;
        if(res[i].attachedfileBookbank==='A'){
          checkbank = res[i].attachedfileBookbank
        }else{
          checkbank='B'
        }

       this.BillId.push({
         noid : i,
         idbill: res[i].id,
         salesid: res[i].salesid,
         invoiceid :  res[i].invoiceid,
         invoicename : res[i].invoicingname,
         orderaccount : res[i].orderaccount,
         invoicedate :  res[i].invoicedate,
         duedate :  res[i].duedate,
         salesbalance :  res[i].salesbalance,
         sumtax :  res[i].sumtax,
         invoiceamount:  res[i].invoiceamount,
         billno : res[i].billno,
         approve : res[i].approve,
         payment : res[i].payment,
         urlimg : res[i].attachedfile,
         delivery : res[i].deliveryname,
         check: false,
         nameimg : res[i].attachedfile,
         saleman :res[i].salegroup,
         remark : res[i].remark,
         remarkst :res[i].remarkstatus,
         numrun: sumst,
         imgbookbank : res[i].attachedfileBookbank,
         numrunbank:sumstbook,
         checkbank : checkbank
        });}

        this.showbill();
        this.sumsuccess();
        this.INVSuccess=this.BillId
     /* this.clients = res;
// You'll have to wait that changeDetection occurs and projects data into
      // the HTML template, you can ask Angular to that for you ;-)

          this.chRef.checkNoChanges();
      example = $('#example');
       this.dataTable = example.DataTable({
        'paging'      : true,
        'lengthChange': false,
        'searching'   : true,
        'ordering'    : true,
        'info'        : true,
        'autoWidth'   : true
      });

       this.showbill();
   */
  }else{
    this.showbillno = 'ไม่มีรายการ'
    this.productpricesuccess=0;
    this.sumvatsuccess=0;
    this.sumpricesuccess=0;
   }

    },error=>{
    //  this.openModal(true,'เกิดปัญหาในการ Process ข้อมูล กรุณารายการใหม่อีกครั้ง',false);
    alert('เกิดปัญหาในการ Process ข้อมูล กรุณารายการใหม่อีกครั้ง');
    });
}



getIdBillSuccessINV(){
  this.getdate();
  if(this.dateinvid==''){
    this.dateinvid='%20';
  }
  this.BillId=[];
  this.http.get<any>(this.url +'get_bill_ID_SuccessCH/'+this.billno +'/'+ this.nameinv + '/' + this.fromdate+'/'+ this.todate +'/'+this.dateinvid ).subscribe((res: any[]) => {
    if(res.length > 0){
      for(var i=0; i< res.length; i++) {
        var alllength=JSON.stringify(res[i].attachedfile).length;
        var St1 =JSON.stringify(res[i].attachedfile).substring(2,4);
        var St2=JSON.stringify(res[i].attachedfile).substring(alllength-8,alllength-5);
        var St3=JSON.stringify(res[i].attachedfile).substring(alllength-19,alllength-15);
        var sumst =  `${St1}-${St2}${St3}`;


        for(var v =0; v< res.length; v++){
          var alllengthbook=JSON.stringify(res[i].attachedfileBookbank).length;
          var St1book =JSON.stringify(res[i].attachedfileBookbank).substring(2,4);
          var St2book=JSON.stringify(res[i].attachedfileBookbank).substring(alllengthbook-8,alllengthbook-5);
          var St3book=JSON.stringify(res[i].attachedfileBookbank).substring(alllengthbook-19,alllengthbook-15);
          var sumstbook =  `${St1book}-${St2book}${St3book}`;
        }
        var checkbank;
        if(res[i].attachedfileBookbank==='A'){
          checkbank = res[i].attachedfileBookbank
        }else{
          checkbank='B'
        }

       this.BillId.push({
         noid : i,
         idbill: res[i].id,
         salesid: res[i].salesid,
         invoiceid :  res[i].invoiceid,
         invoicename : res[i].invoicingname,
         orderaccount : res[i].orderaccount,
         invoicedate :  res[i].invoicedate,
         duedate :  res[i].duedate,
         salesbalance :  res[i].salesbalance,
         sumtax :  res[i].sumtax,
         invoiceamount:  res[i].invoiceamount,
         billno : res[i].billno,
         approve : res[i].approve,
         payment : res[i].payment,
         urlimg : res[i].attachedfile,
         delivery : res[i].deliveryname,
         check: false,
         nameimg : res[i].attachedfile,
         saleman :res[i].salegroup,
         remark : res[i].remark,
         remarkst :res[i].remarkstatus,
         numrun: sumst,
         imgbookbank : res[i].attachedfileBookbank,
         numrunbank:sumstbook,
         checkbank : checkbank
        });}

      if(this.dateinvid='%20'){
        this.dateinvid='';
      }
      this.sumsuccess();
  }else{

    this.productpricesuccess=0;
    this.sumvatsuccess=0;
    this.sumpricesuccess=0;
   }

    },error=>{
     // this.openModal(true,,false);
     alert('เกิดปัญหาในการ Process ข้อมูล');
    });
}

sumsuccess(){

  this.productpricesuccess=0;
  this.sumvatsuccess=0;
  this.sumpricesuccess=0;
  if(this.BillId.length > 0) {
    for(var i=0;i< this.BillId.length;i++){
      this.productpricesuccess +=this.BillId[i].salesbalance;
      this.sumvatsuccess += this.BillId[i].sumtax;
      this.sumpricesuccess +=this.BillId[i].invoiceamount;
    }
  }
}









showbill(){
  if(this.BillId[0].billno==''){
    this.showbillno = this.BillId[0].invoicename;
  }else{
     this.showbillno =  this.BillId[0].invoicename +' :  '+ this.BillId[0].billno;
  }

}

selectAllbookbank(checked) {
  for(var i=0; i< this.BillId.length; i++) {
    if(this.viewBookbank==='A'){
      if(this.BillId[i].checkbank==='A'){
        this.BillId[i].check=checked;
      }

    }else if (this.viewBookbank==='B') {
      if(this.BillId[i].checkbank==='B'){
        this.BillId[i].check=checked;
      }
    }else{
      this.BillId[i].check=checked;
    }

  }  this.GetbillTrue();
  this.total = this.Bill_approve.length;
  this.sumTrue();
}

checkIfAllSelectedbookbank(checked,index) {
  this.BillId[index].check=checked;
  this.total=0;
  this.GetbillTrue();
  this.total = this.Bill_approve.length;
  this.getcaheck();
  this.sumTrue();
}


selectAll(checked) {
  for(var i=0; i< this.BillId.length; i++) {
    this.BillId[i].check=checked;
  }  this.GetbillTrue();
  this.total = this.Bill_approve.length;
  this.sumTrue();
}

checkIfAllSelected(checked,index) {
  this.BillId[index].check=checked;
  this.total=0;
  this.GetbillTrue();
  this.total = this.Bill_approve.length;
  this.getcaheck();
  this.sumTrue();
}


GetbillTrue(){
  this.Bill_approve=[];
   for (var i=0; i< this.BillId.length;i++){
     if(this.BillId[i].check==true) {
       this.Bill_approve.push({
         id:this.BillId[i].invoiceid,
         billno:this.BillId[i].billno
                   });
     }
   }

 }
 getcaheck(){
  var ch=0;
 for (var i=0;i<this.BillId.length;i++){
if(this.BillId[i].check==true){
  ch++;
}else {
  ch--;
}
 }
if( ch == this.BillId.length ){
  this.testcheck=true;

}else {
  this.testcheck=false;

}

}


 deletelallid(value) {
  this.Bill_approve.splice(value,1);
}

 UpLoadApprove(st,remark){

  this.productprice=0;
  this.sumvat=0;
  this.sumprice=0;
  this.load = 'รายการที่ : ' + this.Bill_approve.length.toString();
  if(this.Bill_approve.length != 0){
    this.http.post(this.url + 'update_invoice_ByApprove',{
      invoiceid :this.Bill_approve[0].id,
      stapp : st,
      remark : ','+remark
    }).subscribe(res =>{
      if (res == true) {
        this.deletelallid(0);
        this.load = 'รายการที่ : ' + this.Bill_approve.length.toString();
      }else{

      }
     },error=>{
       alert('เกิดปัญหาในการ Process ข้อมูล');
     // this.openModal(true,'เกิดปัญหาในการ Process ข้อมูล',false);
    });
  }else{
      if(this.Bill_approve.length ==0){
  clearInterval(this.setInterval);
  this.SearchComplete();
  this.getIdBill(this.SearchRevertINVname,this.SearchRevertBillno);
 /* this.openModalalert(false,'',false);
  this.openModal2(true,'บันทึกข้อมูลเรียบร้อย',true); */
  this.textload="บันทึกข้อมูล Remark เรียบร้อย"
  this.btnPDF=true;
  this.testcheck=false;
    }
  }

 }

 UpLoadremark(remark,st,typeupload){

  this.productprice=0;
  this.sumvat=0;
  this.sumprice=0;
  this.load = 'รายการที่ : ' + this.Bill_approve.length.toString();
  if(this.Bill_approve.length != 0){
    this.http.post(this.url + 'update_invoice_Byremark',{
      invoiceid : this.Bill_approve[0].id,
      remark : ','+remark,
      st : st
    }).subscribe(res =>{
      if (res == true) {
        this.deletelallid(0);
        this.load = 'รายการที่ : ' + this.Bill_approve.length.toString();
      }else{

      }
     },error=>{
    //  this.openModal(true,'เกิดปัญหาในการ Process ข้อมูล',false);
    alert('เกิดปัญหาในการ Process ข้อมูล');
    });
  }else{
      if(this.Bill_approve.length ==0){
  clearInterval(this.setInterval);
  this.Remark='';
  this.selectAll(false);
 // alert(typeupload)
  if(typeupload==='Revert'){
    this.getIdBillRevert(this.SearchRevertINVname,this.SearchRevertBillno);
  }else{
    this.getIdBill(this.SearchRevertINVname,this.SearchRevertBillno);
  }
 // this.openModalalert(false,'',false);
 // this.openModal2(true,'บันทึกข้อมูล Remark เรียบร้อย ',true);
  // this.getIdBillRevert(this.SearchRevertINVname,this.SearchRevertBillno)
  this.SearchComplete();
    this.textload="บันทึกข้อมูล Remark เรียบร้อย"
    this.btnPDF=true;
    this.testcheck=false;
    }
  }

 }

 addremarkRevert(){
  var remark = '';
  var st ='0';
  var typeupload='Revert'
  remark =this.Remark;
  this.GetbillTrue();
      if(this.Bill_approve.length > 0 ){
                        this.openModalalert(true,'กำลังบันทึกข้อมูล',false);
                        this.setInterval=setInterval(() => this.UpLoadremark(remark,st,typeupload), 400);
                      }else{
                        }
 }
 UpLoadRevert(st,remark){

  this.productprice=0;
  this.sumvat=0;
  this.sumprice=0;
  this.load = 'รายการที่ : ' + this.Bill_approve.length.toString();
  if(this.Bill_approve.length != 0){
    this.http.post(this.url + 'update_invoice_ByRevert',{
    invoiceid :this.Bill_approve[0].id,
    stapp :st,
    remark :remark
 }).subscribe(res =>{
      if (res == true) {
        this.deletelallid(0);
        this.load = 'รายการที่ : ' + this.Bill_approve.length.toString();
      }else{

      }
     },error=>{
     // this.openModal(true,'เกิดปัญหาในการ Process ข้อมูล',false);
     alert('เกิดปัญหาในการ Process ข้อมูล')
    });
  }else{
      if(this.Bill_approve.length ==0){
  clearInterval(this.setInterval);
      this.SearchCompleteED();
      this.getIdBillRevert(this.SearchRevertINVname,this.SearchRevertBillno);
  /*this.openModalalert(false,'',false);
  this.openModal2(true,'บันทึกข้อมูลเรียบร้อย Revert',true);*/
  this.textload="บันทึกข้อมูลเรียบร้อย"
  this.btnPDF=true;
  this.testcheck=false;
    }
  }
 }
addremark(template){
  var remark = '';
  var st ='1';
  var typeupload='Approve'
  remark =this.Remark;
  this.GetbillTrue();
  this.ModalremarkRef.hide();
      if(this.Bill_approve.length > 0 ){
                       // this.openModalalert(true,'กำลังบันทึกข้อมูล',false);
                       this.textload="กำลังบันทึกข้อมูล";
                       this.openModalshow(template)
                       this.btnPDF=false;
                        this.setInterval=setInterval(() => this.UpLoadremark(remark,st,typeupload), 400);
                      }else{
                        }
}

 onUpload(template){
  this.Bill_approve=[];
  var remark = '';
  if(this.Remark==''){
    remark=''
    }else{
      remark =this.Remark;
    }
 var st = 'อนุมัติแล้ว'
  this.GetbillTrue();
      if(this.Bill_approve.length > 0 ){
                      //  this.openModalalert(true,'กำลังบันทึกข้อมูล',false);
                      this.textload="กำลังบันทึกข้อมูล";
                      this.openModalshow(template)
                      this.btnPDF=false;
                        this.setInterval=setInterval(() => this.UpLoadApprove(st,remark), 400);
                      }else{
                        }

}

onRevert(template){
  this.Bill_approve=[];
  var remark = '';
  if(this.Remark==''){
    remark=''
    }else{
      remark =this.Remark;
    }
 var st = 'รออนุมัติ'
  this.GetbillTrue();
      if(this.Bill_approve.length > 0 ){
                       // this.openModalalert(true,'กำลังบันทึกข้อมูล',false);
                       this.textload="กำลังบันทึกข้อมูล";
                      this.openModalshow(template)
                      this.btnPDF=false;
                        this.setInterval=setInterval(() => this.UpLoadRevert(st,remark), 400);
                      }else{
                        }

}


openModalBillViewDetail(ViewDetail,Invoiceid,
  orderaccount:string,
  salesid:string,
  invoicename:string,
  invoicedate:string,
  duedate:string,
  delivery:string,
  salesbalance:string,
  sumtax:string,
  invoiceamount:string,
  saleman:string,
  remark:string,
  imgBill,imgbookbank) : void {
  var I3remark =  remark.substring(0,4)
  if(I3remark===' , ,'){
    this.remarkshow =  remark.substring(4)
    }else{
      this.remarkshow =  remark.substring(2)
    }
    this.SalesId= '';
    this.SONo= '';
    this.InvoiceNo= '';
    this.OrderAccount= '';
    this.Invoicingname= '';
    this.InvoiceDate= '';
    this.DueDate= '';
    this.Deliveryname= '';
    this.productsun= '';
    this.VAT= '';
    this.suntotal= '';

    this.SalesId= saleman;
  this.SONo= salesid;
  this.InvoiceNo= Invoiceid;
  this.OrderAccount= orderaccount;
  this.Invoicingname= invoicename;
  this.InvoiceDate= invoicedate;
  this.DueDate= duedate;
  this.Deliveryname= delivery;
  this.productsun= salesbalance;
  this.VAT= sumtax;
  this.suntotal= invoiceamount;
   /*this.remarkshow=I3remark;
 this.mdlSampleIsOpenBill = open;
  this.altBill=text;
  this.checkreloadBill=load;*/
  this.setImgInvoice(imgBill);
  this.setImgBill(imgBill);
  this.setImgBookbank(imgbookbank);
  this.modalRefviewDetail = this.modalService.show(ViewDetail,
    {class: 'modal-lg'}
   );
}
closemodelBill(cl: boolean) {
this.mdlSampleIsOpenBill=cl;
if(this.checkreloadBill==true) {
  this.SearchComplete();
}
}
openModalimg(ViewDetailApprove,nameimage : string, invid :string,
  orderaccount:string,
  salesid:string,
  invoicename:string,
  invoicedate:string,
  duedate:string,
  delivery:string,
  salesbalance:string,
  sumtax:string,
  invoiceamount:string,
  saleman:string,
  remark:string,
  imgbookbank :string
) : void {
  this.SalesId= '';
  this.SONo= '';
  this.InvoiceNo= '';
  this.OrderAccount= '';
  this.Invoicingname= '';
  this.InvoiceDate= '';
  this.DueDate= '';
  this.Deliveryname= '';
  this.productsun= '';
  this.VAT= '';
  this.suntotal= '';
  this.remarkshow='';
  var I3remark =  remark.substring(0,4)
  if(I3remark===' , ,'){
    this.remarkshow =  remark.substring(4)
    }else{
      this.remarkshow =  remark.substring(2)
    }
 /* this.mdlSampleIsOpenimg = open;*/
  this.altimg='';
  this.altimg=invid;
 /* this.checkreloadimg=load;*/
  this.setImgBill(nameimage);
  this.setImgBookbank(imgbookbank);
  this.mdlSampleIsOpensuccess = false;

  this.SalesId= saleman;
  this.SONo= salesid;
  this.InvoiceNo= invid;
  this.OrderAccount= orderaccount;
  this.Invoicingname= invoicename;
  this.InvoiceDate= invoicedate;
  this.DueDate= duedate;
  this.Deliveryname= delivery;
  this.productsun= salesbalance;
  this.VAT= sumtax;
  this.suntotal= invoiceamount;
  this.modalRefviewDetail = this.modalService.show(ViewDetailApprove,
    {class: 'modal-lg'}
   );
}
closemodelimg(cl: boolean) {
/*this.mdlSampleIsOpenimg=cl;*/
if(this.checkreloadimg==false) {

}

}

openModal(open : boolean,text: string,load:boolean) : void {
  this.mdlSampleIsOpen = open;
  this.alt=text;
  this.checkreload=load;
}
closemodel(cl: boolean) {
this.mdlSampleIsOpen=cl;
if(this.checkreload==true) {
}
}
/*openModalapprove(open : boolean,text: string,load:boolean,billID) : void {
this.BillId=[];

this.mdlSampleIsOpenApprove = open;
this.altApprove=text;
this.checkreloadApprove=load;
}*/
openModal2(open : boolean,text: string,load:boolean) : void {
this.mdlSampleIsOpen2 = open;
this.alt2=text;
this.checkreload2=load;
}

openModalalert(open : boolean,text: string,load:boolean) : void {
this.mdlSampleIsOpen3 = open;
this.alt3=text;
this.checkreload3=load;
}

closemodelapprove(cl: boolean) {
this.mdlSampleIsOpenApprove=cl;
if(this.checkreloadApprove==true) {

}
}

closemodel2(cl: boolean) {
this.mdlSampleIsOpen2=cl;
if(this.checkreload2==true) {

}

}

openModalsuccess(viewsuccess,invname,billno) : void {
   var databillid ='';
  /* if(billno==''){
    databillid='%20'
  }else{
    databillid=billno;
  }
  this.http.get<any>(this.url +'get_Group_IMG/'+databillid +'/'+ invname + '/' + this.fromdate+'/'+ this.todate )
  .subscribe((res: any[]) => {
    if(res.length>0){
        alert(JSON.stringify(res))
    }
});*/
this.dateinvid='';
  this.getIdBillSuccess(invname,billno);
  this.ModalRefApprove = this.modalService.show(viewsuccess,
    {class: 'modal-lg' , ignoreBackdropClick: true}
   );
}
closemodelsuccess(cl: boolean) {
this.mdlSampleIsOpensuccess=cl;
if(this.checkreloadsuccess==false) {
}
}

setImgInvoice(nameimage){
  var nameimg =`${nameimage}`;
  this.ImageIN ='../assets/imageINV/'+nameimg;
}

setImgBill(nameimage){
    this.test2 = nameimage
    this.ImageBillno = this.urlimg+ this.test2;

}

setImgBookbank(nameimage){
  this.test2 = nameimage
  if(nameimage==='A'){
    this.ImageBookbank= this.urlimgDefault;
  }else{
    this.ImageBookbank = this.urlimgBookbank+ nameimage;
  }

}

handleFileInput(file: FileList,modelshow) {
  this.load ='';
  this.selectedFile='';
  this.Btnimg=false;
  this.btnPDF=false;
if (file.item(0).type =="image/jpeg" && file.item(0).size <= (1024*1024*5) ) {
//this.openModal2(true,'กำลังปรับขนาดไฟล์',false)
this.textload="กำลังปรับขนาดไฟล์";
this.modalRefshow = this.modalService.show(modelshow,
    {class: 'modal-sm' , backdrop: "static" }
   );
let image = file.item(0);
this.ng2ImgMax.resizeImage(image, 1024, 768).subscribe(
  result => {
    this.selectedFile = result;
    this.textload= this.selectedFile.name;
    //Show image preview
    var reader = new FileReader();
    reader.onload = (event:any) => {
      this.imageUrl = event.target.result;
    //  this.modalRefshow.hide();
    }
    reader.readAsDataURL(this.selectedFile);
    //this.openModal2(false,'',false)
 //   this.modalRefshow.hide();
   // this.Btnimg=true
    this.end()
   },
  error => {
  //  this.openModal(true,error,false)
  alert('เกิดข้อผิดพลาด')
  }
);




  /*  this.selectedFile = file.item(0);*/


} else {
this.load='';
//this.openModal(true,'ชนิดไฟล์รูปภาพไม่ถูกต้อง กรุณาเลือกชนิดไฟล์เป็น .jpeg และ ไฟล์ขนาดไม่เกิน 5120 kb',false)
alert('ชนิดไฟล์รูปภาพไม่ถูกต้อง กรุณาเลือกชนิดไฟล์เป็น .jpeg และ ไฟล์ขนาดไม่เกิน 5120 kb')
      this.imageUrl =  "assets/img/default-image.png";
  }


}

end(){
    //this.textload='ปิด'
    this.Btnimg=true;
    this.btnPDF=false;
    this.modalRefshow.hide();
}

OpenUploadbookbank(template: TemplateRef<any>,idSo) {
  this.btnPDF=false;
  this.btnREpdf=false;
  this.showIDso=""
  this.showIDso=idSo;
  this.textload="";
  this.imageUrl= "assets/img/default-image.png";
  this.selectedFile=null;
  this.modalRef = this.modalService.show(template, this.config);
}

onUploadbookbank(templateShow: TemplateRef<any>,template: TemplateRef<any>){
  this.modalRef.hide();
  this.textload="กำลังอัพโหลดไฟล์ โปรดรอ.";
  this.openModalshow(templateShow);
  const fd = new FormData();

  let time = new Date().getFullYear() +'-'+ new Date().getMonth()+'-'+new Date().getDate();
  fd.append('bookbank', this.selectedFile, this.selectedFile.name)
              this.http.post<any>(this.url+this.namebookbank+'-'+time+'/uploadbookbank',fd, {
                reportProgress:true,
                observe:'events',

              }).subscribe(event => {
                  if(event.type === HttpEventType.UploadProgress){

                  }else if (event.type === HttpEventType.Response)
                  {
                      console.log(event);
                      if (event.body.success === true) {

                        this.textload="อัพโหลดไฟล์เสร็จสิ้น"
                        this.textload="โปรดรอ."
                        this.btnPDF=false;
                        this.Btnimg=false;
                        //this.updataFlie(this.Bill_approve[i].id,event.body._name)
                      // alert(event.body._namebook)
                        this.setInterval=setInterval(() => this.updataFlie(event.body._namebook), 700);





                      }
                      else
                      {
                        this.textload="เกิดปัญหาในการ upload กรุณาทำรายการใหม่"
                        this.btnPDF=false;
                        this.Btnimg=false;
                        this.btnREpdf=true;
                        //alert('เกิดปัญหาในการ upload กรุณาทำรายการใหม่' )
                      }
                  }
              });

}




 updataFlie(_name)  {
  //updataDPF_idSo
  if(this.Bill_approve.length != 0){
    this.http.post<any>(this.url+'updatabookbank_idSo',{
      idSo : this.Bill_approve[0].id,
      _name : _name
    }).subscribe(res=> {
        //  alert(res)
        if(res==true){
          this.deletelallid(0);
        }else{
              // this.textload="เกิดปัญหาในการ เพิ่มรายการ"
        }

        })
  }else{
      if(this.Bill_approve.length ==0){
  clearInterval(this.setInterval);
  this.btnPDF=true
    }
  }
/*
do {
  //alert(num)
  this.http.post<any>(this.url+'updatabookbank_idSo',{
    idSo : this.Bill_approve[num].id,
    _name : _name
  }).subscribe(res=> {
      //  alert(res)
      if(res==true){
        //this.textload="ทำรายการเสร็จสิ้น"

        num++;
      }else{
            // this.textload="เกิดปัญหาในการ เพิ่มรายการ"

      }

      })
      console.log(num)
} while (num <= this.Bill_approve.length );
  */
 }
 openModalshow(templateShow: TemplateRef<any>) {
  this.modalRef = this.modalService.show(templateShow, {class: 'modal-sm' , backdrop: "static"});
}

 openModalbookbank(Invoicingname,Billno,Idcostomer,templateShow: TemplateRef<any>) {
  this.modalRef = this.modalService.show(templateShow,
   // Object.assign({}, { class: 'modal-lg', backdrop: "static" })
     {class: 'modal-lg' , backdrop: "static" }
    );
      this.showIDso= Invoicingname;
      this.textload="";
      this.imageUrl= "assets/img/default-image.png";
      this.selectedFile=null;
      this.namebookbank=Idcostomer;
      this.viewBookbank='A';
  this.getIdBill_nobookbank(Invoicingname,Billno);
}

confirmimg(): void{
  this.modalRefshow.hide();
}

confirm() {
  this.modalRef.hide();

 // this.Searchsolist();
}

decline(template): void {
  this.selectedFile=null;
  this.modalRef.hide();
  this.modalRef = this.modalService.show(template, this.config);

}

openModalviewDetail(template: TemplateRef<any>,Invoiceid,
  orderaccount:string,
  salesid:string,
  invoicename:string,
  invoicedate:string,
  duedate:string,
  delivery:string,
  salesbalance:string,
  sumtax:string,
  invoiceamount:string,
  saleman:string,
  remark:string,
  imgBill,
  imgbookbank){
    var I3remark =  remark.substring(0,4)
    if(I3remark===' , ,'){
      this.remarkshow =  remark.substring(4)
      }else{
        this.remarkshow =  remark.substring(2)
      }
      this.SalesId= '';
      this.SONo= '';
      this.InvoiceNo= '';
      this.OrderAccount= '';
      this.Invoicingname= '';
      this.InvoiceDate= '';
      this.DueDate= '';
      this.Deliveryname= '';
      this.productsun= '';
      this.VAT= '';
      this.suntotal= '';

      this.SalesId= saleman;
    this.SONo= salesid;
    this.InvoiceNo= Invoiceid;
    this.OrderAccount= orderaccount;
    this.Invoicingname= invoicename;
    this.InvoiceDate= invoicedate;
    this.DueDate= duedate;
    this.Deliveryname= delivery;
    this.productsun= salesbalance;
    this.VAT= sumtax;
    this.suntotal= invoiceamount;
   /* this.setImgInvoice(imgBill);*/
    this.setImgBill(imgBill);
      if(imgbookbank==='A'){
        this.CkimgBookbank= false;
      }else{
        this.CkimgBookbank= true;
        this.setImgBookbank(imgbookbank);
      }


    this.modalRefviewDetail = this.modalService.show(template,
      {class: 'modal-lg'}
     );
}


Modalremark(template: TemplateRef<any>){
  this.ModalremarkRef = this.modalService.show(template,
    {class: 'modal-lg'}
   );
}


}
