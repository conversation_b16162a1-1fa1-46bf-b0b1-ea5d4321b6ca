<app-topmenu></app-topmenu>
<section style="padding-top:60px">
    <div class="container-fluid" style="padding-right: 5px; padding-left: 5px;">
        <h5 class="p-sm-1 bg-secondary text-white text-center">Completed Invoice List</h5>
        <div class="needs-validation" novalidate>
            <div class="form-row">
                <div class="col-md-2 mb-2" *ngIf="testclose">
                    <select multiple [(ngModel)]="datasalegroup" class="custom-select custom-select-sm">
                        <option selected disabled value="">--เลือกรหัสพนักงานขาย--</option>
                        <option value="1">เลือกรายการทั้งหมด</option>
                        <option *ngFor="let item of DateGroupsaleman" [value]="item.groupid">
                            {{item.groupid}} ({{item.name}})
                        </option>

                    </select>
                </div>
                <!--<form [formGroup]="myForm">
                <ng-multiselect-dropdown
                    name="city"
                    [placeholder]="'Select City'"
                    [data]="cities"
                    formControlName="city"
                    [disabled]="disabled"
                    [settings]="dropdownSettings"
                    (onSelect)="onItemSelect($event)">
                </ng-multiselect-dropdown>
           </form>-->
                <div class="col-md-2 mb-2">
                    <!-- <select  [(ngModel)]="ModelStApprove" placeholder="พนักงานขาย"  class="custom-select custom-select-sm text-sm-left">
                        <option selected value="null" disabled>สถานะ Approve</option>
                        <option *ngFor="let item of stApprove" [ngValue]="item">
                              {{item.value}}
                      </option>
                    </select>-->
                    <select [(ngModel)]="keyboardStrApprove" class="custom-select custom-select-sm">
                        <option [selected]="keyboardStrApprove == 'สถานะ Approve'" value="">สถานะ Approve</option>
                        <option [selected]="keyboardStrApprove == 'Approval แล้ว'" value="1">Approval แล้ว</option>
                        <option [selected]="keyboardStrApprove == 'ยังไม่ได้ Approve'" value="0">ยังไม่ได้ Approve</option>
                    </select>
                    <div style=" position: relative; margin-top: 5px;">
                        <ng-template #rt let-r="result" let-t="term">
                            <div>
                                <label (mousedown)="searchselectprint()">{{r.name}} ({{r.accountnum}})</label>
                            </div>
        
                        </ng-template>
        
                        <input id="typeahead-template" placeholder="ชื่อลูกค้า" type="text" class="form-control form-control-sm" [(ngModel)]="Customer"
                            name="model" [ngbTypeahead]="search" [resultTemplate]="rt" [inputFormatter]="formatter" />
                        <div style="position: absolute;z-index: 10;width: 15%;height: 100%;top: 1px;right: 1px;font-size: 18px;cursor: pointer;text-align: center;"
                            (click)="cancel()">x</div>
                        <!--  <input id="inputcustomer" class="form-control form-control-sm" [(ngModel)]="Customer" type="text" name="customer" placeholder="ค้นหาลูกค้า">-->
                    </div>


                </div>
                <div class="col-md-1 mb-1">
                    <!--<select [(ngModel)]="Modelpaymenttype" class="custom-select custom-select-sm text-sm-left" >
                        <option selected  value="null" disabled>เงื่อนไขชำระ</option>
                      <option *ngFor="let i of paymenttype" [ngValue]="i">
                          {{i.value}}
                        </option>
                    </select>-->
                    <select [(ngModel)]="keyboardStrpaymenttype" class="custom-select custom-select-sm ">
                        <option [selected]="keyboardStrpaymenttype == 'เงื่อนไขชำระ'" value="">เงื่อนไขชำระ</option>
                        <option [selected]="keyboardStrpaymenttype == 'เงินสด'" value="0">เงินสด</option>
                        <option [selected]="keyboardStrpaymenttype == 'เครดิต'" value="1">เครดิต</option>
                    </select>
                </div>
                <div class="col-md-1 mb-1">
                    <select [(ngModel)]="printst" (change)="searchselectprint()" class="custom-select custom-select-sm" name="" id="">
                        <option value="3">เลือกทุุกรายการ</option>
                        <option value="รอbookbank">รอbookbank</option>
                        <option value="รออนุมัติ">รออนุมัติ</option>
                        <option value="อนุมัติแล้ว">approve แล้ว</option>

                    </select>
                </div>

                <!--<input id="fromdate" class="form-control form-control-sm" ngbDatepicker #d="ngbDatepicker" type="text" value="{{dateshipping}}"  [(ngModel)]="fromdate" name="fromdate" placeholder="จากวันที่ dd/mm/yyyy ">
              [(ngModel)]="fromdate" [(ngModel)]="todate" 
                                <button class="btn btn-outline-secondary calendar" (click)="d.toggle()" type="button"></button> -->
                <!-- <input type="text" class="form-control form-control-sm "   ngbDatepicker #d="ngbDatepicker"  (click)="d.toggle()"  placeholder="จากวันที่ dd/mm/yyyy">-->
                <!--  <div class="col-md-2 mb-2">
                  <input id="fromdate" class="form-control form-control-sm"  type="date" value="{{dateshipping}}"  [(ngModel)]="fromdate" name="fromdate" placeholder="จากวันที่ dd/mm/yyyy ">
                  </div>-->
                <!-- <div class="col-md-2 mb-2">
                           <input id="todate" class="form-control form-control-sm" type="date" value="{{dateshippingto}}"  [(ngModel)]="todate" name="todate" placeholder="ถึงวันที่">
                         </div>-->

                <div class="col-xs-12 col-12 col-md-2 form-group">
                    <input type="text" placeholder="DD/MM/YYYY" class="form-control" bsDatepicker [bsConfig]="{ dateInputFormat: 'DD/MM/YYYY' }"
                        class="form-control form-control-sm" [(ngModel)]="Datafromdate">
                </div>
                <div class="col-xs-12 col-12 col-md-2 form-group">
                    <input type="text" placeholder="DD/MM/YYYY" class="form-control" bsDatepicker [bsConfig]="{ dateInputFormat: 'DD/MM/YYYY' }"
                        class="form-control form-control-sm" [(ngModel)]="Datatodate">
                </div>

                <div class="col-md-2 mb-3 col-12 text-center text-sm-center text-md-center text-lg-left" style="padding-right: 0px; padding-left: 0px;">
                    <button [disabled]="searchbtn" style="width: 58px; margin-right: 3px;" class="btn btn-primary btn-sm font-weight-light" (click)="SearchComplete()"
                        type="submit">Search</button>
                    <button [disabled]="searchbtn" *ngIf="testclose" style=" width: 58px;margin-right: 3px;" class="btn btn-primary btn-sm font-weight-light"
                        (click)="SearchCompleteED(keyboardStrApprove,keyboardStrpaymenttype)" type="submit">Revert</button>
                    <button style=" width: 58px; margin-right: 3px;" [disabled]="dataExport==='' || exportbtn" (click)="newGetExport(seachheaderlist,productprice,sumvat,sumprice)"
                        class="btn btn-primary btn-sm font-weight-light" type="submit">Export</button>
                    <button *ngIf="  datalogin[0].salegroup=='admin'&&(datalogin[0].id_group_user =='1860Administrator' || datalogin[0].id_group_user=='153Admin') "
                        style=" width: 58px;" (click)="GetDelete(ShowDeleteINV,Datafromdate,Datatodate)" class="btn btn-primary btn-sm font-weight-light"
                        type="submit">Delete</button>
                </div>

            </div>
        </div>
        <table class="table table-hover table-bordered table-sm">
            <thead>
                <tr class="text-sm-center bg-light">
                    <th class="text-sm-center font-weight-normal" scope="col"></th>
                    <th class="text-sm-center font-weight-normal" scope="col">พนักงานขาย</th>
                    <th class="text-sm-center font-weight-normal" scope="col">ชื่อลูกค้า</th>
                    <th class="text-sm-center font-weight-normal" scope="col">Bill No</th>
                    <th class="text-sm-center font-weight-normal" scope="col">มูลค่าสินค้า</th>
                    <th class="text-sm-center font-weight-normal" scope="col">VAT</th>
                    <th class="text-sm-center font-weight-normal" scope="col">มูลค่ารวม</th>
                    <th class="font-weight-light" scope="col" class="text-sm-center" width="60px" *ngIf="!searchbtn "></th>
                    <th class="font-weight-light" scope="col" class="text-sm-center" width="60px" *ngIf="testclose"></th>
                    <th class="font-weight-light" scope="col" class="text-sm-center" width="60px" *ngIf="!testclose"></th>
                    <th class="font-weight-light" scope="col" class="text-sm-center" width="60px"></th>
                </tr>
            </thead>
            <tbody>    
                <tr class="text-sm-left " *ngFor="let item of seachheaderlist; let i=index">
                    <ng-container>
                        <!--<td class="text-sm-center font-weight-normal" [ngStyle]="{'color':getColor(item.approve,item.remarkstatus,item.CK)}">{{ item.CK }}</td>-->
                        <td class="text-sm-center font-weight-normal" [ngStyle]="{'color':getColor(item.approve,item.remarkstatus,item.CK)}">{{
                            i+1 }} </td>
                        <td class="text-sm-center font-weight-normal" [ngStyle]="{'color':getColor(item.approve,item.remarkstatus,item.CK)}">{{
                            item.Salegroup }}</td>

                        <td class="text-sm-left font-weight-normal" [ngStyle]="{'color':getColor(item.approve,item.remarkstatus,item.CK)}">{{
                            item.Invoicingname }} </td>
                        <td class="text-sm-center font-weight-normal" [ngStyle]="{'color':getColor(item.approve,item.remarkstatus,item.CK)}">{{
                            item.Billno }}</td>
                        <td class="text-sm-right text-right font-weight-normal" [ngStyle]="{'color':getColor(item.approve,item.remarkstatus,item.CK)}">{{
                            item.Salesbalance | number:'1.2-2' }}</td>
                        <td class="text-sm-right text-right font-weight-normal" [ngStyle]="{'color':getColor(item.approve,item.remarkstatus,item.CK)}">{{
                            item.Sumtax | number:'1.2-2' }}</td>
                        <td class="text-sm-right text-right font-weight-normal" [ngStyle]="{'color':getColor(item.approve,item.remarkstatus,item.CK)}">{{
                            item.Invoiceamount | number:'1.2-2' }}</td>



                        <td class="text-center bg-light" *ngIf="!searchbtn">
                            <button class="btn btn btn-success font-weight-light" [disabled]="item.CK==='อนุมัติแล้ว' || item.CK=='รออนุมัติกลับ' " style="padding: 0pt;;width: 99%"
                                (click)="openModalbookbank(item.Invoicingname,item.Billno,item.Orderaccount,templateView,item.Salegroup)">
                                Bookbank
                            </button>
                            <!-- data-target="#ModalApprovesuccess" [disabled]="item.CK!='รอbookbank'" (click) ="openModalsuccess(true,'',true,item.Invoicingname,item.Billno)"-->
                        </td>

                        <td class="text-center bg-light" *ngIf="testclose && SearchCompleteED_ ">
                            <button class="btn btn btn-warning font-weight-light" [disabled]="item.CK!='รออนุมัติกลับ' " style="padding: 0pt;width: 99%"
                                aria-expanded="true" (click)="OpenModalRevert(item.Invoicingname,item.Billno,ModalApprove,item.Orderaccount,item.Salegroup)">
                                Revert
                            </button>
                        </td>
                        <td class="text-center bg-light" *ngIf="testclose  && SearchComplete_ ">
                            <button class="btn btn btn-warning font-weight-light" [disabled]="item.CK!='รออนุมัติ' || item.CK=='รออนุมัติกลับ'" style="padding: 0pt;width: 99%"
                                aria-expanded="true" (click)="OpenModalApprove(item.Invoicingname,item.Billno,ModalApprove,item.Orderaccount,item.Salegroup)">
                                Approve
                            </button>
                        </td>
                        <td class="text-center bg-light" *ngIf="!testclose">
                            <button class="btn btn btn-warning font-weight-light" [disabled]="item.approve==='อนุมัติแล้ว' || item.CK!='รออนุมัติ'" style="padding: 0pt;width: 99%"
                                aria-expanded="true" (click)="getIdBillwaitModal(item.Invoicingname,item.Billno,Modalwait,item.Orderaccount,item.Salegroup)">
                                wait
                            </button>
                        </td>
                        <td class="text-center bg-light">
                            <button class="btn btn btn-success font-weight-light" style="padding: 0pt;;width: 99%" (click)="openModalsuccess(ModalApproveSuccess,item.Invoicingname,item.Billno,item.Orderaccount,item.Salegroup)">
                                View
                            </button><!-- data-target="#ModalApprovesuccess"  (click) ="openModalsuccess(true,'',true,item.Invoicingname,item.Billno)"-->
                        </td>
                    </ng-container>
                </tr>
      
                <tr class="text-sm-left">
                    <td class="text-sm-right font-weight-normal" colspan="4">มูลค่ารวม</td>
                    <td class="text-sm-right font-weight-normal">{{ productprice | number:'1.2-2' }}</td>
                    <td class="text-sm-right font-weight-normal">{{ sumvat | number:'1.2-2' }}</td>
                    <td class="text-sm-right font-weight-normal">{{ sumprice | number:'1.2-2' }}</td>
                    <th class="text-sm-center font-weight-light"></th>
                    <th class="text-sm-center font-weight-light" *ngIf="!searchbtn "></th>
                    <th class="text-sm-center font-weight-light" *ngIf="testclose"></th>
                    <th class="text-sm-center font-weight-light" *ngIf="!testclose"></th>
                </tr>

            </tbody>
        </table>
    </div>
</section>

<!--wait salesid,orderaccount,invoicingname,invoiceid,billingnotesdate,-->
<!--<div class="modal fade" id="Modalwait" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg" role="document">
      <div class="modal-content">
          <div class="modal-header">
              <h5 class="modal-title" id="exampleModalLabel" style="color: red;" >BillNo : {{ showbillno }} </h5>
              <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                  <span aria-hidden="true" >&times;</span>
              </button>
          </div>
          <div class="modal-body" style="padding : 5px;"  >
                  <pagination-controls style="margin-bottom: 0px;" class="text-right ngx-pagination"  (pageChange)="p=$event"></pagination-controls>
                  <div style="height: 400px; overflow-y: auto;">
                       <table class="table table-hover table-bordered table-sm" style="margin-bottom: 0px" >
                          <thead>
                              <tr class="text-sm-center bg-light">
                                  <th class="text-sm-center font-weight-normal" scope="col"></th>
                                  <th class="text-sm-center font-weight-normal" scope="col">SO No</th>
                                  <th class="text-sm-center font-weight-normal"  scope="col">Invoice No</th>
                                  <th class="text-sm-center font-weight-normal" scope="col">Invoice Date</th>
                                  <th class="text-sm-center font-weight-normal" scope="col">Due Date</th>
                                  <th class="text-sm-center font-weight-normal" scope="">ชำระเงิน</th>
                                  <th class="text-sm-center font-weight-normal" scope="">มูลค่าสินค้า</th>
                                  <th class="text-sm-center font-weight-normal" scope="col">VAT</th>
                                  <th class="text-sm-center font-weight-normal" scope="">มูลค่ารวม</th>
                              
                                  <td class="text-sm-center font-weight-light"></td>
                              </tr>
                          </thead>
                          <tbody>
                              <tr *ngFor="let itembill of BillId | paginate: { itemsPerPage: 10, currentPage: p } " >
                                  <td class="text-sm-center font-weight-normal"  [ngStyle]="{'color':getColor2(itembill.remarkst)}" >{{itembill.noid +1 }}</td>
                                  <td class="text-sm-center font-weight-normal" [ngStyle]="{'color':getColor2(itembill.remarkst)}" style="width :400px;" >{{itembill.salesid}}</td>
                                  <td class="text-sm-center font-weight-normal" [ngStyle]="{'color':getColor2(itembill.remarkst)}" style="width :400px;" >{{itembill.invoiceid}}</td>
                                  <td class="text-sm-center font-weight-normal" [ngStyle]="{'color':getColor2(itembill.remarkst)}" >{{itembill.invoicedate | date:'dd/MM/yyyy'}}</td>
                                  <td class="text-sm-center font-weight-normal" [ngStyle]="{'color':getColor2(itembill.remarkst)}" >{{itembill.duedate | date:'dd/MM/yyyy'}}</td>
                                  <td class="text-sm-center font-weight-normal" [ngStyle]="{'color':getColor2(itembill.remarkst)}" >{{itembill.payment}}</td>
                                  <td class="text-sm-right font-weight-normal" [ngStyle]="{'color':getColor2(itembill.remarkst)}" style="text-align: right;">{{itembill.salesbalance | number:'1.2-2'}}</td>
                                  <td class="text-sm-right font-weight-normal" [ngStyle]="{'color':getColor2(itembill.remarkst)}" style="text-align: right;">{{itembill.sumtax | number:'1.2-2'}}</td>
                                  <td class="text-sm-right font-weight-normal" [ngStyle]="{'color':getColor2(itembill.remarkst)}" style="text-align: right;">{{itembill.invoiceamount | number:'1.2-2'}}</td>                  
                                  <td class="text-center bg-light">
                                          <button class="btn btn-link font-weight-light" style="padding: 0pt"
                                          data-toggle="modal" aria-expanded="true" data-target="#ModalViewIMG"
                                           (click)="openModalBill(false,'',false,itembill.invoiceid,
                                                                                  itembill.orderaccount,
                                                                                  itembill.salesid,
                                                                                  itembill.invoicename,
                                                                                  itembill.invoicedate,
                                                                                  itembill.duedate, 
                                                                                  itembill.delivery,
                                                                                  itembill.salesbalance,
                                                                                  itembill.sumtax,
                                                                                  itembill.invoiceamount,
                                                                                  itembill.saleman,
                                                                                  itembill.remark,
                                                                                  itembill.urlimg,

                                                                                  )"
                                           aria-controls="collapseOne">
                                               {{itembill.numrun}}
                                           </button>
                                  </td>
                              </tr>    
                          </tbody> 
                     
                  </table>
                  </div>
          </div>
          
          <div class="modal-footer" align="right" style="padding : 3px;" >
              <div class="alert alert-success" style="width :400px; top: 7px;" role="alert">
                      มูลค่าสินค้า : {{productpricesuccess | number:'1.2-2' }}
              </div>
              <div class="alert alert-success" style="width :300px; top: 7px;" role="alert">
                      มูลค่าภาษี : {{sumvatsuccess | number:'1.2-2' }}
              </div>
              <div class="alert alert-success" style="width :400px; top: 7px;" role="alert">
                      มูลค่ารวม : {{sumpricesuccess | number:'1.2-2' }}
              </div>
                <button type="button" id="btnClose" class="btn btn-danger" data-dismiss="modal" ><i class="fa fa-times fa-fw"></i> ปิด</button>
          </div>
    

      </div>
  </div>
</div>-->
<!--endwait-->

<div id="mdlSample" class="modal fade" role="dialog" [ngStyle]="{'display': mdlSampleIsOpen ? 'block' : 'none', 'opacity': 1}">
    <div class="modal-dialog modal-md">
        <div class="modal-content">
            <div class="modal-header colhaederal">
                <h4 class="modal-title">Report</h4>
            </div>
            <div class="modal-body">{{alt}}</div>
            <div class="modal-footer" align="right">
                <button type="button" id="btnClose" class="btn btn-danger" (click)="closemodel(false)"><i class="fa fa-times fa-fw"></i>
                    ปิด</button></div>
        </div>
    </div>
</div>

<!--<div id="mdlSample" class="modal fade" role="dialog" [ngStyle]="{'display': mdlSampleIsOpenApprove ? 'block' : 'none', 'opacity': 1}">
        <div class="modal-dialog modal-lg">
        <div class="modal-content">
        <div class="modal-header colhaederal">
        <h4 class="modal-title">Approve Invoice </h4>
        </div>
        <div class="modal-body"  >{{altApprove}}</div>
        <table class="table table-hover table-bordered table-sm" >
            <thead>
                <tr class="text-sm-center bg-light">
                    <th class="font-weight-light" scope="col">Item</th>
                    <th class="font-weight-light" scope="col">SO No</th>
                    <th class="font-weight-light" scope="col">Invoice No</th>
                    <th class="font-weight-light" scope="col">Invoice Date</th>
                    <th class="font-weight-light" scope="col">Due Date</th>
                    <th class="font-weight-light" scope="col">เงื่อนไขการชำระ</th>
                    <th class="font-weight-light" scope="col">มูลค่าสินค้า</th>
                    <th class="font-weight-light" scope="col">VAT</th>
                    <th class="font-weight-light" scope="col">มูลค่ารวม</th>
                    <th class="font-weight-light" scope="col" class="text-sm-center" width="30px">
                        <input type="checkbox" [checked]="testcheck"  (click)="selectAll($event.target.checked);">
                    </th>
                </tr>
            </thead>
            <tbody>
                <tr *ngFor="let itembill of BillId; let i=index" >
                    <th class="text-sm-center font-weight-light">{{i+1}}</th>
                    <th class="text-sm-center font-weight-light">{{itembill.salesid}}</th>
                    <td class="text-sm-center font-weight-light">{{itembill.invoiceid}}</td>
                    <td class="text-sm-center font-weight-light">{{itembill.invoicedate | date:'yyyy/MM/dd'}}</td>
                    <td class="text-sm-center font-weight-light">{{itembill.duedate | date:'yyyy/MM/dd'}}</td>
                    <td class="text-sm-right font-weight-light">{{itembill.payment}}</td>
                    <td class="text-sm-right font-weight-light">{{itembill.salesbalance | number:'1.2-2'}}</td>
                    <th class="text-sm-right font-weight-light">{{itembill.sumtax | number:'1.2-2'}}</th>
                    <th class="text-sm-right font-weight-light">{{itembill.invoiceamount | number:'1.2-2'}}</th>
                    <td class="text-center">
                        <input type="checkbox"  [checked]="itembill.check" (click)="checkIfAllSelected($event.target.checked,i);" >
                    </td>
                </tr>
            </tbody>
        </table>
        
        <div class="modal-footer" align="right">
                   
                    <button type="button"   class="btn btn-primary" (click)="onUpload()" >Upload</button>
                    <button type="button"  id="btnClose" class="btn btn-danger" (click)="closemodelapprove(false)"><i class="fa fa-times fa-fw"></i> ปิด</button></div>
        </div>
        </div>
        </div> -->

<div class="modal fade" id="ModalApprove" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="exampleModalLabel" style="color: red;">BillNo : {{ showbillno }} </h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body" style="padding : 5px;">
                <pagination-controls style="margin-bottom: 0px;" class="text-right ngx-pagination" (pageChange)="p=$event"></pagination-controls>
                <div style="height: 400px; overflow-y: auto;">
                    <table class="table table-hover table-bordered table-sm" style="margin-bottom: 0px">
                        <thead>
                            <tr class="text-sm-center bg-light">
                                <th class="text-sm-center font-weight-normal" scope="col"></th>
                                <th class="text-sm-center font-weight-normal" scope="col">SO No</th>
                                <th class="text-sm-center font-weight-normal" scope="col">Invoice No</th>
                                <th class="text-sm-center font-weight-normal" scope="col">Invoice Date</th>
                                <th class="text-sm-center font-weight-normal" scope="col">Due Date</th>
                                <th class="text-sm-center font-weight-normal" scope="">ชำระเงิน</th>
                                <th class="text-sm-center font-weight-normal" scope="">มูลค่าสินค้า</th>
                                <th class="text-sm-center font-weight-normal" scope="col">VAT</th>
                                <th class="text-sm-center font-weight-normal" scope="">มูลค่ารวม</th>
                                <th class="font-weight-light" scope="col" class="text-sm-center" width="30px">
                                    <!-- [(ngModel)]="selectedAll"-->
                                    <input type="checkbox" [(ngModel)]="testcheck" (click)="selectAll($event.target.checked);">
                                </th>
                                <td class="text-sm-center font-weight-light"></td>
                            </tr>
                        </thead>
                        <tbody>
                            <tr *ngFor="let itembill of BillId | paginate: { itemsPerPage: 10, currentPage: p } ">
                                <td class="text-sm-center font-weight-normal" [ngStyle]="{'color':getColor2(itembill.remarkst)}">{{itembill.noid
                                    +1 }}</td>
                                <td class="text-sm-center font-weight-normal" [ngStyle]="{'color':getColor2(itembill.remarkst)}" style="width :400px;">{{itembill.salesid}}</td>
                                <td class="text-sm-center font-weight-normal" [ngStyle]="{'color':getColor2(itembill.remarkst)}" style="width :400px;">{{itembill.invoiceid}}</td>
                                <td class="text-sm-center font-weight-normal" [ngStyle]="{'color':getColor2(itembill.remarkst)}">{{itembill.invoicedate
                                    | date:'dd/MM/yyyy'}}</td>
                                <td class="text-sm-center font-weight-normal" [ngStyle]="{'color':getColor2(itembill.remarkst)}">{{itembill.duedate
                                    | date:'dd/MM/yyyy'}}</td>
                                <td class="text-sm-center font-weight-normal" [ngStyle]="{'color':getColor2(itembill.remarkst)}">{{itembill.payment}}</td>
                                <td class="text-sm-right font-weight-normal" [ngStyle]="{'color':getColor2(itembill.remarkst)}" style="text-align: right;">{{itembill.salesbalance
                                    | number:'1.2-2'}}</td>
                                <td class="text-sm-right font-weight-normal" [ngStyle]="{'color':getColor2(itembill.remarkst)}" style="text-align: right;">{{itembill.sumtax
                                    | number:'1.2-2'}}</td>
                                <td class="text-sm-right font-weight-normal" [ngStyle]="{'color':getColor2(itembill.remarkst)}" style="text-align: right;">{{itembill.invoiceamount
                                    | number:'1.2-2'}}</td>
                                <td class="text-center">
                                    <input type="checkbox" [checked]="itembill.check" (click)="checkIfAllSelected($event.target.checked,itembill.noid);">
                                </td>
                                <td class="text-center bg-light">
                                    <button class="btn btn-link font-weight-light" style="padding: 0pt" data-toggle="modal" aria-expanded="true" data-target="#ModalViewIMG"
                                        (click)="openModalBill(false,'',false,itembill.invoiceid,
                                                                                                itembill.orderaccount,
                                                                                                itembill.salesid,
                                                                                                itembill.invoicename,
                                                                                                itembill.invoicedate,
                                                                                                itembill.duedate, 
                                                                                                itembill.delivery,
                                                                                                itembill.salesbalance,
                                                                                                itembill.sumtax,
                                                                                                itembill.invoiceamount,
                                                                                                itembill.saleman,
                                                                                                itembill.remark,
                                                                                                itembill.urlimg,
                                                                                                itembill.imgbookbank
                                                                                                )" aria-controls="collapseOne">
                                        {{itembill.numrun}}
                                    </button>
                                </td>
                            </tr>
                        </tbody>
                        <!--   <thead>
                                        <tr class="text-sm-left">
                                                <th class="text-sm-center font-weight-light"></th>
                                                <th class="text-sm-center font-weight-light"></th>
                                                <th class="text-sm-center font-weight-light"></th>
                                                <th class="text-sm-center font-weight-light"></th>
                                                <th class="text-sm-center font-weight-light"></th>
                                                <th class="text-sm-center font-weight-light">มูลค่ารวม</th>
                                                <th class="text-sm-right font-weight-normal">{{ trueproductprice | number:'1.2-2' }}</th>
                                                <th class="text-sm-right font-weight-normal"></th>
                                                <th class="text-sm-right font-weight-normal"></th>
                                                <th class="text-sm-center font-weight-normal"></th>
                                                <th class="text-sm-center font-weight-normal"></th>
                                        </tr>
                                        </thead>-->
                    </table>
                </div>
                <div>
                    <div class="alert alert-success text-right" style="margin-bottom: 0px;" role="alert">
                        มูลค่ารวม : {{ trueproductprice | number:'1.2-2' }} | Vat : {{ truesumvat | number:'1.2-2' }} | ราคาสุทธิ : {{ truesumprice
                        | number:'1.2-2' }}
                    </div>
                </div>
                <div class="collapse" style="margin-top: 5px;" id="collapseExample">

                </div>
            </div>
            <div class="modal-footer" style="padding: 3px;">
                <div class="alert alert-success" style="width :200px; top: 7px;" role="alert">
                    รายที่เลือกทั้งหมด : {{ total }}
                </div>
                <button class="btn btn-primary" data-toggle="modal" aria-expanded="true" data-target="#Modalremark">
                    Comment
                </button>
                <button *ngIf="SearchComplete_ " type="button" [disabled]="total==0" class="btn btn-primary" data-dismiss="modal" aria-expanded="false"
                    (click)="onUpload()">Approve</button>
                <button *ngIf="SearchCompleteED_" type="button" [disabled]="total==0" class="btn btn-primary" data-dismiss="modal" aria-expanded="false"
                    (click)="onRevert()">Revert</button>
                <button type="button" id="btnClose" class="btn btn-danger" data-dismiss="modal" aria-expanded="false"><i
                        class="fa fa-times fa-fw"></i>Close</button>

            </div>
        </div>
    </div>
</div>

<!-- <div class="modal fade" id="Modalremark" >
                    <div class="modal-dialog modal-lg"  role="document" >
                            <div class="modal-content">
                                <div class="modal-body" style="padding: 5px;">{{altBill}}
                                        <div class="card card-body" style="padding : 5px;" >
                                                <div class="form-group">
                                                <label for="comment">Comment:</label>
                                                <textarea class="form-control"  [(ngModel)]="Remark"  rows="5" id="comment"></textarea>
                                                </div>
                                                <div *ngIf="SearchComplete_ ">
                                                <button type="button" [disabled]="total==0 || Remark==''" class="btn btn-primary" (click)="addremark()" data-dismiss="modal" aria-expanded="false" >บันทึกรายละเอียดแบบไม่ Approve</button>
                                                </div>
                                                <div *ngIf="SearchCompleteED_ ">
                                                  <button type="button" [disabled]="total==0 || Remark==''" class="btn btn-primary" (click)="addremarkRevert()"  data-dismiss="modal" aria-expanded="false" >บันทึกรายละเอียดแบบไม่ Revert</button>
                                                  </div>
                                        </div>
                                </div>     
                            </div>
                    </div>  
        </div> -->

<div class="modal fade" id="ModalViewIMG">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-body" style="padding: 5px;">{{altBill}}
                <ngb-tabset>
                    <ngb-tab>
                        <ng-template ngbTabTitle target="_blank"><b>Images Billno</b></ng-template>
                        <ng-template ngbTabContent>
                            <img src="{{ImageBillno}}" style=" margin: 5px; width: 99%; height: 500px;" />
                        </ng-template>
                    </ngb-tab>
                    <ngb-tab>
                        <ng-template ngbTabTitle target="_blank"><b>Images BookBank</b></ng-template>
                        <ng-template ngbTabContent>
                            <img src="{{ImageBookbank}}" style=" margin: 5px; width: 99%; height: 500px;" />
                        </ng-template>
                    </ngb-tab>
                    <ngb-tab>
                        <ng-template ngbTabTitle target="_blank" href="localhost:4200/assets/img/default-image.png"><b>Detail
                                INV
                            </b></ng-template>
                        <ng-template ngbTabContent>
                            <div style="margin: 5px; height: 500px;">
                                <table class="table table-hover table-bordered table-sm">
                                    <thead>
                                        <tr>
                                            <th style="width : 140px;" scope="col"></th>
                                            <th scope="col">detail</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <th>Sales Id</th>
                                            <td class="text-sm-left font-weight-normal">{{ SalesId }}</td>
                                        </tr>
                                        <tr>
                                            <th>SO No</th>
                                            <td class="text-sm-left font-weight-normal">{{ SONo }}</td>
                                        </tr>
                                        <tr>
                                            <th>Invoice No</th>
                                            <td class="text-sm-left font-weight-normal">{{ InvoiceNo }}</td>
                                        </tr>
                                        <tr>
                                            <th>OrderAccount</th>
                                            <td class="text-sm-left font-weight-normal">{{ OrderAccount }}</td>
                                        </tr>
                                        <tr>
                                            <th>Invoicingname</th>
                                            <td class="text-sm-left font-weight-normal">{{ Invoicingname }}</td>
                                        </tr>
                                        <tr>
                                            <th>Invoice Date</th>
                                            <td class="text-sm-left font-weight-normal">{{ InvoiceDate | date:'dd/MM/yyyy'
                                                }}
                                            </td>
                                        </tr>
                                        <tr>
                                            <th>Invoice DueDate</th>
                                            <td class="text-sm-left font-weight-normal">{{ DueDate | date:'dd/MM/yyyy' }}</td>
                                        </tr>
                                        <tr>
                                            <th>Deliveryname</th>
                                            <td class="text-sm-left font-weight-normal">{{ Deliveryname }}</td>
                                        </tr>
                                        <tr>
                                            <th>มูลค่าสินค้า</th>
                                            <td class="text-sm-left font-weight-normal">{{ productsun | number:'1.2-2' }}</td>
                                        </tr>
                                        <tr>
                                            <th>VAT</th>
                                            <td class="text-sm-left font-weight-normal">{{ VAT | number:'1.2-2' }}</td>
                                        </tr>
                                        <tr>
                                            <th>มูลค่ารวม</th>
                                            <td class="text-sm-left font-weight-normal">{{ suntotal | number:'1.2-2' }}</td>
                                        </tr>
                                        <tr>
                                            <th>Comment</th>
                                            <td class="text-sm-left font-weight-normal">
                                                {{ remarkshow }}
                                            </td>
                                        </tr>

                                    </tbody>
                                </table>


                            </div>
                        </ng-template>
                    </ngb-tab>
                </ngb-tabset>
            </div>
            <div class="modal-footer" style="padding: 5px;" align="right">
                <button type="button" id="btnClose" class="btn btn-danger" data-dismiss="modal" aria-expanded="false" (click)="closemodelBill(false)"><i
                        class="fa fa-times fa-fw"></i> ปิด</button></div>
        </div>
    </div>
</div>





<div id="mdlSample" class="modal fade" role="dialog" [ngStyle]="{'display': mdlSampleIsOpen ? 'block' : 'none', 'opacity': 1}">
    <div class="modal-dialog modal-md">
        <div class="modal-content">
            <div class="modal-header colhaederal">
                <h4 class="modal-title">Report</h4>
            </div>
            <div class="modal-body">{{alt}}</div>
            <div class="modal-footer" align="right">
                <button type="button" id="btnClose" class="btn btn-danger" (click)="closemodel(false)"><i class="fa fa-times fa-fw"></i>
                    ปิด</button></div>
        </div>
    </div>
</div>




<div id="mdlSample" class="modal fade" role="dialog" [ngStyle]="{'display': mdlSampleIsOpen2 ? 'block' : 'none', 'opacity': 1}">
    <div class="modal-dialog modal-md">
        <div class="modal-content">
            <div class="modal-header colhaederal">
                <h4 class="modal-title">Report</h4>
            </div>
            <div class="modal-body">{{alt2}}</div>
            <div class="modal-footer" align="right">
                <button type="button" id="btnClose" class="btn btn-danger" (click)="closemodel2(false)"><i class="fa fa-times fa-fw"></i>
                    ปิด</button></div>
        </div>
    </div>
</div>
<div id="mdlSample" class="modal fade" role="dialog" [ngStyle]="{'display': mdlSampleIsOpen3 ? 'block' : 'none', 'opacity': 1}">
    <div class="modal-dialog modal-md">
        <div class="modal-content">
            <div class="modal-header colhaederal">
                <h4 class="modal-title">Report</h4>
            </div>
            <div class="modal-body">{{alt3}} {{ load }}
                <br>
                <!--  <div class="progress">
                                <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" aria-valuenow="75" aria-valuemin="0" aria-valuemax="100" style="width: 75%"></div>
                              </div>-->
            </div>
            <div class="modal-footer" align="right">
                <button type="button" id="btnClose" class="btn btn-danger" disabled><i class="fa fa-times fa-fw"></i> ปิด</button></div>
        </div>
    </div>
</div>









<div class="modal fade" id="ModalApprovesuccess" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="exampleModalLabel" style="color: red;">ApproveSuccess : {{ showbillno }} </h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body" style="padding: 5px;">
                <div class="form-row" style="margin-bottom: 5px; margin-left: 10px; margin-right: 0px;">

                    <div class="col-md-3">
                        <input id="fromdate " class="form-control form-control-sm " [(ngModel)]="dateinvid" type="text" placeholder="ค้นหาเลข INV">
                    </div>
                    <!--  <div class="col-md-2 mb-2">
                                    <button class="btn btn-primary btn-sm font-weight-light" (click)="getIdBillSuccessINV()" type="submit">Search</button>
                                      </div>        -->
                </div>


                <pagination-controls style="margin-bottom: 0px;" class="text-right ngx-pagination" (pageChange)="p=$event"></pagination-controls>
                <div style="overflow-y: auto;">
                    <table id="example" class="table table-hover table-bordered table-sm" cellspacing="0">
                        <thead>
                            <tr>
                                <th class="text-sm-center font-weight-normal" scope="col"></th>
                                <th class="text-sm-center font-weight-normal" scope="col">SO No</th>
                                <th class="text-sm-center font-weight-normal" scope="col">Invoice No</th>
                                <th class="text-sm-center font-weight-normal" scope="col">Invoice Date</th>
                                <th class="text-sm-center font-weight-normal" scope="col">Due Date</th>
                                <th class="text-sm-center font-weight-normal" scope="col">มูลค่าสินค้า</th>
                                <th class="text-sm-center font-weight-normal" scope="col">VAT</th>
                                <th class="text-sm-center font-weight-normal" scope="col">มูลค่ารวม</th>
                                <th class="text-sm-center font-weight-normal" scope="col"></th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr *ngFor="let item of BillId | paginate: { itemsPerPage: 10, currentPage: p }">
                                <td class="text-sm-center font-weight-normal" [ngStyle]="{'color':getColor2(item.remarkst)}">{{item.noid
                                    +1 }}</td>
                                <td class="text-sm-center font-weight-normal" [ngStyle]="{'color':getColor2(item.remarkst)}">{{item.salesid}}</td>
                                <td class="text-sm-center font-weight-normal" [ngStyle]="{'color':getColor2(item.remarkst)}">{{item.invoiceid}}</td>
                                <th class="text-sm-center font-weight-normal" [ngStyle]="{'color':getColor2(item.remarkst)}">{{item.invoicedate|
                                    date:'dd/MM/yyyy' }}</th>
                                <th class="text-sm-center font-weight-normal" [ngStyle]="{'color':getColor2(item.remarkst)}">{{item.duedate
                                    | date:'dd/MM/yyyy' }}</th>
                                <td class="text-sm-right font-weight-normal" [ngStyle]="{'color':getColor2(item.remarkst)}" style="text-align: right;">{{item.salesbalance
                                    | number:'1.2-2' }}</td>
                                <td class="text-sm-right font-weight-normal" [ngStyle]="{'color':getColor2(item.remarkst)}" style="text-align: right;">{{item.sumtax
                                    | number:'1.2-2' }}</td>
                                <td class="text-sm-right font-weight-normal" [ngStyle]="{'color':getColor2(item.remarkst)}" style="text-align: right;">{{item.invoiceamount
                                    | number:'1.2-2' }}</td>
                                <td class="text-sm-center font-weight-normal">
                                    <button class="btn btn-link font-weight-light" style="padding: 0pt" data-toggle="modal" aria-expanded="true" data-target="#ModalApprovesuccessIMG"
                                        (click)="openModalimg(false,'',false,item.nameimg,item.invoiceid,
                                                item.orderaccount,
                                                item.salesid,
                                                item.invoicename,
                                                item.invoicedate,
                                                item.duedate, 
                                                item.delivery,
                                                item.salesbalance,
                                                item.sumtax,
                                                item.invoiceamount,
                                                item.saleman,
                                                item.remark)">
                                        {{item.numrun}}
                                    </button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="modal-footer" align="right" style="padding : 3px;">
                <div class="alert alert-success" style="width :400px; top: 7px;" role="alert">
                    มูลค่าสินค้า : {{productpricesuccess | number:'1.2-2' }}
                </div>
                <div class="alert alert-success" style="width :300px; top: 7px;" role="alert">
                    มูลค่าภาษี : {{sumvatsuccess | number:'1.2-2' }}
                </div>
                <div class="alert alert-success" style="width :400px; top: 7px;" role="alert">
                    มูลค่ารวม : {{sumpricesuccess | number:'1.2-2' }}
                </div>
                <button type="button" id="btnClose" class="btn btn-danger" data-dismiss="modal"><i class="fa fa-times fa-fw"></i>
                    ปิด</button>
            </div>
        </div>
    </div>
</div>


<div class="modal fade" id="ModalApprovesuccessIMG" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-body" style="padding: 5px;">
                <ngb-tabset>
                    <ngb-tab>
                        <ng-template ngbTabTitle target="_blank" href="localhost:4200/assets/img/default-image.png"><b style="color: red;">Images
                                : {{altimg}}</b></ng-template>
                        <ng-template ngbTabContent>
                            <img src="{{ImageBillno}}" style=" margin: 5px; width: 99%; height: 500px;" />
                        </ng-template>
                    </ngb-tab>
                    <ngb-tab>
                        <ng-template ngbTabTitle target="_blank" href="localhost:4200/assets/img/default-image.png"><b style="color: red;">Detail
                                INV
                            </b></ng-template>
                        <ng-template ngbTabContent>
                            <div style="margin: 5px; height: 500px;">
                                <table class="table table-hover table-bordered table-sm">
                                    <thead>
                                        <tr>
                                            <th style="width : 140px;" scope="col"></th>
                                            <th scope="col">detail</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <th>Sales Id</th>
                                            <td class="text-sm-left font-weight-normal">{{ SalesId }}</td>
                                        </tr>
                                        <tr>
                                            <th>SO No</th>
                                            <td class="text-sm-left font-weight-normal">{{ SONo }}</td>
                                        </tr>
                                        <tr>
                                            <th>Invoice No</th>
                                            <td class="text-sm-left font-weight-normal">{{ InvoiceNo }}</td>
                                        </tr>
                                        <tr>
                                            <th>OrderAccount</th>
                                            <td class="text-sm-left font-weight-normal">{{ OrderAccount }}</td>
                                        </tr>
                                        <tr>
                                            <th>Invoicingname</th>
                                            <td class="text-sm-left font-weight-normal">{{ Invoicingname }}</td>
                                        </tr>
                                        <tr>
                                            <th>Invoice Date</th>
                                            <td class="text-sm-left font-weight-normal">{{ InvoiceDate | date:'dd/MM/yyyy'
                                                }}
                                            </td>
                                        </tr>
                                        <tr>
                                            <th>Invoice DueDate</th>
                                            <td class="text-sm-left font-weight-normal">{{ DueDate | date:'dd/MM/yyyy' }}</td>
                                        </tr>
                                        <tr>
                                            <th>Deliveryname</th>
                                            <td class="text-sm-left font-weight-normal">{{ Deliveryname }}</td>
                                        </tr>
                                        <tr>
                                            <th>มูลค่าสินค้า</th>
                                            <td class="text-sm-left font-weight-normal">{{ productsun | number:'1.2-2' }}</td>
                                        </tr>
                                        <tr>
                                            <th>VAT</th>
                                            <td class="text-sm-left font-weight-normal">{{ VAT | number:'1.2-2' }}</td>
                                        </tr>
                                        <tr>
                                            <th>มูลค่ารวม</th>
                                            <td class="text-sm-left font-weight-normal">{{ suntotal | number:'1.2-2' }}</td>
                                        </tr>
                                        <tr>
                                            <th>Comment</th>
                                            <td class="text-sm-left font-weight-normal">
                                                {{ remarkshow }}
                                            </td>
                                        </tr>

                                    </tbody>
                                </table>


                            </div>
                        </ng-template>
                    </ngb-tab>

                </ngb-tabset>
            </div>
            <div class="modal-footer" align="right" style="padding:3px;">
                <button type="button" id="btnClose" class="btn btn-danger" data-dismiss="modal"><i class="fa fa-times fa-fw"></i>
                    ปิด</button>
            </div>
        </div>
    </div>

    <!--   <div id="mdlSample" class="modal fade" role="dialog" [ngStyle]="{'display': mdlSampleIsOpenimg ? 'block' : 'none', 'opacity': 1}">
                        <div class="modal-dialog modal-lg" role="document">
                            <div class="modal-content" style="top: 30px;" >
                            <div class="modal-body" style="padding: 5px;">
                                <ngb-tabset>
                                    <ngb-tab>
                                      <ng-template ngbTabTitle target ="_blank" href="localhost:4200/assets/img/default-image.png"  ><b style="color: red;">Images : {{altimg}}</b></ng-template>
                                      <ng-template ngbTabContent >
                                            <img src="{{ImageBillno}}" style=" margin: 5px; width: 99%; height: 500px;"/>
                                       </ng-template>
                                    </ngb-tab>
                                    <ngb-tab>
                                        <ng-template ngbTabTitle target ="_blank" href="localhost:4200/assets/img/default-image.png"  ><b style="color: red;">Detail INV</b></ng-template>
                                        <ng-template ngbTabContent  >
                                             <div style="margin: 5px; height: 500px;" >
                                                <table class="table table-hover table-bordered table-sm">
                                                    <thead>
                                                      <tr>
                                                        <th style="width : 140px;" scope="col"></th>
                                                        <th scope="col">detail</th>
                                                      </tr>
                                                    </thead>
                                                    <tbody>
                                                      <tr>
                                                        <th>Sales Id</th>
                                                        <td class="text-sm-left font-weight-normal" >{{ SalesId }}</td>
                                                      </tr>
                                                      <tr>
                                                        <th>SO No</th>
                                                        <td class="text-sm-left font-weight-normal" >{{ SONo }}</td>
                                                      </tr>
                                                      <tr>
                                                        <th>Invoice No</th>
                                                        <td class="text-sm-left font-weight-normal" >{{ InvoiceNo }}</td>
                                                      </tr>
                                                      <tr>
                                                        <th>OrderAccount</th>
                                                        <td class="text-sm-left font-weight-normal"  >{{ OrderAccount }}</td>
                                                      </tr>
                                                      <tr>
                                                        <th>Invoicingname</th>
                                                        <td class="text-sm-left font-weight-normal"  >{{ Invoicingname }}</td>
                                                      </tr>
                                                      <tr>
                                                        <th>Invoice Date</th>
                                                        <td class="text-sm-left font-weight-normal"  >{{ InvoiceDate | date:'yyyy/MM/dd' }}</td>
                                                      </tr>
                                                      <tr>
                                                        <th>Invoice DueDate</th>
                                                        <td class="text-sm-left font-weight-normal"  >{{ DueDate | date:'yyyy/MM/dd' }}</td>
                                                      </tr>
                                                      <tr>
                                                        <th>Deliveryname</th>
                                                        <td class="text-sm-left font-weight-normal"  >{{ Deliveryname }}</td>
                                                      </tr>
                                                      <tr>
                                                        <th>มูลค่าสินค้า</th>
                                                        <td class="text-sm-left font-weight-normal"  >{{ productsun | number:'1.2-2'  }}</td>
                                                      </tr>
                                                      <tr>
                                                        <th>VAT</th>
                                                        <td class="text-sm-left font-weight-normal"  >{{ VAT | number:'1.2-2'  }}</td>
                                                      </tr>
                                                      <tr>
                                                        <th>มูลค่ารวม</th>
                                                        <td class="text-sm-left font-weight-normal"  >{{ suntotal | number:'1.2-2'  }}</td>
                                                      </tr>
                                                      
                                                    </tbody>
                                                  </table>

                                              
                                             </div>
                                         </ng-template>
                                      </ngb-tab>
                                    
                                  </ngb-tabset>
                            </div>
                            
                        <div class="modal-footer" align="right" style="padding:3px;" >
                                   <button type="button" id="btnClose" class="btn btn-danger"  data-toggle="modal" aria-expanded="true"data-target="#ModalApprovesuccess" (click)="closemodelimg(false)"><i class="fa fa-times fa-fw"></i> ปิด</button>
                                </div>
                        </div>
                        </div>
                        </div>-->

    <ng-template #template>
        <div class="modal-header">
            <h4 class="modal-title pull-left">UploadPDF : {{showIDso}}</h4>
            <button type="button" class="close pull-right" aria-label="Close" (click)="modalRef.hide()">
                <span aria-hidden="true">&times;</span>
            </button>

        </div>
        <div class="modal-body">

            <div class="form-group" style="margin-bottom: 0rem">
                <label for="exampleFormControlFile1">Input file PDF</label>
                <input type="file" class="form-control-file" accept="application/pdf, image/*" (change)="handleFileInput($event.target.files,templateShow)"
                    id="exampleFormControlFile1">
            </div>
            <!--<div class="custom-file col-md-12" >
                                        <input type="file" class="custom-file-input" #pdf    id="inputGroupFile01" aria-describedby="inputGroupFileAddon01">
                                        <label class="custom-file-label" [(ngModel)]="textload" for="inputGroupFile01">{{ textload }} </label>
                                      </div>-->
        </div>
        <div class="modal-footer">
            <button type="button" [disabled]="selectedFile==null" class="btn btn-primary" (click)="onUpload(showIDso,templateShow,template)">Upload</button>
            <button type="button" (click)="modalRef.hide()" class="btn btn-secondary">Close</button>

        </div>
    </ng-template>

    <ng-template #templateShow>
        <div class="modal-body text-center">
            <P>{{textload}} </P><br>
            <button *ngIf="Btnimg" type="button" class="btn btn-default" (click)="confirmimg()">ปิด</button>
            <button *ngIf="btnPDF" type="button" class="btn btn-default" (click)="confirm()">ปิด</button>
            <button *ngIf="btnREpdf" type="button" class="btn btn-primary" (click)="decline(template)">ทำรายการใหม่</button>
        </div>
        <!---->
    </ng-template>

    <ng-template #templateViewDetail>
        <div class="modal-body" style=" padding: 3px; ">
            <ngb-tabset>
                <ngb-tab>
                    <ng-template ngbTabTitle target="_blank"><b>Images Billno</b></ng-template>
                    <ng-template ngbTabContent>
                        <img src="{{ImageBillno}}" style=" margin: 5px; width: 99%;" />
                    </ng-template>
                </ngb-tab>
                <ngb-tab *ngIf="CkimgBookbank">
                    <ng-template ngbTabTitle target="_blank"><b>Images BookBank</b></ng-template>
                    <ng-template ngbTabContent>
                        <img src="{{ImageBookbank}}" style=" margin: 5px; width: 99%;" />
                    </ng-template>
                </ngb-tab>

                <ngb-tab>
                    <ng-template ngbTabTitle target="_blank" href="localhost:4200/assets/img/default-image.png"><b>Detail
                            INV
                        </b></ng-template>
                    <ng-template ngbTabContent>
                        <div style="margin: 5px; height: 500px;">
                            <table class="table table-hover table-bordered table-sm">
                                <thead>
                                    <tr>
                                        <th style="width : 140px;" scope="col"></th>
                                        <th scope="col">detail</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <th>Sales Id</th>
                                        <td class="text-sm-left font-weight-normal">{{ SalesId }}</td>
                                    </tr>
                                    <tr>
                                        <th>SO No</th>
                                        <td class="text-sm-left font-weight-normal">{{ SONo }}</td>
                                    </tr>
                                    <tr>
                                        <th>Invoice No</th>
                                        <td class="text-sm-left font-weight-normal">{{ InvoiceNo }}</td>
                                    </tr>
                                    <tr>
                                        <th>OrderAccount</th>
                                        <td class="text-sm-left font-weight-normal">{{ OrderAccount }}</td>
                                    </tr>
                                    <tr>
                                        <th>Invoicingname</th>
                                        <td class="text-sm-left font-weight-normal">{{ Invoicingname }}</td>
                                    </tr>
                                    <tr>
                                        <th>Invoice Date</th>
                                        <td class="text-sm-left font-weight-normal">{{ InvoiceDate | date:'dd/MM/yyyy' }}</td>
                                    </tr>
                                    <tr>
                                        <th>Invoice DueDate</th>
                                        <td class="text-sm-left font-weight-normal">{{ DueDate | date:'dd/MM/yyyy' }}</td>
                                    </tr>
                                    <tr>
                                        <th>Deliveryname</th>
                                        <td class="text-sm-left font-weight-normal">{{ Deliveryname }}</td>
                                    </tr>
                                    <tr>
                                        <th>มูลค่าสินค้า</th>
                                        <td class="text-sm-left font-weight-normal">{{ productsun | number:'1.2-2' }}</td>
                                    </tr>
                                    <tr>
                                        <th>VAT</th>
                                        <td class="text-sm-left font-weight-normal">{{ VAT | number:'1.2-2' }}</td>
                                    </tr>
                                    <tr>
                                        <th>มูลค่ารวม</th>
                                        <td class="text-sm-left font-weight-normal">{{ suntotal | number:'1.2-2' }}</td>
                                    </tr>
                                    <tr>
                                        <th>Comment</th>
                                        <td class="text-sm-left font-weight-normal">
                                            {{ remarkshow }}
                                        </td>
                                    </tr>

                                </tbody>
                            </table>


                        </div>
                    </ng-template>
                </ngb-tab>
            </ngb-tabset>

        </div>
        <div class="modal-footer">
            <button type="button" (click)="modalRefviewDetail.hide()" class="btn btn-secondary">Close</button>
        </div>
    </ng-template>

    <ng-template #templateViewDetailApprove>
        <div class="modal-body" style=" padding: 3px; ">
            <ngb-tabset>
                <ngb-tab>
                    <ng-template ngbTabTitle target="_blank"><b>Images Billno</b></ng-template>
                    <ng-template ngbTabContent>
                        <img src="{{ImageBillno}}" style=" margin: 5px; width: 99%;" />
                    </ng-template>
                </ngb-tab>
                <ngb-tab>
                    <ng-template ngbTabTitle target="_blank"><b>Images BookBank</b></ng-template>
                    <ng-template ngbTabContent>
                        <img src="{{ImageBookbank}}" style=" margin: 5px; width: 99%;" />
                    </ng-template>
                </ngb-tab>

                <ngb-tab>
                    <ng-template ngbTabTitle target="_blank" href="localhost:4200/assets/img/default-image.png"><b>Detail
                            INV
                        </b></ng-template>
                    <ng-template ngbTabContent>
                        <div style="margin: 5px; height: 500px;">
                            <table class="table table-hover table-bordered table-sm">
                                <thead>
                                    <tr>
                                        <th style="width : 140px;" scope="col"></th>
                                        <th scope="col">detail</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <th>Sales Id</th>
                                        <td class="text-sm-left font-weight-normal">{{ SalesId }}</td>
                                    </tr>
                                    <tr>
                                        <th>SO No</th>
                                        <td class="text-sm-left font-weight-normal">{{ SONo }}</td>
                                    </tr>
                                    <tr>
                                        <th>Invoice No</th>
                                        <td class="text-sm-left font-weight-normal">{{ InvoiceNo }}</td>
                                    </tr>
                                    <tr>
                                        <th>OrderAccount</th>
                                        <td class="text-sm-left font-weight-normal">{{ OrderAccount }}</td>
                                    </tr>
                                    <tr>
                                        <th>Invoicingname</th>
                                        <td class="text-sm-left font-weight-normal">{{ Invoicingname }}</td>
                                    </tr>
                                    <tr>
                                        <th>Invoice Date</th>
                                        <td class="text-sm-left font-weight-normal">{{ InvoiceDate | date:'dd/MM/yyyy' }}</td>
                                    </tr>
                                    <tr>
                                        <th>Invoice DueDate</th>
                                        <td class="text-sm-left font-weight-normal">{{ DueDate | date:'dd/MM/yyyy' }}</td>
                                    </tr>
                                    <tr>
                                        <th>Deliveryname</th>
                                        <td class="text-sm-left font-weight-normal">{{ Deliveryname }}</td>
                                    </tr>
                                    <tr>
                                        <th>มูลค่าสินค้า</th>
                                        <td class="text-sm-left font-weight-normal">{{ productsun | number:'1.2-2' }}</td>
                                    </tr>
                                    <tr>
                                        <th>VAT</th>
                                        <td class="text-sm-left font-weight-normal">{{ VAT | number:'1.2-2' }}</td>
                                    </tr>
                                    <tr>
                                        <th>มูลค่ารวม</th>
                                        <td class="text-sm-left font-weight-normal">{{ suntotal | number:'1.2-2' }}</td>
                                    </tr>
                                    <tr>
                                        <th>Comment</th>
                                        <td class="text-sm-left font-weight-normal">
                                            {{ remarkshow }}
                                        </td>
                                    </tr>

                                </tbody>
                            </table>


                        </div>
                    </ng-template>
                </ngb-tab>
            </ngb-tabset>

        </div>
        <div class="modal-footer">
            <button type="button" (click)="modalRefviewDetail.hide()" class="btn btn-secondary">Close</button>
        </div>
    </ng-template>

    <ng-template #templateView>
        <div class="modal-header">
            <h4 class="modal-title pull-left"> Company : {{ showIDso }}</h4>
            <button type="button" class="close pull-right" aria-label="Close" (click)="modalRefupbookbank.hide()">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
        <div class="modal-body" style=" padding: 3px; ">
            <div class="col-md-3 mb-3 col-sm-6">
                <select [(ngModel)]="viewBookbank" (change)="searchviewBookbank()" class="custom-select custom-select-sm" name="" id="">
                    <option value="3">เลือกทุุกรายการ</option>
                    <option value="A">รอupbookbank</option>
                    <option value="B">รอ Approve</option>
                </select>
            </div>

            <div style="height: 400px; overflow-y: auto;">
                <table class="table table-hover table-bordered table-sm" style="margin-bottom: 0px">
                    <thead>
                        <tr class="text-sm-center bg-light">
                            <th class="text-sm-center font-weight-normal" scope="col"></th>
                            <th class="text-sm-center font-weight-normal" scope="col">SO No</th>
                            <th class="text-sm-center font-weight-normal" scope="col">Invoice No</th>
                            <th class="text-sm-center font-weight-normal" scope="col">Invoice Date</th>
                            <th class="text-sm-center font-weight-normal" scope="col">Due Date</th>
                            <th class="text-sm-center font-weight-normal" scope="">ชำระเงิน</th>
                            <th class="text-sm-center font-weight-normal" scope="">มูลค่าสินค้า</th>
                            <th class="text-sm-center font-weight-normal" scope="col">VAT</th>
                            <th class="text-sm-center font-weight-normal" scope="">มูลค่ารวม</th>
                            <th class="font-weight-light" scope="col" class="text-sm-center" width="30px">
                                <!-- [(ngModel)]="selectedAll"-->
                                <input type="checkbox" [(ngModel)]="testcheck" (click)="selectAllbookbank($event.target.checked);">
                            </th>
                            <td class="text-sm-center font-weight-light"></td>
                        </tr>
                    </thead>
                    <tbody>
                        <tr *ngFor="let itembill of Bookbank">
                            <td class="text-sm-center font-weight-normal" [ngStyle]="{'color':getColorbookbank(itembill.remarkst,itembill.imgbookbank)}">{{itembill.noid
                                +1 }}</td>
                            <td class="text-sm-center font-weight-normal" [ngStyle]="{'color':getColorbookbank(itembill.remarkst,itembill.imgbookbank)}"
                                style="width :400px;">{{itembill.salesid}}</td>
                            <td class="text-sm-center font-weight-normal" [ngStyle]="{'color':getColorbookbank(itembill.remarkst,itembill.imgbookbank)}"
                                style="width :400px;">{{itembill.invoiceid}}</td>
                            <td class="text-sm-center font-weight-normal" [ngStyle]="{'color':getColorbookbank(itembill.remarkst,itembill.imgbookbank)}">{{itembill.invoicedate
                                | date:'dd/MM/yyyy'}}</td>
                            <td class="text-sm-center font-weight-normal" [ngStyle]="{'color':getColorbookbank(itembill.remarkst,itembill.imgbookbank)}">{{itembill.duedate
                                | date:'dd/MM/yyyy'}}</td>
                            <td class="text-sm-center font-weight-normal" [ngStyle]="{'color':getColorbookbank(itembill.remarkst,itembill.imgbookbank)}">{{itembill.payment}}</td>

                            <td class="text-sm-right font-weight-normal" [ngStyle]="{'color':getColorbookbank(itembill.remarkst,itembill.imgbookbank)}"
                                style="text-align: right;">{{itembill.salesbalance | number:'1.2-2'}}</td>
                            <td class="text-sm-right font-weight-normal" [ngStyle]="{'color':getColorbookbank(itembill.remarkst,itembill.imgbookbank)}"
                                style="text-align: right;">{{itembill.sumtax | number:'1.2-2'}}</td>
                            <td class="text-sm-right font-weight-normal" [ngStyle]="{'color':getColorbookbank(itembill.remarkst,itembill.imgbookbank)}"
                                style="text-align: right;">{{itembill.invoiceamount | number:'1.2-2'}}</td>
                            <td class="text-center">
                                <input type="checkbox" [checked]="itembill.check" (click)="checkIfAllSelectedbookbank($event.target.checked,itembill.noid);">
                            </td>
                            <td class="text-center bg-light">
                                <button class="btn btn-link font-weight-light" style="padding: 0pt" (click)="openModalviewDetail(templateViewDetail,itembill.invoiceid,
                                                                                               itembill.orderaccount,
                                                                                               itembill.salesid,
                                                                                               itembill.invoicename,
                                                                                               itembill.invoicedate,
                                                                                               itembill.duedate, 
                                                                                               itembill.delivery,
                                                                                               itembill.salesbalance,
                                                                                               itembill.sumtax,
                                                                                               itembill.invoiceamount,
                                                                                               itembill.saleman,
                                                                                               itembill.remark,
                                                                                               itembill.urlimg,
                                                                                               itembill.imgbookbank
                                                                                               )" aria-controls="collapseOne">
                                    {{itembill.numrun}}
                                </button>
                            </td>
                        </tr>
                    </tbody>
                    <!--   <thead>
                                       <tr class="text-sm-left">
                                               <th class="text-sm-center font-weight-light"></th>
                                               <th class="text-sm-center font-weight-light"></th>
                                               <th class="text-sm-center font-weight-light"></th>
                                               <th class="text-sm-center font-weight-light"></th>
                                               <th class="text-sm-center font-weight-light"></th>
                                               <th class="text-sm-center font-weight-light">มูลค่ารวม</th>
                                               <th class="text-sm-right font-weight-normal">{{ trueproductprice | number:'1.2-2' }}</th>
                                               <th class="text-sm-right font-weight-normal"></th>
                                               <th class="text-sm-right font-weight-normal"></th>
                                               <th class="text-sm-center font-weight-normal"></th>
                                               <th class="text-sm-center font-weight-normal"></th>
                                       </tr>
                                       </thead>-->
                </table>
            </div>
            <div class="custom-file col-md-9" style="margin-bottom: 10px">
                <input type="file" class="custom-file-input" [(ngModel)]="Flielist" #Image accept="image/*" (change)="handleFileInput($event.target.files,templateShow)"
                    id="inputGroupFile01" aria-describedby="inputGroupFileAddon01">
                <label class="custom-file-label" [(ngModel)]="textload" for="inputGroupFile01">{{ textload }} </label>
            </div>
            <div id="collapseEvent" class="card card-block card-header" (collapsed)="collapsed()" (expanded)="expanded()" [collapse]="isCollapsed">
                <div class="card card-body">
                    <form #imageForm=ngForm style="text-align: center;">
                        <img [src]="imageUrl" style="width:100%;">
                    </form>
                </div>
            </div>
            <div>
                <div class="alert alert-success text-right" style="margin-bottom: 0px;" role="alert">
                    มูลค่ารวม : {{ trueproductprice | number:'1.2-2' }} | Vat : {{ truesumvat | number:'1.2-2' }} | ราคาสุทธิ : {{ truesumprice
                    | number:'1.2-2' }}
                </div>
            </div>
        </div>
        <div class="modal-footer">
            <button type="button" class="btn btn-primary" (click)="isCollapsed = !isCollapsed" [disabled]="selectedFile==null" [attr.aria-expanded]="!isCollapsed"
                aria-controls="collapseEvent">ViewBookbank
            </button>
            <button type="button" [disabled]="selectedFile==null || total==0" class="btn btn-primary" (click)="onUploadbookbank(templateShow,templateView)">UploadBookbank</button>
            <button type="button" (click)="endtemplateView()" class="btn btn-secondary">Close</button>
        </div>
    </ng-template>

    <ng-template #ModalApprove>
        <div class="modal-header">
            <h4 class="modal-title pull-left">BillNo : {{ showbillno }} </h4>
            <button type="button" class="close pull-right" aria-label="Close" (click)="ModalRefApprove.hide()">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
        <div class="modal-body" style=" padding: 3px; ">
            <pagination-controls style="margin-bottom: 0px;" class="text-right ngx-pagination" (pageChange)="p=$event"></pagination-controls>
            <div style="height: 400px; overflow-y: auto;">
                <table class="table table-hover table-bordered table-sm" style="margin-bottom: 0px">
                    <thead>
                        <tr class="text-sm-center bg-light">
                            <th class="text-sm-center font-weight-normal" scope="col"></th>
                            <th class="text-sm-center font-weight-normal" scope="col">SO No</th>
                            <th class="text-sm-center font-weight-normal" scope="col">Invoice No</th>
                            <th class="text-sm-center font-weight-normal" scope="col">Invoice Date</th>
                            <th class="text-sm-center font-weight-normal" scope="col">Due Date</th>
                            <th class="text-sm-center font-weight-normal" scope="">ชำระเงิน</th>
                            <th class="text-sm-center font-weight-normal" scope="">มูลค่าสินค้า</th>
                            <th class="text-sm-center font-weight-normal" scope="col">VAT</th>
                            <th class="text-sm-center font-weight-normal" scope="">มูลค่ารวม</th>
                            <th class="font-weight-light" scope="col" class="text-sm-center" width="30px">
                                <!-- [(ngModel)]="selectedAll"-->
                                <input type="checkbox" [(ngModel)]="testcheck" (click)="selectAll($event.target.checked);">
                            </th>
                            <td class="text-sm-center font-weight-light"></td>
                        </tr>
                    </thead>
                    <tbody>
                        <tr *ngFor="let itembill of BillId | paginate: { itemsPerPage: 10, currentPage: p } ">
                            <td class="text-sm-center font-weight-normal" [ngStyle]="{'color':getColor2(itembill.remarkst)}">{{itembill.noid
                                +1 }}</td>
                            <td class="text-sm-center font-weight-normal" [ngStyle]="{'color':getColor2(itembill.remarkst)}" style="width :400px;">{{itembill.salesid}}</td>
                            <td class="text-sm-center font-weight-normal" [ngStyle]="{'color':getColor2(itembill.remarkst)}" style="width :400px;">{{itembill.invoiceid}}</td>
                            <td class="text-sm-center font-weight-normal" [ngStyle]="{'color':getColor2(itembill.remarkst)}">{{itembill.invoicedate
                                | date:'dd/MM/yyyy'}}</td>
                            <td class="text-sm-center font-weight-normal" [ngStyle]="{'color':getColor2(itembill.remarkst)}">{{itembill.duedate
                                | date:'dd/MM/yyyy'}}</td>
                            <td class="text-sm-center font-weight-normal" [ngStyle]="{'color':getColor2(itembill.remarkst)}">{{itembill.payment}}</td>
                            <td class="text-sm-right font-weight-normal" [ngStyle]="{'color':getColor2(itembill.remarkst)}" style="text-align: right;">{{itembill.salesbalance
                                | number:'1.2-2'}}</td>
                            <td class="text-sm-right font-weight-normal" [ngStyle]="{'color':getColor2(itembill.remarkst)}" style="text-align: right;">{{itembill.sumtax
                                | number:'1.2-2'}}</td>
                            <td class="text-sm-right font-weight-normal" [ngStyle]="{'color':getColor2(itembill.remarkst)}" style="text-align: right;">{{itembill.invoiceamount
                                | number:'1.2-2'}}</td>
                            <td class="text-center">
                                <input type="checkbox" [checked]="itembill.check" (click)="checkIfAllSelected($event.target.checked,itembill.noid);">
                            </td>
                            <td class="text-center bg-light">
                                <button class="btn btn-link font-weight-light" style="padding: 0pt" (click)="openModalBillViewDetail(templateViewDetailApprove,itembill.invoiceid,
                                                                                                itembill.orderaccount,
                                                                                                itembill.salesid,
                                                                                                itembill.invoicename,
                                                                                                itembill.invoicedate,
                                                                                                itembill.duedate, 
                                                                                                itembill.delivery,
                                                                                                itembill.salesbalance,
                                                                                                itembill.sumtax,
                                                                                                itembill.invoiceamount,
                                                                                                itembill.saleman,
                                                                                                itembill.remark,
                                                                                                itembill.urlimg,
                                                                                                itembill.imgbookbank
                                                                                                )" aria-controls="collapseOne">
                                    <!-- {{itembill.numrun}}<br>-->
                                    {{itembill.numrun}}
                                </button>
                            </td>
                        </tr>
                    </tbody>
                </table>



            </div>
            <div class="alert alert-success text-right" style="margin-bottom: 0px;" role="alert">
                มูลค่ารวม : {{ trueproductprice | number:'1.2-2' }} | Vat : {{ truesumvat | number:'1.2-2' }} | ราคาสุทธิ : {{ truesumprice
                | number:'1.2-2' }}
            </div>
        </div>
        <div class="modal-footer" style="padding: 5px;">

            <!-- <button  *ngIf="SearchComplete_" type="button" [disabled]="total==0" class="btn btn-primary pull-left" (click)="onUpload(templateShow)" >Return BookBank</button>-->
            <div class="alert alert-success" style="width :200px; top: 7px;" role="alert">
                รายที่เลือกทั้งหมด : {{ total }}
            </div>
            <button class="btn btn-primary" (click)="Modalremark(ModalremarkNew)">
                Comment
            </button>
            <button *ngIf="SearchComplete_ &&  datalogin[0].id_group_user=='1860Administrator'" type="button" [disabled]="total==0" class="btn btn-primary" (click)="onUpload(templateShow)">Approve</button>
            <button *ngIf="SearchCompleteED_" type="button" [disabled]="total==0" class="btn btn-primary" (click)="onRevert(templateShow)">Revert</button>
            <button type="button" (click)="ModalRefApprove.hide()" class="btn btn-secondary">Close</button>
        </div>
    </ng-template>

    <ng-template #ModalremarkNew>
        <div class="modal-body" style="padding: 5px;">{{altBill}}
            <div class="card card-body" style="padding : 5px;">
                <div class="form-group">
                    <label for="comment">Comment:</label>
                    <textarea class="form-control" [(ngModel)]="Remark" rows="5" id="comment"></textarea>
                </div>
                <div *ngIf="SearchComplete_ ">
                    <button type="button" [disabled]="total==0 || Remark==''" class="btn btn-primary" (click)="addremark(templateShow)">บันทึกรายละเอียดแบบไม่
                        Approve
                    </button>
                </div>
                <div *ngIf="SearchCompleteED_ ">
                    <button type="button" [disabled]="total==0 || Remark==''" class="btn btn-primary" (click)="addremarkRevert()">บันทึกรายละเอียดแบบไม่
                        Revert
                    </button>
                </div>
                <!-- <button type="button" (click)="ModalremarkRef.hide()" class="btn btn-secondary">Close</button>-->
            </div>
        </div>
    </ng-template>

    <ng-template #ModalApproveSuccess>
        <div class="modal-header">
            <h5 class="modal-title pull-left">Success : {{ showbillno }} </h5>
            <button type="button" class="close pull-right" aria-label="Close" (click)="ModalRefApprove.hide()">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
        <div class="modal-body" style=" padding: 3px; ">
            <div class="form-row" style="margin-bottom: 5px; margin-left: 10px; margin-right: 0px;">

                <div class="col-md-3">
                    <input id="fromdate " class="form-control form-control-sm " [(ngModel)]="dateinvid" (change)="searchSuccess($event.target.value)"
                        (keyup)="searchSuccess($event.target.value)" type="text" placeholder="ค้นหาเลข INV">
                </div>
                <!--   <div class="col-md-2 mb-2">
                                                <button class="btn btn-primary btn-sm font-weight-light " (click)="getIdBillSuccessINV()" type="submit">Search</button>
                                                  </div>     -->
            </div>


            <pagination-controls style="margin-bottom: 0px;" class="text-right ngx-pagination" (pageChange)="p=$event"></pagination-controls>
            <div style="overflow-y: auto;">
                <table id="example" class="table table-hover table-bordered table-sm" cellspacing="0">
                    <thead>
                        <tr>
                            <th class="text-sm-center font-weight-normal" scope="col"></th>
                            <th class="text-sm-center font-weight-normal" scope="col">SO No</th>
                            <th nowrap class="text-sm-center font-weight-normal" scope="col">Invoice No</th>
                            <th nowrap class="text-sm-center font-weight-normal" scope="col">Invoice Date</th>
                            <th class="text-sm-center font-weight-normal" scope="col">Due Date</th>
                            <th class="text-sm-center font-weight-normal" scope="col">มูลค่าสินค้า</th>
                            <th class="text-sm-center font-weight-normal" scope="col">VAT</th>
                            <th class="text-sm-center font-weight-normal" scope="col">มูลค่ารวม</th>
                            <th class="text-sm-center font-weight-normal" scope="col"></th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr *ngFor="let item of INVSuccess | paginate: { itemsPerPage: 10, currentPage: p }">
                            <td class="text-sm-center font-weight-normal" [ngStyle]="{'color':getColor2(item.remarkst)}">{{item.noid
                                +1 }}</td>
                            <td nowrap class="text-sm-center font-weight-normal" [ngStyle]="{'color':getColor2(item.remarkst)}">{{item.salesid}}</td>
                            <td nowrap class="text-sm-center font-weight-normal" [ngStyle]="{'color':getColor2(item.remarkst)}">{{item.invoiceid}}</td>
                            <th nowrap class="text-sm-center font-weight-normal" [ngStyle]="{'color':getColor2(item.remarkst)}">{{item.invoicedate|
                                date:'dd/MM/yyyy' }}</th>
                            <th nowrap class="text-sm-center font-weight-normal" [ngStyle]="{'color':getColor2(item.remarkst)}">{{item.duedate
                                | date:'dd/MM/yyyy' }}</th>
                            <td class="text-sm-right font-weight-normal" [ngStyle]="{'color':getColor2(item.remarkst)}" style="text-align: right;">{{item.salesbalance
                                | number:'1.2-2' }}</td>
                            <td class="text-sm-right font-weight-normal" [ngStyle]="{'color':getColor2(item.remarkst)}" style="text-align: right;">{{item.sumtax
                                | number:'1.2-2' }}</td>
                            <td class="text-sm-right font-weight-normal" [ngStyle]="{'color':getColor2(item.remarkst)}" style="text-align: right;">{{item.invoiceamount
                                | number:'1.2-2' }}</td>
                            <td class="text-sm-center font-weight-normal">
                                <button class="btn btn-link font-weight-light" style="padding: 0pt" (click)="openModalimg(templateViewDetailApprove,item.nameimg,item.invoiceid,
                                                            item.orderaccount,
                                                            item.salesid,
                                                            item.invoicename,
                                                            item.invoicedate,
                                                            item.duedate, 
                                                            item.delivery,
                                                            item.salesbalance,
                                                            item.sumtax,
                                                            item.invoiceamount,
                                                            item.saleman,
                                                            item.remark,
                                                            item.imgbookbank)">
                                    {{item.numrun}}
                                </button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
        <div class="modal-footer" style="padding: 5px;">

            <button type="button" (click)="ModalRefApprove.hide()" class="btn btn-secondary">Close</button>
        </div>
    </ng-template>

    <ng-template #Modalwait>
        <div class="modal-header">
            <h4 class="modal-title pull-left">BillNo : {{ showbillno }} </h4>
            <button type="button" class="close pull-right" aria-label="Close" (click)="ModalRefApprove.hide()">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
        <div class="modal-body" style=" padding: 3px; ">
            <pagination-controls style="margin-bottom: 0px;" class="text-right ngx-pagination" (pageChange)="p=$event"></pagination-controls>
            <div style="height: 400px; overflow-y: auto;">
                <table class="table table-hover table-bordered table-sm" style="margin-bottom: 0px">
                    <thead>
                        <tr class="text-sm-center bg-light">
                            <th class="text-sm-center font-weight-normal" scope="col"></th>
                            <th class="text-sm-center font-weight-normal" scope="col">SO No</th>
                            <th class="text-sm-center font-weight-normal" scope="col">Invoice No</th>
                            <th class="text-sm-center font-weight-normal" scope="col">Invoice Date</th>
                            <th class="text-sm-center font-weight-normal" scope="col">Due Date</th>
                            <th class="text-sm-center font-weight-normal" scope="">ชำระเงิน</th>
                            <th class="text-sm-center font-weight-normal" scope="">มูลค่าสินค้า</th>
                            <th class="text-sm-center font-weight-normal" scope="col">VAT</th>
                            <th class="text-sm-center font-weight-normal" scope="">มูลค่ารวม</th>

                            <td class="text-sm-center font-weight-light"></td>
                        </tr>
                    </thead>
                    <tbody>
                        <tr *ngFor="let itembill of BillId | paginate: { itemsPerPage: 10, currentPage: p } ">
                            <td class="text-sm-center font-weight-normal" [ngStyle]="{'color':getColor2(itembill.remarkst)}">{{itembill.noid
                                +1 }}</td>
                            <td class="text-sm-center font-weight-normal" [ngStyle]="{'color':getColor2(itembill.remarkst)}" style="width :400px;">{{itembill.salesid}}</td>
                            <td class="text-sm-center font-weight-normal" [ngStyle]="{'color':getColor2(itembill.remarkst)}" style="width :400px;">{{itembill.invoiceid}}</td>
                            <td class="text-sm-center font-weight-normal" [ngStyle]="{'color':getColor2(itembill.remarkst)}">{{itembill.invoicedate
                                | date:'dd/MM/yyyy'}}</td>
                            <td class="text-sm-center font-weight-normal" [ngStyle]="{'color':getColor2(itembill.remarkst)}">{{itembill.duedate
                                | date:'dd/MM/yyyy'}}</td>
                            <td class="text-sm-center font-weight-normal" [ngStyle]="{'color':getColor2(itembill.remarkst)}">{{itembill.payment}}</td>
                            <td class="text-sm-right font-weight-normal" [ngStyle]="{'color':getColor2(itembill.remarkst)}" style="text-align: right;">{{itembill.salesbalance
                                | number:'1.2-2'}}</td>
                            <td class="text-sm-right font-weight-normal" [ngStyle]="{'color':getColor2(itembill.remarkst)}" style="text-align: right;">{{itembill.sumtax
                                | number:'1.2-2'}}</td>
                            <td class="text-sm-right font-weight-normal" [ngStyle]="{'color':getColor2(itembill.remarkst)}" style="text-align: right;">{{itembill.invoiceamount
                                | number:'1.2-2'}}</td>
                            <td class="text-center bg-light">
                                <button class="btn btn-link font-weight-light" style="padding: 0pt" (click)="openModalBillViewDetail(templateViewDetailApprove,itembill.invoiceid,
                                                                                                            itembill.orderaccount,
                                                                                                            itembill.salesid,
                                                                                                            itembill.invoicename,
                                                                                                            itembill.invoicedate,
                                                                                                            itembill.duedate, 
                                                                                                            itembill.delivery,
                                                                                                            itembill.salesbalance,
                                                                                                            itembill.sumtax,
                                                                                                            itembill.invoiceamount,
                                                                                                            itembill.saleman,
                                                                                                            itembill.remark,
                                                                                                            itembill.urlimg,
                                                                                                            itembill.imgbookbank
                                                                                                            )" aria-controls="collapseOne">
                                    {{itembill.numrun}}
                                </button>
                            </td>
                        </tr>
                    </tbody>

                </table>
            </div>
        </div>
        <div class="modal-footer" style="padding: 5px;">
            <div class="alert alert-success" style="width :400px; top: 7px;" role="alert">
                มูลค่าสินค้า : {{productpricesuccess | number:'1.2-2' }}
            </div>
            <div class="alert alert-success" style="width :300px; top: 7px;" role="alert">
                มูลค่าภาษี : {{sumvatsuccess | number:'1.2-2' }}
            </div>
            <div class="alert alert-success" style="width :400px; top: 7px;" role="alert">
                มูลค่ารวม : {{sumpricesuccess | number:'1.2-2' }}
            </div>
            <button type="button" (click)="ModalRefApprove.hide()" class="btn btn-secondary">Close</button>
        </div>
    </ng-template>

    <ng-template #ShowDeleteINV>
        <div class="modal-body text-lg-left">
            <P>ต้องการจะลบรายการ Invoice </P>
            <P>จากวันที่ {{Formdate | date:'dd/MM/yyyy'}} ถึง {{Todate | date:'dd/MM/yyyy'}} </P>
            <P *ngIf="!btnInvDelete">จำนวนรายการทั้งหมด : {{ numDataDelete }}
                <br>จำนวนรูป BooKBank : {{ numBookbankDelete }}
                <br>จำนวนรูป Invoice : {{ numINVDelete }}
            </P>
            <p *ngIf="CKDeleteload">{{ txtDeleteShow }}</p>
            <div *ngIf="!CKDeleteload">
                <div *ngIf="CKbookbank && CkINV">
                    จำนวนรายการทั้งหมดที่ลบ : {{ numDataDelete }}
                </div>
                <div *ngIf="CKbookbank">
                    จำนวนรูป BooKBank ที่ลบ : {{ numBookbankDelete }}
                </div>
                <div *ngIf="CkINV">
                    จำนวนรูป Invoice ที่ลบ : {{ numINVDelete }}
                </div>
            </div>
            <button type="button" *ngIf="btnInvDelete" (click)="SearchCompleteINV_Delete(Formdate,Todate)" style="margin-bottom : 10px;"
                class="btn btn-default">ยืนยันการทำรายการ</button>
            <br>
            <!-- <button type="button" *ngIf="btnInvDelete" (click)="SearchNameINV_BookBank(Formdate,Todate)" style="margin-bottom : 10px;" class="btn btn-default" >ทดสอบ</button>
                          <button type="button" *ngIf="btnInvDelete" (click)="deleteIMG()" style="margin-bottom : 10px;" class="btn btn-default" >ตั้งลบรูป</button>-->
            <button type="button" *ngIf="!btnInvDelete && CKDeleteload" [disabled]="numDataDelete==0" style="float:left;" (click)="DeleteINV()"
                class="btn btn-default">ลบ</button>
            <button type="button" style="float:right; margin-left: 3px;" (click)="modalDeleteClose()" class="btn btn-default">ปิด</button>
            <button type="button" *ngIf="!btnInvDelete" [disabled]="numDataDelete==0" style="float:right;" (click)="OpenDeleteINVDetail(ShowDeleteINVDetail)"
                class="btn btn-default">รายการ</button>

        </div>
        <!---->
    </ng-template>
    <ng-template #ShowDeleteINVDetail>
        <div class="modal-body text-center">
            <!--DataDelete-->
            <table class="table table-hover table-bordered table-sm">
                <thead>
                    <tr class="text-sm-center bg-light">
                        <th class="font-weight-light" scope="col">Item</th>
                        <th class="font-weight-light" scope="col">SO No</th>
                        <th class="font-weight-light" scope="col">Invoice No</th>
                        <th class="font-weight-light" scope="col">Name Invoice No</th>
                        <th class="font-weight-light" scope="col">Date</th>
                    </tr>
                </thead>
                <tbody>
                    <tr *ngFor="let item of DataDelete; let i=index">
                        <th class="text-sm-center font-weight-light">{{i+1}}</th>
                        <th class="text-sm-center font-weight-light">{{item.salesid}}</th>
                        <td class="text-sm-center font-weight-light">{{item.invoiceid}}</td>
                        <td class="text-sm-left font-weight-light">{{item.invoicingname}}</td>
                        <td class="text-sm-center font-weight-light">{{item.billingnotesdate | date:'dd/MM/yyyy'}}</td>
                    </tr>
                </tbody>
            </table>
            <button type="button" (click)="modalDeleteINVDetail.hide()" class="btn btn-default">ปิด</button>
        </div>
        <!---->
    </ng-template>