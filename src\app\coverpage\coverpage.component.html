<app-topmenu></app-topmenu>
<div class="container-fluid">  
<section style="padding-top:60px">
  <h5 class="p-sm-1 bg-secondary text-white text-center">Cover Page Printing</h5>
  <!--Filter Section-->
  <div class="form-row">
      <div class="col-md-2 mb-2" *ngIf="testclose" >
    
        <select multiple [(ngModel)]="saleid"   class="custom-select custom-select-sm" >
            <option selected disabled value="">--เลือกรหัสพนักงานขาย--</option>
            <option value="1">เลือกรายการทั้งหมด</option>
            <option *ngFor="let item of DateGroupsaleman" [value]="item.groupid">
                    {{item.groupid}} ({{item.name}})
            </option>
        </select>
      </div>
      <div class="col-md-2 mb-2">
     <select [(ngModel)]="delivery" class="custom-select custom-select-sm" name="" id="">
         <option selected value="0">กรุณาเลือกเงื่อนไขการขนส่ง</option>
         <option  value="1">เลือกทุุกรายการ</option>
         <option *ngFor="let item of    deliverytype" value="{{item.deliver}}">{{item.deliver}}</option>
     </select>
      </div>
      <div class="col-md-1 mb-1">
        <select [(ngModel)]="saletype" (change)="searchselectsale()" class="custom-select custom-select-sm" name="" id="">
            <option  value="1">เลือกทุุกรายการ</option>
            <option  value="5637170908">Sale กรุงเทพ</option>
            <option  value="5637170910">Sale ต่างจังหวัด</option>
        </select>
         </div>
         <div class="col-md-1 mb-1">
            <select [(ngModel)]="printst" (change)="searchselectprint()" class="custom-select custom-select-sm" name="" id="">
                <option  value="3">เลือกทุุกรายการ</option>
                <option  value="0">ยังไม่ Print</option>
                <option  value="1">Print แล้ว</option>
            </select>
             </div>
       <!-- <div class="col-md-2 mb-2">
        <input id="transport" class="form-control form-control-sm" type="text" name="transport" [(ngModel)]="transport" placeholder="เงื่อนไขขนส่ง">
    </div>
    <div class="col-md-2 mb-2">
          <input id="fromdate" class="form-control form-control-sm" type="date" name="formdate" [(ngModel)]="formdate" placeholder="จากวันที่">
      </div>
      <div class="col-md-2 mb-2">
          <input id="todate" class="form-control form-control-sm" type="date" name="todate" [(ngModel)]="todate" placeholder="ถึงวันที่">
      </div>-->
      <div class="col-xs-12 col-12 col-md-2 form-group">
            <input type="text"
            placeholder="DD/MM/YYYY"
            class="form-control"
            bsDatepicker
            [bsConfig]="{ dateInputFormat: 'DD/MM/YYYY' }"
            class="form-control form-control-sm"
            [(ngModel)]="Datafromdate"
            >
            </div>
       
    <div class="col-xs-12 col-12 col-md-2 form-group">
            <input type="text"
                   placeholder="DD/MM/YYYY"
                   class="form-control"
                   bsDatepicker
                   [bsConfig]="{ dateInputFormat: 'DD/MM/YYYY' }"
                   class="form-control form-control-sm"
                   [(ngModel)]="Datatodate">
          </div> 
      <div class="col-md-2 mb-2 col-12 text-center text-sm-center text-md-left text-lg-left">
          <button  [disabled]="searchbtn" (click)="Searchsaleheader()" class="btn btn-primary btn-sm font-weight-light" type="submit">Search</button>
      </div>
  </div>
  <div id="accordion">
      <div class="card">
          <div class="card-header" id="headingOne" style="padding-top:0px;padding-bottom:0px">
              <h5 class="mb-0">
                  <button class="btn btn-link" data-toggle="collapse" data-target="#collapseOne" aria-expanded="true" aria-controls="collapseOne">
                      Sale Order List
                  </button>
                <!--  <button class="btn btn-primary btn-sm font-weight-light" type="submit" style="width:60px;height:26px;padding:0px">Print</button> -->
              </h5>

          </div>

          <div id="collapseOne" class="collapse show" aria-labelledby="headingOne" data-parent="#accordion">
              <div class="card-body">
                  <div class="container-fluid">
                      <table class="table table-hover table-bordered table-sm">
                          <thead>
                              <tr class="text-sm-center bg-light">
                              
                                    <th class="font-weight-normal" scope="col">Item</th>
                                    <th class="font-weight-light" scope="col">เลขที่ SO</th>
                                    <th class="font-weight-normal"  scope="col">วันที่</th>
                                    <th class="font-weight-normal"  scope="col">พนักงานขาย</th>
                                    <th class="font-weight-normal"  scope="col">ลูกค้า</th>
                                    <th class="font-weight-normal"  scope="col">ที่อยู่ INV</th>
                                    <th class="font-weight-normal"  scope="col">ที่อยู่ขนส่ง</th>
                                    <th class="font-weight-normal"  scope="col">VAT/No VAT</th>
                                    <th class="font-weight-normal"  scope="col">น้ำหนักรวม</th>
                                    <th class="font-weight-normal"  scope="col">ประเภทขนส่ง</th>
                                    <th class="font-weight-normal"  scope="col">หมายเหตุ</th>
                                    <th class="font-weight-normal"  scope="col">Note ภายใน</th>
                                    <th class="font-weight-normal"  scope="col">เงินสด/เครดิต</th>
                                    <th class="font-weight-normal"  scope="col">Print</th>
                                  
                        
                            
                                 
                                                    
                                <!--  <th class="font-weight-normal text-center" scope="col" class="text-sm-center" width="40px">
                                      <input style="display: none" [(ngModel)]="checkallheader" (click)="clickallchecksaleheaderlist($event.target.checked)" type="checkbox" class="font-weight-light">
                                  </th> -->
                                  
                              </tr>
                          </thead>
                          <tbody>
                              <tr *ngFor="let item of seachheaderlist; let i=index" class="text-sm-left">
                                <td [ngStyle]="{'color':getColorslineprint(item.flag_print)}" class="text-sm-center font-weight-normal">{{i+1}}</td>
                                <td [ngStyle]="{'color':getColorslineprint(item.flag_print)}" class="text-sm-center font-weight-normal">{{item.id}}</td>
                                <td [ngStyle]="{'color':getColorslineprint(item.flag_print)}" class="text-sm-center font-weight-normal">{{item.dateid}}</td>
                                <td [ngStyle]="{'color':getColorslineprint(item.flag_print)}" class="text-sm-center font-weight-normal">{{item.SalesId}}</td>
                                <td [ngStyle]="{'color':getColorslineprint(item.flag_print)}" class="font-weight-normal">{{item.cusname}}</td>
                                <td [ngStyle]="{'color':getColorslineprint(item.flag_print)}" class="font-weight-normal">({{item.Desin}}) {{item.invaddress}}</td>
                                <td [ngStyle]="{'color':getColorslineprint(item.flag_print)}" class="font-weight-normal">({{item.Desdlv}}) {{item.deliveryaddress}}</td>
                                <td [ngStyle]="{'color':getColorslineprint(item.flag_print)}" class="text-sm-center font-weight-normal">{{item.vattype}}</td>
                                <td [ngStyle]="{'color':getColorslineprint(item.flag_print)}" class="text-sm-center font-weight-normal">{{item.totalweight | number:'1.2-2' }}</td>
                                <td [ngStyle]="{'color':getColorslineprint(item.flag_print)}" class="text-sm-center font-weight-normal">{{item.DlvMode}}</td>
                                <td [ngStyle]="{'color':getColorslineprint(item.flag_print)}" class="text-sm-center font-weight-normal">{{item.remark}}</td>
                                <td [ngStyle]="{'color':getColorslineprint(item.flag_print)}" class="text-sm-center font-weight-normal">{{item.CustomerRef}}</td>
                                <td [ngStyle]="{'color':getColorslineprint(item.flag_print)}" class="text-sm-center font-weight-normal">{{item.Payment}}</td>
                               <td class="text-center">
                                      <input (click)="clickchecksaleheaderlist(i,$event.target.checked,item.id,item.cusname,item.Desin,item.Desdlv,item.AXID)" [checked]="item.check" type="checkbox">
                                    </td> 
                                <!--  <td class="text-sm-center font-weight-light">
                                      <button  class="btn btn-link font-weight-light" style="padding: 0pt" aria-expanded="true" aria-controls="collapseOne">
                                          View
                                      </button>
                                  </td> -->
                              </tr>
                          </tbody>
                      </table>
                  </div>
              </div>
          </div>
      </div>
      <div class="card">
          <div class="card-header" id="headingTwo" style="padding-top:0px;padding-bottom:0px">
              <h5 class="mb-0" style="vertical-align:central">
                  <button (click)="clickloadcoverinfo()" class="btn btn-link collapsed" data-toggle="collapse" data-target="#collapseTwo" aria-expanded="false" aria-controls="collapseTwo">
                      Cover Page Info
                  </button>
              </h5>
          </div>
          <div id="collapseTwo" class="collapse" aria-labelledby="headingTwo" data-parent="#accordion">
              <div class="card-body">
                  <table class="table table-hover table-bordered table-sm">
                      <thead>
                          <tr class="text-sm-center bg-light">
                              <th class="font-weight-normal text-center" scope="col">Item</th>
                              <th class="font-weight-normal text-center" scope="col">รหัสสินค้า</th>
                              <th class="font-weight-normal text-center" scope="col">จำนวนชิ้น</th>
                              <th class="font-weight-normal text-center" scope="col">จำนวนลัง/มัด</th>
                              <th class="font-weight-normal text-center" scope="col">จำนวนแผ่น</th>
                             <!-- <th class="font-weight-normal text-center" scope="col" class="text-sm-center" width="90px">
                                  <input [(ngModel)]="checkallsaleline" (click)="clickallchecksalelist($event.target.checked)" type="checkbox" class="font-weight-light"> Check All
                              </th> -->
                              <th><button  [disabled]="searchbtn"  data-toggle="modal" data-target="#exampleModals" aria-expanded="true" (click)="printPX()" class="btn btn-link">Print<span class="badge badge-primary badge-pill">{{pageprintall | number:'1.0-0'}}</span></button></th>
                          </tr>
                      </thead>
                      <tbody>
                              <tr *ngFor="let item of salelinelist; let i=index" class="text-sm-left">
                                  <td class="text-sm-center font-weight-normal text-center">{{i+1}}</td>
                                  <td class="text-sm-left font-weight-normal text-center">{{item.productcode}}</td>
                                  <td class="text-sm-center font-weight-normal text-center">{{item.numpcs}}</td>
                                  <td class="text-sm-center font-weight-normal text-center">{{item.numpacking | number:'1.0-2'}}</td>
                                  <td class="text-sm-center font-weight-normal text-center">{{item.numpage | number:'1.0-0'}}</td>
                                <!--  <td class="text-sm-center font-weight-normal text-center">
                                      <input (click)="clickchecksalelinelist(i,$event.target.checked)" [checked]="item.check" type="checkbox">
                                  </td> -->
                                  <td class="text-center">
                                     <!-- <button  [disabled]="searchbtn" class="btn btn-link font-weight-light" style="padding: 0pt" data-toggle="modal" data-target="#exampleModals" aria-expanded="true"
                                          aria-controls="collapseOne">
                                          Print
                                      </button> -->
                                  </td> 
                              </tr>
                              
                      </tbody>
                      
                  </table>
                  
              </div>
          </div>
      </div>
  </div>
</section>
</div>
<div class="modal fade" id="exampleModals" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
  <div class="modal-dialog" role="document">
      <div class="modal-content">
        <div class="modal-header">
            <h5 class="modal-title" id="exampleModalLabel">ป้อนชื่อผู้รับ และ ที่อยู่ขนส่ง จำนวนแผ่นทั้งหมด {{pageprintall | number:'1.0-0'}}</h5>
            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
        <div class="modal-body">
          <div class="form-group">
            <select (change)="SelectName($event)"  class="custom-select custom-select-sm">
                <option  value="D">ชื่อขนส่ง</option>
                <option  value="I">ชื่อวางบิล</option>
                <option selected value="N">ชื่อลูกค้า</option>
            </select>
          </div>
                <div  class="form-group">
                    <label for="">ป้อนชื่อผู้รับ</label>
                    <input class="form-control" type="text" name="recipientname" [(ngModel)]="recipientname" id="recipientname">
                </div>
                <div  class="form-group">
                    <label for="">เลขที่ SOAX</label>
                    <input class="form-control" type="text" name="Axidshow" [(ngModel)]="Axidshow" id="Axidshow">
                </div>
                <div class="form-group">
                        <label for="">ขนาดตัวอักษรชื่อผู้รับ</label>
                    <select [(ngModel)]="pxHaerder"  class="custom-select custom-select-sm">
                        <option  value="60">60</option>
                        <option  value="65">65</option>
                        <option  value="70">70</option>
                        <option  value="75">75</option>
                        <option  value="80">80</option>
                        <option  value="85">85</option>

                    </select>
                     </div>
              <!--  <div class="form-group">
                <select  (change)="deliveryaddressselect($event)" [(ngModel)]="deliveryselect" class="custom-select " name="" id="">
<option value="0">กรุณาเลือกที่อยู่</option>
<option *ngFor="let item of daliverrydata" value="{{item.name}}">{{item.name}}</option>
                </select>
                </div> -->

                
                <div  class="form-group">
                    <label for="addressname">ที่อยู่จัดส่ง</label>
                    <textarea class="form-control" name="addressname" id="addressname" cols="30"  [(ngModel)]="addressname" rows="4">

                    </textarea>
                </div>
          
        </div>
          
          <div class="modal-footer">
              <label class="text-danger">{{alerttext}}</label>
              <button  type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
              <button [disabled]="searchbtn" (click)="setflagprint()"  type="button" class="btn btn-primary">ตกลง</button>
          </div>
      </div>
  </div>
</div>

<div id="mdlSample" class="modal fade" role="dialog" [ngStyle]="{'display': mdlSampleIsOpen ? 'block' : 'none', 'opacity': 1}">
    <div class="modal-dialog modal-md">
    <div class="modal-content">
    <div class="modal-header colhaederal">
    <h4 class="modal-title">Report</h4>
    </div>
    <div class="modal-body">{{alt}}
        

    </div>
    <div class="modal-footer" align="right">
                <button type="button" id="btnClose" class="btn btn-danger" (click)="closemodel(false)"><i class="fa fa-times fa-fw"></i> ปิด</button></div>
                
    </div>
    </div>
    </div>


    <div class="modal fade" id="exampleModalLabel" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="exampleModalLabel"></h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="input-group">
                        <div  class="form-group">
                            <label for="">ป้อนชื่อผู้รับ</label>
                            <input type="text" name="recipientname" [(ngModel)]="recipientname" id="recipientname">
                                    </div>
    
                    </div>
                </div>
                <div class="modal-footer">
                    <button [disabled]="searchbtn" type="button" class="btn btn-secondary" (click)="print()"   data-dismiss="modal">ตกลง</button>
          
                </div>
            </div>
        </div>
    </div>
    <link href="coverpage.component.css">