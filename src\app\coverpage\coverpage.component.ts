import { templateJitUrl } from '@angular/compiler';
import { WebapiService } from './../webapi.service';
import { Component, OnInit } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { NgbDateStruct, NgbCalendar } from '@ng-bootstrap/ng-bootstrap';
import { Router } from '@angular/router';

export interface numgroup {
  id: string;
}
export interface saleline{
  productcode: string;
  numgroup: string;
  numpcs: string;
  numpacking: number;
  numpage: number;
  check: boolean;
}

export interface checkheader {
  id: string;
  AXID:string;
  SalesId: string;
  CustAccount: string;
  SalesName: string;
  Desin:string;
  Desdlv:string;
  DlvMode: string
  address: string;
  check: boolean;
  dateid:string;
  vattype:string;
  totalweight:number;
  Payment:string;
  locationno:string;
  deliveryaddress:string;
  invaddress: string;
  CustomerRef:string;
  remark:string;
  InvAddress:string;
  flag_print:string;
  gsale:string;
  cusname:string;
}
export interface idcall{
  id: string;
}

export interface add{
  name:string;
}

@Component({
  selector: 'app-coverpage',
  templateUrl: './coverpage.component.html',
  styleUrls: ['./coverpage.component.css']
})
export class CoverpageComponent implements OnInit {

  printPX(){
    this.pxHaerder=70;
  }


  getColorslineprint(disst) { 
    if(disst==1){
     return '#0AC103';
    } 
    }

  mdlSampleIsOpen : boolean = false;
  timerInterval :any;
  checkreload=true;


  deliveryselect='0';
  showgetrecipientname=false;
  recipientname='';
  customername='ชื่อลูกค้า';
  customeraddress='ที่อยู่ลูกค้า';
pageprintall=0;
   allid:idcall[]=[];
  alt='';
 checkallheader=false;
 checkallsaleline=false;
numgrouplist :numgroup[]=[];
salelist: any[];
salelinelist: saleline[]=[];
headerlist: checkheader[]=[];
seachheaderlist:checkheader[]=[];
url='';
saleid='';
fromdate='';
DateGroupsaleman: any[]=[];
transport: string;
datalogin: any;
groupsale:string;
testclose: boolean;
todate='';
fromDate: NgbDateStruct;
toDate: NgbDateStruct;
Datatodate:any;
delivery='0';
deliverytype=[{'deliver':'รถบริษัท'},{'deliver':'รับเอง'},{'deliver':'ขนส่ง'}]
Datafromdate:any;
addressdata:any[]=[];

saletype='1';
printst='3';
idsetidprint='';
daliverrydata:add[]=[];
permisstiondata:any[]=[];
exportbtn=true;
searchbtn=true;
uncheck=false;
alerttext='';
addressname='กรุณาเลือกที่อยู่';
selectname='';
selectinv='';
selectdlv='';
pxHaerder= 70;
Axidshow='';
  constructor(private router: Router, private service: WebapiService, private http: HttpClient, private calendar: NgbCalendar ) {
    localStorage.removeItem('DataSOderreview');
    localStorage.removeItem('DataSOderlist');
    this.url=service.geturlservice()
    this.fromDate = calendar.getPrev(calendar.getToday(), 'd', 30);
this.toDate = calendar.getNext(calendar.getToday(), 'm', 3);
this.datalogin=JSON.parse(sessionStorage.getItem('login'))
this.Datatodate= new Date(this.toDate.year,this.toDate.month-1,this.toDate.day);
this.Datafromdate= new Date(this.fromDate.year, this.fromDate.month-1, this.fromDate.day);
this.getdate();
this.pxHaerder=70;
this.permisstiondata=JSON.parse(sessionStorage.getItem('menu'))

    
    if (this.datalogin==null){
      this.router.navigate(['login']);
       }else{
        this.getuser();
        this.exportbtn=!this.permisstiondata[12].flag_print;
        this.searchbtn=!this.permisstiondata[12].flag_action;

       }



  }

  ngOnInit() {
    this.getgroupsaleman();
  }
  getallselect() {
    
  }

  getuser(){
    if(this.datalogin[0].id_group_user=='153Admin' || this.datalogin[0].id_group_user=='1860Administrator' ){
      this.groupsale='';
      this.testclose=true;
    }else{
      this.testclose=false;
     this.saleid=this.datalogin[0].salegroup;
    }
  }

  
  getdate(){
    this.fromdate = `${this.Datafromdate.getFullYear()}-${this.Datafromdate.getMonth()+1}-${this.Datafromdate.getDate()}`;
    this.todate = `${this.Datatodate.getFullYear()}-${this.Datatodate.getMonth()+1}-${this.Datatodate.getDate()}`;
   }

  Searchsaleheader() {
    this.pxHaerder=60;
    this.getdate();
    this.headerlist=[];
    var datasalegroup='';
    if(this.saleid==''){
      this.openModal(true,'กรุณาป้อนรหัสพนักงานขาย หรือ วันที่ต้องการค้นหา',false);
    } else {
      if(this.saleid==''){
this.saleid='%20';
      }
      if(this.fromdate==''){
        this.fromdate =`${this.fromDate}`;
      }
      if(this.todate==''){
        this.todate=`${this.toDate}`;
      }
      if(this.saleid==''){
        if(this.datalogin[0].salegroup =='admin'){
          datasalegroup =`${this.saleid}`;
        }else{
          datasalegroup = `${this.datalogin[0].salegroup}`;
        }
      }
  
      if(this.saleid !==''){
        if(this.datalogin[0].salegroup =='admin'){
          datasalegroup =`${this.saleid}`;
        }else{
          datasalegroup = `${this.datalogin[0].salegroup}`;
        }
      }
    
if(this.delivery=='0'){
  alert('กรุณาเลือกประเภทการขนส่ง');
return;
}

      this.http.get<any>(this.url+'get_sale_header/'+ datasalegroup +'/'+this.fromdate+'/'+this.todate+'/'+this.delivery).subscribe(res => {
       if(res.length > 0){
       
     for(var i=0; i< res.length; i++) {
        this.headerlist.push({
          id: res[i].id,
          AXID:res[i].AXID,
          SalesId: res[i].SalesId,
          CustAccount: res[i].CustAccount,
          SalesName: res[i].description[0],
          Desin:res[i].InvName,
          Desdlv:res[i].DeliveryName,
          DlvMode: res[i].DlvMode,
          address: res[i].address,
          check: false,
          dateid: res[i].dateid,
          vattype: res[i].vattype,
          totalweight:res[i].totalweight,
          Payment:res[i].Payment,
          locationno:res[i].locationno,
          deliveryaddress:res[i].deliveryaddress,
          invaddress:res[i].invaddress,
          CustomerRef:res[i].CustomerRef,
          remark:res[i].remark,
          InvAddress:res[i].InvAddress,
          flag_print:res[i].flag_print,
          gsale:res[i].gsale,
          cusname:res[i].cusname
        });
       
   
     }
     this.seachheaderlist=this.headerlist;
        if(datasalegroup=='%20'){
          this.saleid='';
        }
       }else {
         this.openModal(true,'ไม่พบข้อมูลที่ค้นหา',false);
         if(datasalegroup=='%20'){
          this.saleid='';
        }
       }
      })
    }

    
  }
  clickallchecksaleheaderlist(checked) {

for(var i=0; i< this.headerlist.length; i++) {
  this.headerlist[i].check=checked;
 
  this.customername=this.headerlist[0].SalesName;
}

  }
loadaddresscoverpage(value){
  this.daliverrydata=[];

this.http.get<any>(this.url+'get_addresscoverpage/'+value).subscribe(res=>{

  if(res.length>0){
    this.addressname=res[0].invadd;
  
  } else {
    this.addressname=this.headerlist[0].deliveryaddress;
  }
})

}
searchselectsale(){
  if(this.saletype==='1'){
    this.seachheaderlist=this.headerlist;
  } else  {
    this.seachheaderlist=this.headerlist.filter(v => v.gsale.toLowerCase().indexOf(this.saletype.toLowerCase()) > -1).slice(0, 50);
  } 
}

searchselectprint(){

  if(this.printst==='3'){
    this.seachheaderlist=this.headerlist;
  } else  {
    //alert(JSON.stringify(this.headerlist[0]));
    this.seachheaderlist=this.headerlist.filter(v => v.flag_print.toLowerCase().indexOf(this.printst.toLowerCase()) > -1).slice(0, 50);
  } 
}
SelectName(name:any){

  if(name.target.value=='N'){
    this.recipientname=this.selectname;
   } else if (name.target.value=='I'){
    this.recipientname=this.selectinv;
   } else if(name.target.value=='D'){
    this.recipientname=this.selectdlv;
   }
 
}
deliveryaddressselect(event:any){
  this.addressname=event.target.value;
 
}
  clickchecksaleheaderlist(index,checked,idso,name,Inv,Dlv,axid) {
    this.Axidshow=axid;
    this.idsetidprint=idso;
    if(checked==true){
      this.uncheck=false;
      var lono='';
      if(this.headerlist[index].locationno==null && this.headerlist[index].locationno==undefined){
        lono=this.headerlist[index].InvAddress;
      } else {
        lono =this.headerlist[index].locationno[0];
      }

      this.addressname=this.headerlist[index].invaddress;
     // alert(this.headerlist[index].invaddress);
     // this.loadaddresscoverpage(lono);
     this.recipientname=name;
   
     this.selectname=name;
     this.selectinv=Inv;
     this.selectdlv=Dlv;
     //alert(  this.selectname+'/'+this.selectinv+'/'+this.selectdlv);
    }
    
    var ch=0;

    if(checked==true){
for(var i=0;i<this.headerlist.length;i++){
  this.headerlist[i].check=false;
}
    }
    this.headerlist[index].check=checked;
    if(this.headerlist[index].check==true) {
      this.customeraddress=this.headerlist[index].address;
      this.customername=this.headerlist[index].SalesName;
    } else {
      this.customername='ชื่อลูกค้า';
      this.customeraddress='ที่อยู่ลูกค้า';
    }
  
   
    for(var i=0; i< this.headerlist.length; i++) {
    if(this.headerlist[i].check==true) {
ch++;
    } else {
      ch--;
    }
    }
    if(ch==this.headerlist.length) {
      this.checkallheader=true;
    } else {
      this.checkallheader=false;
    }
   
  
    
  }

  clickallchecksalelist(checked) {
    this.pxHaerder=70;
    for(var i=0; i< this.salelinelist.length; i++) {
      this.salelinelist[i].check=checked;
    }
    this.countpageallprint();
      }

      clickchecksalelinelist(index,checked) {
        var ch=0;
        this.salelinelist[index].check=checked;
        for(var i=0; i< this.salelinelist.length; i++) {
        if(this.salelinelist[i].check==true) {
    ch++;
        } else {
          ch--;
        }
        }
    
        if(ch==this.salelinelist.length) {
          this.checkallsaleline=true;
        } else {
          this.checkallsaleline=false;
        }
       
      
        this.countpageallprint();
      }

countpageallprint() {
  this.pageprintall=0;
  var pageall=0;
  for(var i=0; i< this.salelinelist.length; i++) {

     
      pageall += this.salelinelist[i].numpage;
    

    }
    this.pageprintall=Math.ceil(pageall);
}
  getdataderay() {
    if(this.allid.length >0) {
      var persan=0;
      var nopersan=0;
      var numpagefi=0;
      var numpackfi=0;
      var packnum=0;
      var allpk=0;
       
          this.http.get<any>(this.url+'get_sale_linebyid/'+this.allid[0].id).subscribe(res =>{
            if(res.length>0){
        for (var i =0; i< res.length; i++) {
packnum=res[i].sumsaleqty/res[i].taxpackagingqty;
allpk=packnum/res[i].qty;
persan=packnum%res[i].qty;
          //persan=res[i].sumsaleqty%res[i].qty;
          //nopersan=res[i].sumsaleqty/res[i].qty;
       
          if(persan == 0 || persan <1.0) {
            numpagefi=allpk*res[i].pack;
          if(numpagefi<1.0){
numpagefi=1;
          }
            //numpagefi=nopersan*res[i].pack
          } else {
            //numpackfi=  nopersan;
            numpagefi=(allpk*res[i].pack);
          }
         // alert(numpackfi)
          this.salelinelist.push({
            productcode: res[i].grp,
            numgroup: res[i].numgroup,
            numpcs: res[i].salesqty,
            numpacking: packnum,
            numpage:numpagefi ,
            check: false,
          });
        }
        this.countpageallprint();
            }else {
              this.openModal(true,'ไม่พบข้อมูลที่ค้นหา',false);
            }
          })
          this.allid.splice(0,1);
          if(this.allid.length < 1) {
            clearInterval(this.timerInterval);
          }
      }
  }
  clickloadcoverinfo() {
  
    this.salelinelist=[];

    for (var i=0; i< this.headerlist.length;i++){
if(this.headerlist[i].check==false) {

}else {
this.allid.push({
  id:this.headerlist[i].id
});
}
    }
   
    this.timerInterval= setInterval(() => this.getdataderay(), 100);
    
  }
  getgroupsaleman(){
    this.DateGroupsaleman=[];
    this.http.get<any>(this.url +'salesman' ).subscribe(res => {
      if(res.length > 0){
     this.DateGroupsaleman=res;
          }else{
          alert('ไม่สามารถติดต่อข้อมูลรหัส saleman ได้');
          }
      });
  }

  printpage(){
    window.print();
  }
  openModal(open : boolean,text: string,load:boolean) : void {
    this.mdlSampleIsOpen = open;
    this.alt=text;
    this.checkreload=load;
}
 closemodel(cl: boolean) {
  this.mdlSampleIsOpen=cl;
  if(this.checkreload==true) {
    location.reload();
  }
 
}

setflagprint(){
  var body={id:this.idsetidprint}
  this.http.post(this.url+'set_printsaleheader',body).subscribe(res=>{
    if(res==true){
      this.print();
    }
    
  });
}
clrscrdataprint(){
  this.daliverrydata=[];
  this.addressname=='กรุณาเลือกที่อยู่'
  this.recipientname='';
}

print(): void {

    let  popupWin;
  if(this.recipientname !='' && this.addressname!='กรุณาเลือกที่อยู่') {
    popupWin = window.open('', '_blank');
    popupWin.document.open();
    popupWin.document.write(`
    <html>
    <head>
      <title>ใบแปะหน้ากล่อง</title>
      <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
      <meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<meta http-equiv="Content-Language" content="en-us">
<meta http-equiv="Content-Script-Type" content="text/javascript">
<meta name="GENERATOR" content="TrackInternet._Default Class">
      
      <script src="https://code.jquery.com/jquery-3.3.1.js" ></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.14.3/umd/popper.min.js" ></script>
<script src="https://stackpath.bootstrapcdn.com/bootstrap/4.1.3/js/bootstrap.min.js"></script>
      <style>
.textcen{
text-align: center;
}
.he{
  border-width:5px;  
border-style:solid;
border-color: black;
padding-bottom:0px;
}
.footertable
{
  text-align: center;
  position : absolute;
  bottom : 0;
  width:98%;
  height : 150px;
  margin-top : 0px;
    
    
}


      </style>
    </head>
<body onafterprint="saveprintsucc()" onload="window.print();window.close()">
<div class="container-fluid ">
        
            <div class="col-md-12">
         
            <div class=" he ">
            <lable ><p style=" font-size: 25px;text-align: right;line-height: 2px;margin-right: 13%; "><b>${this.Axidshow}</b></p> </lable>
            <h1 class="textcen" style=" font-size: 50px;">*** กรุณาอย่าวางของหนักทับ *** </p></h1>
               </div>
               <div class="textcen p-1">
                   <span class="textcen" ><h2 style=" font-size: ${ this.pxHaerder }px">${this.recipientname}</h2></span>
               </div>
               <div class=" textcen p-1">
                   <div class="textcen" ><h2 style=" font-size: 50px"></h2></div>
               </div>
               <div class="textcen p-1">
                   <div class="textcen" ><h2 style=" font-size: 40px"> ${this.addressname}
                    </h2></div>
               </div>
               <div class="he footertable">
              
                   <h1  class="textcen" style="font-size: 50px">*** กรุณาอย่าวางของหนักทับ ***</h1>
               </div>

            </div>
        
            
    </div>
</body>
  </html>`);
  } else if(this.addressname=='กรุณาเลือกที่อยู่'){
    this.alerttext='กรุณาเลือก ที่อยู่!!!!!';
  } else {
    this.alerttext='กรุณาป้อน ชื่อผู้รับ!!!!';
  }
  
    popupWin.document.close();

  
 //this.daliverrydata=[];
}

}
