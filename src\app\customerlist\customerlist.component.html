
<app-topmenu></app-topmenu>
<div class="container-fluid" style="padding-right: 5px; padding-left: 5px;" >
<section style="padding-top:60px">
  <h5 class="p-sm-1 bg-secondary text-white text-center">Customer List</h5>
  <div class="form-row">
        <div *ngIf="chackuser" class="col-md-2 mb-2">
                <select multiple [(ngModel)]="Salegroup"  class="custom-select custom-select-sm" >
                        <option selected disabled value="">--เลือกรหัสพนักงานขาย--</option>
                        <option *ngFor="let item of DateGroupsaleman" [value]="item.groupid">
                                {{item.groupid}} ({{item.name}})
                        </option>
                    </select> 
                   <!-- <select multiple [(ngModel)]="CodeSo" [disabled]="groupsale !=='admin'" class="custom-select custom-select-sm" [disabled]="groupsale !=='admin'">
                        <option selected disabled value="">--เลือกรหัสพนักงานขาย--</option>
                        <option *ngFor="let item of DateGroupsaleman" [value]="item.groupid">
                               {{item.groupid}} ({{item.name}})
                        </option>
                    </select>-->
            </div>
    <div  class="col-md-2 mb-2">
            <ng-template    #rtcode let-r="result" let-t="term">
                    <div >
                            <label>{{r.name}} ({{r.accountnum}})</label>
                    </div>
              
        </ng-template>
        <input    id="typeahead-template"  placeholder="{{companycode}}"  type="text" class="form-control form-control-sm" [(ngModel)]="getcustomercode" name="getcustomercode" [ngbTypeahead]="searchcode" [resultTemplate]="rtcode"
        [inputFormatter]="formattercode" />
    </div>
    <div class="col-md-2 mb-2">
        <ng-template    #rt let-r="result" let-t="term">
            <div >
                    <label>{{r.name}} ({{r.accountnum}})</label>
            </div>
      
</ng-template>

<input    id="typeahead-template"  placeholder="{{companyname}}"  type="text" class="form-control form-control-sm" [(ngModel)]="getcustomername" name="getcustomername" [ngbTypeahead]="search" [resultTemplate]="rt"
  [inputFormatter]="formatter" />
    </div>
    <div class="col-md-2 mb-2">
            <ng-template    #rtaddress let-r="result" let-t="term">
                    <div >
                            <label>{{r.address}}</label>
                    </div>
              
        </ng-template>
        
        <input    id="typeahead-template"  placeholder="ที่อยู่วางบิลหลัก"  type="text" class="form-control form-control-sm" [(ngModel)]="getcustomeraddress" name="getcustomeraddress" [ngbTypeahead]="searchaddress" [resultTemplate]="rtaddress"
          [inputFormatter]="formatteraddress" />
    </div>
    <div class="col-md-2 mb-2">
            <input id="groupcustomer" class="form-control form-control-sm" type="text" name="groupcustomer" [(ngModel)]="groupcustomer" placeholder="ค้นหากลุ่มลูกค้า">
        </div>
    <div class="col-md-2 mb-3 col-12 text-center text-sm-center text-md-center text-lg-left" style="padding-right: 0px; padding-left: 0px;">
        <button [disabled]="searchbtn"  style="width: 60px;" (click)="Searchcustomerclick()" class="btn btn-primary btn-sm font-weight-light">Search</button>
        <button [disabled]="exportbtn"  style="margin-left: 3px; width: 60px;" class="btn btn-primary btn-sm font-weight-light">Export</button>
        <ng-template #tipContent let-greeting="greeting"><b>{{name}}</b>!</ng-template>
        <button 
        [disabled]="searchbtn"
        [ngbTooltip]="tipContent"
        triggers="manual" #t2="ngbTooltip"
        (click)="syncdatacustomer(t2)"
     style="margin-left: 3px; width: 60px;"
      class="btn btn-primary btn-sm font-weight-light">Import</button>
        <!--" (click)="exportdataexcel()"-->
    </div>
  </div>  
  <table class="table table-hover table-bordered table-sm">
      <thead>
          <tr class="text-sm-center font-weight-light bg-light">
              <th class="font-weight-normal text-center"  style="width: 55px;"scope="col">Item</th>
              <th class="font-weight-normal text-center" style="width: 90px;" scope="col">กลุ่มลูกค้า</th>
              <th class="font-weight-normal text-center"  style="width: 90px;" scope="col">รหัสลูกค้า</th>
              <th class="font-weight-normal text-center"  style="width: 90px;" scope="col">ประเภทการชำระเงิน</th>
              <th class="font-weight-normal text-center" scope="col">ชื่อลูกค้า</th>
              <th class="font-weight-normal text-center" scope="col">ที่อยู่วางบิลหลัก</th>
              <th class="font-weight-normal text-center" scope="col">ที่อยู่ส่งสินค้าหลัก</th>
          </tr>
      </thead>
      <tbody>
          <tr *ngFor="let item of customerlists; let i = index">
              <td class="text-center  font-weight-normal" >{{i+1}}</td>
              <td class="text-center  font-weight-normal" >{{item.custgroup}}</td>
              <td class="text-center font-weight-normal">{{item.accountnum}}</td>
              <td class="text-center  font-weight-normal" >{{item.paymtermid}}</td>
              <td class="font-weight-normal" >{{item.name}}</td>
              <td class="font-weight-normal" >{{item.invoiceaddress}}</td>
              <td class="font-weight-normal" >{{item.deliveryaddress}}</td>
          </tr>
         
      </tbody>
  </table>
  <div id="mdlSample" class="modal fade" role="dialog" [ngStyle]="{'display': mdlSampleIsOpen ? 'block' : 'none', 'opacity': 1}">
        <div class="modal-dialog modal-md">
        <div class="modal-content">
        <div class="modal-header colhaederal">
        <h4 class="modal-title">Report</h4>
        </div>
        <div class="modal-body">{{alt}}</div>
        <div class="modal-footer" align="right">
                    <button type="button" id="btnClose" class="btn btn-danger" (click)="closemodel(false)"><i class="fa fa-times fa-fw"></i> ปิด</button></div>
        </div>
        </div>
        </div>        
</section>
</div>