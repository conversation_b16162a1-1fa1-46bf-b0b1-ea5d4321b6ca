
import { HttpClient } from '@angular/common/http';
import { Component, OnInit } from '@angular/core';
import { Angular5Csv } from 'angular5-csv/dist/Angular5-csv';
import { Observable } from 'rxjs';
import { login } from '../entites/login.entites';
import { menulist } from '../entites/menulist.entites';
import { WebapiService } from '../webapi.service';

import { Router } from '@angular/router';
import { debounceTime, map } from 'rxjs/operators';

@Component({
  selector: 'app-customerlist',
  templateUrl: './customerlist.component.html',
  styleUrls: ['./customerlist.component.css']
})
export class CustomerlistComponent implements OnInit {
  mdlSampleIsOpen : boolean = false;
  alt='';
  checkreload=true;



logindata: login;
menudata: menulist;
  options = {
    fieldSeparator: ',',
    quoteStrings: '"',
    decimalseparator: '.',
    showLabels: true,
    showTitle: true,
    useBom: true,
    noDownload: false,
    headers: ["กลุ่มลูกค้า", "รหัสลูกค้า","ชื่อลูกค้า","ที่อยู่วางบิลหลัก","ที่อยู่ส่งสินค้าหลัก","รหัสผู้ขาย"]
  };
customerlists: any[];
Salegroup='';
groupcustomer = '';
codecustomer ='';
namecustomer ='';
addressblin ='';
numitem=0;
url: string;
toolsssop=true;
menulistcustomer:any[];
welcame=false;
Name:any[]=[];
Interval:any;
DateGroupsaleman: any[]=[];
chackuser=false;
groupsale:string;
CodeSo='';
permisstiondata:any[]=[];
exportbtn=true;
searchbtn=true;
successimport:'กำลังนำเข้าข้อมูล';
name:string;
customers:any[]=[];
customeraddressauto:any[]=[];
getcustomercode:any;
getcustomername:any;
getcustomeraddress:any;
companycode='ค้นหารหัสลูกค้า';
companyname='ค้นหาชื่อลูกค้า';
enablecustomer=false;
datalogin;
  constructor(private http: HttpClient, private service: WebapiService,private router: Router) {

    localStorage.removeItem('DataSOderreview');
    localStorage.removeItem('DataSOderlist');
    this.name = 'กำลังนำเข้าข้อมูลกรุณารอสักครู่.....';
    this.url = this.service.urlApi;
    this.Name=JSON.parse(sessionStorage.getItem('login'))
   /* alert(this.service.setalert());*/


   this.datalogin=JSON.parse(sessionStorage.getItem('login'))
   if (this.datalogin==null){
     this.router.navigate(['login']);
      }else{
        this.groupsale=this.Name[0].salegroup;
        this.permisstiondata=JSON.parse(sessionStorage.getItem('menu'))
      this.exportbtn=!this.permisstiondata[0].flag_print;
      this.searchbtn=!this.permisstiondata[0].flag_action;
      }

   /* this.openModal(true,'ยินดีต้อนรับ',false);*/
   }
   search = (text$: Observable<any>) =>

   //Autocomplete ลูกค้า
 text$.pipe(
   debounceTime(200),
   map(term => term === '' ? []
     : this.customers.filter(v => v.name.toLowerCase().indexOf(term.toLowerCase()) > -1 ||  v.accountnum.toLowerCase().indexOf(term.toLowerCase()) > -1).slice(0, 50))
 );
 formatter = (x: {name: string,accountnum :string}) => x.name;

 searchcode = (text$: Observable<any>) =>

 //Autocomplete ลูกค้า
text$.pipe(
 debounceTime(200),
 map(term => term === '' ? []
   : this.customers.filter(v => v.name.toLowerCase().indexOf(term.toLowerCase()) > -1 ||  v.accountnum.toLowerCase().indexOf(term.toLowerCase()) > -1).slice(0, 50))
);
formattercode = (x: {name: string,accountnum :string}) => x.accountnum;


searchaddress = (text$: Observable<any>) =>

 //Autocomplete ลูกค้า
text$.pipe(
 debounceTime(200),
 map(term => term === '' ? []
   : this.customeraddressauto.filter(v => v.address.toLowerCase().indexOf(term.toLowerCase()) > -1).slice(0, 50))
);
formatteraddress = (x: {address: string}) => x.address;

 ngOnInit() {

    this.getcostomerauto();
    var idgroup='';
    if(this.groupsale=='admin'){
      idgroup='%20'
    }else{
      idgroup=this.groupsale;
    }
    this.Interval= setInterval(()=>  this.Searchcustomer(idgroup),500);
if(this.groupsale=='admin'){
this.chackuser=true;
}
  }


  toggleWithGreeting(tooltip, greeting: string) {
    if (tooltip.isOpen()) {
      tooltip.close();
    } else {
      tooltip.open({greeting});
    }
  }
  closetooltip(){
    this.toolsssop=false;
  }
  getgroupsaleman(){
    this.DateGroupsaleman=[];
    this.http.get<any>(this.url +'salesman' ).subscribe(res => {
      if(res.length > 0){
     this.DateGroupsaleman=res;
          }else{
          alert('ไม่สามารถติดต่อข้อมูลรหัส saleman ได้')
          }
      });
  }

  syncdatacustomer(tool){
    this.toggleWithGreeting(tool,'');
    const Http = new XMLHttpRequest();
   // const url='syncso/Service.asmx/PullingData';
   const url='syncso/Service.asmx/PullingData?iFile=customer';
    Http.open("GET", url);
    Http.send();


   // Http.open("POST", url);
   // Http.setRequestHeader("Content-Type", "application/json;charset=UTF-8");
   // Http.send(JSON.stringify({ "iFile": "customer" }));


    Http.onreadystatechange=(e)=>{
    if(Http.readyState==4 && Http.status==200){
      this.name='นำเข้าข้อมูล เสร็จสิ้น';
      if(confirm('นำเข้าข้อมูล เสร็จสิ้น')){
        this.toggleWithGreeting(tool,'');
      } else {
        this.toggleWithGreeting(tool,'');
      }

    }

    }
  }


  getcostomerauto(){
    var idsale=this.Name[0].salegroup;
    if(this.Name[0].salegroup==='admin' ){
idsale='%20';
    } else {
      idsale=this.Name[0].salegroup;
    }

    this.http.get<any>(this.url + 'customerauto/'+idsale).subscribe(res =>{
      this.customers=res;
      this.getcostomeraddressauto();
    })
  }

  getcostomeraddressauto(){
    var idsale=this.Name[0].salegroup;
    if(this.Name[0].salegroup==='admin'){
idsale='%20';
    } else {
      idsale=this.Name[0].salegroup;
    }

    this.http.get<any>(this.url + 'customeraddressauto/'+idsale).subscribe(res =>{
      this.customeraddressauto=res;
    })
  }
  Searchcustomer(saleid) {
    var groupcustomer='';
    var codecustomer='';
    var namecustomer='';
    var addressblin='';

    if(this.groupcustomer == '') {
      groupcustomer='%20';
    }
    if(this.codecustomer=='') {
      codecustomer ='%20';
    }
    if (this.namecustomer =='') {
        namecustomer='%20';
    }
    if(this.addressblin=='') {
        addressblin='%20';
    }
    this.customerlists=[];
    this.http.get<any>(this.url + 'customer_list/' + saleid+ '/' + codecustomer + '/' + namecustomer + '/' + addressblin  + '/' + groupcustomer).subscribe(res =>{
      if( res.length > 0) {
        this.getgroupsaleman();
        this.customerlists=res
        if(codecustomer=='%20'){
          this.codecustomer='';
        }
        if(namecustomer=='%20'){
          this.namecustomer='';
        }
        if(addressblin=='%20'){
          this.addressblin='';
        }
        if(groupcustomer=='%20'){
          this.groupcustomer='';
        }
      } else {
        this.getgroupsaleman();
        if(codecustomer=='%20'){
          this.codecustomer='';
        }
        if(namecustomer=='%20'){
          this.namecustomer='';
        }
        if(addressblin=='%20'){
          this.addressblin='';
        }
        if(groupcustomer=='%20'){
          this.groupcustomer='';
        }

        this.openModal(true,'ไม่พบข้อมูลที่ค้นหา',false);
      }
      clearInterval(this.Interval);
    });
  }
  Searchcustomerclick(){
    //alert(JSON.stringify('code/'+JSON.stringify(this.getcustomercode))+'  name/'+JSON.stringify(this.getcustomername));
    var datacodeSo='';
    var groupcustomer='';
    var codecustomer='';
    var namecustomer='';
    var addressblin='';
    if(this.getcustomeraddress==undefined || this.getcustomeraddress=='')
    {
      this.addressblin='';
    } else {
      this.addressblin=this.getcustomeraddress.locationno;
    }

    if(this.getcustomercode==undefined || this.getcustomercode==''){
      this.codecustomer='';

    }
    else {
      this.codecustomer=this.getcustomercode.accountnum;
    }

    if(this.getcustomername==undefined || this.getcustomername==''){
      this.namecustomer='';
    } else {
      this.namecustomer=this.getcustomername.name;
    }
    if(this.groupcustomer == '') {
      groupcustomer='%20';
    }else{
      groupcustomer=this.groupcustomer
    }
    if(this.codecustomer=='') {
      codecustomer ='%20';
    }else{
      codecustomer=this.codecustomer
    }
    if (this.namecustomer =='') {
        namecustomer='%20';
    }else{
      namecustomer=this.namecustomer
    }
    if(this.addressblin=='') {
        addressblin='%20';
    }else{
      addressblin=this.addressblin
    }

    if(this.Salegroup==''){
      if(this.Name[0].salegroup =='admin'){
        datacodeSo =`${this.Salegroup}`;
      }else{
        datacodeSo = `${this.Name[0].salegroup}`;
      }
    }

    if(this.Salegroup !==''){
      if(this.Name[0].salegroup =='admin'){
        datacodeSo =`${this.Salegroup}`;
      }else{
        datacodeSo = `${this.Name[0].salegroup}`;
      }
    }
    if(datacodeSo==''){
      datacodeSo='%20';
    }

    this.customerlists=[];
    this.http.get<any>(this.url + 'customer_list/' + datacodeSo + '/' + codecustomer + '/' + namecustomer + '/' + addressblin  + '/' + groupcustomer).subscribe(res =>{
      if( res.length > 0) {
        this.customerlists=res
        if(codecustomer=='%20'){
          this.codecustomer='';
        }
        if(namecustomer=='%20'){
          this.namecustomer='';
        }
        if(addressblin=='%20'){
          this.addressblin='';
        }
        if(groupcustomer=='%20'){
          this.groupcustomer='';
        }
      } else {
        if(codecustomer=='%20'){
          this.codecustomer='';
        }
        if(namecustomer=='%20'){
          this.namecustomer='';
        }
        if(addressblin=='%20'){
          this.addressblin='';
        }
        if(groupcustomer=='%20'){
          this.groupcustomer='';
        }
        this.openModal(true,'ไม่พบข้อมูลที่ค้นหา',false);
      }
    });

  }

  exportdataexcel() {
    if(this.customerlists==undefined){
      this.openModal(true,'ไม่พบข้อมูล',false);
    }else {
      new Angular5Csv(this.customerlists, 'CustomerList', this.options);
    }

  }

  openModal(open : boolean,text: string,load:boolean) : void {
    this.mdlSampleIsOpen = open;
    this.alt=text;
    this.checkreload=load;
  }
  closemodel(cl: boolean) {
  this.mdlSampleIsOpen=cl;
  if(this.checkreload==true) {
   /* location.reload();*/
  }

  }
}
