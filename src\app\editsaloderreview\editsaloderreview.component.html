<app-topmenu></app-topmenu>
<div class="container-fluid" style="padding-right: 5px; padding-left: 5px;">
    <section style="padding-top:60px">
        <h5 class="p-sm-1 col-xs-12 col-12 col-md-12 bg-secondary text-white text-center">Sale Order Record</h5>
        <div id="accordion">
            <div class="card">
                <div class="card-header" id="headingOne" style="padding-top:0px;padding-bottom:0px">
                    <div class="form-row">
                        <h5 class="mb-0">
                            <button class="btn btn-link" data-toggle="collapse" data-target="#collapseOne" aria-expanded="true" aria-controls="collapseOne">
                          Sale Order Header
                      </button>

                        </h5>
                        <div class="col" style="padding-top:4px; ">
                            <!--  <button (click)="deleteebeforupdate()" class="btn btn-warning btn-sm font-weight-light" type="submit" style="width:45px;height:26px;padding:0px">Draft</button> -->
                            <!-- <button style="margin-left: 3px;" [ngStyle]="{'display': none}" (click)="print()" class="btn btn-primary btn-sm font-weight-light" type="submit" style="width:65px;height:26px;padding:0px">Quotation</button> -->
                            <button style="margin-left: 3px;" *ngIf="dissave" (click)="deleteebeforupdate();" class="btn btn-primary btn-sm font-weight-light" type="button" style="width:45px;height:26px;padding:0px">Save</button>
                            <!-- <button  style="margin-left: 3px; display: none" (click)="closeeditlistoder()" class="btn btn-primary btn-sm font-weight-light" type="button" style="width:45px;height:26px;padding:0px">Close</button> -->
                            <label style="margin-left: 2%" class="text-info">  เลขที่ SO WEB: {{idforshow}}</label>
                        </div>

                    </div>

                </div>

                <div id="collapseOne" class="collapse show" aria-labelledby="headingOne" data-parent="#accordion">
                    <div class="card-body">
                        <div class="container-fluid">
                            <form class="needs-validation" novalidate>
                                <div class="form-row">
                                    <div class="col-md-4 mb-3">

                                        <ng-template #rt let-r="result" let-t="term">
                                            <div (click)="getcustomersalefunction(r.name)">
                                                <label (mousedown)="getcustomersalefunction(r.name)">{{r.name}} ({{r.accountnum}})({{r.regnum}})</label>
                                            </div>

                                        </ng-template>

                                        <input [disabled]="ennablecustomer" tooltip="ป้อนชื่อ หรือ รหัสลูกค้า" triggers="focus" id="typeahead-template" placeholder="{{company}}" type="text" class="form-control form-control-sm" [(ngModel)]="model" name="model" [ngbTypeahead]="search" [resultTemplate]="rt"
                                            [inputFormatter]="formatter" />

                                    </div>
                                    <div class="col-md-4 mb-3">


                                        <!--<label for="validationTooltip02">ที่อยู่วางบิล</label>-->
                                        <select tooltip="เลือกที่อยู่ วางบิล" (change)="setlocationno($event.target.value)" class="custom-select custom-select-sm">
                                     
                                    <option selected  value="{{item.locationno}}" *ngFor="let item of inaddress">({{item.name}}) {{item.address}}</option>
                                </select>
                                    </div>
                                    <div class="col-md-4 mb-3">

                                        <!--<label for="validationTooltip02">ที่อยู่จัดส่งสินค้า</label>-->
                                        <select (change)='selectlocationde($event.target.value)' tooltip="เลือกที่อยู่ ขนส่ง" class="custom-select custom-select-sm">
                                      
                                      <option selected  value="{{item.locationno}}" *ngFor="let item of daddress">({{item.name}}) {{item.address}}</option>
                                  </select>
                                    </div>
                                </div>
                                <div class="form-row">
                                    <div class="col-md-1 mb-3">
                                        <!--<label for="validationTooltip03">ใบกำกับภาษี</label>-->
                                        <select tooltip="เลือกภาษี" (change)="selectvat($event.target.value)" class="custom-select custom-select-sm">
                                   <!--   <option selected>ใบกำกับภาษี</option> -->
                                    <option  selected *ngFor="let item of vatlist" value="{{item.vattype}}">{{item.vattype}}</option>
                                  </select>
                                    </div>
                                    <div class="col-md-1 mb-1">
                                        <!--<label for="validationTooltip04">ประเภทการขนส่ง</label>-->
                                        <select tooltip="เลือกประเภทการขนส่ง" (change)="selectdelivery($event.target.value)" class="custom-select custom-select-sm">
                                  <option selected *ngFor="let item of deliverytype" value="{{item.deliver}}" >{{item.deliver}}</option>
                                
                                  </select>
                                    </div>
                                    <div class="col-md-2 mb-2 form-group">
                                        <input type="text" tooltip="Ship Date" placeholder="DD/MM/YYYY" class="form-control" bsDatepicker class="form-control form-control-sm" [(bsValue)]="bsValue" value="{{ bsValue | date:'dd/MM/yyyy' }} " (bsValueChange)="clickdate($event)">

                                        <!-- <input class="form-control form-control-sm" (focusout)="clickdateshipping($event.target.value)"  type="date" value="{{dateshipping}}"  id="dateshipping" name="dateshipping"  placeholder="วันที่"/> -->
                                    </div>
                                    <div class="col-md-1 mb-1">



                                        <!--<label for="validationTooltip05">เงินสด/เครดิต</label>-->
                                        <select tooltip="เลือกการชำระเงิน" (change)="selectpaymentfn($event.target.value)" class="custom-select custom-select-sm">
                                    <!--  <option selected>เงินสด/เครดิต</option> -->
                                      <option  selected *ngFor="let item of paymentlist" [value]="item.paymtermid">{{item.paymtermid}}</option>
                                    <!--  <option  value="0">เงินสด/เครดิต</option>
                                   <option value="1">เงินสด</option>
                                   <option value="2">เครดิต</option> -->
                                  </select>

                                    </div>

                                    <div class="col-md-3 mb-2">
                                        <!--<label for="validationTooltip01">หมายเหตุ</label>-->
                                        <input [(ngModel)]="notesoinput" maxlength="60" tooltip="Note ภายใน" (input)="checklength($event.target.value,60)" tooltip-show-trigger="focusin" tooltip-hide-trigger="focusout" name="notesoinput" type="text" class="form-control form-control-sm" id="notesoinput"
                                            placeholder="Note ภายใน">
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <!--<label for="validationTooltip01">หมายเหตุ</label>-->
                                        <input [(ngModel)]="remarksoinput" (focusout)="CheckValueRemark($event.target.value)" tooltip="หมายเหตุ" maxlength="255" (input)="checklength($event.target.value,255)" name="remarksoinput" type="text" class="form-control form-control-sm" id="remarksoinput"
                                            placeholder="หมายเหตุ">
                                    </div>
                                </div>
                            </form>

                        </div>
                    </div>
                </div>
            </div>
            <div class="card">
                <div class="card-header" id="headingTwo" style="padding-top:0px;padding-bottom:0px">
                    <div class="form-row">

                        <h5 class="mb-0" style="vertical-align:central">
                            <button [disabled]="disproductline" (click)="selectsaleoderline()" class="btn btn-link collapsed" data-toggle="collapse" data-target="#collapseTwo" aria-expanded="false" aria-controls="collapseTwo">
                      Product Line Item
                  </button>
                        </h5>
                        <div class="col" style="padding-top:7px; ">
                            <label *ngIf="Instock!=''" class="text-success">Item In Stock: {{Instock}}</label>
                        </div>
                    </div>
                </div>
                <div id="collapseTwo" class="collapse" aria-labelledby="headingTwo" data-parent="#accordion">
                    <div class="card-body">
                        <div class="form-row">
                            <div class="col-md-2 mb-2">
                                <ng-template #rtpr let-r="result" let-t="term">
                                    <div (click)="Searchitemclick(r.itemid)" (mousedown)="Searchitemclick(r.itemid)">
                                        <label for="typeahead-templatep">({{r.itemid}}) {{r.name}}</label>
                                    </div>

                                </ng-template>
                                <input id="typeahead-templatep" tooltip="ป้อน รหัสสินค้า" triggers="focus" placeholder="{{showitem}}" (focusout)="Searchitem()" type="text" class="form-control form-control-sm" [(ngModel)]="productidsearch" name="productidsearch" [ngbTypeahead]="searchpr"
                                    [resultTemplate]="rtpr" [inputFormatter]="formatterpr" />

                            </div>
                            <div class="col-md-1 mb-1">

                                <input type="number" [(ngModel)]="numberproductsearch" tooltip="ป้อน จำนวนสินค้า" triggers="focus" (focusout)="checknumproduct($event.target.value)" class="form-control form-control-sm" id="validationTooltip01" placeholder="จำนวน">
                            </div>
                            <div class="col-md-1 mb-1">
                                <select tooltip="เลือกหน่วยนับ" (change)="getunitid($event)" class="custom-select custom-select-sm">
                            <option   selected *ngFor="let item of unitlist" value="{{item.unit+item.unitid}}">{{item.unitid}}</option>
                          </select>
                            </div>
                            <div class="col-md-1 mb-1">
                                <input [disabled]="ennableeditprice" (focusout)="editpriceperunit($event.target.value)" type="text" class="form-control form-control-sm" id="validationTooltip01" tooltip="ราคาตั้ง" value="{{ priceperunit | myCurrency}}" placeholder="ราคา/หน่วย" required>
                            </div>
                            <div class="col-md-1 mb-1">
                                <input (focusout)="editdiscount()" type="number" class="form-control form-control-sm" tooltip="ป้อน %ส่วนลด#1" triggers="focus" id="validationTooltip01" [(ngModel)]="dis1" placeholder="%ส่วนลด#1" required>
                            </div>
                            <div class="col-md-1 mb-1">
                                <input (focusout)="editdiscount()" type="text" class="form-control form-control-sm" tooltip="ป้อน %ส่วนลด#2" triggers="focus" id="validationTooltip01" [(ngModel)]="dis2" placeholder="%ส่วนลด#2" required>
                            </div>
                            <div class="col-md-1 mb-1">
                                <input (focusout)="editdiscount()" type="text" class="form-control form-control-sm" tooltip="ป้อน %ส่วนลด#3" triggers="focus" id="validationTooltip01" [(ngModel)]="dis3" placeholder="%ส่วนลด#3" required>
                            </div>
                            <div class="col-md-1 mb-1">
                                <input type="text" class="form-control currency form-control-sm text-right" tooltip="รวมส่วนลด" value="{{ alldiscount | myCurrency}}" placeholder="รวมส่วนลด" readonly>
                            </div>
                            <div class="col-md-1 mb-1">
                                <input type="text" class="form-control currency form-control-sm text-right" value="{{ finalprice | myCurrency}}" tooltip="มูลค่าสินค้า" placeholder="มูลค่าสินค้า" readonly>
                            </div>
                            <div class="col-md-1 mb-1">
                                <button (click)="addsaleline()" class="btn btn-primary btn-sm font-weight-light" type="button">เพิ่มรายการ</button>
                            </div>
                            <div class="col-md-1 mb-1" *ngIf="ennablecustomer==false">
                                <div class="form-group form-check">
                                    <input [(ngModel)]="freeitem" (click)="checkfreeitemfunction($event.target.checked)" type="checkbox" class="form-control" id="exampleCheck1">
                                    <label class="form-check-label" for="exampleCheck1">ของแถม</label>
                                </div>
                            </div>
                        </div>

                        <table class="table table-hover table-bordered table-sm">
                            <thead>
                                <tr class="text-sm-center bg-light">
                                    <th class="font-weight-normal">Item</th>
                                    <th class="font-weight-normal">รหัสสินค้า</th>
                                    <th class="font-weight-normal">ชื่อสินค้า</th>
                                    <th class="font-weight-normal">จำนวน</th>
                                    <th class="font-weight-normal">หน่วยนับ</th>
                                    <th class="font-weight-normal">PACK</th>
                                    <th class="font-weight-normal">น้ำหนัก(KG.)</th>
                                    <th class="font-weight-normal">In Stock</th>
                                    <th class="font-weight-normal">ราคา/หน่วย</th>
                                    <th class="font-weight-normal">ราคาสินค้า</th>
                                    <th class="font-weight-normal">%ลด#1</th>
                                    <th class="font-weight-normal">%ลด#2</th>
                                    <th class="font-weight-normal">%ลด#3</th>
                                    <th class="font-weight-normal">รวมส่วนลด</th>
                                    <th class="font-weight-normal">มูลค่าสุทธิ</th>
                                    <th width="50px"></th>
                                    <th width="50px"></th>
                                </tr>
                            </thead>

                            <tr *ngFor="let item of lineoderlist  ; let i=index">
                                <td class="text-center">{{item.linenum}}</td>
                                <td class="text-center">{{item.iditem}}</td>
                                <td>{{item.nameproduct}}</td>
                                <td class="text-right font-weight-normal">{{item.numberpcs}}</td>
                                <td class="text-center font-weight-normal">{{item.unitid}}</td>
                                <td class="text-right font-weight-normal">{{item.packingitem | number:'1.2-2'}}</td>
                                <td class="text-right font-weight-normal">{{item.totleweight | number:'1.2-2'}}</td>
                                <td class="text-right font-weight-normal">{{Realtimeinstock(item.iditem)| number:'1.0-2'}}</td>
                                <td class="text-right font-weight-normal">{{item.priceperunit | number:'1.2-2'}}</td>
                                <td class="text-right font-weight-normal">{{item.priceproduct | number:'1.2-2'}}</td>
                                <td class="text-right font-weight-normal">{{item.discount1 | number:'1.2-2'}}</td>
                                <td class="text-right font-weight-normal">{{item.discount2 | number:'1.2-2'}}</td>
                                <td class="text-right font-weight-normal">{{item.discount3 | number:'1.2-2'}}</td>
                                <td class="text-right font-weight-normal">{{item.sumdiscount | number:'1.2-2'}}</td>
                                <td class="text-right font-weight-normal">{{item.sumallprice | number:'1.2-2'}}</td>
                                <td class="text-center">
                                    <label (click)="foreditlistlineoderclick(i,true)" class="text-info btn-link  btn-sm">แก้ไข</label>
                                </td>
                                <td class="text-center">
                                    <label (click)="deletelistlineoderclick(i,item.idline)" class="text-danger btn-link btn-sm">ลบ</label>
                                </td>
                            </tr>

                            <tr>
                                <td class="border-0 bg-light"></td>
                                <td class="border-0 bg-light"></td>
                                <td class="border-0 bg-light"></td>
                                <td class="border-0 bg-light"></td>
                                <td class="border-0 bg-light"></td>
                                <td class="border-0 bg-light"></td>
                                <td class="border-0 bg-light"></td>
                                <td class="border-0 bg-light"></td>
                                <td class="border-0 bg-light"></td>
                                <td class="border-0 bg-light"></td>
                                <td class="border-0 bg-light"></td>
                                <td class="border-0 bg-light"></td>
                                <td class="text-center bg-light font-weight-normal">มูลค่ารวม</td>
                                <td class="text-right bg-light font-weight-normal">{{sumalldiscount | number:'1.2-2'}}</td>
                                <td class="text-right bg-light font-weight-normal">{{sumallpricedis | number:'1.2-2'}}</td>
                                <td class="bg-light"></td>
                                <td class="bg-light"></td>
                            </tr>
                            <tr>
                                <td class="border-0 bg-light"></td>
                                <td class="border-0 bg-light"></td>
                                <td class="border-0 bg-light"></td>
                                <td class="border-0 bg-light"></td>
                                <td class="border-0 bg-light"></td>
                                <td class="border-0 bg-light"></td>
                                <td class="border-0 bg-light"></td>
                                <td class="border-0 bg-light"></td>
                                <td class="border-0 bg-light"></td>
                                <td class="border-0 bg-light"></td>
                                <td class="border-0 bg-light"></td>
                                <td class="border-0 bg-light"></td>
                                <td class="text-center bg-light font-weight-normal">น้ำหนักรวม(KG.)</td>
                                <td class="text-right bg-light font-weight-normal">{{sumallweight | number:'1.2-2'}}</td>
                                <td class="border-0 bg-light"></td>
                                <td class="bg-light"></td>
                                <td class="bg-light"></td>
                            </tr>

                        </table>

                    </div>
                </div>
            </div>

        </div>

        <div id="mdlSample" class="modal fade" role="dialog" [ngStyle]="{'display': mdlSampleIsOpen ? 'block' : 'none', 'opacity': 1}">
            <div class="modal-dialog modal-md">
                <div class="modal-content">
                    <div class="modal-header colhaederal">
                        <h4 class="modal-title">Report</h4>
                    </div>
                    <div class="modal-body">{{alt}} </div>
                    <div class="modal-footer" align="right">
                        <button type="button" id="btnClose" (click)="closemodel(false)" class="btn btn-danger"><i class="fa fa-times fa-fw"></i> ปิด</button></div>
                </div>
            </div>
        </div>

        <div class="modal fade" id="testmessage" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="exampleModalLabel">Modal title</h5>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                  </button>
                    </div>
                    <div class="modal-body">
                        ...
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                        <button type="button" class="btn btn-primary">Save changes</button>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>