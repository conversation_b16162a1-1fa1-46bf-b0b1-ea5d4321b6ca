import { Component, OnInit } from '@angular/core';
import { checkheader } from './../coverpage/coverpage.component';
import {listLocales} from 'ngx-bootstrap/chronos';
import { element } from 'protractor';
import { paymenttype } from './../completeinvoice/completeinvoice.component';
import { NgbCalendar, NgbDateStruct } from '@ng-bootstrap/ng-bootstrap';
import { HttpClient, HttpHeaders, HttpHeaderResponse, HttpHandler } from '@angular/common/http';

import { WebapiService } from '../webapi.service';
import {Observable} from 'rxjs';
import {debounceTime, map} from 'rxjs/operators';
import { forEach } from '@angular/router/src/utils/collection';
import {ActivatedRoute, Router} from '@angular/router';
import { FormGroup, FormBuilder } from '@angular/forms';
import { ThrowStmt } from '@angular/compiler';
import { BsLocaleService } from 'ngx-bootstrap/datepicker';
import { FormControl } from '@angular/forms';

export interface payment{
  paymtermid: string;
  paymenttype: string;
}

export interface idsaleoder{
  text: string;
  saleid: string;
  year: number;
  month: string;
  runnumber: number;
}

export interface lineoder{
  idline:number;
  id:string;
  linenum:number;
  custaccount:string;
  iditem: string;
  nameproduct: string;
  numberpcs: number;
  unitid: string; 
  vattype:string;
  packingitem: number;
  totleweight: number;
  priceperunit: number;
  priceproduct: number; 
  discount1: number;
  discount2: number;
  discount3: number;
  sumdiscount: number;
  sumallprice: number;
  wh:string;
  checkpcs:string;
  disst:string;
  eidtable:number;
  saleid:string;
  dateshipping:string;
};

export interface productlistauto{
  inventlocationid:	string;
itemgroupid:	string;
itemid:	string;
catname:	string;
name:	string;
price:	number;
taxpackagingqty:	number;
unitid:	string;
packaginggroupid:	string;
netweight:	number;
}
export interface headerodersale{
  idhead:number;
  id: string;
Custaccount:string;
vattype:string;
paymenttype:string;
deliverytype:string;
locationno:string;
amount: number;
discount:number;
totalweigth:number;
note:string;
remark:string;
dateshipping:string;
wh:string;
saleid:string;
locationde:string;
filetype:string;
filename:string;
}
export interface promotion{
  itemrelation:string;
  accountrelation:string;
  accountcode:string;
  quantityamountfrom:number; 
  quantityamountto:number;
  amount:number;
  percent1:number;
  percent2:number;
  percent3:number
  relation:string;
}

export interface unitid{
  inventlocationid: string;
  unitid:string;
  unit:string;
  itemid:string;
}

export interface getwherehosae{
  wh: string;
  num:number;
  sumallpricedis: number;
sumalldiscount:number;
sumallweight:number;
}
export interface addressinv{
  name: string;
  accountnum: string;
  address: string;
  locationno: string;
};
export interface addressdary{
  name: string;
  accountnum: string;
  address: string;
  locationno: string;
};

@Component({
  selector: 'app-editsaloderreview',
  templateUrl: './editsaloderreview.component.html',
  styleUrls: ['./editsaloderreview.component.css']
})



export class EditsaloderreviewComponent implements OnInit {
  mdlSampleIsOpen : boolean = false;
  alt='';
  checkreload=true;

  realldiscount:number;
reallpcs:number;
regetunitprice:number;
reprice:number;
redis1:number;
redis2:number;
redis3:number;
refinalprice:number;
renumproduct:number;
renumberproductsearch:number;
disproductline=true;
filetype='';
filename='';
usseredit='';
accountnum='';
olddis1=0.00;
olddis2=0.00;
olddis3=0.00;
upperdis='';
eidtable:number;
ennableeditprice=true;
openbtnclose=false;
getpromotionedit=false;
checkpacking=0;
loaduom:any;
uom:any;
uomclick:any;
getcheckpcs='P';
setinheader: any;
getwh: any=[];
warehouselist:getwherehosae[]=[];
wh='';
getallidsaleoder:idsaleoder[]=[];
getrunnumber='';
checkadditem=false;
getidsaleoder:string;
runnumber:number;
filterargs = {title: 'hello'};
items = [{title: 'hello world'}, {title: 'hello kitty'}, {title: 'foo bar'}];
selectedpaymentmap='';
countryForm: FormGroup;
testitemid:string;
getpackingprice=0.00;
getunitprice=0.00;
checkunitprice:string;
inventlocationid: string;
showitem="รหัสสินค้า";
headerlist:headerodersale[]=[];
headerlistsave:headerodersale[]=[];
freeitem=false;
  dateshipping:string;
  promotionlist :promotion[]=[];
  accoutnreration='';
  pricegroup='';
productauto: any;
  timerInterval:any;
  lineoderlist:lineoder[]=[];
  lineoderlistsave:lineoder[]=[];
  today=new Date();
  index: number;
 x=1;
  numlist=0;
  customerno: string;
  locationno='';
  notesoinput='';
remarksoinput='';
  checkbuttonsave=false;
  sumalldiscount=0;
  sumallpricedis=0;
  sumallweight=0;
  productname: string;
  selectunitid='';
  packingid='';
  numpackingitem=0;
  numline=1;
  weightproduct=0;
  alldatenow:string;
  idsaleoder='';
  testpara= '';
  model: any[]=[];
  productidsearch:any;
  numberproductsearch=null;
  searching = false;
  searchFailed = false;
url: string;
salecustomer ='';
priceperunit ='0';
selectpayment: string;
amount='0';
totalweight='0.0';
discount='0';
iditem='';
price=0;
allpcs=0;
allsumprice=0.0;
dis1=0.00;
dis2=0.00;
dis3=0.00;
alldiscount=0.0;
finalprice=0.0;
numproduct: number;
productlist: any[];
unitlist: unitid[]=[];
daddress: addressdary[];
inaddress: addressinv[];
deliveryonclick='';
deliverytype=[{'deliver':'รถบริษัท'},{'deliver':'รับเอง'},{'deliver':'ขนส่ง'},{'deliver':'ประเภทการขนส่ง'}]
paymentlist: payment[]=[];
vatlist=[{'vattype':'VAT ไม่ยื่น'},{'vattype':'VAT'}];
vatselect: string;
salecustomerlist: any[];
  customers: any[];
  showvat=true;
  idsoedit ='';
  company='ป้อนชื่อ หรือ รหัสลูกค้า';
  shownovat=true;
Namelogin:any[];
setin:any;
setloadcustomeredit:any;
setloadaddress:any;
bsValue :Date;

fromdate='';
todate='';
fromDate: NgbDateStruct;
toDate: NgbDateStruct;
Datatodate:any;
Datafromdate:any;
locale = 'th';
locales = listLocales();
ennablecustomer=false;
locationnodelie='';
idforshow='';
idhead=0;
time='';
dissave =false;
setpackde=0;
Instock='';
StockAllList=[];
  constructor(private http: HttpClient,private service: WebapiService, private route: ActivatedRoute,private router: Router,private fb: FormBuilder,private calendar: NgbCalendar,private localeService: BsLocaleService) {
    localStorage.removeItem('DataSOderlist');
    this.bsValue=new Date();
    this.url=service.geturlservice();
    this.fromDate = calendar.getPrev(calendar.getToday(), 'd', 30);
    this.toDate = calendar.getToday();
    this.fromdate='';
    this.todate='';
    this.Datatodate= new Date(this.toDate.day,this.toDate.month-1,this.toDate.year);
    this.Datafromdate= new Date(this.fromDate.year, this.fromDate.month-1, this.fromDate.day);
    this.getdate();
    this.LoadAllstock();
    this.model=null;
    this.time=this.bsValue.getHours()+':'+this.bsValue.getMinutes()+':'+this.bsValue.getSeconds();
    //ดึงข้อฒุลการ เข้าสู่ระบบ
    //[(ngModel)]="alldiscount"
this.Namelogin=JSON.parse(sessionStorage.getItem('login'));
this.usseredit=this.Namelogin[0].salegroup;
this.setpackde=this.Namelogin[0].pack;
if (this.Namelogin==null){
  this.router.navigate(['login']);
   }
this.idsoedit=this.route.snapshot.queryParams.idso;

if(this.idsoedit==undefined  ){
  
 
  localStorage.removeItem('DataSOderreview');
  localStorage.removeItem('DataSOderlist');


} else {
  this.idsaleoder=this.idsoedit;
  this.idforshow=this.idsoedit;
  this.setloadcustomeredit=setInterval(()=>this.selecttoeditheader(this.idsoedit),800); //300

}

var getdate='';
var day=this.today.getDate();
var month=this.today.getMonth()+1;
var year=this.today.getFullYear();
if(day.toString().length >1){
getdate=day.toString();
}else {
getdate='0'+day.toString();
}
if(month.toString().length >1){
  this.dateshipping=year.toString()+'-'+month.toString()+'-'+getdate;
}else {
  this.dateshipping=year.toString()+'-0'+month.toString()+'-'+getdate;
}
this.loadrunnumberid(this.dateshipping);
   }

   getdate(){
    this.fromdate = `${this.Datafromdate.getFullYear()}-${this.Datafromdate.getMonth()+1}-${this.Datafromdate.getDate()}`;
    this.todate = `${this.Datatodate.getFullYear()}-${this.Datatodate.getMonth()+1}-${this.Datatodate.getDate()}`;
   }

  search = (text$: Observable<any>) =>

  //Autocomplete ลูกค้า
text$.pipe(
  debounceTime(200),
  map(term => term === '' ? []
    : this.customers.filter(v => v.name.toLowerCase().indexOf(term.toLowerCase()) > -1 ||  v.accountnum.toLowerCase().indexOf(term.toLowerCase()) > -1).slice(0, 50))
);
formatter = (x: {name: string,accountnum :string,regnum:string }) => x.name + ' ('+x.accountnum+')'+'('+x.regnum+')';

searchpr = (text$: Observable<any>) =>


//Autocomplete รหัสสินค้า
text$.pipe(
  debounceTime(200),
  map(term => term === '' ? []
    : this.productauto.filter(v => v.itemid.toLowerCase().indexOf(term.toLowerCase()) > -1 ||  v.name.toLowerCase().indexOf(term.toLowerCase()) > -1).slice(0, 50))
);
formatterpr = (x: {itemid: string,name :string}) => x.itemid;
  ngOnInit() {
    if(this.Namelogin[0].accountnum != undefined){
      this.getcustomersalefunction(this.Namelogin[0].accountnum);
  this.ennablecustomer=true;
  this.getproductid(this.Namelogin[0].salegroup);
  
     }
  }

    //ดึงข้อมูลการรัน ID Sale Oder จาก Database
    loadrunnumberid(date) {
      var text = "SW";
      var zero='0';
      var num=0;
      var saleid=this.Namelogin[0].salegroup;
      var month=date.substring(5,7);
      var year=date.substring(0,4);
      this.http.get<any>(this.url+'checknumbersaleoder/'+text+'/'+saleid+'/'+year.toString()+'/'+month.toString()+'/'+0).subscribe(res =>{
        this.getcostomerauto();
        num=res[0].num+1;
       for(var i=0; i<3- JSON.stringify(res[0].num).length;i++){
      zero =zero+'0';
       }
      
        this.getrunnumber=zero+num;
       
        this.idsaleoder=this.makeidsaleOder(this.getrunnumber,num,date);
      
      
      });
      
     }
  
  //ดึงรหัสลูกค้ามาใช้ใน Autocomplete
    getcostomerauto(){
      var idsale=this.Namelogin[0].salegroup;
      if(this.Namelogin[0].salegroup==='admin'){
  idsale='%20';
      } else {
        idsale=this.Namelogin[0].salegroup;
      }
    
      this.http.get<any>(this.url + 'customerauto/'+idsale).subscribe(res =>{
        this.customers=res;
 
      })
    }
  //ดึงรหัสสินค้ามาใช้ใน Autocomplete
  getproductid(accountnum){
    this.http.get<any>(this.url + 'productauto/'+accountnum).subscribe(res => {
      if(res.length >0 ){
        this.productauto=res;
     
      }
    });
  }
  
  //ตั้งสินค้าให้เป็นของแุถม
  checkfreeitemfunction(value){
    if(value==true){
      this.checkadditem=value;
      this.reprice=parseFloat(this.priceperunit);
      this.redis1=this.dis1;
  this.redis2=this.dis2;
  this.redis3=this.dis3;
  this.refinalprice=this.finalprice;
  this.reallpcs=this.allpcs;
  this.regetunitprice=this.getunitprice;
  this.realldiscount=this.alldiscount;
      this.priceperunit='0';
      this.dis1=0;
      this.dis2=0;
      this.dis3=0;
      this.finalprice=0;
    
      this.getunitprice=0;
      this.alldiscount=0;
  
    } else {
      this.checkadditem=value;
      this.priceperunit=this.reprice.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2});
      this.dis1=this.redis1;
      this.dis2=this.redis2;
      this.dis3=this.redis3;
      this.finalprice=this.refinalprice;
      this.allpcs=this.reallpcs;
      this.getunitprice=this.regetunitprice;
      this.alldiscount=this.realldiscount;
    }
   
  }
  
  //ดึงข้อมูล Sale Heaader จาก Database มาแก้ไข
    selecttoeditheader(valueid) {
    
      this.http.get<any>(this.url+'find_saleheader/'+valueid).subscribe(res =>{
        if(res.length >0) {
          var daya= new Date();
          clearInterval(this.setloadcustomeredit);
       
          var getdate= res[0].dateid.toString();
          this.dateshipping=getdate;
        
          var day = getdate.substring(0,2);
          var month =getdate.substring(3,5);
          var year=getdate.substring(6,10);
          //this.bsValue.setDate(day);
          if(day==daya.getDate() && month==daya.getMonth()+1 ){
            
          } else {
            this.bsValue=new Date(year,month-1,day);
          }
          this.remarksoinput=res[0].remark;
  this.selectedsetsaleheader(res);
  this.setloadaddress=setInterval(()=> this.getcustomersalefunction(this.company),1000);

    this.mappaymenttype(res[0].paymenttype);
    this.usseredit=res[0].SalesId;
  this.company=res[0].SalesName;
  this.deliveryonclick=res[0].DlvMode;
  /*this.Searchcustomeraddress(res[0].InvoiceAccount);*/
  
  if(res[0].DlvMode==='ขนส่ง') {
    this.deliverytype=[{'deliver':'รถบริษัท'},{'deliver':'รับเอง'},{'deliver':'ขนส่ง'}];
  } else if(res[0].DlvMode==='รับเอง') {
    this.deliverytype=[{'deliver':'รถบริษัท'},{'deliver':'ขนส่ง'},{'deliver':'รับเอง'}];
  } else if(res[0].DlvMode==='รถบริษัท') {
    this.deliverytype=[{'deliver':'รับเอง'},{'deliver':'ขนส่ง'},{'deliver':'รถบริษัท'}];
  }
if(res[0].vattype==='VAT') {
  this.vatselect=res[0].vattype;
    this.vatlist=[{'vattype':'VAT ไม่ยื่น'},{'vattype':'VAT'}];
  } else if (res[0].vattype==='NO VAT') {
    this.vatselect=res[0].vattype;
    this.vatlist=[{'vattype':'VAT'},{'vattype':'VAT ไม่ยื่น'}];
  } else if(res[0].vattype==='VAT ไม่ยื่น'){
    this.vatselect=res[0].vattype;
    this.vatlist=[{'vattype':'VAT'},{'vattype':'VAT ไม่ยื่น'}];
  }
  
  
        }
       
  
      },error=>{
   
    
      });
  
     
      
    
     
  
      
    this.openModal(false,'กำลังโหลดข้อมูล',false);
    }
  
  //สร้าง SaleOder ID ใหม่จากวันที่ ที่เลือก
  clickdateshipping(value){
  this.loadrunnumberid(value);
  }
    setsaleheader(id,wh) {
      this.headerlist=[];
      this.headerlist.push({
        idhead:0,
        id: id,
        Custaccount: this.customerno,
        vattype: this.vatselect,
        paymenttype: this.selectpayment,
        deliverytype: this.deliveryonclick,
        locationno:this.locationno,
        amount: this.sumallpricedis,
        discount:this.sumalldiscount,
        totalweigth:this.sumallweight,
        note:this.notesoinput,
        remark:this.remarksoinput,
        dateshipping:this.dateshipping,
        wh:wh,
        locationde:this.locationnodelie,
        saleid:this.Namelogin[0].salegroup,
        filetype:'-',
        filename:'-'
      });
    }
  
  //เก็บข้อมูล Sale Header ที่ดึงมาจาก Database เก็บใส่ Array
  selectedsetsaleheader(value:any){
    this.filetype=value[0].filetype;
    this.filename=value[0].filename;
    this.idhead=value[0].idhead;
    this.locationno=value[0].InvAddress;
    this.locationnodelie=value[0].DeliveryAddress;
    this.notesoinput=value[0].CustomerRef;
    this.remarksoinput=value[0].remark;
    for(var i=0;i<value.length;i++){
      this.headerlist.push({
        idhead:value[i].idhead,
        id: value[i].id,
        Custaccount: value[i].InvoiceAccount,
        vattype: value[i].TaxGroup,
        paymenttype: value[i].Payment,
        deliverytype: value[i].DlvMode,
        locationno:value[i].InvAddress,
        amount: value[i].amount,
        discount:value[i].discount,
        totalweigth:value[i].totalweigh,
        note:value[i].notesoinput,
        remark:value[i].remark,
        dateshipping:value[i].ReceiptDateRequested,
        wh:value[i].InventLocationId,
        locationde:value[i].DeliveryAddress,
        saleid:this.Namelogin[0].salegroup,
        filetype:value[i].filetype,
        filename:value[i].filename
      });
    }
    if(this.idsoedit !='' || this.idsoedit !=undefined){
      this.lineoderlist=[];
      this.http.get<any>(this.url+'find_saleline/'+this.idsoedit).subscribe(res =>{
     
       if(res.length >0){
         var d1,d2,d3;
        // alert(JSON.stringify(res));
    for(var i=0; i< res.length;i++) {
    
      if(res[i].disc1==null){
        d1=0;
      } else {
        d1=res[i].disc1;
      }
      if(res[i].disc2==null){
        d2=0;
     } else {
      d2=res[i].disc2;
    }
     if(res[i].disc3==null){
      d3=0;
     } else {
      d3=res[i].disc3;
     }
  
      this.lineoderlist.push({
        idline:res[i].idline,
        id: res[i].id,
        linenum: res[i].linenum,
        custaccount:res[i].CustAccount,
        iditem: res[i].ItemId,
        vattype:res[i].TaxItemGroup,
        nameproduct: res[i].Name,
       numberpcs: res[i].SalesQty,
       unitid:  res[i].SalesUnit,
       packingitem: res[i].packqty,
      totleweight: res[i].totalweight,
      priceperunit: res[i].PriceUnit,
      priceproduct: res[i].totaldisc+res[i].LineAmount,
      discount1: res[i].IVZ_Percent1_CT,
      discount2: res[i].IVZ_Percent2_CT,
      discount3: res[i].IVZ_Percent3_CT,
      sumdiscount: parseFloat(d1) +parseFloat(d2)+parseFloat(d3),
      sumallprice: res[i].LineAmount,
      wh: res[i].InventLocationId,
      checkpcs:res[i].checkpcs,
      disst:res[i].disstate,
      eidtable: res[i].eidtable,
      saleid:res[i].SalesGroup,
      dateshipping:this.dateshipping+ ' '+this.time
      });
 ;
    }
    
    this.idsaleoder=res[0].id;
    if(this.lineoderlist.length >0) {
    this.sumalldiscount=0;
    this.sumallpricedis=0;
    this.sumallweight=0;
 
    for (var i=0;i< this.lineoderlist.length; i++) {
    this.sumalldiscount =this.sumalldiscount + parseFloat(this.lineoderlist[i].sumdiscount.toFixed(2));
    this.sumallpricedis =this.sumallpricedis + parseFloat(this.lineoderlist[i].sumallprice.toFixed(2));
    this.sumallweight = this.sumallweight + parseFloat(this.lineoderlist[i].totleweight.toFixed(2));
    }
 
   }
   
 }
  
   }, errar => {
     status=errar.status;
     alert('เกิดปัญหาในการ Process ข้อมูล กรุณาลองใหม่อีกครั้ง');
     location.reload();
     //this.openModal(true,'เกิดปัญหาในการ Process ข้อมูล กรุณาลองใหม่อีกครั้ง',false);
   });
 }
  }
  
  //ดึงข้อมูล Sale Line จาก Database มาแก้ไข
    selectsaleoderline() {
      this.setdefultUnit();
      if(this.idsoedit =='' || this.idsoedit ==undefined){
        this.setsaleheader(this.idsaleoder,this.inventlocationid);
      
      }
  
      this.getproductid(this.customerno);
   
     
      var status=200;
      do {
        
     
      if(this.lineoderlist.length<1){
        if(this.deliveryonclick !=''){
  
          if(this.idsoedit !='' || this.idsoedit !=undefined){
           this.lineoderlist=[];
           this.http.get<any>(this.url+'find_saleline/'+this.idsoedit).subscribe(res =>{
           var ch=0;
            if(res.length >0){
             // alert(JSON.stringify(res));
         for(var i=0; i< res.length;i++) {
          if(this.lineoderlist.length>0){
            for(var x=0;x<this.lineoderlist.length;x++){
             if(this.lineoderlist[x].iditem==res[i].ItemId && this.lineoderlist[x].numberpcs== res[i].SalesQty && this.lineoderlist[x].priceperunit== res[i].PriceUnit){
            ch=1
             }
            }
            if(ch==0){
              this.lineoderlist.push({
                idline:res[i].idline,
                 id: res[i].id,
                 linenum: res[i].linenum,
                 custaccount:res[i].CustAccount,
                 iditem: res[i].ItemId,
                 nameproduct: res[i].Name,
                 vattype:res[i].TaxItemGroup,
                numberpcs: res[i].SalesQty,
                unitid:  res[i].SalesUnit,
                packingitem: res[i].packqty,
               totleweight: res[i].totalweight,
               priceperunit: res[i].PriceUnit,
               priceproduct: res[i].totaldisc+res[i].LineAmount,
               discount1: res[i].IVZ_Percent1_CT,
               discount2: res[i].IVZ_Percent2_CT,
               discount3: res[i].IVZ_Percent3_CT,
               sumdiscount: parseFloat(res[i].disc1) +parseFloat(res[i].disc2)+parseFloat(res[i].disc3),
               sumallprice: res[i].LineAmount,
               wh: res[i].InventLocationId,
               checkpcs:res[i].checkpcs,
               disst:res[i].disstate,
               eidtable: res[i].eidtable,
               saleid:res[i].SalesGroup,
               dateshipping:this.dateshipping+ ' '+this.time
               });
            } else {
            
            }
            } else {
              this.lineoderlist.push({
                idline:res[i].idline,
                 id: res[i].id,
                 linenum: res[i].linenum,
                 custaccount:res[i].CustAccount,
                 iditem: res[i].ItemId,
                 vattype:res[i].TaxItemGroup,
                 nameproduct: res[i].Name,
                numberpcs: res[i].SalesQty,
                unitid:  res[i].SalesUnit,
                packingitem: res[i].packqty,
               totleweight: res[i].totalweight,
               priceperunit: res[i].PriceUnit,
               priceproduct: res[i].totaldisc+res[i].LineAmount,
               discount1: res[i].IVZ_Percent1_CT,
               discount2: res[i].IVZ_Percent2_CT,
               discount3: res[i].IVZ_Percent3_CT,
               sumdiscount: parseFloat(res[i].disc1) +parseFloat(res[i].disc2)+parseFloat(res[i].disc3),
               sumallprice: res[i].LineAmount,
               wh: res[i].InventLocationId,
               checkpcs:res[i].checkpcs,
               disst:res[i].disstate,
               eidtable: res[i].eidtable,
               saleid:res[i].SalesGroup,
               dateshipping:this.dateshipping+' '+this.time
               });
            ;
            }
       
         }
         
         this.idsaleoder=res[0].id;
         if(this.lineoderlist.length >0) {
         this.sumalldiscount=0;
         this.sumallpricedis=0;
         this.sumallweight=0;
      
         for (var i=0;i< this.lineoderlist.length; i++) {
         this.sumalldiscount =this.sumalldiscount + parseFloat(this.lineoderlist[i].sumdiscount.toFixed(2));
         this.sumallpricedis =this.sumallpricedis + parseFloat(this.lineoderlist[i].sumallprice.toFixed(2));
         this.sumallweight = this.sumallweight + parseFloat(this.lineoderlist[i].totleweight.toFixed(2));
         }
      
        }
        
      }
       
        }, errar => {
          status=errar.status;
          alert('เกิดปัญหาในการ Process ข้อมูล กรุณาลองใหม่อีกครั้ง');
          location.reload();
          //this.openModal(true,'เกิดปัญหาในการ Process ข้อมูล กรุณาลองใหม่อีกครั้ง',false);
        });
      }
        } else {
          alert('กรุณาเลือก การขนส่ง');
          //this.openModal(true,'กรุณาเลือก การขนส่ง',false);
        }
      }
  
    } while (status==400);
  
  
    }
    //เก็บข้อมูล วันที่
    clickdate(data){
  var month=this.bsValue.getMonth()+1;
  if(month <10){
    this.dateshipping=this.bsValue.getFullYear()+'-'+'0'+month+'-'+this.bsValue.getUTCDate();
  } else {
    this.dateshipping=this.bsValue.getFullYear()+'-'+month+'-'+this.bsValue.getUTCDate();
  }
  
    }
  
    //เก็บข้อมูล ประเภทการ ขนส่งสินค้า
    selectdelivery(value) {
      this.deliveryonclick=value;
    }
  
    //เก็บข้อมูล ประเภทการชำระเงิน
    selectpaymentfn(value) {
      this.selectpayment=value;
    }
  
    //ตั้งค่าข้อมูล ประเภท ขนส่งสินค้า
  mappaymenttype(paymenttype) {
    this.selectpayment=paymenttype;
  /*if(paymenttype==='เงินสด') { 
    this.dis3=3.00;
    this.selectpayment=paymenttype
    this.paymentlist=[{'paymenttype':'เงินสด-เครดิต'},{'paymenttype':'เครดิต'},{'paymenttype':'เงินสด'}];
  } else if (paymenttype==='เครดิต') {
    this.selectpayment=paymenttype
    this.paymentlist=[{'paymenttype':'เงินสด-เครดิต'},{'paymenttype':'เงินสด'},{'paymenttype':'เครดิต'}];
  }
  */
   
  this.http.get<any>(this.url+'paymentlist').subscribe(res =>{
  this.paymentlist=[];
    if(res.length > 0){
   for (var i=0;i< res.length; i++){
     if(res[i].paymtermid==paymenttype){
      this.paymentlist.push({
        paymtermid:res[i].paymtermid,
        paymenttype: res[i].paymenttype
      })
     } else {
      this.paymentlist.unshift({
        paymtermid:res[i].paymtermid,
        paymenttype: res[i].paymenttype
      })
     }
  
   }
  
    }
    this.getwarehouse();
  
  
  });
  
  }

  selectlocationde(value){
this.locationnodelie=value
  }
  
  //ตั้งค่าข้อมูล ประเภทภาษี
  maptaxgrouptype(vattype) {
if(this.idsoedit==undefined){ 
if(vattype==='VAT') {
  this.vatselect=vattype;
    this.vatlist=[{'vattype':'VAT ไม่ยื่น'},{'vattype':'VAT'}];
  } else if (vattype==='NO VAT') {
    this.vatselect=vattype;
    this.vatlist=[{'vattype':'VAT'},{'vattype':'VAT ไม่ยื่น'}];
  } else if(vattype==='VAT ไม่ยื่น'){
    this.vatselect=vattype;
    this.vatlist=[{'vattype':'VAT'},{'vattype':'VAT ไม่ยื่น'}];
  }
  
}

  }
  
  
  //ค้าหาที่อยู่ จากชื่อ หรือ รหัสลูกค้า
  Searchcustomeraddress(valuecustomer) { // without type info
//alert('Clik');
    this.daddress=[];
    this.inaddress=[];
    this.http.get<any>(this.url +'customer_list' + '/%20/%20/' + valuecustomer + '/%20/%20').subscribe(res =>{
    
      if(res.length >0) {
        this.customerno=res[0].accountnum;
        this.getaddressdary(res[0].accountnum);
     
        this.getaddressinv(res[0].accountnum);
        //this.locationno=this.inaddress[0].locationno;
        
        this.locationno=res[0].locationno;
        this.locationnodelie=res[0].locationno;
      }
      
     
   
        },error =>{
          alert('เกิดปัญหาในการ Process ข้อมูล กรุณาลองใหม่อีกครั้ง');
          //this.openModal(true,'เกิดปัญหาในการ Process ข้อมูล กรุณาลองใหม่อีกครั้ง',false);
        });
  }
  
  //เก็บข้อมูล เลขที่ ที่อยู่
    setlocationno(value) {
  this.locationno=value;
    }
  
    //เก็บข้อมูล ภาษี
    selectvat(value) {
  this.vatselect=value;
  //alert(this.vatselect);
  if(value==='VAT ไม่ยื่น'){
    this.notesoinput='บิลเขียว';
  } else {
    this.notesoinput='';
  }
    }
  
    //อัพเดทข้อมูล การรัน เลขที่ SaleOder
  updaterunnumber(value: any){
  
    var urlpost=`${ this.url }${'updaterunnumber'}/${value[0].text}/${value[0].saleid}/${value[0].year}/${value[0].month}/${value[0].runnumber}`;
    this.http.post(urlpost,'').subscribe(res =>{
      if(res==true){
        this.getallidsaleoder=[];
        this.loadrunnumberid(this.dateshipping);
        alert('บันทึกข้อมูลเสร็จสิ้น');
        //this.openModal(true,'บันทึกข้อมูลเสร็จสิ้น',true);
        this.company='';
        this.idsoedit=undefined;
      }
   
    });
  }
    /*/so/createso/:id/:Custaccount/:vattype/:paymenttype/:deliverytype/:locationno/:amount/:discount/:totalweight/:note/:remark' 
      
    */
   //ลบข้อมูล SaleOder เมื่อมีการ อัพเดทข้อมูล
   deleteebeforupdate() {
  if(this.idsoedit != undefined) {
    var urlpost=`${ this.url }${'delete_sale_line'}/${ this.idsoedit}/${this.Namelogin[0].salegroup}`;
    this.http.post(urlpost,'').subscribe(res=> {
      this.dissave=false;
     this.savesaleoderlist();
     
    })
  
  }
    
   }
  clrscrsaleheader(){
  
    this.model=null;
  this.idsaleoder='';
  this.customerno='';
  this.vatselect='';
  this.selectpayment='';
  this.deliveryonclick='';
  this.locationno='';
   this.sumallpricedis=0; 
    this.sumalldiscount =0;
     this.sumallweight=0;
     this.inaddress=[];
     this.daddress=[];
     this.notesoinput='';
     this.remarksoinput='';
     //alert('บันทึกข้อมูลเสร็จสิ้น');
  }
  
   // บันทึกข้อมูล Sale Oder Header
  savesaleoderlist() {
    var noteina='';
  var noteinb='';
  var remarka='';
  var remarkb='';
    if(this.deliveryonclick=='') {
      alert('กรุณาเลือก การขนส่ง');
      //this.openModal(true,'กรุณาเลือก การขนส่ง',false);
    } else{
      if(this.remarksoinput=='') {
        this.remarksoinput='N';
        remarkb='N';
      } else{
        remarka=  this.remarksoinput.replace('/','-');
        remarkb=remarka.replace('%','เปอร์เซ็น');
      }
      if(this.notesoinput=='') {
        this.notesoinput='N';
        noteinb='N';
      } else {
        this.notesoinput.replace('/','-');
        noteina=this.notesoinput.replace('/','-');
        noteinb=noteina.replace('%','เปอร์เซ็น');
      }
     if(this.wh==''){
       this.wh='';
     }
     if(this.headerlist.length>0){
      this.wh=this.headerlist[0].wh;
     }
     if(this.Namelogin[0].salegroup=='admin'){
  
    } else {
     this.usseredit=this.Namelogin[0].salegroup;
    }
    this.dateshipping=this.bsValue.getFullYear()+'-'+(parseInt(this.bsValue.getMonth().toString())+1)+'-'+this.bsValue.getDate();
    var body={
      Data:[{
        id:this.idsaleoder,
        Custaccount:this.customerno,
        vattype:this.vatselect,
        paymenttype:this.selectpayment,
        deliverytype:this.deliveryonclick,
        locationno:this.locationno ,
        amount:this.sumallpricedis,
        discount:this.sumalldiscount ,
        totalweight:this.sumallweight,
        note:noteinb,
        remark:remarkb,
        dateshipping:this.dateshipping+' '+this.time,
        saleid:this.usseredit,
        wh:this.wh,
        locationde:this.locationnodelie,
        idhead: this.idhead,
        filetype:this.filetype,
        filename:this.filename
      }]
    }

    //alert(this.locationno+'/'+this.locationnodelie);
   var urlpost=`${ this.url }${ 'createsonew' }`;
          if(this.daddress != null) {
             this.http.post(urlpost,body).subscribe(res => {
               if(res==true) {
              this.savesalelinefn();
               } else {
                 
               }
             },err=>{
               alert('ไม่สามารถบันทึกข้อมูลได้กรุณาลองใหม่อีก ครั้ง');
               return;
             })
           } else {
             alert('กรุณาเลือกลูกค้า');
             //this.openModal(true,'กรุณาเลือกลูกค้า',false);
           }
       this.sumalldiscount=0;
       this.sumallpricedis=0;
       this.sumallweight=0;
      this.remarksoinput='';
      this.notesoinput='';
    }
  
   
  }
  /*
  /so/savesaleline/:id/:linenumber/:custaccount/:itemid/:saleqty/:packqty/:vattype/:price/:percent1/:percent2/:percent3/:lineweight' */
  //บันทึกข้อมูล SaleLine แบบยังไม่แยก คลังสินค้า
  savesalelinefn() {
    this.dateshipping=this.bsValue.getFullYear()+'-'+(parseInt(this.bsValue.getMonth().toString())+1)+'-'+this.bsValue.getDate();
    var i=0;
    var line=[];
if(this.lineoderlist.length>0){
  for(var i=0;i<this.lineoderlist.length;i++){
    line.push({
      id:this.lineoderlist[i].id,
      linenum:this.lineoderlist[i].linenum,
      custaccount:this.customerno,
      iditem:this.lineoderlist[i].iditem,
      numberpcs:this.lineoderlist[i].numberpcs,
      packingitem:this.lineoderlist[i].packingitem,
      vattype:this.vatselect,
      priceperunit:this.lineoderlist[i].priceperunit,
      discount1:this.lineoderlist[i].discount1,
      discount2:this.lineoderlist[i].discount2,
      discount3:this.lineoderlist[i].discount3,
      totleweight:this.lineoderlist[i].totleweight,
      dateshipping:this.dateshipping+ ' '+this.time,
      saleid:this.lineoderlist[i].saleid,
      checkpcs:this.lineoderlist[i].checkpcs,
      disst:this.lineoderlist[i].disst,
      eidtable:this.lineoderlist[i].eidtable,
      idline:this.lineoderlist[i].idline
    });
  }

} else {
  return;
}
      var urlpost= `${ this.url }${ 'savesalelinenew' }`;
      this.http.post(urlpost,{Data:line}).subscribe(res =>{
    this.clrscrlinelist();
    this.company='';
    this.clrscrsaleheader();
    this.headerlist=[];
    if(this.Namelogin[0].accountnum != undefined){
      this.getcustomersalefunction(this.Namelogin[0].accountnum);
      }
      if(this.idsoedit==undefined){
   
    
      }else {
        alert('บันทึกข้อมูลเสร็จสิ้น');
        this.closeeditlistoder();
      }
      this.dissave=false;
    //this.router.navigate(['/soreview']);
    this.clrscrlinelist();
    this.company='';
    this.clrscrsaleheader();
    if(this.Namelogin[0].accountnum != undefined){
      this.getcustomersalefunction(this.Namelogin[0].accountnum);
      }
    //this.openModal(true,'บันทึกข้อมูลเสร็จสิ้น',true);
    this.headerlist=[];
    this.lineoderlist=[];
    //this.router.navigate(['/soreview']);
  
  this.sumalldiscount=0;
  this.sumallpricedis=0;
  this.sumallweight=0;
  this.x++;
    
    })
   
  /*for (var i=0; i<this.lineoderlist.length; i++) {
    
    var urlpost= `${ this.url }${ '/so/savesaleline' }/${ this.lineoderlist[i].id}/${ i+1 }/${ this.inaddress[0].accountnum }/${ this.lineoderlist[i].iditem}/${ this.lineoderlist[i].numberpcs}/${ this.lineoderlist[i].packingitem}/${ this.vatselect }/${ this.lineoderlist[i].priceperunit}/${ this.lineoderlist[i].discount1 }/${ this.lineoderlist[i].discount2 }/${ this.lineoderlist[i].discount3 }/${ this.lineoderlist[i].totleweight}`;
   alert(urlpost);
    this.http.post(urlpost,'').subscribe(res =>{
    if(res !=  true) {
      alert('SaveLineFail!!!!');
    } 
    });
    
  }*/
  
  }
  
  //ดึงข้อมูลราคาสินค้าจากรหัสสินค้า
  Searchproductlist(productid) {
    var body={
      inventlocationid:'',
      itemgroupid:'',
      itemid:productid,
      catname:'',
    name:''
    }
  
  this.http.post<any>(this.url + 'product_list',body).subscribe(res => {
    if(res.length >0 ){
      this.eidtable=res[0].eidtable;
      if(res[0].eidtable==0){
        this.ennableeditprice=true;
      } else {
        this.ennableeditprice=false;
      }
  this.productlist=res;
  this.checkpromotionfunction(productid);
  for (var i=0;i< this.promotionlist.length;i++) {
    if( this.promotionlist[i].amount >0){
      this.priceperunit = this.promotionlist[i].amount.toString();
    
    }
  }
  
  this.iditem=res[0].itemid;
  this.numproduct=parseInt(JSON.stringify(res[0].taxpackagingqty));
  this.productname=res[0].name
  this.weightproduct=res[0].netweight;
  
    }
  })
  }
  setdefultUnit(){
    if(this.setpackde==1){
      this.getcheckpcs='U';
    }else{
      this.getcheckpcs='P';
    }
  }
  async LoadAllstock(){
    await this.http.get<any[]>('http://119.59.112.47/SoAPI/api/values/GetStockCountGetAllGroup').subscribe(res=>{
      this.StockAllList=  res;
    });
  }
  Realtimeinstock(itemid){
    var stock=[];
    if(this.StockAllList.length>0){
      stock =[this.StockAllList.find(x => x.itemid == itemid)];
      //alert(JSON.stringify(stock));
      return stock[0].avastock;
    }
    
    }
    viewsstockitem(itemid){

      this.Instock=(this.Realtimeinstock(itemid)=='0'?'0':this.Realtimeinstock(itemid));
    // this.http.get<any[]>('http://119.59.112.47/SoAPI/api/values/getstockcountget/'+itemid).subscribe(res=>{
    //   res[0].avastock;
    // //alert(res);
    // });
    }
  //(focusin)="Searchitem()"
  //ค้นหาสินค้าจากรหัสสินค้า
  Searchitem() {
    this.numberproductsearch='';
    this.priceperunit='0'
    var forword=''
    var low='';
    this.freeitem=false;
    // if(this.setpackde==1){
    //   this.getcheckpcs='U';
    // }else{
    //   this.getcheckpcs='P';
    // }
    //alert(this.productidsearch.itemid);
   if(this.productidsearch.itemid!=undefined && this.productidsearch.itemid !=''){
    //alert(1);
    forword=this.productidsearch.itemid;
    low=this.productidsearch.itemid;
   } else if(this.productidsearch.itemid==undefined ) { 
  
    forword=this.showitem;
     low=this.showitem;
   }else {
   
   }
   //alert(forword+'/'+this.showitem);
  
  
  /*if(this.showitem==="รหัสสินค้า"){
  
  } else {
    forword=this.showitem;
    low=this.showitem;
  }*/
  /*this.iditem=idproduct;*/
  //alert(forword);
  var body={
    itemid:forword,
    itemidor:low
  }
  
  this.http.post<any>(this.url+'uom',body).subscribe(res => {
    this.unitlist=[];
  if(res.length > 0) {
    this.checkadditem=false;
    this.inventlocationid=res[0].inventlocationid;
    this.checkunitprice=res[1].unit;
    for(var i=0;i< res.length;i++){
      if(res[i].unit==this.getcheckpcs){
        this.unitlist.push({
          inventlocationid: res[i].inventlocationid,
          unitid:res[i].unitid,
          unit:res[i].unit,
          itemid:res[i].itemid
        });
      } else {
        this.unitlist.unshift({
          inventlocationid: res[i].inventlocationid,
          unitid:res[i].unitid,
          unit:res[i].unit,
          itemid:res[i].itemid
        });
      }
    }
  
   
    this.selectunitid=res[0].unitid;
    this.packingid=res[1].unitid;
    this.Searchproductlist(res[0].itemid);
    this.viewsstockitem(res[0].itemid);
  } else {
   /*this.openModal(true,'ไม่พบสินค้า',false);*/   
  }
  })
  
  }
  Searchitemclick(value) {
    var body={
      itemid:value,
      itemidor:''
    }
    this.http.post<any>(this.url+'uom',body).subscribe(res => {
      this.unitlist=[];
      if(res.length > 0) {
       
        clearInterval(this.loaduom);
        this.inventlocationid=res[0].inventlocationid;
        this.checkunitprice=res[1].unit;
        this.checkadditem=false;
        for(var i=0;i< res.length;i++){
          if(res[i].unit==this.getcheckpcs){
            this.unitlist.push({
              inventlocationid: res[i].inventlocationid,
              unitid:res[i].unitid,
              unit:res[i].unit,
              itemid:res[i].itemid
            });
          } else {
            this.unitlist.unshift({
              inventlocationid: res[i].inventlocationid,
              unitid:res[i].unitid,
              unit:res[i].unit,
              itemid:res[i].itemid
            });
          }
        }
        this.selectunitid=res[0].unitid;
        this.packingid=res[1].unitid;
        this.Searchproductlist(res[0].itemid);
        this.viewsstockitem(res[0].itemid);
      } else {
       /*this.openModal(true,'ไม่พบสินค้า',false);*/   
      }
      })
    /*this.iditem=idproduct;*/
   
    }
  
  Searchcustomersalekey(event:any) { 
    this.getcustomersalefunction(event.target.value);
  }
  
  //ค้นหาลูกค้าจากชื่อหรือ รหัสลูกค้า พร้อมเรียก Function ดึงที่อยู่
  getcustomersalefunction(value) {
//alert('sdasd');
    this.daddress=[];
    this.inaddress=[];
    this.http.get<any>(this.url +'customer_list/%20/%20/'+value+'/%20/%20').subscribe(res =>{
      clearInterval(this.setloadaddress);
      if(res.length >0) {
        this.dissave=true;
        
        if(this.idsoedit==undefined){
     
          
          if(res[0].DLVMODE=='01-ขนส่ง' || res[0].DLVMODE=='03-ขนส่ง'){
            this.deliveryonclick='ขนส่ง';
            this.deliverytype=[{'deliver':'รถบริษัท'},{'deliver':'รับเอง'},{'deliver':'ขนส่ง'}];
          } else if(res[0].DLVMODE=='02รถบริษัท') {
            this.deliveryonclick='รถบริษัท';
            this.deliverytype=[{'deliver':'รับเอง'},{'deliver':'ขนส่ง'},{'deliver':'รถบริษัท'}];
          } else if(res[0].DLVMODE=='03-รับเอง'){
            this.deliveryonclick='รับเอง';
            this.deliverytype=[{'deliver':'รถบริษัท'},{'deliver':'ขนส่ง'},{'deliver':'รับเอง'}];
          } else {
            this.deliveryonclick='';
            this.deliverytype=[{'deliver':'รถบริษัท'},{'deliver':'รับเอง'},{'deliver':'ขนส่ง'},{'deliver':'ประเภทการขนส่ง'}]
          }
      this.remarksoinput=res[0].MEMO;
      this.locationno=res[0].locationno;
      this.locationnodelie=res[0].locationno;
        }
      
        clearInterval(this.setloadaddress);
        this.customerno=res[0].accountnum;
        this.getaddressdary(res[0].accountnum);
        this.getaddressinv(res[0].accountnum);
        
        if(this.Namelogin[0].accountnum != undefined){
         
         this.company= res[0].name;
         }
         this.accountnum=res[0].accountnum;
        this.accoutnreration=res[0].linedisc;
        this.pricegroup=res[0].pricegroup;
       // this.locationno=this.inaddress[0].locationno;
        this.maptaxgrouptype(res[0].vattype);

        //this.mappaymenttype(res[0].paymtermid);

        this.selectedpaymentmap=res[0].paymtermid;
        this.openModal(false,'กำลังโหลดข้อมูล กรุณารอสักครู่....',false);
      }
      this.disproductline=false;
        },error =>{
          this.openModal(true,'กำลังโหลดข้อมูล กรุณารอสักครู่....',false);
          status=error.status; 
        })
  
        this.dissave=true;
  }
  
  
  Searchcustomersalekeychen(event:any) { 
    this.getcustomersalefunction(event.target.value);
   
  }
//ดึงที่อยู่ วางบิล
getaddressinv(accountnum)
{
  this.inaddress=[];
  var body={
    accountnum:accountnum
  }
  this.http.post<any>(this.url+'get_invaddress',body).subscribe(res=>{
    this.inaddress=[];
    if(res.length>0){
      this.inaddress=[];
      for(var i=0; i< res.length; i++){
        if(this.idsoedit==undefined){
          if(res[i].DPRIMARY==1){
            this.locationno=res[i].locationno;
            this.inaddress.push({
              name: res[i].description,
              accountnum:res[i].accountnum,
              address:res[i].address ,
              locationno: res[i].locationno
          
            })
          } else {
        this.inaddress.unshift({
          name: res[i].description,
          accountnum:res[i].accountnum,
          address:res[i].address ,
          locationno: res[i].locationno
        })
          }
        } else {
            if(res[i].locationno==this.locationno){
              this.locationno=res[i].locationno;
              this.inaddress.push({
                name: res[i].description,
                accountnum:res[i].accountnum,
                address:res[i].address ,
                locationno: res[i].locationno
            
              })
            } else {
          this.inaddress.unshift({
            name: res[i].description,
            accountnum:res[i].accountnum,
            address:res[i].address ,
            locationno: res[i].locationno
          })
            }
        }
      
       
    
      }
      this.locationno=this.inaddress[this.inaddress.length-1].locationno;
    }
  })
 
  //this.locationno=this.inaddress[this.inaddress.length-1].locationno;
}
//ดึงที่อยู่ขนส่งสินค้า
getaddressdary(accountnum){
  this.daddress=[];
  var body={
    accountnum:accountnum
  }
this.http.post<any>(this.url+'get_dlvaddress',body).subscribe(res=>{
  this.daddress=[];
  var chedlv=0;
if(res.length>0){
  for(var x=0;x<res.length;x++){
    if(res[x].DPRIMARY==2){
      chedlv=1;
    } else {
      chedlv=0;
    }
  }
  for (var i=0;i<res.length;i++){
    if(this.idsoedit==undefined){
      if(chedlv==1){
        if(res[i].DPRIMARY==2){
          this.locationnodelie=res[i].locationno;
          this.daddress.push({
            name: res[i].description,
            accountnum:res[i].accountnum,
            address:res[i].address ,
            locationno: res[i].locationno
        
          })
        } else {
      this.daddress.unshift({
        name: res[i].description,
        accountnum:res[i].accountnum,
        address:res[i].address ,
        locationno: res[i].locationno
      })
        }
      } else{
        if(res[i].DPRIMARY==1){
          this.locationnodelie=res[i].locationno;
          this.daddress.push({
            name: res[i].description,
            accountnum:res[i].accountnum,
            address:res[i].address ,
            locationno: res[i].locationno
        
          })
        } else {
      this.daddress.unshift({
        name: res[i].description,
        accountnum:res[i].accountnum,
        address:res[i].address ,
        locationno: res[i].locationno
      })
        }
      }
     
    } else {
      if(res[i].locationno==this.locationnodelie){
        this.locationnodelie=res[i].locationno;
        this.daddress.push({
          name: res[i].description,
          accountnum:res[i].accountnum,
          address:res[i].address ,
          locationno: res[i].locationno
      
        })
      } else {
    this.daddress.unshift({
      name: res[i].description,
      accountnum:res[i].accountnum,
      address:res[i].address ,
      locationno: res[i].locationno
    })
      }
    }
     
     
    }
    this.locationnodelie=this.daddress[this.daddress.length-1].locationno;
}
})
  //alert(this.locationnodelie);

//this.locationnodelie=this.daddress[this.daddress.length-1].locationno;
}
  //คำนวนโปรโมชั่น
  checkpromotionfunction(value){
    var stbefor=this.accoutnreration.replace('+','');
    var st=stbefor.replace('%','');
    this.promotionlist=[];
  //   if(this.accoutnreration==''){
  // this.accoutnreration='%20';
  //   }
  // if(this.pricegroup==''){
  //   this.pricegroup='%20';
  // }
//     if(st==''){
// st='%20';
//     }
    var body={
      itemid:value,
      accountrelation:st,
      pricegroup:this.pricegroup,
      accountnum:this.accountnum
    }
   
    this.http.post<any>(this.url+'map_promotion',body).subscribe(res =>{
  if(res.length >0){
    this.upperdis=res[0].friendlyname;
  for (var i=0;i<res.length;i++){
    this.promotionlist.push({
      itemrelation: res[i].itemrelation,
    accountrelation	: res[i].accountrelation,
    accountcode:res[i].accountcode,
    quantityamountfrom: res[i].quantityamountfrom,
    quantityamountto: res[i].quantityamountto,
    amount:res[i].amount,
    percent1: res[i].percent1,
    percent2: res[i].percent2,
    relation: res[i].relation,
    percent3: res[i].percent3,
    });
  }
  }
  this.http.post<any>(this.url+'map_promotion_price',body).subscribe(res=>{
    //alert(JSON.stringify(res));
        if(res.length>0){
          for(var x=0;x<res.length;x++){
            this.promotionlist.push({
              itemrelation: res[x].itemrelation,
            accountrelation	: res[x].accountrelation,
            accountcode:res[x].accountcode,
            quantityamountfrom: res[x].quantityamountfrom,
            quantityamountto: res[x].quantityamountto,
            amount:res[x].amount,
            percent1: res[x].percent1,
            percent2: res[x].percent2,
            relation: res[x].relation,
            percent3: res[x].percent3,
            });
          }
        }
      })
    })
  }
  //คำนวน ราคาสินค้าจาก จำนวน
  checknumproduct(event) {
  
    var packitem=event;
    var pcsitem=packitem*this.numproduct;
    this.alldiscount=0.0;
    if(this.getcheckpcs==='P'){
      this.allpcs=pcsitem;
    this.numpackingitem=event;
    this.checkpacking=0;
    
    } else {
     
      this.allpcs=packitem;
      this.numpackingitem=packitem/this.numproduct;
      this.checkpacking=packitem%this.numproduct;
    pcsitem=packitem;
    
    }
    //this.checkpacking=packitem%this.numproduct;
    
    var disc1=0.00,disc2=0.00,disc3=0.00,pricec=0.00,pricecpa=0.00;
    var priceperdis1=0,priceperdis2=0,priceperdis3=0,sumdis=0.00,allprice=0.00,sumpricebedis1=0,sumpricebedis2=0,sumpricebedis3=0,finalpri,sumdisdd=0;
    if(this.iditem=='') {
      alert('กรุณาป้อนรหัสสินค้า');
      return;
    //this.openModal(true,'กรุณาป้อนรหัสสินค้า',false); 
    } else {
    
    
    
    if(this.promotionlist.length >0){
    
    for (var i=0;i< this.promotionlist.length;i++){
      if(this.promotionlist[i].amount > 0 && this.promotionlist[i].relation=='4'){
        this.checkadditem=true;
        pricec =this.promotionlist[i].amount;
      }else
      if(this.accoutnreration==='%20'){
        this.accoutnreration='';
      }
    
      if( pcsitem >= this.promotionlist[i].quantityamountfrom && pcsitem < this.promotionlist[i].quantityamountto  && this.promotionlist[i].relation=='5'){
        disc1=this.promotionlist[i].percent1;
        disc2=this.promotionlist[i].percent2;
        disc3=this.promotionlist[i].percent3;
        this.olddis1=this.promotionlist[i].percent1;
        this.olddis2=this.promotionlist[i].percent2;
        this.olddis3=this.promotionlist[i].percent3;
    
    //alert(1);
    //alert(disc3);
      } else if( pcsitem >= this.promotionlist[i].quantityamountfrom && this.promotionlist[i].quantityamountto  ==0   && this.promotionlist[i].relation=='5') {
        //alert(2);
        disc1=this.promotionlist[i].percent1;
        disc2=this.promotionlist[i].percent2;
        disc3=this.promotionlist[i].percent3;
       //alert(disc1+'/'+pcsitem);
        this.olddis1=this.promotionlist[i].percent1;
        this.olddis2=this.promotionlist[i].percent2;
        this.olddis3=this.promotionlist[i].percent3;
     
      }
      // else if(this.accoutnreration == this.promotionlist[i].accountrelation && pcsitem >= this.promotionlist[i].quantityamountfrom && pcsitem > this.promotionlist[i].quantityamountto  && this.promotionlist[i].relation=='5') {
      //  disc1=this.promotionlist[i].percent1;
      //   disc2=this.promotionlist[i].percent2;
      //   disc3=this.promotionlist[i].percent3;
      //   this.olddis1=this.promotionlist[i].percent1;
      //   this.olddis2=this.promotionlist[i].percent2;
      //   this.olddis3=this.promotionlist[i].percent3;
      // }

    }
      
      } else {
        
        pricec=parseFloat(this.priceperunit);
      }
    
    this.dis1=disc1;
    this.dis2=disc2;
    if(this.dis3>0){
      this.dis3=0;
    }
    if(this.dis3==0){
      this.dis3=disc3;
    }
    if((this.selectpayment==='S60' ||this.selectpayment==='N01' || this.selectpayment==='N07'|| this.selectpayment==='TT' || this.selectpayment ==='COD') && disc3==0){
      disc3=3.00;
    this.dis3=3.00;
    }
    if(this.checkunitprice=='U') {
      this.priceperunit=pricec.toString();
    } else {
      this.priceperunit=pricec.toString();
       this.getpackingprice=pricec* this.numproduct;
      //alert('BBSS'+parseFloat(pricec.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2})).toFixed(2)); 
    }
    
    this.getunitprice=pricec;
    allprice=pcsitem*pricec;
    
    if((disc1 > 0 && disc3 < 1) || (disc1 > 0 && disc2 >0 && disc3 > 0)) {
      priceperdis1=allprice*disc1/100;
      sumpricebedis1=allprice-priceperdis1;
    
      finalpri = parseFloat(sumpricebedis1.toFixed(2));
      if(disc2 > 0) {
        priceperdis2=sumpricebedis1*disc2/100;
        sumpricebedis2=sumpricebedis1-priceperdis2;
     
        finalpri = parseFloat(sumpricebedis2.toFixed(2));
        if(disc3 > 0) {
          priceperdis3=sumpricebedis2*disc3/100;
          sumpricebedis3=sumpricebedis2-priceperdis3;
          finalpri = parseFloat(sumpricebedis3.toFixed(2));
       
        }
      }
     
      sumdis=priceperdis1+priceperdis2+priceperdis3;
      sumdisdd=sumdis;
      this.alldiscount=sumdisdd;
      this.finalprice=finalpri;
    } else if(disc1 >0 && disc3 >0) {
      priceperdis1=allprice*disc1/100;
      sumpricebedis1=allprice-priceperdis1;
    
      finalpri = parseFloat(sumpricebedis1.toFixed(2));
        if(disc3 > 0) {
          priceperdis3=sumpricebedis1*disc3/100;
          sumpricebedis3=sumpricebedis1-priceperdis3;
          finalpri = parseFloat(sumpricebedis3.toFixed(2));
         
        }
      
      
      sumdis=priceperdis1+priceperdis2+priceperdis3;
      sumdisdd=sumdis;
      this.alldiscount=sumdisdd;
      this.finalprice=finalpri;
    } else if( disc1 <1 && disc2 <1 && disc3 >0) {
      priceperdis1=allprice*disc3/100;
      sumpricebedis1=allprice-priceperdis1;
    
      finalpri = parseFloat(sumpricebedis1.toFixed(2));
      sumdis=priceperdis1+priceperdis2+priceperdis3;
      sumdisdd=sumdis;
      this.alldiscount=sumdisdd;
      this.finalprice=finalpri;
    }
    else {
      this.finalprice=allprice;
    }
    
    }
    //alert(this.finalprice);
    //alert(this.finalprice);
    }
  editpriceperunit(value){
    this.priceperunit=value;
    this.getunitprice=value;
    this.editdiscount();
  }
  
  //แก้ไขส่วนลด
  editdiscount() {
    var pcsitem=this.allpcs;
  var disc1,disc2,disc3,pricec;
  var priceperdis1=0,priceperdis2=0,priceperdis3=0,sumdis=0,allprice=0,sumpricebedis1=0,sumpricebedis2=0,sumpricebedis3=0,finalpri,sumdisdd=0.00;
   disc1 = this.dis1;
   disc2 = this.dis2;
   disc3 = this.dis3;
   pricec=this.getunitprice;
  allprice=pcsitem*pricec;
  
    if((disc1 > 0 && disc3 < 1) || (disc1 > 0 && disc2 >0 && disc3 > 0)) {
      priceperdis1=allprice*disc1/100;
      sumpricebedis1=allprice-priceperdis1;
      finalpri = sumpricebedis1;
      if(disc2 > 0) {
        priceperdis2=sumpricebedis1*disc2/100;
        sumpricebedis2=sumpricebedis1-priceperdis2;
        finalpri = sumpricebedis2;
        if(disc3 > 0) {
          priceperdis3=sumpricebedis2*disc3/100;
          sumpricebedis3=sumpricebedis2-priceperdis3;
          finalpri = sumpricebedis3;
        }
      }
      
      sumdis=priceperdis1+priceperdis2+priceperdis3;
      sumdisdd=sumdis;
      this.alldiscount=sumdisdd;
      this.finalprice=finalpri;
    } else if(disc1 >0 && disc3 >0) {
      priceperdis1=allprice*disc1/100;
      sumpricebedis1=allprice-priceperdis1;
      finalpri = sumpricebedis1;
        if(disc3 > 0) {
          priceperdis3=sumpricebedis1*disc3/100;
          sumpricebedis3=sumpricebedis1-priceperdis3;
          finalpri = sumpricebedis3;
        }
      
      
      sumdis=priceperdis1+priceperdis2+priceperdis3;
      sumdisdd=sumdis;
      this.alldiscount=sumdisdd;
      this.finalprice=finalpri;
  
    } else if( disc1 <1 && disc2 <1 && disc3 >0) {
      priceperdis1=allprice*disc3/100;
      sumpricebedis1=allprice-priceperdis1;
      finalpri = sumpricebedis1;
      sumdis=priceperdis1+priceperdis2+priceperdis3;
      sumdisdd=sumdis;
      this.alldiscount=sumdisdd;
      this.finalprice=finalpri;
     
    } 
  
    else {
    
      this.finalprice=allprice;
    }
    if(disc1==0 && disc2==0 && disc3==0){
      this.alldiscount=0.00;
    }
  
  
  }
  clrscrlinelist(){
    this.iditem='';
    this.productname='';
    this.allpcs=0;
    this.selectunitid='';
    this.numpackingitem=0;
    this.priceperunit='0';
    this.dis1=0;
    this.dis2=0;
    this.dis3=0;
    this.alldiscount=0;
    this.finalprice=0;
    this.productidsearch=null;
    this.numberproductsearch=null;
    this.unitlist=[];
    this.showitem='รหัสสินค้า';
    this.realldiscount=0;
    this.reallpcs=0;
    this.regetunitprice=0;
    this.reprice=0;
    this.redis1=0;
    this.redis2=0;
    this.redis3=0;
    this.refinalprice=0;
    this.checkbuttonsave=false;
    this.checkadditem=false;
  }
  //สร้าง SaleOder ID
   makeidsaleOder(value,numvalue,date) {
    var text = "SW-";
    var zero='0';
    var saleid=this.Namelogin[0].salegroup;
  var month=date.substring(5,7);
  var ye=date.substring(0,4);
  var year=ye+543;
  
  var monst=month;
  var numst=this.getrunnumber;
  /*if(month<10){
  monst='0'+month.toString();
  }*/
  /*for(var i=0;i<this.getrunnumber.length;i++){
  zero =zero+'0';
  }*/
  this.getallidsaleoder.push({
    text:'SW',
    saleid: saleid,
    year: ye,
    month:month,
    runnumber:numvalue
  });
  if(this.idsoedit==undefined){
    this.idforshow=text+year.toString().substring(2,4)+monst+saleid+'-'+value;
  }
    return text+year.toString().substring(2,4)+monst+saleid+'-'+value;
  }
  
  /*:id/:linenumber/:custaccount/:itemid/:saleqty/:packqty/:price/:percent1/:percent2/:percent3/:lineweight''*/
  
  //เพิ่มสินค้าลง SaleLine
  addsaleline() { 
    if(this.lineoderlist.length>0){
      var chitem=0;
  for(var i=0;i<this.lineoderlist.length;i++){
  if(this.lineoderlist[i].iditem==this.iditem){
    chitem=1;
  }
  }
  if(chitem==1 && this.checkbuttonsave==false){
    if(confirm('สินค้าชินนี้มีอยู่แล้ว ต้องการเพิ่มสินค้า ใช่ หรือ ไม่')){
  
    } else {
      return;
    }
  }
    }
    var disst='000';
   
  if(this.getpromotionedit==true){
    this.checkadditem=true;
    if(this.promotionlist.length >0){
    
      for (var i=0;i< this.promotionlist.length;i++){
        if(this.accoutnreration==='%20'){
          this.accoutnreration='';
        }
         if(this.accoutnreration == this.promotionlist[i].accountrelation && this.allpcs >= this.promotionlist[i].quantityamountto && this.allpcs < this.promotionlist[i].quantityamountto  && this.promotionlist[i].relation=='5'){
          this.olddis1=this.promotionlist[i].percent1;
          this.olddis2=this.promotionlist[i].percent2;
          this.olddis3=this.promotionlist[i].percent3;
       
        } else if(this.accoutnreration == this.promotionlist[i].accountrelation && this.allpcs >= this.promotionlist[i].quantityamountfrom && this.allpcs < this.promotionlist[i].quantityamountto  && this.promotionlist[i].relation=='5') {
         
        
          this.olddis1=this.promotionlist[i].percent1;
          this.olddis2=this.promotionlist[i].percent2;
          this.olddis3=this.promotionlist[i].percent3;
        
        } else if(this.accoutnreration == this.promotionlist[i].accountrelation && this.allpcs >= this.promotionlist[i].quantityamountfrom && this.allpcs > this.promotionlist[i].quantityamountto  && this.promotionlist[i].relation=='5') {
        
          this.olddis1=this.promotionlist[i].percent1;
          this.olddis2=this.promotionlist[i].percent2;
          this.olddis3=this.promotionlist[i].percent3;
        }
      }
        
        } 
  }
  
  
  if(this.upperdis!=''){
    if(this.dis1 >parseInt(this.upperdis)){
      disst='100';
    } else {
      disst='000';
    }
  }

  
    /*if(this.olddis1 != this.dis1 && this.olddis2 == this.dis2 && this.olddis3 == this.dis3){
      disst='100';
    } else if(this.olddis1 != this.dis1 && this.olddis2 != this.dis2 && this.olddis3 != this.dis3){
      disst='111';
    } else if(this.olddis1 != this.dis1 && this.olddis2 == this.dis2 && this.olddis3 != this.dis3){
      disst='101';
    } else if(this.olddis1 != this.dis1 && this.olddis2 != this.dis2 && this.olddis3 == this.dis3){
      disst='110';
    } else if(this.olddis1 == this.dis1 && this.olddis2 == this.dis2 && this.olddis3 != this.dis3){
      disst='001';
    } else if(this.olddis1 == this.dis1 && this.olddis2 != this.dis2 && this.olddis3 != this.dis3){
      disst='011';
    }
    if(this.selectpayment==='N01' || this.selectpayment==='N07'|| this.selectpayment==='TT'){
  if(disst==='111' && this.dis3==3){
    disst='110';
  } else if(disst==='101'&& this.dis3==3){
    disst='100';
  } else if(disst==='011'&& this.dis3==3) {
    disst='010';
  } else if(disst==='001'&& this.dis3==3) {
    disst='000';
  }
    }*/
  
    if(this.checkpacking > 0) {
      alert('สินค้าไม่เต็ม Pack');
   //this.openModal(true,'สินค้าไม่เต็ม Pack ต้องการ เพิ่มรายการใช่หรือไม่',false);
   //return;
    }
    clearInterval(this.loaduom);
    var packpcs;
 
   // alert(this.priceperunit+'/'+this.checkadditem+'/'+this.numberproductsearch);
    
    this.freeitem=false;
    if(this.iditem=='') {
      alert('กรุณาป้อนรหัสสินค้า');
      return;
      //this.openModal(true,'กรุณาป้อนรหัสสินค้า',false);
    } else if(this.checkadditem==false || (parseFloat(this.priceperunit) <1 && this.checkadditem != true) || (this.priceperunit=='0' && this.checkadditem != true)|| (this.numberproductsearch=='' || this.numberproductsearch==0)){
      
      alert('สินค้าราคาเป็น 0 ขายไม่ได้');
      return;
      //this.openModal(true,'สินค้าราคาเป็น 0 ขายไม่ได้',false);
    } else if(this.checkbuttonsave==true) {
      this.lineoderlist[this.index].id = this.idsaleoder;
      this.lineoderlist[this.index].iditem = this.iditem;
      this.lineoderlist[this.index].nameproduct = this.productname;
      this.lineoderlist[this.index].numberpcs = this.allpcs;
     this.lineoderlist[this.index].unitid = this.selectunitid;
      this.lineoderlist[this.index].packingitem =this.numpackingitem ;
      this.lineoderlist[this.index].priceperunit = this.getunitprice;
      this.lineoderlist[this.index].priceproduct=this.allpcs*this.getunitprice;
      this.lineoderlist[this.index].totleweight=this.numpackingitem*this.weightproduct;
      this.lineoderlist[this.index].discount1 = this.dis1;
      this.lineoderlist[this.index].discount2 = this.dis2;
      this.lineoderlist[this.index].discount3 = this.dis3;
      this.lineoderlist[this.index].sumdiscount = this.alldiscount;
    this.lineoderlist[this.index].sumallprice = this.finalprice;
      //this.lineoderlist[this.index].iditem = this.productidsearch.itemid;
      this.lineoderlist[this.index].checkpcs=this.getcheckpcs;
      this.lineoderlist[this.index].disst=disst;
     //this.lineoderlist[this.index].packingitem = this.numberproductsearch;
     this.iditem='';
     this.productname='';
     this.allpcs=0;
     this.selectunitid='';
     this.numpackingitem=0;
     this.priceperunit='0';
     this.dis1=0;
     this.dis2=0;
     this.dis3=0;
     this.alldiscount=0;
     this.finalprice=0;
     this.productidsearch=null;
     this.numberproductsearch=null;
     this.unitlist=[];
     this.showitem='รหัสสินค้า';
     this.realldiscount=0;
     this.reallpcs=0;
     this.regetunitprice=0;
     this.reprice=0;
     this.redis1=0;
     this.redis2=0;
     this.redis3=0;
     this.refinalprice=0;
     this.checkbuttonsave=false;
     this.checkadditem=false;
     this.getcheckpcs='P';
     this.checkpacking=0;
     this.getpromotionedit=false;
     disst='000';
     this.olddis1=0;
     this.olddis2=0;
     this.olddis3=0;
    // alert(JSON.stringify(this.lineoderlist[0].iditem));
    }else {
  
  /*this.headerlist[0].wh=this.inventlocationid;*/
  this.lineoderlist.push({
    idline:0,
    id: this.idsaleoder,
    linenum: this.lineoderlist.length,
    custaccount:this.accountnum,
    iditem: this.iditem,
    vattype:this.vatselect,
    nameproduct: this.productname,
   numberpcs: this.allpcs,
   unitid: this.selectunitid,
   packingitem: this.numpackingitem,
  totleweight: this.weightproduct*this.numpackingitem,
  priceperunit: this.getunitprice,
  priceproduct: this.allpcs*this.getunitprice,
  discount1: this.dis1,
  discount2: this.dis2,
  discount3: this.dis3,
  sumdiscount: this.alldiscount,
  sumallprice: this.finalprice,
  wh:this.inventlocationid,
  checkpcs:this.getcheckpcs,
  disst:disst,
  eidtable: this.eidtable,
  saleid:this.Namelogin[0].salegroup,
  dateshipping:this.dateshipping+ ' '+this.time
  })
  this.iditem='';
  this.productname='';
  this.allpcs=0;
  this.selectunitid='';
  this.numpackingitem=0;
  this.priceperunit='0';
  this.dis1=0;
  this.dis2=0;
  this.dis3=0;
  this.alldiscount=0;
  this.finalprice=0;
  this.productidsearch=null;
  this.numberproductsearch=null;
  this.inventlocationid='';
  this.unitlist=[];
  this.showitem='รหัสสินค้า';
  this.realldiscount=0;
  this.reallpcs=0;
  this.regetunitprice=0;
  this.reprice=0;
  this.redis1=0;
  this.redis2=0;
  this.redis3=0;
  this.refinalprice=0;
  this.checkadditem=false;
  this.getcheckpcs='P';
  this.checkpacking=0;
  this.getpromotionedit=false;
  disst='000';
  this.olddis1=0;
  this.olddis2=0;
  this.olddis3=0;
    }
    this.deleteunitlist();
  
  this.numline++;
    /*var urlpost=`${this.url+'/so/savesaleline/'}${this.idsaleoder}`
    this.http.post(urlpost,'')*/
  
    if(this.lineoderlist.length >0) {
      this.sumalldiscount=0;
      this.sumallpricedis=0;
      this.sumallweight=0;
  for (var i=0;i< this.lineoderlist.length; i++) {
  this.sumalldiscount =this.sumalldiscount + parseFloat(this.lineoderlist[i].sumdiscount.toFixed(2));
  this.sumallpricedis =this.sumallpricedis + parseFloat(this.lineoderlist[i].sumallprice.toFixed(2));
  this.sumallweight = this.sumallweight+ parseFloat(this.lineoderlist[i].totleweight.toFixed(2));
  }
    }
  //alert(this.getcheckpcs);
  this.runninnglinenumsaleline(this.lineoderlist);
  
  this.setdefultUnit();

  }
  runninnglinenumsaleline(order:lineoder[]){
    if(order.length>0){
  for(var i=0;i<order.length;i++){
    order[i].linenum=i+1;
  }
  this.lineoderlist=order;
    } else{
      return;
    }
 
  }
  deleteunitlist(){
   if( this.unitlist.length>0){
  for(var i=0;i<this.unitlist.length;i++){
    this.unitlist.pop();
  }
   }
  }
  
  //ลบ Sale Line ออกจาก Array
  deletelistlineoderclick(value,id) {
    if(id>0){
   
      var urlpost=`${ this.url }${'delete_sale_linebyidline'}/${id}/${this.Namelogin[0].salegroup}`;
      this.http.post(urlpost,'').subscribe(res=> {
      // this.setinheader= setInterval(this.savesaleoderlistsave(),);
      this.lineoderlist.splice(value,1);
      this.lineoderlistsave.splice(value,1);
      alert('ลบ Sale Line เสร็จสิ้น');
      if(this.lineoderlist.length >0 || this.lineoderlist.length <1) {
        this.sumalldiscount=0;
        this.sumallpricedis=0;
        this.sumallweight=0;
      for (var i=0;i< this.lineoderlist.length; i++) {
      this.sumalldiscount =this.sumalldiscount + parseFloat(this.lineoderlist[i].sumdiscount.toFixed(2));
      this.sumallpricedis =this.sumallpricedis + parseFloat(this.lineoderlist[i].sumallprice.toFixed(2));
      this.sumallweight = this.sumallweight+ parseFloat(this.lineoderlist[i].totleweight.toFixed(2));
      }
      }
      })
    } else {
      this.lineoderlist.splice(value,1);
      this.lineoderlistsave.splice(value,1);
      if(this.lineoderlist.length >0 || this.lineoderlist.length <1) {
        this.sumalldiscount=0;
        this.sumallpricedis=0;
        this.sumallweight=0;
      for (var i=0;i< this.lineoderlist.length; i++) {
      this.sumalldiscount =this.sumalldiscount + parseFloat(this.lineoderlist[i].sumdiscount.toFixed(2));
      this.sumallpricedis =this.sumallpricedis + parseFloat(this.lineoderlist[i].sumallprice.toFixed(2));
      this.sumallweight = this.sumallweight+ parseFloat(this.lineoderlist[i].totleweight.toFixed(2));
      }
      }
    }
  
  
  
  if(this.lineoderlist.length >0 || this.lineoderlist.length <1) {
    this.sumalldiscount=0;
    this.sumallpricedis=0;
    this.sumallweight=0;
  for (var i=0;i< this.lineoderlist.length; i++) {
  this.sumalldiscount =this.sumalldiscount + parseFloat(this.lineoderlist[i].sumdiscount.toFixed(2));
  this.sumallpricedis =this.sumallpricedis + parseFloat(this.lineoderlist[i].sumallprice.toFixed(2));
  this.sumallweight = this.sumallweight+ parseFloat(this.lineoderlist[i].totleweight.toFixed(2));
  }
  }

  this.runninnglinenumsaleline(this.lineoderlist);
  }
  
  
  //สลับการแสดงผล ราคาสินค้า Packing / Unit
  getunitid(value:any) {
   
  
  var st=value.target.value;
   var ch=st.substring(0,1);

  
  this.checkunitprice=ch;
  
  if(ch=='P') {
    this.getcheckpcs='P';
  //this.priceperunit=this.getpackingprice;
  }else {
    this.getcheckpcs='U';
    this.selectunitid=st.substring(1);
    //this.priceperunit=this.getunitprice;
  }
  
  
  this.checknumproduct(this.numberproductsearch);
  this.editdiscount();
  }
  foreditlistlineoderclick(value,i){
    this.getpromotionedit=i;
    this.checkadditem=true;
  
    this.loaduom = setInterval(()=>this.Searchitemclick(this.iditem),500);//150
    
    this.editlistlineoderclick(value); 
  
  }
  
  //คลิกแก้ไข Sale Oderline
  editlistlineoderclick(value) {
    var numpcs=this.lineoderlist[value].numberpcs;
      this.Searchproductlist(this.lineoderlist[value].iditem);
       
        this.clrscrlinelist();
      this.checkbuttonsave=true;
      this.index=value;
      this.idsaleoder=this.lineoderlist[value].id;
      this.iditem=this.lineoderlist[value].iditem;
    
      this.showitem=this.lineoderlist[value].iditem;
      this.productname=this.lineoderlist[value].nameproduct;
      this.allpcs=this.lineoderlist[value].numberpcs;
      this.selectunitid=this.lineoderlist[value].unitid;
    
      if(this.lineoderlist[value].checkpcs==='P'){
        this.numproduct=this.lineoderlist[value].numberpcs/this.lineoderlist[value].packingitem;
        this.getcheckpcs='P';
        this.checkunitprice='P';
        var pricepack= this.lineoderlist[value].priceperunit;
        this.numberproductsearch=this.lineoderlist[value].packingitem;
        var sdas=pricepack;
        this.priceperunit=sdas.toString();
        //this.checknumproduct(this.lineoderlist[value].packingitem);
        this.getunitprice=pricepack;
       
      } else{
    this.lineoderlist[value].priceperunit
        this.getcheckpcs='U';
        this.checkunitprice='U';
        this.numberproductsearch=this.lineoderlist[value].numberpcs;
        this.priceperunit=this.lineoderlist[value].priceperunit.toString();
       // this.checknumproduct(this.lineoderlist[value].numberpcs);
        this.getunitprice=this.lineoderlist[value].priceperunit;
      }
      this.numpackingitem=this.lineoderlist[value].packingitem;
    
      this.dis1=this.lineoderlist[value].discount1;
      this.dis2=this.lineoderlist[value].discount2;
      this.dis3=this.lineoderlist[value].discount3;
      this.alldiscount=this.lineoderlist[value].sumdiscount;
      this.finalprice=this.lineoderlist[value].sumallprice;
      this.productidsearch=this.lineoderlist[value].iditem;
     
      this.numproduct=this.lineoderlist[value].numberpcs/this.lineoderlist[value].packingitem;
    
    
    }
  //โหลดข้อมูลคลังสินค้าจาก Database 
  getwarehouse(){
    this.http.get<any>(this.url+'getwarehouse').subscribe(res=>{
      this.getwh=res;
    },error =>{
      this.openModal(true,'เกิดปัญหาในการ Process ข้อมูล กรุณาลองใหม่อีกครั้ง',false);
    })
  }
  checklength(valelength,numlength){
    if(valelength.length>numlength){
      alert('จำนวนข้อมูล ต้องไม่เกิน '+numlength+' ตัวอักษร');
    
    }
    }
  //สร้าง Sale Oder แยกคัลงสินค้าและ บันทึกข้อมูล
 /* savesaleoderbywherehouse(){
  
    this.warehouselist=[];
  if(this.getwh.length>0){
    
  for(var i=0;i<this.getwh.length;i++){
  this.warehouselist.push({
    wh:this.getwh[i].inventlocationid,
    num:i+1,
    sumalldiscount:0,
    sumallpricedis:0,
    sumallweight:0
  });
  }*/
     /* alert(JSON.stringify(this.lineoderlist));
      alert(JSON.stringify(this.headerlist));*/
     /* var weight;
      var discount;
      var allprice;
     for(var x=0;x<this.warehouselist.length;x++){
  
      for(var i=0;i<this.lineoderlist.length;i++){
        weight=this.lineoderlist[i].totleweight;
        discount=this.lineoderlist[i].sumdiscount;
        allprice=this.lineoderlist[i].sumallprice;
        if(this.lineoderlist[i].wh==this.warehouselist[x].wh && this.lineoderlist[i].eidtable==0){
  this.warehouselist[x].sumalldiscount=parseFloat(this.warehouselist[x].sumalldiscount.toString())+parseFloat(this.lineoderlist[i].sumdiscount.toString());
  this.warehouselist[x].sumallpricedis=parseFloat(this.warehouselist[x].sumallpricedis.toString())+parseFloat(this.lineoderlist[i].sumallprice.toString());
  this.warehouselist[x].sumallweight=parseFloat(this.warehouselist[x].sumallweight.toString())+parseFloat(this.lineoderlist[i].totleweight.toString());
        } else if(this.lineoderlist[i].eidtable==1){
          this.warehouselist[0].sumalldiscount=parseFloat(this.warehouselist[0].sumalldiscount.toString())+parseFloat(this.lineoderlist[i].sumdiscount.toString());
          this.warehouselist[0].sumallpricedis=parseFloat(this.warehouselist[0].sumallpricedis.toString())+parseFloat(this.lineoderlist[i].sumallprice.toString());
          this.warehouselist[0].sumallweight=parseFloat(this.warehouselist[0].sumallweight.toString())+parseFloat(this.lineoderlist[i].totleweight.toString());
        }
  
   
     }
     }
  
     
    if(this.warehouselist.length>1){
    for (var i=0;i<this.warehouselist.length;i++){
    if(this.warehouselist[i].sumalldiscount == 0 && this.warehouselist[i].sumallpricedis == 0 && this.warehouselist[i].sumallweight == 0){
  
    } else {
      this.headerlistsave.push({
        idsaleoder: this.headerlist[0].idsaleoder+'-'+this.warehouselist[i].num,
        customerno:this.headerlist[0].customerno ,
        vatselect:this.headerlist[0].vatselect,
        selectpayment:this.headerlist[0].selectpayment,
        deliveryonclick:this.headerlist[0].deliveryonclick,
        locationno:this.headerlist[0].locationno,
        sumallpricedis:this.warehouselist[i].sumallpricedis,
        sumalldiscount:this.warehouselist[i].sumalldiscount,
        sumallweight:this.warehouselist[i].sumallweight,
        notesoinput:this.headerlist[0].notesoinput,
        remarksoinput:this.headerlist[0].remarksoinput,
        dateshipping:this.headerlist[0].dateshipping,
        wh:this.warehouselist[i].wh
      });
    }
    }
  
    for(var x=0;x<this.headerlistsave.length;x++){
      var id=this.headerlistsave[x].idsaleoder;
      if(this.headerlistsave.length<=1){
        if(id.length==16){
          this.headerlistsave[x].idsaleoder=id.substring(0,16);
        } else if(id.length==17){
          this.headerlistsave[x].idsaleoder=id.substring(0,17);
        } else if(id.length==15){
          this.headerlistsave[x].idsaleoder=id.substring(0,15);
        } else if(id.length==14){
          this.headerlistsave[x].idsaleoder=id.substring(0,14);
        }
        
      }
     for(var i=0;i<this.lineoderlist.length;i++){
       var ch=0;
      if(this.lineoderlist[i].wh==this.headerlistsave[x].wh){
  
          this.lineoderlistsave.push({
            id:this.headerlistsave[x].idsaleoder,
            line:this.lineoderlist[i].line,
            iditem: this.lineoderlist[i].iditem,
            nameproduct:this.lineoderlist[i].nameproduct,
            numberpcs:this.lineoderlist[i].numberpcs,
            unitid: this.lineoderlist[i].unitid, 
            packingitem:this.lineoderlist[i].packingitem,
            totleweight:this.lineoderlist[i].totleweight,
            priceperunit:this.lineoderlist[i].priceperunit,
            priceproduct:this.lineoderlist[i].priceproduct,  
            discount1:this.lineoderlist[i].discount1,
            discount2:this.lineoderlist[i].discount2,
            discount3:this.lineoderlist[i].discount3,
            sumdiscount:this.lineoderlist[i].sumdiscount,
            sumallprice:this.lineoderlist[i].sumallprice,
            wh:this.lineoderlist[i].wh,
            checkpcs:this.lineoderlist[i].checkpcs,
            disst:this.lineoderlist[i].disst,
            eidtable: this.lineoderlist[i].eidtable
                  })
        
          
      }
     }
    }
    //alert(JSON.stringify(this.lineoderlistsave));
    //alert(JSON.stringify(this.headerlistsave));
    //this.this.idsaleoder();
    this.deleteebeforupdatesave(this.idsoedit);
    this.setinheader=setInterval(() => this.savesaleoderlistsave(), 300);
    
    }else {
      
    }
  } else {
    alert('เกิดข้อผิดพลาดกรุณาลองใหม่อีกครั้ง');
    //this.openModal(true,'เกิดข้อผิดพลาดกรุณาลองใหม่อีกครั้ง',false);
  }
  console.log(JSON.stringify(this.headerlistsave));
  } */
  
  
  //บันทึกขอมูล Sale Line แบบ แยก คลังสินค้า
  /*savesalelinebywarehouselist() {
    var i=0;
  
      var urlpost= `${ this.url }${ 'savesaleline' }/${ this.lineoderlistsave[0].id}/${ this.x }/${ this.inaddress[0].accountnum }/${ this.lineoderlistsave[0].iditem}/${ this.lineoderlistsave[0].numberpcs}/${ this.lineoderlistsave[0].packingitem}/${ this.vatselect }/${ this.lineoderlistsave[0].priceperunit}/${ this.lineoderlistsave[0].discount1 }/${ this.lineoderlistsave[0].discount2 }/${ this.lineoderlistsave[0].discount3 }/${ this.lineoderlistsave[0].totleweight}/${ this.dateshipping }/${ this.Namelogin[0].salegroup }/${this.lineoderlistsave[0].checkpcs}/${this.lineoderlistsave[0].disst}/${this.lineoderlistsave[0].eidtable}`;
      this.http.post(urlpost,'').subscribe(res =>{
    if(res !=  true) {
      clearInterval(this.setin);
      this.openModal(true,'Save Fail'+ JSON.stringify(res),false);
    }  else {
  
  this.deletelistlineodersave(0);
  if(this.lineoderlistsave.length==0) {
    clearInterval(this.setin);
  
    this.headerlist=[];
    this.company='';
    this.clrscrsaleheader();
   
    //this.openModal(true,'บันทึกข้อมูลเสร็จสิ้น',true);
    this.deleteebeforupdatesave(this.idsaleoder);
  } else if(this.idsoedit!=undefined && this.lineoderlist.length==0){
    clearInterval(this.setin);
    this.clrscrsaleheader();
    this.clrscrlinelist();
  
    if(this.Namelogin[0].accountnum != undefined){
      this.getcustomersalefunction(this.Namelogin[0].accountnum);
      }
    alert('บันทึกข้อมูลเสร็จสิ้น');
    //this.openModal(true,'บันทึกข้อมูลเสร็จสิ้น',true);
    this.deleteebeforupdatesave(this.idsaleoder);
    this.company='';
    this.headerlist=[];
    this.headerlistsave=[];
    this.router.navigate(['/soreview']);
  }
  this.sumalldiscount=0;
  this.sumallpricedis=0;
  this.sumallweight=0;
  this.x++;
    }
    })
   
  
  
  }*/
  
  
  deletelistlineodersave(value) {
  
    this.lineoderlistsave.splice(value,1);
    this.lineoderlist.splice(value,1);
    
    if(this.lineoderlist.length >0 || this.lineoderlist.length <1) {
      this.sumalldiscount=0;
      this.sumallpricedis=0;
      this.sumallweight=0;
    for (var i=0;i< this.lineoderlist.length; i++) {
    this.sumalldiscount =this.sumalldiscount + parseFloat(this.lineoderlist[i].sumdiscount.toFixed(2));
    this.sumallpricedis =this.sumallpricedis + parseFloat(this.lineoderlist[i].sumallprice.toFixed(2));
    this.sumallweight = this.sumallweight+ parseFloat(this.lineoderlist[i].totleweight.toFixed(2));
    }
    }
    
    }
  //บันทึกข้อมูล Sale header แบบ แยกคลังสินค้า
  /*  savesaleoderlistsave() {
  
      if(this.deliveryonclick=='') {
        alert('กรุณาเลือก การขนส่ง');
        //this.openModal(true,'กรุณาเลือก การขนส่ง',false);
      } else{
        if(this.remarksoinput=='') {
          this.remarksoinput='N';
        }
        if(this.notesoinput=='') {
          this.notesoinput='N';
        }
       if(this.wh==''){
         this.wh='%20';
       }
       if(this.headerlistsave[0].notesoinput==''){
         this.headerlistsave[0].notesoinput=='N';
       }
       if(this.headerlistsave[0].remarksoinput==''){
         this.headerlistsave[0].remarksoinput='N';
       }
     var urlpost=`${ this.url }${ 'createso' }/${ this.headerlistsave[0].idsaleoder }/${ this.headerlistsave[0].customerno }/${ this.headerlistsave[0].vatselect }/${ this.headerlistsave[0].selectpayment }/${ this.headerlistsave[0].deliveryonclick }/${ this.locationno }/${ this.headerlistsave[0].sumallpricedis }/${ this.headerlistsave[0].sumalldiscount }/${ this.headerlistsave[0].sumallweight }/${ this.notesoinput }/${ this.remarksoinput }/${ this.headerlistsave[0].dateshipping }/${ this.Namelogin[0].salegroup }/${ this.headerlistsave[0].wh }`;
            if(this.daddress != null) {
               this.http.post(urlpost,'').subscribe(res => {
                 if(res==true) {
            
                 this.headerlistsave.splice(0,1);
                  if(this.headerlistsave.length==0){
                    clearInterval(this.setinheader);
                  this.setin=setInterval(() => this.savesalelinebywarehouselist(), 300);
                  this.sumalldiscount=0;
                  this.sumallpricedis=0;
                  this.sumallweight=0;
                 this.remarksoinput='';
                 this.notesoinput='';
                  
                  }
                 } else {
                   alert(JSON.stringify(res));
                 }
               })
             } else {
               alert('กรุณาเลือกลูกค้า');
               //this.openModal(true,'กรุณาเลือกลูกค้า',false);
             }
     
      }
    
     
    }
    */
  //ลบข้อมูล SaleOder หลังจาก บันทึกข้อมูล Sale Oder แบบแยกคลังสินคาแล้ว
    deleteebeforupdatesave(value) {
      if(this.idsoedit != undefined) {
      
        var urlpost=`${ this.url }${'delete_sale_line'}/${ value}`;
        this.http.post(urlpost,'').subscribe(res=> {
        // this.setinheader= setInterval(this.savesaleoderlistsave(),);
         
        })
      
      } else {
     
      }
        
       }
  
  
       closeeditlistoder(){
         this.router.navigate(['/soreview']);
  
       }
       CheckValueRemark(value){
         if(value==''){
          this.remarksoinput='BY'
         }
       }
  convertnumber(number:number){
  var parts = number.toFixed(2).split(".");
  var num = parts[0].replace(/(\d)(?=(\d{3})+(?!\d))/g, "$1,") + 
      (parts[1] ? "." + parts[1] : "");
      return num;
  }
  //สั่งพิมใบ สั่งซื้อ
       print(): void {
   
        let  popupWin;
   
        popupWin = window.open('', '_blank');
        popupWin.document.open();
        popupWin.document.write(`
        <html>
        <head>
          <title>ใบแปะหน้ากล่อง</title>
          <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
          <meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
    <meta http-equiv="Content-Language" content="en-us">
    <meta http-equiv="Content-Script-Type" content="text/javascript">
    <meta name="GENERATOR" content="TrackInternet._Default Class">
          
          <script src="https://code.jquery.com/jquery-3.3.1.js" ></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.14.3/umd/popper.min.js" ></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.1.3/js/bootstrap.min.js"></script>
          <style>
    .textcen{
    text-align: center;
    }
    .he{
      border-width:5px;  
    border-style:solid;
    border-color: black;
    }
          </style>
        </head>
    <body onload="window.print();window.close()">
    <div class="container-fluid ">
            
                <div class="col-md-12">
                <div class=" he ">
                <h1 class="textcen" style=" font-size: 50px">*** กรุณาอย่าวางของหนักทับ ***</h1>
                   </div>
                   <div class="textcen p-1">
                       <span class="textcen" ><h2 style=" font-size: 45px"></h2></span>
                   </div>
                   <div class=" textcen p-1">
                       <div class="textcen" ><h2 style=" font-size: 40px"></h2></div>
                   </div>
                   <div class="textcen p-1">
                       <div class="textcen" ><h2 style=" font-size: 40px"> 
                        </h2></div>
                   </div>
                   <div class=" he">
                       <span class="textcen"  ><h1 style=" font-size: 50px">*** กรุณาอย่าวางของหนักทับ ***</h1> </span>
                   </div>
    
                </div>
            
                
        </div>
    </body>
      </html>`
        );
       
     
      
        popupWin.document.close();
      
      
     
    }
  
       //เปิดแจ้งเตือน 
  openModal(open : boolean,text: string,load:boolean) : void {
    this.mdlSampleIsOpen = open;
    this.alt=text;
    this.checkreload=load;
  }
  //ปิดแจ้งเตือน
  closemodel(cl: boolean) {
  this.mdlSampleIsOpen=false;
  if(this.checkreload==true) {
    this.router.navigate(['sorecord']);
    this.notesoinput='';
    this.remarksoinput='';
  this.inaddress=[];
  this.daddress=[];
  this.model=[];
  }
  
  }
  applyLocale(pop: any) {
    this.localeService.use(this.locale);
    pop.hide();
    pop.show();
  }
  }
  
