
  .autocomplete {
      /*the container must be positioned relative:*/
      position: relative;
      display: inline-block;
  }

  /*input {
      border: 1px solid transparent;
      background-color: #f1f1f1;
      padding: 10px;
      font-size: 16px;
  }*/

  /*input[type=text] {
          background-color: #f1f1f1;
          width: 100%;
      }*/

  /*input[type=submit] {
          background-color: DodgerBlue;
          color: #fff;
          cursor: pointer;
      }*/

  .autocomplete-items {
      position: absolute;
      border: 1px solid #d4d4d4;
      border-bottom: none;
      border-top: none;
      z-index: 99;
      /*position the autocomplete items to be the same width as the container:*/
      top: 100%;
      left: 0;
      right: 0;
  }

  .autocomplete-items div {
      padding: 10px;
      cursor: pointer;
      background-color: #fff;
      border-bottom: 1px solid #d4d4d4;
  }

  .autocomplete-items div:hover {
      /*when hovering an item:*/
      background-color: #e9e9e9;
  }

  .autocomplete-active {
      /*when navigating through the items using the arrow keys:*/
      background-color: DodgerBlue !important;
      color: #ffffff;
  }

  .custom-day {
    text-align: center;
    padding: 0.185rem 0.25rem;
    display: inline-block;
    height: 2rem;
    width: 2rem;
  }
  .custom-day.focused {
    background-color: #e6e6e6;
  }
  .custom-day.range, .custom-day:hover {
    background-color: rgb(2, 117, 216);
    color: white;
  }
  .custom-day.faded {
    background-color: rgba(2, 117, 216, 0.5);
  }