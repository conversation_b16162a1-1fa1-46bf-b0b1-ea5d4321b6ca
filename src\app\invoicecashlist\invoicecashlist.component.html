<app-topmenu></app-topmenu>
<section style="padding-top:60px">
    <div class="container-fluid col-md-12 col-xs-12 col-sm-12 col-12" style="padding-right: 5px; padding-left: 5px;">
        <h5 class="p-sm-1 bg-secondary text-white text-center">Cash Invoice List</h5>
        <!--Filter section-->

        <div class="form-row">
            <div class="col-md-2 mb-2" *ngIf="testclose">
                <!--<input id="inputproduct" class="form-control form-control-sm"  [disabled]="groupsale !=='admin'" [(ngModel)]="CodeSo" type="text" name="myproduct" placeholder="รหัสพนักงานขาย">-->

                <select multiple [(ngModel)]="CodeSo" class="custom-select custom-select-sm">
                    <option selected disabled value="">--เลือกรหัสพนักงานขาย--</option>
                    <option [selected]="CodeSo == 'เลือกรายการทั้งหมด'" value="1">เลือกรายการทั้งหมด</option>
                    <option [selected]="CodeSo == item.groupid" value="{{item.groupid}}" *ngFor="let item of DateGroupsaleman" [value]="item.groupid">
                        {{item.groupid}} ({{item.name}})
                    </option>
                </select>
            </div>
            <div class="col-md-2 mb-2" style=" position: relative;">
                <ng-template #rt let-r="result" let-t="term">
                    <div (click)="getcustomersalefunction(r.name)">
                        <label (mousedown)="getcustomersalefunction(r.name)">{{r.name}} ({{r.accountnum}})</label>
                    </div>

                </ng-template>

                <input id="typeahead-template" placeholder="{{txtcustomer}}" type="text" class="form-control form-control-sm" [(ngModel)]="Customer"
                    name="model" [ngbTypeahead]="search" [resultTemplate]="rt" [inputFormatter]="formatter" />
                <div style="position: absolute;z-index: 10;width: 15%;height: 100%;top: 1px;right: 1px;font-size: 18px;cursor: pointer;text-align: center;"
                    (click)="cancel()">x</div>
                <!--  <input id="inputcustomer" class="form-control form-control-sm" [(ngModel)]="Customer" type="text" name="customer" placeholder="ค้นหาลูกค้า">-->
            </div>
            <div class="col-md-2 mb-2">
                <input id="inputproduct" class="form-control form-control-sm" [(ngModel)]="CodeInvoice" type="text" name="CodeInvoice" placeholder="เลขที่ Invoice">
            </div>

            <div class="col-xs-12 col-12 col-md-2 form-group">
                <input type="text" placeholder="DD/MM/YYYY" class="form-control" bsDatepicker [bsConfig]="{ dateInputFormat: 'DD/MM/YYYY' }"
                    class="form-control form-control-sm" [(ngModel)]="Datafromdate">
            </div>

            <div class="col-xs-12 col-12 col-md-2 form-group">
                <input type="text" placeholder="DD/MM/YYYY" class="form-control" bsDatepicker [bsConfig]="{ dateInputFormat: 'DD/MM/YYYY' }"
                    class="form-control form-control-sm" [(ngModel)]="Datatodate">
            </div>
            <!-- <div class="col-md-2 mb-2">
           <input id="fromdate" class="form-control form-control-sm" type="date" value="{{dateshipping}}" [(ngModel)]="fromdate" name="fromdate" placeholder="จากวันที่">
          
        </div>
        <div class="col-md-2 mb-2">
            <input id="todate" class="form-control form-control-sm" type="date" value="{{dateshippingto}}" [(ngModel)]="todate" name="todate" placeholder="ถึงวันที่">
        </div>-->
            <div class="col-md-2 mb-3 col-12 text-center text-sm-center text-md-center text-lg-left" style="padding-right: 0px; padding-left: 0px;">
                <button style="width: 55px;" class="btn btn-primary btn-sm font-weight-light" (click)="SearchInvoicecashlist()" type="submit">Search</button>
                <button style="margin-left: 3px; width: 68px;" class="btn btn-primary btn-sm font-weight-light" (click)="SearchInvoicecashlistEd()"
                    type="submit">Edit INV</button>
                <!--<button  style="margin-left: 3px; width: 55px;"class="btn btn-primary btn-sm font-weight-light" (click)="cancel()" >Clear</button>-->
            </div>
        </div>
        <table class="table table-hover table-bordered table-sm">
            <thead>
                <tr class="text-sm-center bg-light">
                    <th class="text-center font-weight-normal" scope="col">Item</th>
                    <th class="text-center font-weight-normal" scope="col">พนักงานขาย</th>
                    <th class="text-center font-weight-normal" scope="col">รหัสลูกค้า</th>
                    <th class="text-center  font-weight-normal" scope="col">ชื่อลูกค้า</th>
                    <th class="text-center font-weight-normal" scope="col">มูลค่าสินค้า</th>
                    <th class="text-center font-weight-normal" scope="col">VAT</th>
                    <th class="text-center font-weight-normal" scope="col">มูลค่ารวม</th>
                    <th class="text-center " scope="col" class="text-sm-center" width="60px"></th>
                </tr>
            </thead>
            <tbody>
                <tr class="text-sm-left" *ngFor="let item of DataSearch; let i=index;">

                    <td class="text-center font-weight-normal" [ngStyle]="{'color':getColormin(item.invoicedate,item.Invoiceamount,item.sumMax,item.summin),'background-color':getColorminBG(item.typeCK) }">{{i+1}}
                    </td>
                    <td class="text-center font-weight-normal" [ngStyle]="{'color':getColormin(item.invoicedate,item.Invoiceamount,item.sumMax,item.summin)}">{{item.Salegroup}}</td>
                    <td class="text-center font-weight-normal" [ngStyle]="{'color':getColormin(item.invoicedate,item.Invoiceamount,item.sumMax,item.summin)}">{{item.Orderaccount}}</td>
                    <td class="text-left font-weight-normal " [ngStyle]="{'color':getColormin(item.invoicedate,item.Invoiceamount,item.sumMax,item.summin)}">{{item.Invoicingname}}</td>
                    <!--<td class="text-center  font-weight-light" >{{item.invoicedate | date: 'dd/MM/yyyy'  }}</td>-->
                    <!--<td class="text-center  font-weight-light" >{{item.duedate | date: 'dd/MM/yyyy' }}</td>-->
                    <td class="text-right font-weight-normal" [ngStyle]="{'color':getColormin(item.invoicedate,item.Invoiceamount,item.sumMax,item.summin)}">{{item.Salesbalance
                        | number:'1.2-2' }}</td>
                    <td class="text-right font-weight-normal " [ngStyle]="{'color':getColormin(item.invoicedate,item.Invoiceamount,item.sumMax,item.summin)}">{{item.Sumtax
                        | number:'1.2-2' }}</td>
                    <td class="text-right font-weight-normal" [ngStyle]="{'color':getColormin(item.invoicedate,item.Invoiceamount,item.sumMax,item.summin)}">{{item.Invoiceamount
                        | number:'1.2-2' }}</td>
                    <!--<td class="text-center  font-weight-light" >{{item.dlvmode}}</td>-->
                    <td class="text-center">
                        <button class="btn btn btn-warning" (click)="setinvoiceid(item.Orderaccount,item.Salegroup)" style="padding: 0pt" data-toggle="modal"
                            data-target="#ModalupFile" aria-expanded="true" aria-controls="collapseOne">
                            Upload
                        </button>
                    </td>
                </tr>

                <tr class="text-sm-left">
                    <td class="text-sm-center font-weight-light"></td>
                    <td class="text-sm-center font-weight-light"></td>
                    <td class="text-sm-center font-weight-light"></td>
                    <td class="text-sm-right font-weight-normal">มูลค่ารวม</td>
                    <td class="text-sm-right font-weight-normal">{{ productprice | number:'1.2-2' }}</td>
                    <td class="text-sm-right font-weight-normal">{{ sumvat | number:'1.2-2' }}</td>
                    <td class="text-sm-right font-weight-normal">{{ sumprice | number:'1.2-2' }}</td>
                    <th class="text-sm-right font-weight-light"></th>

                </tr>
            </tbody>
        </table>

    </div>
</section>

<div class="modal fade" id="ModalupFile" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="exampleModalLabel" style="color: red;">Upload File invoice : {{ setInvoice }}
                </h5>
                <button type="button" class="close" data-dismiss="modal" (click)="clear()" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body" style="padding: 3px;">
                <div class="form-group col-md-3 col-sm-6 col-xs-6">
                    <select [(ngModel)]="paymenttype" (change)="searchviewPaymenttype()" class="custom-select custom-select-sm">
                        <option value="All">ทุกรายการ</option>
                        <option value="N01">N01</option>
                        <option value="N07">N07</option>
                        <option value="TT">TT</option>
                        <option value="COD">COD</option>
                    </select>
                </div>
                <div style="overflow-y: auto;">
                    <table class="table table-hover table-bordered table-sm">
                        <thead>
                            <tr class="text-sm-center bg-light">
                                <th class="text-sm-center font-weight-normal" style="text-align: center;" scope="col"></th>
                                <th nowrap class="text-sm-center font-weight-normal" style="text-align: center;" scope="col">SO
                                    No
                                </th>
                                <th nowrap class="text-sm-center font-weight-normal" style="text-align: center;" scope="col">Invoice
                                    No
                                </th>
                                <th nowrap class="text-sm-center font-weight-normal" style="text-align: center;" scope="col">Invoice
                                    Date
                                </th>
                                <th nowrap class="text-sm-center font-weight-normal" style="text-align: center;" scope="col">Due
                                    Date
                                </th>
                                <th class="text-sm-center font-weight-normal" scope="col"></th>
                                <th class="text-sm-center font-weight-normal" style="text-align: center;" scope="col">มูลค่าสินค้า</th>
                                <th class="text-sm-center font-weight-normal" style="text-align: center;" scope="col">VAT</th>
                                <th class="text-sm-center font-weight-normal" style="text-align: center;" scope="col">มูลค่ารวม</th>
                                <th class="text-sm-center font-weight-normal" scope="col" class="text-sm-center" width="30px">
                                    <input type="checkbox" [(ngModel)]="testcheck" (click)="selectAll($event.target.checked);">
                                </th>
                                <th class="text-sm-center font-weight-normal" scope="col"></th>
                                <th class="text-sm-center font-weight-normal" scope="col" *ngIf="!closeED"></th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr *ngFor="let itembill of viewpaymenttype; let i=index">
                                <td class="text-sm-center font-weight-normal" [ngStyle]="{'color':getColor(itembill.invoicedate,itembill.invoiceamount),'background-color':getColorminBG(itembill.typeCK)}"
                                
                                style="text-align: center;">{{i+1}}</td>
                                <td nowrap class="text-sm-center font-weight-normal" [ngStyle]="{'color':getColor(itembill.invoicedate,itembill.invoiceamount)}"
                                    style="text-align: center;">{{itembill.salesid}}</td>
                                <td nowrap class="text-sm-center font-weight-normal" [ngStyle]="{'color':getColor(itembill.invoicedate,itembill.invoiceamount)}"
                                    style="text-align: center;">{{itembill.invoiceid}}</td>
                                <td nowrap class="text-sm-center font-weight-normal" [ngStyle]="{'color':getColor(itembill.invoicedate,itembill.invoiceamount)}"
                                    style="text-align: center;">{{itembill.invoicedate | date:
                                    'dd/MM/yyyy'}}
                                </td>
                                <td nowrap class="text-sm-center font-weight-normal" [ngStyle]="{'color':getColor(itembill.invoicedate,itembill.invoiceamount)}"
                                    style="text-align: center;">{{itembill.duedate | date: 'dd/MM/yyyy'}}</td>
                                <td class="text-sm-center font-weight-normal" [ngStyle]="{'color':getColor(itembill.invoicedate,itembill.invoiceamount)}"
                                    style="text-align: center;">{{itembill.payment}}</td>
                                <td class="text-sm-right font-weight-normal" [ngStyle]="{'color':getColor(itembill.invoicedate,itembill.invoiceamount)}"
                                    style="text-align: right;">{{itembill.salesbalance | number:'1.2-2'}}</td>
                                <th class="text-sm-right font-weight-normal" [ngStyle]="{'color':getColor(itembill.invoicedate,itembill.invoiceamount)}"
                                    style="text-align: right;">{{itembill.sumtax | number:'1.2-2'}}</th>
                                <th class="text-sm-right font-weight-normal" [ngStyle]="{'color':getColor(itembill.invoicedate,itembill.invoiceamount)}"
                                    style="text-align: right;">{{itembill.invoiceamount | number:'1.2-2'}}</th>
                                <td class="text-center">

                                    <input type="checkbox" [checked]="itembill.check" (click)="checkIfAllSelected($event.target.checked,i,paymenttype);">
                                </td>
                                <td class="text-center">
                                    <button *ngIf="closeED" class="btn btn btn-warning" style="padding: 0px;" (click)="openModalIMG(Modalview,itembill.imgurl,itembill.typeCK)">
                                        View
                                    </button>
                                    <button *ngIf="!closeED" [disabled]="(itembill.typeCK!=='1')" class="btn btn btn-warning" style="padding: 0px;" (click)="openModalIMG(Modalview,itembill.imgurl,itembill.typeCK)">
                                        View
                                    </button>
                                </td>
                                <td class="text-center" *ngIf="!closeED">
                                    <button class="btn btn btn-warning" data-dismiss="modal" style="padding: 0px;" (click)="openpdf(itembill.invoiceid)">
                                        print
                                    </button>

                                  
                                </td>
                            </tr>

                            <tr *ngIf="numviewpaymenttype">
                                <td class="text-center font-weight-normal" colspan="11" style="text-align: center;"> ไม่มีรายการ
                                </td>
                            </tr>
                            <td class="text-sm-right text-md-right text-right font-weight-normal" colspan="6" style="text-align: right;">มูลค่ารวม</td>
                            <td class="text-sm-right font-weight-normal" style="text-align: right;">{{ trueproductprice |
                                number:'1.2-2' }}</td>
                            <th class="text-sm-right font-weight-normal" style="text-align: right;">{{ truesumvat | number:'1.2-2'
                                }}
                            </th>
                            <th class="text-sm-right font-weight-normal" style="text-align: right;">{{ truesumprice | number:'1.2-2'
                                }}
                            </th>
                            <td class="text-sm-center font-weight-normal"></td>
                            <td class="text-sm-center font-weight-normal"></td>
                            <td class="text-sm-center font-weight-normal" *ngIf="!closeED"></td>
                        </tbody>

                    </table>
                </div>

                <!-- <img src="{{ImageIN}}" style="width: 200px; height: 200px;"/><br>
                {{imgInvoice}}-->
                <div class="form-group col-sm-6 offset-sm-3">
                <div class="custom-file" style="margin-bottom: 10px">
                    <input type="file" class="custom-file-input" [(ngModel)]="Filelist" #Image accept="image/*" (change)="handleFileInput($event.target.files)"
                     #idFile   id="inputGroupFile01" aria-describedby="inputGroupFileAddon01">
                    <label class="custom-file-label" [(ngModel)]="textload" for="inputGroupFile01">{{ textload }} </label>
                </div>
                </div>
                <div *ngIf="total!=0" class="form-group col-sm-6 offset-sm-3" >
                    <!--  <div class="custom-control custom-checkbox col-sm-6">
                      <input type="checkbox" class="custom-control-input" [(ngModel)]="chDraft" id="customCheck1">
                        <label class="custom-control-label" for="customCheck1">Draft</label>
             


                    </div>-->
                    <div class="form-group col-sm-12">
                            <select [(ngModel)]="chDraft" class="custom-select custom-select-sm">
                                    <option value="A">เลือกประเภทสถานะ</option>
                                <option value="0">รายการเสร็จสิ้น</option>
                                <option value="1">Draft รายการนี้ไว้</option>
                            </select>
                        </div>
                </div>



                <div class="collapse" id="collapseExample">
                    <div class="card card-body">
                        <form #imageForm=ngForm style="text-align: center;">
                            <img [src]="imageUrl" style="width:70%;">
                        </form>
                    </div>
                </div>


                <!-- <input type="file" (ngSubmit)="OnSubmit(Caption,Image)"(change)="onFileSelect($event)" accept="image/*" >
                <input type="file" #Image accept="image/*" (change)="handleFileInput($event.target.files)">
              <input type="file" (change)="Preview($event)" accept="image/*" >-->

            </div>
            <div class="modal-footer" style="padding: 3px;">


                <div class="alert alert-success" style="width :200px; top: 7px;" role="alert">
                    รายที่เลือกทั้งหมด : {{ total }}
                </div>
                <button type="button" class="btn btn-secondary" (click)="clear()" data-dismiss="modal">Close</button>
                <button class="btn btn-primary" data-toggle="collapse" href="#collapseExample" role="button" aria-expanded="false" [disabled]="selectedFile==''"
                    aria-controls="collapseExample">
                    ViewImage
                </button>
                <button type="button" [disabled]="( total==0 || chDraft =='A' || Filelist ==undefined && chDraft =='0' && selectedFile =='')" data-dismiss="modal" class="btn btn-primary" (click)="onUpload(chDraft,total)">Upload</button>
            </div>
        </div>
    </div>
</div>





<div id="mdlSample" class="modal fade" role="dialog" [ngStyle]="{'display': mdlSampleIsOpen ? 'block' : 'none', 'opacity': 1}">
    <div class="modal-dialog modal-md">
        <div class="modal-content">
            <div class="modal-header colhaederal">
                <h4 class="modal-title">Report</h4>
            </div>
            <div class="modal-body">{{alt}} {{load}}</div>
            <div class="modal-footer" align="right">
                <button type="button" id="btnClose" class="btn btn-danger" (click)="closemodel(false)"><i class="fa fa-times fa-fw"></i>
                    ปิด</button></div>
        </div>
    </div>
</div>

<div id="mdlSample" class="modal fade" role="dialog" [ngStyle]="{'display': mdlSampleIsOpen2 ? 'block' : 'none', 'opacity': 1}">
    <div class="modal-dialog modal-md">
        <div class="modal-content">
            <div class="modal-header colhaederal">
                <h4 class="modal-title">Report</h4>
            </div>
            <div class="modal-body">{{alt2}} {{load}} </div>
            <div class="modal-footer" align="right">
                <button type="button" id="btnClose" class="btn btn-danger" disabled (click)="closemodel2(false)"><i class="fa fa-times fa-fw"></i>
                    ปิด</button></div>
        </div>
    </div>
</div>

<div id="mdlSample" class="modal fade" role="dialog" [ngStyle]="{'display': mdlSampleIsOpensuccess ? 'block' : 'none', 'opacity': 1}">
    <div class="modal-dialog modal-md">
        <div class="modal-content">
            <div class="modal-header colhaederal">
                <h4 class="modal-title">Report</h4>
            </div>
            <div class="modal-body">{{altsuccess}} {{load}} </div>
            <div class="modal-footer" align="right">
                <button type="button" id="btnClose" class="btn btn-danger" (click)="closemodelsuccess(false)"><i class="fa fa-times fa-fw"></i>
                    ปิด</button></div>
        </div>
    </div>
</div>

<!--<div id="mdlSample" class="modal fade" role="dialog" [ngStyle]="{'display': mdlSampleIsOpenIMG ? 'block' : 'none', 'opacity': 1}">
    <div class="modal-dialog modal-lg"  role="document" style="top: 50%;" >
        <div class="modal-content " style="top: 30px;" >
        <div class="modal-body" style="padding: 5px;">
            <ngb-tabset>
                <ngb-tab>
                  <ng-template ngbTabTitle target ="_blank"><b>Images Billno</b></ng-template>
                  <ng-template ngbTabContent >
                        <img src="{{ImageBillno}}" style=" margin: 5px; width: 99%; height: 99%;"/>
                   </ng-template>
                </ngb-tab>
                
              </ngb-tabset>
        </div>     
        <div class="modal-footer" style="padding: 5px;" align="right">
            <button type="button"  id="btnClose" class="btn btn-danger" (click)="closemodelIMG(false)"> ปิด</button>
        </div>
        </div>
        </div>
</div>-->

<ng-template #Modalview>
    <div class="modal-body" style="padding: 5px;">{{altBill}}
        <div class="card card-body" style="padding : 5px;">
            <ngb-tabset>
                <ngb-tab>
                    <ng-template ngbTabTitle target="_blank"><b>Images Billno</b></ng-template>
                    <ng-template ngbTabContent>
                        <img src="{{ImageBillno}}" style=" margin: 5px; width: 99%; height: 99%;" />
                    </ng-template>
                </ngb-tab>

            </ngb-tabset>
            <!-- <button type="button" (click)="ModalremarkRef.hide()" class="btn btn-secondary">Close</button>-->
        </div>
    </div>
    <div class="modal-footer" style="padding: 5px;" align="right">
        <button type="button" id="btnClose" class="btn btn-danger" (click)="modalRefshow.hide()"> ปิด</button>
    </div>
</ng-template>

