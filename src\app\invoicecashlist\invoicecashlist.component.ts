import { payment } from './../sorecord/sorecord.component';
import { Router, ActivatedRoute } from '@angular/router';
import { UploadImageService } from './../shared/upload-image.service';
import { Component, OnInit, ViewChild, TemplateRef } from '@angular/core';
import { NgbDateStruct, NgbCalendar, NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { HttpClient, HttpEventType } from '@angular/common/http';
import { WebapiService } from '../webapi.service';
import { Route } from '@angular/compiler/src/core';
import { Ng2ImgMaxService } from 'ng2-img-max';
import { Observable } from 'rxjs';
import { debounceTime, map } from 'rxjs/operators';
import { BsModalService } from 'ngx-bootstrap/modal';
import { BsModalRef } from 'ngx-bootstrap/modal/bs-modal-ref.service';
import { Console } from '@angular/core/src/console';

export interface idinvoice {
  id: string;
  salesid: string;
  invoiceid: string;
  invoicedate: string;
  duedate: string;
  salesbalance: number;
  sumtax: number;
  invoiceamount: number;
  SOno: string;
  orderaccount: string;
  payment: string;
  imgurl: string;
  check: boolean;
  typeCK: string;
}
export interface idcall {
  id: string;
  SOno: string;
  orderaccount: string;
  //Productprice: Number;
 // Sumvat: Number;
 // Sumprice: Number;
}

@Component({
  selector: 'app-invoicecashlist',
  templateUrl: './invoicecashlist.component.html',
  styleUrls: ['./invoicecashlist.component.css']
})


export class InvoicecashlistComponent implements OnInit {

  modalRefshow: BsModalRef;
  ModalremarkRef: BsModalRef;


  config = {
    ignoreBackdropClick: true,
    class: 'modal-md'
  };
  configview = {
    ignoreBackdropClick: true,
    class: 'modal-lg '
  };



  Remark = '';
  max = 2000;
  mdlSampleIsOpen: boolean = false;
  alt = '';
  checkreload = true;

  mdlSampleIsOpenIMG: boolean = false;
  altimg = '';

  mdlSampleIsOpen2: boolean = false;
  alt2 = '';
  checkreload2 = true;

  mdlSampleIsOpensuccess: boolean = false;
  altsuccess = '';
  checkreloadsuccess = true;

  idinvi: idinvoice[] = [];
  allid: idcall[] = [];

  hoveredDate: NgbDateStruct;
  DataSearch: any[];
  fromDate: NgbDateStruct;
  toDate: NgbDateStruct;
  Customer: any;
  CodeInvoice = '';
  CodeSo = '';
  imginvoice = "";
  url: string;
  fromdate = '';
  ImageIN = '';
  todate = '';
  productprice = 0;
  sumvat = 0;
  sumprice = 0;
  setInvoice = '';
  selectedFile = null;
  imgInvoice = '';
  textload = '';
  load: string = " สถานะ : Upload : 0 %";
  imageUrl: string = "assets/img/default-image.png";
  uploadimg = '';
  dateshipping = '';
  dateshippingto = '';
  datalogin: any[] = [];
  groupsale: string;

  testclose = false;
  testcheck = false;


  trueproductprice = 0;
  truesumvat = 0;
  truesumprice = 0;

  total: Number;
  setInterval: any;
  dateINV: string;
  dateinv: string;

  DateGroupsaleman: any;
  toDateimg = new Date();

  bsValueform: Date;
  bsValueto: Date;
  Datatodate: any;
  Datafromdate: any;

  DataED = '';
  ImageBillno = '';
  urlimg = '';

  closeNoED: boolean = false;
  closeED: boolean = false;

  customers = [];
  paCodeSO: string;
  paCustomer: string;
  paCustomerid: string;
  txtcustomer: string = 'ค้นหาจากลูกค้า';
  Datatoback: string;
  datatoback: string;
  datalogin2;
  paymenttype;
  viewpaymenttype: any;
  numviewpaymenttype: boolean = false;
  Filelist: any;

  chDraft = "0";

  listINV ='';
  constructor(private modalService: BsModalService, 
    private route: ActivatedRoute,
     private router: Router, 
     private ng2ImgMax: Ng2ImgMaxService, 
     private calendar: NgbCalendar, 
     private http: HttpClient, 
     private service: WebapiService) {
    this.datalogin = JSON.parse(sessionStorage.getItem('login'))
    localStorage.removeItem('DataSOderreview');
    localStorage.removeItem('DataSOderlist');
    this.datalogin = JSON.parse(sessionStorage.getItem('login'))
    console.log(sessionStorage.getItem('cashDateTo') + '/' + sessionStorage.getItem('cashDateFrom'))
    this.paymenttype = 'All'
    if (this.datalogin == null) {
      this.router.navigate(['login']);
    }

    this.urlimg = service.geturlserviceIMG();

    if (sessionStorage.getItem('cashDateTo') != null && sessionStorage.getItem('cashDateFrom') != null) {
      this.Datatodate = new Date(sessionStorage.getItem('cashDateTo'));
      this.Datafromdate = new Date(sessionStorage.getItem('cashDateFrom'));

    } else {
      this.fromDate = calendar.getPrev(calendar.getToday(), 'd', 365);
      this.toDate = calendar.getToday();
      this.Datatodate = new Date(this.toDate.year, this.toDate.month - 1, this.toDate.day);
      this.Datafromdate = new Date(this.toDate.year, this.toDate.month - 1, 1);
    }






    this.url = service.geturlservice();
    this.uploadimg = service.geturlloadimgservice();
    //this.datalogin[0].salegroup;
    // this.groupsale=this.datalogin[0].salegroup
    this.getuser();
    this.total = 0;
    this.dateinv = '';
    this.dateINV = '';
    this.getgroupsaleman();
    //  this.Datafromdate= new Date(this.fromDate.year, this.fromDate.month-1, 1);
    //  this.Datafromdate= new Date(this.fromDate.year, this.fromDate.month-1, 1);
    /*this.getdate();*/
    if (this.route.snapshot.queryParams.todate == undefined) {
      this.getbackto();
    } else {
      this.getdatetotoback();
    }
    if (this.route.snapshot.queryParams.fromdate == undefined) {
      this.getbackfromdate();
    } else {
      this.getdatefromdatetoback();
    }
    if (this.route.snapshot.queryParams.CodeINV == undefined) {

    } else {
      this.getidINV();
    }
    if (this.route.snapshot.queryParams.Customer == undefined) {

    } else {
      this.getbackcustomer();
    }

    if (this.route.snapshot.queryParams.CodeSo == undefined) {

    } else {
      this.getdataIDsales();
    }
    if (this.route.snapshot.queryParams.datatoback == undefined) {

    } else {
      this.getDatatoback();
    }



  }



  getDatatoback() {
    this.datatoback = this.route.snapshot.queryParams.datatoback
    this.DataSearch = [];
    this.DataED = '0';
    this.sumprice = 0.00;
    this.sumvat = 0.00;
    this.productprice = 0.00;
    this.Datatoback = this.datatoback;
    this.http.get<any>(this.datatoback).subscribe(res => {
      if (res.length > 0) {
        this.DataSearch = res;
        this.sumprice = 0.00;
        this.sumvat = 0.00;
        this.productprice = 0.00;
        this.sum();
        /*this.getdate();*/
      } else {
        this.sumprice = 0.00;
        this.sumvat = 0.00;
        this.productprice = 0.00;
        alert('ไม่พบข้อมูล');
      }

      /*   this.getdate();*/

    }, error => {
      alert('เกิดปัญหาในการ Process ข้อมูล กรุณารายการใหม่อีกครั้ง');
    });
  }


  getbackto() {
    this.todate = `${this.Datatodate.getFullYear()}-${this.Datatodate.getMonth() + 1}-${this.Datatodate.getDate()}`;
  }
  getbackfromdate() {
    this.fromdate = `${this.Datafromdate.getFullYear()}-${this.Datafromdate.getMonth() + 1}-${this.Datafromdate.getDate()}`;
  }

  getbackcustomer() {


    if (this.route.snapshot.queryParams.Customer == '%20') {
      this.txtcustomer = 'ค้นหาจากลูกค้า'
    } else {
      this.txtcustomer = this.route.snapshot.queryParams.Customer;
    }
  }
  getdatetotoback() {
    this.Datatodate = new Date(this.route.snapshot.queryParams.todate)
    this.Datafromdate = new Date(this.route.snapshot.queryParams.fromdate)
  }
  getdatefromdatetoback() {

    this.Datafromdate = new Date(this.route.snapshot.queryParams.fromdate)
  }
  getdataIDsales() {

    this.CodeSo = this.route.snapshot.queryParams.CodeSo;

  }


  Modalremark(template: TemplateRef<any>) {
    this.ModalremarkRef = this.modalService.show(template,
      { class: 'modal-lg' }
    );
  }


  getidINV() {

    this.CodeInvoice = this.route.snapshot.queryParams.CodeINV;
    this.dateINV = this.route.snapshot.queryParams.CodeINV;
    /* this.CodeSo=this.route.snapshot.queryParams.CodeSo
     this.Customer=this.route.snapshot.queryParams.Customer*/
  }
  getColor(country, sum) {

    // console.log(typeCK);


    if (sum < 0) {
      return '#000';
    }
    let today = Number(new Date())
    let invdate = Number(new Date(country))
    var invdate7 = today - invdate;
    /*alert(today);*********
    alert(invdate);     return 'green';*/
    if (invdate7 > **********) {
      return 'red';
    } else if (invdate7 <= ********** && invdate7 >= *********) {
      return '#0e0dde';
    } else {
      return 'green';
    }

  }

  getColormin(country, sum, summax, summin) {

    if (summax == summin && sum < 0) {
      return '#000';
    }

    let today = Number(new Date())
    let invdate = Number(new Date(country))
    var invdate7 = today - invdate;
    /*alert(today);********* = 7 วัน
    //1296000000   = 15 วัน
    //********** = 12วัน
    //86400000 = 1 วัน
    alert(invdate);     return 'green';*/
    if (invdate7 > **********) {
      return 'red';
    } else if (invdate7 <= ********** && invdate7 >= *********) {
      return '#0e0dde';
    } else {
      return 'green';
    }
  }

  getColorminBG(CK) {
    if (CK == 1) {
      return '#969595';
    } else {
      return '';
    }
  }




  /////
  search = (text$: Observable<any>) =>

    //Autocomplete ลูกค้า
    text$.pipe(
      debounceTime(200),
      map(term => term === '' ? []
        : this.customers.filter(v => v.name.toLowerCase().indexOf(term.toLowerCase()) > -1 || v.accountnum.toLowerCase().indexOf(term.toLowerCase()) > -1).slice(0, 50))
    );
  formatter = (x: { name: string, accountnum: string }) => x.name + ' (' + x.accountnum + ')';
  //////


  //ดึงรหัสลูกค้ามาใช้ใน Autocomplete
  getcostomerauto() {
    var idsale = this.datalogin[0].salegroup;
    if (this.datalogin[0].id_group_user == '153Admin' || this.datalogin[0].id_group_user == '1860Administrator') {
      idsale = '%20';
    } else {
      idsale = this.datalogin[0].salegroup;
    }

    this.http.get<any>(this.url + 'customerauto/' + idsale).subscribe(res => {
      this.customers = res;

    })
  }

  test(ele) {
    console.log(ele)
  }
  opendelete(event) {

    this.chDraft = "0";
  }

  getdate() {
    this.fromdate = `${this.Datafromdate.getFullYear()}-${this.Datafromdate.getMonth() + 1}-${this.Datafromdate.getDate()}`;
    this.todate = `${this.Datatodate.getFullYear()}-${this.Datatodate.getMonth() + 1}-${this.Datatodate.getDate()}`;
  }

  getgroupsaleman() {
    this.DateGroupsaleman = [];
    this.http.get<any>(this.url + 'salesman').subscribe(res => {
      if (res.length > 0) {
        this.DateGroupsaleman = res;
        this.getcostomerauto();
      } else {
        alert('ไม่สามารถติดต่อข้อมูลรหัส saleman ได้')
      }
    });
  }

  getuser() {
    /* if(this.datalogin[0].salegroup=='admin'){
       this.CodeSo='';
       this.testclose=true;
     }else{
      this.testclose=false;
      this.CodeSo=this.datalogin[0].salegroup;
     }*/
    if (this.datalogin[0].id_group_user == '153Admin' || this.datalogin[0].id_group_user == '1860Administrator') {
      this.CodeSo = '';
      this.testclose = true;
    } else {
      this.testclose = false;
      this.CodeSo = this.datalogin[0].salegroup;
    }
  }

  context: CanvasRenderingContext2D;
  /* @ViewChild("Mycanvas") Mycanvas;
   Preview(e : any):void{
    this.selectedFile = <File>e.target.files[0];
    let canvas = this.Mycanvas.nativeElement;
    let context = canvas.getContext('2d');
    context.clearRect(0, 0, 300, 300);

    var render = new FileReader();
    render.onload = function(event){
      var img = new Image();
      img.onload = function(){
        canvas.width = img.width;
        canvas.height = img.height;
        context.drawImage(img, 0, 0);
      };
      img.src = event.target.result;
    };
    render.readAsDataURL(e.target.files[0]);

   }*/
  handleFileInput(file: FileList) {
    this.load = '';
    this.selectedFile = '';
    console.log(this.Filelist)
    if (file.item(0).type == "image/jpeg") {
      // this.openModal2(true,'กำลังปรับขนาดไฟล์',false)&& file.item(0).size <= (1024*1024*5)
      let image = file.item(0);
      this.selectedFile = image;
      this.textload = this.selectedFile.name;
      var reader = new FileReader();
      reader.onload = (event: any) => {
        this.imageUrl = event.target.result;
      }
      reader.readAsDataURL(this.selectedFile);

      /* this.ng2ImgMax.resizeImage(image, 1024, 768).subscribe(
         result => {
           this.selectedFile = image;
           this.textload= this.selectedFile.name;
           //Show image preview
           var reader = new FileReader();
           reader.onload = (event:any) => {
             this.imageUrl = event.target.result;
           }
           reader.readAsDataURL(this.selectedFile);
          // this.openModal2(false,'',false)
         },
         error => {
          // this.openModal(true,error,false)
          alert('กรุณารายการใหม่อีกครั้ง');
         }
       );*/
      console.log(this.Filelist.length)
      console.log(this.selectedFile)
    } else {
      this.load = '';
      this.openModal(true, 'ชนิดไฟล์รูปภาพไม่ถูกต้อง กรุณาเลือกชนิดไฟล์เป็น .jpeg', false)
      this.imageUrl = "assets/img/default-image.png";
    }


  }





  ngOnInit() {

  }

  SearchInvoicecashlistEd() {
    this.closeED = true;
    this.DataED = '1';
    this.DataSearch = [];
    this.sumprice = 0.00;
    this.sumvat = 0.00;
    this.productprice = 0.00;
    this.load = '';
    var datacodeSo = '';
    this.dateINV = '';
    var dataCustomer = '';
    this.getdate();

    if (this.CodeInvoice !== '') {
      this.dateINV = this.CodeInvoice;
    }
    if (this.Customer == undefined && this.CodeInvoice == '' && this.CodeSo == '') {
      this.openModal(true, 'กรุณาป้อนข้อมูลอย่างน้อย 1 ช่อง ถ้าไม่ระบุวันที่ วันที่จะย้อนหลังเป็นเวลา 365 วัน', false)
    } else {
      if (this.fromdate == '') {
        this.fromdate = `${this.todate}`;
        sessionStorage.setItem('cashDateFrom', this.fromdate)
      } else {
        sessionStorage.setItem('cashDateFrom', this.fromdate)
      }
      if (this.todate == '') {
        this.todate = `${this.fromDate}`;
        sessionStorage.setItem('cashDateTo', this.todate)
      } else {
        sessionStorage.setItem('cashDateTo', this.todate)
      }


      if (this.Customer === undefined) {
        dataCustomer = '%20';
      } else {
        dataCustomer = `${this.Customer.accountnum}`
      }
      if (this.CodeInvoice == '') {
        this.CodeInvoice = '%20';
      }
      if (this.CodeSo == '') {
        if (this.datalogin[0].id_group_user == '153Admin' || this.datalogin[0].id_group_user == '1860Administrator') {
          datacodeSo = `${this.CodeSo}`;

        } else {
          datacodeSo = `${this.datalogin[0].salegroup}`;

        }
      }

      if (this.CodeSo !== '') {
        if (this.datalogin[0].id_group_user == '153Admin' || this.datalogin[0].id_group_user == '1860Administrator') {
          datacodeSo = `${this.CodeSo}`;

        } else {
          datacodeSo = `${this.datalogin[0].salegroup}`;

        }
      }
      if (datacodeSo == '') {
        datacodeSo = '%20';

      }
      if (dataCustomer === 'undefined') {
        dataCustomer = '%20';

      }


      /*alert(this.url + 'cash_invoice/'+ this.fromdate + '/' + this.todate +'/' + this.CodeSo + '/' + this.CodeInvoice + '/' + this.Customer)*/
      this.http.get<any>(this.url + 'cash_invoice_modify_INV/' + this.fromdate + '/' + this.todate + '/' + datacodeSo + '/' + this.CodeInvoice + '/' + dataCustomer).subscribe(res => {
        if (res.length > 0) {
          this.DataSearch = res;
          this.sumprice = 0.00;
          this.sumvat = 0.00;
          this.productprice = 0.00;
          this.sum();
          /*this.getdate();*/
          if (this.datalogin[0].salegroup == 'admin') {
            /* this.CodeSo='';*/
          } else {

          }
          if (dataCustomer == '%20') {
            dataCustomer = '';
          }
          if (this.CodeInvoice == '%20') {
            this.CodeInvoice = '';
          }

        } else {
          if (this.datalogin[0].id_group_user == '153Admin' || this.datalogin[0].id_group_user == '1860Administrator') {
            /* this.CodeSo='';*/
          } else {

          }
          if (dataCustomer == '%20') {
            dataCustomer = '';
          }
          if (this.CodeInvoice == '%20') {
            this.CodeInvoice = '';
          }
          this.sumprice = 0.00;
          this.sumvat = 0.00;
          this.productprice = 0.00;
          this.openModal(true, 'ไม่พบข้อมูล', false);
          /*   this.getdate();*/
        }
      }, error => {
        //  this.openModal(true,'เกิดปัญหาในการ Process ข้อมูล กรุณารายการใหม่อีกครั้ง',false);
        alert('เกิดปัญหาในการ Process ข้อมูล กรุณารายการใหม่อีกครั้ง');
      });
    }
  }


  SearchInvoicecashlist() {




    this.closeED = false;
    this.DataED = '0';
    this.DataSearch = [];
    this.sumprice = 0.00;
    this.sumvat = 0.00;
    this.productprice = 0.00;
    this.load = '';
    var datacodeSo = '';
    this.dateINV = '';
    var dataCustomer = '';
    this.getdate();
    var datatobackid: string = this.route.snapshot.queryParams.customerid;
    var datatobackname: string = this.route.snapshot.queryParams.Customer;
    if (this.CodeInvoice !== '') {
      this.dateINV = this.CodeInvoice;
    }
    if (this.Customer === undefined && this.CodeInvoice == '' && this.CodeSo == '' && this.txtcustomer == 'ค้นหาจากลูกค้า') {
      this.openModal(true, 'กรุณาป้อนข้อมูลอย่างน้อย 1 ช่อง ถ้าไม่ระบุวันที่ วันที่จะย้อนหลังเป็นเวลา 365 วัน', false)
    } else {
      if (this.fromdate == '') {
        this.fromdate = `${this.todate}`;
        sessionStorage.setItem('cashDateFrom', this.fromdate)
      } else {
        sessionStorage.setItem('cashDateFrom', this.fromdate)
      }
      if (this.todate == '') {
        this.todate = `${this.fromDate}`;
        sessionStorage.setItem('cashDateTo', this.todate)
      } else {
        sessionStorage.setItem('cashDateTo', this.todate)
      }

      if (this.Customer === undefined && datatobackid === undefined) {
        dataCustomer = '%20';
        this.paCustomer = '%20';

      } else if (datatobackid !== undefined && this.Customer === undefined) {

        dataCustomer = datatobackid;
        this.paCustomer = datatobackname;
        this.paCustomerid = datatobackid;
      }
      else {
        this.txtcustomer = 'ค้นหาจากลูกค้า'
        dataCustomer = `${this.Customer.accountnum}`
        this.paCustomer = `${this.Customer.name}`
        this.paCustomerid = `${this.Customer.accountnum}`
      }

      if (this.CodeInvoice == '') {
        this.CodeInvoice = '%20';
      }
      if (this.CodeSo == '') {
        if (this.datalogin[0].id_group_user == '153Admin' || this.datalogin[0].id_group_user == '1860Administrator') {
          datacodeSo = `${this.CodeSo}`;
          this.paCodeSO = `${this.CodeSo}`;
        } else {
          datacodeSo = `${this.datalogin[0].salegroup}`;
          this.paCodeSO = `${this.datalogin[0].salegroup}`;
        }
      }

      if (this.CodeSo !== '') {
        if (this.datalogin[0].id_group_user == '153Admin' || this.datalogin[0].id_group_user == '1860Administrator') {
          datacodeSo = `${this.CodeSo}`;
          this.paCodeSO = `${this.CodeSo}`;
        } else {
          datacodeSo = `${this.datalogin[0].salegroup}`;
          this.paCodeSO = `${this.datalogin[0].salegroup}`;
        }
      }

      if (datacodeSo == '') {
        datacodeSo = '%20';
        this.paCodeSO = '%20'
      }
      if (dataCustomer === 'undefined') {
        dataCustomer = '%20';
        this.paCustomer = '%20'
      }
      //  alert(dataCustomer)
      this.Datatoback = this.url + 'cash_invoice_search/' + this.fromdate + '/' + this.todate + '/' + datacodeSo + '/' + this.CodeInvoice + '/' + dataCustomer
      /*alert(this.url + 'cash_invoice/'+ this.fromdate + '/' + this.todate +'/' + this.CodeSo + '/' + this.CodeInvoice + '/' + this.Customer)*/
      this.http.get<any>(this.url + 'cash_invoice_search/' + this.fromdate + '/' + this.todate + '/' + datacodeSo + '/' + this.CodeInvoice + '/' + dataCustomer).subscribe(res => {

        if (res.length > 0) {
          this.DataSearch = res;
          this.sumprice = 0.00;
          this.sumvat = 0.00;
          this.productprice = 0.00;
          this.sum();
          /*this.getdate();*/
          if (this.datalogin[0].salegroup == 'admin') {
            /* this.CodeSo='';*/
          } else {

          }
          if (dataCustomer == '%20') {
            dataCustomer = '';
          }
          if (this.CodeInvoice == '%20') {
            this.CodeInvoice = '';
          }

        } else {
          if (this.datalogin[0].salegroup == 'admin') {
            /* this.CodeSo='';*/
          } else {

          }
          if (dataCustomer == '%20') {
            dataCustomer = '';
          }
          if (this.CodeInvoice == '%20') {
            this.CodeInvoice = '';
          }
          this.sumprice = 0.00;
          this.sumvat = 0.00;
          this.productprice = 0.00;
          this.openModal(true, 'ไม่พบข้อมูล', false);
          /*   this.getdate();*/
        }
      }, error => {
        //  this.openModal(true,'เกิดปัญหาในการ Process ข้อมูล กรุณารายการใหม่อีกครั้ง',false);
        alert('เกิดปัญหาในการ Process ข้อมูล กรุณารายการใหม่อีกครั้ง');
      });
    }

  }
  sum() {
    if (this.DataSearch.length > 0) {
      for (var i = 0; i < this.DataSearch.length; i++) {
        this.productprice += this.DataSearch[i].Salesbalance;
        this.sumvat += this.DataSearch[i].Sumtax;
        this.sumprice += this.DataSearch[i].Invoiceamount;
      }
    }
  }

  searchINV(datasale, dataCustomer) {
    this.idinvi = [];
    this.allid = [];
    this.listINV ='';
    this.getdate();
    if (this.dateINV == '') {
      this.dateinv = '%20';
    } else {
      this.dateinv = this.dateINV;
    }
    this.http.get<any>(this.url + 'cash_invoice/' + this.fromdate + '/' + this.todate + '/' + datasale + '/' + dataCustomer + '/' + this.dateinv + '/' + this.DataED).subscribe(res => {
      if (res.length > 0) {
        this.dateinv = '';
        for (var i = 0; i < res.length; i++) {
          this.idinvi.push({
            id: res[i].id,
            salesid: res[i].salesid,
            invoiceid: res[i].invoiceid,
            invoicedate: res[i].invoicedate,
            duedate: res[i].duedate,
            salesbalance: res[i].salesbalance,
            sumtax: res[i].sumtax,
            invoiceamount: res[i].invoiceamount,
            SOno: res[i].salesid,
            orderaccount: res[i].orderaccount,
            payment: res[i].payment,
            imgurl: res[i].attachedfile,
            check: false,
            typeCK: res[i].typeCK
          });
        }

        this.getdate();
        if (this.datalogin[0].id_group_user == '153Admin' || this.datalogin[0].id_group_user == '1860Administrator') {

        } else {

        }
      } else {
        this.dateinv = '';
        if (this.datalogin[0].id_group_user == '153Admin' || this.datalogin[0].id_group_user == '1860Administrator') {

        } else {

        }
        this.openModal(true, 'ไม่พบข้อมูล', false);
        this.getdate();
      }
    }, error => {
      //this.openModal(true,'เกิดปัญหาในการ Process ข้อมูล',false);
      alert('เกิดปัญหาในการ Process ข้อมูล');
    });
  }
  selectAll(checked) {
    this.truesumprice = 0.00;
    this.truesumvat = 0.00;
    this.trueproductprice = 0.00;
    for (var i = 0; i < this.idinvi.length; i++) {
      if (this.paymenttype == this.idinvi[i].payment) {
        this.idinvi[i].check = checked;
      } else if (this.paymenttype === 'All') {
        this.idinvi[i].check = checked;
      }
    } this.GetINV();
    this.total = this.allid.length
    this.sumTrue(this.paymenttype);
  }

  checkIfAllSelected(checked, index, payment) {
    this.viewpaymenttype[index].check = checked;
    this.total = 0;
    this.GetINV();
    this.total = this.allid.length
    this.getcaheck();
    this.sumTrue(payment);
  }


  sumTrue(payment) {
    this.truesumprice = 0.00;
    this.truesumvat = 0.00;
    this.trueproductprice = 0.00;
    for (var i = 0; i < this.viewpaymenttype.length; i++) {
      if (this.viewpaymenttype[i].check == true) {
        if (payment == this.viewpaymenttype[i].payment) {
          this.trueproductprice += this.viewpaymenttype[i].salesbalance;
          this.truesumvat += this.viewpaymenttype[i].sumtax;
          this.truesumprice += this.viewpaymenttype[i].invoiceamount;
        } else if (this.paymenttype === 'All') {
          this.trueproductprice += this.viewpaymenttype[i].salesbalance;
          this.truesumvat += this.viewpaymenttype[i].sumtax;
          this.truesumprice += this.viewpaymenttype[i].invoiceamount;
        }

      }

    }
  }

  getcaheck() {
    var ch = 0;
    for (var i = 0; i < this.viewpaymenttype.length; i++) {
      if (this.viewpaymenttype[i].check == true) {
        ch++;
      } else {
        ch--;
      }
    }
    if (this.viewpaymenttype.length == ch) {
      this.testcheck = true;
    } else {
      this.testcheck = false;
    }

  }


  GetINV() {
    this.allid = [];
    this.listINV='';
    for (var i = 0; i < this.viewpaymenttype.length; i++) {
      if (this.viewpaymenttype[i].check == true) {
        this.allid.push({
          id: this.viewpaymenttype[i].invoiceid,
          SOno: this.viewpaymenttype[i].SOno,
          orderaccount: this.viewpaymenttype[i].orderaccount,
        //  Productprice: this.viewpaymenttype[i].salesbalance,
        //  Sumvat: this.viewpaymenttype[i].sumtax,
        //  Sumprice: this.viewpaymenttype[i].invoiceamount
        });
      
          this.listINV = this.listINV +',' +this.viewpaymenttype[i].invoiceid
        
      }

    }
  }


  setinvoiceid(dataCustomer, salegroup) {



    this.Filelist = undefined;
    this.chDraft = "0";
    this.searchINV(salegroup, dataCustomer);
    this.truesumprice = 0.00;
    this.truesumvat = 0.00;
    this.trueproductprice = 0.00;
    this.testcheck = false;
    this.setInvoice = '';
    this.selectedFile = '';
    this.textload = '';
    this.total = 0;
    this.imageUrl = 'assets/img/default-image.png';
    this.setInvoice = dataCustomer;
    this.paymenttype = 'All'
    // this.viewpaymenttype=this.idinvi;
    //  this.numviewpaymenttype=0;
    this.searchviewPaymenttype();
    this.Cknumviewpaymenttype(this.paymenttype)
  }

  onFileSelect(event) {
    this.selectedFile = <File>event.target.files[0];

  }
  onUpload(ele, total) {

    var datatypeCK = 0;
    if (ele == "1") {
      datatypeCK = 1
    } else if (ele == "0") {
      datatypeCK = 2
    } else {
      alert("เลือกประเภทสถานะก่อนทำรายการ")
      return false;
    }
    if (total < 1) {
      alert("ทำรายการไม่ถูกต้อง กรุณาทำรายการใหม่")
      return false;
    }

    this.openModal2(true, 'กำลังบันทึกข้อมูล', false);
    this.toDateimg = new Date();

    var dateimg = `${this.toDateimg.getFullYear()}-${this.toDateimg.getMonth() + 1}-${this.toDateimg.getDate()}`;
    var random = `${this.toDateimg.getMilliseconds()}-${this.toDateimg.getMinutes()}`
    const fd = new FormData();
    var Random = this.getRandomInt(this.max);
    /* var nameimg = this.allid[0].SOno;*/
    var imgsetInvoice = `${this.allid[0].orderaccount}-${dateimg}-${random}-${Random}.jpg`;
    var urlimg = `${this.url}${imgsetInvoice}/inv/upload`;
    console.log(this.selectedFile)
    if (this.selectedFile == '') {
      imgsetInvoice = "";
      this.newsaveinvoice(imgsetInvoice, datatypeCK);
    } else {
      fd.append('image', this.selectedFile, this.selectedFile.name)

      this.openModal2(false, '', false);
      this.openModal2(true, 'กำลังอัพโหลดรูปภาพ', false);
      this.http.post<any>(urlimg, fd, {
        reportProgress: true,
        observe: 'events'
      }).subscribe(event => {
        if (event.type === HttpEventType.UploadProgress) {
          console.log('Upload' + Math.round(event.loaded / event.total * 100) + '%');
          this.load = ' สถานะ :  Upload ' + Math.round(event.loaded / event.total * 100) + ' %';
        } else if (event.type === HttpEventType.Response) {
          console.log(event);
          if (event.body.success === true) {
            this.openModal2(false, '', false);
            this.openModal2(true, 'กำลังบันทึกข้อมูล : ', false);
            // this.setInterval=setInterval(() => this.saveinvoice(imgsetInvoice), 400);  
            this.newsaveinvoice(imgsetInvoice, datatypeCK);

          } else {
            this.openModal2(false, '', false);
            this.openModal(true, 'ไม่สามารถบันทึกข้อมูลได้', false);
          }
        }
        /* if (event.success === true) {
           this.openModal2(false,'',false);
           this.openModal(true,'บันทึกข้อมูลเสร็จสิ้น',false); 
       }else{
         this.openModal2(false,'',false);
         this.openModal(true,'ไม่สามารถบันทึกข้อมูลได้',false);
       } */
      });
    }



    this.DataSearch = [];
    this.sumprice = 0.00;
    this.sumvat = 0.00;
    this.productprice = 0.00;
  }

  newsaveinvoice(imgsetInvoice, typeCK) {
    //  alert(JSON.stringify(this.allid))
    this.http.post(this.url + 'update_invoiceNEW', {
   //   Data: this.allid,
      Data : this.listINV,
      nameimg: imgsetInvoice,
      CK: typeCK
    }).subscribe(res => {
      this.openModal2(false, '', false);
      if (res == true) {

        this.load = '';
        this.openModalsuccess(true, 'บันทึกข้อมูลเสร็จสิ้น', false);
        // this.load ='รายการที่ : '+ this.allid.length.toString() + '-' + res.toString();
        //  alert('โอเคครับ')
        if (this.closeED == true) {
          this.SearchInvoicecashlistEd();
        } else {
          this.SearchInvoicecashlist();
        }

        console.log(this.allid.length + '--------' + res.toString())
      }
    }, error => {
      //เกิดปัญหาในการ Process ข้อมูล
      // this.openModalsuccess(true,'เกิดปัญหาในการ Process ข้อมูล',false); 
      // this.openModal(true,'เกิดปัญหาในการ Process ข้อมูล',false);
      alert('เกิดปัญหาในการ Process ข้อมูล');
    });

  }



  saveinvoice(imgsetInvoice) {
    if (this.allid.length != 0) {
      this.http.post(this.url + 'update_invoice/' + this.allid[0].id + '/' + imgsetInvoice, '').subscribe(res => {
        if (res == true) {
          this.deletelallid(0);
          this.load = 'รายการที่ : ' + this.allid.length.toString() + '-' + res.toString();
          console.log(this.allid.length + '--------' + res.toString())
        }
      }, error => {
        this.openModal2(true, 'เกิดปัญหาในการ Process ข้อมูล', false);
      });
    } else {
      if (this.allid.length == 0) {
        clearInterval(this.setInterval);
        this.openModal2(false, '', false);
        this.load = '';
        this.openModalsuccess(true, 'บันทึกข้อมูลเสร็จสิ้น', false);
      }
    }

  }
  openModalsuccess(open: boolean, text: string, load: boolean): void {
    this.mdlSampleIsOpensuccess = open;
    this.altsuccess = text;
    this.checkreloadsuccess = load;
  }
  closemodelsuccess(cl: boolean) {
    this.mdlSampleIsOpensuccess = cl;
    if (this.checkreloadsuccess == false) {
      //   this.SearchInvoicecashlist();
      //alert('OK')
    }
  }


  clear() {
    this.setInvoice = '';
    this.selectedFile = '';
    this.textload = '';
    this.imageUrl = 'assets/img/default-image.png';
  }
  openModal(open: boolean, text: string, load: boolean): void {
    this.mdlSampleIsOpen = open;
    this.alt = text;
    this.checkreload = load;
  }
  closemodel(cl: boolean) {
    this.mdlSampleIsOpen = cl;
    if (this.checkreload == false) {

    }
  }
  closemodel2(cl: boolean) {
    this.mdlSampleIsOpen = cl;
    if (this.checkreload == false) {

    }

  }
  openModal2(open: boolean, text: string, load: boolean): void {
    this.mdlSampleIsOpen2 = open;
    this.alt2 = text;
    this.checkreload2 = load;
  }
  getRandomInt(max) {
    return Math.floor(Math.random() * Math.floor(max));
  }
  deletelallid(value) {
    this.allid.splice(value, 1);
  }
  /*openModalIMG(open : boolean,text: string,load:boolean,texturl :string) : void {
    this.mdlSampleIsOpenIMG = open;
    this.altimg=text;
    this.setImgBill(texturl)
  }*/
  closemodelIMG(cl: boolean) {
    this.mdlSampleIsOpenIMG = cl;
    if (this.checkreload == false) {

    }
  }

  openModalIMG(template: TemplateRef<any>, texturl: string, CK) {
    // this.altimg=text;
    this.setImgBill(texturl, CK)
    this.modalRefshow = this.modalService.show(template,
      { class: 'modal-lg' }
    );

  }

  setImgBill(nameimage, CK) {
    this.ImageBillno = this.urlimg + nameimage;

  }

  openpdf(valueid: string) {
    //this.router.navigate(['/PDFprint', { queryParams: { idINV: valueid }}]);this.fromdate + '/' + this.todate
    this.router.navigate(['/PDFprint'], { queryParams: { idINV: valueid, fromdate: this.fromdate, todate: this.todate, CodeSo: this.paCodeSO, Customer: this.paCustomer, paINV: this.CodeInvoice, idcustomer: this.paCustomerid, Datatoback: this.Datatoback } });
    /* this.router.navigate(['PDFprint']);*/
  }


  searchviewPaymenttype() {
    this.viewpaymenttype = [];
    this.numviewpaymenttype = false;
    let Ck = false
    this.testcheck = Ck
    this.selectAll(this.testcheck);
    // this.numviewpaymenttype= this.viewpaymenttype.length
    if (this.paymenttype === 'All') {
      this.viewpaymenttype = this.idinvi;
      this.Cknumviewpaymenttype(this.paymenttype);
      // alert(this.paymenttype);
    } else {
      this.viewpaymenttype = this.idinvi.filter(v => v.payment.toLowerCase().indexOf(this.paymenttype.toLowerCase()) > -1);
      this.numviewpaymenttype = this.viewpaymenttype.length
      this.Cknumviewpaymenttype(this.paymenttype);
      //  alert('2');
    }

  }


  Cknumviewpaymenttype(v) {
    if (v === 'All') {
      // this.numviewpaymenttype= this.idinvi.length
      if (this.viewpaymenttype.length < 1) {
        // this.numviewpaymenttype=true;
      } else {
        this.numviewpaymenttype = false;
      }
    } else {
      // this.numviewpaymenttype= this.viewpaymenttype.length
      if (this.viewpaymenttype.length < 1) {
        this.numviewpaymenttype = true;
      } else {
        this.numviewpaymenttype = false;
      }
    }
  }

  cancel() {

    this.Customer = undefined;

  }





}
