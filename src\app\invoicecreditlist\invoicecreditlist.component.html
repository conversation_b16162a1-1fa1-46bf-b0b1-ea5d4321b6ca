<app-topmenu></app-topmenu>
<section style="padding-top:60px">
    <div class="container-fluid" style="padding-right: 5px; padding-left: 5px;">
        <h5 class="p-sm-1 bg-secondary text-white text-center">Credit Invoice List</h5>
        <!--Filter Section-->
        <div class="form-row">
            <div class="col-md-2 mb-2" *ngIf="testclose">

                <select multiple [(ngModel)]="salegroup" class="custom-select custom-select-sm">
                    <option selected disabled value="">--เลือกรหัสพนักงานขาย--</option>
                    <option value="1">เลือกรายการทั้งหมด</option>
                    <option *ngFor="let item of DateGroupsaleman" [value]="item.groupid">
                        {{item.groupid}} ({{item.name}})
                    </option>
                </select>

            </div>
            <div class="col-md-2 mb-2">
                <ng-template #rt let-r="result" let-t="term">
                    <div (click)="getcustomersalefunction(r.name)">
                        <label (mousedown)="getcustomersalefunction(r.name)">{{r.name}} ({{r.accountnum}})</label>
                    </div>

                </ng-template>
                <div style=" position: relative;">
                    <input id="typeahead-template" placeholder="{{txtcustomer}}" type="text" class="form-control form-control-sm" [(ngModel)]="Customer"
                        name="model" [ngbTypeahead]="search" [resultTemplate]="rt" [inputFormatter]="formatter" />

                    <div style="position: absolute;z-index: 10;width: 15%;height: 100%;top: 1px;right: 1px;font-size: 18px;cursor: pointer;text-align: center;"
                        (click)="cancel()">x</div>
                    <!--    <input id="customer" class="form-control form-control-sm" type="text" [(ngModel)]="customer" name="customer" placeholder="ค้นหาลูกค้า">-->
                </div>
            </div>

            <div class="col-md-2 mb-2">
                <input id="billno" class="form-control form-control-sm" type="text" [(ngModel)]="billno" name="billno" placeholder="เลขที่บิล">
            </div>


            <!--  <div class="col-md-2 mb-2"> 
                        <input id="fromdate" class="form-control form-control-sm" type="date" value="{{dateshipping}}" [(ngModel)]="fromdate" name="fromdate" placeholder="จากวันที่">
                    </div>
                    <div class="col-md-2 mb-2">
                        <input id="todate" class="form-control form-control-sm" type="date" value="{{dateshippingto}}" [(ngModel)]="todate" name="todate" placeholder="ถึงวันที่">
                    </div>-->
            <div class="col-xs-12 col-12 col-md-2 form-group">
                <input type="text" placeholder="DD/MM/YYYY" class="form-control" bsDatepicker [bsConfig]="{ dateInputFormat: 'DD/MM/YYYY' }"
                    class="form-control form-control-sm" [(ngModel)]="Datafromdate">
            </div>

            <div class="col-xs-12 col-12 col-md-2 form-group">
                <input type="text" placeholder="DD/MM/YYYY" class="form-control" bsDatepicker [bsConfig]="{ dateInputFormat: 'DD/MM/YYYY' }"
                    class="form-control form-control-sm" [(ngModel)]="Datatodate">
            </div>
            <div class="col-md-2 mb-3 col-12 text-center text-sm-center text-md-center text-lg-left" style="padding-right: 0px; padding-left: 0px;">
                <button style="width: 55px;" (click)="Searchinvoicecredit()" class="btn btn-primary btn-sm font-weight-light" type="button">Search</button>

                <button style="margin-left: 3px; width: 68px;" (click)="SearchinvoicecreditED()" class="btn btn-primary btn-sm font-weight-light"
                    type="button">Edit Bill</button>
                <!--<button  style="margin-left: 3px; width: 55px;"class="btn btn-primary btn-sm font-weight-light" (click)="cancel()" >Clear</button>-->
            </div>
        </div>
        <table class="table table-hover table-bordered table-sm">
            <thead>
                <tr class="text-sm-center bg-light">
                    <th class="text-sm-center font-weight-normal" style="text-align: center;" scope="col">Item</th>
                    <th class="text-sm-center font-weight-normal" style="text-align: center;" scope="col">Bill No</th>
                    <th class="text-sm-center font-weight-normal" style="text-align: center;" scope="col">Bill Date</th>
                    <th class="text-sm-center font-weight-normal" style="text-align: center;" scope="col">Due Date</th>
                    <th class="text-sm-center font-weight-normal" style="text-align: center;" scope="col">พนักงานขาย</th>
                    <th class="text-sm-center font-weight-normal" scope="col">รหัสลูกค้า</th>
                    <th class="text-sm-center font-weight-normal" scope="col" style="width: 35%;text-align: center;">ชื่อลูกค้า</th>
                    <th class="text-sm-center font-weight-normal" style="text-align: center;" scope="col">มูลค่าสินค้า</th>
                    <th class="text-sm-center font-weight-normal" style="text-align: center;" scope="col">VAT</th>
                    <th class="text-sm-center font-weight-normal" style="text-align: center;" scope="col">มูลค่ารวม</th>
                    <th class="font-weight-light" scope="col" class="text-sm-center" width="60px"></th>
                </tr>
            </thead>
            <tbody>
                <tr *ngFor="let item of invoicecredit; let i=index" class="text-sm-left">
                    <td class="text-sm-center font-weight-normal " style="text-align: center;" [ngStyle]="{'color':getColor(item.invoicedate,item.invoiceamount,item.sumMax,item.summin),'background-color':getColorminBG(item.typeCK)}">{{i+1}}</td>
                    <td class="text-sm-center font-weight-normal " style="text-align: center;" [ngStyle]="{'color':getColor(item.invoicedate,item.invoiceamount,item.sumMax,item.summin)}">{{item.billno}}</td>
                    <td class="text-sm-center font-weight-normal" style="text-align: center;" [ngStyle]="{'color':getColor(item.invoicedate,item.invoiceamount,item.sumMax,item.summin)}">{{item.billingnotesdate
                        | date: 'dd/MM/yyyy' }}</td>
                    <td class="text-sm-center font-weight-normal" style="text-align: center;" [ngStyle]="{'color':getColor(item.invoicedate,item.invoiceamount,item.sumMax,item.summin)}">{{item.duedate
                        | date: 'dd/MM/yyyy' }}</td>
                    <td class="text-sm-center font-weight-normal" style="text-align: center;" [ngStyle]="{'color':getColor(item.invoicedate,item.invoiceamount,item.sumMax,item.summin)}">{{item.salegroup}}</td>
                    <td class="text-sm-center font-weight-normal" style="text-align: center;" [ngStyle]="{'color':getColor(item.invoicedate,item.invoiceamount,item.sumMax,item.summin)}">{{item.orderaccount}}</td>
                    <td class="text-sm-left font-weight-normal" style="text-align: left;" [ngStyle]="{'color':getColor(item.invoicedate,item.invoiceamount,item.sumMax,item.summin)}">{{item.invoicingname}}</td>
                    <td class="text-sm-right  font-weight-normal" style="text-align: right;" [ngStyle]="{'color':getColor(item.invoicedate,item.invoiceamount,item.sumMax,item.summin)}">{{item.salesbalance
                        | number:'1.2-2' }}</td>
                    <td class="text-sm-right font-weight-normal" style="text-align: right;" [ngStyle]="{'color':getColor(item.invoicedate,item.invoiceamount,item.sumMax,item.summin)}">{{item.sumtax
                        | number:'1.2-2' }}</td>
                    <td class="text-sm-right font-weight-normal" style="text-align: right;" [ngStyle]="{'color':getColor(item.invoicedate,item.invoiceamount,item.sumMax,item.summin)}">{{item.invoiceamount
                        | number:'1.2-2'}}</td>
                    <td class="text-center">
                        <button class="btn btn btn-warning" style="padding: 0pt" data-toggle="modal" (click)="GetBillNo(item)" data-target="#ModalupFile"
                            aria-expanded="true" aria-controls="collapseOne">
                            Upload
                        </button>
                    </td>
                </tr>
                <tr class="text-sm-left">
                    <th class="text-sm-center font-weight-light"></th>
                    <td class="text-sm-center font-weight-light"></td>
                    <td class="text-sm-center font-weight-light"></td>
                    <td class="text-sm-center font-weight-light"></td>
                    <td class="text-sm-center font-weight-light"></td>
                    <td class="text-sm-center font-weight-light"></td>
                    <td class="text-sm-right font-weight-normal" style="text-align: right;">มูลค่ารวม</td>
                    <td class="text-sm-right font-weight-normal" style="text-align: right;">{{ productprice | number:'1.2-2'
                        }}
                    </td>
                    <td class="text-sm-right font-weight-normal" style="text-align: right;">{{ sumvat | number:'1.2-2' }}</td>
                    <td class="text-sm-right font-weight-normal" style="text-align: right;">{{ sumprice | number:'1.2-2'
                        }}
                    </td>
                    <td class="text-sm-center font-weight-light"></td>


                </tr>
            </tbody>
        </table>
    </div>
</section>

<div class="modal fade" id="ModalupFile" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="exampleModalLabel" style="color: red;">Upload File Bill No : {{ showbillno }}
                </h5>
                <button type="button" class="close" data-dismiss="modal" (click)="clear()" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div style="overflow-y: auto;">
                    <table class="table table-hover table-bordered table-sm">
                        <thead>
                            <tr class="text-sm-center bg-light">
                                <th class="text-sm-center font-weight-normal" scope="col"></th>
                                <th nowrap class="text-sm-center font-weight-normal" style="text-align: center;" scope="col">SO
                                    No
                                </th>
                                <th nowrap class="text-sm-center font-weight-normal" style="text-align: center;" scope="col">Invoice
                                    No
                                </th>
                                <th nowrap class="text-sm-center font-weight-normal" style="text-align: center;" scope="col">Invoice
                                    Date
                                </th>
                                <th nowrap class="text-sm-center font-weight-normal" style="text-align: center;" scope="col">Due
                                    Date
                                </th>
                                <th class="text-sm-center font-weight-normal" style="text-align: center;" scope="col">มูลค่าสินค้า</th>
                                <th class="text-sm-center font-weight-normal" style="text-align: center;" scope="col">VAT</th>
                                <th class="text-sm-center font-weight-normal" style="text-align: center;" scope="col">มูลค่ารวม</th>
                                <th class="text-sm-center font-weight-normal" scope="col" class="text-sm-center" width="30px">
                                    <!-- [(ngModel)]="selectedAll"-->
                                    <input type="checkbox" [(ngModel)]="testcheck" (click)="selectAll($event.target.checked);">
                                </th>
                                <th class="text-sm-center font-weight-normal" scope="col"></th>
                                <th class="text-sm-center font-weight-normal" scope="col"></th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr *ngFor="let itembill of idinvi; let i=index">
                                <th nowrap class="text-sm-center font-weight-normal" style="text-align: center;" [ngStyle]="{'color':getColorinv(itembill.invoicedate,itembill.invoiceamount),'background-color':getColorminBG(itembill.typeCK)}">{{i+1}}</th>
                                <th nowrap class="text-sm-center font-weight-normal" style="text-align: center;" [ngStyle]="{'color':getColorinv(itembill.invoicedate,itembill.invoiceamount)}">{{itembill.salesid}}</th>
                                <td nowrap class="text-sm-center font-weight-normal" style="text-align: center;" [ngStyle]="{'color':getColorinv(itembill.invoicedate,itembill.invoiceamount)}">{{itembill.invoiceid}}</td>
                                <td nowrap class="text-sm-center font-weight-normal" style="text-align: center;" [ngStyle]="{'color':getColorinv(itembill.invoicedate,itembill.invoiceamount)}">{{itembill.invoicedate
                                    | date: 'dd/MM/yyyy'}}</td>
                                <td nowrap class="text-sm-center font-weight-normal" style="text-align: center;" [ngStyle]="{'color':getColorinv(itembill.invoicedate,itembill.invoiceamount)}">{{itembill.duedate
                                    | date: 'dd/MM/yyyy'}}</td>
                                <td class="text-sm-right font-weight-normal" style="text-align: right;" [ngStyle]="{'color':getColorinv(itembill.invoicedate,itembill.invoiceamount)}">{{itembill.salesbalance
                                    | number:'1.2-2'}}</td>
                                <th class="text-sm-right font-weight-normal" style="text-align: right;" [ngStyle]="{'color':getColorinv(itembill.invoicedate,itembill.invoiceamount)}">{{itembill.sumtax
                                    | number:'1.2-2'}}</th>
                                <th class="text-sm-right font-weight-normal" style="text-align: right;" [ngStyle]="{'color':getColorinv(itembill.invoicedate,itembill.invoiceamount)}">{{itembill.invoiceamount
                                    | number:'1.2-2'}}</th>
                                <td class="text-center">
                                    <input type="checkbox" [checked]="itembill.check" (click)="checkIfAllSelected($event.target.checked,i);">
                                </td>
                                <td class="text-center" >
                                    <button *ngIf="closeED" class="btn btn-sm btn-warning" style="padding: 0px;" (click)="openModalIMG(Modalview,itembill.imgurl,itembill.typeCK)">
                                        View
                                    </button>
                                    <button *ngIf="!closeED" [disabled]="(itembill.typeCK!=='1')" class="btn btn btn-warning" style="padding: 0px;" (click)="openModalIMG(Modalview,itembill.imgurl,itembill.typeCK)">
                                        View
                                    </button>
                                </td>
                                <td class="text-center" *ngIf="!closeED">
                                    <button class="btn btn btn-warning" data-dismiss="modal" style="padding: 0px;" (click)="openpdf(itembill.invoiceid)">
                                        print
                                    </button>
                                </td>
                            </tr>



                            <td class="text-sm-right text-md-right text-right font-weight-normal" colspan="5" style="text-align: right;">มูลค่ารวม</td>
                            <td class="text-sm-right font-weight-normal" style="text-align: right;">{{ trueproductprice |
                                number:'1.2-2' }}</td>
                            <td class="text-sm-right font-weight-normal" style="text-align: right;">{{ truesumvat | number:'1.2-2'
                                }}
                            </td>
                            <td class="text-sm-right font-weight-normal" style="text-align: right;">{{ truesumprice | number:'1.2-2'
                                }}
                            </td>
                            <td class="text-sm-center font-weight-normal"></td>
                            <td class="text-sm-center font-weight-normal"></td>
                            <td class="text-sm-center font-weight-normal"></td>
                        </tbody>
                    </table>
                </div>

                <!-- <img src="{{ImageIN}}" style="width: 200px; height: 200px;"/><br>
                  {{imgInvoice}}-->
                <div class="form-group col-sm-6 offset-sm-3">
                    <div class="custom-file" style="margin-bottom: 10px">
                        <input type="file" class="custom-file-input" [(ngModel)]="Filelist" #Image accept="image/*" (change)="handleFileInput($event.target.files)"
                            id="inputGroupFile01" aria-describedby="inputGroupFileAddon01">
                        <label class="custom-file-label" [(ngModel)]="textload" for="inputGroupFile01">{{ textload }} </label>
                    </div>
                </div>
                <div *ngIf="total!=0" class="form-group col-sm-6 offset-sm-3">
                    <!--<div class="custom-control custom-checkbox col-sm-6">
                        <input type="checkbox" class="custom-control-input" [(ngModel)]="chDraft" id="customCheck1">
                        <label class="custom-control-label" for="customCheck1">Draft</label>
                    </div> -->
                    <div class="form-group col-sm-12">
                            <select [(ngModel)]="chDraft" class="custom-select custom-select-sm">
                                    <option value="A">เลือกประเภทสถานะ</option>
                                <option value="0">รายการเสร็จสิ้น</option>
                                <option value="1">Draft รายการนี้ไว้</option>
                            </select>
                        </div>
                </div>



                <div class="collapse" id="collapseExample">
                    <div class="card card-body">
                        <form #imageForm=ngForm style="text-align: center;">
                            <img [src]="imageUrl" style="width:70%;">
                        </form>
                    </div>
                </div>


            </div>
            <div class="modal-footer">
                <div class="alert alert-success" style="width :200px; top: 7px;" role="alert">
                    รายที่เลือกทั้งหมด : {{ total }}
                </div>

                <button type="button" class="btn btn-secondary" (click)="clear()" data-dismiss="modal">Close</button>
                <button class="btn btn-primary" data-toggle="collapse" href="#collapseExample" role="button" aria-expanded="false" [disabled]="selectedFile==''"
                    aria-controls="collapseExample">
                    ViewImage
                </button>
                <button type="button" [disabled]="( total==0 || chDraft =='A' || Filelist ==undefined  && chDraft =='0' && selectedFile =='' )" data-dismiss="modal" class="btn btn-primary" (click)="onUpload(chDraft,total)">Upload</button>

            </div>
        </div>
    </div>
</div>

<!--<div class="modal fade bd-example-modal-lg" id="ddd" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg" role="document">
      <div class="modal-content">
          <div class="modal-header">
              <h5 class="modal-title" id="exampleModalLabel">Upload Payment Info</h5>
              <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                  <span aria-hidden="true">&times;</span>
              </button>
          </div>
          <div class="modal-body">

                  <div>
                          <table class="table table-hover table-bordered table-sm">
                              <thead>
                                  <tr class="text-sm-center bg-light">
                                      <th class="text-sm-center font-weight-normal" scope="col">Item</th>
                                      <th class="text-sm-center font-weight-normal" scope="col">SO No</th>
                                      <th class="text-sm-center font-weight-normal" scope="col">Invoice No</th>
                                      <th class="text-sm-center font-weight-normal" scope="col">Invoice Date</th>
                                      <th class="text-sm-center font-weight-normal" scope="col">Due Date</th>
                                      <th class="text-sm-center font-weight-normal" scope="col">มูลค่าสินค้า</th>
                                      <th class="text-sm-center font-weight-normal" scope="col">VAT</th>
                                      <th class="text-sm-center font-weight-normal" scope="col">มูลค่ารวม</th>
                                      <th class="font-weight-light" scope="col" class="text-sm-center" width="30px">
                                          <input type="checkbox">
                                      </th>
                                  </tr>
                              </thead>
                              <tbody>
                                  <tr>
                                      <th class="text-sm-center font-weight-light">1</th>
                                      <th class="text-sm-center font-weight-light">N6107077</th>
                                      <td class="text-sm-center font-weight-light">NV17-00052</td>
                                      <td class="text-sm-center font-weight-light">01/05/61</td>
                                      <td class="text-sm-center font-weight-light">01/06/61</td>
                                      <td class="text-sm-right font-weight-light">90,065.00</td>
                                      <th class="text-sm-center font-weight-light">0.00</th>
                                      <th class="text-sm-center font-weight-light">90,065.00</th>
                                      <td class="text-center">
                                          <input type="checkbox">
                                      </td>
                                  </tr>
                                  <tr>
                                      <th class="text-sm-center font-weight-light">2</th>
                                      <th class="text-sm-center font-weight-light">N6107078</th>
                                      <td class="text-sm-center font-weight-light">NV17-00053</td>
                                      <td class="text-sm-center font-weight-light">01/05/61</td>
                                      <td class="text-sm-center font-weight-light">01/06/61</td>
                                      <td class="text-sm-right font-weight-light">55,065.00</td>
                                      <th class="text-sm-center font-weight-light">0.00</th>
                                      <th class="text-sm-center font-weight-light">55,065.00</th>
                                      <td class="text-center">
                                          <input type="checkbox">
                                      </td>
                                  </tr>
                                  <tr>
                                      <th class="text-sm-center font-weight-light">3</th>
                                      <th class="text-sm-center font-weight-light">N6107079</th>
                                      <td class="text-sm-center font-weight-light">NV17-00054</td>
                                      <td class="text-sm-center font-weight-light">01/05/61</td>
                                      <td class="text-sm-center font-weight-light">01/06/61</td>
                                      <td class="text-sm-right font-weight-light">15,000.00</td>
                                      <th class="text-sm-center font-weight-light">0.00</th>
                                      <th class="text-sm-center font-weight-light">15,000.00</th>
                                      <td class="text-center">
                                          <input type="checkbox">
                                      </td>
                                  </tr>
                              </tbody>
                          </table>
                      </div>

              <div class="input-group">
                  <div class="custom-file">
                      <input type="file" class="custom-file-input" id="inputGroupFile04">
                      <label class="custom-file-label" for="inputGroupFile04">Choose file</label>
                  </div>                       
              </div>
          </div>


          <div class="modal-footer">
              <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
              <button type="button" class="btn btn-primary">Upload</button>
          </div>
      </div>
  </div>
</div>-->

<div id="mdlSample" class="modal fade" role="dialog" [ngStyle]="{'display': mdlSampleIsOpen ? 'block' : 'none', 'opacity': 1}">
    <div class="modal-dialog modal-md">
        <div class="modal-content">
            <div class="modal-header colhaederal">
                <h4 class="modal-title">Report</h4>
            </div>
            <div class="modal-body">{{alt}} {{load}} </div>
            <div class="modal-footer" align="right">
                <button type="button" id="btnClose" class="btn btn-danger" disabled (click)="closemodel(false)"><i class="fa fa-times fa-fw"></i>
                    ปิด</button></div>
        </div>
    </div>
</div>

<div id="mdlSample" class="modal fade" role="dialog" [ngStyle]="{'display': mdlSampleIsOpen2 ? 'block' : 'none', 'opacity': 1}">
    <div class="modal-dialog modal-md">
        <div class="modal-content">
            <div class="modal-header colhaederal">
                <h4 class="modal-title">Report</h4>
            </div>
            <div class="modal-body">{{alt2}} {{load}} </div>
            <div class="modal-footer" align="right">
                <button type="button" id="btnClose" class="btn btn-danger" (click)="closemodel(false)"><i class="fa fa-times fa-fw"></i>
                    ปิด</button></div>
        </div>
    </div>
</div>
<div id="mdlSample" class="modal fade" role="dialog" [ngStyle]="{'display': mdlSampleIsOpensuccess ? 'block' : 'none', 'opacity': 1}">
    <div class="modal-dialog modal-md">
        <div class="modal-content">
            <div class="modal-header colhaederal">
                <h4 class="modal-title">Report</h4>
            </div>
            <div class="modal-body">{{altsuccess}} {{load}} </div>
            <div class="modal-footer" align="right">
                <button type="button" id="btnClose" class="btn btn-danger" (click)="closemodelsuccess(false)"><i class="fa fa-times fa-fw"></i>
                    ปิด</button></div>
        </div>
    </div>
</div>

<div id="mdlSample" class="modal fade" role="dialog" [ngStyle]="{'display': mdlSampleIsOpenIMG ? 'block' : 'none', 'opacity': 1}">
    <div class="modal-dialog modal-lg" role="document" style="top :50%;">
        <div class="modal-content " style="top: 30px;">
            <div class="modal-body" style="padding: 5px;">
                <ngb-tabset>
                    <ngb-tab>
                        <ng-template ngbTabTitle target="_blank"><b>Images Billno</b></ng-template>
                        <ng-template ngbTabContent style="overflow-y: auto;">
                            <img src="{{ImageBillno}}" style=" margin: 5px; width: 99%; height: 99%;" />
                        </ng-template>
                    </ngb-tab>

                </ngb-tabset>
            </div>
            <div class="modal-footer" style="padding: 5px;" align="right">
                <button type="button" id="btnClose" class="btn btn-danger" (click)="closemodelIMG(false)"> ปิด</button>
            </div>
        </div>
    </div>
</div>


<ng-template #Modalview>
    <div class="modal-body" style="padding: 5px;">{{altBill}}
        <div class="card card-body" style="padding : 5px;">
            <ngb-tabset>
                <ngb-tab>
                    <ng-template ngbTabTitle target="_blank"><b>Images Billno</b></ng-template>
                    <ng-template ngbTabContent>
                        <img src="{{ImageBillno}}" style=" margin: 5px; width: 99%; height: 99%;" />
                    </ng-template>
                </ngb-tab>

            </ngb-tabset>
            <!-- <button type="button" (click)="ModalremarkRef.hide()" class="btn btn-secondary">Close</button>-->
        </div>
    </div>
    <div class="modal-footer" style="padding: 5px;" align="right">
        <button type="button" id="btnClose" class="btn btn-danger" (click)="modalRefshow.hide()"> ปิด</button>
    </div>
</ng-template>