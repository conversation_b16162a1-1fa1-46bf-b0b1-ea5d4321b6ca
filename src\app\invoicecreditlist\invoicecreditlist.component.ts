import { WebapiService } from './../webapi.service';
import { Component, OnInit, ViewChild, TemplateRef } from '@angular/core';
import { HttpClient, HttpEventType } from '@angular/common/http';
import { NgbDateStruct, NgbCalendar } from '@ng-bootstrap/ng-bootstrap';
import { Ng2ImgMaxService } from 'ng2-img-max';
import { Observable } from 'rxjs';
import { debounceTime, map } from 'rxjs/operators';
import { Router, ActivatedRoute } from '@angular/router';
import { BsModalService } from 'ngx-bootstrap/modal';
import { BsModalRef } from 'ngx-bootstrap/modal/bs-modal-ref.service';
import { join } from 'path';
export interface idinvoice {
  id: string;
  salesid: string;
  invoiceid: string;
  invoicedate: string;
  duedate: string;
  salesbalance: number;
  sumtax: number;
  invoiceamount: number;
  billno: string;
  imgurl: string;
  check: boolean;
  typeCK: string;
}
export interface idcall {
  id: string;
  billno: string;
  Productprice: Number;
  Sumvat: Number;
  Sumprice: Number;
}
export interface namebillimg {
  billnonameimg: string;
}
@Component({
  selector: 'app-invoicecreditlist',
  templateUrl: './invoicecreditlist.component.html',
  styleUrls: ['./invoicecreditlist.component.css']
})
export class InvoicecreditlistComponent implements OnInit {
  modalRefshow: BsModalRef;

  config = {
    ignoreBackdropClick: true,
    class: 'modal-md'
  };
  configview = {
    ignoreBackdropClick: true,
    class: 'modal-lg '
  };
  load: string = " สถานะ :  Upload : 0 %";
  max = 2000;
  showbillno = '';
  url: string;
  idinvi: idinvoice[] = [];
  allid: idcall[] = [];
  billnoname: namebillimg[] = [];
  billnoDataSearch: any[];
  invoicecredit: any[];
  fromdate = '';
  todate = '';
  salegroup = '';
  billno = '';
  Customer: any;
  fromDate: NgbDateStruct;
  toDate: NgbDateStruct;
  setInvoice = '';
  selectedFile = null;
  imgInvoice = '';
  textload = '';
  imageUrl: string = "assets/img/default-image.png";
  billnoINV: any[] = [{
    id: '',
    billnoINVnum: ''
  }];
  selectedAll: any;
  checkedall = false;
  data1: any;
  data2: any;
  mdlSampleIsOpen: boolean = false;
  alt = '';
  checkreload = true;

  mdlSampleIsOpen2: boolean = false;
  alt2 = '';
  checkreload2 = true;

  mdlSampleIsOpensuccess: boolean = false;
  altsuccess = '';
  checkreloadsuccess = true;

  setInterval: any;
  total: Number = 0;
  selectAll2: boolean = false;
  testcheck: boolean;

  productprice = 0;
  sumvat = 0;
  sumprice = 0;



  trueproductprice = 0;
  truesumvat = 0;
  truesumprice = 0;
  dateshipping = '';
  dateshippingto = '';

  datalogin: any[] = [];
  groupsale: string;

  testclose = false;
  DateGroupsaleman: any;

  toDateimg = new Date();


  bsValueform: Date;
  bsValueto: Date;
  Datatodate: any;
  Datafromdate: any;
  DataED = '';


  closeNoED: boolean = false;
  closeED: boolean = false;
  mdlSampleIsOpenIMG: boolean = false;
  altimg = '';
  ImageBillno = '';
  urlimg = '';


  customers = [];


  paCodeSale;
  paCustomer;
  txtcustomer;
  Datatoback;
  patxtcustomer;
  Filelist: any;

  chDraft = "0";

  listInv ='';
  constructor(private modalService: BsModalService, private router: Router, private ng2ImgMax: Ng2ImgMaxService, private http: HttpClient, private service: WebapiService, private calendar: NgbCalendar, private route: ActivatedRoute) {
    localStorage.removeItem('DataSOderreview');
    localStorage.removeItem('DataSOderlist');


    this.url = service.geturlservice();
    this.urlimg = service.geturlserviceIMG();
    this.fromDate = calendar.getPrev(calendar.getToday(), 'd', 365);
    this.toDate = calendar.getToday();
    this.datalogin = JSON.parse(sessionStorage.getItem('login'))
    if (this.datalogin == null) {
      this.router.navigate(['login']);
    }
    // this.groupsale=localStorage.getItem('salegroup');
    this.getuser();
    this.getgroupsaleman();
    // this.Datatodate= new Date(this.toDate.year,this.toDate.month-1,this.toDate.day);
    console.log(sessionStorage.getItem('cecreditDateTo'))
    if (sessionStorage.getItem('cecreditDateTo') != null && sessionStorage.getItem('cecreditDateFrom') != null) {
      this.Datatodate = new Date(sessionStorage.getItem('cecreditDateTo'));
      this.Datafromdate = new Date(sessionStorage.getItem('cecreditDateFrom'));

    } else {
      this.fromDate = calendar.getPrev(calendar.getToday(), 'd', 365);
      this.toDate = calendar.getToday();
      this.Datatodate = new Date(this.toDate.year, this.toDate.month - 1, this.toDate.day);
      this.Datafromdate = new Date(this.toDate.year, this.toDate.month - 1, 1);
    }




    //  this.Datafromdate= new Date(this.fromDate.year, this.fromDate.month-1, 1);
    // this.Datafromdate= new Date(this.toDate.year, this.toDate.month-1, 1);
    if (this.route.snapshot.queryParams.todate == undefined) {
      this.getbackto();
    } else {
      this.getdatetotoback();
    }
    if (this.route.snapshot.queryParams.todate == undefined) {
      this.getbackto();
    } else {
      this.getdatetotoback();
    }
    if (this.route.snapshot.queryParams.fromdate == undefined) {
      this.getbackfromdate();
    } else {
      this.getdatefromdatetoback();
    }



    if (this.route.snapshot.queryParams.pacodesale == undefined) {

    } else {
      this.salegroup = this.route.snapshot.queryParams.pacodesale;
    }
    if (this.route.snapshot.queryParams.patxtcustomer == undefined) {

      this.txtcustomer = 'ค้นหาลูกค้า'
    } else {
      if (this.route.snapshot.queryParams.patxtcustomer == '%20') {
        this.txtcustomer = 'ค้นหาลูกค้า'
      } else {
        this.txtcustomer = this.route.snapshot.queryParams.patxtcustomer;
        this.paCustomer = this.route.snapshot.queryParams.pacustomer;
      }
    }
    if (this.route.snapshot.queryParams.pabill == undefined) {

    } else {
      this.billno = this.route.snapshot.queryParams.pabill;
    }
    //fromdate=2018-2-1&todate=2019-2-1&pacodesale=1&pacustomer=%2520&pabill=&datatoback=bill
    if (this.route.snapshot.queryParams.datatoback == undefined) {

    } else {
      this.getDatatoback();
    }

  }
  getDatatoback() {
    this.route.snapshot.queryParams.datatoback
    this.invoicecredit = [];
    this.sumprice = 0.00;
    this.sumvat = 0.00;
    this.productprice = 0.00;
    this.DataED = '0';
    this.sumprice = 0.00;
    this.sumvat = 0.00;
    this.productprice = 0.00;
    this.Datatoback = this.route.snapshot.queryParams.datatoback;
    this.http.get<any>(this.route.snapshot.queryParams.datatoback).subscribe(res => {
      if (res.length > 0) {
        this.invoicecredit = res;

        this.sumprice = 0.00;
        this.sumvat = 0.00;
        this.productprice = 0.00;
        this.sum();
        /*this.getdate();*/
      } else {
        this.sumprice = 0.00;
        this.sumvat = 0.00;
        this.productprice = 0.00;
        alert('ไม่พบข้อมูล');
      }

      /*   this.getdate();*/

    }, error => {
      alert('เกิดปัญหาในการ Process ข้อมูล กรุณารายการใหม่อีกครั้ง');
    });
  }


  getdatefromdatetoback() {
    this.Datafromdate = new Date(this.route.snapshot.queryParams.fromdate)
  }
  getdatetotoback() {
    this.Datatodate = new Date(this.route.snapshot.queryParams.todate)
    this.Datafromdate = new Date(this.route.snapshot.queryParams.fromdate)
  }
  getbackto() {
    this.todate = `${this.Datatodate.getFullYear()}-${this.Datatodate.getMonth() + 1}-${this.Datatodate.getDate()}`;
  }
  getbackfromdate() {
    this.fromdate = `${this.Datafromdate.getFullYear()}-${this.Datafromdate.getMonth() + 1}-${this.Datafromdate.getDate()}`;
  }


  getColor(country: Date, sum, summax, summin) {

    if (summax == summin && sum < 0) {
      return '#000';
    }
    var day = new Date(country)
    const datetest: any = { day: 1, month: day.getMonth() + 1, year: day.getFullYear() };
    let today = Number(new Date())
    let day1 = this.calendar.getNext(datetest, 'm', 1)
    let day2 = this.calendar.getPrev(day1, 'd', 1)
    let dateNumer2 = Number(new Date(day2.year, day2.month - 1, day2.day))
    let daynumbersum = today - dateNumer2
    // var invdate60 = (dateNumer+ + 5184000000 ) 
    //86400000 1day
    //********** 70วัน
    //********** 90วัน
    /*alert(today);
    //2592000000 30วัน
    // 5184000000  60วัน
    alert(invdate);     return 'green';*/
    //alert(invdate  +'/'+ dateNum+'/'+invdate60)
    if (daynumbersum > **********) {
      return 'red';
    } else if (daynumbersum <= ********** && daynumbersum >= **********) {
      return '#0e0dde';
    } else {
      return 'green';
    }

  }

  getColorminBG(CK) {
    if (CK == 1) {
      return '#969595';
    } else {
      return '';
    }
  }



  getColorinv(country, sum) {

    if (sum < 0) {
      return '#000';
    }
    var day = new Date(country)
    const datetest: any = { day: 1, month: day.getMonth() + 1, year: day.getFullYear() };
    let today = Number(new Date())
    let day1 = this.calendar.getNext(datetest, 'm', 1)
    let day2 = this.calendar.getPrev(day1, 'd', 1)
    let dateNumer2 = Number(new Date(day2.year, day2.month - 1, day2.day))
    let daynumbersum = today - dateNumer2
    // var invdate60 = (dateNumer+ + 5184000000 ) 
    //86400000 1day
    /*alert(today);
    //2592000000 30วัน
    // 5184000000  60วัน
    alert(invdate);     return 'green';*/
    //alert(invdate  +'/'+ dateNum+'/'+invdate60)
    if (daynumbersum > **********) {
      return 'red';
    } else if (daynumbersum <= ********** && daynumbersum >= **********) {
      return '#0e0dde';
    } else {
      return 'green';
    }
  }
  /////
  search = (text$: Observable<any>) =>

    //Autocomplete ลูกค้า
    text$.pipe(
      debounceTime(200),
      map(term => term === '' ? []
        : this.customers.filter(v => v.name.toLowerCase().indexOf(term.toLowerCase()) > -1 || v.accountnum.toLowerCase().indexOf(term.toLowerCase()) > -1).slice(0, 50))
    );
  formatter = (x: { name: string, accountnum: string }) => x.name + ' (' + x.accountnum + ')';
  //////


  //ดึงรหัสลูกค้ามาใช้ใน Autocomplete
  getcostomerauto() {
    var idsale = this.datalogin[0].salegroup;
    if (this.datalogin[0].id_group_user == '153Admin' || this.datalogin[0].id_group_user == '1860Administrator') {
      idsale = '%20';
    } else {
      idsale = this.datalogin[0].salegroup;
    }

    this.http.get<any>(this.url + 'customerauto/' + idsale).subscribe(res => {
      this.customers = res;

    })
  }


  getgroupsaleman() {
    this.DateGroupsaleman = [];
    this.http.get<any>(this.url + 'salesman').subscribe(res => {
      if (res.length > 0) {
        this.DateGroupsaleman = res;
        this.getcostomerauto();
      } else {
        alert('ไม่สามารถติดต่อข้อมูลรหัส saleman ได้')
      }
    });
  }

  getuser() {
    if (this.datalogin[0].id_group_user == '153Admin' || this.datalogin[0].id_group_user == '1860Administrator') {
      this.salegroup = '';
      this.testclose = true;
    } else {
      this.testclose = false;
      this.salegroup = this.datalogin[0].salegroup;
    }
  }

  ngOnInit() {

  }
  getRandomInt(max) {
    return Math.floor(Math.random() * Math.floor(max));
  }
  openModal(open: boolean, text: string, load: boolean): void {
    this.mdlSampleIsOpen = open;
    this.alt = text;
    this.checkreload = load;
  }
  openModal2(open: boolean, text: string, load: boolean): void {
    this.mdlSampleIsOpen2 = open;
    this.alt2 = text;
    this.checkreload2 = load;
  }

  closemodel(cl: boolean) {
    this.mdlSampleIsOpen2 = cl;
    if (this.checkreload2 == false) {

    }
  }

  openModalsuccess(open: boolean, text: string, load: boolean): void {
    this.mdlSampleIsOpensuccess = open;
    this.altsuccess = text;
    this.checkreloadsuccess = load;
  }
  closemodelsuccess(cl: boolean) {
    this.mdlSampleIsOpensuccess = cl;
    if (this.checkreloadsuccess == false) {
      if (this.closeED == true) {
        this.SearchinvoicecreditED();
      } else {
        this.Searchinvoicecredit();
      }

    }
  }

  selectAll(checked) {
    this.truesumprice = 0.00;
    this.truesumvat = 0.00;
    this.trueproductprice = 0.00;
    for (var i = 0; i < this.idinvi.length; i++) {
      this.idinvi[i].check = checked;
    } this.Getbillbo();
    this.total = this.allid.length
    this.sumTrue();
  }

  checkIfAllSelected(checked, index) {
    this.idinvi[index].check = checked;
    this.total = 0;
    this.Getbillbo();
    this.total = this.allid.length
    this.getcaheck();
    this.sumTrue();
  }


  sum() {
    if (this.invoicecredit.length > 0) {
      for (var i = 0; i < this.invoicecredit.length; i++) {
        this.productprice += this.invoicecredit[i].salesbalance;
        this.sumvat += this.invoicecredit[i].sumtax;
        this.sumprice += this.invoicecredit[i].invoiceamount;
      }
    }
  }

  sumTrue() {
    this.truesumprice = 0.00;
    this.truesumvat = 0.00;
    this.trueproductprice = 0.00;
    for (var i = 0; i < this.idinvi.length; i++) {
      if (this.idinvi[i].check == true) {
        this.trueproductprice += this.idinvi[i].salesbalance;
        this.truesumvat += this.idinvi[i].sumtax;
        this.truesumprice += this.idinvi[i].invoiceamount;
      }

    }
  }


  getdate() {
    this.fromdate = `${this.Datafromdate.getFullYear()}-${this.Datafromdate.getMonth() + 1}-${this.Datafromdate.getDate()}`;
    this.todate = `${this.Datatodate.getFullYear()}-${this.Datatodate.getMonth() + 1}-${this.Datatodate.getDate()}`;
  }

  getcaheck() {

    var ch = 0;
    for (var i = 0; i < this.idinvi.length; i++) {
      if (this.idinvi[i].check == true) {
        ch++;
      } else {
        ch--;
      }
    }
    if (this.idinvi.length == ch) {
      this.testcheck = true;
    } else {
      this.testcheck = false;
    }

  }

  GetBillNo(item) {
    this.Filelist = undefined;
    this.chDraft = '0';
    this.getdate();
    this.selectedFile = '';
    this.testcheck = false;
    const billno = item.billno;
    const idcus = item.orderaccount;

    if (this.fromdate == '') {
      this.fromdate = `${this.fromDate}`;
    }
    if (this.todate == '') {
      this.todate = `${this.toDate}`;
    }
    this.textload = '';
    this.total = 0;
    this.truesumprice = 0.00;
    this.truesumvat = 0.00;
    this.trueproductprice = 0.00;
    this.imageUrl = "assets/img/default-image.png";
    this.idinvi = [];
    this.allid = [];
    this.http.get<any>(this.url + 'get_billno/' + billno + '/' + idcus + '/' + this.fromdate + '/' + this.todate + '/' + this.DataED).subscribe(res => {
      if (res.length > 0) {
        this.billnoDataSearch = res;
        for (var i = 0; i < res.length; i++) {
          this.idinvi.push({
            id: res[i].id,
            salesid: res[i].salesid,
            invoiceid: res[i].invoiceid,
            invoicedate: res[i].invoicedate,
            duedate: res[i].duedate,
            salesbalance: res[i].salesbalance,
            sumtax: res[i].sumtax,
            invoiceamount: res[i].invoiceamount,
            billno: res[i].billno,
            imgurl: res[i].attachedfile,
            check: false,
            typeCK: res[i].typeCK
          });
        }
        this.showbillno = this.idinvi[0].billno

      } else {
        alert('ไม่พบข้อมูลที่ค้นหา');
        this.idinvi = [];
      }
    })

  }


  handleFileInput(file: FileList) {
    this.load = '';
    this.selectedFile = '';
    if (file.item(0).type == "image/jpeg") {
      //this.openModal(true,'กำลังปรับขนาดไฟล์',false)&& file.item(0).size <= (1024*1024*5)
      let image = file.item(0);

      this.selectedFile = image;
      this.textload = this.selectedFile.name;
      var reader = new FileReader();
      reader.onload = (event: any) => {
        this.imageUrl = event.target.result;
      }
      reader.readAsDataURL(this.selectedFile);

      /* this.ng2ImgMax.resizeImage(image, 1024, 768).subscribe(
         result => {
           this.selectedFile = image;
           this.textload= this.selectedFile.name;
           //Show image preview
           var reader = new FileReader();
           reader.onload = (event:any) => {
             this.imageUrl = event.target.result;
           }
           reader.readAsDataURL(this.selectedFile);
           this.openModal(false,'',false)
         },
         error => {
           this.openModal2(true,error,false)
         }/
       );*
     
       
         /*  this.selectedFile = file.item(0);*/

    } else {
      this.load = '';
      this.openModal2(true, 'ชนิดไฟล์รูปภาพไม่ถูกต้อง กรุณาเลือกชนิดไฟล์เป็น .jpeg ', false)
      this.imageUrl = "assets/img/default-image.png";
    }

    /*  this.selectedFile = file.item(0);
      this.textload= this.selectedFile.name;
      //Show image preview
      var reader = new FileReader();
      reader.onload = (event:any) => {
        this.imageUrl = event.target.result;
      }
      reader.readAsDataURL(this.selectedFile);*/
  }


  /* if (file.item(0).type =="image/jpeg" && file.item(0).size <= (1024*1024*5) ) {
     // this.openModal2(true,'กำลังปรับขนาดไฟล์',false)
      let image = file.item(0);
      this.selectedFile = image;
      this.textload= this.selectedFile.name;
      var reader = new FileReader();
          reader.onload = (event:any) => {
            this.imageUrl = event.target.result;
          }
          reader.readAsDataURL(this.selectedFile);
     
    } else {
      this.load='';
      alert('ชนิดไฟล์รูปภาพไม่ถูกต้อง กรุณาเลือกชนิดไฟล์เป็น .jpeg และ ไฟล์ขนาดไม่เกิน 5120 kb');
      //this.openModal(true,'ชนิดไฟล์รูปภาพไม่ถูกต้อง กรุณาเลือกชนิดไฟล์เป็น .jpeg และ ไฟล์ขนาดไม่เกิน 5120 kb',false)    
        }
       this.imageUrl =  "assets/img/default-image.png";
       this.textload= "";
       return false;
      }*/

  SearchinvoicecreditED() {
    this.txtcustomer = 'ค้นหาลูกค้า'
    this.closeED = true;
    this.DataED = '1';
    this.getdate();
    this.invoicecredit = [];
    this.sumprice = 0.00;
    this.sumvat = 0.00;
    this.productprice = 0.00;
    this.load = '';
    var datacodeSo = '';
    var dataCustomer = '';
    if (this.billno == '' && this.Customer == undefined && this.salegroup == '') {
      this.openModal2(true, 'กรุณาป้อนข้อมูลอย่างน้อย 1 ช่อง ถ้าไม่ระบุวันที่ วันที่จะย้อนหลังเป็นเวลา 365 วัน', false)

    } else {
      if (this.fromdate == '') {
        this.fromdate = `${this.fromdate}`;
        sessionStorage.setItem('cecreditDateFrom', this.fromdate);
      } else {
        sessionStorage.setItem('cecreditDateFrom', this.fromdate);
      }
      if (this.todate == '') {
        this.todate = `${this.toDate}`;
        sessionStorage.setItem('cecreditDateTo', this.todate);
      } else {
        sessionStorage.setItem('cecreditDateTo', this.todate);

      }



      if (this.billno == '') {
        this.billno = '%20';
      }
      if (this.Customer == undefined) {
        dataCustomer = '%20';
      } else {
        dataCustomer = `${this.Customer.accountnum}`
      }
      //  alert(this.salegroup +'//'+this.datalogin[0].salegroup)

      if (this.salegroup == '') {
        if (this.datalogin[0].id_group_user == '153Admin' || this.datalogin[0].id_group_user == '1860Administrator') {
          datacodeSo = `${this.salegroup}`;
        } else {
          datacodeSo = `${this.datalogin[0].salegroup}`;
        }
      }

      if (this.salegroup !== '') {
        if (this.datalogin[0].id_group_user == '153Admin' || this.datalogin[0].id_group_user == '1860Administrator') {
          datacodeSo = `${this.salegroup}`;
        } else {
          datacodeSo = `${this.datalogin[0].salegroup}`;
        }
      }

      if (datacodeSo == '') {
        datacodeSo = '%20';
      }

      if (dataCustomer == 'undefined') {
        dataCustomer = '%20';
      }

      /* alert(this.url + 'credit_invoice/'+ this.fromdate + '/' + this.todate +'/' + this.salegroup + '/' + this.billno + '/' + this.customer);*/
      this.http.get<any>(this.url + 'credit_invoice/' + this.fromdate + '/' + this.todate + '/' + datacodeSo + '/' + this.billno + '/' + dataCustomer + '/' + this.DataED).subscribe(res => {
        if (res.length > 0) {
          this.invoicecredit = res;
          this.sumprice = 0.00;
          this.sumvat = 0.00;
          this.productprice = 0.00;
          this.sum();
          if (this.datalogin[0].id_group_user == '153Admin' || this.datalogin[0].id_group_user == '1860Administrator') {
            /* this.salegroup='';*/
          }

          if (datacodeSo == '%20') {
            datacodeSo = '';
            this.salegroup = '';
          }
          if (dataCustomer == '%20') {
            dataCustomer = '';
          }
          if (this.billno == '%20') {
            this.billno = '';
          }

        } else {
          if (this.datalogin[0].id_group_user == '153Admin' || this.datalogin[0].id_group_user == '1860Administrator') {
            /* this.salegroup='';*/
          }
          if (datacodeSo == '%20') {
            datacodeSo = '';
            this.salegroup = '';
          }
          if (dataCustomer == '%20') {
            dataCustomer = '';
          }
          if (this.billno == '%20') {
            this.billno = '';
          }
          this.openModal2(true, 'ไม่พบข้อมูล', false)
        }
      }, error => {
        this.openModal2(true, 'เกิดปัญหาในการ Process ข้อมูล กรุณารายการใหม่อีกครั้ง', false);
      });
    }
  }

  Searchinvoicecredit() {
    this.txtcustomer = 'ค้นหาลูกค้า'
    this.closeED = false;
    this.DataED = '0';
    this.getdate();
    this.invoicecredit = [];
    this.sumprice = 0.00;
    this.sumvat = 0.00;
    this.productprice = 0.00;
    this.load = '';
    var datacodeSo = '';
    var dataCustomer = '';
    if (this.billno == '' && this.Customer == undefined && this.salegroup == '') {
      this.openModal2(true, 'กรุณาป้อนข้อมูลอย่างน้อย 1 ช่อง ถ้าไม่ระบุวันที่ วันที่จะย้อนหลังเป็นเวลา 365 วัน', false)

    } else {
      if (this.fromdate == '') {
        this.fromdate = `${this.fromdate}`;
        sessionStorage.setItem('cecreditDateFrom', this.fromdate);
      } else {
        sessionStorage.setItem('cecreditDateFrom', this.fromdate);
      }
      if (this.todate == '') {
        this.todate = `${this.toDate}`;
        sessionStorage.setItem('cecreditDateTo', this.todate);
      } else {
        sessionStorage.setItem('cecreditDateTo', this.todate);

      }


      if (this.billno == '') {
        this.billno = '%20';
      }
      if (this.Customer == undefined) {
        if (this.route.snapshot.queryParams.pacustomer == undefined) {
          dataCustomer = '%20';
          this.paCustomer = '%20';
          this.patxtcustomer = '%20';
        } else {
          dataCustomer = this.route.snapshot.queryParams.pacustomer;
          this.paCustomer = this.route.snapshot.queryParams.pacustomer;

        }

      } else {
        dataCustomer = `${this.Customer.accountnum}`;
        this.paCustomer = `${this.Customer.accountnum}`;
        this.patxtcustomer = `${this.Customer.name}`;
      }

      if (this.salegroup == '') {
        if (this.datalogin[0].id_group_user == '153Admin' || this.datalogin[0].id_group_user == '1860Administrator') {
          datacodeSo = `${this.salegroup}`;
          this.paCodeSale = `${this.salegroup}`;
        } else {
          datacodeSo = `${this.datalogin[0].salegroup}`;
          this.paCodeSale = `${this.datalogin[0].salegroup}`;
        }
      }

      if (this.salegroup !== '') {
        if (this.datalogin[0].id_group_user == '153Admin' || this.datalogin[0].id_group_user == '1860Administrator') {
          datacodeSo = `${this.salegroup}`;
          this.paCodeSale = `${this.salegroup}`;
        } else {
          datacodeSo = `${this.datalogin[0].salegroup}`;
          this.paCodeSale = `${this.datalogin[0].salegroup}`;
        }
      }

      if (this.paCodeSale == '') {
        this.paCodeSale = '%20';
      }

      if (this.paCustomer == 'undefined') {
        this.paCustomer = '%20';
      }


      /* alert(this.url + 'credit_invoice/'+ this.fromdate + '/' + this.todate +'/' + this.salegroup + '/' + this.billno + '/' + this.customer);*/
      this.Datatoback = this.url + 'credit_invoice/' + this.fromdate + '/' + this.todate + '/' + this.paCodeSale + '/' + this.billno + '/' + this.paCustomer + '/' + this.DataED
      this.http.get<any>(this.url + 'credit_invoice/' + this.fromdate + '/' + this.todate + '/' + this.paCodeSale + '/' + this.billno + '/' + this.paCustomer + '/' + this.DataED).subscribe(res => {
        // alert(JSON.stringify(this.invoicecredit))
        if (res.length > 0) {
          this.invoicecredit = res;

          this.sumprice = 0.00;
          this.sumvat = 0.00;
          this.productprice = 0.00;
          this.sum();
          if (this.datalogin[0].id_group_user == '153Admin' || this.datalogin[0].id_group_user == '1860Administrator') {
            /* this.salegroup='';*/
          }

          if (this.paCodeSale == '%20') {
            this.paCodeSale = '';
            this.salegroup = '';
          }

          dataCustomer = '';

          if (this.billno == '%20') {
            this.billno = '';
          }

        } else {
          if (this.datalogin[0].id_group_user == '153Admin' || this.datalogin[0].id_group_user == '1860Administrator') {
            /* this.salegroup='';*/
          }
          if (this.paCodeSale == '%20') {
            this.paCodeSale = '';
            this.salegroup = '';
          }
          if (this.paCustomer == '%20') {
            this.paCustomer = '';
          }
          if (this.billno == '%20') {
            this.billno = '';
          }
          this.openModal2(true, 'ไม่พบข้อมูล', false)
        }
      }, error => {
        this.openModal2(true, 'เกิดปัญหาในการ Process ข้อมูล กรุณารายการใหม่อีกครั้ง', false);
      });
    }

  }



  clear() {
    this.imageUrl = "assets/img/default-image.png";
    this.textload = "";
    this.selectedFile = "";
    this.setInvoice = "";
  }



  newsaveinvoice(billno, typeCK) {
    //ฟังชั่นใหม่
    // alert(JSON.stringify(this.allid))
    this.http.post(this.url + 'update_invoiceNEW', {
    //  Data: this.allid,
    Data:this.listInv,
      nameimg: billno,
      CK: typeCK
    }).subscribe(res => {
      if (res == true) {
        this.openModal(false, '', false);
        this.openModal2(false, '', false);
        this.load = '';
        this.invoicecredit = [];
        this.openModalsuccess(true, 'บันทึกข้อมูลเสร็จสิ้น', false);
        // this.load ='รายการที่ : '+ this.allid.length.toString() + '-' + res.toString();
        //  alert('โอเคครับ')
        // console.log(this.allid.length + '--------' + res.toString())
      }
    }, error => {
      //เกิดปัญหาในการ Process ข้อมูล
      this.openModalsuccess(true, 'เกิดปัญหาในการ Process ข้อมูล', false);
    });
  }




  saveinvoice(billno) {
    //ฟังชั่นเก่า


    if (this.allid.length != 0) {

      this.http.post(this.url + 'update_invoice/' + this.allid[0].id + '/' + billno, '').subscribe(res => {
        if (res == true) {
          this.deletelallid(0);
          this.load = 'รายการที่ : ' + this.allid.length.toString() + '-' + res.toString();
          console.log(this.allid.length + '--------' + res.toString())
        }
      }, error => {
        this.openModal2(true, 'เกิดปัญหาในการ Process ข้อมูล', false);
      });
    } else {
      if (this.allid.length == 0) {
        clearInterval(this.setInterval);
        this.openModal(false, '', false);
        this.load = '';
        this.invoicecredit = [];
        this.openModalsuccess(true, 'บันทึกข้อมูลเสร็จสิ้น', false);
      }
    }

  }

  Getbillbo() {
    this.allid = [];
    this.listInv ='';
    for (var i = 0; i < this.idinvi.length; i++) {
      if (this.idinvi[i].check == true) {
        this.allid.push({
          id: this.idinvi[i].invoiceid,
          billno: this.idinvi[i].billno,
          Productprice: this.idinvi[i].salesbalance,
          Sumvat: this.idinvi[i].sumtax,
          Sumprice: this.idinvi[i].invoiceamount
        });
        this.listInv = this.listInv +',' +this.idinvi[i].invoiceid
      }

    }
  }

  deletelallid(value) {
    this.allid.splice(value, 1);
  }

  onUpload(typeCK, total) {

    var datatypeCK = 0;
    if (typeCK == "1") {
      datatypeCK = 1
    } else if (typeCK == "0") {
      datatypeCK = 2
    } else {
      alert("เลือกประเภทสถานะก่อนทำรายการ")
      return false;
    }
    if (total < 1) {
      alert("ทำรายการไม่ถูกต้อง กรุณาทำรายการใหม่")
      return false;
    }
    var billname = '';
    this.Getbillbo();
    var Random = this.getRandomInt(this.max);
    billname = `${this.allid[0].billno}-${this.toDate}-${Random}.jpg`;
    this.http.get<any>(this.url + 'get_billnoname/' + billname).subscribe(res => {
      if (res.length < 1) {
        if (this.allid.length > 0) {
          /* const fd = new FormData();
               var urlimg =`${this.url}/${billname}/bill/upload`;
                 fd.append('image', this.selectedFile, this.selectedFile.name)  
                 this.http.post<any>(urlimg,fd).subscribe(res => {
                        if (res.length ==undefined ) {
                         alert('3 = '+JSON.stringify(res.success));
                           alert(billname);*/
          this.openModal(true, 'กำลังบันทึกข้อมูล', false);
          this.load = "สถานะ : Upload : 0 %";
          this.imgupload(billname, datatypeCK);
          /*   }else{
                 alert('Failed to save.');
               } 
              
               });  */
        } else {
          this.openModal(true, 'กรุณาเลือก InvoiceID ก่อน', false)
          this.idinvi = [];
          this.allid = [];
        }
      } else {
        this.openModal(true, 'กรุณาทำรายการใหม่อีกครั้ง', false)
        this.invoicecredit = [];
      }
    }, error => {
      this.openModal2(true, 'เกิดปัญหาในการ Process ข้อมูล', false);
    });
  }




  imgupload(billname, datatypeCK) {
    this.sumprice = 0.00;
    this.sumvat = 0.00;
    this.productprice = 0.00;

    if (this.selectedFile == '') {
      billname = "";
      this.newsaveinvoice(billname, datatypeCK);

    } else {
      const fd = new FormData();
      var urlimg = `${this.url}${billname}/bill/upload`;
      fd.append('image', this.selectedFile, this.selectedFile.name)
      this.http.post<any>(urlimg, fd, {
        reportProgress: true,
        observe: 'events'
      }).subscribe(event => {
        if (event.type === HttpEventType.UploadProgress) {
          console.log('Upload' + Math.round(event.loaded / event.total * 100) + ' %');
          this.load = 'สถานะ : Upload : ' + Math.round(event.loaded / event.total * 100) + ' %';
        } else if (event.type === HttpEventType.Response) {
          console.log(event);
          if (event.body.success === true) {
            //this.setInterval=setInterval(() => this.saveinvoice(billname), 400);     
            this.newsaveinvoice(billname, datatypeCK);
          } else {
            this.openModal(false, '', false);
            this.openModal2(true, 'ไม่สามารถบันทึกข้อมูลได้', false);
          }
        }

      });
    }


  }

  openModalIMG3(open: boolean, text: string, load: boolean, texturl: string): void {
    this.mdlSampleIsOpenIMG = open;
    this.altimg = text;
   // this.setImgBill(texturl)
  }
  closemodelIMG(cl: boolean) {
    this.mdlSampleIsOpenIMG = cl;
    if (this.checkreload == false) {
    }
  }


  openModalIMG(template: TemplateRef<any>, texturl: string, CK) {
    // this.altimg=text;
    this.setImgBill(texturl, CK)
    this.modalRefshow = this.modalService.show(template,
      { class: 'modal-lg' }
    );

  }
  setImgBill(nameimage, CK) {

   // if (CK == '1') {
   //   nameimage = this.imageUrl;
   //   this.ImageBillno = nameimage;
   // } else {
      this.ImageBillno = this.urlimg + nameimage;
   // }

  }


  openpdf(valueid: string) {
    var page = 'bill';
    //this.router.navigate(['/PDFprint', { queryParams: { idINV: valueid }}]);this.fromdate + '/' + this.todate
    this.router.navigate(['/PDFprint'], { queryParams: { idINV: valueid, fromdate: this.fromdate, todate: this.todate, pacodesale: this.paCodeSale, pacustomer: this.paCustomer, pabill: this.billno, Datatoback: this.Datatoback, page: page, patxtcustomer: this.patxtcustomer } });
    /* this.router.navigate(['PDFprint']);*/
  }
  cancel() {

    this.Customer = undefined;

  }


}