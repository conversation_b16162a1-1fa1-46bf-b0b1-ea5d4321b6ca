<!DOCTYPE html>
<html ng-app="loginpage">

<head>
    <meta name="viewport" content="width=device-width, initial-scale=1">   
</head>

<body>
    <div class="container">
            <div class="row">
              <div class="col-md-4 offset-md-4">
                  <img   src="assets/img/LOGO--NANO-(-24-05-2018).png" width="99%" style="margin-top: 10%">
              </div>
            
                    <div class="col-md-4 offset-md-4 form-control" >
                            <form #form="ngForm" role="form" (submit)="onSubmit(form.value)" action="">
                                    <br>
                                    <fieldset ngModelGroup="user_detail">
                                
                                      <div class="form-group">
                                        <label for="first_name">Username</label>        
                                        <input ngModel name="Username" type="text" class="form-control" id="Username" [(ngModel)]="Username"  placeholder="Username" required>
                                      </div>
          
                                      <div class="form-group">
                                        <label for="last_name">Password</label>        
                                        <input ngModel name="Password" type="password" class="form-control" id="Password" [(ngModel)]="Password" placeholder="Password" required>
                                      </div>
          
                                     
                                  
                                          <div class=" form-inline form-check form-group">
                                              <input class="form-control" type="checkbox" (click)="clickcsaleslogin($event.target.checked)" [checked]="salslogin"   name="" id="salescheck">
                                              <label class="form-check-label" for="salescheck">Sales</label>
                                              <input style="margin-left: 3px" class="form-control" type="checkbox" (click)="clickcustomerlogin($event.target.checked)" [checked]="customerlogin"  name="" id="customercheck">
                                              <label class="form-check-label" for="customercheck" >Customer</label>
                                            </div>
                                        <div class="form-group form-check">
                                          <input class="form-check-input" type="checkbox" [checked]="rememberlogin"  (click)="clickremembermelogin($event.target.checked)" name="" value="Remember Me" id="exampleCheck1">
                                          <label class="form-check-label" for="exampleCheck1">Remember me</label>
                                        </div>
                                      
                                      
                                    </fieldset>   
                              
                                   <!-- <label class="text-danger">ALLBAD = {{numberbad}}</label> <br>
                                    <label [ngStyle]="{'color':getColor()}">ALLNOBAD = {{numnobad}}</label>  --> 
                                    <button type="submit" style="margin-bottom: 20px"  class="btn btn-success w-100">Login</button>
                                  
                                </form>
                 </div>
            </div>
    </div>
    
    <div id="mdlSample" class="modal fade" role="dialog" [ngStyle]="{'display': mdlSampleIsOpen ? 'block' : 'none', 'opacity': 1}">
        <div class="modal-dialog modal-md">
        <div class="modal-content">
        <div class="modal-header colhaederal">
        <h4 class="modal-title">Report</h4>
        </div>
        <div class="modal-body">{{alt}}</div>
        <div class="modal-footer" align="right">
                    <button type="button" id="btnClose" class="btn btn-danger" (click)="closemodel(false)"><i class="fa fa-times fa-fw"></i> ปิด</button></div>
        </div>
        </div>
        </div>
    <pre>{{closeResult}}</pre>
 
</body>


</html>


<div *ngIf="isModalShown" [config]="{ show: true ,  ignoreBackdropClick: true }" (onHidden)="onHidden()" bsModal #autoShownModal="bs-modal"
     class="modal fade" tabindex="-1" role="dialog" aria-labelledby="dialog-auto-name">
  <div class="modal-dialog modal-sm">
    <div class="modal-content">
      <div class="modal-body">
        <img src="assets/img/Spinner-1s-200px.gif" style="width:100%;">
       <!-- <img [src]="nameUrl"  style="width:100%;"> -->
      </div>
    </div>
  </div>
</div>

<ng-template #syncdata>
        <div class="modal-body text-center">
            <p *ngIf="!CKbtntrue" [ngStyle]="{'color':getColorHost(CkHost)}">{{CkHost}}</p>
            <P *ngIf="CKbtntrue" [ngStyle]="{'color':getColorHost(CkHost)}" > {{textshow}}</P>
            <button type="button" *ngIf="CksumHost > 14 && !CKbtntrue " [disabled]="CksumHost < 14" class="btn btn-primary" (click)="syncdataHost()"  >syncdata</button>
            <button type="button" *ngIf="CksumHost < 14" class="btn btn-primary" (click)="CKhost()"  >ติดต่อฐานข้อมูลใหม่</button>
            <button type="button" *ngIf="!CKbtntrue" class="btn btn-default" (click)="syncdatamodalRef.hide()"  >ปิด</button>
          </div><!---->
      </ng-template>