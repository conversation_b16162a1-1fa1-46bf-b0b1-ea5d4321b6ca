
import { menulist } from './../entites/menulist.entites';
import { AppComponent } from './../app.component';
import { FormBuilder} from '@angular/forms';
import { HttpClient } from '@angular/common/http';
import { Component, OnInit, ViewChild } from '@angular/core';

import { Router } from '../../../node_modules/@angular/router';

import { WebapiService } from '../webapi.service';
import { login } from '../entites/login.entites';
import { CookieService } from 'ngx-cookie-service';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { SoComponent } from '../so/so.component';
 
import { BsModalService } from 'ngx-bootstrap/modal';
import { BsModalRef } from 'ngx-bootstrap/modal/bs-modal-ref.service';

import { ModalDirective } from 'ngx-bootstrap/modal';
export interface pageper{
  pagename:string;
}

/*[routerLink]="['/customerlist']"*/
@Component({
  selector: 'app-login',
  templateUrl: './login.component.html',
  styleUrls: ['./login.component.css']
})

export class LoginComponent implements OnInit {
  mdlSampleIsOpen : boolean = false;
  alt='';
  checkreload=true;
  rememberlogin= false;
  customerlogin=false;
  salslogin=false;
  @ViewChild('autoShownModal') autoShownModal: ModalDirective;
  isModalShown: boolean = false;

  showModal(): void {
    this.isModalShown = true;
  }
 
  hideModal(): void {
    this.autoShownModal.hide();
  }
 
  onHidden(): void {
    this.isModalShown = false;
  }

  CksumHost=0;
  CkHost='';
  
  HostTrue=false;

  uncheckableRadioModel = 'Middle';
  cookieValue = 'UNKNOWN';
  Username='';
  Password='';
testuserlogin: any[];
logindata: login[];
menulistdata: menulist[];
  closeResult: string;
  private loggedin = false;
url: string;
nameurl: string;
loginfail: string;
  validate: string;
  numps: number;
  setdata = {};
  saveuser: any;
  model: AppComponent;
  timerInterval: any;
  numberbad=0;
  numnobad =false;
  numall=0;
  setInterval:any;

  name :string;
  idcustomer:string;
  idusercustomer:string;
  clr:any[]=[];
  permissionloaddata:any[]=[];
  pageweb: pageper[]=[];

  syncdatamodalRef: BsModalRef;
  configviewsync = {
    ignoreBackdropClick: true,
    class : 'modal-sm'
  };
  urlhost='';
  CKbtntrue=false;
  textshow='';

  
constructor(private modalService2: BsModalService,private modalService: NgbModal, private http: HttpClient, private fb: FormBuilder, private trou: Router, private service: WebapiService,private cookie: CookieService) {
  
 

  
  localStorage.removeItem('DataSOderreview');
  localStorage.removeItem('DataSOderlist');
 /* sessionStorage.clear();
  localStorage.clear();*/
  //this.urlhost = service.geturlHost();
  this.Username=this.getCookie('username');
  this.Password=this.getCookie('password');
  
  this.service.getuserlogin(this.clr);
  if(this.getCookie('checkremember')==='true'){
    this.rememberlogin=true;
  }else{
    this.deleteCookie('checkremember');
  }

  if(this.getCookie('CK1')==='S'){
    this.salslogin=true ;
    this.customerlogin=false;
    console.log(1)
  }else if(this.getCookie('CK1')==='C') {
    this.salslogin=false ;
    this.customerlogin=true;
    console.log(2)
  }else{
    this.salslogin=false ;
    this.customerlogin=false;
    console.log(3)
    this.deleteCookie('CK1');
  }



    this.validate = '';
    this.url = this.service.geturlservice();
    this.checkreqserver();
   /* if(this.numnobad <10){*/
     /* var i
      for(i=0;i<=10;i++){
        this.http.get(this.url+'menulist_top1').subscribe(res=>{
            alert('1');
            return;
            },error=>{
              
            })
      }*/
    /*  this.setInterval= setInterval(()=>  this.http.get(this.url+'menulist_top1').subscribe(res=>{
      this.numnobad++;
        },error=>{
          
        }),200);
   }else{
    clearInterval(this.setInterval);
   }*/


    
   }

  /* setv (){
    if(localStorage.getItem('CK')==null || localStorage.getItem('CK')==""){
      localStorage.setItem("CK",null)
    }else{
     var dataCK =  localStorage.getItem('CK');
     console.log(dataCK)
      if(dataCK=="S"){
        this.salslogin==true 
        this.customerlogin==false
  
        console.log(this.salslogin);
       console.log(1)
      }else if(dataCK=="C"){
        this.salslogin==false 
       this.customerlogin==true
       console.log(2)
      }else{
        console.log(3)
        this.salslogin==false 
       this.customerlogin==false
      }
  
    }
   }*/


   setcookielofin(name,value){
    var cookie=name+'='+value+';';
    var date = new Date();
    date.setDate(date.getDate() + 365);
  
     cookie += 'expires=' + date.toString() + ';';
document.cookie=cookie;
   }
    getCookie(cname) {
    var name = cname + "=";
    var ca = document.cookie.split(';');
    for(var i = 0; i < ca.length; i++) {
      var c = ca[i];
      while (c.charAt(0) == ' ') {
        c = c.substring(1);
      }
      if (c.indexOf(name) == 0) {
        return c.substring(name.length, c.length);
      }
    }
    return "";
  }
  deleteCookie(name){
    var cookie = name + '=;';
    cookie += 'expires=' + (new Date()).toString() + ';';

    document.cookie = cookie;
  }
   open() {
    const modalRef = this.modalService.open(SoComponent, { size: 'lg' , windowClass: 'modal-Top100', backdrop: "static" });
    modalRef.componentInstance.name = this.name;
    modalRef.componentInstance.idcustomer = this.idcustomer;
    modalRef.componentInstance.idusercustomer = this.idusercustomer;

  }
  checkreqserver(){
    this.CksumHost=0;
    this.CkHost='.'
     for(var i=0;i<40;i++){
      this.http.get(this.url+'menulist_top1').subscribe(res=>{
        this.numnobad=true;
        this.CksumHost = this.CksumHost +1
        //alert(JSON.stringify(res))
       // console.log(this.CksumHost)
      this.CKNumHost(this.CksumHost);
             },error=>{
              this.numnobad=false;
             });

             if(this.numnobad==true){
               
              continue;
             } else {
             
             }
     }

   }
   CKNumHost(data:Number){
    this.CkHost='';
    this.HostTrue=false;
    if(data > 14){
        this.onHidden();
    }else{

    }
  }
   getColor() { 
      if( this.numnobad==true)
      {
        return 'green';
      }else{
       
        return 'red';
      }
    }

  ngOnInit() {
    this.validate = '';
    this.numps = 0;
    this.loginfail = '';

  }
  setloginst(value: boolean) {
    this.loggedin = value;
  }
  get isLogin() {
    return this.loggedin;
  }

  onSubmit(value) {
   
    
    this.Username=value.user_detail.Username;
    this.Password=value.user_detail.Password;
   if(this.salslogin==true && this.customerlogin==false){
    this.http.post<any>(this.url+'login',{
      id : value.user_detail.Username,
      pw : value.user_detail.Password
    })
    .subscribe((res: login[]) => {
      if (JSON.stringify(res) === '[]') {
        this.openModal(true,'ผู้ใช้งาน หรือ รหัสผ่านไม่ถูกต้อง',false);
        this.trou.navigate(['']);
        
      } else if (res[0].login_user === value.user_detail.Username ) {
      
          this.saveuser = res;
          if(  this.rememberlogin==true){
            this.setcookielofin('username',this.Username);
            this.setcookielofin('password',this.Password);
          }
  
        /* localStorage.setItem('idgroupuser', res[0].id_group_user);
          localStorage.setItem('Name', res[0].name_user); */
          this.logindata=res;
       this.service.getuserlogin(res);
     
       /*this.http.get<any>(this.url + 'find_permission/' + res[0].id_group_user).subscribe((res: menulist[]) => {
      
   
        this.menulistdata=res;
        localStorage.setItem('menulogin',JSON.stringify(res));
      this.service.getmenulogin(res);
    
      
        },error =>{
          console.log(error);
        }); */
        sessionStorage.setItem('login',JSON.stringify(res));
       
        sessionStorage.setItem('salegroup',res[0].salegroup)
        this.loadpermisstion(res[0].id_group_user,res[0].id_group_user);
       
     
       
        
       } else {
        alert(this.loginfail);
        this.trou.navigate(['']);
       }
      },error=>{
        status=error.status;
        this.openModal(true,'เกิดปัญหาในการ Process ข้อมูล กรุณาลองใหม่อีกครั้ง',false);
      })
   } else if(this.salslogin==false && this.customerlogin==true){
     
    this.http.post<any>(this.url+'login_customer',{
      id : value.user_detail.Username,
      pw : value.user_detail.Password
    })
    .subscribe((res: login[]) => {
      if (JSON.stringify(res) === '[]') {
        this.openModal(true,'ผู้ใช้งาน หรือ รหัสผ่านไม่ถูกต้อง',false);
        this.trou.navigate(['']);
        
      } else if (res[0].login_user === value.user_detail.Username ) {
    
          this.saveuser = res;
          if(  this.rememberlogin==true){
            this.setcookielofin('username',this.Username);
            this.setcookielofin('password',this.Password);
          }
  
        /* localStorage.setItem('idgroupuser', res[0].id_group_user);
          localStorage.setItem('Name', res[0].name_user); */
          this.logindata=res;
       this.service.getuserlogin(res);
       localStorage.setItem('salegroup',res[0].salegroup)
       sessionStorage.setItem('login',JSON.stringify(res));
     //alert(JSON.stringify(res));
       /*this.http.get<any>(this.url + 'find_permission/' + res[0].id_group_user).subscribe((res: menulist[]) => {
      
   
        this.menulistdata=res;
        localStorage.setItem('menulogin',JSON.stringify(res));
      this.service.getmenulogin(res);
    
      
        },error =>{
          console.log(error);
        }); */
        if(res[0].checklogin ==0){
 
          this.name=res[0].name_user.toString();
          this.idusercustomer=res[0].id_user.toString();
          this.open();
        }else{

          this.loadpermisstion(res[0].id_group_user,res[0].id_group_user);
      
        }

        
        
       } else {
        alert(this.loginfail);
        this.trou.navigate(['']);
       }
      },error=>{
        status=error.status;
        this.openModal(true,'เกิดปัญหาในการ Process ข้อมูล กรุณาลองใหม่อีกครั้ง',false);
      })
   }
    
    
  
}

loadpermisstion(value,group){
  this.http.post<any>(this.url + 'find_permission',{
    id_user_group : value
  }).subscribe(res => {
    if(res.length>0) {
      this.service.setpromisstiondata(res);
      sessionStorage.setItem('menu',JSON.stringify(res));
      this.permissionloaddata=res;
      for(var i=0;i<this.permissionloaddata.length;i++){
        if(this.permissionloaddata[i].flag_view==true){
          this.pageweb.push({
          pagename:this.permissionloaddata[i].des_menu    
          });
        }
          }
      if((group==='153Admin' && this.pageweb.length==15)|| group!='saleadmin'){
        this.trou.navigate(['sorecord']);
      } else {
        if(this.permissionloaddata.length>0){
       var pagelink='';
      
              if(this.pageweb[0].pagename==='Customer'){
                pagelink='customerlist';
              } else if(this.pageweb[0].pagename==='Product List'){
                pagelink='productlist';
              }else if(this.pageweb[0].pagename==='Price Master'){
                pagelink='pricemaster';
              }else if(this.pageweb[0].pagename==='Sale Order'){
                pagelink='sorecord';
              }else if(this.pageweb[0].pagename==='Sale Order List'){
                pagelink='solist1';
              }else if(this.pageweb[0].pagename==='Sale Order Review'){
                pagelink='soreview';
              }else if(this.pageweb[0].pagename==='Sale Order History'){
                pagelink='sohistory';
              }else if(this.pageweb[0].pagename==='Cash Invoice List'){
                pagelink='invoicecashlist';
              }else if(this.pageweb[0].pagename==='Credit Invoice List'){
                pagelink='invoicecreditlist';
              }else if(this.pageweb[0].pagename==='Complete Invloice List'){
                pagelink='completeinvoice';
              }else if(this.pageweb[0].pagename==='Master Package'){
                pagelink='masterpackage';
              }else if(this.pageweb[0].pagename==='Grouping Product'){
                pagelink='productgroup';
              }else if(this.pageweb[0].pagename==='Cover Page'){
                pagelink='coverpage';
              }else if(this.pageweb[0].pagename==='User Accounts'){
                pagelink='users';
              }else if(this.pageweb[0].pagename==='Permission Setting'){
                pagelink='permission';
              }
              this.trou.navigate([pagelink]);
          } else {
            
          }
      }
     
  
    } 
      },error =>{
        console.log(error);
      })
  
  
}
clickcustomerlogin(value){
this.customerlogin=value;
if(value==true){
this.salslogin=false;

this.setcookielofin('CK1','C');
}
}
clickcsaleslogin(value){
this.salslogin=value;
if(value==true){
this.customerlogin=false;
this.setcookielofin('CK1','S');
}
}
clickremembermelogin(value){
  this.rememberlogin=value;
  this.setcookielofin('checkremember',value);
if(value==false) {
  this.deleteCookie('username');
  this.deleteCookie('password');
// this.cookie.delete('username');
// this.cookie.delete('password');
}
}



openModal(open : boolean,text: string,load:boolean) : void {
  this.mdlSampleIsOpen = open;
  this.alt=text;
  this.checkreload=load;
}
closemodel(cl: boolean) {
this.mdlSampleIsOpen=cl;
if(this.checkreload==true) {
  location.reload();
}

}



OpenCKSync(template) {
    
  this.CKhost()
 
 this.syncdatamodalRef = this.modalService2.show(template, this.configviewsync);
}

CKhost() {
 this.CksumHost=0;
 this.CkHost='.'

 for(var a=0 ; a< 10 ; a++){
   this.CkHost =   this.CkHost +'.'
   if(a< 10){
     for(var i=0;i<20;i++){
       this.http.get(this.urlhost+'menulist_top1').subscribe(res=>{
         this.CksumHost = this.CksumHost +1
         //alert(JSON.stringify(res))
        // console.log(this.CksumHost)
       this.CKNumHost2(this.CksumHost);
        
 
              },error=>{
               this.CKNumHost(this.CksumHost);
              }); 
              if(this.CksumHost > 14){
               return;
             }               
      }
    
   }
 }

 


}


CKNumHost2(data:Number){
 this.CkHost='';
 this.HostTrue=false;
 if(data > 14){
   this.HostTrue=true;
   this.CkHost = 'ติดต่อฐานข้อมูลได้'
 }else{
   this.HostTrue=false;
   this.CkHost = 'ติดต่อฐานข้อมูลไม่ได้'
 }
}

getColorHost(data:string){
 this.HostTrue=false;
 if(data==='ติดต่อฐานข้อมูลได้' || data ==='โปรดรอ'){

   this.HostTrue=true;
   return 'Green'
 }else if (data ==='ติดต่อฐานข้อมูลไม่ได้')  {
   this.HostTrue=false;
   return 'red'
 }else{
   this.HostTrue=false;
   return 'red'
 }
}
syncdataHost(){
  this.CKhost()

  if(this.HostTrue==true){   
    this.textshow='โปรดรอ.'
    this.CKbtntrue=true;
  const Http = new XMLHttpRequest();
  const url='http://localhost:9090/sync_SO/service.asmx/Import';
  Http.open("POST", url);
  Http.send();
  
  Http.onreadystatechange=(e)=>{
  if(Http.readyState==4 && Http.status==200){

    if(confirm('นำเข้าข้อมูล เสร็จสิ้น')){
        alert('นำเข้าข้อมูล เสร็จสิ้น')
        this.syncdatamodalRef.hide();
    } else {
    
    }
   
  }

  }
}
else{
  alert('การเชื่อมต่อถูกตัดขาด')
      this.CkHost = 'การเชื่อมต่อถูกตัดขาด'
}

 }

 }

 
