<app-topmenu></app-topmenu>
<section style="padding-top:60px">
  <div class="container-fluid">
      <h5 class="p-sm-1 bg-secondary text-white text-center">Master Package</h5>
      <form class="needs-validation" novalidate>
          <div class="form-row">
                <div class="col-md-5 mb-2">
                        <ng-template  #rt let-r="result" let-t="term">
                        <label>{{r.itemid}}</label>
                      </ng-template>
                        <input  id="typeahead-template" placeholder="รหัสสินค้า" type="text" class="form-control form-control-sm" [(ngModel)]="packget" name="packget" [ngbTypeahead]="search" [resultTemplate]="rt"
                          [inputFormatter]="formatter" />
                            
                  </div>
              <div class="col-md-2 mb-2">
                  <input id="numpacking" type="text" class="form-control form-control-sm" name="numpacking" [(ngModel)]="numpacking"  placeholder="ระบุจำนวนกล่อง/มัด">
              </div>
              <div class="col-md-2 mb-2">
                  <input id="numpcs" type="text" class="form-control form-control-sm" name="numpcs" [(ngModel)]="numpcs"  placeholder="ระบุจำนวนแผ่น">
              </div>
              <div class="col-md-1 mb-2 col-12 text-center text-sm-center text-md-center text-lg-left">
                  <button [disabled]="searchbtn" (click)="addmasterpackage()" class="btn btn-primary btn-sm font-weight-light" type="button">เพิ่มรายการ</button>
              </div>
              <div class="col-md-2 mb-2 float-right">
                <input  (keyup)="searchmaster($event.target.value)" class="form-control form-control-sm"   placeholder="ค้นหารหัสสินค้า">
            </div>
          </div>
      </form>
      <table class="table table-hover table-bordered table-sm">
          <thead>
              <tr class="text-sm-center bg-light">
                  <th class="font-weight-normal test-center" scope="col">Item</th>
                  <th class="font-weight-normal test-center" scope="col">รหัสสินค้า</th>
                  <th class="font-weight-normal test-center" scope="col">สินค้า</th>
                  <th class="font-weight-normal test-center" scope="col">จำนวนกล่อง/มัด</th>
                  <th class="font-weight-normal test-center" scope="col">จำนวนแผ่น</th>
                  <th class="font-weight-normal test-center" scope="col"></th>
              </tr>
          </thead>
          <tbody>
              <tr *ngFor="let item of masterpackgetlistse; let i=index">
                  <th class="text-sm-center font-weight-normal test-center">{{i+1}}</th>
                  <th class="text-sm-center font-weight-normal test-center">{{item.itemid}}</th>
                  <td class="text-sm-left font-weight-normal">{{item.name}}</td>
                  <td class="text-sm-center font-weight-normal">{{item.qty}}</td>
                  <td class="text-sm-center font-weight-normal">{{item.pack}}</td>
                  <td class="text-center">
                      <button (click)="editmasterpackagefn(item.id,item.itemid,item.qty,item.pack)" class="btn btn-link font-weight-light" style="padding: 0pt" data-toggle="modal" data-target="#editmasterpackage" aria-expanded="true"
                          aria-controls="collapseOne">
                          Update
                      </button>
                  </td>
              </tr>
          </tbody>
      </table>

  </div>
</section>

<div class="modal fade" id="editmasterpackage" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
  <div class="modal-dialog" role="document">
      <div class="modal-content">
          <div class="modal-header">
              <h5 class="modal-title" id="exampleModalLabel">Edit MasterPackage</h5>
              <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                  <span aria-hidden="true">&times;</span>
              </button>
          </div>
          <div class="modal-body">
              <div class="input-group">
                    <div class="col-md-12 mb-2">
                            <ng-template  #rt let-r="result" let-t="term">
                            <label>{{r.itemid}}</label>
                          </ng-template>
                            <input  id="typeahead-template" placeholder="{{itemidedit}}"  type="text" class="form-control form-control-sm" [(ngModel)]="packget" name="packget" [ngbTypeahead]="search" [resultTemplate]="rt"
                              [inputFormatter]="formatter" />
                                
                      </div>
            
              </div>
              <div class="input-group">
                    <div class="col-md-12 mb-2">
                            <input id="editnumpacking" type="text" class="form-control form-control-sm" name="editnumpacking" [(ngModel)]="editnumpacking"  placeholder="ระบุจำนวนกล่อง/มัด">
                        </div>
              </div>

              <div class="input-group">
                    <div class="col-md-12 mb-2">
                            <input id="editnumpcs" type="text" class="form-control form-control-sm" name="editnumpcs" [(ngModel)]="editnumpcs"  placeholder="ระบุจำนวนแผ่น">
                        </div>
              </div>
          </div>
          <div class="modal-footer">
              <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
              <button  [disabled]="searchbtn" (click)="updatepackage()" type="button" class="btn btn-primary" data-dismiss="modal">Update</button>
              <button [disabled]="searchbtn" (click)="deletepackage()" type="button" class="btn btn-danger" data-dismiss="modal">Delete</button>
          </div>
      </div>
  </div>
</div>
<div id="mdlSample" class="modal fade" role="dialog" [ngStyle]="{'display': mdlSampleIsOpen ? 'block' : 'none', 'opacity': 1}">
        <div class="modal-dialog modal-md">
        <div class="modal-content">
        <div class="modal-header colhaederal">
        <h4 class="modal-title">Report</h4>
        </div>
        <div class="modal-body">{{alt}}</div>
        <div class="modal-footer" align="right">
                    <button type="button" id="btnClose" class="btn btn-danger" (click)="closemodel(false)"><i class="fa fa-times fa-fw"></i> ปิด</button></div>
        </div>
        </div>
        </div>
        
     