import { WebapiService } from './../webapi.service';
import { Component, OnInit } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Server } from 'selenium-webdriver/safari';
import {debounceTime, map} from 'rxjs/operators';
import {Observable} from 'rxjs';
import { $ } from 'protractor';
import { AppComponent } from '../app.component';
import { Router } from '../../../node_modules/@angular/router';
import parseXlsx from 'excel';
@Component({
  selector: 'app-masterpackage',
  templateUrl: './masterpackage.component.html',
  styleUrls: ['./masterpackage.component.css']
})
export class MasterpackageComponent implements OnInit {
  mdlSampleIsOpen : boolean = false;
  alt='';
  checkreload=true;



  urlApi = 'http://119.59.112.478:1433';
  itemidedit='';
  editnumpacking='';
  editnumpcs='';
  idpack: number;
url='';
packget: any;
product='';
numpacking='';
numpcs='';
masterpackgetlist: any[]=[];
productlist: any[];
file:File;
permisstiondata:any[]=[];
exportbtn=true;
searchbtn=true;
masterpackgetlistse:any[]=[];
  constructor(private http: HttpClient, private service: WebapiService,private app: AppComponent ,private router: Router) {
    localStorage.removeItem('DataSOderreview');
    localStorage.removeItem('DataSOderlist');
    this.url=service.geturlservice();
    this.permisstiondata=JSON.parse(sessionStorage.getItem('menu'))

    if (this.permisstiondata==null){
      this.router.navigate(['login']);
       }else{
      
        this.exportbtn=!this.permisstiondata[10].flag_print;
        this.searchbtn=!this.permisstiondata[10].flag_action;

       }




   }
   search = (text$: Observable<any>) =>

   text$.pipe(
     debounceTime(200),
     map(term => term === '' ? []
       : this.productlist.filter(v => v.itemid.toLowerCase().indexOf(term.toLowerCase()) > -1).slice(0, 10))
   );
   formatter = (x: {itemid: string}) => x.itemid;
  ngOnInit() {

    this.http.get<any>(this.url + 'productauto/admin').subscribe(res => {
      if(res.length >0 ){
        this.productlist=res;
        this.getmasterpack();
      }
    })

  }
  addmasterpackage() {
    
    if(this.packget==undefined) {
      this.openModal(true,'กรุณาป้อนรหัสสินค้า',false);
    } else if(this.numpacking=='') {
      this.openModal(true,'กรุณาป้อนจำนวนกล่อง/มัด',false);
    } else if(this.numpcs==''){
      this.openModal(true,'กรุณาป้อนจำนวนแผ่น',false);
    } else {
      var item;
      var st='';
      if(this.packget.itemid==undefined){
        item=this.packget;
      } else {
        item=this.packget.itemid;
      }
      st=item.toString();
      this.http.get<any>(this.url+'checkitemidtran/'+st.toUpperCase()+'/P').subscribe(res =>{
      if(res[0].num>=1){
        this.openModal(true,'รหัสสินค้า'+st.toUpperCase()+'มีในระบบแล้ว',false)
      } else {
        var urlpost=`${ this.url }${'add_packgetmaster'}/${ st.toUpperCase() }/${ this.numpacking }/${ this.numpcs}`;
        this.http.post(urlpost,'').subscribe(res =>{
    if(res==true){
    
      this.openModal(true,'บันทึกข้อมูลเสร็จสิ้น',true);
      this.packget=[];
      this.numpacking='';
      this.numpcs='';
    }
        })
      }
      })
    
    }
   
  }
  getmasterpack() {
    this.http.get<any>(this.url+'get_masterpack').subscribe(res =>{
      if(res.length >0) {
this.masterpackgetlist=res;
this.masterpackgetlistse=this.masterpackgetlist;
      }
    })
  }

  searchmaster(value : string){
    if(value.length>0){
      this.masterpackgetlistse=this.masterpackgetlist.filter(v => v.itemid.toLowerCase().indexOf(value.toLowerCase()) > -1).slice(0, 50);
    } else {
      this.masterpackgetlistse=this.masterpackgetlist;
    }

  }

  editmasterpackagefn(id,itemid,qty,pack) {
    this.idpack=id;
    this.itemidedit=itemid;
    this.editnumpacking=qty;
    this.editnumpcs=pack;
  }

  updatepackage() { 
   
    var idcheck;
    if( this.packget==undefined) {
      idcheck=this.itemidedit;
    }else {
idcheck=this.packget.itemid;
    }
   
    var urlpost=`${ this.url }${'update_packmaster'}/${ this.idpack }/${ idcheck }/${ this.editnumpacking }/${this.editnumpcs}`;
    this.http.post(urlpost,'').subscribe(res =>{
      if(res==true) {
        this.openModal(true,'บันทึกข้อมูลเสร็จสิ้น',true);
        this.idpack=0;
        this.itemidedit='';
        this.editnumpacking='';
        this.editnumpcs='';
      }
    });
   
  }

  deletepackage() {
    var idcheck;
    if( this.packget==undefined) {
      idcheck=this.itemidedit;
    }else {
idcheck=this.packget.itemid;
    }
    var urlpost=`${this.url}${'deletepackproduct'}/${idcheck}/${'M'}`;
    this.http.post(urlpost,'').subscribe(res =>{
     
      if(res==true){
        this.getmasterpack();
        this.openModal(true,'ลบข้อมูลเสร็จสิ้น',true);
      }
    },error =>{
      this.openModal(true,'เกิดข้อผิดพลาดกรุณาลองใหม่อีกครั้ง',true);
    })
  }

  openModal(open : boolean,text: string,load:boolean) : void {
    this.mdlSampleIsOpen = open;
    this.alt=text;
    this.checkreload=load;
}
 closemodel(cl: boolean) {
  this.mdlSampleIsOpen=false;
  if(this.checkreload==true) {
    this.router.navigate(['masterpackage']);
    this.getmasterpack();
  }
 
}

}
