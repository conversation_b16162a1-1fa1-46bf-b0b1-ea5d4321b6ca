.bordered {
    border: 1px solid rgb(0, 0, 0);
    border-radius: 6px 6px 6px 6px;
    -moz-border-radius: 6px 6px 6px 6px;
    -webkit-border-radius: 6px 6px 6px 6px;
    box-sizing: content-box;
}
.table2 {
    border-spacing: 0;

}        
.bordered th:first-child {
    border-radius: 6px 0 0 0;
    -moz-border-radius: 6px 0 0 0;
    -webkit-border-radius: 6px 0 0 0;
}

.bordered th:last-child {
    border-radius: 0 6px 0 0;
    -moz-border-radius: 0 6px 0 0;
    -webkit-border-radius: 0 6px 0 0;
}

.bordered td:first-child, .bordered th:first-child {
    border-left: medium none;
}

.bordered th {
    background-color: rgb(0, 0, 0);
    background-image: -moz-linear-gradient(center top , rgb(0, 0, 0), rgb(0, 0, 0));
    background-image: -webkit-gradient(linear, 0 0, 0 bottom, from(black), to(black), color-stop(.4, black));
    border-top: medium none;
    box-shadow: 0 1px 0 rgb(0, 0, 0) inset;
    text-shadow: 0 1px 0 rgb(0, 0, 0);
}

.bordered td, .bordered th {
    border-left: 1px solid black;
    border-top: 1px solid black;
    padding: 10px;
    text-align: left;
}


