<div class="col-sm-12 col-md-12 col-lg-12  text-center" style="margin-top: 50px; margin-bottom:20px;">
      <button style="margin-right: 5px;" (click)="captureScreen()" class="btn btn-primary btn-sm font-weight-light" type="button" >Download INV PDF</button>
<button (click)="toback()" class="btn btn-primary btn-sm font-weight-light" type="button" >กลับหน้าหลัก</button>

    <!--<input id="inputproduct" class="form-control form-control-sm"  [disabled]="groupsale !=='admin'" [(ngModel)]="CodeSo" type="text" name="myproduct" placeholder="รหัสพนักงานขาย">-->  
</div>
<div class="col-sm-12 col-md-12 col-lg-12  text-center" style="margin-bottom:20px;">
    <select  [(ngModel)]="pageHeightmin" class="custom-select custom-select-sm col-sm-2 col-md-2 col-lg-2">
    <option disabled value="">--กรณีโหลด PDF แล้วเกินหน้า--</option>
    <option value="305">เกินหน้า</option>
    <option value="300">ค่าเริ่มต้น</option>
    <option value="295">ไม่เต็มหน้า</option>
    <option value="295">Galaxy Tab S3</option>
    <option value="290">I5 SE</option>
    <option value="302">Microsoft Edge</option>
</select>
</div>

<div class="container-fluid" align='center'>
<div id="convert"  style=" width: 1100px; padding-left: 5px; padding-right: 5px; " >
      <!--NOVATเขียว-->
      <div *ngIf="taxgroup"> 
 <table *ngFor="let y of dataprint" style="width: 100%; height: 295px; margin-bottom: 20px; margin-top: 0px;  " >
                <tr>
                    <td style='padding: 5px 8px 8px; position:relative;'>
                       
                        <table style='font-family:Tahoma,segoe UI;font-size: 12pt; padding: 5px; width: 100%;margin-bottom: 43px;' [ngStyle]="{'margin-top':gettop2(pageHeightmin)}" cellspacing='0'
                            cellpadding='2' align='center' border='0'>
                          
                                <td align='center'>       
                                        <strong style="font-size:28pt">ต้นฉบับใบส่งของชั่วคราว</strong>
                                        <p style="font-size:25pt">ORIGINAL DELIVERY BILL</p>
                                </td>
                                
                        </table>
                        <div valign="bottom" style="" >
                            <img src="assets/img/1herder.jpg"   style="position:absolute;top: 85px;width: 55px; right: 80px;" >
                        </div> 
                    </td>
                </tr>
                <tr>
                    <td>
                        <div style='font-family: Segoe UI;; font-size: 15px; color: rgb(0, 0, 0);'>
                                    <div  style='font-size: 13px; width: 150px; font-family: segui ui;;font-size: 9pt; color:black;'>
                                                สำนักงานใหญ่
                                            </div>
                            <table width='100%' style='width:100%; margin-bottom: 5px;' cellspacing='0' cellpadding='2' align='center' >
                                <tbody>
                                    <tr *ngFor="let item of DataHerderINV"height="150px">
                                       <div class="rcorners"  style="height: 150px; float: left;    width: 49.9%;">
                                           <table>
                                               <tr>
                                                   <td width="7%" valign="top" class="text-right" style="font-family: segui ui;font-size: 12pt; padding: 5px; padding-top: 10px; ">
                                          ขายให้   : <br>
                                          Soid To.
                                          </td>
                                        <td class="text-left" width="43%" valign="top" style="font-family: segui ui;font-size: 12pt;  padding: 5px; padding-top: 10px;" >
                                              {{item.nameINVherder}}<br>
                                              {{item.addressINV}}  <br><br>
                                              {{item.regnum}}
                                        </td>
                                               </tr>
                                           </table>
                                       </div>
                                        
                                       <div class="rcorners" style="height: 150px;float: right;    width: 49.9%;">
                                        <table>
                                            <tr>
                                                <td  width="8%" valign="top" class="font-weight-normal text-right" style='font-family: segui ui;font-size: 12pt;  padding: 5px;padding-top: 10px;'>
                                                    สถานที่ส่ง  : 
                                               </td>
                                               <td  width="42%" valign="top" class="font-weight-normal text-left" style='font-family: segui ui;font-size: 12pt;  padding: 5px;padding-top: 10px;'>
                                                   {{ item.addressDEL}}<br><br>
                                                   เบอร์โทรลูกค้า : {{ item.telnumber }}
                                              </td>
                                            </tr>
                                        </table>
                                    </div>
                                      
                                    
                                    </tr>
                                </tbody>
                            </table><!--background-color: rgba(40, 197, 40, 0.911);style="color: #fff; background-color: rgba(40, 197, 40, 0.911);"-->
                       


                            <div class="rcorners" style="width:100%;background-color:  rgba(40, 197, 40, 0.911);">
                                 <table   align='center'  style=' width:100%;font-family:Segoe UI;font-size: 12pt; '>
                                            <tbody >
                                              <tr style="color: #fff;" >
                                                  <td align='center' style=" padding: 5px;border-right: 1px solid black; " class="font-weight-normal text-center" >รหัสลูกค้า<br>Customer Code</td>
                                                  <td align='center' style="padding: 5px;    border-right: 1px solid black; width: 250px;"class="font-weight-normal text-center"    >พนักงานขาย<br>Salesman</td>
                                                  <td align='center' style="padding: 5px;     border-right: 1px solid black;   width: 150px; "class="font-weight-normal text-center"  >เงื่อนไขการชำระเงิน<br>Payment Condtion</td>
                                                  <td align='center' style="padding: 5px;    border-right: 1px solid black; width: 180px; "class="font-weight-normal text-center"  >วันครบกำหนด<br>Due Date</td>
                                                  <td align='center' style="padding: 5px;    border-right: 1px solid black;  width: 180px; "class="font-weight-normal text-center"  >เลขที่<br>No.</td>
                                                  <td align='center' style="padding: 5px;   width: 180px;" class="font-weight-normal text-center" >วันที่<br>Date</td>
                                              </tr>
                                              <tr  style="color: rgb(0, 0, 0);background-color: #fff" *ngFor="let itemX of DataHerderINV" >
                                                  <td align='center' style="padding: 5px;     border-right: 1px solid black; " class="text-center" >{{ itemX.salesid }}<br>{{itemX.orderaccount}}</td>
                                                  <td align='center'  class="text-center" style="padding: 5px;     border-right: 1px solid black; " >{{ itemX.name }}</td>
                                                  <td align='center' style="padding: 5px;     border-right: 1px solid black;" class="text-center" >{{itemX.paymenttype}}</td>
                                                  <td align='center' style="padding: 5px;     border-right: 1px solid black;" class="text-center" >{{ itemX.duedate | date: 'dd/MM/yyyy' }}</td>
                                                  <td align='center'style="padding: 5px;     border-right: 1px solid black;" class="text-center" >{{ itemX.invoiceid }}</td>
                                                  <td align='center'style="padding: 5px;" class="text-center" >{{ itemX.invoicedate | date: 'dd/MM/yyyy' }}</td>
                                              </tr>
                                            </tbody>
                                        </table>
                            </div>
                                   
                           
                            <div class="rcorners" style="width:100%;background-color:  rgba(40, 197, 40, 0.911);margin-top: 3px; font-family:Segoe UI;font-size: 10pt; ">
                                <table width='100%' cellspacing='0' cellpadding='2' align='center' border='0' style='width:100%;'>
                                  <thead>
                                      <tr  style="color: #fff; background-color: rgba(40, 197, 40, 0.911);font-family:Segoe UI;font-size: 12pt;" >
                                          <td align='center' style="padding: 2px; border-right: 1px solid black; width: 45px;"class="text-center" >ลำดับ<br>Itme</td>
                                          <td align='center' style="padding: 2px;  width: 90px;" class="text-center">เลขที่ใบสั่ง<br>สินค้า<br>P.O.No.</td>
                                          <td align='center' style="padding: 2px; border-left: 1px solid black; width: 115px;" class="text-center" >รหัสสินค้า<br>Part Code</td>
                                          <td align='center' style="padding: 2px; border-left: 1px solid black;"class="text-center"  >รายการ<br>Description</td>
                                       
                                          <td align='center' style="padding: 2px; border-left: 1px solid black; width: 80px;" class="text-center" >จำนวน<br>Quantity</td>
                                          <td align='center' style="padding: 2px; border-left: 1px solid black; width: 80px;"class="text-center"  >หน่วยละ<br>Unit Price</td>
                                          <td align='center' colspan="3" style="padding: 2px; border-left: 1px solid black;" class="text-center" >ส่วนลด<br>Disc %</td>
                                          <td align='center' style="padding: 2px; border-left: 1px solid black;"class="text-center"  >ราคาสุทธิ<br>ต่อชิ้น</td>
                                          <td align='center' style="padding: 2px; border-left: 1px solid black;"class="text-center"  >จำนวนเงิน<br>Amount</td>
                                      </tr>
                                    </thead>
                                      <tbody style="background-color: #fff;font-family:Segoe UI;font-size: 11pt;">
                                      <tr *ngFor="let x of y.data" style="color: rgb(0, 0, 0);background-color: #fff"  >
                                          <td align='center' style=" padding: 5px;padding: 5px; border-right: 1px solid black;" class="text-center" [ngStyle]="{'color':getColor(x.Number)}" >{{ x.Number }}</td>
                                          <td align='center' style="padding: 5px; border-right: 1px solid black;" class="text-center" >{{ x.PO }}</td>
                                          <td align='center' style="padding: 5px; border-right: 1px solid black; " class=" text-left" > {{ x.itemid }}</td>
                                          <td align='center' style="padding: 5px; border-right: 1px solid black; font-size: 9.8pt " class="text-left" >{{x.productname}}</td>
                                          <td align='center'style="padding: 5px; border-right: 1px solid black; " class="text-right" > {{ x.qtyline | number:'1.0-0'  }}</td>
                                          <td align='center'style="padding: 5px; border-right: 1px solid black; " class="text-right" > {{ x.salesprice | number:'1.2-2'  }}</td>
                                          <td align='center' style="padding: 5px; border-right: 1px solid black; width: 38px; " class="text-right" [ngStyle]="{'color':getColorivz(x.ivz_percent1_ct | number:'1.0-0')}" >{{ x.ivz_percent1_ct | number }}</td>
                                          <td align='center' style="padding: 5px; border-right: 1px solid black; width: 38px; " class="text-right" [ngStyle]="{'color':getColorivz(x.ivz_percent2_ct | number:'1.0-0')}" >{{ x.ivz_percent2_ct | number }}</td>
                                          <td align='center' style="padding: 5px; border-right: 1px solid black; width: 38px; " class="text-right" [ngStyle]="{'color':getColorivz(x.ivz_percent3_ct | number:'1.0-0')}" ><sup>{{ x.ivz_percent3_ct | number }}</sup></td>
                                          <td align='center'style="padding: 5px; border-right: 1px solid black; width: 90px; " class="text-right" > {{ x.totalTH  | number:'1.3-3' }} </td>
                                          <td align='center'style="padding: 5px;  width: 90px;" class="text-right" >{{ x.lineamountmst | number:'1.2-2'}}</td>
                       
                                      </tr>
                                     
                                    <tr style="background-color: #fff">
                                          <td align='center'style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                          <td align='center' style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                          <td align='center' style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                          <td align='center' style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                          <td align='center'style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                          <td align='center'style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                          <td align='center' style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                          <td align='center' style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                          <td align='center' style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                          <td align='center'style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                          <td align='center'style="padding: 5px; " class="text-center" ></td>
                                    </tr>
                                    <tr style="background-color: #fff">
                                          <td align='center'style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                          <td align='center' style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                          <td align='center' style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                          <td align='center' style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                          <td align='center'style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                          <td align='center'style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                          <td align='center' style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                          <td align='center' style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                          <td align='center' style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                          <td align='center'style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                          <td align='center'style="padding: 5px;" class="text-center" ></td>
                                    </tr>
                                    <tr style="background-color: #fff">
                                          <td align='center'style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                          <td align='center' style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                          <td align='center' style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                          <td align='center' style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                          <td align='center'style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                          <td align='center'style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                          <td align='center' style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                          <td align='center' style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                          <td align='center' style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                          <td align='center'style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                          <td align='center'style="padding: 5px; " class="text-center" ></td>
                                    </tr>
                                    <tr style="background-color: #fff">
                                          <td align='center'style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                          <td align='center' style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                          <td align='center' style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                          <td align='center' style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                          <td align='center'style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                          <td align='center'style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                          <td align='center' style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                          <td align='center' style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                          <td align='center' style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                          <td align='center'style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                          <td align='center'style="padding: 5px; " class="text-center" ></td>
                                    </tr>
                                    <tr style="background-color: #fff">
                                          <td align='center'style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                          <td align='center' style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                          <td align='center' style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                          <td align='center' style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                          <td align='center'style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                          <td align='center'style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                          <td align='center' style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                          <td align='center' style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                          <td align='center' style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                          <td align='center'style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                          <td align='center'style="padding: 5px; " class="text-center" ></td>
                                    </tr>
                            
                                    <tr style="background-color: #fff">
                                          <td align='center'style="padding: 5px; border-right: 1px solid black; " class="text-center" ></td>
                                          <td align='center' style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                          <td align='center' style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                          <td align='center' style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                          <td align='center'style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                          <td align='center'style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                          <td align='center' style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                          <td align='center' style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                          <td align='center' style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                          <td align='center'style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                          <td align='center'style="padding: 5px; " class="text-center" ></td>
                                    </tr>
                                    <tr style="background-color: #fff">
                                        <td align='center'style="padding: 5px; border-right: 1px solid black; " class="text-center" ></td>
                                        <td align='center' style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                        <td align='center' style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                        <td align='center' style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                        <td align='center'style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                        <td align='center'style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                        <td align='center' style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                        <td align='center' style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                        <td align='center' style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                        <td align='center'style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                        <td align='center'style="padding: 5px; " class="text-center" ></td>
                                  </tr>
                              
                                 
                                   
                                   
                                    
                                
                                     
                                      <tr style="color: rgb(0, 0, 0); height: 100px;background-color: #fff " >
                                       
                                        <td align='center' valign="top" colspan="5" rowspan="3" style="font-family:Segoe UI;font-size: 12pt;padding: 15px; text-align: left;border-right: 1px solid black;border-top: 1px solid black;" class="text-left" >
                                         <div  [ngStyle]="{'display':getnumpage(y.page)}" >({{ SumtotalTH }})</div> 
                                        
                                    </td>
                                        <td align='center' colspan="4" rowspan="3" style="padding: 15px; text-align: center;border-right: 1px solid black;border-top: 1px solid black; font-family:Tohama;font-size: 14pt;" class="text-center" >จำนวนเงินรวมทั้งสิ้น<br>(Grand Total Amount)</td>
                                        <td align='center' colspan="2" style="padding: 0px; text-align: right;border-top: 1px solid black;" class="text-right"> 
                                        <div style="width:100%;padding: 3px; font-family: Tohama;font-size: 11pt;"  [ngStyle]="{'display':getnumpage(y.page)}">
                                          {{ Sumtotal  | number:'1.2-2' }} <!--sumtaxamount-->
                                        </div>
                                        <div style="width:100%;padding: 3px; font-family: Tohama;font-size: 11pt;"  [ngStyle]="{'display':getnumpage(y.page)}">
                                          {{taxnew  | number:'1.2-2'}}
                                        </div>
                                       <!-- <div style="width:100%;padding: 3px; font-family: Tohama;font-size: 11pt;"  [ngStyle]="{'display':getnumpage(y.page)}">
                                            {{salesbalance | number:'1.2-2'}}
                                          </div>-->
                                        <div  [ngStyle]="{'display':getnumpage(y.page)}"  style="width:100%;padding: 3px; font-family: Tohama;font-size: 11pt; border-block-end: 1px solid; border-top: 1px solid;margin-bottom: 2px;">
                                          {{ Sumtotal  | number:'1.2-2' }} 
                                        </div>
                                        <div [ngStyle]="{'display':getnumpage(y.page)}" style="color:#fff;width:100%; border-top: 1px solid black;font-family: Tohama;font-size: 11pt;">
                                            sssss
                                        </div>
                                    </td>
                                 </tr>
                                    
                                    </tbody>
                                </table>
                            </div>
                            <div class="rcorners" style="width:100%;;margin-top: 3px;text-align:center; ">
                                <table width='100%' cellspacing='0' cellpadding='2' align='center'  style='width:100%;'>
                                    <tbody>
                                      <tr style="color: rgb(0, 0, 0);" >
                                          <td align='center' style="padding: 10px;border-right: 1px solid black;font-family:Segoe UI;font-size: 11pt;" width="30%"class="text-align: center;"   ><br><br><br >.................................................................................<br>ผู้อนุมัติ Autherized<br><br>วันที่ Date ....................................</td>
                                          <td align='center' style="padding: 15px;border-right: 1px solid black; " width="40%"><div style="font-family:Segoe UI;font-size: 15pt;"  class="text-align: center;" >บริษัทฯ ขอสงวนสิทธิ์ในการเปลื่ยนสินค้า <br>ภายใน 7 วัน นับจากวันที่ส่งสินค้า</div></td>
                                          <td align='center' style="font-family:Segoe UI;font-size: 11pt;" width="30%"class="text-align: center;"  ><br><br><br >.................................................................................<br>ผู้รับของ Receive Signature<br><br>วันที่ Date ....................................</td>
                                      </tr>   
                                    </tbody>
                                </table>   
                            </div>
                      
                           
                    </div>
                  
                    </td>
                   
                </tr>   
            </table>

      </div>   
      <!--EndNOVAT-->
     <div *ngIf="!taxgroup">
      <table *ngFor="let y of dataprint" style="width: 100%; height: 295px; " >
            <tr>
                <td>
                  <table width= "100%" style='font-family: segui ui;font-size: 12pt;'[ngStyle]="{'margin-top':gettopDOM(y.page,pageHeightmin)}" align='center' border='0'>
                      <td align='center'> 
                            <img src="assets/img/Herder.jpg" width="100%" [ngStyle]="{'margin-bottom':gettop(pageHeightmin)}" >  <!--style="margin-bottom: 40px;"40-->        
                              <strong style="font-size:28pt;">สำเนาใบกำกับภาษี / ใบส่งสินค้า / ใบแจ้งหนี้</strong>
                              <p style="font-size:22pt">COPY TAX INVOICE / DELIVERY ORDER / INVOICE</p>
                      </td>
              </table>
                </td>
            </tr>
            <tr>
                <td>
                    <div style='font-family: Arial,Helvetica,sans-serif; font-size: 15px; color: rgb(0, 0, 0);'>
                        <table width="100%">
                              <tr>
                                    <td width="50%" class="text-left" style='font-family: segui ui;font-size: 12pt;  color:black;'>
                                          สำนักงานใหญ่<br>
                                          เลขประจำตัวผู้เสียภาษีอากร 0105529040739   
                                    </td>
                                    <td width="50%" class="text-right" valign="bottom" style='font-family: segui ui;font-size: 12pt; color:black;' >
                                          (ไม่ใช่ใบกำกับภาษี)
                                    </td>
                              </tr>
                        </table>

                        <table width='100%' style='width:100%; margin-bottom: 5px;' cellspacing='0' cellpadding='2' align='center' >
                            <tbody>
                                <tr *ngFor="let item of DataHerderINV"height="150px">
                                   <div class="rcorners"  style="height: 150px; float: left;    width: 49.9%;">
                                       <table>
                                           <tr>
                                               <td width="7%" valign="top" class="text-right" style="font-family: segui ui;font-size: 12pt; padding: 5px; padding-top: 10px; ">
                                      ขายให้   : <br>
                                      Soid To.
                                      </td>
                                    <td class="text-left" width="43%" valign="top" style="font-family: segui ui;font-size: 12pt;  padding: 5px; padding-top: 10px;" >
                                          {{item.nameINVherder}}<br>
                                          {{item.addressINV}}  <br><br>
                                          {{item.regnum}}
                                    </td>
                                           </tr>
                                       </table>
                                   </div>
                                    
                                   <div class="rcorners" style="height: 150px;float: right;    width: 49.9%;">
                                    <table>
                                        <tr>
                                            <td  width="8%" valign="top" class="font-weight-normal text-right" style='font-family: segui ui;font-size: 12pt;  padding: 5px;padding-top: 10px;'>
                                                สถานที่ส่ง  : 
                                           </td>
                                           <td  width="42%" valign="top" class="font-weight-normal text-left" style='font-family: segui ui;font-size: 12pt;  padding: 5px;padding-top: 10px;'>
                                               {{ item.addressDEL}}<br><br>
                                               เบอร์โทรลูกค้า : {{ item.telnumber }}
                                          </td>
                                        </tr>
                                    </table>
                                </div>
                                  
                                
                                </tr>
                            </tbody>
                        </table>




                      <!--  <table width='100%' style='width:100%;' cellspacing='0' cellpadding='2' align='center' border='0'>
                            <tbody style="width:100%">
                                <tr *ngFor="let item of DataHerderINV" height="120px" >
                                    <td width="7%" valign="top" class="text-right" style="font-size: 15px; padding: 5px; padding-top: 10px;  border-left: 1px solid black;border-bottom: 1px solid black;border-top: 1px solid black;">
                                      ขายให้ : <br>
                                      Soid To.
                                      </td>
                                    <td width="43%" class="text-left" valign="top" style="font-size: 15px; padding: 5px; padding-top: 10px;  border-bottom: 1px solid black;border-top: 1px solid black;" >
                                          {{item.invoicingname}}<br>
                                          {{item.addressINV}} <br>
                                          {{item.regnum}}
                                    </td>
                                    <td  width="8%" valign="top" class="font-weight-normal text-right" style=' padding: 5px;padding-top: 10px; font-size: 15px; border-left: 1px solid black;border-bottom: 1px solid black;border-top: 1px solid black;'>
                                       สถานที่ส่ง  : 
                                  </td>
                                  <td  width="42%" valign="top" class="font-weight-normal text-left" style='  padding: 5px;padding-top: 10px; font-size: 15px;border-right: 1px solid black;border-bottom: 1px solid black;border-top: 1px solid black;'>
                                      {{ item.addressDEL}}<br><br>
                                      เบอร์โทรลูกค้า : {{ item.telnumber }}
                                 </td>
                                
                                </tr>
                            </tbody>
                        </table>-->
                       <!-- <table width='100%' cellspacing='0'  cellpadding='2' align='center'  style='width:100%; margin-top: 5px  ;font-size: 13px;'>
                            <tbody>
                              <tr style="color: #fff;  background-color: #8b2323;" >
                                  <td align='center' style=" font-size: 13px; padding: 5px; border-left: 1px solid black;border-bottom: 1px solid black;border-top: 1px solid black;" >รหัสลูกค้า<br>Customer Code</td>
                                  <td align='center' style=" font-size: 13px;padding: 5px; border-bottom: 1px solid black;border-top: 1px solid black; border-left: 1px solid black;"   >พนักงานขาย<br>Salesman</td>
                                  <td align='center' style=" font-size: 13px;padding: 5px; border-bottom: 1px solid black;border-top: 1px solid black; border-left: 1px solid black;"  >เงื่อนไขการชำระเงิน<br>Payment Condtion</td>
                                  <td align='center' style=" font-size: 13px;padding: 5px; border-bottom: 1px solid black;border-top: 1px solid black; border-left: 1px solid black;"  >วันครบกำหนด<br>Due Date</td>
                                  <td align='center' style=" font-size: 13px;padding: 5px; border-bottom: 1px solid black;border-top: 1px solid black; border-left: 1px solid black;"  >เลขที่<br>No.</td>
                                  <td align='center' style="font-size: 13px;padding: 5px; border-bottom: 1px solid black;border-top: 1px solid black;border-right: 1px solid black; border-left: 1px solid black;"  >วันที่<br>Date</td>
                              </tr>
                              <tr style="color: rgb(0, 0, 0)" *ngFor="let itemX of DataHerderINV" >
                                  <td align='center' style="font-size: 13px;padding: 5px; border-left: 1px solid black;border-bottom: 1px solid black;" class="text-center" >{{ itemX.salesid }}<br>{{itemX.orderaccount}}</td>
                                  <td align='center' style="font-size: 13px;padding: 5px; border-left: 1px solid black;border-bottom: 1px solid black;" class="text-center" >{{ itemX.name }}</td>
                                  <td align='center' style="font-size: 13px;padding: 5px; border-left: 1px solid black;border-bottom: 1px solid black;" class="text-center" >{{itemX.paymenttype}}</td>
                                  <td align='center' style="font-size: 13px;padding: 5px; border-left: 1px solid black;border-bottom: 1px solid black;" class="text-center" >{{ itemX.duedate | date: 'dd/MM/yyyy' }}</td>
                                  <td align='center'style="font-size: 13px;padding: 5px; border-left: 1px solid black;border-bottom: 1px solid black;" class="text-center" >{{ itemX.invoiceid }}</td>
                                  <td align='center'style="font-size: 13px;padding: 5px; border-left: 1px solid black;border-right: 1px solid black;border-bottom: 1px solid black;" class="text-center" >{{ itemX.invoicedate | date: 'dd/MM/yyyy' }}</td>
                              </tr>
                            </tbody>
                        </table>-->
                        <div class="rcorners" style="width:100%;background-color: #8b2323;">
                            <table   align='center'  style=' width:100%;font-family:Segoe UI;font-size: 12pt; '>
                                       <tbody >
                                         <tr style="color: #fff;" >
                                             <td align='center' style=" padding: 5px;border-right: 1px solid black; " class="font-weight-normal text-center" >รหัสลูกค้า<br>Customer Code</td>
                                             <td align='center' style="padding: 5px;    border-right: 1px solid black; width: 250px;"class="font-weight-normal text-center"    >พนักงานขาย<br>Salesman</td>
                                             <td align='center' style="padding: 5px;     border-right: 1px solid black;   width: 150px; "class="font-weight-normal text-center"  >เงื่อนไขการชำระเงิน<br>Payment Condtion</td>
                                             <td align='center' style="padding: 5px;    border-right: 1px solid black; width: 180px; "class="font-weight-normal text-center"  >วันครบกำหนด<br>Due Date</td>
                                             <td align='center' style="padding: 5px;    border-right: 1px solid black;  width: 180px; "class="font-weight-normal text-center"  >เลขที่<br>No.</td>
                                             <td align='center' style="padding: 5px;   width: 180px;" class="font-weight-normal text-center" >วันที่<br>Date</td>
                                         </tr>
                                         <tr  style="color: rgb(0, 0, 0);background-color: #fff" *ngFor="let itemX of DataHerderINV" >
                                             <td align='center' style="padding: 5px;     border-right: 1px solid black; " class="text-center" >{{ itemX.salesid }}<br>{{itemX.orderaccount}}</td>
                                             <td align='center'  class="text-center" style="padding: 5px;     border-right: 1px solid black; " >{{ itemX.name }}</td>
                                             <td align='center' style="padding: 5px;     border-right: 1px solid black;" class="text-center" >{{itemX.paymenttype}}</td>
                                             <td align='center' style="padding: 5px;     border-right: 1px solid black;" class="text-center" >{{ itemX.duedate | date: 'dd/MM/yyyy' }}</td>
                                             <td align='center'style="padding: 5px;     border-right: 1px solid black;" class="text-center" >{{ itemX.invoiceid }}</td>
                                             <td align='center'style="padding: 5px;" class="text-center" >{{ itemX.invoicedate | date: 'dd/MM/yyyy' }}</td>
                                         </tr>
                                       </tbody>
                                   </table>
                       </div>
                       <!-- <table width='100%' cellspacing='0' cellpadding='2' align='center' border='0' style='width:100%; margin-top: 3px; font-size: 13px;'>
                          <tbody>
                            <tr  style="color: #fff; background-color:  #8b2323;" >
                                <td align='center' style=" font-size: 13px;padding: 5px; border-left: 1px solid black;border-right: 1px solid black;border-bottom: 1px solid black;border-top: 1px solid black; width: 45px;"class="text-center" >ลำดับ<br>Itme</td>
                                <td align='center' style=" font-size: 13px;padding: 5px; border-bottom: 1px solid black;border-top: 1px solid black; width: 70px;">เลขที่ใบสั่ง<br>สินค้า<br>P.O.No.</td>
                                <td align='center' style=" font-size: 13px;padding: 5px; border-left: 1px solid black;border-bottom: 1px solid black;border-top: 1px solid black; width: 115px;" class="text-center" >รหัสสินค้า<br>Part Code</td>
                                <td align='center' style="font-size: 13px;padding: 5px; border-left: 1px solid black;border-bottom: 1px solid black;border-top: 1px solid black;"class="text-center"  >รายการ<br>Description</td>
                             
                                <td align='center' style="font-size: 13px;padding: 5px; border-left: 1px solid black;border-bottom: 1px solid black;border-top: 1px solid black; width: 70px;" class="text-center" >จำนวน<br>Quantity</td>
                                <td align='center' style="font-size: 13px;padding: 5px; border-left: 1px solid black;border-bottom: 1px solid black;border-top: 1px solid black; width: 70px;"class="text-center"  >หน่วยละ<br>Unit Price</td>
                                <td align='center' colspan="3" style="font-size: 13px;padding: 5px; border-left: 1px solid black;border-bottom: 1px solid black;border-top: 1px solid black;" class="text-center" >ส่วนลด<br>Disc %</td>
                                <td align='center' style="font-size: 13px;padding: 5px; border-left: 1px solid black;border-bottom: 1px solid black;border-top: 1px solid black;"class="text-center"  >ราคาสุทธิ<br>ต่อชิ้น</td>
                                <td align='center' style="font-size: 13px;padding: 5px; border-right: 1px solid black; border-left: 1px solid black;border-bottom: 1px solid black;border-top: 1px solid black;"class="text-center"  >จำนวนเงิน<br>Amount</td>
                            </tr>
                            <tr *ngFor="let x of y.data" style="color: rgb(0, 0, 0);" >
                                <td align='center' style="font-size: 13px;padding: 5px;padding: 5px; border-right: 1px solid black; border-left: 1px solid black;" class="text-center"[ngStyle]="{'color':getColor(x.Number)}" >{{ x.Number }}</td>
                                <td align='center' style="font-size: 13px;padding: 5px; border-right: 1px solid black;" class="text-center" >{{ x.PO }}</td>
                                <td align='center' style="font-size: 13px;padding: 5px; border-right: 1px solid black;" class=" text-left" > {{ x.itemid }}</td>
                                <td align='center' style="font-size: 13px;padding: 5px; border-right: 1px solid black;" class="text-left" >{{x.productname}}</td>
                                <td align='center'style="font-size: 13px;padding: 5px; border-right: 1px solid black;" class="text-right" > {{ x.qtyline | number:'1.2-2'  }}</td>
                                <td align='center'style="font-size: 13px;padding: 5px; border-right: 1px solid black;" class="text-right" > {{ x.salesprice | number:'1.2-2'  }}</td>
                                <td align='center' style="font-size: 13px;padding: 5px; border-right: 1px solid black; width: 50px;" class="text-right" >{{ x.ivz_percent1_ct | number:'1.0-0' }}</td>
                                <td align='center' style="font-size: 13px;padding: 5px; border-right: 1px solid black; width: 50px;" class="text-right" >{{ x.ivz_percent2_ct | number:'1.0-0' }}</td>
                                <td align='center' style="font-size: 13px;padding: 5px; border-right: 1px solid black; width: 50px;" class="text-right" >{{ x.ivz_percent3_ct | number:'1.0-0' }}</td>
                                <td align='center'style="font-size: 13px;padding: 5px; border-right: 1px solid black; width: 80px; " class="text-right" > {{ x.totalline | number:'1.2-2' }} </td>
                                <td align='center'style="font-size: 13px;padding: 5px; border-right: 1px solid black; width: 80px;" class="text-right" >{{ x.lineamountmst | number:'1.2-2'}}</td>
             
                            </tr>
                          <tr>
                                <td align='center'style="padding: 5px; border-right: 1px solid black; border-left: 1px solid black;" class="text-center" ></td>
                                <td align='center' style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                <td align='center' style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                <td align='center' style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                <td align='center'style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                <td align='center'style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                <td align='center' style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                <td align='center' style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                <td align='center' style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                <td align='center'style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                <td align='center'style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                          </tr>
                          <tr>
                                <td align='center'style="padding: 5px; border-right: 1px solid black; border-left: 1px solid black;" class="text-center" ></td>
                                <td align='center' style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                <td align='center' style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                <td align='center' style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                <td align='center'style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                <td align='center'style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                <td align='center' style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                <td align='center' style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                <td align='center' style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                <td align='center'style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                <td align='center'style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                          </tr>                                         
                          <tr>
                                <td align='center'style="padding: 5px; border-right: 1px solid black; border-left: 1px solid black;" class="text-center" ></td>
                                <td align='center' style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                <td align='center' style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                <td align='center' style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                <td align='center'style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                <td align='center'style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                <td align='center' style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                <td align='center' style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                <td align='center' style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                <td align='center'style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                <td align='center'style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                          </tr>
                         
                          <tr>
                                <td align='center'style="padding: 5px; border-right: 1px solid black; border-left: 1px solid black;" class="text-center" ></td>
                                <td align='center' style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                <td align='center' style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                <td align='center' style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                <td align='center'style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                <td align='center'style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                <td align='center' style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                <td align='center' style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                <td align='center' style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                <td align='center'style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                <td align='center'style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                          </tr>
                          <tr>
                                <td align='center'style="padding: 5px; border-right: 1px solid black; border-left: 1px solid black;" class="text-center" ></td>
                                <td align='center' style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                <td align='center' style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                <td align='center' style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                <td align='center'style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                <td align='center'style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                <td align='center' style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                <td align='center' style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                <td align='center' style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                <td align='center'style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                <td align='center'style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                          </tr>
                          <tr>
                                <td align='center'style="padding: 5px; border-right: 1px solid black; border-left: 1px solid black;" class="text-center" ></td>
                                <td align='center' style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                <td align='center' style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                <td align='center' style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                <td align='center'style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                <td align='center'style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                <td align='center' style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                <td align='center' style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                <td align='center' style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                <td align='center'style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                <td align='center'style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                          </tr>
                          <tr>
                                <td align='center'style="padding: 5px; border-right: 1px solid black; border-left: 1px solid black;" class="text-center" ></td>
                                <td align='center' style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                <td align='center' style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                <td align='center' style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                <td align='center'style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                <td align='center'style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                <td align='center' style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                <td align='center' style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                <td align='center' style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                <td align='center'style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                <td align='center'style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                          </tr>
                         
                         
                      
                           
                            <tr style="color: rgb(0, 0, 0); height: 100px; " >
                             
                              <td align='center' colspan="5" rowspan="3" style="padding: 15px; text-align: left;border-left: 1px solid black;border-right: 1px solid black;border-bottom: 1px solid black;border-top: 1px solid black;" class="text-left" ></td>
                              <td align='center' colspan="4" rowspan="3" style="padding: 0px; text-align: center;border-right: 1px solid black;border-bottom: 1px solid black;border-top: 1px solid black;" class="text-right" >
                                    <div style="width:100%;padding: 3px; font-size: 13px;">
                                          ราคารวมทั้งสิ้น (TOTAL)
                                        </div>
                                        <div style="width:100%;padding: 3px;font-size: 13px;">
                                          จำนวนภาษีมูลค่าเพิ่ม (VAT)
                                        </div>
                                        <div style="width:100%;padding: 3px; font-size: 13px;">
                                          รวมเงินทั้งสิ้น (TOTAL)
                                        </div>
                              </td>
                              <td align='center' colspan="2"  style="padding: 0px; text-align: right;border-right: 1px solid black;border-bottom: 1px solid black;border-top: 1px solid black;"> 
                                    <div style="width:100%;padding: 3px; font-size: 13px;" [ngStyle]="{'display':getnumpage(y.page)}">
                                          {{ salesbalance  | number:'1.2-2' }}
                                        </div>
                                        <div style="width:100%;padding: 3px; font-size: 13px;" [ngStyle]="{'display':getnumpage(y.page)}">
                                          {{sumtaxamount  | number:'1.2-2'}}
                                        </div>
                                        <div [ngStyle]="{'display':getnumpage(y.page)}" style="font-size: 13px;width:100%;padding: 3px; border-block-end: 1px solid; border-top: 1px solid;margin-bottom: 2px;">
                                          {{ Sumtotal  | number:'1.3-3' }} 
                                        </div>
                                        <div [ngStyle]="{'display':getnumpage(y.page)}" style="width:100%; border-top: 1px solid;">

                                    </div>
                              </td>
                       
                          </tr>              
                          
                          </tbody>
                      </table>-->
                      <div class="rcorners" style="width:100%;background-color:  #8b2323;margin-top: 3px; font-family:Segoe UI;font-size: 10pt; ">
                        <table width='100%' cellspacing='0' cellpadding='2' align='center' border='0' style='width:100%;'>
                          <thead>
                              <tr  style="color: #fff; background-color:  #8b2323;font-family:Segoe UI;font-size: 12pt;" >
                                  <td align='center' style="padding: 2px; border-right: 1px solid black; width: 45px;"class="text-center" >ลำดับ<br>Itme</td>
                                  <td align='center' style="padding: 2px;  width: 80px;" class="text-center">เลขที่ใบสั่ง<br>สินค้า<br>P.O.No.</td>
                                  <td align='center' style="padding: 2px; border-left: 1px solid black; width: 130px;" class="text-center" >รหัสสินค้า<br>Part Code</td>
                                  <td align='center' style="padding: 2px; border-left: 1px solid black;"class="text-center"  >รายการ<br>Description</td>
                               
                                  <td align='center' style="padding: 2px; border-left: 1px solid black; width: 70px;" class="text-center" >จำนวน<br>Quantity</td>
                                  <td align='center' style="padding: 2px; border-left: 1px solid black; width: 80px;"class="text-center"  >หน่วยละ<br>Unit Price</td>
                                  <td align='center' colspan="3" style="padding: 2px; border-left: 1px solid black;" class="text-center" >ส่วนลด<br>Disc %</td>
                                  <td align='center' style="padding: 2px; border-left: 1px solid black;"class="text-center"  >ราคาสุทธิ<br>ต่อชิ้น</td>
                                  <td align='center' style="padding: 2px; border-left: 1px solid black;"class="text-center"  >จำนวนเงิน<br>Amount</td>
                              </tr>
                            </thead>
                              <tbody style="background-color: #fff;font-family:Segoe UI;font-size: 10pt;">
                              <tr *ngFor="let x of y.data" style="color: rgb(0, 0, 0);background-color: #fff"  >
                                  <td align='center' style=" padding: 5px;padding: 5px; border-right: 1px solid black;" class="text-center" [ngStyle]="{'color':getColor(x.Number)}" >{{ x.Number }}</td>
                                  <td align='center' style="padding: 5px; border-right: 1px solid black;" class="text-center" >{{ x.PO }}</td>
                                  <td align='center' style="padding: 5px; border-right: 1px solid black; " class=" text-left" > {{ x.itemid }}</td>
                                  <td align='center' style="padding: 5px; border-right: 1px solid black; font-size: 9.8pt " class="text-left" >{{x.productname}}</td>
                                  <td align='center'style="padding: 5px; border-right: 1px solid black; " class="text-right" > {{ x.qtyline | number:'1.0-0'  }}</td>
                                  <td align='center'style="padding: 5px; border-right: 1px solid black; " class="text-right" > {{ x.salesprice | number:'1.2-2'  }}</td>
                                  <td align='center' style="padding: 5px; border-right: 1px solid black; width: 38px; " class="text-right" [ngStyle]="{'color':getColorivz(x.ivz_percent1_ct | number:'1.0-0')}" >{{ x.ivz_percent1_ct | number }}</td>
                                  <td align='center' style="padding: 5px; border-right: 1px solid black; width: 38px; " class="text-right"[ngStyle]="{'color':getColorivz(x.ivz_percent2_ct | number:'1.0-0')}" >{{ x.ivz_percent2_ct | number }}</td>
                                  <td align='center' style="padding: 5px; border-right: 1px solid black; width: 38px; " class="text-right"[ngStyle]="{'color':getColorivz(x.ivz_percent3_ct | number:'1.0-0')}" ><sup>{{ x.ivz_percent3_ct | number }}</sup></td>
                                  <td align='center'style="padding: 5px; border-right: 1px solid black; width: 90px; " class="text-right" > {{ x.totalTH | number:'1.3-3'  }}  </td>
                                  <td align='center'style="padding: 5px;  width: 90px;" class="text-right" >{{ x.lineamountmst | number:'1.2-2'}}</td>
               
                              </tr>
                             
                            <tr style="background-color: #fff">
                                  <td align='center'style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                  <td align='center' style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                  <td align='center' style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                  <td align='center' style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                  <td align='center'style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                  <td align='center'style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                  <td align='center' style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                  <td align='center' style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                  <td align='center' style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                  <td align='center'style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                  <td align='center'style="padding: 5px; " class="text-center" ></td>
                            </tr>
                            <tr style="background-color: #fff">
                                <td align='center'style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                <td align='center' style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                <td align='center' style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                <td align='center' style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                <td align='center'style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                <td align='center'style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                <td align='center' style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                <td align='center' style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                <td align='center' style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                <td align='center'style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                <td align='center'style="padding: 5px; " class="text-center" ></td>
                          </tr>
                          
                          
                          <tr style="color: rgb(0, 0, 0); height: 100px; " >
                             
                            <td align='center' valign="top" colspan="5" rowspan="3" style="padding: 15px; text-align: left;border-right: 1px solid black;border-top: 1px solid black;" class="text-left" >
                                <div  [ngStyle]="{'display':getnumpage(y.page)}" >({{ SumtotalTH }})</div> 
                            </td>
                            <td align='center' colspan="4" rowspan="3" style="padding: 0px; text-align: center;border-right: 1px solid black;border-top: 1px solid black;" class="text-right" >
                                  <div style="width:100%;padding: 3px; font-size: 13px;">
                                        ราคารวมทั้งสิ้น (TOTAL)
                                      </div>
                                      <div style="width:100%;padding: 3px;font-size: 13px;">
                                        จำนวนภาษีมูลค่าเพิ่ม (VAT)
                                      </div>
                                      <div style="width:100%;padding: 3px; font-size: 13px;">
                                        รวมเงินทั้งสิ้น (TOTAL)
                                      </div>
                                      <div [ngStyle]="{'display':getnumpage(y.page)}" style="color:#fff;width:100%;font-family: Tohama;font-size: 7pt;">
                                        55555
                              </div>
                            </td>
                            <td align='center' colspan="2"  style="padding: 0px; text-align: right;border-top: 1px solid black;"> 
                                  <div style="width:100%;padding: 3px; font-size: 13px;" [ngStyle]="{'display':getnumpage(y.page)}">
                                        {{ salesbalance  | number:'1.2-2' }}
                                      </div>
                                      <div style="width:100%;padding: 3px; font-size: 13px;" [ngStyle]="{'display':getnumpage(y.page)}">
                                        {{sumtaxamount  | number:'1.2-2'}}
                                      </div>
                                      <div [ngStyle]="{'display':getnumpage(y.page)}" style="font-size: 13px;width:100%;padding: 3px; border-block-end: 1px solid; border-top: 1px solid;margin-bottom: 2px;">
                                        {{ Sumtotal  | number:'1.2-2' }} 
                                      </div>
                                      <div [ngStyle]="{'display':getnumpage(y.page)}" style="color:#fff;width:100%; border-top: 1px solid black;font-family: Tohama;font-size: 7pt;">
                                            55555
                                  </div>
                            </td>
                     
                        </tr>              
                            
                            </tbody>
                        </table>
                    </div>
                    <div class="rcorners" style="width:100%;margin-top: 3px;text-align:center;"  [ngStyle]="{'margin-bottom':gettopDOMBtoom(y.page)}">
                        <table width='100%' cellspacing='0' cellpadding='2' align='center' style='width:100%;text-align:center;'>
                            <tbody>
                                <tr style="color: rgb(0, 0, 0);" >
                                    <td align='center' style="padding: 15px;  width: 40%; border-right: 1px solid black;font-family: Tohama;font-size: 11pt; " valign="top" >รับสินค้าดังรายการข้างบนไว้ถูกต้องในสภาพเรียบร้อยแล้ว<br>Received the above goods in good order & condition<br><br ><br ><br>.................................................................................<br>ลงนามและประทับตรา&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;ผู้รับของ<br><br><div valign="bottom">วันที่ DATE ............../............../..............</div></td>
                                    <td align='center' style="padding: 15px;  width: 30%;font-family: Tohama;font-size: 11pt; " valign="top" >ในนาม บริษัทบางบอนพลาสติค กรุ๊ปจำกัด<br><br><br><br ><br>........................................................................<br>ผู้นำส่ง DELIVERER<br><br><div valign="bottom" align='left' >ผิด ตก ยกเว้น</div></td>
                                    <td align='center' style="padding: 15px;  width: 30%;  border-left: 1px solid black;font-family: Tohama;font-size: 11pt; "><div valign="top" align='left' >ผู้ตรวจ</div><br><br><br><br><br><br ><br><div valign="bottom" align='right' >...........................................................  บันทึก</div> </td>
                                </tr>   
                            </tbody>
                        </table>
                    </div>
                </div>
              
                </td>
               
            </tr>   
        </table>
     </div>
      <!--VAt-->
</div>
</div>
