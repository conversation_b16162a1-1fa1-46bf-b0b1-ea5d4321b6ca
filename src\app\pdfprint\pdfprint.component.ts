import { Component, OnInit, ViewChild } from '@angular/core';
import { ElementRef} from '@angular/core';
import * as jspdf from 'jspdf';
import html2canvas from 'html2canvas';
import { HttpClient } from '@angular/common/http';
import { WebapiService } from '../webapi.service';
import { ActivatedRoute, Router } from '@angular/router';

export interface stApprovetest{
  idApprove : number;
  ApproveName : number;
}
export interface stApprovetest2{
  idApprove : number;
  ApproveName : number;
}
export interface testdata{
page:number;
data:any;
}




export interface DataLine {
  Number :string;
  PO :string;
  salesid	:string;
invoiceid	:string;
linenum	:string;
itemid	:string;
productname:string;
qtyline	:string;
salesprice :string;
ivz_percent1_ct :string;
ivz_percent2_ct :string;
ivz_percent3_ct:string;
salesunit	:string;
taxamount	:string;
totalline : string;
lineamountmst	:string;
totalTH:string;
}
export interface DataLine2 {
  Number :string;
  PO :string;
  salesid	:string;
invoiceid	:string;
linenum	:string;
itemid	:string;
productname:string;
qtyline	:string;
salesprice :string;
ivz_percent1_ct :string;
ivz_percent2_ct :string;
ivz_percent3_ct:string;
salesunit	:string;
taxamount	:string;
totalline : string;
lineamountmst	:string;
totalTH:string;
}

export interface Dataherder {
  name:string;
invoiceid	:string;
salesid	:string;
duedate	:string;
invoicedate	:string;
invoicingname	:string;
addressINV	:string;
addressDEL	:string;
telnumber	:string;
regnum : string;
taxgroup :string;
salesbalance : number;
sumtax : number;
invoiceamount : number;
paymenttype:string;
orderaccount:string;
nameINVherder:string;
}



@Component({
  selector: 'app-pdfprint',
  templateUrl: './pdfprint.component.html',
  styleUrls: ['./pdfprint.component.css']
})
export class PDFprintComponent implements OnInit {
  @ViewChild('content') content:ElementRef;
DataLine :DataLine[]=[];
dataprint:testdata[]=[];

Data:stApprovetest[]=[];
Data2:stApprovetest2[]=[];
num:number=0;
Num :any;


url:string;

DataHerderINV:Dataherder[]=[];
DataINV:DataLine2[]=[];
sumtaxamount:Number=0;
salesbalance:Number=0;
Sumtotal:Number=0;
SumtotalTH:string;
taxgroup:boolean;
Numpage:Number;
pageHeightmin =300;

idINV:string;
fromdate:string;
todate:string;
CodeSo:string;
Customer:string;
CodeInvoice:string;
Customerid:string;
Datatoback:string;
datalogin;


pacodesale;
pacustomer;
pabill;
page;
patxtcustomer;
taxnew=0;
//idINV=TI18-04472&fromdate=2019-2-1&todate=2019-2-15&pacodesale=1&pacustomer=%2520&pabill=&Datatoback=bill
//idINV=TI18-03489&fromdate=2017-10-16&todate=2018-10-16&CodeSo=1&Customer=%2520
  constructor(private http: HttpClient, private service: WebapiService, private route: ActivatedRoute,private router: Router,) { 
    localStorage.removeItem('DataSOderreview');
    localStorage.removeItem('DataSOderlist');
    this.idINV=this.route.snapshot.queryParams.idINV;
    this.fromdate=this.route.snapshot.queryParams.fromdate
    this.todate=this.route.snapshot.queryParams.todate
    this.CodeSo=this.route.snapshot.queryParams.CodeSo
    this.Customer=this.route.snapshot.queryParams.Customer
    this.CodeInvoice=this.route.snapshot.queryParams.paINV
    this.Customerid=this.route.snapshot.queryParams.idcustomer
    this.Datatoback=this.route.snapshot.queryParams.Datatoback
    this.page=this.route.snapshot.queryParams.page;
    this.pacodesale= this.route.snapshot.queryParams.pacodesale
    this.pabill= this.route.snapshot.queryParams.pabill
    this.pacustomer= this.route.snapshot.queryParams.pacustomer
    this.patxtcustomer=this.route.snapshot.queryParams.patxtcustomer
    this.url=service.geturlservice();
    if(this.idINV===undefined){
      alert('รูปแบบข้อมูลผิดพลาด');
       this.router.navigate(['/invoicecashlist'], { queryParams: { fromdate:this.fromdate,todate:this.todate,CodeSo:this.CodeSo,Customer:this.Customer,CodeINV:this.CodeInvoice,customerid:this.Customerid,datatoback:this.Datatoback } });
    }else{
      this.DataSearchINV();
    }
  /*  for(var i=1 ;i<=100;i++){
      this.Data.push({
        idApprove: i,
        ApproveName: i
      });
    }  this.GetData();*/

    this.datalogin=JSON.parse(sessionStorage.getItem('login'))
if (this.datalogin==null){
  this.router.navigate(['login']);
   }else{


   }
  }

  ngOnInit() {

  }

  getDue(country) :string { 
   
    let invdate = Number(new Date(country))
    var invdate7 =  invdate + 2592000000;
    var DateDue = new Date(invdate7)
    var strDateDue = DateDue.toString();
      return strDateDue;
  }
 

  DataSearchINV(){
    this.DataHerderINV=[]

    this.http.get<any>(this.url + 'PDF_invoice_herder/'+  this.idINV ).subscribe(res => {
      if(res.length < 1) {
         alert('ไม่พบข้อมูลส่วนหัว')
         this.router.navigate(['/invoicecashlist'], { queryParams: { fromdate:this.fromdate,todate:this.todate,CodeSo:this.CodeSo,Customer:this.Customer,CodeINV:this.CodeInvoice,customerid:this.Customerid,datatoback:this.Datatoback } });
      }else{
        for(var i=0;i<res.length;i++){
          var Dataitemid ='';
          var Dataitemname ='';
          var DataPO='';
          /*var datedue = this.getDue(res[i].invoicedate)
          alert(datedue);*/
          this.DataHerderINV.push({
            name:res[i].name,
            invoiceid	:res[i].invoiceid,
            salesid	:res[i].salesid,
            duedate	:res[i].duedate,
            invoicedate	:res[i].invoicedate,
            invoicingname	:res[i].invoicingname,
            addressINV	:res[i].addressINV,
            addressDEL	:res[i].addressDEL,
            telnumber	:res[i].telnumber,
            regnum : res[i].regnum,
            taxgroup:res[i].taxgroup,
            salesbalance :res[i].salesbalance,
            sumtax : res[i].sumtax,
            invoiceamount : res[i].invoiceamount,
            paymenttype:res[i].paymenttype,
            orderaccount:res[i].orderaccount,
            nameINVherder:res[i].nameInvoiceHeader
          })
          this.Sumtotal =res[i].invoiceamount
          this.sumtaxamount =res[i].sumtax
          this.salesbalance =res[i].salesbalance
      this.SearchINVData();
      this.gettaxgroup();
      }
    }
    },error=>{
      alert('เกิดปัญหาในการ Process ข้อมูล กรุณารายการใหม่อีกครั้ง');
    });
  }
  SearchINVData(){
    this.DataINV=[]
    this.http.get<any>(this.url + 'PDF_invoice/'+  this.idINV ).subscribe(res => {

 
      if(res.length < 1){
  
        alert('ไม่พบข้อมูลรายละเอียด invoice')
        this.router.navigate(['/invoicecashlist'], { queryParams: { fromdate:this.fromdate,todate:this.todate,CodeSo:this.CodeSo,Customer:this.Customer,CodeINV:this.CodeInvoice,customerid:this.Customerid,datatoback:this.Datatoback } });
      }else{
        for(var i=0;i<res.length;i++){
          var Dataitemid ='';
          var Dataitemname ='';
          var DataPO='';
          if(res[i].lineamountmst==0){
            DataPO='ของแถม'
          }
          this.DataINV.push({
            Number :(i+1).toString(),
            PO :DataPO,
             salesid : res[i].salesid,
            invoiceid	:res[i].invoiceid,
            linenum	:res[i].linenum,
            itemid	:res[i].itemid,
            productname:res[i].name,
            qtyline	:res[i].qtyline,
            salesprice :res[i].salesprice,
            ivz_percent1_ct :res[i].ivz_percent1_ct,
            ivz_percent2_ct :res[i].ivz_percent2_ct,
            ivz_percent3_ct:res[i].ivz_percent3_ct,
            salesunit	:res[i].salesunit,
            taxamount	:res[i].taxamount,
            totalline : res[i].totalline,
            lineamountmst	:res[i].lineamountmst,
            totalTH: res[i].totalTH2
          })   
        }
        this.GetData();
      }
      
    },error=>{
      alert('เกิดปัญหาในการ Process ข้อมูล กรุณารายการใหม่อีกครั้ง');
    });
  }
  
  gettaxgroup(){
    if(this.DataHerderINV[0].taxgroup==='DOM'){
      this.taxgroup=false; //แดงเลือดหมู

    }else{
      this.taxgroup=true; // เขียว
    }
  }

  gettopDOMBtoom(y){
    if(y!=this.Numpage){
      return '20px';
  }else{
    return '20px'
  }
  }

  gettopDOM(y,pageHeightmin){
    if(y==1){
      return '3px'
    }else if (y==this.Numpage && pageHeightmin==302){
      return '15px'
    }else{
      return '25px'
    }
  }

  gettop(v){
      if(v==302){
        return '13px'
      }else{
        return '30px'
      }
  }
  gettop2(v){
    if(v==302){
      return '60px'
    }else{
      return '76px'
    }
}
  public captureScreen()
  {
      /* alert(this.pageHeightmin)
   alert(JSON.stringify(this.dataprint))*/
    var data = document.getElementById('convert');
    html2canvas(data).then(canvas => {
      var imgWidth = 210; //208
      var pageHeight = this.pageHeightmin;  
      var imgHeight = canvas.height * imgWidth / canvas.width;
      var heightLeft = imgHeight;
      var date = new Date();
      var del = Math.floor(heightLeft/pageHeight)+1
      const contentDataURL = canvas.toDataURL('image/jpeg',1.0)
      let pdf = new jspdf('p', 'mm', 'a4');
      var position = 0;
      pdf.addImage(contentDataURL, 'JPEG', 0, position, imgWidth, imgHeight)
      heightLeft -= pageHeight;
  
      while (heightLeft >= 0) {
        position = heightLeft - imgHeight;
        pdf.addPage();
        pdf.addImage(contentDataURL, 'JPEG', 0, position, imgWidth, imgHeight);
        heightLeft -= pageHeight;
      }
      /*pdf.deletePage(del);*/
      
      pdf.save(this.idINV+'|dd-MM-yyyy :'+ date.getDate()+'-'+(date.getMonth()+1)+'-'+date.getFullYear() +'.PDF')
  }); 
  }
 /* GetData(){
    alert(this.DataINV.length)
    var x=this.DataINV.length;
    var y =x/18;
    var sum =Math.ceil(y);
    var a =0;
    var s=x%18;
   var g =18-s
    if(g>0){
  for (var i=0;i<g;i++){
    this.DataLine.push({
      salesid	:"11111",
      invoiceid	:"11111",
      linenum	:"11111",
      itemid	:"11111",
      qtyline	:"11111",
      salesprice :"11111",
      ivz_percent1_ct :"11111",
      ivz_percent2_ct :"11111",
      ivz_percent3_ct:"11111",
      salesunit	:"11111",
      taxamount	:"11111",
      lineamountmst	:"11111",
    });
  }
    }
    for(var z=0;z<sum;z++){
    for (var i=this.num;i<=17+this.num;i++){
        this.DataLine.push({
          salesid	: this.DataINV[i].salesid,
      invoiceid	:this.DataINV[i].invoiceid,
      linenum	:this.DataINV[i].linenum,
      itemid	:this.DataINV[i].itemid,
      qtyline	:this.DataINV[i].qtyline,
      salesprice :this.DataINV[i].salesprice,
      ivz_percent1_ct :this.DataINV[i].ivz_percent1_ct,
      ivz_percent2_ct :this.DataINV[i].ivz_percent2_ct,
      ivz_percent3_ct:this.DataINV[i].ivz_percent3_ct,
      salesunit	:this.DataINV[i].salesunit,
      taxamount	:this.DataINV[i].taxamount,
      lineamountmst	:this.DataINV[i].lineamountmst,
        })
      }
    
    }
    this.dataprint.push({
      page:a+1,
      data:this.DataLine
    })
    this.DataLine=[];
    this.num +=18;
    }*/
  
GetData(){
    var x=this.DataINV.length;
    var y =x/18;
    var sum =Math.ceil(y);
    var a =0;
    var s=x%18;
   var g =18-s
    if(g>0){
  for (var i=0;i<g;i++){
    this.DataINV.push({
      Number :"GG",
      PO:"",
      salesid : "",
     invoiceid	:"",
     linenum	:"",
     itemid	:"",
     productname:"",
     qtyline	:"",
     salesprice :"",
     ivz_percent1_ct :"",
     ivz_percent2_ct :"",
     ivz_percent3_ct:"",
     salesunit	:"",
     taxamount	:"",
     totalline :"",
     lineamountmst	:"",
     totalTH:""
   })   
  }
    }
    for(var z=0;z<sum;z++){
    for (var i=this.num;i<=17+this.num;i++){
    
      if(this.DataINV[i].salesid===undefined){
        this.DataLine.push({
          Number:"GG",
          PO:"",
          salesid : "",
          invoiceid	:"",
          linenum	:"",
          itemid	:"",
          productname:"",
          qtyline	:"",
          salesprice :"",
          ivz_percent1_ct :"",
          ivz_percent2_ct :"",
          ivz_percent3_ct:"",
          salesunit	:"",
          taxamount	:"",
          totalline :"",
          lineamountmst	:"",
          totalTH:""
        })
      } else {
        this.DataLine.push({
          Number :this.DataINV[i].Number,
          PO:this.DataINV[i].PO,
          salesid	: this.DataINV[i].salesid,
          invoiceid	:this.DataINV[i].invoiceid,
          linenum	:this.DataINV[i].linenum,
          itemid	:this.DataINV[i].itemid,
          productname:this.DataINV[i].productname,
          qtyline	:this.DataINV[i].qtyline,
          salesprice :this.DataINV[i].salesprice,
          ivz_percent1_ct :this.DataINV[i].ivz_percent1_ct,
          ivz_percent2_ct :this.DataINV[i].ivz_percent2_ct,
          ivz_percent3_ct:this.DataINV[i].ivz_percent3_ct,
          salesunit	:this.DataINV[i].salesunit,
          taxamount	:this.DataINV[i].taxamount,
          totalline : this.DataINV[i].totalline,
          lineamountmst	:this.DataINV[i].lineamountmst,
          totalTH:this.DataINV[i].totalTH
        })
      }
    
    }
    this.dataprint.push({
      page:z+1,
      data:this.DataLine
    })
    this.DataLine=[];
    this.num +=18;
    this.getsumtotalTH();
   
    } 
  }

  numpage (){
    this.Numpage=this.dataprint.length
  }

  getnumpage(Numpage){
    if(Numpage!=this.Numpage){
        return 'none';
    }
  }
  getsumtotalTH(){
    this.numpage();
    if(this.Sumtotal==0){
      this.SumtotalTH="ศูนย์บาทถ้วน"
    }else{
        
    
     // this.Sumtotal=100.11

       this.SumtotalTH = this.ArabicNumberToText(this.Sumtotal.toFixed(2).replace('-',''));
    }
  }

  toback(){
    if(this.page=='bill'){
     // idINV=TI18-04472&fromdate=2019-2-1&todate=2019-2-15&pacodesale=1&pacustomer=%2520&pabill=&Datatoback=bill
      this.router.navigate(['/invoicecreditlist'], { queryParams: { fromdate:this.fromdate,todate:this.todate,pacodesale:this.pacodesale,pacustomer:this.pacustomer,pabill:this.pabill,datatoback:this.Datatoback, patxtcustomer : this.patxtcustomer } });
    }else{
       this.router.navigate(['/invoicecashlist'], { queryParams: { fromdate:this.fromdate,todate:this.todate,CodeSo:this.CodeSo,Customer:this.Customer,CodeINV:this.CodeInvoice,customerid:this.Customerid,datatoback:this.Datatoback } });
    }
   

  }
  ArabicNumberToText(Number) :string
  {
    
    var Number = this.CheckNumber(Number);
    var NumberArray = new Array ("ศูนย์", "หนึ่ง", "สอง", "สาม", "สี่", "ห้า", "หก", "เจ็ด", "แปด", "เก้า", "สิบ");
    var DigitArray = new Array ("", "สิบ", "ร้อย", "พัน", "หมื่น", "แสน", "ล้าน");
    var BahtText = "";
    if (isNaN(Number))
    {
      return "ข้อมูลนำเข้าไม่ถูกต้อง";
    } else
    {
      if ((Number - 0) > 9999999.9999)
      {
        return "ข้อมูลนำเข้าเกินขอบเขตที่ตั้งไว้";
      } else
      {
        Number = Number.split (".");
        if (Number[1].length > 0)
        {
          Number[1] = Number[1].substring(0, 2);
        }
        var NumberLen = Number[0].length - 0;
        for(var i = 0; i < NumberLen; i++)
        {
          var tmp = Number[0].substring(i, i + 1) - 0;
          if (tmp != 0)
          {
            if ((i == (NumberLen - 1)) && (tmp == 1))
            {
              BahtText += "เอ็ด";
            } else
            if ((i == (NumberLen - 2)) && (tmp == 2))
            {
              BahtText += "ยี่";
            } else
            if ((i == (NumberLen - 2)) && (tmp == 1))
            {
              BahtText += "";
            } else
            {
           
              BahtText += NumberArray[tmp];
            }
            BahtText += DigitArray[NumberLen - i - 1];
          }
        
        }
        BahtText += "บาท";
        if ((Number[1] == "0") || (Number[1] == "00"))
        {
          BahtText += "ถ้วน";
        } else
        {
         var DecimalLen = Number[1].length - 0;
         var s=parseInt(Number[1]);
          for (var i = 0; i < DecimalLen; i++)
          {
            var tmp = Number[1].substring(i, i + 1) - 0;
            if (tmp != 0)
            {
              if ((i == (DecimalLen - 1)) && (tmp == 1) && s !=1)
              {
              //  alert('S'+s+'/'+i);
                BahtText += "เอ็ด";
              } else
              if ((i == (DecimalLen - 2)) && (tmp == 2))
              {
               // alert(2);
                BahtText += "ยี่";
              } else
              if ((i == (DecimalLen - 2)) && (tmp == 1))
              {
//alert(3);
                BahtText += "";
              } else
              {
              //  alert(4);
                BahtText += NumberArray[tmp];
              }
            //  alert(NumberArray[tmp]);
              BahtText += DigitArray[DecimalLen - i - 1];
            }
           
          }
          BahtText += "สตางค์";
        }
        return BahtText;
      }
    }
  }
  CheckNumber(Number){
    var decimal = false;
    Number = Number.toString();						
    Number = Number.replace (/ |,|บาท|฿/gi,'');  		
    for (var i = 0; i < Number.length; i++)
    {
      if(Number[i] =='.'){
        decimal = true;
      }
    }
    if(decimal == false){
      Number = Number+'.00';
    }
    return Number
  }



  getColor(country) { 

    switch (country) {
      case 'GG':
        return '#ffffff';
    }
  }
  getColorivz(country){ 

    switch (country) {
      case '0':
        return '#ffffff';
    }
  }
}
