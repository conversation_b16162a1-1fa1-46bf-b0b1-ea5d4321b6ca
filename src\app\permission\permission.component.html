<app-topmenu></app-topmenu>
<section style="padding-top:60px">
  <h5 class="p-sm-1 bg-secondary text-white text-center">User Permission</h5>
  <div class="container-fluid">       
      <div class="row">
          <div class="col-sm-3">
              <h5 class="p-sm-1 bg-secondary text-white font-weight-light">User Group</h5>
              <div class="btn-group-vertical list-group ">
                    <button *ngFor="let group of groupusers" (click)="getgroupid(group.id_group_user); " style="text-align: left; margin-top: 4px; padding: 10px;" type="button" class="btn btn-outline-success "> {{group.des_group}} </button>
                  </div>
              <!--<ul class="list-group ">
                  <li *ngFor="let group of groupusers" (click)="getgroupid(group.id_group_user); " class="list-group-item justify-content-between align-items-center">
                      {{group.des_group}} 
                  </li>
              </ul>-->
          </div>
          <div class="col-sm-9">
              <h5 class="p-sm-1 bg-secondary text-white font-weight-light">Permission Setting</h5>
              <table class="table table-striped"  >
                <thead><!-- [mfData]="data" #mf="mfDataTable"[mfRowsOnPage]="5"   -->
                <tr>
                    <th style="width: 40%">
                            Page/Menu
                        <!-- <mfDefaultSorter by="name">Page/Menu</mfDefaultSorter>-->
                    </th>
                    <th style="width: 20%" class="text-center">
                            View
                        <!-- <mfDefaultSorter by="email">View</mfDefaultSorter>-->
                    </th>
                    <th style="width: 20%" class="text-center">
                            Export
                        <!-- <mfDefaultSorter by="age">Export</mfDefaultSorter>-->
                    </th>
                    <th style="width: 20%" class="text-center">
                            Action
                       <!-- <mfDefaultSorter by="city" >Action</mfDefaultSorter>-->
                    </th>
                </tr>
                </thead>
                <tbody>
                <tr *ngFor="let item of menulist">
                    <td>{{item.des_menu}}</td>
                    <td class="text-center" ><input [checked]="item.flag_view"  [disabled]="ennablecheck" (click)="clickrowstableview(item.id_menu,$event.target.checked)"  type="checkbox"></td>
                    <td class="text-center"><input [checked]="item.flag_print" [disabled]="ennablecheck" (click)="clickrowstableexport(item.id_menu,$event.target.checked)"  type="checkbox" ></td>
                    <td class="text-center"><input [checked]="item.flag_action" [disabled]="ennablecheck" (click)="clickrowstablevaction(item.id_menu,$event.target.checked)"  type="checkbox" ></td>
                </tr>
                </tbody>
                <tfoot>
                <tr>
                    <td colspan="4">
                        <mfBootstrapPaginator [rowsOnPageSet]="[5,10,25]"></mfBootstrapPaginator>
                    </td>
                </tr>
                </tfoot>
            </table>
          </div>
      </div>
  </div>

  <div class="modal-footer h-auto">
     
  </div>
</section>
<section>
  <div class="modal fade" id="exampleModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
      <div class="modal-dialog" role="document">
          <div class="modal-content">
              <div class="modal-header">
                  <h5 class="modal-title" id="exampleModalLabel">User Info</h5>
                  <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                      <span aria-hidden="true">&times;</span>
                  </button>
              </div>
              <div class="modal-body">
                  <form>
                      <div class="form-group">
                          <input type="text" class="form-control form-control-sm" id="validationTooltip01" placeholder="User Name" required>
                      </div>
                      <div class="form-group">
                          <input type="text" class="form-control form-control-sm" id="validationTooltip01" placeholder="Email Address" required>
                      </div>
                      <div class="form-group">
                          <input type="text" class="form-control form-control-sm" id="validationTooltip01" placeholder="Mobile" required>
                      </div>
                      <div class="form-group">
                          <input type="text" class="form-control form-control-sm" id="validationTooltip01" placeholder="Log In Name" required>
                      </div>
                      <div class="form-group">
                          <input type="text" class="form-control form-control-sm" id="validationTooltip01" placeholder="Password" required>
                      </div>
                  </form>
              </div>
              <div class="modal-footer">
                  <button type="button" class="btn btn-secondary btn-sm" data-dismiss="modal">Close</button>
                  <button type="button" class="btn btn-success btn-sm">Apply</button>
              </div>
          </div>
      </div>
  </div>
</section>
<section>
  <div class="modal fade" id="exampleModal1" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
      <div class="modal-dialog" role="document">
          <div class="modal-content">
              <div class="modal-header">
                  <h5 class="modal-title" id="exampleModalLabel">Delete Data Confirmation</h5>
                  <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                      <span aria-hidden="true">&times;</span>
                  </button>
              </div>

              <div class="modal-footer fixed-bottom">
                  <button type="button" class="btn btn-secondary btn-sm" data-dismiss="modal">Close</button>
                  <button type="button" class="btn btn-danger btn-sm">Delete</button>
              </div>
          </div>
      </div>
  </div>
</section>
