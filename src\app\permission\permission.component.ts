import { HttpClient } from '@angular/common/http';
import { Component, OnInit } from '@angular/core';
import { WebapiService } from './../webapi.service';
// Removed deprecated imports


@Component({
  selector: 'app-permission',
  templateUrl: './permission.component.html',
  styleUrls: ['./permission.component.css']
})
export class PermissionComponent implements OnInit {
   groupusers: any[];
  savegroup: string;
menulist: any[];
permis: any[];
url: string;
idgroup: string;
countmenu: number;
tstese: string;
ennablecheck = true;
disabled = true;
  constructor(private http: HttpClient, private servive: WebapiService) {
    localStorage.removeItem('DataSOderreview');
    localStorage.removeItem('DataSOderlist');

    this.url = this.servive.geturlservice();
   }

  ngOnInit() {
    this.getmenulist();
  this.getusergroup();
   this.idgroup = '';
   this.tstese ='';

/*this.groupusers = this._sowebservice.getallgroup();*/
  }
  getusergroup() {
this.http.get<any[]>(this.url + 'usergroup').subscribe(res => {
  this.groupusers = res;
});

  }
  getmenulist() {
    this.http.get<any[]>('../../assets/menulist.json').subscribe(res => {
this.menulist = res;
this.countmenu = this.menulist.length;
/*alert(this.menulist.length);*/
    });
  }
  getgroupid(value) {
    this.idgroup = value;
    this.ennablecheck = false;
        this.http.post<any>(this.url + 'find_permission',{
          id_user_group : value
        }).subscribe(res => {
      if(res==''){
       this.getmenulist();
      }else{
        this.menulist=res;
      }

    });

  }
  testclick(vaule) {
    this.tstese = vaule;
  }
  clickrowstableview(value,checked){
    const urlpostview =`${ this.url }${ 'view_permission' }/${this.idgroup}/${value}/${checked}`;
this.http.post<any[]>(urlpostview,'').subscribe(res => {
  });
  }
  clickrowstableexport(value,checked){
    const urlpostview =`${ this.url }${ 'print_permission' }/${this.idgroup}/${value}/${checked}`;
    this.http.post<any[]>(urlpostview,'').subscribe(res => {
    });
      }
      clickrowstablevaction(value,checked){
        const urlpostview =`${ this.url }${ 'action_permission' }/${this.idgroup}/${value}/${checked}`;
        this.http.post<any[]>(urlpostview,'').subscribe(res => {
        });
          }

}
