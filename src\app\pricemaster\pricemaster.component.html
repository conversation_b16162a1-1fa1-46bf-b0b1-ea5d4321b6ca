<app-topmenu></app-topmenu>
   <!-- Page Content -->
   <div class="container-fluid"style="padding-right: 5px; padding-left: 5px;">
    <section style="padding-top:60px" >
      <h5 class="p-sm-1 bg-secondary text-white text-center">Price Master</h5>
      <div class="form-row">
        <div class="col-md-2 mb-2">
                <ng-template  #rt let-r="result" let-t="term">
                        <label>{{r.itemid}}</label>
                      </ng-template>
                        <input  id="typeahead-template" placeholder="รหัสสินค้า" type="text" class="form-control form-control-sm" [(ngModel)]="itemcode" name="itemcode" [ngbTypeahead]="search" [resultTemplate]="rt"
                          [inputFormatter]="formatter" />
        </div>
        <div class="col-md-2 mb-2">
                <ng-template  #rtname let-r="result" let-t="term">
                        <label>{{r.name}}</label>
                      </ng-template>
                        <input  id="typeahead-template" placeholder="ชื่อสินค้า" type="text" class="form-control form-control-sm" [(ngModel)]="itemname" name="itemname" [ngbTypeahead]="searchname" [resultTemplate]="rtname"
                          [inputFormatter]="formattername" />
        </div>
        <div class="col-md-2 mb-2">
            <input id="accountrelation" class="form-control form-control-sm" type="text" name="accountrelation" [(ngModel)]="accountrelation" placeholder="กลุ่มลูกค้า">
        </div>
        <div class="col-xs-12 col-12 col-md-2 form-group">
                <input type="text"
                       placeholder="DD/MM/YYYY"
                       class="form-control"
                       bsDatepicker
                       [bsConfig]="{ dateInputFormat: 'DD/MM/YYYY' }"
                       class="form-control form-control-sm"
                       [(ngModel)]="Datatodate">
              </div> 
       <!-- <div class="col-md-2 mb-2">
            <input id="todate" class="form-control form-control-sm" type="date" name="todate" [(ngModel)]="todate" placeholder="ถึงวันที่">
        </div>-->
        <div class="col-md-2 mb-3 col-12 text-center text-sm-center text-md-center text-lg-left" style="padding-right: 0px; padding-left: 0px;">
            <button [disabled]="searchbtn" style="width: 60px;" (click)="Searpricemaster()" class="btn btn-primary btn-sm font-weight-light" type="submit">Search</button>
            <button [disabled]="exportbtn" style="margin-left: 3px; width: 60px;" (click)="exportdataexcel()" class="btn btn-primary btn-sm font-weight-light" type="submit">Export</button>
            <ng-template #tipContent let-greeting="greeting"><b>{{name}}</b>!</ng-template>
            <button 
            [disabled]="searchbtn"
            [ngbTooltip]="tipContent"
            triggers="manual" #t2="ngbTooltip"
            (click)="syncdatapricemaster(t2)"
         style="margin-left: 3px; width: 60px;"
          class="btn btn-primary btn-sm font-weight-light">Import</button>
        </div>
      </div> 
      <table class="table table-hover table-bordered table-sm font-weight-light">
          <thead>
              <tr class="text-sm-center bg-light">
                  <th class="font-weight-normal" scope="col">ลำดับที่</th>
                  <th class="font-weight-normal"  scope="col">รหัสสินค้า</th>
                  <th class="font-weight-normal"  scope="col">ชื่อสินค้า</th>
                  <th class="font-weight-normal"  scope="col">จากวันที่</th>
                  <th class="font-weight-normal"  scope="col">ถึงวันที่</th>
                  <th class="font-weight-normal"  scope="col">ประเภทราคา</th>
                  <th class="font-weight-normal"  scope="col">กลุ่มลูกค้า</th>
                  <th class="font-weight-normal"  scope="col">ปริมาณ order</th>
                  <th class="font-weight-normal"  scope="col">ราคา</th>
                  <th class="font-weight-normal"  scope="col">ส่วนลด#1</th>
                  <th class="font-weight-normal"  scope="col">ส่วนลด#2</th>
                  <th class="font-weight-normal"  scope="col">ส่วนลด#3</th>
                  <th class="font-weight-normal"  scope="col">Upper ส่วนลด</th>
                  <th class="font-weight-normal"  scope="col"></th>
              </tr>
          </thead>
          <tbody>
              <tr *ngFor="let item of pricemasterlist; let i = index">
                  <th class="text-sm-center font-weight-normal">{{i+1}}</th>
                  <td class="text-sm-center font-weight-normal">{{item.itemid}}</td>
                  <td class="font-weight-normal">{{item.name}}</td>
                  <td class="text-sm-center font-weight-normal">{{item.fromdate | date:'dd/MM/yyyy'}}</td>
                  <td class="text-sm-center font-weight-normal">{{item.todate | date:'dd/MM/yyyy'}}</td>
                  <td class="text-sm-center font-weight-normal">{{item.accountcode}}</td>
                  <td class="text-sm-center font-weight-normal">{{item.accountrelation}}</td>
                  <td class="text-sm-center font-weight-normal">{{item.orderamount}}</td>
                  <td class="text-sm-right font-weight-normal">{{item.price}}</td>
                  <td class="text-sm-center font-weight-normal">{{item.percent1 | number:'1.2-2'}}%</td>
                  <td class="text-sm-center font-weight-normal">{{item.percent2 | number:'1.2-2'}}%</td>
                  <td class="text-sm-center font-weight-normal">{{item.percent3 | number:'1.2-2'}}%</td>
                  <td class="text-sm-center font-weight-normal">{{item.friendlyname | number:'1.2-2'}}</td>
                  <td class="text-sm-center font-weight-normal"> <button (click)="getdatadiscount(item.percent3,item.recid,item.friendlyname)"  class="btn btn-link font-weight-light" style="padding: 0pt" data-toggle="modal" data-target="#editdiscount3" aria-expanded="true"
                    aria-controls="collapseOne">
                    Edit
                </button></td>
              </tr>
          </tbody>
      </table>

      <div id="mdlSample" class="modal fade" role="dialog" [ngStyle]="{'display': mdlSampleIsOpen ? 'block' : 'none', 'opacity': 1}">
            <div class="modal-dialog modal-md">
            <div class="modal-content">
            <div class="modal-header colhaederal">
            <h4 class="modal-title">Report</h4>
            </div>
            <div class="modal-body">{{alt}}</div>
            <div class="modal-footer" align="right">
                        <button type="button" id="btnClose" class="btn btn-danger" (click)="closemodel(false)"><i class="fa fa-times fa-fw"></i> ปิด</button></div>
            </div>
            </div>
            </div>  



            <div class="modal fade" id="editdiscount3" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
                <div class="modal-dialog" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="exampleModalLabel">Edit Discount</h5>
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                <span aria-hidden="true">&times;</span>
                            </button>
                        </div>
                        <div class="modal-body">
                            <div class="form-group">
                                <label for="discountvalue"> แก้ไขส่วนลดที่ 3 {{recid}}</label>
                               <input class="form-control" [(ngModel)]="discountvalue"  type="text" name="discountvalue" id="discountvalue">
            
                            </div>

                            <div>
                                <label for="upperdis">แก้ไข Upper ส่วนลด</label>
                                <input class="form-control" [(ngModel)]="upperdis"  type="text" name="upperdis" id="upperdis">
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                            <button (click)="uodatepercenpromotion()" type="button" class="btn btn-primary" data-dismiss="modal">Update ส่วนลด</button>
                        </div>
                    </div>
                </div>
            </div>
          

</section>
</div>