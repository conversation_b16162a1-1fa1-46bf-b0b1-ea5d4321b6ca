import { HttpClient } from '@angular/common/http';
import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { NgbCalendar, NgbDateStruct } from '@ng-bootstrap/ng-bootstrap';
import { Angular5Csv } from 'angular5-csv/dist/Angular5-csv';
import { Observable } from 'rxjs';
import { debounceTime, map } from 'rxjs/operators';
import { WebapiService } from './../webapi.service';



@Component({
  selector: 'app-pricemaster',
  templateUrl: './pricemaster.component.html',
  styleUrls: ['./pricemaster.component.css']
})
export class PricemasterComponent implements OnInit {
  mdlSampleIsOpen : boolean = false;
  alt='';
  checkreload=true;

  discountvalue: number;

  options = {
    fieldSeparator: ',',
    quoteStrings: '"',
    decimalseparator: '.',
    showLabels: true,
    showTitle: true,
    useBom: true,
    noDownload: false,
    headers: ["รหัสสินค้า", "ชื่อสินค้า","จากวันที่","ถึงวันที่",'ประเภทราคา',"กลุ่มลูกค้า","ปริมาณ order","ราคา","ส่วนลด#1","ส่วนลด#2","ส่วนสด#3"]
  };
  url: string;
  pricemasterlist: any[];
  Itemrelation = '';
  nameprice = '';
  accountrelation = '';
  todate = '';
  reItemrelation = '';
  renameprice = '';
  reaccountrelation = '';
  retodate = '';
recid='';
setloadpricemaster:any;
Datatodate:any;
Datafromdate:any;
Name:any[]=[];
fromDate: NgbDateStruct;
toDate: NgbDateStruct;
permisstiondata:any[]=[];
  exportbtn=true;
searchbtn=true;
upperdis='';
itemcode:any;
itemname:any;
name:string;
productlistauto:any[]=[];
salegroup;
  constructor(private router: Router, private http: HttpClient, private service: WebapiService, private calendar: NgbCalendar) {
    localStorage.removeItem('DataSOderreview');
    localStorage.removeItem('DataSOderlist');
    this.name = 'กำลังนำเข้าข้อมูลกรุณารอสักครู่.....';
    this.url=service.geturlservice();
this.Name=JSON.parse(sessionStorage.getItem('login'))
    this.toDate = calendar.getToday();
    this.Datatodate= new Date(this.toDate.year,this.toDate.month-1,this.toDate.day);
   this.getdate();

    if (this.Name==null){
      this.router.navigate(['login']);
       }else{

        this.permisstiondata=JSON.parse(sessionStorage.getItem('menu'))
        this.salegroup=JSON.stringify(sessionStorage.getItem('salegroup'))
        this.exportbtn=!this.permisstiondata[2].flag_print;
        this.searchbtn=!this.permisstiondata[2].flag_action;

       }


   }
   search = (text$: Observable<any>) =>

   text$.pipe(
     debounceTime(200),
     map(term => term === '' ? []
       : this.productlistauto.filter(v => v.itemid.toLowerCase().indexOf(term.toLowerCase()) > -1).slice(0, 10))
   );
   formatter = (x: {itemid: string}) => x.itemid;

   searchname = (text$: Observable<any>) =>

   text$.pipe(
     debounceTime(200),
     map(term => term === '' ? []
       : this.productlistauto.filter(v => v.name.toLowerCase().indexOf(term.toLowerCase()) > -1).slice(0, 10))
   );
   formattername = (x: {name: string}) => x.name;
  ngOnInit() {
    this.getproductauto(this.salegroup);
  }
  toggleWithGreeting(tooltip, greeting: string) {
    if (tooltip.isOpen()) {
      tooltip.close();
    } else {
      tooltip.open({greeting});
    }
  }

  getproductauto(accountnum){
    this.http.get<any>(this.url + 'productauto/admin').subscribe(res => {
      if(res.length >0 ){
        this.productlistauto=res;
      }
    })
  }
  syncdatapricemaster(tool){
    this.toggleWithGreeting(tool,'');
    const Http = new XMLHttpRequest();
    const url='syncso/Service.asmx/PullingData?iFile=PriceMaster';
    Http.open("GET", url);
    Http.send();

    Http.onreadystatechange=(e)=>{
    if(Http.readyState==4 && Http.status==200){
      this.name='นำเข้าข้อมูล เสร็จสิ้น';
      if(confirm('นำเข้าข้อมูล เสร็จสิ้น')){
        this.toggleWithGreeting(tool,'');
      } else {
        this.toggleWithGreeting(tool,'');
      }
    }

    }
  }
  getdate(){

    this.todate = `${this.Datatodate.getFullYear()}-${this.Datatodate.getMonth()+1}-${this.Datatodate.getDate()}`;
   }
  Searpricemaster() {
    this.pricemasterlist
    this.reItemrelation = this.Itemrelation;
    this.renameprice = this.nameprice;
    this.reaccountrelation = this.accountrelation;
    this.getdate();
    var Itemrelation='';
    var nameprice='';
    var accountrelation='';
    if(this.itemcode==undefined || this.itemcode==''){

      this.Itemrelation='';
    } else{
      this.Itemrelation=this.itemcode.itemid;
    }

    if(this.itemname==undefined || this.itemname==''){
      this.nameprice=''
    } else {
      this.nameprice=this.itemname.itemid;
    }

    if(this.Itemrelation=='') {
      Itemrelation='';
    }else{
      Itemrelation=this.Itemrelation
    }
    if(this.nameprice=='') {
      nameprice='';
    }else{
      nameprice=this.nameprice
    }
    if(this.accountrelation=='') {
      accountrelation='';
    }else{
      accountrelation=this.accountrelation
    }
    if(this.todate=='') {
      this.todate=`${this.toDate}`;
    }
    this.pricemasterlist=[];
    var body={
      itemrelation:Itemrelation,
      name:nameprice,
      accountrelation:accountrelation,
      todate:this.todate,
      user:this.Name[0].salegroup
    };
this.http.post<any>(this.url + 'pricemaster',body).subscribe(res => {
if(res.length > 0) {
  this.retodate=this.todate;
this.pricemasterlist=res;
  if(Itemrelation==''){
    this.Itemrelation='';
  }
  if(nameprice==''){
    this.nameprice='';
  }
  if(accountrelation==''){
    this.accountrelation='';
  }
} else {
  if(Itemrelation==''){
    this.Itemrelation='';
  }
  if(nameprice==''){
    this.nameprice='';
  }
  if(accountrelation==''){
    this.accountrelation='';
  }

  this.openModal(true,'ไม่พบข้อมูลที่ค้นหา',false);
  this.pricemasterlist=[];
}
},error =>{
  this.openModal(true,'เกิดข้อผิดพลาด กรุณาลองใหม่อีกครั้ง',false);
});
  }

  Searpricemasterresear() {

    if(this.reItemrelation=='') {
this.reItemrelation='';
    }
    if(this.renameprice=='') {
      this.renameprice='';
    }
    if(this.reaccountrelation=='') {
      this.reaccountrelation='';
    }
    if(this.retodate=='') {
      this.retodate='';
    }
    this.pricemasterlist=[];

    var body={
      itemrelation:this.reItemrelation,
      name:this.renameprice,
      accountrelation:this.reaccountrelation,
      todate:this.retodate,
      user:this.Name[0].salegroup
    };
this.http.post<any>(this.url + 'pricemaster',body).subscribe(res => {
if(res.length > 0) {
this.pricemasterlist=res;

} else {

  this.pricemasterlist=[];
}
clearInterval(this.setloadpricemaster);
},error =>{

});
  }

  exportdataexcel() {
    if(this.pricemasterlist==undefined) {
      this.openModal(true,'ไม่พบข้อมูล',false);
    }else {
      new Angular5Csv(this.pricemasterlist, 'PriceMaster', this.options);
    }

  }
  getdatadiscount(value,recidvalue,upper){
    this.discountvalue=value;
    this.recid=recidvalue;
    this.upperdis=upper;
  }
  uodatepercenpromotion(){

    //'/node/apinano/api/updatepromotion/:numpercen/:recid'
    var urlpost=`${this.url}${'updatepromotion'}/${this.discountvalue}/${this.recid}/${this.upperdis}`;
    this.http.post(urlpost,'').subscribe(res =>{
if(res==true){
  this.openModal(true,'บันทึกข้อมูลเสร็จสิ้น',false);
  //this.setloadpricemaster=setInterval(()=> this.Searpricemasterresear(),200);
  this.Searpricemasterresear();
}
    });
  }
  openModal(open : boolean,text: string,load:boolean) : void {
    this.mdlSampleIsOpen = open;
    this.alt=text;
    this.checkreload=load;
  }
  closemodel(cl: boolean) {
  this.mdlSampleIsOpen=cl;
  if(this.checkreload==true) {
  }

  }
}
