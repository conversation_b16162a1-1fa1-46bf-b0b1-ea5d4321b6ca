      
      <div class="col-md-12 col-sm-12 col-lg-12 text-center" style="margin-top: 15px;" >
         <!-- <select  [(ngModel)]="pageHeightmin" class="custom-select custom-select-sm col-sm-2">
          <option disabled value="">--กรณีโหลด PDF แล้วเกินหน้า--</option>
          <option value="305">เกินหน้า</option>
          <option value="300">ค่าเริ่มต้น</option>
          <option value="295">ไม่เต็มหน้า</option>
          <option value="295">Galaxy Tab S3</option>
          <option value="290">I5 SE</option>
      </select> -->
      <div class="btn-group" role="group" aria-label="Basic example" style="z-index: 10;">
          <button  data-toggle="modal" data-target="#getaddress" aria-expanded="true" class="btn  btn-outline-info">เปลี่ยนชื่อลูกค้า</button>
        <button (click)="captureScreen()" type="button"  class="btn btn-outline-primary">โหลดPDF</button>
      <!-- <button (click)="tabletprint()" type="button" class="btn btn-outline-success">Tablet</button>-->
      
        <button (click)="blacktosaleoderlist()" class="btn btn-outline-danger"> Close</button>
      </div>
      
          <div class="col-md-12 col-sm-12 col-lg-12 text-center" style="z-index: 10; margin-bottom: 0px; margin-top: 3px;" >
              <div class="col-auto my-1">
                  <div class="custom-control custom-checkbox mr-sm-2">
                    <input type="checkbox" class="custom-control-input" [(ngModel)]="test"  (click)="loadform($event.target.checked)" id="customControlAutosizing">
                    <label class="custom-control-label" for="customControlAutosizing">แสดงน้ำหนักสินค้า</label>
                  </div>
                </div>
            </div>
      </div>
      <div class="col-md-12 col-sm-12 col-lg-12 text-center" style="z-index: 10; margin-bottom: 0px; margin-top: 3px;" >
        <select  [(ngModel)]="pageHeightmin" class="custom-select custom-select-sm col-sm-2 col-md-2 col-lg-2">
          <option disabled value="">--กรณีโหลด PDF แล้วเกินหน้า--</option>
          <option value="305">เกินหน้า</option>
          <option value="300">ค่าเริ่มต้น</option>
          <option value="295">ไม่เต็มหน้า</option>
          <option value="300">Computer</option>
          <option value="295">Notebook</option>
          <option value="290">Notebook ไม่เต็มหน้า</option>
      </select>
      </div>
    <!--[ngStyle]="{'margin-bottom': marbot}" [ngStyle]="{'margin-top': getnumpage(itemhead.numpage)}" -->
  
          <div class="container-fluid" align='center'>
              <div id="fontpage" style="width: 1100px; margin-top: 0px;"align='center'> 
                  <div style="padding-top: 1px; margin-bottom: 510px; width: 100%;" [ngStyle]="{'margin-top':getnumpage(itemhead.numpage),'margin-bottom':getnumpageMAX(itemhead.numpage)}"  *ngFor="let itemhead of pagepdf"  >
                      <div style="padding-left: 5px;padding-right: 5px" >           
                            <div class="row">
                            <div class="col-md-12 col-lg-12 col-sm-12">
                            <img src="assets/img/BGP.png" width="100%" style="padding-top: 5px; margin-top: 10px;">
                            <!-- <img style="margin-left: 10%" src="../../assets/img/logo.png"> -->
                            </div>     
                            </div>
                                    <div class="row">
                                      <div class="col-md-12 col-lg-12 col-sm-12">
                                    <table   style="margin-top: 1.5%; height: 235px; width: 100%;">
                                      <thead class="coltbtop coltbbot coltbleft coltbrigth">
                                        <th colspan="4"  class="text-center coltbtop coltbbot coltbleft coltbrigth" style="background-color: #e0e6e7">ใบเสนอราคา/QUOTATION</th>
                                      </thead>
                                      <tbody class="coltbbot"  style="border: 2px;" >
                                      <tr>
                                        <td class="coltbleft">  Company : {{getnamecustomer}}</td>
                                        
                                        <td width="300px" class="coltbbot coltbleft coltbrigth">เลขที่/ No. :{{this.salehderprint[0].id}}</td>
                                      </tr>
                                      <tr>
                                        <td class="coltbbot coltbleft coltbrigth"  rowspan="2"> Address : {{getaddresscustomer}}</td>
                                        
                                        <td  class=" coltbbot coltbrigth ">วันที่ Date :{{this.salehderprint[0].dateid}}</td>
                                      </tr>
                                      <tr>
                                        
                                      
                                        <td  class=" coltbbot coltbrigth  ">TEL. </td>
                                      </tr>
                                    
                                    </tbody>
                                    </table>
                                      </div>
                                    </div> 
                                      
                                    <div class="row">
                                      <div class="col-md-12 col-lg-12 col-sm-12 text-left">
                                        <label style="margin-top: 15px;" >ขอเสนอราคาและเงื่อนไขสำหรับท่านดังนี้</label><br>
                                        <label >We are please to submit you the following described here in at price, items and terms stated :</label>
                                      </div>
                                    </div>
                            <div class="col-md-12 col-lg-12 col-sm-12" style="padding-right: 0px;  padding-left: 0px;">
                            <table cellpadding="7"   style="margin-top: 1%;height: 295px; width: 100%;">
                                              <thead  style="border: 1; border-color: black" >
                                                <tr class="text-center coltbtop coltbleft  coltbrigth" >
                                                    <th style="width:53px; padding: 0px;" class="coltbrigth">ลำดับที่</th>
                                                    <th class="coltbrigth">รายการ</th>
                                                    <th style="width:50px;" class="coltbrigth">จำนวน</th>
                                                    <th *ngIf="test" width="5%" class="coltbrigth">น้ำหนัก</th>
                                                    <th class="coltbrigth">ราคา</th>
                                                    <th colspan="3" class="coltbrigth">ส่วนลด</th>
                                                    <th style="width:100px;" >จำนวนเงิน</th>
                                              
                                                </tr>
                                                <tr class="text-center coltbbot coltbleft coltbrigth">
                                                    <th style="width:53px; padding: 0px;" class="coltbrigth">ITEM</th>
                                                    <th class="coltbrigth">DESCRIPTION</th>
                                                    <th style="width:50px;" class="coltbrigth">Quantity</th>
                                                    <th *ngIf="test" width="5%" class="coltbrigth">Weight</th>
                                                    <th class="coltbrigth">Price</th>
                                                    <th colspan="3" class="coltbrigth">Discount</th>
                                                    <th>Amount</th>
                                                </tr>
                                                
                                                  
                                              </thead>
                                             
                                              
                                              
                                <tr *ngFor="let item of itemhead.datasale; let i=index" class="coltbleft coltbrigth">
                                <!-- <td scope="col" class="coltbrigth text-center">{{i+1}}</td>-->
                                <td [ngStyle]="{'color': getdisplayline(item.lineindex)}" scope="col" class="coltbrigth text-center">{{item.lineindex}}</td> 
                                <td scope="col"  class="coltbrigth text-left">{{item.ItemId}} {{item.Name}}</td>
                                <td scope="col" class="coltbrigth text-right">{{item.SalesQty}}</td>
                                <td scope="col" *ngIf="test" style="width:80px;" class="coltbrigth text-right">{{item.totalweight | number:'1.2-2'}}</td>
                                <td scope="col"  style="width:80px;" class="coltbrigth text-right">{{item.PriceUnit | number:'1.2-2'}}</td>
                                <td scope="col" style="width:65px;" class="coltbrigth text-right">{{item.IVZ_Percent1_CT}}</td>
                                <td scope="col" style="width:65px;" class="coltbrigth text-right">{{item.IVZ_Percent2_CT}}</td>
                                <td scope="col" style="width:65px;" class="coltbrigth text-right">{{item.IVZ_Percent3_CT}}</td>
                                <td scope="col" class="text-right">{{item.LineAmount | number:'1.2-2'}}</td>
                                </tr>         
                                         
                                <tr style="background-color: #fff">
                                    <td align='center'style="padding: 5px; border-right: 1px solid black;border-left: 1px solid black;" class="coltbrigth text-center" ></td>
                                    <td align='center' style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                    <td align='center' style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                    <td align='center' *ngIf="test" style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                    <td align='center' style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                    <td align='center'style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                    <td align='center'style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                    <td align='center' style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                    <td align='center' style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                   
                              </tr>
                              <tr style="background-color: #fff">
                                  <td align='center'style="padding: 5px; border-right: 1px solid black;border-left: 1px solid black;" class="coltbrigth text-center" ></td>
                                  <td align='center' style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                  <td align='center' style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                  <td align='center' *ngIf="test" style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                  <td align='center' style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                  <td align='center'style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>

                                  <td align='center'style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                  <td align='center' style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                  <td align='center' style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                 
                            </tr>
                            <tr style="background-color: #fff">
                                <td align='center'style="padding: 5px; border-right: 1px solid black;border-left: 1px solid black;" class="coltbrigth text-center" ></td>
                                <td align='center' style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                <td align='center' style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                <td align='center'*ngIf="test" style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                <td align='center' style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                <td align='center'style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                <td align='center'style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                <td align='center' style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                                <td align='center' style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                               
                          </tr>
                          <tr style="background-color: #fff">
                              <td align='center'style="padding: 5px; border-right: 1px solid black;border-left: 1px solid black;" class="coltbrigth text-center" ></td>
                              <td align='center' style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                              <td align='center' style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                              <td align='center'*ngIf="test" style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                              <td align='center' style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                              <td align='center'style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                              <td align='center'style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                              <td align='center' style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                              <td align='center' style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                             
                        </tr>
                        <tr style="background-color: #fff">
                            <td align='center'style="padding: 5px; border-right: 1px solid black;border-left: 1px solid black;" class="coltbrigth text-center" ></td>
                            <td align='center' style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                            <td align='center' style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                            <td align='center'*ngIf="test" style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                            <td align='center' style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                            <td align='center'style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                            <td align='center'style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                            <td align='center' style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                            <td align='center' style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                           
                      </tr>
                      <tr style="background-color: #fff">
                          <td align='center'style="padding: 5px; border-right: 1px solid black;border-left: 1px solid black;" class="coltbrigth text-center" ></td>
                          <td align='center' style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                          <td align='center' style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                          <td align='center'*ngIf="test" style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                          <td align='center' style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                          <td align='center'style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                          <td align='center'style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                          <td align='center' style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                          <td align='center' style="padding: 5px; border-right: 1px solid black;" class="text-center" ></td>
                         
                    </tr>
                    <tr style="background-color: #fff">
                        <td align='center'style="padding: 5px; border-bottom: 1px solid black; border-right: 1px solid black;border-left: 1px solid black;" class="coltbrigth text-center" ></td>
                        <td align='center' style="padding: 5px; border-bottom: 1px solid black; border-right: 1px solid black;" class="text-center" ></td>
                        <td align='center' style="padding: 5px; border-bottom: 1px solid black; border-right: 1px solid black;" class="text-center" ></td>
                        <td align='center'*ngIf="test" style="padding: 5px; border-bottom: 1px solid black; border-right: 1px solid black;" class="text-center" ></td>
                        <td align='center' style="padding: 5px; border-bottom: 1px solid black; border-right: 1px solid black;" class="text-center" ></td>
                        <td align='center'style="padding: 5px; border-bottom: 1px solid black; border-right: 1px solid black;" class="text-center" ></td>
                        <td align='center'style="padding: 5px; border-bottom: 1px solid black; border-right: 1px solid black;" class="text-center" ></td>
                        <td align='center' style="padding: 5px; border-bottom: 1px solid black; border-right: 1px solid black;" class="text-center" ></td>
                        <td align='center' style="padding: 5px; border-bottom: 1px solid black; border-right: 1px solid black;" class="text-center" ></td>
                       
                  </tr>
                                
                             
                           
                                <tbody class="coltbbot">
                                
                          <tfoot *ngIf="test"  class=" coltbbot  coltbrigth coltbleft">
                            <tr >
                                <td   class=" coltbbot coltbrigth " colspan="3" rowspan="2">หมายเหตุ*  <label>{{remark}}</label> </td>
                                <td [ngStyle]="{'color': getdisplayfoot(itemhead.closefoot)}" class="text-center  coltbbot coltbrigth"  colspan="2" rowspan="1">น้ำหนักรวม</td>
                                <td class="text-center  coltbbot coltbrigth"  colspan="3">ราคารวมทั้งสิ้น(TOTAL)</td>
                                <td [ngStyle]="{'color': getdisplayfoot(itemhead.closefoot)}"  class="text-right  coltbbot">{{ sumtotal | myCurrency}}</td>
                              </tr>
                              <tr >
                                  <td [ngStyle]="{'color': getdisplayfoot(itemhead.closefoot)}" class="text-right  coltbbot coltbrigth"   colspan="2" rowspan="1"> {{Totalweight | number:'1.2-2'}} KG.</td>
                                <td  class="text-center coltbbot coltbrigth" colspan="3">จำนวนภาษีมูลค่าเพิ่ม</td>
                                <td [ngStyle]="{'color': getdisplayfoot(itemhead.closefoot)}" class="text-right  coltbbot" ><span [ngStyle]="{'color': getdisplayvat(vat,itemhead.numpage)}" >0</span>{{vat | myCurrency}}</td>
                              </tr>
                              <tr >                                  
                                <td [ngStyle]="{'color': getdisplayfoot(itemhead.closefoot)}"  class="text-left  coltbbot coltbrigth"  colspan="5">{{pricethai}}</td>
                                <td  class="text-center  coltbrigth" colspan="3">รวมทั้งสิ้น(TOTAL)</td>
                              
                                <td  [ngStyle]="{'color': getdisplayfoot(itemhead.closefoot)}" class="text-right " >{{ total | myCurrency}}</td>
                              </tr>
                          </tfoot> 

                          <tfoot *ngIf="!test"  class=" coltbbot  coltbrigth coltbleft"> <!--ไม่แสดงน้ำหนัก-->
                              <tr >
                                  <td   class=" coltbbot coltbrigth " colspan="3" rowspan="2">หมายเหตุ* <label>{{remark}}</label> </td>
                                  
                                  <td class="text-center  coltbbot coltbrigth"  colspan="4">ราคารวมทั้งสิ้น(TOTAL)</td>
                                  <td [ngStyle]="{'color': getdisplayfoot(itemhead.closefoot)}"  class="text-right  coltbbot">{{ sumtotal | number:'1.2-2'}}</td>
                                </tr>
                                <tr >
                                
                                  <td   class="text-center coltbbot coltbrigth" colspan="4"><div *ngIf="salehderprint[0].vattype=='VAT'" >จำนวนภาษีมูลค่าเพิ่ม</div></td>
                                 <!-- <td [ngStyle]="{'color': getdisplayfoot(itemhead.closefoot)}" class="text-right  coltbbot" ><span [ngStyle]="{'color': getdisplayvat(vat,itemhead.numpage)}" >0</span>{{vat | myCurrency}}</td>-->
                                 <td [ngStyle]="{'color': getdisplayfoot(itemhead.closefoot)}" class="text-right  coltbbot" ><span [ngStyle]="{'color': getdisplayvat(vat,itemhead.numpage)}" >0</span>{{vat | myCurrency}} </td>
                                </tr>
                                <tr >
                                  <td [ngStyle]="{'color': getdisplayfoot(itemhead.closefoot)}"  class="text-left  coltbbot coltbrigth"  colspan="3">{{pricethai}}</td>
                                  
                                  <td  class="text-center  coltbrigth" colspan="4">รวมทั้งสิ้น(TOTAL)</td>
                                
                                  <td  [ngStyle]="{'color': getdisplayfoot(itemhead.closefoot)}" class="text-right " >{{total | number:'1.2-2'  }}</td>
                                </tr>
                            </tfoot> 
                        
                            </table>
                            <div  align='center' style=" margin-top: 47px;">
                                <div style="margin-top: 15px" class="col-12 col-md-12 col-lg-12 col-sm-12 form-inline">
                                  <div class="col-4 col-md-4 col-sm-4 col-lg-4" style="margin-bottom: 5px">
                                    <table style="width: 100%; border-right: 1px solid black; border-left: 1px solid black; border-top : 1px solid black;  border-bottom: 1px solid black;">
                                      <tbody>
                                        <tr>
                                          <td>
                                          <label class="text-center"> &nbsp; </label>
                                            <label class="text-center"> &nbsp;</label>
                                            <hr style="border-top: 1px solid #343a40; margin-top: -0.03rem;">
                                              <label style="margin-top: 35px" class="text-center" >Purchaser/ผู้อนุมัติซื้อ</label>
                                              <label style="margin-bottom: 25px"  class="text-center">Date____/____/____</label>
                                          </td>
                                        </tr>
                                      </tbody>
                                    </table>
                                  </div>
                                  <div class="col-4 col-md-4 col-sm-4 col-lg-4" style="margin-bottom: 5px">
                                    <table style=" width: 100%; border-right: 1px solid black; border-left: 1px solid black; border-top : 1px solid black;  border-bottom: 1px solid black; ">
                                      <tbody>
                                        <tr>
                                          <td>
                                          <p class="text-center" style="margin-bottom: 0px;">{{this.datalogin[0].name_user}}</p>
                                          <p class="text-center" style="margin-bottom: 0px;">Mobile : {{this.datalogin[0].mobile}}</p>
                                            <hr style="border-top: 1px solid #343a40; margin-top: -0.03rem;">
                                              <label style="margin-top: 35px"  class="text-center" >Sale/พนักงานขาย</label>
                                              <label style="margin-bottom: 25px"  class="text-center">Date____/____/____</label>
                                          </td>
                                        </tr>
                                      </tbody>
                                    </table>
                                  </div>
                                  <div class="col-4 col-md-4 col-sm-4 col-lg-4"  style="margin-bottom: 5px">
                                    <table style=" width: 100%; border-right: 1px solid black; border-left: 1px solid black; border-top : 1px solid black;  border-bottom: 1px solid black;" >
                                      <tbody>
                                        <tr>
                                          <td>
                                          <label class="text-center"> &nbsp;</label>
                                            <label class="text-center">&nbsp;</label>
                                         
                                              <hr style="border-top: 1px solid #343a40; margin-top: -0.03rem;">
                                              <label style="margin-top: 35px" class="text-center" >Manager/ผู้จัดการฝ่ายขาย</label>
                                              <label style="margin-bottom: 25px" class="text-center">Date____/____/____</label>
                                          </td>
                                        </tr>
                                      </tbody>
                                    </table>
                                  </div>
                                </div>
                              </div> 
                            </div>
           
            
                          
           
                      </div>         
                  </div>
             
                
              </div>

















              <div class="modal fade" id="getaddress" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
                  <div class="modal-dialog" role="document">
                      <div class="modal-content">
                          <div class="modal-header">
                              <h5 class="modal-title" id="exampleModalLabel">เปลี่ยนชื่อลูกค้า ที่ อยู่ลูกค้า <br> (เลขที่ : {{this.salehderprint[0].id}} ) </h5>
                              <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                  <span aria-hidden="true">&times;</span>
                              </button>
                          </div>
                          <div class="modal-body">
                           
                                 <div class="from-froup">
                                  <label for="getnamecustomer">ชื่อลูกค้า</label>
                                  <textarea [(ngModel)]="getnamecustomer" name="getnamecustomer" id="getnamecustomer" cols="20" rows="3" class="form-control"></textarea>
                                 </div>
                                 <div class="from-froup">
                                <label for="getaddresscustomer">ที่อยู่ลูกค้า</label>
                                <textarea  [(ngModel)]="getaddresscustomer" name="getaddresscustomer" id="getaddresscustomer" cols="30" rows="5" class="form-control" ></textarea>
                                  </div>
                              
                          </div>
                          <div class="modal-footer">
                              <button type="button" class="btn btn-secondary" data-dismiss="modal" >Close</button>
                              <button type="button" data-dismiss="modal" class="btn btn-primary">ตกลง</button>
                          </div>
                      </div>
                  </div>
                 </div>
          </div>

      
     