import { lineoder } from './../editsaloderreview/editsaloderreview.component';
import { Component, OnInit } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { ActivatedRoute, Router } from '@angular/router';
import { WebapiService } from '../webapi.service';
import html2canvas from 'html2canvas';
import * as jspdf from 'jspdf';
import { toInteger } from '@ng-bootstrap/ng-bootstrap/util/util';


export interface pagenum{
  numpage:number;
  datasale:any;
  closefoot:boolean;
}
export interface salelistline{
  lineindex:string;
  idline	:string;
id:string;
linenum		:string;
CurrencyCode:string;
CustAccount	:string;
CustGroup	:string;
ShippingDateRequested	:string;
ItemId	:string;
Name	:string;
SalesQty	:string;
SalesType	:string;
InventSiteId:string;
InventLocationId	:string;
PriceUnit	:string;
SalesUnit	:string;
SalesGroup:string;
SalesCategory	:string;
TaxItemGroup	:string;
TaxGroup:string;
CostPrice	:string;
DefaultDimension:string;
IVZ_Percent1_CT		:string;
IVZ_Percent2_CT		:string;
IVZ_Percent3_CT		:string;
BarCodeType	:string;
BarCode	:string;
LinePercent		:string;
totaldisc		:string;
LineAmount	:string;
SalesId	:string;
packqty	:string;
totalweight	:string;
disc1		:string;
disc2	:string;
disc3	:string;
pkggroup	:string;
checkpcs	:string;
state	:string;
stoderlisr:string;
disstate:string;
eidtable:string;
statest	:string;
}


@Component({
  selector: 'app-printsaleoderlist',
  templateUrl: './printsaleoderlist.component.html',
  styleUrls: ['./printsaleoderlist.component.css']
})

export class PrintsaleoderlistComponent implements OnInit {
  Maxpage:number;
  pageHeightmin =300;
  test=true;
  Totalweight:number;
  getnumpage(Numpage){
    if(Numpage==1){
        return '5px';
    }else{
      return '0px';
    }
  }
  getnumpageMAX(Numpage){
    this.numpage ()
    if(Numpage==this.Maxpage){
      return '0px';
    }else if(Numpage!=this.Maxpage && this.pageHeightmin ==295){
      return '63px';
    }else if(Numpage!=this.Maxpage && this.pageHeightmin ==290){
      return '63px';
    
    }else{
      return '50px';
    }
  }

  getdisplayline(value){
if(value==='W'){
return '#FFFFFF';
} else {
  return '#000000';
}
  }

  numpage (){
    this.Maxpage=this.pagepdf.length
  }

  getdisplayvat(value,value2){
    this.numpage()
      if(value == 0 && value2==this.Maxpage){
         return '#000000'
      } else if (value == 0 && value2 ==this.Maxpage) {
        return '#FFFFFF'
      } else if(value > 0 && value2 < this.Maxpage){
        return '#ffffff'
      } else if(value > 0 && value2 ==this.Maxpage){
        return '#ffffff'
      }   

      }

  getdisplayfoot(value){
    if(value==true){
      return '#000000';
    } else {
      return '#FFFFFF';
    }
      }
    
      marbot='400';
     // lineheight='0px'; //40
     // pageHeightmin =305;
      widthpage='auto'
 
salelistcut:salelistline[]=[];
pagepdf:pagenum[]=[];
  idsoedit:string;
  salehderprint:any[]=[];
  url:string;
  //pageHeightmin =300;
  salelistview:any[]=[];
  salelist:salelistline[]=[];
  total:number;
  vat:number;
  sumtotal:number;
  pricethai='';
  closefoottabel='none';
  getnamecustomer='';
  getaddresscustomer='';
  datalogin;
  remark='';
  constructor(private http:HttpClient,private route: ActivatedRoute,private router: Router,private service: WebapiService) { 
this.url=service.geturlservice();
    this.idsoedit=this.route.snapshot.queryParams.idso;
//alert('H='+window.screen.availHeight+' / W='+window.screen.availWidth);
this.datalogin=JSON.parse(sessionStorage.getItem('login'))

if (this.datalogin==null){
  this.router.navigate(['login']);
   }else{


   }

   //alert(this.datalogin[0].name_user+ '//'+this.datalogin[0].mobile)
  }

  ngOnInit() {
    this.loaddataprint(this.idsoedit);
    this.test=false;
  }
  loadform(ck){
    if(ck==true){
      this.test=true;
 
    }else{
      this.test=false;
     
    }
  }
  public captureScreen()
  {
      /* alert(this.pageHeightmin)
   alert(JSON.stringify(this.dataprint))*/
    var data = document.getElementById('fontpage');
    html2canvas(data).then(canvas => {
      var imgWidth = 210; //208
      var pageHeight = this.pageHeightmin;  
      var imgHeight = canvas.height * imgWidth / canvas.width;
      var heightLeft = imgHeight;
      var del = Math.floor(heightLeft/pageHeight)+1
      const contentDataURL = canvas.toDataURL('image/jpeg',1.0)
      let pdf = new jspdf('p', 'mm', 'a4');
      var position = 0;
      pdf.addImage(contentDataURL, 'JPEG', 0, position, imgWidth, imgHeight)
      heightLeft -= pageHeight;
  
      while (heightLeft >= 0) {
        position = heightLeft - imgHeight;
        pdf.addPage();
        pdf.addImage(contentDataURL, 'JPEG', 0, position, imgWidth, imgHeight);
        heightLeft -= pageHeight;
      }
      /*pdf.deletePage(del);*/
      pdf.save('SalesOder.PDF')
  }); 
  }

  pcprint(){
 
    this.pageHeightmin =300;
    this.marbot='35%';
   // this.lineheight='40px';
  
   
    this.widthpage='1500px';
    var imgWidth =(window.screen.availHeight/4)+10;
    //alert(imgWidth+'/'+window.screen.availHeight);
   
    var data = document.getElementById('fontpage');
    let pdf = new jspdf();
   html2canvas(data,{
    width:1500,
    height:6000 
   }).then(canvas => {
      //208
      var pageHeight = this.pageHeightmin;  
      var imgHeight = canvas.height * imgWidth / canvas.width;
      var test=(canvas.height * imgWidth)/canvas.width;
      var heightLeft = imgHeight;
      //var del = Math.floor(heightLeft/pageHeight)+1
      const contentDataURL = canvas.toDataURL('image/jpeg',1.0)
   
      var position = 0;
      pdf.addImage(contentDataURL, 'JPEG', 0, position, imgWidth, imgHeight)
      heightLeft -= pageHeight;
  //alert(position+'/'+imgWidth+'/'+imgHeight);
      if(this.pagepdf.length>1){ 
        position = heightLeft - imgHeight;
    
        pdf.addPage();
        pdf.addImage(contentDataURL, 'JPEG', 0, position, imgWidth, imgHeight);
        //heightLeft -= pageHeight;
        //alert('INIF'+position+'/'+imgWidth+'/'+imgHeight);
      }
      pdf.save('SalesOder.PDF');
      //pdf.deletePage(del);
    
  }); 
 
  }
  tabletprint(){
    this.marbot='50%';
  //  this.lineheight='40px';
    this.pageHeightmin =310;
    this.widthpage='1500px'
     /* alert(this.pageHeightmin)
   alert(JSON.stringify(this.dataprint))*/
   var data = document.getElementById('fontpage');
   html2canvas(data).then(canvas => {
     var imgWidth = 208 ; //208
     var pageHeight = this.pageHeightmin;  
     var imgHeight = canvas.height * imgWidth / canvas.width;
     var heightLeft = imgHeight;
    // var del = Math.floor(heightLeft/pageHeight)+1
     const contentDataURL = canvas.toDataURL('image/jpeg',1.0)
     let pdf = new jspdf('p', 'mm', 'a4');
     var position = 0;
     pdf.addImage(contentDataURL, 'JPEG', 0, position, imgWidth, imgHeight)
     heightLeft -= pageHeight;

     while (heightLeft >= 0) {
       position = heightLeft - imgHeight;
       pdf.addPage();
       pdf.addImage(contentDataURL, 'JPEG', 0, position, imgWidth, imgHeight);
       
       heightLeft -= pageHeight;
     }
     /*pdf.deletePage(del);*/
     pdf.save('SalesOder.PDF')
 }); 
  }

  mobileprint(){
    
  }
  loaddataprint(value){
    this.getaddresscustomer='';
    this.getnamecustomer='';
      this.http.get<any>(this.url+'find_saleheader/'+value).subscribe(res=>{
        //alert(res[0].remark);
 this.remark=res[0].remark;
        this.Maxpage= res.length;
    this.salehderprint=res;
    this.getsaloderlist(value);
    this.getnamecustomer=res[0].InvName;
    if(res[0].address!=null || res[0].address!=undefined){
      this.getaddresscustomer=res[0].address[0];
    } else {
      this.getaddresscustomer='';
    }
    this.total=res[0].amount;

this.vat=parseFloat(((res[0].amount/1.07)*0.07).toFixed(2));
if(res[0].TaxGroup==='NoVAT' || (res[0].vattype=="VAT ไม่ยื่น" && res[0].TaxGroup=="DOM") ){
this.vat=0;
}
//var sunmix=this.vat+res[0].amount
var sunmix=res[0].amount-this.vat;
this.sumtotal=parseFloat(sunmix.toFixed(2));
    //alert(JSON.stringify(res));
    this.pricethai=this.ArabicNumberToText(this.total.toFixed(2));
  
    
      },error=>{
        //return;
      });
  }
  getsaloderlist(valueid){
    var numfor=0;
    var allline=18;
    var salelinenum=0;
    var linefor=0;
    var countpage=1;
    var ssline=0;
    var numemty=0;
    this.salelistview=[];
    this.salelist=[];
        this.http.get<any>(this.url+'find_saleline/'+valueid).subscribe(res =>{
      if(res.length>0){
        this.Totalweight =0;
        for(var i =0; i< res.length; i++){
          this.Totalweight += res[i].totalweight;
        }
        var numline=1;
        ssline=res.length%18;
        if(res.length>18){
          countpage=Math.ceil(res.length/18);
        }
     numemty=18-ssline;
        for(var i=0;i<res.length;i++){
        this.salelist.push({
          lineindex:numline.toString(),
          idline:res[i].idline,
          id:res[i].id,
          linenum:res[i].linenum,
          CurrencyCode:res[i].CurrencyCode,
          CustAccount:res[i].CustAccount,
          CustGroup:res[i].CustGroup,
          ShippingDateRequested	:res[i].ShippingDateRequested,
          ItemId:res[i].ItemId,
          Name:res[i].Name,
          SalesQty:res[i].SalesQty,
          SalesType:res[i].SalesType,
          InventSiteId:res[i].InventSiteId,
          InventLocationId:res[i].InventLocationId,
          PriceUnit:res[i].PriceUnit,
          SalesUnit:res[i].SalesUnit,
          SalesGroup:res[i].SalesGroup,
          SalesCategory:res[i].SalesCategory,
          TaxItemGroup:res[i].TaxItemGroup,
          TaxGroup:res[i].TaxGroup,
          CostPrice:res[i].CostPrice,
          DefaultDimension:res[i].DefaultDimension,
          IVZ_Percent1_CT:res[i].IVZ_Percent1_CT+'%',
          IVZ_Percent2_CT:res[i].IVZ_Percent2_CT+'%',
          IVZ_Percent3_CT:res[i].IVZ_Percent3_CT+'%',
          BarCodeType:res[i].BarCodeType,
          BarCode:res[i].BarCode,
          LinePercent:res[i].LinePercent,
          totaldisc:res[i].totaldisc,
          LineAmount:res[i].LineAmount,
          SalesId:res[i].SalesId,
          packqty:res[i].packqty,
          totalweight:res[i].totalweight,
          disc1:res[i].disc1,
          disc2:res[i].disc2,
          disc3:res[i].disc3,
          pkggroup:res[i].pkggroup,
          checkpcs:res[i].checkpcs,
          state:res[i].state,
          stoderlisr:res[i].stoderlisr,
          disstate:res[i].disstate,
          eidtable:res[i].eidtable,
          statest:res[i].statest
        });
     
        numline++;
        }
        for(var x=0;x<numemty;x++){
          this.salelist.push({
            lineindex:'W',
            idline:'',
            id:'',
            linenum:'',
            CurrencyCode:'',
            CustAccount:'',
            CustGroup:'',
            ShippingDateRequested	:'',
            ItemId:'',
            Name:'',
            SalesQty:'',
            SalesType:'',
            InventSiteId:'',
            InventLocationId:'',
            PriceUnit:'',
            SalesUnit:'',
            SalesGroup:'',
            SalesCategory:'',
            TaxItemGroup:'',
            TaxGroup:'',
            CostPrice:'',
            DefaultDimension:'',
            IVZ_Percent1_CT:'',
            IVZ_Percent2_CT:'',
            IVZ_Percent3_CT:'',
            BarCodeType:'',
            BarCode:'',
            LinePercent:'',
            totaldisc:'',
            LineAmount:'',
            SalesId:'',
            packqty:'',
            totalweight:'',
            disc1:'',
            disc2:'',
            disc3:'',
            pkggroup:'',
            checkpcs:'',
            state:'',
            stoderlisr:'',
            disstate:'',
            eidtable:'',
            statest:''
          });
        }
 
    var numin=0;
    var numli=18;
  
 for (var p=0;p<countpage;p++){

for(var z=numin;z<numli;z++){
 
this.salelistcut.push({
  lineindex: this.salelist[z].lineindex,
  idline:this.salelist[z].idline,
  id:this.salelist[z].id,
  linenum:this.salelist[z].linenum,
  CurrencyCode:this.salelist[z].CurrencyCode,
  CustAccount:this.salelist[z].CustAccount,
  CustGroup:this.salelist[z].CustGroup,
  ShippingDateRequested	:this.salelist[z].ShippingDateRequested,
  ItemId:this.salelist[z].ItemId,
  Name:this.salelist[z].Name,
  SalesQty:this.salelist[z].SalesQty,
  SalesType:this.salelist[z].SalesType,
  InventSiteId:this.salelist[z].InventSiteId,
  InventLocationId:this.salelist[z].InventLocationId,
  PriceUnit:this.salelist[z].PriceUnit,
  SalesUnit:this.salelist[z].SalesUnit,
  SalesGroup:this.salelist[z].SalesGroup,
  SalesCategory:this.salelist[z].SalesCategory,
  TaxItemGroup:this.salelist[z].TaxItemGroup,
  TaxGroup:this.salelist[z].TaxGroup,
  CostPrice:this.salelist[z].CostPrice,
  DefaultDimension:this.salelist[z].DefaultDimension,
  IVZ_Percent1_CT:this.salelist[z].IVZ_Percent1_CT,
  IVZ_Percent2_CT:this.salelist[z].IVZ_Percent2_CT,
  IVZ_Percent3_CT:this.salelist[z].IVZ_Percent3_CT,
  BarCodeType:this.salelist[z].BarCodeType,
  BarCode:this.salelist[z].BarCode,
  LinePercent:this.salelist[z].LinePercent,
  totaldisc:this.salelist[z].totaldisc,
  LineAmount:this.salelist[z].LineAmount,
  SalesId:this.salelist[z].SalesId,
  packqty:this.salelist[z].packqty,
  totalweight:this.salelist[z].totalweight,
  disc1:this.salelist[z].disc1,
  disc2:this.salelist[z].disc2,
  disc3:this.salelist[z].disc3,
  pkggroup:this.salelist[z].pkggroup,
  checkpcs:this.salelist[z].checkpcs,
  state:this.salelist[z].state,
  stoderlisr:this.salelist[z].stoderlisr,
  disstate:this.salelist[z].disstate,
  eidtable:this.salelist[z].eidtable,
  statest:this.salelist[z].statest
});

}
numin+=18;
numli+=18;

          this.pagepdf.push({
            numpage:p+1,
            datasale:this.salelistcut,
            closefoot:false
          });
    
          this.salelistcut=[];
        } 
 //alert(JSON.stringify(this.pagepdf));
      }
      this.pagepdf[this.pagepdf.length-1].closefoot=true;
    })
  }

  blacktosaleoderlist(){
    this.router.navigate(['solist1']);
  }
  ThaiNumberToText(Number)
  {
    Number = Number.replace (/๐/gi,'0');  
    Number = Number.replace (/๑/gi,'1');  
    Number = Number.replace (/๒/gi,'2');
    Number = Number.replace (/๓/gi,'3');
    Number = Number.replace (/๔/gi,'4');
    Number = Number.replace (/๕/gi,'5');
    Number = Number.replace (/๖/gi,'6');
    Number = Number.replace (/๗/gi,'7');
    Number = Number.replace (/๘/gi,'8');
    Number = Number.replace (/๙/gi,'9');
    return 	this.ArabicNumberToText(Number);
  }
  
   ArabicNumberToText(Number) :string
  {
    var Number = this.CheckNumber(Number);
    var NumberArray = new Array ("ศูนย์", "หนึ่ง", "สอง", "สาม", "สี่", "ห้า", "หก", "เจ็ด", "แปด", "เก้า", "สิบ");
    var DigitArray = new Array ("", "สิบ", "ร้อย", "พัน", "หมื่น", "แสน", "ล้าน");
    var BahtText = "";
    if (isNaN(Number))
    {
      return "ข้อมูลนำเข้าไม่ถูกต้อง";
    } else
    {
      if ((Number - 0) > 9999999.9999)
      {
        return "ข้อมูลนำเข้าเกินขอบเขตที่ตั้งไว้";
      } else
      {
        Number = Number.split (".");
        if (Number[1].length > 0)
        {
          Number[1] = Number[1].substring(0, 2);
        }
        var NumberLen = Number[0].length - 0;
        for(var i = 0; i < NumberLen; i++)
        {
          var tmp = Number[0].substring(i, i + 1) - 0;
          if (tmp != 0)
          {
            if ((i == (NumberLen - 1)) && (tmp == 1))
            {
              BahtText += "เอ็ด";
            } else
            if ((i == (NumberLen - 2)) && (tmp == 2))
            {
              BahtText += "ยี่";
            } else
            if ((i == (NumberLen - 2)) && (tmp == 1))
            {
              BahtText += "";
            } else
            {
              BahtText += NumberArray[tmp];
            }
            BahtText += DigitArray[NumberLen - i - 1];
          }
        }
        BahtText += "บาท";
        if ((Number[1] == "0") || (Number[1] == "00"))
        {
          BahtText += "ถ้วน";
        } else
        {
         var DecimalLen = Number[1].length - 0;
         var s=parseInt(Number[1]);
          for (var i = 0; i < DecimalLen; i++)
          {
            var tmp = Number[1].substring(i, i + 1) - 0;
            if (tmp != 0)
            {
              if ((i == (DecimalLen - 1)) && (tmp == 1) && s !=1 )
              {
                BahtText += "เอ็ด";
              } else
              if ((i == (DecimalLen - 2)) && (tmp == 2))
              {
                BahtText += "ยี่";
              } else
              if ((i == (DecimalLen - 2)) && (tmp == 1))
              {
                BahtText += "";
              } else
              {
                BahtText += NumberArray[tmp];
              }
              BahtText += DigitArray[DecimalLen - i - 1];
            }
          }
          BahtText += "สตางค์";
        }
        return BahtText;
      }
    }
  }
  
   CheckNumber(Number){
    var decimal = false;
    Number = Number.toString();						
    Number = Number.replace (/ |,|บาท|฿/gi,'');  		
    for (var i = 0; i < Number.length; i++)
    {
      if(Number[i] =='.'){
        decimal = true;
      }
    }
    if(decimal == false){
      Number = Number+'.00';
    }
    return Number
  }
}
