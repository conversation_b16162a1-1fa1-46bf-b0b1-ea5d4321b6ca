import { MasterpackageComponent } from './../masterpackage/masterpackage.component';
import { WebapiService } from './../webapi.service';
import { HttpClient } from '@angular/common/http';
import { Component, OnInit } from '@angular/core';
import {debounceTime, map} from 'rxjs/operators';
import {Observable} from 'rxjs';
import { Router } from '../../../node_modules/@angular/router';
export interface productdetail {
  inventlocationid: string;
  itemgroupid: string;
  itemid: string;
  catname: string;
  name: string;
  price: number;
  taxpackagingqty: number;
  unitid: string;
  packaginggroupid: string;
  netweight: string;
}

export interface dataexcelfile{
  Itemnumber: string;
  Productname: string;
  pack: string;
  page: string;
}
@Component({
  selector: 'app-productgroup',
  templateUrl: './productgroup.component.html',
  styleUrls: ['./productgroup.component.css']
})

export class ProductgroupComponent implements OnInit {
  mdlSampleIsOpen : boolean = false;
  alt='';
  checkreload=true;



urlApi = 'http://119.59.112.478:1433';
url='';
idgrouping:number;
itemidedit='';
editproductgroup='';
model: any;
itemid='';
productgroup='';
productlist: any[];
productgrouplist: any[]=[];
productgrouplistse:any[]=[];
arrayBuffer:any;
file:File;
numcheckid=0;
setchecknum:any;
/*wb:Workbook = new Workbook();*/
dataimport: dataexcelfile[]=[];
permisstiondata:any[]=[];
exportbtn=true;
searchbtn=true;
  constructor(private http: HttpClient, private service: WebapiService,private router: Router) { 
    localStorage.removeItem('DataSOderreview');
    localStorage.removeItem('DataSOderlist');
this.url=service.geturlservice();
this.permisstiondata=JSON.parse(sessionStorage.getItem('menu'))


if (this.permisstiondata==null){
  this.router.navigate(['login']);
   }else{

    this.exportbtn=!this.permisstiondata[11].flag_print;
    this.searchbtn=!this.permisstiondata[11].flag_action;

   }

  }
  search = (text$: Observable<any>) =>

text$.pipe(
  debounceTime(200),
  map(term => term === '' ? []
    : this.productlist.filter(v => v.itemid.toLowerCase().indexOf(term.toLowerCase()) > -1).slice(0, 10))
);
formatter = (x: {itemid: string}) => x.itemid;
  ngOnInit() {
    this.http.get<any>(this.url + 'productauto/admin').subscribe(res => {
      if(res.length >0 ){
        this.productlist=res;
        this.getdataproductgroup();
      }
    })
  }

checkaitemid(valueid:string,page:string){
if(valueid !=undefined){
  
}
}
searchproductgroup(value:string){
  if(value.length>0){
    this.productgrouplistse=this.productgrouplist.filter(v => v.itemid.toLowerCase().indexOf(value.toLowerCase()) > -1).slice(0, 50);
  } else {
    this.productgrouplistse=this.productgrouplist;
  }
}
addproductgroup() {

  if(this.model==undefined){
  this.openModal(true,'กรุณาป้อนรหัสสิ้นค้า',false);
  } else if(this.productgroup=='') {
    this.openModal(true,'กรุณาป้อนกลุ่มสินค้า',false);
  } else {
    var item;
    var st='';
    if(this.model.itemid==undefined){
item=this.model;
    } else{
item=this.model.itemid;
    }
     st=item.toString();
   this.http.get<any>(this.url+'checkitemidtran/'+st.toUpperCase()+'/G').subscribe(res =>{
    if(res[0].num>=1){
   this.openModal(true,'รหัสสินค้า'+st.toUpperCase()+'มีในระบบแล้ว',false)
   
    } else {
      var urlpost=`${ this.url }/${ 'add_product_group'}/${ st.toUpperCase() }/${ this.productgroup }`;
      this.http.post(urlpost,'').subscribe(res =>{
        if(res==true){
          this.getdataproductgroup()
          this.openModal(true,'บันทึกข้อมูลเสร็จสิ้น',true);
          this.productgroup='';
          this.model=[];
        }
  
      });
    }
 
    });
      
   
  }
  
 
}
/*getexcel(){
  this.wb.xlsx.readFile("./e.xlsx").then(function(){
    //sheet object
    let sheet:Worksheet = this.wb.getWorksheet("Sheet1");

    let totalRowsIncludingEmptyRows:number = sheet.rowCount
    console.log("total number of rows : "+totalRowsIncludingEmptyRows)
    let emptyCell = sheet.getRow(1).getCell(2).value
    console.log("Empty cell's value : "+emptyCell)
    let valueCell = sheet.getRow(2).getCell(2).value
    console.log("Value cell's value : "+valueCell)
    let nullCell = sheet.getRow(3).getCell(2).value
    console.log("null cell's value : "+nullCell)
  });
}*/
geteditgrouping(id,itemid,numgroup) {
  this.idgrouping=id;
  this.itemidedit=itemid;
  this.editproductgroup=numgroup;
}

updategrouping(){
  var idcheck;
  if(this.model==undefined) {
    idcheck=this.itemidedit;
  }else {
    idcheck=this.model.itemid;

  }
  var urlpost=`${ this.url }/${ 'update_product_group' }/${ this.idgrouping }/${ idcheck }/${this.editproductgroup}`
  this.http.post(urlpost,'').subscribe(res =>{
    if(res==true) {
      this.getdataproductgroup()
      this.openModal(true,'บันทึกข้อมูลเสร็จสิ้น',true);
     
    }
  })
}
getdataproductgroup() {
  
  this.http.get<any>(this.url+'get_product_group').subscribe(res=> {
    if(res.length >0) {
      this.productgrouplist=res;
      this.productgrouplistse=this.productgrouplist;
    }
  })
}

deletegrouping() {
  var idcheck;
  if(this.model==undefined) {
    idcheck=this.itemidedit;
  }else {
    idcheck=this.model.itemid;

  }

  var urlpost=`${this.url}${'deletepackproduct'}/${idcheck}/${'G'}`;
  this.http.post(urlpost,'').subscribe(res => {
    if(res==true) {
      this.getdataproductgroup()
this.openModal(true,'ลบข้อมูลเสร็จสิ้น',true);
    }
  })
}
openModal(open : boolean,text: string,load:boolean) : void {
  this.mdlSampleIsOpen = open;
  this.alt=text;
  this.checkreload=load;
}
closemodel(cl: boolean) {
this.mdlSampleIsOpen=false;
if(this.checkreload==true) {
this.router.navigate(['productgroup']);
this.getdataproductgroup();
}

}

}
