import { HttpClient } from '@angular/common/http';
import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { Angular5Csv } from 'angular5-csv/dist/Angular5-csv';
import { Observable } from 'rxjs';
import { debounceTime, map } from 'rxjs/operators';
import { WebapiService } from '../webapi.service';


@Component({
  selector: 'app-productlist',
  templateUrl: './productlist.component.html',
  styleUrls: ['./productlist.component.css']
})
export class ProductlistComponent implements OnInit {
  mdlSampleIsOpen : boolean = false;
  alt='';
  checkreload=true;




   options = {
    fieldSeparator: ',',
    quoteStrings: '"',
    decimalseparator: '.',
    showLabels: true,
    showTitle: true,
    useBom: true,
    noDownload: false,
    headers: ["คลัง", "รหัสกลุ่มสินค้า","รหัสสินค้า","กลุ่มสินค้า","ชื่อสินค้า","ราคาตั้ง","จำนวนต่อ Pack","หน่วยนับ","หน่วยของแพ็ค","น้ำหนัก"]
  };
  productlist: any[];
  url: string;
  Inventlocationid ='';
  itemgroupid ='';
  itemid ='';
  catname ='';
  nameproduct ='';
  productlistauto:any[]=[];
  permisstiondata:any[]=[];
  exportbtn=true;
searchbtn=true;
itemcode:any;
itemname:any;
name:string;
salegroup;
  constructor(private router: Router,private http: HttpClient, private service: WebapiService) {
    localStorage.removeItem('DataSOderreview');
    localStorage.removeItem('DataSOderlist');
    this.name = 'กำลังนำเข้าข้อมูลกรุณารอสักครู่.....';
    this.url=service.geturlservice();
    /*this.permisstiondata=JSON.parse(sessionStorage.getItem('menu'))
    this.salegroup=JSON.stringify(sessionStorage.getItem('salegroup'))
    this.exportbtn=!this.permisstiondata[1].flag_print;
    this.searchbtn=!this.permisstiondata[1].flag_action;*/
    this.salegroup=JSON.stringify(sessionStorage.getItem('salegroup'))
    this.permisstiondata=JSON.parse(sessionStorage.getItem('menu'))
    if (this.permisstiondata==null){
      this.router.navigate(['login']);
       }else{

        this.permisstiondata=JSON.parse(sessionStorage.getItem('menu'))
        this.salegroup=JSON.stringify(sessionStorage.getItem('salegroup'))
        this.exportbtn=!this.permisstiondata[1].flag_print;
        this.searchbtn=!this.permisstiondata[1].flag_action;

       }

  }
  search = (text$: Observable<any>) =>

  text$.pipe(
    debounceTime(200),
    map(term => term === '' ? []
      : this.productlistauto.filter(v => v.itemid.toLowerCase().indexOf(term.toLowerCase()) > -1).slice(0, 10))
  );
  formatter = (x: {itemid: string}) => x.itemid;

  searchname = (text$: Observable<any>) =>

  text$.pipe(
    debounceTime(200),
    map(term => term === '' ? []
      : this.productlistauto.filter(v => v.name.toLowerCase().indexOf(term.toLowerCase()) > -1).slice(0, 10))
  );
  formattername = (x: {name: string}) => x.name;
  ngOnInit() {

    this.getproductauto(this.salegroup);
  }

getproductauto(accountnum){
  this.http.get<any>(this.url + 'productauto/admin').subscribe(res => {
    if(res.length >0 ){
      this.productlistauto=res;
    }
  })
}
  toggleWithGreeting(tooltip, greeting: string) {
    if (tooltip.isOpen()) {
      tooltip.close();
    } else {
      tooltip.open({greeting});
    }
  }

  syncdataproduct(tool){

    this.toggleWithGreeting(tool,'');
    const Http = new XMLHttpRequest();
    const url='syncso/Service.asmx/PullingData?iFile=Product';
    Http.open("GET", url);
    Http.send();

    Http.onreadystatechange=(e)=>{
    if(Http.readyState==4 && Http.status==200){
      this.name='นำเข้าข้อมูล เสร็จสิ้น';
      if(confirm('นำเข้าข้อมูล เสร็จสิ้น')){
        this.toggleWithGreeting(tool,'');
      } else {
        this.toggleWithGreeting(tool,'');
      }
    }

    }
  }

  Searchporductlist() {
    this.productlist=[];
    var Inventlocationid='';
    var itemgroupid='';
    var itemid='';
    var catname='';
    var nameproduct='';

    if(this.itemcode==undefined || this.itemcode==''){

      this.itemid='';
    } else{
      this.itemid=this.itemcode.itemid;
    }

    if(this.itemname==undefined || this.itemname==''){
      this.nameproduct=''
    } else {
      this.nameproduct=this.itemname.itemid;
    }
    if (this.Inventlocationid==''){
      Inventlocationid='%20';
    }else{
      Inventlocationid=this.Inventlocationid
    }
    if(this.itemgroupid==''){
      itemgroupid='%20';
    }else{
      itemgroupid=this.itemgroupid
    }
    if(this.itemid==''){
      itemid='%20';
    }else{
      itemid=this.itemid
    }
    if(this.catname=='') {
      catname='%20';
    }else{
      catname=this.catname
    }
    if(this.nameproduct=='') {
      nameproduct='%20';
    }else{
      nameproduct=this.nameproduct
    }
    // var inventlocationid = req.body.inventlocationid;
    // var itemgroupid = req.body.itemgroupid; +  + '/' +  + '/' +  + '/' +  + '/' +
    // var itemid = req.body.itemid;
    // var catname = req.body.catname;
    // var name = req.body.name;
    var body={
      inventlocationid:(Inventlocationid=='%20'?'':Inventlocationid),
      itemgroupid:(itemgroupid=='%20'?'':itemgroupid),
      itemid:(itemid=='%20'?'':itemid),
      catname:(catname=='%20'?'':catname),
      name:(nameproduct=='%20'?'':nameproduct)
    }
    this.http.post<any>(this.url + 'product_list',body)
    .subscribe(res => {
if(res.length >0) {
this.productlist=res;
    if(Inventlocationid=='%20'){
      this.Inventlocationid='';
    }
    if(itemgroupid=='%20'){
      this.itemgroupid='';
    }
    if(itemid=='%20'){
      this.itemid='';
    }
    if(catname=='%20'){
      this.catname='';
    }
    if(nameproduct=='%20'){
      this.nameproduct='';
    }

} else {
  if(Inventlocationid=='%20'){
    this.Inventlocationid='';
  }
  if(itemgroupid=='%20'){
    this.itemgroupid='';
  }
  if(itemid=='%20'){
    this.itemid='';
  }
  if(catname=='%20'){
    this.catname='';
  }
  if(nameproduct=='%20'){
    this.nameproduct='';
  }
  this.openModal(true,'ไม่พบข้อมูลที่ค้นหา',false);
}
    });
  }

  exportdataexcel() {
   if(this.productlist==undefined){
    this.openModal(true,'ไม่พบข้อมูล',false);
   }
    new Angular5Csv(this.productlist, 'ProduceList', this.options);
  }
  openModal(open : boolean,text: string,load:boolean) : void {
    this.mdlSampleIsOpen = open;
    this.alt=text;
    this.checkreload=load;
  }
  closemodel(cl: boolean) {
  this.mdlSampleIsOpen=cl;
  if(this.checkreload==true) {
    location.reload();
  }

  }

}
