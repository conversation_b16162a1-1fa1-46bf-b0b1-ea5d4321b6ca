  
  <app-topmenu></app-topmenu><!-- Page Content -->
  <div class="container-fluid" style="padding-right: 5px; padding-left: 5px;">
    <section style="padding-top:60px" >
      <h5 class="p-sm-1 bg-secondary text-white text-center">Sale Stock</h5>
      <div class="form-row">
        <!-- <div class="col-md-2 mb-2">
            <input id="Inventlocationid" class="form-control form-control-sm" type="text" name="Inventlocationid" [(ngModel)]="Inventlocationid" placeholder="ค้นหาคลัง">
        </div> -->
        <!-- <div class="col-md-2 mb-2">
            <input id="itemgroupid" class="form-control form-control-sm" type="text" name="itemgroupid" [(ngModel)]="itemgroupid"  placeholder="ค้นหารหัสกลุ่มสินค้า">
        </div> -->
        <div class="col-md-2 mb-2">
                <!-- <ng-template  #rt let-r="result" let-t="term">
                      <label (click)="FilterProduct()" >{{r.itemid}}</label>
                      </ng-template> -->
                        <input  id="typeahead-template" (keyup)="FilterProduct()"  placeholder="รหัสสินค้า" type="text" class="form-control form-control-sm" [(ngModel)]="itemcode" name="itemcode" 
                           />
        </div>
        <!-- <div class="col-md-2 mb-2">
            <input id="catname" class="form-control form-control-sm" type="text" name="catname" [(ngModel)]="catname" placeholder="ค้นหากลุ่มสินค้า">
        </div> -->
        <div class="col-md-2 mb-2">
                <!-- <ng-template  #rtname let-r="result" let-t="term">
                        <label>{{r.name}}</label>
                      </ng-template> -->
                        <input  id="typeahead-template" placeholder="ชื่อสินค้า" (keyup)="FilterProductName()" type="text" class="form-control form-control-sm" [(ngModel)]="itemname" name="itemname" 
                          />
        </div>
        <div class="col-md-2 mb-2">
          <select multiple [(ngModel)]="groupitem" (change)="FilterProductcategory()"  class="custom-select custom-select-sm" >
            <option selected value="0">--เลือกรายการทั้งหมด--</option>
            <option *ngFor="let item of ProductCategoryList" [value]="item.name">
                   {{item.name}}
            </option>
        </select>
  </div>
        <div class="col-md-2 mb-3 col-12 text-center text-sm-center text-md-center text-lg-left" style="padding-right: 0px; padding-left: 0px;">
            <!-- <button [disabled]="searchbtn" style="width: 60px;" (click)="Searchporductlist()" class="btn btn-primary btn-sm font-weight-light" type="submit">Search</button> -->
            <!-- <button [disabled]="exportbtn" style="margin-left: 3px; width: 60px;"  (click)="exportdataexcel()" class="btn btn-primary btn-sm font-weight-light" type="submit">Export</button>
            <ng-template #tipContent let-greeting="greeting"><b>{{name}}</b>!</ng-template>
            <button 
        [disabled]="searchbtn"
        [ngbTooltip]="tipContent"
        triggers="manual" #t2="ngbTooltip"
        (click)="syncdataproduct(t2)"
     style="margin-left: 3px; width: 60px;"
      class="btn btn-primary btn-sm font-weight-light">Import</button> -->
     
        </div>
      </div> 
      <table class="table table-hover table-bordered table-sm">
          <thead>
              <tr class="text-sm-center font-weight-light bg-light">
                  <th scope="col" class="font-weight-normal">ลำดับ</th>
                  <!-- <th scope="col" class="font-weight-normal">คลัง</th> -->
                  <!-- <th scope="col" class="font-weight-normal">กลุ่มสินค้า</th> -->
                 <th scope="col" class="font-weight-normal">รหัสสินค้า</th>
                  <th scope="col" class="font-weight-normal">ชื่อสินค้า</th>
                 <!-- <th scope="col" class="font-weight-light">ราคาตั้ง</th> -->
                  <th scope="col" class="font-weight-normal">AVASTOCK</th>
                  <th scope="col" class="font-weight-normal">ORDERED</th>
                  <th scope="col" class="font-weight-normal">PHYSTOCK</th>
                  <th scope="col" class="font-weight-normal">PO</th>
                  <th scope="col" class="font-weight-normal">PROD</th>
                  <th scope="col" class="font-weight-normal">RESERVE</th>
                       <!-- <th scope="col" class="font-weight-light"></th> -->
              </tr>
          </thead>
          <tbody>
              <tr *ngFor="let item of productlist; let i = index">
                  <th class="text-sm-center font-weight-normal ">{{i+1}}</th>
                  <!-- <td class="text-sm-center font-weight-normal">{{item.inventlocationid}}</td> -->
                  <!-- <td class="text-sm-center font-weight-normal">{{item.catname}}</td> -->
                  <td class="text-sm-center font-weight-normal">{{item.itemid}}</td>
                  <td class="text-sm-center font-weight-normal" >{{item.name}}</td>
                <!--  <td class="text-sm-right">{{item.price  | number:'1.2-2'}}</td>-->
                  <td class="text-sm-center font-weight-normal">{{item.avastock | number:'1.2-2'}}</td>
                  <td class="text-sm-center font-weight-normal">{{item.ordered | number:'1.2-2'}}</td>
                  <td class="text-sm-center font-weight-normal">{{item.phystock | number:'1.2-2'}}</td>
                  <td class="text-sm-center font-weight-normal">{{item.po | number:'1.2-2'}}</td>
                  <td class="text-sm-center font-weight-normal">{{item.prod | number:'1.2-2'}}</td>
                  <td class="text-sm-center font-weight-normal">{{item.reserve | number:'1.2-2'}}</td>
                  <!-- <td class="text-sm-center font-weight-normal">{{item.category}}({{item.itemgroupid}})</td> -->
              </tr>
             
          </tbody>
      </table>
      <div id="mdlSample" class="modal fade" role="dialog" [ngStyle]="{'display': mdlSampleIsOpen ? 'block' : 'none', 'opacity': 1}">
            <div class="modal-dialog modal-md">
            <div class="modal-content">
            <div class="modal-header colhaederal">
            <h4 class="modal-title">Report</h4>
            </div>
            <div class="modal-body">{{alt}}</div>
            <div class="modal-footer" align="right">
                        <button type="button" id="btnClose" class="btn btn-danger" (click)="closemodel(false)"><i class="fa fa-times fa-fw"></i> ปิด</button></div>
            </div>
            </div>
            </div>
</section>
 </div>