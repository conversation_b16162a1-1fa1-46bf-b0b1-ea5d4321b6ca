     
               <div class="modal-header" >
                      <h4 class="modal-title">ยินดีต้อนรับเข้าสู่ระบบสมาชิก NaNo Product</h4>
                     <!-- <button type="button" class="close" aria-label="Close" (click)="activeModal.dismiss('Cross click')">
                        <span aria-hidden="true">&times;</span>
                      </button>-->
                    </div>
                    <div class="modal-body">
                      <p> {{Datanameuser}} </p>
                      <div [formGroup]="registrationFormGroup" >
                              <div class="form-group">
                                  
                                      <div class="form-group ">
                                      <label for="email">Email : </label>
                                      <label class="bg-alert" *ngIf="registrationFormGroup.controls.email.errors?.pattern && registrationFormGroup.controls.email.touched"> *format Email Error</label>
                                      <input  type="text" [(ngModel)]="email" class="form-control" pattern="^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\.[a-zA-Z0-9.]+$" name="email" placeholder="Email" formControlName="email"/>
            
                                      </div>
                                      <label for="Tel">Mobile : </label>
                                      <label class="bg-alert" *ngIf="registrationFormGroup.controls.Tel.errors?.pattern && registrationFormGroup.controls.Tel.touched"> *format Mobile Error</label>
                                      <input type="text" [(ngModel)]="Tel" class="form-control" pattern="^((\\+91-?)|0)?[0-9]{10}$" name="Tel" placeholder="0801234567" formControlName="Tel"/>
                              </div>
                              <div class="form-group" style=" margin-bottom: 5px">
                                  <div [formGroup]="passwordFormGroup">
                                      <label for="Tel">Username : </label>
                                          <input type="text" value="{{DataIdcustomer}}" class="form-control" readonly/>
                                          <label for="password">Password : </label>
                                          <label class="bg-alert" *ngIf="passwordFormGroup.controls.password.errors && passwordFormGroup.controls.password.touched"  >*can be 7 - 15 characters long. </label>
                                         
                                          <label class="bg-alert" *ngIf="passwordFormGroup.controls.password.errors?.required && passwordFormGroup.controls.password.touched"> *Password is required.</label>
                                          <input class="form-control" required type="password" name="password" placeholder="Enter Password can be 7-15 characters long." formControlName="password">
            
                                          <label for="repeatPassword">Repeat Password : </label>
                                          <label class="bg-alert" *ngIf="passwordFormGroup.controls.repeatPassword.errors && passwordFormGroup.controls.repeatPassword.touched"  >*can be 7 - 15 characters long. </label>
                                          <label class="bg-alert" *ngIf="passwordFormGroup.controls.repeatPassword.errors?.required && passwordFormGroup.controls.repeatPassword.touched"> *Repeat password is required.</label>
                                          <input class="form-control" required type="password" name="repeatPassword" placeholder="Enter RepeatPassword can be 7-15 characters long."  formControlName="repeatPassword">       
                                  </div> 
                              </div>
                         
                              <p class="alert  bg-danger" *ngIf="passwordFormGroup.errors?.doesMatchPassword">Password does not match</p>
                      </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-outline-primary" (click)="activeModal.close()">Close</button>
                        <button type="button" class="btn btn-outline-primary" [disabled]="!registrationFormGroup.valid" (click)="onClickRegisterCustomer()" >Save changes</button>
                    </div>
