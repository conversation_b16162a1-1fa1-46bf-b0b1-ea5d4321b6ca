import { Component, OnInit, Input } from '@angular/core';
import { NgbActiveModal, NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { WebapiService } from '../webapi.service';
import { FormControl, FormGroup, Validators, FormBuilder } from '@angular/forms';
import { ModalDismissReasons } from '@ng-bootstrap/ng-bootstrap';
import { Router } from '@angular/router';
import { HttpClient } from '@angular/common/http';

@Component({
  selector: 'app-so',
  templateUrl: './so.component.html',
  styleUrls: ['./so.component.css']
})

export class SoComponent implements OnInit {
  registrationFormGroup: FormGroup;
  passwordFormGroup: FormGroup;
  data1:string
  Data:any;
  Datanameuser:string;
  DataIduser:string;
  DataIdcustomer:string;
  closeResult: string;
  name_user: string;
  selectedValuesaleman: string;
  selectedValuecustomer:string;
  selectedValue: string;
  email: string;
  staticAlertClosed = true;
  mobile: string;
  username: string;
  repeatPassword: string;
  password: string;
  Tel: string;

  datauser : string;
  dataEmail : string;
  datatel : string;
  urlserver :string;
  constructor( private http: HttpClient, public activeModal: NgbActiveModal,private trou: Router, private modalService: NgbModal,private service: WebapiService, private formBuilder: FormBuilder) {
   this.Data=service.setuserlogin();
   this.urlserver = service.geturlservice();
   this.Datanameuser=this.Data[0].name_user;
   this.DataIduser=this.Data[0].id_user;
   this.DataIdcustomer=this.Data[0].accountnum;
   
   this.passwordFormGroup = this.formBuilder.group({
    password: new FormControl('', [Validators.required, Validators.minLength(7), Validators.maxLength(15)]),
    
    repeatPassword: new FormControl('', [Validators.required, Validators.minLength(7), Validators.maxLength(15)])
  },{
    validator: RegistrationValidator.validate.bind(this)
  });


  this.registrationFormGroup = this.formBuilder.group({
    Tel: [],
    email: [],
    passwordFormGroup: this.passwordFormGroup
  });

   }

  ngOnInit() {
  }

  test(){
   alert(JSON.stringify(this.Data));
  }
  onClickRegisterCustomer(){
    this.datauser=this.registrationFormGroup.value.edusername;
      this.dataEmail=this.registrationFormGroup.value.email;
      this.datatel=this.registrationFormGroup.value.Tel;
      const datapassword=this.passwordFormGroup.value.password;
      this.Datanameuser=this.Data[0].name_user;
      this.DataIduser=this.Data[0].id_user;
      this.DataIdcustomer=this.Data[0].accountnum;
      if(this.dataEmail=='' || this.dataEmail===undefined){
        this.dataEmail='%20'
      }
      if(this.datatel=='' || this.datatel===undefined){
        this.datatel='%20'
      }
     const url = `${ this.urlserver }update_user_customer_1/${  this.DataIduser }/${ this.dataEmail }/${ this.datatel }/${ datapassword }/${ this.DataIdcustomer }`;
          this.http.post(url, '').subscribe((res) => {
                        if (res === true) {
                          alert('บันทึกรายการเสร็จสิ้น')
                            this.activeModal.close()
                          }
                      },error=>{
                        alert('เกิดปัญหาในการ Process ข้อมูล กรุณาทำรายการใหม่อีกครั้ง');
                      })
   
  }
}
export class RegistrationValidator {
  static validate(registrationFormGroup: FormGroup) {
      let password = registrationFormGroup.controls.password.value;
      let repeatPassword = registrationFormGroup.controls.repeatPassword.value;

      if (repeatPassword.length <= 0) {
          return null;
      }

      if (repeatPassword !== password) {
          return {
              doesMatchPassword: true
          };
      }
      return null;

  }
}