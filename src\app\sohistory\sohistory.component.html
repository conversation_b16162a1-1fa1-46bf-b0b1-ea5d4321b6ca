<app-topmenu></app-topmenu>

    <section style="padding-top:60px">
        <div class="container-fluid col-md-12 col-xs-12 col-sm-12 col-12" style="padding-right: 5px; padding-left: 5px;" >
            <h5 class="p-sm-1 bg-secondary text-white text-center font-weight-light col-md-12 col-xs-12 col-sm-12 col-12">Sale Order History</h5>
            <div class="form-row">
             <!-- <div class="col-md-2 mb-2">
                  <input id="fromdate" class="form-control form-control-sm" type="date" [(ngModel)]="formdate" name="formdate" placeholder="จากวันที่">
              </div>
              <div class="col-md-2 mb-2">
                  <input id="todate" class="form-control form-control-sm" type="date"  [(ngModel)]="todate" name="todate" placeholder="ถึงวันที่">
              </div>-->
              <div class="col-md-2 mb-2" *ngIf="testclose"  >
                      <select multiple [(ngModel)]="salegroup"  class="custom-select custom-select-sm custom-select-md" >
                              <option selected  value="">--เลือกรหัสพนักงานขาย--</option>
                              <option value="%20">เลือกรายการทั้งหมด</option>
                              <option *ngFor="let item of DateGroupsaleman" [value]="item.groupid">
                                      {{item.groupid}} ({{item.name}})
                              </option>
                          </select>
              </div>
              <div class="col-md-2 mb-2">
                    <ng-template    #rt let-r="result" let-t="term">
                            <div >
                                    <label>{{r.name}} ({{r.accountnum}})</label>
                            </div>
                      
              </ng-template>
            
                <input    id="typeahead-template"  placeholder="{{company}}"  type="text" class="form-control form-control-sm" [(ngModel)]="getcustomer" name="getcustomer" [ngbTypeahead]="search" [resultTemplate]="rt"
                  [inputFormatter]="formatter" />
              </div>
              <div class="col-xs-12 col-12 col-md-2 form-group">
                  <input type="text"
                  placeholder="DD/MM/YYYY"
                  class="form-control"
                  bsDatepicker
                  [bsConfig]="{ dateInputFormat: 'DD/MM/YYYY' }"
                  class="form-control form-control-sm form-control-md"
                  [(ngModel)]="Datafromdate"
                  >
                  </div>
             
          <div class="col-xs-12 col-12 col-md-2 form-group">
                  <input type="text"
                         placeholder="DD/MM/YYYY"
                         class="form-control"
                         bsDatepicker
                         [bsConfig]="{ dateInputFormat: 'DD/MM/YYYY' }"
                         class="form-control form-control-sm form-control-md"
                         [(ngModel)]="Datatodate">
                         
                </div> 
               
                <div class="col-xs-12 col-12 col-md-1 form-group">
                    <select class="custom-select custom-select-sm" (change)="searchselect()" [(ngModel)]="typesync" name="" id="">
                        <option  class="text-black-50" value="111">ทุกรายการ</option>
                        <option  class="text-black-50" value="1">Openorder</option>
                        <option class="text-black-50" value="2">Delivered</option>
                        <option class="text-black-50" value="3">Invoiced</option>
                        <option class="text-black-50" value="4">Canceled</option>
                        <option class="text-black-50" value="5">Order Draft</option>
                        <option  class="text-black-50" value="6">รอเข้าระบบ AX</option>                      
                    </select>
                </div>
                <div class="col-xs-12 col-12 col-md-1 form-group">
                    <select class="custom-select custom-select-sm" (change)="searchpaymenttype()" [(ngModel)]="paymenttype">
                            <option  class="text-black-50" value="All">ทุกรายการ</option>
                            <option  class="text-black-50" value="TT">TT</option>
                            <option  class="text-black-50" value="N01">N01</option>
                            <option class="text-black-50" value="N07">N07</option>
                            <option class="text-black-50" value="COD">COD</option>
                            <option class="text-black-50" value="N15">N15</option>
                            <option class="text-black-50" value="N30">N30</option>
                            <option class="text-black-50" value="N60">N60</option>
                            <option  class="text-black-50" value="N90">N90</option>   
                            <option  class="text-black-50" value="N120">N120</option>                     
                        </select>
                </div>
              <div class="col-md-2 mb-2 col-12 text-center text-sm-center text-md-left text-lg-left" style="padding-right: 0px; padding-left: 0px;">
                  <button  [disabled]="searchbtn"  style="width: 60px;" (click)="searchsohistory()"  class="btn btn-primary btn-sm font-weight-light" type="button">Search</button>
                  <button [disabled]="exportbtn"  style="margin-left: 3px; width: 68px;" (click)="exportdataexcel()" class="btn btn-primary btn-sm font-weight-light" type="button">Export</button>
                 <button *ngIf="deletehistorybtn"  style="margin-left: 3px; width: 100px;" data-toggle="modal" data-target="#DeleteHistory" aria-expanded="true" class="btn btn-primary btn-sm font-weight-light" type="button">Delete History</button>
              </div>
            </div> 
            <table class="table table-hover table-bordered table-sm"> 
                <thead>
                    <tr class="text-sm-center bg-light">
                        <th class="font-weight-normal " scope="col">Item</th>
                      <th class="font-weight-light" scope="col">เลขที่ SO</th>
                      <th class="font-weight-light" scope="col">เลขที่ SO AX</th>
                        <th class="font-weight-normal" scope="col">วันที่</th>
                        <th class="font-weight-normal"  scope="col">เวลา</th>
                        <th class="font-weight-normal" scope="col">พนักงานขาย</th>
                        <th class="font-weight-normal" scope="col">ลูกค้า</th>
                        <th class="font-weight-normal"  scope="col">ชื่อ INV</th>
                        <th class="font-weight-light"  scope="col">TAX ID</th>
                        <th class="font-weight-normal"  scope="col">ชื่อขนส่ง</th>
                        <th class="font-weight-normal" scope="col">มูลค่าสินค้า</th>
                        <th class="font-weight-normal" scope="col">มูลค่าสุทธิ</th>
                        <th class="font-weight-normal" scope="col">VAT/No VAT</th>
                        <th class="font-weight-normal" scope="col">ประเภทขนส่ง</th>
                        <th class="font-weight-normal" scope="col">เงินสด/เครดิต</th>
                        <th class="font-weight-normal" scope="col">Note ภายใน</th>
                        <th class="font-weight-normal" scope="col">หมายเหตุ</th>
                        <!-- <th class="font-weight-normal" scope="col">สถานะ</th> -->
                        <th class="font-weight-normal" scope="col"></th>
                    </tr>
                </thead>
                <tbody>
                    <tr *ngFor="let item of sohitorylistfilter; let i=index" class="text-sm-left" >
                        <td class="text-sm-center font-weight-light"  [ngStyle]="{'color':getColorFile(item.filetype)}" >{{i+1}}</td>
                    <td class="text-sm-center font-weight-light"  [ngStyle]="{'color':getColorFile(item.filetype)}" >{{item.id}}</td>
                    <td class="text-sm-center font-weight-light"  [ngStyle]="{'color':getColorFile(item.filetype)}">{{item.AXID}}</td>
                        <td class="text-sm-center font-weight-normal"  [ngStyle]="{'color':getColorFile(item.filetype)}">{{item.ShippingDateRequested | date:'dd/MM/yyyy'}}</td>
                        <td tooltip="แก้ไขครั้งล่าสุด {{item.lastupdate | date:'dd/MM/yyyy HH:mm:ss':'UTC'}}" class="text-sm-center font-weight-normal">{{item.timeedit | date:'HH:mm:ss':'UTC'}}</td>
                        <td class="text-sm-center font-weight-normal"  [ngStyle]="{'color':getColorFile(item.filetype)}">{{item.SalesId}}</td>
                        <td class="font-weight-normal"  [ngStyle]="{'color':getColorFile(item.filetype)}">{{item.salesname}}</td>
                        <td class="font-weight-normal"  [ngStyle]="{'color':getColorFile(item.filetype)}" >{{item.InvName}}</td>
                        <td class="text-sm-center font-weight-light"  [ngStyle]="{'color':getColorFile(item.filetype)}" >{{item.regnum}}</td>
                      <td class="font-weight-normal"  [ngStyle]="{'color':getColorFile(item.filetype)}" >{{item.DeliveryName}}</td>
                        <td class="text-sm-right font-weight-normal"  [ngStyle]="{'color':getColorFile(item.filetype)}" >{{item.amount | number:'1.2-2'}}</td>
                        <td class="text-sm-right font-weight-normal"  [ngStyle]="{'color':getColorFile(item.filetype)}" >{{item.price | number:'1.2-2'}}</td>
                        <td class="text-sm-center font-weight-normal" [ngStyle]="{'color':getColorFile(item.filetype)}" >{{item.vattype}}</td>
                        <td class="text-sm-center font-weight-normal" [ngStyle]="{'color':getColorFile(item.filetype)}">{{item.DlvMode}}</td>
                        <td class="text-sm-center font-weight-normal" [ngStyle]="{'color':getColorFile(item.filetype)}">{{item.paymenttype}}</td>
                        <td class="text-sm-right font-weight-normal" [ngStyle]="{'color':getColorFile(item.filetype)}">{{item.CustomerRef}}</td>
                       <td class="text-sm-right font-weight-normal" [ngStyle]="{'color':getColorFile(item.filetype)}">{{item.remark}}</td>
                        <!-- <td *ngIf="item.stcheck!=0" [ngStyle]="{'color':getColorst(item.state)}" [ngStyle]="{'color':getColorFile(item.filetype)}"  class=" text-sm-center font-weight-normal">{{item.statest}}</td> -->
                        <td *ngIf="item.stcheck==0" [ngStyle]="{'color':getColorsdaft(item.stcheck)}"  [ngStyle]="{'color':getColorFile(item.filetype)}" class=" text-sm-center font-weight-normal">{{item.textstatus}}</td>
                        <td class="text-sm-center font-weight-normal">
                            <button (click)="getsaloderhistory(item.id,templateView,item.filetype,item.filename)" class="btn btn-link font-weight-normal" style="padding: 0pt">
                            View
                        </button>
                        </td>
                    </tr>
                    
                    <tr *ngIf="Ckviewsohitorylistfilter">
                        <td class="text-center font-weight-normal"colspan="18" style="text-align: center;"  > ไม่มีรายการ </td>
                    </tr>
                </tbody>
            </table>
      
        </div>
        <div id="mdlSample" class="modal fade" role="dialog" [ngStyle]="{'display': mdlSampleIsOpen ? 'block' : 'none', 'opacity': 1}">
              <div class="modal-dialog modal-md">
              <div class="modal-content">
              <div class="modal-header colhaederal">
              <h4 class="modal-title">Report</h4>
              </div>
              <div class="modal-body">{{alt}}</div>
              <div class="modal-footer" align="right">
                          <button type="button" id="btnClose" class="btn btn-danger" (click)="closemodel(false)"><i class="fa fa-times fa-fw"></i> ปิด</button></div>
              </div>
              </div>
              </div>
      
            <body>
                <div class="modal fade bd-example-modal-lg" id="ViewDetailSaleoder" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
                    <div class="modal-dialog modal-lg" role="document">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title" id="exampleModalLabel">Sale Oder Detail</h5>
                                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                    <span aria-hidden="true">&times;</span>
                                </button>
                            </div>
                            <div class="modal-body modal-lg">
                                <div style="overflow-x: auto; ">
                                <table class="table table-hover table-bordered table-sm">
                                    <thead>
                                      <tr>
                                            <th class="text-center font-weight-normal">รหัสสินค้า</th>
                                            <th class="text-center font-weight-normal">ชื่อสินค้า</th>
                                            <th class="font-weight-normal">Pack</th>
                                            <th class="font-weight-normal">จำนวน</th>
                                            <th class="font-weight-normal">น้ำหนัก</th>
                                            <th class="font-weight-normal">ราคา</th>
                                            <th class="font-weight-normal text-center" colspan="3">ส่วนลด</th>
                                            <th class="font-weight-normal">มูลค่าสุทธิ</th>
                                            <!-- <th class="font-weight-normal">สถานะ</th> -->
                                      </tr>
                                    </thead>
                                    <tbody>
                                      <tr *ngFor="let item of salelistview; let i=index">
                                            <td class="text-center font-weight-sm">{{item.ItemId}}</td>
                                            <td class="text-left  font-weight-sm">{{item.Name}}</td>
                                            <td class="text-center font-weight-sm">{{item.packqty | number:'1.2-2'}}</td>
                                            <td class="text-center font-weight-sm">{{item.SalesQty | number:'1.2-2'}}</td>
                                            <td class="text-center font-weight-sm">{{item.totalweight | number:'1.2-2'}}</td>
                                            <td class="text-center font-weight-sm">{{item.PriceUnit | number:'1.2-2'}}</td>
                                            <td class="text-right font-weight-sm" [ngStyle]="{'color':getColordis1(item.disstate)}">{{item.IVZ_Percent1_CT | number:'1.2-2'}}%</td>
                                            <td class="text-right font-weight-sm" [ngStyle]="{'color':getColordis2(item.disstate)}">{{item.IVZ_Percent2_CT | number:'1.2-2'}}%</td>
                                            <td class="text-right font-weight-sm" [ngStyle]="{'color':getColordis3(item.disstate)}">{{item.IVZ_Percent3_CT | number:'1.2-2'}}%</td>
                                            <td class="text-right font-weight-sm">{{item.LineAmount | number:'1.2-2'}}</td>
                                            <!-- <td [ngStyle]="{'color':getColorst(item.state)}" class="text-right font-weight-sm ">{{item.statest}}</td> -->
                                      </tr>
                                    </tbody>
                                  </table>
                                </div>
                                
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                               <!-- <button type="button" class="btn btn-primary">Upload</button> -->
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal fade bd-example-modal-sm" id="DeleteHistory" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
                    <div class="modal-dialog modal-sm" role="document">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title" id="exampleModalLabel">DeleteHistory</h5>
                                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                    <span aria-hidden="true">&times;</span>
                                </button>
                            </div>
                            <div class="modal-body modal-sm">
                                <div class="col-xs-12 col-12 col-md-12 form-group">
                                    <input type="text"
                                    placeholder="DD/MM/YYYY"
                                    class="form-control"
                                    bsDatepicker
                                    [bsConfig]="{ dateInputFormat: 'DD/MM/YYYY' }"
                                    class="form-control form-control-sm form-control-md"
                                    [(ngModel)]="Deletefromdate"
                                    >
                                    </div>
                               
                            <div class="col-xs-12 col-12 col-md-12 form-group">
                                    <input type="text"
                                           placeholder="DD/MM/YYYY"
                                           class="form-control"
                                           bsDatepicker
                                           [bsConfig]="{ dateInputFormat: 'DD/MM/YYYY' }"
                                           class="form-control form-control-sm form-control-md"
                                           [(ngModel)]="Deletetodate">
                                           
                                  </div> 
                                
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                               <button type="button" (click)="deletehistory()" class="btn btn-primary">Delete</button>
                            </div>
                        </div>
                    </div>
                </div>
            </body>
      
      
      </section>
      
      <ng-template #templateView>
        <div class="modal-header">
          <h4 class="modal-title pull-left">Sale Oder Detail : {{ showIDso }}</h4>
          <button type="button" class="close pull-right" aria-label="Close" (click)="modalRef.hide()">
            <span aria-hidden="true">&times;</span>
          </button>   
        </div>
        <div class="modal-body" style=" padding: 3px; ">  
                                      
                        <tabset #staticTabs>
                          <tab heading="Sale Oder list" style="padding-top : 5px;" >
                            <div style="overflow-x: auto; ">
                                <table class="table table-hover table-bordered table-sm">
                                    <thead>
                                      <tr>
                                            <th class="text-center font-weight-normal">รหัสสินค้า</th>
                                            <th class="text-center font-weight-normal">ชื่อสินค้า</th>
                                            <th class="font-weight-normal">Pack</th>
                                            <th class="font-weight-normal">จำนวน</th>
                                            <th class="font-weight-normal">น้ำหนัก</th>
                                            <th class="font-weight-normal">ราคา</th>
                                            <th class="font-weight-normal text-center" colspan="3">ส่วนลด</th>
                                            <th class="font-weight-normal">มูลค่าสุทธิ</th>
                                            <!-- <th class="font-weight-normal">สถานะ</th> -->
                                      </tr>
                                    </thead>
                                    <tbody>
                                      <tr *ngFor="let item of salelistview; let i=index">
                                            <td class="text-center font-weight-sm" >{{item.ItemId}}</td>
                                            <td class="text-left  font-weight-sm">{{item.Name}}</td>
                                            <td class="text-center font-weight-sm">{{item.packqty | number:'1.2-2'}}</td>
                                            <td class="text-center font-weight-sm">{{item.SalesQty | number:'1.2-2'}}</td>
                                            <td class="text-center font-weight-sm">{{item.totalweight | number:'1.2-2'}}</td>
                                            <td class="text-center font-weight-sm">{{item.PriceUnit | number:'1.2-2'}}</td>
                                            <td class="text-right font-weight-sm" [ngStyle]="{'color':getColordis1(item.disstate)}">{{item.IVZ_Percent1_CT | number:'1.2-2'}}%</td>
                                            <td class="text-right font-weight-sm" [ngStyle]="{'color':getColordis2(item.disstate)}">{{item.IVZ_Percent2_CT | number:'1.2-2'}}%</td>
                                            <td class="text-right font-weight-sm" [ngStyle]="{'color':getColordis3(item.disstate)}">{{item.IVZ_Percent3_CT | number:'1.2-2'}}%</td>
                                            <td class="text-right font-weight-sm">{{item.LineAmount | number:'1.2-2'}}</td>
                                            <!-- <td [ngStyle]="{'color':getColorst(item.state)}" class="text-right font-weight-sm ">{{item.statest}}</td> -->
                                      </tr>
                                    </tbody>
                                  </table>
                                </div>
                      
    
                          </tab>
                        
                          <tab *ngIf="!Cktype && CkNull " heading="File">
                                <div  class="card card-body">
                                        <form #imageForm=ngForm  style="text-align: center;">
                                            <img [src]="nameUrl"  style="width:100%;"> 
                                        </form>
                                </div>
                              
                                
                          </tab>
                     
                        </tabset>
                      </div>
            <div class="modal-footer">             
            <a  *ngIf="Cktype && CkNull" href="{{nameUrl}}" target="_blank"  type="button" class="btn btn-primary" >View PDF</a>    
                <button type="button" (click)="modalRef.hide()" class="btn btn-secondary">Close</button>
            </div>
      </ng-template>
    



