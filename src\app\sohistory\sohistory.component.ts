import { WebapiService } from './../webapi.service';
import { Component, OnInit } from '@angular/core';
import { Chart } from 'chart.js';
import { Angular5Csv } from 'angular5-csv/Angular5-csv';
import { NgbCalendar, NgbDateStruct } from '@ng-bootstrap/ng-bootstrap';
import { HttpClient } from '@angular/common/http';
import {debounceTime, map} from 'rxjs/operators';
import { Observable } from 'rxjs';
import { Router } from '@angular/router';
import { BsModalService } from 'ngx-bootstrap/modal';
import { BsModalRef } from 'ngx-bootstrap/modal/bs-modal-ref.service';
import { getFullYear } from 'ngx-bootstrap/chronos/utils/date-getters';

@Component({
  selector: 'app-sohistory',
  templateUrl: './sohistory.component.html',
  styleUrls: ['./sohistory.component.css']
})


export class SohistoryComponent implements OnInit {

  getColorst(disst) { 
 if(disst==0){
  return '#05C1FF';
 } else if(disst==1) {
  return '#FF9505';
 } else if(disst==2){
   return '#C305FF';
 }else if(disst==3){
  return '#FF05D5';
}else if(disst==4){
  return '#0540FF';
}else if(disst==5){
  return '#0AC103';
}
} 
getColorsdaft(disst) { 
  if(disst==0){
   return '#7FFF00';
  } 
  }

  getColordis1(disst) { 
    var st =disst;
    var fi =st.substring(0,1);
    switch (fi) {
      case '1':
        return 'red';
    }
  }

  getColordis2(disst) { 
    var st =disst;
    var fi =st.substring(1,2);
    switch (fi) {
      case '1':
        return 'red';
    }
  }
  getColordis3(disst) { 
    var st =disst;
    var fi =st.substring(2,3);
    switch (fi) {
      case '1':
        return 'red';
    }
  }

  mdlSampleIsOpen : boolean = false;
  alt='';
  checkreload=true;

  options = { 
    fieldSeparator: ',',
    quoteStrings: '"',
    decimalseparator: '.',
    showLabels: true, 
    showTitle: true,
    useBom: true,
    noDownload: false,
    headers: ["เลขที่ SO", "วันที่","พนักงานขาย",'ลูกค้า','มูลค่าสินค้า','มูลค่าสุทธิ','VAT/No VAT',"ประเภทขนส่ง","เงินสด/เครดิต","Note ภายใน","หมายเหตุ","สถานะ"]
  };

url: string;
  sohitorylist: any[];
  sohitorylistfilter:any[]=[];
  fromdate='';
  todate='';
  fromDate: NgbDateStruct;
  toDate: NgbDateStruct;
  salegroup='';
  customer='';
  DateGroupsaleman:any[]=[];
  Name:any[]=[];
  Interval:any;
  Deletefromdate:any;
  Deletetodate:any;
  typesync='111';
  dateshipping='';
  dateshippingto='';
  datalogin: any[]=[];
  groupsale:string;
  testclose: boolean;
  chackuser=false;
  salelistview:any[]=[];
  getcustomer:any;
  Datatodate:any;
Datafromdate:any;
discustomer=false;
permisstiondata:any[]=[];
exportbtn=true;
searchbtn=true;
company='ค้นหาลูกค้า';

customers:any[]=[];

modalRef: BsModalRef;
config = {
  ignoreBackdropClick: false,
  class : 'modal-lg'
};
nameUrl='';

Cktype;
CkNull;
paymenttype='All'
Ckviewsohitorylistfilter=false;
deletehistorybtn=false;
  constructor(private modalService: BsModalService, private http: HttpClient, private service: WebapiService, private calendar: NgbCalendar, private router: Router) {
    localStorage.removeItem('DataSOderreview');
    localStorage.removeItem('DataSOderlist');
    this.url=service.geturlservice();
    this.Name=JSON.parse(sessionStorage.getItem('login'))
    this.fromDate = calendar.getPrev(calendar.getToday(), 'd', 30);
    this.toDate = calendar.getNext(calendar.getToday(), 'm', 3);
    this.datalogin=JSON.parse(sessionStorage.getItem('login'))
    this.fromdate='';
    this.todate='';
    this.Datatodate= new Date(this.toDate.year,this.toDate.month-1,this.toDate.day);
    this.Datafromdate= new Date(this.fromDate.year, this.fromDate.month-1, this.fromDate.day);
    this.getdate();

    this.permisstiondata=JSON.parse(sessionStorage.getItem('menu'))

    if (this.permisstiondata==null){
      this.router.navigate(['login']);
       }else{
        this.getuser();
    this.searchsohistory();
        this.exportbtn=!this.permisstiondata[6].flag_print;
        this.searchbtn=!this.permisstiondata[6].flag_action;
    
       }


  }

  getuser(){
    if(this.datalogin[0].id_group_user=='153Admin' || this.datalogin[0].id_group_user=='1860Administrator'){
      this.groupsale='';
      this.testclose=true;
      this.deletehistorybtn=true;
    }else{
      this.testclose=false;
     this.groupsale=this.datalogin[0].salegroup;
    }
  }
searchselect(){
 // alert(this.typesync+'//'+this.paymenttype)
if(this.typesync==='111'  && this.paymenttype==='All'){
  this.sohitorylistfilter=this.sohitorylist;
} else if(this.typesync=='0' || this.typesync=='1' || this.typesync=='2' || this.typesync=='3' || this.typesync=='4') {
  
  if(this.paymenttype==='All'){
    this.sohitorylistfilter=this.sohitorylist.filter(v => v.state.toLowerCase().indexOf(this.typesync.toLowerCase()) > -1).slice(0, 2000);
  }else{
    this.sohitorylistfilter=this.sohitorylist.filter(v => v.state.toLowerCase().indexOf(this.typesync.toLowerCase()) > -1).filter(v => v.paymenttype.toLowerCase().indexOf(this.paymenttype.toLowerCase()) > -1).slice(0, 2000);
  }
  
  //this.sohitorylistfilter=this.sohitorylist.filter(v => v.state.toLowerCase().indexOf(this.typesync.toLowerCase()) && v.paymenttype.toLowerCase().indexOf(this.paymenttype.toLowerCase()) > -1).slice(0, 50);



} else {
  var se='';
  if(this.typesync=='5'){
    se='0'//
  } else if(this.typesync=='6'){
    se='1'
  } else if(this.typesync=='7') {
    se='3'
  }

  if(this.paymenttype==='All'){
    this.sohitorylistfilter=this.sohitorylist.filter(v => v.stcheck.toLowerCase().indexOf(se.toLowerCase()) > -1).slice(0, 2000);
  }else{
    this.sohitorylistfilter=this.sohitorylist.filter(v => v.stcheck.toLowerCase().indexOf(se.toLowerCase()) > -1).filter(v => v.paymenttype.toLowerCase().indexOf(this.paymenttype.toLowerCase()) > -1).slice(0, 2000);
  }
  this.Cknum();
//  this.sohitorylistfilter=this.sohitorylist.filter(v => v.stcheck.toLowerCase().indexOf(se.toLowerCase()) && v.paymenttype.toLowerCase().indexOf(this.paymenttype.toLowerCase()) > -1).slice(0, 50);
}



  }
  //paymenttype
  searchpaymenttype(){
    //alert(this.typesync+'//'+this.paymenttype)
    if(this.typesync==='111' && this.paymenttype==='All'){
      this.sohitorylistfilter=this.sohitorylist;
    } else if(this.typesync=='0' || this.typesync=='1' || this.typesync=='2' || this.typesync=='3' || this.typesync=='4') {
      
      if(this.paymenttype==='All'){
        this.sohitorylistfilter=this.sohitorylist.filter(v => v.state.toLowerCase().indexOf(this.typesync.toLowerCase()) > -1).slice(0, 2000);
      }else{
        this.sohitorylistfilter=this.sohitorylist.filter(v => v.state.toLowerCase().indexOf(this.typesync.toLowerCase()) > -1).filter(v => v.paymenttype.toLowerCase().indexOf(this.paymenttype.toLowerCase()) > -1).slice(0, 2000);
      }
      
      //this.sohitorylistfilter=this.sohitorylist.filter(v => v.state.toLowerCase().indexOf(this.typesync.toLowerCase()) && v.paymenttype.toLowerCase().indexOf(this.paymenttype.toLowerCase()) > -1).slice(0, 50);
    
    
    
    } else {
      var se='';
      if(this.typesync=='5'){
        se='0'//
      } else if(this.typesync=='6'){
        se='1'
      } else if(this.typesync=='7') {
        se='3'
      }
    
      if(this.paymenttype==='All'){
        this.sohitorylistfilter=this.sohitorylist.filter(v => v.stcheck.toLowerCase().indexOf(se.toLowerCase()) > -1).slice(0, 2000);
      }else{
        this.sohitorylistfilter=this.sohitorylist.filter(v => v.stcheck.toLowerCase().indexOf(se.toLowerCase()) > -1).filter(v => v.paymenttype.toLowerCase().indexOf(this.paymenttype.toLowerCase()) > -1).slice(0, 2000);
      }


    }
    this.Cknum();
}

Cknum(){
  
  if(this.sohitorylistfilter.length < 1){
    this.Ckviewsohitorylistfilter =true;
  }else{
    this.Ckviewsohitorylistfilter =false;
  }
}


 /* <option  class="text-black-50" value="111">ทุกรายการ</option>
  <option  class="text-black-50" value="0">รอเข้าระบบ AX</option>
<option  class="text-black-50" value="1">Openorder</option>
<option class="text-black-50" value="2">Delivered</option>
<option class="text-black-50" value="3">Invoiced</option>
<option class="text-black-50" value="4">Canceled</option>
<option class="text-black-50" value="5">Order Draft</option>
<option class="text-black-50" value="6">รอ Sync</option>
<option class="text-black-50" value="7">กำลัง Sync</option> */

  //Autocomplete ลูกค้า
/*text$.pipe(
  debounceTime(200),
  map(term => term === '' ? []
    : this.customers.filter(v => v.name.toLowerCase().indexOf(term.toLowerCase()) > -1 ||  v.accountnum.toLowerCase().indexOf(term.toLowerCase()) > -1).slice(0, 50))
);
formatter = (x: {name: string,accountnum :string}) => x.name + ' ('+x.accountnum+')'; */

 deletehistory(){
   //alert(this.Deletefromdate);
   if(this.Deletefromdate==undefined || this.Deletetodate==undefined){
alert('กรุณาเลือกวันที่ ต้องการลบ Order History');
return;
   }
   var fmonth=this.Deletefromdate.getMonth()+1;
   var fday=this.Deletefromdate.getDate();
   var fmonthst='';
   var fdayst='';
   var tmonth=this.Deletetodate.getMonth()+1;
   var tday=this.Deletetodate.getDate();
   var tmonthst='';
   var tdayst='';
   if(fmonth<=9){
    fmonthst='0'+fmonth;
   } else{
     fmonthst=fmonth;
   }
   if(fday<=9){
fdayst='0'+fday;
   }else{
     fdayst=fday;
   }
   if(tmonth<=9){
    tmonthst='0'+tmonth;
   } else{
     tmonthst=tmonth;
   }
   if(tday<=9){
tdayst='0'+tday;
   }else{
     tdayst=tday;
   }
   var fromdaate=this.Deletefromdate.getFullYear()+'-'+fmonthst+'-'+fdayst;
   var todate=this.Deletetodate.getFullYear()+'-'+tmonthst+'-'+tdayst;
 
 if(confirm('ต้องการ ลบ Order History ใช่ หรือ ไม่')){
  const Http = new XMLHttpRequest();
  const url='syncso/Service.asmx/DeleteHistory?D1='+fromdaate+'&D2='+todate;
  Http.open("GET", url);
  Http.send();
  Http.onreadystatechange=(e)=>{
  if(Http.readyState==4 && Http.status==200){
  alert('ลบ Order History เสร็จสิ้น');
  }
  }
 }
 }
  getdate(){
    this.fromdate = `${this.Datafromdate.getFullYear()}-${this.Datafromdate.getMonth()+1}-${this.Datafromdate.getDate()}`;
    this.todate = `${this.Datatodate.getFullYear()}-${this.Datatodate.getMonth()+1}-${this.Datatodate.getDate()}`;
   }
   
   search = (text$: Observable<any>) =>

   //Autocomplete ลูกค้า
 text$.pipe(
   debounceTime(200),
   map(term => term === '' ? []
     : this.customers.filter(v => v.name.toLowerCase().indexOf(term.toLowerCase()) > -1 ||  v.accountnum.toLowerCase().indexOf(term.toLowerCase()) > -1).slice(0, 50))
 );
 formatter = (x: {name: string,accountnum :string}) => x.name + ' ('+x.accountnum+')';


  ngOnInit() {
    if(this.Name[0].salegroup=='admin'){
      this.chackuser=true;
      }
    this.getuser();
    this.getgroupsaleman();
  this.getcostomerauto();
  //this.Interval= setInterval(()=> this.Searchsohistory(this.datalogin[0].salegroup),300); 
  }
  getcostomerauto(){
    var idsale=this.datalogin[0].salegroup;
    if(this.datalogin[0].id_group_user=='153Admin' || this.datalogin[0].id_group_user=='1860Administrator'){
idsale='%20';
    } else {
      idsale=this.datalogin[0].salegroup;
    }
  
    this.http.get<any>(this.url + 'customerauto/'+idsale).subscribe(res =>{
      this.customers=res;
    })
  }


  gettype(type){
    if(type==="application/pdf"){
      return true;
    }else{
      return false;
    }
  }

  gettypeNull(type){
    if(type==""){
      return false;
    }else{
      return true;
    }
  }
  getColorFile(type) { 
    if(type!==""){
      return '#0317ee';
    }
   }
  getsaloderhistory(valueid,template,filetype,filename){
    this.salelistview=[];
    this.modalRef = this.modalService.show(template, this.config);
 this.http.get<any>(this.url+'find_saleline/'+valueid).subscribe(res =>{
   if(res.length>0){
    this.salelistview=[];
this.salelistview=res;
this.Cktype = this.gettype(filetype);
this.CkNull = this.gettypeNull(filetype);
this.nameUrl='http://119.59.112.47/assets/PDF/'+filename;
   }
 })
  }

  Searchsohistory(saleid) {
    this.getdate();


 

    var datasalegroup='';
    if(this.fromdate==''){
      this.fromdate =`${this.fromDate}`;
    }
    if(this.todate==''){
      this.todate=`${this.toDate}`;
    }
    if(saleid==''){
      if(this.datalogin[0].salegroup =='admin'){
        datasalegroup ='%20';
      }else{
        datasalegroup = `${this.datalogin[0].salegroup}`;
      }
    }

    if(saleid !==''){
      if(this.datalogin[0].salegroup =='admin'){
        datasalegroup ='%20';
      }else{
        datasalegroup = `${this.datalogin[0].salegroup}`;
      }
    }

    if(datasalegroup==''){
      datasalegroup='%20';
    }
    if(this.customer=='') {
      this.customer='%20';
    }
    if(this.customer==undefined){
      this.customer='%20';
    }
  
    this.sohitorylist=[];
    this.http.get<any[]>(this.url + 'solist/'+ this.fromdate +'/'+ this.todate +'/'+datasalegroup+'/'+this.customer+'/4').subscribe(res =>{
      if(res.length >0 ) {
        if(datasalegroup=='%20'){
          this.salegroup='';
        }
        if(this.customer=='%20'){
          this.customer='';
        }
this.sohitorylist=res;
this.sohitorylistfilter=this.sohitorylist;
      } else {
        if(datasalegroup=='%20'){
          this.salegroup='';
        }
        if(this.customer=='%20'){
          this.customer='';
        }
         this.sohitorylist=[];
         alert('ไม่พบข้อมูลที่ค้นหา');
        //this.openModal(true,'ไม่พบข้อมูลที่ค้นหา',false)
      }
      clearInterval(this.Interval);
    })
  }

  searchsohistory() {
    this.getdate();
   
    if(this.getcustomer==undefined){
      this.customer='';
    } else {
    
      this.customer=this.getcustomer.name;
    }
    var datasalegroup='';
    if(this.fromdate==''){
      this.fromdate =`${this.fromDate}`;
    }
    if(this.todate==''){
      this.todate=`${this.toDate}`;
    }
    if(this.groupsale==''){
      if(this.datalogin[0].id_group_user=='153Admin' || this.datalogin[0].id_group_user=='1860Administrator'){
        datasalegroup =`${this.salegroup}`;
      }else{
        datasalegroup = `${this.datalogin[0].salegroup}`;
      }
    }

    if(this.groupsale !==''){
      if(this.datalogin[0].id_group_user=='153Admin' || this.datalogin[0].id_group_user=='1860Administrator'){
        datasalegroup =`${this.salegroup}`;
      }else{
        datasalegroup = `${this.datalogin[0].salegroup}`;
      }
    }

    if(datasalegroup==''){
      datasalegroup='%20';
    }
    if(this.customer=='') {
      this.customer='%20';
    }
    if(this.Name[0].accountnum !=undefined){
      this.discustomer=true;
      this.customer=this.Name[0].accountnum;
    }
    if(this.customer==undefined){
      this.customer='%20';
    }
    this.sohitorylist=[];
    this.http.get<any[]>(this.url + 'solist/'+  this.fromdate +'/'+this.todate+'/'+datasalegroup+'/'+this.customer+'/4').subscribe(res =>{
      if(res.length >0 ) {
        if(datasalegroup=='%20'){
          this.salegroup='';
        }
        if(this.customer=='%20'){
          this.customer='';
        }
          this.sohitorylist=res;
          this.sohitorylistfilter=this.sohitorylist;
      } else {
        this.sohitorylist=[];
        if(datasalegroup=='%20'){
          this.salegroup='';
        }
        if(this.customer=='%20'){
          this.customer='';
        }
        alert('ไม่พบข้อมูลที่ค้นหา');
        //this.openModal(true,'ไม่พบข้อมูลที่ค้นหา',false)
      }
      clearInterval(this.Interval);
    })
  }

  getgroupsaleman(){
    this.DateGroupsaleman=[];
    this.http.get<any>(this.url +'salesman' ).subscribe(res => {
      if(res.length > 0){
     this.DateGroupsaleman=res;
          }else{

          alert('ไม่สามารถติดต่อข้อมูลรหัส saleman ได้')
          }
      });
  }
  exportdataexcel() {
    if(this.sohitorylist==undefined){
      alert('ไม่พบข้อมูล');
//this.openModal(true,'ไม่พบข้อมูล',false);
    } else {
      new Angular5Csv(this.sohitorylist, 'Sohistory', this.options);
    }
    
  }

  openModal(open : boolean,text: string,load:boolean) : void {
    this.mdlSampleIsOpen = open;
    this.alt=text;
    this.checkreload=load;
  }
  closemodel(cl: boolean) {
  this.mdlSampleIsOpen=cl;
  if(this.checkreload==true) {
    location.reload();
  }
  
  }
}
