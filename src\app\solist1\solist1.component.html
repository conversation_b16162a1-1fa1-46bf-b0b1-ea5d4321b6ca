<app-topmenu></app-topmenu>
<section style="padding-top:60px">
  <div class="container-fluid" style="padding-right: 5px; padding-left: 5px;" >
      <h5 class="p-sm-1 bg-secondary text-white text-center ">Sale Order List</h5>
      <div class="form-row">
       <!-- <div class="col-md-2 mb-2">
            <input id="fromdate" class="form-control form-control-sm" type="date" name="formdate" [(ngModel)]="formdate" placeholder="จากวันที่">
        </div>
        <div class="col-md-2 mb-2">
            <input id="todate" class="form-control form-control-sm" type="date" name="todate" [(ngModel)]="todate"  placeholder="ถึงวันที่">
        </div>-->
        <div class="col-md-2 mb-2" *ngIf="testclose" >
            <select multiple [(ngModel)]="salegroup"  class="custom-select custom-select-sm" >
                    <option selected value="">--เลือกรหัสพนักงานขาย--</option>
                    <option value="%20">เลือกรายการทั้งหมด</option>
                    <option *ngFor="let item of DateGroupsaleman" [value]="item.groupid">
                            {{item.groupid}} ({{item.name}})
                    </option>
                </select>
        
    </div>
    <div *ngIf="enablecustomer" class="col-md-2 mb-2">
            <ng-template    #rt let-r="result" let-t="term">
                    <div (click)="getcustomersalefunction(r.name)">
                            <label (mousedown)="getcustomersalefunction(r.name)" >{{r.name}} ({{r.accountnum}})</label>
                    </div>
              
      </ng-template>
    
        <input    id="typeahead-template"  placeholder="{{company}}"  type="text" class="form-control form-control-sm" [(ngModel)]="getcustomer" name="getcustomer" [ngbTypeahead]="search" [resultTemplate]="rt"
          [inputFormatter]="formatter" />

    </div>
        <div class="col-xs-12 col-12 col-md-2 form-group">
                <input type="text"
                placeholder="DD/MM/YYYY"
                class="form-control"
                bsDatepicker
                [bsConfig]="{ dateInputFormat: 'DD/MM/YYYY' }"
                class="form-control form-control-sm"
                [(ngModel)]="Datafromdate"
                >
                </div>
        
        <div class="col-xs-12 col-12 col-md-2 form-group">
                <input type="text"
                       placeholder="DD/MM/YYYY"
                       class="form-control"
                       bsDatepicker
                       [bsConfig]="{ dateInputFormat: 'DD/MM/YYYY' }"
                       class="form-control form-control-sm"
                       [(ngModel)]="Datatodate">
              </div> 
        <div class="col-md-3 mb-3 col-12 text-center text-sm-center text-md-left text-lg-left" style="padding-right: 0px; padding-left: 0px;">
            <button [disabled]="searchbtn" style="width: 60px;" (click)="Searchsolist()" class="btn btn-primary btn-sm font-weight-light" type="submit">Search</button>
            <button  [disabled]="exportbtn" style="margin-left: 3px; width: 65px;" (click)="exportdataexcel()" class="btn btn-primary btn-sm font-weight-light" type="submit">Export</button>
        </div>
      </div> 
      <table class="table table-hover table-bordered table-sm">
          <thead>
              <tr class="text-sm-center bg-light">
                  <th class="font-weight-normal" scope="col">Item</th>
                  <th class="font-weight-light" scope="col">เลขที่ SO</th>
                  <th class="font-weight-normal"  scope="col">วันที่</th>
                  <th class="font-weight-normal"  scope="col">เวลา</th>
                  <th class="font-weight-normal"  scope="col">พนักงานขาย</th>
                  <th class="font-weight-normal"  scope="col">ลูกค้า</th>
                    <th class="font-weight-normal"  scope="col">ชื่อ INV</th>
                  <th class="font-weight-light"  scope="col">TAX ID</th>
                      <th class="font-weight-normal"  scope="col">ชื่อขนส่ง</th>
                  <th *ngIf="enablecustomer" class="font-weight-normal"  scope="col">มูลค่าสินค้า</th>
                  <th *ngIf="enablecustomer" class="font-weight-normal"  scope="col">มูลค่าสุทธิ</th>
                  <th class="font-weight-normal"  scope="col">VAT/No VAT</th>
                  <th class="font-weight-normal"  scope="col">น้ำหนักรวม</th>
                  <th class="font-weight-normal"  scope="col">ประเภทขนส่ง</th>
                  <th class="font-weight-normal"  scope="col">เงินสด/เครดิต</th>
                  <th class="font-weight-normal"  scope="col">Note ภายใน</th>
                  <th class="font-weight-normal"  scope="col">หมายเหตุ</th>
                  <th class="font-weight-normal"  scope="col">View</th>
                  <th *ngIf="showtablecustomer" class="font-weight-normal"  scope="col">Print</th>
                  <th   class="font-weight-normal"  scope="col">Edit</th>
                  <th  class="font-weight-normal"  scope="col">Delete</th>
               
                                  </tr>
          </thead>
          <tbody><!--regnum-->
              <tr *ngFor="let item of solist; let i = index" class="text-sm-left">
                  <td class="text-sm-center  font-weight-normal" [ngStyle]="{'color':getColorFile(item.filetype)}">{{i+1}}</td>
                  <td class="text-sm-center font-weight-normal" [ngStyle]="{'color':getColorFile(item.filetype)}" (click)="OpenUploadPDF(template,item.id)" >{{item.id}}</td>
                  <td class="text-sm-center font-weight-normal" [ngStyle]="{'color':getColorFile(item.filetype)}">{{item.ShippingDateRequested | date:'dd/MM/yyyy'}}</td>
                  <td tooltip="แก้ไขครั้งล่าสุด {{item.lastupdate | date:'dd/MM/yyyy HH:mm:ss':'UTC'}}" [ngStyle]="{'color':getColorFile(item.filetype)}" class="text-sm-center font-weight-normal">{{item.timeedit | date:'HH:mm:ss':'UTC'}}</td>
                  <td class="text-sm-center font-weight-normal" [ngStyle]="{'color':getColorFile(item.filetype)}">{{item.SalesId}}</td>
                  <td class="font-weight-normal" [ngStyle]="{'color':getColorFile(item.filetype)}">{{item.salesname}}</td>
                    <td class="font-weight-normal" [ngStyle]="{'color':getColorFile(item.filetype)}">{{item.InvName}}</td>
                   <td class="text-center-sm" [ngStyle]="{'color':getColorFile(item.filetype)}">{{item.regnum}}</td>
                      <td class="font-weight-normal" [ngStyle]="{'color':getColorFile(item.filetype)}">{{item.DeliveryName}}</td>
                  <td *ngIf="enablecustomer" class="text-sm-right font-weight-normal" [ngStyle]="{'color':getColorFile(item.filetype)}">{{item.amount | number:'1.2-2'}}</td>
                  <td *ngIf="enablecustomer" class="text-sm-right font-weight-normal" [ngStyle]="{'color':getColorFile(item.filetype)}">{{item.price | number:'1.2-2'}}</td>
                  <td class="text-sm-center font-weight-normal" [ngStyle]="{'color':getColorFile(item.filetype)}">{{item.vattype}}</td>
                  <td class="text-sm-center font-weight-normal" [ngStyle]="{'color':getColorFile(item.filetype)}">{{item.totalweight | number:'1.2-2' }}</td>
                  <td class="text-sm-center font-weight-normal" [ngStyle]="{'color':getColorFile(item.filetype)}">{{item.DlvMode}}</td>
                  <td class="text-sm-center font-weight-normal" [ngStyle]="{'color':getColorFile(item.filetype)}">{{item.paymenttype}}</td>
                  <td class="text-sm-center font-weight-normal" [ngStyle]="{'color':getColorFile(item.filetype)}">{{item.CustomerRef}}</td>
                  <td class="text-sm-center font-weight-normal" [ngStyle]="{'color':getColorFile(item.filetype)}">{{item.remark}}</td>
                  <td class="text-sm-center font-weight-normal"> <button (click)="getsaloderlist(item.id,false,templateView,item.filetype,item.filename)" class="btn btn-link font-weight-normal" style="padding: 0pt">
                    View
                </button> 
            </td>
            <td *ngIf="showtablecustomer" ><button (click)="loaddataprint(item.id)" class="btn btn-link font-weight-normal" type="button" style="width:65px;height:26px;padding:0px">Quotation</button> </td>
            <td   class="text-center">
                <label (click)="editviewsore(item.id)"  class="text-info btn-link  btn-sm">แก้ไข</label>
          </td> 
           <td  class="text-center">
            <label (click)="deletesaloder(item.id)"  class="text-danger btn-link  btn-sm">ลบ</label>
      </td>
              </tr>
          </tbody>
      </table>

  </div>

  <div id="mdlSample" class="modal fade" role="dialog" [ngStyle]="{'display': mdlSampleIsOpen ? 'block' : 'none', 'opacity': 1}">
        <div class="modal-dialog modal-md">
        <div class="modal-content">
        <div class="modal-header colhaederal">
        <h4 class="modal-title">Report</h4>
        </div>
        <div class="modal-body">{{alt}}</div>
        <div class="modal-footer" align="right">
                    <button type="button" id="btnClose" class="btn btn-danger" (click)="closemodel(false)"><i class="fa fa-times fa-fw"></i> ปิด</button></div>
        </div>
        </div>
        </div>


        <div class="modal fade bd-example-modal-lg" id="ViewDetailSaleoderlist" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-lg" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="exampleModalLabel">Sale Oder Detail</h5>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body modal-lg">
                        <table class="table table-hover table-bordered table-sm">
                            <thead>
                              <tr>
                                <th class="text-center font-weight-normal">รหัสสินค้า</th>
                                <th class="text-center font-weight-normal">ชื่อสินค้า</th>
                                <th class="font-weight-normal">Pack</th>
                                <th class="font-weight-normal">จำนวน</th>
                                <th class="font-weight-normal">น้ำหนัก</th>
                                <th *ngIf="enablecustomer" class="font-weight-normal">ราคา</th>
                                <th  *ngIf="enablecustomer"class="font-weight-normal text-center" colspan="3">ส่วนลด</th>
                                <th *ngIf="enablecustomer" class="font-weight-normal">มูลค่าสุทธิ</th>
                              </tr>
                            </thead>
                            <tbody>
                              <tr *ngFor="let item of salelistview; let i=index">
                                <td   class="text-center  font-weight-sm" >{{item.ItemId}}</td>
                                <td class="text-left  font-weight-sm">{{item.Name}}</td>
                                <td class="text-center font-weight-sm">{{item.packqty | number:'1.2-2'}}</td>
                                <td  class="text-center font-weight-sm">{{item.SalesQty | number:'1.2-2'}}</td>
                                <td class="text-center font-weight-sm">{{item.totalweight | number:'1.2-2'}}</td>
                                <td *ngIf="enablecustomer" class="text-center font-weight-sm">{{item.PriceUnit | number:'1.2-2'}}</td>
                                <td *ngIf="enablecustomer" class="text-right font-weight-sm" [ngStyle]="{'color':getColordis1(item.disstate)}">{{item.IVZ_Percent1_CT | number:'1.2-2'}}%</td>
                                <td *ngIf="enablecustomer" class="text-right font-weight-sm" [ngStyle]="{'color':getColordis2(item.disstate)}">{{item.IVZ_Percent2_CT | number:'1.2-2'}}%</td>
                                <td *ngIf="enablecustomer" class="text-right font-weight-sm" [ngStyle]="{'color':getColordis3(item.disstate)}">{{item.IVZ_Percent3_CT | number:'1.2-2'}}%</td>
                                
                                <td *ngIf="enablecustomer" class="text-right font-weight-sm">{{item.LineAmount | number:'1.2-2'}}</td>
                              </tr>
                            </tbody>
                          </table>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                  
                    </div>
                </div>
            </div>
        </div>
</section>



<ng-template #template>
    <div class="modal-header">
      <h4 class="modal-title pull-left">UploadPDF : {{showIDso}}</h4>
      <button type="button" class="close pull-right" aria-label="Close" (click)="modalRef.hide()">
        <span aria-hidden="true">&times;</span>
      </button>

    </div>
    <div class="modal-body">
      
            <div class="form-group" style="margin-bottom: 0rem">
                    <label for="exampleFormControlFile1">Input file PDF</label>
                    <input type="file" class="form-control-file" accept="application/pdf, image/*" (change)="handleFileInput($event.target.files)" id="exampleFormControlFile1">
                  </div>
        <!--<div class="custom-file col-md-12" >
                <input type="file" class="custom-file-input" #pdf    id="inputGroupFile01" aria-describedby="inputGroupFileAddon01">
                <label class="custom-file-label" [(ngModel)]="textload" for="inputGroupFile01">{{ textload }} </label>
              </div>-->
    </div>
    <div class="modal-footer">
            <button type="button" [disabled]="selectedFile==null"  class="btn btn-primary" (click)="onUpload(showIDso,templateShow,template)" >Upload</button>
            <button type="button" (click)="modalRef.hide()" class="btn btn-secondary">Close</button>
      
        </div>
  </ng-template>


  <ng-template #templateShow>
    <div class="modal-body text-center">
        {{textload}} <br>
        <button *ngIf="btnPDF" type="button" class="btn btn-default" (click)="confirm()" >ปิด</button>
        <button *ngIf="btnREpdf" type="button" class="btn btn-primary" (click)="decline(template)" >ทำรายการใหม่</button>
      </div><!---->
  </ng-template>

  

<ng-template #templateView>
    <div class="modal-header">
      <h4 class="modal-title pull-left">Sale Oder Detail : {{ showIDso }}</h4>
      <button type="button" class="close pull-right" aria-label="Close" (click)="modalRef.hide()">
        <span aria-hidden="true">&times;</span>
      </button>   
    </div>
    <div class="modal-body" style=" padding: 3px; ">  
                                  
                    <tabset #staticTabs>
                      <tab heading="Sale Oder list" style="padding-top : 5px;" >
                            <div style="overflow-x: auto;" >
                                <table class="table table-hover table-bordered table-sm">
                                    <thead>
                                      <tr>
                                        <th class="text-center font-weight-normal">รหัสสินค้า</th>
                                        <th class="text-center font-weight-normal">ชื่อสินค้า</th>
                                        <th class="font-weight-normal">Pack</th>
                                        <th class="font-weight-normal">จำนวน</th>
                                        <th class="font-weight-normal">น้ำหนัก</th>
                                        <th *ngIf="enablecustomer" class="font-weight-normal">ราคา</th>
                                        <th  *ngIf="enablecustomer"class="font-weight-normal text-center" colspan="3">ส่วนลด</th>
                                        <th *ngIf="enablecustomer" class="font-weight-normal">มูลค่าสุทธิ</th>
                                      </tr>
                                    </thead>
                                    <tbody>
                                      <tr *ngFor="let item of salelistview; let i=index">
                                        <td   class="text-center  font-weight-sm" >{{item.ItemId}}</td>
                                        <td class="text-left  font-weight-sm">{{item.Name}}</td>
                                        <td class="text-center font-weight-sm">{{item.packqty | number:'1.2-2'}}</td>
                                        <td  class="text-center font-weight-sm">{{item.SalesQty | number:'1.2-2'}}</td>
                                        <td class="text-center font-weight-sm">{{item.totalweight | number:'1.2-2'}}</td>
                                        <td *ngIf="enablecustomer" class="text-center font-weight-sm">{{item.PriceUnit | number:'1.2-2'}}</td>
                                        <td *ngIf="enablecustomer" class="text-right font-weight-sm" [ngStyle]="{'color':getColordis1(item.disstate)}">{{item.IVZ_Percent1_CT | number:'1.2-2'}}%</td>
                                        <td *ngIf="enablecustomer" class="text-right font-weight-sm" [ngStyle]="{'color':getColordis2(item.disstate)}">{{item.IVZ_Percent2_CT | number:'1.2-2'}}%</td>
                                        <td *ngIf="enablecustomer" class="text-right font-weight-sm" [ngStyle]="{'color':getColordis3(item.disstate)}">{{item.IVZ_Percent3_CT | number:'1.2-2'}}%</td>
                                        
                                        <td *ngIf="enablecustomer" class="text-right font-weight-sm">{{item.LineAmount | number:'1.2-2'}}</td>
                                      </tr>
                                    </tbody>
                                  </table>
                                   </div>
                  

                      </tab>
                    
                      <tab *ngIf="!Cktype && CkNull " heading="File">
                            <div  class="card card-body">
                                    <form #imageForm=ngForm  style="text-align: center;">
                                        <img [src]="nameUrl"  style="width:100%;"> 
                                    </form>
                            </div>
                          
                            
                      </tab>
                 
                    </tabset>
                  </div>
        <div class="modal-footer">             
        <a  *ngIf="Cktype && CkNull" href="{{nameUrl}}" target="_blank"  type="button" class="btn btn-primary" >View PDF</a>    
            <button type="button" (click)="modalRef.hide()" class="btn btn-secondary">Close</button>
        </div>
  </ng-template>
