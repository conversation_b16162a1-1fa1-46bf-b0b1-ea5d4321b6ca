import { WebapiService } from './../webapi.service';
import { HttpClient,HttpEventType } from '@angular/common/http';
import { Component, OnInit, TemplateRef  } from '@angular/core';
import { Angular5Csv } from 'angular5-csv/Angular5-csv';
import { NgbDateStruct, NgbCalendar } from '@ng-bootstrap/ng-bootstrap';
import { Router } from '@angular/router';
import {Observable} from 'rxjs';
import {debounceTime, map} from 'rxjs/operators';
import { BsModalService } from 'ngx-bootstrap/modal';
import { BsModalRef } from 'ngx-bootstrap/modal/bs-modal-ref.service';
import { TabsetComponent } from 'ngx-bootstrap/tabs';
@Component({
  selector: 'app-solist1',
  templateUrl: './solist1.component.html',
  styleUrls: ['./solist1.component.css']
})
export class Solist1Component implements OnInit {


  getColordis1(disst) {
    var st =disst;
    var fi =st.substring(0,1);
    switch (fi) {
      case '1':
        return 'red';
    }
  }

  getColordis2(disst) {
    var st =disst;
    var fi =st.substring(1,2);
    switch (fi) {
      case '1':
        return 'red';
    }
  }
  getColordis3(disst) {
    var st =disst;
    var fi =st.substring(2,3);
    switch (fi) {
      case '1':
        return 'red';
    }
  }

  mdlSampleIsOpen : boolean = false;
  alt='';
  checkreload=true;

savedata:any[]=[];
  options = {
    fieldSeparator: ',',
    quoteStrings: '"',
    decimalseparator: '.',
    showLabels: true,
    showTitle: true,
    useBom: true,
    noDownload: false,
    headers: ["วันที่", "พนักงานขาย","ลูกค้า","มูลค่าสินค้า","มูลค่าสุทธิ","VAT/No VAT","น้ำหนักรวม","ประเภทขนส่ง","เงินสด/เครดิต","Note ภายใน","หมายเหตุ"]
  };

url: string;
solist: any[];
fromdate='';

salegroup='';
customer='';

getcustomer:any;
DateGroupsaleman:any[]=[];

datalogin: any[]=[];
groupsale:string;
testclose: boolean;
todate='';
fromDate: NgbDateStruct;
toDate: NgbDateStruct;
chackuser=false;
Name:any;
Datatodate:any;
Datafromdate:any;
salelistview:any[]=[];
salehderprint:any[]=[];
showtablecustomer=true;
discustomer=false;
loaddata:any[]=[];
permisstiondata:any[]=[];
exportbtn=true;
searchbtn=true;
customers:any[]=[];
enablecustomer=false;
company='ค้นหาลูกค้า';
modalRef: BsModalRef;
config = {
  ignoreBackdropClick: true,
  class : 'modal-md'
};
configview = {
  ignoreBackdropClick: true,
  class : 'modal-lg'
};
showIDso;
textload="";
selectedFile =null;

btnPDF=false;
btnREpdf=false;

Cktype;
CkNull;
nameUrl:string;
  constructor(private modalService: BsModalService, private http: HttpClient, private service: WebapiService, private calendar: NgbCalendar, private router: Router) {
    localStorage.removeItem('DataSOderreview');
    this.url=service.geturlservice();
    this.Name=JSON.parse(sessionStorage.getItem('login'))
    console.log(this.Name);
    this.fromDate = calendar.getPrev(calendar.getToday(), 'd', 30);
    this.toDate = calendar.getNext(calendar.getToday(), 'm', 3);
    this.datalogin=JSON.parse(sessionStorage.getItem('login'))
    this.Datatodate= new Date(this.toDate.year,this.toDate.month-1,this.toDate.day);
    this.Datafromdate= new Date(this.fromDate.year, this.fromDate.month-1, this.fromDate.day);
    this.getdate();
    this.loaddata=JSON.parse(localStorage.getItem('DataSOderlist'));
    this.permisstiondata=JSON.parse(sessionStorage.getItem('menu'))



if (this.datalogin==null || this.permisstiondata==null){
  this.router.navigate(['login']);
   }else{
    this.getuser();
    this.exportbtn=!this.permisstiondata[4].flag_print;
    this.searchbtn=!this.permisstiondata[4].flag_action;

   }

   }



   getuser(){
    if(this.datalogin[0].id_group_user=='153Admin' || this.datalogin[0].id_group_user=='1860Administrator'){
      this.groupsale='';
      this.testclose=true;
    }else{
      this.testclose=false;
     this.salegroup=this.datalogin[0].salegroup;
    }
  }

  getdate(){
    this.fromdate = `${this.Datafromdate.getFullYear()}-${this.Datafromdate.getMonth()+1}-${this.Datafromdate.getDate()}`;
    this.todate = `${this.Datatodate.getFullYear()}-${this.Datatodate.getMonth()+1}-${this.Datatodate.getDate()}`;
   }

   search = (text$: Observable<any>) =>

   //Autocomplete ลูกค้า
 text$.pipe(
   debounceTime(200),
   map(term => term === '' ? []
     : this.customers.filter(v => v.name.toLowerCase().indexOf(term.toLowerCase()) > -1 ||  v.accountnum.toLowerCase().indexOf(term.toLowerCase()) > -1).slice(0, 50))
 );
 formatter = (x: {name: string,accountnum :string}) => x.name + ' ('+x.accountnum+')';



  ngOnInit() {
    this.getgroupsaleman();
this.getcostomerauto();
    if(this.Name[0].salegroup==='admin'){
      this.chackuser=true;
      }
      if(this.Name[0].accountnum!=null ||this.Name[0].accountnum!=undefined){
       this.enablecustomer=false;
      }  else {
        this.enablecustomer=true;
      }
  }

  getcostomerauto(){
    var idsale=this.datalogin[0].salegroup;
    if(this.datalogin[0].salegroup==='admin'){
idsale='%20';
    } else {
      idsale=this.datalogin[0].salegroup;
    }

    this.http.get<any>(this.url + 'customerauto/'+idsale).subscribe(res =>{
      this.customers=res;
    })
  }

  Searchsolist() {


    if(this.getcustomer==undefined){
      this.customer='';
    } else {

      this.customer=this.getcustomer.name;
    }
      this.getdate();
      var datasalegroup='';
      if(this.customer=='') {
        this.customer='%20';
      }

      if(this.Name[0].accountnum !=undefined){
        //alert(this.Name[0].accountnum);
        this.discustomer=true;
        this.showtablecustomer=false;
        this.customer=this.Name[0].accountnum;
      }
      if(this.fromdate==''){
        this.fromdate =`${this.fromDate}`;
      }
      if(this.todate==''){
        this.todate=`${this.toDate}`;
      }
      if(this.salegroup==''){
        if(this.datalogin[0].id_group_user=='153Admin' || this.datalogin[0].id_group_user=='1860Administrator'){

          datasalegroup =`${this.salegroup}`;
        }else{

          datasalegroup = `${this.datalogin[0].salegroup}`;
        }
      }

      if(this.salegroup !==''){
        if(this.datalogin[0].id_group_user=='153Admin' || this.datalogin[0].id_group_user=='1860Administrator'){

          datasalegroup =`${this.salegroup}`;
        }else{

          datasalegroup = this.datalogin[0].salegroup;
        }
      }

      if(datasalegroup==''){
        datasalegroup='%20';
      }

      if(this.loaddata !=null && JSON.stringify(this.loaddata) !='[]'){
        datasalegroup=this.loaddata[0].datasalegroup;
    this.customer=this.loaddata[0].customer;
    this.fromdate=this.loaddata[0].fromdate;
    this.todate=this.loaddata[0].todate;
    this.Datatodate= new Date(this.loaddata[0].todate);
    this.Datafromdate= new Date(this.loaddata[0].fromdate);
    this.loaddata=[];
  this.company= this.customer;
      }


      this.solist=[];
this.http.get<any>(this.url +'solist/'+ this.fromdate +'/'+ this.todate +'/'+ datasalegroup +'/'+ this.customer+'/0').subscribe(res => {
  if(res.length > 0) {

    this.savedata=[{
      fromdate:this.fromdate,
      todate:this.todate,
      datasalegroup:datasalegroup,
      customer:this.customer
    }];


    if(datasalegroup=='%20'){
      this.salegroup='';
    }
    if(this.customer=='%20'){
      this.customer='';
    }
   this.solist=res;

  } else {
    if(datasalegroup=='%20'){
      this.salegroup='';
    }
    if(this.customer=='%20'){
      this.customer='';
    }
    this.solist=[];
    alert('ไม่พบข้อมูลที่ค้นหา');
    //this.openModal(true,'ไม่พบข้อมูลที่ค้นหา',false);
  }

 })



  }


  deletesaloder(value) {
    if(confirm('ต้องการลบ รายการที่เลือกใช่หรือไม่')){
      if(value != undefined) {

        var urlpost=`${ this.url }${'delete_sale_line'}/${ value}/${this.datalogin[0].salegroup}`;
        this.http.post(urlpost,'').subscribe(res=> {
        // this.setinheader= setInterval(this.savesaleoderlistsave(),);
         alert('ลบข้อมูลเสร็จสิ้น');
         this.Searchsolist();
        })

      } else {

      }
    } else {
      return;
    }


     }
  editviewsore(valueid) {
    localStorage.setItem('DataSOderlist',JSON.stringify(this.savedata));
    this.savedata=[];
    this.loaddata=[];
    this.router.navigate(['/sorecord'], { queryParams: { idso: valueid } });
      }

      gettype(type){
        if(type==="application/pdf"){
          return true;
        }else{
          return false;
        }
      }

      gettypeNull(type){
        if(type==""){
          return false;
        }else{
          return true;
        }
      }

      getColorFile(type) {
        if(type!==""){
          return '#0317ee';
        }
       }
  getsaloderlist(valueid,check,template: TemplateRef<any>,type,nameFile){

    this.Cktype = this.gettype(type);
    this.CkNull = this.gettypeNull(type);

    this.showIDso=valueid;
    this.nameUrl='http://119.59.112.47/assets/PDF/'+nameFile;
    this.modalRef = this.modalService.show(template, this.configview);
    this.salelistview=[];
        this.http.get<any>(this.url+'find_saleline/'+valueid).subscribe(res =>{
      if(res.length>0){
   this.salelistview=res;
   if(check==true){
    this.printpdf();
   }

      }
    })
  }
  getgroupsaleman(){
    this.DateGroupsaleman=[];
    this.http.get<any>(this.url +'salesman' ).subscribe(res => {
      if(res.length > 0){
        this.Searchsolist();

     this.DateGroupsaleman=res;
          }else{
          alert('ไม่สามารถติดต่อข้อมูลรหัส saleman ได้')
          }
      });
  }
  exportdataexcel() {
   if(this.solist==undefined){
     alert('ไม่พบข้อมูล');
    //this.openModal(true,'ไม่พบข้อมูล',false);
   }else{
    new Angular5Csv(this.solist, 'CustomerList', this.options);
   }

  }

   ThaiNumberToText(Number)
  {
    Number = Number.replace (/๐/gi,'0');
    Number = Number.replace (/๑/gi,'1');
    Number = Number.replace (/๒/gi,'2');
    Number = Number.replace (/๓/gi,'3');
    Number = Number.replace (/๔/gi,'4');
    Number = Number.replace (/๕/gi,'5');
    Number = Number.replace (/๖/gi,'6');
    Number = Number.replace (/๗/gi,'7');
    Number = Number.replace (/๘/gi,'8');
    Number = Number.replace (/๙/gi,'9');
    return 	this.ArabicNumberToText(Number);
  }

   ArabicNumberToText(Number) :string
  {
    var Number = this.CheckNumber(Number);
    var NumberArray = new Array ("ศูนย์", "หนึ่ง", "สอง", "สาม", "สี่", "ห้า", "หก", "เจ็ด", "แปด", "เก้า", "สิบ");
    var DigitArray = new Array ("", "สิบ", "ร้อย", "พัน", "หมื่น", "แสน", "ล้าน");
    var BahtText = "";
    if (isNaN(Number))
    {
      return "ข้อมูลนำเข้าไม่ถูกต้อง";
    } else
    {
      if ((Number - 0) > 9999999.9999)
      {
        return "ข้อมูลนำเข้าเกินขอบเขตที่ตั้งไว้";
      } else
      {
        Number = Number.split (".");
        if (Number[1].length > 0)
        {
          Number[1] = Number[1].substring(0, 2);
        }
        var NumberLen = Number[0].length - 0;
        for(var i = 0; i < NumberLen; i++)
        {
          var tmp = Number[0].substring(i, i + 1) - 0;
          if (tmp != 0)
          {
            if ((i == (NumberLen - 1)) && (tmp == 1))
            {
              BahtText += "เอ็ด";
            } else
            if ((i == (NumberLen - 2)) && (tmp == 2))
            {
              BahtText += "ยี่";
            } else
            if ((i == (NumberLen - 2)) && (tmp == 1))
            {
              BahtText += "";
            } else
            {
              BahtText += NumberArray[tmp];
            }
            BahtText += DigitArray[NumberLen - i - 1];
          }
        }
        BahtText += "บาท";
        if ((Number[1] == "0") || (Number[1] == "00"))
        {
          BahtText += "ถ้วน";
        } else
        {
         var DecimalLen = Number[1].length - 0;
          for (var i = 0; i < DecimalLen; i++)
          {
            var tmp = Number[1].substring(i, i + 1) - 0;
            if (tmp != 0)
            {
              if ((i == (DecimalLen - 1)) && (tmp == 1))
              {
                BahtText += "เอ็ด";
              } else
              if ((i == (DecimalLen - 2)) && (tmp == 2))
              {
                BahtText += "ยี่";
              } else
              if ((i == (DecimalLen - 2)) && (tmp == 1))
              {
                BahtText += "";
              } else
              {
                BahtText += NumberArray[tmp];
              }
              BahtText += DigitArray[DecimalLen - i - 1];
            }
          }
          BahtText += "สตางค์";
        }
        return BahtText;
      }
    }
  }

   CheckNumber(Number){
    var decimal = false;
    Number = Number.toString();
    Number = Number.replace (/ |,|บาท|฿/gi,'');
    for (var i = 0; i < Number.length; i++)
    {
      if(Number[i] =='.'){
        decimal = true;
      }
    }
    if(decimal == false){
      Number = Number+'.00';
    }
    return Number
  }
loaddataprint(value){
  this.router.navigate(['printsaleoderlist'], { queryParams: { idso: value } });
 /* this.http.get<any>(this.url+'find_saleheader/'+value).subscribe(res=>{
    this.salehderprint=res;
    //alert(this.salehderprint[0].dateid);
    //alert(JSON.stringify(res));
    this.getsaloderlist(value,true);

      },error=>{
        //return;
      });*/
}
//สั่งพิมใบ สั่งซื้อ
printpdf() {
  //this.salelistview=[];
//this.salehderprint=[];

  var linesale='';
  var linesalepage2='';
  var item=0;
  if(this.salelistview.length>0 && this.salelistview.length <=18){
for(var i=0;i<this.salelistview.length;i++){
  item++;
    linesale +=`
    <tr style="line-height: 6px" class="coltbleft coltbrigth">
    <td scope="col" class="coltbrigth text-center">`+item+`</td>
    <td scope="col"  class="coltbrigth text-left">`+this.salelistview[i].ItemId+' '+this.salelistview[i].Name+`</td>
    <td scope="col"  width="5%" class="coltbrigth text-right">`+parseInt(this.salelistview[i].SalesQty).toLocaleString()+`</td>
    <td scope="col" width="10%" class="coltbrigth text-right">`+parseInt(this.salelistview[i].PriceUnit).toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2})+`</td>
    <td scope="col" class="coltbrigth text-right">`+this.salelistview[i].IVZ_Percent1_CT+`%</td>
    <td scope="col" class="coltbrigth text-right">`+this.salelistview[i].IVZ_Percent2_CT+`%</td>
    <td scope="col" class="coltbrigth text-right">`+this.salelistview[i].IVZ_Percent2_CT+`%</td>
    <td scope="col" class="text-right">`+parseInt(this.salelistview[i].LineAmount).toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2})+`</td>
  </tr>`;
}
var nu=32-item;

  for (var i=0;i<nu;i++){
    linesale +=`
    <tr  style="line-height: 11px" class="coltbleft coltbrigth">
    <td scope="col" class="coltbrigth text-center">&nbsp;</td>
    <td scope="col" class="coltbrigth text-left">&nbsp;</td>
    <td scope="col" width="5%" class="coltbrigth text-center">&nbsp;</td>
    <td scope="col" class="coltbrigth text-center">&nbsp;</td>
    <td scope="col" class="coltbrigth text-center">&nbsp;</td>
    <td scope="col" class="coltbrigth text-center">&nbsp;</td>
    <td scope="col" class="coltbrigth text-center">&nbsp;</td>
    <td scope="col" class="text-right"></td>
  </tr>`;
  }



  } else if(this.salelistview.length >= 19){
    alert(1.1);
    var item2=19;
    for(var i=0;i<18;i++){
      item++;
      linesale +=`
      <tr style="line-height: 38px" class="coltbleft coltbrigth">
        <td scope="col" class="coltbrigth text-center">`+item+`</td>
        <td scope="col" class="coltbrigth text-left">`+this.salelistview[i].ItemId+' '+this.salelistview[i].Name+`</td>
        <td scope="col" width="5%" class="coltbrigth text-right">`+parseInt(this.salelistview[i].SalesQty).toLocaleString()+`</td>
        <td scope="col" class="coltbrigth text-right">`+parseInt(this.salelistview[i].PriceUnit).toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2})+`</td>
        <td scope="col" class="coltbrigth text-right">`+this.salelistview[i].IVZ_Percent1_CT+`%</td>
        <td scope="col" class="coltbrigth text-right">`+this.salelistview[i].IVZ_Percent2_CT+`%</td>
        <td scope="col" class="coltbrigth text-right">`+this.salelistview[i].IVZ_Percent2_CT+`%</td>
        <td scope="col" class="text-right">`+parseInt(this.salelistview[i].LineAmount).toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2})+`</td>
      </tr>`;
    }
    for(var i=19;i<this.salelistview.length;i++){
     alert(1.2);
      linesalepage2 +=`
        <tr style="line-height: 8px" class="coltbleft coltbrigth">
        <td scope="col" class="coltbrigth text-center">`+item+`</td>
        <td scope="col" class="coltbrigth text-left">`+this.salelistview[i].ItemId+' '+this.salelistview[i].Name+`</td>
        <td scope="col" width="5%" class="coltbrigth text-right">`+parseInt(this.salelistview[i].SalesQty).toLocaleString()+`</td>
        <td scope="col" class="coltbrigth text-right">`+parseInt(this.salelistview[i].PriceUnit).toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2})+`</td>
        <td scope="col" class="coltbrigth text-right">`+this.salelistview[i].IVZ_Percent1_CT+`%</td>
        <td scope="col" class="coltbrigth text-right">`+this.salelistview[i].IVZ_Percent2_CT+`%</td>
        <td scope="col" class="coltbrigth text-right">`+this.salelistview[i].IVZ_Percent2_CT+`%</td>
        <td scope="col" class="text-right">`+parseInt(this.salelistview[i].LineAmount).toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2})+`</td>
      </tr>`;
    item2++;
    }
    var nu=32-item2;

  for (var i=0;i<nu;i++){
    alert(1.3);
    linesalepage2 +=`
    <tr  style="line-height: 5px" class="coltbleft coltbrigth">
    <td scope="col" class="coltbrigth text-center">&nbsp;</td>
    <tdscope="col" class="coltbrigth text-left">&nbsp;</td>
    <td scope="col" width="5%" class="coltbrigth text-center">&nbsp;</td>
    <td scope="col" class="coltbrigth text-center">&nbsp;</td>
    <td scope="col" class="coltbrigth text-center">&nbsp;</td>
    <td scope="col" class="coltbrigth text-center">&nbsp;</td>
    <td scope="col" class="coltbrigth text-center">&nbsp;</td>
    <td scope="col" class="text-right"></td>
  </tr>`;
  }
  }
 var tex=(parseInt(this.salehderprint[0].amount)*7)/100;
var sum=tex+parseInt(this.salehderprint[0].amount);
 var bathstr= this.ArabicNumberToText(sum);
 var haederimg=`<body id="fontpage"  onload="window.print();window.close()">
 <!-- <body> -->
 <div id="fontpage" class="container-fluid">

 <div class="row">
 <div class="col-md-12 offset-0">
 <img src="../../assets/img/BPG.jpg" width="100%">
 <!-- <img style="margin-left: 10%" src="../../assets/img/logo.png"> -->
 </div>
 </div>
         <div class="row">
           <div class="col-md-12 offset-0">
        <table   width="100%" style="margin-top: 3%">
          <thead class="coltbtop coltbbot coltbleft coltbrigth">
            <th colspan="4"  class="text-center" style="background-color: #e0e6e7">ใบเสนอราคา/QUOTATION</th>
          </thead>
          <tbody class="coltbbot"  style="border: 2px;" >
           <tr>
             <td class="coltbleft">  Company : `+this.salehderprint[0].SalesName+`</td>

             <td width="250px" class="coltbbot coltbleft coltbrigth">เลขที่/ No. :`+this.salehderprint[0].id+`</td>
           </tr>
           <tr>
             <td class="coltbbot coltbleft coltbrigth"  rowspan="2"> Address : `+this.salehderprint[0].address+`</td>

             <td  class=" coltbbot coltbrigth coltbleft">วันที่ Date :`+this.salehderprint[0].dateid+`</td>
           </tr>
           <tr>


             <td  class=" coltbbot coltbrigth  coltbleft">TEL. </td>
           </tr>
          </tbody>
        </table>
           </div>
         </div>

         <div class="row">
           <div class="col-md-12 offset-0">
             <label >ขอเสนอราคาและเงื่อนไขสำหรับท่านดังนี้</label><br>
             <label >We are please to submit you the following described here in at price, items and terms stated :</label>
           </div>
           </div>`;
 var haeder=` <html>
 <head>
   <title>ใบเสนอราคา / QUOTATION</title>
   <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />

<meta http-equiv="Content-Language" content="en-us">
<meta http-equiv="Content-Script-Type" content="text/javascript">
<link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.1.3/css/bootstrap.min.css" integrity="sha384-MCw98/SFnGE8fJT3GXwEOngsV7Zt27NXFoaoApmYm81iuXoPkFOJwJ8ERdknLPMO" crossorigin="anonymous">
   <script src="https://code.jquery.com/jquery-3.3.1.js" ></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.14.3/umd/popper.min.js" ></script>
<script src="https://stackpath.bootstrapcdn.com/bootstrap/4.1.3/js/bootstrap.js"></script>
   <style>
.coltbtop{
border-top: 1px solid black;
}
.coltbbot{
border-bottom: 1px solid black;
}
.coltbrigth{
border-right: 1px solid black;
}
.coltbleft{
border-left: 1px solid black;
}
.footertable
{
    position:absolute;
    bottom: 8px;
    right: 16px;
    center:15px;
}
#fontpage{
  font-size: 20px;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}
   </style>
 </head>`;
 var sale=`   <div id="fontpage"  class="container-fluid footertable " >
 <div class="row" style="margin-top: 2%; margin-bottom: 2%; margin-left: 10%">
   <div class="col-12 form-inline">
     <div class="col-4">
       <table  border="1">
         <tbody>
           <tr>
             <td>
             <label class="text-center"> </label><BR>
               <label class="text-center">__________________________________</label><BR>
                 <label class="text-center" >Purchaser/ผู้อนุมัติซื้อ</label>
                 <label  class="text-center">Date____/____/____</label>
             </td>
           </tr>
         </tbody>
       </table>
     </div>
     <div class="col-4">
       <table  border="1">
         <tbody>
           <tr>
             <td>
             <label class="text-center"> </label><BR>
               <label class="text-center">__________________________________</label><BR>
                 <label class="text-center" >Sale/พนักงานขาย</label>
                 <label  class="text-center">Date____/____/____</label>
             </td>
           </tr>
         </tbody>
       </table>
     </div>
     <div class="col-4" >
       <table  border="1">
         <tbody>
           <tr>
             <td>
             <label class="text-center"> </label><BR>
               <label class="text-center">__________________________________</label><BR>
                 <label class="text-center" >Manager/ผู้จัดการฝ่ายขาย</label>
                 <label  class="text-center">Date____/____/____</label>
             </td>
           </tr>
         </tbody>
       </table>
     </div>
   </div>
   </div>
</div>`;
 var foot=`     <tfoot id="fontpage" class="coltbtop coltbbot  coltbrigth coltbleft">
 <tr >
     <td  class=" coltbbot coltbrigth " colspan="3" rowspan="2">หมายเหตุ* ${ this.salehderprint[0].remark}</td>

     <td class="text-center coltbbot coltbrigth" colspan="4">ราคารวมทั้งสิ้น(TOTAL)</td>
     <td class="text-right coltbbot">`+parseInt(this.salehderprint[0].amount).toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2})+`</td>
   </tr>
   <tr>

     <td  class="text-center coltbbot coltbrigth" colspan="4">จำนวนภาษีมูลค่าเพิ่ม</td>
     <td class="text-right coltbbot">`+tex.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2})+`</td>
   </tr>
   <tr>
     <td   class="text-center coltbbot coltbrigth" colspan="3">`+bathstr+`</td>

     <td  class="text-center coltbrigth" colspan="4">รวมทั้งสิ้น(TOTAL)</td>

     <td class="text-right">`+sum.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2})+`</td>
   </tr>
</tfoot>`;
  let  popupWin;
  var pageweb=`
  ${haeder}
${haederimg}
          <div id="fontpage" class="row">
            <div class="col-md-12 offset-0">
              <table cellpadding="7"  width="100%"  style="margin-top: 1%">
              <thead  style="border: 1; border-color: black" >
              <tr class="text-center coltbtop coltbleft  coltbrigth" >
                  <th width="5%" class="coltbrigth">ลำดับที่</th>
                  <th class="coltbrigth">รายการ</th>
                  <th width="5%" class="coltbrigth">จำนวน</th>
                  <th class="coltbrigth">ราคา</th>
                  <th colspan="3" class="coltbrigth">ส่วนลด</th>
                  <th >จำนวนเงิน</th>

              </tr>
              <tr class="text-center coltbbot coltbleft coltbrigth">
                  <th width="5%" class="coltbrigth">ITEM</th>
                  <th class="coltbrigth">DESCRIPTION</th>
                  <th width="5%" class="coltbrigth">Quantity</th>
                  <th class="coltbrigth">Price</th>
                  <th colspan="3" class="coltbrigth">Discount</th>
                  <th>Amount</th>
              </tr>


            </thead>
                <tbody class="coltbbot">
                ${linesale}
                </tbody>
              </table>
                </div>
          </div>
          <div class="row" style="margin-top: 2%; margin-left: 6%">


          ${sale}
      </div>
  </div>
</body>
</html>`;
  if(this.salelistview.length < 19){

    var pageweb=`
    ${haeder}
  ${haederimg}
            <div id="fontpage" class="row">
              <div class="col-md-12 offset-0">
                <table cellpadding="7"  width="100%"  style="margin-top: 1%">
                  <thead  style="border: 1; border-color: black" >
                    <tr class="text-center coltbtop coltbleft  coltbrigth" >
                        <th width="5%" class="coltbrigth">ลำดับที่</th>
                        <th class="coltbrigth">รายการ</th>
                        <th width="5%" class="coltbrigth">จำนวน</th>
                        <th class="coltbrigth">ราคา</th>
                        <th colspan="3" class="coltbrigth">ส่วนลด</th>
                        <th >จำนวนเงิน</th>

                    </tr>
                    <tr class="text-center coltbbot coltbleft coltbrigth">
                        <th width="5%" class="coltbrigth">ITEM</th>
                        <th class="coltbrigth">DESCRIPTION</th>
                        <th width="5%" class="coltbrigth">Quantity</th>
                        <th class="coltbrigth">Price</th>
                        <th colspan="3" class="coltbrigth">Discount</th>
                        <th>Amount</th>
                    </tr>


                  </thead>
                  <tbody class="coltbbot">

                  ${linesale}


                  </tbody>
                  ${foot}
                </table>
                  </div>
            </div>
            <div class="row" style="margin-top: 2%; margin-left: 6%">


            ${sale}
        </div>
    </div>
  </body>
  </html>`;
  } else {
    alert(2);
pageweb +=`
${haeder}
${haederimg}
<div id="fontpage" class="row">
              <div class="col-md-12 offset-0">
                <table cellpadding="7"  width="100%"  style="margin-top: 1%">
                  <thead  style="border: 1; border-color: black" >
                    <tr class="text-center coltbtop coltbleft  coltbrigth" >
                        <th width="5%" class="coltbrigth">ลำดับที่</th>
                        <th class="coltbrigth">รายการ</th>
                        <th width="5%" class="coltbrigth">จำนวน</th>
                        <th class="coltbrigth">ราคา</th>
                        <th colspan="3" class="coltbrigth">ส่วนลด</th>
                        <th >จำนวนเงิน</th>

                    </tr>
                    <tr class="text-center coltbbot coltbleft coltbrigth">
                        <th width="5%" class="coltbrigth">ITEM</th>
                        <th class="coltbrigth">DESCRIPTION</th>
                        <th width="5%" class="coltbrigth">Quantity</th>
                        <th class="coltbrigth">Price</th>
                        <th colspan="3" class="coltbrigth">Discount</th>
                        <th>Amount</th>
                    </tr>


                  </thead>
                  <tbody class="coltbbot">

                  ${linesalepage2}


                  </tbody>
                  ${foot}
                </table>
                  </div>
            </div>
            <div class="row" style="margin-top: 2%; margin-left: 6%">


            ${sale}
        </div>
    </div>
  </body>
  </html>`;
  }


  /*popupWin = window.open();
  popupWin.document.open();
  popupWin.document.write(pageweb);


  popupWin.document.close();*/
console.log(pageweb);
item=0;
}
  openModal(open : boolean,text: string,load:boolean) : void {
    this.mdlSampleIsOpen = open;
    this.alt=text;
    this.checkreload=load;
  }
  closemodel(cl: boolean) {
  this.mdlSampleIsOpen=cl;
  if(this.checkreload==true) {
    location.reload();
  }

  }


  handleFileInput(file: FileList) {
    this.textload="";

if (file.item(0).type =="application/pdf" || file.item(0).type =="image/jpeg" ) {
  this.selectedFile= file.item(0);
  this.textload =  this.selectedFile.name


}else{
  alert('ชนิดไฟล์ไม่ถูกต้อง กรุณาเลือกเป็นไฟล์ .PDF หรือ เป็นไฟล์ .jpeg')
  this.textload="";
}
  }

  OpenUploadPDF(template: TemplateRef<any>,idSo) {
    this.btnPDF=false;
    this.btnREpdf=false;
    this.showIDso=""
    this.showIDso=idSo;
    this.selectedFile=null;
    this.modalRef = this.modalService.show(template, this.config);
  }

  onUpload(showIDso,templateShow: TemplateRef<any>,template: TemplateRef<any>){
    this.modalRef.hide();
    this.textload="กำลังอัพโหลดไฟล์ โปรดรอ.";
    this.openModal2(templateShow);
    const fd = new FormData();
    fd.append('PDF', this.selectedFile, this.selectedFile.name)
                this.http.post<any>(this.url+ showIDso +'/uploadPDF',fd, {
                  reportProgress:true,
                  observe:'events',

                }).subscribe(event => {
                    if(event.type === HttpEventType.UploadProgress){

                    }else if (event.type === HttpEventType.Response)
                    {
                        console.log(event);
                        if (event.body.success === true) {

                          this.textload="อัพโหลดไฟล์เสร็จสิ้น"
                          this.btnPDF=true;
                          this.btnREpdf=false;
                          /*  success: true,
                          _path : DIRPDF,
                          _name : res.req.file.filename,
                          _mimetype : res.req.file.mimetype
                            */

                            this.updataFlie(showIDso,event.body._name,event.body._mimetype);
                       /*   alert(JSON.stringify(event.body))
                          alert('upload เสร็จสิ้น : >>>'+event.body.success )*/

                        }
                        else
                        {
                          this.textload="เกิดปัญหาในการ upload กรุณาทำรายการใหม่"
                          this.btnPDF=false;
                          this.btnREpdf=true;
                          //alert('เกิดปัญหาในการ upload กรุณาทำรายการใหม่' )
                        }
                    }
                });

  }

   updataFlie(_idSo,_name,_mimetype){
    //updataDPF_idSo

   this.http.post<any>(this.url+'updataDPF_idSo',{
    idSo : _idSo,
    _name : _name,
    _mimetype : _mimetype
  }).subscribe(res=> {
      //  alert(res)
      if(res==true){
        this.textload="ทำรายการเสร็จสิ้น"
        this.btnPDF=true;
        this.btnREpdf=false;
      }else{
             this.textload="เกิดปัญหาในการ เพิ่มรายการ SaleOrder"
      this.btnPDF=false;
      this.btnREpdf=true;
      }

      })
   }

   openModal2(templateShow: TemplateRef<any>) {
    this.modalRef = this.modalService.show(templateShow, {class: 'modal-sm' , backdrop: "static"});
  }

  confirm(): void {
    this.modalRef.hide();
    this.Searchsolist();
  }

  decline(template): void {
    this.selectedFile=null;
    this.modalRef.hide();
    this.modalRef = this.modalService.show(template, this.config);

  }

}
