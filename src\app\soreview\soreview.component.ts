import { HttpClient, HttpEventType, HttpHeaders } from '@angular/common/http';
import { Component, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import { NgbCalendar, NgbDateStruct } from '@ng-bootstrap/ng-bootstrap';
import { Angular5Csv } from 'angular5-csv/dist/Angular5-csv';
import { saveAs } from 'file-saver/FileSaver';
import { Observable } from 'rxjs';
import { WebapiService } from '../webapi.service';

import { BsModalService } from 'ngx-bootstrap/modal';
import { BsModalRef } from 'ngx-bootstrap/modal/bs-modal-ref.service';
import { TabsetComponent } from 'ngx-bootstrap/tabs';
import { debounceTime, map } from 'rxjs/operators';

export interface saleoderreview{
  dateid:string;
ShippingDateRequested:string;
id:string;
SalesGroup:string;
SalesId:string;
salesname:string;
amount:number;
discount:number;
price:number;
totalweight:number;
DlvMode:string;
paymenttype	:string;
statest:string;
CustomerRef:string;
remark:string;
vattype:string;
state:string;
textstatus:string;
stcheck:number;
checksync:boolean;
timeedit:string;
lastupdate:string;
filetype :string;
filename :string;
InvName:string;
DeliveryName:string;
regnum:string;
}

export interface syncid{
  idso:string;
}

@Component({
  selector: 'app-soreview',
  templateUrl: './soreview.component.html',
  styleUrls: ['./soreview.component.css']

})



export class SoreviewComponent implements OnInit {

  @ViewChild('staticTabs') staticTabs: TabsetComponent;
  getColorst(disst) {
    if(disst==1){
     return '#05C1FF';
    } else if(disst=3) {
     return '#FF9505';
    } else if(disst==2){
      return '#C305FF';
    }else if(disst==3){
     return '#FF05D5';
   }else if(disst==4){
     return '#0AC103';
   }



     }

     getColorsalegroup(disst) {

       return 'rgb(55, 116, 230)';
     }





     getclosesyncdata(disst) {
     /* if(disst==3){
        return 'none';
      } else if(disst==1){
        return 'block';
      }*/
      return 'block';
    }


  getColordis1(disst) {
    var st =disst;
    var fi =st.substring(0,1);
    switch (fi) {
      case '1':
        return 'red';
    }
  }

  getColordis2(disst) {
    var st =disst;
    var fi =st.substring(1,2);
    switch (fi) {
      case '1':
        return 'red';
    }
  }
  getColordis3(disst) {
    var st =disst;
    var fi =st.substring(2,3);
    switch (fi) {
      case '1':
        return 'red';
    }
  }

  getColorFile(type) {
    if(type!==""){
      return '#0317ee';
    }
   }

  mdlSampleIsOpen : boolean = false;
  alt='';
  checkreload=true;

savefile: saveAs;
  options = {
    fieldSeparator: ',',
    quoteStrings: '"',
    decimalseparator: '.',
    showLabels: true,
    showTitle: true,
    useBom: true,
    noDownload: false,
    headers: ["เลขที่ SO", "วันที่","พนักงานขาย",'ลูกค้า','มูลค่าสินค้า','มูลค่าสุทธิ','VAT/No VAT',"น้ำหนักรวม","ประเภทขนส่ง","เงินสด/เครดิต","Note ภายใน"]
  };


  salegroup='';
  customer='';
  url: string;
  soreviewlist: any[];
  DateGroupsaleman:any[]=[];


  settimedownload:any;
  settimedownloadext:any;


  getcustomer:any;
  datalogin: any[]=[];
  groupsale:string;
  testclose: boolean;
  fromdate= '';
  todate='';
  fromDate: NgbDateStruct;
  toDate: NgbDateStruct;
  loaddata:any[]=[];
  Datatodate:any;
Datafromdate:any;
permisstiondata:any[]=[];
exportbtn=true;
searchbtn=true;
selectallsync=false;
saleoderreviewdata:saleoderreview[]=[];
syncidso:syncid[]=[];
savedata:any[]=[];
salelistview:any[]=[];
head: HttpHeaders;
btnloadfile:boolean;
testtodate:Date;
company='ค้นหาลูกค้า';
customers:any[]=[];
typesync='1';
syncbtn=false;
ennablecustomer=false;
private rootSelectionString: string = ".js-rssfeed";
modalRef: BsModalRef;
config = {
  ignoreBackdropClick: true,
  class : 'modal-lg modal-dialog800'
};
configAddflie = {
  ignoreBackdropClick: true,
  class : 'modal-md'
};
showIDso;
textload;
selectedFile =null;

btnPDF=false;
btnREpdf=false;

Cktype;
CkNull;
nameUrl:string;
  constructor(private modalService: BsModalService, private http: HttpClient, private service: WebapiService, private router: Router, private calendar: NgbCalendar) {

    localStorage.removeItem('DataSOderlist');
    this.btnloadfile=false;
    this.url=service.geturlservice();
this.fromDate = calendar.getPrev(calendar.getToday(), 'd', 30);
this.toDate = calendar.getNext(calendar.getToday(), 'm', 3);
this.datalogin=JSON.parse(sessionStorage.getItem('login'))
this.fromdate='';
this.todate='';
this.loaddata=JSON.parse(localStorage.getItem('DataSOderreview'));
this.Datatodate= new Date(this.toDate.year,this.toDate.month-1,this.toDate.day);
this.Datafromdate= new Date(this.fromDate.year, this.fromDate.month-1, this.fromDate.day);
this.getdate();
this.getuser();
this.permisstiondata=JSON.parse(sessionStorage.getItem('menu'));



if (this.datalogin==null){
  this.router.navigate(['login']);
   }else{
    this.getuser();

    this.exportbtn=!this.permisstiondata[5].flag_print;
    this.searchbtn=!this.permisstiondata[5].flag_action;
   if(this.searchbtn==false){
    this.syncbtn=true;
   }

   }

   }
   getuser(){
     if(this.datalogin!=undefined){
      if(this.datalogin[0].id_group_user=='153Admin' || this.datalogin[0].id_group_user=='1860Administrator'){
        this.groupsale='';
        this.testclose=true;
      }else{
        this.testclose=false;
       this.salegroup=this.datalogin[0].salegroup;
      }
     }

  }

  getdate(){
    this.fromdate = `${this.Datafromdate.getFullYear()}-${this.Datafromdate.getMonth()+1}-${this.Datafromdate.getDate()}`;
    this.todate = `${this.Datatodate.getFullYear()}-${this.Datatodate.getMonth()+1}-${this.Datatodate.getDate()}`;
   }
   search = (text$: Observable<any>) =>

   //Autocomplete ลูกค้า
 text$.pipe(
   debounceTime(200),
   map(term => term === '' ? []
     : this.customers.filter(v => v.name.toLowerCase().indexOf(term.toLowerCase()) > -1 ||  v.accountnum.toLowerCase().indexOf(term.toLowerCase()) > -1).slice(0, 50))
 );
 formatter = (x: {name: string,accountnum :string}) => x.name + ' ('+x.accountnum+')';

  ngOnInit() {
    this.getgroupsaleman();
    this.getcostomerauto();
    if(this.datalogin[0].accountnum != undefined){
  this.ennablecustomer=true;
     }
  }

  getcostomerauto(){
    var idsale=this.datalogin[0].salegroup;
    if(this.datalogin[0].id_group_user=='153Admin' || this.datalogin[0].id_group_user=='1860Administrator'){ //.salegroup==='admin'
idsale='%20';
    } else {
      idsale=this.datalogin[0].salegroup;
    }

    this.http.get<any>(this.url + 'customerauto/'+idsale).subscribe(res =>{
      this.customers=res;
    })
  }

  SalesOrderdownload(){

location.href='http://*************/node/apinano/api/downloadfile/SalesOrder.txt';
clearInterval(this.settimedownload);
this.Searchsolist();
  }

  loadsalEXTfile(){

    if(this.syncidso.length<1){
      //alert('กรุณาเลือกข้อมูลที่ต้องการ Sync');
  return;
      } else {
        this.settimedownloadext =setInterval(()=> this.SalesEXTdownload(),500);
      }

  }
  SalesEXTdownload(){
    location.href='http://*************/node/apinano/api/downloadfile/SalesEXT.txt'
    clearInterval(this.settimedownloadext);
      }
  downloadsyncfile() {

if(confirm('ต้องการ Dowload TXT File ใช่หรือไม่')){
this.http.get<any>(this.url+'checkfile').subscribe(res=>{

},error=>{
if(error.status==200){
  location.href=('http://*************/node/apinano/api/downloadfile');
  this.btnloadfile=false;

} else  {
  alert('ไม่พบไฟล์ที่ ค้นหา');
}

})


} else {
return;
}

  }

  gettype(type){
    if(type==="application/pdf"){
      return true;
    }else{
      return false;
    }
  }

  gettypeNull(type){
    if(type==""){
      return false;
    }else{
      return true;
    }
  }

  getsaloderlist(valueid,check,template: TemplateRef<any>,type,nameFile){
    this.salelistview=[];
    this.showIDso=valueid;
    this.Cktype = this.gettype(type);
    this.CkNull = this.gettypeNull(type);
   // alert(this.CkNull)
   //this.nameUrl= 'assets/PDF/'+ nameFile;
     this.nameUrl='http://*************/assets/PDF/'+nameFile;
    // this.nameUrl='http://*************/assets/PDF/SW-1811admin-0240-1543213724302.PDF';
   // alert( this.nameUrl +'/'+ this.Cktype)
        this.http.get<any>(this.url+'find_saleline/'+valueid).subscribe(res =>{
      if(res.length>0){
   this.salelistview=res;
   this.modalRef = this.modalService.show(template, this.config);
   if(check==true){
    //this.printpdf();
   }

      }
    })
  }


  OpenUploadPDF(template: TemplateRef<any>,idSo) {
    this.btnPDF=false;
    this.btnREpdf=false;
    this.showIDso=""
    this.showIDso=idSo;
    this.selectedFile=null;
    this.modalRef = this.modalService.show(template, this.configAddflie);
  }


snydatatoax(value){

/*this.http.post<any>('syncso/Service.asmx/SyncData','idSo:'+value).subscribe(res=>{
  alert(res);
},error=>{
  alert(JSON.stringify(error));
})*/
  const Http = new XMLHttpRequest();
const url='syncso/Service.asmx/SyncData?idSo='+value;
Http.open("GET", url);
Http.send();

Http.onreadystatechange=(e)=>{
if(Http.readyState==4 && Http.status==200){
this.btnloadfile=true;
alert('Sync ข้อมูล เสร็จสิ้น');
this.Searchsolist();
}
}
}


  getgroupsaleman(){
    this.DateGroupsaleman=[];
    this.http.get<any>(this.url +'salesman' ).subscribe(res => {
      if(res.length > 0){
        this.Searchsolist();
     this.DateGroupsaleman=res;
          }else{
          alert('ไม่สามารถติดต่อข้อมูลรหัส saleman ได้');
          }
      });
  }
  deletesaloder(value) {
    if(confirm('ต้องการลบ รายการที่เลือกใช่หรือไม่')){
      if(value != undefined) {

        var urlpost=`${ this.url }${'delete_sale_line'}/${ value}/${this.datalogin[0].salegroup}`;
        this.http.post(urlpost,'').subscribe(res=> {
        // this.setinheader= setInterval(this.savesaleoderlistsave(),);
         alert('ลบข้อมูลเสร็จสิ้น');
         this.Searchsolist();
        })

      } else {

      }
    } else {
      return;
    }


     }

  Searchsolist() {
    this.getdate();

    if(this.getcustomer==undefined){
      this.customer='';
    } else {

      this.customer=this.getcustomer.name;
    }
      var datasalegroup='';
      if(this.customer=='') {
        this.customer='%20';
      }

      if(this.datalogin[0].accountnum !=undefined){
        this.customer=this.datalogin[0].accountnum;
      }

      if(this.fromdate==''){
        this.fromdate =`${this.fromDate}`;
      }
      if(this.todate==''){
        this.todate=`${this.toDate}`;
      }
      if(this.groupsale==''){
        if(this.datalogin[0].id_group_user=='153Admin' || this.datalogin[0].id_group_user=='1860Administrator'){
          datasalegroup =`${this.salegroup}`;
        }else{
          datasalegroup = `${this.datalogin[0].salegroup}`;
        }
      }

      if(this.groupsale !==''){
        if(this.datalogin[0].id_group_user=='153Admin' || this.datalogin[0].id_group_user=='1860Administrator'){
          datasalegroup =`${this.salegroup}`;
        }else{
          datasalegroup = `${this.datalogin[0].salegroup}`;
        }
      }

      if(datasalegroup==''){
        datasalegroup='%20';
      }

      if(this.loaddata !=null && JSON.stringify(this.loaddata) !='[]'){
        datasalegroup=this.loaddata[0].datasalegroup;
        this.customer=this.loaddata[0].customer;
        this.fromdate=this.loaddata[0].fromdate;
        this.todate=this.loaddata[0].todate;
        this.Datatodate= new Date(this.loaddata[0].todate);
this.Datafromdate= new Date(this.loaddata[0].fromdate);
        //alert(JSON.stringify(this.loaddata));
        this.company= this.customer;

        this.loaddata=[];
          }

      this.soreviewlist=[];
      this.saleoderreviewdata=[];
      this.savedata=[];
      this.http.get<any>(this.url +'solist_review/'+this.fromdate+'/'+this.todate +'/'+datasalegroup+'/' + this.customer+'/'+this.typesync).subscribe(res => {
        if(res.length > 0) {

    this.savedata=[{
      fromdate:this.fromdate,
      todate:this.todate,
      datasalegroup:datasalegroup,
      customer:this.customer
    }];
          if(datasalegroup=='%20'){
            this.salegroup='';
          }
          if(this.customer=='%20'){
            this.customer='';
          }
          this.soreviewlist=res;
          for(var i=0;i<res.length;i++){
this.saleoderreviewdata.push({
  dateid:res[i].dateid,
  ShippingDateRequested:res[i].ShippingDateRequested,
  id:res[i].id,
  SalesGroup:res[i].SalesGroup,
  SalesId:res[i].SalesId,
  salesname:res[i].salesname,
  amount:res[i].amount,
  discount:res[i].discount,
  price:res[i].price,
  totalweight:res[i].totalweight,
  DlvMode:res[i].DlvMode,
  paymenttype	:res[i].paymenttype,
  statest:res[i].statest,
  CustomerRef:res[i].CustomerRef,
  remark:res[i].remark,
  vattype:res[i].vattype,
  state:res[i].state,
  textstatus:res[i].textstatus,
  stcheck:res[i].stcheck,
  checksync:false,
  timeedit:res[i].timeedit,
  lastupdate:res[i].lastupdate,
  filetype :res[i].filetype,
  filename :res[i].filename,
  InvName:res[i].InvName,
  DeliveryName:res[i].DeliveryName,
  regnum:res[i].regnum
});
          }
          //alert(JSON.stringify(this.saleoderreviewdata));
        }else {
          this.soreviewlist=[];
          this.saleoderreviewdata=[];
          if(datasalegroup=='%20'){
            this.salegroup='';
          }
          if(this.customer=='%20'){
            this.customer='';
          }
          alert('ไม่พบข้อมูลที่ค้นหา');
          //this.openModal(true,'ไม่พบข้อมูลที่ค้นหา',false);
        }
      },error =>{
        alert('เกิดปัญหาในการ Process ข้อมูล กรุณาลองใหม่อีกครั้ง');
        //this.openModal(true,'เกิดปัญหาในการ Process ข้อมูล กรุณาลองใหม่อีกครั้ง',false);
      });


  }

  selectallsyncdata(valuecheck){
    //alert(valuecheck);
    if(valuecheck==true && this.searchbtn==false){
      this.syncbtn=false;
    } else {
      this.syncbtn=true;
    }
    this.selectallsync=valuecheck;
for(var i=0;i<this.saleoderreviewdata.length;i++){

    this.saleoderreviewdata[i].checksync=valuecheck;


}

  }

  selectsingel(i,valuecheck){
    if(valuecheck==true && this.searchbtn==false){
      this.syncbtn=false;
    } else {
      this.syncbtn=true;
    }
  this.saleoderreviewdata[i].checksync=valuecheck;
  var ch =0;
  for(var x=0;x<this.saleoderreviewdata.length;x++){
if(this.saleoderreviewdata[x].checksync==true){
  this.syncbtn=false;
ch++;
} else {
  ch--;
}
  }
  //alert('ch='+ch+'  data= '+this.saleoderreviewdata.length);
  if(ch==this.saleoderreviewdata.length){
    this.selectallsync=true;
    //alert(1);
  } else{
    this.selectallsync=false;
    //alert(2);
  }

  }

  syncdatasaleoder(){
    var idsync='';
    this.syncidso=[];
  //lert(this.saleoderreviewdata.length);
   //return;
    if(this.saleoderreviewdata.length<1){
    //alert('กรุณาเลือกข้อมูลที่ต้องการ Sync');

    } else {
      for (var i=0;i<this.saleoderreviewdata.length;i++){
      if(this.saleoderreviewdata[i].checksync==true){
        this.syncidso.push({
          idso:this.saleoderreviewdata[i].id
        });
      }

      }

    }

    if(this.syncidso.length>0){
for(var i=0;i<this.syncidso.length;i++){
  if(this.syncidso.length==1){
idsync+=',|'+this.syncidso[i].idso+'|'
  } else {
    idsync+=',|'+this.syncidso[i].idso+'|'
  }
}
  const Http = new XMLHttpRequest();
  const url='syncso/Service.asmx/SyncData?idSo='+idsync.substring(1);
  Http.open("GET", url);
  Http.send();

  Http.onreadystatechange=(e)=>{
  if(Http.readyState==4 && Http.status==200){
   this.settimedownload=setInterval(()=> this.SalesOrderdownload(),500);
  }
  }
this.btnloadfile=true;

//alert('Sync ข้อมูล เสร็จสิ้น');
this.Searchsolist();
    }else {
      alert('กรุณาเลือกข้อมูลที่ต้องการ Sync');
      return;
    }
  }

  exportdataexcel() {
if(this.soreviewlist==undefined){
  alert('ไม่พบข้อมูล');
  //this.openModal(true,'ไม่พบข้อมูล',false);
}else {
  new Angular5Csv(this.soreviewlist, 'Soreview', this.options);
}

  }

  editviewsore(valueid) {

    localStorage.setItem('DataSOderreview',JSON.stringify(this.savedata));
    this.savedata=[];
   // alert('Fucntion กำลัง ปิดปรับปรุง');
//this.openModal(true,'Fucntion กำลัง ปิดปรับปรุง',false);
this.loaddata=[];
this.router.navigate(['/editsaloderreview'], { queryParams: { idso: valueid } });
  }

  openModal(open : boolean,text: string,load:boolean) : void {
    this.mdlSampleIsOpen = open;
    this.alt=text;
    this.checkreload=load;
  }
  closemodel(cl: boolean) {
  this.mdlSampleIsOpen=cl;
  if(this.checkreload==true) {
    location.reload();
  }

  }




  selectTab(tabId: number) {
    this.staticTabs.tabs[tabId].active = true;
  }


  onUpload(showIDso,templateShow: TemplateRef<any>,template: TemplateRef<any>){
    this.modalRef.hide();
    this.textload="กำลังอัพโหลดไฟล์ โปรดรอ.";
    this.openModal2(templateShow);
    const fd = new FormData();
    fd.append('PDF', this.selectedFile, this.selectedFile.name)
                this.http.post<any>(this.url+ showIDso +'/uploadPDF',fd, {
                  reportProgress:true,
                  observe:'events',

                }).subscribe(event => {
                    if(event.type === HttpEventType.UploadProgress){

                    }else if (event.type === HttpEventType.Response)
                    {
                        console.log(event);
                        if (event.body.success === true) {

                          this.textload="อัพโหลดไฟล์เสร็จสิ้น"
                          this.btnPDF=true;
                          this.btnREpdf=false;
                          /*  success: true,
                          _path : DIRPDF,
                          _name : res.req.file.filename,
                          _mimetype : res.req.file.mimetype
                            */

                            this.updataFlie(showIDso,event.body._name,event.body._mimetype);
                       /*   alert(JSON.stringify(event.body))
                          alert('upload เสร็จสิ้น : >>>'+event.body.success )*/

                        }
                        else
                        {
                          this.textload="เกิดปัญหาในการ upload กรุณาทำรายการใหม่"
                          this.btnPDF=false;
                          this.btnREpdf=true;
                          //alert('เกิดปัญหาในการ upload กรุณาทำรายการใหม่' )
                        }
                    }
                });

  }

  updataFlie(_idSo,_name,_mimetype){
    //updataDPF_idSo

   this.http.post<any>(this.url+'updataDPF_idSo',{
    idSo : _idSo,
    _name : _name,
    _mimetype : _mimetype
  }).subscribe(res=> {
      //  alert(res)
      if(res==true){
        this.textload="ทำรายการเสร็จสิ้น"
        this.btnPDF=true;
        this.btnREpdf=false;
      }else{
             this.textload="เกิดปัญหาในการ เพิ่มรายการ SaleOrder"
      this.btnPDF=false;
      this.btnREpdf=true;
      }

      })
   }

   openModal2(templateShow: TemplateRef<any>) {
    this.modalRef = this.modalService.show(templateShow, {class: 'modal-sm' , backdrop: "static"});
  }

  confirm(): void {
    this.modalRef.hide();
    this.Searchsolist();
  }

  decline(template): void {
    this.selectedFile=null;
    this.modalRef.hide();
    this.modalRef = this.modalService.show(template, this.config);

  }

  handleFileInput(file: FileList) {
    this.textload="";

if (file.item(0).type =="application/pdf" || file.item(0).type =="image/jpeg" ) {
  this.selectedFile= file.item(0);
  this.textload =  this.selectedFile.name


}else{
  alert('ชนิดไฟล์ไม่ถูกต้อง กรุณาเลือกเป็นไฟล์ .PDF หรือ เป็นไฟล์ .jpeg')
  this.textload="";
}
  }
}
