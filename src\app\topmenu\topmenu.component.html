<div class="container-fluid ">
<nav class="navbar navbar-expand-xl navbar-dark bg-success fixed-top">
    <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarResponsive" aria-controls="navbarResponsive"
        aria-expanded="false" aria-label="Toggle navigation">
        <span class="navbar-toggler-icon"></span>
    </button>  
  <!--  <a class="navbar-brand justify-content-center " href="" [routerLink]="['/customerlist']">Sale Order Online</a>  -->  
  <a class="navbar-brand justify-content-center " href="{{nameUrl}}" target="_blank"  tooltip="คู่มือการใช้งานระบบ"  placement="bottom" >Sale Order Online</a> 
  
  <div><a class="navbar-brand justify-content-center " href=""></a></div>
      <div class="collapse navbar-collapse" id="navbarResponsive">
          <ul class="navbar-nav mr-auto">
              <li class="nav-item dropdown active" *ngIf="Master">
                  <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-toggle="dropdown" aria-haspopup="true"
                      aria-expanded="false">
                      Master Data
                  </a>
                  <div class="dropdown-menu" aria-labelledby="navbarDropdown">
                      <!--<a *ngIf="flag[0].flag_view"  class="dropdown-item" href="#" [routerLink]="['/']">Master Data Sync</a>
                      <a  *ngIf="flag[1].flag_view" class="dropdown-item" href="#" [routerLink]="['/']">Sale Order Sync</a>
                      <div class="dropdown-divider"></div>-->
                      <a  *ngIf="flag[0].flag_view" class="dropdown-item" href="" [routerLink]="['/customerlist']">Customer</a>
                      <a *ngIf="flag[1].flag_view"  class="dropdown-item" href="" [routerLink]="['/productlist']">Product List</a>
                      <a *ngIf="flag[2].flag_view"  class="dropdown-item" href="" [routerLink]="['/pricemaster']">Price Master</a>
                  </div>
              </li>

              <li class="nav-item dropdown active" *ngIf="Sale" >
                  <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-toggle="dropdown" aria-haspopup="true"
                      aria-expanded="false">
                      Sale Order
                  </a>
                  <div class="dropdown-menu" aria-labelledby="navbarDropdown">
                      <a *ngIf="flag[3].flag_view" class="dropdown-item" href="" [routerLink]="['/sorecord']">Sale Order</a>
                      <a *ngIf="flag[4].flag_view" class="dropdown-item" href="" [routerLink]="['/solist1']">Sale Order List</a>
                      <a  *ngIf="flag[5].flag_view" class="dropdown-item" href="" [routerLink]="['/soreview']">Sale Order Review</a>
                      <div class="dropdown-divider"></div>
                      <a  *ngIf="flag[7].flag_view" class="dropdown-item" href="" [routerLink]="['/salestatus']">Sales Order Status</a>
                      <a  *ngIf="flag[6].flag_view" class="dropdown-item" href="" [routerLink]="['/sohistory']">Sale Order History</a>
                      <a  *ngIf="flag[8].flag_view" class="dropdown-item" href="" [routerLink]="['/salestock']">Sale Stock</a>
                  </div>
              </li>
              <li class="nav-item dropdown active" *ngIf="Invoice">
                  <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-toggle="dropdown" aria-haspopup="true"
                      aria-expanded="false">
                      Invoice
                  </a>
                  <div class="dropdown-menu" aria-labelledby="navbarDropdown">
                      <a *ngIf="flag[9].flag_view" class="dropdown-item" href="" [routerLink]="['/invoicecashlist']">Cash Invoice List</a>
                      <a *ngIf="flag[10].flag_view" class="dropdown-item" href="" [routerLink]="['/invoicecreditlist']">Credit Invoice List</a>
                      <div class="dropdown-divider"></div>
                      <a *ngIf="flag[11].flag_view" class="dropdown-item" href="" [routerLink]="['/completeinvoice']">Completed Invoice List</a>
                  </div>
              </li>
              <li class="nav-item dropdown active" *ngIf="Transport">
                  <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-toggle="dropdown" aria-haspopup="true"
                      aria-expanded="false">
                      Transport Info
                  </a>
                  <div class="dropdown-menu" aria-labelledby="navbarDropdown">
                      <a *ngIf="flag[12].flag_view"  class="dropdown-item" href="" [routerLink]="['/masterpackage']">Master Package</a>
                      <a *ngIf="flag[13].flag_view"  class="dropdown-item" [routerLink]="['/productgroup']">Grouping Product</a>
                      <div class="dropdown-divider"></div>
                      <a *ngIf="flag[14].flag_view" class="dropdown-item" href="" [routerLink]="['/coverpage']">Cover Page</a>
                  </div>
              </li>
             <!-- <li class="nav-item dropdown active" *ngIf="Report">
                <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                    Report
                </a>
                <div class="dropdown-menu" aria-labelledby="navbarDropdown">
                    <a  *ngIf="flag[15].flag_view" class="dropdown-item" href="sohistory.html" [routerLink]="['/sohistory']">Sale Order History</a>
                    <div class="dropdown-divider"></div>
                    <a *ngIf="flag[16].flag_view"  class="dropdown-item" href="sumbyproduct.html" [routerLink]="['/sumbyproduct']">Sale Summary By Product</a>
                    <a *ngIf="flag[17].flag_view"  class="dropdown-item" href="#">Sale Summary By Customer</a>
                    <a *ngIf="flag[18].flag_view"  class="dropdown-item" href="#">Sale Summary By Salesman</a>
                    <div class="dropdown-divider"></div>
                    <a *ngIf="flag[19].flag_view" class="dropdown-item" href="" [routerLink]="['/trendproduct']">Sale Order Trend By Product</a>
                    <a *ngIf="flag[20].flag_view" class="dropdown-item" href="#">Sale Order Trend By Customer</a>
                    <a *ngIf="flag[21].flag_view" class="dropdown-item" href="#">Sale Order Trend By Salesman</a>
                </div>
            </li>-->
              <li class="nav-item dropdown active" *ngIf="Security">
                  <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-toggle="dropdown" aria-haspopup="true"
                      aria-expanded="false">
                      Security Control
                  </a>
                  <div class="dropdown-menu" aria-labelledby="navbarDropdown">
                      <a *ngIf="flag[15].flag_view" class="dropdown-item" href="" [routerLink]="['/users']">User Accounts</a>
                      <a *ngIf="flag[16].flag_view" class="dropdown-item" href="" [routerLink]="['/permission']">Permission Setting</a>
                  </div>
              </li>
              <li class="nav-item dropdown active">
                <a class="nav-link " (click)="logoutbtn()" href="" id="navbarDropdown" role="button"  aria-haspopup="true"
                aria-expanded="false">
             Logout
            </a>
              </li>
        
                    <li class="nav-item dropdown active">
                            <a class="nav-link" id="navbarDropdown" role="button" data-toggle="dropdown" aria-haspopup="true"
                            aria-expanded="false">
                         Welcome  {{Name[0].name_user}}
                        </a>
                          </li>
                
            
          </ul>
      </div>
</nav>

<div class="modal fade" id="exampleModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="exampleModalLabel">Upload Invoice</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="input-group">
                    <div class="custom-file">
                        <input type="file" class="custom-file-input" id="inputGroupFile04">
                        <label class="custom-file-label" for="inputGroupFile04">Choose file</label>
                    </div>

                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary">Upload</button>
            </div>
        </div>
    </div>
</div>
</div>