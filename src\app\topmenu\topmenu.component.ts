import { LoginComponent } from './../login/login.component';
import { WebapiService } from './../webapi.service';
import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '../../../node_modules/@angular/router';
import { HttpClient } from '@angular/common/http';
import { login } from '../entites/login.entites';
import { menulist } from '../entites/menulist.entites';
@Component({
  selector: 'app-topmenu',
  templateUrl: './topmenu.component.html',
  styleUrls: ['./topmenu.component.css'],
})
export class TopmenuComponent implements OnInit {
  mdlSampleIsOpen : boolean = false;
  alt='';
  checkreload=true;

  public nameuser: login;
  public menud: menulist;
  menulit :any[]=[];
  url: string;
  Name:any;
  allname: any;
  idgroupuser :string;
  flag: any[]=[];
  Master = true;
  Sale = true;
  Invoice =true;
  Transport = true;
  Report = true;
  Security = true;
  timerInterval:any;
  menulog:any[]=[];
  memu:any;
  nameUrl='assets/img/default-image.png'
  CKuser='';
  constructor( private route: ActivatedRoute, private rout: Router, private http: HttpClient, private servive: WebapiService) {
    this.url = this.servive.geturlservice();  


    this.getsessionStorage();
    this.setpermissionmenu(this.memu);
/*if(this.Name !=[]){
  alert('1')
  if(this.Name==[]){
    this.http.get<any>(this.url + 'find_permission/' + this.Name[0].id_group_user).subscribe((res: menulist[]) => {
      if(res.length>0) {
        this.setpermissionmenu(res);
        
      } 
    
        },error =>{
       
          console.log(error);
        })   
    } else {
      alert('2')
 this.setpermissionmenu(this.memu);
    
    }
}else{
  this.rout.navigate(['login']);
} */
  
  }

  
  getPDF(){
     
  }


  getsessionStorage(){
    if(JSON.parse(sessionStorage.getItem('login'))==null || JSON.parse(sessionStorage.getItem('menu'))==null){
      this.rout.navigate(['login']);
      sessionStorage.clear();
    }else{
     
      this.Name=JSON.parse(sessionStorage.getItem('login'))
      // this.Name=servive.setuserlogin();
       //alert(JSON.stringify(this.Name[0].id_group_user));
       this.memu=JSON.parse(sessionStorage.getItem('menu'))
  
       this.CKuser= sessionStorage.getItem('salegroup')
       this.CKusrt(this.CKuser)
    }
  }


  CKusrt(user){
  if(user == null){
    this.nameUrl='assets/img/ระบบการสั่งซื้อสินค้าออนไลน์(Customer).pdf'
  }else{
    this.nameUrl='assets/img/ระบบการสั่งซื้อสินค้าออนไลน์(Admin).pdf'
  }
  }

  logoutbtn() {
    sessionStorage.clear();
    localStorage.clear();
    this.servive.getuserlogin(undefined);
    this.servive.getmenulogin(undefined);
    this.servive.getalert(false);
    this.rout.navigate(['login']);
    sessionStorage.removeItem('cashDateTo');
    sessionStorage.removeItem('cashDateFrom');
    sessionStorage.removeItem('cecreditDateFrom');
    sessionStorage.removeItem('cecreditDateFrom');
  }

  clicklogout(){
    this.openModal(true,'ออกจากระบบเสร็จสิ้น',true);
  }
  ngOnInit() {
  
    
}
  setpermissionmenu(value: any){
  
    if (value != undefined) {
      this.flag=value;
      /*alert(this.flag[0].flag_view);*/
      if(this.flag[0].flag_view == false &&this.flag[1].flag_view == false &&this.flag[2].flag_view == false ) {
       this.Master = false;
      }
      if(this.flag[3].flag_view == false &&this.flag[4].flag_view == false &&this.flag[5].flag_view == false &&this.flag[6].flag_view == false ) {
        this.Sale = false;
      }
      if (this.flag[7].flag_view == false &&this.flag[8].flag_view == false &&this.flag[9].flag_view == false ) {
   this.Invoice = false;
      }
      if(this.flag[10].flag_view == false &&this.flag[11].flag_view == false &&this.flag[12].flag_view == false ) {
        this.Transport = false;
   
      }
      if (this.flag[13].flag_view == false &&this.flag[14].flag_view == false ) {
   this.Security = false;
      }
        }else {
          
          this.Master = false;
          this.Sale = false;
          this.Invoice = false;
          this.Transport = false;
          this.Security = false;
          this.rout.navigate[''];
        }
  }
  get alldetailuser() {
    return this.nameuser;
  }
  testclick() {
    alert('ok');
  }

/*sandhome() {
  this.router.navigate(['users']);
}*/

openModal(open : boolean,text: string,load:boolean) : void {
  this.mdlSampleIsOpen = open;
  this.alt=text;
  this.checkreload=load;
}
closemodel(cl: boolean) {
this.mdlSampleIsOpen=false;
if(this.checkreload==true) {
  this.logoutbtn();
}

}
}
