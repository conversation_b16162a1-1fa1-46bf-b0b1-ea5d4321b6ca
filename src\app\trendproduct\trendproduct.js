
    //$(function () {
    //    //Initialize Select2 Elements
    //    $('.select2').select2()
    //})

var randomScalingFactor = function() {
    return Math.ceil(Math.random() * 10.0) * Math.pow(10, <PERSON>.ceil(Math.random() * 5));
};

var config = {
    type: 'line',
    data: {
        labels: ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'November', 'December'],
        datasets: [
            {
                label: 'HB45-18W',
            backgroundColor: window.chartColors.blue,
            borderColor: window.chartColors.blue,
            fill: false,
            data: [500, 460, 420, 400, 380, 260, 200,180,370,400,330],},
         {
             label: 'NNIE16',
             backgroundColor: window.chartColors.red,
             borderColor: window.chartColors.red,
             fill: false,
             data: [400, 560, 520, 300, 580, 160, 400, 280, 470, 500, 630],},
         {
             label: 'BM-30CN2P15A',
             backgroundColor: window.chartColors.orange,
             borderColor: window.chartColors.orange,
             fill: false,
             data: [300, 460, 420, 300, 480, 360, 400, 380, 270, 400, 430],},
        ]
    },
    options: {
        responsive: true,
        title: {
            display: true,
            text: 'Sale Order Trend By Product'
        },
        scales: {
            xAxes: [{
                display: true,
            }],
            yAxes: [{
                display: true,
                scaleLabel: {
                    display: true,
                    labelString: 'Value'
                }
            }]
        }
    }
};

$(document).ready(function () {
    alert('ok');
    $('.js-example-basic-multiple').select2();
});

window.onload = function() {    
    var ctx = document.getElementById('canvas').getContext('2d');
    window.myLine = new Chart(ctx, config);
};

document.getElementById('randomizeData').addEventListener('click', function() {
    config.data.datasets.forEach(function(dataset) {
        dataset.data = dataset.data.map(function() {
            return randomScalingFactor();
        });

    });

    window.myLine.update();
});
