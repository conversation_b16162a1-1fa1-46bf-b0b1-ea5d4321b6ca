<app-topmenu></app-topmenu>
<section style="padding-top:60px">


  <ngb-alert type="success" style="margin-bottom: 5px;" *ngIf="!staticAlertClosed" (close)="staticAlertClosed = true">
    Save Data Completed!</ngb-alert>
  <h5 class="p-sm-1 bg-secondary text-white text-center">User Accounts</h5>
  <div class="container-fluid">
    <div class="row">
      <div class="col-sm-3">
        <h5 class="p-sm-1 bg-secondary text-white font-weight-light">User Group</h5>
        <button style="margin-bottom: 5px;" class="btn btn-success" (click)="openModelClass(content)">ADD </button>
        <!-- <ul class="list-group" *ngFor="let usergroup of usergroups">
                        <li class="list-group-item d-flex justify-content-between align-items-center" (click)="SetGroup(usergroup)" data-toggle="modal" data-target="#EdModalgroup" >
                              {{ usergroup.des_group }}
                            <span class="badge badge-success badge-pill"></span>
                        </li>
                    </ul>-->
        <div class="btn-group-vertical list-group ">
          <button *ngFor="let usergroup of usergroups" (click)="SetGroup(usergroup)" style="text-align: left; margin-top: 4px; padding: 10px;"
            (click)="SetGroup(usergroup)" data-toggle="modal" data-target="#EdModalgroup" type="button" class="btn btn-outline-success ">{{
            usergroup.des_group }}</button>
        </div>
      </div>


      <div class="col-sm-9">
        <h5 class="p-sm-1 bg-secondary text-white font-weight-light">User List - Viewer</h5>

        <div class="accordion" id="accordionExample">
          <div class="card">
            <div class="card-header" id="headingOne" style="padding-top:0px;padding-bottom:0px">
              <h5 class="mb-0">
                <button class="btn btn-link" type="button" data-toggle="collapse" data-target="#collapseOne" aria-expanded="true" aria-controls="collapseOne">
                  Group Sales
                </button>
              </h5>
            </div>
            <div id="collapseOne" class="collapse show" aria-labelledby="headingOne" data-parent="#accordionExample">
              <div class="card-body" style="padding: 5px;">
                <button style="margin-bottom: 5px;" class="btn btn-success" (click)="getgroupsaleman()" data-toggle="modal" data-target="#ModalAddUser">Add
                  User </button>
                <table class="table table-hover table-bordered table-sm">
                  <thead>
                    <tr class="font-weight-light text-sm-center">
                      <th class="text-sm-center " scope="col">User Name</th>
                      <th class="text-sm-center " scope="col">User Group</th>
                      <th class="text-sm-center " scope="col">Sales Group</th>
                      <th class="text-sm-center " scope="col">Log In Name</th>
                      <th class="text-sm-center " scope="col">Password</th>
                      <th scope="col" class="text-sm-center" width="60px"></th>
                      <th scope="col" class="text-sm-center" width="60px"></th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr class="font-weight-light text-center" *ngFor="let userlogin of userlogins">
                      <td class="text-sm-center font-weight-normal"> {{ userlogin.name_user }} </td>
                      <td class="text-sm-center font-weight-normal">{{ userlogin.des_group }} </td>
                      <td class="text-sm-center font-weight-normal">{{ userlogin.salegroup }} </td>
                      <td class="text-sm-center font-weight-normal">{{ userlogin.login_user }} </td>
                      <td class="text-sm-center font-weight-normal">{{ userlogin.pw }} </td>

                      <td class="text-center">

                        <button type="button" style="height: 29px; padding-top: 0px;" class="btn btn-link font-weight-light" (click)="Edituser(userlogin)"
                          data-toggle="modal" class="btn btn-warning" data-target="#Edituser">Edit</button>
                      </td>
                      <td class="text-center">
                        <button type="button" style="height: 29px; padding-top: 0px;" class="btn btn-link font-weight-light text-danger" (click)="setdel(userlogin)"
                          data-toggle="modal" class="btn btn-danger " data-target="#DelModal">Delete</button>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>

            <div class="card">
              <div class="card-header" id="headingTwo" style="padding-top:0px;padding-bottom:0px">
                <h5 class="mb-0">
                  <button class="btn btn-link collapsed" (click)="LoaduserCustomer()" type="button" data-toggle="collapse" data-target="#collapseTwo"
                    aria-expanded="false" aria-controls="collapseTwo">
                    Group Customer
                  </button>
                </h5>
              </div>
              <div id="collapseTwo" class="collapse" aria-labelledby="headingTwo" data-parent="#accordionExample">
                <div class="card-body" style="padding: 5px;">
                  <button style="margin-bottom: 5px;" class="btn btn-success" (click)="addcustomer()" data-toggle="modal" data-target="#ModalAddCustomer">Add
                    Customer </button>
                  <table class="table table-hover table-bordered table-sm">
                    <thead>
                      <tr class="font-weight-light text-sm-center">
                        <th class="text-sm-center " scope="col">User Name</th>
                        <th class="text-sm-center " scope="col">User Group</th>
                        <th class="text-sm-center " scope="col">Sales Group</th>
                        <th class="text-sm-center " scope="col">Log In Name</th>
                        <th class="text-sm-center " scope="col">Password</th>
                        <th scope="col" class="text-sm-center" width="60px"></th>
                        <th scope="col" class="text-sm-center" width="60px"></th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr class="font-weight-light text-center" *ngFor="let itme of userCustomer">
                        <td class="text-sm-center font-weight-normal"> {{ itme.name_user }} </td>
                        <td class="text-sm-center font-weight-normal">{{ itme.des_group }} </td>
                        <td class="text-sm-center font-weight-normal">{{ itme.salegroup }} </td>
                        <td class="text-sm-center font-weight-normal">{{ itme.login_user }} </td>
                        <td class="text-sm-center font-weight-normal">{{ itme.pw }} </td>

                        <td class="text-center">

                          <button type="button" style="height: 29px; padding-top: 0px;" class="btn btn-link font-weight-light" (click)="Editcustomer(itme)"
                            data-toggle="modal" class="btn btn-warning" data-target="#Editcoutomer">Edit</button>
                        </td>
                        <td class="text-center">
                          <button type="button" style="height: 29px; padding-top: 0px;" class="btn btn-link font-weight-light text-danger" (click)="setdelcustomer(itme)"
                            data-toggle="modal" class="btn btn-danger " data-target="#Delcustomer">Delete</button>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>

          </div>





        </div>
        <!--  <button style="margin-bottom: 5px;" class="btn btn-success" (click)="getgroupsaleman()" data-toggle="modal" data-target="#ModalAddUser" >Add User </button>
                    <table class="table table-hover table-bordered table-sm">
                        <thead>
                            <tr class="font-weight-light text-sm-center">
                                <th class="text-sm-center " scope="col">User Name</th>
                                <th class="text-sm-center " scope="col">User Group</th>
                                <th class="text-sm-center " scope="col">Sales Group</th>
                                <th class="text-sm-center " scope="col">Log In Name</th>
                                <th class="text-sm-center " scope="col">Password</th>
                                <th scope="col" class="text-sm-center" width="60px"></th>
                                <th scope="col" class="text-sm-center" width="60px"></th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr class="font-weight-light text-center" *ngFor="let userlogin of userlogins">
                                <td class="text-sm-center font-weight-normal" > {{ userlogin.name_user }} </td>
                                <td class="text-sm-center font-weight-normal" >{{ userlogin.des_group }} </td>
                                <td class="text-sm-center font-weight-normal" >{{ userlogin.salegroup }} </td>
                                <td class="text-sm-center font-weight-normal" >{{ userlogin.login_user }} </td>
                                <td class="text-sm-center font-weight-normal" >{{ userlogin.pw }} </td>

                                <td class="text-center">

                                    <button type="button" style="height: 29px; padding-top: 0px;" class="btn btn-link font-weight-light"  (click)="Edituser(userlogin)"data-toggle="modal" class="btn btn-warning" data-target="#Edituser">Edit</button>
                                </td>
                                <td class="text-center">
                                    <button type="button" style="height: 29px; padding-top: 0px;" class="btn btn-link font-weight-light text-danger" (click)="setdel(userlogin)" data-toggle="modal" class="btn btn-danger " data-target="#DelModal">Delete</button>
                                </td>
                            </tr>
                        </tbody>
                    </table>-->


      </div>
    </div>
  </div>

</section>


<!-- Modal -->

<form #form="ngForm" role="form" (submit)="onSubmit(form.value)">
  <ng-template #content let-c="close" let-d="dismiss">
    <div class="modal-header">
      <h4 class="modal-title">Add Group</h4>
      <button type="button" class="close" aria-label="Close" (click)="d('Cross click')">
        <span aria-hidden="true">&times;</span>
      </button>
    </div>
    <div class="modal-body">
      <fieldset ngModelGroup="user_identification">
        <div class="form-group">
          <label for="last_name">Name Group : </label>
          <input ngModel name="Group_name" type="text" class="form-control" id="Group_name" placeholder="Name Group" required>
        </div>
      </fieldset>
    </div>
    <div class="modal-footer">

      <button type="button" class="btn btn-light" (click)="c('Close click')">Close</button>
      <button type="submit" class="btn btn-primary" (click)="c(onSubmit(form.value))">Save changes</button>
    </div>
  </ng-template>
</form>

<div class="modal fade" id="ModalAddCustomer" style="top:-100px;" tabindex="-1" role="dialog" aria-labelledby="ModalAddUser"
  aria-hidden="true">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="exampleModalLabel">Add User Customer</h5>
        <button type="button" class="close" data-dismiss="modal" (click)="cn()" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <form [formGroup]="customerFormGroup" (ngSubmit)="onClickRegisterCustomer()">

          <label for="exampleFormControlSelect1">Select Group : </label>
          <select [(ngModel)]="selectedValue" class="form-control" placeholder="Select Group" formControlName="Group" name="Group">
            <option selected value="" disabled>--Select Group--</option>
            <option *ngFor="let usergroup of usergroups" [value]="usergroup.id_group_user">
              {{usergroup.des_group}}
            </option>

          </select>
          <label for="exampleFormControlSelect1">Select IDsaleman : </label>
          <select [(ngModel)]="selectedValuecustomer" class="form-control" placeholder="Select Group" formControlName="Groupcustomer"
            name="Groupcustomer">
            <option selected value="" disabled>--Select Customer--</option>
            <option *ngFor="let item of usergroupCustomer" [value]="item.accountnum">
              {{item.accountnum}} ({{item.name}})
            </option>

          </select>

          <div class="form-group">
            <div class="form-group ">
              <label for="email">Email : </label>
              <label class="bg-alert" *ngIf="customerFormGroup.controls['email'].errors['pattern'] && customerFormGroup.controls['email'].touched">
              *format Email Error</label>
              <input type="text" [(ngModel)]="email" class="form-control" pattern="^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\.[a-zA-Z0-9.]+$" name="email"
                placeholder="Email" formControlName="email" />
            </div>
            <label for="Tel">Mobile : </label>

            <label class="bg-alert" *ngIf="customerFormGroup.controls['Tel'].errors['pattern'] && customerFormGroup.controls['Tel'].touched">
            *format Mobile Error</label>
            <input type="text" [(ngModel)]="Tel" class="form-control" pattern="^((\\+91-?)|0)?[0-9]{10}$" name="Tel" placeholder="**********"
              formControlName="Tel" />
          </div>


        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" (click)="cn()" data-dismiss="modal">Close</button>
        <button type="button" class="btn btn-primary" data-dismiss="modal" [disabled]="!customerFormGroup.valid" (click)="onClickRegisterCustomer()">Save
          changes</button>
      </div>
    </div>
  </div>
</div>

<div class="modal fade" id="Editcoutomer" style="top:-100px;" tabindex="-1" role="dialog" aria-labelledby="Editcoutomer"
  aria-hidden="true">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="exampleModalLabel">Edist Customer</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">

        <form [formGroup]="EdcustomerFormGroup">

          <label for="namme">Select Group : </label>
          <select (change)="selectvat($event.target.value)" class="form-control" type="text" name="Group">
            <option *ngFor="let item of DateGroupNone" value="{{item.id_group_user}}">{{item.des_group}}</option>
            <option selected *ngFor="let item of DateGroupHave" value="{{item.id_group_user}}">{{item.des_group}}</option>
          </select>

          <div class="form-group">
            <fieldset disabled>
              <label for="namme">Select GroupSalesman : </label>
              <select (change)="selectgroupsaleman($event.target.value)" class="form-control" type="text" name="Groupsale">
                <option *ngFor="let item of DateGroupNonesaleman" value="{{item.groupid}}"> {{item.groupid}} ({{item.name}})</option>
                <option selected *ngFor="let itemSALE of DateGroupHavesaleman" value="{{itemSALE.groupid}}"> {{itemSALE.groupid}}
                  ({{itemSALE.name}})</option>
              </select>
            </fieldset>

            <label for="namme">Name : </label>
            <input type="text" [(ngModel)]="edname_user" class="form-control" type="text" name="edname_user" placeholder="Name" formControlName="edname_user"
              readonly/>
            <div [formGroup]="EdcustomerFormGroup2">
              <label for="email">Email : </label>

              <label class="bg-alert" *ngIf="EdcustomerFormGroup2.controls['edemail'].errors['pattern'] && EdcustomerFormGroup2.controls['edemail'].touched">
              *format Email Error</label>
              <input type="text" [(ngModel)]="edemail" class="form-control" pattern="^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\.[a-zA-Z0-9.]+$" name="edemail"
                placeholder="Email" formControlName="edemail" />


              <label for="Tel">Mobile : </label>

              <label class="bg-alert" *ngIf="EdcustomerFormGroup2.controls['edmobile'].errors['pattern'] && EdcustomerFormGroup2.controls['edmobile'].touched">
              *format Mobile Error</label>
              <input type="text" [(ngModel)]="edmobile" class="form-control" pattern="^((\\+91-?)|0)?[0-9]{10}$" name="edmobile" placeholder="**********"
                formControlName="edmobile" />
            </div>
          </div>


          <label for="usernamme">Username : </label>
          <label class="bg-alert" *ngIf="EdcustomerFormGroup.controls['edusername'].errors['required'] && EdcustomerFormGroup.controls['edusername'].touched">
          *Username is required</label>
          <input type="text" [(ngModel)]="edusername" class="form-control" type="text" name="edusername" formControlName="edusername"
            readonly/>

          <div [formGroup]="EdpasswordFormGroup2" style="margin-bottom: 5px">

            <label for="password">Password : </label>
            <label class="bg-alert" *ngIf="EdpasswordFormGroup2.controls['Edpassword2'].errors['required'] && EdpasswordFormGroup2.controls['Edpassword2'].touched">
            *Password is required</label>
            <input class="form-control" [(ngModel)]="Edpassword2" type="password" name="Edpassword2" formControlName="Edpassword2">

            <label for="repeatPassword">Repeat Password : </label>
            <label class="bg-alert" *ngIf="EdpasswordFormGroup2.controls['EdrepeatPassword2'].errors['required'] && EdpasswordFormGroup2.controls['EdrepeatPassword2'].touched">
            *Repeat password is required</label>
            <input class="form-control" type="password" name="EdrepeatPassword2" formControlName="EdrepeatPassword2">

          </div>
          <p class="alert  bg-danger" *ngIf="EdpasswordFormGroup2.errors['doesMatchPassword']">Password does not match</p>





        </form>

      </div>

      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-dismiss="modal" (click)="cn()">Close</button>
        <button type="button" class="btn btn-primary" data-dismiss="modal" [disabled]="!EdcustomerFormGroup.valid" (click)="onClickEdcustomer()">Save
          changes</button>
      </div>
    </div>
  </div>
</div>

<div class="modal fade" id="Delcustomer" style="top:-100px;" tabindex="-1" role="dialog" aria-labelledby="DelModal" aria-hidden="true">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="exampleModalLabel">Delete User Customer</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">

        Would you like to delete. {{ namedel }} <br> {{nameshowdelete}}

      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
        <button type="button" class="btn btn-primary" data-dismiss="modal" (click)="Deletecustomer()">Delete</button>
      </div>
    </div>
  </div>
</div>
<!--END CUSTOMER Modal-->
<!-- Modal -->

<div class="modal fade" id="ModalAddUser" style="top:-100px;" tabindex="-1" role="dialog" aria-labelledby="ModalAddUser"
  aria-hidden="true">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="exampleModalLabel">Add User</h5>
        <button type="button" class="close" data-dismiss="modal" (click)="cn()" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">

        <form [formGroup]="registrationFormGroup" (ngSubmit)="onClickRegister()">

          <label for="exampleFormControlSelect1">Select Group : </label>
          <select [(ngModel)]="selectedValue" class="form-control" placeholder="Select Group" formControlName="Group" name="Group">
            <option selected value="" disabled>--Select Group--</option>
            <option *ngFor="let usergroup of usergroups" [value]="usergroup.id_group_user">
              {{usergroup.des_group}}
            </option>

          </select>
          <label for="exampleFormControlSelect1">Select IDsaleman : </label>
          <select [(ngModel)]="selectedValuesaleman" class="form-control" placeholder="Select Group" formControlName="Groupsale" name="Groupsale">
            <option selected value="" disabled>--Select IDsaleman--</option>
            <option *ngFor="let item of DateGroupsaleman" [value]="item.groupid">
              {{item.groupid}} ({{item.name}})
            </option>

          </select>

          <div class="form-group">
            <label for="namme">Name : </label><label class="bg-alert" *ngIf="registrationFormGroup.controls['name_user'].errors['required'] && registrationFormGroup.controls['name_user'].touched">
            *Enter Name</label>
            <input type="text" [(ngModel)]="name_user" class="form-control" type="text" name="name_user" placeholder="Name" formControlName="name_user"
            />


            <div class="form-group ">
              <label for="email">Email : </label><label class="bg-alert" *ngIf="registrationFormGroup.controls['email'].errors['required'] && registrationFormGroup.controls['email'].touched">
              *Enter Email</label>
              <label class="bg-alert" *ngIf="registrationFormGroup.controls['email'].errors['pattern'] && registrationFormGroup.controls['email'].touched">
              *format Email Error</label>
              <input type="text" [(ngModel)]="email" class="form-control" pattern="^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\.[a-zA-Z0-9.]+$" name="email"
                placeholder="Email" formControlName="email" />

            </div>
            <label for="Tel">Mobile : </label>
            <label class="bg-alert" *ngIf="registrationFormGroup.controls['Tel'].errors['required'] && registrationFormGroup.controls['Tel'].touched">
            *Enter Mobile </label>
            <label class="bg-alert" *ngIf="registrationFormGroup.controls['Tel'].errors['pattern'] && registrationFormGroup.controls['Tel'].touched">
            *format Mobile Error</label>
            <input type="text" [(ngModel)]="Tel" class="form-control" pattern="^((\\+91-?)|0)?[0-9]{10}$" name="Tel" placeholder="**********"
              formControlName="Tel" />
          </div>
          <div class="form-group" style=" margin-bottom: 5px">
            <label for="usernamme">Username : </label><label class="bg-alert" *ngIf="registrationFormGroup.controls['username'].errors['required'] && registrationFormGroup.controls['username'].touched">
            *Enter Username is required</label>

            <input type="text" [(ngModel)]="username" class="form-control" type="text" name="username" formControlName="username" placeholder="Enter Username"
            />

            <div [formGroup]="passwordFormGroup">
              <label for="password">Password : </label><label class="bg-alert" *ngIf="passwordFormGroup.controls['password'].errors['required'] && passwordFormGroup.controls['password'].touched">
              *Password is required</label>
              <input class="form-control" type="password" name="password" placeholder="Enter Password" formControlName="password">

              <label for="repeatPassword">Repeat Password : </label><label class="bg-alert" *ngIf="passwordFormGroup.controls['repeatPassword'].errors['required'] && passwordFormGroup.controls['repeatPassword'].touched">
              *Repeat password is required</label>
              <input class="form-control" type="password" name="repeatPassword" placeholder="Enter RepeatPassword" formControlName="repeatPassword">


            </div>
          </div>
          <p class="alert  bg-danger" *ngIf="passwordFormGroup.errors['doesMatchPassword']">Password does not match</p>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" (click)="cn()" data-dismiss="modal">Close</button>
        <button type="button" class="btn btn-primary" data-dismiss="modal" [disabled]="!registrationFormGroup.valid" (click)="onClickRegister()">Save
          changes</button>
      </div>
    </div>
  </div>
</div>

<div class="modal fade" id="Edituser" style="top:-100px;" tabindex="-1" role="dialog" aria-labelledby="Edituser" aria-hidden="true">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="exampleModalLabel">Edist User</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">

        <form [formGroup]="EditFormGrouped" (ngSubmit)="onClickEdUser()">

          <label for="namme">Select Group : </label>
          <select (change)="selectvat($event.target.value)" class="form-control" type="text">
            <option *ngFor="let item of DateGroupNone" value="{{item.id_group_user}}">{{item.des_group}}</option>
            <option selected *ngFor="let item of DateGroupHave" value="{{item.id_group_user}}">{{item.des_group}}</option>
          </select>

          <!-- <input  type="text" [(ngModel)]="des_group" class="form-control" type="text" name="des_group" placeholder="Name" formControlName="des_group" readonly/>-->
          <div class="form-group">
            <label for="namme">Select GroupSalesman : </label>
            <select (change)="selectgroupsaleman($event.target.value)" class="form-control" type="text">
              <option *ngFor="let item of DateGroupNonesaleman" value="{{item.groupid}}"> {{item.groupid}} ({{item.name}})</option>
              <option selected *ngFor="let itemSALE of DateGroupHavesaleman" value="{{itemSALE.groupid}}"> {{itemSALE.groupid}}
                ({{itemSALE.name}})</option>
            </select>


            <label for="namme">Name : </label><label class="bg-alert" *ngIf="EditFormGrouped.controls['edname_user'].errors['required'] && EditFormGrouped.controls['edname_user'].touched">
            *Enter Name</label>
            <input type="text" [(ngModel)]="edname_user" class="form-control" type="text" name="edname_user" placeholder="Name" formControlName="edname_user"
            />

            <label for="email">Email : </label>
            <label class="bg-alert" *ngIf="EditFormGrouped.controls['edemail'].errors['required'] && EditFormGrouped.controls['edemail'].touched">
            *Enter Email</label>
            <label class="bg-alert" *ngIf="EditFormGrouped.controls['edemail'].errors['pattern'] && EditFormGrouped.controls['edemail'].touched">
            *format Email Error</label>
            <input type="text" [(ngModel)]="edemail" class="form-control" pattern="^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\.[a-zA-Z0-9.]+$" name="edemail"
              placeholder="Email" formControlName="edemail" />


            <label for="Tel">Mobile : </label>
            <label class="bg-alert" *ngIf="EditFormGrouped.controls['edmobile'].errors['required'] && EditFormGrouped.controls['edmobile'].touched">
            *Enter Mobile </label>
            <label class="bg-alert" *ngIf="EditFormGrouped.controls['edmobile'].errors['pattern'] && EditFormGrouped.controls['edmobile'].touched">
            *format Mobile Error</label>
            <input type="text" [(ngModel)]="edmobile" class="form-control" pattern="^((\\+91-?)|0)?[0-9]{10}$" name="edmobile" placeholder="**********"
              formControlName="edmobile" />
          </div>


          <label for="usernamme">Username : </label>
          <label class="bg-alert" *ngIf="EditFormGrouped.controls['edusername'].errors['required'] && EditFormGrouped.controls['edusername'].touched">
          *Username is required</label>
          <input type="text" [(ngModel)]="edusername" class="form-control" type="text" name="edusername" formControlName="edusername"
            readonly/>



          <div [formGroup]="EdpasswordFormGroup" style="margin-bottom: 5px">

            <label for="password">Password : </label>
            <label class="bg-alert" *ngIf="EdpasswordFormGroup.controls['Edpassword'].errors['required'] && EdpasswordFormGroup.controls['Edpassword'].touched">
            *Password is required</label>
            <input class="form-control" [(ngModel)]="Edpassword" type="password" name="Edpassword" formControlName="Edpassword">

            <label for="repeatPassword">Repeat Password : </label>
            <label class="bg-alert" *ngIf="EdpasswordFormGroup.controls['EdrepeatPassword'].errors['required'] && EdpasswordFormGroup.controls['EdrepeatPassword'].touched">
            *Repeat password is required</label>
            <input class="form-control" [(ngModel)]="EdrepeatPassword" type="password" name="EdrepeatPassword" formControlName="EdrepeatPassword">

          </div>
          <p class="alert  bg-danger" *ngIf="EdpasswordFormGroup.errors['doesMatchPassword']">Password does not match</p>
        </form>

      </div>

      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-dismiss="modal" (click)="cn()">Close</button>
        <button type="button" class="btn btn-primary" data-dismiss="modal" [disabled]="!EditFormGrouped.valid" (click)="onClickEdUser()">Save
          changes</button>
      </div>
    </div>
  </div>
</div>

<div class="modal fade" id="DelModal" style="top:-100px;" tabindex="-1" role="dialog" aria-labelledby="DelModal" aria-hidden="true">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="exampleModalLabel">Delete User</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">

        Would you like to delete. {{ namedel }}

      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
        <button type="button" class="btn btn-primary" data-dismiss="modal" (click)="Deleteuser()">Delete</button>
      </div>
    </div>
  </div>
</div>

<!-- Modal -->
<div class="modal fade" id="EdModalgroup" style="top:-100px;" tabindex="-1" role="dialog" aria-labelledby="EdModalgroup"
  aria-hidden="true">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="exampleModalLabel">Edit Group User</h5>
        <button type="button" class="close" (click)="Cset()" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <label for="last_name">Name Group : </label>
        <input [(ngModel)]="setdes_group" name="Group_name" type="text" class="form-control" id="Group_name" placeholder="Name Group"
          required>
        <div class="custom-control custom-checkbox">
          <input type="checkbox" class="custom-control-input" [(ngModel)]="chdel" (click)="opendelete($event.target.checked)" id="customCheck1">
          <label class="custom-control-label" for="customCheck1">Would you like to delete a group?</label>
        </div>
      </div>
      <div class="modal-footer">

        <button type="button" style="float: left;" class="btn btn-light" data-dismiss="modal" [disabled]="chdel==false" (click)="deleteGroup()">Delete</button>

        <button type="button" class="btn btn-secondary" data-dismiss="modal" (click)="Cset()">Close</button>
        <button type="button" class="btn btn-primary" data-dismiss="modal" (click)="SaveGroup()">Save changes</button>
      </div>
    </div>
  </div>
</div>
