import { HttpClient } from '@angular/common/http';
import { Component, ViewEncapsulation } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { WebapiService } from '../webapi.service';


@Component({
  selector: 'app-users',
  templateUrl: './users.component.html',
  styleUrls: ['./users.component.css'],
  encapsulation: ViewEncapsulation.None,
  styles: [`
  .bg-alert{
    color: red;
  }
  .alert {
    position: relative;
    padding: 0.75rem 1.25rem;
    margin-bottom: 5px;
    border: 1px solid transparent;
    border-radius: 0.25rem;
}
  `]


})

export class UsersComponent {
  clsoedelete=true;
  chdel: boolean = false;
  setid_group:string;
  setdes_group:string;

  namedel: string;
  id_del:string;

  id_Saleman: string;
  des_group: string;
  id_group_user: string;
  edname_user: string;
  edemail: string;
  edmobile: string;
  edusername: string;
  Edpassword: string;
  EdrepeatPassword: string;

  registrationFormGroup: FormGroup;
  passwordFormGroup: FormGroup;

  EditFormGrouped:FormGroup;
  EdpasswordFormGroup: FormGroup;

  customerFormGroup: FormGroup;
  EdcustomerFormGroup: FormGroup;
  EdcustomerFormGroup2: FormGroup;
  EdpasswordFormGroup2: FormGroup;
  Edpassword2: string;
  EdrepeatPassword2: string;
  nameshowdelete:string;

  customerID:string;
  id_user_customer:string;

  name_user: string;
  selectedValuesaleman: string;
  selectedValuecustomer:string;
  selectedValue: string;
  email: string;
  staticAlertClosed = true;
  mobile: string;
  username: string;
  repeatPassword: string;
  password: string;
  Tel: string;



  closeResult: string;
  message = '';
  message2: any ;
  userlogins: any[];
  usergroups: any[];
  usergroupCustomer: any[];
  userCustomer: any[];
  dataapp: object;
  data1: number;
  data2: string;
  url: string;
  urldata: any;
  mProducts: any[] = [];
  max = 2000;

  dataGroup: any;
  dataName: string;
  dataEmail: string;
  datatel: string;
  datausername: string;
  datapassword1: string;
  datapassword2: string;
  dataid:string;

  datauser:number;
  datetest: string;
  datetest2: any;
  urlserver: string;
  keyboardStrGroup : string = '1';
  keyboardStrApprove : string = '';
  idGroup : string;
  checkupdate: string;

  DateGroupHave:any;
  DateGroupNone:any;
  DateGroupsaleman:any;
  DateGroupHavesaleman:any;
  DateGroupNonesaleman:any;
 /* values = '';
  onKey(value: string) {
    this.datausername = value ;
    const Urlapi = this.service.getDummy2();
    const Urluserlist = this.service.getUrlUserlist();
    const urlcheckuser = `${ Urlapi }${ Urluserlist }/%20/${ this.datausername }`;
    this.http.get<any[]>(urlcheckuser).subscribe(res => {
      if (res.length < 1) {
        alert('ผ่าน')
      }else{
        alert('ไม่ผ่าน')
      }
    });
  }*/
 datalogin: any[]=[];
  constructor(private service: WebapiService, private http: HttpClient, private modalService: NgbModal, private formBuilder: FormBuilder) {
    this.urlserver = service.geturlservice();
    localStorage.removeItem('DataSOderreview');
    localStorage.removeItem('DataSOderlist');
   this.LoadGroup();
  // this.test();
   this.selectedValue='';
   this.selectedValuesaleman='';
   this.passwordFormGroup = this.formBuilder.group({
    password: ['', Validators.required],
    repeatPassword: ['', Validators.required]
  },{
    validator: RegistrationValidator.validate.bind(this)
  });

  this.registrationFormGroup = this.formBuilder.group({
    username: ['', Validators.required],
   Group: ['', Validators.required],
   Groupsale: ['', Validators.required],
   name_user: ['', Validators.required],
    Tel: ['', Validators.required],
    email: ['', Validators.required],
    passwordFormGroup: this.passwordFormGroup
  });

  this.EdpasswordFormGroup = this.formBuilder.group({
    Edpassword: ['', Validators.required],
    EdrepeatPassword: ['', Validators.required]
  },{
    validator: EdRegistrationValidator.validate.bind(this)
  });

  this.EdpasswordFormGroup2 = this.formBuilder.group({
    Edpassword2: ['', Validators.required],
    EdrepeatPassword2: ['', Validators.required]
  },{
    validator: EdRegistrationValidator2.validate.bind(this)
  });

  this.EditFormGrouped = this.formBuilder.group({
    edusername: ['', Validators.required],
    edname_user: ['', Validators.required],
    edmobile: ['', Validators.required],
    edemail: ['', Validators.required],
    editFormGrouped: this.EdpasswordFormGroup
  });

  this.customerFormGroup= this.formBuilder.group({
   Group: ['', Validators.required],
   Groupcustomer: ['', Validators.required],
    Tel: [],
    email: []
  });
  this.EdcustomerFormGroup=this.formBuilder.group({
    edusername: ['', Validators.required],
    edname_user: ['', Validators.required],
    EdcustomerFormGroup : this.EdpasswordFormGroup2
   });

   this.EdcustomerFormGroup2=this.formBuilder.group({
    edmobile: [],
    edemail: []
   });
  }

  test(){

    this.http.get<any>('http://192.168.1.128:9090/node/apinano/api/gw').subscribe(res => {

      alert( JSON.stringify(res.Stations.Station[1].Province));

     });
  }

  setopen() {
    this.clsoedelete=true;
    this.chdel=false;
  }


  opendelete(event) {
    this.clsoedelete=false;
    this.chdel=false;
  }
  Cset(){
    this.setdes_group=" ";
    this.setid_group= " ";
    this.setopen();
  }

  SetGroup(usergroup){
    this.setid_group=usergroup.id_group_user;
    this.setdes_group=usergroup.des_group;
    this.chdel=false;
  }

  SaveGroup(){
    const dataNameGroup= this.setdes_group;
    const dataid =  this.setid_group;
   const url = `${ this.urlserver }update_usergroup`;
   this.http.post(url, {
    id_group_user :dataid,
    des_group :dataNameGroup
   }).subscribe((res) => {
      console.log(res);
      if (res === true) {
          this.staticAlertClosed = false ;
          setTimeout(() => this.staticAlertClosed = true , 7000);
         this.LoadGroup();


      }else{
        alert('Failed to save!!');
      }
    });
    this.setdes_group=" ";
    this.setid_group= " ";
  }

  deleteGroup(){
    const dataid =  this.setid_group;
    const url = `${ this.urlserver }delete_usergroup`;
    this.http.post(url, {
      id_user_group : dataid
    }).subscribe((res) => {
       console.log(res);
       if (res === true) {
           this.staticAlertClosed = false ;
           setTimeout(() => this.staticAlertClosed = true , 7000);
          this.LoadGroup();

       }else{
         alert('Failed to save!!');
       }
     });
     this.setdes_group=" ";
     this.setid_group= " ";
  }


  setdelcustomer(itme){
    this.namedel='';
    this.id_del='';
    this.nameshowdelete='';
    this.namedel=itme.accountnum;
    this.id_del=itme.id_user;
    this.nameshowdelete=itme.name_user;
  }

  Deletecustomer(){
    const Dataid_del= this.id_del;
    const Dateaccountnum= this.namedel;
    const url = `${ this.urlserver }delete_customer`;
    this.http.post(url,{
      id_user : Dataid_del,
      id_accountnum : Dateaccountnum
    }).subscribe((res) => {
      console.log(res);
       if (res === true) {
          this.staticAlertClosed = false ;
          setTimeout(() => this.staticAlertClosed = true , 7000);
          this.namedel='';
          this.id_del='';
          this.nameshowdelete='';
           this.LoaduserCustomer()
      }else{
        alert('Failed to save!!.')
      }

    });
  }

  setdel(userlogin){
    this.namedel='';
    this.id_del='';
    this.namedel=userlogin.name_user;
    this.id_del=userlogin.id_user;
  }

  Deleteuser(){
    const Dataid_del= this.id_del;
    const url = `${ this.urlserver }delete_user`;
    this.http.post(url,{
      id_user : Dataid_del
    }).subscribe((res) => {
      console.log(res);
       if (res === true) {
          this.staticAlertClosed = false ;
          setTimeout(() => this.staticAlertClosed = true , 7000);
           this.id_del="";

           this.Loaduser()
      }else{
        alert('Failed to save!!.')
      }

    });
    }


    setgroup(des_name,Groupsale){
      this.DateGroupHave=[];
      this.http.get<any>(this.urlserver +'des_group_data/'+des_name ).subscribe(res => {

       if(res.length > 0){
      this.DateGroupHave=res;
      this.setgroupNull(des_name,Groupsale);
           }else{

           }


       });
    }

    setgroupNull(des_name,Groupsale){
      this.DateGroupNone=[];
      this.http.get<any>(this.urlserver +'des_group/'+des_name ).subscribe(res => {

        if(res.length > 0){
       this.DateGroupNone=res;
       this.setgroupsaleman(Groupsale);
            }else{

            }
        });
    }

    setgroupsaleman(Groupsale){
      this.DateGroupHavesaleman=[];
      this.http.get<any>(this.urlserver +'groupsaleman/'+Groupsale ).subscribe(res => {
       if(res.length > 0){
      this.DateGroupHavesaleman=res;
      this.setgroupNullsaleman(Groupsale);
           }else{

           }


       });
    }

    setgroupNullsaleman(Groupsale){
      this.DateGroupNonesaleman=[];
      this.http.get<any>(this.urlserver +'groupsaleman_NO/'+Groupsale ).subscribe(res => {
        if(res.length > 0){
       this.DateGroupNonesaleman=res;
            }else{

            }
        });
    }

    getgroupsaleman(){
      this.selectedValue="";
      this.selectedValuesaleman="";
      this.name_user="";
      this.email="";
      this.Tel="";
      this.username="";
      this.passwordFormGroup.value.password="";
      this.passwordFormGroup.value.repeatPassword="";
      this.DateGroupsaleman=[];
      this.http.get<any>(this.urlserver +'salesman_full' ).subscribe(res => {
        if(res.length > 0){
       this.DateGroupsaleman=res;
            }else{
            alert('ไม่สามารถติดข้อมูลรหัส saleman ได้')
            }
        });
    }

    Editcustomer(itme){
      this.customerID='';
      this.customerID=itme.accountnum;
      this.id_user_customer='';
      this.id_user_customer=itme.id_user;
      this.id_Saleman='';
      this.des_group='';
      this.dataid='';
      this.setgroup(itme.des_group,itme.salegroup);
      this.edname_user=itme.name_user
      this.edemail=itme.email
      this.edmobile=itme.mobile
      this.edusername=itme.login_user
     this.Edpassword2=itme.pw
     this.EdrepeatPassword2=itme.pw
     this.id_group_user=itme.id_group_user
     this.dataid=itme.id_user;
     this.des_group=itme.id_group_user;
     this.id_Saleman=itme.salegroup;

    }

    onClickEdcustomer(){
      this.datauser=this.EdcustomerFormGroup.value.edusername;
      this.dataEmail=this.EdcustomerFormGroup2.value.edemail;
      this.datatel=this.EdcustomerFormGroup2.value.edmobile;
      const datapassword=this.EdpasswordFormGroup2.value.Edpassword2;

      if(this.dataEmail=='' || this.dataEmail===undefined){
        this.dataEmail='%20'
      }
      if(this.datatel==''|| this.datatel===undefined){
        this.datatel='%20'
      }
     const url = `${ this.urlserver }update_user_customer`;
                     this.http.post(url, {
                      id_user: this.id_user_customer,
                      email:this.dataEmail,
                      mobile:this.datatel,
                      pw:datapassword,
                    customerid : this.customerID
                     }).subscribe((res) => {
                        console.log(res);
                        if (res === true) {
                            this.staticAlertClosed = false ;
                            setTimeout(() => this.staticAlertClosed = true , 7000);
                            this.des_group='';
                            this.LoaduserCustomer();
                           /* this.EditFormGrouped.value.id_User="";
                      this.EditFormGrouped.value.edname_user="";
                      this.EditFormGrouped.value.edemail="";
                      this.EditFormGrouped.value.edmobile="";
                      this.EditFormGrouped.value.edusername="";
                      this.EdpasswordFormGroup.value.Edpassword="";*/
                                          }
                      })
    }


  Edituser(userlogin){
    this.id_Saleman='';
    this.des_group='';
    this.dataid='';
    this.setgroup(userlogin.des_group,userlogin.salegroup);
    this.edname_user=userlogin.name_user
    this.edemail=userlogin.email
    this.edmobile=userlogin.mobile
    this.edusername=userlogin.login_user
   this.Edpassword=userlogin.pw
   this.EdrepeatPassword=userlogin.pw
   this.id_group_user=userlogin.id_group_user
   this.dataid=userlogin.id_user;
   this.des_group=userlogin.id_group_user;
   this.id_Saleman=userlogin.salegroup;
  }


onClickEdUser(){

  this.datauser=this.EditFormGrouped.value.edname_user;
  this.dataName=this.EditFormGrouped.value.edname_user;
  this.dataEmail=this.EditFormGrouped.value.edemail;
  this.datatel=this.EditFormGrouped.value.edmobile;

  this.datausername=this.EditFormGrouped.value.edusername;
  const datapassword=this.EdpasswordFormGroup.value.Edpassword
  const dataNameGroup= this.EditFormGrouped.value.des_group;
  const url = `${ this.urlserver }update_user`;
                 this.http.post(url, {
                  id_group_user :this.des_group,
                  id_user:this.dataid ,
                  name_user:this.dataName,
                  email:this.dataEmail,
                  mobile:this.datatel,
                  login_user:this.datausername,
                  pw:datapassword,
                  salesmanid:this.id_Saleman,
                 }).subscribe((res) => {
                    console.log(res);
                    if (res === true) {
                        this.staticAlertClosed = false ;
                        setTimeout(() => this.staticAlertClosed = true , 7000);
                        this.des_group='';
                        this.dataid='';
                        this.Loaduser();
                       /* this.EditFormGrouped.value.id_User="";
                       /:id_group_user/:id_user/:name_user/:email/:mobile/:login_user/:pw/:salesmanid
                  this.EditFormGrouped.value.edname_user="";
                  this.EditFormGrouped.value.edemail="";
                  this.EditFormGrouped.value.edmobile="";
                  this.EditFormGrouped.value.edusername="";
                  this.EdpasswordFormGroup.value.Edpassword="";*/
                                      }
                  })


}

selectvat(value){
  this.des_group = value;
}
selectgroupsaleman(value){
  this.id_Saleman='';
  this.id_Saleman = value;
}

addcustomer()
{
  this.selectedValuecustomer="";
this.LoadGroupCustomer();
}

  ngOnInit() {

  }
  onClickRegister(){

  this.datauser = this.getRandomInt(this.max);
  this.datauser += this.registrationFormGroup.value.name_user;
  this.dataName= this.registrationFormGroup.value.name_user;
  this.dataEmail= this.registrationFormGroup.value.email;
  this.datatel= this.registrationFormGroup.value.Tel;
  this.datausername= this.registrationFormGroup.value.username;
  this.datapassword1= this.registrationFormGroup.value.passwordFormGroup.password;
  const url = `${ this.urlserver }create_user`;
  const urlcheckuser = `${ this.urlserver }find_user/%20/${ this.datausername }`;

  this.http.get<any[]>(urlcheckuser).subscribe(res => {
              if (res.length < 1) {

                  this.http.post(url, {
                    id_group_user :this.selectedValue,
                    id_user :this.datauser,
                    name_user :this.dataName,
                    email :this.dataEmail,
                    mobile :this.datatel,
                    login_user :this.datausername,
                    pw :this.datapassword1 ,
                  salesmanid : this.selectedValuesaleman,
                  }).subscribe((res) => {
                    console.log(res);
                    if (res === true) {
                        this.staticAlertClosed = false ;
                        setTimeout(() => this.staticAlertClosed = true , 7000);
                        this.LoadGroup();
                        this.selectedValue="";
                        this.selectedValuesaleman="";
                        this.name_user="";
                        this.email="";
                        this.username="";
                        this.Tel="";
                        this.id_Saleman='';
                        this.des_group='';
                        this.dataid='';
                                      }
                  });
                  this.selectedValue=" ";
                  this.name_user="";
                  this.email="";
                  this.username="";
                  this.Tel="";
                  this.id_Saleman='';
                  this.des_group='';
                  this.dataid='';

                } else {
                alert('มี Username นี้ อยู่ในระบบแล้ว');
                }
                });
  }

  onClickRegisterCustomer(){
    let dateusersum
    let url
    this.datatel='';
    this.dataEmail='';
    dateusersum = this.getRandomInt(this.max);
    dateusersum += this.selectedValuecustomer;

   if(this.customerFormGroup.value.email===undefined || this.customerFormGroup.value.email==''){

    this.datatel=''
   }else{
     this.dataEmail= this.customerFormGroup.value.email;
   }
   if(this.customerFormGroup.value.Tel===undefined ||this.customerFormGroup.value.Tel=='' ){
    this.dataEmail=''
   }else{
    this.datatel= this.customerFormGroup.value.Tel;
   }
   /*/:id_group_user/:id_user/:email/:mobile/:customerid*/
     url = `${ this.urlserver }create_customer`;
    const urlcheckuser = `${ this.urlserver }find_user_customer/${ this.selectedValuecustomer }`;
    this.http.get<any[]>(urlcheckuser).subscribe(res => {
                if (res.length < 1) {
                    this.http.post(url, {
                      id_group_user :this.selectedValue,
                      id_user :dateusersum ,
                      email :this.dataEmail,
                      mobile :this.datatel,
                      customerid :this.selectedValuecustomer,
                    }).subscribe((res) => {
                      if (res === true) {
                          this.staticAlertClosed = false ;
                          setTimeout(() => this.staticAlertClosed = true , 3000);
                          this.selectedValue="";
                          this.selectedValuecustomer="";
                          this.customerFormGroup.value.email="";
                          this.customerFormGroup.value.Tel="";
                          this.Tel="";
                          this.LoaduserCustomer();
                                        }
                    },error=>{
                      alert('เกิดปัญหาในการ Process ข้อมูล กรุณาทำรายการใหม่อีกครั้ง');
                    });
                    this.selectedValue="";
                    this.customerFormGroup.value.email="";
                    this.customerFormGroup.value.Tel="";
                    this.email="";
                    this.selectedValuecustomer="";
                    this.Tel="";


                  } else {
                  alert('มี Username นี้ อยู่ในระบบแล้ว');
                  }
                  },error=>{
                    alert('เกิดปัญหาในการ Process ข้อมูล กรุณารายการใหม่อีกครั้ง');
                  });
    }


  openModelClass(content) {
    this.modalService.open(content, {});
  }
  openModelAdduser(contentuser) {
    this.modalService.open(contentuser, {});
  }
  Loaduser() {
    this.userlogins=[];
    this.http.get<any>(this.urlserver + 'userlist').subscribe(res => {
      this.userlogins = res;
    });
  }
  LoadGroup() {
    this.usergroups=[];
    this.http.get<any>(this.urlserver + 'usergroup').subscribe(res => {
    //this.service.DataGroup().then
      this.usergroups = res;
      this.Loaduser();
    });
  }

  LoadGroupCustomer() {
    this.usergroupCustomer=[];
    this.http.get<any>(this.urlserver + 'customer_list').subscribe(res => {
      this.usergroupCustomer = res;
    });
  }

  LoaduserCustomer() {
    this.userCustomer=[];
    this.http.get<any>(this.urlserver + 'user_customer').subscribe(res => {
      this.userCustomer = res;
    });
  }

  onSubmit(value) {

    this.data1 = this.getRandomInt(this.max);
    this.data1 += value.user_identification.Group_name;
    this.data2 = value.user_identification.Group_name;
   const url = `${ this.urlserver }create_usergroup`;
   this.http.post(url,{
    id_group_user :this.data1,
    des_group :this.data2
   }).subscribe((res) => {
      console.log(res);
      if (res === true) {
          this.staticAlertClosed = false ;
          setTimeout(() => this.staticAlertClosed = true , 7000);
          this.LoadGroup();
      }
    });
  }
 /* onSubmitadduser(value){
  this.dataName= value.user_date.Name;
  this.dataEmail=value.user_date.Email;
  this.datatel=value.user_date.Tel;
  this.datausername=value.user_date.Username;
  this.datapassword1=value.user_date.Password1;
  this.datapassword2=value.user_date.Password2;
  alert(this.selectedValue + ' ' +  this.dataName+ ' '+ this.dataEmail+ '  ' +this.datatel+ ' ' +this.datausername+ ' ' +this.datapassword1 )
  }*/

  cn(){
    this.selectedValue=" ";
    this.name_user="";
    this.email="";
    this.username="";
    this.Tel="";
  }

  getgroup(usergroup){
    this.dataGroup=usergroup.des_group;
    alert(this.dataGroup)
  }
  getRandomInt(max) {
    return Math.floor(Math.random() * Math.floor(max));
  }
  getuserlist(event: any) {
    this.datausername= this.registrationFormGroup.value.username;
    this.datetest = event.target.value;
    const url = `${ this.urlserver }find_user/%20/${ this.datetest }`;
this.http.get<any[]>(url).subscribe(res => {
  this.datetest2 = res;
  alert(res.length);
});
  }
}


export class RegistrationValidator {
  static validate(registrationFormGroup: FormGroup) {
      let password = registrationFormGroup.controls["password"].value;
      let repeatPassword = registrationFormGroup.controls["repeatPassword"].value;

      if (repeatPassword.length <= 0) {
          return null;
      }

      if (repeatPassword !== password) {
          return {
              doesMatchPassword: true
          };
      }
      return null;

  }
}

export class EdRegistrationValidator {
  static validate(EditFormGrouped: FormGroup) {
      let Edpassword = EditFormGrouped.controls["Edpassword"].value;
      let EdrepeatPassword = EditFormGrouped.controls["EdrepeatPassword"].value;

      if (EdrepeatPassword.length <= 0) {
          return null;
      }

      if (EdrepeatPassword !== Edpassword) {
          return {
              doesMatchPassword: true
          };
      }
      return null;

  }
}

export class EdRegistrationValidator2 {
  static validate(EditFormGrouped2: FormGroup) {
      let Edpassword2 = EditFormGrouped2.controls["Edpassword2"].value;
      let EdrepeatPassword2 = EditFormGrouped2.controls["EdrepeatPassword2"].value;

      if (EdrepeatPassword2.length <= 0) {
          return null;
      }

      if (EdrepeatPassword2 !== Edpassword2) {
          return {
              doesMatchPassword: true
          };
      }
      return null;

  }
}



export interface Group {
  value: string;
  viewValue: string;
}

