import { Injectable } from '@angular/core';
import { HttpClient,HttpParams, HttpHeaders } from '@angular/common/http';
import {catchError, debounceTime, distinctUntilChanged, map, tap, switchMap} from 'rxjs/operators';
import { Observable } from 'rxjs';


@Injectable()


export class WebapiService  {
  CKHost = JSON.parse(sessionStorage.getItem('CKhost'))

  //urlApi = 'http://*************/node/apinano/api/';
 urlApi = 'http://*************/node/apinano/api/';
  urlimg = 'http://*************/assets/imageBill/';
  urlimgDefault ='http://*************/assets/img/default-image.png'
  urlimgbookbank = 'http://*************/assets/bookbank/';
  urlApiuploadimg = 'http://*************/node/apinanoloadimg/api/';
  urluserlist = '/security/userlist';
  urlApifind_user = 'http://*************/security/find_user/';
  userlogin: any[];
  menulogin: any;
  urlApipostuser = '/node/apinano/api/create_user';
  urlApideluser = '/node/apinano/api/delete_user';
  urlApiUserlist = '/node/apinano/api/find_user' ;
  urlApiUpdateUserlist = '/node/apinano/api/update_user' ;

  urlApiUpdataGroup = '/node/apinano/api/update_usergroup';
  urlusergroup = '/node/apinano/api/usergroup';
  urlApipostgroup = '/node/apinano/api/create_usergroup';
  urlApiDelGroup = '/node/apinano/api/delete_usergroup' ;
alt=false;
premissiondata:any[]=[];
  constructor(private http: HttpClient) {
  }


  getDummy2(): string {
    return this.urlApi;
  }
  getUrlUpdataGroup(): string {
    return this.urlApiUpdataGroup;
  }

  getUrlGroup(): string {
    return this.urlApipostgroup;
  }
  getUrlDelGroup(): string {
    return this.urlApiDelGroup;
  }


getuserlogin(value: any): any{
this.userlogin=value;
}

setuserlogin(): any{
  return this.userlogin;
}
getmenulogin(value: any): any{
  this.menulogin=value;
  }

setmenulogin(): any{
  return this.menulogin;
}
getalert(value: boolean){
this.alt=value;
}
setalert(): any{
return this.alt;
}
  getUrluser(): string {
    return this.urlApipostuser;
  }
  getUrlUserlist(): string {
    return this.urlApiUserlist;
  }
  getUrlUpdateUserlist(): string {
    return this.urlApiUpdateUserlist;
  }
  getUrldeluser(): string {
    return this.urlApideluser;
  }
setpromisstiondata(value:any){
this.premissiondata=value;
}
getpermisstiondata():any{
  return this.premissiondata;
}
 
  feedData(): Promise<any[]> {
    const url = this.urlApi + this.urluserlist;
    return this.http.get<any[]>(url)
    .toPromise()
    .catch();
  }

  DataGroup(): Promise<any[]> {
    const url = this.urlApi + this.urlusergroup;
    return this.http.get<any[]>(url)
    .toPromise()
    .catch();
  }
  geturlservice(): string {

  // return this.urlApi='http://localhost/node/apinano/api/';
    return this.urlApi='http://*************/node/apinano/api/';
   // return this.urlApi='http://*************3:9090/node/apinano/api/';
  
   
  }
  geturlloadimgservice(): string {
    return this.urlApiuploadimg;
  }
  geturlserviceIMG(): string {
    return this.urlimg;
  }

  geturlimgDefault():string{
    return this.urlimgDefault;
  }

  geturlserviceIMGbookbank():string{
    return this.urlimgbookbank;
  }
  downloadfiletxt(valuename:string){
    var body={filename:valuename};
    return this.http.get(this.urlApi+'downloadfile');
    }
    
}



module Ajax {
  export class Options {
    url: string;
    method: string;
    data: Object;
    constructor(url: string, method?: string, data?: Object) {
        this.url = url;
        this.method = method || "get";
        this.data = data || {};
    }
  }
  export class Service {
    public request = (options: Options, successCallback: Function, errorCallback?: Function): void => {
        var that = this;
        $.ajax({
            url: options.url,
            type: options.method,
            data: options.data,
            cache: false,
            success: function (d) {
                successCallback(d);
            },
            error: function (d) {
                if (errorCallback) {
                    errorCallback(d);
                    return;
                }
                var errorTitle = "Error in (" + options.url + ")";
                var fullError = JSON.stringify(d);
                console.log(errorTitle);
                console.log(fullError);
                that.showJqueryDialog(fullError, errorTitle);
            }
        });
    }
    public get = (url: string, successCallback: Function, errorCallback?: Function): void => {
        this.request(new Options(url), successCallback, errorCallback);
    }
    public getWithDataInput = (url: string, data: Object, successCallback: Function, errorCallback?: Function): void => {
        this.request(new Options(url, "get", data), successCallback, errorCallback);
    }
    public post = (url: string, successCallback: Function, errorCallback?: Function): void => {
        this.request(new Options(url, "post"), successCallback, errorCallback);
    }
    public postWithData = (url: string, data: Object, successCallback: Function, errorCallback?: Function): void => {
        this.request(new Options(url, "post", data), successCallback, errorCallback);
    }
  
    public showJqueryDialog = (message: string, title?: string, height?: number): void => {
        alert(title + "\n" + message);
        title = title || "Info";
        height = height || 120;
        message = message.replace("\r", "").replace("\n", "<br/>");
    }
  }
  }