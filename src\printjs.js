/*$(document).ready(function(e){
alert('PRINT');
});*/
function saveprintsucc(){
  var data={id:'SW-1812S003-0014'};
  $.ajax({
    url: "http://119.59.112.47/node/apinano/api/set_printsaleheader",
    method: "POST",
    data: data,
    dataType: 'json',
    contentType: "application/json",
     success: function(result,status,jqXHR ){
      alert('sadasd'+result.d);
     },
     error(jqXHR, textStatus, errorThrown){
         //Do something
     }
}); 
}
