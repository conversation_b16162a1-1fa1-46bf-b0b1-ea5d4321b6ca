/* You can add global styles to this file, and also import other style files */
@import "~bootstrap/dist/css/bootstrap.css";


.modal-dialog {
    -webkit-transform: translate(0,-50%);
    -o-transform: translate(0,-50%);
    transform: translate(0,-50%);
    top: 10%;
    margin: 0 auto;
}
.colhaederal{
    background-color:rgb(164, 197, 235);
}
.modal-content {
    overflow:hidden;
}
.ngx-pagination {
    margin-bottom: 0;
}

.modal-Top100{
    top: -100px;
}
.bg-alert{
 color:red;
}
@font-face {
    font-family: 'angsa';
    src: url('assets/angsa.ttf');
}

.rcorners {
    border-radius: 10px ;
    border: 1px solid black;
    overflow: hidden;
}


.table2 {
    border-collapse: collapse;
    border-radius: 1em;
    overflow: hidden;
    border: 1px solid black;
  }
  
  .th2, .td2 {
    padding: 1em;
    background: #ddd;
    border-bottom: 1px solid white; 
 
  }
  .modalcinfig{
    max-width: 80%;
    
}